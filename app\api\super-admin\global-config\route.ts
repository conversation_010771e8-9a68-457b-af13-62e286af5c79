import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get global configuration from database
    const configs = await prisma.globalConfig.findMany()
    
    // Convert array to object for easier access
    const configObject = configs.reduce((acc, config) => {
      let value = config.value
      
      // Parse JSON values
      if (config.type === 'boolean') {
        value = value === 'true'
      } else if (config.type === 'number') {
        value = parseInt(value)
      } else if (config.type === 'json') {
        try {
          value = JSON.parse(value)
        } catch (e) {
          value = {}
        }
      }
      
      acc[config.key] = value
      return acc
    }, {} as Record<string, any>)

    return NextResponse.json({
      success: true,
      config: configObject
    })
  } catch (error) {
    console.error('Error fetching global config:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const config = await request.json()

    // Update or create configuration entries
    const updates = []
    
    for (const [key, value] of Object.entries(config)) {
      let stringValue = String(value)
      let type = 'string'
      
      if (typeof value === 'boolean') {
        type = 'boolean'
        stringValue = value.toString()
      } else if (typeof value === 'number') {
        type = 'number'
        stringValue = value.toString()
      } else if (typeof value === 'object' && value !== null) {
        type = 'json'
        stringValue = JSON.stringify(value)
      }

      updates.push(
        prisma.globalConfig.upsert({
          where: { key },
          update: {
            value: stringValue,
            type,
            updatedAt: new Date()
          },
          create: {
            key,
            value: stringValue,
            type,
            description: `Configuration for ${key}`
          }
        })
      )
    }

    await Promise.all(updates)

    // Log the configuration change
    await prisma.auditLog.create({
      data: {
        action: 'UPDATE_GLOBAL_CONFIG',
        entityType: 'GLOBAL_CONFIG',
        entityId: 'global',
        userId: session.user.id,
        details: {
          updatedKeys: Object.keys(config),
          timestamp: new Date().toISOString()
        }
      }
    }).catch(() => {
      // Ignore audit log errors
    })

    return NextResponse.json({
      success: true,
      message: 'Global configuration updated successfully'
    })
  } catch (error) {
    console.error('Error updating global config:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
