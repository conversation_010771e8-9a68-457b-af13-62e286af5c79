(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1123],{27910:function(e,t,s){Promise.resolve().then(s.bind(s,93581))},93581:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return Y}});var a=s(57437),r=s(2265),l=s(85754),n=s(45179),i=s(31478),c=s(45509),d=s(19160),o=s(93930),x=s(27815),m=s(92457),u=s(9883),h=s(4322),f=s(90998),j=s(97418),p=s(72894),g=s(41298),y=s(41827),N=s(17472),v=s(49617),b=s(45367),w=s(5925),k=s(49842),S=s(23444),C=s(86443),I=s(42706),Z=s(47934),P=s(92919);let R=["Electronics","Software","Hardware","Services","Consulting","Training","Support","Maintenance","Subscription","Digital Products","Physical Products","Other"],A=[{value:"USD",label:"USD ($)"},{value:"EUR",label:"EUR (€)"},{value:"GBP",label:"GBP (\xa3)"},{value:"CAD",label:"CAD (C$)"},{value:"AUD",label:"AUD (A$)"}];function F(e){let{isOpen:t,onClose:s,onSuccess:i,item:d,mode:o}=e,[x,m]=(0,r.useState)(!1),[u,f]=(0,r.useState)({name:"",description:"",sku:"",category:"",unitPrice:0,costPrice:"",currency:"USD",trackInventory:!1,stockQuantity:"",lowStockAlert:"",taxable:!0,taxRate:0,accountingCode:"",active:!0});(0,r.useEffect)(()=>{if(d&&"edit"===o){var e,t,s;f({name:d.name,description:d.description||"",sku:d.sku||"",category:d.category||"",unitPrice:d.unitPrice,costPrice:(null===(e=d.costPrice)||void 0===e?void 0:e.toString())||"",currency:d.currency,trackInventory:d.trackInventory,stockQuantity:(null===(t=d.stockQuantity)||void 0===t?void 0:t.toString())||"",lowStockAlert:(null===(s=d.lowStockAlert)||void 0===s?void 0:s.toString())||"",taxable:d.taxable,taxRate:d.taxRate,accountingCode:d.accountingCode||"",active:d.active})}else f({name:"",description:"",sku:"",category:"",unitPrice:0,costPrice:"",currency:"USD",trackInventory:!1,stockQuantity:"",lowStockAlert:"",taxable:!0,taxRate:0,accountingCode:"",active:!0})},[d,o,t]);let p=(e,t)=>{f(s=>({...s,[e]:t}))},y=async e=>{if(e.preventDefault(),!u.name.trim()){w.toast.error("Item name is required");return}if(u.unitPrice<0){w.toast.error("Unit price must be positive");return}if(u.costPrice&&0>parseFloat(u.costPrice)){w.toast.error("Cost price must be positive");return}if(u.trackInventory&&!u.stockQuantity){w.toast.error("Stock quantity is required when inventory tracking is enabled");return}m(!0);try{let e={name:u.name.trim(),description:u.description.trim()||void 0,sku:u.sku.trim()||void 0,category:u.category||void 0,unitPrice:u.unitPrice,costPrice:u.costPrice?parseFloat(u.costPrice):void 0,currency:u.currency,trackInventory:u.trackInventory,stockQuantity:u.trackInventory&&u.stockQuantity?parseInt(u.stockQuantity):void 0,lowStockAlert:u.trackInventory&&u.lowStockAlert?parseInt(u.lowStockAlert):void 0,taxable:u.taxable,taxRate:u.taxRate,accountingCode:u.accountingCode.trim()||void 0,active:u.active},t="edit"===o?"/api/items/".concat(null==d?void 0:d.id):"/api/items",a="edit"===o?"PUT":"POST",r=await fetch(t,{method:a,headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!r.ok){let e=await r.json();throw Error(e.error||"Failed to ".concat(o," item"))}w.toast.success("Item ".concat("edit"===o?"updated":"created"," successfully!")),i(),s()}catch(e){w.toast.error(e instanceof Error?e.message:"Failed to ".concat(o," item"))}finally{m(!1)}},N=u.costPrice&&u.unitPrice>0?(u.unitPrice-parseFloat(u.costPrice))/u.unitPrice*100:0,v=u.trackInventory&&u.stockQuantity?u.unitPrice*parseInt(u.stockQuantity):0;return(0,a.jsx)(I.Vq,{open:t,onOpenChange:s,children:(0,a.jsxs)(I.cZ,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsx)(I.fK,{children:(0,a.jsxs)(I.$N,{className:"flex items-center",children:[(0,a.jsx)(h.Z,{className:"h-5 w-5 mr-2"}),"edit"===o?"Edit Item":"Create New Item"]})}),(0,a.jsxs)("form",{onSubmit:y,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold flex items-center",children:[(0,a.jsx)(h.Z,{className:"h-4 w-4 mr-2"}),"Basic Information"]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(k._,{htmlFor:"name",children:"Item Name *"}),(0,a.jsx)(n.I,{id:"name",value:u.name,onChange:e=>p("name",e.target.value),placeholder:"Enter item name",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(k._,{htmlFor:"sku",children:"SKU"}),(0,a.jsx)(n.I,{id:"sku",value:u.sku,onChange:e=>p("sku",e.target.value),placeholder:"Stock Keeping Unit"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(k._,{htmlFor:"description",children:"Description"}),(0,a.jsx)(S.g,{id:"description",value:u.description,onChange:e=>p("description",e.target.value),placeholder:"Item description...",rows:3})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(k._,{htmlFor:"category",children:"Category"}),(0,a.jsxs)(c.Ph,{value:u.category,onValueChange:e=>p("category",e),children:[(0,a.jsx)(c.i4,{children:(0,a.jsx)(c.ki,{placeholder:"Select category"})}),(0,a.jsx)(c.Bw,{children:R.map(e=>(0,a.jsx)(c.Ql,{value:e,children:e},e))})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(C.r,{id:"active",checked:u.active,onCheckedChange:e=>p("active",e)}),(0,a.jsx)(k._,{htmlFor:"active",children:"Active"})]})]})]}),(0,a.jsx)(Z.Z,{}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold flex items-center",children:[(0,a.jsx)(g.Z,{className:"h-4 w-4 mr-2"}),"Pricing"]}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(k._,{htmlFor:"unitPrice",children:"Unit Price *"}),(0,a.jsx)(n.I,{id:"unitPrice",type:"number",step:"0.01",min:"0",value:u.unitPrice,onChange:e=>p("unitPrice",parseFloat(e.target.value)||0),placeholder:"0.00",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(k._,{htmlFor:"costPrice",children:"Cost Price"}),(0,a.jsx)(n.I,{id:"costPrice",type:"number",step:"0.01",min:"0",value:u.costPrice,onChange:e=>p("costPrice",e.target.value),placeholder:"0.00"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(k._,{htmlFor:"currency",children:"Currency"}),(0,a.jsxs)(c.Ph,{value:u.currency,onValueChange:e=>p("currency",e),children:[(0,a.jsx)(c.i4,{children:(0,a.jsx)(c.ki,{})}),(0,a.jsx)(c.Bw,{children:A.map(e=>(0,a.jsx)(c.Ql,{value:e.value,children:e.label},e.value))})]})]})]}),u.costPrice&&(0,a.jsxs)("div",{className:"p-3 bg-green-50 rounded-lg border border-green-200",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-green-800",children:"Profit Margin:"}),(0,a.jsxs)("span",{className:"text-lg font-bold text-green-600",children:[N.toFixed(1),"%"]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between mt-1",children:[(0,a.jsx)("span",{className:"text-sm text-green-700",children:"Profit per Unit:"}),(0,a.jsx)("span",{className:"text-sm font-semibold text-green-600",children:new Intl.NumberFormat("en-US",{style:"currency",currency:u.currency}).format(u.unitPrice-parseFloat(u.costPrice))})]})]})]}),(0,a.jsx)(Z.Z,{}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold flex items-center",children:[(0,a.jsx)(j.Z,{className:"h-4 w-4 mr-2"}),"Inventory Management"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(C.r,{id:"trackInventory",checked:u.trackInventory,onCheckedChange:e=>p("trackInventory",e)}),(0,a.jsx)(k._,{htmlFor:"trackInventory",children:"Track Inventory"})]})]}),u.trackInventory&&(0,a.jsxs)("div",{className:"space-y-4 p-4 bg-blue-50 rounded-lg border border-blue-200",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(k._,{htmlFor:"stockQuantity",children:"Stock Quantity *"}),(0,a.jsx)(n.I,{id:"stockQuantity",type:"number",min:"0",value:u.stockQuantity,onChange:e=>p("stockQuantity",e.target.value),placeholder:"0",required:u.trackInventory})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(k._,{htmlFor:"lowStockAlert",children:"Low Stock Alert"}),(0,a.jsx)(n.I,{id:"lowStockAlert",type:"number",min:"0",value:u.lowStockAlert,onChange:e=>p("lowStockAlert",e.target.value),placeholder:"10"})]})]}),u.stockQuantity&&(0,a.jsx)("div",{className:"p-3 bg-blue-100 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-blue-800",children:"Stock Value:"}),(0,a.jsx)("span",{className:"text-lg font-bold text-blue-600",children:new Intl.NumberFormat("en-US",{style:"currency",currency:u.currency}).format(v)})]})})]})]}),(0,a.jsx)(Z.Z,{}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold flex items-center",children:[(0,a.jsx)(P.Z,{className:"h-4 w-4 mr-2"}),"Tax & Accounting"]}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(C.r,{id:"taxable",checked:u.taxable,onCheckedChange:e=>p("taxable",e)}),(0,a.jsx)(k._,{htmlFor:"taxable",children:"Taxable"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(k._,{htmlFor:"taxRate",children:"Tax Rate (%)"}),(0,a.jsx)(n.I,{id:"taxRate",type:"number",step:"0.01",min:"0",max:"100",value:u.taxRate,onChange:e=>p("taxRate",parseFloat(e.target.value)||0),placeholder:"0.00",disabled:!u.taxable})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(k._,{htmlFor:"accountingCode",children:"Accounting Code"}),(0,a.jsx)(n.I,{id:"accountingCode",value:u.accountingCode,onChange:e=>p("accountingCode",e.target.value),placeholder:"e.g., 4000"})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-2 pt-4 border-t",children:[(0,a.jsx)(l.z,{type:"button",variant:"outline",onClick:s,children:"Cancel"}),(0,a.jsx)(l.z,{type:"submit",disabled:x,children:x?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"edit"===o?"Updating...":"Creating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(h.Z,{className:"h-4 w-4 mr-2"}),"edit"===o?"Update Item":"Create Item"]})})]})]})]})})}var U=s(64280),z=s(85790),Q=s(66654),_=s(30525),V=s(93505);function T(){let[e,t]=(0,r.useState)(null),[s,n]=(0,r.useState)(!0),[d,o]=(0,r.useState)("30"),u=async()=>{try{n(!0);let e=await fetch("/api/items/analytics?period=".concat(d));if(!e.ok)throw Error("Failed to fetch analytics");let s=await e.json();t(s)}catch(e){w.toast.error("Failed to load item analytics"),console.error("Error fetching analytics:",e)}finally{n(!1)}};(0,r.useEffect)(()=>{u()},[d]);let y=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e),N=e=>new Intl.NumberFormat("en-US").format(e);if(s)return(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})});if(!e)return(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)(m.Z,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,a.jsx)("p",{children:"Failed to load analytics data"})]});let v=e.summary.totalStockQuantity>0?e.summary.usedItemsCount/e.summary.totalItems*100:0,b=e.summary.totalStockValue>0&&e.summary.totalCostValue>0?(e.summary.totalStockValue-e.summary.totalCostValue)/e.summary.totalStockValue*100:0;return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Item Analytics"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(c.Ph,{value:d,onValueChange:o,children:[(0,a.jsx)(c.i4,{className:"w-32",children:(0,a.jsx)(c.ki,{})}),(0,a.jsxs)(c.Bw,{children:[(0,a.jsx)(c.Ql,{value:"7",children:"Last 7 days"}),(0,a.jsx)(c.Ql,{value:"30",children:"Last 30 days"}),(0,a.jsx)(c.Ql,{value:"90",children:"Last 90 days"}),(0,a.jsx)(c.Ql,{value:"365",children:"Last year"})]})]}),(0,a.jsxs)(l.z,{variant:"outline",onClick:u,size:"sm",children:[(0,a.jsx)(U.Z,{className:"h-4 w-4 mr-2"}),"Refresh"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsx)(x.Zb,{children:(0,a.jsx)(x.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-blue-100 rounded-full",children:(0,a.jsx)(h.Z,{className:"h-6 w-6 text-blue-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Total Items"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.totalItems})]})]})})}),(0,a.jsx)(x.Zb,{children:(0,a.jsx)(x.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-green-100 rounded-full",children:(0,a.jsx)(f.Z,{className:"h-6 w-6 text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Active Items"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.activeItems})]})]})})}),(0,a.jsx)(x.Zb,{children:(0,a.jsx)(x.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-purple-100 rounded-full",children:(0,a.jsx)(j.Z,{className:"h-6 w-6 text-purple-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Tracked Items"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.trackedItems})]})]})})}),(0,a.jsx)(x.Zb,{children:(0,a.jsx)(x.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-orange-100 rounded-full",children:(0,a.jsx)(p.Z,{className:"h-6 w-6 text-orange-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Low Stock"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.lowStockCount})]})]})})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsx)(x.Zb,{children:(0,a.jsx)(x.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-emerald-100 rounded-full",children:(0,a.jsx)(g.Z,{className:"h-6 w-6 text-emerald-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Stock Value"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:y(e.summary.totalStockValue)})]})]})})}),(0,a.jsx)(x.Zb,{children:(0,a.jsx)(x.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-indigo-100 rounded-full",children:(0,a.jsx)(z.Z,{className:"h-6 w-6 text-indigo-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Avg Unit Price"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:y(e.summary.avgUnitPrice)})]})]})})}),(0,a.jsx)(x.Zb,{children:(0,a.jsx)(x.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-pink-100 rounded-full",children:(0,a.jsx)(Q.Z,{className:"h-6 w-6 text-pink-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Usage Value"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:y(e.summary.totalUsageValue)})]})]})})}),(0,a.jsx)(x.Zb,{children:(0,a.jsx)(x.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-teal-100 rounded-full",children:(0,a.jsx)(_.Z,{className:"h-6 w-6 text-teal-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Profit Margin"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[b.toFixed(1),"%"]})]})]})})})]}),(0,a.jsxs)(x.Zb,{children:[(0,a.jsx)(x.Ol,{children:(0,a.jsxs)(x.ll,{className:"flex items-center",children:[(0,a.jsx)(j.Z,{className:"h-5 w-5 mr-2"}),"Inventory Overview"]})}),(0,a.jsx)(x.aY,{children:(0,a.jsxs)("div",{className:"grid grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:N(e.summary.totalStockQuantity)}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Total Stock Quantity"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-green-600",children:e.summary.avgStockPerItem}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Avg Stock per Item"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("p",{className:"text-2xl font-bold text-purple-600",children:[v.toFixed(1),"%"]}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Stock Utilization"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-orange-600",children:e.summary.itemsWithCostPrice}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Items with Cost Price"})]})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)(x.Zb,{children:[(0,a.jsx)(x.Ol,{children:(0,a.jsx)(x.ll,{children:"Items by Category"})}),(0,a.jsx)(x.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:e.itemsByCategory.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsx)(i.C,{variant:"outline",children:e.category})}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("span",{className:"font-semibold",children:e.count}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Avg: ",y(e.avgPrice)]})]})]},e.category))})})]}),(0,a.jsxs)(x.Zb,{children:[(0,a.jsx)(x.Ol,{children:(0,a.jsx)(x.ll,{children:"Items by Status"})}),(0,a.jsx)(x.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:e.itemsByStatus.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsx)(i.C,{className:"Active"===e.status?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800",children:e.status})}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("span",{className:"font-semibold",children:e.count}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Avg: ",y(e.avgPrice)]})]})]},e.status))})})]})]}),(0,a.jsxs)(x.Zb,{children:[(0,a.jsx)(x.Ol,{children:(0,a.jsxs)(x.ll,{className:"flex items-center",children:[(0,a.jsx)(V.Z,{className:"h-5 w-5 mr-2"}),"Top Selling Items"]})}),(0,a.jsx)(x.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:e.topSellingItems.slice(0,5).map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center",children:(0,a.jsxs)("span",{className:"text-xs font-semibold text-blue-600",children:["#",t+1]})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:e.category||"Uncategorized"})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"font-semibold text-green-600",children:y(e.totalRevenue)}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[e.usageCount," uses • ",N(e.totalQuantitySold)," sold"]})]})]},e.id))})})]}),e.lowStockItems.length>0&&(0,a.jsxs)(x.Zb,{children:[(0,a.jsx)(x.Ol,{children:(0,a.jsxs)(x.ll,{className:"flex items-center",children:[(0,a.jsx)(p.Z,{className:"h-5 w-5 mr-2 text-orange-600"}),"Low Stock Alert"]})}),(0,a.jsx)(x.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:e.lowStockItems.slice(0,5).map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-orange-50 rounded-lg border border-orange-200",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[e.sku||"No SKU"," • ",e.category||"Uncategorized"]}),(0,a.jsx)("div",{className:"flex items-center space-x-2 mt-1",children:(0,a.jsxs)("p",{className:"text-sm text-orange-600",children:["Stock: ",e.stockQuantity," / Alert: ",e.lowStockAlert]})})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"font-semibold text-green-600",children:y(e.stockValue)}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[y(e.unitPrice)," each"]})]})]},e.id))})})]}),(0,a.jsxs)(x.Zb,{children:[(0,a.jsx)(x.Ol,{children:(0,a.jsxs)(x.ll,{className:"flex items-center",children:[(0,a.jsx)(m.Z,{className:"h-5 w-5 mr-2"}),"Category Performance"]})}),(0,a.jsx)(x.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:e.categoryPerformance.slice(0,5).map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:e.category}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 mt-1 text-sm text-gray-500",children:[(0,a.jsxs)("span",{children:[e.itemCount," items"]}),(0,a.jsxs)("span",{children:[e.activeItems," active"]}),(0,a.jsxs)("span",{children:[e.trackedItems," tracked"]})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"font-semibold text-blue-600",children:y(e.avgPrice)}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[e.usageCount," uses • ",N(e.totalStock)," stock"]})]})]},e.category))})})]}),e.profitabilityAnalysis.length>0&&(0,a.jsxs)(x.Zb,{children:[(0,a.jsx)(x.Ol,{children:(0,a.jsxs)(x.ll,{className:"flex items-center",children:[(0,a.jsx)(z.Z,{className:"h-5 w-5 mr-2"}),"Most Profitable Items"]})}),(0,a.jsx)(x.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:e.profitabilityAnalysis.slice(0,5).map(e=>{var t;return(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:e.category||"Uncategorized"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,a.jsxs)("p",{className:"text-sm text-green-600",children:["Margin: ",null===(t=e.profitMarginPercent)||void 0===t?void 0:t.toFixed(1),"%"]}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[e.usageCount," uses"]})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"font-semibold text-green-600",children:y(e.profitPerUnit||0)}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"profit per unit"})]})]},e.id)})})})]}),(0,a.jsxs)(x.Zb,{children:[(0,a.jsx)(x.Ol,{children:(0,a.jsxs)(x.ll,{className:"flex items-center",children:[(0,a.jsx)(h.Z,{className:"h-5 w-5 mr-2"}),"Recent Items (Last 7 Days)"]})}),(0,a.jsx)(x.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:0===e.recentItems.length?(0,a.jsx)("p",{className:"text-gray-500 text-center py-4",children:"No recent items"}):e.recentItems.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[e.sku||"No SKU"," • ",e.category||"Uncategorized"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,a.jsx)(i.C,{className:e.active?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800",variant:"outline",children:e.active?"Active":"Inactive"}),e.trackInventory&&(0,a.jsx)(i.C,{variant:"outline",children:"Tracked"})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"font-semibold text-green-600",children:y(e.unitPrice)}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:e.trackInventory?"Stock: ".concat(e.stockQuantity||0):"No tracking"})]})]},e.id))})})]})]})}function Y(){let[e,t]=(0,r.useState)([]),[s,k]=(0,r.useState)(!0),[S,C]=(0,r.useState)(!1),[I,Z]=(0,r.useState)(null),[P,R]=(0,r.useState)(!1),[A,U]=(0,r.useState)(""),[z,Q]=(0,r.useState)(""),[_,V]=(0,r.useState)(""),[Y,E]=(0,r.useState)(""),[D,O]=(0,r.useState)(!1),[B,M]=(0,r.useState)(1),[L,q]=(0,r.useState)(1),[$,K]=(0,r.useState)(0),H=async()=>{try{k(!0);let e=new URLSearchParams({page:B.toString(),limit:"50",...A&&{search:A},...z&&"all"!==z&&{category:z},..._&&"all"!==_&&{status:_},...Y&&"all"!==Y&&{trackInventory:Y},...D&&{lowStock:"true"}}),s=await fetch("/api/items?".concat(e));if(!s.ok)throw Error("Failed to fetch items");let a=await s.json();t(a.items),q(a.pagination.pages),K(a.pagination.total)}catch(e){w.toast.error("Failed to load items"),console.error("Error fetching items:",e)}finally{k(!1)}};(0,r.useEffect)(()=>{H()},[B,A,z,_,Y,D]);let W=()=>{M(1),H()},G=e=>{Z(e),C(!0)},J=async e=>{if(confirm('Are you sure you want to delete "'.concat(e.name,'"?')))try{let t=await fetch("/api/items/".concat(e.id),{method:"DELETE"});if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to delete item")}w.toast.success("Item deleted successfully"),H()}catch(e){w.toast.error(e instanceof Error?e.message:"Failed to delete item")}},X=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD";return new Intl.NumberFormat("en-US",{style:"currency",currency:t}).format(e)},ee=e=>e?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800",et=e=>{if(!e)return"bg-gray-100 text-gray-800";let t=["bg-blue-100 text-blue-800","bg-purple-100 text-purple-800","bg-green-100 text-green-800","bg-orange-100 text-orange-800","bg-pink-100 text-pink-800","bg-indigo-100 text-indigo-800"];return t[Math.abs(e.split("").reduce((e,t)=>(e=(e<<5)-e+t.charCodeAt(0))&e,0))%t.length]},es=e.filter(e=>e.active).length,ea=e.filter(e=>e.trackInventory).length,er=e.filter(e=>e.isLowStock).length,el=e.reduce((e,t)=>e+t.stockValue,0),en=Array.from(new Set(e.map(e=>e.category).filter(Boolean)));return s&&0===e.length?(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,a.jsxs)("div",{className:"container mx-auto py-6 space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Items"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"Manage your products and services"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(l.z,{variant:"outline",onClick:()=>R(!P),children:[(0,a.jsx)(m.Z,{className:"h-4 w-4 mr-2"}),"Analytics"]}),(0,a.jsxs)(l.z,{onClick:()=>C(!0),children:[(0,a.jsx)(u.Z,{className:"h-4 w-4 mr-2"}),"New Item"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsx)(x.Zb,{children:(0,a.jsx)(x.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-blue-100 rounded-full",children:(0,a.jsx)(h.Z,{className:"h-6 w-6 text-blue-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Total Items"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:$})]})]})})}),(0,a.jsx)(x.Zb,{children:(0,a.jsx)(x.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-green-100 rounded-full",children:(0,a.jsx)(f.Z,{className:"h-6 w-6 text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Active Items"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:es})]})]})})}),(0,a.jsx)(x.Zb,{children:(0,a.jsx)(x.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-purple-100 rounded-full",children:(0,a.jsx)(j.Z,{className:"h-6 w-6 text-purple-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Tracked Items"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:ea})]})]})})}),(0,a.jsx)(x.Zb,{children:(0,a.jsx)(x.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-orange-100 rounded-full",children:(0,a.jsx)(p.Z,{className:"h-6 w-6 text-orange-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Low Stock"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:er})]})]})})})]}),(0,a.jsx)(x.Zb,{children:(0,a.jsx)(x.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-emerald-100 rounded-full",children:(0,a.jsx)(g.Z,{className:"h-6 w-6 text-emerald-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Total Stock Value"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:X(el)})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Avg per Item"}),(0,a.jsx)("p",{className:"text-lg font-semibold text-gray-700",children:X(ea>0?el/ea:0)})]})]})})}),P&&(0,a.jsx)(T,{}),(0,a.jsx)(x.Zb,{children:(0,a.jsx)(x.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-4",children:[(0,a.jsx)("div",{className:"flex-1 min-w-64",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(y.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,a.jsx)(n.I,{placeholder:"Search items...",value:A,onChange:e=>U(e.target.value),onKeyPress:e=>"Enter"===e.key&&W(),className:"pl-10"})]})}),(0,a.jsxs)(c.Ph,{value:z,onValueChange:Q,children:[(0,a.jsx)(c.i4,{className:"w-48",children:(0,a.jsx)(c.ki,{placeholder:"All Categories"})}),(0,a.jsxs)(c.Bw,{children:[(0,a.jsx)(c.Ql,{value:"all",children:"All Categories"}),en.map(e=>(0,a.jsx)(c.Ql,{value:e,children:e},e))]})]}),(0,a.jsxs)(c.Ph,{value:_,onValueChange:V,children:[(0,a.jsx)(c.i4,{className:"w-32",children:(0,a.jsx)(c.ki,{placeholder:"Status"})}),(0,a.jsxs)(c.Bw,{children:[(0,a.jsx)(c.Ql,{value:"all",children:"All Status"}),(0,a.jsx)(c.Ql,{value:"active",children:"Active"}),(0,a.jsx)(c.Ql,{value:"inactive",children:"Inactive"})]})]}),(0,a.jsxs)(c.Ph,{value:Y,onValueChange:E,children:[(0,a.jsx)(c.i4,{className:"w-40",children:(0,a.jsx)(c.ki,{placeholder:"Inventory"})}),(0,a.jsxs)(c.Bw,{children:[(0,a.jsx)(c.Ql,{value:"all",children:"All Items"}),(0,a.jsx)(c.Ql,{value:"true",children:"Tracked"}),(0,a.jsx)(c.Ql,{value:"false",children:"Not Tracked"})]})]}),(0,a.jsxs)(l.z,{variant:D?"default":"outline",onClick:()=>O(!D),size:"sm",children:[(0,a.jsx)(p.Z,{className:"h-4 w-4 mr-2"}),"Low Stock"]}),(0,a.jsxs)(l.z,{onClick:W,size:"sm",children:[(0,a.jsx)(y.Z,{className:"h-4 w-4 mr-2"}),"Search"]})]})})}),(0,a.jsxs)(x.Zb,{children:[(0,a.jsx)(x.Ol,{children:(0,a.jsxs)(x.ll,{children:["Items (",$,")"]})}),(0,a.jsxs)(x.aY,{children:[(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)(d.iA,{children:[(0,a.jsx)(d.xD,{children:(0,a.jsxs)(d.SC,{children:[(0,a.jsx)(d.ss,{children:"Item"}),(0,a.jsx)(d.ss,{children:"Category"}),(0,a.jsx)(d.ss,{children:"Price"}),(0,a.jsx)(d.ss,{children:"Stock"}),(0,a.jsx)(d.ss,{children:"Usage"}),(0,a.jsx)(d.ss,{children:"Status"}),(0,a.jsx)(d.ss,{className:"w-12"})]})}),(0,a.jsx)(d.RM,{children:s?(0,a.jsx)(d.SC,{children:(0,a.jsx)(d.pj,{colSpan:7,className:"text-center py-8",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"})})}):0===e.length?(0,a.jsx)(d.SC,{children:(0,a.jsx)(d.pj,{colSpan:7,className:"text-center py-8 text-gray-500",children:"No items found"})}):e.map(e=>(0,a.jsxs)(d.SC,{children:[(0,a.jsx)(d.pj,{children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[e.sku&&"SKU: ".concat(e.sku),e.description&&(0,a.jsx)("div",{className:"truncate max-w-xs",children:e.description})]})]})}),(0,a.jsx)(d.pj,{children:e.category?(0,a.jsx)(i.C,{className:et(e.category),variant:"outline",children:e.category}):(0,a.jsx)("span",{className:"text-gray-400",children:"-"})}),(0,a.jsx)(d.pj,{children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:X(e.unitPrice,e.currency)}),e.costPrice&&(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["Cost: ",X(e.costPrice,e.currency),e.profitMargin&&(0,a.jsxs)("span",{className:"ml-1 text-green-600",children:["(",e.profitMargin.toFixed(1),"%)"]})]})]})}),(0,a.jsx)(d.pj,{children:e.trackInventory?(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"font-medium ".concat(e.isLowStock?"text-orange-600":""),children:[e.stockQuantity||0,e.isLowStock&&(0,a.jsx)(p.Z,{className:"inline h-4 w-4 ml-1 text-orange-600"})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["Value: ",X(e.stockValue,e.currency)]})]}):(0,a.jsx)("span",{className:"text-gray-400",children:"Not tracked"})}),(0,a.jsx)(d.pj,{children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"font-medium",children:[e.usage.usageCount," uses"]}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[e.usage.totalQuantity," total qty"]})]})}),(0,a.jsx)(d.pj,{children:(0,a.jsx)(i.C,{className:ee(e.active),variant:"outline",children:e.active?"Active":"Inactive"})}),(0,a.jsx)(d.pj,{children:(0,a.jsxs)(o.h_,{children:[(0,a.jsx)(o.$F,{asChild:!0,children:(0,a.jsx)(l.z,{variant:"ghost",size:"sm",children:(0,a.jsx)(N.Z,{className:"h-4 w-4"})})}),(0,a.jsxs)(o.AW,{align:"end",children:[(0,a.jsxs)(o.Xi,{onClick:()=>G(e),children:[(0,a.jsx)(v.Z,{className:"mr-2 h-4 w-4"}),"Edit"]}),(0,a.jsxs)(o.Xi,{onClick:()=>J(e),children:[(0,a.jsx)(b.Z,{className:"mr-2 h-4 w-4"}),"Delete"]})]})]})})]},e.id))})]})}),L>1&&(0,a.jsxs)("div",{className:"flex items-center justify-between mt-4",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["Showing ",(B-1)*50+1," to ",Math.min(50*B,$)," of ",$," items"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(l.z,{variant:"outline",size:"sm",onClick:()=>M(e=>Math.max(1,e-1)),disabled:1===B,children:"Previous"}),(0,a.jsxs)("span",{className:"text-sm",children:["Page ",B," of ",L]}),(0,a.jsx)(l.z,{variant:"outline",size:"sm",onClick:()=>M(e=>Math.min(L,e+1)),disabled:B===L,children:"Next"})]})]})]})]}),(0,a.jsx)(F,{isOpen:S,onClose:()=>{C(!1),Z(null)},onSuccess:H,item:I,mode:I?"edit":"create"})]})}},31478:function(e,t,s){"use strict";s.d(t,{C:function(){return i}});var a=s(57437);s(2265);var r=s(96061),l=s(1657);let n=(0,r.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600",info:"border-transparent bg-blue-500 text-white hover:bg-blue-600"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:s,...r}=e;return(0,a.jsx)("div",{className:(0,l.cn)(n({variant:s}),t),...r})}},85754:function(e,t,s){"use strict";s.d(t,{z:function(){return d}});var a=s(57437),r=s(2265),l=s(67256),n=s(96061),i=s(1657);let c=(0,n.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=r.forwardRef((e,t)=>{let{className:s,variant:r,size:n,asChild:d=!1,...o}=e,x=d?l.g7:"button";return(0,a.jsx)(x,{className:(0,i.cn)(c({variant:r,size:n,className:s})),ref:t,...o})});d.displayName="Button"},27815:function(e,t,s){"use strict";s.d(t,{Ol:function(){return i},SZ:function(){return d},Zb:function(){return n},aY:function(){return o},eW:function(){return x},ll:function(){return c}});var a=s(57437),r=s(2265),l=s(1657);let n=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...r})});n.displayName="Card";let i=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",s),...r})});i.displayName="CardHeader";let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("h3",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",s),...r})});c.displayName="CardTitle";let d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("p",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",s),...r})});d.displayName="CardDescription";let o=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",s),...r})});o.displayName="CardContent";let x=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",s),...r})});x.displayName="CardFooter"},42706:function(e,t,s){"use strict";s.d(t,{$N:function(){return f},Be:function(){return j},Vq:function(){return c},cN:function(){return h},cZ:function(){return m},fK:function(){return u},hg:function(){return d},t9:function(){return x}});var a=s(57437),r=s(2265),l=s(28712),n=s(82549),i=s(1657);let c=l.fC,d=l.xz,o=l.h_;l.x8;let x=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.aV,{ref:t,className:(0,i.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...r})});x.displayName=l.aV.displayName;let m=r.forwardRef((e,t)=>{let{className:s,children:r,...c}=e;return(0,a.jsxs)(o,{children:[(0,a.jsx)(x,{}),(0,a.jsxs)(l.VY,{ref:t,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",s),...c,children:[r,(0,a.jsxs)(l.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(n.Z,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});m.displayName=l.VY.displayName;let u=e=>{let{className:t,...s}=e;return(0,a.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...s})};u.displayName="DialogHeader";let h=e=>{let{className:t,...s}=e;return(0,a.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...s})};h.displayName="DialogFooter";let f=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.Dx,{ref:t,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",s),...r})});f.displayName=l.Dx.displayName;let j=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.dk,{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",s),...r})});j.displayName=l.dk.displayName},93930:function(e,t,s){"use strict";s.d(t,{$F:function(){return x},AW:function(){return m},Ju:function(){return f},VD:function(){return j},Xi:function(){return u},bO:function(){return h},h_:function(){return o}});var a=s(57437),r=s(2265),l=s(23291),n=s(17158),i=s(62442),c=s(76369),d=s(1657);let o=l.fC,x=l.xz;l.ZA,l.Uv,l.Tr,l.Ee,r.forwardRef((e,t)=>{let{className:s,inset:r,children:i,...c}=e;return(0,a.jsxs)(l.fF,{ref:t,className:(0,d.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",r&&"pl-8",s),...c,children:[i,(0,a.jsx)(n.Z,{className:"ml-auto h-4 w-4"})]})}).displayName=l.fF.displayName,r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.tu,{ref:t,className:(0,d.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...r})}).displayName=l.tu.displayName;let m=r.forwardRef((e,t)=>{let{className:s,sideOffset:r=4,...n}=e;return(0,a.jsx)(l.Uv,{children:(0,a.jsx)(l.VY,{ref:t,sideOffset:r,className:(0,d.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...n})})});m.displayName=l.VY.displayName;let u=r.forwardRef((e,t)=>{let{className:s,inset:r,...n}=e;return(0,a.jsx)(l.ck,{ref:t,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r&&"pl-8",s),...n})});u.displayName=l.ck.displayName;let h=r.forwardRef((e,t)=>{let{className:s,children:r,checked:n,...c}=e;return(0,a.jsxs)(l.oC,{ref:t,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),checked:n,...c,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(l.wU,{children:(0,a.jsx)(i.Z,{className:"h-4 w-4"})})}),r]})});h.displayName=l.oC.displayName,r.forwardRef((e,t)=>{let{className:s,children:r,...n}=e;return(0,a.jsxs)(l.Rk,{ref:t,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...n,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(l.wU,{children:(0,a.jsx)(c.Z,{className:"h-2 w-2 fill-current"})})}),r]})}).displayName=l.Rk.displayName;let f=r.forwardRef((e,t)=>{let{className:s,inset:r,...n}=e;return(0,a.jsx)(l.__,{ref:t,className:(0,d.cn)("px-2 py-1.5 text-sm font-semibold",r&&"pl-8",s),...n})});f.displayName=l.__.displayName;let j=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.Z0,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",s),...r})});j.displayName=l.Z0.displayName},45179:function(e,t,s){"use strict";s.d(t,{I:function(){return n}});var a=s(57437),r=s(2265),l=s(1657);let n=r.forwardRef((e,t)=>{let{className:s,type:r,...n}=e;return(0,a.jsx)("input",{type:r,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:t,...n})});n.displayName="Input"},49842:function(e,t,s){"use strict";s.d(t,{_:function(){return d}});var a=s(57437),r=s(2265),l=s(36743),n=s(96061),i=s(1657);let c=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.f,{ref:t,className:(0,i.cn)(c(),s),...r})});d.displayName=l.f.displayName},45509:function(e,t,s){"use strict";s.d(t,{Bw:function(){return f},Ph:function(){return o},Ql:function(){return j},i4:function(){return m},ki:function(){return x}});var a=s(57437),r=s(2265),l=s(99530),n=s(83523),i=s(9224),c=s(62442),d=s(1657);let o=l.fC;l.ZA;let x=l.B4,m=r.forwardRef((e,t)=>{let{className:s,children:r,...i}=e;return(0,a.jsxs)(l.xz,{ref:t,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",s),...i,children:[r,(0,a.jsx)(l.JO,{asChild:!0,children:(0,a.jsx)(n.Z,{className:"h-4 w-4 opacity-50"})})]})});m.displayName=l.xz.displayName;let u=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.u_,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",s),...r,children:(0,a.jsx)(i.Z,{className:"h-4 w-4"})})});u.displayName=l.u_.displayName;let h=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.$G,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",s),...r,children:(0,a.jsx)(n.Z,{className:"h-4 w-4"})})});h.displayName=l.$G.displayName;let f=r.forwardRef((e,t)=>{let{className:s,children:r,position:n="popper",...i}=e;return(0,a.jsx)(l.h_,{children:(0,a.jsxs)(l.VY,{ref:t,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:n,...i,children:[(0,a.jsx)(u,{}),(0,a.jsx)(l.l_,{className:(0,d.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),(0,a.jsx)(h,{})]})})});f.displayName=l.VY.displayName,r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.__,{ref:t,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",s),...r})}).displayName=l.__.displayName;let j=r.forwardRef((e,t)=>{let{className:s,children:r,...n}=e;return(0,a.jsxs)(l.ck,{ref:t,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...n,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(l.wU,{children:(0,a.jsx)(c.Z,{className:"h-4 w-4"})})}),(0,a.jsx)(l.eT,{children:r})]})});j.displayName=l.ck.displayName,r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.Z0,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",s),...r})}).displayName=l.Z0.displayName},47934:function(e,t,s){"use strict";s.d(t,{Z:function(){return i}});var a=s(57437),r=s(2265),l=s(26823),n=s(1657);let i=r.forwardRef((e,t)=>{let{className:s,orientation:r="horizontal",decorative:i=!0,...c}=e;return(0,a.jsx)(l.f,{ref:t,decorative:i,orientation:r,className:(0,n.cn)("shrink-0 bg-border","horizontal"===r?"h-[1px] w-full":"h-full w-[1px]",s),...c})});i.displayName=l.f.displayName},86443:function(e,t,s){"use strict";s.d(t,{r:function(){return i}});var a=s(57437),r=s(2265),l=s(92376),n=s(1657);let i=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.fC,{className:(0,n.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",s),...r,ref:t,children:(0,a.jsx)(l.bU,{className:(0,n.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});i.displayName=l.fC.displayName},19160:function(e,t,s){"use strict";s.d(t,{RM:function(){return c},SC:function(){return d},iA:function(){return n},pj:function(){return x},ss:function(){return o},xD:function(){return i}});var a=s(57437),r=s(2265),l=s(1657);let n=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{className:"relative w-full overflow-auto",children:(0,a.jsx)("table",{ref:t,className:(0,l.cn)("w-full caption-bottom text-sm",s),...r})})});n.displayName="Table";let i=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("thead",{ref:t,className:(0,l.cn)("[&_tr]:border-b",s),...r})});i.displayName="TableHeader";let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("tbody",{ref:t,className:(0,l.cn)("[&_tr:last-child]:border-0",s),...r})});c.displayName="TableBody",r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("tfoot",{ref:t,className:(0,l.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",s),...r})}).displayName="TableFooter";let d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("tr",{ref:t,className:(0,l.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",s),...r})});d.displayName="TableRow";let o=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("th",{ref:t,className:(0,l.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",s),...r})});o.displayName="TableHead";let x=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("td",{ref:t,className:(0,l.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",s),...r})});x.displayName="TableCell",r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("caption",{ref:t,className:(0,l.cn)("mt-4 text-sm text-muted-foreground",s),...r})}).displayName="TableCaption"},23444:function(e,t,s){"use strict";s.d(t,{g:function(){return n}});var a=s(57437),r=s(2265),l=s(1657);let n=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:t,...r})});n.displayName="Textarea"},1657:function(e,t,s){"use strict";s.d(t,{cn:function(){return l}});var a=s(57042),r=s(74769);function l(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.m6)((0,a.W)(t))}}},function(e){e.O(0,[6723,9502,1706,4138,4997,2012,3523,2971,4938,1744],function(){return e(e.s=27910)}),_N_E=e.O()}]);