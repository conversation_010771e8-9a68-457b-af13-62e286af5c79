(()=>{var e={};e.id=5892,e.ids=[5892],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},48407:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>c});var t=a(50482),r=a(69108),l=a(62563),i=a.n(l),d=a(68300),n={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);a.d(s,n);let c=["",{children:["dashboard",{children:["tasks",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,39620)),"C:\\proj\\nextjs-saas\\app\\dashboard\\tasks\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,96339)),"C:\\proj\\nextjs-saas\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,59504)),"C:\\proj\\nextjs-saas\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,69361,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\proj\\nextjs-saas\\app\\dashboard\\tasks\\[id]\\page.tsx"],x="/dashboard/tasks/[id]/page",m={require:a,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/tasks/[id]/page",pathname:"/dashboard/tasks/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},3912:(e,s,a)=>{Promise.resolve().then(a.bind(a,43251))},43251:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>k});var t=a(95344),r=a(3729),l=a(22254),i=a(61351),d=a(16212),n=a(69436),c=a(57341),o=a(11880),x=a(66138),m=a(63024),h=a(75695),p=a(38271),u=a(2246),j=a(36341),f=a(2768),N=a(18822),g=a(55794),y=a(25545),b=a(44669),v=a(20783),w=a.n(v);function k({params:e}){let[s,a]=(0,r.useState)(null),[v,k]=(0,r.useState)(!0),[C,D]=(0,r.useState)(!1),Z=(0,l.useRouter)(),E=async()=>{try{k(!0);let s=await fetch(`/api/tasks/${e.id}`);if(!s.ok){if(404===s.status){b.toast.error("Task not found"),Z.push("/dashboard/tasks");return}throw Error("Failed to fetch task")}let t=await s.json();a(t.task)}catch(e){b.toast.error("Failed to load task"),console.error("Error fetching task:",e)}finally{k(!1)}};(0,r.useEffect)(()=>{E()},[e.id]);let _=async()=>{if(confirm("Are you sure you want to delete this task?"))try{if(!(await fetch(`/api/tasks/${e.id}`,{method:"DELETE"})).ok)throw Error("Failed to delete task");b.toast.success("Task deleted successfully"),Z.push("/dashboard/tasks")}catch(e){b.toast.error("Failed to delete task"),console.error("Error deleting task:",e)}},T=async s=>{try{if(!(await fetch(`/api/tasks/${e.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:s})})).ok)throw Error("Failed to update task status");b.toast.success("Task status updated"),E()}catch(e){b.toast.error("Failed to update task status"),console.error("Error updating task status:",e)}};if(v)return t.jsx("div",{className:"container mx-auto py-6",children:t.jsx("div",{className:"flex items-center justify-center py-8",children:t.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})})});if(!s)return t.jsx("div",{className:"container mx-auto py-6",children:(0,t.jsxs)("div",{className:"text-center py-8",children:[t.jsx(x.Z,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),t.jsx("p",{className:"text-gray-500",children:"Task not found"}),t.jsx(d.z,{asChild:!0,className:"mt-4",children:(0,t.jsxs)(w(),{href:"/dashboard/tasks",children:[t.jsx(m.Z,{className:"h-4 w-4 mr-2"}),"Back to Tasks"]})})]})});let z=s.dueDate&&new Date(s.dueDate)<new Date&&"DONE"!==s.status;return(0,t.jsxs)("div",{className:"container mx-auto py-6 space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[t.jsx(d.z,{variant:"outline",asChild:!0,children:(0,t.jsxs)(w(),{href:"/dashboard/tasks",children:[t.jsx(m.Z,{className:"h-4 w-4 mr-2"}),"Back to Tasks"]})}),(0,t.jsxs)("div",{children:[t.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:s.title}),t.jsx("p",{className:"text-gray-600",children:"Task Details"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(d.z,{variant:"outline",onClick:()=>D(!0),children:[t.jsx(h.Z,{className:"h-4 w-4 mr-2"}),"Edit"]}),(0,t.jsxs)(d.z,{variant:"outline",onClick:_,className:"text-red-600 hover:text-red-700",children:[t.jsx(p.Z,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,t.jsxs)(i.Zb,{children:[t.jsx(i.Ol,{children:t.jsx(i.ll,{children:"Task Information"})}),(0,t.jsxs)(i.aY,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(e=>{let s={TODO:{label:"To Do",className:"bg-gray-100 text-gray-800"},IN_PROGRESS:{label:"In Progress",className:"bg-blue-100 text-blue-800"},REVIEW:{label:"Review",className:"bg-yellow-100 text-yellow-800"},DONE:{label:"Done",className:"bg-green-100 text-green-800"},CANCELLED:{label:"Cancelled",className:"bg-red-100 text-red-800"}},a=s[e]||s.TODO;return t.jsx(n.C,{className:a.className,children:a.label})})(s.status),(e=>{let s={LOW:{label:"Low",className:"bg-gray-100 text-gray-600"},MEDIUM:{label:"Medium",className:"bg-blue-100 text-blue-600"},HIGH:{label:"High",className:"bg-orange-100 text-orange-600"},URGENT:{label:"Urgent",className:"bg-red-100 text-red-600"},CRITICAL:{label:"Critical",className:"bg-red-200 text-red-800"}},a=s[e]||s.MEDIUM;return t.jsx(n.C,{className:a.className,children:a.label})})(s.priority),t.jsx(n.C,{variant:"outline",children:s.type}),s.category&&t.jsx(n.C,{variant:"outline",children:s.category})]}),"DONE"!==s.status&&(0,t.jsxs)(d.z,{onClick:()=>T("DONE"),size:"sm",children:[t.jsx(u.Z,{className:"h-4 w-4 mr-2"}),"Mark Complete"]})]}),s.description&&(0,t.jsxs)("div",{children:[t.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Description"}),t.jsx("p",{className:"text-gray-700 whitespace-pre-wrap",children:s.description})]}),s.tags.length>0&&(0,t.jsxs)("div",{children:[t.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Tags"}),t.jsx("div",{className:"flex flex-wrap gap-2",children:s.tags.map((e,s)=>(0,t.jsxs)(n.C,{variant:"secondary",className:"flex items-center space-x-1",children:[t.jsx(j.Z,{className:"h-3 w-3"}),t.jsx("span",{children:e})]},s))})]})]})]}),(s.lead||s.customer||s.quotation||s.invoice||s.contract)&&(0,t.jsxs)(i.Zb,{children:[t.jsx(i.Ol,{children:t.jsx(i.ll,{children:"Related Records"})}),(0,t.jsxs)(i.aY,{className:"space-y-4",children:[s.lead&&(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-blue-50 rounded-lg",children:[(0,t.jsxs)("div",{children:[t.jsx("p",{className:"font-medium",children:"Lead"}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[s.lead.firstName," ",s.lead.lastName,s.lead.companyName&&` (${s.lead.companyName})`]})]}),t.jsx(d.z,{variant:"outline",size:"sm",asChild:!0,children:t.jsx(w(),{href:`/dashboard/leads/${s.lead.id}`,children:t.jsx(f.Z,{className:"h-4 w-4"})})})]}),s.customer&&(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-green-50 rounded-lg",children:[(0,t.jsxs)("div",{children:[t.jsx("p",{className:"font-medium",children:"Customer"}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[s.customer.name,s.customer.company&&` (${s.customer.company})`]})]}),t.jsx(d.z,{variant:"outline",size:"sm",asChild:!0,children:t.jsx(w(),{href:`/dashboard/customers/${s.customer.id}`,children:t.jsx(f.Z,{className:"h-4 w-4"})})})]}),s.quotation&&(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-purple-50 rounded-lg",children:[(0,t.jsxs)("div",{children:[t.jsx("p",{className:"font-medium",children:"Quotation"}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[s.quotation.title," - $",s.quotation.total.toLocaleString()]})]}),t.jsx(d.z,{variant:"outline",size:"sm",asChild:!0,children:t.jsx(w(),{href:`/dashboard/quotations/${s.quotation.id}`,children:t.jsx(f.Z,{className:"h-4 w-4"})})})]})]})]})]}),t.jsx("div",{className:"space-y-6",children:(0,t.jsxs)(i.Zb,{children:[t.jsx(i.Ol,{children:t.jsx(i.ll,{children:"Details"})}),(0,t.jsxs)(i.aY,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[t.jsx(N.Z,{className:"h-4 w-4 text-gray-400"}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm text-gray-500",children:"Assigned To"}),t.jsx("p",{className:"font-medium",children:s.assignedTo?s.assignedTo.name||s.assignedTo.email:"Unassigned"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[t.jsx(N.Z,{className:"h-4 w-4 text-gray-400"}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm text-gray-500",children:"Created By"}),t.jsx("p",{className:"font-medium",children:s.createdBy.name||s.createdBy.email})]})]}),t.jsx(c.Z,{}),s.startDate&&(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[t.jsx(g.Z,{className:"h-4 w-4 text-gray-400"}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm text-gray-500",children:"Start Date"}),t.jsx("p",{className:"font-medium",children:new Date(s.startDate).toLocaleDateString()})]})]}),s.dueDate&&(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[t.jsx(g.Z,{className:`h-4 w-4 ${z?"text-red-500":"text-gray-400"}`}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm text-gray-500",children:"Due Date"}),(0,t.jsxs)("p",{className:`font-medium ${z?"text-red-600":""}`,children:[new Date(s.dueDate).toLocaleDateString(),z&&" (Overdue)"]})]})]}),s.completedAt&&(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[t.jsx(u.Z,{className:"h-4 w-4 text-green-500"}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm text-gray-500",children:"Completed"}),t.jsx("p",{className:"font-medium text-green-600",children:new Date(s.completedAt).toLocaleDateString()})]})]}),t.jsx(c.Z,{}),(s.estimatedHours||s.actualHours)&&(0,t.jsxs)("div",{className:"space-y-2",children:[s.estimatedHours&&(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[t.jsx(y.Z,{className:"h-4 w-4 text-gray-400"}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm text-gray-500",children:"Estimated Hours"}),(0,t.jsxs)("p",{className:"font-medium",children:[s.estimatedHours,"h"]})]})]}),s.actualHours&&(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[t.jsx(y.Z,{className:"h-4 w-4 text-gray-400"}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm text-gray-500",children:"Actual Hours"}),(0,t.jsxs)("p",{className:"font-medium",children:[s.actualHours,"h"]})]})]})]}),t.jsx(c.Z,{}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:[(0,t.jsxs)("p",{children:["Created: ",new Date(s.createdAt).toLocaleString()]}),(0,t.jsxs)("p",{children:["Updated: ",new Date(s.updatedAt).toLocaleString()]})]})]})]})})]}),C&&t.jsx(o.t,{task:s,onClose:()=>D(!1),onSuccess:()=>{D(!1),E()}})]})}},17470:(e,s,a)=>{"use strict";a.d(s,{Bw:()=>u,Ph:()=>o,Ql:()=>j,i4:()=>m,ki:()=>x});var t=a(95344),r=a(3729),l=a(1146),i=a(25390),d=a(12704),n=a(62312),c=a(91626);let o=l.fC;l.ZA;let x=l.B4,m=r.forwardRef(({className:e,children:s,...a},r)=>(0,t.jsxs)(l.xz,{ref:r,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...a,children:[s,t.jsx(l.JO,{asChild:!0,children:t.jsx(i.Z,{className:"h-4 w-4 opacity-50"})})]}));m.displayName=l.xz.displayName;let h=r.forwardRef(({className:e,...s},a)=>t.jsx(l.u_,{ref:a,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:t.jsx(d.Z,{className:"h-4 w-4"})}));h.displayName=l.u_.displayName;let p=r.forwardRef(({className:e,...s},a)=>t.jsx(l.$G,{ref:a,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:t.jsx(i.Z,{className:"h-4 w-4"})}));p.displayName=l.$G.displayName;let u=r.forwardRef(({className:e,children:s,position:a="popper",...r},i)=>t.jsx(l.h_,{children:(0,t.jsxs)(l.VY,{ref:i,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...r,children:[t.jsx(h,{}),t.jsx(l.l_,{className:(0,c.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),t.jsx(p,{})]})}));u.displayName=l.VY.displayName,r.forwardRef(({className:e,...s},a)=>t.jsx(l.__,{ref:a,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...s})).displayName=l.__.displayName;let j=r.forwardRef(({className:e,children:s,...a},r)=>(0,t.jsxs)(l.ck,{ref:r,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...a,children:[t.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:t.jsx(l.wU,{children:t.jsx(n.Z,{className:"h-4 w-4"})})}),t.jsx(l.eT,{children:s})]}));j.displayName=l.ck.displayName,r.forwardRef(({className:e,...s},a)=>t.jsx(l.Z0,{ref:a,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",e),...s})).displayName=l.Z0.displayName},57341:(e,s,a)=>{"use strict";a.d(s,{Z:()=>o});var t=a(95344),r=a(3729),l=a(62409),i="horizontal",d=["horizontal","vertical"],n=r.forwardRef((e,s)=>{let{decorative:a,orientation:r=i,...n}=e,c=d.includes(r)?r:i;return(0,t.jsx)(l.WV.div,{"data-orientation":c,...a?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...n,ref:s})});n.displayName="Separator";var c=a(91626);let o=r.forwardRef(({className:e,orientation:s="horizontal",decorative:a=!0,...r},l)=>t.jsx(n,{ref:l,decorative:a,orientation:s,className:(0,c.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",e),...r}));o.displayName=n.displayName},2768:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]])},39620:(e,s,a)=>{"use strict";a.r(s),a.d(s,{$$typeof:()=>l,__esModule:()=>r,default:()=>i});let t=(0,a(86843).createProxy)(String.raw`C:\proj\nextjs-saas\app\dashboard\tasks\[id]\page.tsx`),{__esModule:r,$$typeof:l}=t,i=t.default}};var s=require("../../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[1638,7948,6671,4626,7792,2506,8830,2125,5045,5803,5904],()=>a(48407));module.exports=t})();