"use strict";(()=>{var e={};e.id=8968,e.ids=[8968],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},10180:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>x,originalPathname:()=>b,patchFetch:()=>O,requestAsyncStorage:()=>w,routeModule:()=>h,serverHooks:()=>C,staticGenerationAsyncStorage:()=>f,staticGenerationBailout:()=>_});var a={};r.r(a),r.d(a,{GET:()=>g,POST:()=>I});var o=r(95419),s=r(69108),n=r(99678),i=r(78070),l=r(81355),u=r(3205),p=r(9108),d=r(25252),c=r(52178);let m=d.Ry({name:d.Z_().min(1,"Name is required"),email:d.Z_().email("Invalid email").optional().nullable(),phone:d.Z_().optional().nullable(),company:d.Z_().optional().nullable(),address:d.Z_().optional().nullable(),city:d.Z_().optional().nullable(),state:d.Z_().optional().nullable(),country:d.Z_().optional().nullable(),postalCode:d.Z_().optional().nullable(),industry:d.Z_().optional().nullable(),website:d.Z_().url("Invalid website URL").optional().nullable().or(d.i0("")),notes:d.Z_().optional().nullable(),status:d.Km(["ACTIVE","INACTIVE","PROSPECT"]).default("PROSPECT"),tags:d.IX(d.Z_()).default([])}),y=d.Ry({customers:d.IX(m),options:d.Ry({skipDuplicates:d.O7().default(!0),updateExisting:d.O7().default(!1)}).default({})});async function I(e){try{let t=await (0,l.getServerSession)(u.L);if(!t?.user?.id||!t?.user?.companyId)return i.Z.json({error:"Unauthorized"},{status:401});let r=await e.json(),{customers:a,options:o}=y.parse(r),s={total:a.length,imported:0,updated:0,skipped:0,errors:[]};for(let e=0;e<a.length;e++){let r=a[e];try{let a=null;if(r.email&&(a=await p._.customer.findFirst({where:{email:r.email,companyId:t.user.companyId}})),a)o.updateExisting?(await p._.customer.update({where:{id:a.id},data:{...r,updatedAt:new Date}}),s.updated++):o.skipDuplicates?s.skipped++:s.errors.push({row:e+1,error:"Customer with this email already exists",data:r});else{let e=await p._.customer.create({data:{...r,companyId:t.user.companyId,createdById:t.user.id}});await p._.activity.create({data:{type:"NOTE",title:"Customer Imported",description:`Customer "${e.name}" was imported`,customerId:e.id,companyId:t.user.companyId,createdById:t.user.id}}),s.imported++}}catch(t){s.errors.push({row:e+1,error:t instanceof Error?t.message:"Unknown error",data:r})}}return i.Z.json({success:!0,results:s})}catch(e){if(e instanceof c.jm)return i.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("Error importing customers:",e),i.Z.json({error:"Failed to import customers"},{status:500})}}async function g(e){try{let t=await (0,l.getServerSession)(u.L);if(!t?.user?.id||!t?.user?.companyId)return i.Z.json({error:"Unauthorized"},{status:401});let{searchParams:r}=new URL(e.url),a=r.get("format")||"json",o={name:"John Doe",email:"<EMAIL>",phone:"******-123-4567",company:"Acme Corporation",address:"123 Main Street",city:"New York",state:"NY",country:"USA",postalCode:"10001",industry:"Technology",website:"https://example.com",notes:"Important customer notes",status:"PROSPECT",tags:["vip","enterprise"]};if("csv"===a){let e=Object.keys(o),t=e.join(","),r=e.map(e=>{let t=o[e];return Array.isArray(t)?`"${t.join(";")}"`:`"${t}"`}).join(","),a=`${t}
${r}`;return new Response(a,{headers:{"Content-Type":"text/csv","Content-Disposition":'attachment; filename="customer-import-template.csv"'}})}return i.Z.json({template:o,instructions:{name:"Required. Customer full name",email:"Optional. Must be valid email format",phone:"Optional. Customer phone number",company:"Optional. Company name",address:"Optional. Street address",city:"Optional. City name",state:"Optional. State/Province",country:"Optional. Country name",postalCode:"Optional. ZIP/Postal code",industry:"Optional. Industry sector",website:"Optional. Must be valid URL",notes:"Optional. Additional notes",status:"Optional. One of: ACTIVE, INACTIVE, PROSPECT (default: PROSPECT)",tags:"Optional. Array of tags or semicolon-separated string"},example:[o]})}catch(e){return console.error("Error generating import template:",e),i.Z.json({error:"Failed to generate template"},{status:500})}}let h=new o.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/customers/import/route",pathname:"/api/customers/import",filename:"route",bundlePath:"app/api/customers/import/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\customers\\import\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:w,staticGenerationAsyncStorage:f,serverHooks:C,headerHooks:x,staticGenerationBailout:_}=h,b="/api/customers/import/route";function O(){return(0,n.patchFetch)({serverHooks:C,staticGenerationAsyncStorage:f})}},3205:(e,t,r)=>{r.d(t,{L:()=>u});var a=r(86485),o=r(10375),s=r(50694),n=r(6521),i=r.n(n),l=r(9108);let u={providers:[(0,a.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await l._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),r=t?.companyId;if(!r&&t){let e=await l._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(r=e?.id)&&await l._.user.update({where:{id:t.id},data:{companyId:r}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await i().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await l._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:r}}catch(e){return console.error("Authentication error:",e),null}}}),(0,o.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,s.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,r)=>{r.d(t,{_:()=>o});let a=require("@prisma/client"),o=globalThis.prisma??new a.PrismaClient}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,6206,6521,2455,4520,5252],()=>r(10180));module.exports=a})();