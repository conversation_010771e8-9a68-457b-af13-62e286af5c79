import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Create super admin user first (without company)
  const hashedPassword = await bcrypt.hash('admin123', 12)

  const superAdmin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Super Admin',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'SUPER_ADMIN',
      status: 'ACTIVE',
      emailVerified: new Date()
    }
  })

  // Create a demo company with the super admin as owner
  const demoCompany = await prisma.company.upsert({
    where: { id: 'demo-company' },
    update: {},
    create: {
      id: 'demo-company',
      name: 'Demo Company',
      email: '<EMAIL>',
      phone: '******-0123',
      address: '123 Demo Street',
      city: 'Demo City',
      state: 'Demo State',
      postalCode: '12345',
      country: 'United States',
      website: 'https://democompany.com',
      industry: 'Technology',
      size: 'SMALL',
      status: 'ACTIVE',
      ownerId: superAdmin.id
    }
  })

  // Create demo customers
  const demoCustomer1 = await prisma.customer.create({
    data: {
      name: 'John Smith',
      email: '<EMAIL>',
      phone: '******-0101',
      company: 'Smith Enterprises',
      address: '456 Customer Ave',
      city: 'Customer City',
      state: 'CA',
      zipCode: '90210',
      country: 'United States',
      companyId: demoCompany.id,
      createdById: 'demo-user'
    }
  })

  const demoCustomer2 = await prisma.customer.create({
    data: {
      name: 'Jane Doe',
      email: '<EMAIL>',
      phone: '******-0102',
      company: 'Doe Industries',
      address: '789 Business Blvd',
      city: 'Business City',
      state: 'NY',
      zipCode: '10001',
      country: 'United States',
      companyId: demoCompany.id,
      createdById: 'demo-user'
    }
  })

  // Skip pricing plans as the model doesn't exist in current schema

  // Super admin and demo company already created above

  // Create demo user
  const demoUserPassword = await bcrypt.hash('demo123', 12)
  
  const demoUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Demo User',
      email: '<EMAIL>',
      password: demoUserPassword,
      role: 'ADMIN',
      status: 'ACTIVE',
      companyId: demoCompany.id,
      emailVerified: new Date()
    }
  })

  // Create demo subscription
  const starterPlan = await prisma.pricingPlan.findUnique({
    where: { id: 'starter' }
  })

  if (starterPlan) {
    await prisma.subscription.upsert({
      where: { id: 'demo-subscription' },
      update: {},
      create: {
        id: 'demo-subscription',
        planId: starterPlan.id,
        status: 'TRIAL',
        billingCycle: 'MONTHLY',
        trialStartDate: new Date(),
        trialEndDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days from now
        features: starterPlan.features,
        limits: starterPlan.limits
      }
    })

    // Update company with subscription
    await prisma.company.update({
      where: { id: demoCompany.id },
      data: { subscriptionId: 'demo-subscription' }
    })
  }

  // Create sample customers
  const sampleCustomers = [
    {
      name: 'Acme Corporation',
      email: '<EMAIL>',
      phone: '******-0101',
      company: 'Acme Corporation',
      industry: 'Manufacturing',
      status: 'ACTIVE',
      companyId: demoCompany.id,
      createdById: demoUser.id
    },
    {
      name: 'Tech Solutions Inc',
      email: '<EMAIL>',
      phone: '******-0102',
      company: 'Tech Solutions Inc',
      industry: 'Technology',
      status: 'ACTIVE',
      companyId: demoCompany.id,
      createdById: demoUser.id
    }
  ]

  for (const customer of sampleCustomers) {
    await prisma.customer.create({ data: customer })
  }

  // Create sample items
  const sampleItems = [
    {
      name: 'Consulting Hours',
      description: 'Professional consulting services',
      price: 150,
      cost: 75,
      category: 'Services',
      status: 'ACTIVE',
      companyId: demoCompany.id,
      createdById: demoUser.id
    },
    {
      name: 'Project Management',
      description: 'Complete project management service',
      price: 2500,
      cost: 1200,
      category: 'Services',
      status: 'ACTIVE',
      companyId: demoCompany.id,
      createdById: demoUser.id
    }
  ]

  for (const item of sampleItems) {
    await prisma.item.create({ data: item })
  }

  console.log('✅ Database seeded successfully!')
  console.log('')
  console.log('🔑 Login credentials:')
  console.log('Super Admin: <EMAIL> / admin123')
  console.log('Demo User: <EMAIL> / demo123')
  console.log('')
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
