import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Create super admin user first (without company)
  const hashedPassword = await bcrypt.hash('admin123', 12)

  const superAdmin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Super Admin',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'SUPER_ADMIN',
      status: 'ACTIVE',
      emailVerified: new Date()
    }
  })

  // Create a demo company with the super admin as owner
  const demoCompany = await prisma.company.upsert({
    where: { id: 'demo-company' },
    update: {},
    create: {
      id: 'demo-company',
      name: 'Demo Company',
      email: '<EMAIL>',
      phone: '******-0123',
      address: '123 Demo Street',
      city: 'Demo City',
      state: 'Demo State',
      postalCode: '12345',
      country: 'United States',
      website: 'https://democompany.com',
      industry: 'Technology',
      size: 'SMALL',
      status: 'ACTIVE',
      ownerId: superAdmin.id
    }
  })

  // Create demo customers
  const demoCustomer1 = await prisma.customer.create({
    data: {
      name: 'John Smith',
      email: '<EMAIL>',
      phone: '******-0101',
      companyName: 'Smith Enterprises',
      address: '456 Customer Ave',
      city: 'Customer City',
      state: 'CA',
      postalCode: '90210',
      country: 'United States',
      companyId: demoCompany.id,
      createdById: superAdmin.id
    }
  })

  const demoCustomer2 = await prisma.customer.create({
    data: {
      name: 'Jane Doe',
      email: '<EMAIL>',
      phone: '******-0102',
      companyName: 'Doe Industries',
      address: '789 Business Blvd',
      city: 'Business City',
      state: 'NY',
      postalCode: '10001',
      country: 'United States',
      companyId: demoCompany.id,
      createdById: superAdmin.id
    }
  })

  // Skip pricing plans as the model doesn't exist in current schema

  // Super admin and demo company already created above

  // Create demo user
  const demoUserPassword = await bcrypt.hash('demo123', 12)
  
  const demoUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Demo User',
      email: '<EMAIL>',
      password: demoUserPassword,
      role: 'ADMIN',
      status: 'ACTIVE',
      companyId: demoCompany.id,
      emailVerified: new Date()
    }
  })

  // Skip subscription creation as PricingPlan model doesn't exist

  // Create sample customers
  const sampleCustomers = [
    {
      name: 'Acme Corporation',
      email: '<EMAIL>',
      phone: '******-0101',
      companyName: 'Acme Corporation',
      industry: 'Manufacturing',
      companyId: demoCompany.id,
      createdById: superAdmin.id
    },
    {
      name: 'Tech Solutions Inc',
      email: '<EMAIL>',
      phone: '******-0102',
      companyName: 'Tech Solutions Inc',
      industry: 'Technology',
      companyId: demoCompany.id,
      createdById: superAdmin.id
    }
  ]

  for (const customer of sampleCustomers) {
    await prisma.customer.create({ data: customer })
  }

  // Create sample items
  const sampleItems = [
    {
      name: 'Consulting Hours',
      description: 'Professional consulting services',
      unitPrice: 150,
      costPrice: 75,
      category: 'Services',
      companyId: demoCompany.id
    },
    {
      name: 'Project Management',
      description: 'Complete project management service',
      unitPrice: 2500,
      costPrice: 1200,
      category: 'Services',
      companyId: demoCompany.id
    }
  ]

  for (const item of sampleItems) {
    await prisma.item.create({ data: item })
  }

  // Create some test payment transactions
  console.log('🔄 Creating test payment transactions...')

  const testTransactions = await prisma.transaction.createMany({
    data: [
      {
        type: 'PAYMENT',
        amount: 1500.00,
        currency: 'USD',
        paymentMethod: 'CREDIT_CARD',
        paymentStatus: 'COMPLETED',
        reference: 'PAY-001',
        description: 'Payment for services rendered',
        notes: 'Customer payment via credit card',
        companyId: demoCompany.id,
        transactionDate: new Date('2024-01-15'),
      },
      {
        type: 'PAYMENT',
        amount: 2500.00,
        currency: 'USD',
        paymentMethod: 'BANK_TRANSFER',
        paymentStatus: 'COMPLETED',
        reference: 'PAY-002',
        description: 'Monthly subscription payment',
        notes: 'Bank transfer payment',
        companyId: demoCompany.id,
        transactionDate: new Date('2024-01-20'),
      },
      {
        type: 'PAYMENT',
        amount: 750.00,
        currency: 'USD',
        paymentMethod: 'CHECK',
        paymentStatus: 'PENDING',
        reference: 'PAY-003',
        description: 'Consulting services payment',
        notes: 'Check payment - pending clearance',
        companyId: demoCompany.id,
        transactionDate: new Date('2024-01-25'),
      },
      {
        type: 'PAYMENT',
        amount: 3200.00,
        currency: 'USD',
        paymentMethod: 'PAYPAL',
        paymentStatus: 'COMPLETED',
        reference: 'PAY-004',
        description: 'Project milestone payment',
        notes: 'PayPal payment for project completion',
        companyId: demoCompany.id,
        transactionDate: new Date('2024-02-01'),
      },
      {
        type: 'PAYMENT',
        amount: 1200.00,
        currency: 'USD',
        paymentMethod: 'CASH',
        paymentStatus: 'COMPLETED',
        reference: 'PAY-005',
        description: 'Cash payment for services',
        notes: 'Cash payment received in office',
        companyId: demoCompany.id,
        transactionDate: new Date('2024-02-05'),
      }
    ]
  })

  console.log(`✅ Created ${testTransactions.count} test payment transactions`)
  console.log('✅ Database seeded successfully!')
  console.log('')
  console.log('🔑 Login credentials:')
  console.log('Super Admin: <EMAIL> / admin123')
  console.log('Demo User: <EMAIL> / demo123')
  console.log('')
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
