import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const paymentSchema = z.object({
  amount: z.number().min(0.01, 'Payment amount must be greater than 0'),
  paymentDate: z.string().optional(),
  paymentMethod: z.enum(['CASH', 'CHECK', 'CREDIT_CARD', 'BANK_TRANSFER', 'PAYPAL', 'OTHER']).default('CASH'),
  reference: z.string().optional(),
  notes: z.string().optional()
})

// GET /api/invoices/[id]/payments - Get payments for invoice
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify invoice exists and belongs to user's company
    const invoice = await prisma.invoice.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId
      }
    })

    if (!invoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 })
    }

    // Get all payments for this invoice
    const payments = await prisma.transaction.findMany({
      where: {
        invoiceId: params.id,
        type: 'PAYMENT'
      },
      include: {
        createdBy: {
          select: {
            name: true,
            email: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    return NextResponse.json({
      payments: payments.map(payment => ({
        id: payment.id,
        amount: Number(payment.amount),
        paymentDate: payment.date,
        paymentMethod: payment.method,
        reference: payment.reference,
        notes: payment.notes,
        createdBy: payment.createdBy,
        createdAt: payment.createdAt
      }))
    })

  } catch (error) {
    console.error('Error fetching payments:', error)
    return NextResponse.json(
      { error: 'Failed to fetch payments' },
      { status: 500 }
    )
  }
}

// POST /api/invoices/[id]/payments - Record payment for invoice
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = paymentSchema.parse(body)

    // Get invoice with current payment status
    const invoice = await prisma.invoice.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId
      },
      include: {
        transactions: {
          where: { type: 'PAYMENT' }
        }
      }
    })

    if (!invoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 })
    }

    // Calculate current paid amount
    const currentPaidAmount = invoice.transactions.reduce(
      (sum, transaction) => sum + Number(transaction.amount),
      0
    )

    // Validate payment amount
    const remainingAmount = Number(invoice.total) - currentPaidAmount
    if (validatedData.amount > remainingAmount) {
      return NextResponse.json(
        { error: `Payment amount cannot exceed remaining balance of ${remainingAmount.toFixed(2)}` },
        { status: 400 }
      )
    }

    // Create payment transaction
    const paymentDate = validatedData.paymentDate 
      ? new Date(validatedData.paymentDate)
      : new Date()

    const payment = await prisma.$transaction(async (tx) => {
      // Create payment transaction
      const newPayment = await tx.transaction.create({
        data: {
          type: 'PAYMENT',
          amount: validatedData.amount,
          date: paymentDate,
          method: validatedData.paymentMethod,
          reference: validatedData.reference,
          notes: validatedData.notes,
          invoiceId: params.id,
          customerId: invoice.customerId,
          companyId: session.user.companyId!,
          createdById: session.user.id
        },
        include: {
          createdBy: {
            select: {
              name: true,
              email: true
            }
          }
        }
      })

      // Calculate new paid amount
      const newPaidAmount = currentPaidAmount + validatedData.amount
      const isFullyPaid = newPaidAmount >= Number(invoice.total)

      // Update invoice paid amount and status
      await tx.invoice.update({
        where: { id: params.id },
        data: {
          paidAmount: newPaidAmount,
          status: isFullyPaid ? 'PAID' : invoice.status,
          paidAt: isFullyPaid ? new Date() : invoice.paidAt
        }
      })

      // Log activity
      await tx.activity.create({
        data: {
          type: 'PAYMENT',
          title: 'Payment Recorded',
          description: `Payment of $${validatedData.amount.toFixed(2)} recorded for invoice ${invoice.invoiceNumber}`,
          invoiceId: params.id,
          customerId: invoice.customerId,
          companyId: session.user.companyId!,
          createdById: session.user.id
        }
      })

      return newPayment
    })

    return NextResponse.json({
      payment: {
        id: payment.id,
        amount: Number(payment.amount),
        paymentDate: payment.date,
        paymentMethod: payment.method,
        reference: payment.reference,
        notes: payment.notes,
        createdBy: payment.createdBy,
        createdAt: payment.createdAt
      },
      message: 'Payment recorded successfully'
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error recording payment:', error)
    return NextResponse.json(
      { error: 'Failed to record payment' },
      { status: 500 }
    )
  }
}

// DELETE /api/invoices/[id]/payments/[paymentId] - Delete payment
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const paymentId = searchParams.get('paymentId')

    if (!paymentId) {
      return NextResponse.json({ error: 'Payment ID is required' }, { status: 400 })
    }

    // Verify payment exists and belongs to the invoice and company
    const payment = await prisma.transaction.findFirst({
      where: {
        id: paymentId,
        invoiceId: params.id,
        type: 'PAYMENT',
        companyId: session.user.companyId
      },
      include: {
        invoice: true
      }
    })

    if (!payment) {
      return NextResponse.json({ error: 'Payment not found' }, { status: 404 })
    }

    await prisma.$transaction(async (tx) => {
      // Delete the payment
      await tx.transaction.delete({
        where: { id: paymentId }
      })

      // Recalculate invoice paid amount
      const remainingPayments = await tx.transaction.findMany({
        where: {
          invoiceId: params.id,
          type: 'PAYMENT',
          id: { not: paymentId }
        }
      })

      const newPaidAmount = remainingPayments.reduce(
        (sum, transaction) => sum + Number(transaction.amount),
        0
      )

      const isFullyPaid = newPaidAmount >= Number(payment.invoice.total)

      // Update invoice
      await tx.invoice.update({
        where: { id: params.id },
        data: {
          paidAmount: newPaidAmount,
          status: isFullyPaid ? 'PAID' : (newPaidAmount > 0 ? 'SENT' : payment.invoice.status),
          paidAt: isFullyPaid ? payment.invoice.paidAt : null
        }
      })

      // Log activity
      await tx.activity.create({
        data: {
          type: 'PAYMENT',
          title: 'Payment Deleted',
          description: `Payment of $${Number(payment.amount).toFixed(2)} was deleted from invoice ${payment.invoice.invoiceNumber}`,
          invoiceId: params.id,
          customerId: payment.invoice.customerId,
          companyId: session.user.companyId!,
          createdById: session.user.id
        }
      })
    })

    return NextResponse.json({ message: 'Payment deleted successfully' })

  } catch (error) {
    console.error('Error deleting payment:', error)
    return NextResponse.json(
      { error: 'Failed to delete payment' },
      { status: 500 }
    )
  }
}
