"use strict";(()=>{var e={};e.id=1974,e.ids=[1974],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},82247:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>_,originalPathname:()=>D,patchFetch:()=>k,requestAsyncStorage:()=>f,routeModule:()=>g,serverHooks:()=>E,staticGenerationAsyncStorage:()=>h,staticGenerationBailout:()=>x});var r={};a.r(r),a.d(r,{DELETE:()=>w,GET:()=>y,PUT:()=>I});var s=a(95419),o=a(69108),i=a(99678),n=a(78070),l=a(81355),d=a(3205),u=a(9108),c=a(25252),p=a(52178);let m=c.Ry({title:c.Z_().min(1,"Title is required").optional(),description:c.Z_().optional(),status:c.Km(["TODO","IN_PROGRESS","REVIEW","DONE","CANCELLED"]).optional(),priority:c.Km(["LOW","MEDIUM","HIGH","URGENT","CRITICAL"]).optional(),dueDate:c.Z_().optional(),startDate:c.Z_().optional(),assignedToId:c.Z_().optional(),tags:c.IX(c.Z_()).optional(),customFields:c.IM(c.Yj()).optional(),type:c.Z_().optional(),category:c.Z_().optional(),estimatedHours:c.Rx().optional(),actualHours:c.Rx().optional(),completionNotes:c.Z_().optional()});async function y(e,{params:t}){try{let e=await (0,l.getServerSession)(d.L);if(!e?.user?.id||!e?.user?.companyId)return n.Z.json({error:"Unauthorized"},{status:401});let a=await u._.task.findFirst({where:{id:t.id,companyId:e.user.companyId},include:{assignedTo:{select:{id:!0,name:!0,email:!0}},createdBy:{select:{id:!0,name:!0,email:!0}},lead:{select:{id:!0,firstName:!0,lastName:!0,companyName:!0,email:!0,phone:!0}},customer:{select:{id:!0,name:!0,email:!0,company:!0,phone:!0}},quotation:{select:{id:!0,title:!0,total:!0,status:!0}},invoice:{select:{id:!0,invoiceNumber:!0,total:!0,status:!0}},contract:{select:{id:!0,title:!0,status:!0,startDate:!0,endDate:!0}}}});if(!a)return n.Z.json({error:"Task not found"},{status:404});return n.Z.json({task:a})}catch(e){return console.error("Error fetching task:",e),n.Z.json({error:"Failed to fetch task"},{status:500})}}async function I(e,{params:t}){try{let a=await (0,l.getServerSession)(d.L);if(!a?.user?.id||!a?.user?.companyId)return n.Z.json({error:"Unauthorized"},{status:401});let r=await e.json(),s=m.parse(r),o=await u._.task.findFirst({where:{id:t.id,companyId:a.user.companyId}});if(!o)return n.Z.json({error:"Task not found"},{status:404});if(s.assignedToId&&!await u._.user.findFirst({where:{id:s.assignedToId,companyId:a.user.companyId}}))return n.Z.json({error:"Assigned user not found or not in your company"},{status:400});let i={...s,updatedAt:new Date};void 0!==s.dueDate&&(i.dueDate=s.dueDate?new Date(s.dueDate):null),void 0!==s.startDate&&(i.startDate=s.startDate?new Date(s.startDate):null),s.status&&("DONE"===s.status&&"DONE"!==o.status?i.completedAt=new Date:"DONE"!==s.status&&"DONE"===o.status&&(i.completedAt=null));let c=await u._.task.update({where:{id:t.id},data:i,include:{assignedTo:{select:{id:!0,name:!0,email:!0}},createdBy:{select:{id:!0,name:!0,email:!0}},lead:{select:{id:!0,firstName:!0,lastName:!0,companyName:!0}},customer:{select:{id:!0,name:!0,email:!0,company:!0}}}});return n.Z.json({task:c})}catch(e){if(e instanceof p.jm)return n.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("Error updating task:",e),n.Z.json({error:"Failed to update task"},{status:500})}}async function w(e,{params:t}){try{let e=await (0,l.getServerSession)(d.L);if(!e?.user?.id||!e?.user?.companyId)return n.Z.json({error:"Unauthorized"},{status:401});if(!await u._.task.findFirst({where:{id:t.id,companyId:e.user.companyId}}))return n.Z.json({error:"Task not found"},{status:404});return await u._.task.delete({where:{id:t.id}}),n.Z.json({message:"Task deleted successfully"})}catch(e){return console.error("Error deleting task:",e),n.Z.json({error:"Failed to delete task"},{status:500})}}let g=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/tasks/[id]/route",pathname:"/api/tasks/[id]",filename:"route",bundlePath:"app/api/tasks/[id]/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\tasks\\[id]\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:f,staticGenerationAsyncStorage:h,serverHooks:E,headerHooks:_,staticGenerationBailout:x}=g,D="/api/tasks/[id]/route";function k(){return(0,i.patchFetch)({serverHooks:E,staticGenerationAsyncStorage:h})}},3205:(e,t,a)=>{a.d(t,{L:()=>d});var r=a(86485),s=a(10375),o=a(50694),i=a(6521),n=a.n(i),l=a(9108);let d={providers:[(0,r.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await l._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),a=t?.companyId;if(!a&&t){let e=await l._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(a=e?.id)&&await l._.user.update({where:{id:t.id},data:{companyId:a}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await n().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await l._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:a}}catch(e){return console.error("Authentication error:",e),null}}}),(0,s.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,o.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,a)=>{a.d(t,{_:()=>s});let r=require("@prisma/client"),s=globalThis.prisma??new r.PrismaClient}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[1638,6206,6521,2455,4520,5252],()=>a(82247));module.exports=r})();