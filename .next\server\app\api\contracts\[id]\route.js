"use strict";(()=>{var e={};e.id=4262,e.ids=[4262],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},61092:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>_,originalPathname:()=>C,patchFetch:()=>Z,requestAsyncStorage:()=>E,routeModule:()=>w,serverHooks:()=>h,staticGenerationAsyncStorage:()=>f,staticGenerationBailout:()=>v});var a={};r.r(a),r.d(a,{DELETE:()=>g,GET:()=>y,PUT:()=>I});var n=r(95419),o=r(69108),s=r(99678),i=r(78070),l=r(81355),c=r(3205),u=r(9108),d=r(25252),p=r(52178);let m=d.Ry({title:d.Z_().min(1,"Title is required").optional(),description:d.Z_().optional().nullable(),customerId:d.Z_().min(1,"Customer is required").optional(),quotationId:d.Z_().optional().nullable(),invoiceId:d.Z_().optional().nullable(),type:d.Km(["SERVICE","PRODUCT","SUBSCRIPTION","MAINTENANCE","CONSULTING","OTHER"]).optional(),status:d.Km(["DRAFT","REVIEW","SENT","SIGNED","ACTIVE","COMPLETED","CANCELLED","EXPIRED"]).optional(),value:d.Rx().min(0,"Contract value must be positive").optional().nullable(),currency:d.Z_().optional(),startDate:d.Z_().optional().nullable(),endDate:d.Z_().optional().nullable(),renewalDate:d.Z_().optional().nullable(),autoRenewal:d.O7().optional(),renewalPeriod:d.Rx().optional().nullable(),terms:d.Z_().optional().nullable(),conditions:d.Z_().optional().nullable(),notes:d.Z_().optional().nullable(),templateId:d.Z_().optional().nullable(),signatureRequired:d.O7().optional(),priority:d.Km(["LOW","MEDIUM","HIGH","URGENT"]).optional(),tags:d.IX(d.Z_()).optional(),assignedToId:d.Z_().optional().nullable()});async function y(e,{params:t}){try{let e=await (0,l.getServerSession)(c.L);if(!e?.user?.id)return i.Z.json({error:"Unauthorized"},{status:401});let r=await u._.contract.findFirst({where:{id:t.id,companyId:e.user.companyId||void 0},include:{customer:{select:{id:!0,name:!0,email:!0,company:!0,phone:!0,address:!0,city:!0,state:!0,country:!0,postalCode:!0}},quotation:{select:{id:!0,quotationNumber:!0,title:!0,total:!0}},invoice:{select:{id:!0,invoiceNumber:!0,total:!0,status:!0}},template:{select:{id:!0,name:!0,type:!0,content:!0}},createdBy:{select:{name:!0,email:!0}},assignedTo:{select:{name:!0,email:!0}},signatures:{orderBy:{createdAt:"desc"},include:{signedBy:{select:{name:!0,email:!0}}}},documents:{orderBy:{createdAt:"desc"},include:{uploadedBy:{select:{name:!0}}}},activities:{orderBy:{createdAt:"desc"},take:20,include:{createdBy:{select:{name:!0}}}},_count:{select:{activities:!0,signatures:!0,documents:!0}}}});if(!r)return i.Z.json({error:"Contract not found"},{status:404});return i.Z.json(r)}catch(e){return console.error("Error fetching contract:",e),i.Z.json({error:"Failed to fetch contract"},{status:500})}}async function I(e,{params:t}){try{let r=await (0,l.getServerSession)(c.L);if(!r?.user?.id)return i.Z.json({error:"Unauthorized"},{status:401});let a=await e.json(),n=m.parse(a),o=await u._.contract.findFirst({where:{id:t.id,companyId:r.user.companyId||void 0}});if(!o)return i.Z.json({error:"Contract not found"},{status:404});if("SIGNED"===o.status||"COMPLETED"===o.status)return i.Z.json({error:"Cannot edit signed or completed contracts"},{status:400});let s={...n};n.startDate&&(s.startDate=new Date(n.startDate)),n.endDate&&(s.endDate=new Date(n.endDate)),n.renewalDate&&(s.renewalDate=new Date(n.renewalDate));let d=await u._.$transaction(async e=>{let a=await e.contract.update({where:{id:t.id},data:s,include:{customer:{select:{id:!0,name:!0,email:!0,company:!0}},quotation:{select:{id:!0,quotationNumber:!0,title:!0}},invoice:{select:{id:!0,invoiceNumber:!0}},template:{select:{id:!0,name:!0,type:!0}},createdBy:{select:{name:!0,email:!0}},assignedTo:{select:{name:!0,email:!0}}}});return n.status&&n.status!==o.status&&await e.activity.create({data:{type:"STATUS_CHANGE",title:"Contract Status Updated",description:`Contract status changed from ${o.status} to ${n.status}`,contractId:t.id,customerId:a.customerId,quotationId:a.quotationId,invoiceId:a.invoiceId,companyId:r.user.companyId,createdById:r.user.id}}),a});return i.Z.json(d)}catch(e){if(e instanceof p.jm)return i.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("Error updating contract:",e),i.Z.json({error:"Failed to update contract"},{status:500})}}async function g(e,{params:t}){try{let e=await (0,l.getServerSession)(c.L);if(!e?.user?.id)return i.Z.json({error:"Unauthorized"},{status:401});let r=await u._.contract.findFirst({where:{id:t.id,companyId:e.user.companyId||void 0},include:{_count:{select:{signatures:!0,documents:!0}}}});if(!r)return i.Z.json({error:"Contract not found"},{status:404});if(r._count.signatures>0||r._count.documents>0)return i.Z.json({error:"Cannot delete contract with existing signatures or documents",details:r._count},{status:400});if("SIGNED"===r.status||"ACTIVE"===r.status)return i.Z.json({error:"Cannot delete signed or active contracts"},{status:400});return await u._.contract.delete({where:{id:t.id}}),i.Z.json({message:"Contract deleted successfully"})}catch(e){return console.error("Error deleting contract:",e),i.Z.json({error:"Failed to delete contract"},{status:500})}}let w=new n.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/contracts/[id]/route",pathname:"/api/contracts/[id]",filename:"route",bundlePath:"app/api/contracts/[id]/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\contracts\\[id]\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:E,staticGenerationAsyncStorage:f,serverHooks:h,headerHooks:_,staticGenerationBailout:v}=w,C="/api/contracts/[id]/route";function Z(){return(0,s.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:f})}},3205:(e,t,r)=>{r.d(t,{L:()=>c});var a=r(86485),n=r(10375),o=r(50694),s=r(6521),i=r.n(s),l=r(9108);let c={providers:[(0,a.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await l._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),r=t?.companyId;if(!r&&t){let e=await l._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(r=e?.id)&&await l._.user.update({where:{id:t.id},data:{companyId:r}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await i().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await l._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:r}}catch(e){return console.error("Authentication error:",e),null}}}),(0,n.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,o.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,r)=>{r.d(t,{_:()=>n});let a=require("@prisma/client"),n=globalThis.prisma??new a.PrismaClient}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,6206,6521,2455,4520,5252],()=>r(61092));module.exports=a})();