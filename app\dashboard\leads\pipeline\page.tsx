'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Search,
  Filter,
  Plus,
  Mail,
  Phone,
  Building2,
  DollarSign,
  Calendar,
  User,
  TrendingUp,
  AlertCircle,
  Target,
  Eye
} from 'lucide-react'
import { toast } from 'react-hot-toast'
import Link from 'next/link'

interface Lead {
  id: string
  firstName: string
  lastName: string
  email: string
  phone: string | null
  companyName: string | null
  title: string | null
  source: string
  status: 'NEW' | 'CONTACTED' | 'QUALIFIED' | 'PROPOSAL' | 'NEGOTIATION' | 'CLOSED_WON' | 'CLOSED_LOST'
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
  budget: number | null
  score: number
  createdAt: string
  assignedTo: {
    id: string
    name: string | null
    email: string
  } | null
}

interface PipelineColumn {
  id: string
  title: string
  status: Lead['status']
  color: string
  leads: Lead[]
  count: number
}

const PIPELINE_STAGES: Omit<PipelineColumn, 'leads' | 'count'>[] = [
  { id: 'new', title: 'New Leads', status: 'NEW', color: 'bg-gray-100 border-gray-300' },
  { id: 'contacted', title: 'Contacted', status: 'CONTACTED', color: 'bg-blue-100 border-blue-300' },
  { id: 'qualified', title: 'Qualified', status: 'QUALIFIED', color: 'bg-green-100 border-green-300' },
  { id: 'proposal', title: 'Proposal', status: 'PROPOSAL', color: 'bg-yellow-100 border-yellow-300' },
  { id: 'negotiation', title: 'Negotiation', status: 'NEGOTIATION', color: 'bg-orange-100 border-orange-300' },
  { id: 'won', title: 'Closed Won', status: 'CLOSED_WON', color: 'bg-green-100 border-green-500' },
  { id: 'lost', title: 'Closed Lost', status: 'CLOSED_LOST', color: 'bg-red-100 border-red-300' }
]

export default function LeadPipelinePage() {
  const { data: session } = useSession()
  const [columns, setColumns] = useState<PipelineColumn[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [sourceFilter, setSourceFilter] = useState<string>('all')
  const [priorityFilter, setPriorityFilter] = useState<string>('all')
  const [assigneeFilter, setAssigneeFilter] = useState<string>('all')

  const fetchLeads = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        limit: '1000',
        ...(searchTerm && { search: searchTerm }),
        ...(sourceFilter !== 'all' && { source: sourceFilter }),
        ...(priorityFilter !== 'all' && { priority: priorityFilter }),
        ...(assigneeFilter !== 'all' && { assignedTo: assigneeFilter })
      })

      const response = await fetch(`/api/leads?${params}`)
      if (!response.ok) {
        throw new Error('Failed to fetch leads')
      }

      const data = await response.json()
      
      // Group leads by status
      const groupedLeads = PIPELINE_STAGES.map(stage => ({
        ...stage,
        leads: data.leads.filter((lead: Lead) => lead.status === stage.status),
        count: data.leads.filter((lead: Lead) => lead.status === stage.status).length
      }))

      setColumns(groupedLeads)
    } catch (error) {
      toast.error('Failed to load pipeline data')
      console.error('Error fetching leads:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (session?.user?.companyId) {
      fetchLeads()
    }
  }, [session?.user?.companyId, searchTerm, sourceFilter, priorityFilter, assigneeFilter])

  const handleDragEnd = async (result: DropResult) => {
    const { destination, source, draggableId } = result

    // If dropped outside a droppable area
    if (!destination) return

    // If dropped in the same position
    if (destination.droppableId === source.droppableId && destination.index === source.index) {
      return
    }

    const sourceColumn = columns.find(col => col.id === source.droppableId)
    const destColumn = columns.find(col => col.id === destination.droppableId)

    if (!sourceColumn || !destColumn) return

    const draggedLead = sourceColumn.leads.find(lead => lead.id === draggableId)
    if (!draggedLead) return

    // Update lead status
    try {
      const response = await fetch(`/api/leads/${draggableId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: destColumn.status
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to update lead status')
      }

      // Update local state
      const newColumns = columns.map(column => {
        if (column.id === source.droppableId) {
          // Remove from source column
          return {
            ...column,
            leads: column.leads.filter(lead => lead.id !== draggableId),
            count: column.count - 1
          }
        } else if (column.id === destination.droppableId) {
          // Add to destination column
          const updatedLead = { ...draggedLead, status: destColumn.status }
          const newLeads = [...column.leads]
          newLeads.splice(destination.index, 0, updatedLead)
          return {
            ...column,
            leads: newLeads,
            count: column.count + 1
          }
        }
        return column
      })

      setColumns(newColumns)
      toast.success(`Lead moved to ${destColumn.title}`)
    } catch (error) {
      toast.error('Failed to update lead status')
      console.error('Error updating lead:', error)
    }
  }

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'LOW':
        return <Badge variant="secondary" className="text-xs">Low</Badge>
      case 'MEDIUM':
        return <Badge className="bg-blue-100 text-blue-800 text-xs">Medium</Badge>
      case 'HIGH':
        return <Badge className="bg-orange-100 text-orange-800 text-xs">High</Badge>
      case 'URGENT':
        return <Badge variant="destructive" className="text-xs">Urgent</Badge>
      default:
        return <Badge variant="secondary" className="text-xs">{priority}</Badge>
    }
  }

  const getSourceColor = (source: string) => {
    switch (source) {
      case 'WEBSITE': return 'text-blue-600'
      case 'REFERRAL': return 'text-green-600'
      case 'SOCIAL_MEDIA': return 'text-purple-600'
      case 'EMAIL_CAMPAIGN': return 'text-orange-600'
      case 'COLD_CALL': return 'text-red-600'
      case 'TRADE_SHOW': return 'text-indigo-600'
      case 'PARTNER': return 'text-pink-600'
      default: return 'text-gray-600'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Loading Pipeline</h3>
          <p className="text-gray-500">Please wait while we fetch your leads...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Lead Pipeline</h1>
          <p className="text-gray-500">Drag and drop leads through your sales pipeline</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button asChild>
            <Link href="/dashboard/leads/new">
              <Plus className="h-4 w-4 mr-2" />
              Add Lead
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/dashboard/leads">
              <TrendingUp className="h-4 w-4 mr-2" />
              List View
            </Link>
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-wrap items-center gap-4">
            <div className="flex-1 min-w-64">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search leads..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={sourceFilter} onValueChange={setSourceFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Source" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Sources</SelectItem>
                <SelectItem value="WEBSITE">Website</SelectItem>
                <SelectItem value="REFERRAL">Referral</SelectItem>
                <SelectItem value="SOCIAL_MEDIA">Social Media</SelectItem>
                <SelectItem value="EMAIL_CAMPAIGN">Email Campaign</SelectItem>
                <SelectItem value="COLD_CALL">Cold Call</SelectItem>
                <SelectItem value="TRADE_SHOW">Trade Show</SelectItem>
                <SelectItem value="PARTNER">Partner</SelectItem>
                <SelectItem value="OTHER">Other</SelectItem>
              </SelectContent>
            </Select>
            <Select value={priorityFilter} onValueChange={setPriorityFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Priorities</SelectItem>
                <SelectItem value="LOW">Low</SelectItem>
                <SelectItem value="MEDIUM">Medium</SelectItem>
                <SelectItem value="HIGH">High</SelectItem>
                <SelectItem value="URGENT">Urgent</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Pipeline Statistics */}
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
        {columns.map((column) => (
          <Card key={column.id} className={`${column.color} border-2`}>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-gray-900">{column.count}</div>
              <div className="text-sm text-gray-600">{column.title}</div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Kanban Board */}
      <DragDropContext onDragEnd={handleDragEnd}>
        <div className="flex space-x-4 overflow-x-auto pb-4">
          {columns.map((column) => (
            <div key={column.id} className="flex-shrink-0 w-80">
              <Card className={`${column.color} border-2`}>
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center justify-between text-sm font-medium">
                    <span>{column.title}</span>
                    <Badge variant="secondary" className="text-xs">
                      {column.count}
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <Droppable droppableId={column.id}>
                  {(provided, snapshot) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.droppableProps}
                      className={`min-h-96 p-2 space-y-2 ${
                        snapshot.isDraggingOver ? 'bg-gray-50' : ''
                      }`}
                    >
                      {column.leads.map((lead, index) => (
                        <Draggable key={lead.id} draggableId={lead.id} index={index}>
                          {(provided, snapshot) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                              className={`${
                                snapshot.isDragging ? 'rotate-2 shadow-lg' : ''
                              }`}
                            >
                              <LeadCard lead={lead} />
                            </div>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                      {column.leads.length === 0 && (
                        <div className="text-center py-8 text-gray-400">
                          <TrendingUp className="h-8 w-8 mx-auto mb-2" />
                          <p className="text-sm">No leads in this stage</p>
                        </div>
                      )}
                    </div>
                  )}
                </Droppable>
              </Card>
            </div>
          ))}
        </div>
      </DragDropContext>
    </div>
  )
}

// Lead Card Component
function LeadCard({ lead }: { lead: Lead }) {
  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'LOW':
        return <Badge variant="secondary" className="text-xs">Low</Badge>
      case 'MEDIUM':
        return <Badge className="bg-blue-100 text-blue-800 text-xs">Medium</Badge>
      case 'HIGH':
        return <Badge className="bg-orange-100 text-orange-800 text-xs">High</Badge>
      case 'URGENT':
        return <Badge variant="destructive" className="text-xs">Urgent</Badge>
      default:
        return <Badge variant="secondary" className="text-xs">{priority}</Badge>
    }
  }

  const getSourceColor = (source: string) => {
    switch (source) {
      case 'WEBSITE': return 'text-blue-600'
      case 'REFERRAL': return 'text-green-600'
      case 'SOCIAL_MEDIA': return 'text-purple-600'
      case 'EMAIL_CAMPAIGN': return 'text-orange-600'
      case 'COLD_CALL': return 'text-red-600'
      case 'TRADE_SHOW': return 'text-indigo-600'
      case 'PARTNER': return 'text-pink-600'
      default: return 'text-gray-600'
    }
  }

  return (
    <Card className="bg-white hover:shadow-md transition-shadow cursor-pointer">
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Header */}
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h3 className="font-medium text-sm text-gray-900 truncate">
                {lead.firstName} {lead.lastName}
              </h3>
              {lead.companyName && (
                <p className="text-xs text-gray-500 truncate">{lead.companyName}</p>
              )}
              {lead.title && (
                <p className="text-xs text-gray-400 truncate">{lead.title}</p>
              )}
            </div>
            <div className="flex items-center space-x-1">
              {getPriorityBadge(lead.priority)}
              <Button variant="ghost" size="sm" className="h-6 w-6 p-0" asChild>
                <Link href={`/dashboard/leads/${lead.id}`}>
                  <Eye className="h-3 w-3" />
                </Link>
              </Button>
            </div>
          </div>

          {/* Contact Info */}
          <div className="space-y-1">
            <div className="flex items-center space-x-2 text-xs text-gray-600">
              <Mail className="h-3 w-3" />
              <span className="truncate">{lead.email}</span>
            </div>
            {lead.phone && (
              <div className="flex items-center space-x-2 text-xs text-gray-600">
                <Phone className="h-3 w-3" />
                <span>{lead.phone}</span>
              </div>
            )}
          </div>

          {/* Metrics */}
          <div className="flex items-center justify-between text-xs">
            <div className="flex items-center space-x-2">
              <Target className="h-3 w-3 text-blue-600" />
              <span className="text-gray-600">Score: {lead.score}/100</span>
            </div>
            {lead.budget && (
              <div className="flex items-center space-x-1">
                <DollarSign className="h-3 w-3 text-green-600" />
                <span className="text-gray-600">${lead.budget.toLocaleString()}</span>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between pt-2 border-t border-gray-100">
            <div className={`text-xs ${getSourceColor(lead.source)}`}>
              {lead.source.replace('_', ' ')}
            </div>
            <div className="text-xs text-gray-400">
              {new Date(lead.createdAt).toLocaleDateString()}
            </div>
          </div>

          {/* Assigned To */}
          {lead.assignedTo && (
            <div className="flex items-center space-x-2 text-xs text-gray-500">
              <User className="h-3 w-3" />
              <span className="truncate">{lead.assignedTo.name}</span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
