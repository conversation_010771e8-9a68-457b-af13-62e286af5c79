(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4290],{90998:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]])},11981:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},73067:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},68291:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},92457:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},98253:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Building2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},28203:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},13008:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},99155:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("CheckSquare",[["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}],["path",{d:"M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11",key:"1jnkn4"}]])},6141:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},41298:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},76637:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]])},81016:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Flame",[["path",{d:"M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z",key:"96xj49"}]])},28956:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},38244:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]])},8759:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]])},1295:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},2882:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},17472:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("MoreHorizontal",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},49617:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},12741:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},9883:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},92295:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},3928:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Snowflake",[["line",{x1:"2",x2:"22",y1:"12",y2:"12",key:"1dnqot"}],["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"m20 16-4-4 4-4",key:"rquw4f"}],["path",{d:"m4 8 4 4-4 4",key:"12s3z9"}],["path",{d:"m16 4-4 4-4-4",key:"1tumq1"}],["path",{d:"m8 20 4-4 4 4",key:"9p200w"}]])},44135:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},33673:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Tag",[["path",{d:"M12 2H2v10l9.29 9.29c.94.94 2.48.94 3.42 0l6.58-6.58c.94-.94.94-2.48 0-3.42L12 2Z",key:"14b2ls"}],["path",{d:"M7 7h.01",key:"7u93v4"}]])},66654:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},89275:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Thermometer",[["path",{d:"M14 4v10.54a4 4 0 1 1-4 0V4a2 2 0 0 1 4 0Z",key:"17jzev"}]])},45367:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},74522:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("TrendingDown",[["polyline",{points:"22 17 13.5 8.5 8.5 13.5 2 7",key:"1r2t7k"}],["polyline",{points:"16 17 22 17 22 11",key:"11uiuu"}]])},85790:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},52431:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("UserCheck",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]])},67972:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},82104:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},24033:function(e,t,n){e.exports=n(15313)},66062:function(e,t,n){"use strict";n.d(t,{fC:function(){return Z},z$:function(){return b}});var r=n(2265),i=n(42210),o=n(56989),c=n(85744),a=n(73763),u=n(85184),l=n(94977),s=n(85606),d=n(9381),f=n(57437),y="Checkbox",[p,h]=(0,o.b)(y),[k,x]=p(y);function v(e){let{__scopeCheckbox:t,checked:n,children:i,defaultChecked:o,disabled:c,form:u,name:l,onCheckedChange:s,required:d,value:p="on",internal_do_not_use_render:h}=e,[x,v]=(0,a.T)({prop:n,defaultProp:o??!1,onChange:s,caller:y}),[g,m]=r.useState(null),[Z,M]=r.useState(null),b=r.useRef(!1),w=!g||!!u||!!g.closest("form"),j={checked:x,disabled:c,setChecked:v,control:g,setControl:m,name:l,form:u,value:p,hasConsumerStoppedPropagationRef:b,required:d,defaultChecked:!C(o)&&o,isFormControl:w,bubbleInput:Z,setBubbleInput:M};return(0,f.jsx)(k,{scope:t,...j,children:"function"==typeof h?h(j):i})}var g="CheckboxTrigger",m=r.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:n,...o},a)=>{let{control:u,value:l,disabled:s,checked:y,required:p,setControl:h,setChecked:k,hasConsumerStoppedPropagationRef:v,isFormControl:m,bubbleInput:Z}=x(g,e),M=(0,i.e)(a,h),b=r.useRef(y);return r.useEffect(()=>{let e=u?.form;if(e){let t=()=>k(b.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[u,k]),(0,f.jsx)(d.WV.button,{type:"button",role:"checkbox","aria-checked":C(y)?"mixed":y,"aria-required":p,"data-state":D(y),"data-disabled":s?"":void 0,disabled:s,value:l,...o,ref:M,onKeyDown:(0,c.M)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,c.M)(n,e=>{k(e=>!!C(e)||!e),Z&&m&&(v.current=e.isPropagationStopped(),v.current||e.stopPropagation())})})});m.displayName=g;var Z=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,name:r,checked:i,defaultChecked:o,required:c,disabled:a,value:u,onCheckedChange:l,form:s,...d}=e;return(0,f.jsx)(v,{__scopeCheckbox:n,checked:i,defaultChecked:o,disabled:a,required:c,onCheckedChange:l,name:r,form:s,value:u,internal_do_not_use_render:({isFormControl:e})=>(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(m,{...d,ref:t,__scopeCheckbox:n}),e&&(0,f.jsx)(j,{__scopeCheckbox:n})]})})});Z.displayName=y;var M="CheckboxIndicator",b=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,forceMount:r,...i}=e,o=x(M,n);return(0,f.jsx)(s.z,{present:r||C(o.checked)||!0===o.checked,children:(0,f.jsx)(d.WV.span,{"data-state":D(o.checked),"data-disabled":o.disabled?"":void 0,...i,ref:t,style:{pointerEvents:"none",...e.style}})})});b.displayName=M;var w="CheckboxBubbleInput",j=r.forwardRef(({__scopeCheckbox:e,...t},n)=>{let{control:o,hasConsumerStoppedPropagationRef:c,checked:a,defaultChecked:s,required:y,disabled:p,name:h,value:k,form:v,bubbleInput:g,setBubbleInput:m}=x(w,e),Z=(0,i.e)(n,m),M=(0,u.D)(a),b=(0,l.t)(o);r.useEffect(()=>{if(!g)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!c.current;if(M!==a&&e){let n=new Event("click",{bubbles:t});g.indeterminate=C(a),e.call(g,!C(a)&&a),g.dispatchEvent(n)}},[g,M,a,c]);let j=r.useRef(!C(a)&&a);return(0,f.jsx)(d.WV.input,{type:"checkbox","aria-hidden":!0,defaultChecked:s??j.current,required:y,disabled:p,name:h,value:k,form:v,...t,tabIndex:-1,ref:Z,style:{...t.style,...b,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function C(e){return"indeterminate"===e}function D(e){return C(e)?"indeterminate":e?"checked":"unchecked"}j.displayName=w},28712:function(e,t,n){"use strict";n.d(t,{Dx:function(){return er},VY:function(){return en},aV:function(){return et},dk:function(){return ei},fC:function(){return J},h_:function(){return ee},x8:function(){return eo},xz:function(){return Q}});var r=n(2265),i=n(85744),o=n(42210),c=n(56989),a=n(20966),u=n(73763),l=n(79249),s=n(52759),d=n(52730),f=n(85606),y=n(9381),p=n(31244),h=n(73386),k=n(85859),x=n(67256),v=n(57437),g="Dialog",[m,Z]=(0,c.b)(g),[M,b]=m(g),w=e=>{let{__scopeDialog:t,children:n,open:i,defaultOpen:o,onOpenChange:c,modal:l=!0}=e,s=r.useRef(null),d=r.useRef(null),[f,y]=(0,u.T)({prop:i,defaultProp:o??!1,onChange:c,caller:g});return(0,v.jsx)(M,{scope:t,triggerRef:s,contentRef:d,contentId:(0,a.M)(),titleId:(0,a.M)(),descriptionId:(0,a.M)(),open:f,onOpenChange:y,onOpenToggle:r.useCallback(()=>y(e=>!e),[y]),modal:l,children:n})};w.displayName=g;var j="DialogTrigger",C=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,c=b(j,n),a=(0,o.e)(t,c.triggerRef);return(0,v.jsx)(y.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":c.open,"aria-controls":c.contentId,"data-state":$(c.open),...r,ref:a,onClick:(0,i.M)(e.onClick,c.onOpenToggle)})});C.displayName=j;var D="DialogPortal",[R,z]=m(D,{forceMount:void 0}),q=e=>{let{__scopeDialog:t,forceMount:n,children:i,container:o}=e,c=b(D,t);return(0,v.jsx)(R,{scope:t,forceMount:n,children:r.Children.map(i,e=>(0,v.jsx)(f.z,{present:n||c.open,children:(0,v.jsx)(d.h,{asChild:!0,container:o,children:e})}))})};q.displayName=D;var E="DialogOverlay",I=r.forwardRef((e,t)=>{let n=z(E,e.__scopeDialog),{forceMount:r=n.forceMount,...i}=e,o=b(E,e.__scopeDialog);return o.modal?(0,v.jsx)(f.z,{present:r||o.open,children:(0,v.jsx)(_,{...i,ref:t})}):null});I.displayName=E;var V=(0,x.Z8)("DialogOverlay.RemoveScroll"),_=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=b(E,n);return(0,v.jsx)(h.Z,{as:V,allowPinchZoom:!0,shards:[i.contentRef],children:(0,v.jsx)(y.WV.div,{"data-state":$(i.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),N="DialogContent",H=r.forwardRef((e,t)=>{let n=z(N,e.__scopeDialog),{forceMount:r=n.forceMount,...i}=e,o=b(N,e.__scopeDialog);return(0,v.jsx)(f.z,{present:r||o.open,children:o.modal?(0,v.jsx)(O,{...i,ref:t}):(0,v.jsx)(F,{...i,ref:t})})});H.displayName=N;var O=r.forwardRef((e,t)=>{let n=b(N,e.__scopeDialog),c=r.useRef(null),a=(0,o.e)(t,n.contentRef,c);return r.useEffect(()=>{let e=c.current;if(e)return(0,k.Ry)(e)},[]),(0,v.jsx)(P,{...e,ref:a,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,i.M)(e.onCloseAutoFocus,e=>{e.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:(0,i.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,i.M)(e.onFocusOutside,e=>e.preventDefault())})}),F=r.forwardRef((e,t)=>{let n=b(N,e.__scopeDialog),i=r.useRef(!1),o=r.useRef(!1);return(0,v.jsx)(P,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(i.current||n.triggerRef.current?.focus(),t.preventDefault()),i.current=!1,o.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(i.current=!0,"pointerdown"!==t.detail.originalEvent.type||(o.current=!0));let r=t.target;n.triggerRef.current?.contains(r)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),P=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:i,onOpenAutoFocus:c,onCloseAutoFocus:a,...u}=e,d=b(N,n),f=r.useRef(null),y=(0,o.e)(t,f);return(0,p.EW)(),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(s.M,{asChild:!0,loop:!0,trapped:i,onMountAutoFocus:c,onUnmountAutoFocus:a,children:(0,v.jsx)(l.XB,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":$(d.open),...u,ref:y,onDismiss:()=>d.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(G,{titleId:d.titleId}),(0,v.jsx)(Y,{contentRef:f,descriptionId:d.descriptionId})]})]})}),T="DialogTitle",A=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=b(T,n);return(0,v.jsx)(y.WV.h2,{id:i.titleId,...r,ref:t})});A.displayName=T;var L="DialogDescription",S=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=b(L,n);return(0,v.jsx)(y.WV.p,{id:i.descriptionId,...r,ref:t})});S.displayName=L;var W="DialogClose",B=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=b(W,n);return(0,v.jsx)(y.WV.button,{type:"button",...r,ref:t,onClick:(0,i.M)(e.onClick,()=>o.onOpenChange(!1))})});function $(e){return e?"open":"closed"}B.displayName=W;var U="DialogTitleWarning",[X,K]=(0,c.k)(U,{contentName:N,titleName:T,docsSlug:"dialog"}),G=({titleId:e})=>{let t=K(U),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return r.useEffect(()=>{e&&!document.getElementById(e)&&console.error(n)},[n,e]),null},Y=({contentRef:e,descriptionId:t})=>{let n=K("DialogDescriptionWarning"),i=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${n.contentName}}.`;return r.useEffect(()=>{let n=e.current?.getAttribute("aria-describedby");t&&n&&!document.getElementById(t)&&console.warn(i)},[i,e,t]),null},J=w,Q=C,ee=q,et=I,en=H,er=A,ei=S,eo=B}}]);