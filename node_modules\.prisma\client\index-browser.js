
Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 5.22.0
 * Query Engine version: 605197351a3c8bdd595af2d2a9bc3025bca48ea2
 */
Prisma.prismaVersion = {
  client: "5.22.0",
  engine: "605197351a3c8bdd595af2d2a9bc3025bca48ea2"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.NotFoundError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`NotFoundError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.AccountScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  type: 'type',
  provider: 'provider',
  providerAccountId: 'providerAccountId',
  refresh_token: 'refresh_token',
  access_token: 'access_token',
  expires_at: 'expires_at',
  token_type: 'token_type',
  scope: 'scope',
  id_token: 'id_token',
  session_state: 'session_state'
};

exports.Prisma.SessionScalarFieldEnum = {
  id: 'id',
  sessionToken: 'sessionToken',
  userId: 'userId',
  expires: 'expires'
};

exports.Prisma.VerificationTokenScalarFieldEnum = {
  identifier: 'identifier',
  token: 'token',
  expires: 'expires'
};

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  email: 'email',
  password: 'password',
  name: 'name',
  firstName: 'firstName',
  lastName: 'lastName',
  avatar: 'avatar',
  phone: 'phone',
  role: 'role',
  status: 'status',
  title: 'title',
  department: 'department',
  bio: 'bio',
  timezone: 'timezone',
  language: 'language',
  emailVerified: 'emailVerified',
  emailVerificationToken: 'emailVerificationToken',
  passwordResetToken: 'passwordResetToken',
  passwordResetExpires: 'passwordResetExpires',
  twoFactorEnabled: 'twoFactorEnabled',
  twoFactorSecret: 'twoFactorSecret',
  lastLoginAt: 'lastLoginAt',
  loginCount: 'loginCount',
  lastActiveAt: 'lastActiveAt',
  companyId: 'companyId',
  permissions: 'permissions',
  settings: 'settings',
  preferences: 'preferences',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CompanyScalarFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  phone: 'phone',
  address: 'address',
  city: 'city',
  state: 'state',
  country: 'country',
  postalCode: 'postalCode',
  website: 'website',
  logo: 'logo',
  industry: 'industry',
  size: 'size',
  businessType: 'businessType',
  taxId: 'taxId',
  registrationNumber: 'registrationNumber',
  settings: 'settings',
  branding: 'branding',
  theme: 'theme',
  subscriptionId: 'subscriptionId',
  ownerId: 'ownerId',
  status: 'status',
  maxUsers: 'maxUsers',
  maxCustomers: 'maxCustomers',
  maxQuotations: 'maxQuotations',
  maxInvoices: 'maxInvoices',
  maxContracts: 'maxContracts',
  maxStorage: 'maxStorage',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SubscriptionScalarFieldEnum = {
  id: 'id',
  plan: 'plan',
  status: 'status',
  priceId: 'priceId',
  customerId: 'customerId',
  subscriptionId: 'subscriptionId',
  startDate: 'startDate',
  endDate: 'endDate',
  trialEndDate: 'trialEndDate',
  cancelledAt: 'cancelledAt',
  billingCycle: 'billingCycle',
  amount: 'amount',
  currency: 'currency',
  features: 'features',
  limits: 'limits',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.LeadScalarFieldEnum = {
  id: 'id',
  firstName: 'firstName',
  lastName: 'lastName',
  email: 'email',
  phone: 'phone',
  companyName: 'companyName',
  title: 'title',
  website: 'website',
  source: 'source',
  status: 'status',
  priority: 'priority',
  address: 'address',
  city: 'city',
  state: 'state',
  country: 'country',
  postalCode: 'postalCode',
  industry: 'industry',
  companySize: 'companySize',
  budget: 'budget',
  timeline: 'timeline',
  score: 'score',
  qualified: 'qualified',
  qualifiedAt: 'qualifiedAt',
  convertedAt: 'convertedAt',
  customerId: 'customerId',
  assignedToId: 'assignedToId',
  createdById: 'createdById',
  companyId: 'companyId',
  description: 'description',
  tags: 'tags',
  customFields: 'customFields',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CustomerScalarFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  phone: 'phone',
  companyName: 'companyName',
  title: 'title',
  website: 'website',
  address: 'address',
  city: 'city',
  state: 'state',
  country: 'country',
  postalCode: 'postalCode',
  industry: 'industry',
  companySize: 'companySize',
  taxId: 'taxId',
  status: 'status',
  priority: 'priority',
  creditLimit: 'creditLimit',
  paymentTerms: 'paymentTerms',
  assignedToId: 'assignedToId',
  createdById: 'createdById',
  companyId: 'companyId',
  description: 'description',
  tags: 'tags',
  customFields: 'customFields',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ItemScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  sku: 'sku',
  category: 'category',
  unitPrice: 'unitPrice',
  costPrice: 'costPrice',
  currency: 'currency',
  trackInventory: 'trackInventory',
  stockQuantity: 'stockQuantity',
  lowStockAlert: 'lowStockAlert',
  taxable: 'taxable',
  taxRate: 'taxRate',
  accountingCode: 'accountingCode',
  active: 'active',
  companyId: 'companyId',
  tags: 'tags',
  customFields: 'customFields',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.QuotationScalarFieldEnum = {
  id: 'id',
  quotationNumber: 'quotationNumber',
  title: 'title',
  description: 'description',
  customerId: 'customerId',
  status: 'status',
  validUntil: 'validUntil',
  acceptedAt: 'acceptedAt',
  rejectedAt: 'rejectedAt',
  subtotal: 'subtotal',
  taxRate: 'taxRate',
  taxAmount: 'taxAmount',
  discountType: 'discountType',
  discountValue: 'discountValue',
  discountAmount: 'discountAmount',
  total: 'total',
  currency: 'currency',
  terms: 'terms',
  internalNotes: 'internalNotes',
  paymentTerms: 'paymentTerms',
  assignedToId: 'assignedToId',
  createdById: 'createdById',
  companyId: 'companyId',
  tags: 'tags',
  customFields: 'customFields',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.QuotationItemScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  quantity: 'quantity',
  unitPrice: 'unitPrice',
  discount: 'discount',
  taxRate: 'taxRate',
  total: 'total',
  itemId: 'itemId',
  quotationId: 'quotationId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.InvoiceScalarFieldEnum = {
  id: 'id',
  invoiceNumber: 'invoiceNumber',
  title: 'title',
  description: 'description',
  customerId: 'customerId',
  quotationId: 'quotationId',
  status: 'status',
  issueDate: 'issueDate',
  dueDate: 'dueDate',
  paidAt: 'paidAt',
  subtotal: 'subtotal',
  taxRate: 'taxRate',
  taxAmount: 'taxAmount',
  discountType: 'discountType',
  discountValue: 'discountValue',
  discountAmount: 'discountAmount',
  total: 'total',
  paidAmount: 'paidAmount',
  balanceAmount: 'balanceAmount',
  currency: 'currency',
  terms: 'terms',
  internalNotes: 'internalNotes',
  paymentTerms: 'paymentTerms',
  paymentMethod: 'paymentMethod',
  assignedToId: 'assignedToId',
  createdById: 'createdById',
  companyId: 'companyId',
  tags: 'tags',
  customFields: 'customFields',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.InvoiceItemScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  quantity: 'quantity',
  unitPrice: 'unitPrice',
  discount: 'discount',
  taxRate: 'taxRate',
  total: 'total',
  itemId: 'itemId',
  invoiceId: 'invoiceId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ContractScalarFieldEnum = {
  id: 'id',
  contractNumber: 'contractNumber',
  title: 'title',
  description: 'description',
  type: 'type',
  customerId: 'customerId',
  quotationId: 'quotationId',
  invoiceId: 'invoiceId',
  status: 'status',
  priority: 'priority',
  startDate: 'startDate',
  endDate: 'endDate',
  renewalDate: 'renewalDate',
  autoRenewal: 'autoRenewal',
  renewalPeriod: 'renewalPeriod',
  signedAt: 'signedAt',
  value: 'value',
  currency: 'currency',
  terms: 'terms',
  conditions: 'conditions',
  internalNotes: 'internalNotes',
  signatureRequired: 'signatureRequired',
  assignedToId: 'assignedToId',
  createdById: 'createdById',
  companyId: 'companyId',
  tags: 'tags',
  customFields: 'customFields',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TaskScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  status: 'status',
  priority: 'priority',
  dueDate: 'dueDate',
  startDate: 'startDate',
  completedAt: 'completedAt',
  assignedToId: 'assignedToId',
  createdById: 'createdById',
  companyId: 'companyId',
  leadId: 'leadId',
  customerId: 'customerId',
  quotationId: 'quotationId',
  invoiceId: 'invoiceId',
  contractId: 'contractId',
  tags: 'tags',
  customFields: 'customFields',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ActivityScalarFieldEnum = {
  id: 'id',
  type: 'type',
  title: 'title',
  description: 'description',
  createdById: 'createdById',
  companyId: 'companyId',
  leadId: 'leadId',
  customerId: 'customerId',
  quotationId: 'quotationId',
  invoiceId: 'invoiceId',
  contractId: 'contractId',
  taskId: 'taskId',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.NoteScalarFieldEnum = {
  id: 'id',
  title: 'title',
  content: 'content',
  createdById: 'createdById',
  companyId: 'companyId',
  leadId: 'leadId',
  customerId: 'customerId',
  quotationId: 'quotationId',
  invoiceId: 'invoiceId',
  contractId: 'contractId',
  taskId: 'taskId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DocumentScalarFieldEnum = {
  id: 'id',
  name: 'name',
  originalName: 'originalName',
  type: 'type',
  mimeType: 'mimeType',
  size: 'size',
  url: 'url',
  storageProvider: 'storageProvider',
  storagePath: 'storagePath',
  uploadedById: 'uploadedById',
  companyId: 'companyId',
  leadId: 'leadId',
  customerId: 'customerId',
  quotationId: 'quotationId',
  invoiceId: 'invoiceId',
  contractId: 'contractId',
  taskId: 'taskId',
  tags: 'tags',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SignatureScalarFieldEnum = {
  id: 'id',
  signatureData: 'signatureData',
  signatureType: 'signatureType',
  signerName: 'signerName',
  signerEmail: 'signerEmail',
  signerIp: 'signerIp',
  signedAt: 'signedAt',
  provider: 'provider',
  providerSignatureId: 'providerSignatureId',
  requestedById: 'requestedById',
  companyId: 'companyId',
  quotationId: 'quotationId',
  contractId: 'contractId',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TransactionScalarFieldEnum = {
  id: 'id',
  type: 'type',
  amount: 'amount',
  currency: 'currency',
  paymentMethod: 'paymentMethod',
  paymentStatus: 'paymentStatus',
  reference: 'reference',
  description: 'description',
  notes: 'notes',
  providerId: 'providerId',
  providerData: 'providerData',
  transactionDate: 'transactionDate',
  processedAt: 'processedAt',
  companyId: 'companyId',
  invoiceId: 'invoiceId',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.FinancialAccountScalarFieldEnum = {
  id: 'id',
  name: 'name',
  accountNumber: 'accountNumber',
  accountType: 'accountType',
  accountSubType: 'accountSubType',
  description: 'description',
  balance: 'balance',
  currency: 'currency',
  isActive: 'isActive',
  bankName: 'bankName',
  routingNumber: 'routingNumber',
  accountNumberMask: 'accountNumberMask',
  parentAccountId: 'parentAccountId',
  normalBalance: 'normalBalance',
  taxReportingCode: 'taxReportingCode',
  isSystemAccount: 'isSystemAccount',
  companyId: 'companyId',
  createdById: 'createdById',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AccountTransactionScalarFieldEnum = {
  id: 'id',
  transactionNumber: 'transactionNumber',
  description: 'description',
  reference: 'reference',
  amount: 'amount',
  currency: 'currency',
  transactionDate: 'transactionDate',
  debitAccountId: 'debitAccountId',
  creditAccountId: 'creditAccountId',
  transactionType: 'transactionType',
  status: 'status',
  customerId: 'customerId',
  invoiceId: 'invoiceId',
  quotationId: 'quotationId',
  contractId: 'contractId',
  isReconciled: 'isReconciled',
  reconciledAt: 'reconciledAt',
  reconciledBy: 'reconciledBy',
  companyId: 'companyId',
  createdById: 'createdById',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BudgetScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  startDate: 'startDate',
  endDate: 'endDate',
  budgetType: 'budgetType',
  budgetedAmount: 'budgetedAmount',
  actualAmount: 'actualAmount',
  variance: 'variance',
  status: 'status',
  currency: 'currency',
  isActive: 'isActive',
  accountId: 'accountId',
  companyId: 'companyId',
  createdById: 'createdById',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CompanySettingsScalarFieldEnum = {
  id: 'id',
  companyName: 'companyName',
  companyEmail: 'companyEmail',
  companyPhone: 'companyPhone',
  companyAddress: 'companyAddress',
  companyCity: 'companyCity',
  companyState: 'companyState',
  companyCountry: 'companyCountry',
  companyPostalCode: 'companyPostalCode',
  companyWebsite: 'companyWebsite',
  companyLogo: 'companyLogo',
  industry: 'industry',
  businessType: 'businessType',
  taxId: 'taxId',
  registrationNumber: 'registrationNumber',
  defaultCurrency: 'defaultCurrency',
  taxRate: 'taxRate',
  timezone: 'timezone',
  dateFormat: 'dateFormat',
  timeFormat: 'timeFormat',
  language: 'language',
  primaryColor: 'primaryColor',
  secondaryColor: 'secondaryColor',
  accentColor: 'accentColor',
  fontFamily: 'fontFamily',
  invoicePrefix: 'invoicePrefix',
  quotationPrefix: 'quotationPrefix',
  contractPrefix: 'contractPrefix',
  invoiceNumbering: 'invoiceNumbering',
  defaultPaymentTerms: 'defaultPaymentTerms',
  bankDetails: 'bankDetails',
  paymentMethods: 'paymentMethods',
  emailSettings: 'emailSettings',
  notificationSettings: 'notificationSettings',
  securitySettings: 'securitySettings',
  integrationSettings: 'integrationSettings',
  featureSettings: 'featureSettings',
  customSettings: 'customSettings',
  companyId: 'companyId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserSettingsScalarFieldEnum = {
  id: 'id',
  theme: 'theme',
  language: 'language',
  timezone: 'timezone',
  dateFormat: 'dateFormat',
  timeFormat: 'timeFormat',
  dashboardLayout: 'dashboardLayout',
  defaultView: 'defaultView',
  itemsPerPage: 'itemsPerPage',
  emailNotifications: 'emailNotifications',
  pushNotifications: 'pushNotifications',
  smsNotifications: 'smsNotifications',
  notificationTypes: 'notificationTypes',
  profileVisibility: 'profileVisibility',
  showEmail: 'showEmail',
  showPhone: 'showPhone',
  fontSize: 'fontSize',
  highContrast: 'highContrast',
  reducedMotion: 'reducedMotion',
  autoSave: 'autoSave',
  confirmActions: 'confirmActions',
  shortcuts: 'shortcuts',
  customPreferences: 'customPreferences',
  userId: 'userId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SystemSettingsScalarFieldEnum = {
  id: 'id',
  key: 'key',
  value: 'value',
  category: 'category',
  description: 'description',
  isPublic: 'isPublic',
  isEditable: 'isEditable',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AuditLogScalarFieldEnum = {
  id: 'id',
  action: 'action',
  entityType: 'entityType',
  entityId: 'entityId',
  userId: 'userId',
  userEmail: 'userEmail',
  userRole: 'userRole',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  requestUrl: 'requestUrl',
  requestMethod: 'requestMethod',
  oldValues: 'oldValues',
  newValues: 'newValues',
  metadata: 'metadata',
  severity: 'severity',
  companyId: 'companyId',
  createdAt: 'createdAt'
};

exports.Prisma.SystemLogScalarFieldEnum = {
  id: 'id',
  level: 'level',
  message: 'message',
  source: 'source',
  category: 'category',
  userId: 'userId',
  companyId: 'companyId',
  requestId: 'requestId',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  errorCode: 'errorCode',
  stackTrace: 'stackTrace',
  metadata: 'metadata',
  createdAt: 'createdAt'
};

exports.Prisma.PricingPlanScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  monthlyPrice: 'monthlyPrice',
  yearlyPrice: 'yearlyPrice',
  currency: 'currency',
  maxUsers: 'maxUsers',
  maxCompanies: 'maxCompanies',
  maxCustomers: 'maxCustomers',
  maxQuotations: 'maxQuotations',
  maxInvoices: 'maxInvoices',
  maxContracts: 'maxContracts',
  maxStorage: 'maxStorage',
  features: 'features',
  isActive: 'isActive',
  isPublic: 'isPublic',
  sortOrder: 'sortOrder',
  trialDays: 'trialDays',
  stripeProductId: 'stripeProductId',
  stripePriceId: 'stripePriceId',
  stripeYearlyPriceId: 'stripeYearlyPriceId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.NotificationScalarFieldEnum = {
  id: 'id',
  title: 'title',
  message: 'message',
  type: 'type',
  userId: 'userId',
  companyId: 'companyId',
  isGlobal: 'isGlobal',
  isRead: 'isRead',
  readAt: 'readAt',
  actionUrl: 'actionUrl',
  actionText: 'actionText',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SecurityAlertScalarFieldEnum = {
  id: 'id',
  type: 'type',
  severity: 'severity',
  title: 'title',
  description: 'description',
  userId: 'userId',
  companyId: 'companyId',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  requestUrl: 'requestUrl',
  status: 'status',
  resolvedAt: 'resolvedAt',
  resolvedBy: 'resolvedBy',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.FeatureFlagScalarFieldEnum = {
  id: 'id',
  key: 'key',
  name: 'name',
  description: 'description',
  isEnabled: 'isEnabled',
  rolloutPercentage: 'rolloutPercentage',
  targetUsers: 'targetUsers',
  targetCompanies: 'targetCompanies',
  targetRoles: 'targetRoles',
  environment: 'environment',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.UserRole = exports.$Enums.UserRole = {
  SUPER_ADMIN: 'SUPER_ADMIN',
  ADMIN: 'ADMIN',
  MANAGER: 'MANAGER',
  SALES: 'SALES',
  ACCOUNTANT: 'ACCOUNTANT',
  USER: 'USER'
};

exports.UserStatus = exports.$Enums.UserStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  SUSPENDED: 'SUSPENDED',
  PENDING: 'PENDING'
};

exports.CompanySize = exports.$Enums.CompanySize = {
  STARTUP: 'STARTUP',
  SMALL: 'SMALL',
  MEDIUM: 'MEDIUM',
  LARGE: 'LARGE',
  ENTERPRISE: 'ENTERPRISE'
};

exports.CompanyStatus = exports.$Enums.CompanyStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  SUSPENDED: 'SUSPENDED',
  TRIAL: 'TRIAL'
};

exports.SubscriptionPlan = exports.$Enums.SubscriptionPlan = {
  FREE: 'FREE',
  STARTER: 'STARTER',
  PROFESSIONAL: 'PROFESSIONAL',
  ENTERPRISE: 'ENTERPRISE',
  CUSTOM: 'CUSTOM'
};

exports.SubscriptionStatus = exports.$Enums.SubscriptionStatus = {
  ACTIVE: 'ACTIVE',
  CANCELLED: 'CANCELLED',
  EXPIRED: 'EXPIRED',
  TRIAL: 'TRIAL',
  PAST_DUE: 'PAST_DUE'
};

exports.LeadSource = exports.$Enums.LeadSource = {
  WEBSITE: 'WEBSITE',
  REFERRAL: 'REFERRAL',
  SOCIAL_MEDIA: 'SOCIAL_MEDIA',
  EMAIL_CAMPAIGN: 'EMAIL_CAMPAIGN',
  COLD_CALL: 'COLD_CALL',
  TRADE_SHOW: 'TRADE_SHOW',
  ADVERTISEMENT: 'ADVERTISEMENT',
  PARTNER: 'PARTNER',
  OTHER: 'OTHER'
};

exports.LeadStatus = exports.$Enums.LeadStatus = {
  NEW: 'NEW',
  CONTACTED: 'CONTACTED',
  QUALIFIED: 'QUALIFIED',
  PROPOSAL: 'PROPOSAL',
  NEGOTIATION: 'NEGOTIATION',
  CLOSED_WON: 'CLOSED_WON',
  CLOSED_LOST: 'CLOSED_LOST'
};

exports.Priority = exports.$Enums.Priority = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH',
  URGENT: 'URGENT',
  CRITICAL: 'CRITICAL'
};

exports.CustomerStatus = exports.$Enums.CustomerStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  PROSPECT: 'PROSPECT',
  CHURNED: 'CHURNED'
};

exports.QuotationStatus = exports.$Enums.QuotationStatus = {
  DRAFT: 'DRAFT',
  SENT: 'SENT',
  VIEWED: 'VIEWED',
  ACCEPTED: 'ACCEPTED',
  REJECTED: 'REJECTED',
  EXPIRED: 'EXPIRED',
  REVISED: 'REVISED'
};

exports.InvoiceStatus = exports.$Enums.InvoiceStatus = {
  DRAFT: 'DRAFT',
  SENT: 'SENT',
  VIEWED: 'VIEWED',
  PAID: 'PAID',
  PARTIALLY_PAID: 'PARTIALLY_PAID',
  OVERDUE: 'OVERDUE',
  CANCELLED: 'CANCELLED',
  REFUNDED: 'REFUNDED'
};

exports.ContractType = exports.$Enums.ContractType = {
  SERVICE: 'SERVICE',
  PRODUCT: 'PRODUCT',
  SUBSCRIPTION: 'SUBSCRIPTION',
  MAINTENANCE: 'MAINTENANCE',
  CONSULTING: 'CONSULTING',
  LICENSE: 'LICENSE',
  PARTNERSHIP: 'PARTNERSHIP',
  NDA: 'NDA',
  OTHER: 'OTHER'
};

exports.ContractStatus = exports.$Enums.ContractStatus = {
  DRAFT: 'DRAFT',
  REVIEW: 'REVIEW',
  SENT: 'SENT',
  SIGNED: 'SIGNED',
  ACTIVE: 'ACTIVE',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED',
  EXPIRED: 'EXPIRED',
  RENEWED: 'RENEWED'
};

exports.TaskStatus = exports.$Enums.TaskStatus = {
  TODO: 'TODO',
  IN_PROGRESS: 'IN_PROGRESS',
  REVIEW: 'REVIEW',
  DONE: 'DONE',
  CANCELLED: 'CANCELLED'
};

exports.ActivityType = exports.$Enums.ActivityType = {
  NOTE: 'NOTE',
  CALL: 'CALL',
  EMAIL: 'EMAIL',
  MEETING: 'MEETING',
  TASK: 'TASK',
  STATUS_CHANGE: 'STATUS_CHANGE',
  DOCUMENT_UPLOAD: 'DOCUMENT_UPLOAD',
  PAYMENT_RECEIVED: 'PAYMENT_RECEIVED',
  CONTRACT_SIGNED: 'CONTRACT_SIGNED',
  SYSTEM: 'SYSTEM'
};

exports.DocumentType = exports.$Enums.DocumentType = {
  CONTRACT: 'CONTRACT',
  INVOICE: 'INVOICE',
  QUOTATION: 'QUOTATION',
  RECEIPT: 'RECEIPT',
  AGREEMENT: 'AGREEMENT',
  PROPOSAL: 'PROPOSAL',
  REPORT: 'REPORT',
  IMAGE: 'IMAGE',
  OTHER: 'OTHER'
};

exports.TransactionType = exports.$Enums.TransactionType = {
  PAYMENT: 'PAYMENT',
  REFUND: 'REFUND',
  ADJUSTMENT: 'ADJUSTMENT',
  FEE: 'FEE',
  DISCOUNT: 'DISCOUNT'
};

exports.PaymentMethod = exports.$Enums.PaymentMethod = {
  CASH: 'CASH',
  CHECK: 'CHECK',
  BANK_TRANSFER: 'BANK_TRANSFER',
  CREDIT_CARD: 'CREDIT_CARD',
  DEBIT_CARD: 'DEBIT_CARD',
  PAYPAL: 'PAYPAL',
  STRIPE: 'STRIPE',
  CRYPTO: 'CRYPTO',
  OTHER: 'OTHER'
};

exports.PaymentStatus = exports.$Enums.PaymentStatus = {
  PENDING: 'PENDING',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
  CANCELLED: 'CANCELLED',
  REFUNDED: 'REFUNDED'
};

exports.AccountType = exports.$Enums.AccountType = {
  ASSET: 'ASSET',
  LIABILITY: 'LIABILITY',
  EQUITY: 'EQUITY',
  REVENUE: 'REVENUE',
  EXPENSE: 'EXPENSE'
};

exports.BalanceType = exports.$Enums.BalanceType = {
  DEBIT: 'DEBIT',
  CREDIT: 'CREDIT'
};

exports.TransactionStatus = exports.$Enums.TransactionStatus = {
  PENDING: 'PENDING',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED',
  FAILED: 'FAILED'
};

exports.BudgetType = exports.$Enums.BudgetType = {
  MONTHLY: 'MONTHLY',
  QUARTERLY: 'QUARTERLY',
  ANNUAL: 'ANNUAL',
  CUSTOM: 'CUSTOM'
};

exports.BudgetStatus = exports.$Enums.BudgetStatus = {
  DRAFT: 'DRAFT',
  ACTIVE: 'ACTIVE',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED'
};

exports.AuditSeverity = exports.$Enums.AuditSeverity = {
  LOW: 'LOW',
  INFO: 'INFO',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH',
  CRITICAL: 'CRITICAL'
};

exports.LogLevel = exports.$Enums.LogLevel = {
  DEBUG: 'DEBUG',
  INFO: 'INFO',
  WARN: 'WARN',
  ERROR: 'ERROR',
  FATAL: 'FATAL'
};

exports.NotificationType = exports.$Enums.NotificationType = {
  INFO: 'INFO',
  SUCCESS: 'SUCCESS',
  WARNING: 'WARNING',
  ERROR: 'ERROR',
  SECURITY: 'SECURITY',
  SYSTEM: 'SYSTEM'
};

exports.SecurityAlertType = exports.$Enums.SecurityAlertType = {
  FAILED_LOGIN: 'FAILED_LOGIN',
  SUSPICIOUS_ACTIVITY: 'SUSPICIOUS_ACTIVITY',
  DATA_BREACH: 'DATA_BREACH',
  UNAUTHORIZED_ACCESS: 'UNAUTHORIZED_ACCESS',
  MALWARE_DETECTED: 'MALWARE_DETECTED',
  POLICY_VIOLATION: 'POLICY_VIOLATION',
  ACCOUNT_COMPROMISE: 'ACCOUNT_COMPROMISE',
  SYSTEM_INTRUSION: 'SYSTEM_INTRUSION'
};

exports.AlertSeverity = exports.$Enums.AlertSeverity = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH',
  CRITICAL: 'CRITICAL'
};

exports.AlertStatus = exports.$Enums.AlertStatus = {
  OPEN: 'OPEN',
  INVESTIGATING: 'INVESTIGATING',
  RESOLVED: 'RESOLVED',
  FALSE_POSITIVE: 'FALSE_POSITIVE',
  IGNORED: 'IGNORED'
};

exports.Prisma.ModelName = {
  Account: 'Account',
  Session: 'Session',
  VerificationToken: 'VerificationToken',
  User: 'User',
  Company: 'Company',
  Subscription: 'Subscription',
  Lead: 'Lead',
  Customer: 'Customer',
  Item: 'Item',
  Quotation: 'Quotation',
  QuotationItem: 'QuotationItem',
  Invoice: 'Invoice',
  InvoiceItem: 'InvoiceItem',
  Contract: 'Contract',
  Task: 'Task',
  Activity: 'Activity',
  Note: 'Note',
  Document: 'Document',
  Signature: 'Signature',
  Transaction: 'Transaction',
  FinancialAccount: 'FinancialAccount',
  AccountTransaction: 'AccountTransaction',
  Budget: 'Budget',
  CompanySettings: 'CompanySettings',
  UserSettings: 'UserSettings',
  SystemSettings: 'SystemSettings',
  AuditLog: 'AuditLog',
  SystemLog: 'SystemLog',
  PricingPlan: 'PricingPlan',
  Notification: 'Notification',
  SecurityAlert: 'SecurityAlert',
  FeatureFlag: 'FeatureFlag'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }
        
        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
