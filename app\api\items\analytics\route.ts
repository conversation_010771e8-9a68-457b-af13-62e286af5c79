import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '30' // days
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - parseInt(period))

    // Get comprehensive item analytics
    const [
      totalItems,
      itemsByCategory,
      itemsByStatus,
      inventoryMetrics,
      pricingAnalysis,
      topSellingItems,
      lowStockItems,
      recentItems,
      categoryPerformance,
      stockValueAnalysis,
      itemUsageStats,
      profitabilityAnalysis
    ] = await Promise.all([
      // Total items
      prisma.item.count({
        where: {
          companyId: session.user.companyId,
          createdAt: { gte: startDate }
        }
      }),

      // Items by category
      prisma.item.groupBy({
        by: ['category'],
        where: {
          companyId: session.user.companyId,
          createdAt: { gte: startDate }
        },
        _count: {
          id: true
        },
        _avg: {
          unitPrice: true
        },
        _sum: {
          stockQuantity: true
        }
      }),

      // Items by status (active/inactive)
      prisma.item.groupBy({
        by: ['active'],
        where: {
          companyId: session.user.companyId,
          createdAt: { gte: startDate }
        },
        _count: {
          id: true
        },
        _avg: {
          unitPrice: true
        }
      }),

      // Inventory metrics
      Promise.all([
        // Items with inventory tracking
        prisma.item.count({
          where: {
            companyId: session.user.companyId,
            trackInventory: true
          }
        }),
        // Total stock value
        prisma.item.aggregate({
          where: {
            companyId: session.user.companyId,
            trackInventory: true,
            stockQuantity: { not: null }
          },
          _sum: {
            stockQuantity: true
          }
        }),
        // Average stock per item
        prisma.item.aggregate({
          where: {
            companyId: session.user.companyId,
            trackInventory: true,
            stockQuantity: { not: null }
          },
          _avg: {
            stockQuantity: true
          }
        }),
        // Items with low stock alerts
        prisma.item.count({
          where: {
            companyId: session.user.companyId,
            trackInventory: true,
            AND: [
              { stockQuantity: { not: null } },
              { lowStockAlert: { not: null } },
              { stockQuantity: { lte: prisma.item.fields.lowStockAlert } }
            ]
          }
        })
      ]),

      // Pricing analysis
      Promise.all([
        // Average unit price
        prisma.item.aggregate({
          where: {
            companyId: session.user.companyId,
            active: true
          },
          _avg: {
            unitPrice: true
          }
        }),
        // Price range
        prisma.item.aggregate({
          where: {
            companyId: session.user.companyId,
            active: true
          },
          _min: {
            unitPrice: true
          },
          _max: {
            unitPrice: true
          }
        }),
        // Items with cost price
        prisma.item.count({
          where: {
            companyId: session.user.companyId,
            costPrice: { not: null }
          }
        })
      ]),

      // Top selling items (based on quotation/invoice usage)
      prisma.$queryRaw`
        SELECT 
          i.id,
          i.name,
          i."unitPrice",
          i.category,
          i."stockQuantity",
          COUNT(qi.id) + COUNT(ii.id) as usage_count,
          SUM(COALESCE(qi.quantity, 0) + COALESCE(ii.quantity, 0)) as total_quantity_sold,
          SUM(COALESCE(qi.quantity * qi."unitPrice", 0) + COALESCE(ii.quantity * ii."unitPrice", 0)) as total_revenue
        FROM "items" i
        LEFT JOIN "quotation_items" qi ON i.id = qi."itemId"
        LEFT JOIN "invoice_items" ii ON i.id = ii."itemId"
        WHERE i."companyId" = ${session.user.companyId}
          AND i."createdAt" >= ${startDate}
        GROUP BY i.id, i.name, i."unitPrice", i.category, i."stockQuantity"
        ORDER BY usage_count DESC, total_revenue DESC
        LIMIT 10
      `,

      // Low stock items
      prisma.item.findMany({
        where: {
          companyId: session.user.companyId,
          trackInventory: true,
          AND: [
            { stockQuantity: { not: null } },
            { lowStockAlert: { not: null } },
            { stockQuantity: { lte: prisma.item.fields.lowStockAlert } }
          ]
        },
        select: {
          id: true,
          name: true,
          sku: true,
          category: true,
          stockQuantity: true,
          lowStockAlert: true,
          unitPrice: true
        },
        orderBy: [
          { stockQuantity: 'asc' }
        ],
        take: 10
      }),

      // Recent items
      prisma.item.findMany({
        where: {
          companyId: session.user.companyId,
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
          }
        },
        select: {
          id: true,
          name: true,
          sku: true,
          category: true,
          unitPrice: true,
          stockQuantity: true,
          trackInventory: true,
          active: true,
          createdAt: true
        },
        orderBy: { createdAt: 'desc' },
        take: 10
      }),

      // Category performance
      prisma.$queryRaw`
        SELECT 
          i.category,
          COUNT(i.id) as item_count,
          AVG(i."unitPrice") as avg_price,
          SUM(COALESCE(i."stockQuantity", 0)) as total_stock,
          COUNT(CASE WHEN i."trackInventory" = true THEN 1 END) as tracked_items,
          COUNT(CASE WHEN i.active = true THEN 1 END) as active_items,
          COUNT(qi.id) + COUNT(ii.id) as usage_count
        FROM "items" i
        LEFT JOIN "quotation_items" qi ON i.id = qi."itemId"
        LEFT JOIN "invoice_items" ii ON i.id = ii."itemId"
        WHERE i."companyId" = ${session.user.companyId}
          AND i."createdAt" >= ${startDate}
        GROUP BY i.category
        ORDER BY item_count DESC
      `,

      // Stock value analysis
      prisma.$queryRaw`
        SELECT 
          SUM(COALESCE(i."stockQuantity", 0) * i."unitPrice") as total_stock_value,
          SUM(CASE WHEN i."costPrice" IS NOT NULL 
              THEN COALESCE(i."stockQuantity", 0) * i."costPrice" 
              ELSE 0 END) as total_cost_value,
          COUNT(CASE WHEN i."trackInventory" = true THEN 1 END) as tracked_items_count,
          AVG(COALESCE(i."stockQuantity", 0) * i."unitPrice") as avg_item_value
        FROM "items" i
        WHERE i."companyId" = ${session.user.companyId}
          AND i."trackInventory" = true
          AND i.active = true
      `,

      // Item usage statistics
      prisma.$queryRaw`
        SELECT 
          COUNT(DISTINCT qi."itemId") + COUNT(DISTINCT ii."itemId") as used_items_count,
          COUNT(qi.id) + COUNT(ii.id) as total_usage_count,
          AVG(COALESCE(qi.quantity, 0) + COALESCE(ii.quantity, 0)) as avg_quantity_per_use,
          SUM(COALESCE(qi.quantity * qi."unitPrice", 0) + COALESCE(ii.quantity * ii."unitPrice", 0)) as total_usage_value
        FROM "items" i
        LEFT JOIN "quotation_items" qi ON i.id = qi."itemId"
        LEFT JOIN "invoice_items" ii ON i.id = ii."itemId"
        WHERE i."companyId" = ${session.user.companyId}
          AND (qi."createdAt" >= ${startDate} OR ii."createdAt" >= ${startDate} OR (qi.id IS NULL AND ii.id IS NULL))
      `,

      // Profitability analysis
      prisma.$queryRaw`
        SELECT 
          i.id,
          i.name,
          i.category,
          i."unitPrice",
          i."costPrice",
          CASE 
            WHEN i."costPrice" IS NOT NULL AND i."costPrice" > 0 
            THEN ((i."unitPrice" - i."costPrice") / i."costPrice" * 100)
            ELSE NULL 
          END as profit_margin_percent,
          CASE 
            WHEN i."costPrice" IS NOT NULL 
            THEN (i."unitPrice" - i."costPrice")
            ELSE NULL 
          END as profit_per_unit,
          COUNT(qi.id) + COUNT(ii.id) as usage_count
        FROM "items" i
        LEFT JOIN "quotation_items" qi ON i.id = qi."itemId"
        LEFT JOIN "invoice_items" ii ON i.id = ii."itemId"
        WHERE i."companyId" = ${session.user.companyId}
          AND i."costPrice" IS NOT NULL
          AND i.active = true
        GROUP BY i.id, i.name, i.category, i."unitPrice", i."costPrice"
        ORDER BY profit_margin_percent DESC NULLS LAST
        LIMIT 10
      `
    ])

    // Process metrics
    const [trackedItems, totalStock, avgStock, lowStockCount] = inventoryMetrics
    const [avgPrice, priceRange, itemsWithCost] = pricingAnalysis
    const stockValueResult = stockValueAnalysis[0] as any
    const usageStatsResult = itemUsageStats[0] as any

    return NextResponse.json({
      summary: {
        totalItems,
        activeItems: itemsByStatus.find(s => s.active)?._count.id || 0,
        inactiveItems: itemsByStatus.find(s => !s.active)?._count.id || 0,
        trackedItems,
        lowStockCount,
        totalStockQuantity: totalStock._sum.stockQuantity || 0,
        avgStockPerItem: Math.round(avgStock._avg.stockQuantity || 0),
        avgUnitPrice: Number(avgPrice._avg.unitPrice || 0),
        minPrice: Number(priceRange._min.unitPrice || 0),
        maxPrice: Number(priceRange._max.unitPrice || 0),
        itemsWithCostPrice: itemsWithCost,
        totalStockValue: Number(stockValueResult?.total_stock_value || 0),
        totalCostValue: Number(stockValueResult?.total_cost_value || 0),
        usedItemsCount: Number(usageStatsResult?.used_items_count || 0),
        totalUsageCount: Number(usageStatsResult?.total_usage_count || 0),
        totalUsageValue: Number(usageStatsResult?.total_usage_value || 0)
      },
      itemsByCategory: itemsByCategory.map(item => ({
        category: item.category || 'Uncategorized',
        count: item._count.id,
        avgPrice: Number(item._avg.unitPrice || 0),
        totalStock: item._sum.stockQuantity || 0
      })),
      itemsByStatus: itemsByStatus.map(item => ({
        status: item.active ? 'Active' : 'Inactive',
        count: item._count.id,
        avgPrice: Number(item._avg.unitPrice || 0)
      })),
      topSellingItems: (topSellingItems as any[]).map(item => ({
        id: item.id,
        name: item.name,
        category: item.category,
        unitPrice: Number(item.unitPrice),
        stockQuantity: item.stockQuantity,
        usageCount: Number(item.usage_count),
        totalQuantitySold: Number(item.total_quantity_sold),
        totalRevenue: Number(item.total_revenue)
      })),
      lowStockItems: lowStockItems.map(item => ({
        id: item.id,
        name: item.name,
        sku: item.sku,
        category: item.category,
        stockQuantity: item.stockQuantity,
        lowStockAlert: item.lowStockAlert,
        unitPrice: Number(item.unitPrice),
        stockValue: Number(item.unitPrice) * (item.stockQuantity || 0)
      })),
      recentItems: recentItems.map(item => ({
        id: item.id,
        name: item.name,
        sku: item.sku,
        category: item.category,
        unitPrice: Number(item.unitPrice),
        stockQuantity: item.stockQuantity,
        trackInventory: item.trackInventory,
        active: item.active,
        createdAt: item.createdAt
      })),
      categoryPerformance: (categoryPerformance as any[]).map(cat => ({
        category: cat.category || 'Uncategorized',
        itemCount: Number(cat.item_count),
        avgPrice: Number(cat.avg_price),
        totalStock: Number(cat.total_stock),
        trackedItems: Number(cat.tracked_items),
        activeItems: Number(cat.active_items),
        usageCount: Number(cat.usage_count)
      })),
      profitabilityAnalysis: (profitabilityAnalysis as any[]).map(item => ({
        id: item.id,
        name: item.name,
        category: item.category,
        unitPrice: Number(item.unitPrice),
        costPrice: Number(item.costPrice),
        profitMarginPercent: item.profit_margin_percent ? Number(item.profit_margin_percent) : null,
        profitPerUnit: item.profit_per_unit ? Number(item.profit_per_unit) : null,
        usageCount: Number(item.usage_count)
      })),
      period: parseInt(period)
    })

  } catch (error) {
    console.error('Error fetching item analytics:', error)
    return NextResponse.json(
      { error: 'Failed to fetch item analytics' },
      { status: 500 }
    )
  }
}
