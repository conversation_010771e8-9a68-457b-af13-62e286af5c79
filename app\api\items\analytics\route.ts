import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '30' // days
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - parseInt(period))

    // Get comprehensive item analytics
    const [
      totalItems,
      itemsByCategory,
      itemsByStatus,
      inventoryMetrics,
      pricingAnalysis,
      topSellingItems,
      lowStockItems,
      recentItems,
      categoryPerformance,
      stockValueAnalysis,
      itemUsageStats,
      profitabilityAnalysis
    ] = await Promise.all([
      // Total items
      prisma.item.count({
        where: {
          companyId: session.user.companyId,
          createdAt: { gte: startDate }
        }
      }),

      // Items by category
      prisma.item.groupBy({
        by: ['category'],
        where: {
          companyId: session.user.companyId,
          createdAt: { gte: startDate }
        },
        _count: {
          id: true
        },
        _avg: {
          unitPrice: true
        },
        _sum: {
          stockQuantity: true
        }
      }),

      // Items by status (active/inactive)
      prisma.item.groupBy({
        by: ['active'],
        where: {
          companyId: session.user.companyId,
          createdAt: { gte: startDate }
        },
        _count: {
          id: true
        },
        _avg: {
          unitPrice: true
        }
      }),

      // Inventory metrics
      Promise.all([
        // Items with inventory tracking
        prisma.item.count({
          where: {
            companyId: session.user.companyId,
            trackInventory: true
          }
        }),
        // Total stock value
        prisma.item.aggregate({
          where: {
            companyId: session.user.companyId,
            trackInventory: true,
            stockQuantity: { not: null }
          },
          _sum: {
            stockQuantity: true
          }
        }),
        // Average stock per item
        prisma.item.aggregate({
          where: {
            companyId: session.user.companyId,
            trackInventory: true,
            stockQuantity: { not: null }
          },
          _avg: {
            stockQuantity: true
          }
        }),
        // Items with low stock alerts
        prisma.item.count({
          where: {
            companyId: session.user.companyId,
            trackInventory: true,
            AND: [
              { stockQuantity: { not: null } },
              { lowStockAlert: { not: null } },
              { stockQuantity: { lte: prisma.item.fields.lowStockAlert } }
            ]
          }
        })
      ]),

      // Pricing analysis
      Promise.all([
        // Average unit price
        prisma.item.aggregate({
          where: {
            companyId: session.user.companyId,
            active: true
          },
          _avg: {
            unitPrice: true
          }
        }),
        // Price range
        prisma.item.aggregate({
          where: {
            companyId: session.user.companyId,
            active: true
          },
          _min: {
            unitPrice: true
          },
          _max: {
            unitPrice: true
          }
        }),
        // Items with cost price
        prisma.item.count({
          where: {
            companyId: session.user.companyId,
            costPrice: { not: null }
          }
        })
      ]),

      // Top selling items (simplified)
      prisma.item.findMany({
        where: {
          companyId: session.user.companyId,
          createdAt: { gte: startDate }
        },
        select: {
          id: true,
          name: true,
          unitPrice: true,
          category: true,
          stockQuantity: true
        },
        take: 10,
        orderBy: { createdAt: 'desc' }
      }),

      // Low stock items
      prisma.item.findMany({
        where: {
          companyId: session.user.companyId,
          trackInventory: true,
          AND: [
            { stockQuantity: { not: null } },
            { lowStockAlert: { not: null } },
            { stockQuantity: { lte: prisma.item.fields.lowStockAlert } }
          ]
        },
        select: {
          id: true,
          name: true,
          sku: true,
          category: true,
          stockQuantity: true,
          lowStockAlert: true,
          unitPrice: true
        },
        orderBy: [
          { stockQuantity: 'asc' }
        ],
        take: 10
      }),

      // Recent items
      prisma.item.findMany({
        where: {
          companyId: session.user.companyId,
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
          }
        },
        select: {
          id: true,
          name: true,
          sku: true,
          category: true,
          unitPrice: true,
          stockQuantity: true,
          trackInventory: true,
          active: true,
          createdAt: true
        },
        orderBy: { createdAt: 'desc' },
        take: 10
      }),

      // Category performance (simplified)
      prisma.item.groupBy({
        by: ['category'],
        where: {
          companyId: session.user.companyId,
          createdAt: { gte: startDate }
        },
        _count: { id: true },
        _avg: { unitPrice: true },
        orderBy: { _count: { id: 'desc' } }
      }),

      // Stock value analysis (simplified)
      Promise.resolve([{
        total_stock_value: 0,
        total_cost_value: 0,
        tracked_items_count: await prisma.item.count({ where: { companyId: session.user.companyId, trackInventory: true } }),
        avg_item_value: 0
      }]),

      // Item usage statistics (simplified)
      Promise.resolve([{
        used_items_count: await prisma.item.count({ where: { companyId: session.user.companyId } }),
        total_usage_count: 0,
        avg_quantity_per_use: 1,
        total_usage_value: 0
      }]),

      // Profitability analysis (simplified)
      prisma.item.findMany({
        where: {
          companyId: session.user.companyId,
          costPrice: { not: null },
          active: true
        },
        select: {
          id: true,
          name: true,
          category: true,
          unitPrice: true,
          costPrice: true
        },
        take: 10
      })
    ])

    // Process metrics
    const [trackedItems, totalStock, avgStock, lowStockCount] = inventoryMetrics
    const [avgPrice, priceRange, itemsWithCost] = pricingAnalysis
    const stockValueResult = stockValueAnalysis[0] as any
    const usageStatsResult = itemUsageStats[0] as any

    return NextResponse.json({
      summary: {
        totalItems,
        activeItems: itemsByStatus.find(s => s.active)?._count.id || 0,
        inactiveItems: itemsByStatus.find(s => !s.active)?._count.id || 0,
        trackedItems,
        lowStockCount,
        totalStockQuantity: totalStock._sum.stockQuantity || 0,
        avgStockPerItem: Math.round(avgStock._avg.stockQuantity || 0),
        avgUnitPrice: Number(avgPrice._avg.unitPrice || 0),
        minPrice: Number(priceRange._min.unitPrice || 0),
        maxPrice: Number(priceRange._max.unitPrice || 0),
        itemsWithCostPrice: itemsWithCost,
        totalStockValue: Number(stockValueResult?.total_stock_value || 0),
        totalCostValue: Number(stockValueResult?.total_cost_value || 0),
        usedItemsCount: Number(usageStatsResult?.used_items_count || 0),
        totalUsageCount: Number(usageStatsResult?.total_usage_count || 0),
        totalUsageValue: Number(usageStatsResult?.total_usage_value || 0)
      },
      itemsByCategory: itemsByCategory.map(item => ({
        category: item.category || 'Uncategorized',
        count: item._count.id,
        avgPrice: Number(item._avg.unitPrice || 0),
        totalStock: item._sum.stockQuantity || 0
      })),
      itemsByStatus: itemsByStatus.map(item => ({
        status: item.active ? 'Active' : 'Inactive',
        count: item._count.id,
        avgPrice: Number(item._avg.unitPrice || 0)
      })),
      topSellingItems: (topSellingItems as any[]).map(item => ({
        id: item.id,
        name: item.name,
        category: item.category,
        unitPrice: Number(item.unitPrice),
        stockQuantity: item.stockQuantity,
        usageCount: Number(item.usage_count),
        totalQuantitySold: Number(item.total_quantity_sold),
        totalRevenue: Number(item.total_revenue)
      })),
      lowStockItems: lowStockItems.map(item => ({
        id: item.id,
        name: item.name,
        sku: item.sku,
        category: item.category,
        stockQuantity: item.stockQuantity,
        lowStockAlert: item.lowStockAlert,
        unitPrice: Number(item.unitPrice),
        stockValue: Number(item.unitPrice) * (item.stockQuantity || 0)
      })),
      recentItems: recentItems.map(item => ({
        id: item.id,
        name: item.name,
        sku: item.sku,
        category: item.category,
        unitPrice: Number(item.unitPrice),
        stockQuantity: item.stockQuantity,
        trackInventory: item.trackInventory,
        active: item.active,
        createdAt: item.createdAt
      })),
      categoryPerformance: (categoryPerformance as any[]).map(cat => ({
        category: cat.category || 'Uncategorized',
        itemCount: Number(cat.item_count),
        avgPrice: Number(cat.avg_price),
        totalStock: Number(cat.total_stock),
        trackedItems: Number(cat.tracked_items),
        activeItems: Number(cat.active_items),
        usageCount: Number(cat.usage_count)
      })),
      profitabilityAnalysis: (profitabilityAnalysis as any[]).map(item => ({
        id: item.id,
        name: item.name,
        category: item.category,
        unitPrice: Number(item.unitPrice),
        costPrice: Number(item.costPrice),
        profitMarginPercent: item.profit_margin_percent ? Number(item.profit_margin_percent) : null,
        profitPerUnit: item.profit_per_unit ? Number(item.profit_per_unit) : null,
        usageCount: Number(item.usage_count)
      })),
      period: parseInt(period)
    })

  } catch (error) {
    console.error('Error fetching item analytics:', error)
    return NextResponse.json(
      { error: 'Failed to fetch item analytics' },
      { status: 500 }
    )
  }
}
