{"name": "nextjs-saas-app", "version": "1.0.0", "description": "Complete SaaS Application with Next.js, Prisma, and TypeScript", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check ."}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@headlessui/react": "^2.2.4", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^5.1.1", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^5.7.1", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.0.7", "@stripe/stripe-js": "^2.4.0", "@tanstack/react-query": "^5.17.9", "@tanstack/react-table": "^8.21.3", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^0.2.0", "date-fns": "^3.0.6", "framer-motion": "^10.16.16", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.303.0", "next": "14.0.4", "next-auth": "^4.24.5", "next-themes": "^0.2.1", "nodemailer": "^6.9.8", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.60.0", "react-hot-toast": "^2.5.2", "recharts": "^2.9.3", "resend": "^3.0.0", "sonner": "^2.0.6", "stripe": "^14.25.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.76", "zustand": "^4.4.7"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.10.6", "@types/nodemailer": "^6.4.14", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.21", "eslint": "^8.56.0", "eslint-config-next": "14.0.4", "postcss": "^8.5.6", "prettier": "^3.1.1", "prisma": "^5.7.1", "tailwindcss": "^3.4.17", "tsx": "^4.6.2", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}}