(()=>{var e={};e.id=8996,e.ids=[8996],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},49679:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>d});var a=t(50482),r=t(69108),l=t(62563),n=t.n(l),i=t(68300),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);t.d(s,c);let d=["",{children:["dashboard",{children:["payments",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,34670)),"C:\\proj\\nextjs-saas\\app\\dashboard\\payments\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,96339)),"C:\\proj\\nextjs-saas\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,59504)),"C:\\proj\\nextjs-saas\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\proj\\nextjs-saas\\app\\dashboard\\payments\\page.tsx"],x="/dashboard/payments/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/payments/page",pathname:"/dashboard/payments",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},88639:(e,s,t)=>{Promise.resolve().then(t.bind(t,33398))},33398:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>f});var a=t(95344),r=t(3729),l=t(61351),n=t(16212),i=t(69436),c=t(92549),d=t(85674),o=t(37121),x=t(48411),m=t(51838),h=t(28765),p=t(53148),u=t(44669),y=t(20783),j=t.n(y);function f(){let[e,s]=(0,r.useState)([]),[t,y]=(0,r.useState)(!0),[f,g]=(0,r.useState)(""),[N,v]=(0,r.useState)("all"),[b,w]=(0,r.useState)("all");(0,r.useEffect)(()=>{C()},[]);let C=async()=>{try{y(!0);let e=await fetch("/api/payments");if(!e.ok)throw Error("Failed to fetch payments");let t=await e.json();s(t.payments)}catch(e){u.toast.error("Failed to load payments"),console.error("Error fetching payments:",e)}finally{y(!1)}},Z=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e),P=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),k=e=>{switch(e){case"COMPLETED":return"bg-green-100 text-green-800";case"PENDING":return"bg-yellow-100 text-yellow-800";case"FAILED":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},E=e=>{switch(e){case"CREDIT_CARD":return a.jsx(d.Z,{className:"h-4 w-4"});case"BANK_TRANSFER":return a.jsx(o.Z,{className:"h-4 w-4"});default:return a.jsx(x.Z,{className:"h-4 w-4"})}},M=e.filter(e=>{let s=e.invoice?.invoiceNumber.toLowerCase().includes(f.toLowerCase())||e.invoice?.customer.name.toLowerCase().includes(f.toLowerCase())||e.reference?.toLowerCase().includes(f.toLowerCase()),t="all"===N||e.status===N,a="all"===b||e.paymentMethod===b;return s&&t&&a}),A=M.reduce((e,s)=>e+s.amount,0),_=M.filter(e=>"COMPLETED"===e.status).reduce((e,s)=>e+s.amount,0);return t?(0,a.jsxs)("div",{className:"space-y-6",children:[a.jsx("div",{className:"flex items-center justify-between",children:a.jsx("h1",{className:"text-3xl font-bold",children:"Payments"})}),a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[1,2,3].map(e=>a.jsx(l.Zb,{children:a.jsx(l.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[a.jsx("div",{className:"h-4 bg-gray-200 rounded w-1/2 mb-2"}),a.jsx("div",{className:"h-8 bg-gray-200 rounded w-3/4"})]})})},e))})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("h1",{className:"text-3xl font-bold",children:"Payments"}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[a.jsx(n.z,{variant:"outline",asChild:!0,children:(0,a.jsxs)(j(),{href:"/dashboard/invoices",children:[a.jsx(o.Z,{className:"h-4 w-4 mr-2"}),"Manage Invoices"]})}),a.jsx(n.z,{asChild:!0,children:(0,a.jsxs)(j(),{href:"/dashboard/payments/new",children:[a.jsx(m.Z,{className:"h-4 w-4 mr-2"}),"Record Payment"]})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[a.jsx(l.Zb,{children:a.jsx(l.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx(x.Z,{className:"h-8 w-8 text-blue-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Payments"}),a.jsx("p",{className:"text-2xl font-bold",children:Z(A)})]})]})})}),a.jsx(l.Zb,{children:a.jsx(l.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx(d.Z,{className:"h-8 w-8 text-green-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Completed"}),a.jsx("p",{className:"text-2xl font-bold",children:Z(_)})]})]})})}),a.jsx(l.Zb,{children:a.jsx(l.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx(o.Z,{className:"h-8 w-8 text-purple-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Transactions"}),a.jsx("p",{className:"text-2xl font-bold",children:M.length})]})]})})})]}),a.jsx(l.Zb,{children:a.jsx(l.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[a.jsx("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[a.jsx(h.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),a.jsx(c.I,{placeholder:"Search by invoice, customer, or reference...",value:f,onChange:e=>g(e.target.value),className:"pl-10"})]})}),(0,a.jsxs)("select",{value:N,onChange:e=>v(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[a.jsx("option",{value:"all",children:"All Status"}),a.jsx("option",{value:"COMPLETED",children:"Completed"}),a.jsx("option",{value:"PENDING",children:"Pending"}),a.jsx("option",{value:"FAILED",children:"Failed"}),a.jsx("option",{value:"CANCELLED",children:"Cancelled"})]}),(0,a.jsxs)("select",{value:b,onChange:e=>w(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[a.jsx("option",{value:"all",children:"All Methods"}),a.jsx("option",{value:"CASH",children:"Cash"}),a.jsx("option",{value:"CHECK",children:"Check"}),a.jsx("option",{value:"CREDIT_CARD",children:"Credit Card"}),a.jsx("option",{value:"BANK_TRANSFER",children:"Bank Transfer"}),a.jsx("option",{value:"PAYPAL",children:"PayPal"}),a.jsx("option",{value:"OTHER",children:"Other"})]})]})})}),(0,a.jsxs)(l.Zb,{children:[a.jsx(l.Ol,{children:a.jsx(l.ll,{children:"Payment History"})}),a.jsx(l.aY,{children:0===M.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[a.jsx(d.Z,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No payments found"}),a.jsx("p",{className:"text-gray-500",children:f||"all"!==N||"all"!==b?"Try adjusting your filters":"Start by recording your first payment"})]}):a.jsx("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[a.jsx("thead",{children:(0,a.jsxs)("tr",{className:"border-b",children:[a.jsx("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Invoice"}),a.jsx("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Customer"}),a.jsx("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Amount"}),a.jsx("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Method"}),a.jsx("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Date"}),a.jsx("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Status"}),a.jsx("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Actions"})]})}),a.jsx("tbody",{children:M.map(e=>(0,a.jsxs)("tr",{className:"border-b hover:bg-gray-50",children:[a.jsx("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx(o.Z,{className:"h-4 w-4 text-gray-400 mr-2"}),a.jsx("span",{className:"font-medium",children:e.invoice?.invoiceNumber||"N/A"})]})}),a.jsx("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium",children:e.invoice?.customer.name}),e.invoice?.customer.company&&a.jsx("div",{className:"text-sm text-gray-500",children:e.invoice.customer.company})]})}),a.jsx("td",{className:"py-3 px-4 font-medium",children:Z(e.amount)}),a.jsx("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[E(e.paymentMethod),a.jsx("span",{className:"ml-2",children:e.paymentMethod.replace("_"," ")})]})}),a.jsx("td",{className:"py-3 px-4 text-gray-600",children:P(e.paymentDate)}),a.jsx("td",{className:"py-3 px-4",children:a.jsx(i.C,{className:k(e.status),children:e.status})}),a.jsx("td",{className:"py-3 px-4",children:a.jsx("div",{className:"flex items-center space-x-2",children:e.invoice&&a.jsx(n.z,{variant:"ghost",size:"sm",asChild:!0,children:a.jsx(j(),{href:`/dashboard/invoices/${e.invoice.id}`,children:a.jsx(p.Z,{className:"h-4 w-4"})})})})})]},e.id))})]})})})]})]})}},92549:(e,s,t)=>{"use strict";t.d(s,{I:()=>n});var a=t(95344),r=t(3729),l=t(91626);let n=r.forwardRef(({className:e,type:s,...t},r)=>a.jsx("input",{type:s,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...t}));n.displayName="Input"},48411:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},53148:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},71542:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("FileCheck",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["path",{d:"m9 15 2 2 4-4",key:"1grp1n"}]])},91917:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},51838:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},74243:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Receipt",[["path",{d:"M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1-2-1Z",key:"wqdwcb"}],["path",{d:"M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8",key:"1h4pet"}],["path",{d:"M12 17V7",key:"pyj7ub"}]])},28240:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},34670:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>l,__esModule:()=>r,default:()=>n});let a=(0,t(86843).createProxy)(String.raw`C:\proj\nextjs-saas\app\dashboard\payments\page.tsx`),{__esModule:r,$$typeof:l}=a,n=a.default}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[1638,7948,6671,4626,7792,2506,2125,5045],()=>t(49679));module.exports=a})();