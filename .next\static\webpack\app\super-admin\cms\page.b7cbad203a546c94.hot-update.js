"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/super-admin/cms/page",{

/***/ "(app-pages-browser)/./app/super-admin/cms/page.tsx":
/*!**************************************!*\
  !*** ./app/super-admin/cms/page.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CMSPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Layout,Monitor,Plus,Quote,RefreshCw,Save,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Layout,Monitor,Plus,Quote,RefreshCw,Save,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Layout,Monitor,Plus,Quote,RefreshCw,Save,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Layout,Monitor,Plus,Quote,RefreshCw,Save,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Layout,Monitor,Plus,Quote,RefreshCw,Save,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/panels-top-left.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Layout,Monitor,Plus,Quote,RefreshCw,Save,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Layout,Monitor,Plus,Quote,RefreshCw,Save,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Layout,Monitor,Plus,Quote,RefreshCw,Save,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Layout,Monitor,Plus,Quote,RefreshCw,Save,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/quote.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction CMSPage() {\n    var _session_user, _content_features, _content_features1, _content_features2, _content_features3, _content_features_items, _content_features4, _content_testimonials, _content_testimonials1, _content_testimonials2, _content_testimonials3, _content_testimonials_items, _content_testimonials4, _content_faq, _content_faq1, _content_faq2, _content_faq3, _content_faq_items, _content_faq4, _content_cta, _content_cta1, _content_cta2, _content_cta3, _content_cta4, _content_cta5, _content_cta6, _content_seo, _content_seo1, _content_seo2, _content_seo3;\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        hero: {\n            enabled: true,\n            title: \"Build Your SaaS Business\",\n            subtitle: \"The Complete Platform\",\n            description: \"Everything you need to launch, grow, and scale your SaaS business. From customer management to billing, we've got you covered.\",\n            primaryCTA: {\n                text: \"Start Free Trial\",\n                link: \"/auth/signup\"\n            },\n            secondaryCTA: {\n                text: \"Watch Demo\",\n                link: \"/demo\"\n            },\n            backgroundImage: \"\",\n            backgroundVideo: \"\"\n        },\n        features: {\n            enabled: true,\n            title: \"Everything You Need\",\n            subtitle: \"Powerful Features\",\n            items: [\n                {\n                    id: \"1\",\n                    title: \"Customer Management\",\n                    description: \"Manage your customers, track interactions, and build lasting relationships.\",\n                    icon: \"users\",\n                    image: \"\"\n                },\n                {\n                    id: \"2\",\n                    title: \"Subscription Billing\",\n                    description: \"Automated billing, invoicing, and payment processing for recurring revenue.\",\n                    icon: \"credit-card\",\n                    image: \"\"\n                },\n                {\n                    id: \"3\",\n                    title: \"Analytics & Reports\",\n                    description: \"Comprehensive analytics to track your business performance and growth.\",\n                    icon: \"bar-chart\",\n                    image: \"\"\n                }\n            ]\n        },\n        pricing: {\n            enabled: true,\n            title: \"Simple, Transparent Pricing\",\n            subtitle: \"Choose the plan that fits your needs\",\n            showPricingTable: true,\n            customMessage: \"\"\n        },\n        testimonials: {\n            enabled: true,\n            title: \"What Our Customers Say\",\n            subtitle: \"Trusted by thousands of businesses\",\n            items: [\n                {\n                    id: \"1\",\n                    name: \"John Smith\",\n                    role: \"CEO\",\n                    company: \"TechCorp\",\n                    content: \"This platform has transformed how we manage our SaaS business. Highly recommended!\",\n                    avatar: \"\",\n                    rating: 5\n                }\n            ]\n        },\n        faq: {\n            enabled: true,\n            title: \"Frequently Asked Questions\",\n            subtitle: \"Everything you need to know\",\n            items: [\n                {\n                    id: \"1\",\n                    question: \"How do I get started?\",\n                    answer: \"Simply sign up for a free trial and follow our onboarding guide to set up your account.\"\n                },\n                {\n                    id: \"2\",\n                    question: \"Can I cancel anytime?\",\n                    answer: \"Yes, you can cancel your subscription at any time. No long-term contracts or cancellation fees.\"\n                }\n            ]\n        },\n        cta: {\n            enabled: true,\n            title: \"Ready to Get Started?\",\n            description: \"Join thousands of businesses already using our platform to grow their SaaS.\",\n            buttonText: \"Start Your Free Trial\",\n            buttonLink: \"/auth/signup\",\n            backgroundImage: \"\"\n        },\n        footer: {\n            enabled: true,\n            companyDescription: \"The complete SaaS platform for modern businesses.\",\n            links: [\n                {\n                    id: \"1\",\n                    title: \"Product\",\n                    items: [\n                        {\n                            id: \"1\",\n                            text: \"Features\",\n                            link: \"/features\"\n                        },\n                        {\n                            id: \"2\",\n                            text: \"Pricing\",\n                            link: \"/pricing\"\n                        },\n                        {\n                            id: \"3\",\n                            text: \"Security\",\n                            link: \"/security\"\n                        }\n                    ]\n                },\n                {\n                    id: \"2\",\n                    title: \"Company\",\n                    items: [\n                        {\n                            id: \"1\",\n                            text: \"About\",\n                            link: \"/about\"\n                        },\n                        {\n                            id: \"2\",\n                            text: \"Blog\",\n                            link: \"/blog\"\n                        },\n                        {\n                            id: \"3\",\n                            text: \"Careers\",\n                            link: \"/careers\"\n                        }\n                    ]\n                }\n            ],\n            socialLinks: {\n                twitter: \"\",\n                linkedin: \"\",\n                facebook: \"\",\n                instagram: \"\"\n            },\n            copyrightText: \"\\xa9 2024 Your Company. All rights reserved.\"\n        },\n        seo: {\n            title: \"SaaS Platform - Build Your Business\",\n            description: \"The complete SaaS platform for modern businesses. Customer management, billing, analytics, and more.\",\n            keywords: \"saas, platform, business, customer management, billing, analytics\",\n            ogImage: \"\"\n        }\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [previewMode, setPreviewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                lineNumber: 285,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n            lineNumber: 284,\n            columnNumber: 7\n        }, this);\n    }\n    if (status === \"unauthenticated\") {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.redirect)(\"/auth/signin\");\n    }\n    if ((session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.role) !== \"SUPER_ADMIN\") {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.redirect)(\"/dashboard\");\n    }\n    const fetchContent = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/super-admin/cms\", {\n                credentials: \"include\"\n            });\n            const data = await response.json();\n            if (data.success) {\n                setContent({\n                    ...content,\n                    ...data.content\n                });\n            }\n        } catch (error) {\n            console.error(\"Error fetching CMS content:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_11__.toast.error(\"Failed to load CMS content\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const saveContent = async ()=>{\n        try {\n            setSaving(true);\n            const response = await fetch(\"/api/super-admin/cms\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(content)\n            });\n            const data = await response.json();\n            if (data.success) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_11__.toast.success(\"CMS content saved successfully\");\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_11__.toast.error(data.error || \"Failed to save CMS content\");\n            }\n        } catch (error) {\n            console.error(\"Error saving CMS content:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_11__.toast.error(\"Failed to save CMS content\");\n        } finally{\n            setSaving(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchContent();\n    }, []);\n    const updateContent = (section, key, value)=>{\n        setContent((prev)=>({\n                ...prev,\n                [section]: {\n                    ...prev[section],\n                    [key]: value\n                }\n            }));\n    };\n    // Feature management functions\n    const addFeature = ()=>{\n        const newFeature = {\n            id: Date.now().toString(),\n            title: \"New Feature\",\n            description: \"Feature description\",\n            icon: \"star\",\n            image: \"\"\n        };\n        setContent((prev)=>{\n            var _prev_features;\n            return {\n                ...prev,\n                features: {\n                    ...prev.features,\n                    items: [\n                        ...((_prev_features = prev.features) === null || _prev_features === void 0 ? void 0 : _prev_features.items) || [],\n                        newFeature\n                    ]\n                }\n            };\n        });\n    };\n    const removeFeature = (id)=>{\n        setContent((prev)=>{\n            var _prev_features_items, _prev_features;\n            return {\n                ...prev,\n                features: {\n                    ...prev.features,\n                    items: ((_prev_features = prev.features) === null || _prev_features === void 0 ? void 0 : (_prev_features_items = _prev_features.items) === null || _prev_features_items === void 0 ? void 0 : _prev_features_items.filter((item)=>item.id !== id)) || []\n                }\n            };\n        });\n    };\n    const updateFeature = (id, key, value)=>{\n        setContent((prev)=>{\n            var _prev_features_items, _prev_features;\n            return {\n                ...prev,\n                features: {\n                    ...prev.features,\n                    items: ((_prev_features = prev.features) === null || _prev_features === void 0 ? void 0 : (_prev_features_items = _prev_features.items) === null || _prev_features_items === void 0 ? void 0 : _prev_features_items.map((item)=>item.id === id ? {\n                            ...item,\n                            [key]: value\n                        } : item)) || []\n                }\n            };\n        });\n    };\n    // Testimonial management functions\n    const addTestimonial = ()=>{\n        const newTestimonial = {\n            id: Date.now().toString(),\n            name: \"Customer Name\",\n            role: \"CEO\",\n            company: \"Company Name\",\n            content: \"Great testimonial content...\",\n            avatar: \"\",\n            rating: 5\n        };\n        setContent((prev)=>{\n            var _prev_testimonials;\n            return {\n                ...prev,\n                testimonials: {\n                    ...prev.testimonials,\n                    items: [\n                        ...((_prev_testimonials = prev.testimonials) === null || _prev_testimonials === void 0 ? void 0 : _prev_testimonials.items) || [],\n                        newTestimonial\n                    ]\n                }\n            };\n        });\n    };\n    const removeTestimonial = (id)=>{\n        setContent((prev)=>{\n            var _prev_testimonials_items, _prev_testimonials;\n            return {\n                ...prev,\n                testimonials: {\n                    ...prev.testimonials,\n                    items: ((_prev_testimonials = prev.testimonials) === null || _prev_testimonials === void 0 ? void 0 : (_prev_testimonials_items = _prev_testimonials.items) === null || _prev_testimonials_items === void 0 ? void 0 : _prev_testimonials_items.filter((item)=>item.id !== id)) || []\n                }\n            };\n        });\n    };\n    const updateTestimonial = (id, key, value)=>{\n        setContent((prev)=>{\n            var _prev_testimonials_items, _prev_testimonials;\n            return {\n                ...prev,\n                testimonials: {\n                    ...prev.testimonials,\n                    items: ((_prev_testimonials = prev.testimonials) === null || _prev_testimonials === void 0 ? void 0 : (_prev_testimonials_items = _prev_testimonials.items) === null || _prev_testimonials_items === void 0 ? void 0 : _prev_testimonials_items.map((item)=>item.id === id ? {\n                            ...item,\n                            [key]: value\n                        } : item)) || []\n                }\n            };\n        });\n    };\n    // FAQ management functions\n    const addFAQ = ()=>{\n        const newFAQ = {\n            id: Date.now().toString(),\n            question: \"New question?\",\n            answer: \"Answer to the question.\"\n        };\n        setContent((prev)=>{\n            var _prev_faq;\n            return {\n                ...prev,\n                faq: {\n                    ...prev.faq,\n                    items: [\n                        ...((_prev_faq = prev.faq) === null || _prev_faq === void 0 ? void 0 : _prev_faq.items) || [],\n                        newFAQ\n                    ]\n                }\n            };\n        });\n    };\n    const removeFAQ = (id)=>{\n        setContent((prev)=>{\n            var _prev_faq_items, _prev_faq;\n            return {\n                ...prev,\n                faq: {\n                    ...prev.faq,\n                    items: ((_prev_faq = prev.faq) === null || _prev_faq === void 0 ? void 0 : (_prev_faq_items = _prev_faq.items) === null || _prev_faq_items === void 0 ? void 0 : _prev_faq_items.filter((item)=>item.id !== id)) || []\n                }\n            };\n        });\n    };\n    const updateFAQ = (id, key, value)=>{\n        setContent((prev)=>{\n            var _prev_faq_items, _prev_faq;\n            return {\n                ...prev,\n                faq: {\n                    ...prev.faq,\n                    items: ((_prev_faq = prev.faq) === null || _prev_faq === void 0 ? void 0 : (_prev_faq_items = _prev_faq.items) === null || _prev_faq_items === void 0 ? void 0 : _prev_faq_items.map((item)=>item.id === id ? {\n                            ...item,\n                            [key]: value\n                        } : item)) || []\n                }\n            };\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row sm:items-center justify-between gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-8 w-8 text-indigo-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                        lineNumber: 484,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"Landing Page CMS\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                lineNumber: 483,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 mt-1\",\n                                children: \"Manage your landing page content and layout\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                lineNumber: 487,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                        lineNumber: 482,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                variant: \"outline\",\n                                onClick: fetchContent,\n                                disabled: loading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 \".concat(loading ? \"animate-spin\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                lineNumber: 490,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>setPreviewMode(!previewMode),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 13\n                                    }, this),\n                                    previewMode ? \"Edit Mode\" : \"Preview\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                lineNumber: 494,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                onClick: saveContent,\n                                disabled: saving,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 \".concat(saving ? \"animate-spin\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                        lineNumber: 499,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Save Changes\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                lineNumber: 498,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                        lineNumber: 489,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                lineNumber: 481,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.Tabs, {\n                defaultValue: \"hero\",\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsList, {\n                        className: \"grid w-full grid-cols-7\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                value: \"hero\",\n                                children: \"Hero\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                lineNumber: 508,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                value: \"features\",\n                                children: \"Features\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                lineNumber: 509,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                value: \"pricing\",\n                                children: \"Pricing\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                lineNumber: 510,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                value: \"testimonials\",\n                                children: \"Testimonials\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                lineNumber: 511,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                value: \"faq\",\n                                children: \"FAQ\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                lineNumber: 512,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                value: \"cta\",\n                                children: \"CTA\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                lineNumber: 513,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                value: \"seo\",\n                                children: \"SEO\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                lineNumber: 514,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                        lineNumber: 507,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                        value: \"hero\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 522,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Hero Section\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"Configure the main hero section of your landing page\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 525,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-4 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            htmlFor: \"heroEnabled\",\n                                                            className: \"text-base font-medium\",\n                                                            children: \"Enable Hero Section\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 532,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Show the hero section on your landing page\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 535,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 531,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                    id: \"heroEnabled\",\n                                                    checked: content.hero.enabled,\n                                                    onCheckedChange: (checked)=>updateContent(\"hero\", \"enabled\", checked)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 530,\n                                            columnNumber: 15\n                                        }, this),\n                                        content.hero.enabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"heroTitle\",\n                                                                    children: \"Main Title\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 550,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    id: \"heroTitle\",\n                                                                    value: content.hero.title,\n                                                                    onChange: (e)=>updateContent(\"hero\", \"title\", e.target.value),\n                                                                    placeholder: \"Your main headline\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 551,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 549,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"heroSubtitle\",\n                                                                    children: \"Subtitle\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 559,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    id: \"heroSubtitle\",\n                                                                    value: content.hero.subtitle,\n                                                                    onChange: (e)=>updateContent(\"hero\", \"subtitle\", e.target.value),\n                                                                    placeholder: \"Supporting headline\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 560,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 558,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            htmlFor: \"heroDescription\",\n                                                            children: \"Description\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 570,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__.Textarea, {\n                                                            id: \"heroDescription\",\n                                                            value: content.hero.description,\n                                                            onChange: (e)=>updateContent(\"hero\", \"description\", e.target.value),\n                                                            placeholder: \"Describe your product or service\",\n                                                            rows: 3\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 571,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 569,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"primaryCTAText\",\n                                                                    children: \"Primary Button Text\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 582,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    id: \"primaryCTAText\",\n                                                                    value: content.hero.primaryCTA.text,\n                                                                    onChange: (e)=>updateContent(\"hero\", \"primaryCTA\", {\n                                                                            ...content.hero.primaryCTA,\n                                                                            text: e.target.value\n                                                                        }),\n                                                                    placeholder: \"Get Started\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 583,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 581,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"primaryCTALink\",\n                                                                    children: \"Primary Button Link\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 591,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    id: \"primaryCTALink\",\n                                                                    value: content.hero.primaryCTA.link,\n                                                                    onChange: (e)=>updateContent(\"hero\", \"primaryCTA\", {\n                                                                            ...content.hero.primaryCTA,\n                                                                            link: e.target.value\n                                                                        }),\n                                                                    placeholder: \"/auth/signup\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 592,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 590,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 580,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"secondaryCTAText\",\n                                                                    children: \"Secondary Button Text\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 603,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    id: \"secondaryCTAText\",\n                                                                    value: content.hero.secondaryCTA.text,\n                                                                    onChange: (e)=>updateContent(\"hero\", \"secondaryCTA\", {\n                                                                            ...content.hero.secondaryCTA,\n                                                                            text: e.target.value\n                                                                        }),\n                                                                    placeholder: \"Learn More\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 604,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 602,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"secondaryCTALink\",\n                                                                    children: \"Secondary Button Link\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 612,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    id: \"secondaryCTALink\",\n                                                                    value: content.hero.secondaryCTA.link,\n                                                                    onChange: (e)=>updateContent(\"hero\", \"secondaryCTA\", {\n                                                                            ...content.hero.secondaryCTA,\n                                                                            link: e.target.value\n                                                                        }),\n                                                                    placeholder: \"/demo\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 613,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 611,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 601,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            className: \"text-base font-medium\",\n                                                            children: \"Background Media\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 624,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                            htmlFor: \"heroBackgroundImage\",\n                                                                            children: \"Background Image URL\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                            lineNumber: 627,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                            id: \"heroBackgroundImage\",\n                                                                            value: content.hero.backgroundImage,\n                                                                            onChange: (e)=>updateContent(\"hero\", \"backgroundImage\", e.target.value),\n                                                                            placeholder: \"https://example.com/hero-bg.jpg\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                            lineNumber: 628,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 626,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                            htmlFor: \"heroBackgroundVideo\",\n                                                                            children: \"Background Video URL\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                            lineNumber: 636,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                            id: \"heroBackgroundVideo\",\n                                                                            value: content.hero.backgroundVideo,\n                                                                            onChange: (e)=>updateContent(\"hero\", \"backgroundVideo\", e.target.value),\n                                                                            placeholder: \"https://example.com/hero-video.mp4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                            lineNumber: 637,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 635,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 625,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 623,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 547,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                    lineNumber: 529,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                            lineNumber: 519,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                        lineNumber: 518,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                        value: \"features\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 657,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Features Section\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 656,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"Configure the features showcase section\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 660,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                    lineNumber: 655,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-4 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            htmlFor: \"featuresEnabled\",\n                                                            className: \"text-base font-medium\",\n                                                            children: \"Enable Features Section\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 667,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Show the features section on your landing page\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 670,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 666,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                    id: \"featuresEnabled\",\n                                                    checked: ((_content_features = content.features) === null || _content_features === void 0 ? void 0 : _content_features.enabled) || false,\n                                                    onCheckedChange: (checked)=>updateContent(\"features\", \"enabled\", checked)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 674,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 665,\n                                            columnNumber: 15\n                                        }, this),\n                                        ((_content_features1 = content.features) === null || _content_features1 === void 0 ? void 0 : _content_features1.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"featuresTitle\",\n                                                                    children: \"Section Title\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 685,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    id: \"featuresTitle\",\n                                                                    value: ((_content_features2 = content.features) === null || _content_features2 === void 0 ? void 0 : _content_features2.title) || \"\",\n                                                                    onChange: (e)=>updateContent(\"features\", \"title\", e.target.value),\n                                                                    placeholder: \"Everything You Need\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 686,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 684,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"featuresSubtitle\",\n                                                                    children: \"Section Subtitle\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 694,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    id: \"featuresSubtitle\",\n                                                                    value: ((_content_features3 = content.features) === null || _content_features3 === void 0 ? void 0 : _content_features3.subtitle) || \"\",\n                                                                    onChange: (e)=>updateContent(\"features\", \"subtitle\", e.target.value),\n                                                                    placeholder: \"Powerful Features\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 695,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 693,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 683,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    className: \"text-base font-medium\",\n                                                                    children: \"Feature Items\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 707,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                    onClick: addFeature,\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                            lineNumber: 709,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Add Feature\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 708,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 706,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: (_content_features4 = content.features) === null || _content_features4 === void 0 ? void 0 : (_content_features_items = _content_features4.items) === null || _content_features_items === void 0 ? void 0 : _content_features_items.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                                    className: \"p-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-start justify-between mb-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: [\n                                                                                        \"Feature \",\n                                                                                        index + 1\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                    lineNumber: 718,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    variant: \"ghost\",\n                                                                                    size: \"sm\",\n                                                                                    onClick: ()=>removeFeature(feature.id),\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                        className: \"h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                        lineNumber: 724,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                    lineNumber: 719,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                            lineNumber: 717,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                                            children: \"Feature Title\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                            lineNumber: 729,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                            value: feature.title,\n                                                                                            onChange: (e)=>updateFeature(feature.id, \"title\", e.target.value),\n                                                                                            placeholder: \"Feature title\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                            lineNumber: 730,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                    lineNumber: 728,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                                            children: \"Icon Name\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                            lineNumber: 737,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                            value: feature.icon,\n                                                                                            onChange: (e)=>updateFeature(feature.id, \"icon\", e.target.value),\n                                                                                            className: \"w-full p-2 border border-gray-300 rounded-md\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                    value: \"users\",\n                                                                                                    children: \"Users\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                                    lineNumber: 743,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                    value: \"credit-card\",\n                                                                                                    children: \"Credit Card\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                                    lineNumber: 744,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                    value: \"bar-chart\",\n                                                                                                    children: \"Bar Chart\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                                    lineNumber: 745,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                    value: \"building\",\n                                                                                                    children: \"Building\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                                    lineNumber: 746,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                    value: \"shield\",\n                                                                                                    children: \"Shield\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                                    lineNumber: 747,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                    value: \"globe\",\n                                                                                                    children: \"Globe\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                                    lineNumber: 748,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                    value: \"zap\",\n                                                                                                    children: \"Zap\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                                    lineNumber: 749,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                    value: \"file-text\",\n                                                                                                    children: \"File Text\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                                    lineNumber: 750,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                            lineNumber: 738,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                    lineNumber: 736,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                            lineNumber: 727,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2 mt-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                                    children: \"Feature Description\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                    lineNumber: 755,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__.Textarea, {\n                                                                                    value: feature.description,\n                                                                                    onChange: (e)=>updateFeature(feature.id, \"description\", e.target.value),\n                                                                                    placeholder: \"Feature description\",\n                                                                                    rows: 2\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                    lineNumber: 756,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                            lineNumber: 754,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, feature.id, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 716,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 714,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 705,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 682,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                    lineNumber: 664,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                            lineNumber: 654,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                        lineNumber: 653,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                        value: \"testimonials\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 778,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Testimonials Section\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 777,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"Manage customer testimonials and reviews\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 781,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                    lineNumber: 776,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-4 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            htmlFor: \"testimonialsEnabled\",\n                                                            className: \"text-base font-medium\",\n                                                            children: \"Enable Testimonials Section\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 788,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Show customer testimonials on your landing page\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 791,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 787,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                    id: \"testimonialsEnabled\",\n                                                    checked: ((_content_testimonials = content.testimonials) === null || _content_testimonials === void 0 ? void 0 : _content_testimonials.enabled) || false,\n                                                    onCheckedChange: (checked)=>updateContent(\"testimonials\", \"enabled\", checked)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 795,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 786,\n                                            columnNumber: 15\n                                        }, this),\n                                        ((_content_testimonials1 = content.testimonials) === null || _content_testimonials1 === void 0 ? void 0 : _content_testimonials1.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"testimonialsTitle\",\n                                                                    children: \"Section Title\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 806,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    id: \"testimonialsTitle\",\n                                                                    value: ((_content_testimonials2 = content.testimonials) === null || _content_testimonials2 === void 0 ? void 0 : _content_testimonials2.title) || \"\",\n                                                                    onChange: (e)=>updateContent(\"testimonials\", \"title\", e.target.value),\n                                                                    placeholder: \"What Our Customers Say\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 807,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 805,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"testimonialsSubtitle\",\n                                                                    children: \"Section Subtitle\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 815,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    id: \"testimonialsSubtitle\",\n                                                                    value: ((_content_testimonials3 = content.testimonials) === null || _content_testimonials3 === void 0 ? void 0 : _content_testimonials3.subtitle) || \"\",\n                                                                    onChange: (e)=>updateContent(\"testimonials\", \"subtitle\", e.target.value),\n                                                                    placeholder: \"Trusted by thousands of businesses\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 816,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 814,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 804,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    className: \"text-base font-medium\",\n                                                                    children: \"Testimonials\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 828,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                    onClick: addTestimonial,\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                            lineNumber: 830,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Add Testimonial\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 829,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 827,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: (_content_testimonials4 = content.testimonials) === null || _content_testimonials4 === void 0 ? void 0 : (_content_testimonials_items = _content_testimonials4.items) === null || _content_testimonials_items === void 0 ? void 0 : _content_testimonials_items.map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                                    className: \"p-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-start justify-between mb-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: [\n                                                                                        \"Testimonial \",\n                                                                                        index + 1\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                    lineNumber: 839,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    variant: \"ghost\",\n                                                                                    size: \"sm\",\n                                                                                    onClick: ()=>removeTestimonial(testimonial.id),\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                        className: \"h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                        lineNumber: 845,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                    lineNumber: 840,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                            lineNumber: 838,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                                            children: \"Customer Name\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                            lineNumber: 850,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                            value: testimonial.name,\n                                                                                            onChange: (e)=>updateTestimonial(testimonial.id, \"name\", e.target.value),\n                                                                                            placeholder: \"John Smith\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                            lineNumber: 851,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                    lineNumber: 849,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                                            children: \"Role\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                            lineNumber: 858,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                            value: testimonial.role,\n                                                                                            onChange: (e)=>updateTestimonial(testimonial.id, \"role\", e.target.value),\n                                                                                            placeholder: \"CEO\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                            lineNumber: 859,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                    lineNumber: 857,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                                            children: \"Company\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                            lineNumber: 866,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                            value: testimonial.company,\n                                                                                            onChange: (e)=>updateTestimonial(testimonial.id, \"company\", e.target.value),\n                                                                                            placeholder: \"TechCorp\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                            lineNumber: 867,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                    lineNumber: 865,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                            lineNumber: 848,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                                            children: \"Rating (1-5)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                            lineNumber: 876,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                            type: \"number\",\n                                                                                            min: \"1\",\n                                                                                            max: \"5\",\n                                                                                            value: testimonial.rating,\n                                                                                            onChange: (e)=>updateTestimonial(testimonial.id, \"rating\", parseInt(e.target.value) || 5)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                            lineNumber: 877,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                    lineNumber: 875,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                                            children: \"Avatar URL (optional)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                            lineNumber: 886,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                            value: testimonial.avatar,\n                                                                                            onChange: (e)=>updateTestimonial(testimonial.id, \"avatar\", e.target.value),\n                                                                                            placeholder: \"https://example.com/avatar.jpg\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                            lineNumber: 887,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                    lineNumber: 885,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                            lineNumber: 874,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2 mt-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                                    children: \"Testimonial Content\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                    lineNumber: 895,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__.Textarea, {\n                                                                                    value: testimonial.content,\n                                                                                    onChange: (e)=>updateTestimonial(testimonial.id, \"content\", e.target.value),\n                                                                                    placeholder: \"This platform has transformed our business...\",\n                                                                                    rows: 3\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                    lineNumber: 896,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                            lineNumber: 894,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, testimonial.id, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 837,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 835,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 826,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 803,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                    lineNumber: 785,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                            lineNumber: 775,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                        lineNumber: 774,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                        value: \"faq\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"FAQ Section\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 917,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"Manage frequently asked questions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 918,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                    lineNumber: 916,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-4 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            htmlFor: \"faqEnabled\",\n                                                            className: \"text-base font-medium\",\n                                                            children: \"Enable FAQ Section\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 925,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Show FAQ section on your landing page\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 928,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 924,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                    id: \"faqEnabled\",\n                                                    checked: ((_content_faq = content.faq) === null || _content_faq === void 0 ? void 0 : _content_faq.enabled) || false,\n                                                    onCheckedChange: (checked)=>updateContent(\"faq\", \"enabled\", checked)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 932,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 923,\n                                            columnNumber: 15\n                                        }, this),\n                                        ((_content_faq1 = content.faq) === null || _content_faq1 === void 0 ? void 0 : _content_faq1.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"faqTitle\",\n                                                                    children: \"Section Title\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 943,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    id: \"faqTitle\",\n                                                                    value: ((_content_faq2 = content.faq) === null || _content_faq2 === void 0 ? void 0 : _content_faq2.title) || \"\",\n                                                                    onChange: (e)=>updateContent(\"faq\", \"title\", e.target.value),\n                                                                    placeholder: \"Frequently Asked Questions\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 944,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 942,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"faqSubtitle\",\n                                                                    children: \"Section Subtitle\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 952,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    id: \"faqSubtitle\",\n                                                                    value: ((_content_faq3 = content.faq) === null || _content_faq3 === void 0 ? void 0 : _content_faq3.subtitle) || \"\",\n                                                                    onChange: (e)=>updateContent(\"faq\", \"subtitle\", e.target.value),\n                                                                    placeholder: \"Everything you need to know\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 953,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 951,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 941,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    className: \"text-base font-medium\",\n                                                                    children: \"FAQ Items\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 965,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                    onClick: addFAQ,\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                            lineNumber: 967,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Add FAQ\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 966,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 964,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: (_content_faq4 = content.faq) === null || _content_faq4 === void 0 ? void 0 : (_content_faq_items = _content_faq4.items) === null || _content_faq_items === void 0 ? void 0 : _content_faq_items.map((faq, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                                    className: \"p-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-start justify-between mb-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: [\n                                                                                        \"FAQ \",\n                                                                                        index + 1\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                    lineNumber: 976,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    variant: \"ghost\",\n                                                                                    size: \"sm\",\n                                                                                    onClick: ()=>removeFAQ(faq.id),\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Layout_Monitor_Plus_Quote_RefreshCw_Save_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                        className: \"h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                        lineNumber: 982,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                    lineNumber: 977,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                            lineNumber: 975,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                                            children: \"Question\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                            lineNumber: 987,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                            value: faq.question,\n                                                                                            onChange: (e)=>updateFAQ(faq.id, \"question\", e.target.value),\n                                                                                            placeholder: \"What is your question?\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                            lineNumber: 988,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                    lineNumber: 986,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                                            children: \"Answer\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                            lineNumber: 995,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__.Textarea, {\n                                                                                            value: faq.answer,\n                                                                                            onChange: (e)=>updateFAQ(faq.id, \"answer\", e.target.value),\n                                                                                            placeholder: \"Provide a detailed answer...\",\n                                                                                            rows: 3\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                            lineNumber: 996,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                                    lineNumber: 994,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                            lineNumber: 985,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, faq.id, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 974,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 972,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 963,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 940,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                    lineNumber: 922,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                            lineNumber: 915,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                        lineNumber: 914,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                        value: \"cta\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"Call-to-Action Section\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 1018,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"Configure the final call-to-action section\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 1019,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                    lineNumber: 1017,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-4 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            htmlFor: \"ctaEnabled\",\n                                                            className: \"text-base font-medium\",\n                                                            children: \"Enable CTA Section\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 1026,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Show the call-to-action section on your landing page\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 1029,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 1025,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                    id: \"ctaEnabled\",\n                                                    checked: ((_content_cta = content.cta) === null || _content_cta === void 0 ? void 0 : _content_cta.enabled) || false,\n                                                    onCheckedChange: (checked)=>updateContent(\"cta\", \"enabled\", checked)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 1033,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 1024,\n                                            columnNumber: 15\n                                        }, this),\n                                        ((_content_cta1 = content.cta) === null || _content_cta1 === void 0 ? void 0 : _content_cta1.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"ctaTitle\",\n                                                                    children: \"CTA Title\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 1044,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    id: \"ctaTitle\",\n                                                                    value: ((_content_cta2 = content.cta) === null || _content_cta2 === void 0 ? void 0 : _content_cta2.title) || \"\",\n                                                                    onChange: (e)=>updateContent(\"cta\", \"title\", e.target.value),\n                                                                    placeholder: \"Ready to Get Started?\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 1045,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 1043,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"ctaButtonText\",\n                                                                    children: \"Button Text\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 1053,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    id: \"ctaButtonText\",\n                                                                    value: ((_content_cta3 = content.cta) === null || _content_cta3 === void 0 ? void 0 : _content_cta3.buttonText) || \"\",\n                                                                    onChange: (e)=>updateContent(\"cta\", \"buttonText\", e.target.value),\n                                                                    placeholder: \"Start Your Free Trial\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 1054,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 1052,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 1042,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            htmlFor: \"ctaDescription\",\n                                                            children: \"CTA Description\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 1064,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__.Textarea, {\n                                                            id: \"ctaDescription\",\n                                                            value: ((_content_cta4 = content.cta) === null || _content_cta4 === void 0 ? void 0 : _content_cta4.description) || \"\",\n                                                            onChange: (e)=>updateContent(\"cta\", \"description\", e.target.value),\n                                                            placeholder: \"Join thousands of businesses already using our platform...\",\n                                                            rows: 3\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 1065,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 1063,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"ctaButtonLink\",\n                                                                    children: \"Button Link\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 1076,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    id: \"ctaButtonLink\",\n                                                                    value: ((_content_cta5 = content.cta) === null || _content_cta5 === void 0 ? void 0 : _content_cta5.buttonLink) || \"\",\n                                                                    onChange: (e)=>updateContent(\"cta\", \"buttonLink\", e.target.value),\n                                                                    placeholder: \"/auth/signup\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 1077,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 1075,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"ctaBackgroundImage\",\n                                                                    children: \"Background Image URL\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 1085,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    id: \"ctaBackgroundImage\",\n                                                                    value: ((_content_cta6 = content.cta) === null || _content_cta6 === void 0 ? void 0 : _content_cta6.backgroundImage) || \"\",\n                                                                    onChange: (e)=>updateContent(\"cta\", \"backgroundImage\", e.target.value),\n                                                                    placeholder: \"https://example.com/cta-bg.jpg\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                                    lineNumber: 1086,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 1084,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 1074,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 1041,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                    lineNumber: 1023,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                            lineNumber: 1016,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                        lineNumber: 1015,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                        value: \"seo\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"SEO Settings\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 1104,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"Configure SEO meta tags and social sharing\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 1105,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                    lineNumber: 1103,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            htmlFor: \"seoTitle\",\n                                                            children: \"Page Title\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 1112,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                            id: \"seoTitle\",\n                                                            value: ((_content_seo = content.seo) === null || _content_seo === void 0 ? void 0 : _content_seo.title) || \"\",\n                                                            onChange: (e)=>updateContent(\"seo\", \"title\", e.target.value),\n                                                            placeholder: \"SaaS Platform - Build Your Business\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 1113,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: \"Recommended: 50-60 characters\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 1119,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 1111,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            htmlFor: \"seoKeywords\",\n                                                            children: \"Keywords\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 1122,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                            id: \"seoKeywords\",\n                                                            value: ((_content_seo1 = content.seo) === null || _content_seo1 === void 0 ? void 0 : _content_seo1.keywords) || \"\",\n                                                            onChange: (e)=>updateContent(\"seo\", \"keywords\", e.target.value),\n                                                            placeholder: \"saas, platform, business, management\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 1123,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: \"Comma-separated keywords\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                            lineNumber: 1129,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 1121,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 1110,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                    htmlFor: \"seoDescription\",\n                                                    children: \"Meta Description\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 1134,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__.Textarea, {\n                                                    id: \"seoDescription\",\n                                                    value: ((_content_seo2 = content.seo) === null || _content_seo2 === void 0 ? void 0 : _content_seo2.description) || \"\",\n                                                    onChange: (e)=>updateContent(\"seo\", \"description\", e.target.value),\n                                                    placeholder: \"The complete SaaS platform for modern businesses...\",\n                                                    rows: 3\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 1135,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: \"Recommended: 150-160 characters\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 1142,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 1133,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                    htmlFor: \"seoOgImage\",\n                                                    children: \"Open Graph Image URL\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 1146,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                    id: \"seoOgImage\",\n                                                    value: ((_content_seo3 = content.seo) === null || _content_seo3 === void 0 ? void 0 : _content_seo3.ogImage) || \"\",\n                                                    onChange: (e)=>updateContent(\"seo\", \"ogImage\", e.target.value),\n                                                    placeholder: \"https://example.com/og-image.jpg\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 1147,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: \"Recommended: 1200x630px\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                                    lineNumber: 1153,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                            lineNumber: 1145,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                                    lineNumber: 1109,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                            lineNumber: 1102,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                        lineNumber: 1101,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n                lineNumber: 506,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\cms\\\\page.tsx\",\n        lineNumber: 479,\n        columnNumber: 5\n    }, this);\n}\n_s(CMSPage, \"PXqsaU0wp/5yXV80WFnZdgCyYW0=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession\n    ];\n});\n_c = CMSPage;\nvar _c;\n$RefreshReg$(_c, \"CMSPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9zdXBlci1hZG1pbi9jbXMvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTJDO0FBQ0M7QUFDRjtBQUNzRDtBQUNqRDtBQUNGO0FBQ0E7QUFDTTtBQUNKO0FBQ2dDO0FBbUIxRDtBQUNrQjtBQWlIeEIsU0FBUzRCO1FBcUpsQkMsZUE4WHFCQyxtQkFLWkEsb0JBT2dCQSxvQkFTQUEsb0JBa0JSQSx5QkFBQUEsb0JBa0ZJQSx1QkFLWkEsd0JBT2dCQSx3QkFTQUEsd0JBa0JSQSw2QkFBQUEsd0JBa0dJQSxjQUtaQSxlQU9nQkEsZUFTQUEsZUFrQlJBLG9CQUFBQSxlQThESUEsY0FLWkEsZUFPZ0JBLGVBU0FBLGVBV0ZBLGVBWUVBLGVBU0FBLGVBMkJKQSxjQVVBQSxlQVlGQSxlQVlBQTs7SUEzK0J2QixNQUFNLEVBQUVDLE1BQU1GLE9BQU8sRUFBRUcsTUFBTSxFQUFFLEdBQUc5QiwyREFBVUE7SUFDNUMsTUFBTSxDQUFDNEIsU0FBU0csV0FBVyxHQUFHakMsK0NBQVFBLENBQWE7UUFDakRrQyxNQUFNO1lBQ0pDLFNBQVM7WUFDVEMsT0FBTztZQUNQQyxVQUFVO1lBQ1ZDLGFBQWE7WUFDYkMsWUFBWTtnQkFDVkMsTUFBTTtnQkFDTkMsTUFBTTtZQUNSO1lBQ0FDLGNBQWM7Z0JBQ1pGLE1BQU07Z0JBQ05DLE1BQU07WUFDUjtZQUNBRSxpQkFBaUI7WUFDakJDLGlCQUFpQjtRQUNuQjtRQUNBQyxVQUFVO1lBQ1JWLFNBQVM7WUFDVEMsT0FBTztZQUNQQyxVQUFVO1lBQ1ZTLE9BQU87Z0JBQ0w7b0JBQ0VDLElBQUk7b0JBQ0pYLE9BQU87b0JBQ1BFLGFBQWE7b0JBQ2JVLE1BQU07b0JBQ05DLE9BQU87Z0JBQ1Q7Z0JBQ0E7b0JBQ0VGLElBQUk7b0JBQ0pYLE9BQU87b0JBQ1BFLGFBQWE7b0JBQ2JVLE1BQU07b0JBQ05DLE9BQU87Z0JBQ1Q7Z0JBQ0E7b0JBQ0VGLElBQUk7b0JBQ0pYLE9BQU87b0JBQ1BFLGFBQWE7b0JBQ2JVLE1BQU07b0JBQ05DLE9BQU87Z0JBQ1Q7YUFDRDtRQUNIO1FBQ0FDLFNBQVM7WUFDUGYsU0FBUztZQUNUQyxPQUFPO1lBQ1BDLFVBQVU7WUFDVmMsa0JBQWtCO1lBQ2xCQyxlQUFlO1FBQ2pCO1FBQ0FDLGNBQWM7WUFDWmxCLFNBQVM7WUFDVEMsT0FBTztZQUNQQyxVQUFVO1lBQ1ZTLE9BQU87Z0JBQ0w7b0JBQ0VDLElBQUk7b0JBQ0pPLE1BQU07b0JBQ05DLE1BQU07b0JBQ05DLFNBQVM7b0JBQ1QxQixTQUFTO29CQUNUMkIsUUFBUTtvQkFDUkMsUUFBUTtnQkFDVjthQUNEO1FBQ0g7UUFDQUMsS0FBSztZQUNIeEIsU0FBUztZQUNUQyxPQUFPO1lBQ1BDLFVBQVU7WUFDVlMsT0FBTztnQkFDTDtvQkFDRUMsSUFBSTtvQkFDSmEsVUFBVTtvQkFDVkMsUUFBUTtnQkFDVjtnQkFDQTtvQkFDRWQsSUFBSTtvQkFDSmEsVUFBVTtvQkFDVkMsUUFBUTtnQkFDVjthQUNEO1FBQ0g7UUFDQUMsS0FBSztZQUNIM0IsU0FBUztZQUNUQyxPQUFPO1lBQ1BFLGFBQWE7WUFDYnlCLFlBQVk7WUFDWkMsWUFBWTtZQUNackIsaUJBQWlCO1FBQ25CO1FBQ0FzQixRQUFRO1lBQ045QixTQUFTO1lBQ1QrQixvQkFBb0I7WUFDcEJDLE9BQU87Z0JBQ0w7b0JBQ0VwQixJQUFJO29CQUNKWCxPQUFPO29CQUNQVSxPQUFPO3dCQUNMOzRCQUFFQyxJQUFJOzRCQUFLUCxNQUFNOzRCQUFZQyxNQUFNO3dCQUFZO3dCQUMvQzs0QkFBRU0sSUFBSTs0QkFBS1AsTUFBTTs0QkFBV0MsTUFBTTt3QkFBVzt3QkFDN0M7NEJBQUVNLElBQUk7NEJBQUtQLE1BQU07NEJBQVlDLE1BQU07d0JBQVk7cUJBQ2hEO2dCQUNIO2dCQUNBO29CQUNFTSxJQUFJO29CQUNKWCxPQUFPO29CQUNQVSxPQUFPO3dCQUNMOzRCQUFFQyxJQUFJOzRCQUFLUCxNQUFNOzRCQUFTQyxNQUFNO3dCQUFTO3dCQUN6Qzs0QkFBRU0sSUFBSTs0QkFBS1AsTUFBTTs0QkFBUUMsTUFBTTt3QkFBUTt3QkFDdkM7NEJBQUVNLElBQUk7NEJBQUtQLE1BQU07NEJBQVdDLE1BQU07d0JBQVc7cUJBQzlDO2dCQUNIO2FBQ0Q7WUFDRDJCLGFBQWE7Z0JBQ1hDLFNBQVM7Z0JBQ1RDLFVBQVU7Z0JBQ1ZDLFVBQVU7Z0JBQ1ZDLFdBQVc7WUFDYjtZQUNBQyxlQUFlO1FBQ2pCO1FBQ0FDLEtBQUs7WUFDSHRDLE9BQU87WUFDUEUsYUFBYTtZQUNicUMsVUFBVTtZQUNWQyxTQUFTO1FBQ1g7SUFDRjtJQUNBLE1BQU0sQ0FBQ0MsU0FBU0MsV0FBVyxHQUFHOUUsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDK0UsUUFBUUMsVUFBVSxHQUFHaEYsK0NBQVFBLENBQUM7SUFDckMsTUFBTSxDQUFDaUYsYUFBYUMsZUFBZSxHQUFHbEYsK0NBQVFBLENBQUM7SUFFL0MsSUFBSWdDLFdBQVcsV0FBVztRQUN4QixxQkFDRSw4REFBQ21EO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNEO2dCQUFJQyxXQUFVOzs7Ozs7Ozs7OztJQUdyQjtJQUVBLElBQUlwRCxXQUFXLG1CQUFtQjtRQUNoQzdCLHlEQUFRQSxDQUFDO0lBQ1g7SUFFQSxJQUFJMEIsQ0FBQUEsb0JBQUFBLCtCQUFBQSxnQkFBQUEsUUFBU3dELElBQUksY0FBYnhELG9DQUFBQSxjQUFlMEIsSUFBSSxNQUFLLGVBQWU7UUFDekNwRCx5REFBUUEsQ0FBQztJQUNYO0lBRUEsTUFBTW1GLGVBQWU7UUFDbkIsSUFBSTtZQUNGUixXQUFXO1lBQ1gsTUFBTVMsV0FBVyxNQUFNQyxNQUFNLHdCQUF3QjtnQkFDbkRDLGFBQWE7WUFDZjtZQUNBLE1BQU0xRCxPQUFPLE1BQU13RCxTQUFTRyxJQUFJO1lBRWhDLElBQUkzRCxLQUFLNEQsT0FBTyxFQUFFO2dCQUNoQjFELFdBQVc7b0JBQUUsR0FBR0gsT0FBTztvQkFBRSxHQUFHQyxLQUFLRCxPQUFPO2dCQUFDO1lBQzNDO1FBQ0YsRUFBRSxPQUFPOEQsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsK0JBQStCQTtZQUM3Q2pFLG1EQUFLQSxDQUFDaUUsS0FBSyxDQUFDO1FBQ2QsU0FBVTtZQUNSZCxXQUFXO1FBQ2I7SUFDRjtJQUVBLE1BQU1nQixjQUFjO1FBQ2xCLElBQUk7WUFDRmQsVUFBVTtZQUNWLE1BQU1PLFdBQVcsTUFBTUMsTUFBTSx3QkFBd0I7Z0JBQ25ETyxRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FQLGFBQWE7Z0JBQ2JRLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQ3JFO1lBQ3ZCO1lBRUEsTUFBTUMsT0FBTyxNQUFNd0QsU0FBU0csSUFBSTtZQUVoQyxJQUFJM0QsS0FBSzRELE9BQU8sRUFBRTtnQkFDaEJoRSxtREFBS0EsQ0FBQ2dFLE9BQU8sQ0FBQztZQUNoQixPQUFPO2dCQUNMaEUsbURBQUtBLENBQUNpRSxLQUFLLENBQUM3RCxLQUFLNkQsS0FBSyxJQUFJO1lBQzVCO1FBQ0YsRUFBRSxPQUFPQSxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyw2QkFBNkJBO1lBQzNDakUsbURBQUtBLENBQUNpRSxLQUFLLENBQUM7UUFDZCxTQUFVO1lBQ1JaLFVBQVU7UUFDWjtJQUNGO0lBRUEvRSxnREFBU0EsQ0FBQztRQUNScUY7SUFDRixHQUFHLEVBQUU7SUFFTCxNQUFNYyxnQkFBZ0IsQ0FBQ0MsU0FBMkJDLEtBQWFDO1FBQzdEdEUsV0FBV3VFLENBQUFBLE9BQVM7Z0JBQ2xCLEdBQUdBLElBQUk7Z0JBQ1AsQ0FBQ0gsUUFBUSxFQUFFO29CQUNULEdBQUdHLElBQUksQ0FBQ0gsUUFBUTtvQkFDaEIsQ0FBQ0MsSUFBSSxFQUFFQztnQkFDVDtZQUNGO0lBQ0Y7SUFFQSwrQkFBK0I7SUFDL0IsTUFBTUUsYUFBYTtRQUNqQixNQUFNQyxhQUFhO1lBQ2pCM0QsSUFBSTRELEtBQUtDLEdBQUcsR0FBR0MsUUFBUTtZQUN2QnpFLE9BQU87WUFDUEUsYUFBYTtZQUNiVSxNQUFNO1lBQ05DLE9BQU87UUFDVDtRQUNBaEIsV0FBV3VFLENBQUFBO2dCQUlLQTttQkFKSTtnQkFDbEIsR0FBR0EsSUFBSTtnQkFDUDNELFVBQVU7b0JBQ1IsR0FBRzJELEtBQUszRCxRQUFRO29CQUNoQkMsT0FBTzsyQkFBSzBELEVBQUFBLGlCQUFBQSxLQUFLM0QsUUFBUSxjQUFiMkQscUNBQUFBLGVBQWUxRCxLQUFLLEtBQUksRUFBRTt3QkFBRzREO3FCQUFXO2dCQUN0RDtZQUNGOztJQUNGO0lBRUEsTUFBTUksZ0JBQWdCLENBQUMvRDtRQUNyQmQsV0FBV3VFLENBQUFBO2dCQUlBQSxzQkFBQUE7bUJBSlM7Z0JBQ2xCLEdBQUdBLElBQUk7Z0JBQ1AzRCxVQUFVO29CQUNSLEdBQUcyRCxLQUFLM0QsUUFBUTtvQkFDaEJDLE9BQU8wRCxFQUFBQSxpQkFBQUEsS0FBSzNELFFBQVEsY0FBYjJELHNDQUFBQSx1QkFBQUEsZUFBZTFELEtBQUssY0FBcEIwRCwyQ0FBQUEscUJBQXNCTyxNQUFNLENBQUNDLENBQUFBLE9BQVFBLEtBQUtqRSxFQUFFLEtBQUtBLFFBQU8sRUFBRTtnQkFDbkU7WUFDRjs7SUFDRjtJQUVBLE1BQU1rRSxnQkFBZ0IsQ0FBQ2xFLElBQVl1RCxLQUFhQztRQUM5Q3RFLFdBQVd1RSxDQUFBQTtnQkFJQUEsc0JBQUFBO21CQUpTO2dCQUNsQixHQUFHQSxJQUFJO2dCQUNQM0QsVUFBVTtvQkFDUixHQUFHMkQsS0FBSzNELFFBQVE7b0JBQ2hCQyxPQUFPMEQsRUFBQUEsaUJBQUFBLEtBQUszRCxRQUFRLGNBQWIyRCxzQ0FBQUEsdUJBQUFBLGVBQWUxRCxLQUFLLGNBQXBCMEQsMkNBQUFBLHFCQUFzQlUsR0FBRyxDQUFDRixDQUFBQSxPQUMvQkEsS0FBS2pFLEVBQUUsS0FBS0EsS0FBSzs0QkFBRSxHQUFHaUUsSUFBSTs0QkFBRSxDQUFDVixJQUFJLEVBQUVDO3dCQUFNLElBQUlTLFVBQzFDLEVBQUU7Z0JBQ1Q7WUFDRjs7SUFDRjtJQUVBLG1DQUFtQztJQUNuQyxNQUFNRyxpQkFBaUI7UUFDckIsTUFBTUMsaUJBQWlCO1lBQ3JCckUsSUFBSTRELEtBQUtDLEdBQUcsR0FBR0MsUUFBUTtZQUN2QnZELE1BQU07WUFDTkMsTUFBTTtZQUNOQyxTQUFTO1lBQ1QxQixTQUFTO1lBQ1QyQixRQUFRO1lBQ1JDLFFBQVE7UUFDVjtRQUNBekIsV0FBV3VFLENBQUFBO2dCQUlLQTttQkFKSTtnQkFDbEIsR0FBR0EsSUFBSTtnQkFDUG5ELGNBQWM7b0JBQ1osR0FBR21ELEtBQUtuRCxZQUFZO29CQUNwQlAsT0FBTzsyQkFBSzBELEVBQUFBLHFCQUFBQSxLQUFLbkQsWUFBWSxjQUFqQm1ELHlDQUFBQSxtQkFBbUIxRCxLQUFLLEtBQUksRUFBRTt3QkFBR3NFO3FCQUFlO2dCQUM5RDtZQUNGOztJQUNGO0lBRUEsTUFBTUMsb0JBQW9CLENBQUN0RTtRQUN6QmQsV0FBV3VFLENBQUFBO2dCQUlBQSwwQkFBQUE7bUJBSlM7Z0JBQ2xCLEdBQUdBLElBQUk7Z0JBQ1BuRCxjQUFjO29CQUNaLEdBQUdtRCxLQUFLbkQsWUFBWTtvQkFDcEJQLE9BQU8wRCxFQUFBQSxxQkFBQUEsS0FBS25ELFlBQVksY0FBakJtRCwwQ0FBQUEsMkJBQUFBLG1CQUFtQjFELEtBQUssY0FBeEIwRCwrQ0FBQUEseUJBQTBCTyxNQUFNLENBQUNDLENBQUFBLE9BQVFBLEtBQUtqRSxFQUFFLEtBQUtBLFFBQU8sRUFBRTtnQkFDdkU7WUFDRjs7SUFDRjtJQUVBLE1BQU11RSxvQkFBb0IsQ0FBQ3ZFLElBQVl1RCxLQUFhQztRQUNsRHRFLFdBQVd1RSxDQUFBQTtnQkFJQUEsMEJBQUFBO21CQUpTO2dCQUNsQixHQUFHQSxJQUFJO2dCQUNQbkQsY0FBYztvQkFDWixHQUFHbUQsS0FBS25ELFlBQVk7b0JBQ3BCUCxPQUFPMEQsRUFBQUEscUJBQUFBLEtBQUtuRCxZQUFZLGNBQWpCbUQsMENBQUFBLDJCQUFBQSxtQkFBbUIxRCxLQUFLLGNBQXhCMEQsK0NBQUFBLHlCQUEwQlUsR0FBRyxDQUFDRixDQUFBQSxPQUNuQ0EsS0FBS2pFLEVBQUUsS0FBS0EsS0FBSzs0QkFBRSxHQUFHaUUsSUFBSTs0QkFBRSxDQUFDVixJQUFJLEVBQUVDO3dCQUFNLElBQUlTLFVBQzFDLEVBQUU7Z0JBQ1Q7WUFDRjs7SUFDRjtJQUVBLDJCQUEyQjtJQUMzQixNQUFNTyxTQUFTO1FBQ2IsTUFBTUMsU0FBUztZQUNiekUsSUFBSTRELEtBQUtDLEdBQUcsR0FBR0MsUUFBUTtZQUN2QmpELFVBQVU7WUFDVkMsUUFBUTtRQUNWO1FBQ0E1QixXQUFXdUUsQ0FBQUE7Z0JBSUtBO21CQUpJO2dCQUNsQixHQUFHQSxJQUFJO2dCQUNQN0MsS0FBSztvQkFDSCxHQUFHNkMsS0FBSzdDLEdBQUc7b0JBQ1hiLE9BQU87MkJBQUswRCxFQUFBQSxZQUFBQSxLQUFLN0MsR0FBRyxjQUFSNkMsZ0NBQUFBLFVBQVUxRCxLQUFLLEtBQUksRUFBRTt3QkFBRzBFO3FCQUFPO2dCQUM3QztZQUNGOztJQUNGO0lBRUEsTUFBTUMsWUFBWSxDQUFDMUU7UUFDakJkLFdBQVd1RSxDQUFBQTtnQkFJQUEsaUJBQUFBO21CQUpTO2dCQUNsQixHQUFHQSxJQUFJO2dCQUNQN0MsS0FBSztvQkFDSCxHQUFHNkMsS0FBSzdDLEdBQUc7b0JBQ1hiLE9BQU8wRCxFQUFBQSxZQUFBQSxLQUFLN0MsR0FBRyxjQUFSNkMsaUNBQUFBLGtCQUFBQSxVQUFVMUQsS0FBSyxjQUFmMEQsc0NBQUFBLGdCQUFpQk8sTUFBTSxDQUFDQyxDQUFBQSxPQUFRQSxLQUFLakUsRUFBRSxLQUFLQSxRQUFPLEVBQUU7Z0JBQzlEO1lBQ0Y7O0lBQ0Y7SUFFQSxNQUFNMkUsWUFBWSxDQUFDM0UsSUFBWXVELEtBQWFDO1FBQzFDdEUsV0FBV3VFLENBQUFBO2dCQUlBQSxpQkFBQUE7bUJBSlM7Z0JBQ2xCLEdBQUdBLElBQUk7Z0JBQ1A3QyxLQUFLO29CQUNILEdBQUc2QyxLQUFLN0MsR0FBRztvQkFDWGIsT0FBTzBELEVBQUFBLFlBQUFBLEtBQUs3QyxHQUFHLGNBQVI2QyxpQ0FBQUEsa0JBQUFBLFVBQVUxRCxLQUFLLGNBQWYwRCxzQ0FBQUEsZ0JBQWlCVSxHQUFHLENBQUNGLENBQUFBLE9BQzFCQSxLQUFLakUsRUFBRSxLQUFLQSxLQUFLOzRCQUFFLEdBQUdpRSxJQUFJOzRCQUFFLENBQUNWLElBQUksRUFBRUM7d0JBQU0sSUFBSVMsVUFDMUMsRUFBRTtnQkFDVDtZQUNGOztJQUNGO0lBRUEscUJBQ0UsOERBQUM3QjtRQUFJQyxXQUFVOzswQkFFYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDs7MENBQ0MsOERBQUNBO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ2xFLHFJQUFPQTt3Q0FBQ2tFLFdBQVU7Ozs7OztrREFDbkIsOERBQUN1Qzt3Q0FBR3ZDLFdBQVU7a0RBQW1DOzs7Ozs7Ozs7Ozs7MENBRW5ELDhEQUFDd0M7Z0NBQUV4QyxXQUFVOzBDQUFxQjs7Ozs7Ozs7Ozs7O2tDQUVwQyw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDM0UseURBQU1BO2dDQUFDb0gsU0FBUTtnQ0FBVUMsU0FBU3hDO2dDQUFjeUMsVUFBVWxEOztrREFDekQsOERBQUN6RCxxSUFBU0E7d0NBQUNnRSxXQUFXLGdCQUE4QyxPQUE5QlAsVUFBVSxpQkFBaUI7Ozs7OztvQ0FBUTs7Ozs7OzswQ0FHM0UsOERBQUNwRSx5REFBTUE7Z0NBQUNvSCxTQUFRO2dDQUFVQyxTQUFTLElBQU01QyxlQUFlLENBQUNEOztrREFDdkQsOERBQUM1RCxxSUFBR0E7d0NBQUMrRCxXQUFVOzs7Ozs7b0NBQ2RILGNBQWMsY0FBYzs7Ozs7OzswQ0FFL0IsOERBQUN4RSx5REFBTUE7Z0NBQUNxSCxTQUFTaEM7Z0NBQWFpQyxVQUFVaEQ7O2tEQUN0Qyw4REFBQzVELHFJQUFJQTt3Q0FBQ2lFLFdBQVcsZ0JBQTZDLE9BQTdCTCxTQUFTLGlCQUFpQjs7Ozs7O29DQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU96RSw4REFBQ2pFLHNEQUFJQTtnQkFBQ2tILGNBQWE7Z0JBQU81QyxXQUFVOztrQ0FDbEMsOERBQUNwRSwwREFBUUE7d0JBQUNvRSxXQUFVOzswQ0FDbEIsOERBQUNuRSw2REFBV0E7Z0NBQUNzRixPQUFNOzBDQUFPOzs7Ozs7MENBQzFCLDhEQUFDdEYsNkRBQVdBO2dDQUFDc0YsT0FBTTswQ0FBVzs7Ozs7OzBDQUM5Qiw4REFBQ3RGLDZEQUFXQTtnQ0FBQ3NGLE9BQU07MENBQVU7Ozs7OzswQ0FDN0IsOERBQUN0Riw2REFBV0E7Z0NBQUNzRixPQUFNOzBDQUFlOzs7Ozs7MENBQ2xDLDhEQUFDdEYsNkRBQVdBO2dDQUFDc0YsT0FBTTswQ0FBTTs7Ozs7OzBDQUN6Qiw4REFBQ3RGLDZEQUFXQTtnQ0FBQ3NGLE9BQU07MENBQU07Ozs7OzswQ0FDekIsOERBQUN0Riw2REFBV0E7Z0NBQUNzRixPQUFNOzBDQUFNOzs7Ozs7Ozs7Ozs7a0NBSTNCLDhEQUFDeEYsNkRBQVdBO3dCQUFDd0YsT0FBTTtrQ0FDakIsNEVBQUNuRyxxREFBSUE7OzhDQUNILDhEQUFDRywyREFBVUE7O3NEQUNULDhEQUFDQywwREFBU0E7NENBQUM0RSxXQUFVOzs4REFDbkIsOERBQUM1RCxxSUFBTUE7b0RBQUM0RCxXQUFVOzs7Ozs7Z0RBQWlCOzs7Ozs7O3NEQUdyQyw4REFBQzlFLGdFQUFlQTtzREFBQzs7Ozs7Ozs7Ozs7OzhDQUluQiw4REFBQ0QsNERBQVdBO29DQUFDK0UsV0FBVTs7c0RBQ3JCLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEOztzRUFDQyw4REFBQ3hFLHVEQUFLQTs0REFBQ3NILFNBQVE7NERBQWM3QyxXQUFVO3NFQUF3Qjs7Ozs7O3NFQUcvRCw4REFBQ3dDOzREQUFFeEMsV0FBVTtzRUFBd0I7Ozs7Ozs7Ozs7Ozs4REFJdkMsOERBQUN2RSx5REFBTUE7b0RBQ0xrQyxJQUFHO29EQUNIbUYsU0FBU3BHLFFBQVFJLElBQUksQ0FBQ0MsT0FBTztvREFDN0JnRyxpQkFBaUIsQ0FBQ0QsVUFBWTlCLGNBQWMsUUFBUSxXQUFXOEI7Ozs7Ozs7Ozs7Ozt3Q0FJbEVwRyxRQUFRSSxJQUFJLENBQUNDLE9BQU8sa0JBQ25CLDhEQUFDZ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ3pFLHVEQUFLQTtvRUFBQ3NILFNBQVE7OEVBQVk7Ozs7Ozs4RUFDM0IsOERBQUN2SCx1REFBS0E7b0VBQ0pxQyxJQUFHO29FQUNId0QsT0FBT3pFLFFBQVFJLElBQUksQ0FBQ0UsS0FBSztvRUFDekJnRyxVQUFVLENBQUNDLElBQU1qQyxjQUFjLFFBQVEsU0FBU2lDLEVBQUVDLE1BQU0sQ0FBQy9CLEtBQUs7b0VBQzlEZ0MsYUFBWTs7Ozs7Ozs7Ozs7O3NFQUdoQiw4REFBQ3BEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ3pFLHVEQUFLQTtvRUFBQ3NILFNBQVE7OEVBQWU7Ozs7Ozs4RUFDOUIsOERBQUN2SCx1REFBS0E7b0VBQ0pxQyxJQUFHO29FQUNId0QsT0FBT3pFLFFBQVFJLElBQUksQ0FBQ0csUUFBUTtvRUFDNUIrRixVQUFVLENBQUNDLElBQU1qQyxjQUFjLFFBQVEsWUFBWWlDLEVBQUVDLE1BQU0sQ0FBQy9CLEtBQUs7b0VBQ2pFZ0MsYUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQUtsQiw4REFBQ3BEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ3pFLHVEQUFLQTs0REFBQ3NILFNBQVE7c0VBQWtCOzs7Ozs7c0VBQ2pDLDhEQUFDckgsNkRBQVFBOzREQUNQbUMsSUFBRzs0REFDSHdELE9BQU96RSxRQUFRSSxJQUFJLENBQUNJLFdBQVc7NERBQy9COEYsVUFBVSxDQUFDQyxJQUFNakMsY0FBYyxRQUFRLGVBQWVpQyxFQUFFQyxNQUFNLENBQUMvQixLQUFLOzREQUNwRWdDLGFBQVk7NERBQ1pDLE1BQU07Ozs7Ozs7Ozs7Ozs4REFJViw4REFBQ3JEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0Q7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDekUsdURBQUtBO29FQUFDc0gsU0FBUTs4RUFBaUI7Ozs7Ozs4RUFDaEMsOERBQUN2SCx1REFBS0E7b0VBQ0pxQyxJQUFHO29FQUNId0QsT0FBT3pFLFFBQVFJLElBQUksQ0FBQ0ssVUFBVSxDQUFDQyxJQUFJO29FQUNuQzRGLFVBQVUsQ0FBQ0MsSUFBTWpDLGNBQWMsUUFBUSxjQUFjOzRFQUFFLEdBQUd0RSxRQUFRSSxJQUFJLENBQUNLLFVBQVU7NEVBQUVDLE1BQU02RixFQUFFQyxNQUFNLENBQUMvQixLQUFLO3dFQUFDO29FQUN4R2dDLGFBQVk7Ozs7Ozs7Ozs7OztzRUFHaEIsOERBQUNwRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUN6RSx1REFBS0E7b0VBQUNzSCxTQUFROzhFQUFpQjs7Ozs7OzhFQUNoQyw4REFBQ3ZILHVEQUFLQTtvRUFDSnFDLElBQUc7b0VBQ0h3RCxPQUFPekUsUUFBUUksSUFBSSxDQUFDSyxVQUFVLENBQUNFLElBQUk7b0VBQ25DMkYsVUFBVSxDQUFDQyxJQUFNakMsY0FBYyxRQUFRLGNBQWM7NEVBQUUsR0FBR3RFLFFBQVFJLElBQUksQ0FBQ0ssVUFBVTs0RUFBRUUsTUFBTTRGLEVBQUVDLE1BQU0sQ0FBQy9CLEtBQUs7d0VBQUM7b0VBQ3hHZ0MsYUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQUtsQiw4REFBQ3BEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0Q7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDekUsdURBQUtBO29FQUFDc0gsU0FBUTs4RUFBbUI7Ozs7Ozs4RUFDbEMsOERBQUN2SCx1REFBS0E7b0VBQ0pxQyxJQUFHO29FQUNId0QsT0FBT3pFLFFBQVFJLElBQUksQ0FBQ1EsWUFBWSxDQUFDRixJQUFJO29FQUNyQzRGLFVBQVUsQ0FBQ0MsSUFBTWpDLGNBQWMsUUFBUSxnQkFBZ0I7NEVBQUUsR0FBR3RFLFFBQVFJLElBQUksQ0FBQ1EsWUFBWTs0RUFBRUYsTUFBTTZGLEVBQUVDLE1BQU0sQ0FBQy9CLEtBQUs7d0VBQUM7b0VBQzVHZ0MsYUFBWTs7Ozs7Ozs7Ozs7O3NFQUdoQiw4REFBQ3BEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ3pFLHVEQUFLQTtvRUFBQ3NILFNBQVE7OEVBQW1COzs7Ozs7OEVBQ2xDLDhEQUFDdkgsdURBQUtBO29FQUNKcUMsSUFBRztvRUFDSHdELE9BQU96RSxRQUFRSSxJQUFJLENBQUNRLFlBQVksQ0FBQ0QsSUFBSTtvRUFDckMyRixVQUFVLENBQUNDLElBQU1qQyxjQUFjLFFBQVEsZ0JBQWdCOzRFQUFFLEdBQUd0RSxRQUFRSSxJQUFJLENBQUNRLFlBQVk7NEVBQUVELE1BQU00RixFQUFFQyxNQUFNLENBQUMvQixLQUFLO3dFQUFDO29FQUM1R2dDLGFBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFNbEIsOERBQUNwRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUN6RSx1REFBS0E7NERBQUN5RSxXQUFVO3NFQUF3Qjs7Ozs7O3NFQUN6Qyw4REFBQ0Q7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDRDtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUN6RSx1REFBS0E7NEVBQUNzSCxTQUFRO3NGQUFzQjs7Ozs7O3NGQUNyQyw4REFBQ3ZILHVEQUFLQTs0RUFDSnFDLElBQUc7NEVBQ0h3RCxPQUFPekUsUUFBUUksSUFBSSxDQUFDUyxlQUFlOzRFQUNuQ3lGLFVBQVUsQ0FBQ0MsSUFBTWpDLGNBQWMsUUFBUSxtQkFBbUJpQyxFQUFFQyxNQUFNLENBQUMvQixLQUFLOzRFQUN4RWdDLGFBQVk7Ozs7Ozs7Ozs7Ozs4RUFHaEIsOERBQUNwRDtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUN6RSx1REFBS0E7NEVBQUNzSCxTQUFRO3NGQUFzQjs7Ozs7O3NGQUNyQyw4REFBQ3ZILHVEQUFLQTs0RUFDSnFDLElBQUc7NEVBQ0h3RCxPQUFPekUsUUFBUUksSUFBSSxDQUFDVSxlQUFlOzRFQUNuQ3dGLFVBQVUsQ0FBQ0MsSUFBTWpDLGNBQWMsUUFBUSxtQkFBbUJpQyxFQUFFQyxNQUFNLENBQUMvQixLQUFLOzRFQUN4RWdDLGFBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQVk5Qiw4REFBQ3hILDZEQUFXQTt3QkFBQ3dGLE9BQU07a0NBQ2pCLDRFQUFDbkcscURBQUlBOzs4Q0FDSCw4REFBQ0csMkRBQVVBOztzREFDVCw4REFBQ0MsMERBQVNBOzRDQUFDNEUsV0FBVTs7OERBQ25CLDhEQUFDM0QscUlBQUlBO29EQUFDMkQsV0FBVTs7Ozs7O2dEQUFpQjs7Ozs7OztzREFHbkMsOERBQUM5RSxnRUFBZUE7c0RBQUM7Ozs7Ozs7Ozs7Ozs4Q0FJbkIsOERBQUNELDREQUFXQTtvQ0FBQytFLFdBQVU7O3NEQUNyQiw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDs7c0VBQ0MsOERBQUN4RSx1REFBS0E7NERBQUNzSCxTQUFROzREQUFrQjdDLFdBQVU7c0VBQXdCOzs7Ozs7c0VBR25FLDhEQUFDd0M7NERBQUV4QyxXQUFVO3NFQUF3Qjs7Ozs7Ozs7Ozs7OzhEQUl2Qyw4REFBQ3ZFLHlEQUFNQTtvREFDTGtDLElBQUc7b0RBQ0htRixTQUFTcEcsRUFBQUEsb0JBQUFBLFFBQVFlLFFBQVEsY0FBaEJmLHdDQUFBQSxrQkFBa0JLLE9BQU8sS0FBSTtvREFDdENnRyxpQkFBaUIsQ0FBQ0QsVUFBWTlCLGNBQWMsWUFBWSxXQUFXOEI7Ozs7Ozs7Ozs7Ozt3Q0FJdEVwRyxFQUFBQSxxQkFBQUEsUUFBUWUsUUFBUSxjQUFoQmYseUNBQUFBLG1CQUFrQkssT0FBTyxtQkFDeEIsOERBQUNnRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0Q7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDekUsdURBQUtBO29FQUFDc0gsU0FBUTs4RUFBZ0I7Ozs7Ozs4RUFDL0IsOERBQUN2SCx1REFBS0E7b0VBQ0pxQyxJQUFHO29FQUNId0QsT0FBT3pFLEVBQUFBLHFCQUFBQSxRQUFRZSxRQUFRLGNBQWhCZix5Q0FBQUEsbUJBQWtCTSxLQUFLLEtBQUk7b0VBQ2xDZ0csVUFBVSxDQUFDQyxJQUFNakMsY0FBYyxZQUFZLFNBQVNpQyxFQUFFQyxNQUFNLENBQUMvQixLQUFLO29FQUNsRWdDLGFBQVk7Ozs7Ozs7Ozs7OztzRUFHaEIsOERBQUNwRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUN6RSx1REFBS0E7b0VBQUNzSCxTQUFROzhFQUFtQjs7Ozs7OzhFQUNsQyw4REFBQ3ZILHVEQUFLQTtvRUFDSnFDLElBQUc7b0VBQ0h3RCxPQUFPekUsRUFBQUEscUJBQUFBLFFBQVFlLFFBQVEsY0FBaEJmLHlDQUFBQSxtQkFBa0JPLFFBQVEsS0FBSTtvRUFDckMrRixVQUFVLENBQUNDLElBQU1qQyxjQUFjLFlBQVksWUFBWWlDLEVBQUVDLE1BQU0sQ0FBQy9CLEtBQUs7b0VBQ3JFZ0MsYUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQU1sQiw4REFBQ3BEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0Q7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDekUsdURBQUtBO29FQUFDeUUsV0FBVTs4RUFBd0I7Ozs7Ozs4RUFDekMsOERBQUMzRSx5REFBTUE7b0VBQUNxSCxTQUFTckI7b0VBQVlnQyxNQUFLOztzRkFDaEMsOERBQUNuSCxxSUFBSUE7NEVBQUM4RCxXQUFVOzs7Ozs7d0VBQWlCOzs7Ozs7Ozs7Ozs7O3NFQUtyQyw4REFBQ0Q7NERBQUlDLFdBQVU7dUVBQ1p0RCxxQkFBQUEsUUFBUWUsUUFBUSxjQUFoQmYsMENBQUFBLDBCQUFBQSxtQkFBa0JnQixLQUFLLGNBQXZCaEIsOENBQUFBLHdCQUF5Qm9GLEdBQUcsQ0FBQyxDQUFDd0IsU0FBU0Msc0JBQ3RDLDhEQUFDdkkscURBQUlBO29FQUFrQmdGLFdBQVU7O3NGQUMvQiw4REFBQ0Q7NEVBQUlDLFdBQVU7OzhGQUNiLDhEQUFDd0Q7b0ZBQUd4RCxXQUFVOzt3RkFBYzt3RkFBU3VELFFBQVE7Ozs7Ozs7OEZBQzdDLDhEQUFDbEkseURBQU1BO29GQUNMb0gsU0FBUTtvRkFDUlksTUFBSztvRkFDTFgsU0FBUyxJQUFNaEIsY0FBYzRCLFFBQVEzRixFQUFFOzhGQUV2Qyw0RUFBQ3hCLHFJQUFNQTt3RkFBQzZELFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O3NGQUd0Qiw4REFBQ0Q7NEVBQUlDLFdBQVU7OzhGQUNiLDhEQUFDRDtvRkFBSUMsV0FBVTs7c0dBQ2IsOERBQUN6RSx1REFBS0E7c0dBQUM7Ozs7OztzR0FDUCw4REFBQ0QsdURBQUtBOzRGQUNKNkYsT0FBT21DLFFBQVF0RyxLQUFLOzRGQUNwQmdHLFVBQVUsQ0FBQ0MsSUFBTXBCLGNBQWN5QixRQUFRM0YsRUFBRSxFQUFFLFNBQVNzRixFQUFFQyxNQUFNLENBQUMvQixLQUFLOzRGQUNsRWdDLGFBQVk7Ozs7Ozs7Ozs7Ozs4RkFHaEIsOERBQUNwRDtvRkFBSUMsV0FBVTs7c0dBQ2IsOERBQUN6RSx1REFBS0E7c0dBQUM7Ozs7OztzR0FDUCw4REFBQ2tJOzRGQUNDdEMsT0FBT21DLFFBQVExRixJQUFJOzRGQUNuQm9GLFVBQVUsQ0FBQ0MsSUFBTXBCLGNBQWN5QixRQUFRM0YsRUFBRSxFQUFFLFFBQVFzRixFQUFFQyxNQUFNLENBQUMvQixLQUFLOzRGQUNqRW5CLFdBQVU7OzhHQUVWLDhEQUFDMEQ7b0dBQU92QyxPQUFNOzhHQUFROzs7Ozs7OEdBQ3RCLDhEQUFDdUM7b0dBQU92QyxPQUFNOzhHQUFjOzs7Ozs7OEdBQzVCLDhEQUFDdUM7b0dBQU92QyxPQUFNOzhHQUFZOzs7Ozs7OEdBQzFCLDhEQUFDdUM7b0dBQU92QyxPQUFNOzhHQUFXOzs7Ozs7OEdBQ3pCLDhEQUFDdUM7b0dBQU92QyxPQUFNOzhHQUFTOzs7Ozs7OEdBQ3ZCLDhEQUFDdUM7b0dBQU92QyxPQUFNOzhHQUFROzs7Ozs7OEdBQ3RCLDhEQUFDdUM7b0dBQU92QyxPQUFNOzhHQUFNOzs7Ozs7OEdBQ3BCLDhEQUFDdUM7b0dBQU92QyxPQUFNOzhHQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0ZBSWhDLDhEQUFDcEI7NEVBQUlDLFdBQVU7OzhGQUNiLDhEQUFDekUsdURBQUtBOzhGQUFDOzs7Ozs7OEZBQ1AsOERBQUNDLDZEQUFRQTtvRkFDUDJGLE9BQU9tQyxRQUFRcEcsV0FBVztvRkFDMUI4RixVQUFVLENBQUNDLElBQU1wQixjQUFjeUIsUUFBUTNGLEVBQUUsRUFBRSxlQUFlc0YsRUFBRUMsTUFBTSxDQUFDL0IsS0FBSztvRkFDeEVnQyxhQUFZO29GQUNaQyxNQUFNOzs7Ozs7Ozs7Ozs7O21FQTVDREUsUUFBUTNGLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0EwRHJDLDhEQUFDaEMsNkRBQVdBO3dCQUFDd0YsT0FBTTtrQ0FDakIsNEVBQUNuRyxxREFBSUE7OzhDQUNILDhEQUFDRywyREFBVUE7O3NEQUNULDhEQUFDQywwREFBU0E7NENBQUM0RSxXQUFVOzs4REFDbkIsOERBQUMxRCxxSUFBS0E7b0RBQUMwRCxXQUFVOzs7Ozs7Z0RBQWlCOzs7Ozs7O3NEQUdwQyw4REFBQzlFLGdFQUFlQTtzREFBQzs7Ozs7Ozs7Ozs7OzhDQUluQiw4REFBQ0QsNERBQVdBO29DQUFDK0UsV0FBVTs7c0RBQ3JCLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEOztzRUFDQyw4REFBQ3hFLHVEQUFLQTs0REFBQ3NILFNBQVE7NERBQXNCN0MsV0FBVTtzRUFBd0I7Ozs7OztzRUFHdkUsOERBQUN3Qzs0REFBRXhDLFdBQVU7c0VBQXdCOzs7Ozs7Ozs7Ozs7OERBSXZDLDhEQUFDdkUseURBQU1BO29EQUNMa0MsSUFBRztvREFDSG1GLFNBQVNwRyxFQUFBQSx3QkFBQUEsUUFBUXVCLFlBQVksY0FBcEJ2Qiw0Q0FBQUEsc0JBQXNCSyxPQUFPLEtBQUk7b0RBQzFDZ0csaUJBQWlCLENBQUNELFVBQVk5QixjQUFjLGdCQUFnQixXQUFXOEI7Ozs7Ozs7Ozs7Ozt3Q0FJMUVwRyxFQUFBQSx5QkFBQUEsUUFBUXVCLFlBQVksY0FBcEJ2Qiw2Q0FBQUEsdUJBQXNCSyxPQUFPLG1CQUM1Qiw4REFBQ2dEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUN6RSx1REFBS0E7b0VBQUNzSCxTQUFROzhFQUFvQjs7Ozs7OzhFQUNuQyw4REFBQ3ZILHVEQUFLQTtvRUFDSnFDLElBQUc7b0VBQ0h3RCxPQUFPekUsRUFBQUEseUJBQUFBLFFBQVF1QixZQUFZLGNBQXBCdkIsNkNBQUFBLHVCQUFzQk0sS0FBSyxLQUFJO29FQUN0Q2dHLFVBQVUsQ0FBQ0MsSUFBTWpDLGNBQWMsZ0JBQWdCLFNBQVNpQyxFQUFFQyxNQUFNLENBQUMvQixLQUFLO29FQUN0RWdDLGFBQVk7Ozs7Ozs7Ozs7OztzRUFHaEIsOERBQUNwRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUN6RSx1REFBS0E7b0VBQUNzSCxTQUFROzhFQUF1Qjs7Ozs7OzhFQUN0Qyw4REFBQ3ZILHVEQUFLQTtvRUFDSnFDLElBQUc7b0VBQ0h3RCxPQUFPekUsRUFBQUEseUJBQUFBLFFBQVF1QixZQUFZLGNBQXBCdkIsNkNBQUFBLHVCQUFzQk8sUUFBUSxLQUFJO29FQUN6QytGLFVBQVUsQ0FBQ0MsSUFBTWpDLGNBQWMsZ0JBQWdCLFlBQVlpQyxFQUFFQyxNQUFNLENBQUMvQixLQUFLO29FQUN6RWdDLGFBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFNbEIsOERBQUNwRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ3pFLHVEQUFLQTtvRUFBQ3lFLFdBQVU7OEVBQXdCOzs7Ozs7OEVBQ3pDLDhEQUFDM0UseURBQU1BO29FQUFDcUgsU0FBU1g7b0VBQWdCc0IsTUFBSzs7c0ZBQ3BDLDhEQUFDbkgscUlBQUlBOzRFQUFDOEQsV0FBVTs7Ozs7O3dFQUFpQjs7Ozs7Ozs7Ozs7OztzRUFLckMsOERBQUNEOzREQUFJQyxXQUFVO3VFQUNadEQseUJBQUFBLFFBQVF1QixZQUFZLGNBQXBCdkIsOENBQUFBLDhCQUFBQSx1QkFBc0JnQixLQUFLLGNBQTNCaEIsa0RBQUFBLDRCQUE2Qm9GLEdBQUcsQ0FBQyxDQUFDNkIsYUFBYUosc0JBQzlDLDhEQUFDdkkscURBQUlBO29FQUFzQmdGLFdBQVU7O3NGQUNuQyw4REFBQ0Q7NEVBQUlDLFdBQVU7OzhGQUNiLDhEQUFDd0Q7b0ZBQUd4RCxXQUFVOzt3RkFBYzt3RkFBYXVELFFBQVE7Ozs7Ozs7OEZBQ2pELDhEQUFDbEkseURBQU1BO29GQUNMb0gsU0FBUTtvRkFDUlksTUFBSztvRkFDTFgsU0FBUyxJQUFNVCxrQkFBa0IwQixZQUFZaEcsRUFBRTs4RkFFL0MsNEVBQUN4QixxSUFBTUE7d0ZBQUM2RCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztzRkFHdEIsOERBQUNEOzRFQUFJQyxXQUFVOzs4RkFDYiw4REFBQ0Q7b0ZBQUlDLFdBQVU7O3NHQUNiLDhEQUFDekUsdURBQUtBO3NHQUFDOzs7Ozs7c0dBQ1AsOERBQUNELHVEQUFLQTs0RkFDSjZGLE9BQU93QyxZQUFZekYsSUFBSTs0RkFDdkI4RSxVQUFVLENBQUNDLElBQU1mLGtCQUFrQnlCLFlBQVloRyxFQUFFLEVBQUUsUUFBUXNGLEVBQUVDLE1BQU0sQ0FBQy9CLEtBQUs7NEZBQ3pFZ0MsYUFBWTs7Ozs7Ozs7Ozs7OzhGQUdoQiw4REFBQ3BEO29GQUFJQyxXQUFVOztzR0FDYiw4REFBQ3pFLHVEQUFLQTtzR0FBQzs7Ozs7O3NHQUNQLDhEQUFDRCx1REFBS0E7NEZBQ0o2RixPQUFPd0MsWUFBWXhGLElBQUk7NEZBQ3ZCNkUsVUFBVSxDQUFDQyxJQUFNZixrQkFBa0J5QixZQUFZaEcsRUFBRSxFQUFFLFFBQVFzRixFQUFFQyxNQUFNLENBQUMvQixLQUFLOzRGQUN6RWdDLGFBQVk7Ozs7Ozs7Ozs7Ozs4RkFHaEIsOERBQUNwRDtvRkFBSUMsV0FBVTs7c0dBQ2IsOERBQUN6RSx1REFBS0E7c0dBQUM7Ozs7OztzR0FDUCw4REFBQ0QsdURBQUtBOzRGQUNKNkYsT0FBT3dDLFlBQVl2RixPQUFPOzRGQUMxQjRFLFVBQVUsQ0FBQ0MsSUFBTWYsa0JBQWtCeUIsWUFBWWhHLEVBQUUsRUFBRSxXQUFXc0YsRUFBRUMsTUFBTSxDQUFDL0IsS0FBSzs0RkFDNUVnQyxhQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0ZBSWxCLDhEQUFDcEQ7NEVBQUlDLFdBQVU7OzhGQUNiLDhEQUFDRDtvRkFBSUMsV0FBVTs7c0dBQ2IsOERBQUN6RSx1REFBS0E7c0dBQUM7Ozs7OztzR0FDUCw4REFBQ0QsdURBQUtBOzRGQUNKc0ksTUFBSzs0RkFDTEMsS0FBSTs0RkFDSkMsS0FBSTs0RkFDSjNDLE9BQU93QyxZQUFZckYsTUFBTTs0RkFDekIwRSxVQUFVLENBQUNDLElBQU1mLGtCQUFrQnlCLFlBQVloRyxFQUFFLEVBQUUsVUFBVW9HLFNBQVNkLEVBQUVDLE1BQU0sQ0FBQy9CLEtBQUssS0FBSzs7Ozs7Ozs7Ozs7OzhGQUc3Riw4REFBQ3BCO29GQUFJQyxXQUFVOztzR0FDYiw4REFBQ3pFLHVEQUFLQTtzR0FBQzs7Ozs7O3NHQUNQLDhEQUFDRCx1REFBS0E7NEZBQ0o2RixPQUFPd0MsWUFBWXRGLE1BQU07NEZBQ3pCMkUsVUFBVSxDQUFDQyxJQUFNZixrQkFBa0J5QixZQUFZaEcsRUFBRSxFQUFFLFVBQVVzRixFQUFFQyxNQUFNLENBQUMvQixLQUFLOzRGQUMzRWdDLGFBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7OztzRkFJbEIsOERBQUNwRDs0RUFBSUMsV0FBVTs7OEZBQ2IsOERBQUN6RSx1REFBS0E7OEZBQUM7Ozs7Ozs4RkFDUCw4REFBQ0MsNkRBQVFBO29GQUNQMkYsT0FBT3dDLFlBQVlqSCxPQUFPO29GQUMxQnNHLFVBQVUsQ0FBQ0MsSUFBTWYsa0JBQWtCeUIsWUFBWWhHLEVBQUUsRUFBRSxXQUFXc0YsRUFBRUMsTUFBTSxDQUFDL0IsS0FBSztvRkFDNUVnQyxhQUFZO29GQUNaQyxNQUFNOzs7Ozs7Ozs7Ozs7O21FQS9ERE8sWUFBWWhHLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0E2RXpDLDhEQUFDaEMsNkRBQVdBO3dCQUFDd0YsT0FBTTtrQ0FDakIsNEVBQUNuRyxxREFBSUE7OzhDQUNILDhEQUFDRywyREFBVUE7O3NEQUNULDhEQUFDQywwREFBU0E7c0RBQUM7Ozs7OztzREFDWCw4REFBQ0YsZ0VBQWVBO3NEQUFDOzs7Ozs7Ozs7Ozs7OENBSW5CLDhEQUFDRCw0REFBV0E7b0NBQUMrRSxXQUFVOztzREFDckIsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7O3NFQUNDLDhEQUFDeEUsdURBQUtBOzREQUFDc0gsU0FBUTs0REFBYTdDLFdBQVU7c0VBQXdCOzs7Ozs7c0VBRzlELDhEQUFDd0M7NERBQUV4QyxXQUFVO3NFQUF3Qjs7Ozs7Ozs7Ozs7OzhEQUl2Qyw4REFBQ3ZFLHlEQUFNQTtvREFDTGtDLElBQUc7b0RBQ0htRixTQUFTcEcsRUFBQUEsZUFBQUEsUUFBUTZCLEdBQUcsY0FBWDdCLG1DQUFBQSxhQUFhSyxPQUFPLEtBQUk7b0RBQ2pDZ0csaUJBQWlCLENBQUNELFVBQVk5QixjQUFjLE9BQU8sV0FBVzhCOzs7Ozs7Ozs7Ozs7d0NBSWpFcEcsRUFBQUEsZ0JBQUFBLFFBQVE2QixHQUFHLGNBQVg3QixvQ0FBQUEsY0FBYUssT0FBTyxtQkFDbkIsOERBQUNnRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0Q7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDekUsdURBQUtBO29FQUFDc0gsU0FBUTs4RUFBVzs7Ozs7OzhFQUMxQiw4REFBQ3ZILHVEQUFLQTtvRUFDSnFDLElBQUc7b0VBQ0h3RCxPQUFPekUsRUFBQUEsZ0JBQUFBLFFBQVE2QixHQUFHLGNBQVg3QixvQ0FBQUEsY0FBYU0sS0FBSyxLQUFJO29FQUM3QmdHLFVBQVUsQ0FBQ0MsSUFBTWpDLGNBQWMsT0FBTyxTQUFTaUMsRUFBRUMsTUFBTSxDQUFDL0IsS0FBSztvRUFDN0RnQyxhQUFZOzs7Ozs7Ozs7Ozs7c0VBR2hCLDhEQUFDcEQ7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDekUsdURBQUtBO29FQUFDc0gsU0FBUTs4RUFBYzs7Ozs7OzhFQUM3Qiw4REFBQ3ZILHVEQUFLQTtvRUFDSnFDLElBQUc7b0VBQ0h3RCxPQUFPekUsRUFBQUEsZ0JBQUFBLFFBQVE2QixHQUFHLGNBQVg3QixvQ0FBQUEsY0FBYU8sUUFBUSxLQUFJO29FQUNoQytGLFVBQVUsQ0FBQ0MsSUFBTWpDLGNBQWMsT0FBTyxZQUFZaUMsRUFBRUMsTUFBTSxDQUFDL0IsS0FBSztvRUFDaEVnQyxhQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBTWxCLDhEQUFDcEQ7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUN6RSx1REFBS0E7b0VBQUN5RSxXQUFVOzhFQUF3Qjs7Ozs7OzhFQUN6Qyw4REFBQzNFLHlEQUFNQTtvRUFBQ3FILFNBQVNQO29FQUFRa0IsTUFBSzs7c0ZBQzVCLDhEQUFDbkgscUlBQUlBOzRFQUFDOEQsV0FBVTs7Ozs7O3dFQUFpQjs7Ozs7Ozs7Ozs7OztzRUFLckMsOERBQUNEOzREQUFJQyxXQUFVO3VFQUNadEQsZ0JBQUFBLFFBQVE2QixHQUFHLGNBQVg3QixxQ0FBQUEscUJBQUFBLGNBQWFnQixLQUFLLGNBQWxCaEIseUNBQUFBLG1CQUFvQm9GLEdBQUcsQ0FBQyxDQUFDdkQsS0FBS2dGLHNCQUM3Qiw4REFBQ3ZJLHFEQUFJQTtvRUFBY2dGLFdBQVU7O3NGQUMzQiw4REFBQ0Q7NEVBQUlDLFdBQVU7OzhGQUNiLDhEQUFDd0Q7b0ZBQUd4RCxXQUFVOzt3RkFBYzt3RkFBS3VELFFBQVE7Ozs7Ozs7OEZBQ3pDLDhEQUFDbEkseURBQU1BO29GQUNMb0gsU0FBUTtvRkFDUlksTUFBSztvRkFDTFgsU0FBUyxJQUFNTCxVQUFVOUQsSUFBSVosRUFBRTs4RkFFL0IsNEVBQUN4QixxSUFBTUE7d0ZBQUM2RCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztzRkFHdEIsOERBQUNEOzRFQUFJQyxXQUFVOzs4RkFDYiw4REFBQ0Q7b0ZBQUlDLFdBQVU7O3NHQUNiLDhEQUFDekUsdURBQUtBO3NHQUFDOzs7Ozs7c0dBQ1AsOERBQUNELHVEQUFLQTs0RkFDSjZGLE9BQU81QyxJQUFJQyxRQUFROzRGQUNuQndFLFVBQVUsQ0FBQ0MsSUFBTVgsVUFBVS9ELElBQUlaLEVBQUUsRUFBRSxZQUFZc0YsRUFBRUMsTUFBTSxDQUFDL0IsS0FBSzs0RkFDN0RnQyxhQUFZOzs7Ozs7Ozs7Ozs7OEZBR2hCLDhEQUFDcEQ7b0ZBQUlDLFdBQVU7O3NHQUNiLDhEQUFDekUsdURBQUtBO3NHQUFDOzs7Ozs7c0dBQ1AsOERBQUNDLDZEQUFRQTs0RkFDUDJGLE9BQU81QyxJQUFJRSxNQUFNOzRGQUNqQnVFLFVBQVUsQ0FBQ0MsSUFBTVgsVUFBVS9ELElBQUlaLEVBQUUsRUFBRSxVQUFVc0YsRUFBRUMsTUFBTSxDQUFDL0IsS0FBSzs0RkFDM0RnQyxhQUFZOzRGQUNaQyxNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O21FQTFCSDdFLElBQUlaLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0F5Q2pDLDhEQUFDaEMsNkRBQVdBO3dCQUFDd0YsT0FBTTtrQ0FDakIsNEVBQUNuRyxxREFBSUE7OzhDQUNILDhEQUFDRywyREFBVUE7O3NEQUNULDhEQUFDQywwREFBU0E7c0RBQUM7Ozs7OztzREFDWCw4REFBQ0YsZ0VBQWVBO3NEQUFDOzs7Ozs7Ozs7Ozs7OENBSW5CLDhEQUFDRCw0REFBV0E7b0NBQUMrRSxXQUFVOztzREFDckIsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7O3NFQUNDLDhEQUFDeEUsdURBQUtBOzREQUFDc0gsU0FBUTs0REFBYTdDLFdBQVU7c0VBQXdCOzs7Ozs7c0VBRzlELDhEQUFDd0M7NERBQUV4QyxXQUFVO3NFQUF3Qjs7Ozs7Ozs7Ozs7OzhEQUl2Qyw4REFBQ3ZFLHlEQUFNQTtvREFDTGtDLElBQUc7b0RBQ0htRixTQUFTcEcsRUFBQUEsZUFBQUEsUUFBUWdDLEdBQUcsY0FBWGhDLG1DQUFBQSxhQUFhSyxPQUFPLEtBQUk7b0RBQ2pDZ0csaUJBQWlCLENBQUNELFVBQVk5QixjQUFjLE9BQU8sV0FBVzhCOzs7Ozs7Ozs7Ozs7d0NBSWpFcEcsRUFBQUEsZ0JBQUFBLFFBQVFnQyxHQUFHLGNBQVhoQyxvQ0FBQUEsY0FBYUssT0FBTyxtQkFDbkIsOERBQUNnRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0Q7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDekUsdURBQUtBO29FQUFDc0gsU0FBUTs4RUFBVzs7Ozs7OzhFQUMxQiw4REFBQ3ZILHVEQUFLQTtvRUFDSnFDLElBQUc7b0VBQ0h3RCxPQUFPekUsRUFBQUEsZ0JBQUFBLFFBQVFnQyxHQUFHLGNBQVhoQyxvQ0FBQUEsY0FBYU0sS0FBSyxLQUFJO29FQUM3QmdHLFVBQVUsQ0FBQ0MsSUFBTWpDLGNBQWMsT0FBTyxTQUFTaUMsRUFBRUMsTUFBTSxDQUFDL0IsS0FBSztvRUFDN0RnQyxhQUFZOzs7Ozs7Ozs7Ozs7c0VBR2hCLDhEQUFDcEQ7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDekUsdURBQUtBO29FQUFDc0gsU0FBUTs4RUFBZ0I7Ozs7Ozs4RUFDL0IsOERBQUN2SCx1REFBS0E7b0VBQ0pxQyxJQUFHO29FQUNId0QsT0FBT3pFLEVBQUFBLGdCQUFBQSxRQUFRZ0MsR0FBRyxjQUFYaEMsb0NBQUFBLGNBQWFpQyxVQUFVLEtBQUk7b0VBQ2xDcUUsVUFBVSxDQUFDQyxJQUFNakMsY0FBYyxPQUFPLGNBQWNpQyxFQUFFQyxNQUFNLENBQUMvQixLQUFLO29FQUNsRWdDLGFBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFLbEIsOERBQUNwRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUN6RSx1REFBS0E7NERBQUNzSCxTQUFRO3NFQUFpQjs7Ozs7O3NFQUNoQyw4REFBQ3JILDZEQUFRQTs0REFDUG1DLElBQUc7NERBQ0h3RCxPQUFPekUsRUFBQUEsZ0JBQUFBLFFBQVFnQyxHQUFHLGNBQVhoQyxvQ0FBQUEsY0FBYVEsV0FBVyxLQUFJOzREQUNuQzhGLFVBQVUsQ0FBQ0MsSUFBTWpDLGNBQWMsT0FBTyxlQUFlaUMsRUFBRUMsTUFBTSxDQUFDL0IsS0FBSzs0REFDbkVnQyxhQUFZOzREQUNaQyxNQUFNOzs7Ozs7Ozs7Ozs7OERBSVYsOERBQUNyRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ3pFLHVEQUFLQTtvRUFBQ3NILFNBQVE7OEVBQWdCOzs7Ozs7OEVBQy9CLDhEQUFDdkgsdURBQUtBO29FQUNKcUMsSUFBRztvRUFDSHdELE9BQU96RSxFQUFBQSxnQkFBQUEsUUFBUWdDLEdBQUcsY0FBWGhDLG9DQUFBQSxjQUFha0MsVUFBVSxLQUFJO29FQUNsQ29FLFVBQVUsQ0FBQ0MsSUFBTWpDLGNBQWMsT0FBTyxjQUFjaUMsRUFBRUMsTUFBTSxDQUFDL0IsS0FBSztvRUFDbEVnQyxhQUFZOzs7Ozs7Ozs7Ozs7c0VBR2hCLDhEQUFDcEQ7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDekUsdURBQUtBO29FQUFDc0gsU0FBUTs4RUFBcUI7Ozs7Ozs4RUFDcEMsOERBQUN2SCx1REFBS0E7b0VBQ0pxQyxJQUFHO29FQUNId0QsT0FBT3pFLEVBQUFBLGdCQUFBQSxRQUFRZ0MsR0FBRyxjQUFYaEMsb0NBQUFBLGNBQWFhLGVBQWUsS0FBSTtvRUFDdkN5RixVQUFVLENBQUNDLElBQU1qQyxjQUFjLE9BQU8sbUJBQW1CaUMsRUFBRUMsTUFBTSxDQUFDL0IsS0FBSztvRUFDdkVnQyxhQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FXNUIsOERBQUN4SCw2REFBV0E7d0JBQUN3RixPQUFNO2tDQUNqQiw0RUFBQ25HLHFEQUFJQTs7OENBQ0gsOERBQUNHLDJEQUFVQTs7c0RBQ1QsOERBQUNDLDBEQUFTQTtzREFBQzs7Ozs7O3NEQUNYLDhEQUFDRixnRUFBZUE7c0RBQUM7Ozs7Ozs7Ozs7Ozs4Q0FJbkIsOERBQUNELDREQUFXQTtvQ0FBQytFLFdBQVU7O3NEQUNyQiw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUN6RSx1REFBS0E7NERBQUNzSCxTQUFRO3NFQUFXOzs7Ozs7c0VBQzFCLDhEQUFDdkgsdURBQUtBOzREQUNKcUMsSUFBRzs0REFDSHdELE9BQU96RSxFQUFBQSxlQUFBQSxRQUFRNEMsR0FBRyxjQUFYNUMsbUNBQUFBLGFBQWFNLEtBQUssS0FBSTs0REFDN0JnRyxVQUFVLENBQUNDLElBQU1qQyxjQUFjLE9BQU8sU0FBU2lDLEVBQUVDLE1BQU0sQ0FBQy9CLEtBQUs7NERBQzdEZ0MsYUFBWTs7Ozs7O3NFQUVkLDhEQUFDWDs0REFBRXhDLFdBQVU7c0VBQXdCOzs7Ozs7Ozs7Ozs7OERBRXZDLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUN6RSx1REFBS0E7NERBQUNzSCxTQUFRO3NFQUFjOzs7Ozs7c0VBQzdCLDhEQUFDdkgsdURBQUtBOzREQUNKcUMsSUFBRzs0REFDSHdELE9BQU96RSxFQUFBQSxnQkFBQUEsUUFBUTRDLEdBQUcsY0FBWDVDLG9DQUFBQSxjQUFhNkMsUUFBUSxLQUFJOzREQUNoQ3lELFVBQVUsQ0FBQ0MsSUFBTWpDLGNBQWMsT0FBTyxZQUFZaUMsRUFBRUMsTUFBTSxDQUFDL0IsS0FBSzs0REFDaEVnQyxhQUFZOzs7Ozs7c0VBRWQsOERBQUNYOzREQUFFeEMsV0FBVTtzRUFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7OztzREFJekMsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ3pFLHVEQUFLQTtvREFBQ3NILFNBQVE7OERBQWlCOzs7Ozs7OERBQ2hDLDhEQUFDckgsNkRBQVFBO29EQUNQbUMsSUFBRztvREFDSHdELE9BQU96RSxFQUFBQSxnQkFBQUEsUUFBUTRDLEdBQUcsY0FBWDVDLG9DQUFBQSxjQUFhUSxXQUFXLEtBQUk7b0RBQ25DOEYsVUFBVSxDQUFDQyxJQUFNakMsY0FBYyxPQUFPLGVBQWVpQyxFQUFFQyxNQUFNLENBQUMvQixLQUFLO29EQUNuRWdDLGFBQVk7b0RBQ1pDLE1BQU07Ozs7Ozs4REFFUiw4REFBQ1o7b0RBQUV4QyxXQUFVOzhEQUF3Qjs7Ozs7Ozs7Ozs7O3NEQUd2Qyw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDekUsdURBQUtBO29EQUFDc0gsU0FBUTs4REFBYTs7Ozs7OzhEQUM1Qiw4REFBQ3ZILHVEQUFLQTtvREFDSnFDLElBQUc7b0RBQ0h3RCxPQUFPekUsRUFBQUEsZ0JBQUFBLFFBQVE0QyxHQUFHLGNBQVg1QyxvQ0FBQUEsY0FBYThDLE9BQU8sS0FBSTtvREFDL0J3RCxVQUFVLENBQUNDLElBQU1qQyxjQUFjLE9BQU8sV0FBV2lDLEVBQUVDLE1BQU0sQ0FBQy9CLEtBQUs7b0RBQy9EZ0MsYUFBWTs7Ozs7OzhEQUVkLDhEQUFDWDtvREFBRXhDLFdBQVU7OERBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVFyRDtHQXgvQndCeEQ7O1FBQ1kxQix1REFBVUE7OztLQUR0QjBCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2FwcC9zdXBlci1hZG1pbi9jbXMvcGFnZS50c3g/YTdjYSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgdXNlU2Vzc2lvbiB9IGZyb20gJ25leHQtYXV0aC9yZWFjdCdcbmltcG9ydCB7IHJlZGlyZWN0IH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJ1xuaW1wb3J0IHsgQ2FyZCwgQ2FyZENvbnRlbnQsIENhcmREZXNjcmlwdGlvbiwgQ2FyZEhlYWRlciwgQ2FyZFRpdGxlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2NhcmQnXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnV0dG9uJ1xuaW1wb3J0IHsgSW5wdXQgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvaW5wdXQnXG5pbXBvcnQgeyBMYWJlbCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9sYWJlbCdcbmltcG9ydCB7IFRleHRhcmVhIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3RleHRhcmVhJ1xuaW1wb3J0IHsgU3dpdGNoIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3N3aXRjaCdcbmltcG9ydCB7IFRhYnMsIFRhYnNDb250ZW50LCBUYWJzTGlzdCwgVGFic1RyaWdnZXIgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvdGFicydcbmltcG9ydCB7IEJhZGdlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2JhZGdlJ1xuaW1wb3J0IHtcbiAgTW9uaXRvcixcbiAgU2F2ZSxcbiAgUmVmcmVzaEN3LFxuICBFeWUsXG4gIFVwbG9hZCxcbiAgUGx1cyxcbiAgVHJhc2gyLFxuICBFZGl0LFxuICBJbWFnZSBhcyBJbWFnZUljb24sXG4gIFR5cGUsXG4gIExheW91dCxcbiAgU3RhcixcbiAgVXNlcnMsXG4gIENoZWNrQ2lyY2xlLFxuICBBcnJvd1JpZ2h0LFxuICBRdW90ZVxufSBmcm9tICdsdWNpZGUtcmVhY3QnXG5pbXBvcnQgeyB0b2FzdCB9IGZyb20gJ3JlYWN0LWhvdC10b2FzdCdcblxuaW50ZXJmYWNlIENNU0NvbnRlbnQge1xuICAvLyBIZXJvIFNlY3Rpb25cbiAgaGVybzoge1xuICAgIGVuYWJsZWQ6IGJvb2xlYW5cbiAgICB0aXRsZTogc3RyaW5nXG4gICAgc3VidGl0bGU6IHN0cmluZ1xuICAgIGRlc2NyaXB0aW9uOiBzdHJpbmdcbiAgICBwcmltYXJ5Q1RBOiB7XG4gICAgICB0ZXh0OiBzdHJpbmdcbiAgICAgIGxpbms6IHN0cmluZ1xuICAgIH1cbiAgICBzZWNvbmRhcnlDVEE6IHtcbiAgICAgIHRleHQ6IHN0cmluZ1xuICAgICAgbGluazogc3RyaW5nXG4gICAgfVxuICAgIGJhY2tncm91bmRJbWFnZTogc3RyaW5nXG4gICAgYmFja2dyb3VuZFZpZGVvOiBzdHJpbmdcbiAgfVxuICBcbiAgLy8gRmVhdHVyZXMgU2VjdGlvblxuICBmZWF0dXJlczoge1xuICAgIGVuYWJsZWQ6IGJvb2xlYW5cbiAgICB0aXRsZTogc3RyaW5nXG4gICAgc3VidGl0bGU6IHN0cmluZ1xuICAgIGl0ZW1zOiBBcnJheTx7XG4gICAgICBpZDogc3RyaW5nXG4gICAgICB0aXRsZTogc3RyaW5nXG4gICAgICBkZXNjcmlwdGlvbjogc3RyaW5nXG4gICAgICBpY29uOiBzdHJpbmdcbiAgICAgIGltYWdlOiBzdHJpbmdcbiAgICB9PlxuICB9XG4gIFxuICAvLyBQcmljaW5nIFNlY3Rpb25cbiAgcHJpY2luZzoge1xuICAgIGVuYWJsZWQ6IGJvb2xlYW5cbiAgICB0aXRsZTogc3RyaW5nXG4gICAgc3VidGl0bGU6IHN0cmluZ1xuICAgIHNob3dQcmljaW5nVGFibGU6IGJvb2xlYW5cbiAgICBjdXN0b21NZXNzYWdlOiBzdHJpbmdcbiAgfVxuICBcbiAgLy8gVGVzdGltb25pYWxzIFNlY3Rpb25cbiAgdGVzdGltb25pYWxzOiB7XG4gICAgZW5hYmxlZDogYm9vbGVhblxuICAgIHRpdGxlOiBzdHJpbmdcbiAgICBzdWJ0aXRsZTogc3RyaW5nXG4gICAgaXRlbXM6IEFycmF5PHtcbiAgICAgIGlkOiBzdHJpbmdcbiAgICAgIG5hbWU6IHN0cmluZ1xuICAgICAgcm9sZTogc3RyaW5nXG4gICAgICBjb21wYW55OiBzdHJpbmdcbiAgICAgIGNvbnRlbnQ6IHN0cmluZ1xuICAgICAgYXZhdGFyOiBzdHJpbmdcbiAgICAgIHJhdGluZzogbnVtYmVyXG4gICAgfT5cbiAgfVxuICBcbiAgLy8gRkFRIFNlY3Rpb25cbiAgZmFxOiB7XG4gICAgZW5hYmxlZDogYm9vbGVhblxuICAgIHRpdGxlOiBzdHJpbmdcbiAgICBzdWJ0aXRsZTogc3RyaW5nXG4gICAgaXRlbXM6IEFycmF5PHtcbiAgICAgIGlkOiBzdHJpbmdcbiAgICAgIHF1ZXN0aW9uOiBzdHJpbmdcbiAgICAgIGFuc3dlcjogc3RyaW5nXG4gICAgfT5cbiAgfVxuICBcbiAgLy8gQ1RBIFNlY3Rpb25cbiAgY3RhOiB7XG4gICAgZW5hYmxlZDogYm9vbGVhblxuICAgIHRpdGxlOiBzdHJpbmdcbiAgICBkZXNjcmlwdGlvbjogc3RyaW5nXG4gICAgYnV0dG9uVGV4dDogc3RyaW5nXG4gICAgYnV0dG9uTGluazogc3RyaW5nXG4gICAgYmFja2dyb3VuZEltYWdlOiBzdHJpbmdcbiAgfVxuICBcbiAgLy8gRm9vdGVyXG4gIGZvb3Rlcjoge1xuICAgIGVuYWJsZWQ6IGJvb2xlYW5cbiAgICBjb21wYW55RGVzY3JpcHRpb246IHN0cmluZ1xuICAgIGxpbmtzOiBBcnJheTx7XG4gICAgICBpZDogc3RyaW5nXG4gICAgICB0aXRsZTogc3RyaW5nXG4gICAgICBpdGVtczogQXJyYXk8e1xuICAgICAgICBpZDogc3RyaW5nXG4gICAgICAgIHRleHQ6IHN0cmluZ1xuICAgICAgICBsaW5rOiBzdHJpbmdcbiAgICAgIH0+XG4gICAgfT5cbiAgICBzb2NpYWxMaW5rczoge1xuICAgICAgdHdpdHRlcjogc3RyaW5nXG4gICAgICBsaW5rZWRpbjogc3RyaW5nXG4gICAgICBmYWNlYm9vazogc3RyaW5nXG4gICAgICBpbnN0YWdyYW06IHN0cmluZ1xuICAgIH1cbiAgICBjb3B5cmlnaHRUZXh0OiBzdHJpbmdcbiAgfVxuICBcbiAgLy8gU0VPXG4gIHNlbzoge1xuICAgIHRpdGxlOiBzdHJpbmdcbiAgICBkZXNjcmlwdGlvbjogc3RyaW5nXG4gICAga2V5d29yZHM6IHN0cmluZ1xuICAgIG9nSW1hZ2U6IHN0cmluZ1xuICB9XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIENNU1BhZ2UoKSB7XG4gIGNvbnN0IHsgZGF0YTogc2Vzc2lvbiwgc3RhdHVzIH0gPSB1c2VTZXNzaW9uKClcbiAgY29uc3QgW2NvbnRlbnQsIHNldENvbnRlbnRdID0gdXNlU3RhdGU8Q01TQ29udGVudD4oe1xuICAgIGhlcm86IHtcbiAgICAgIGVuYWJsZWQ6IHRydWUsXG4gICAgICB0aXRsZTogJ0J1aWxkIFlvdXIgU2FhUyBCdXNpbmVzcycsXG4gICAgICBzdWJ0aXRsZTogJ1RoZSBDb21wbGV0ZSBQbGF0Zm9ybScsXG4gICAgICBkZXNjcmlwdGlvbjogJ0V2ZXJ5dGhpbmcgeW91IG5lZWQgdG8gbGF1bmNoLCBncm93LCBhbmQgc2NhbGUgeW91ciBTYWFTIGJ1c2luZXNzLiBGcm9tIGN1c3RvbWVyIG1hbmFnZW1lbnQgdG8gYmlsbGluZywgd2VcXCd2ZSBnb3QgeW91IGNvdmVyZWQuJyxcbiAgICAgIHByaW1hcnlDVEE6IHtcbiAgICAgICAgdGV4dDogJ1N0YXJ0IEZyZWUgVHJpYWwnLFxuICAgICAgICBsaW5rOiAnL2F1dGgvc2lnbnVwJ1xuICAgICAgfSxcbiAgICAgIHNlY29uZGFyeUNUQToge1xuICAgICAgICB0ZXh0OiAnV2F0Y2ggRGVtbycsXG4gICAgICAgIGxpbms6ICcvZGVtbydcbiAgICAgIH0sXG4gICAgICBiYWNrZ3JvdW5kSW1hZ2U6ICcnLFxuICAgICAgYmFja2dyb3VuZFZpZGVvOiAnJ1xuICAgIH0sXG4gICAgZmVhdHVyZXM6IHtcbiAgICAgIGVuYWJsZWQ6IHRydWUsXG4gICAgICB0aXRsZTogJ0V2ZXJ5dGhpbmcgWW91IE5lZWQnLFxuICAgICAgc3VidGl0bGU6ICdQb3dlcmZ1bCBGZWF0dXJlcycsXG4gICAgICBpdGVtczogW1xuICAgICAgICB7XG4gICAgICAgICAgaWQ6ICcxJyxcbiAgICAgICAgICB0aXRsZTogJ0N1c3RvbWVyIE1hbmFnZW1lbnQnLFxuICAgICAgICAgIGRlc2NyaXB0aW9uOiAnTWFuYWdlIHlvdXIgY3VzdG9tZXJzLCB0cmFjayBpbnRlcmFjdGlvbnMsIGFuZCBidWlsZCBsYXN0aW5nIHJlbGF0aW9uc2hpcHMuJyxcbiAgICAgICAgICBpY29uOiAndXNlcnMnLFxuICAgICAgICAgIGltYWdlOiAnJ1xuICAgICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICAgaWQ6ICcyJyxcbiAgICAgICAgICB0aXRsZTogJ1N1YnNjcmlwdGlvbiBCaWxsaW5nJyxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogJ0F1dG9tYXRlZCBiaWxsaW5nLCBpbnZvaWNpbmcsIGFuZCBwYXltZW50IHByb2Nlc3NpbmcgZm9yIHJlY3VycmluZyByZXZlbnVlLicsXG4gICAgICAgICAgaWNvbjogJ2NyZWRpdC1jYXJkJyxcbiAgICAgICAgICBpbWFnZTogJydcbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgIGlkOiAnMycsXG4gICAgICAgICAgdGl0bGU6ICdBbmFseXRpY3MgJiBSZXBvcnRzJyxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogJ0NvbXByZWhlbnNpdmUgYW5hbHl0aWNzIHRvIHRyYWNrIHlvdXIgYnVzaW5lc3MgcGVyZm9ybWFuY2UgYW5kIGdyb3d0aC4nLFxuICAgICAgICAgIGljb246ICdiYXItY2hhcnQnLFxuICAgICAgICAgIGltYWdlOiAnJ1xuICAgICAgICB9XG4gICAgICBdXG4gICAgfSxcbiAgICBwcmljaW5nOiB7XG4gICAgICBlbmFibGVkOiB0cnVlLFxuICAgICAgdGl0bGU6ICdTaW1wbGUsIFRyYW5zcGFyZW50IFByaWNpbmcnLFxuICAgICAgc3VidGl0bGU6ICdDaG9vc2UgdGhlIHBsYW4gdGhhdCBmaXRzIHlvdXIgbmVlZHMnLFxuICAgICAgc2hvd1ByaWNpbmdUYWJsZTogdHJ1ZSxcbiAgICAgIGN1c3RvbU1lc3NhZ2U6ICcnXG4gICAgfSxcbiAgICB0ZXN0aW1vbmlhbHM6IHtcbiAgICAgIGVuYWJsZWQ6IHRydWUsXG4gICAgICB0aXRsZTogJ1doYXQgT3VyIEN1c3RvbWVycyBTYXknLFxuICAgICAgc3VidGl0bGU6ICdUcnVzdGVkIGJ5IHRob3VzYW5kcyBvZiBidXNpbmVzc2VzJyxcbiAgICAgIGl0ZW1zOiBbXG4gICAgICAgIHtcbiAgICAgICAgICBpZDogJzEnLFxuICAgICAgICAgIG5hbWU6ICdKb2huIFNtaXRoJyxcbiAgICAgICAgICByb2xlOiAnQ0VPJyxcbiAgICAgICAgICBjb21wYW55OiAnVGVjaENvcnAnLFxuICAgICAgICAgIGNvbnRlbnQ6ICdUaGlzIHBsYXRmb3JtIGhhcyB0cmFuc2Zvcm1lZCBob3cgd2UgbWFuYWdlIG91ciBTYWFTIGJ1c2luZXNzLiBIaWdobHkgcmVjb21tZW5kZWQhJyxcbiAgICAgICAgICBhdmF0YXI6ICcnLFxuICAgICAgICAgIHJhdGluZzogNVxuICAgICAgICB9XG4gICAgICBdXG4gICAgfSxcbiAgICBmYXE6IHtcbiAgICAgIGVuYWJsZWQ6IHRydWUsXG4gICAgICB0aXRsZTogJ0ZyZXF1ZW50bHkgQXNrZWQgUXVlc3Rpb25zJyxcbiAgICAgIHN1YnRpdGxlOiAnRXZlcnl0aGluZyB5b3UgbmVlZCB0byBrbm93JyxcbiAgICAgIGl0ZW1zOiBbXG4gICAgICAgIHtcbiAgICAgICAgICBpZDogJzEnLFxuICAgICAgICAgIHF1ZXN0aW9uOiAnSG93IGRvIEkgZ2V0IHN0YXJ0ZWQ/JyxcbiAgICAgICAgICBhbnN3ZXI6ICdTaW1wbHkgc2lnbiB1cCBmb3IgYSBmcmVlIHRyaWFsIGFuZCBmb2xsb3cgb3VyIG9uYm9hcmRpbmcgZ3VpZGUgdG8gc2V0IHVwIHlvdXIgYWNjb3VudC4nXG4gICAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgICBpZDogJzInLFxuICAgICAgICAgIHF1ZXN0aW9uOiAnQ2FuIEkgY2FuY2VsIGFueXRpbWU/JyxcbiAgICAgICAgICBhbnN3ZXI6ICdZZXMsIHlvdSBjYW4gY2FuY2VsIHlvdXIgc3Vic2NyaXB0aW9uIGF0IGFueSB0aW1lLiBObyBsb25nLXRlcm0gY29udHJhY3RzIG9yIGNhbmNlbGxhdGlvbiBmZWVzLidcbiAgICAgICAgfVxuICAgICAgXVxuICAgIH0sXG4gICAgY3RhOiB7XG4gICAgICBlbmFibGVkOiB0cnVlLFxuICAgICAgdGl0bGU6ICdSZWFkeSB0byBHZXQgU3RhcnRlZD8nLFxuICAgICAgZGVzY3JpcHRpb246ICdKb2luIHRob3VzYW5kcyBvZiBidXNpbmVzc2VzIGFscmVhZHkgdXNpbmcgb3VyIHBsYXRmb3JtIHRvIGdyb3cgdGhlaXIgU2FhUy4nLFxuICAgICAgYnV0dG9uVGV4dDogJ1N0YXJ0IFlvdXIgRnJlZSBUcmlhbCcsXG4gICAgICBidXR0b25MaW5rOiAnL2F1dGgvc2lnbnVwJyxcbiAgICAgIGJhY2tncm91bmRJbWFnZTogJydcbiAgICB9LFxuICAgIGZvb3Rlcjoge1xuICAgICAgZW5hYmxlZDogdHJ1ZSxcbiAgICAgIGNvbXBhbnlEZXNjcmlwdGlvbjogJ1RoZSBjb21wbGV0ZSBTYWFTIHBsYXRmb3JtIGZvciBtb2Rlcm4gYnVzaW5lc3Nlcy4nLFxuICAgICAgbGlua3M6IFtcbiAgICAgICAge1xuICAgICAgICAgIGlkOiAnMScsXG4gICAgICAgICAgdGl0bGU6ICdQcm9kdWN0JyxcbiAgICAgICAgICBpdGVtczogW1xuICAgICAgICAgICAgeyBpZDogJzEnLCB0ZXh0OiAnRmVhdHVyZXMnLCBsaW5rOiAnL2ZlYXR1cmVzJyB9LFxuICAgICAgICAgICAgeyBpZDogJzInLCB0ZXh0OiAnUHJpY2luZycsIGxpbms6ICcvcHJpY2luZycgfSxcbiAgICAgICAgICAgIHsgaWQ6ICczJywgdGV4dDogJ1NlY3VyaXR5JywgbGluazogJy9zZWN1cml0eScgfVxuICAgICAgICAgIF1cbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgIGlkOiAnMicsXG4gICAgICAgICAgdGl0bGU6ICdDb21wYW55JyxcbiAgICAgICAgICBpdGVtczogW1xuICAgICAgICAgICAgeyBpZDogJzEnLCB0ZXh0OiAnQWJvdXQnLCBsaW5rOiAnL2Fib3V0JyB9LFxuICAgICAgICAgICAgeyBpZDogJzInLCB0ZXh0OiAnQmxvZycsIGxpbms6ICcvYmxvZycgfSxcbiAgICAgICAgICAgIHsgaWQ6ICczJywgdGV4dDogJ0NhcmVlcnMnLCBsaW5rOiAnL2NhcmVlcnMnIH1cbiAgICAgICAgICBdXG4gICAgICAgIH1cbiAgICAgIF0sXG4gICAgICBzb2NpYWxMaW5rczoge1xuICAgICAgICB0d2l0dGVyOiAnJyxcbiAgICAgICAgbGlua2VkaW46ICcnLFxuICAgICAgICBmYWNlYm9vazogJycsXG4gICAgICAgIGluc3RhZ3JhbTogJydcbiAgICAgIH0sXG4gICAgICBjb3B5cmlnaHRUZXh0OiAnwqkgMjAyNCBZb3VyIENvbXBhbnkuIEFsbCByaWdodHMgcmVzZXJ2ZWQuJ1xuICAgIH0sXG4gICAgc2VvOiB7XG4gICAgICB0aXRsZTogJ1NhYVMgUGxhdGZvcm0gLSBCdWlsZCBZb3VyIEJ1c2luZXNzJyxcbiAgICAgIGRlc2NyaXB0aW9uOiAnVGhlIGNvbXBsZXRlIFNhYVMgcGxhdGZvcm0gZm9yIG1vZGVybiBidXNpbmVzc2VzLiBDdXN0b21lciBtYW5hZ2VtZW50LCBiaWxsaW5nLCBhbmFseXRpY3MsIGFuZCBtb3JlLicsXG4gICAgICBrZXl3b3JkczogJ3NhYXMsIHBsYXRmb3JtLCBidXNpbmVzcywgY3VzdG9tZXIgbWFuYWdlbWVudCwgYmlsbGluZywgYW5hbHl0aWNzJyxcbiAgICAgIG9nSW1hZ2U6ICcnXG4gICAgfVxuICB9KVxuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKVxuICBjb25zdCBbc2F2aW5nLCBzZXRTYXZpbmddID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtwcmV2aWV3TW9kZSwgc2V0UHJldmlld01vZGVdID0gdXNlU3RhdGUoZmFsc2UpXG5cbiAgaWYgKHN0YXR1cyA9PT0gJ2xvYWRpbmcnKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTEyIHctMTIgYm9yZGVyLWItMiBib3JkZXItYmx1ZS02MDBcIj48L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIClcbiAgfVxuXG4gIGlmIChzdGF0dXMgPT09ICd1bmF1dGhlbnRpY2F0ZWQnKSB7XG4gICAgcmVkaXJlY3QoJy9hdXRoL3NpZ25pbicpXG4gIH1cblxuICBpZiAoc2Vzc2lvbj8udXNlcj8ucm9sZSAhPT0gJ1NVUEVSX0FETUlOJykge1xuICAgIHJlZGlyZWN0KCcvZGFzaGJvYXJkJylcbiAgfVxuXG4gIGNvbnN0IGZldGNoQ29udGVudCA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgc2V0TG9hZGluZyh0cnVlKVxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9zdXBlci1hZG1pbi9jbXMnLCB7XG4gICAgICAgIGNyZWRlbnRpYWxzOiAnaW5jbHVkZSdcbiAgICAgIH0pXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpXG4gICAgICBcbiAgICAgIGlmIChkYXRhLnN1Y2Nlc3MpIHtcbiAgICAgICAgc2V0Q29udGVudCh7IC4uLmNvbnRlbnQsIC4uLmRhdGEuY29udGVudCB9KVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBDTVMgY29udGVudDonLCBlcnJvcilcbiAgICAgIHRvYXN0LmVycm9yKCdGYWlsZWQgdG8gbG9hZCBDTVMgY29udGVudCcpXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgY29uc3Qgc2F2ZUNvbnRlbnQgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldFNhdmluZyh0cnVlKVxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9zdXBlci1hZG1pbi9jbXMnLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJ1xuICAgICAgICB9LFxuICAgICAgICBjcmVkZW50aWFsczogJ2luY2x1ZGUnLFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShjb250ZW50KVxuICAgICAgfSlcblxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKVxuICAgICAgXG4gICAgICBpZiAoZGF0YS5zdWNjZXNzKSB7XG4gICAgICAgIHRvYXN0LnN1Y2Nlc3MoJ0NNUyBjb250ZW50IHNhdmVkIHN1Y2Nlc3NmdWxseScpXG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0b2FzdC5lcnJvcihkYXRhLmVycm9yIHx8ICdGYWlsZWQgdG8gc2F2ZSBDTVMgY29udGVudCcpXG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHNhdmluZyBDTVMgY29udGVudDonLCBlcnJvcilcbiAgICAgIHRvYXN0LmVycm9yKCdGYWlsZWQgdG8gc2F2ZSBDTVMgY29udGVudCcpXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldFNhdmluZyhmYWxzZSlcbiAgICB9XG4gIH1cblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGZldGNoQ29udGVudCgpXG4gIH0sIFtdKVxuXG4gIGNvbnN0IHVwZGF0ZUNvbnRlbnQgPSAoc2VjdGlvbjoga2V5b2YgQ01TQ29udGVudCwga2V5OiBzdHJpbmcsIHZhbHVlOiBhbnkpID0+IHtcbiAgICBzZXRDb250ZW50KHByZXYgPT4gKHtcbiAgICAgIC4uLnByZXYsXG4gICAgICBbc2VjdGlvbl06IHtcbiAgICAgICAgLi4ucHJldltzZWN0aW9uXSxcbiAgICAgICAgW2tleV06IHZhbHVlXG4gICAgICB9XG4gICAgfSkpXG4gIH1cblxuICAvLyBGZWF0dXJlIG1hbmFnZW1lbnQgZnVuY3Rpb25zXG4gIGNvbnN0IGFkZEZlYXR1cmUgPSAoKSA9PiB7XG4gICAgY29uc3QgbmV3RmVhdHVyZSA9IHtcbiAgICAgIGlkOiBEYXRlLm5vdygpLnRvU3RyaW5nKCksXG4gICAgICB0aXRsZTogJ05ldyBGZWF0dXJlJyxcbiAgICAgIGRlc2NyaXB0aW9uOiAnRmVhdHVyZSBkZXNjcmlwdGlvbicsXG4gICAgICBpY29uOiAnc3RhcicsXG4gICAgICBpbWFnZTogJydcbiAgICB9XG4gICAgc2V0Q29udGVudChwcmV2ID0+ICh7XG4gICAgICAuLi5wcmV2LFxuICAgICAgZmVhdHVyZXM6IHtcbiAgICAgICAgLi4ucHJldi5mZWF0dXJlcyxcbiAgICAgICAgaXRlbXM6IFsuLi4ocHJldi5mZWF0dXJlcz8uaXRlbXMgfHwgW10pLCBuZXdGZWF0dXJlXVxuICAgICAgfVxuICAgIH0pKVxuICB9XG5cbiAgY29uc3QgcmVtb3ZlRmVhdHVyZSA9IChpZDogc3RyaW5nKSA9PiB7XG4gICAgc2V0Q29udGVudChwcmV2ID0+ICh7XG4gICAgICAuLi5wcmV2LFxuICAgICAgZmVhdHVyZXM6IHtcbiAgICAgICAgLi4ucHJldi5mZWF0dXJlcyxcbiAgICAgICAgaXRlbXM6IHByZXYuZmVhdHVyZXM/Lml0ZW1zPy5maWx0ZXIoaXRlbSA9PiBpdGVtLmlkICE9PSBpZCkgfHwgW11cbiAgICAgIH1cbiAgICB9KSlcbiAgfVxuXG4gIGNvbnN0IHVwZGF0ZUZlYXR1cmUgPSAoaWQ6IHN0cmluZywga2V5OiBzdHJpbmcsIHZhbHVlOiBhbnkpID0+IHtcbiAgICBzZXRDb250ZW50KHByZXYgPT4gKHtcbiAgICAgIC4uLnByZXYsXG4gICAgICBmZWF0dXJlczoge1xuICAgICAgICAuLi5wcmV2LmZlYXR1cmVzLFxuICAgICAgICBpdGVtczogcHJldi5mZWF0dXJlcz8uaXRlbXM/Lm1hcChpdGVtID0+XG4gICAgICAgICAgaXRlbS5pZCA9PT0gaWQgPyB7IC4uLml0ZW0sIFtrZXldOiB2YWx1ZSB9IDogaXRlbVxuICAgICAgICApIHx8IFtdXG4gICAgICB9XG4gICAgfSkpXG4gIH1cblxuICAvLyBUZXN0aW1vbmlhbCBtYW5hZ2VtZW50IGZ1bmN0aW9uc1xuICBjb25zdCBhZGRUZXN0aW1vbmlhbCA9ICgpID0+IHtcbiAgICBjb25zdCBuZXdUZXN0aW1vbmlhbCA9IHtcbiAgICAgIGlkOiBEYXRlLm5vdygpLnRvU3RyaW5nKCksXG4gICAgICBuYW1lOiAnQ3VzdG9tZXIgTmFtZScsXG4gICAgICByb2xlOiAnQ0VPJyxcbiAgICAgIGNvbXBhbnk6ICdDb21wYW55IE5hbWUnLFxuICAgICAgY29udGVudDogJ0dyZWF0IHRlc3RpbW9uaWFsIGNvbnRlbnQuLi4nLFxuICAgICAgYXZhdGFyOiAnJyxcbiAgICAgIHJhdGluZzogNVxuICAgIH1cbiAgICBzZXRDb250ZW50KHByZXYgPT4gKHtcbiAgICAgIC4uLnByZXYsXG4gICAgICB0ZXN0aW1vbmlhbHM6IHtcbiAgICAgICAgLi4ucHJldi50ZXN0aW1vbmlhbHMsXG4gICAgICAgIGl0ZW1zOiBbLi4uKHByZXYudGVzdGltb25pYWxzPy5pdGVtcyB8fCBbXSksIG5ld1Rlc3RpbW9uaWFsXVxuICAgICAgfVxuICAgIH0pKVxuICB9XG5cbiAgY29uc3QgcmVtb3ZlVGVzdGltb25pYWwgPSAoaWQ6IHN0cmluZykgPT4ge1xuICAgIHNldENvbnRlbnQocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIHRlc3RpbW9uaWFsczoge1xuICAgICAgICAuLi5wcmV2LnRlc3RpbW9uaWFscyxcbiAgICAgICAgaXRlbXM6IHByZXYudGVzdGltb25pYWxzPy5pdGVtcz8uZmlsdGVyKGl0ZW0gPT4gaXRlbS5pZCAhPT0gaWQpIHx8IFtdXG4gICAgICB9XG4gICAgfSkpXG4gIH1cblxuICBjb25zdCB1cGRhdGVUZXN0aW1vbmlhbCA9IChpZDogc3RyaW5nLCBrZXk6IHN0cmluZywgdmFsdWU6IGFueSkgPT4ge1xuICAgIHNldENvbnRlbnQocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIHRlc3RpbW9uaWFsczoge1xuICAgICAgICAuLi5wcmV2LnRlc3RpbW9uaWFscyxcbiAgICAgICAgaXRlbXM6IHByZXYudGVzdGltb25pYWxzPy5pdGVtcz8ubWFwKGl0ZW0gPT5cbiAgICAgICAgICBpdGVtLmlkID09PSBpZCA/IHsgLi4uaXRlbSwgW2tleV06IHZhbHVlIH0gOiBpdGVtXG4gICAgICAgICkgfHwgW11cbiAgICAgIH1cbiAgICB9KSlcbiAgfVxuXG4gIC8vIEZBUSBtYW5hZ2VtZW50IGZ1bmN0aW9uc1xuICBjb25zdCBhZGRGQVEgPSAoKSA9PiB7XG4gICAgY29uc3QgbmV3RkFRID0ge1xuICAgICAgaWQ6IERhdGUubm93KCkudG9TdHJpbmcoKSxcbiAgICAgIHF1ZXN0aW9uOiAnTmV3IHF1ZXN0aW9uPycsXG4gICAgICBhbnN3ZXI6ICdBbnN3ZXIgdG8gdGhlIHF1ZXN0aW9uLidcbiAgICB9XG4gICAgc2V0Q29udGVudChwcmV2ID0+ICh7XG4gICAgICAuLi5wcmV2LFxuICAgICAgZmFxOiB7XG4gICAgICAgIC4uLnByZXYuZmFxLFxuICAgICAgICBpdGVtczogWy4uLihwcmV2LmZhcT8uaXRlbXMgfHwgW10pLCBuZXdGQVFdXG4gICAgICB9XG4gICAgfSkpXG4gIH1cblxuICBjb25zdCByZW1vdmVGQVEgPSAoaWQ6IHN0cmluZykgPT4ge1xuICAgIHNldENvbnRlbnQocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIGZhcToge1xuICAgICAgICAuLi5wcmV2LmZhcSxcbiAgICAgICAgaXRlbXM6IHByZXYuZmFxPy5pdGVtcz8uZmlsdGVyKGl0ZW0gPT4gaXRlbS5pZCAhPT0gaWQpIHx8IFtdXG4gICAgICB9XG4gICAgfSkpXG4gIH1cblxuICBjb25zdCB1cGRhdGVGQVEgPSAoaWQ6IHN0cmluZywga2V5OiBzdHJpbmcsIHZhbHVlOiBhbnkpID0+IHtcbiAgICBzZXRDb250ZW50KHByZXYgPT4gKHtcbiAgICAgIC4uLnByZXYsXG4gICAgICBmYXE6IHtcbiAgICAgICAgLi4ucHJldi5mYXEsXG4gICAgICAgIGl0ZW1zOiBwcmV2LmZhcT8uaXRlbXM/Lm1hcChpdGVtID0+XG4gICAgICAgICAgaXRlbS5pZCA9PT0gaWQgPyB7IC4uLml0ZW0sIFtrZXldOiB2YWx1ZSB9IDogaXRlbVxuICAgICAgICApIHx8IFtdXG4gICAgICB9XG4gICAgfSkpXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICB7LyogSGVhZGVyICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIHNtOmZsZXgtcm93IHNtOml0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gZ2FwLTRcIj5cbiAgICAgICAgPGRpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgPE1vbml0b3IgY2xhc3NOYW1lPVwiaC04IHctOCB0ZXh0LWluZGlnby02MDBcIiAvPlxuICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+TGFuZGluZyBQYWdlIENNUzwvaDE+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMCBtdC0xXCI+TWFuYWdlIHlvdXIgbGFuZGluZyBwYWdlIGNvbnRlbnQgYW5kIGxheW91dDwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwib3V0bGluZVwiIG9uQ2xpY2s9e2ZldGNoQ29udGVudH0gZGlzYWJsZWQ9e2xvYWRpbmd9PlxuICAgICAgICAgICAgPFJlZnJlc2hDdyBjbGFzc05hbWU9e2BoLTQgdy00IG1yLTIgJHtsb2FkaW5nID8gJ2FuaW1hdGUtc3BpbicgOiAnJ31gfSAvPlxuICAgICAgICAgICAgUmVmcmVzaFxuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cIm91dGxpbmVcIiBvbkNsaWNrPXsoKSA9PiBzZXRQcmV2aWV3TW9kZSghcHJldmlld01vZGUpfT5cbiAgICAgICAgICAgIDxFeWUgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgIHtwcmV2aWV3TW9kZSA/ICdFZGl0IE1vZGUnIDogJ1ByZXZpZXcnfVxuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgIDxCdXR0b24gb25DbGljaz17c2F2ZUNvbnRlbnR9IGRpc2FibGVkPXtzYXZpbmd9PlxuICAgICAgICAgICAgPFNhdmUgY2xhc3NOYW1lPXtgaC00IHctNCBtci0yICR7c2F2aW5nID8gJ2FuaW1hdGUtc3BpbicgOiAnJ31gfSAvPlxuICAgICAgICAgICAgU2F2ZSBDaGFuZ2VzXG4gICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBDb250ZW50IE1hbmFnZW1lbnQgKi99XG4gICAgICA8VGFicyBkZWZhdWx0VmFsdWU9XCJoZXJvXCIgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgIDxUYWJzTGlzdCBjbGFzc05hbWU9XCJncmlkIHctZnVsbCBncmlkLWNvbHMtN1wiPlxuICAgICAgICAgIDxUYWJzVHJpZ2dlciB2YWx1ZT1cImhlcm9cIj5IZXJvPC9UYWJzVHJpZ2dlcj5cbiAgICAgICAgICA8VGFic1RyaWdnZXIgdmFsdWU9XCJmZWF0dXJlc1wiPkZlYXR1cmVzPC9UYWJzVHJpZ2dlcj5cbiAgICAgICAgICA8VGFic1RyaWdnZXIgdmFsdWU9XCJwcmljaW5nXCI+UHJpY2luZzwvVGFic1RyaWdnZXI+XG4gICAgICAgICAgPFRhYnNUcmlnZ2VyIHZhbHVlPVwidGVzdGltb25pYWxzXCI+VGVzdGltb25pYWxzPC9UYWJzVHJpZ2dlcj5cbiAgICAgICAgICA8VGFic1RyaWdnZXIgdmFsdWU9XCJmYXFcIj5GQVE8L1RhYnNUcmlnZ2VyPlxuICAgICAgICAgIDxUYWJzVHJpZ2dlciB2YWx1ZT1cImN0YVwiPkNUQTwvVGFic1RyaWdnZXI+XG4gICAgICAgICAgPFRhYnNUcmlnZ2VyIHZhbHVlPVwic2VvXCI+U0VPPC9UYWJzVHJpZ2dlcj5cbiAgICAgICAgPC9UYWJzTGlzdD5cblxuICAgICAgICB7LyogSGVybyBTZWN0aW9uICovfVxuICAgICAgICA8VGFic0NvbnRlbnQgdmFsdWU9XCJoZXJvXCI+XG4gICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxMYXlvdXQgY2xhc3NOYW1lPVwiaC01IHctNSBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICBIZXJvIFNlY3Rpb25cbiAgICAgICAgICAgICAgPC9DYXJkVGl0bGU+XG4gICAgICAgICAgICAgIDxDYXJkRGVzY3JpcHRpb24+XG4gICAgICAgICAgICAgICAgQ29uZmlndXJlIHRoZSBtYWluIGhlcm8gc2VjdGlvbiBvZiB5b3VyIGxhbmRpbmcgcGFnZVxuICAgICAgICAgICAgICA8L0NhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gcC00IGJvcmRlciByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiaGVyb0VuYWJsZWRcIiBjbGFzc05hbWU9XCJ0ZXh0LWJhc2UgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAgRW5hYmxlIEhlcm8gU2VjdGlvblxuICAgICAgICAgICAgICAgICAgPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgICAgICBTaG93IHRoZSBoZXJvIHNlY3Rpb24gb24geW91ciBsYW5kaW5nIHBhZ2VcbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8U3dpdGNoXG4gICAgICAgICAgICAgICAgICBpZD1cImhlcm9FbmFibGVkXCJcbiAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e2NvbnRlbnQuaGVyby5lbmFibGVkfVxuICAgICAgICAgICAgICAgICAgb25DaGVja2VkQ2hhbmdlPXsoY2hlY2tlZCkgPT4gdXBkYXRlQ29udGVudCgnaGVybycsICdlbmFibGVkJywgY2hlY2tlZCl9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAge2NvbnRlbnQuaGVyby5lbmFibGVkICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJoZXJvVGl0bGVcIj5NYWluIFRpdGxlPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwiaGVyb1RpdGxlXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb250ZW50Lmhlcm8udGl0bGV9XG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUNvbnRlbnQoJ2hlcm8nLCAndGl0bGUnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIllvdXIgbWFpbiBoZWFkbGluZVwiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJoZXJvU3VidGl0bGVcIj5TdWJ0aXRsZTwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICBpZD1cImhlcm9TdWJ0aXRsZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Y29udGVudC5oZXJvLnN1YnRpdGxlfVxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVDb250ZW50KCdoZXJvJywgJ3N1YnRpdGxlJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJTdXBwb3J0aW5nIGhlYWRsaW5lXCJcbiAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImhlcm9EZXNjcmlwdGlvblwiPkRlc2NyaXB0aW9uPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPFRleHRhcmVhXG4gICAgICAgICAgICAgICAgICAgICAgaWQ9XCJoZXJvRGVzY3JpcHRpb25cIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb250ZW50Lmhlcm8uZGVzY3JpcHRpb259XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVDb250ZW50KCdoZXJvJywgJ2Rlc2NyaXB0aW9uJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRGVzY3JpYmUgeW91ciBwcm9kdWN0IG9yIHNlcnZpY2VcIlxuICAgICAgICAgICAgICAgICAgICAgIHJvd3M9ezN9XG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJwcmltYXJ5Q1RBVGV4dFwiPlByaW1hcnkgQnV0dG9uIFRleHQ8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJwcmltYXJ5Q1RBVGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Y29udGVudC5oZXJvLnByaW1hcnlDVEEudGV4dH1cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlQ29udGVudCgnaGVybycsICdwcmltYXJ5Q1RBJywgeyAuLi5jb250ZW50Lmhlcm8ucHJpbWFyeUNUQSwgdGV4dDogZS50YXJnZXQudmFsdWUgfSl9XG4gICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkdldCBTdGFydGVkXCJcbiAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInByaW1hcnlDVEFMaW5rXCI+UHJpbWFyeSBCdXR0b24gTGluazwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICBpZD1cInByaW1hcnlDVEFMaW5rXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb250ZW50Lmhlcm8ucHJpbWFyeUNUQS5saW5rfVxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVDb250ZW50KCdoZXJvJywgJ3ByaW1hcnlDVEEnLCB7IC4uLmNvbnRlbnQuaGVyby5wcmltYXJ5Q1RBLCBsaW5rOiBlLnRhcmdldC52YWx1ZSB9KX1cbiAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiL2F1dGgvc2lnbnVwXCJcbiAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInNlY29uZGFyeUNUQVRleHRcIj5TZWNvbmRhcnkgQnV0dG9uIFRleHQ8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJzZWNvbmRhcnlDVEFUZXh0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb250ZW50Lmhlcm8uc2Vjb25kYXJ5Q1RBLnRleHR9XG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUNvbnRlbnQoJ2hlcm8nLCAnc2Vjb25kYXJ5Q1RBJywgeyAuLi5jb250ZW50Lmhlcm8uc2Vjb25kYXJ5Q1RBLCB0ZXh0OiBlLnRhcmdldC52YWx1ZSB9KX1cbiAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiTGVhcm4gTW9yZVwiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJzZWNvbmRhcnlDVEFMaW5rXCI+U2Vjb25kYXJ5IEJ1dHRvbiBMaW5rPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwic2Vjb25kYXJ5Q1RBTGlua1wiXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Y29udGVudC5oZXJvLnNlY29uZGFyeUNUQS5saW5rfVxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVDb250ZW50KCdoZXJvJywgJ3NlY29uZGFyeUNUQScsIHsgLi4uY29udGVudC5oZXJvLnNlY29uZGFyeUNUQSwgbGluazogZS50YXJnZXQudmFsdWUgfSl9XG4gICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIi9kZW1vXCJcbiAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICB7LyogQmFja2dyb3VuZCBNZWRpYSAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxMYWJlbCBjbGFzc05hbWU9XCJ0ZXh0LWJhc2UgZm9udC1tZWRpdW1cIj5CYWNrZ3JvdW5kIE1lZGlhPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiaGVyb0JhY2tncm91bmRJbWFnZVwiPkJhY2tncm91bmQgSW1hZ2UgVVJMPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICBpZD1cImhlcm9CYWNrZ3JvdW5kSW1hZ2VcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Y29udGVudC5oZXJvLmJhY2tncm91bmRJbWFnZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVDb250ZW50KCdoZXJvJywgJ2JhY2tncm91bmRJbWFnZScsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJodHRwczovL2V4YW1wbGUuY29tL2hlcm8tYmcuanBnXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiaGVyb0JhY2tncm91bmRWaWRlb1wiPkJhY2tncm91bmQgVmlkZW8gVVJMPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICBpZD1cImhlcm9CYWNrZ3JvdW5kVmlkZW9cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Y29udGVudC5oZXJvLmJhY2tncm91bmRWaWRlb31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVDb250ZW50KCdoZXJvJywgJ2JhY2tncm91bmRWaWRlbycsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJodHRwczovL2V4YW1wbGUuY29tL2hlcm8tdmlkZW8ubXA0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgPC9UYWJzQ29udGVudD5cblxuICAgICAgICB7LyogRmVhdHVyZXMgU2VjdGlvbiAqL31cbiAgICAgICAgPFRhYnNDb250ZW50IHZhbHVlPVwiZmVhdHVyZXNcIj5cbiAgICAgICAgICA8Q2FyZD5cbiAgICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPFN0YXIgY2xhc3NOYW1lPVwiaC01IHctNSBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICBGZWF0dXJlcyBTZWN0aW9uXG4gICAgICAgICAgICAgIDwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICA8Q2FyZERlc2NyaXB0aW9uPlxuICAgICAgICAgICAgICAgIENvbmZpZ3VyZSB0aGUgZmVhdHVyZXMgc2hvd2Nhc2Ugc2VjdGlvblxuICAgICAgICAgICAgICA8L0NhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gcC00IGJvcmRlciByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiZmVhdHVyZXNFbmFibGVkXCIgY2xhc3NOYW1lPVwidGV4dC1iYXNlIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICAgIEVuYWJsZSBGZWF0dXJlcyBTZWN0aW9uXG4gICAgICAgICAgICAgICAgICA8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgIFNob3cgdGhlIGZlYXR1cmVzIHNlY3Rpb24gb24geW91ciBsYW5kaW5nIHBhZ2VcbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8U3dpdGNoXG4gICAgICAgICAgICAgICAgICBpZD1cImZlYXR1cmVzRW5hYmxlZFwiXG4gICAgICAgICAgICAgICAgICBjaGVja2VkPXtjb250ZW50LmZlYXR1cmVzPy5lbmFibGVkIHx8IGZhbHNlfVxuICAgICAgICAgICAgICAgICAgb25DaGVja2VkQ2hhbmdlPXsoY2hlY2tlZCkgPT4gdXBkYXRlQ29udGVudCgnZmVhdHVyZXMnLCAnZW5hYmxlZCcsIGNoZWNrZWQpfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHtjb250ZW50LmZlYXR1cmVzPy5lbmFibGVkICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJmZWF0dXJlc1RpdGxlXCI+U2VjdGlvbiBUaXRsZTwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICBpZD1cImZlYXR1cmVzVGl0bGVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2NvbnRlbnQuZmVhdHVyZXM/LnRpdGxlIHx8ICcnfVxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVDb250ZW50KCdmZWF0dXJlcycsICd0aXRsZScsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRXZlcnl0aGluZyBZb3UgTmVlZFwiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJmZWF0dXJlc1N1YnRpdGxlXCI+U2VjdGlvbiBTdWJ0aXRsZTwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICBpZD1cImZlYXR1cmVzU3VidGl0bGVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2NvbnRlbnQuZmVhdHVyZXM/LnN1YnRpdGxlIHx8ICcnfVxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVDb250ZW50KCdmZWF0dXJlcycsICdzdWJ0aXRsZScsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiUG93ZXJmdWwgRmVhdHVyZXNcIlxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIHsvKiBGZWF0dXJlIEl0ZW1zICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgY2xhc3NOYW1lPVwidGV4dC1iYXNlIGZvbnQtbWVkaXVtXCI+RmVhdHVyZSBJdGVtczwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXthZGRGZWF0dXJlfSBzaXplPVwic21cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxQbHVzIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICBBZGQgRmVhdHVyZVxuICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtjb250ZW50LmZlYXR1cmVzPy5pdGVtcz8ubWFwKChmZWF0dXJlLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgPENhcmQga2V5PXtmZWF0dXJlLmlkfSBjbGFzc05hbWU9XCJwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGp1c3RpZnktYmV0d2VlbiBtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+RmVhdHVyZSB7aW5kZXggKyAxfTwvaDQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByZW1vdmVGZWF0dXJlKGZlYXR1cmUuaWQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUcmFzaDIgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsPkZlYXR1cmUgVGl0bGU8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmZWF0dXJlLnRpdGxlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUZlYXR1cmUoZmVhdHVyZS5pZCwgJ3RpdGxlJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkZlYXR1cmUgdGl0bGVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsPkljb24gTmFtZTwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmZWF0dXJlLmljb259XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlRmVhdHVyZShmZWF0dXJlLmlkLCAnaWNvbicsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHAtMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwidXNlcnNcIj5Vc2Vyczwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiY3JlZGl0LWNhcmRcIj5DcmVkaXQgQ2FyZDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiYmFyLWNoYXJ0XCI+QmFyIENoYXJ0PC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJidWlsZGluZ1wiPkJ1aWxkaW5nPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJzaGllbGRcIj5TaGllbGQ8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImdsb2JlXCI+R2xvYmU8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cInphcFwiPlphcDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiZmlsZS10ZXh0XCI+RmlsZSBUZXh0PC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yIG10LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWw+RmVhdHVyZSBEZXNjcmlwdGlvbjwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRleHRhcmVhXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17ZmVhdHVyZS5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlRmVhdHVyZShmZWF0dXJlLmlkLCAnZGVzY3JpcHRpb24nLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkZlYXR1cmUgZGVzY3JpcHRpb25cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcm93cz17Mn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgPC9DYXJkPlxuICAgICAgICA8L1RhYnNDb250ZW50PlxuXG4gICAgICAgIHsvKiBUZXN0aW1vbmlhbHMgU2VjdGlvbiAqL31cbiAgICAgICAgPFRhYnNDb250ZW50IHZhbHVlPVwidGVzdGltb25pYWxzXCI+XG4gICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxRdW90ZSBjbGFzc05hbWU9XCJoLTUgdy01IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgIFRlc3RpbW9uaWFscyBTZWN0aW9uXG4gICAgICAgICAgICAgIDwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICA8Q2FyZERlc2NyaXB0aW9uPlxuICAgICAgICAgICAgICAgIE1hbmFnZSBjdXN0b21lciB0ZXN0aW1vbmlhbHMgYW5kIHJldmlld3NcbiAgICAgICAgICAgICAgPC9DYXJkRGVzY3JpcHRpb24+XG4gICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHAtNCBib3JkZXIgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInRlc3RpbW9uaWFsc0VuYWJsZWRcIiBjbGFzc05hbWU9XCJ0ZXh0LWJhc2UgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAgRW5hYmxlIFRlc3RpbW9uaWFscyBTZWN0aW9uXG4gICAgICAgICAgICAgICAgICA8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgIFNob3cgY3VzdG9tZXIgdGVzdGltb25pYWxzIG9uIHlvdXIgbGFuZGluZyBwYWdlXG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPFN3aXRjaFxuICAgICAgICAgICAgICAgICAgaWQ9XCJ0ZXN0aW1vbmlhbHNFbmFibGVkXCJcbiAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e2NvbnRlbnQudGVzdGltb25pYWxzPy5lbmFibGVkIHx8IGZhbHNlfVxuICAgICAgICAgICAgICAgICAgb25DaGVja2VkQ2hhbmdlPXsoY2hlY2tlZCkgPT4gdXBkYXRlQ29udGVudCgndGVzdGltb25pYWxzJywgJ2VuYWJsZWQnLCBjaGVja2VkKX1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7Y29udGVudC50ZXN0aW1vbmlhbHM/LmVuYWJsZWQgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInRlc3RpbW9uaWFsc1RpdGxlXCI+U2VjdGlvbiBUaXRsZTwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICBpZD1cInRlc3RpbW9uaWFsc1RpdGxlXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb250ZW50LnRlc3RpbW9uaWFscz8udGl0bGUgfHwgJyd9XG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUNvbnRlbnQoJ3Rlc3RpbW9uaWFscycsICd0aXRsZScsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiV2hhdCBPdXIgQ3VzdG9tZXJzIFNheVwiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJ0ZXN0aW1vbmlhbHNTdWJ0aXRsZVwiPlNlY3Rpb24gU3VidGl0bGU8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJ0ZXN0aW1vbmlhbHNTdWJ0aXRsZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Y29udGVudC50ZXN0aW1vbmlhbHM/LnN1YnRpdGxlIHx8ICcnfVxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVDb250ZW50KCd0ZXN0aW1vbmlhbHMnLCAnc3VidGl0bGUnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlRydXN0ZWQgYnkgdGhvdXNhbmRzIG9mIGJ1c2luZXNzZXNcIlxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIHsvKiBUZXN0aW1vbmlhbCBJdGVtcyAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGNsYXNzTmFtZT1cInRleHQtYmFzZSBmb250LW1lZGl1bVwiPlRlc3RpbW9uaWFsczwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXthZGRUZXN0aW1vbmlhbH0gc2l6ZT1cInNtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8UGx1cyBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgQWRkIFRlc3RpbW9uaWFsXG4gICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICAgICAge2NvbnRlbnQudGVzdGltb25pYWxzPy5pdGVtcz8ubWFwKCh0ZXN0aW1vbmlhbCwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxDYXJkIGtleT17dGVzdGltb25pYWwuaWR9IGNsYXNzTmFtZT1cInAtNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQganVzdGlmeS1iZXR3ZWVuIG1iLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj5UZXN0aW1vbmlhbCB7aW5kZXggKyAxfTwvaDQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByZW1vdmVUZXN0aW1vbmlhbCh0ZXN0aW1vbmlhbC5pZCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRyYXNoMiBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMyBnYXAtNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWw+Q3VzdG9tZXIgTmFtZTwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3Rlc3RpbW9uaWFsLm5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlVGVzdGltb25pYWwodGVzdGltb25pYWwuaWQsICduYW1lJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkpvaG4gU21pdGhcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsPlJvbGU8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXt0ZXN0aW1vbmlhbC5yb2xlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZVRlc3RpbW9uaWFsKHRlc3RpbW9uaWFsLmlkLCAncm9sZScsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJDRU9cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsPkNvbXBhbnk8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXt0ZXN0aW1vbmlhbC5jb21wYW55fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZVRlc3RpbW9uaWFsKHRlc3RpbW9uaWFsLmlkLCAnY29tcGFueScsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJUZWNoQ29ycFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC00IG10LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsPlJhdGluZyAoMS01KTwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1pbj1cIjFcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtYXg9XCI1XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3Rlc3RpbW9uaWFsLnJhdGluZ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVUZXN0aW1vbmlhbCh0ZXN0aW1vbmlhbC5pZCwgJ3JhdGluZycsIHBhcnNlSW50KGUudGFyZ2V0LnZhbHVlKSB8fCA1KX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbD5BdmF0YXIgVVJMIChvcHRpb25hbCk8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXt0ZXN0aW1vbmlhbC5hdmF0YXJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlVGVzdGltb25pYWwodGVzdGltb25pYWwuaWQsICdhdmF0YXInLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiaHR0cHM6Ly9leGFtcGxlLmNvbS9hdmF0YXIuanBnXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMiBtdC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsPlRlc3RpbW9uaWFsIENvbnRlbnQ8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUZXh0YXJlYVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3Rlc3RpbW9uaWFsLmNvbnRlbnR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZVRlc3RpbW9uaWFsKHRlc3RpbW9uaWFsLmlkLCAnY29udGVudCcsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiVGhpcyBwbGF0Zm9ybSBoYXMgdHJhbnNmb3JtZWQgb3VyIGJ1c2luZXNzLi4uXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJvd3M9ezN9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgPC9UYWJzQ29udGVudD5cblxuICAgICAgICB7LyogRkFRIFNlY3Rpb24gKi99XG4gICAgICAgIDxUYWJzQ29udGVudCB2YWx1ZT1cImZhcVwiPlxuICAgICAgICAgIDxDYXJkPlxuICAgICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICAgIDxDYXJkVGl0bGU+RkFRIFNlY3Rpb248L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgPENhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgICBNYW5hZ2UgZnJlcXVlbnRseSBhc2tlZCBxdWVzdGlvbnNcbiAgICAgICAgICAgICAgPC9DYXJkRGVzY3JpcHRpb24+XG4gICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHAtNCBib3JkZXIgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImZhcUVuYWJsZWRcIiBjbGFzc05hbWU9XCJ0ZXh0LWJhc2UgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAgRW5hYmxlIEZBUSBTZWN0aW9uXG4gICAgICAgICAgICAgICAgICA8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgIFNob3cgRkFRIHNlY3Rpb24gb24geW91ciBsYW5kaW5nIHBhZ2VcbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8U3dpdGNoXG4gICAgICAgICAgICAgICAgICBpZD1cImZhcUVuYWJsZWRcIlxuICAgICAgICAgICAgICAgICAgY2hlY2tlZD17Y29udGVudC5mYXE/LmVuYWJsZWQgfHwgZmFsc2V9XG4gICAgICAgICAgICAgICAgICBvbkNoZWNrZWRDaGFuZ2U9eyhjaGVja2VkKSA9PiB1cGRhdGVDb250ZW50KCdmYXEnLCAnZW5hYmxlZCcsIGNoZWNrZWQpfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHtjb250ZW50LmZhcT8uZW5hYmxlZCAmJiAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiZmFxVGl0bGVcIj5TZWN0aW9uIFRpdGxlPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwiZmFxVGl0bGVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2NvbnRlbnQuZmFxPy50aXRsZSB8fCAnJ31cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlQ29udGVudCgnZmFxJywgJ3RpdGxlJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJGcmVxdWVudGx5IEFza2VkIFF1ZXN0aW9uc1wiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJmYXFTdWJ0aXRsZVwiPlNlY3Rpb24gU3VidGl0bGU8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJmYXFTdWJ0aXRsZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Y29udGVudC5mYXE/LnN1YnRpdGxlIHx8ICcnfVxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVDb250ZW50KCdmYXEnLCAnc3VidGl0bGUnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkV2ZXJ5dGhpbmcgeW91IG5lZWQgdG8ga25vd1wiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgey8qIEZBUSBJdGVtcyAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGNsYXNzTmFtZT1cInRleHQtYmFzZSBmb250LW1lZGl1bVwiPkZBUSBJdGVtczwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXthZGRGQVF9IHNpemU9XCJzbVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFBsdXMgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIEFkZCBGQVFcbiAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7Y29udGVudC5mYXE/Lml0ZW1zPy5tYXAoKGZhcSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxDYXJkIGtleT17ZmFxLmlkfSBjbGFzc05hbWU9XCJwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGp1c3RpZnktYmV0d2VlbiBtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+RkFRIHtpbmRleCArIDF9PC9oND5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHJlbW92ZUZBUShmYXEuaWQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUcmFzaDIgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWw+UXVlc3Rpb248L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmYXEucXVlc3Rpb259XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlRkFRKGZhcS5pZCwgJ3F1ZXN0aW9uJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIldoYXQgaXMgeW91ciBxdWVzdGlvbj9cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsPkFuc3dlcjwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGV4dGFyZWFcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2ZhcS5hbnN3ZXJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlRkFRKGZhcS5pZCwgJ2Fuc3dlcicsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJQcm92aWRlIGEgZGV0YWlsZWQgYW5zd2VyLi4uXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcm93cz17M31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9DYXJkPlxuICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgIDwvVGFic0NvbnRlbnQ+XG5cbiAgICAgICAgey8qIENUQSBTZWN0aW9uICovfVxuICAgICAgICA8VGFic0NvbnRlbnQgdmFsdWU9XCJjdGFcIj5cbiAgICAgICAgICA8Q2FyZD5cbiAgICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgICA8Q2FyZFRpdGxlPkNhbGwtdG8tQWN0aW9uIFNlY3Rpb248L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgPENhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgICBDb25maWd1cmUgdGhlIGZpbmFsIGNhbGwtdG8tYWN0aW9uIHNlY3Rpb25cbiAgICAgICAgICAgICAgPC9DYXJkRGVzY3JpcHRpb24+XG4gICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHAtNCBib3JkZXIgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImN0YUVuYWJsZWRcIiBjbGFzc05hbWU9XCJ0ZXh0LWJhc2UgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAgRW5hYmxlIENUQSBTZWN0aW9uXG4gICAgICAgICAgICAgICAgICA8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgIFNob3cgdGhlIGNhbGwtdG8tYWN0aW9uIHNlY3Rpb24gb24geW91ciBsYW5kaW5nIHBhZ2VcbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8U3dpdGNoXG4gICAgICAgICAgICAgICAgICBpZD1cImN0YUVuYWJsZWRcIlxuICAgICAgICAgICAgICAgICAgY2hlY2tlZD17Y29udGVudC5jdGE/LmVuYWJsZWQgfHwgZmFsc2V9XG4gICAgICAgICAgICAgICAgICBvbkNoZWNrZWRDaGFuZ2U9eyhjaGVja2VkKSA9PiB1cGRhdGVDb250ZW50KCdjdGEnLCAnZW5hYmxlZCcsIGNoZWNrZWQpfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHtjb250ZW50LmN0YT8uZW5hYmxlZCAmJiAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiY3RhVGl0bGVcIj5DVEEgVGl0bGU8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJjdGFUaXRsZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Y29udGVudC5jdGE/LnRpdGxlIHx8ICcnfVxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVDb250ZW50KCdjdGEnLCAndGl0bGUnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlJlYWR5IHRvIEdldCBTdGFydGVkP1wiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJjdGFCdXR0b25UZXh0XCI+QnV0dG9uIFRleHQ8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJjdGFCdXR0b25UZXh0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb250ZW50LmN0YT8uYnV0dG9uVGV4dCB8fCAnJ31cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlQ29udGVudCgnY3RhJywgJ2J1dHRvblRleHQnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlN0YXJ0IFlvdXIgRnJlZSBUcmlhbFwiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJjdGFEZXNjcmlwdGlvblwiPkNUQSBEZXNjcmlwdGlvbjwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDxUZXh0YXJlYVxuICAgICAgICAgICAgICAgICAgICAgIGlkPVwiY3RhRGVzY3JpcHRpb25cIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb250ZW50LmN0YT8uZGVzY3JpcHRpb24gfHwgJyd9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVDb250ZW50KCdjdGEnLCAnZGVzY3JpcHRpb24nLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJKb2luIHRob3VzYW5kcyBvZiBidXNpbmVzc2VzIGFscmVhZHkgdXNpbmcgb3VyIHBsYXRmb3JtLi4uXCJcbiAgICAgICAgICAgICAgICAgICAgICByb3dzPXszfVxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiY3RhQnV0dG9uTGlua1wiPkJ1dHRvbiBMaW5rPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwiY3RhQnV0dG9uTGlua1wiXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Y29udGVudC5jdGE/LmJ1dHRvbkxpbmsgfHwgJyd9XG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUNvbnRlbnQoJ2N0YScsICdidXR0b25MaW5rJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCIvYXV0aC9zaWdudXBcIlxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiY3RhQmFja2dyb3VuZEltYWdlXCI+QmFja2dyb3VuZCBJbWFnZSBVUkw8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJjdGFCYWNrZ3JvdW5kSW1hZ2VcIlxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2NvbnRlbnQuY3RhPy5iYWNrZ3JvdW5kSW1hZ2UgfHwgJyd9XG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUNvbnRlbnQoJ2N0YScsICdiYWNrZ3JvdW5kSW1hZ2UnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cImh0dHBzOi8vZXhhbXBsZS5jb20vY3RhLWJnLmpwZ1wiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgPC9DYXJkPlxuICAgICAgICA8L1RhYnNDb250ZW50PlxuXG4gICAgICAgIHsvKiBTRU8gU2VjdGlvbiAqL31cbiAgICAgICAgPFRhYnNDb250ZW50IHZhbHVlPVwic2VvXCI+XG4gICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgPENhcmRUaXRsZT5TRU8gU2V0dGluZ3M8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgPENhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgICBDb25maWd1cmUgU0VPIG1ldGEgdGFncyBhbmQgc29jaWFsIHNoYXJpbmdcbiAgICAgICAgICAgICAgPC9DYXJkRGVzY3JpcHRpb24+XG4gICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInNlb1RpdGxlXCI+UGFnZSBUaXRsZTwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgaWQ9XCJzZW9UaXRsZVwiXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb250ZW50LnNlbz8udGl0bGUgfHwgJyd9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlQ29udGVudCgnc2VvJywgJ3RpdGxlJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlNhYVMgUGxhdGZvcm0gLSBCdWlsZCBZb3VyIEJ1c2luZXNzXCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5SZWNvbW1lbmRlZDogNTAtNjAgY2hhcmFjdGVyczwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJzZW9LZXl3b3Jkc1wiPktleXdvcmRzPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICBpZD1cInNlb0tleXdvcmRzXCJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2NvbnRlbnQuc2VvPy5rZXl3b3JkcyB8fCAnJ31cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVDb250ZW50KCdzZW8nLCAna2V5d29yZHMnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwic2FhcywgcGxhdGZvcm0sIGJ1c2luZXNzLCBtYW5hZ2VtZW50XCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5Db21tYS1zZXBhcmF0ZWQga2V5d29yZHM8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJzZW9EZXNjcmlwdGlvblwiPk1ldGEgRGVzY3JpcHRpb248L0xhYmVsPlxuICAgICAgICAgICAgICAgIDxUZXh0YXJlYVxuICAgICAgICAgICAgICAgICAgaWQ9XCJzZW9EZXNjcmlwdGlvblwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Y29udGVudC5zZW8/LmRlc2NyaXB0aW9uIHx8ICcnfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVDb250ZW50KCdzZW8nLCAnZGVzY3JpcHRpb24nLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlRoZSBjb21wbGV0ZSBTYWFTIHBsYXRmb3JtIGZvciBtb2Rlcm4gYnVzaW5lc3Nlcy4uLlwiXG4gICAgICAgICAgICAgICAgICByb3dzPXszfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+UmVjb21tZW5kZWQ6IDE1MC0xNjAgY2hhcmFjdGVyczwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInNlb09nSW1hZ2VcIj5PcGVuIEdyYXBoIEltYWdlIFVSTDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICBpZD1cInNlb09nSW1hZ2VcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2NvbnRlbnQuc2VvPy5vZ0ltYWdlIHx8ICcnfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVDb250ZW50KCdzZW8nLCAnb2dJbWFnZScsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiaHR0cHM6Ly9leGFtcGxlLmNvbS9vZy1pbWFnZS5qcGdcIlxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+UmVjb21tZW5kZWQ6IDEyMDB4NjMwcHg8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgIDwvVGFic0NvbnRlbnQ+XG4gICAgICA8L1RhYnM+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVNlc3Npb24iLCJyZWRpcmVjdCIsIkNhcmQiLCJDYXJkQ29udGVudCIsIkNhcmREZXNjcmlwdGlvbiIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJCdXR0b24iLCJJbnB1dCIsIkxhYmVsIiwiVGV4dGFyZWEiLCJTd2l0Y2giLCJUYWJzIiwiVGFic0NvbnRlbnQiLCJUYWJzTGlzdCIsIlRhYnNUcmlnZ2VyIiwiTW9uaXRvciIsIlNhdmUiLCJSZWZyZXNoQ3ciLCJFeWUiLCJQbHVzIiwiVHJhc2gyIiwiTGF5b3V0IiwiU3RhciIsIlF1b3RlIiwidG9hc3QiLCJDTVNQYWdlIiwic2Vzc2lvbiIsImNvbnRlbnQiLCJkYXRhIiwic3RhdHVzIiwic2V0Q29udGVudCIsImhlcm8iLCJlbmFibGVkIiwidGl0bGUiLCJzdWJ0aXRsZSIsImRlc2NyaXB0aW9uIiwicHJpbWFyeUNUQSIsInRleHQiLCJsaW5rIiwic2Vjb25kYXJ5Q1RBIiwiYmFja2dyb3VuZEltYWdlIiwiYmFja2dyb3VuZFZpZGVvIiwiZmVhdHVyZXMiLCJpdGVtcyIsImlkIiwiaWNvbiIsImltYWdlIiwicHJpY2luZyIsInNob3dQcmljaW5nVGFibGUiLCJjdXN0b21NZXNzYWdlIiwidGVzdGltb25pYWxzIiwibmFtZSIsInJvbGUiLCJjb21wYW55IiwiYXZhdGFyIiwicmF0aW5nIiwiZmFxIiwicXVlc3Rpb24iLCJhbnN3ZXIiLCJjdGEiLCJidXR0b25UZXh0IiwiYnV0dG9uTGluayIsImZvb3RlciIsImNvbXBhbnlEZXNjcmlwdGlvbiIsImxpbmtzIiwic29jaWFsTGlua3MiLCJ0d2l0dGVyIiwibGlua2VkaW4iLCJmYWNlYm9vayIsImluc3RhZ3JhbSIsImNvcHlyaWdodFRleHQiLCJzZW8iLCJrZXl3b3JkcyIsIm9nSW1hZ2UiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsInNhdmluZyIsInNldFNhdmluZyIsInByZXZpZXdNb2RlIiwic2V0UHJldmlld01vZGUiLCJkaXYiLCJjbGFzc05hbWUiLCJ1c2VyIiwiZmV0Y2hDb250ZW50IiwicmVzcG9uc2UiLCJmZXRjaCIsImNyZWRlbnRpYWxzIiwianNvbiIsInN1Y2Nlc3MiLCJlcnJvciIsImNvbnNvbGUiLCJzYXZlQ29udGVudCIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsInVwZGF0ZUNvbnRlbnQiLCJzZWN0aW9uIiwia2V5IiwidmFsdWUiLCJwcmV2IiwiYWRkRmVhdHVyZSIsIm5ld0ZlYXR1cmUiLCJEYXRlIiwibm93IiwidG9TdHJpbmciLCJyZW1vdmVGZWF0dXJlIiwiZmlsdGVyIiwiaXRlbSIsInVwZGF0ZUZlYXR1cmUiLCJtYXAiLCJhZGRUZXN0aW1vbmlhbCIsIm5ld1Rlc3RpbW9uaWFsIiwicmVtb3ZlVGVzdGltb25pYWwiLCJ1cGRhdGVUZXN0aW1vbmlhbCIsImFkZEZBUSIsIm5ld0ZBUSIsInJlbW92ZUZBUSIsInVwZGF0ZUZBUSIsImgxIiwicCIsInZhcmlhbnQiLCJvbkNsaWNrIiwiZGlzYWJsZWQiLCJkZWZhdWx0VmFsdWUiLCJodG1sRm9yIiwiY2hlY2tlZCIsIm9uQ2hlY2tlZENoYW5nZSIsIm9uQ2hhbmdlIiwiZSIsInRhcmdldCIsInBsYWNlaG9sZGVyIiwicm93cyIsInNpemUiLCJmZWF0dXJlIiwiaW5kZXgiLCJoNCIsInNlbGVjdCIsIm9wdGlvbiIsInRlc3RpbW9uaWFsIiwidHlwZSIsIm1pbiIsIm1heCIsInBhcnNlSW50Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/super-admin/cms/page.tsx\n"));

/***/ })

});