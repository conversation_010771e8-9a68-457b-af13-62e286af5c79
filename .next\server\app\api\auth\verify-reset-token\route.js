"use strict";(()=>{var e={};e.id=481,e.ids=[481],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},34066:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>k,originalPathname:()=>y,patchFetch:()=>g,requestAsyncStorage:()=>h,routeModule:()=>v,serverHooks:()=>f,staticGenerationAsyncStorage:()=>m,staticGenerationBailout:()=>j});var a={};t.r(a),t.d(a,{POST:()=>c});var s=t(95419),i=t(69108),n=t(99678),o=t(78070),u=t(9108),d=t(25252),l=t(52178);let p=d.Ry({token:d.Z_().min(1,"Token is required")});async function c(e){try{let r=await e.json(),{token:t}=p.parse(r),a=await u._.passwordReset.findFirst({where:{token:t,expiresAt:{gt:new Date},usedAt:null},include:{user:{select:{id:!0,email:!0}}}});if(!a)return o.Z.json({error:"Invalid or expired reset token"},{status:400});return o.Z.json({message:"Token is valid",user:{id:a.user.id,email:a.user.email}})}catch(e){if(e instanceof l.jm)return o.Z.json({error:"Invalid request data",details:e.errors},{status:400});return console.error("Token verification error:",e),o.Z.json({error:"Internal server error"},{status:500})}}let v=new s.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/auth/verify-reset-token/route",pathname:"/api/auth/verify-reset-token",filename:"route",bundlePath:"app/api/auth/verify-reset-token/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\auth\\verify-reset-token\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:h,staticGenerationAsyncStorage:m,serverHooks:f,headerHooks:k,staticGenerationBailout:j}=v,y="/api/auth/verify-reset-token/route";function g(){return(0,n.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:m})}},9108:(e,r,t)=>{t.d(r,{_:()=>s});let a=require("@prisma/client"),s=globalThis.prisma??new a.PrismaClient}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[1638,6206,5252],()=>t(34066));module.exports=a})();