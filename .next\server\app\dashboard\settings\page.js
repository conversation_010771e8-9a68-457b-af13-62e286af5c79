(()=>{var e={};e.id=3455,e.ids=[3455],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},94527:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>l.a,__next_app__:()=>h,originalPathname:()=>x,pages:()=>o,routeModule:()=>p,tree:()=>c});var r=a(50482),i=a(69108),t=a(62563),l=a.n(t),n=a(68300),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);a.d(s,d);let c=["",{children:["dashboard",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,82849)),"C:\\proj\\nextjs-saas\\app\\dashboard\\settings\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,96339)),"C:\\proj\\nextjs-saas\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,59504)),"C:\\proj\\nextjs-saas\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,69361,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\proj\\nextjs-saas\\app\\dashboard\\settings\\page.tsx"],x="/dashboard/settings/page",h={require:a,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/dashboard/settings/page",pathname:"/dashboard/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},88126:(e,s,a)=>{Promise.resolve().then(a.bind(a,62151))},62151:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>m});var r=a(95344),i=a(61351),t=a(16212),l=a(92549),n=a(1586),d=a(69436),c=a(18822),o=a(91700),x=a(33037),h=a(23485),p=a(85674);function m(){return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Settings"}),r.jsx("p",{className:"text-gray-600 mt-1",children:"Manage your account and application preferences"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[r.jsx("div",{className:"lg:col-span-1",children:(0,r.jsxs)(i.Zb,{children:[r.jsx(i.Ol,{children:r.jsx(i.ll,{children:"Settings"})}),r.jsx(i.aY,{children:(0,r.jsxs)("nav",{className:"space-y-2",children:[(0,r.jsxs)("a",{href:"#profile",className:"flex items-center space-x-3 px-3 py-2 rounded-md bg-blue-50 text-blue-700",children:[r.jsx(c.Z,{className:"h-4 w-4"}),r.jsx("span",{children:"Profile"})]}),(0,r.jsxs)("a",{href:"#company",className:"flex items-center space-x-3 px-3 py-2 rounded-md hover:bg-gray-50",children:[r.jsx(o.Z,{className:"h-4 w-4"}),r.jsx("span",{children:"Company"})]}),(0,r.jsxs)("a",{href:"#notifications",className:"flex items-center space-x-3 px-3 py-2 rounded-md hover:bg-gray-50",children:[r.jsx(x.Z,{className:"h-4 w-4"}),r.jsx("span",{children:"Notifications"})]}),(0,r.jsxs)("a",{href:"#security",className:"flex items-center space-x-3 px-3 py-2 rounded-md hover:bg-gray-50",children:[r.jsx(h.Z,{className:"h-4 w-4"}),r.jsx("span",{children:"Security"})]}),(0,r.jsxs)("a",{href:"#billing",className:"flex items-center space-x-3 px-3 py-2 rounded-md hover:bg-gray-50",children:[r.jsx(p.Z,{className:"h-4 w-4"}),r.jsx("span",{children:"Billing"})]})]})})]})}),(0,r.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,r.jsxs)(i.Zb,{id:"profile",children:[r.jsx(i.Ol,{children:(0,r.jsxs)(i.ll,{className:"flex items-center space-x-2",children:[r.jsx(c.Z,{className:"h-5 w-5"}),r.jsx("span",{children:"Profile Settings"})]})}),(0,r.jsxs)(i.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[r.jsx(n._,{htmlFor:"firstName",children:"First Name"}),r.jsx(l.I,{id:"firstName",placeholder:"John"})]}),(0,r.jsxs)("div",{children:[r.jsx(n._,{htmlFor:"lastName",children:"Last Name"}),r.jsx(l.I,{id:"lastName",placeholder:"Doe"})]})]}),(0,r.jsxs)("div",{children:[r.jsx(n._,{htmlFor:"email",children:"Email"}),r.jsx(l.I,{id:"email",type:"email",placeholder:"<EMAIL>"})]}),(0,r.jsxs)("div",{children:[r.jsx(n._,{htmlFor:"phone",children:"Phone"}),r.jsx(l.I,{id:"phone",placeholder:"+****************"})]}),r.jsx(t.z,{children:"Save Changes"})]})]}),(0,r.jsxs)(i.Zb,{id:"company",children:[r.jsx(i.Ol,{children:(0,r.jsxs)(i.ll,{className:"flex items-center space-x-2",children:[r.jsx(o.Z,{className:"h-5 w-5"}),r.jsx("span",{children:"Company Settings"})]})}),(0,r.jsxs)(i.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[r.jsx(n._,{htmlFor:"companyName",children:"Company Name"}),r.jsx(l.I,{id:"companyName",placeholder:"Acme Corporation"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[r.jsx(n._,{htmlFor:"industry",children:"Industry"}),r.jsx(l.I,{id:"industry",placeholder:"Technology"})]}),(0,r.jsxs)("div",{children:[r.jsx(n._,{htmlFor:"companySize",children:"Company Size"}),(0,r.jsxs)("select",{className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[r.jsx("option",{children:"1-10 employees"}),r.jsx("option",{children:"11-50 employees"}),r.jsx("option",{children:"51-200 employees"}),r.jsx("option",{children:"200+ employees"})]})]})]}),(0,r.jsxs)("div",{children:[r.jsx(n._,{htmlFor:"website",children:"Website"}),r.jsx(l.I,{id:"website",placeholder:"https://example.com"})]}),r.jsx(t.z,{children:"Update Company"})]})]}),(0,r.jsxs)(i.Zb,{id:"notifications",children:[r.jsx(i.Ol,{children:(0,r.jsxs)(i.ll,{className:"flex items-center space-x-2",children:[r.jsx(x.Z,{className:"h-5 w-5"}),r.jsx("span",{children:"Notification Preferences"})]})}),(0,r.jsxs)(i.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-medium",children:"Email Notifications"}),r.jsx("p",{className:"text-sm text-gray-500",children:"Receive notifications via email"})]}),r.jsx("input",{type:"checkbox",className:"toggle",defaultChecked:!0})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-medium",children:"New Customer Alerts"}),r.jsx("p",{className:"text-sm text-gray-500",children:"Get notified when new customers sign up"})]}),r.jsx("input",{type:"checkbox",className:"toggle",defaultChecked:!0})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-medium",children:"Payment Notifications"}),r.jsx("p",{className:"text-sm text-gray-500",children:"Receive alerts for payments and invoices"})]}),r.jsx("input",{type:"checkbox",className:"toggle",defaultChecked:!0})]}),r.jsx(t.z,{children:"Save Preferences"})]})]}),(0,r.jsxs)(i.Zb,{id:"security",children:[r.jsx(i.Ol,{children:(0,r.jsxs)(i.ll,{className:"flex items-center space-x-2",children:[r.jsx(h.Z,{className:"h-5 w-5"}),r.jsx("span",{children:"Security Settings"})]})}),(0,r.jsxs)(i.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[r.jsx(n._,{htmlFor:"currentPassword",children:"Current Password"}),r.jsx(l.I,{id:"currentPassword",type:"password"})]}),(0,r.jsxs)("div",{children:[r.jsx(n._,{htmlFor:"newPassword",children:"New Password"}),r.jsx(l.I,{id:"newPassword",type:"password"})]}),(0,r.jsxs)("div",{children:[r.jsx(n._,{htmlFor:"confirmPassword",children:"Confirm New Password"}),r.jsx(l.I,{id:"confirmPassword",type:"password"})]}),r.jsx(t.z,{children:"Change Password"})]})]}),(0,r.jsxs)(i.Zb,{id:"billing",children:[r.jsx(i.Ol,{children:(0,r.jsxs)(i.ll,{className:"flex items-center space-x-2",children:[r.jsx(p.Z,{className:"h-5 w-5"}),r.jsx("span",{children:"Billing & Subscription"})]})}),(0,r.jsxs)(i.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-medium",children:"Current Plan"}),r.jsx("p",{className:"text-sm text-gray-500",children:"Professional Plan"})]}),r.jsx(d.C,{variant:"success",children:"Active"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-medium",children:"Next Billing Date"}),r.jsx("p",{className:"text-sm text-gray-500",children:"February 15, 2024"})]}),r.jsx("span",{className:"font-medium",children:"$49/month"})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[r.jsx(t.z,{variant:"outline",children:"Change Plan"}),r.jsx(t.z,{variant:"outline",children:"Update Payment Method"})]})]})]})]})]})]})}},92549:(e,s,a)=>{"use strict";a.d(s,{I:()=>l});var r=a(95344),i=a(3729),t=a(91626);let l=i.forwardRef(({className:e,type:s,...a},i)=>r.jsx("input",{type:s,className:(0,t.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:i,...a}));l.displayName="Input"},1586:(e,s,a)=>{"use strict";a.d(s,{_:()=>c});var r=a(95344),i=a(3729),t=a(14217),l=a(49247),n=a(91626);let d=(0,l.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=i.forwardRef(({className:e,...s},a)=>r.jsx(t.f,{ref:a,className:(0,n.cn)(d(),e),...s}));c.displayName=t.f.displayName},71542:(e,s,a)=>{"use strict";a.d(s,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(69224).Z)("FileCheck",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["path",{d:"m9 15 2 2 4-4",key:"1grp1n"}]])},91917:(e,s,a)=>{"use strict";a.d(s,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(69224).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},74243:(e,s,a)=>{"use strict";a.d(s,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(69224).Z)("Receipt",[["path",{d:"M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1-2-1Z",key:"wqdwcb"}],["path",{d:"M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8",key:"1h4pet"}],["path",{d:"M12 17V7",key:"pyj7ub"}]])},28240:(e,s,a)=>{"use strict";a.d(s,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(69224).Z)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},82849:(e,s,a)=>{"use strict";a.r(s),a.d(s,{$$typeof:()=>t,__esModule:()=>i,default:()=>l});let r=(0,a(86843).createProxy)(String.raw`C:\proj\nextjs-saas\app\dashboard\settings\page.tsx`),{__esModule:i,$$typeof:t}=r,l=r.default},14217:(e,s,a)=>{"use strict";a.d(s,{f:()=>n});var r=a(3729),i=a(62409),t=a(95344),l=r.forwardRef((e,s)=>(0,t.jsx)(i.WV.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));l.displayName="Label";var n=l}};var s=require("../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),r=s.X(0,[1638,7948,6671,4626,7792,2506,2125,5045],()=>a(94527));module.exports=r})();