import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Building2,
  Users,
  FileText,
  CreditCard,
  BarChart3,
  Shield,
  Zap,
  Globe,
  CheckCircle,
  ArrowRight,
  Star,
  Quote
} from 'lucide-react'
import Link from 'next/link'
import { LandingPageContent } from '@/components/landing/landing-page-content'

const features = [
  {
    icon: Users,
    title: 'Customer Management',
    description: 'Complete CRM with lead tracking, customer profiles, and activity management.'
  },
  {
    icon: FileText,
    title: 'Invoicing & Quotations',
    description: 'Professional invoices and quotations with automated calculations and templates.'
  },
  {
    icon: Building2,
    title: 'Multi-Tenant Architecture',
    description: 'Secure data isolation with company-based access control and team management.'
  },
  {
    icon: CreditCard,
    title: 'Subscription Management',
    description: 'Flexible pricing plans with trial management and automated billing.'
  },
  {
    icon: BarChart3,
    title: 'Analytics & Reports',
    description: 'Real-time dashboards with business insights and performance metrics.'
  },
  {
    icon: Shield,
    title: 'Enterprise Security',
    description: 'Role-based access control with audit logs and data encryption.'
  }
]

const businessTypes = [
  { name: 'Retail Business', color: 'bg-blue-100 text-blue-800' },
  { name: 'Healthcare Services', color: 'bg-red-100 text-red-800' },
  { name: 'Consulting Services', color: 'bg-indigo-100 text-indigo-800' },
  { name: 'Manufacturing', color: 'bg-orange-100 text-orange-800' },
  { name: 'Education Services', color: 'bg-green-100 text-green-800' },
  { name: 'Jewellery Business', color: 'bg-purple-100 text-purple-800' }
]

const benefits = [
  'Complete business management in one platform',
  'Industry-specific templates and workflows',
  'Real-time collaboration and team management',
  'Automated invoicing and payment tracking',
  'Advanced analytics and reporting',
  'Mobile-responsive design',
  '24/7 customer support',
  'Regular updates and new features'
]

export default function HomePage() {
  return <LandingPageContent />

}
