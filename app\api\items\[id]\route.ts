import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateItemSchema = z.object({
  name: z.string().min(1, 'Name is required').optional(),
  description: z.string().optional(),
  sku: z.string().optional(),
  category: z.string().optional(),
  unitPrice: z.number().min(0, 'Unit price must be positive').optional(),
  costPrice: z.number().min(0, 'Cost price must be positive').optional(),
  currency: z.string().optional(),
  trackInventory: z.boolean().optional(),
  stockQuantity: z.number().int().min(0).optional(),
  lowStockAlert: z.number().int().min(0).optional(),
  taxable: z.boolean().optional(),
  taxRate: z.number().min(0).max(100).optional(),
  accountingCode: z.string().optional(),
  active: z.boolean().optional()
})

// GET /api/items/[id] - Get single item
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const item = await prisma.item.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId
      },
      include: {
        createdBy: {
          select: {
            name: true,
            email: true
          }
        },
        quotationItems: {
          include: {
            quotation: {
              select: {
                id: true,
                quotationNumber: true,
                title: true,
                status: true,
                total: true,
                createdAt: true,
                customer: {
                  select: {
                    name: true,
                    company: true
                  }
                }
              }
            }
          },
          orderBy: { createdAt: 'desc' },
          take: 10
        },
        invoiceItems: {
          include: {
            invoice: {
              select: {
                id: true,
                invoiceNumber: true,
                title: true,
                status: true,
                total: true,
                createdAt: true,
                customer: {
                  select: {
                    name: true,
                    company: true
                  }
                }
              }
            }
          },
          orderBy: { createdAt: 'desc' },
          take: 10
        }
      }
    })

    if (!item) {
      return NextResponse.json({ error: 'Item not found' }, { status: 404 })
    }

    // Calculate usage statistics
    const quotationUsage = item.quotationItems.reduce((sum, qi) => sum + qi.quantity, 0)
    const invoiceUsage = item.invoiceItems.reduce((sum, ii) => sum + ii.quantity, 0)
    const totalUsage = quotationUsage + invoiceUsage
    const usageCount = item.quotationItems.length + item.invoiceItems.length

    // Calculate revenue from usage
    const quotationRevenue = item.quotationItems.reduce((sum, qi) => sum + (qi.quantity * Number(qi.unitPrice)), 0)
    const invoiceRevenue = item.invoiceItems.reduce((sum, ii) => sum + (ii.quantity * Number(ii.unitPrice)), 0)
    const totalRevenue = quotationRevenue + invoiceRevenue

    return NextResponse.json({
      item: {
        id: item.id,
        name: item.name,
        description: item.description,
        sku: item.sku,
        category: item.category,
        unitPrice: Number(item.unitPrice),
        costPrice: item.costPrice ? Number(item.costPrice) : null,
        currency: item.currency,
        trackInventory: item.trackInventory,
        stockQuantity: item.stockQuantity,
        lowStockAlert: item.lowStockAlert,
        taxable: item.taxable,
        taxRate: Number(item.taxRate),
        accountingCode: item.accountingCode,
        active: item.active,
        createdBy: item.createdBy,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
        usage: {
          totalQuantity: totalUsage,
          usageCount,
          quotationUsage,
          invoiceUsage,
          totalRevenue,
          quotationRevenue,
          invoiceRevenue,
          quotations: item.quotationItems.map(qi => ({
            id: qi.id,
            quantity: qi.quantity,
            unitPrice: Number(qi.unitPrice),
            total: qi.quantity * Number(qi.unitPrice),
            quotation: qi.quotation
          })),
          invoices: item.invoiceItems.map(ii => ({
            id: ii.id,
            quantity: ii.quantity,
            unitPrice: Number(ii.unitPrice),
            total: ii.quantity * Number(ii.unitPrice),
            invoice: ii.invoice
          }))
        },
        stockValue: item.trackInventory && item.stockQuantity 
          ? Number(item.unitPrice) * item.stockQuantity 
          : 0,
        profitMargin: item.costPrice 
          ? ((Number(item.unitPrice) - Number(item.costPrice)) / Number(item.unitPrice)) * 100
          : null,
        profitPerUnit: item.costPrice 
          ? Number(item.unitPrice) - Number(item.costPrice)
          : null,
        isLowStock: item.trackInventory && item.stockQuantity !== null && item.lowStockAlert !== null
          ? item.stockQuantity <= item.lowStockAlert
          : false
      }
    })

  } catch (error) {
    console.error('Error fetching item:', error)
    return NextResponse.json(
      { error: 'Failed to fetch item' },
      { status: 500 }
    )
  }
}

// PUT /api/items/[id] - Update item
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = updateItemSchema.parse(body)

    // Check if item exists and belongs to user's company
    const existingItem = await prisma.item.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId
      }
    })

    if (!existingItem) {
      return NextResponse.json({ error: 'Item not found' }, { status: 404 })
    }

    // Check for duplicate SKU if updating SKU
    if (validatedData.sku && validatedData.sku !== existingItem.sku) {
      const duplicateItem = await prisma.item.findFirst({
        where: {
          sku: validatedData.sku,
          companyId: session.user.companyId,
          id: { not: params.id }
        }
      })

      if (duplicateItem) {
        return NextResponse.json(
          { error: 'An item with this SKU already exists' },
          { status: 400 }
        )
      }
    }

    // Validate inventory settings
    if (validatedData.trackInventory !== undefined) {
      if (validatedData.trackInventory && validatedData.stockQuantity === undefined && existingItem.stockQuantity === null) {
        return NextResponse.json(
          { error: 'Stock quantity is required when enabling inventory tracking' },
          { status: 400 }
        )
      }
      
      if (!validatedData.trackInventory) {
        // Clear inventory fields if disabling tracking
        validatedData.stockQuantity = null
        validatedData.lowStockAlert = null
      }
    }

    const item = await prisma.$transaction(async (tx) => {
      // Update the item
      const updatedItem = await tx.item.update({
        where: { id: params.id },
        data: validatedData,
        include: {
          createdBy: {
            select: {
              name: true,
              email: true
            }
          }
        }
      })

      // Log activity
      await tx.activity.create({
        data: {
          type: 'ITEM',
          title: 'Item Updated',
          description: `Item "${updatedItem.name}" was updated`,
          itemId: params.id,
          companyId: session.user.companyId!,
          createdById: session.user.id
        }
      })

      return updatedItem
    })

    return NextResponse.json({
      item: {
        id: item.id,
        name: item.name,
        description: item.description,
        sku: item.sku,
        category: item.category,
        unitPrice: Number(item.unitPrice),
        costPrice: item.costPrice ? Number(item.costPrice) : null,
        currency: item.currency,
        trackInventory: item.trackInventory,
        stockQuantity: item.stockQuantity,
        lowStockAlert: item.lowStockAlert,
        taxable: item.taxable,
        taxRate: Number(item.taxRate),
        accountingCode: item.accountingCode,
        active: item.active,
        createdBy: item.createdBy,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
        stockValue: item.trackInventory && item.stockQuantity 
          ? Number(item.unitPrice) * item.stockQuantity 
          : 0,
        profitMargin: item.costPrice 
          ? ((Number(item.unitPrice) - Number(item.costPrice)) / Number(item.unitPrice)) * 100
          : null,
        isLowStock: item.trackInventory && item.stockQuantity !== null && item.lowStockAlert !== null
          ? item.stockQuantity <= item.lowStockAlert
          : false
      },
      message: 'Item updated successfully'
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating item:', error)
    return NextResponse.json(
      { error: 'Failed to update item' },
      { status: 500 }
    )
  }
}

// DELETE /api/items/[id] - Delete item
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if item exists and belongs to user's company
    const item = await prisma.item.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId
      },
      include: {
        quotationItems: true,
        invoiceItems: true
      }
    })

    if (!item) {
      return NextResponse.json({ error: 'Item not found' }, { status: 404 })
    }

    // Check if item is used in quotations or invoices
    if (item.quotationItems.length > 0 || item.invoiceItems.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete item that is used in quotations or invoices. Consider deactivating it instead.' },
        { status: 400 }
      )
    }

    await prisma.$transaction(async (tx) => {
      // Delete the item
      await tx.item.delete({
        where: { id: params.id }
      })

      // Log activity
      await tx.activity.create({
        data: {
          type: 'ITEM',
          title: 'Item Deleted',
          description: `Item "${item.name}" was deleted`,
          companyId: session.user.companyId!,
          createdById: session.user.id
        }
      })
    })

    return NextResponse.json({ message: 'Item deleted successfully' })

  } catch (error) {
    console.error('Error deleting item:', error)
    return NextResponse.json(
      { error: 'Failed to delete item' },
      { status: 500 }
    )
  }
}
