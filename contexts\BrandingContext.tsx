'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'

interface BrandingConfig {
  appName: string
  logoUrl: string
  faviconUrl: string
  primaryColor: string
  secondaryColor: string
  accentColor: string
  backgroundColor: string
  textColor: string
  theme: string
  fontFamily: string
  customCss: string
}

interface BrandingContextType {
  branding: BrandingConfig
  updateBranding: (config: Partial<BrandingConfig>) => void
  loading: boolean
}

const defaultBranding: BrandingConfig = {
  appName: 'SaaS Platform',
  logoUrl: '',
  faviconUrl: '',
  primaryColor: '#3b82f6',
  secondaryColor: '#64748b',
  accentColor: '#10b981',
  backgroundColor: '#ffffff',
  textColor: '#1f2937',
  theme: 'light',
  fontFamily: 'Inter, sans-serif',
  customCss: ''
}

const BrandingContext = createContext<BrandingContextType | undefined>(undefined)

export function BrandingProvider({ children }: { children: React.ReactNode }) {
  const [branding, setBranding] = useState<BrandingConfig>(defaultBranding)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchBranding()
  }, [])

  const fetchBranding = async () => {
    try {
      const response = await fetch('/api/global-config/branding')
      const data = await response.json()
      
      if (data.success && data.branding) {
        setBranding({ ...defaultBranding, ...data.branding })
        applyBranding({ ...defaultBranding, ...data.branding })
      } else {
        applyBranding(defaultBranding)
      }
    } catch (error) {
      console.error('Error fetching branding config:', error)
      applyBranding(defaultBranding)
    } finally {
      setLoading(false)
    }
  }

  const updateBranding = (config: Partial<BrandingConfig>) => {
    const newBranding = { ...branding, ...config }
    setBranding(newBranding)
    applyBranding(newBranding)
  }

  const applyBranding = (config: BrandingConfig) => {
    // Apply CSS custom properties
    const root = document.documentElement
    root.style.setProperty('--primary-color', config.primaryColor)
    root.style.setProperty('--secondary-color', config.secondaryColor)
    root.style.setProperty('--accent-color', config.accentColor)
    root.style.setProperty('--background-color', config.backgroundColor)
    root.style.setProperty('--text-color', config.textColor)
    root.style.setProperty('--font-family', config.fontFamily)

    // Apply theme class
    document.body.className = document.body.className.replace(/theme-\w+/g, '')
    document.body.classList.add(`theme-${config.theme}`)

    // Update document title
    document.title = config.appName

    // Update favicon
    if (config.faviconUrl) {
      let favicon = document.querySelector('link[rel="icon"]') as HTMLLinkElement
      if (!favicon) {
        favicon = document.createElement('link')
        favicon.rel = 'icon'
        document.head.appendChild(favicon)
      }
      favicon.href = config.faviconUrl
    }

    // Apply custom CSS
    let customStyleElement = document.getElementById('custom-branding-css')
    if (config.customCss) {
      if (!customStyleElement) {
        customStyleElement = document.createElement('style')
        customStyleElement.id = 'custom-branding-css'
        document.head.appendChild(customStyleElement)
      }
      customStyleElement.textContent = config.customCss
    } else if (customStyleElement) {
      customStyleElement.remove()
    }

    // Update meta theme-color for mobile browsers
    let themeColorMeta = document.querySelector('meta[name="theme-color"]') as HTMLMetaElement
    if (!themeColorMeta) {
      themeColorMeta = document.createElement('meta')
      themeColorMeta.name = 'theme-color'
      document.head.appendChild(themeColorMeta)
    }
    themeColorMeta.content = config.primaryColor
  }

  return (
    <BrandingContext.Provider value={{ branding, updateBranding, loading }}>
      {children}
    </BrandingContext.Provider>
  )
}

export function useBranding() {
  const context = useContext(BrandingContext)
  if (context === undefined) {
    throw new Error('useBranding must be used within a BrandingProvider')
  }
  return context
}

export default BrandingContext
