"use strict";(()=>{var e={};e.id=9543,e.ids=[9543],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},78557:(e,t,n)=>{n.r(t),n.d(t,{headerHooks:()=>b,originalPathname:()=>f,patchFetch:()=>w,requestAsyncStorage:()=>m,routeModule:()=>p,serverHooks:()=>y,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>h});var i={};n.r(i),n.d(i,{GET:()=>d});var s=n(95419),o=n(69108),r=n(99678),a=n(78070),u=n(81355),c=n(3205),l=n(9108);async function d(e){try{let e=await (0,u.getServerSession)(c.L);if(!e?.user?.id||!e?.user?.companyId)return a.Z.json({error:"Unauthorized"},{status:401});let[t,n,i,s,o,r,d,p,m,g]=await Promise.all([l._.companySettings.findUnique({where:{companyId:e.user.companyId}}),Promise.all([l._.userSettings.count({where:{user:{companyId:e.user.companyId}}}),l._.userSettings.groupBy({by:["theme"],where:{user:{companyId:e.user.companyId}},_count:{id:!0}}),l._.userSettings.aggregate({where:{user:{companyId:e.user.companyId}},_count:{emailNotifications:!0,pushNotifications:!0,smsNotifications:!0}})]),Promise.all([l._.systemSettings.count(),l._.systemSettings.groupBy({by:["category"],_count:{id:!0}}),l._.systemSettings.groupBy({by:["isPublic"],_count:{id:!0}}),l._.systemSettings.groupBy({by:["isEditable"],_count:{id:!0}})]),t?.featureSettings?Object.entries(t.featureSettings).map(([e,t])=>({feature:e,enabled:!!t})):[],t?.securitySettings?{twoFactorEnabled:!!t.securitySettings?.twoFactorRequired,sessionTimeout:t.securitySettings?.sessionTimeout||30,passwordPolicyEnabled:!!t.securitySettings?.passwordPolicy,ipWhitelistCount:Array.isArray(t.securitySettings?.ipWhitelist)?t.securitySettings.ipWhitelist.length:0,allowedDomainsCount:Array.isArray(t.securitySettings?.allowedDomains)?t.securitySettings.allowedDomains.length:0}:null,t?.integrationSettings?{webhooksCount:Array.isArray(t.integrationSettings?.webhooks)?t.integrationSettings.webhooks.length:0,apiKeysCount:Object.keys(t.integrationSettings?.apiKeys||{}).length,connectedServicesCount:Object.keys(t.integrationSettings?.connectedServices||{}).length}:null,t?.notificationSettings?Object.entries(t.notificationSettings).map(([e,t])=>({type:e,enabled:!!t})):[],l._.userSettings.groupBy({by:["theme"],where:{user:{companyId:e.user.companyId}},_count:{id:!0}}),l._.userSettings.groupBy({by:["language"],where:{user:{companyId:e.user.companyId}},_count:{id:!0}}),l._.userSettings.groupBy({by:["timezone"],where:{user:{companyId:e.user.companyId}},_count:{id:!0}})]),[y,b,h]=n,[f,w,S,_]=i,C=await l._.user.count({where:{companyId:e.user.companyId}});return a.Z.json({summary:{totalUsers:C,usersWithSettings:y,settingsConfigurationRate:C>0?y/C*100:0,totalSystemSettings:f,companySettingsConfigured:!!t,lastSettingsUpdate:t?.updatedAt||null},companySettings:{configured:!!t,currency:t?.defaultCurrency||"USD",timezone:t?.timezone||"UTC",language:t?.language||"en",taxRate:t?Number(t.taxRate):0,branding:{primaryColor:t?.primaryColor||"#3b82f6",secondaryColor:t?.secondaryColor||"#1e3a8a",accentColor:t?.accentColor||"#f59e0b",fontFamily:t?.fontFamily||"Inter"},documentSettings:{invoicePrefix:t?.invoicePrefix||"INV",quotationPrefix:t?.quotationPrefix||"QUO",contractPrefix:t?.contractPrefix||"CON",invoiceNumbering:t?.invoiceNumbering||"sequential"}},userPreferences:{themes:b.map(e=>({theme:e.theme,count:e._count.id,percentage:y>0?e._count.id/y*100:0})),languages:m.map(e=>({language:e.language,count:e._count.id,percentage:y>0?e._count.id/y*100:0})),timezones:g.map(e=>({timezone:e.timezone,count:e._count.id,percentage:y>0?e._count.id/y*100:0})),notifications:{emailEnabled:y>0?h._count.emailNotifications/y*100:0,pushEnabled:y>0?h._count.pushNotifications/y*100:0,smsEnabled:y>0?h._count.smsNotifications/y*100:0}},systemSettings:{total:f,categories:w.map(e=>({category:e.category,count:e._count.id})),public:S.find(e=>e.isPublic)?._count.id||0,private:S.find(e=>!e.isPublic)?._count.id||0,editable:_.find(e=>e.isEditable)?._count.id||0,readonly:_.find(e=>!e.isEditable)?._count.id||0},features:{enabled:s.filter(e=>e.enabled).length,disabled:s.filter(e=>!e.enabled).length,usage:s.map(e=>({feature:e.feature,enabled:e.enabled}))},security:o?{twoFactorEnabled:o.twoFactorEnabled,sessionTimeout:o.sessionTimeout,passwordPolicyEnabled:o.passwordPolicyEnabled,ipWhitelistCount:o.ipWhitelistCount,allowedDomainsCount:o.allowedDomainsCount,securityScore:[o.twoFactorEnabled?20:0,o.passwordPolicyEnabled?20:0,o.sessionTimeout<=30?20:0,o.ipWhitelistCount>0?20:0,o.allowedDomainsCount>0?20:0].reduce((e,t)=>e+t,0)}:null,integrations:r?{webhooksCount:r.webhooksCount,apiKeysCount:r.apiKeysCount,connectedServicesCount:r.connectedServicesCount,integrationScore:[r.webhooksCount>0?33:0,r.apiKeysCount>0?33:0,r.connectedServicesCount>0?34:0].reduce((e,t)=>e+t,0)}:null,notifications:{types:d.map(e=>({type:e.type,enabled:e.enabled})),enabledCount:d.filter(e=>e.enabled).length,disabledCount:d.filter(e=>!e.enabled).length}})}catch(e){return console.error("Error fetching settings analytics:",e),a.Z.json({error:"Failed to fetch settings analytics"},{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/settings/analytics/route",pathname:"/api/settings/analytics",filename:"route",bundlePath:"app/api/settings/analytics/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\settings\\analytics\\route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:m,staticGenerationAsyncStorage:g,serverHooks:y,headerHooks:b,staticGenerationBailout:h}=p,f="/api/settings/analytics/route";function w(){return(0,r.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:g})}},3205:(e,t,n)=>{n.d(t,{L:()=>c});var i=n(86485),s=n(10375),o=n(50694),r=n(6521),a=n.n(r),u=n(9108);let c={providers:[(0,i.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await u._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),n=t?.companyId;if(!n&&t){let e=await u._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(n=e?.id)&&await u._.user.update({where:{id:t.id},data:{companyId:n}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await a().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await u._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:n}}catch(e){return console.error("Authentication error:",e),null}}}),(0,s.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,o.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,n)=>{n.d(t,{_:()=>s});let i=require("@prisma/client"),s=globalThis.prisma??new i.PrismaClient}};var t=require("../../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),i=t.X(0,[1638,6206,6521,2455,4520],()=>n(78557));module.exports=i})();