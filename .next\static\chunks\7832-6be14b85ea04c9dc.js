"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7832],{47832:function(e,t,s){s.d(t,{d:function(){return v}});var i=s(57437),a=s(2265),n=s(61865),l=s(37570),r=s(92160),d=s(85754),c=s(45179),o=s(49842),u=s(42706),m=s(9883),x=s(45367),p=s(92919),h=s(5925);let j=r.Ry({description:r.Z_().min(1,"Description is required"),quantity:r.Rx().min(1,"Quantity must be at least 1"),unitPrice:r.Rx().min(0,"Unit price must be positive"),discount:r.Rx().min(0).max(100).default(0),taxRate:r.Rx().min(0).max(100).default(0)}),y=r.Ry({title:r.Z_().min(1,"Title is required"),description:r.Z_().optional(),customerId:r.Z_().min(1,"Customer is required"),leadId:r.Z_().optional(),status:r.Km(["DRAFT","SENT","VIEWED","ACCEPTED","REJECTED","EXPIRED"]).default("DRAFT"),validUntil:r.Z_().optional(),terms:r.Z_().optional(),notes:r.Z_().optional(),paymentTerms:r.Z_().optional(),paymentDueDays:r.Rx().min(0).default(30),items:r.IX(j).min(1,"At least one item is required"),taxRate:r.Rx().min(0).max(100).default(0),discountType:r.Km(["PERCENTAGE","FIXED"]).default("PERCENTAGE"),discountValue:r.Rx().min(0).default(0)});function v(e){let{isOpen:t,onClose:s,onSuccess:r,quotation:j,mode:v,preselectedCustomerId:f,preselectedLeadId:b}=e,[g,N]=(0,a.useState)(!1),[E,T]=(0,a.useState)([]),[R,I]=(0,a.useState)([]),{register:w,handleSubmit:D,formState:{errors:A},reset:F,watch:_,control:C,setValue:P}=(0,n.cI)({resolver:(0,l.F)(y),defaultValues:j?{title:j.title,description:j.description||"",customerId:j.customerId||f||"",leadId:j.leadId||b||"",status:j.status||"DRAFT",validUntil:j.validUntil?new Date(j.validUntil).toISOString().split("T")[0]:"",terms:j.terms||"",notes:j.notes||"",items:j.items||[{description:"",quantity:1,unitPrice:0,discount:0,taxRate:0}],taxRate:j.taxRate||0,discountType:j.discountType||"PERCENTAGE",discountValue:j.discountValue||0}:{status:"DRAFT",customerId:f||"",leadId:b||"",items:[{description:"",quantity:1,unitPrice:0,discount:0,taxRate:0}],taxRate:0,discountType:"PERCENTAGE",discountValue:0}}),{fields:q,append:S,remove:U}=(0,n.Dq)({control:C,name:"items"}),V=_("items"),Z=_("taxRate"),k=_("discountType"),G=_("discountValue");(0,a.useEffect)(()=>{let e=async()=>{try{let[e,t]=await Promise.all([fetch("/api/customers?limit=100"),fetch("/api/leads?limit=100")]);if(e.ok){let t=await e.json();T(t.customers)}if(t.ok){let e=await t.json();I(e.leads)}}catch(e){console.error("Error fetching data:",e)}};t&&e()},[t]);let Q=(()=>{let e=V.reduce((e,t)=>{let s=(t.quantity||0)*(t.unitPrice||0),i=s*(t.discount||0)/100,a=s-i,n=a*(t.taxRate||0)/100;return e+a+n},0),t=e,s=(t="PERCENTAGE"===k?e-e*(G||0)/100:e-(G||0))*(Z||0)/100;return{subtotal:Math.round(100*e)/100,total:Math.round(100*(t+s))/100,taxAmount:Math.round(100*s)/100,discountAmount:"PERCENTAGE"===k?Math.round(e*(G||0)/100*100)/100:G||0}})(),$=e=>{q.length>1&&U(e)},z=async e=>{N(!0);try{let t="create"===v?"/api/quotations":"/api/quotations/".concat(j.id),i=await fetch(t,{method:"create"===v?"POST":"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!i.ok){let e=await i.json();throw Error(e.error||"Failed to save quotation")}h.toast.success("Quotation ".concat("create"===v?"created":"updated"," successfully")),F(),r(),s()}catch(e){h.toast.error(e instanceof Error?e.message:"An error occurred")}finally{N(!1)}},O=()=>{F(),s()};return(0,i.jsx)(u.Vq,{open:t,onOpenChange:O,children:(0,i.jsxs)(u.cZ,{className:"max-w-6xl max-h-[90vh] overflow-y-auto",children:[(0,i.jsxs)(u.fK,{children:[(0,i.jsx)(u.$N,{children:"create"===v?"Create New Quotation":"Edit Quotation"}),(0,i.jsx)(u.Be,{children:"create"===v?"Create a new quotation with items, pricing, and terms.":"Update the quotation information and items."})]}),(0,i.jsxs)("form",{onSubmit:D(z),className:"space-y-6",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(o._,{htmlFor:"title",children:"Title *"}),(0,i.jsx)(c.I,{id:"title",...w("title"),placeholder:"Quotation title"}),A.title&&(0,i.jsx)("p",{className:"text-sm text-red-600 mt-1",children:A.title.message})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(o._,{htmlFor:"status",children:"Status"}),(0,i.jsx)("select",{id:"status",...w("status"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[{value:"DRAFT",label:"Draft"},{value:"SENT",label:"Sent"},{value:"VIEWED",label:"Viewed"},{value:"ACCEPTED",label:"Accepted"},{value:"REJECTED",label:"Rejected"},{value:"EXPIRED",label:"Expired"}].map(e=>(0,i.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(o._,{htmlFor:"customerId",children:"Customer *"}),(0,i.jsxs)("select",{id:"customerId",...w("customerId"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,i.jsx)("option",{value:"",children:"Select a customer"}),E.map(e=>(0,i.jsxs)("option",{value:e.id,children:[e.name," ",e.company&&"(".concat(e.company,")")]},e.id))]}),A.customerId&&(0,i.jsx)("p",{className:"text-sm text-red-600 mt-1",children:A.customerId.message})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(o._,{htmlFor:"leadId",children:"Related Lead"}),(0,i.jsxs)("select",{id:"leadId",...w("leadId"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,i.jsx)("option",{value:"",children:"Select a lead (optional)"}),R.map(e=>(0,i.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(o._,{htmlFor:"validUntil",children:"Valid Until"}),(0,i.jsx)(c.I,{id:"validUntil",type:"date",...w("validUntil")})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(o._,{htmlFor:"description",children:"Description"}),(0,i.jsx)("textarea",{id:"description",...w("description"),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Brief description of the quotation..."})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("h3",{className:"text-lg font-medium",children:"Items"}),(0,i.jsxs)(d.z,{type:"button",onClick:()=>{S({description:"",quantity:1,unitPrice:0,discount:0,taxRate:0})},size:"sm",children:[(0,i.jsx)(m.Z,{className:"h-4 w-4 mr-2"}),"Add Item"]})]}),(0,i.jsx)("div",{className:"space-y-4",children:q.map((e,t)=>{var s,a,n,l;return(0,i.jsxs)("div",{className:"grid grid-cols-12 gap-2 items-end p-4 border rounded-lg",children:[(0,i.jsxs)("div",{className:"col-span-4",children:[(0,i.jsx)(o._,{htmlFor:"items.".concat(t,".description"),children:"Description *"}),(0,i.jsx)(c.I,{...w("items.".concat(t,".description")),placeholder:"Item description"}),(null===(a=A.items)||void 0===a?void 0:null===(s=a[t])||void 0===s?void 0:s.description)&&(0,i.jsx)("p",{className:"text-sm text-red-600 mt-1",children:null===(l=A.items[t])||void 0===l?void 0:null===(n=l.description)||void 0===n?void 0:n.message})]}),(0,i.jsxs)("div",{className:"col-span-2",children:[(0,i.jsx)(o._,{htmlFor:"items.".concat(t,".quantity"),children:"Qty *"}),(0,i.jsx)(c.I,{type:"number",min:"1",...w("items.".concat(t,".quantity"),{valueAsNumber:!0}),placeholder:"1"})]}),(0,i.jsxs)("div",{className:"col-span-2",children:[(0,i.jsx)(o._,{htmlFor:"items.".concat(t,".unitPrice"),children:"Unit Price *"}),(0,i.jsx)(c.I,{type:"number",min:"0",step:"0.01",...w("items.".concat(t,".unitPrice"),{valueAsNumber:!0}),placeholder:"0.00"})]}),(0,i.jsxs)("div",{className:"col-span-1",children:[(0,i.jsx)(o._,{htmlFor:"items.".concat(t,".discount"),children:"Disc %"}),(0,i.jsx)(c.I,{type:"number",min:"0",max:"100",...w("items.".concat(t,".discount"),{valueAsNumber:!0}),placeholder:"0"})]}),(0,i.jsxs)("div",{className:"col-span-1",children:[(0,i.jsx)(o._,{htmlFor:"items.".concat(t,".taxRate"),children:"Tax %"}),(0,i.jsx)(c.I,{type:"number",min:"0",max:"100",...w("items.".concat(t,".taxRate"),{valueAsNumber:!0}),placeholder:"0"})]}),(0,i.jsxs)("div",{className:"col-span-1",children:[(0,i.jsx)(o._,{children:"Total"}),(0,i.jsxs)("div",{className:"px-3 py-2 bg-gray-50 rounded-md text-sm",children:["$",(()=>{let e=V[t];if(!e)return"0.00";let s=(e.quantity||0)*(e.unitPrice||0),i=s*(e.discount||0)/100,a=s-i,n=a*(e.taxRate||0)/100;return(a+n).toFixed(2)})()]})]}),(0,i.jsx)("div",{className:"col-span-1",children:(0,i.jsx)(d.z,{type:"button",variant:"destructive",size:"sm",onClick:()=>$(t),disabled:1===q.length,children:(0,i.jsx)(x.Z,{className:"h-4 w-4"})})})]},e.id)})})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("h3",{className:"text-lg font-medium",children:"Settings"}),(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(o._,{htmlFor:"discountType",children:"Discount Type"}),(0,i.jsxs)("select",{id:"discountType",...w("discountType"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,i.jsx)("option",{value:"PERCENTAGE",children:"Percentage"}),(0,i.jsx)("option",{value:"FIXED",children:"Fixed Amount"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsxs)(o._,{htmlFor:"discountValue",children:["Discount ","PERCENTAGE"===k?"%":"$"]}),(0,i.jsx)(c.I,{type:"number",min:"0",step:"0.01",...w("discountValue",{valueAsNumber:!0}),placeholder:"0"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(o._,{htmlFor:"taxRate",children:"Overall Tax Rate (%)"}),(0,i.jsx)(c.I,{type:"number",min:"0",max:"100",step:"0.01",...w("taxRate",{valueAsNumber:!0}),placeholder:"0"})]})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("h3",{className:"text-lg font-medium flex items-center",children:[(0,i.jsx)(p.Z,{className:"h-5 w-5 mr-2"}),"Totals"]}),(0,i.jsxs)("div",{className:"space-y-2 p-4 bg-gray-50 rounded-lg",children:[(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{children:"Subtotal:"}),(0,i.jsxs)("span",{children:["$",Q.subtotal.toFixed(2)]})]}),Q.discountAmount>0&&(0,i.jsxs)("div",{className:"flex justify-between text-red-600",children:[(0,i.jsx)("span",{children:"Discount:"}),(0,i.jsxs)("span",{children:["-$",Q.discountAmount.toFixed(2)]})]}),Q.taxAmount>0&&(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{children:"Tax:"}),(0,i.jsxs)("span",{children:["$",Q.taxAmount.toFixed(2)]})]}),(0,i.jsxs)("div",{className:"flex justify-between font-bold text-lg border-t pt-2",children:[(0,i.jsx)("span",{children:"Total:"}),(0,i.jsxs)("span",{children:["$",Q.total.toFixed(2)]})]})]})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(o._,{htmlFor:"paymentTerms",children:"Payment Terms"}),(0,i.jsx)("textarea",{id:"paymentTerms",...w("paymentTerms"),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Net 30, 2/10 Net 30, Payment due upon receipt, etc..."})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(o._,{htmlFor:"paymentDueDays",children:"Payment Due (Days)"}),(0,i.jsx)(c.I,{id:"paymentDueDays",type:"number",min:"0",...w("paymentDueDays",{valueAsNumber:!0}),placeholder:"30"}),(0,i.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Number of days after invoice date"})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(o._,{htmlFor:"terms",children:"Terms & Conditions"}),(0,i.jsx)("textarea",{id:"terms",...w("terms"),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"General terms, delivery conditions, warranties, etc..."})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(o._,{htmlFor:"notes",children:"Internal Notes"}),(0,i.jsx)("textarea",{id:"notes",...w("notes"),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Internal notes (not visible to customer)..."})]})]}),(0,i.jsxs)(u.cN,{children:[(0,i.jsx)(d.z,{type:"button",variant:"outline",onClick:O,children:"Cancel"}),(0,i.jsx)(d.z,{type:"submit",disabled:g,children:g?"Saving...":"create"===v?"Create Quotation":"Update Quotation"})]})]})]})})}}}]);