{"version": 3, "file": "GlobalFiltering.js", "sources": ["../../../src/features/GlobalFiltering.ts"], "sourcesContent": ["import { FilterFn, FilterFnOption } from '..'\nimport { BuiltInFilterFn, filterFns } from '../filterFns'\nimport {\n  Column,\n  OnChangeFn,\n  Table,\n  Updater,\n  RowData,\n  TableFeature,\n} from '../types'\nimport { isFunction, makeStateUpdater } from '../utils'\n\nexport interface GlobalFilterTableState {\n  globalFilter: any\n}\n\nexport interface GlobalFilterColumnDef {\n  /**\n   * Enables/disables the **global** filter for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#enableglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  enableGlobalFilter?: boolean\n}\n\nexport interface GlobalFilterColumn {\n  /**\n   * Returns whether or not the column can be **globally** filtered. Set to `false` to disable a column from being scanned during global filtering.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#getcanglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  getCanGlobalFilter: () => boolean\n}\n\nexport interface GlobalFilterOptions<TData extends RowData> {\n  /**\n   * Enables/disables **global** filtering for all columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#enableglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  enableGlobalFilter?: boolean\n  /**\n   * If provided, this function will be called with the column and should return `true` or `false` to indicate whether this column should be used for global filtering.\n   *\n   * This is useful if the column can contain data that is not `string` or `number` (i.e. `undefined`).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#getcolumncanglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  getColumnCanGlobalFilter?: (column: Column<TData, unknown>) => boolean\n  /**\n   * The filter function to use for global filtering.\n   * - A `string` referencing a built-in filter function\n   * - A `string` that references a custom filter functions provided via the `tableOptions.filterFns` option\n   * - A custom filter function\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#globalfilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  globalFilterFn?: FilterFnOption<TData>\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.globalFilter` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#onglobalfilterchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  onGlobalFilterChange?: OnChangeFn<any>\n}\n\nexport interface GlobalFilterInstance<TData extends RowData> {\n  /**\n   * Currently, this function returns the built-in `includesString` filter function. In future releases, it may return more dynamic filter functions based on the nature of the data provided.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#getglobalautofilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  getGlobalAutoFilterFn: () => FilterFn<TData> | undefined\n  /**\n   * Returns the filter function (either user-defined or automatic, depending on configuration) for the global filter.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#getglobalfilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  getGlobalFilterFn: () => FilterFn<TData> | undefined\n  /**\n   * Resets the **globalFilter** state to `initialState.globalFilter`, or `true` can be passed to force a default blank state reset to `undefined`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#resetglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  resetGlobalFilter: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.globalFilter` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#setglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  setGlobalFilter: (updater: Updater<any>) => void\n}\n\n//\n\nexport const GlobalFiltering: TableFeature = {\n  getInitialState: (state): GlobalFilterTableState => {\n    return {\n      globalFilter: undefined,\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): GlobalFilterOptions<TData> => {\n    return {\n      onGlobalFilterChange: makeStateUpdater('globalFilter', table),\n      globalFilterFn: 'auto',\n      getColumnCanGlobalFilter: column => {\n        const value = table\n          .getCoreRowModel()\n          .flatRows[0]?._getAllCellsByColumnId()\n          [column.id]?.getValue()\n\n        return typeof value === 'string' || typeof value === 'number'\n      },\n    } as GlobalFilterOptions<TData>\n  },\n\n  createColumn: <TData extends RowData>(\n    column: Column<TData, unknown>,\n    table: Table<TData>\n  ): void => {\n    column.getCanGlobalFilter = () => {\n      return (\n        (column.columnDef.enableGlobalFilter ?? true) &&\n        (table.options.enableGlobalFilter ?? true) &&\n        (table.options.enableFilters ?? true) &&\n        (table.options.getColumnCanGlobalFilter?.(column) ?? true) &&\n        !!column.accessorFn\n      )\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.getGlobalAutoFilterFn = () => {\n      return filterFns.includesString\n    }\n\n    table.getGlobalFilterFn = () => {\n      const { globalFilterFn: globalFilterFn } = table.options\n\n      return isFunction(globalFilterFn)\n        ? globalFilterFn\n        : globalFilterFn === 'auto'\n          ? table.getGlobalAutoFilterFn()\n          : table.options.filterFns?.[globalFilterFn as string] ??\n            filterFns[globalFilterFn as BuiltInFilterFn]\n    }\n\n    table.setGlobalFilter = updater => {\n      table.options.onGlobalFilterChange?.(updater)\n    }\n\n    table.resetGlobalFilter = defaultState => {\n      table.setGlobalFilter(\n        defaultState ? undefined : table.initialState.globalFilter\n      )\n    }\n  },\n}\n"], "names": ["GlobalFiltering", "getInitialState", "state", "globalFilter", "undefined", "getDefaultOptions", "table", "onGlobalFilterChange", "makeStateUpdater", "globalFilterFn", "getColumnCanGlobalFilter", "column", "_table$getCoreRowMode", "value", "getCoreRowModel", "flatRows", "_getAllCellsByColumnId", "id", "getValue", "createColumn", "getCanGlobalFilter", "_column$columnDef$ena", "_table$options$enable", "_table$options$enable2", "_table$options$getCol", "columnDef", "enableGlobalFilter", "options", "enableFilters", "accessorFn", "createTable", "getGlobalAutoFilterFn", "filterFns", "includesString", "getGlobalFilterFn", "_table$options$filter", "_table$options$filter2", "isFunction", "setGlobalFilter", "updater", "resetGlobalFilter", "defaultState", "initialState"], "mappings": ";;;;;;;;;;;;;;;AA6FA;;AAEO,MAAMA,eAA6B,GAAG;EAC3CC,eAAe,EAAGC,KAAK,IAA6B;IAClD,OAAO;AACLC,MAAAA,YAAY,EAAEC,SAAS;MACvB,GAAGF,KAAAA;KACJ,CAAA;GACF;EAEDG,iBAAiB,EACfC,KAAmB,IACY;IAC/B,OAAO;AACLC,MAAAA,oBAAoB,EAAEC,sBAAgB,CAAC,cAAc,EAAEF,KAAK,CAAC;AAC7DG,MAAAA,cAAc,EAAE,MAAM;MACtBC,wBAAwB,EAAEC,MAAM,IAAI;AAAA,QAAA,IAAAC,qBAAA,CAAA;AAClC,QAAA,MAAMC,KAAK,GAAA,CAAAD,qBAAA,GAAGN,KAAK,CAChBQ,eAAe,EAAE,CACjBC,QAAQ,CAAC,CAAC,CAAC,KAAAH,IAAAA,IAAAA,CAAAA,qBAAA,GAFAA,qBAAA,CAEEI,sBAAsB,EAAE,CACrCL,MAAM,CAACM,EAAE,CAAC,KAHCL,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,qBAAA,CAGCM,QAAQ,EAAE,CAAA;QAEzB,OAAO,OAAOL,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,CAAA;AAC/D,OAAA;KACD,CAAA;GACF;AAEDM,EAAAA,YAAY,EAAEA,CACZR,MAA8B,EAC9BL,KAAmB,KACV;IACTK,MAAM,CAACS,kBAAkB,GAAG,MAAM;AAAA,MAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,CAAA;AAChC,MAAA,OACE,CAAAH,CAAAA,qBAAA,GAACV,MAAM,CAACc,SAAS,CAACC,kBAAkB,KAAAL,IAAAA,GAAAA,qBAAA,GAAI,IAAI,OAAAC,qBAAA,GAC3ChB,KAAK,CAACqB,OAAO,CAACD,kBAAkB,KAAA,IAAA,GAAAJ,qBAAA,GAAI,IAAI,CAAC,KAAAC,CAAAA,sBAAA,GACzCjB,KAAK,CAACqB,OAAO,CAACC,aAAa,KAAA,IAAA,GAAAL,sBAAA,GAAI,IAAI,CAAC,KAAAC,CAAAA,qBAAA,GACpClB,KAAK,CAACqB,OAAO,CAACjB,wBAAwB,oBAAtCJ,KAAK,CAACqB,OAAO,CAACjB,wBAAwB,CAAGC,MAAM,CAAC,YAAAa,qBAAA,GAAI,IAAI,CAAC,IAC1D,CAAC,CAACb,MAAM,CAACkB,UAAU,CAAA;KAEtB,CAAA;GACF;EAEDC,WAAW,EAA0BxB,KAAmB,IAAW;IACjEA,KAAK,CAACyB,qBAAqB,GAAG,MAAM;MAClC,OAAOC,mBAAS,CAACC,cAAc,CAAA;KAChC,CAAA;IAED3B,KAAK,CAAC4B,iBAAiB,GAAG,MAAM;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,CAAA;MAC9B,MAAM;AAAE3B,QAAAA,cAAc,EAAEA,cAAAA;OAAgB,GAAGH,KAAK,CAACqB,OAAO,CAAA;AAExD,MAAA,OAAOU,gBAAU,CAAC5B,cAAc,CAAC,GAC7BA,cAAc,GACdA,cAAc,KAAK,MAAM,GACvBH,KAAK,CAACyB,qBAAqB,EAAE,GAAAI,CAAAA,qBAAA,GAAAC,CAAAA,sBAAA,GAC7B9B,KAAK,CAACqB,OAAO,CAACK,SAAS,KAAvBI,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,sBAAA,CAA0B3B,cAAc,CAAW,KAAA0B,IAAAA,GAAAA,qBAAA,GACnDH,mBAAS,CAACvB,cAAc,CAAoB,CAAA;KACnD,CAAA;AAEDH,IAAAA,KAAK,CAACgC,eAAe,GAAGC,OAAO,IAAI;AACjCjC,MAAAA,KAAK,CAACqB,OAAO,CAACpB,oBAAoB,IAAlCD,IAAAA,IAAAA,KAAK,CAACqB,OAAO,CAACpB,oBAAoB,CAAGgC,OAAO,CAAC,CAAA;KAC9C,CAAA;AAEDjC,IAAAA,KAAK,CAACkC,iBAAiB,GAAGC,YAAY,IAAI;AACxCnC,MAAAA,KAAK,CAACgC,eAAe,CACnBG,YAAY,GAAGrC,SAAS,GAAGE,KAAK,CAACoC,YAAY,CAACvC,YAChD,CAAC,CAAA;KACF,CAAA;AACH,GAAA;AACF;;;;"}