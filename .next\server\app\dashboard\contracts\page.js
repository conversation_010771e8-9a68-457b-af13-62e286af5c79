(()=>{var e={};e.id=7316,e.ids=[7316],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},82573:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>o,pages:()=>x,routeModule:()=>h,tree:()=>d});var a=t(50482),r=t(69108),l=t(62563),n=t.n(l),c=t(68300),i={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>c[e]);t.d(s,i);let d=["",{children:["dashboard",{children:["contracts",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,67200)),"C:\\proj\\nextjs-saas\\app\\dashboard\\contracts\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,96339)),"C:\\proj\\nextjs-saas\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,59504)),"C:\\proj\\nextjs-saas\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],x=["C:\\proj\\nextjs-saas\\app\\dashboard\\contracts\\page.tsx"],o="/dashboard/contracts/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/contracts/page",pathname:"/dashboard/contracts",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},83866:(e,s,t)=>{Promise.resolve().then(t.bind(t,34233))},34233:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>z});var a=t(95344),r=t(3729),l=t(47674),n=t(61351),c=t(16212),i=t(69436),d=t(10763),x=t(32845),o=t(17470),m=t(50340),h=t(33733),u=t(37121),j=t(7060),g=t(48411),p=t(98989),N=t(17910);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let y=(0,t(69224).Z)("Timer",[["line",{x1:"10",x2:"14",y1:"2",y2:"2",key:"14vaq8"}],["line",{x1:"12",x2:"15",y1:"14",y2:"11",key:"17fdiu"}],["circle",{cx:"12",cy:"14",r:"8",key:"1e1u0o"}]]);var f=t(25545),v=t(66827),b=t(55794),w=t(89895),C=t(45961),E=t(44669);function S(){let[e,s]=(0,r.useState)(null),[t,l]=(0,r.useState)(!0),[d,x]=(0,r.useState)("30"),S=async()=>{try{l(!0);let e=await fetch(`/api/contracts/analytics?period=${d}`);if(!e.ok)throw Error("Failed to fetch analytics");let t=await e.json();s(t)}catch(e){E.toast.error("Failed to load contract analytics"),console.error("Error fetching analytics:",e)}finally{l(!1)}};(0,r.useEffect)(()=>{S()},[d]);let Z=e=>{switch(e){case"DRAFT":default:return"bg-gray-100 text-gray-800";case"REVIEW":return"bg-yellow-100 text-yellow-800";case"SENT":return"bg-blue-100 text-blue-800";case"SIGNED":return"bg-green-100 text-green-800";case"ACTIVE":return"bg-emerald-100 text-emerald-800";case"COMPLETED":return"bg-purple-100 text-purple-800";case"CANCELLED":return"bg-red-100 text-red-800";case"EXPIRED":return"bg-orange-100 text-orange-800"}},D=e=>{switch(e){case"SERVICE":return"bg-blue-100 text-blue-800";case"PRODUCT":return"bg-green-100 text-green-800";case"SUBSCRIPTION":return"bg-purple-100 text-purple-800";case"MAINTENANCE":return"bg-orange-100 text-orange-800";case"CONSULTING":return"bg-indigo-100 text-indigo-800";default:return"bg-gray-100 text-gray-800"}},R=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e);return t?a.jsx("div",{className:"flex items-center justify-center py-8",children:a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):e?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("h3",{className:"text-lg font-semibold",children:"Contract Analytics"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(o.Ph,{value:d,onValueChange:x,children:[a.jsx(o.i4,{className:"w-32",children:a.jsx(o.ki,{})}),(0,a.jsxs)(o.Bw,{children:[a.jsx(o.Ql,{value:"7",children:"Last 7 days"}),a.jsx(o.Ql,{value:"30",children:"Last 30 days"}),a.jsx(o.Ql,{value:"90",children:"Last 90 days"}),a.jsx(o.Ql,{value:"365",children:"Last year"})]})]}),(0,a.jsxs)(c.z,{variant:"outline",onClick:S,size:"sm",children:[a.jsx(h.Z,{className:"h-4 w-4 mr-2"}),"Refresh"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-blue-100 rounded-full",children:a.jsx(u.Z,{className:"h-6 w-6 text-blue-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Total Contracts"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.totalContracts})]})]})})}),a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-green-100 rounded-full",children:a.jsx(j.Z,{className:"h-6 w-6 text-green-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Active Contracts"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.activeContracts})]})]})})}),a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-purple-100 rounded-full",children:a.jsx(g.Z,{className:"h-6 w-6 text-purple-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Total Value"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:R(e.summary.totalValue)})]})]})})}),a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-orange-100 rounded-full",children:a.jsx(p.Z,{className:"h-6 w-6 text-orange-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Pending Signatures"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.pendingSignatures})]})]})})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-indigo-100 rounded-full",children:a.jsx(N.Z,{className:"h-6 w-6 text-indigo-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Signature Rate"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[e.summary.signatureRate.toFixed(1),"%"]})]})]})})}),a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-pink-100 rounded-full",children:a.jsx(y,{className:"h-6 w-6 text-pink-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Avg Time to Sign"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[e.summary.avgSignatureDays.toFixed(1),"d"]})]})]})})}),a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-teal-100 rounded-full",children:a.jsx(f.Z,{className:"h-6 w-6 text-teal-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Avg Duration"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[e.summary.avgDurationDays,"d"]})]})]})})}),a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-emerald-100 rounded-full",children:a.jsx(v.Z,{className:"h-6 w-6 text-emerald-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Auto-Renewal"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.autoRenewalContracts})]})]})})})]}),(0,a.jsxs)(n.Zb,{children:[a.jsx(n.Ol,{children:(0,a.jsxs)(n.ll,{className:"flex items-center",children:[a.jsx(g.Z,{className:"h-5 w-5 mr-2"}),"Contract Value Overview"]})}),a.jsx(n.aY,{children:(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("p",{className:"text-2xl font-bold text-green-600",children:R(e.summary.activeValue)}),a.jsx("p",{className:"text-sm text-gray-500",children:"Active Contract Value"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("p",{className:"text-2xl font-bold text-blue-600",children:R(e.summary.averageValue)}),a.jsx("p",{className:"text-sm text-gray-500",children:"Average Contract Value"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("p",{className:"text-2xl font-bold text-purple-600",children:R(e.renewalAnalysis.autoRenewalValue)}),a.jsx("p",{className:"text-sm text-gray-500",children:"Auto-Renewal Value"})]})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)(n.Zb,{children:[a.jsx(n.Ol,{children:a.jsx(n.ll,{children:"Contracts by Status"})}),a.jsx(n.aY,{children:a.jsx("div",{className:"space-y-3",children:e.contractsByStatus.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("div",{className:"flex items-center space-x-2",children:a.jsx(i.C,{className:Z(e.status),children:e.status})}),(0,a.jsxs)("div",{className:"text-right",children:[a.jsx("span",{className:"font-semibold",children:e.count}),a.jsx("p",{className:"text-sm text-gray-500",children:R(e.value)})]})]},e.status))})})]}),(0,a.jsxs)(n.Zb,{children:[a.jsx(n.Ol,{children:a.jsx(n.ll,{children:"Contracts by Type"})}),a.jsx(n.aY,{children:a.jsx("div",{className:"space-y-3",children:e.contractsByType.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("div",{className:"flex items-center space-x-2",children:a.jsx(i.C,{className:D(e.type),children:e.type})}),(0,a.jsxs)("div",{className:"text-right",children:[a.jsx("span",{className:"font-semibold",children:e.count}),a.jsx("p",{className:"text-sm text-gray-500",children:R(e.value)})]})]},e.type))})})]})]}),(0,a.jsxs)(n.Zb,{children:[a.jsx(n.Ol,{children:(0,a.jsxs)(n.ll,{className:"flex items-center",children:[a.jsx(b.Z,{className:"h-5 w-5 mr-2"}),"Renewal & Expiration Analysis"]})}),a.jsx(n.aY,{children:(0,a.jsxs)("div",{className:"grid grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("p",{className:"text-2xl font-bold text-orange-600",children:e.renewalAnalysis.expiring30Days}),a.jsx("p",{className:"text-sm text-gray-500",children:"Expiring in 30 Days"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("p",{className:"text-2xl font-bold text-yellow-600",children:e.renewalAnalysis.expiring90Days}),a.jsx("p",{className:"text-sm text-gray-500",children:"Expiring in 90 Days"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("p",{className:"text-2xl font-bold text-green-600",children:e.summary.autoRenewalContracts}),a.jsx("p",{className:"text-sm text-gray-500",children:"Auto-Renewal Contracts"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("p",{className:"text-2xl font-bold text-blue-600",children:e.renewalAnalysis.avgDurationDays}),a.jsx("p",{className:"text-sm text-gray-500",children:"Avg Duration (Days)"})]})]})})]}),(0,a.jsxs)(n.Zb,{children:[a.jsx(n.Ol,{children:(0,a.jsxs)(n.ll,{className:"flex items-center",children:[a.jsx(w.Z,{className:"h-5 w-5 mr-2"}),"Top Customers by Contract Value"]})}),a.jsx(n.aY,{children:a.jsx("div",{className:"space-y-3",children:e.customerContracts.slice(0,5).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center",children:(0,a.jsxs)("span",{className:"text-xs font-semibold text-blue-600",children:["#",s+1]})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium",children:e.customer.name}),a.jsx("p",{className:"text-sm text-gray-500",children:e.customer.company})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[a.jsx("p",{className:"font-semibold text-green-600",children:R(e.totalValue)}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[e.contractCount," contracts"]})]})]},e.customer.id))})})]}),e.expiringContracts.length>0&&(0,a.jsxs)(n.Zb,{children:[a.jsx(n.Ol,{children:(0,a.jsxs)(n.ll,{className:"flex items-center",children:[a.jsx(C.Z,{className:"h-5 w-5 mr-2 text-orange-600"}),"Contracts Expiring Soon"]})}),a.jsx(n.aY,{children:a.jsx("div",{className:"space-y-3",children:e.expiringContracts.slice(0,5).map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-orange-50 rounded-lg border border-orange-200",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium",children:e.contractNumber}),a.jsx("p",{className:"text-sm text-gray-600",children:e.title}),a.jsx("p",{className:"text-sm text-gray-500",children:e.customer.name}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,a.jsxs)("p",{className:"text-sm text-orange-600",children:["Expires in ",e.daysUntilExpiry," days"]}),e.autoRenewal&&a.jsx(i.C,{variant:"outline",className:"text-xs",children:"Auto-Renewal"})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[a.jsx("p",{className:"font-semibold text-green-600",children:R(e.value)}),a.jsx("p",{className:"text-sm text-gray-500",children:new Date(e.endDate).toLocaleDateString()})]})]},e.id))})})]}),(0,a.jsxs)(n.Zb,{children:[a.jsx(n.Ol,{children:(0,a.jsxs)(n.ll,{className:"flex items-center",children:[a.jsx(b.Z,{className:"h-5 w-5 mr-2"}),"Recent Contracts (Last 7 Days)"]})}),a.jsx(n.aY,{children:a.jsx("div",{className:"space-y-3",children:0===e.recentContracts.length?a.jsx("p",{className:"text-gray-500 text-center py-4",children:"No recent contracts"}):e.recentContracts.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium",children:e.contractNumber}),a.jsx("p",{className:"text-sm text-gray-600",children:e.title}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[e.customer.name," • ",new Date(e.createdAt).toLocaleDateString()]})]}),(0,a.jsxs)("div",{className:"text-right",children:[a.jsx("p",{className:"font-semibold text-green-600",children:R(e.value)}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(i.C,{className:Z(e.status),variant:"outline",children:e.status}),a.jsx(i.C,{className:D(e.type),variant:"outline",children:e.type})]})]})]},e.id))})})]})]}):(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[a.jsx(m.Z,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),a.jsx("p",{children:"Failed to load analytics data"})]})}var Z=t(92549),D=t(1586),R=t(93601),T=t(16802),A=t(57341),k=t(23485),I=t(18822),O=t(38271);function P({open:e,contract:s,onClose:t,onSuccess:l}){let[n,d]=(0,r.useState)([]),[x,m]=(0,r.useState)(!1),[h,u]=(0,r.useState)(!1),[j,g]=(0,r.useState)({signerName:"",signerEmail:"",signerRole:"",signatureType:"ELECTRONIC",notes:""}),N=async()=>{if(s)try{m(!0);let e=await fetch(`/api/contracts/${s.id}/signatures`);if(!e.ok)throw Error("Failed to fetch signatures");let t=await e.json();d(t.signatures)}catch(e){E.toast.error("Failed to load signatures"),console.error("Error fetching signatures:",e)}finally{m(!1)}};(0,r.useEffect)(()=>{e&&s&&(N(),g(e=>({...e,signerName:s.customer.name,signerEmail:"",signerRole:"Client"})))},[e,s]);let y=(e,s)=>{g(t=>({...t,[e]:s}))},f=async e=>{if(e.preventDefault(),s){if(!j.signerName||!j.signerEmail){E.toast.error("Signer name and email are required");return}if(n.find(e=>e.signerEmail.toLowerCase()===j.signerEmail.toLowerCase())){E.toast.error("This email has already signed the contract");return}u(!0);try{let e=await fetch(`/api/contracts/${s.id}/signatures`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({signerName:j.signerName,signerEmail:j.signerEmail,signerRole:j.signerRole||void 0,signatureType:j.signatureType,notes:j.notes||void 0})});if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to record signature")}E.toast.success("Signature recorded successfully!"),g({signerName:"",signerEmail:"",signerRole:"",signatureType:"ELECTRONIC",notes:""}),await N(),l()}catch(e){E.toast.error(e instanceof Error?e.message:"Failed to record signature")}finally{u(!1)}}},v=async e=>{if(s&&confirm("Are you sure you want to delete this signature?"))try{let t=await fetch(`/api/contracts/${s.id}/signatures?signatureId=${e}`,{method:"DELETE"});if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to delete signature")}E.toast.success("Signature deleted successfully!"),await N(),l()}catch(e){E.toast.error(e instanceof Error?e.message:"Failed to delete signature")}},w=()=>{g({signerName:"",signerEmail:"",signerRole:"",signatureType:"ELECTRONIC",notes:""}),d([]),t()};if(!s)return null;let C=["SENT","REVIEW"].includes(s.status);return a.jsx(T.Vq,{open:e,onOpenChange:w,children:(0,a.jsxs)(T.cZ,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[a.jsx(T.fK,{children:(0,a.jsxs)(T.$N,{className:"flex items-center",children:[a.jsx(p.Z,{className:"h-5 w-5 mr-2"}),"Signature Management"]})}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(p.Z,{className:"h-5 w-5 text-blue-600"}),a.jsx("span",{className:"font-semibold",children:s.contractNumber}),a.jsx(i.C,{className:(e=>{switch(e){case"DRAFT":default:return"bg-gray-100 text-gray-800";case"REVIEW":return"bg-yellow-100 text-yellow-800";case"SENT":return"bg-blue-100 text-blue-800";case"SIGNED":return"bg-green-100 text-green-800";case"ACTIVE":return"bg-emerald-100 text-emerald-800";case"COMPLETED":return"bg-purple-100 text-purple-800";case"CANCELLED":return"bg-red-100 text-red-800";case"EXPIRED":return"bg-orange-100 text-orange-800"}})(s.status),children:s.status})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(k.Z,{className:"h-4 w-4 text-gray-500"}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["Signature ",s.signatureRequired?"Required":"Optional"]})]})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[a.jsx("p",{className:"font-medium",children:s.title}),(0,a.jsxs)("div",{className:"flex items-center mt-1",children:[a.jsx(I.Z,{className:"h-4 w-4 mr-1"}),a.jsx("span",{children:s.customer.name}),s.customer.company&&(0,a.jsxs)("span",{className:"ml-1",children:["(",s.customer.company,")"]})]})]})]}),C&&(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("h3",{className:"text-lg font-semibold",children:"Record New Signature"}),(0,a.jsxs)("form",{onSubmit:f,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx(D._,{htmlFor:"signerName",children:"Signer Name *"}),a.jsx(Z.I,{id:"signerName",value:j.signerName,onChange:e=>y("signerName",e.target.value),placeholder:"Full name of signer",required:!0})]}),(0,a.jsxs)("div",{children:[a.jsx(D._,{htmlFor:"signerEmail",children:"Signer Email *"}),a.jsx(Z.I,{id:"signerEmail",type:"email",value:j.signerEmail,onChange:e=>y("signerEmail",e.target.value),placeholder:"<EMAIL>",required:!0})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx(D._,{htmlFor:"signerRole",children:"Role (optional)"}),a.jsx(Z.I,{id:"signerRole",value:j.signerRole,onChange:e=>y("signerRole",e.target.value),placeholder:"e.g., CEO, Manager, Client"})]}),(0,a.jsxs)("div",{children:[a.jsx(D._,{htmlFor:"signatureType",children:"Signature Type"}),(0,a.jsxs)(o.Ph,{value:j.signatureType,onValueChange:e=>y("signatureType",e),children:[a.jsx(o.i4,{children:a.jsx(o.ki,{})}),(0,a.jsxs)(o.Bw,{children:[a.jsx(o.Ql,{value:"ELECTRONIC",children:"Electronic"}),a.jsx(o.Ql,{value:"DIGITAL",children:"Digital"}),a.jsx(o.Ql,{value:"WET_SIGNATURE",children:"Wet Signature"})]})]})]})]}),(0,a.jsxs)("div",{children:[a.jsx(D._,{htmlFor:"notes",children:"Notes (optional)"}),a.jsx(R.g,{id:"notes",value:j.notes,onChange:e=>y("notes",e.target.value),placeholder:"Additional notes about this signature...",rows:3})]}),a.jsx(c.z,{type:"submit",disabled:h,children:h?(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Recording..."]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(p.Z,{className:"h-4 w-4 mr-2"}),"Record Signature"]})})]})]}),!C&&a.jsx("div",{className:"p-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:a.jsx("p",{className:"text-yellow-800",children:'Signatures can only be added when the contract is in "Sent" or "Review" status.'})}),a.jsx(A.Z,{}),(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("h3",{className:"text-lg font-semibold",children:"Signature History"}),x?a.jsx("div",{className:"flex items-center justify-center py-4",children:a.jsx("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"})}):0===n.length?a.jsx("p",{className:"text-gray-500 text-center py-4",children:"No signatures recorded"}):a.jsx("div",{className:"space-y-3",children:n.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-green-50 rounded-lg border border-green-200",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[a.jsx(p.Z,{className:"h-4 w-4 text-green-600"}),a.jsx("span",{className:"font-semibold text-green-800",children:"Signed"}),a.jsx(i.C,{variant:"outline",children:e.signatureType})]}),(0,a.jsxs)("div",{className:"text-sm space-y-1",children:[(0,a.jsxs)("div",{children:[a.jsx("strong",{children:"Name:"})," ",e.signerName]}),(0,a.jsxs)("div",{children:[a.jsx("strong",{children:"Email:"})," ",e.signerEmail]}),e.signerRole&&(0,a.jsxs)("div",{children:[a.jsx("strong",{children:"Role:"})," ",e.signerRole]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[a.jsx(b.Z,{className:"h-3 w-3"}),(0,a.jsxs)("span",{children:[a.jsx("strong",{children:"Signed:"})," ",new Date(e.signedAt).toLocaleString()]})]}),e.notes&&(0,a.jsxs)("div",{children:[a.jsx("strong",{children:"Notes:"})," ",e.notes]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 mt-2",children:["IP: ",e.ipAddress||"Unknown"," • Recorded by ",e.signedBy.name||e.signedBy.email," on"," ",new Date(e.createdAt).toLocaleDateString()]})]})]}),a.jsx(c.z,{variant:"outline",size:"sm",onClick:()=>v(e.id),className:"text-red-600 hover:text-red-700 ml-4",disabled:"ACTIVE"===s.status,children:a.jsx(O.Z,{className:"h-4 w-4"})})]},e.id))})]}),a.jsx("div",{className:"flex justify-end pt-4 border-t",children:a.jsx(c.z,{variant:"outline",onClick:w,children:"Close"})})]})]})})}var V=t(62093),L=t(53148),F=t(75695),_=t(1960),Y=t(36135),U=t(96885),q=t(51838),$=t(20886),M=t(20783),G=t.n(M);function z(){let{data:e}=(0,l.useSession)(),[s,t]=(0,r.useState)([]),[o,h]=(0,r.useState)(!0),[u,N]=(0,r.useState)(!1),[y,v]=(0,r.useState)(null),[w,Z]=(0,r.useState)(!1),[D,R]=(0,r.useState)(!1),[T,A]=(0,r.useState)(null),[k,M]=(0,r.useState)({total:0,draft:0,active:0,signed:0,expired:0,totalValue:0}),z=async()=>{try{let e=await fetch("/api/contracts");if(!e.ok)throw Error("Failed to fetch contracts");let s=await e.json();t(s.contracts);let a=s.contracts.length,r=s.contracts.filter(e=>"DRAFT"===e.status).length,l=s.contracts.filter(e=>"ACTIVE"===e.status).length,n=s.contracts.filter(e=>"SIGNED"===e.status).length,c=s.contracts.filter(e=>"EXPIRED"===e.status).length,i=s.contracts.reduce((e,s)=>e+(s.value||0),0);M({total:a,draft:r,active:l,signed:n,expired:c,totalValue:i})}catch(e){E.toast.error("Failed to load contracts"),console.error("Error fetching contracts:",e)}finally{h(!1)}};(0,r.useEffect)(()=>{z()},[]);let X=async e=>{if(confirm(`Are you sure you want to delete contract "${e.contractNumber}"?`))try{let s=await fetch(`/api/contracts/${e.id}`,{method:"DELETE"});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to delete contract")}E.toast.success("Contract deleted successfully"),z()}catch(e){E.toast.error(e instanceof Error?e.message:"Failed to delete contract")}},K=e=>{v(e),N(!0)},W=async e=>{try{let s=await fetch(`/api/contracts/${e}/pdf`);if(!s.ok)throw Error("Failed to generate PDF");let t=await s.blob(),a=window.URL.createObjectURL(t),r=document.createElement("a");r.href=a,r.download=`contract-${e}.html`,document.body.appendChild(r),r.click(),window.URL.revokeObjectURL(a),document.body.removeChild(r),E.toast.success("PDF downloaded successfully")}catch(e){E.toast.error("Failed to download PDF"),console.error("Error downloading PDF:",e)}},B=e=>{A(e),R(!0)},H=e=>{if(!e)return a.jsx(i.C,{variant:"secondary",children:"Unknown"});switch(e){case"DRAFT":return a.jsx(i.C,{variant:"secondary",children:"Draft"});case"REVIEW":return a.jsx(i.C,{variant:"warning",children:"Review"});case"SENT":return a.jsx(i.C,{variant:"info",children:"Sent"});case"SIGNED":return a.jsx(i.C,{variant:"success",children:"Signed"});case"ACTIVE":return a.jsx(i.C,{variant:"success",children:"Active"});case"COMPLETED":return a.jsx(i.C,{variant:"success",children:"Completed"});case"CANCELLED":return a.jsx(i.C,{variant:"destructive",children:"Cancelled"});case"EXPIRED":return a.jsx(i.C,{variant:"destructive",children:"Expired"});default:return a.jsx(i.C,{variant:"secondary",children:e})}},Q=e=>{let s={SERVICE:"bg-blue-100 text-blue-800",PRODUCT:"bg-green-100 text-green-800",SUBSCRIPTION:"bg-purple-100 text-purple-800",MAINTENANCE:"bg-orange-100 text-orange-800",CONSULTING:"bg-indigo-100 text-indigo-800",OTHER:"bg-gray-100 text-gray-800"};return a.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${s[e]||s.OTHER}`,children:e})},J=e=>{switch(e){case"URGENT":return a.jsx(i.C,{variant:"destructive",children:"Urgent"});case"HIGH":return a.jsx(i.C,{variant:"warning",children:"High"});case"MEDIUM":return a.jsx(i.C,{variant:"info",children:"Medium"});case"LOW":return a.jsx(i.C,{variant:"secondary",children:"Low"});default:return a.jsx(i.C,{variant:"secondary",children:e})}};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Contracts"}),a.jsx("p",{className:"text-gray-600 mt-1",children:"Create and manage your contracts"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(c.z,{variant:"outline",onClick:()=>Z(!w),children:[a.jsx(m.Z,{className:"h-4 w-4 mr-2"}),"Analytics"]}),(0,a.jsxs)(c.z,{onClick:()=>N(!0),children:[a.jsx(q.Z,{className:"h-4 w-4 mr-2"}),"New Contract"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4",children:[(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(n.ll,{className:"text-sm font-medium",children:"Total Contracts"}),a.jsx(p.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(n.aY,{children:[a.jsx("div",{className:"text-2xl font-bold",children:k.total}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"All contracts"})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(n.ll,{className:"text-sm font-medium",children:"Draft"}),a.jsx(f.Z,{className:"h-4 w-4 text-gray-600"})]}),(0,a.jsxs)(n.aY,{children:[a.jsx("div",{className:"text-2xl font-bold",children:k.draft}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"Draft contracts"})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(n.ll,{className:"text-sm font-medium",children:"Active"}),a.jsx(j.Z,{className:"h-4 w-4 text-green-600"})]}),(0,a.jsxs)(n.aY,{children:[a.jsx("div",{className:"text-2xl font-bold",children:k.active}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"Active contracts"})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(n.ll,{className:"text-sm font-medium",children:"Signed"}),a.jsx(p.Z,{className:"h-4 w-4 text-blue-600"})]}),(0,a.jsxs)(n.aY,{children:[a.jsx("div",{className:"text-2xl font-bold",children:k.signed}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"Signed contracts"})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(n.ll,{className:"text-sm font-medium",children:"Expired"}),a.jsx(C.Z,{className:"h-4 w-4 text-red-600"})]}),(0,a.jsxs)(n.aY,{children:[a.jsx("div",{className:"text-2xl font-bold",children:k.expired}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"Expired contracts"})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(n.ll,{className:"text-sm font-medium",children:"Total Value"}),a.jsx(g.Z,{className:"h-4 w-4 text-green-600"})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:["$",k.totalValue.toLocaleString()]}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"Total contract value"})]})]})]}),w&&a.jsx(S,{}),(0,a.jsxs)(n.Zb,{children:[a.jsx(n.Ol,{children:a.jsx(n.ll,{children:"Contract Management"})}),a.jsx(n.aY,{children:o?a.jsx("div",{className:"flex items-center justify-center py-8",children:a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):a.jsx(d.w,{columns:[{accessorKey:"contractNumber",header:"Contract",cell:({row:e})=>{let s=e.original;return(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center",children:a.jsx(p.Z,{className:"h-4 w-4 text-purple-600"})}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium",children:s.contractNumber}),a.jsx("div",{className:"text-sm text-gray-500",children:s.title})]})]})}},{accessorKey:"customer",header:"Customer",cell:({row:e})=>{let s=e.original.customer;return(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium",children:s.name}),s.company&&a.jsx("div",{className:"text-sm text-gray-500",children:s.company})]})}},{accessorKey:"type",header:"Type",cell:({row:e})=>Q(e.getValue("type"))},{accessorKey:"status",header:"Status",cell:({row:e})=>H(e.getValue("status"))},{accessorKey:"priority",header:"Priority",cell:({row:e})=>J(e.getValue("priority"))},{accessorKey:"value",header:"Value",cell:({row:e})=>{let s=e.getValue("value"),t=e.original.currency;return s?(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(g.Z,{className:"h-3 w-3 text-green-600"}),(0,a.jsxs)("span",{className:"font-medium",children:[t," ",s.toLocaleString()]})]}):a.jsx("span",{className:"text-gray-400 text-sm",children:"-"})}},{accessorKey:"endDate",header:"End Date",cell:({row:e})=>{let s=e.getValue("endDate");if(!s)return a.jsx("span",{className:"text-gray-400 text-sm",children:"-"});let t=new Date(s),r=t<new Date(new Date().getTime()+2592e6);return(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(b.Z,{className:`h-3 w-3 ${r?"text-red-400":"text-gray-400"}`}),a.jsx("span",{className:`text-sm ${r?"text-red-600":""}`,children:t.toLocaleDateString()})]})}},{accessorKey:"assignedTo",header:"Assigned To",cell:({row:e})=>{let s=e.original.assignedTo;return s?(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(I.Z,{className:"h-3 w-3 text-gray-400"}),a.jsx("span",{className:"text-sm",children:s.name||"Unknown"})]}):a.jsx("span",{className:"text-gray-400 text-sm",children:"Unassigned"})}},{id:"actions",cell:({row:e})=>{let s=e.original;return(0,a.jsxs)($.h_,{children:[a.jsx($.$F,{asChild:!0,children:a.jsx(c.z,{variant:"ghost",className:"h-8 w-8 p-0",children:a.jsx(V.Z,{className:"h-4 w-4"})})}),(0,a.jsxs)($.AW,{align:"end",children:[a.jsx($.Ju,{children:"Actions"}),a.jsx($.Xi,{asChild:!0,children:(0,a.jsxs)(G(),{href:`/dashboard/contracts/${s.id}`,children:[a.jsx(L.Z,{className:"mr-2 h-4 w-4"}),"View Details"]})}),(0,a.jsxs)($.Xi,{onClick:()=>K(s),children:[a.jsx(F.Z,{className:"mr-2 h-4 w-4"}),"Edit"]}),(0,a.jsxs)($.Xi,{children:[a.jsx(_.Z,{className:"mr-2 h-4 w-4"}),"Duplicate"]}),a.jsx($.VD,{}),(0,a.jsxs)($.Xi,{children:[a.jsx(Y.Z,{className:"mr-2 h-4 w-4"}),"Send for Signature"]}),(0,a.jsxs)($.Xi,{onClick:()=>W(s.id),children:[a.jsx(U.Z,{className:"mr-2 h-4 w-4"}),"Download PDF"]}),(0,a.jsxs)($.Xi,{onClick:()=>B(s),children:[a.jsx(p.Z,{className:"mr-2 h-4 w-4"}),"Manage Signatures"]}),a.jsx($.VD,{}),(0,a.jsxs)($.Xi,{onClick:()=>X(s),className:"text-red-600",disabled:"SIGNED"===s.status||"ACTIVE"===s.status||s._count.signatures>0,children:[a.jsx(O.Z,{className:"mr-2 h-4 w-4"}),"Delete"]})]})]})}}],data:s,searchPlaceholder:"Search contracts..."})})]}),a.jsx(x.R,{isOpen:u,onClose:()=>{N(!1),v(null)},onSuccess:z,contract:y,mode:y?"edit":"create"}),a.jsx(P,{open:D,contract:T,onClose:()=>{R(!1),A(null)},onSuccess:()=>{R(!1),A(null),z()}})]})}},57341:(e,s,t)=>{"use strict";t.d(s,{Z:()=>x});var a=t(95344),r=t(3729),l=t(62409),n="horizontal",c=["horizontal","vertical"],i=r.forwardRef((e,s)=>{let{decorative:t,orientation:r=n,...i}=e,d=c.includes(r)?r:n;return(0,a.jsx)(l.WV.div,{"data-orientation":d,...t?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...i,ref:s})});i.displayName="Separator";var d=t(91626);let x=r.forwardRef(({className:e,orientation:s="horizontal",decorative:t=!0,...r},l)=>a.jsx(i,{ref:l,decorative:t,orientation:s,className:(0,d.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",e),...r}));x.displayName=i.displayName},93601:(e,s,t)=>{"use strict";t.d(s,{g:()=>n});var a=t(95344),r=t(3729),l=t(91626);let n=r.forwardRef(({className:e,...s},t)=>a.jsx("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:t,...s}));n.displayName="Textarea"},55794:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},25545:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},48411:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},67200:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>l,__esModule:()=>r,default:()=>n});let a=(0,t(86843).createProxy)(String.raw`C:\proj\nextjs-saas\app\dashboard\contracts\page.tsx`),{__esModule:r,$$typeof:l}=a,n=a.default}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[1638,7948,6671,4626,7792,2506,8830,7150,3117,2125,5045,5232,5374],()=>t(82573));module.exports=a})();