"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/super-admin/reports/route";
exports.ids = ["app/api/super-admin/reports/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Freports%2Froute&page=%2Fapi%2Fsuper-admin%2Freports%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Freports%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Freports%2Froute&page=%2Fapi%2Fsuper-admin%2Freports%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Freports%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_proj_nextjs_saas_app_api_super_admin_reports_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/super-admin/reports/route.ts */ \"(rsc)/./app/api/super-admin/reports/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/super-admin/reports/route\",\n        pathname: \"/api/super-admin/reports\",\n        filename: \"route\",\n        bundlePath: \"app/api/super-admin/reports/route\"\n    },\n    resolvedPagePath: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\api\\\\super-admin\\\\reports\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_proj_nextjs_saas_app_api_super_admin_reports_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/super-admin/reports/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Freports%2Froute&page=%2Fapi%2Fsuper-admin%2Freports%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Freports%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/super-admin/reports/route.ts":
/*!**********************************************!*\
  !*** ./app/api/super-admin/reports/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n// GET /api/super-admin/reports - Get comprehensive analytics and reports\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id || session?.user?.role !== \"SUPER_ADMIN\") {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Super admin access required\"\n            }, {\n                status: 403\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const period = searchParams.get(\"period\") || \"30\" // days\n        ;\n        const reportType = searchParams.get(\"type\") || \"overview\";\n        const periodDays = parseInt(period);\n        const startDate = new Date(Date.now() - periodDays * 24 * 60 * 60 * 1000);\n        // Base metrics\n        const [totalUsers, totalCompanies, totalRevenue, totalActivities, activeUsers, activeCompanies] = await Promise.all([\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.count(),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.company.count(),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.subscription.aggregate({\n                where: {\n                    status: \"ACTIVE\"\n                },\n                _sum: {\n                    amount: true\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.activity.count(),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.count({\n                where: {\n                    status: \"ACTIVE\"\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.company.count({\n                where: {\n                    status: \"ACTIVE\"\n                }\n            })\n        ]);\n        // Simple time series data - using basic counts to avoid complex groupBy issues\n        const timeSeriesData = await Promise.all([\n            // User growth - simple count approach\n            Promise.resolve([\n                {\n                    date: new Date().toISOString().split(\"T\")[0],\n                    count: await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.count().catch(()=>0)\n                }\n            ]),\n            // Company growth - simple count approach\n            Promise.resolve([\n                {\n                    date: new Date().toISOString().split(\"T\")[0],\n                    count: await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.company.count().catch(()=>0)\n                }\n            ]),\n            // Revenue - simple sum approach\n            Promise.resolve([\n                {\n                    date: new Date().toISOString().split(\"T\")[0],\n                    revenue: await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.subscription.aggregate({\n                        _sum: {\n                            amount: true\n                        },\n                        where: {\n                            status: \"ACTIVE\"\n                        }\n                    }).then((result)=>Number(result._sum.amount || 0)).catch(()=>0)\n                }\n            ]),\n            // Activity - simple count approach\n            Promise.resolve([\n                {\n                    date: new Date().toISOString().split(\"T\")[0],\n                    count: await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.activity.count().catch(()=>0)\n                }\n            ])\n        ]);\n        const [userGrowth, companyGrowth, revenueGrowth, activityGrowth] = timeSeriesData;\n        // Geographic distribution - with error handling\n        const geographicData = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.company.groupBy({\n            by: [\n                \"country\"\n            ],\n            _count: {\n                id: true\n            },\n            where: {\n                country: {\n                    not: null\n                }\n            },\n            orderBy: {\n                _count: {\n                    id: \"desc\"\n                }\n            },\n            take: 10\n        }).catch(()=>[]);\n        // Industry distribution - with error handling\n        const industryData = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.company.groupBy({\n            by: [\n                \"industry\"\n            ],\n            _count: {\n                id: true\n            },\n            where: {\n                industry: {\n                    not: null\n                }\n            },\n            orderBy: {\n                _count: {\n                    id: \"desc\"\n                }\n            },\n            take: 10\n        }).catch(()=>[]);\n        // User role distribution - with error handling\n        const roleData = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.groupBy({\n            by: [\n                \"role\"\n            ],\n            _count: {\n                id: true\n            },\n            orderBy: {\n                _count: {\n                    id: \"desc\"\n                }\n            }\n        }).catch(()=>[]);\n        // Subscription plan distribution - with error handling\n        const planData = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.subscription.groupBy({\n            by: [\n                \"plan\"\n            ],\n            _count: {\n                id: true\n            },\n            _sum: {\n                amount: true\n            },\n            orderBy: {\n                _count: {\n                    id: \"desc\"\n                }\n            }\n        }).catch(()=>[]);\n        // Top performing companies - simplified query with error handling\n        const topCompanies = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.company.findMany({\n            take: 10,\n            include: {\n                subscription: {\n                    select: {\n                        amount: true,\n                        status: true\n                    }\n                },\n                _count: {\n                    select: {\n                        users: true,\n                        customers: true,\n                        quotations: true,\n                        invoices: true,\n                        activities: true\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: \"desc\"\n            } // Simplified ordering\n        }).catch(()=>[]);\n        // Recent activities summary - with error handling\n        const recentActivities = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.activity.findMany({\n            take: 20,\n            where: {\n                createdAt: {\n                    gte: startDate\n                }\n            },\n            include: {\n                company: {\n                    select: {\n                        name: true\n                    }\n                },\n                createdBy: {\n                    select: {\n                        name: true,\n                        email: true\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: \"desc\"\n            }\n        }).catch(()=>[]);\n        // Conversion funnel data\n        const funnelData = await Promise.all([\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.company.count(),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.company.count({\n                where: {\n                    status: \"ACTIVE\"\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.subscription.count({\n                where: {\n                    status: \"ACTIVE\"\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.subscription.count({\n                where: {\n                    status: \"ACTIVE\",\n                    amount: {\n                        gt: 0\n                    }\n                }\n            }) // Revenue generating\n        ]);\n        // Performance metrics - simplified with mock data for missing tables\n        const performanceMetrics = {\n            userEngagement: {\n                dailyActiveUsers: await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.count({\n                    where: {\n                        lastActiveAt: {\n                            gte: new Date(Date.now() - 24 * 60 * 60 * 1000)\n                        }\n                    }\n                }).catch(()=>0),\n                weeklyActiveUsers: await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.count({\n                    where: {\n                        lastActiveAt: {\n                            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)\n                        }\n                    }\n                }).catch(()=>0),\n                monthlyActiveUsers: await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.count({\n                    where: {\n                        lastActiveAt: {\n                            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)\n                        }\n                    }\n                }).catch(()=>0)\n            },\n            systemHealth: {\n                errorRate: 0.5,\n                avgResponseTime: 150,\n                uptime: 99.9 // Mock data - would come from actual monitoring\n            }\n        };\n        // Calculate growth rates\n        const previousPeriodStart = new Date(startDate.getTime() - periodDays * 24 * 60 * 60 * 1000);\n        const previousPeriodUsers = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.count({\n            where: {\n                createdAt: {\n                    gte: previousPeriodStart,\n                    lt: startDate\n                }\n            }\n        });\n        const currentPeriodUsers = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.count({\n            where: {\n                createdAt: {\n                    gte: startDate\n                }\n            }\n        });\n        const userGrowthRate = previousPeriodUsers > 0 ? (currentPeriodUsers - previousPeriodUsers) / previousPeriodUsers * 100 : 0;\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            overview: {\n                totalUsers,\n                totalCompanies,\n                totalRevenue: Number(totalRevenue._sum.amount || 0),\n                totalActivities,\n                activeUsers,\n                activeCompanies,\n                userGrowthRate,\n                conversionRate: totalCompanies > 0 ? activeCompanies / totalCompanies * 100 : 0\n            },\n            timeSeries: {\n                userGrowth: Array.isArray(userGrowth) ? userGrowth.map((item)=>({\n                        date: item.date,\n                        value: Number(item.count)\n                    })) : [],\n                companyGrowth: Array.isArray(companyGrowth) ? companyGrowth.map((item)=>({\n                        date: item.date,\n                        value: Number(item.count)\n                    })) : [],\n                revenueGrowth: Array.isArray(revenueGrowth) ? revenueGrowth.map((item)=>({\n                        date: item.date,\n                        value: Number(item.revenue || 0)\n                    })) : [],\n                activityGrowth: Array.isArray(activityGrowth) ? activityGrowth.map((item)=>({\n                        date: item.date,\n                        value: Number(item.count)\n                    })) : []\n            },\n            distribution: {\n                geographic: Array.isArray(geographicData) ? geographicData.map((item)=>({\n                        country: item.country,\n                        count: item._count.id\n                    })) : [],\n                industry: Array.isArray(industryData) ? industryData.map((item)=>({\n                        industry: item.industry,\n                        count: item._count.id\n                    })) : [],\n                roles: Array.isArray(roleData) ? roleData.map((item)=>({\n                        role: item.role,\n                        count: item._count.id\n                    })) : [],\n                plans: Array.isArray(planData) ? planData.map((item)=>({\n                        plan: item.plan,\n                        count: item._count.id,\n                        revenue: Number(item._sum.amount || 0)\n                    })) : []\n            },\n            topCompanies: Array.isArray(topCompanies) ? topCompanies.map((company)=>({\n                    id: company.id,\n                    name: company.name,\n                    industry: company.industry,\n                    subscription: company.subscription,\n                    metrics: {\n                        users: company._count?.users || 0,\n                        customers: company._count?.customers || 0,\n                        quotations: company._count?.quotations || 0,\n                        invoices: company._count?.invoices || 0,\n                        activities: company._count?.activities || 0\n                    }\n                })) : [],\n            recentActivities: Array.isArray(recentActivities) ? recentActivities.map((activity)=>({\n                    id: activity.id,\n                    type: activity.type,\n                    title: activity.title,\n                    company: activity.company?.name,\n                    user: activity.createdBy?.name,\n                    createdAt: activity.createdAt\n                })) : [],\n            funnel: {\n                totalCompanies: funnelData[0],\n                activeCompanies: funnelData[1],\n                paidSubscriptions: funnelData[2],\n                revenueGenerating: funnelData[3]\n            },\n            performance: performanceMetrics,\n            period: periodDays,\n            generatedAt: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error(\"Error generating reports:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to generate reports\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/super-admin/reports/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/github */ \"(rsc)/./node_modules/next-auth/providers/github.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n\nconst authOptions = {\n    // Using JWT strategy instead of database adapter for now\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                try {\n                    if (!credentials?.email || !credentials?.password) {\n                        console.log(\"Missing credentials\");\n                        return null;\n                    }\n                    console.log(\"Attempting to authenticate user:\", credentials.email);\n                    const user = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.findUnique({\n                        where: {\n                            email: credentials.email\n                        },\n                        select: {\n                            id: true,\n                            email: true,\n                            name: true,\n                            password: true,\n                            role: true,\n                            companyId: true\n                        }\n                    });\n                    // Get the company ID - either as member or owner\n                    let finalCompanyId = user?.companyId;\n                    if (!finalCompanyId && user) {\n                        const ownedCompany = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.company.findFirst({\n                            where: {\n                                ownerId: user.id\n                            },\n                            select: {\n                                id: true\n                            }\n                        });\n                        finalCompanyId = ownedCompany?.id;\n                        // If user is company owner, update their companyId for future queries\n                        if (finalCompanyId) {\n                            await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                                where: {\n                                    id: user.id\n                                },\n                                data: {\n                                    companyId: finalCompanyId\n                                }\n                            });\n                        }\n                    }\n                    if (!user) {\n                        console.log(\"User not found:\", credentials.email);\n                        return null;\n                    }\n                    if (!user.password) {\n                        console.log(\"User has no password set:\", credentials.email);\n                        return null;\n                    }\n                    const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_3___default().compare(credentials.password, user.password);\n                    if (!isPasswordValid) {\n                        console.log(\"Invalid password for user:\", credentials.email);\n                        return null;\n                    }\n                    // Update last login\n                    await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                        where: {\n                            id: user.id\n                        },\n                        data: {\n                            lastLoginAt: new Date(),\n                            loginCount: {\n                                increment: 1\n                            }\n                        }\n                    });\n                    console.log(\"User authenticated successfully:\", user.email);\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name,\n                        role: user.role,\n                        companyId: finalCompanyId\n                    };\n                } catch (error) {\n                    console.error(\"Authentication error:\", error);\n                    return null;\n                }\n            }\n        }),\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            clientId: process.env.GITHUB_CLIENT_ID,\n            clientSecret: process.env.GITHUB_CLIENT_SECRET\n        })\n    ],\n    session: {\n        strategy: \"jwt\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                console.log(\"JWT callback - user data:\", {\n                    id: user.id,\n                    email: user.email,\n                    role: user.role,\n                    companyId: user.companyId\n                });\n                token.role = user.role;\n                token.companyId = user.companyId;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n                session.user.companyId = token.companyId;\n                console.log(\"Session callback - final session:\", {\n                    id: session.user.id,\n                    email: session.user.email,\n                    role: session.user.role,\n                    companyId: session.user.companyId\n                });\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        signUp: \"/auth/signup\",\n        error: \"/auth/error\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zYWFzLWFwcC8uL2xpYi9wcmlzbWEudHM/OTgyMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/lru-cache","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Freports%2Froute&page=%2Fapi%2Fsuper-admin%2Freports%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Freports%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();