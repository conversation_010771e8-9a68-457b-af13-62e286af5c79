"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/super-admin/reports/route";
exports.ids = ["app/api/super-admin/reports/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Freports%2Froute&page=%2Fapi%2Fsuper-admin%2Freports%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Freports%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Freports%2Froute&page=%2Fapi%2Fsuper-admin%2Freports%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Freports%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_proj_nextjs_saas_app_api_super_admin_reports_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/super-admin/reports/route.ts */ \"(rsc)/./app/api/super-admin/reports/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/super-admin/reports/route\",\n        pathname: \"/api/super-admin/reports\",\n        filename: \"route\",\n        bundlePath: \"app/api/super-admin/reports/route\"\n    },\n    resolvedPagePath: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\api\\\\super-admin\\\\reports\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_proj_nextjs_saas_app_api_super_admin_reports_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/super-admin/reports/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Freports%2Froute&page=%2Fapi%2Fsuper-admin%2Freports%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Freports%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/super-admin/reports/route.ts":
/*!**********************************************!*\
  !*** ./app/api/super-admin/reports/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n// GET /api/super-admin/reports - Get comprehensive analytics and reports\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id || session?.user?.role !== \"SUPER_ADMIN\") {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Super admin access required\"\n            }, {\n                status: 403\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const period = searchParams.get(\"period\") || \"30\" // days\n        ;\n        const reportType = searchParams.get(\"type\") || \"overview\";\n        const periodDays = parseInt(period);\n        const startDate = new Date(Date.now() - periodDays * 24 * 60 * 60 * 1000);\n        // Base metrics\n        const [totalUsers, totalCompanies, totalRevenue, totalActivities, activeUsers, activeCompanies] = await Promise.all([\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.count(),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.company.count(),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.subscription.aggregate({\n                where: {\n                    status: \"ACTIVE\"\n                },\n                _sum: {\n                    amount: true\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.activity.count(),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.count({\n                where: {\n                    status: \"ACTIVE\"\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.company.count({\n                where: {\n                    status: \"ACTIVE\"\n                }\n            })\n        ]);\n        // Time series data for charts\n        const timeSeriesData = await Promise.all([\n            // User growth over time\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.$queryRaw`\n        SELECT \n          DATE(created_at) as date,\n          COUNT(*) as count\n        FROM users \n        WHERE created_at >= ${startDate}\n        GROUP BY DATE(created_at)\n        ORDER BY date ASC\n      `,\n            // Company growth over time\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.$queryRaw`\n        SELECT \n          DATE(created_at) as date,\n          COUNT(*) as count\n        FROM companies \n        WHERE created_at >= ${startDate}\n        GROUP BY DATE(created_at)\n        ORDER BY date ASC\n      `,\n            // Revenue over time\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.$queryRaw`\n        SELECT \n          DATE(s.created_at) as date,\n          SUM(s.amount) as revenue\n        FROM subscriptions s\n        WHERE s.created_at >= ${startDate} AND s.status = 'ACTIVE'\n        GROUP BY DATE(s.created_at)\n        ORDER BY date ASC\n      `,\n            // Activity over time\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.$queryRaw`\n        SELECT \n          DATE(created_at) as date,\n          COUNT(*) as count\n        FROM activities \n        WHERE created_at >= ${startDate}\n        GROUP BY DATE(created_at)\n        ORDER BY date ASC\n      `\n        ]);\n        const [userGrowth, companyGrowth, revenueGrowth, activityGrowth] = timeSeriesData;\n        // Geographic distribution\n        const geographicData = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.company.groupBy({\n            by: [\n                \"country\"\n            ],\n            _count: {\n                id: true\n            },\n            where: {\n                country: {\n                    not: null\n                }\n            },\n            orderBy: {\n                _count: {\n                    id: \"desc\"\n                }\n            },\n            take: 10\n        });\n        // Industry distribution\n        const industryData = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.company.groupBy({\n            by: [\n                \"industry\"\n            ],\n            _count: {\n                id: true\n            },\n            where: {\n                industry: {\n                    not: null\n                }\n            },\n            orderBy: {\n                _count: {\n                    id: \"desc\"\n                }\n            },\n            take: 10\n        });\n        // User role distribution\n        const roleData = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.groupBy({\n            by: [\n                \"role\"\n            ],\n            _count: {\n                id: true\n            },\n            orderBy: {\n                _count: {\n                    id: \"desc\"\n                }\n            }\n        });\n        // Subscription plan distribution\n        const planData = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.subscription.groupBy({\n            by: [\n                \"plan\"\n            ],\n            _count: {\n                id: true\n            },\n            _sum: {\n                amount: true\n            },\n            orderBy: {\n                _count: {\n                    id: \"desc\"\n                }\n            }\n        });\n        // Top performing companies\n        const topCompanies = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.company.findMany({\n            take: 10,\n            include: {\n                subscription: {\n                    select: {\n                        amount: true,\n                        status: true\n                    }\n                },\n                _count: {\n                    select: {\n                        users: true,\n                        customers: true,\n                        quotations: true,\n                        invoices: true,\n                        activities: true\n                    }\n                }\n            },\n            orderBy: {\n                activities: {\n                    _count: \"desc\"\n                }\n            }\n        });\n        // Recent activities summary\n        const recentActivities = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.activity.findMany({\n            take: 20,\n            where: {\n                createdAt: {\n                    gte: startDate\n                }\n            },\n            include: {\n                company: {\n                    select: {\n                        name: true\n                    }\n                },\n                createdBy: {\n                    select: {\n                        name: true,\n                        email: true\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: \"desc\"\n            }\n        });\n        // Conversion funnel data\n        const funnelData = await Promise.all([\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.company.count(),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.company.count({\n                where: {\n                    status: \"ACTIVE\"\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.subscription.count({\n                where: {\n                    status: \"ACTIVE\"\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.subscription.count({\n                where: {\n                    status: \"ACTIVE\",\n                    amount: {\n                        gt: 0\n                    }\n                }\n            }) // Revenue generating\n        ]);\n        // Performance metrics\n        const performanceMetrics = {\n            userEngagement: {\n                dailyActiveUsers: await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.count({\n                    where: {\n                        lastActiveAt: {\n                            gte: new Date(Date.now() - 24 * 60 * 60 * 1000)\n                        }\n                    }\n                }),\n                weeklyActiveUsers: await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.count({\n                    where: {\n                        lastActiveAt: {\n                            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)\n                        }\n                    }\n                }),\n                monthlyActiveUsers: await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.count({\n                    where: {\n                        lastActiveAt: {\n                            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)\n                        }\n                    }\n                })\n            },\n            systemHealth: {\n                errorRate: await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.systemLog.count({\n                    where: {\n                        level: \"ERROR\",\n                        createdAt: {\n                            gte: startDate\n                        }\n                    }\n                }) / Math.max(await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.systemLog.count({\n                    where: {\n                        createdAt: {\n                            gte: startDate\n                        }\n                    }\n                }), 1) * 100,\n                avgResponseTime: 150,\n                uptime: 99.9 // This would come from actual monitoring\n            }\n        };\n        // Calculate growth rates\n        const previousPeriodStart = new Date(startDate.getTime() - periodDays * 24 * 60 * 60 * 1000);\n        const previousPeriodUsers = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.count({\n            where: {\n                createdAt: {\n                    gte: previousPeriodStart,\n                    lt: startDate\n                }\n            }\n        });\n        const currentPeriodUsers = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.count({\n            where: {\n                createdAt: {\n                    gte: startDate\n                }\n            }\n        });\n        const userGrowthRate = previousPeriodUsers > 0 ? (currentPeriodUsers - previousPeriodUsers) / previousPeriodUsers * 100 : 0;\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            overview: {\n                totalUsers,\n                totalCompanies,\n                totalRevenue: Number(totalRevenue._sum.amount || 0),\n                totalActivities,\n                activeUsers,\n                activeCompanies,\n                userGrowthRate,\n                conversionRate: totalCompanies > 0 ? activeCompanies / totalCompanies * 100 : 0\n            },\n            timeSeries: {\n                userGrowth: userGrowth.map((item)=>({\n                        date: item.date,\n                        value: Number(item.count)\n                    })),\n                companyGrowth: companyGrowth.map((item)=>({\n                        date: item.date,\n                        value: Number(item.count)\n                    })),\n                revenueGrowth: revenueGrowth.map((item)=>({\n                        date: item.date,\n                        value: Number(item.revenue || 0)\n                    })),\n                activityGrowth: activityGrowth.map((item)=>({\n                        date: item.date,\n                        value: Number(item.count)\n                    }))\n            },\n            distribution: {\n                geographic: geographicData.map((item)=>({\n                        country: item.country,\n                        count: item._count.id\n                    })),\n                industry: industryData.map((item)=>({\n                        industry: item.industry,\n                        count: item._count.id\n                    })),\n                roles: roleData.map((item)=>({\n                        role: item.role,\n                        count: item._count.id\n                    })),\n                plans: planData.map((item)=>({\n                        plan: item.plan,\n                        count: item._count.id,\n                        revenue: Number(item._sum.amount || 0)\n                    }))\n            },\n            topCompanies: topCompanies.map((company)=>({\n                    id: company.id,\n                    name: company.name,\n                    industry: company.industry,\n                    subscription: company.subscription,\n                    metrics: {\n                        users: company._count.users,\n                        customers: company._count.customers,\n                        quotations: company._count.quotations,\n                        invoices: company._count.invoices,\n                        activities: company._count.activities\n                    }\n                })),\n            recentActivities: recentActivities.map((activity)=>({\n                    id: activity.id,\n                    type: activity.type,\n                    title: activity.title,\n                    company: activity.company?.name,\n                    user: activity.createdBy?.name,\n                    createdAt: activity.createdAt\n                })),\n            funnel: {\n                totalCompanies: funnelData[0],\n                activeCompanies: funnelData[1],\n                paidSubscriptions: funnelData[2],\n                revenueGenerating: funnelData[3]\n            },\n            performance: performanceMetrics,\n            period: periodDays,\n            generatedAt: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error(\"Error generating reports:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to generate reports\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/super-admin/reports/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/github */ \"(rsc)/./node_modules/next-auth/providers/github.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n\nconst authOptions = {\n    // Using JWT strategy instead of database adapter for now\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                try {\n                    if (!credentials?.email || !credentials?.password) {\n                        console.log(\"Missing credentials\");\n                        return null;\n                    }\n                    console.log(\"Attempting to authenticate user:\", credentials.email);\n                    const user = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.findUnique({\n                        where: {\n                            email: credentials.email\n                        },\n                        select: {\n                            id: true,\n                            email: true,\n                            name: true,\n                            password: true,\n                            role: true,\n                            companyId: true\n                        }\n                    });\n                    // Get the company ID - either as member or owner\n                    let finalCompanyId = user?.companyId;\n                    if (!finalCompanyId && user) {\n                        const ownedCompany = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.company.findFirst({\n                            where: {\n                                ownerId: user.id\n                            },\n                            select: {\n                                id: true\n                            }\n                        });\n                        finalCompanyId = ownedCompany?.id;\n                        // If user is company owner, update their companyId for future queries\n                        if (finalCompanyId) {\n                            await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                                where: {\n                                    id: user.id\n                                },\n                                data: {\n                                    companyId: finalCompanyId\n                                }\n                            });\n                        }\n                    }\n                    if (!user) {\n                        console.log(\"User not found:\", credentials.email);\n                        return null;\n                    }\n                    if (!user.password) {\n                        console.log(\"User has no password set:\", credentials.email);\n                        return null;\n                    }\n                    const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_3___default().compare(credentials.password, user.password);\n                    if (!isPasswordValid) {\n                        console.log(\"Invalid password for user:\", credentials.email);\n                        return null;\n                    }\n                    // Update last login\n                    await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                        where: {\n                            id: user.id\n                        },\n                        data: {\n                            lastLoginAt: new Date(),\n                            loginCount: {\n                                increment: 1\n                            }\n                        }\n                    });\n                    console.log(\"User authenticated successfully:\", user.email);\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name,\n                        role: user.role,\n                        companyId: finalCompanyId\n                    };\n                } catch (error) {\n                    console.error(\"Authentication error:\", error);\n                    return null;\n                }\n            }\n        }),\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            clientId: process.env.GITHUB_CLIENT_ID,\n            clientSecret: process.env.GITHUB_CLIENT_SECRET\n        })\n    ],\n    session: {\n        strategy: \"jwt\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                console.log(\"JWT callback - user data:\", {\n                    id: user.id,\n                    email: user.email,\n                    role: user.role,\n                    companyId: user.companyId\n                });\n                token.role = user.role;\n                token.companyId = user.companyId;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n                session.user.companyId = token.companyId;\n                console.log(\"Session callback - final session:\", {\n                    id: session.user.id,\n                    email: session.user.email,\n                    role: session.user.role,\n                    companyId: session.user.companyId\n                });\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        signUp: \"/auth/signup\",\n        error: \"/auth/error\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zYWFzLWFwcC8uL2xpYi9wcmlzbWEudHM/OTgyMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/lru-cache","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Freports%2Froute&page=%2Fapi%2Fsuper-admin%2Freports%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Freports%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();