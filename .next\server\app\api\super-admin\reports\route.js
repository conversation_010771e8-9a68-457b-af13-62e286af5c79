"use strict";(()=>{var e={};e.id=4081,e.ids=[4081],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},38636:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>g,originalPathname:()=>h,patchFetch:()=>v,requestAsyncStorage:()=>m,routeModule:()=>d,serverHooks:()=>w,staticGenerationAsyncStorage:()=>y,staticGenerationBailout:()=>_});var a={};r.r(a),r.d(a,{GET:()=>p});var s=r(95419),n=r(69108),o=r(99678),i=r(78070),u=r(81355),c=r(3205),l=r(9108);async function p(e){try{let t=await (0,u.getServerSession)(c.L);if(!t?.user?.id||t?.user?.role!=="SUPER_ADMIN")return i.Z.json({error:"Super admin access required"},{status:403});let{searchParams:r}=new URL(e.url),a=r.get("period")||"30";r.get("type");let s=parseInt(a),n=new Date(Date.now()-864e5*s),[o,p,d,m,y,w]=await Promise.all([l._.user.count(),l._.company.count(),l._.subscription.aggregate({where:{status:"ACTIVE"},_sum:{amount:!0}}),l._.activity.count(),l._.user.count({where:{status:"ACTIVE"}}),l._.company.count({where:{status:"ACTIVE"}})]),[g,_,h,v]=await Promise.all([Promise.resolve([{date:new Date().toISOString().split("T")[0],count:await l._.user.count().catch(()=>0)}]),Promise.resolve([{date:new Date().toISOString().split("T")[0],count:await l._.company.count().catch(()=>0)}]),Promise.resolve([{date:new Date().toISOString().split("T")[0],revenue:await l._.subscription.aggregate({_sum:{amount:!0},where:{status:"ACTIVE"}}).then(e=>Number(e._sum.amount||0)).catch(()=>0)}]),Promise.resolve([{date:new Date().toISOString().split("T")[0],count:await l._.activity.count().catch(()=>0)}])]),A=await l._.company.groupBy({by:["country"],_count:{id:!0},where:{country:{not:null}},orderBy:{_count:{id:"desc"}},take:10}).catch(()=>[]),I=await l._.company.groupBy({by:["industry"],_count:{id:!0},where:{industry:{not:null}},orderBy:{_count:{id:"desc"}},take:10}).catch(()=>[]),b=await l._.user.groupBy({by:["role"],_count:{id:!0},orderBy:{_count:{id:"desc"}}}).catch(()=>[]),x=await l._.subscription.groupBy({by:["plan"],_count:{id:!0},_sum:{amount:!0},orderBy:{_count:{id:"desc"}}}).catch(()=>[]),T=await l._.company.findMany({take:10,include:{subscription:{select:{amount:!0,status:!0}},_count:{select:{users:!0,customers:!0,quotations:!0,invoices:!0,activities:!0}}},orderBy:{createdAt:"desc"}}).catch(()=>[]),q=await l._.activity.findMany({take:20,where:{createdAt:{gte:n}},include:{company:{select:{name:!0}},createdBy:{select:{name:!0,email:!0}}},orderBy:{createdAt:"desc"}}).catch(()=>[]),f=await Promise.all([l._.company.count(),l._.company.count({where:{status:"ACTIVE"}}),l._.subscription.count({where:{status:"ACTIVE"}}),l._.subscription.count({where:{status:"ACTIVE",amount:{gt:0}}})]),E={userEngagement:{dailyActiveUsers:await l._.user.count({where:{lastActiveAt:{gte:new Date(Date.now()-864e5)}}}).catch(()=>0),weeklyActiveUsers:await l._.user.count({where:{lastActiveAt:{gte:new Date(Date.now()-6048e5)}}}).catch(()=>0),monthlyActiveUsers:await l._.user.count({where:{lastActiveAt:{gte:new Date(Date.now()-2592e6)}}}).catch(()=>0)},systemHealth:{errorRate:.5,avgResponseTime:150,uptime:99.9}},S=new Date(n.getTime()-864e5*s),C=await l._.user.count({where:{createdAt:{gte:S,lt:n}}}),D=await l._.user.count({where:{createdAt:{gte:n}}});return i.Z.json({overview:{totalUsers:o,totalCompanies:p,totalRevenue:Number(d._sum.amount||0),totalActivities:m,activeUsers:y,activeCompanies:w,userGrowthRate:C>0?(D-C)/C*100:0,conversionRate:p>0?w/p*100:0},timeSeries:{userGrowth:Array.isArray(g)?g.map(e=>({date:e.date,value:Number(e.count)})):[],companyGrowth:Array.isArray(_)?_.map(e=>({date:e.date,value:Number(e.count)})):[],revenueGrowth:Array.isArray(h)?h.map(e=>({date:e.date,value:Number(e.revenue||0)})):[],activityGrowth:Array.isArray(v)?v.map(e=>({date:e.date,value:Number(e.count)})):[]},distribution:{geographic:Array.isArray(A)?A.map(e=>({country:e.country,count:e._count.id})):[],industry:Array.isArray(I)?I.map(e=>({industry:e.industry,count:e._count.id})):[],roles:Array.isArray(b)?b.map(e=>({role:e.role,count:e._count.id})):[],plans:Array.isArray(x)?x.map(e=>({plan:e.plan,count:e._count.id,revenue:Number(e._sum.amount||0)})):[]},topCompanies:Array.isArray(T)?T.map(e=>({id:e.id,name:e.name,industry:e.industry,subscription:e.subscription,metrics:{users:e._count?.users||0,customers:e._count?.customers||0,quotations:e._count?.quotations||0,invoices:e._count?.invoices||0,activities:e._count?.activities||0}})):[],recentActivities:Array.isArray(q)?q.map(e=>({id:e.id,type:e.type,title:e.title,company:e.company?.name,user:e.createdBy?.name,createdAt:e.createdAt})):[],funnel:{totalCompanies:f[0],activeCompanies:f[1],paidSubscriptions:f[2],revenueGenerating:f[3]},performance:E,period:s,generatedAt:new Date().toISOString()})}catch(e){return console.error("Error generating reports:",e),i.Z.json({error:"Failed to generate reports"},{status:500})}}let d=new s.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/super-admin/reports/route",pathname:"/api/super-admin/reports",filename:"route",bundlePath:"app/api/super-admin/reports/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\super-admin\\reports\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:m,staticGenerationAsyncStorage:y,serverHooks:w,headerHooks:g,staticGenerationBailout:_}=d,h="/api/super-admin/reports/route";function v(){return(0,o.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:y})}},3205:(e,t,r)=>{r.d(t,{L:()=>c});var a=r(86485),s=r(10375),n=r(50694),o=r(6521),i=r.n(o),u=r(9108);let c={providers:[(0,a.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await u._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),r=t?.companyId;if(!r&&t){let e=await u._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(r=e?.id)&&await u._.user.update({where:{id:t.id},data:{companyId:r}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await i().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await u._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:r}}catch(e){return console.error("Authentication error:",e),null}}}),(0,s.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,n.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,r)=>{r.d(t,{_:()=>s});let a=require("@prisma/client"),s=globalThis.prisma??new a.PrismaClient}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,6206,6521,2455,4520],()=>r(38636));module.exports=a})();