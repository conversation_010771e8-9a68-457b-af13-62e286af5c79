import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const companyUpdateSchema = z.object({
  name: z.string().optional(),
  email: z.string().email().optional(),
  phone: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  country: z.string().optional(),
  postalCode: z.string().optional(),
  website: z.string().url().optional().or(z.literal('')),
  industry: z.string().optional(),
  size: z.enum(['STARTUP', 'SMALL', 'MEDIUM', 'LARGE', 'ENTERPRISE']).optional(),
  businessType: z.string().optional(),
  status: z.enum(['ACTIVE', 'SUSPENDED', 'INACTIVE']).optional(),
  taxId: z.string().optional(),
  registrationNumber: z.string().optional()
})

// GET /api/super-admin/companies - Get all companies with admin details
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || session?.user?.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Super admin access required' }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') || ''
    const industry = searchParams.get('industry') || ''
    const size = searchParams.get('size') || ''

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {}

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
        { industry: { contains: search, mode: 'insensitive' } },
        { businessType: { contains: search, mode: 'insensitive' } }
      ]
    }

    if (status) {
      where.status = status
    }

    if (industry) {
      where.industry = industry
    }

    if (size) {
      where.size = size
    }

    const [companies, total] = await Promise.all([
      prisma.company.findMany({
        where,
        include: {
          owner: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true,
              status: true,
              lastLoginAt: true,
              loginCount: true
            }
          },
          members: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true,
              status: true,
              lastLoginAt: true
            }
          },
          _count: {
            select: {
              members: true,
              customers: true,
              quotations: true,
              invoices: true,
              contracts: true,
              items: true,
              activities: true
            }
          }
        },
        orderBy: [
          { createdAt: 'desc' }
        ],
        skip,
        take: limit
      }),
      prisma.company.count({ where })
    ])

    // Get additional metrics for each company
    const companiesWithMetrics = await Promise.all(
      companies.map(async (company) => {
        const [
          recentActivity,
          totalRevenue,
          activeUsers,
          lastActivity
        ] = await Promise.all([
          // Recent activity count
          prisma.activity.count({
            where: {
              companyId: company.id,
              createdAt: {
                gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
              }
            }
          }),
          // Total revenue (from paid invoices)
          prisma.invoice.aggregate({
            where: {
              companyId: company.id,
              status: 'PAID'
            },
            _sum: {
              totalAmount: true
            }
          }),
          // Active users count
          prisma.user.count({
            where: {
              companyId: company.id,
              status: 'ACTIVE'
            }
          }),
          // Last activity
          prisma.activity.findFirst({
            where: {
              companyId: company.id
            },
            orderBy: {
              createdAt: 'desc'
            },
            select: {
              createdAt: true,
              type: true,
              title: true
            }
          })
        ])

        return {
          id: company.id,
          name: company.name,
          email: company.email,
          phone: company.phone,
          address: company.address,
          city: company.city,
          state: company.state,
          country: company.country,
          postalCode: company.postalCode,
          website: company.website,
          logo: company.logo,
          industry: company.industry,
          size: company.size,
          businessType: company.businessType,
          status: company.status,
          taxId: company.taxId,
          registrationNumber: company.registrationNumber,
          owner: company.owner,
          members: company.members,
          counts: {
            members: company._count.members,
            customers: company._count.customers,
            quotations: company._count.quotations,
            invoices: company._count.invoices,
            contracts: company._count.contracts,
            items: company._count.items,
            activities: company._count.activities
          },
          metrics: {
            recentActivity,
            totalRevenue: Number(totalRevenue._sum.totalAmount || 0),
            activeUsers,
            lastActivity
          },
          createdAt: company.createdAt,
          updatedAt: company.updatedAt
        }
      })
    )

    return NextResponse.json({
      companies: companiesWithMetrics,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Error fetching companies:', error)
    return NextResponse.json(
      { error: 'Failed to fetch companies' },
      { status: 500 }
    )
  }
}

// POST /api/super-admin/companies - Create new company (admin only)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || session?.user?.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Super admin access required' }, { status: 403 })
    }

    const body = await request.json()
    const { ownerEmail, ownerName, ownerPassword, ...companyData } = body

    // Validate required fields
    if (!companyData.name || !ownerEmail || !ownerName) {
      return NextResponse.json(
        { error: 'Company name, owner email, and owner name are required' },
        { status: 400 }
      )
    }

    // Check if company or user already exists
    const [existingCompany, existingUser] = await Promise.all([
      prisma.company.findFirst({
        where: {
          OR: [
            { name: companyData.name },
            { email: companyData.email }
          ]
        }
      }),
      prisma.user.findUnique({
        where: { email: ownerEmail }
      })
    ])

    if (existingCompany) {
      return NextResponse.json(
        { error: 'Company with this name or email already exists' },
        { status: 400 }
      )
    }

    if (existingUser) {
      return NextResponse.json(
        { error: 'User with this email already exists' },
        { status: 400 }
      )
    }

    const result = await prisma.$transaction(async (tx) => {
      // Create company
      const company = await tx.company.create({
        data: {
          ...companyData,
          status: 'ACTIVE'
        }
      })

      // Create owner user
      const hashedPassword = ownerPassword ? 
        await require('bcryptjs').hash(ownerPassword, 12) : 
        await require('bcryptjs').hash('tempPassword123', 12)

      const owner = await tx.user.create({
        data: {
          name: ownerName,
          email: ownerEmail,
          password: hashedPassword,
          role: 'ADMIN',
          status: 'ACTIVE',
          companyId: company.id
        }
      })

      // Update company with owner
      await tx.company.update({
        where: { id: company.id },
        data: { ownerId: owner.id }
      })

      // Log admin action
      await tx.activity.create({
        data: {
          type: 'ADMIN',
          title: 'Company Created by Super Admin',
          description: `Company "${company.name}" was created by super admin`,
          companyId: company.id,
          createdById: session.user.id
        }
      })

      return { company, owner }
    })

    return NextResponse.json({
      company: result.company,
      owner: {
        id: result.owner.id,
        name: result.owner.name,
        email: result.owner.email,
        role: result.owner.role
      },
      message: 'Company created successfully'
    })

  } catch (error) {
    console.error('Error creating company:', error)
    return NextResponse.json(
      { error: 'Failed to create company' },
      { status: 500 }
    )
  }
}
