"use strict";(()=>{var e={};e.id=2076,e.ids=[2076],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},65918:(e,r,a)=>{a.r(r),a.d(r,{headerHooks:()=>x,originalPathname:()=>I,patchFetch:()=>w,requestAsyncStorage:()=>m,routeModule:()=>d,serverHooks:()=>y,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>h});var s={};a.r(s),a.d(s,{POST:()=>p});var t=a(95419),i=a(69108),n=a(99678),o=a(78070),c=a(81355),l=a(3205),u=a(9108);async function p(e){try{let e=await u._.pricingPlan.count();if(e>0)return o.Z.json({success:!1,error:"Pricing plans already exist"},{status:400});if(0===e);else{let e=await (0,c.getServerSession)(l.L);if(!e?.user||"SUPER_ADMIN"!==e.user.role)return o.Z.json({success:!1,error:"Unauthorized"},{status:401})}let r=[{name:"Free",description:"Perfect for getting started with basic features",monthlyPrice:0,yearlyPrice:0,currency:"USD",maxUsers:2,maxCompanies:1,maxCustomers:50,maxQuotations:10,maxInvoices:10,maxContracts:5,maxStorage:BigInt(1073741824),features:{basicReporting:!0,emailSupport:!0,mobileApp:!1,advancedAnalytics:!1,customBranding:!1,apiAccess:!1,prioritySupport:!1,customIntegrations:!1,advancedSecurity:!1,dedicatedManager:!1},isActive:!0,isPublic:!0,trialDays:0,sortOrder:1},{name:"Pro",description:"Best for growing businesses with advanced features",monthlyPrice:29,yearlyPrice:290,currency:"USD",maxUsers:10,maxCompanies:3,maxCustomers:500,maxQuotations:100,maxInvoices:100,maxContracts:50,maxStorage:BigInt(***********),features:{basicReporting:!0,emailSupport:!0,mobileApp:!0,advancedAnalytics:!0,customBranding:!0,apiAccess:!0,prioritySupport:!1,customIntegrations:!1,advancedSecurity:!1,dedicatedManager:!1},isActive:!0,isPublic:!0,trialDays:14,sortOrder:2},{name:"Enterprise",description:"For large organizations with custom requirements",monthlyPrice:99,yearlyPrice:990,currency:"USD",maxUsers:50,maxCompanies:10,maxCustomers:5e3,maxQuotations:1e3,maxInvoices:1e3,maxContracts:500,maxStorage:BigInt(***********0),features:{basicReporting:!0,emailSupport:!0,mobileApp:!0,advancedAnalytics:!0,customBranding:!0,apiAccess:!0,prioritySupport:!0,customIntegrations:!0,advancedSecurity:!0,dedicatedManager:!0},isActive:!0,isPublic:!0,trialDays:30,sortOrder:3}],a=await Promise.all(r.map(e=>u._.pricingPlan.create({data:e})));return o.Z.json({success:!0,message:"Default pricing plans created successfully",data:a.map(e=>({...e,monthlyPrice:Number(e.monthlyPrice),yearlyPrice:e.yearlyPrice?Number(e.yearlyPrice):null,maxStorage:Number(e.maxStorage)}))})}catch(e){return console.error("Error seeding pricing plans:",e),o.Z.json({success:!1,error:"Failed to seed pricing plans"},{status:500})}}let d=new t.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/pricing-plans/seed/route",pathname:"/api/pricing-plans/seed",filename:"route",bundlePath:"app/api/pricing-plans/seed/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\pricing-plans\\seed\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:m,staticGenerationAsyncStorage:g,serverHooks:y,headerHooks:x,staticGenerationBailout:h}=d,I="/api/pricing-plans/seed/route";function w(){return(0,n.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:g})}},3205:(e,r,a)=>{a.d(r,{L:()=>l});var s=a(86485),t=a(10375),i=a(50694),n=a(6521),o=a.n(n),c=a(9108);let l={providers:[(0,s.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let r=await c._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),a=r?.companyId;if(!a&&r){let e=await c._.company.findFirst({where:{ownerId:r.id},select:{id:!0}});(a=e?.id)&&await c._.user.update({where:{id:r.id},data:{companyId:a}})}if(!r)return console.log("User not found:",e.email),null;if(!r.password)return console.log("User has no password set:",e.email),null;if(!await o().compare(e.password,r.password))return console.log("Invalid password for user:",e.email),null;return await c._.user.update({where:{id:r.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",r.email),{id:r.id,email:r.email,name:r.name,role:r.role,companyId:a}}catch(e){return console.error("Authentication error:",e),null}}}),(0,t.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,i.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(console.log("JWT callback - user data:",{id:r.id,email:r.email,role:r.role,companyId:r.companyId}),e.role=r.role,e.companyId=r.companyId),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role,e.user.companyId=r.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,r,a)=>{a.d(r,{_:()=>t});let s=require("@prisma/client"),t=globalThis.prisma??new s.PrismaClient}};var r=require("../../../../webpack-runtime.js");r.C(e);var a=e=>r(r.s=e),s=r.X(0,[1638,6206,6521,2455,4520],()=>a(65918));module.exports=s})();