(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4276],{11981:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},92457:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},28203:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},99155:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("CheckSquare",[["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}],["path",{d:"M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11",key:"1jnkn4"}]])},6141:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},42482:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},85790:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},67972:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},25750:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},14638:function(e,s,t){Promise.resolve().then(t.bind(t,69154))},69154:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return L}});var a=t(57437),r=t(2265),l=t(27815),i=t(85754),n=t(31478),c=t(45179),d=t(45509),o=t(93930),x=t(25462),u=t(10548),m=t(92457),h=t(64280),p=t(99155),y=t(66654),f=t(11981),g=t(6141),j=t(28203),b=t(25750),N=t(5925);function v(){let[e,s]=(0,r.useState)(null),[t,c]=(0,r.useState)(!0),[o,x]=(0,r.useState)("30"),u=async()=>{try{c(!0);let e=await fetch("/api/tasks/analytics?period=".concat(o));if(!e.ok)throw Error("Failed to fetch analytics");let t=await e.json();s(t)}catch(e){N.toast.error("Failed to load task analytics"),console.error("Error fetching analytics:",e)}finally{c(!1)}};(0,r.useEffect)(()=>{u()},[o]);let v=e=>{switch(e){case"TODO":default:return"bg-gray-100 text-gray-800";case"IN_PROGRESS":return"bg-blue-100 text-blue-800";case"REVIEW":return"bg-yellow-100 text-yellow-800";case"DONE":return"bg-green-100 text-green-800";case"CANCELLED":return"bg-red-100 text-red-800"}},k=e=>{switch(e){case"LOW":default:return"bg-gray-100 text-gray-600";case"MEDIUM":return"bg-blue-100 text-blue-600";case"HIGH":return"bg-orange-100 text-orange-600";case"URGENT":return"bg-red-100 text-red-600";case"CRITICAL":return"bg-red-200 text-red-800"}};return t?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):e?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Task Analytics"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(d.Ph,{value:o,onValueChange:x,children:[(0,a.jsx)(d.i4,{className:"w-32",children:(0,a.jsx)(d.ki,{})}),(0,a.jsxs)(d.Bw,{children:[(0,a.jsx)(d.Ql,{value:"7",children:"Last 7 days"}),(0,a.jsx)(d.Ql,{value:"30",children:"Last 30 days"}),(0,a.jsx)(d.Ql,{value:"90",children:"Last 90 days"}),(0,a.jsx)(d.Ql,{value:"365",children:"Last year"})]})]}),(0,a.jsxs)(i.z,{variant:"outline",onClick:u,size:"sm",children:[(0,a.jsx)(h.Z,{className:"h-4 w-4 mr-2"}),"Refresh"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsx)(l.Zb,{children:(0,a.jsx)(l.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-blue-100 rounded-full",children:(0,a.jsx)(p.Z,{className:"h-6 w-6 text-blue-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Total Tasks"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.totalTasks})]})]})})}),(0,a.jsx)(l.Zb,{children:(0,a.jsx)(l.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-green-100 rounded-full",children:(0,a.jsx)(y.Z,{className:"h-6 w-6 text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Completed"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.completedTasks}),(0,a.jsxs)("p",{className:"text-xs text-green-600",children:[e.summary.completionRate.toFixed(1),"% rate"]})]})]})})}),(0,a.jsx)(l.Zb,{children:(0,a.jsx)(l.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-red-100 rounded-full",children:(0,a.jsx)(f.Z,{className:"h-6 w-6 text-red-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Overdue"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.overdueTasks})]})]})})}),(0,a.jsx)(l.Zb,{children:(0,a.jsx)(l.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-purple-100 rounded-full",children:(0,a.jsx)(g.Z,{className:"h-6 w-6 text-purple-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Avg Completion"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[e.summary.averageCompletionTime.toFixed(1),"d"]})]})]})})})]}),(0,a.jsxs)(l.Zb,{children:[(0,a.jsx)(l.Ol,{children:(0,a.jsxs)(l.ll,{className:"flex items-center",children:[(0,a.jsx)(j.Z,{className:"h-5 w-5 mr-2"}),"Today's Summary"]})}),(0,a.jsx)(l.aY,{children:(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:e.summary.tasksCreatedToday}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Created"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-green-600",children:e.summary.tasksCompletedToday}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Completed"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-orange-600",children:e.summary.tasksDueToday}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Due Today"})]})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)(l.Zb,{children:[(0,a.jsx)(l.Ol,{children:(0,a.jsx)(l.ll,{children:"Tasks by Status"})}),(0,a.jsx)(l.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:e.tasksByStatus.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsx)(n.C,{className:v(e.status),children:e.status.replace("_"," ")})}),(0,a.jsx)("span",{className:"font-semibold",children:e.count})]},e.status))})})]}),(0,a.jsxs)(l.Zb,{children:[(0,a.jsx)(l.Ol,{children:(0,a.jsx)(l.ll,{children:"Tasks by Priority"})}),(0,a.jsx)(l.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:e.tasksByPriority.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsx)(n.C,{className:k(e.priority),children:e.priority})}),(0,a.jsx)("span",{className:"font-semibold",children:e.count})]},e.priority))})})]}),(0,a.jsxs)(l.Zb,{children:[(0,a.jsx)(l.Ol,{children:(0,a.jsx)(l.ll,{children:"Tasks by Type"})}),(0,a.jsx)(l.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:e.tasksByType.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm",children:e.type}),(0,a.jsx)("span",{className:"font-semibold",children:e.count})]},e.type))})})]}),(0,a.jsxs)(l.Zb,{children:[(0,a.jsx)(l.Ol,{children:(0,a.jsxs)(l.ll,{className:"flex items-center",children:[(0,a.jsx)(b.Z,{className:"h-5 w-5 mr-2"}),"Top Assignees"]})}),(0,a.jsx)(l.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:e.tasksByAssignee.slice(0,5).map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm",children:e.assignee.name||e.assignee.email}),(0,a.jsx)("span",{className:"font-semibold",children:e.count})]},e.assignee.id))})})]})]}),(0,a.jsxs)(l.Zb,{children:[(0,a.jsx)(l.Ol,{children:(0,a.jsx)(l.ll,{children:"Upcoming Tasks (Next 7 Days)"})}),(0,a.jsx)(l.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:0===e.upcomingTasks.length?(0,a.jsx)("p",{className:"text-gray-500 text-center py-4",children:"No upcoming tasks"}):e.upcomingTasks.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:e.title}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Due: ",new Date(e.dueDate).toLocaleDateString(),e.assignedTo&&" • ".concat(e.assignedTo.name||e.assignedTo.email)]})]}),(0,a.jsx)(n.C,{className:k(e.priority),children:e.priority})]},e.id))})})]}),(0,a.jsxs)(l.Zb,{children:[(0,a.jsx)(l.Ol,{children:(0,a.jsx)(l.ll,{children:"High Priority Tasks"})}),(0,a.jsx)(l.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:0===e.highPriorityTasks.length?(0,a.jsx)("p",{className:"text-gray-500 text-center py-4",children:"No high priority tasks"}):e.highPriorityTasks.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:e.title}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[e.dueDate&&"Due: ".concat(new Date(e.dueDate).toLocaleDateString()),e.assignedTo&&" • ".concat(e.assignedTo.name||e.assignedTo.email)]})]}),(0,a.jsx)(n.C,{className:k(e.priority),children:e.priority})]},e.id))})})]})]}):(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)(m.Z,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,a.jsx)("p",{children:"Failed to load analytics data"})]})}var k=t(67972),w=t(17472),E=t(99670),T=t(49617),C=t(45367),D=t(85790),Z=t(9883),O=t(42482),S=t(41827),I=t(61396),P=t.n(I);function L(){let[e,s]=(0,r.useState)([]),[t,m]=(0,r.useState)(!0),[h,b]=(0,r.useState)(!1),[I,L]=(0,r.useState)(!1),[M,A]=(0,r.useState)(null),[R,F]=(0,r.useState)(""),[$,z]=(0,r.useState)(""),[U,Y]=(0,r.useState)(""),[H,V]=(0,r.useState)(""),[_,Q]=(0,r.useState)(1),[B,G]=(0,r.useState)(1),[W,K]=(0,r.useState)(0),q=async()=>{try{m(!0);let e=new URLSearchParams({page:_.toString(),limit:"20"});R&&"all"!==R&&e.append("status",R),$&&"all"!==$&&e.append("priority",$),U&&e.append("assignedToId",U),H&&e.append("search",H);let t=await fetch("/api/tasks?".concat(e));if(!t.ok)throw Error("Failed to fetch tasks");let a=await t.json();s(a.tasks),G(a.pagination.pages),K(a.pagination.total)}catch(e){N.toast.error("Failed to load tasks"),console.error("Error fetching tasks:",e)}finally{m(!1)}};(0,r.useEffect)(()=>{q()},[_,R,$,U,H]);let X=async e=>{if(confirm("Are you sure you want to delete this task?"))try{if(!(await fetch("/api/tasks/".concat(e),{method:"DELETE"})).ok)throw Error("Failed to delete task");N.toast.success("Task deleted successfully"),q()}catch(e){N.toast.error("Failed to delete task"),console.error("Error deleting task:",e)}},J=async(e,s)=>{try{if(!(await fetch("/api/tasks/".concat(e),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:s})})).ok)throw Error("Failed to update task status");N.toast.success("Task status updated"),q()}catch(e){N.toast.error("Failed to update task status"),console.error("Error updating task status:",e)}},ee=e=>{let s={TODO:{label:"To Do",className:"bg-gray-100 text-gray-800"},IN_PROGRESS:{label:"In Progress",className:"bg-blue-100 text-blue-800"},REVIEW:{label:"Review",className:"bg-yellow-100 text-yellow-800"},DONE:{label:"Done",className:"bg-green-100 text-green-800"},CANCELLED:{label:"Cancelled",className:"bg-red-100 text-red-800"}},t=s[e]||s.TODO;return(0,a.jsx)(n.C,{className:t.className,children:t.label})},es=e=>{let s={LOW:{label:"Low",className:"bg-gray-100 text-gray-600"},MEDIUM:{label:"Medium",className:"bg-blue-100 text-blue-600"},HIGH:{label:"High",className:"bg-orange-100 text-orange-600"},URGENT:{label:"Urgent",className:"bg-red-100 text-red-600"},CRITICAL:{label:"Critical",className:"bg-red-200 text-red-800"}},t=s[e]||s.MEDIUM;return(0,a.jsx)(n.C,{className:t.className,children:t.label})};return(0,a.jsxs)("div",{className:"container mx-auto py-6 space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Task Management"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Manage and track your team's tasks and projects"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(i.z,{variant:"outline",onClick:()=>L(!I),children:[(0,a.jsx)(D.Z,{className:"h-4 w-4 mr-2"}),"Analytics"]}),(0,a.jsxs)(i.z,{onClick:()=>b(!0),children:[(0,a.jsx)(Z.Z,{className:"h-4 w-4 mr-2"}),"New Task"]})]})]}),I&&(0,a.jsx)(v,{}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsx)(l.Zb,{children:(0,a.jsx)(l.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-blue-100 rounded-full",children:(0,a.jsx)(p.Z,{className:"h-6 w-6 text-blue-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Total Tasks"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:W})]})]})})}),(0,a.jsx)(l.Zb,{children:(0,a.jsx)(l.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-green-100 rounded-full",children:(0,a.jsx)(y.Z,{className:"h-6 w-6 text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Completed"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.filter(e=>"DONE"===e.status).length})]})]})})}),(0,a.jsx)(l.Zb,{children:(0,a.jsx)(l.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-yellow-100 rounded-full",children:(0,a.jsx)(g.Z,{className:"h-6 w-6 text-yellow-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"In Progress"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.filter(e=>"IN_PROGRESS"===e.status).length})]})]})})}),(0,a.jsx)(l.Zb,{children:(0,a.jsx)(l.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-red-100 rounded-full",children:(0,a.jsx)(f.Z,{className:"h-6 w-6 text-red-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Overdue"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.filter(e=>e.dueDate&&new Date(e.dueDate)<new Date&&"DONE"!==e.status).length})]})]})})})]}),(0,a.jsxs)(l.Zb,{children:[(0,a.jsx)(l.Ol,{children:(0,a.jsxs)(l.ll,{className:"flex items-center",children:[(0,a.jsx)(O.Z,{className:"h-5 w-5 mr-2"}),"Filters"]})}),(0,a.jsx)(l.aY,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(S.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,a.jsx)(c.I,{placeholder:"Search tasks...",value:H,onChange:e=>V(e.target.value),className:"pl-10"})]}),(0,a.jsxs)(d.Ph,{value:R,onValueChange:F,children:[(0,a.jsx)(d.i4,{children:(0,a.jsx)(d.ki,{placeholder:"All Statuses"})}),(0,a.jsxs)(d.Bw,{children:[(0,a.jsx)(d.Ql,{value:"all",children:"All Statuses"}),(0,a.jsx)(d.Ql,{value:"TODO",children:"To Do"}),(0,a.jsx)(d.Ql,{value:"IN_PROGRESS",children:"In Progress"}),(0,a.jsx)(d.Ql,{value:"REVIEW",children:"Review"}),(0,a.jsx)(d.Ql,{value:"DONE",children:"Done"}),(0,a.jsx)(d.Ql,{value:"CANCELLED",children:"Cancelled"})]})]}),(0,a.jsxs)(d.Ph,{value:$,onValueChange:z,children:[(0,a.jsx)(d.i4,{children:(0,a.jsx)(d.ki,{placeholder:"All Priorities"})}),(0,a.jsxs)(d.Bw,{children:[(0,a.jsx)(d.Ql,{value:"all",children:"All Priorities"}),(0,a.jsx)(d.Ql,{value:"LOW",children:"Low"}),(0,a.jsx)(d.Ql,{value:"MEDIUM",children:"Medium"}),(0,a.jsx)(d.Ql,{value:"HIGH",children:"High"}),(0,a.jsx)(d.Ql,{value:"URGENT",children:"Urgent"}),(0,a.jsx)(d.Ql,{value:"CRITICAL",children:"Critical"})]})]}),(0,a.jsx)(i.z,{variant:"outline",onClick:()=>{F("all"),z("all"),Y(""),V("")},children:"Clear Filters"})]})})]}),(0,a.jsxs)(l.Zb,{children:[(0,a.jsx)(l.Ol,{children:(0,a.jsx)(l.ll,{children:"Tasks"})}),(0,a.jsx)(l.aY,{children:t?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,a.jsx)(x.w,{columns:[{accessorKey:"title",header:"Task",cell:e=>{let{row:s}=e,t=s.original;return(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("div",{className:"font-medium",children:t.title}),t.description&&(0,a.jsx)("div",{className:"text-sm text-gray-500 truncate max-w-xs",children:t.description}),t.tags.length>0&&(0,a.jsxs)("div",{className:"flex flex-wrap gap-1",children:[t.tags.slice(0,2).map((e,s)=>(0,a.jsx)(n.C,{variant:"outline",className:"text-xs",children:e},s)),t.tags.length>2&&(0,a.jsxs)(n.C,{variant:"outline",className:"text-xs",children:["+",t.tags.length-2]})]})]})}},{accessorKey:"status",header:"Status",cell:e=>{let{row:s}=e;return ee(s.getValue("status"))}},{accessorKey:"priority",header:"Priority",cell:e=>{let{row:s}=e;return es(s.getValue("priority"))}},{accessorKey:"assignedTo",header:"Assigned To",cell:e=>{let{row:s}=e,t=s.getValue("assignedTo");return t?(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(k.Z,{className:"h-3 w-3 text-blue-600"})}),(0,a.jsx)("span",{className:"text-sm",children:t.name||t.email})]}):(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"Unassigned"})}},{accessorKey:"dueDate",header:"Due Date",cell:e=>{let{row:s}=e,t=s.getValue("dueDate");if(!t)return(0,a.jsx)("span",{className:"text-gray-400",children:"No due date"});let r=new Date(t),l=r<new Date&&"DONE"!==s.original.status;return(0,a.jsxs)("div",{className:"flex items-center space-x-1 ".concat(l?"text-red-600":""),children:[(0,a.jsx)(j.Z,{className:"h-3 w-3"}),(0,a.jsx)("span",{className:"text-sm",children:r.toLocaleDateString()}),l&&(0,a.jsx)(f.Z,{className:"h-3 w-3"})]})}},{accessorKey:"type",header:"Type",cell:e=>{let{row:s}=e,t=s.getValue("type");return(0,a.jsx)(n.C,{variant:"outline",children:t})}},{id:"actions",header:"Actions",cell:e=>{let{row:s}=e,t=s.original;return(0,a.jsxs)(o.h_,{children:[(0,a.jsx)(o.$F,{asChild:!0,children:(0,a.jsx)(i.z,{variant:"ghost",className:"h-8 w-8 p-0",children:(0,a.jsx)(w.Z,{className:"h-4 w-4"})})}),(0,a.jsxs)(o.AW,{align:"end",children:[(0,a.jsx)(o.Xi,{asChild:!0,children:(0,a.jsxs)(P(),{href:"/dashboard/tasks/".concat(t.id),children:[(0,a.jsx)(E.Z,{className:"h-4 w-4 mr-2"}),"View Details"]})}),(0,a.jsxs)(o.Xi,{onClick:()=>A(t),children:[(0,a.jsx)(T.Z,{className:"h-4 w-4 mr-2"}),"Edit Task"]}),"DONE"!==t.status&&(0,a.jsxs)(o.Xi,{onClick:()=>J(t.id,"DONE"),children:[(0,a.jsx)(p.Z,{className:"h-4 w-4 mr-2"}),"Mark Complete"]}),(0,a.jsxs)(o.Xi,{onClick:()=>X(t.id),className:"text-red-600",children:[(0,a.jsx)(C.Z,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]})}}],data:e,searchPlaceholder:"Search tasks..."})})]}),(h||M)&&(0,a.jsx)(u.t,{task:M,onClose:()=>{b(!1),A(null)},onSuccess:()=>{b(!1),A(null),q()}})]})}},36743:function(e,s,t){"use strict";t.d(s,{f:function(){return n}});var a=t(2265),r=t(9381),l=t(57437),i=a.forwardRef((e,s)=>(0,l.jsx)(r.WV.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));i.displayName="Label";var n=i},5925:function(e,s,t){"use strict";let a,r;t.r(s),t.d(s,{CheckmarkIcon:function(){return K},ErrorIcon:function(){return _},LoaderIcon:function(){return B},ToastBar:function(){return en},ToastIcon:function(){return es},Toaster:function(){return ex},default:function(){return eu},resolveValue:function(){return w},toast:function(){return M},useToaster:function(){return U},useToasterStore:function(){return I}});var l,i=t(2265);let n={data:""},c=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||n,d=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,o=/\/\*[^]*?\*\/|  +/g,x=/\n+/g,u=(e,s)=>{let t="",a="",r="";for(let l in e){let i=e[l];"@"==l[0]?"i"==l[1]?t=l+" "+i+";":a+="f"==l[1]?u(i,l):l+"{"+u(i,"k"==l[1]?"":s)+"}":"object"==typeof i?a+=u(i,s?s.replace(/([^,])+/g,e=>l.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,s=>/&/.test(s)?s.replace(/&/g,e):e?e+" "+s:s)):l):null!=i&&(l=/^--/.test(l)?l:l.replace(/[A-Z]/g,"-$&").toLowerCase(),r+=u.p?u.p(l,i):l+":"+i+";")}return t+(s&&r?s+"{"+r+"}":r)+a},m={},h=e=>{if("object"==typeof e){let s="";for(let t in e)s+=t+h(e[t]);return s}return e},p=(e,s,t,a,r)=>{var l;let i=h(e),n=m[i]||(m[i]=(e=>{let s=0,t=11;for(;s<e.length;)t=101*t+e.charCodeAt(s++)>>>0;return"go"+t})(i));if(!m[n]){let s=i!==e?e:(e=>{let s,t,a=[{}];for(;s=d.exec(e.replace(o,""));)s[4]?a.shift():s[3]?(t=s[3].replace(x," ").trim(),a.unshift(a[0][t]=a[0][t]||{})):a[0][s[1]]=s[2].replace(x," ").trim();return a[0]})(e);m[n]=u(r?{["@keyframes "+n]:s}:s,t?"":"."+n)}let c=t&&m.g?m.g:null;return t&&(m.g=m[n]),l=m[n],c?s.data=s.data.replace(c,l):-1===s.data.indexOf(l)&&(s.data=a?l+s.data:s.data+l),n},y=(e,s,t)=>e.reduce((e,a,r)=>{let l=s[r];if(l&&l.call){let e=l(t),s=e&&e.props&&e.props.className||/^go/.test(e)&&e;l=s?"."+s:e&&"object"==typeof e?e.props?"":u(e,""):!1===e?"":e}return e+a+(null==l?"":l)},"");function f(e){let s=this||{},t=e.call?e(s.p):e;return p(t.unshift?t.raw?y(t,[].slice.call(arguments,1),s.p):t.reduce((e,t)=>Object.assign(e,t&&t.call?t(s.p):t),{}):t,c(s.target),s.g,s.o,s.k)}f.bind({g:1});let g,j,b,N=f.bind({k:1});function v(e,s){let t=this||{};return function(){let a=arguments;function r(l,i){let n=Object.assign({},l),c=n.className||r.className;t.p=Object.assign({theme:j&&j()},n),t.o=/ *go\d+/.test(c),n.className=f.apply(t,a)+(c?" "+c:""),s&&(n.ref=i);let d=e;return e[0]&&(d=n.as||e,delete n.as),b&&d[0]&&b(n),g(d,n)}return s?s(r):r}}var k=e=>"function"==typeof e,w=(e,s)=>k(e)?e(s):e,E=(a=0,()=>(++a).toString()),T=()=>{if(void 0===r&&"u">typeof window){let e=matchMedia("(prefers-reduced-motion: reduce)");r=!e||e.matches}return r},C=(e,s)=>{switch(s.type){case 0:return{...e,toasts:[s.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map(e=>e.id===s.toast.id?{...e,...s.toast}:e)};case 2:let{toast:t}=s;return C(e,{type:e.toasts.find(e=>e.id===t.id)?1:0,toast:t});case 3:let{toastId:a}=s;return{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,dismissed:!0,visible:!1}:e)};case 4:return void 0===s.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==s.toastId)};case 5:return{...e,pausedAt:s.time};case 6:let r=s.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(e=>({...e,pauseDuration:e.pauseDuration+r}))}}},D=[],Z={toasts:[],pausedAt:void 0},O=e=>{Z=C(Z,e),D.forEach(e=>{e(Z)})},S={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},I=(e={})=>{let[s,t]=(0,i.useState)(Z),a=(0,i.useRef)(Z);(0,i.useEffect)(()=>(a.current!==Z&&t(Z),D.push(t),()=>{let e=D.indexOf(t);e>-1&&D.splice(e,1)}),[]);let r=s.toasts.map(s=>{var t,a,r;return{...e,...e[s.type],...s,removeDelay:s.removeDelay||(null==(t=e[s.type])?void 0:t.removeDelay)||(null==e?void 0:e.removeDelay),duration:s.duration||(null==(a=e[s.type])?void 0:a.duration)||(null==e?void 0:e.duration)||S[s.type],style:{...e.style,...null==(r=e[s.type])?void 0:r.style,...s.style}}});return{...s,toasts:r}},P=(e,s="blank",t)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:s,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...t,id:(null==t?void 0:t.id)||E()}),L=e=>(s,t)=>{let a=P(s,e,t);return O({type:2,toast:a}),a.id},M=(e,s)=>L("blank")(e,s);M.error=L("error"),M.success=L("success"),M.loading=L("loading"),M.custom=L("custom"),M.dismiss=e=>{O({type:3,toastId:e})},M.remove=e=>O({type:4,toastId:e}),M.promise=(e,s,t)=>{let a=M.loading(s.loading,{...t,...null==t?void 0:t.loading});return"function"==typeof e&&(e=e()),e.then(e=>{let r=s.success?w(s.success,e):void 0;return r?M.success(r,{id:a,...t,...null==t?void 0:t.success}):M.dismiss(a),e}).catch(e=>{let r=s.error?w(s.error,e):void 0;r?M.error(r,{id:a,...t,...null==t?void 0:t.error}):M.dismiss(a)}),e};var A=(e,s)=>{O({type:1,toast:{id:e,height:s}})},R=()=>{O({type:5,time:Date.now()})},F=new Map,$=1e3,z=(e,s=$)=>{if(F.has(e))return;let t=setTimeout(()=>{F.delete(e),O({type:4,toastId:e})},s);F.set(e,t)},U=e=>{let{toasts:s,pausedAt:t}=I(e);(0,i.useEffect)(()=>{if(t)return;let e=Date.now(),a=s.map(s=>{if(s.duration===1/0)return;let t=(s.duration||0)+s.pauseDuration-(e-s.createdAt);if(t<0){s.visible&&M.dismiss(s.id);return}return setTimeout(()=>M.dismiss(s.id),t)});return()=>{a.forEach(e=>e&&clearTimeout(e))}},[s,t]);let a=(0,i.useCallback)(()=>{t&&O({type:6,time:Date.now()})},[t]),r=(0,i.useCallback)((e,t)=>{let{reverseOrder:a=!1,gutter:r=8,defaultPosition:l}=t||{},i=s.filter(s=>(s.position||l)===(e.position||l)&&s.height),n=i.findIndex(s=>s.id===e.id),c=i.filter((e,s)=>s<n&&e.visible).length;return i.filter(e=>e.visible).slice(...a?[c+1]:[0,c]).reduce((e,s)=>e+(s.height||0)+r,0)},[s]);return(0,i.useEffect)(()=>{s.forEach(e=>{if(e.dismissed)z(e.id,e.removeDelay);else{let s=F.get(e.id);s&&(clearTimeout(s),F.delete(e.id))}})},[s]),{toasts:s,handlers:{updateHeight:A,startPause:R,endPause:a,calculateOffset:r}}},Y=N`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,H=N`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,V=N`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,_=v("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Y} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${H} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${V} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,Q=N`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,B=v("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${Q} 1s linear infinite;
`,G=N`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,W=N`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,K=v("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${G} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${W} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,q=v("div")`
  position: absolute;
`,X=v("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,J=N`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,ee=v("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${J} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,es=({toast:e})=>{let{icon:s,type:t,iconTheme:a}=e;return void 0!==s?"string"==typeof s?i.createElement(ee,null,s):s:"blank"===t?null:i.createElement(X,null,i.createElement(B,{...a}),"loading"!==t&&i.createElement(q,null,"error"===t?i.createElement(_,{...a}):i.createElement(K,{...a})))},et=e=>`
0% {transform: translate3d(0,${-200*e}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,ea=e=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${-150*e}%,-1px) scale(.6); opacity:0;}
`,er=v("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,el=v("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,ei=(e,s)=>{let t=e.includes("top")?1:-1,[a,r]=T()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[et(t),ea(t)];return{animation:s?`${N(a)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${N(r)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},en=i.memo(({toast:e,position:s,style:t,children:a})=>{let r=e.height?ei(e.position||s||"top-center",e.visible):{opacity:0},l=i.createElement(es,{toast:e}),n=i.createElement(el,{...e.ariaProps},w(e.message,e));return i.createElement(er,{className:e.className,style:{...r,...t,...e.style}},"function"==typeof a?a({icon:l,message:n}):i.createElement(i.Fragment,null,l,n))});l=i.createElement,u.p=void 0,g=l,j=void 0,b=void 0;var ec=({id:e,className:s,style:t,onHeightUpdate:a,children:r})=>{let l=i.useCallback(s=>{if(s){let t=()=>{a(e,s.getBoundingClientRect().height)};t(),new MutationObserver(t).observe(s,{subtree:!0,childList:!0,characterData:!0})}},[e,a]);return i.createElement("div",{ref:l,className:s,style:t},r)},ed=(e,s)=>{let t=e.includes("top"),a=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:T()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${s*(t?1:-1)}px)`,...t?{top:0}:{bottom:0},...a}},eo=f`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,ex=({reverseOrder:e,position:s="top-center",toastOptions:t,gutter:a,children:r,containerStyle:l,containerClassName:n})=>{let{toasts:c,handlers:d}=U(t);return i.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...l},className:n,onMouseEnter:d.startPause,onMouseLeave:d.endPause},c.map(t=>{let l=t.position||s,n=ed(l,d.calculateOffset(t,{reverseOrder:e,gutter:a,defaultPosition:s}));return i.createElement(ec,{id:t.id,key:t.id,onHeightUpdate:d.updateHeight,className:t.visible?eo:"",style:n},"custom"===t.type?w(t.message,t):r?r(t):i.createElement(en,{toast:t,position:l}))}))},eu=M}},function(e){e.O(0,[6723,9502,1706,4138,1396,4997,2012,5385,528,548,2971,4938,1744],function(){return e(e.s=14638)}),_N_E=e.O()}]);