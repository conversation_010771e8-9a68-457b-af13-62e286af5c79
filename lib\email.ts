import crypto from 'crypto'

// Email configuration - in production, you'd use a service like SendGrid, Mailgun, etc.
const EMAIL_CONFIG = {
  from: process.env.EMAIL_FROM || '<EMAIL>',
  baseUrl: process.env.NEXTAUTH_URL || 'http://localhost:3000'
}

// Generate a secure random token
export function generateVerificationToken(): string {
  return crypto.randomBytes(32).toString('hex')
}

// Generate password reset token
export function generatePasswordResetToken(): string {
  return crypto.randomBytes(32).toString('hex')
}

// Generate invitation token
export function generateInvitationToken(): string {
  return crypto.randomBytes(32).toString('hex')
}

// Email verification
export async function sendVerificationEmail(
  email: string,
  name: string,
  token: string
): Promise<void> {
  const verificationUrl = `${EMAIL_CONFIG.baseUrl}/auth/verify-email?token=${token}`
  
  const emailContent = {
    to: email,
    from: EMAIL_CONFIG.from,
    subject: 'Verify your Business SaaS account',
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Verify Your Account</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #f8f9fa; padding: 30px; border-radius: 0 0 8px 8px; }
            .button { display: inline-block; background: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
            .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Welcome to Business SaaS!</h1>
            </div>
            <div class="content">
              <h2>Hi ${name},</h2>
              <p>Thank you for signing up for Business SaaS! To complete your registration and start using your account, please verify your email address by clicking the button below:</p>
              
              <div style="text-align: center;">
                <a href="${verificationUrl}" class="button">Verify Email Address</a>
              </div>
              
              <p>If the button doesn't work, you can also copy and paste this link into your browser:</p>
              <p style="word-break: break-all; color: #007bff;">${verificationUrl}</p>
              
              <p><strong>This verification link will expire in 24 hours.</strong></p>
              
              <p>If you didn't create an account with Business SaaS, you can safely ignore this email.</p>
              
              <p>Best regards,<br>The Business SaaS Team</p>
            </div>
            <div class="footer">
              <p>© 2024 Business SaaS. All rights reserved.</p>
            </div>
          </div>
        </body>
      </html>
    `,
    text: `
      Hi ${name},
      
      Thank you for signing up for Business SaaS! To complete your registration, please verify your email address by visiting:
      
      ${verificationUrl}
      
      This verification link will expire in 24 hours.
      
      If you didn't create an account with Business SaaS, you can safely ignore this email.
      
      Best regards,
      The Business SaaS Team
    `
  }

  // In development, just log the email
  if (process.env.NODE_ENV === 'development') {
    console.log('📧 Email would be sent:', emailContent)
    console.log('🔗 Verification URL:', verificationUrl)
    return
  }

  // In production, integrate with your email service
  // Example with SendGrid:
  // const sgMail = require('@sendgrid/mail')
  // sgMail.setApiKey(process.env.SENDGRID_API_KEY)
  // await sgMail.send(emailContent)
  
  // For now, just log in production too
  console.log('📧 Email would be sent to:', email)
}

// Password reset email
export async function sendPasswordResetEmail(
  email: string,
  name: string,
  token: string
): Promise<void> {
  const resetUrl = `${EMAIL_CONFIG.baseUrl}/auth/reset-password?token=${token}`
  
  const emailContent = {
    to: email,
    from: EMAIL_CONFIG.from,
    subject: 'Reset your Business SaaS password',
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Reset Your Password</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #f8f9fa; padding: 30px; border-radius: 0 0 8px 8px; }
            .button { display: inline-block; background: #dc3545; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
            .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Password Reset Request</h1>
            </div>
            <div class="content">
              <h2>Hi ${name},</h2>
              <p>We received a request to reset your password for your Business SaaS account. Click the button below to create a new password:</p>
              
              <div style="text-align: center;">
                <a href="${resetUrl}" class="button">Reset Password</a>
              </div>
              
              <p>If the button doesn't work, you can also copy and paste this link into your browser:</p>
              <p style="word-break: break-all; color: #dc3545;">${resetUrl}</p>
              
              <p><strong>This password reset link will expire in 1 hour.</strong></p>
              
              <p>If you didn't request a password reset, you can safely ignore this email. Your password will remain unchanged.</p>
              
              <p>Best regards,<br>The Business SaaS Team</p>
            </div>
            <div class="footer">
              <p>© 2024 Business SaaS. All rights reserved.</p>
            </div>
          </div>
        </body>
      </html>
    `,
    text: `
      Hi ${name},
      
      We received a request to reset your password for your Business SaaS account. Visit this link to create a new password:
      
      ${resetUrl}
      
      This password reset link will expire in 1 hour.
      
      If you didn't request a password reset, you can safely ignore this email.
      
      Best regards,
      The Business SaaS Team
    `
  }

  // In development, just log the email
  if (process.env.NODE_ENV === 'development') {
    console.log('📧 Password reset email would be sent:', emailContent)
    console.log('🔗 Reset URL:', resetUrl)
    return
  }

  console.log('📧 Password reset email would be sent to:', email)
}

// User invitation email
export async function sendInvitationEmail(
  email: string,
  inviterName: string,
  companyName: string,
  token: string,
  role: string
): Promise<void> {
  const invitationUrl = `${EMAIL_CONFIG.baseUrl}/auth/accept-invitation?token=${token}`
  
  const emailContent = {
    to: email,
    from: EMAIL_CONFIG.from,
    subject: `You're invited to join ${companyName} on Business SaaS`,
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Team Invitation</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #f8f9fa; padding: 30px; border-radius: 0 0 8px 8px; }
            .button { display: inline-block; background: #28a745; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
            .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
            .role-badge { background: #e9ecef; color: #495057; padding: 4px 12px; border-radius: 20px; font-size: 14px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>You're Invited!</h1>
            </div>
            <div class="content">
              <h2>Join ${companyName} on Business SaaS</h2>
              <p><strong>${inviterName}</strong> has invited you to join <strong>${companyName}</strong> as a <span class="role-badge">${role}</span>.</p>
              
              <p>Business SaaS is a comprehensive business management platform that helps teams collaborate, manage customers, track leads, and grow their business.</p>
              
              <div style="text-align: center;">
                <a href="${invitationUrl}" class="button">Accept Invitation</a>
              </div>
              
              <p>If the button doesn't work, you can also copy and paste this link into your browser:</p>
              <p style="word-break: break-all; color: #28a745;">${invitationUrl}</p>
              
              <p><strong>This invitation will expire in 7 days.</strong></p>
              
              <p>If you don't want to join this team, you can safely ignore this email.</p>
              
              <p>Best regards,<br>The Business SaaS Team</p>
            </div>
            <div class="footer">
              <p>© 2024 Business SaaS. All rights reserved.</p>
            </div>
          </div>
        </body>
      </html>
    `,
    text: `
      You're invited to join ${companyName} on Business SaaS!
      
      ${inviterName} has invited you to join ${companyName} as a ${role}.
      
      Accept your invitation by visiting:
      ${invitationUrl}
      
      This invitation will expire in 7 days.
      
      If you don't want to join this team, you can safely ignore this email.
      
      Best regards,
      The Business SaaS Team
    `
  }

  // In development, just log the email
  if (process.env.NODE_ENV === 'development') {
    console.log('📧 Invitation email would be sent:', emailContent)
    console.log('🔗 Invitation URL:', invitationUrl)
    return
  }

  console.log('📧 Invitation email would be sent to:', email)
}
