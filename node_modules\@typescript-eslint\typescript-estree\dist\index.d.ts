export { AST, parse, parseAndGenerateServices, ParseAndGenerateServicesResult, } from './parser';
export { ParserServices, ParserServicesWithTypeInformation, ParserServicesWithoutTypeInformation, TSESTreeOptions, } from './parser-options';
export { simpleTraverse } from './simple-traverse';
export * from './ts-estree';
export { createProgramFromConfigFile as createProgram } from './create-program/useProvidedPrograms';
export * from './create-program/getScriptKind';
export { getCanonicalFileName } from './create-program/shared';
export { typescriptVersionIsAtLeast } from './version-check';
export * from './getModifiers';
export { TSError } from './node-utils';
export * from './clear-caches';
export declare const version: string;
//# sourceMappingURL=index.d.ts.map