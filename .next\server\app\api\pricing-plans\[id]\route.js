"use strict";(()=>{var e={};e.id=574,e.ids=[574],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},41292:(e,r,s)=>{s.r(r),s.d(r,{headerHooks:()=>x,originalPathname:()=>f,patchFetch:()=>I,requestAsyncStorage:()=>y,routeModule:()=>g,serverHooks:()=>w,staticGenerationAsyncStorage:()=>h,staticGenerationBailout:()=>P});var t={};s.r(t),s.d(t,{DELETE:()=>m,GET:()=>d,PUT:()=>p});var i=s(95419),a=s(69108),n=s(99678),o=s(78070),l=s(81355),c=s(3205),u=s(9108);async function d(e,{params:r}){try{let e=await u._.pricingPlan.findUnique({where:{id:r.id},select:{id:!0,name:!0,description:!0,monthlyPrice:!0,yearlyPrice:!0,currency:!0,maxUsers:!0,maxCompanies:!0,maxCustomers:!0,maxQuotations:!0,maxInvoices:!0,maxContracts:!0,maxStorage:!0,features:!0,isActive:!0,isPublic:!0,trialDays:!0,sortOrder:!0,stripeProductId:!0,stripePriceId:!0,stripeYearlyPriceId:!0,createdAt:!0,updatedAt:!0}});if(!e)return o.Z.json({success:!1,error:"Pricing plan not found"},{status:404});return o.Z.json({success:!0,data:{...e,monthlyPrice:Number(e.monthlyPrice),yearlyPrice:e.yearlyPrice?Number(e.yearlyPrice):null,maxStorage:Number(e.maxStorage)}})}catch(e){return console.error("Error fetching pricing plan:",e),o.Z.json({success:!1,error:"Failed to fetch pricing plan"},{status:500})}}async function p(e,{params:r}){try{let s=await (0,l.getServerSession)(c.L);if(!s?.user||"SUPER_ADMIN"!==s.user.role)return o.Z.json({success:!1,error:"Unauthorized"},{status:401});let{name:t,description:i,monthlyPrice:a,yearlyPrice:n,currency:d,maxUsers:p,maxCompanies:m,maxCustomers:g,maxQuotations:y,maxInvoices:h,maxContracts:w,maxStorage:x,features:P,isActive:f,isPublic:I,trialDays:q,stripeProductId:j,stripePriceId:v,stripeYearlyPriceId:E}=await e.json();if(!await u._.pricingPlan.findUnique({where:{id:r.id}}))return o.Z.json({success:!1,error:"Pricing plan not found"},{status:404});let _=await u._.pricingPlan.update({where:{id:r.id},data:{name:t,description:i,monthlyPrice:a,yearlyPrice:n,currency:d,maxUsers:p,maxCompanies:m,maxCustomers:g,maxQuotations:y,maxInvoices:h,maxContracts:w,maxStorage:x?BigInt(x):void 0,features:P,isActive:f,isPublic:I,trialDays:q,stripeProductId:j,stripePriceId:v,stripeYearlyPriceId:E}});return o.Z.json({success:!0,data:{..._,monthlyPrice:Number(_.monthlyPrice),yearlyPrice:_.yearlyPrice?Number(_.yearlyPrice):null,maxStorage:Number(_.maxStorage)}})}catch(e){return console.error("Error updating pricing plan:",e),o.Z.json({success:!1,error:"Failed to update pricing plan"},{status:500})}}async function m(e,{params:r}){try{let e=await (0,l.getServerSession)(c.L);if(!e?.user||"SUPER_ADMIN"!==e.user.role)return o.Z.json({success:!1,error:"Unauthorized"},{status:401});if(!await u._.pricingPlan.findUnique({where:{id:r.id}}))return o.Z.json({success:!1,error:"Pricing plan not found"},{status:404});return await u._.pricingPlan.delete({where:{id:r.id}}),o.Z.json({success:!0,message:"Pricing plan deleted successfully"})}catch(e){return console.error("Error deleting pricing plan:",e),o.Z.json({success:!1,error:"Failed to delete pricing plan"},{status:500})}}let g=new i.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/pricing-plans/[id]/route",pathname:"/api/pricing-plans/[id]",filename:"route",bundlePath:"app/api/pricing-plans/[id]/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\pricing-plans\\[id]\\route.ts",nextConfigOutput:"",userland:t}),{requestAsyncStorage:y,staticGenerationAsyncStorage:h,serverHooks:w,headerHooks:x,staticGenerationBailout:P}=g,f="/api/pricing-plans/[id]/route";function I(){return(0,n.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:h})}},3205:(e,r,s)=>{s.d(r,{L:()=>c});var t=s(86485),i=s(10375),a=s(50694),n=s(6521),o=s.n(n),l=s(9108);let c={providers:[(0,t.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let r=await l._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),s=r?.companyId;if(!s&&r){let e=await l._.company.findFirst({where:{ownerId:r.id},select:{id:!0}});(s=e?.id)&&await l._.user.update({where:{id:r.id},data:{companyId:s}})}if(!r)return console.log("User not found:",e.email),null;if(!r.password)return console.log("User has no password set:",e.email),null;if(!await o().compare(e.password,r.password))return console.log("Invalid password for user:",e.email),null;return await l._.user.update({where:{id:r.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",r.email),{id:r.id,email:r.email,name:r.name,role:r.role,companyId:s}}catch(e){return console.error("Authentication error:",e),null}}}),(0,i.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,a.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(console.log("JWT callback - user data:",{id:r.id,email:r.email,role:r.role,companyId:r.companyId}),e.role=r.role,e.companyId=r.companyId),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role,e.user.companyId=r.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,r,s)=>{s.d(r,{_:()=>i});let t=require("@prisma/client"),i=globalThis.prisma??new t.PrismaClient}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[1638,6206,6521,2455,4520],()=>s(41292));module.exports=t})();