'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { ColumnDef } from '@tanstack/react-table'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { DataTable } from '@/components/ui/data-table'
import { QuotationForm } from '@/components/quotations/quotation-form'
import { QuotationAnalytics } from '@/components/quotations/quotation-analytics'
import { SendQuotationModal } from '@/components/quotations/send-quotation-modal'
import {
  FileText,
  Plus,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Send,
  Download,
  Copy,
  DollarSign,
  Calendar,
  User,
  CheckCircle,
  Clock,
  XCircle,
  BarChart3,
  Mail
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { toast } from 'react-hot-toast'
import Link from 'next/link'

interface Quotation {
  id: string
  quotationNumber: string
  title: string
  description: string | null
  status: 'DRAFT' | 'SENT' | 'VIEWED' | 'ACCEPTED' | 'REJECTED' | 'EXPIRED'
  validUntil: string | null
  createdAt: string
  updatedAt: string
  subtotal: number
  total: number
  customer: {
    id: string
    name: string
    email: string | null
    company: string | null
  }
  lead: {
    id: string
    title: string
    status: string
  } | null
  createdBy: {
    name: string | null
    email: string | null
  }
  items: any[]
  _count: {
    activities: number
  }
}

export default function QuotationsPage() {
  const { data: session } = useSession()
  const [quotations, setQuotations] = useState<Quotation[]>([])
  const [loading, setLoading] = useState(true)
  const [showForm, setShowForm] = useState(false)
  const [editingQuotation, setEditingQuotation] = useState<Quotation | null>(null)
  const [showAnalytics, setShowAnalytics] = useState(false)
  const [showSendModal, setShowSendModal] = useState(false)
  const [sendingQuotation, setSendingQuotation] = useState<Quotation | null>(null)
  const [stats, setStats] = useState({
    total: 0,
    draft: 0,
    sent: 0,
    accepted: 0,
    totalValue: 0
  })

  const fetchQuotations = async () => {
    try {
      const response = await fetch('/api/quotations')
      if (!response.ok) throw new Error('Failed to fetch quotations')

      const data = await response.json()
      setQuotations(data.quotations)

      // Calculate stats
      const total = data.quotations.length
      const draft = data.quotations.filter((q: Quotation) => q.status === 'DRAFT').length
      const sent = data.quotations.filter((q: Quotation) => q.status === 'SENT').length
      const accepted = data.quotations.filter((q: Quotation) => q.status === 'ACCEPTED').length
      const totalValue = data.quotations.reduce((sum: number, q: Quotation) => sum + (q.total || 0), 0)

      setStats({ total, draft, sent, accepted, totalValue })
    } catch (error) {
      toast.error('Failed to load quotations')
      console.error('Error fetching quotations:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchQuotations()
  }, [])

  const handleDelete = async (quotation: Quotation) => {
    if (!confirm(`Are you sure you want to delete quotation "${quotation.quotationNumber}"?`)) return

    try {
      const response = await fetch(`/api/quotations/${quotation.id}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to delete quotation')
      }

      toast.success('Quotation deleted successfully')
      fetchQuotations()
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to delete quotation')
    }
  }

  const handleEdit = (quotation: Quotation) => {
    setEditingQuotation(quotation)
    setShowForm(true)
  }

  const handleFormClose = () => {
    setShowForm(false)
    setEditingQuotation(null)
  }

  const handleSendQuotation = (quotation: Quotation) => {
    setSendingQuotation(quotation)
    setShowSendModal(true)
  }

  const handleDownloadPDF = async (quotationId: string) => {
    try {
      const response = await fetch(`/api/quotations/${quotationId}/pdf`)
      if (!response.ok) {
        throw new Error('Failed to generate PDF')
      }

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `quotation-${quotationId}.html`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)

      toast.success('PDF downloaded successfully')
    } catch (error) {
      toast.error('Failed to download PDF')
      console.error('Error downloading PDF:', error)
    }
  }

  const getStatusBadge = (status: string | null | undefined) => {
    if (!status) return <Badge variant="secondary">Unknown</Badge>

    switch (status) {
      case 'DRAFT':
        return <Badge variant="secondary">Draft</Badge>
      case 'SENT':
        return <Badge variant="info">Sent</Badge>
      case 'VIEWED':
        return <Badge variant="warning">Viewed</Badge>
      case 'ACCEPTED':
        return <Badge variant="success">Accepted</Badge>
      case 'REJECTED':
        return <Badge variant="destructive">Rejected</Badge>
      case 'EXPIRED':
        return <Badge variant="secondary">Expired</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const columns: ColumnDef<Quotation>[] = [
    {
      accessorKey: 'quotationNumber',
      header: 'Quotation',
      cell: ({ row }) => {
        const quotation = row.original
        return (
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
              <FileText className="h-4 w-4 text-yellow-600" />
            </div>
            <div>
              <div className="font-medium">{quotation.quotationNumber}</div>
              <div className="text-sm text-gray-500">{quotation.title}</div>
            </div>
          </div>
        )
      }
    },
    {
      accessorKey: 'customer',
      header: 'Customer',
      cell: ({ row }) => {
        const customer = row.original.customer
        return (
          <div>
            <div className="font-medium">{customer.name}</div>
            {customer.company && (
              <div className="text-sm text-gray-500">{customer.company}</div>
            )}
          </div>
        )
      }
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => getStatusBadge(row.getValue('status'))
    },
    {
      accessorKey: 'total',
      header: 'Amount',
      cell: ({ row }) => {
        const total = row.getValue('total') as number
        return (
          <div className="flex items-center space-x-2">
            <DollarSign className="h-3 w-3 text-green-600" />
            <span className="font-medium">${total.toLocaleString()}</span>
          </div>
        )
      }
    },
    {
      accessorKey: 'validUntil',
      header: 'Valid Until',
      cell: ({ row }) => {
        const date = row.getValue('validUntil') as string
        return date ? (
          <div className="flex items-center space-x-2">
            <Calendar className="h-3 w-3 text-gray-400" />
            <span className="text-sm">{new Date(date).toLocaleDateString()}</span>
          </div>
        ) : (
          <span className="text-gray-400 text-sm">-</span>
        )
      }
    },
    {
      accessorKey: 'createdBy',
      header: 'Created By',
      cell: ({ row }) => {
        const createdBy = row.original.createdBy
        return (
          <div className="flex items-center space-x-2">
            <User className="h-3 w-3 text-gray-400" />
            <span className="text-sm">{createdBy.name || 'Unknown'}</span>
          </div>
        )
      }
    },
    {
      id: 'actions',
      cell: ({ row }) => {
        const quotation = row.original
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem asChild>
                <Link href={`/dashboard/quotations/${quotation.id}`}>
                  <Eye className="mr-2 h-4 w-4" />
                  View Details
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleEdit(quotation)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Copy className="mr-2 h-4 w-4" />
                Duplicate
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => handleSendQuotation(quotation)}>
                <Send className="mr-2 h-4 w-4" />
                Send to Customer
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleDownloadPDF(quotation.id)}>
                <Download className="mr-2 h-4 w-4" />
                Download PDF
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => handleDelete(quotation)}
                className="text-red-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )
      }
    }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Quotations</h1>
          <p className="text-gray-600 mt-1">Create and manage your quotations</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => setShowAnalytics(!showAnalytics)}>
            <BarChart3 className="h-4 w-4 mr-2" />
            Analytics
          </Button>
          <Button onClick={() => setShowForm(true)}>
            <Plus className="h-4 w-4 mr-2" />
            New Quotation
          </Button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Quotations</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">All quotations</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Draft</CardTitle>
            <Clock className="h-4 w-4 text-gray-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.draft}</div>
            <p className="text-xs text-muted-foreground">Draft quotations</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Sent</CardTitle>
            <Send className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.sent}</div>
            <p className="text-xs text-muted-foreground">Sent to customers</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Accepted</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.accepted}</div>
            <p className="text-xs text-muted-foreground">Accepted quotations</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Value</CardTitle>
            <DollarSign className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${stats.totalValue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Total quotation value</p>
          </CardContent>
        </Card>
      </div>

      {/* Analytics */}
      {showAnalytics && (
        <QuotationAnalytics />
      )}

      {/* Quotations Table */}
      <Card>
        <CardHeader>
          <CardTitle>Quotation Management</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <DataTable
              columns={columns}
              data={quotations}
              searchPlaceholder="Search quotations..."
            />
          )}
        </CardContent>
      </Card>

      {/* Quotation Form Modal */}
      <QuotationForm
        isOpen={showForm}
        onClose={handleFormClose}
        onSuccess={fetchQuotations}
        quotation={editingQuotation}
        mode={editingQuotation ? 'edit' : 'create'}
      />

      {/* Send Quotation Modal */}
      <SendQuotationModal
        open={showSendModal}
        quotation={sendingQuotation}
        onClose={() => {
          setShowSendModal(false)
          setSendingQuotation(null)
        }}
        onSuccess={() => {
          setShowSendModal(false)
          setSendingQuotation(null)
          fetchQuotations()
        }}
      />
    </div>
  )
}
