"use strict";(()=>{var e={};e.id=8482,e.ids=[8482],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},51741:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>D,originalPathname:()=>R,patchFetch:()=>O,requestAsyncStorage:()=>f,routeModule:()=>v,serverHooks:()=>g,staticGenerationAsyncStorage:()=>w,staticGenerationBailout:()=>h});var o={};a.r(o),a.d(o,{GET:()=>I,POST:()=>y});var n=a(95419),i=a(69108),r=a(99678),s=a(78070),c=a(81355),d=a(3205),l=a(9108),u=a(25252),p=a(52178);let m=u.Ry({customerData:u.Ry({name:u.Z_().min(1,"Customer name is required"),email:u.Z_().email("Valid email is required"),phone:u.Z_().optional(),company:u.Z_().optional(),address:u.Z_().optional(),city:u.Z_().optional(),state:u.Z_().optional(),zipCode:u.Z_().optional(),country:u.Z_().optional(),website:u.Z_().optional(),industry:u.Z_().optional(),companySize:u.Km(["STARTUP","SMALL","MEDIUM","LARGE","ENTERPRISE"]).optional(),notes:u.Z_().optional()}),conversionData:u.Ry({conversionType:u.Km(["DIRECT","QUOTATION","PROPOSAL","TRIAL","DEMO"]),conversionReason:u.Z_().optional(),conversionValue:u.Rx().optional(),conversionDate:u.Z_().optional(),salesRepId:u.Z_().optional(),conversionNotes:u.Z_().optional(),followUpRequired:u.O7().default(!1),followUpDate:u.Z_().optional()}),createQuotation:u.O7().default(!1),quotationData:u.Ry({title:u.Z_().optional(),description:u.Z_().optional(),validUntil:u.Z_().optional(),items:u.IX(u.Ry({description:u.Z_(),quantity:u.Rx(),unitPrice:u.Rx(),discount:u.Rx().default(0)})).optional()}).optional()});async function y(e,{params:t}){try{let a=await (0,c.getServerSession)(d.L);if(!a?.user?.id||!a?.user?.companyId)return s.Z.json({error:"Unauthorized"},{status:401});let o=await e.json(),n=m.parse(o),i=await l._.lead.findFirst({where:{id:t.id,companyId:a.user.companyId},include:{activities:!0,leadNotes:!0}});if(!i)return s.Z.json({error:"Lead not found"},{status:404});if("CONVERTED"===i.status)return s.Z.json({error:"Lead is already converted"},{status:400});let r=await l._.$transaction(async e=>{let o=await e.customer.create({data:{...n.customerData,companyId:a.user.companyId,createdById:a.user.id,leadId:t.id,source:"LEAD_CONVERSION",status:"ACTIVE"}}),r=await e.leadConversion.create({data:{leadId:t.id,customerId:o.id,companyId:a.user.companyId,conversionType:n.conversionData.conversionType,conversionReason:n.conversionData.conversionReason,conversionValue:n.conversionData.conversionValue,conversionDate:n.conversionData.conversionDate?new Date(n.conversionData.conversionDate):new Date,salesRepId:n.conversionData.salesRepId||a.user.id,conversionNotes:n.conversionData.conversionNotes,followUpRequired:n.conversionData.followUpRequired,followUpDate:n.conversionData.followUpDate?new Date(n.conversionData.followUpDate):null,createdById:a.user.id}}),s=await e.lead.update({where:{id:t.id},data:{status:"CONVERTED",convertedAt:new Date,convertedById:a.user.id,customerId:o.id,updatedAt:new Date}});if(await e.activity.create({data:{type:"NOTE",title:"Lead Converted to Customer",description:`Lead successfully converted to customer: ${o.name}. Conversion type: ${n.conversionData.conversionType}`,status:"COMPLETED",priority:"HIGH",leadId:t.id,companyId:a.user.companyId,createdById:a.user.id,completedAt:new Date}}),i.leadNotes.length>0){let t=i.leadNotes.map(e=>({customerId:o.id,title:`[Transferred from Lead] ${e.title}`,content:e.content,isPrivate:e.isPrivate,companyId:a.user.companyId,createdById:e.createdById,createdAt:e.createdAt}));await e.customerNote.createMany({data:t})}let c=null;if(n.createQuotation&&n.quotationData&&(c=await e.quotation.create({data:{title:n.quotationData.title||`Quotation for ${o.name}`,description:n.quotationData.description||"Generated from lead conversion",customerId:o.id,status:"DRAFT",validUntil:new Date(n.quotationData.validUntil?n.quotationData.validUntil:Date.now()+2592e6),companyId:a.user.companyId,createdById:a.user.id,items:n.quotationData.items?{create:n.quotationData.items.map(e=>({description:e.description,quantity:e.quantity,unitPrice:e.unitPrice,discount:e.discount,total:e.quantity*e.unitPrice*(1-e.discount/100),companyId:a.user.companyId}))}:void 0},include:{items:!0}})).items.length>0){let t=c.items.reduce((e,t)=>e+t.total,0);await e.quotation.update({where:{id:c.id},data:{total:t}})}return n.conversionData.followUpRequired&&n.conversionData.followUpDate&&await e.task.create({data:{title:"Follow up with converted customer",description:`Follow up with ${o.name} after lead conversion`,status:"PENDING",priority:"MEDIUM",dueDate:new Date(n.conversionData.followUpDate),customerId:o.id,assignedToId:n.conversionData.salesRepId||a.user.id,companyId:a.user.companyId,createdById:a.user.id}}),{conversion:r,customer:o,lead:s,quotation:c}});return s.Z.json({message:"Lead converted successfully",conversion:r.conversion,customer:r.customer,quotation:r.quotation},{status:201})}catch(e){if(e instanceof p.jm)return s.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("Error converting lead:",e),s.Z.json({error:"Failed to convert lead"},{status:500})}}async function I(e,{params:t}){try{let e=await (0,c.getServerSession)(d.L);if(!e?.user?.id||!e?.user?.companyId)return s.Z.json({error:"Unauthorized"},{status:401});let a=await l._.lead.findFirst({where:{id:t.id,companyId:e.user.companyId},include:{conversion:{include:{customer:!0,salesRep:{select:{id:!0,name:!0,email:!0}}}},customer:!0}});if(!a)return s.Z.json({error:"Lead not found"},{status:404});let o=function(e){let t=0,a=[],o=Math.min(e.score/100*30,30);t+=o,a.push({factor:"Lead Score",points:o,maxPoints:30,status:e.score>=70?"GOOD":e.score>=40?"FAIR":"POOR"});let n=0;e.email&&(n+=7),e.phone&&(n+=7),e.companyName&&(n+=6),t+=n,a.push({factor:"Contact Information",points:n,maxPoints:20,status:n>=15?"GOOD":n>=10?"FAIR":"POOR"});let i=e.budget&&e.budget>0?25:0;t+=i,a.push({factor:"Budget Qualification",points:i,maxPoints:25,status:i>0?"GOOD":"POOR"});let r=e.timeline?15:0;t+=r,a.push({factor:"Timeline Qualification",points:r,maxPoints:15,status:r>0?"GOOD":"POOR"});let s=Math.min(2*(e._count?.activities||0),10);t+=s,a.push({factor:"Engagement Level",points:s,maxPoints:10,status:s>=8?"GOOD":s>=4?"FAIR":"POOR"});let c=t>=80?"HIGH":t>=60?"MEDIUM":"LOW";return{score:t,maxScore:100,percentage:Math.round(t/100*100),level:c,factors:a,recommendations:function(e,t){let a=[];return e.forEach(e=>{if("POOR"===e.status)switch(e.factor){case"Lead Score":a.push({type:"SCORING",priority:"HIGH",message:"Lead score is low. Focus on engagement and qualification activities.",action:"Improve lead engagement"});break;case"Contact Information":a.push({type:"CONTACT",priority:"HIGH",message:"Missing contact information. Collect email, phone, and company details.",action:"Complete contact information"});break;case"Budget Qualification":a.push({type:"QUALIFICATION",priority:"URGENT",message:"Budget not qualified. Determine budget range and decision-making process.",action:"Qualify budget and authority"});break;case"Timeline Qualification":a.push({type:"QUALIFICATION",priority:"HIGH",message:"Timeline not established. Understand urgency and decision timeline.",action:"Establish timeline and urgency"});break;case"Engagement Level":a.push({type:"ENGAGEMENT",priority:"MEDIUM",message:"Low engagement level. Schedule more touchpoints and activities.",action:"Increase engagement activities"})}}),"HIGH"===t&&a.push({type:"CONVERSION",priority:"URGENT",message:"Lead is ready for conversion. Schedule a closing call or send proposal.",action:"Initiate conversion process"}),a}(a,c)}}(a);return s.Z.json({lead:a,conversionReadiness:o,isConverted:"CONVERTED"===a.status,conversion:a.conversion,customer:a.customer})}catch(e){return console.error("Error fetching conversion data:",e),s.Z.json({error:"Failed to fetch conversion data"},{status:500})}}let v=new n.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/leads/[id]/convert/route",pathname:"/api/leads/[id]/convert",filename:"route",bundlePath:"app/api/leads/[id]/convert/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\leads\\[id]\\convert\\route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:f,staticGenerationAsyncStorage:w,serverHooks:g,headerHooks:D,staticGenerationBailout:h}=v,R="/api/leads/[id]/convert/route";function O(){return(0,r.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:w})}},3205:(e,t,a)=>{a.d(t,{L:()=>d});var o=a(86485),n=a(10375),i=a(50694),r=a(6521),s=a.n(r),c=a(9108);let d={providers:[(0,o.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await c._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),a=t?.companyId;if(!a&&t){let e=await c._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(a=e?.id)&&await c._.user.update({where:{id:t.id},data:{companyId:a}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await s().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await c._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:a}}catch(e){return console.error("Authentication error:",e),null}}}),(0,n.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,i.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,a)=>{a.d(t,{_:()=>n});let o=require("@prisma/client"),n=globalThis.prisma??new o.PrismaClient}};var t=require("../../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),o=t.X(0,[1638,6206,6521,2455,4520,5252],()=>a(51741));module.exports=o})();