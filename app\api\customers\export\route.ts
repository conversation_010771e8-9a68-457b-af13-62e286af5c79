import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET /api/customers/export - Export customers to CSV/JSON
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const format = searchParams.get('format') || 'csv'
    const status = searchParams.get('status')
    const industry = searchParams.get('industry')
    const includeStats = searchParams.get('includeStats') === 'true'

    // Build where clause
    const where: any = {
      companyId: session.user.companyId
    }

    if (status) {
      where.status = status
    }

    if (industry) {
      where.industry = industry
    }

    // Fetch customers
    const customers = await prisma.customer.findMany({
      where,
      include: {
        createdBy: {
          select: { name: true, email: true }
        },
        ...(includeStats && {
          _count: {
            select: {
              leads: true,
              quotations: true,
              invoices: true,
              activities: true,
              contracts: true
            }
          },
          invoices: {
            where: { status: 'PAID' },
            select: { total: true }
          }
        })
      },
      orderBy: { createdAt: 'desc' }
    })

    // Process customer data
    const exportData = customers.map(customer => {
      const baseData = {
        id: customer.id,
        name: customer.name,
        email: customer.email || '',
        phone: customer.phone || '',
        company: customer.company || '',
        address: customer.address || '',
        city: customer.city || '',
        state: customer.state || '',
        country: customer.country || '',
        postalCode: customer.postalCode || '',
        industry: customer.industry || '',
        website: customer.website || '',
        notes: customer.notes || '',
        status: customer.status,
        tags: Array.isArray(customer.tags) ? customer.tags.join(';') : '',
        createdAt: customer.createdAt.toISOString(),
        updatedAt: customer.updatedAt.toISOString(),
        createdBy: customer.createdBy.name || customer.createdBy.email || ''
      }

      if (includeStats && '_count' in customer) {
        const totalRevenue = customer.invoices?.reduce((sum: number, invoice: any) => sum + invoice.total, 0) || 0
        
        return {
          ...baseData,
          totalLeads: customer._count.leads,
          totalQuotations: customer._count.quotations,
          totalInvoices: customer._count.invoices,
          totalContracts: customer._count.contracts,
          totalActivities: customer._count.activities,
          totalRevenue: totalRevenue
        }
      }

      return baseData
    })

    if (format === 'json') {
      return NextResponse.json({
        customers: exportData,
        exportedAt: new Date().toISOString(),
        totalCount: exportData.length,
        filters: { status, industry, includeStats }
      })
    }

    // Generate CSV
    if (exportData.length === 0) {
      return new Response('No customers found for export', {
        status: 404,
        headers: { 'Content-Type': 'text/plain' }
      })
    }

    const headers = Object.keys(exportData[0])
    const csvHeaders = headers.join(',')
    
    const csvRows = exportData.map(customer => 
      headers.map(header => {
        const value = customer[header as keyof typeof customer]
        // Escape quotes and wrap in quotes if contains comma, quote, or newline
        if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
          return `"${value.replace(/"/g, '""')}"`
        }
        return value
      }).join(',')
    )

    const csvContent = [csvHeaders, ...csvRows].join('\n')
    
    const timestamp = new Date().toISOString().split('T')[0]
    const filename = `customers-export-${timestamp}.csv`

    return new Response(csvContent, {
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="${filename}"`
      }
    })

  } catch (error) {
    console.error('Error exporting customers:', error)
    return NextResponse.json(
      { error: 'Failed to export customers' },
      { status: 500 }
    )
  }
}

// POST /api/customers/export - Export specific customers by IDs
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { customerIds, format = 'csv', includeStats = false } = body

    if (!Array.isArray(customerIds) || customerIds.length === 0) {
      return NextResponse.json(
        { error: 'Customer IDs are required' },
        { status: 400 }
      )
    }

    // Fetch specific customers
    const customers = await prisma.customer.findMany({
      where: {
        id: { in: customerIds },
        companyId: session.user.companyId
      },
      include: {
        createdBy: {
          select: { name: true, email: true }
        },
        ...(includeStats && {
          _count: {
            select: {
              leads: true,
              quotations: true,
              invoices: true,
              activities: true,
              contracts: true
            }
          },
          invoices: {
            where: { status: 'PAID' },
            select: { total: true }
          }
        })
      },
      orderBy: { name: 'asc' }
    })

    if (customers.length === 0) {
      return NextResponse.json(
        { error: 'No customers found with provided IDs' },
        { status: 404 }
      )
    }

    // Process customer data (same as GET method)
    const exportData = customers.map(customer => {
      const baseData = {
        id: customer.id,
        name: customer.name,
        email: customer.email || '',
        phone: customer.phone || '',
        company: customer.company || '',
        address: customer.address || '',
        city: customer.city || '',
        state: customer.state || '',
        country: customer.country || '',
        postalCode: customer.postalCode || '',
        industry: customer.industry || '',
        website: customer.website || '',
        notes: customer.notes || '',
        status: customer.status,
        tags: Array.isArray(customer.tags) ? customer.tags.join(';') : '',
        createdAt: customer.createdAt.toISOString(),
        updatedAt: customer.updatedAt.toISOString(),
        createdBy: customer.createdBy.name || customer.createdBy.email || ''
      }

      if (includeStats && '_count' in customer) {
        const totalRevenue = customer.invoices?.reduce((sum: number, invoice: any) => sum + invoice.total, 0) || 0
        
        return {
          ...baseData,
          totalLeads: customer._count.leads,
          totalQuotations: customer._count.quotations,
          totalInvoices: customer._count.invoices,
          totalContracts: customer._count.contracts,
          totalActivities: customer._count.activities,
          totalRevenue: totalRevenue
        }
      }

      return baseData
    })

    if (format === 'json') {
      return NextResponse.json({
        customers: exportData,
        exportedAt: new Date().toISOString(),
        totalCount: exportData.length,
        selectedIds: customerIds
      })
    }

    // Generate CSV
    const headers = Object.keys(exportData[0])
    const csvHeaders = headers.join(',')
    
    const csvRows = exportData.map(customer => 
      headers.map(header => {
        const value = customer[header as keyof typeof customer]
        if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
          return `"${value.replace(/"/g, '""')}"`
        }
        return value
      }).join(',')
    )

    const csvContent = [csvHeaders, ...csvRows].join('\n')
    
    const timestamp = new Date().toISOString().split('T')[0]
    const filename = `customers-selected-export-${timestamp}.csv`

    return new Response(csvContent, {
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="${filename}"`
      }
    })

  } catch (error) {
    console.error('Error exporting selected customers:', error)
    return NextResponse.json(
      { error: 'Failed to export selected customers' },
      { status: 500 }
    )
  }
}
