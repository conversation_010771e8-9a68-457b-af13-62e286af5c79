'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { redirect } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { 
  DollarSign, 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  RefreshCw,
  Users,
  Database,
  CheckCircle,
  XCircle,
  Star
} from 'lucide-react'
import { toast } from 'react-hot-toast'

interface PricingPlan {
  id: string
  name: string
  description: string
  monthlyPrice: number
  yearlyPrice: number | null
  currency: string
  maxUsers: number
  maxCompanies: number
  maxCustomers: number
  maxQuotations: number
  maxInvoices: number
  maxContracts: number
  maxStorage: number
  formattedStorage: string
  features: Record<string, boolean>
  isActive: boolean
  isPublic: boolean
  trialDays: number
  sortOrder: number
  stripeProductId: string | null
  stripePriceId: string | null
  stripeYearlyPriceId: string | null
  createdAt: string
  updatedAt: string
  yearlyDiscount: number
}

export default function PricingPlansManagementPage() {
  const { data: session, status } = useSession()
  const [plans, setPlans] = useState<PricingPlan[]>([])
  const [loading, setLoading] = useState(true)

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (status === 'unauthenticated') {
    redirect('/auth/signin')
  }

  if (session?.user?.role !== 'SUPER_ADMIN') {
    redirect('/dashboard')
  }

  const fetchPlans = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/pricing-plans?includeInactive=true')
      const data = await response.json()
      
      if (data.success) {
        setPlans(data.data)
      } else {
        toast.error('Failed to load pricing plans')
      }
    } catch (error) {
      console.error('Error fetching plans:', error)
      toast.error('Failed to load pricing plans')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchPlans()
  }, [])

  const handleToggleActive = async (planId: string, isActive: boolean) => {
    try {
      const response = await fetch(`/api/pricing-plans/${planId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ isActive })
      })

      const data = await response.json()
      
      if (data.success) {
        toast.success(`Plan ${isActive ? 'activated' : 'deactivated'} successfully`)
        fetchPlans()
      } else {
        toast.error(data.error || 'Failed to update plan')
      }
    } catch (error) {
      console.error('Error updating plan:', error)
      toast.error('Failed to update plan')
    }
  }

  const handleDeletePlan = async (planId: string) => {
    if (!confirm('Are you sure you want to delete this pricing plan? This action cannot be undone.')) {
      return
    }

    try {
      const response = await fetch(`/api/pricing-plans/${planId}`, {
        method: 'DELETE'
      })

      const data = await response.json()
      
      if (data.success) {
        toast.success('Plan deleted successfully')
        fetchPlans()
      } else {
        toast.error(data.error || 'Failed to delete plan')
      }
    } catch (error) {
      console.error('Error deleting plan:', error)
      toast.error('Failed to delete plan')
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0
    }).format(amount)
  }

  const getFeatureCount = (features: Record<string, boolean>) => {
    return Object.values(features).filter(Boolean).length
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <div className="flex items-center space-x-3">
            <DollarSign className="h-8 w-8 text-blue-600" />
            <h1 className="text-3xl font-bold text-gray-900">Pricing Plans Management</h1>
          </div>
          <p className="text-gray-500 mt-1">Manage subscription plans, pricing, and features</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={fetchPlans} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Add Plan
          </Button>
        </div>
      </div>

      {/* Plans Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Total Plans</p>
                <p className="text-2xl font-bold text-gray-900">{plans.length}</p>
              </div>
              <DollarSign className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Active Plans</p>
                <p className="text-2xl font-bold text-green-600">
                  {plans.filter(p => p.isActive).length}
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Public Plans</p>
                <p className="text-2xl font-bold text-purple-600">
                  {plans.filter(p => p.isPublic).length}
                </p>
              </div>
              <Eye className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Plans Table */}
      <Card>
        <CardHeader>
          <CardTitle>Pricing Plans ({plans.length})</CardTitle>
          <CardDescription>
            Manage your subscription plans, pricing, and features
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Plan</TableHead>
                    <TableHead>Pricing</TableHead>
                    <TableHead>Limits</TableHead>
                    <TableHead>Features</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {plans.map((plan) => (
                    <TableRow key={plan.id}>
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold">
                            {plan.name.charAt(0)}
                          </div>
                          <div>
                            <div className="flex items-center space-x-2">
                              <p className="font-medium text-gray-900">{plan.name}</p>
                              {plan.name.toLowerCase() === 'pro' && (
                                <Star className="h-4 w-4 text-yellow-500" />
                              )}
                            </div>
                            <p className="text-sm text-gray-500">{plan.description}</p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <p className="font-medium">{formatCurrency(plan.monthlyPrice)}/month</p>
                          {plan.yearlyPrice && (
                            <p className="text-xs text-gray-500">
                              {formatCurrency(plan.yearlyPrice)}/year ({plan.yearlyDiscount}% off)
                            </p>
                          )}
                          {plan.trialDays > 0 && (
                            <Badge variant="secondary" className="text-xs mt-1">
                              {plan.trialDays}-day trial
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm space-y-1">
                          <div className="flex items-center space-x-1">
                            <Users className="h-3 w-3 text-gray-400" />
                            <span>{plan.maxUsers} users</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Database className="h-3 w-3 text-gray-400" />
                            <span>{plan.formattedStorage}</span>
                          </div>
                          <p className="text-xs text-gray-500">
                            {plan.maxCustomers} customers, {plan.maxQuotations} quotes
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {getFeatureCount(plan.features)} features
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-2">
                          <div className="flex items-center space-x-2">
                            <Switch
                              checked={plan.isActive}
                              onCheckedChange={(checked) => handleToggleActive(plan.id, checked)}
                            />
                            <span className="text-sm">
                              {plan.isActive ? 'Active' : 'Inactive'}
                            </span>
                          </div>
                          {plan.isPublic && (
                            <Badge variant="secondary" className="text-xs">
                              Public
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Button variant="ghost" size="sm">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => handleDeletePlan(plan.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
