'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { redirect } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { 
  DollarSign, 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  RefreshCw,
  Users,
  Database,
  CheckCircle,
  XCircle,
  Star
} from 'lucide-react'
import { toast } from 'react-hot-toast'
import { DialogFooter } from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

interface PricingPlan {
  id: string
  name: string
  description: string
  monthlyPrice: number
  yearlyPrice: number | null
  currency: string
  maxUsers: number
  maxCompanies: number
  maxCustomers: number
  maxQuotations: number
  maxInvoices: number
  maxContracts: number
  maxStorage: number
  formattedStorage: string
  features: Record<string, boolean>
  isActive: boolean
  isPublic: boolean
  trialDays: number
  sortOrder: number
  stripeProductId: string | null
  stripePriceId: string | null
  stripeYearlyPriceId: string | null
  createdAt: string
  updatedAt: string
  yearlyDiscount: number
}

interface PlanFormProps {
  formData: any
  setFormData: (data: any) => void
  onSubmit: () => void
  onCancel: () => void
  submitLabel: string
}

function PlanForm({ formData, setFormData, onSubmit, onCancel, submitLabel }: PlanFormProps) {
  const formatStorage = (bytes: number) => {
    const gb = bytes / (1024 * 1024 * 1024)
    return gb.toString()
  }

  const parseStorage = (gb: string) => {
    const num = parseFloat(gb) || 1
    return num * 1024 * 1024 * 1024
  }

  return (
    <div className="space-y-6">
      {/* Basic Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name">Plan Name</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            placeholder="e.g., Pro Plan"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="currency">Currency</Label>
          <Select value={formData.currency} onValueChange={(value) => setFormData(prev => ({ ...prev, currency: value }))}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="USD">USD</SelectItem>
              <SelectItem value="EUR">EUR</SelectItem>
              <SelectItem value="GBP">GBP</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
          placeholder="Brief description of the plan"
          rows={3}
        />
      </div>

      {/* Pricing */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="space-y-2">
          <Label htmlFor="monthlyPrice">Monthly Price</Label>
          <Input
            id="monthlyPrice"
            type="number"
            value={formData.monthlyPrice}
            onChange={(e) => setFormData(prev => ({ ...prev, monthlyPrice: parseFloat(e.target.value) || 0 }))}
            placeholder="0.00"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="yearlyPrice">Yearly Price</Label>
          <Input
            id="yearlyPrice"
            type="number"
            value={formData.yearlyPrice}
            onChange={(e) => setFormData(prev => ({ ...prev, yearlyPrice: parseFloat(e.target.value) || 0 }))}
            placeholder="0.00"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="trialDays">Trial Days</Label>
          <Input
            id="trialDays"
            type="number"
            value={formData.trialDays}
            onChange={(e) => setFormData(prev => ({ ...prev, trialDays: parseInt(e.target.value) || 0 }))}
            placeholder="0"
          />
        </div>
      </div>

      {/* Limits */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Usage Limits</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label htmlFor="maxUsers">Max Users</Label>
            <Input
              id="maxUsers"
              type="number"
              value={formData.maxUsers}
              onChange={(e) => setFormData(prev => ({ ...prev, maxUsers: parseInt(e.target.value) || 1 }))}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="maxCompanies">Max Companies</Label>
            <Input
              id="maxCompanies"
              type="number"
              value={formData.maxCompanies}
              onChange={(e) => setFormData(prev => ({ ...prev, maxCompanies: parseInt(e.target.value) || 1 }))}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="maxCustomers">Max Customers</Label>
            <Input
              id="maxCustomers"
              type="number"
              value={formData.maxCustomers}
              onChange={(e) => setFormData(prev => ({ ...prev, maxCustomers: parseInt(e.target.value) || 10 }))}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="maxQuotations">Max Quotations</Label>
            <Input
              id="maxQuotations"
              type="number"
              value={formData.maxQuotations}
              onChange={(e) => setFormData(prev => ({ ...prev, maxQuotations: parseInt(e.target.value) || 5 }))}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="maxInvoices">Max Invoices</Label>
            <Input
              id="maxInvoices"
              type="number"
              value={formData.maxInvoices}
              onChange={(e) => setFormData(prev => ({ ...prev, maxInvoices: parseInt(e.target.value) || 5 }))}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="maxContracts">Max Contracts</Label>
            <Input
              id="maxContracts"
              type="number"
              value={formData.maxContracts}
              onChange={(e) => setFormData(prev => ({ ...prev, maxContracts: parseInt(e.target.value) || 1 }))}
            />
          </div>
        </div>
        <div className="space-y-2">
          <Label htmlFor="maxStorage">Max Storage (GB)</Label>
          <Input
            id="maxStorage"
            type="number"
            value={formatStorage(formData.maxStorage)}
            onChange={(e) => setFormData(prev => ({ ...prev, maxStorage: parseStorage(e.target.value) }))}
            placeholder="1"
          />
        </div>
      </div>

      {/* Features */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Features</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Object.entries(formData.features).map(([key, value]) => (
            <div key={key} className="flex items-center space-x-2">
              <Checkbox
                id={key}
                checked={value}
                onCheckedChange={(checked) =>
                  setFormData(prev => ({
                    ...prev,
                    features: { ...prev.features, [key]: checked }
                  }))
                }
              />
              <Label htmlFor={key} className="text-sm">
                {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
              </Label>
            </div>
          ))}
        </div>
      </div>

      {/* Settings */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Settings</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="isActive"
              checked={formData.isActive}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked }))}
            />
            <Label htmlFor="isActive">Active</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="isPublic"
              checked={formData.isPublic}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isPublic: checked }))}
            />
            <Label htmlFor="isPublic">Public</Label>
          </div>
          <div className="space-y-2">
            <Label htmlFor="sortOrder">Sort Order</Label>
            <Input
              id="sortOrder"
              type="number"
              value={formData.sortOrder}
              onChange={(e) => setFormData(prev => ({ ...prev, sortOrder: parseInt(e.target.value) || 0 }))}
            />
          </div>
        </div>
      </div>

      <DialogFooter>
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button onClick={onSubmit} disabled={!formData.name.trim()}>
          {submitLabel}
        </Button>
      </DialogFooter>
    </div>
  )
}

export default function PricingPlansManagementPage() {
  const { data: session, status } = useSession()
  const [plans, setPlans] = useState<PricingPlan[]>([])
  const [loading, setLoading] = useState(true)
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [editingPlan, setEditingPlan] = useState<PricingPlan | null>(null)
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    monthlyPrice: 0,
    yearlyPrice: 0,
    currency: 'USD',
    maxUsers: 1,
    maxCompanies: 1,
    maxCustomers: 10,
    maxQuotations: 5,
    maxInvoices: 5,
    maxContracts: 1,
    maxStorage: 1073741824, // 1GB in bytes
    isActive: true,
    isPublic: true,
    trialDays: 0,
    sortOrder: 0,
    features: {
      basicReporting: true,
      emailSupport: true,
      mobileApp: false,
      advancedAnalytics: false,
      customBranding: false,
      apiAccess: false,
      prioritySupport: false,
      customIntegrations: false,
      advancedSecurity: false,
      dedicatedManager: false
    }
  })

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (status === 'unauthenticated') {
    redirect('/auth/signin')
  }

  if (session?.user?.role !== 'SUPER_ADMIN') {
    redirect('/dashboard')
  }

  const fetchPlans = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/pricing-plans?includeInactive=true')

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      console.log('Fetched plans data:', data) // Debug log

      if (data.success && data.data) {
        setPlans(data.data)
        toast.success(`Loaded ${data.data.length} pricing plans`)
      } else {
        console.error('API response error:', data)
        toast.error(data.error || 'Failed to load pricing plans')
      }
    } catch (error) {
      console.error('Error fetching plans:', error)
      toast.error('Failed to load pricing plans')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchPlans()
  }, [])

  const handleToggleActive = async (planId: string, isActive: boolean) => {
    try {
      const response = await fetch(`/api/pricing-plans/${planId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ isActive })
      })

      const data = await response.json()
      
      if (data.success) {
        toast.success(`Plan ${isActive ? 'activated' : 'deactivated'} successfully`)
        fetchPlans()
      } else {
        toast.error(data.error || 'Failed to update plan')
      }
    } catch (error) {
      console.error('Error updating plan:', error)
      toast.error('Failed to update plan')
    }
  }

  const handleCreatePlan = async () => {
    try {
      const response = await fetch('/api/pricing-plans', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Plan created successfully')
        setShowCreateDialog(false)
        resetForm()
        fetchPlans()
      } else {
        toast.error(data.error || 'Failed to create plan')
      }
    } catch (error) {
      console.error('Error creating plan:', error)
      toast.error('Failed to create plan')
    }
  }

  const handleEditPlan = (plan: PricingPlan) => {
    setEditingPlan(plan)
    setFormData({
      name: plan.name,
      description: plan.description,
      monthlyPrice: plan.monthlyPrice,
      yearlyPrice: plan.yearlyPrice || 0,
      currency: plan.currency,
      maxUsers: plan.maxUsers,
      maxCompanies: plan.maxCompanies,
      maxCustomers: plan.maxCustomers,
      maxQuotations: plan.maxQuotations,
      maxInvoices: plan.maxInvoices,
      maxContracts: plan.maxContracts,
      maxStorage: plan.maxStorage,
      isActive: plan.isActive,
      isPublic: plan.isPublic,
      trialDays: plan.trialDays,
      sortOrder: plan.sortOrder,
      features: plan.features
    })
    setShowEditDialog(true)
  }

  const handleUpdatePlan = async () => {
    if (!editingPlan) return

    try {
      const response = await fetch(`/api/pricing-plans/${editingPlan.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Plan updated successfully')
        setShowEditDialog(false)
        setEditingPlan(null)
        resetForm()
        fetchPlans()
      } else {
        toast.error(data.error || 'Failed to update plan')
      }
    } catch (error) {
      console.error('Error updating plan:', error)
      toast.error('Failed to update plan')
    }
  }

  const handleDeletePlan = async (planId: string) => {
    if (!confirm('Are you sure you want to delete this pricing plan? This action cannot be undone.')) {
      return
    }

    try {
      const response = await fetch(`/api/pricing-plans/${planId}`, {
        method: 'DELETE'
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Plan deleted successfully')
        fetchPlans()
      } else {
        toast.error(data.error || 'Failed to delete plan')
      }
    } catch (error) {
      console.error('Error deleting plan:', error)
      toast.error('Failed to delete plan')
    }
  }

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      monthlyPrice: 0,
      yearlyPrice: 0,
      currency: 'USD',
      maxUsers: 1,
      maxCompanies: 1,
      maxCustomers: 10,
      maxQuotations: 5,
      maxInvoices: 5,
      maxContracts: 1,
      maxStorage: 1073741824,
      isActive: true,
      isPublic: true,
      trialDays: 0,
      sortOrder: 0,
      features: {
        basicReporting: true,
        emailSupport: true,
        mobileApp: false,
        advancedAnalytics: false,
        customBranding: false,
        apiAccess: false,
        prioritySupport: false,
        customIntegrations: false,
        advancedSecurity: false,
        dedicatedManager: false
      }
    })
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0
    }).format(amount)
  }

  const getFeatureCount = (features: Record<string, boolean>) => {
    return Object.values(features).filter(Boolean).length
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <div className="flex items-center space-x-3">
            <DollarSign className="h-8 w-8 text-blue-600" />
            <h1 className="text-3xl font-bold text-gray-900">Pricing Plans Management</h1>
          </div>
          <p className="text-gray-500 mt-1">Manage subscription plans, pricing, and features</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={fetchPlans} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button onClick={() => setShowCreateDialog(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add Plan
          </Button>
        </div>
      </div>

      {/* Plans Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Total Plans</p>
                <p className="text-2xl font-bold text-gray-900">{plans.length}</p>
              </div>
              <DollarSign className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Active Plans</p>
                <p className="text-2xl font-bold text-green-600">
                  {plans.filter(p => p.isActive).length}
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Public Plans</p>
                <p className="text-2xl font-bold text-purple-600">
                  {plans.filter(p => p.isPublic).length}
                </p>
              </div>
              <Eye className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Plans Table */}
      <Card>
        <CardHeader>
          <CardTitle>Pricing Plans ({plans.length})</CardTitle>
          <CardDescription>
            Manage your subscription plans, pricing, and features
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Plan</TableHead>
                    <TableHead>Pricing</TableHead>
                    <TableHead>Limits</TableHead>
                    <TableHead>Features</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {plans.map((plan) => (
                    <TableRow key={plan.id}>
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold">
                            {plan.name.charAt(0)}
                          </div>
                          <div>
                            <div className="flex items-center space-x-2">
                              <p className="font-medium text-gray-900">{plan.name}</p>
                              {plan.name.toLowerCase() === 'pro' && (
                                <Star className="h-4 w-4 text-yellow-500" />
                              )}
                            </div>
                            <p className="text-sm text-gray-500">{plan.description}</p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <p className="font-medium">{formatCurrency(plan.monthlyPrice)}/month</p>
                          {plan.yearlyPrice && (
                            <p className="text-xs text-gray-500">
                              {formatCurrency(plan.yearlyPrice)}/year ({plan.yearlyDiscount}% off)
                            </p>
                          )}
                          {plan.trialDays > 0 && (
                            <Badge variant="secondary" className="text-xs mt-1">
                              {plan.trialDays}-day trial
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm space-y-1">
                          <div className="flex items-center space-x-1">
                            <Users className="h-3 w-3 text-gray-400" />
                            <span>{plan.maxUsers} users</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Database className="h-3 w-3 text-gray-400" />
                            <span>{plan.formattedStorage}</span>
                          </div>
                          <p className="text-xs text-gray-500">
                            {plan.maxCustomers} customers, {plan.maxQuotations} quotes
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {getFeatureCount(plan.features)} features
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-2">
                          <div className="flex items-center space-x-2">
                            <Switch
                              checked={plan.isActive}
                              onCheckedChange={(checked) => handleToggleActive(plan.id, checked)}
                            />
                            <span className="text-sm">
                              {plan.isActive ? 'Active' : 'Inactive'}
                            </span>
                          </div>
                          {plan.isPublic && (
                            <Badge variant="secondary" className="text-xs">
                              Public
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditPlan(plan)}
                            title="Edit plan"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeletePlan(plan.id)}
                            title="Delete plan"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create Plan Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Pricing Plan</DialogTitle>
            <DialogDescription>
              Add a new subscription plan with custom pricing and features.
            </DialogDescription>
          </DialogHeader>
          <PlanForm
            formData={formData}
            setFormData={setFormData}
            onSubmit={handleCreatePlan}
            onCancel={() => {
              setShowCreateDialog(false)
              resetForm()
            }}
            submitLabel="Create Plan"
          />
        </DialogContent>
      </Dialog>

      {/* Edit Plan Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Pricing Plan</DialogTitle>
            <DialogDescription>
              Modify the pricing plan details and features.
            </DialogDescription>
          </DialogHeader>
          <PlanForm
            formData={formData}
            setFormData={setFormData}
            onSubmit={handleUpdatePlan}
            onCancel={() => {
              setShowEditDialog(false)
              setEditingPlan(null)
              resetForm()
            }}
            submitLabel="Update Plan"
          />
        </DialogContent>
      </Dialog>
    </div>
  )
}
