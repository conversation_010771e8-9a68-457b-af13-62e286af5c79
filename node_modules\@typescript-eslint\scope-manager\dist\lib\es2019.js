"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib @typescript-eslint/repo-tools
Object.defineProperty(exports, "__esModule", { value: true });
exports.es2019 = void 0;
const es2018_1 = require("./es2018");
const es2019_array_1 = require("./es2019.array");
const es2019_intl_1 = require("./es2019.intl");
const es2019_object_1 = require("./es2019.object");
const es2019_string_1 = require("./es2019.string");
const es2019_symbol_1 = require("./es2019.symbol");
exports.es2019 = {
    ...es2018_1.es2018,
    ...es2019_array_1.es2019_array,
    ...es2019_object_1.es2019_object,
    ...es2019_string_1.es2019_string,
    ...es2019_symbol_1.es2019_symbol,
    ...es2019_intl_1.es2019_intl,
};
//# sourceMappingURL=es2019.js.map