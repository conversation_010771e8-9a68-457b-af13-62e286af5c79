"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4522],{85606:function(e,n,t){t.d(n,{z:function(){return i}});var r=t(2265),o=t(42210),a=t(51030),i=e=>{let n,t;let{present:i,children:l}=e,s=function(e){var n,t;let[o,i]=r.useState(),l=r.useRef(null),s=r.useRef(e),c=r.useRef("none"),[d,f]=(n=e?"mounted":"unmounted",t={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,n)=>t[e][n]??e,n));return r.useEffect(()=>{let e=u(l.current);c.current="mounted"===d?e:"none"},[d]),(0,a.b)(()=>{let n=l.current,t=s.current;if(t!==e){let r=c.current,o=u(n);e?f("MOUNT"):"none"===o||n?.display==="none"?f("UNMOUNT"):t&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),s.current=e}},[e,f]),(0,a.b)(()=>{if(o){let e;let n=o.ownerDocument.defaultView??window,t=t=>{let r=u(l.current).includes(t.animationName);if(t.target===o&&r&&(f("ANIMATION_END"),!s.current)){let t=o.style.animationFillMode;o.style.animationFillMode="forwards",e=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=t)})}},r=e=>{e.target===o&&(c.current=u(l.current))};return o.addEventListener("animationstart",r),o.addEventListener("animationcancel",t),o.addEventListener("animationend",t),()=>{n.clearTimeout(e),o.removeEventListener("animationstart",r),o.removeEventListener("animationcancel",t),o.removeEventListener("animationend",t)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{l.current=e?getComputedStyle(e):null,i(e)},[])}}(i),c="function"==typeof l?l({present:s.isPresent}):r.Children.only(l),d=(0,o.e)(s.ref,(n=Object.getOwnPropertyDescriptor(c.props,"ref")?.get)&&"isReactWarning"in n&&n.isReactWarning?c.ref:(n=Object.getOwnPropertyDescriptor(c,"ref")?.get)&&"isReactWarning"in n&&n.isReactWarning?c.props.ref:c.props.ref||c.ref);return"function"==typeof l||s.isPresent?r.cloneElement(c,{ref:d}):null};function u(e){return e?.animationName||"none"}i.displayName="Presence"},44356:function(e,n,t){t.d(n,{Pc:function(){return M},ck:function(){return C},fC:function(){return F}});var r=t(2265),o=t(85744),a=t(27733),i=t(42210),u=t(56989),l=t(20966),s=t(9381),c=t(16459),d=t(73763),f=t(65400),m=t(57437),p="rovingFocusGroup.onEntryFocus",v={bubbles:!1,cancelable:!0},b="RovingFocusGroup",[w,g,y]=(0,a.B)(b),[h,M]=(0,u.b)(b,[y]),[N,T]=h(b),I=r.forwardRef((e,n)=>(0,m.jsx)(w.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,m.jsx)(w.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,m.jsx)(R,{...e,ref:n})})}));I.displayName=b;var R=r.forwardRef((e,n)=>{let{__scopeRovingFocusGroup:t,orientation:a,loop:u=!1,dir:l,currentTabStopId:w,defaultCurrentTabStopId:y,onCurrentTabStopIdChange:h,onEntryFocus:M,preventScrollOnEntryFocus:T=!1,...I}=e,R=r.useRef(null),A=(0,i.e)(n,R),E=(0,f.gm)(l),[x,F]=(0,d.T)({prop:w,defaultProp:y??null,onChange:h,caller:b}),[C,O]=r.useState(!1),j=(0,c.W)(M),P=g(t),k=r.useRef(!1),[S,U]=r.useState(0);return r.useEffect(()=>{let e=R.current;if(e)return e.addEventListener(p,j),()=>e.removeEventListener(p,j)},[j]),(0,m.jsx)(N,{scope:t,orientation:a,dir:E,loop:u,currentTabStopId:x,onItemFocus:r.useCallback(e=>F(e),[F]),onItemShiftTab:r.useCallback(()=>O(!0),[]),onFocusableItemAdd:r.useCallback(()=>U(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>U(e=>e-1),[]),children:(0,m.jsx)(s.WV.div,{tabIndex:C||0===S?-1:0,"data-orientation":a,...I,ref:A,style:{outline:"none",...e.style},onMouseDown:(0,o.M)(e.onMouseDown,()=>{k.current=!0}),onFocus:(0,o.M)(e.onFocus,e=>{let n=!k.current;if(e.target===e.currentTarget&&n&&!C){let n=new CustomEvent(p,v);if(e.currentTarget.dispatchEvent(n),!n.defaultPrevented){let e=P().filter(e=>e.focusable);D([e.find(e=>e.active),e.find(e=>e.id===x),...e].filter(Boolean).map(e=>e.ref.current),T)}}k.current=!1}),onBlur:(0,o.M)(e.onBlur,()=>O(!1))})})}),A="RovingFocusGroupItem",E=r.forwardRef((e,n)=>{let{__scopeRovingFocusGroup:t,focusable:a=!0,active:i=!1,tabStopId:u,children:c,...d}=e,f=(0,l.M)(),p=u||f,v=T(A,t),b=v.currentTabStopId===p,y=g(t),{onFocusableItemAdd:h,onFocusableItemRemove:M,currentTabStopId:N}=v;return r.useEffect(()=>{if(a)return h(),()=>M()},[a,h,M]),(0,m.jsx)(w.ItemSlot,{scope:t,id:p,focusable:a,active:i,children:(0,m.jsx)(s.WV.span,{tabIndex:b?0:-1,"data-orientation":v.orientation,...d,ref:n,onMouseDown:(0,o.M)(e.onMouseDown,e=>{a?v.onItemFocus(p):e.preventDefault()}),onFocus:(0,o.M)(e.onFocus,()=>v.onItemFocus(p)),onKeyDown:(0,o.M)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){v.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let n=function(e,n,t){var r;let o=(r=e.key,"rtl"!==t?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===n&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===n&&["ArrowUp","ArrowDown"].includes(o)))return x[o]}(e,v.orientation,v.dir);if(void 0!==n){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let o=y().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===n)o.reverse();else if("prev"===n||"next"===n){var t,r;"prev"===n&&o.reverse();let a=o.indexOf(e.currentTarget);o=v.loop?(t=o,r=a+1,t.map((e,n)=>t[(r+n)%t.length])):o.slice(a+1)}setTimeout(()=>D(o))}}),children:"function"==typeof c?c({isCurrentTabStop:b,hasTabStop:null!=N}):c})})});E.displayName=A;var x={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function D(e,n=!1){let t=document.activeElement;for(let r of e)if(r===t||(r.focus({preventScroll:n}),document.activeElement!==t))return}var F=I,C=E},34522:function(e,n,t){t.d(n,{VY:function(){return C},aV:function(){return D},fC:function(){return x},xz:function(){return F}});var r=t(2265),o=t(85744),a=t(56989),i=t(44356),u=t(85606),l=t(9381),s=t(65400),c=t(73763),d=t(20966),f=t(57437),m="Tabs",[p,v]=(0,a.b)(m,[i.Pc]),b=(0,i.Pc)(),[w,g]=p(m),y=r.forwardRef((e,n)=>{let{__scopeTabs:t,value:r,onValueChange:o,defaultValue:a,orientation:i="horizontal",dir:u,activationMode:p="automatic",...v}=e,b=(0,s.gm)(u),[g,y]=(0,c.T)({prop:r,onChange:o,defaultProp:a??"",caller:m});return(0,f.jsx)(w,{scope:t,baseId:(0,d.M)(),value:g,onValueChange:y,orientation:i,dir:b,activationMode:p,children:(0,f.jsx)(l.WV.div,{dir:b,"data-orientation":i,...v,ref:n})})});y.displayName=m;var h="TabsList",M=r.forwardRef((e,n)=>{let{__scopeTabs:t,loop:r=!0,...o}=e,a=g(h,t),u=b(t);return(0,f.jsx)(i.fC,{asChild:!0,...u,orientation:a.orientation,dir:a.dir,loop:r,children:(0,f.jsx)(l.WV.div,{role:"tablist","aria-orientation":a.orientation,...o,ref:n})})});M.displayName=h;var N="TabsTrigger",T=r.forwardRef((e,n)=>{let{__scopeTabs:t,value:r,disabled:a=!1,...u}=e,s=g(N,t),c=b(t),d=A(s.baseId,r),m=E(s.baseId,r),p=r===s.value;return(0,f.jsx)(i.ck,{asChild:!0,...c,focusable:!a,active:p,children:(0,f.jsx)(l.WV.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":m,"data-state":p?"active":"inactive","data-disabled":a?"":void 0,disabled:a,id:d,...u,ref:n,onMouseDown:(0,o.M)(e.onMouseDown,e=>{a||0!==e.button||!1!==e.ctrlKey?e.preventDefault():s.onValueChange(r)}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&s.onValueChange(r)}),onFocus:(0,o.M)(e.onFocus,()=>{let e="manual"!==s.activationMode;p||a||!e||s.onValueChange(r)})})})});T.displayName=N;var I="TabsContent",R=r.forwardRef((e,n)=>{let{__scopeTabs:t,value:o,forceMount:a,children:i,...s}=e,c=g(I,t),d=A(c.baseId,o),m=E(c.baseId,o),p=o===c.value,v=r.useRef(p);return r.useEffect(()=>{let e=requestAnimationFrame(()=>v.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,f.jsx)(u.z,{present:a||p,children:({present:t})=>(0,f.jsx)(l.WV.div,{"data-state":p?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":d,hidden:!t,id:m,tabIndex:0,...s,ref:n,style:{...e.style,animationDuration:v.current?"0s":void 0},children:t&&i})})});function A(e,n){return`${e}-trigger-${n}`}function E(e,n){return`${e}-content-${n}`}R.displayName=I;var x=y,D=M,F=T,C=R}}]);