"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9187],{90998:function(e,r,t){t.d(r,{Z:function(){return n}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,t(62898).Z)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]])},72894:function(e,r,t){t.d(r,{Z:function(){return n}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,t(62898).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},92457:function(e,r,t){t.d(r,{Z:function(){return n}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,t(62898).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},13008:function(e,r,t){t.d(r,{Z:function(){return n}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,t(62898).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},469:function(e,r,t){t.d(r,{Z:function(){return n}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,t(62898).Z)("HardDrive",[["line",{x1:"22",x2:"2",y1:"12",y2:"12",key:"1y58io"}],["path",{d:"M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z",key:"oot6mr"}],["line",{x1:"6",x2:"6.01",y1:"16",y2:"16",key:"sgf278"}],["line",{x1:"10",x2:"10.01",y1:"16",y2:"16",key:"1l4acy"}]])},64280:function(e,r,t){t.d(r,{Z:function(){return n}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,t(62898).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},31478:function(e,r,t){t.d(r,{C:function(){return l}});var n=t(57437);t(2265);var a=t(96061),o=t(1657);let i=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600",info:"border-transparent bg-blue-500 text-white hover:bg-blue-600"}},defaultVariants:{variant:"default"}});function l(e){let{className:r,variant:t,...a}=e;return(0,n.jsx)("div",{className:(0,o.cn)(i({variant:t}),r),...a})}},85754:function(e,r,t){t.d(r,{z:function(){return d}});var n=t(57437),a=t(2265),o=t(67256),i=t(96061),l=t(1657);let s=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,r)=>{let{className:t,variant:a,size:i,asChild:d=!1,...u}=e,f=d?o.g7:"button";return(0,n.jsx)(f,{className:(0,l.cn)(s({variant:a,size:i,className:t})),ref:r,...u})});d.displayName="Button"},27815:function(e,r,t){t.d(r,{Ol:function(){return l},SZ:function(){return d},Zb:function(){return i},aY:function(){return u},eW:function(){return f},ll:function(){return s}});var n=t(57437),a=t(2265),o=t(1657);let i=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("div",{ref:r,className:(0,o.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...a})});i.displayName="Card";let l=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("div",{ref:r,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",t),...a})});l.displayName="CardHeader";let s=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("h3",{ref:r,className:(0,o.cn)("text-2xl font-semibold leading-none tracking-tight",t),...a})});s.displayName="CardTitle";let d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("p",{ref:r,className:(0,o.cn)("text-sm text-muted-foreground",t),...a})});d.displayName="CardDescription";let u=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("div",{ref:r,className:(0,o.cn)("p-6 pt-0",t),...a})});u.displayName="CardContent";let f=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("div",{ref:r,className:(0,o.cn)("flex items-center p-6 pt-0",t),...a})});f.displayName="CardFooter"},10500:function(e,r,t){t.d(r,{E:function(){return l}});var n=t(57437),a=t(2265),o=t(23177),i=t(1657);let l=a.forwardRef((e,r)=>{let{className:t,value:a,...l}=e;return(0,n.jsx)(o.fC,{ref:r,className:(0,i.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",t),...l,children:(0,n.jsx)(o.z$,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(a||0),"%)")}})})});l.displayName=o.fC.displayName},19160:function(e,r,t){t.d(r,{RM:function(){return s},SC:function(){return d},iA:function(){return i},pj:function(){return f},ss:function(){return u},xD:function(){return l}});var n=t(57437),a=t(2265),o=t(1657);let i=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("div",{className:"relative w-full overflow-auto",children:(0,n.jsx)("table",{ref:r,className:(0,o.cn)("w-full caption-bottom text-sm",t),...a})})});i.displayName="Table";let l=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("thead",{ref:r,className:(0,o.cn)("[&_tr]:border-b",t),...a})});l.displayName="TableHeader";let s=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("tbody",{ref:r,className:(0,o.cn)("[&_tr:last-child]:border-0",t),...a})});s.displayName="TableBody",a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("tfoot",{ref:r,className:(0,o.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",t),...a})}).displayName="TableFooter";let d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("tr",{ref:r,className:(0,o.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",t),...a})});d.displayName="TableRow";let u=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("th",{ref:r,className:(0,o.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",t),...a})});u.displayName="TableHead";let f=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("td",{ref:r,className:(0,o.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",t),...a})});f.displayName="TableCell",a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("caption",{ref:r,className:(0,o.cn)("mt-4 text-sm text-muted-foreground",t),...a})}).displayName="TableCaption"},40110:function(e,r,t){t.d(r,{SP:function(){return d},dr:function(){return s},mQ:function(){return l},nU:function(){return u}});var n=t(57437),a=t(2265),o=t(34522),i=t(1657);let l=o.fC,s=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)(o.aV,{ref:r,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),...a})});s.displayName=o.aV.displayName;let d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)(o.xz,{ref:r,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",t),...a})});d.displayName=o.xz.displayName;let u=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)(o.VY,{ref:r,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...a})});u.displayName=o.VY.displayName},1657:function(e,r,t){t.d(r,{cn:function(){return o}});var n=t(57042),a=t(74769);function o(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.m6)((0,n.W)(r))}},23177:function(e,r,t){t.d(r,{fC:function(){return h},z$:function(){return N}});var n=t(2265),a=t(56989),o=t(9381),i=t(57437),l="Progress",[s,d]=(0,a.b)(l),[u,f]=s(l),c=n.forwardRef((e,r)=>{var t,n;let{__scopeProgress:a,value:l=null,max:s,getValueLabel:d=v,...f}=e;(s||0===s)&&!x(s)&&console.error((t=`${s}`,`Invalid prop \`max\` of value \`${t}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let c=x(s)?s:100;null===l||y(l,c)||console.error((n=`${l}`,`Invalid prop \`value\` of value \`${n}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let m=y(l,c)?l:null,p=g(m)?d(m,c):void 0;return(0,i.jsx)(u,{scope:a,value:m,max:c,children:(0,i.jsx)(o.WV.div,{"aria-valuemax":c,"aria-valuemin":0,"aria-valuenow":g(m)?m:void 0,"aria-valuetext":p,role:"progressbar","data-state":b(m,c),"data-value":m??void 0,"data-max":c,...f,ref:r})})});c.displayName=l;var m="ProgressIndicator",p=n.forwardRef((e,r)=>{let{__scopeProgress:t,...n}=e,a=f(m,t);return(0,i.jsx)(o.WV.div,{"data-state":b(a.value,a.max),"data-value":a.value??void 0,"data-max":a.max,...n,ref:r})});function v(e,r){return`${Math.round(e/r*100)}%`}function b(e,r){return null==e?"indeterminate":e===r?"complete":"loading"}function g(e){return"number"==typeof e}function x(e){return g(e)&&!isNaN(e)&&e>0}function y(e,r){return g(e)&&!isNaN(e)&&e<=r&&e>=0}p.displayName=m;var h=c,N=p}}]);