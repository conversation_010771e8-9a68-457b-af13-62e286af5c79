(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5271],{71047:function(e,s,a){Promise.resolve().then(a.bind(a,70409))},70409:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return v}});var r=a(57437),t=a(2265),n=a(24033),l=a(61396),i=a.n(l),o=a(85754),c=a(45179),d=a(49842),m=a(27815),u=a(54900),p=a(98253),x=a(67972),f=a(1295),h=a(5589),g=a(77216),y=a(99670),N=a(22812),j=a(5925);function v(){var e;let s;let[a,l]=(0,t.useState)({firstName:"",lastName:"",email:"",password:"",confirmPassword:"",companyName:"",companySize:"",industry:"",agreeToTerms:!1,agreeToMarketing:!1}),[v,b]=(0,t.useState)(!1),[w,C]=(0,t.useState)(!1),[T,k]=(0,t.useState)(!1),[S,P]=(0,t.useState)({}),[z,E]=(0,t.useState)(1),A=(0,n.useRouter)(),R=()=>{let e={};return a.firstName.trim()||(e.firstName="First name is required"),a.lastName.trim()||(e.lastName="Last name is required"),a.email.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(a.email)||(e.email="Please enter a valid email address"):e.email="Email is required",a.password?a.password.length<8?e.password="Password must be at least 8 characters long":/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(a.password)||(e.password="Password must contain at least one uppercase letter, one lowercase letter, and one number"):e.password="Password is required",a.confirmPassword?a.password!==a.confirmPassword&&(e.confirmPassword="Passwords do not match"):e.confirmPassword="Please confirm your password",P(e),0===Object.keys(e).length},F=()=>{let e={};return a.companyName.trim()||(e.companyName="Company name is required"),a.companySize||(e.companySize="Please select company size"),a.industry||(e.industry="Please select industry"),a.agreeToTerms||(e.agreeToTerms="You must agree to the terms and conditions"),P(e),0===Object.keys(e).length},I=(e,s)=>{l(a=>({...a,[e]:s})),S[e]&&P(s=>({...s,[e]:""}))},Z=async e=>{if(e.preventDefault(),F()){k(!0);try{let e=await fetch("/api/auth/signup",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({firstName:a.firstName,lastName:a.lastName,email:a.email,password:a.password,companyName:a.companyName,companySize:a.companySize,industry:a.industry,agreeToMarketing:a.agreeToMarketing})}),s=await e.json();if(!e.ok){"User already exists"===s.error?P({email:"An account with this email already exists"}):j.toast.error(s.error||"Failed to create account");return}j.toast.success("Account created successfully! Please check your email to verify your account."),A.push("/auth/verify-email?email="+encodeURIComponent(a.email))}catch(e){console.error("Signup error:",e),j.toast.error("An error occurred. Please try again.")}finally{k(!1)}}},_=(e=a.password,s=0,e.length>=8&&s++,/[a-z]/.test(e)&&s++,/[A-Z]/.test(e)&&s++,/\d/.test(e)&&s++,/[^a-zA-Z\d]/.test(e)&&s++,s),O=(e=>{switch(e){case 0:case 1:return{label:"Very Weak",color:"text-red-600"};case 2:return{label:"Weak",color:"text-orange-600"};case 3:return{label:"Fair",color:"text-yellow-600"};case 4:return{label:"Good",color:"text-blue-600"};case 5:return{label:"Strong",color:"text-green-600"};default:return{label:"",color:""}}})(_);return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 p-4",children:(0,r.jsxs)("div",{className:"w-full max-w-md",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center mb-8",children:[(0,r.jsx)(p.Z,{className:"h-8 w-8 text-blue-600 mr-2"}),(0,r.jsx)("span",{className:"text-2xl font-bold text-gray-900",children:"Business SaaS"})]}),(0,r.jsxs)(m.Zb,{children:[(0,r.jsxs)(m.Ol,{className:"space-y-1",children:[(0,r.jsx)(m.ll,{className:"text-2xl text-center",children:"Create your account"}),(0,r.jsx)(m.SZ,{className:"text-center",children:1===z?"Enter your personal information to get started":"Tell us about your company"}),(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-2 pt-4",children:[(0,r.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(z>=1?"bg-blue-600":"bg-gray-300")}),(0,r.jsx)("div",{className:"w-8 h-1 ".concat(z>=2?"bg-blue-600":"bg-gray-300")}),(0,r.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(z>=2?"bg-blue-600":"bg-gray-300")})]})]}),(0,r.jsxs)(m.aY,{children:[(0,r.jsx)("form",{onSubmit:Z,className:"space-y-4",children:1===z?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d._,{htmlFor:"firstName",children:"First Name"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(x.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)(c.I,{id:"firstName",type:"text",placeholder:"John",value:a.firstName,onChange:e=>I("firstName",e.target.value),className:"pl-10"})]}),S.firstName&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:S.firstName})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d._,{htmlFor:"lastName",children:"Last Name"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(x.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)(c.I,{id:"lastName",type:"text",placeholder:"Doe",value:a.lastName,onChange:e=>I("lastName",e.target.value),className:"pl-10"})]}),S.lastName&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:S.lastName})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d._,{htmlFor:"email",children:"Email Address"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(f.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)(c.I,{id:"email",type:"email",placeholder:"<EMAIL>",value:a.email,onChange:e=>I("email",e.target.value),className:"pl-10"})]}),S.email&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:S.email})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d._,{htmlFor:"password",children:"Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(h.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)(c.I,{id:"password",type:v?"text":"password",placeholder:"Create a strong password",value:a.password,onChange:e=>I("password",e.target.value),className:"pl-10 pr-10"}),(0,r.jsx)(o.z,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>b(!v),children:v?(0,r.jsx)(g.Z,{className:"h-4 w-4"}):(0,r.jsx)(y.Z,{className:"h-4 w-4"})})]}),a.password&&(0,r.jsx)("div",{className:"space-y-2",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"flex-1 bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:"h-2 rounded-full transition-all duration-300 ".concat(_<=1?"bg-red-500":2===_?"bg-orange-500":3===_?"bg-yellow-500":4===_?"bg-blue-500":"bg-green-500"),style:{width:"".concat(_/5*100,"%")}})}),(0,r.jsx)("span",{className:"text-xs font-medium ".concat(O.color),children:O.label})]})}),S.password&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:S.password})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d._,{htmlFor:"confirmPassword",children:"Confirm Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(h.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)(c.I,{id:"confirmPassword",type:w?"text":"password",placeholder:"Confirm your password",value:a.confirmPassword,onChange:e=>I("confirmPassword",e.target.value),className:"pl-10 pr-10"}),(0,r.jsx)(o.z,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>C(!w),children:w?(0,r.jsx)(g.Z,{className:"h-4 w-4"}):(0,r.jsx)(y.Z,{className:"h-4 w-4"})})]}),S.confirmPassword&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:S.confirmPassword})]}),(0,r.jsx)(o.z,{type:"button",onClick:()=>{R()&&E(2)},className:"w-full",children:"Continue"})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d._,{htmlFor:"companyName",children:"Company Name"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(N.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)(c.I,{id:"companyName",type:"text",placeholder:"Your Company Inc.",value:a.companyName,onChange:e=>I("companyName",e.target.value),className:"pl-10"})]}),S.companyName&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:S.companyName})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d._,{htmlFor:"companySize",children:"Company Size"}),(0,r.jsxs)("select",{id:"companySize",value:a.companySize,onChange:e=>I("companySize",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"",children:"Select company size"}),(0,r.jsx)("option",{value:"STARTUP",children:"Startup (1-10 employees)"}),(0,r.jsx)("option",{value:"SMALL",children:"Small (11-50 employees)"}),(0,r.jsx)("option",{value:"MEDIUM",children:"Medium (51-200 employees)"}),(0,r.jsx)("option",{value:"LARGE",children:"Large (201-1000 employees)"}),(0,r.jsx)("option",{value:"ENTERPRISE",children:"Enterprise (1000+ employees)"})]}),S.companySize&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:S.companySize})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d._,{htmlFor:"industry",children:"Industry"}),(0,r.jsxs)("select",{id:"industry",value:a.industry,onChange:e=>I("industry",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"",children:"Select industry"}),(0,r.jsx)("option",{value:"TECHNOLOGY",children:"Technology"}),(0,r.jsx)("option",{value:"FINANCE",children:"Finance"}),(0,r.jsx)("option",{value:"HEALTHCARE",children:"Healthcare"}),(0,r.jsx)("option",{value:"MANUFACTURING",children:"Manufacturing"}),(0,r.jsx)("option",{value:"RETAIL",children:"Retail"}),(0,r.jsx)("option",{value:"EDUCATION",children:"Education"}),(0,r.jsx)("option",{value:"CONSULTING",children:"Consulting"}),(0,r.jsx)("option",{value:"REAL_ESTATE",children:"Real Estate"}),(0,r.jsx)("option",{value:"OTHER",children:"Other"})]}),S.industry&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:S.industry})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,r.jsx)(u.X,{id:"agreeToTerms",checked:a.agreeToTerms,onCheckedChange:e=>I("agreeToTerms",!!e)}),(0,r.jsxs)(d._,{htmlFor:"agreeToTerms",className:"text-sm leading-5",children:["I agree to the"," ",(0,r.jsx)(i(),{href:"/terms",className:"text-blue-600 hover:underline",children:"Terms of Service"})," ","and"," ",(0,r.jsx)(i(),{href:"/privacy",className:"text-blue-600 hover:underline",children:"Privacy Policy"})]})]}),S.agreeToTerms&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:S.agreeToTerms}),(0,r.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,r.jsx)(u.X,{id:"agreeToMarketing",checked:a.agreeToMarketing,onCheckedChange:e=>I("agreeToMarketing",!!e)}),(0,r.jsx)(d._,{htmlFor:"agreeToMarketing",className:"text-sm leading-5",children:"I would like to receive product updates and marketing communications"})]})]}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsx)(o.z,{type:"button",variant:"outline",onClick:()=>{E(1)},className:"flex-1",children:"Back"}),(0,r.jsx)(o.z,{type:"submit",className:"flex-1",disabled:T,children:T?"Creating Account...":"Create Account"})]})]})}),(0,r.jsxs)("div",{className:"mt-6 text-center text-sm",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Already have an account? "}),(0,r.jsx)(i(),{href:"/auth/signin",className:"text-blue-600 hover:underline",children:"Sign in"})]})]})]})]})})}},85754:function(e,s,a){"use strict";a.d(s,{z:function(){return c}});var r=a(57437),t=a(2265),n=a(67256),l=a(96061),i=a(1657);let o=(0,l.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=t.forwardRef((e,s)=>{let{className:a,variant:t,size:l,asChild:c=!1,...d}=e,m=c?n.g7:"button";return(0,r.jsx)(m,{className:(0,i.cn)(o({variant:t,size:l,className:a})),ref:s,...d})});c.displayName="Button"},27815:function(e,s,a){"use strict";a.d(s,{Ol:function(){return i},SZ:function(){return c},Zb:function(){return l},aY:function(){return d},eW:function(){return m},ll:function(){return o}});var r=a(57437),t=a(2265),n=a(1657);let l=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...t})});l.displayName="Card";let i=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",a),...t})});i.displayName="CardHeader";let o=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("h3",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",a),...t})});o.displayName="CardTitle";let c=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",a),...t})});c.displayName="CardDescription";let d=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:(0,n.cn)("p-6 pt-0",a),...t})});d.displayName="CardContent";let m=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",a),...t})});m.displayName="CardFooter"},54900:function(e,s,a){"use strict";a.d(s,{X:function(){return o}});var r=a(57437),t=a(2265),n=a(66062),l=a(62442),i=a(1657);let o=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)(n.fC,{ref:s,className:(0,i.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",a),...t,children:(0,r.jsx)(n.z$,{className:(0,i.cn)("flex items-center justify-center text-current"),children:(0,r.jsx)(l.Z,{className:"h-4 w-4"})})})});o.displayName=n.fC.displayName},45179:function(e,s,a){"use strict";a.d(s,{I:function(){return l}});var r=a(57437),t=a(2265),n=a(1657);let l=t.forwardRef((e,s)=>{let{className:a,type:t,...l}=e;return(0,r.jsx)("input",{type:t,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:s,...l})});l.displayName="Input"},49842:function(e,s,a){"use strict";a.d(s,{_:function(){return c}});var r=a(57437),t=a(2265),n=a(36743),l=a(96061),i=a(1657);let o=(0,l.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)(n.f,{ref:s,className:(0,i.cn)(o(),a),...t})});c.displayName=n.f.displayName},1657:function(e,s,a){"use strict";a.d(s,{cn:function(){return n}});var r=a(57042),t=a(74769);function n(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,t.m6)((0,r.W)(s))}}},function(e){e.O(0,[6723,1396,496,2971,4938,1744],function(){return e(e.s=71047)}),_N_E=e.O()}]);