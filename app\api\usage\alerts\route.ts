import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get current subscription and usage
    const subscription = await prisma.subscription.findFirst({
      where: {
        companyId: session.user.companyId,
        status: { in: ['ACTIVE', 'TRIALING', 'PAST_DUE'] }
      },
      include: {
        pricingPlan: true
      }
    })

    if (!subscription) {
      return NextResponse.json({
        success: true,
        data: { alerts: [] }
      })
    }

    // Calculate current usage
    const [
      userCount,
      customerCount,
      quotationCount,
      invoiceCount,
      contractCount
    ] = await Promise.all([
      prisma.user.count({ where: { companyId: session.user.companyId } }),
      prisma.customer.count({ where: { companyId: session.user.companyId } }),
      prisma.quotation.count({ where: { companyId: session.user.companyId } }),
      prisma.invoice.count({ where: { companyId: session.user.companyId } }),
      prisma.contract.count({ where: { companyId: session.user.companyId } })
    ])

    const alerts = []

    // Check each usage metric
    const usageChecks = [
      {
        name: 'Users',
        current: userCount,
        limit: subscription.pricingPlan.maxUsers,
        type: 'users'
      },
      {
        name: 'Customers',
        current: customerCount,
        limit: subscription.pricingPlan.maxCustomers,
        type: 'customers'
      },
      {
        name: 'Quotations',
        current: quotationCount,
        limit: subscription.pricingPlan.maxQuotations,
        type: 'quotations'
      },
      {
        name: 'Invoices',
        current: invoiceCount,
        limit: subscription.pricingPlan.maxInvoices,
        type: 'invoices'
      },
      {
        name: 'Contracts',
        current: contractCount,
        limit: subscription.pricingPlan.maxContracts,
        type: 'contracts'
      }
    ]

    for (const check of usageChecks) {
      const percentage = (check.current / check.limit) * 100

      if (check.current >= check.limit) {
        alerts.push({
          id: `${check.type}-exceeded`,
          type: 'error',
          title: `${check.name} Limit Exceeded`,
          message: `You've reached your ${check.name.toLowerCase()} limit (${check.current}/${check.limit}). Upgrade your plan to add more.`,
          percentage: 100,
          action: {
            label: 'Upgrade Plan',
            url: '/pricing'
          },
          priority: 'high'
        })
      } else if (percentage >= 90) {
        alerts.push({
          id: `${check.type}-critical`,
          type: 'warning',
          title: `${check.name} Limit Almost Reached`,
          message: `You're using ${Math.round(percentage)}% of your ${check.name.toLowerCase()} limit (${check.current}/${check.limit}).`,
          percentage: Math.round(percentage),
          action: {
            label: 'Upgrade Plan',
            url: '/pricing'
          },
          priority: 'high'
        })
      } else if (percentage >= 75) {
        alerts.push({
          id: `${check.type}-warning`,
          type: 'info',
          title: `${check.name} Usage High`,
          message: `You're using ${Math.round(percentage)}% of your ${check.name.toLowerCase()} limit (${check.current}/${check.limit}).`,
          percentage: Math.round(percentage),
          action: {
            label: 'View Usage',
            url: '/subscription?tab=usage'
          },
          priority: 'medium'
        })
      }
    }

    // Check trial expiration
    if (subscription.status === 'TRIALING' && subscription.trialEnd) {
      const daysUntilTrialEnd = Math.ceil(
        (subscription.trialEnd.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
      )

      if (daysUntilTrialEnd <= 3) {
        alerts.push({
          id: 'trial-ending',
          type: 'warning',
          title: 'Trial Ending Soon',
          message: `Your free trial ends in ${daysUntilTrialEnd} day${daysUntilTrialEnd !== 1 ? 's' : ''}. Add a payment method to continue using the service.`,
          percentage: null,
          action: {
            label: 'Add Payment Method',
            url: '/subscription?tab=billing'
          },
          priority: 'high'
        })
      } else if (daysUntilTrialEnd <= 7) {
        alerts.push({
          id: 'trial-reminder',
          type: 'info',
          title: 'Trial Reminder',
          message: `Your free trial ends in ${daysUntilTrialEnd} days. Consider upgrading to continue using all features.`,
          percentage: null,
          action: {
            label: 'View Plans',
            url: '/pricing'
          },
          priority: 'medium'
        })
      }
    }

    // Sort alerts by priority
    const priorityOrder = { high: 3, medium: 2, low: 1 }
    alerts.sort((a, b) => priorityOrder[b.priority] - priorityOrder[a.priority])

    return NextResponse.json({
      success: true,
      data: {
        alerts,
        subscription: {
          planName: subscription.pricingPlan.name,
          status: subscription.status,
          trialEnd: subscription.trialEnd
        }
      }
    })

  } catch (error) {
    console.error('Error fetching usage alerts:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch usage alerts' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { alertId, action } = body

    // Handle alert actions (dismiss, snooze, etc.)
    // This could be expanded to store user preferences for alerts

    if (action === 'dismiss') {
      // In a real app, you might store dismissed alerts in the database
      // to avoid showing them again for a certain period
      
      return NextResponse.json({
        success: true,
        message: 'Alert dismissed'
      })
    }

    return NextResponse.json({
      success: false,
      error: 'Invalid action'
    }, { status: 400 })

  } catch (error) {
    console.error('Error handling alert action:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to handle alert action' },
      { status: 500 }
    )
  }
}
