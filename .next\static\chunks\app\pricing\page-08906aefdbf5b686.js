(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9979],{2152:function(e,t,r){Promise.resolve().then(r.bind(r,51572))},51572:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return v}});var s=r(57437),a=r(2265),n=r(82749),i=r(24033),l=r(27815),c=r(85754),o=r(31478),d=r(86443),u=r(92457),m=r(49423),x=r(49036),f=r(52369),h=r(62442),p=r(55340),g=r(25750),b=r(97332),y=r(71424);function v(){let{data:e}=(0,n.useSession)(),t=(0,i.useRouter)(),[r,v]=(0,a.useState)([]),[j,N]=(0,a.useState)(!0),[w,C]=(0,a.useState)(!1);(0,a.useEffect)(()=>{k()},[]);let k=async()=>{try{let e=await fetch("/api/pricing-plans?publicOnly=true"),t=await e.json();t.success?v(t.data):y.Am.error("Failed to load pricing plans")}catch(e){console.error("Error fetching plans:",e),y.Am.error("Failed to load pricing plans")}finally{N(!1)}},S=r=>{if(!e){t.push("/auth/signin?callbackUrl=/pricing");return}0===r.monthlyPrice?t.push("/dashboard"):t.push("/subscription/checkout?plan=".concat(r.id,"&billing=").concat(w?"yearly":"monthly"))},P=e=>{switch(e){case"basicReporting":case"advancedAnalytics":return(0,s.jsx)(u.Z,{className:"h-4 w-4"});case"prioritySupport":case"emailSupport":return(0,s.jsx)(m.Z,{className:"h-4 w-4"});case"advancedSecurity":return(0,s.jsx)(x.Z,{className:"h-4 w-4"});case"apiAccess":return(0,s.jsx)(f.Z,{className:"h-4 w-4"});default:return(0,s.jsx)(h.Z,{className:"h-4 w-4"})}},A=e=>({basicReporting:"Basic Reporting",emailSupport:"Email Support",mobileApp:"Mobile App Access",advancedAnalytics:"Advanced Analytics",customBranding:"Custom Branding",apiAccess:"API Access",prioritySupport:"Priority Support",customIntegrations:"Custom Integrations",advancedSecurity:"Advanced Security",dedicatedManager:"Dedicated Account Manager"})[e]||e;return j?(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,s.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading pricing plans..."})]})}):(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 py-16",children:[(0,s.jsxs)("div",{className:"text-center mb-16",children:[(0,s.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Choose Your Perfect Plan"}),(0,s.jsx)("p",{className:"text-xl text-gray-600 mb-8 max-w-2xl mx-auto",children:"Start with our free plan and upgrade as your business grows. All plans include our core features with varying limits."}),(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-4 mb-8",children:[(0,s.jsx)("span",{className:"text-sm font-medium ".concat(w?"text-gray-500":"text-blue-600"),children:"Monthly"}),(0,s.jsx)(d.r,{checked:w,onCheckedChange:C,className:"data-[state=checked]:bg-blue-600"}),(0,s.jsx)("span",{className:"text-sm font-medium ".concat(w?"text-blue-600":"text-gray-500"),children:"Yearly"}),r.some(e=>e.yearlyDiscount>0)&&(0,s.jsxs)(o.C,{variant:"secondary",className:"ml-2",children:["Save up to ",Math.max(...r.map(e=>e.yearlyDiscount)),"%"]})]})]}),(0,s.jsx)("div",{className:"grid md:grid-cols-3 gap-8 max-w-6xl mx-auto",children:r.map(e=>{let t=w&&null!==e.yearlyPrice?e.yearlyPrice/12:e.monthlyPrice,r=e.monthlyPrice,a=w&&null!==e.yearlyPrice&&e.yearlyDiscount>0;return(0,s.jsxs)(l.Zb,{className:"relative ".concat(e.isPopular?"ring-2 ring-blue-500 shadow-lg scale-105":"shadow-md"," hover:shadow-xl transition-all duration-300"),children:[e.isPopular&&(0,s.jsx)("div",{className:"absolute -top-4 left-1/2 transform -translate-x-1/2",children:(0,s.jsxs)(o.C,{className:"bg-blue-600 text-white px-4 py-1",children:[(0,s.jsx)(p.Z,{className:"h-3 w-3 mr-1"}),"Most Popular"]})}),(0,s.jsxs)(l.Ol,{className:"text-center pb-4",children:[(0,s.jsx)(l.ll,{className:"text-2xl font-bold",children:e.name}),(0,s.jsx)(l.SZ,{className:"text-gray-600",children:e.description}),(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsxs)("div",{className:"flex items-baseline justify-center",children:[(0,s.jsxs)("span",{className:"text-4xl font-bold text-gray-900",children:["$",t.toFixed(0)]}),(0,s.jsx)("span",{className:"text-gray-500 ml-1",children:"/month"})]}),a&&(0,s.jsxs)("div",{className:"flex items-center justify-center mt-2",children:[(0,s.jsxs)("span",{className:"text-sm text-gray-500 line-through mr-2",children:["$",r,"/month"]}),(0,s.jsxs)(o.C,{variant:"secondary",className:"text-xs",children:[e.yearlyDiscount,"% off"]})]}),w&&null!==e.yearlyPrice&&(0,s.jsxs)("p",{className:"text-sm text-gray-500 mt-1",children:["Billed annually ($",e.yearlyPrice,"/year)"]})]})]}),(0,s.jsxs)(l.aY,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("span",{className:"flex items-center",children:[(0,s.jsx)(g.Z,{className:"h-4 w-4 mr-2 text-gray-400"}),"Users"]}),(0,s.jsx)("span",{className:"font-medium",children:e.maxUsers})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("span",{className:"flex items-center",children:[(0,s.jsx)(b.Z,{className:"h-4 w-4 mr-2 text-gray-400"}),"Storage"]}),(0,s.jsx)("span",{className:"font-medium",children:e.formattedStorage})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{children:"Customers"}),(0,s.jsx)("span",{className:"font-medium",children:e.maxCustomers.toLocaleString()})]})]}),(0,s.jsx)("hr",{className:"my-4"}),(0,s.jsx)("div",{className:"space-y-2",children:Object.entries(e.features).map(e=>{let[t,r]=e;return r&&(0,s.jsxs)("div",{className:"flex items-center text-sm",children:[(0,s.jsx)("div",{className:"text-green-500 mr-2",children:P(t)}),(0,s.jsx)("span",{children:A(t)})]},t)})}),e.trialDays>0&&(0,s.jsx)("div",{className:"mt-4 p-3 bg-blue-50 rounded-lg",children:(0,s.jsxs)("p",{className:"text-sm text-blue-700 font-medium",children:[e.trialDays,"-day free trial"]})})]}),(0,s.jsx)(l.eW,{children:(0,s.jsx)(c.z,{className:"w-full ".concat(e.isPopular?"bg-blue-600 hover:bg-blue-700":""),variant:e.isPopular?"default":"outline",onClick:()=>S(e),children:0===e.monthlyPrice?"Get Started Free":"Start Free Trial"})})]},e.id)})}),(0,s.jsx)("div",{className:"text-center mt-16",children:(0,s.jsxs)("p",{className:"text-gray-600",children:["Need a custom plan? "," ",(0,s.jsx)("a",{href:"/contact",className:"text-blue-600 hover:underline",children:"Contact our sales team"})]})})]})})}},31478:function(e,t,r){"use strict";r.d(t,{C:function(){return l}});var s=r(57437);r(2265);var a=r(96061),n=r(1657);let i=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600",info:"border-transparent bg-blue-500 text-white hover:bg-blue-600"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:r,...a}=e;return(0,s.jsx)("div",{className:(0,n.cn)(i({variant:r}),t),...a})}},85754:function(e,t,r){"use strict";r.d(t,{z:function(){return o}});var s=r(57437),a=r(2265),n=r(67256),i=r(96061),l=r(1657);let c=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=a.forwardRef((e,t)=>{let{className:r,variant:a,size:i,asChild:o=!1,...d}=e,u=o?n.g7:"button";return(0,s.jsx)(u,{className:(0,l.cn)(c({variant:a,size:i,className:r})),ref:t,...d})});o.displayName="Button"},27815:function(e,t,r){"use strict";r.d(t,{Ol:function(){return l},SZ:function(){return o},Zb:function(){return i},aY:function(){return d},eW:function(){return u},ll:function(){return c}});var s=r(57437),a=r(2265),n=r(1657);let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});i.displayName="Card";let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...a})});l.displayName="CardHeader";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})});c.displayName="CardTitle";let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",r),...a})});o.displayName="CardDescription";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",r),...a})});d.displayName="CardContent";let u=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",r),...a})});u.displayName="CardFooter"},86443:function(e,t,r){"use strict";r.d(t,{r:function(){return l}});var s=r(57437),a=r(2265),n=r(92376),i=r(1657);let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(n.fC,{className:(0,i.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",r),...a,ref:t,children:(0,s.jsx)(n.bU,{className:(0,i.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});l.displayName=n.fC.displayName},1657:function(e,t,r){"use strict";r.d(t,{cn:function(){return n}});var s=r(57042),a=r(74769);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.m6)((0,s.W)(t))}}},function(e){e.O(0,[6723,2749,1424,7547,2971,4938,1744],function(){return e(e.s=2152)}),_N_E=e.O()}]);