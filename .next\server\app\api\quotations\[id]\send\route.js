"use strict";(()=>{var e={};e.id=3111,e.ids=[3111],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},35015:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>f,originalPathname:()=>v,patchFetch:()=>q,requestAsyncStorage:()=>h,routeModule:()=>b,serverHooks:()=>y,staticGenerationAsyncStorage:()=>x,staticGenerationBailout:()=>w});var o={};a.r(o),a.d(o,{POST:()=>g});var i=a(95419),r=a(69108),n=a(99678),s=a(78070),l=a(81355),d=a(3205),c=a(9108),p=a(25252),u=a(52178);let m=p.Ry({to:p.Z_().email("Invalid email address"),cc:p.IX(p.Z_().email()).optional().default([]),subject:p.Z_().min(1,"Subject is required"),message:p.Z_().min(1,"Message is required"),includePDF:p.O7().default(!0)});async function g(e,{params:t}){try{let a=await (0,l.getServerSession)(d.L);if(!a?.user?.id||!a?.user?.companyId)return s.Z.json({error:"Unauthorized"},{status:401});let o=await e.json(),i=m.parse(o),r=await c._.quotation.findFirst({where:{id:t.id,companyId:a.user.companyId},include:{customer:{select:{id:!0,name:!0,email:!0,company:!0}},items:{orderBy:{createdAt:"asc"}},createdBy:{select:{name:!0,email:!0}},companyRef:{select:{name:!0,email:!0,phone:!0,website:!0}}}});if(!r)return s.Z.json({error:"Quotation not found"},{status:404});let n=r.items.map(e=>{let t=Number(e.quantity)*Number(e.unitPrice),a=t*Number(e.discount)/100,o=t-a,i=o*Number(e.taxRate)/100;return{...e,total:o+i}}),p=n.reduce((e,t)=>e+Number(t.quantity)*Number(t.unitPrice),0),u="PERCENTAGE"===r.discountType?p*Number(r.discountValue)/100:Number(r.discountValue),g=p-u,b=g*Number(r.taxRate)/100,h=function(e){let{quotation:t,customer:a,company:o,items:i,total:r,message:n}=e;return`
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quotation ${t.quotationNumber}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
            padding: 0;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: bold;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .content {
            padding: 30px;
        }
        .message-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid #667eea;
        }
        .quotation-details {
            background-color: #fff;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #f1f3f4;
        }
        .detail-row:last-child {
            border-bottom: none;
        }
        .detail-label {
            font-weight: 600;
            color: #495057;
        }
        .detail-value {
            color: #212529;
        }
        .items-section {
            margin-bottom: 30px;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .items-table th {
            background-color: #667eea;
            color: white;
            padding: 15px 10px;
            text-align: left;
            font-weight: 600;
        }
        .items-table td {
            padding: 12px 10px;
            border-bottom: 1px solid #e9ecef;
        }
        .items-table tr:last-child td {
            border-bottom: none;
        }
        .total-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 30px;
        }
        .total-amount {
            font-size: 32px;
            font-weight: bold;
            margin: 10px 0;
        }
        .cta-section {
            text-align: center;
            margin: 30px 0;
        }
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            font-size: 16px;
            transition: transform 0.2s;
        }
        .cta-button:hover {
            transform: translateY(-2px);
        }
        .footer {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #6c757d;
            font-size: 14px;
        }
        .company-info {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }
        @media (max-width: 600px) {
            .container {
                margin: 0;
                width: 100%;
            }
            .content {
                padding: 20px;
            }
            .items-table {
                font-size: 14px;
            }
            .items-table th,
            .items-table td {
                padding: 8px 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Quotation ${t.quotationNumber}</h1>
            <p>From ${o.name}</p>
        </div>
        
        <div class="content">
            <div class="message-section">
                <p>${n}</p>
            </div>
            
            <div class="quotation-details">
                <h3 style="margin-top: 0; color: #495057;">Quotation Details</h3>
                <div class="detail-row">
                    <span class="detail-label">Quotation Number:</span>
                    <span class="detail-value">${t.quotationNumber}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Date:</span>
                    <span class="detail-value">${new Date(t.createdAt).toLocaleDateString()}</span>
                </div>
                ${t.validUntil?`
                <div class="detail-row">
                    <span class="detail-label">Valid Until:</span>
                    <span class="detail-value">${new Date(t.validUntil).toLocaleDateString()}</span>
                </div>
                `:""}
                <div class="detail-row">
                    <span class="detail-label">Customer:</span>
                    <span class="detail-value">${a.name}${a.company?` (${a.company})`:""}</span>
                </div>
            </div>
            
            <div class="items-section">
                <h3 style="color: #495057;">Items</h3>
                <table class="items-table">
                    <thead>
                        <tr>
                            <th>Description</th>
                            <th style="text-align: center;">Qty</th>
                            <th style="text-align: right;">Unit Price</th>
                            <th style="text-align: right;">Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${i.map(e=>`
                            <tr>
                                <td><strong>${e.description}</strong></td>
                                <td style="text-align: center;">${Number(e.quantity)}</td>
                                <td style="text-align: right;">$${Number(e.unitPrice).toFixed(2)}</td>
                                <td style="text-align: right;">$${e.total.toFixed(2)}</td>
                            </tr>
                        `).join("")}
                    </tbody>
                </table>
            </div>
            
            <div class="total-section">
                <h3 style="margin: 0;">Total Amount</h3>
                <div class="total-amount">$${r.toFixed(2)}</div>
                <p style="margin: 0; opacity: 0.9;">All prices are in USD</p>
            </div>
            
            <div class="cta-section">
                <a href="http://localhost:3000/quotations/${t.id}/view" class="cta-button">
                    View Full Quotation
                </a>
            </div>
            
            ${t.terms?`
            <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
                <h4 style="margin-top: 0; color: #856404;">Terms & Conditions</h4>
                <p style="margin-bottom: 0; color: #856404;">${t.terms}</p>
            </div>
            `:""}
            
            ${t.notes?`
            <div style="background-color: #d1ecf1; border: 1px solid #bee5eb; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
                <h4 style="margin-top: 0; color: #0c5460;">Additional Notes</h4>
                <p style="margin-bottom: 0; color: #0c5460;">${t.notes}</p>
            </div>
            `:""}
        </div>
        
        <div class="footer">
            <div class="company-info">
                <strong>${o.name}</strong><br>
                ${o.email?`Email: ${o.email}<br>`:""}
                ${o.phone?`Phone: ${o.phone}<br>`:""}
                ${o.website?`Website: ${o.website}`:""}
            </div>
            <p style="margin-top: 20px;">
                Thank you for your business! If you have any questions about this quotation, 
                please don't hesitate to contact us.
            </p>
        </div>
    </div>
</body>
</html>
  `}({quotation:r,customer:r.customer,company:r.companyRef,items:n,total:g+b,message:i.message});return console.log("Email would be sent with the following details:"),console.log("To:",i.to),console.log("CC:",i.cc),console.log("Subject:",i.subject),console.log("HTML Content:",h),"DRAFT"===r.status&&await c._.quotation.update({where:{id:t.id},data:{status:"SENT",sentAt:new Date}}),await c._.activity.create({data:{type:"EMAIL",title:"Quotation Sent",description:`Quotation ${r.quotationNumber} was sent to ${i.to}`,quotationId:r.id,customerId:r.customerId,companyId:a.user.companyId,createdById:a.user.id}}),s.Z.json({success:!0,message:"Quotation sent successfully"})}catch(e){if(e instanceof u.jm)return s.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("Error sending quotation:",e),s.Z.json({error:"Failed to send quotation"},{status:500})}}let b=new i.AppRouteRouteModule({definition:{kind:r.x.APP_ROUTE,page:"/api/quotations/[id]/send/route",pathname:"/api/quotations/[id]/send",filename:"route",bundlePath:"app/api/quotations/[id]/send/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\quotations\\[id]\\send\\route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:h,staticGenerationAsyncStorage:x,serverHooks:y,headerHooks:f,staticGenerationBailout:w}=b,v="/api/quotations/[id]/send/route";function q(){return(0,n.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:x})}},3205:(e,t,a)=>{a.d(t,{L:()=>d});var o=a(86485),i=a(10375),r=a(50694),n=a(6521),s=a.n(n),l=a(9108);let d={providers:[(0,o.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await l._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),a=t?.companyId;if(!a&&t){let e=await l._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(a=e?.id)&&await l._.user.update({where:{id:t.id},data:{companyId:a}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await s().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await l._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:a}}catch(e){return console.error("Authentication error:",e),null}}}),(0,i.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,r.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,a)=>{a.d(t,{_:()=>i});let o=require("@prisma/client"),i=globalThis.prisma??new o.PrismaClient}};var t=require("../../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),o=t.X(0,[1638,6206,6521,2455,4520,5252],()=>a(35015));module.exports=o})();