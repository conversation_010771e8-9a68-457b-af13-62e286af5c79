"use strict";(()=>{var e={};e.id=2748,e.ids=[2748],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},23089:(e,r,i)=>{i.r(r),i.d(r,{headerHooks:()=>w,originalPathname:()=>k,patchFetch:()=>x,requestAsyncStorage:()=>c,routeModule:()=>f,serverHooks:()=>h,staticGenerationAsyncStorage:()=>v,staticGenerationBailout:()=>j});var a={};i.r(a),i.d(a,{POST:()=>p});var t=i(95419),n=i(69108),o=i(99678),s=i(78070),u=i(9108),l=i(25252),d=i(52178);let m=l.Ry({token:l.Z_().min(1,"Token is required")});async function p(e){try{let r=await e.json(),{token:i}=m.parse(r),a=await u._.user.findFirst({where:{emailVerificationToken:i,emailVerificationExpires:{gt:new Date}}});if(!a){if(await u._.user.findFirst({where:{emailVerificationToken:i}}))return s.Z.json({error:"Token expired"},{status:400});return s.Z.json({error:"Invalid or expired verification token"},{status:400})}return await u._.user.update({where:{id:a.id},data:{emailVerified:!0,emailVerificationToken:null,emailVerificationExpires:null,emailVerifiedAt:new Date}}),console.log(`Email verified for user: ${a.email}`),s.Z.json({message:"Email verified successfully",user:{id:a.id,email:a.email,emailVerified:!0}})}catch(e){if(e instanceof d.jm)return s.Z.json({error:"Invalid request data",details:e.errors},{status:400});return console.error("Email verification error:",e),s.Z.json({error:"Internal server error"},{status:500})}}let f=new t.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/auth/verify-email/route",pathname:"/api/auth/verify-email",filename:"route",bundlePath:"app/api/auth/verify-email/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\auth\\verify-email\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:c,staticGenerationAsyncStorage:v,serverHooks:h,headerHooks:w,staticGenerationBailout:j}=f,k="/api/auth/verify-email/route";function x(){return(0,o.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:v})}},9108:(e,r,i)=>{i.d(r,{_:()=>t});let a=require("@prisma/client"),t=globalThis.prisma??new a.PrismaClient}};var r=require("../../../../webpack-runtime.js");r.C(e);var i=e=>r(r.s=e),a=r.X(0,[1638,6206,5252],()=>i(23089));module.exports=a})();