{"version": 3, "file": "getFacetedUniqueValues.js", "sources": ["../../../src/utils/getFacetedUniqueValues.ts"], "sourcesContent": ["import { Table, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\n\nexport function getFacetedUniqueValues<TData extends RowData>(): (\n  table: Table<TData>,\n  columnId: string\n) => () => Map<any, number> {\n  return (table, columnId) =>\n    memo(\n      () => [table.getColumn(columnId)?.getFacetedRowModel()],\n      facetedRowModel => {\n        if (!facetedRowModel) return new Map()\n\n        let facetedUniqueValues = new Map<any, number>()\n\n        for (let i = 0; i < facetedRowModel.flatRows.length; i++) {\n          const values =\n            facetedRowModel.flatRows[i]!.getUniqueValues<number>(columnId)\n\n          for (let j = 0; j < values.length; j++) {\n            const value = values[j]!\n\n            if (facetedUniqueValues.has(value)) {\n              facetedUniqueValues.set(\n                value,\n                (facetedUniqueValues.get(value) ?? 0) + 1\n              )\n            } else {\n              facetedUniqueValues.set(value, 1)\n            }\n          }\n        }\n\n        return facetedUniqueValues\n      },\n      getMemoOptions(\n        table.options,\n        'debugTable',\n        `getFacetedUniqueValues_${columnId}`\n      )\n    )\n}\n"], "names": ["getFacetedUniqueValues", "table", "columnId", "memo", "_table$getColumn", "getColumn", "getFacetedRowModel", "facetedRowModel", "Map", "facetedUniqueValues", "i", "flatRows", "length", "values", "getUniqueValues", "j", "value", "has", "_facetedUniqueValues$", "set", "get", "getMemoOptions", "options"], "mappings": ";;;;;;;;;;;;;;AAGO,SAASA,sBAAsBA,GAGV;AAC1B,EAAA,OAAO,CAACC,KAAK,EAAEC,QAAQ,KACrBC,UAAI,CACF,MAAA;AAAA,IAAA,IAAAC,gBAAA,CAAA;AAAA,IAAA,OAAM,CAAAA,CAAAA,gBAAA,GAACH,KAAK,CAACI,SAAS,CAACH,QAAQ,CAAC,qBAAzBE,gBAAA,CAA2BE,kBAAkB,EAAE,CAAC,CAAA;AAAA,GAAA,EACvDC,eAAe,IAAI;AACjB,IAAA,IAAI,CAACA,eAAe,EAAE,OAAO,IAAIC,GAAG,EAAE,CAAA;AAEtC,IAAA,IAAIC,mBAAmB,GAAG,IAAID,GAAG,EAAe,CAAA;AAEhD,IAAA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,eAAe,CAACI,QAAQ,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;AACxD,MAAA,MAAMG,MAAM,GACVN,eAAe,CAACI,QAAQ,CAACD,CAAC,CAAC,CAAEI,eAAe,CAASZ,QAAQ,CAAC,CAAA;AAEhE,MAAA,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAACD,MAAM,EAAEG,CAAC,EAAE,EAAE;AACtC,QAAA,MAAMC,KAAK,GAAGH,MAAM,CAACE,CAAC,CAAE,CAAA;AAExB,QAAA,IAAIN,mBAAmB,CAACQ,GAAG,CAACD,KAAK,CAAC,EAAE;AAAA,UAAA,IAAAE,qBAAA,CAAA;UAClCT,mBAAmB,CAACU,GAAG,CACrBH,KAAK,EACL,CAAAE,CAAAA,qBAAA,GAACT,mBAAmB,CAACW,GAAG,CAACJ,KAAK,CAAC,KAAAE,IAAAA,GAAAA,qBAAA,GAAI,CAAC,IAAI,CAC1C,CAAC,CAAA;AACH,SAAC,MAAM;AACLT,UAAAA,mBAAmB,CAACU,GAAG,CAACH,KAAK,EAAE,CAAC,CAAC,CAAA;AACnC,SAAA;AACF,OAAA;AACF,KAAA;AAEA,IAAA,OAAOP,mBAAmB,CAAA;AAC5B,GAAC,EACDY,oBAAc,CACZpB,KAAK,CAACqB,OAAO,EACb,YAAY,EACZ,CAAA,uBAAA,EAA0BpB,QAAQ,CAAA,CACpC,CACF,CAAC,CAAA;AACL;;;;"}