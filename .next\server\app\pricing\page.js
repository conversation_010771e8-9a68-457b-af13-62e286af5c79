(()=>{var e={};e.id=9979,e.ids=[9979],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},85129:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=r(50482),a=r(69108),n=r(62563),i=r.n(n),o=r(68300),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c=["",{children:["pricing",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,81125)),"C:\\proj\\nextjs-saas\\app\\pricing\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,59504)),"C:\\proj\\nextjs-saas\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\proj\\nextjs-saas\\app\\pricing\\page.tsx"],u="/pricing/page",m={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/pricing/page",pathname:"/pricing",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},56004:(e,t,r)=>{Promise.resolve().then(r.bind(r,49473))},64588:(e,t,r)=>{Promise.resolve().then(r.bind(r,56189)),Promise.resolve().then(r.bind(r,44669))},19634:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2583,23)),Promise.resolve().then(r.t.bind(r,26840,23)),Promise.resolve().then(r.t.bind(r,38771,23)),Promise.resolve().then(r.t.bind(r,13225,23)),Promise.resolve().then(r.t.bind(r,9295,23)),Promise.resolve().then(r.t.bind(r,43982,23))},49473:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>v});var s=r(95344),a=r(3729),n=r(47674),i=r(22254),o=r(61351),l=r(16212),c=r(69436),d=r(71809),u=r(50340);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let m=(0,r(69224).Z)("Headphones",[["path",{d:"M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3",key:"1xhozi"}]]);var p=r(23485),f=r(79200),x=r(62312),h=r(76755),g=r(89895),y=r(99046),b=r(34755);function v(){let{data:e}=(0,n.useSession)(),t=(0,i.useRouter)(),[r,v]=(0,a.useState)([]),[j,N]=(0,a.useState)(!0),[w,S]=(0,a.useState)(!1);(0,a.useEffect)(()=>{C()},[]);let C=async()=>{try{let e=await fetch("/api/pricing-plans?publicOnly=true"),t=await e.json();t.success?v(t.data):b.Am.error("Failed to load pricing plans")}catch(e){console.error("Error fetching plans:",e),b.Am.error("Failed to load pricing plans")}finally{N(!1)}},P=r=>{if(!e){t.push("/auth/signin?callbackUrl=/pricing");return}0===r.monthlyPrice?t.push("/dashboard"):t.push(`/subscription/checkout?plan=${r.id}&billing=${w?"yearly":"monthly"}`)},k=e=>{switch(e){case"basicReporting":case"advancedAnalytics":return s.jsx(u.Z,{className:"h-4 w-4"});case"prioritySupport":case"emailSupport":return s.jsx(m,{className:"h-4 w-4"});case"advancedSecurity":return s.jsx(p.Z,{className:"h-4 w-4"});case"apiAccess":return s.jsx(f.Z,{className:"h-4 w-4"});default:return s.jsx(x.Z,{className:"h-4 w-4"})}},M=e=>({basicReporting:"Basic Reporting",emailSupport:"Email Support",mobileApp:"Mobile App Access",advancedAnalytics:"Advanced Analytics",customBranding:"Custom Branding",apiAccess:"API Access",prioritySupport:"Priority Support",customIntegrations:"Custom Integrations",advancedSecurity:"Advanced Security",dedicatedManager:"Dedicated Account Manager"})[e]||e;return j?s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),s.jsx("p",{className:"mt-4 text-gray-600",children:"Loading pricing plans..."})]})}):s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 py-16",children:[(0,s.jsxs)("div",{className:"text-center mb-16",children:[s.jsx("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Choose Your Perfect Plan"}),s.jsx("p",{className:"text-xl text-gray-600 mb-8 max-w-2xl mx-auto",children:"Start with our free plan and upgrade as your business grows. All plans include our core features with varying limits."}),(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-4 mb-8",children:[s.jsx("span",{className:`text-sm font-medium ${w?"text-gray-500":"text-blue-600"}`,children:"Monthly"}),s.jsx(d.r,{checked:w,onCheckedChange:S,className:"data-[state=checked]:bg-blue-600"}),s.jsx("span",{className:`text-sm font-medium ${w?"text-blue-600":"text-gray-500"}`,children:"Yearly"}),r.some(e=>e.yearlyDiscount>0)&&(0,s.jsxs)(c.C,{variant:"secondary",className:"ml-2",children:["Save up to ",Math.max(...r.map(e=>e.yearlyDiscount)),"%"]})]})]}),s.jsx("div",{className:"grid md:grid-cols-3 gap-8 max-w-6xl mx-auto",children:r.map(e=>{let t=w&&null!==e.yearlyPrice?e.yearlyPrice/12:e.monthlyPrice,r=e.monthlyPrice,a=w&&null!==e.yearlyPrice&&e.yearlyDiscount>0;return(0,s.jsxs)(o.Zb,{className:`relative ${e.isPopular?"ring-2 ring-blue-500 shadow-lg scale-105":"shadow-md"} hover:shadow-xl transition-all duration-300`,children:[e.isPopular&&s.jsx("div",{className:"absolute -top-4 left-1/2 transform -translate-x-1/2",children:(0,s.jsxs)(c.C,{className:"bg-blue-600 text-white px-4 py-1",children:[s.jsx(h.Z,{className:"h-3 w-3 mr-1"}),"Most Popular"]})}),(0,s.jsxs)(o.Ol,{className:"text-center pb-4",children:[s.jsx(o.ll,{className:"text-2xl font-bold",children:e.name}),s.jsx(o.SZ,{className:"text-gray-600",children:e.description}),(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsxs)("div",{className:"flex items-baseline justify-center",children:[(0,s.jsxs)("span",{className:"text-4xl font-bold text-gray-900",children:["$",t.toFixed(0)]}),s.jsx("span",{className:"text-gray-500 ml-1",children:"/month"})]}),a&&(0,s.jsxs)("div",{className:"flex items-center justify-center mt-2",children:[(0,s.jsxs)("span",{className:"text-sm text-gray-500 line-through mr-2",children:["$",r,"/month"]}),(0,s.jsxs)(c.C,{variant:"secondary",className:"text-xs",children:[e.yearlyDiscount,"% off"]})]}),w&&null!==e.yearlyPrice&&(0,s.jsxs)("p",{className:"text-sm text-gray-500 mt-1",children:["Billed annually ($",e.yearlyPrice,"/year)"]})]})]}),(0,s.jsxs)(o.aY,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("span",{className:"flex items-center",children:[s.jsx(g.Z,{className:"h-4 w-4 mr-2 text-gray-400"}),"Users"]}),s.jsx("span",{className:"font-medium",children:e.maxUsers})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("span",{className:"flex items-center",children:[s.jsx(y.Z,{className:"h-4 w-4 mr-2 text-gray-400"}),"Storage"]}),s.jsx("span",{className:"font-medium",children:e.formattedStorage})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[s.jsx("span",{children:"Customers"}),s.jsx("span",{className:"font-medium",children:e.maxCustomers.toLocaleString()})]})]}),s.jsx("hr",{className:"my-4"}),s.jsx("div",{className:"space-y-2",children:Object.entries(e.features).map(([e,t])=>t&&(0,s.jsxs)("div",{className:"flex items-center text-sm",children:[s.jsx("div",{className:"text-green-500 mr-2",children:k(e)}),s.jsx("span",{children:M(e)})]},e))}),e.trialDays>0&&s.jsx("div",{className:"mt-4 p-3 bg-blue-50 rounded-lg",children:(0,s.jsxs)("p",{className:"text-sm text-blue-700 font-medium",children:[e.trialDays,"-day free trial"]})})]}),s.jsx(o.eW,{children:s.jsx(l.z,{className:`w-full ${e.isPopular?"bg-blue-600 hover:bg-blue-700":""}`,variant:e.isPopular?"default":"outline",onClick:()=>P(e),children:0===e.monthlyPrice?"Get Started Free":"Start Free Trial"})})]},e.id)})}),s.jsx("div",{className:"text-center mt-16",children:(0,s.jsxs)("p",{className:"text-gray-600",children:["Need a custom plan? "," ",s.jsx("a",{href:"/contact",className:"text-blue-600 hover:underline",children:"Contact our sales team"})]})})]})})}},56189:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Providers:()=>d});var s=r(95344),a=r(47674),n=r(6256),i=r(19115),o=r(26274),l=r(3729),c=r(66091);function d({children:e}){let[t]=(0,l.useState)(()=>new i.S({defaultOptions:{queries:{staleTime:6e4,retry:1}}}));return s.jsx(a.SessionProvider,{children:s.jsx(o.aH,{client:t,children:s.jsx(c.lY,{children:s.jsx(n.f,{attribute:"class",defaultTheme:"light",enableSystem:!0,disableTransitionOnChange:!0,children:e})})})})}},69436:(e,t,r)=>{"use strict";r.d(t,{C:()=>o});var s=r(95344);r(3729);var a=r(49247),n=r(91626);let i=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600",info:"border-transparent bg-blue-500 text-white hover:bg-blue-600"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,...r}){return s.jsx("div",{className:(0,n.cn)(i({variant:t}),e),...r})}},16212:(e,t,r)=>{"use strict";r.d(t,{z:()=>c});var s=r(95344),a=r(3729),n=r(32751),i=r(49247),o=r(91626);let l=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef(({className:e,variant:t,size:r,asChild:a=!1,...i},c)=>{let d=a?n.g7:"button";return s.jsx(d,{className:(0,o.cn)(l({variant:t,size:r,className:e})),ref:c,...i})});c.displayName="Button"},61351:(e,t,r)=>{"use strict";r.d(t,{Ol:()=>o,SZ:()=>c,Zb:()=>i,aY:()=>d,eW:()=>u,ll:()=>l});var s=r(95344),a=r(3729),n=r(91626);let i=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));i.displayName="Card";let o=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...t}));o.displayName="CardHeader";let l=a.forwardRef(({className:e,...t},r)=>s.jsx("h3",{ref:r,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let c=a.forwardRef(({className:e,...t},r)=>s.jsx("p",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",e),...t}));c.displayName="CardDescription";let d=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,n.cn)("p-6 pt-0",e),...t}));d.displayName="CardContent";let u=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",e),...t}));u.displayName="CardFooter"},71809:(e,t,r)=>{"use strict";r.d(t,{r:()=>w});var s=r(95344),a=r(3729),n=r(85222),i=r(31405),o=r(98462),l=r(33183),c=r(92062),d=r(63085),u=r(62409),m="Switch",[p,f]=(0,o.b)(m),[x,h]=p(m),g=a.forwardRef((e,t)=>{let{__scopeSwitch:r,name:o,checked:c,defaultChecked:d,required:p,disabled:f,value:h="on",onCheckedChange:g,form:y,...b}=e,[N,w]=a.useState(null),S=(0,i.e)(t,e=>w(e)),C=a.useRef(!1),P=!N||y||!!N.closest("form"),[k,M]=(0,l.T)({prop:c,defaultProp:d??!1,onChange:g,caller:m});return(0,s.jsxs)(x,{scope:r,checked:k,disabled:f,children:[(0,s.jsx)(u.WV.button,{type:"button",role:"switch","aria-checked":k,"aria-required":p,"data-state":j(k),"data-disabled":f?"":void 0,disabled:f,value:h,...b,ref:S,onClick:(0,n.M)(e.onClick,e=>{M(e=>!e),P&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),P&&(0,s.jsx)(v,{control:N,bubbles:!C.current,name:o,value:h,checked:k,required:p,disabled:f,form:y,style:{transform:"translateX(-100%)"}})]})});g.displayName=m;var y="SwitchThumb",b=a.forwardRef((e,t)=>{let{__scopeSwitch:r,...a}=e,n=h(y,r);return(0,s.jsx)(u.WV.span,{"data-state":j(n.checked),"data-disabled":n.disabled?"":void 0,...a,ref:t})});b.displayName=y;var v=a.forwardRef(({__scopeSwitch:e,control:t,checked:r,bubbles:n=!0,...o},l)=>{let u=a.useRef(null),m=(0,i.e)(u,l),p=(0,c.D)(r),f=(0,d.t)(t);return a.useEffect(()=>{let e=u.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(p!==r&&t){let s=new Event("click",{bubbles:n});t.call(e,r),e.dispatchEvent(s)}},[p,r,n]),(0,s.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:r,...o,tabIndex:-1,ref:m,style:{...o.style,...f,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function j(e){return e?"checked":"unchecked"}v.displayName="SwitchBubbleInput";var N=r(91626);let w=a.forwardRef(({className:e,...t},r)=>s.jsx(g,{className:(0,N.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...t,ref:r,children:s.jsx(b,{className:(0,N.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));w.displayName=g.displayName},66091:(e,t,r)=>{"use strict";r.d(t,{TC:()=>l,lY:()=>o});var s=r(95344),a=r(3729);let n={appName:"SaaS Platform",logoUrl:"",faviconUrl:"",primaryColor:"#3b82f6",secondaryColor:"#64748b",accentColor:"#10b981",backgroundColor:"#ffffff",textColor:"#1f2937",theme:"light",fontFamily:"Inter, sans-serif",customCss:""},i=(0,a.createContext)(void 0);function o({children:e}){let[t,r]=(0,a.useState)(n),[o,l]=(0,a.useState)(!0);(0,a.useEffect)(()=>{c()},[]);let c=async()=>{try{let e=await fetch("/api/global-config/branding"),t=await e.json();t.success&&t.branding?(r({...n,...t.branding}),d({...n,...t.branding})):d(n)}catch(e){console.error("Error fetching branding config:",e),d(n)}finally{l(!1)}},d=e=>{let t=document.documentElement;if(t.style.setProperty("--primary-color",e.primaryColor),t.style.setProperty("--secondary-color",e.secondaryColor),t.style.setProperty("--accent-color",e.accentColor),t.style.setProperty("--background-color",e.backgroundColor),t.style.setProperty("--text-color",e.textColor),t.style.setProperty("--font-family",e.fontFamily),document.body.className=document.body.className.replace(/theme-\w+/g,""),document.body.classList.add(`theme-${e.theme}`),document.title=e.appName,e.faviconUrl){let t=document.querySelector('link[rel="icon"]');t||((t=document.createElement("link")).rel="icon",document.head.appendChild(t)),t.href=e.faviconUrl}let r=document.getElementById("custom-branding-css");e.customCss?(r||((r=document.createElement("style")).id="custom-branding-css",document.head.appendChild(r)),r.textContent=e.customCss):r&&r.remove();let s=document.querySelector('meta[name="theme-color"]');s||((s=document.createElement("meta")).name="theme-color",document.head.appendChild(s)),s.content=e.primaryColor};return s.jsx(i.Provider,{value:{branding:t,updateBranding:e=>{let s={...t,...e};r(s),d(s)},loading:o},children:e})}function l(){let e=(0,a.useContext)(i);if(void 0===e)throw Error("useBranding must be used within a BrandingProvider");return e}},91626:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(56815),a=r(79377);function n(...e){return(0,a.m6)((0,s.W)(e))}},50340:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(69224).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},62312:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(69224).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},99046:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(69224).Z)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},23485:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(69224).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},76755:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(69224).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},89895:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(69224).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},79200:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(69224).Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]])},22254:(e,t,r)=>{e.exports=r(14767)},59504:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p,metadata:()=>m});var s=r(25036),a=r(80265),n=r.n(a),i=r(86843);let o=(0,i.createProxy)(String.raw`C:\proj\nextjs-saas\components\providers.tsx`),{__esModule:l,$$typeof:c}=o;o.default;let d=(0,i.createProxy)(String.raw`C:\proj\nextjs-saas\components\providers.tsx#Providers`);var u=r(69636);r(67272);let m={title:{default:"Business SaaS - Complete Business Management Solution",template:"%s | Business SaaS"},description:"Complete SaaS solution for business management including CRM, invoicing, quotations, and more.",keywords:["SaaS","Business Management","CRM","Invoicing","Quotations"],authors:[{name:"Business SaaS Team"}],creator:"Business SaaS",openGraph:{type:"website",locale:"en_US",url:process.env.NEXT_PUBLIC_APP_URL,title:"Business SaaS - Complete Business Management Solution",description:"Complete SaaS solution for business management including CRM, invoicing, quotations, and more.",siteName:"Business SaaS"},twitter:{card:"summary_large_image",title:"Business SaaS - Complete Business Management Solution",description:"Complete SaaS solution for business management including CRM, invoicing, quotations, and more.",creator:"@businesssaas"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function p({children:e}){return s.jsx("html",{lang:"en",suppressHydrationWarning:!0,children:s.jsx("body",{className:n().className,children:(0,s.jsxs)(d,{children:[e,s.jsx(u.x7,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#4ade80",secondary:"#fff"}},error:{duration:4e3,iconTheme:{primary:"#ef4444",secondary:"#fff"}}}})]})})})}},81125:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>n,__esModule:()=>a,default:()=>i});let s=(0,r(86843).createProxy)(String.raw`C:\proj\nextjs-saas\app\pricing\page.tsx`),{__esModule:a,$$typeof:n}=s,i=s.default},67272:()=>{},85222:(e,t,r)=>{"use strict";function s(e,t,{checkForDefaultPrevented:r=!0}={}){return function(s){if(e?.(s),!1===r||!s.defaultPrevented)return t?.(s)}}r.d(t,{M:()=>s})},98462:(e,t,r)=>{"use strict";r.d(t,{b:()=>i,k:()=>n});var s=r(3729),a=r(95344);function n(e,t){let r=s.createContext(t),n=e=>{let{children:t,...n}=e,i=s.useMemo(()=>n,Object.values(n));return(0,a.jsx)(r.Provider,{value:i,children:t})};return n.displayName=e+"Provider",[n,function(a){let n=s.useContext(r);if(n)return n;if(void 0!==t)return t;throw Error(`\`${a}\` must be used within \`${e}\``)}]}function i(e,t=[]){let r=[],n=()=>{let t=r.map(e=>s.createContext(e));return function(r){let a=r?.[e]||t;return s.useMemo(()=>({[`__scope${e}`]:{...r,[e]:a}}),[r,a])}};return n.scopeName=e,[function(t,n){let i=s.createContext(n),o=r.length;r=[...r,n];let l=t=>{let{scope:r,children:n,...l}=t,c=r?.[e]?.[o]||i,d=s.useMemo(()=>l,Object.values(l));return(0,a.jsx)(c.Provider,{value:d,children:n})};return l.displayName=t+"Provider",[l,function(r,a){let l=a?.[e]?.[o]||i,c=s.useContext(l);if(c)return c;if(void 0!==n)return n;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let a=r.reduce((t,{useScope:r,scopeName:s})=>{let a=r(e)[`__scope${s}`];return{...t,...a}},{});return s.useMemo(()=>({[`__scope${t.scopeName}`]:a}),[a])}};return r.scopeName=t.scopeName,r}(n,...t)]}},62409:(e,t,r)=>{"use strict";r.d(t,{WV:()=>o,jH:()=>l});var s=r(3729),a=r(81202),n=r(32751),i=r(95344),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,n.Z8)(`Primitive.${t}`),a=s.forwardRef((e,s)=>{let{asChild:a,...n}=e,o=a?r:t;return(0,i.jsx)(o,{...n,ref:s})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{});function l(e,t){e&&a.flushSync(()=>e.dispatchEvent(t))}},33183:(e,t,r)=>{"use strict";r.d(t,{T:()=>o});var s,a=r(3729),n=r(16069),i=(s||(s=r.t(a,2)))[" useInsertionEffect ".trim().toString()]||n.b;function o({prop:e,defaultProp:t,onChange:r=()=>{},caller:s}){let[n,o,l]=function({defaultProp:e,onChange:t}){let[r,s]=a.useState(e),n=a.useRef(r),o=a.useRef(t);return i(()=>{o.current=t},[t]),a.useEffect(()=>{n.current!==r&&(o.current?.(r),n.current=r)},[r,n]),[r,s,o]}({defaultProp:t,onChange:r}),c=void 0!==e,d=c?e:n;{let t=a.useRef(void 0!==e);a.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${s} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,s])}return[d,a.useCallback(t=>{if(c){let r="function"==typeof t?t(e):t;r!==e&&l.current?.(r)}else o(t)},[c,e,o,l])]}Symbol("RADIX:SYNC_STATE")},16069:(e,t,r)=>{"use strict";r.d(t,{b:()=>a});var s=r(3729),a=globalThis?.document?s.useLayoutEffect:()=>{}},92062:(e,t,r)=>{"use strict";r.d(t,{D:()=>a});var s=r(3729);function a(e){let t=s.useRef({value:e,previous:e});return s.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},63085:(e,t,r)=>{"use strict";r.d(t,{t:()=>n});var s=r(3729),a=r(16069);function n(e){let[t,r]=s.useState(void 0);return(0,a.b)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let s,a;if(!Array.isArray(t)||!t.length)return;let n=t[0];if("borderBoxSize"in n){let e=n.borderBoxSize,t=Array.isArray(e)?e[0]:e;s=t.inlineSize,a=t.blockSize}else s=e.offsetWidth,a=e.offsetHeight;r({width:s,height:a})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1638,7948,6671,4755],()=>r(85129));module.exports=s})();