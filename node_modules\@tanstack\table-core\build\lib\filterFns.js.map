{"version": 3, "file": "filterFns.js", "sources": ["../../src/filterFns.ts"], "sourcesContent": ["import { FilterFn } from './features/ColumnFiltering'\n\nconst includesString: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: string\n) => {\n  const search = filterValue?.toString()?.toLowerCase()\n  return Boolean(\n    row\n      .getValue<string | null>(columnId)\n      ?.toString()\n      ?.toLowerCase()\n      ?.includes(search)\n  )\n}\n\nincludesString.autoRemove = (val: any) => testFalsey(val)\n\nconst includesStringSensitive: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: string\n) => {\n  return Boolean(\n    row.getValue<string | null>(columnId)?.toString()?.includes(filterValue)\n  )\n}\n\nincludesStringSensitive.autoRemove = (val: any) => testFalsey(val)\n\nconst equalsString: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: string\n) => {\n  return (\n    row.getValue<string | null>(columnId)?.toString()?.toLowerCase() ===\n    filterValue?.toLowerCase()\n  )\n}\n\nequalsString.autoRemove = (val: any) => testFalsey(val)\n\nconst arrIncludes: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: unknown\n) => {\n  return row.getValue<unknown[]>(columnId)?.includes(filterValue)\n}\n\narrIncludes.autoRemove = (val: any) => testFalsey(val)\n\nconst arrIncludesAll: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: unknown[]\n) => {\n  return !filterValue.some(\n    val => !row.getValue<unknown[]>(columnId)?.includes(val)\n  )\n}\n\narrIncludesAll.autoRemove = (val: any) => testFalsey(val) || !val?.length\n\nconst arrIncludesSome: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: unknown[]\n) => {\n  return filterValue.some(val =>\n    row.getValue<unknown[]>(columnId)?.includes(val)\n  )\n}\n\narrIncludesSome.autoRemove = (val: any) => testFalsey(val) || !val?.length\n\nconst equals: FilterFn<any> = (row, columnId: string, filterValue: unknown) => {\n  return row.getValue(columnId) === filterValue\n}\n\nequals.autoRemove = (val: any) => testFalsey(val)\n\nconst weakEquals: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: unknown\n) => {\n  return row.getValue(columnId) == filterValue\n}\n\nweakEquals.autoRemove = (val: any) => testFalsey(val)\n\nconst inNumberRange: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: [number, number]\n) => {\n  let [min, max] = filterValue\n\n  const rowValue = row.getValue<number>(columnId)\n  return rowValue >= min && rowValue <= max\n}\n\ninNumberRange.resolveFilterValue = (val: [any, any]) => {\n  let [unsafeMin, unsafeMax] = val\n\n  let parsedMin =\n    typeof unsafeMin !== 'number' ? parseFloat(unsafeMin as string) : unsafeMin\n  let parsedMax =\n    typeof unsafeMax !== 'number' ? parseFloat(unsafeMax as string) : unsafeMax\n\n  let min =\n    unsafeMin === null || Number.isNaN(parsedMin) ? -Infinity : parsedMin\n  let max = unsafeMax === null || Number.isNaN(parsedMax) ? Infinity : parsedMax\n\n  if (min > max) {\n    const temp = min\n    min = max\n    max = temp\n  }\n\n  return [min, max] as const\n}\n\ninNumberRange.autoRemove = (val: any) =>\n  testFalsey(val) || (testFalsey(val[0]) && testFalsey(val[1]))\n\n// Export\n\nexport const filterFns = {\n  includesString,\n  includesStringSensitive,\n  equalsString,\n  arrIncludes,\n  arrIncludesAll,\n  arrIncludesSome,\n  equals,\n  weakEquals,\n  inNumberRange,\n}\n\nexport type BuiltInFilterFn = keyof typeof filterFns\n\n// Utils\n\nfunction testFalsey(val: any) {\n  return val === undefined || val === null || val === ''\n}\n"], "names": ["includesString", "row", "columnId", "filterValue", "_filterValue$toString", "_row$getValue", "search", "toString", "toLowerCase", "Boolean", "getValue", "includes", "autoRemove", "val", "<PERSON><PERSON><PERSON><PERSON>", "includesStringSensitive", "_row$getValue2", "equalsString", "_row$getValue3", "arrIncludes", "_row$getValue4", "arrIncludesAll", "some", "_row$getValue5", "length", "arrIncludesSome", "_row$getValue6", "equals", "weakEquals", "inNumberRange", "min", "max", "rowValue", "resolveFilterValue", "unsafeMin", "unsafeMax", "parsedMin", "parseFloat", "parsedMax", "Number", "isNaN", "Infinity", "temp", "filterFns", "undefined"], "mappings": ";;;;;;;;;;;;AAEA,MAAMA,cAA6B,GAAGA,CACpCC,GAAG,EACHC,QAAgB,EAChBC,WAAmB,KAChB;EAAA,IAAAC,qBAAA,EAAAC,aAAA,CAAA;AACH,EAAA,MAAMC,MAAM,GAAGH,WAAW,IAAAC,IAAAA,IAAAA,CAAAA,qBAAA,GAAXD,WAAW,CAAEI,QAAQ,EAAE,KAAvBH,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,qBAAA,CAAyBI,WAAW,EAAE,CAAA;AACrD,EAAA,OAAOC,OAAO,CAAA,CAAAJ,aAAA,GACZJ,GAAG,CACAS,QAAQ,CAAgBR,QAAQ,CAAC,KAAA,IAAA,IAAA,CAAAG,aAAA,GADpCA,aAAA,CAEIE,QAAQ,EAAE,KAAAF,IAAAA,IAAAA,CAAAA,aAAA,GAFdA,aAAA,CAGIG,WAAW,EAAE,KAAA,IAAA,GAAA,KAAA,CAAA,GAHjBH,aAAA,CAIIM,QAAQ,CAACL,MAAM,CACrB,CAAC,CAAA;AACH,CAAC,CAAA;AAEDN,cAAc,CAACY,UAAU,GAAIC,GAAQ,IAAKC,UAAU,CAACD,GAAG,CAAC,CAAA;AAEzD,MAAME,uBAAsC,GAAGA,CAC7Cd,GAAG,EACHC,QAAgB,EAChBC,WAAmB,KAChB;AAAA,EAAA,IAAAa,cAAA,CAAA;EACH,OAAOP,OAAO,CAAAO,CAAAA,cAAA,GACZf,GAAG,CAACS,QAAQ,CAAgBR,QAAQ,CAAC,KAAAc,IAAAA,IAAAA,CAAAA,cAAA,GAArCA,cAAA,CAAuCT,QAAQ,EAAE,KAAA,IAAA,GAAA,KAAA,CAAA,GAAjDS,cAAA,CAAmDL,QAAQ,CAACR,WAAW,CACzE,CAAC,CAAA;AACH,CAAC,CAAA;AAEDY,uBAAuB,CAACH,UAAU,GAAIC,GAAQ,IAAKC,UAAU,CAACD,GAAG,CAAC,CAAA;AAElE,MAAMI,YAA2B,GAAGA,CAClChB,GAAG,EACHC,QAAgB,EAChBC,WAAmB,KAChB;AAAA,EAAA,IAAAe,cAAA,CAAA;AACH,EAAA,OACE,CAAAA,CAAAA,cAAA,GAAAjB,GAAG,CAACS,QAAQ,CAAgBR,QAAQ,CAAC,KAAA,IAAA,IAAA,CAAAgB,cAAA,GAArCA,cAAA,CAAuCX,QAAQ,EAAE,KAAjDW,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,cAAA,CAAmDV,WAAW,EAAE,OAChEL,WAAW,IAAXA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,WAAW,CAAEK,WAAW,EAAE,CAAA,CAAA;AAE9B,CAAC,CAAA;AAEDS,YAAY,CAACL,UAAU,GAAIC,GAAQ,IAAKC,UAAU,CAACD,GAAG,CAAC,CAAA;AAEvD,MAAMM,WAA0B,GAAGA,CACjClB,GAAG,EACHC,QAAgB,EAChBC,WAAoB,KACjB;AAAA,EAAA,IAAAiB,cAAA,CAAA;AACH,EAAA,OAAA,CAAAA,cAAA,GAAOnB,GAAG,CAACS,QAAQ,CAAYR,QAAQ,CAAC,KAAA,IAAA,GAAA,KAAA,CAAA,GAAjCkB,cAAA,CAAmCT,QAAQ,CAACR,WAAW,CAAC,CAAA;AACjE,CAAC,CAAA;AAEDgB,WAAW,CAACP,UAAU,GAAIC,GAAQ,IAAKC,UAAU,CAACD,GAAG,CAAC,CAAA;AAEtD,MAAMQ,cAA6B,GAAGA,CACpCpB,GAAG,EACHC,QAAgB,EAChBC,WAAsB,KACnB;AACH,EAAA,OAAO,CAACA,WAAW,CAACmB,IAAI,CACtBT,GAAG,IAAA;AAAA,IAAA,IAAAU,cAAA,CAAA;AAAA,IAAA,OAAI,EAAAA,CAAAA,cAAA,GAACtB,GAAG,CAACS,QAAQ,CAAYR,QAAQ,CAAC,aAAjCqB,cAAA,CAAmCZ,QAAQ,CAACE,GAAG,CAAC,CAAA,CAAA;AAAA,GAC1D,CAAC,CAAA;AACH,CAAC,CAAA;AAEDQ,cAAc,CAACT,UAAU,GAAIC,GAAQ,IAAKC,UAAU,CAACD,GAAG,CAAC,IAAI,EAACA,GAAG,IAAHA,IAAAA,IAAAA,GAAG,CAAEW,MAAM,CAAA,CAAA;AAEzE,MAAMC,eAA8B,GAAGA,CACrCxB,GAAG,EACHC,QAAgB,EAChBC,WAAsB,KACnB;AACH,EAAA,OAAOA,WAAW,CAACmB,IAAI,CAACT,GAAG,IAAA;AAAA,IAAA,IAAAa,cAAA,CAAA;AAAA,IAAA,OAAA,CAAAA,cAAA,GACzBzB,GAAG,CAACS,QAAQ,CAAYR,QAAQ,CAAC,KAAA,IAAA,GAAA,KAAA,CAAA,GAAjCwB,cAAA,CAAmCf,QAAQ,CAACE,GAAG,CAAC,CAAA;AAAA,GAClD,CAAC,CAAA;AACH,CAAC,CAAA;AAEDY,eAAe,CAACb,UAAU,GAAIC,GAAQ,IAAKC,UAAU,CAACD,GAAG,CAAC,IAAI,EAACA,GAAG,IAAHA,IAAAA,IAAAA,GAAG,CAAEW,MAAM,CAAA,CAAA;AAE1E,MAAMG,MAAqB,GAAGA,CAAC1B,GAAG,EAAEC,QAAgB,EAAEC,WAAoB,KAAK;AAC7E,EAAA,OAAOF,GAAG,CAACS,QAAQ,CAACR,QAAQ,CAAC,KAAKC,WAAW,CAAA;AAC/C,CAAC,CAAA;AAEDwB,MAAM,CAACf,UAAU,GAAIC,GAAQ,IAAKC,UAAU,CAACD,GAAG,CAAC,CAAA;AAEjD,MAAMe,UAAyB,GAAGA,CAChC3B,GAAG,EACHC,QAAgB,EAChBC,WAAoB,KACjB;AACH,EAAA,OAAOF,GAAG,CAACS,QAAQ,CAACR,QAAQ,CAAC,IAAIC,WAAW,CAAA;AAC9C,CAAC,CAAA;AAEDyB,UAAU,CAAChB,UAAU,GAAIC,GAAQ,IAAKC,UAAU,CAACD,GAAG,CAAC,CAAA;AAErD,MAAMgB,aAA4B,GAAGA,CACnC5B,GAAG,EACHC,QAAgB,EAChBC,WAA6B,KAC1B;AACH,EAAA,IAAI,CAAC2B,GAAG,EAAEC,GAAG,CAAC,GAAG5B,WAAW,CAAA;AAE5B,EAAA,MAAM6B,QAAQ,GAAG/B,GAAG,CAACS,QAAQ,CAASR,QAAQ,CAAC,CAAA;AAC/C,EAAA,OAAO8B,QAAQ,IAAIF,GAAG,IAAIE,QAAQ,IAAID,GAAG,CAAA;AAC3C,CAAC,CAAA;AAEDF,aAAa,CAACI,kBAAkB,GAAIpB,GAAe,IAAK;AACtD,EAAA,IAAI,CAACqB,SAAS,EAAEC,SAAS,CAAC,GAAGtB,GAAG,CAAA;AAEhC,EAAA,IAAIuB,SAAS,GACX,OAAOF,SAAS,KAAK,QAAQ,GAAGG,UAAU,CAACH,SAAmB,CAAC,GAAGA,SAAS,CAAA;AAC7E,EAAA,IAAII,SAAS,GACX,OAAOH,SAAS,KAAK,QAAQ,GAAGE,UAAU,CAACF,SAAmB,CAAC,GAAGA,SAAS,CAAA;AAE7E,EAAA,IAAIL,GAAG,GACLI,SAAS,KAAK,IAAI,IAAIK,MAAM,CAACC,KAAK,CAACJ,SAAS,CAAC,GAAG,CAACK,QAAQ,GAAGL,SAAS,CAAA;AACvE,EAAA,IAAIL,GAAG,GAAGI,SAAS,KAAK,IAAI,IAAII,MAAM,CAACC,KAAK,CAACF,SAAS,CAAC,GAAGG,QAAQ,GAAGH,SAAS,CAAA;EAE9E,IAAIR,GAAG,GAAGC,GAAG,EAAE;IACb,MAAMW,IAAI,GAAGZ,GAAG,CAAA;AAChBA,IAAAA,GAAG,GAAGC,GAAG,CAAA;AACTA,IAAAA,GAAG,GAAGW,IAAI,CAAA;AACZ,GAAA;AAEA,EAAA,OAAO,CAACZ,GAAG,EAAEC,GAAG,CAAC,CAAA;AACnB,CAAC,CAAA;AAEDF,aAAa,CAACjB,UAAU,GAAIC,GAAQ,IAClCC,UAAU,CAACD,GAAG,CAAC,IAAKC,UAAU,CAACD,GAAG,CAAC,CAAC,CAAC,CAAC,IAAIC,UAAU,CAACD,GAAG,CAAC,CAAC,CAAC,CAAE,CAAA;;AAE/D;;AAEO,MAAM8B,SAAS,GAAG;EACvB3C,cAAc;EACde,uBAAuB;EACvBE,YAAY;EACZE,WAAW;EACXE,cAAc;EACdI,eAAe;EACfE,MAAM;EACNC,UAAU;AACVC,EAAAA,aAAAA;AACF,EAAC;AAID;;AAEA,SAASf,UAAUA,CAACD,GAAQ,EAAE;EAC5B,OAAOA,GAAG,KAAK+B,SAAS,IAAI/B,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,EAAE,CAAA;AACxD;;;;"}