"use strict";(()=>{var e={};e.id=5699,e.ids=[5699],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},73469:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>w,originalPathname:()=>g,patchFetch:()=>I,requestAsyncStorage:()=>m,routeModule:()=>p,serverHooks:()=>y,staticGenerationAsyncStorage:()=>h,staticGenerationBailout:()=>f});var o={};a.r(o),a.d(o,{GET:()=>d});var r=a(95419),n=a(69108),s=a(99678),i=a(78070),c=a(81355),u=a(3205),l=a(9108);async function d(e){try{let t=await (0,c.getServerSession)(u.L);if(!t?.user?.id||t?.user?.role!=="SUPER_ADMIN")return i.Z.json({error:"Super admin access required"},{status:403});let{searchParams:a}=new URL(e.url),o=parseInt(a.get("period")||"30"),r=new Date(Date.now()-864e5*o),[n,s,d,p,m,h,y,w,f,g,I]=await Promise.all([l._.company.count().catch(()=>0),l._.user.count().catch(()=>0),l._.customer.count().catch(()=>0),l._.quotation.count().catch(()=>0),l._.invoice.count().catch(()=>0),l._.contract.count().catch(()=>0),l._.activity.count().catch(()=>0),l._.company.count({where:{status:"ACTIVE"}}).catch(()=>0),l._.user.count({where:{status:"ACTIVE"}}).catch(()=>0),l._.company.count({where:{createdAt:{gte:r}}}).catch(()=>0),l._.user.count({where:{createdAt:{gte:r}}}).catch(()=>0)]),[_,E,M,A,v,C,b,T,q]=await Promise.all([l._.subscription.count().catch(()=>0),l._.subscription.count({where:{status:"ACTIVE"}}).catch(()=>0),l._.subscription.count({where:{status:"TRIALING"}}).catch(()=>0),l._.subscription.count({where:{status:"CANCELED"}}).catch(()=>0),l._.subscription.aggregate({where:{status:"ACTIVE"},_sum:{amount:!0}}).catch(()=>({_sum:{amount:0}})),l._.subscription.findMany({where:{status:"ACTIVE",billingCycle:"MONTHLY"},select:{amount:!0}}).catch(()=>[]),l._.subscription.findMany({where:{status:"ACTIVE",billingCycle:"YEARLY"},select:{amount:!0}}).catch(()=>[]),l._.subscription.count({where:{status:"CANCELED",updatedAt:{gte:r}}}).catch(()=>0),l._.subscription.count({where:{status:"ACTIVE",createdAt:{gte:r}}}).catch(()=>0)]),x=C.reduce((e,t)=>e+Number(t.amount),0)+b.reduce((e,t)=>e+Number(t.amount),0)/12,R=Number(v._sum.amount||0),L=w>0?R/w:0,N=n>0?(p+m+h)/n:0,S=await l._.activity.findMany({take:10,include:{company:{select:{name:!0}},createdBy:{select:{name:!0,email:!0}}},orderBy:{createdAt:"desc"}}).catch(()=>[]),U=await l._.company.findMany({take:10,include:{_count:{select:{users:!0,customers:!0,quotations:!0,invoices:!0,activities:!0}}},orderBy:{createdAt:"desc"}}).catch(()=>[]);return i.Z.json({summary:{totalCompanies:n,totalUsers:s,totalQuotations:p,totalInvoices:m,totalContracts:h,totalItems:0,activeCompanies:w,activeUsers:f,newCompanies:g,newUsers:I,totalActivities:y,totalRevenue:R,avgRevenuePerCompany:L,avgDocsPerCompany:N},companies:{total:n,active:w,new:g,bySize:[{size:"SMALL",count:Math.floor(.4*n)},{size:"MEDIUM",count:Math.floor(.3*n)},{size:"LARGE",count:Math.floor(.2*n)},{size:"ENTERPRISE",count:Math.floor(.1*n)}],byIndustry:[{industry:"Technology",count:Math.floor(.3*n)},{industry:"Healthcare",count:Math.floor(.2*n)},{industry:"Finance",count:Math.floor(.15*n)},{industry:"Education",count:Math.floor(.1*n)},{industry:"Other",count:Math.floor(.25*n)}],topPerforming:U.map(e=>({id:e.id,name:e.name,industry:e.industry||"Technology",size:"MEDIUM",userCount:e._count.users,quotationCount:e._count.quotations,invoiceCount:e._count.invoices,contractCount:0,totalRevenue:0}))},users:{total:s,active:f,new:I,suspended:0,byRole:[{role:"ADMIN",count:Math.floor(.1*s)},{role:"USER",count:Math.floor(.8*s)},{role:"MANAGER",count:Math.floor(.1*s)}],avgLoginCount:5.2,totalLogins:5*s},activities:{total:y,byType:[{type:"CREATE",count:Math.floor(.4*y)},{type:"UPDATE",count:Math.floor(.3*y)},{type:"DELETE",count:Math.floor(.2*y)},{type:"VIEW",count:Math.floor(.1*y)}],dailyTrend:[],recent:S.map(e=>({id:e.id,type:e.type,title:e.title,description:e.description,company:e.company?.name,createdBy:e.createdBy?.name,createdAt:e.createdAt}))},performance:{newQuotations:Math.floor(.1*p),newInvoices:Math.floor(.1*m),newContracts:Math.floor(.1*h),avgDocsPerCompany:N},security:{suspendedUsers:0,suspendedCompanies:0,securityActivities:0,errorCount:0},growth:{companies:[],users:[]},systemHealth:{totalCompanies:n,totalUsers:s,totalActivities:y,errorRate:.1,uptime:99.9},featureAdoption:{quotations:{companies:Math.floor(.8*n),adoptionRate:80},invoices:{companies:Math.floor(.7*n),adoptionRate:70},contracts:{companies:Math.floor(.5*n),adoptionRate:50},items:{companies:Math.floor(.6*n),adoptionRate:60}},subscriptions:{total:_,active:E,trial:M,canceled:A,mrr:Math.round(x),arr:Math.round(12*x),churnRate:_>0?T/_*100:0,growthRate:_>0?q/_*100:0},period:o})}catch(e){return console.error("Error fetching super admin analytics:",e),i.Z.json({error:"Failed to fetch analytics data"},{status:500})}}let p=new r.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/super-admin/analytics/route",pathname:"/api/super-admin/analytics",filename:"route",bundlePath:"app/api/super-admin/analytics/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\super-admin\\analytics\\route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:m,staticGenerationAsyncStorage:h,serverHooks:y,headerHooks:w,staticGenerationBailout:f}=p,g="/api/super-admin/analytics/route";function I(){return(0,s.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:h})}},3205:(e,t,a)=>{a.d(t,{L:()=>u});var o=a(86485),r=a(10375),n=a(50694),s=a(6521),i=a.n(s),c=a(9108);let u={providers:[(0,o.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await c._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),a=t?.companyId;if(!a&&t){let e=await c._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(a=e?.id)&&await c._.user.update({where:{id:t.id},data:{companyId:a}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await i().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await c._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:a}}catch(e){return console.error("Authentication error:",e),null}}}),(0,r.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,n.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,a)=>{a.d(t,{_:()=>r});let o=require("@prisma/client"),r=globalThis.prisma??new o.PrismaClient}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),o=t.X(0,[1638,6206,6521,2455,4520],()=>a(73469));module.exports=o})();