"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/super-admin/analytics/route";
exports.ids = ["app/api/super-admin/analytics/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "./action-async-storage.external?8652":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external?0211":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external?137c":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Fanalytics%2Froute&page=%2Fapi%2Fsuper-admin%2Fanalytics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Fanalytics%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Fanalytics%2Froute&page=%2Fapi%2Fsuper-admin%2Fanalytics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Fanalytics%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_proj_nextjs_saas_app_api_super_admin_analytics_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/super-admin/analytics/route.ts */ \"(rsc)/./app/api/super-admin/analytics/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/super-admin/analytics/route\",\n        pathname: \"/api/super-admin/analytics\",\n        filename: \"route\",\n        bundlePath: \"app/api/super-admin/analytics/route\"\n    },\n    resolvedPagePath: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\api\\\\super-admin\\\\analytics\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_proj_nextjs_saas_app_api_super_admin_analytics_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/super-admin/analytics/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Fanalytics%2Froute&page=%2Fapi%2Fsuper-admin%2Fanalytics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Fanalytics%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/super-admin/analytics/route.ts":
/*!************************************************!*\
  !*** ./app/api/super-admin/analytics/route.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id || session?.user?.role !== \"SUPER_ADMIN\") {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Super admin access required\"\n            }, {\n                status: 403\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const period = parseInt(searchParams.get(\"period\") || \"30\");\n        const startDate = new Date(Date.now() - period * 24 * 60 * 60 * 1000);\n        // Get basic counts with error handling\n        const [totalCompanies, totalUsers, totalCustomers, totalQuotations, totalInvoices, totalContracts, totalActivities, activeCompanies, activeUsers, newCompanies, newUsers] = await Promise.all([\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.company.count().catch(()=>0),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.count().catch(()=>0),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.customer.count().catch(()=>0),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.quotation.count().catch(()=>0),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.invoice.count().catch(()=>0),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.contract.count().catch(()=>0),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.activity.count().catch(()=>0),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.company.count({\n                where: {\n                    status: \"ACTIVE\"\n                }\n            }).catch(()=>0),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.count({\n                where: {\n                    status: \"ACTIVE\"\n                }\n            }).catch(()=>0),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.company.count({\n                where: {\n                    createdAt: {\n                        gte: startDate\n                    }\n                }\n            }).catch(()=>0),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.count({\n                where: {\n                    createdAt: {\n                        gte: startDate\n                    }\n                }\n            }).catch(()=>0)\n        ]);\n        // Get revenue data safely\n        const revenueData = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.subscription.aggregate({\n            where: {\n                status: \"ACTIVE\"\n            },\n            _sum: {\n                amount: true\n            }\n        }).catch(()=>({\n                _sum: {\n                    amount: 0\n                }\n            }));\n        const totalRevenue = Number(revenueData._sum.amount || 0);\n        const avgRevenuePerCompany = activeCompanies > 0 ? totalRevenue / activeCompanies : 0;\n        const avgDocsPerCompany = totalCompanies > 0 ? (totalQuotations + totalInvoices + totalContracts) / totalCompanies : 0;\n        // Get recent activities safely\n        const recentActivities = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.activity.findMany({\n            take: 10,\n            include: {\n                company: {\n                    select: {\n                        name: true\n                    }\n                },\n                createdBy: {\n                    select: {\n                        name: true,\n                        email: true\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: \"desc\"\n            }\n        }).catch(()=>[]);\n        // Get top companies safely\n        const topCompanies = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.company.findMany({\n            take: 10,\n            include: {\n                _count: {\n                    select: {\n                        users: true,\n                        customers: true,\n                        quotations: true,\n                        invoices: true,\n                        activities: true\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: \"desc\"\n            }\n        }).catch(()=>[]);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            summary: {\n                totalCompanies,\n                totalUsers,\n                totalQuotations,\n                totalInvoices,\n                totalContracts,\n                totalItems: 0,\n                activeCompanies,\n                activeUsers,\n                newCompanies,\n                newUsers,\n                totalActivities,\n                totalRevenue,\n                avgRevenuePerCompany,\n                avgDocsPerCompany\n            },\n            companies: {\n                total: totalCompanies,\n                active: activeCompanies,\n                new: newCompanies,\n                bySize: [\n                    {\n                        size: \"SMALL\",\n                        count: Math.floor(totalCompanies * 0.4)\n                    },\n                    {\n                        size: \"MEDIUM\",\n                        count: Math.floor(totalCompanies * 0.3)\n                    },\n                    {\n                        size: \"LARGE\",\n                        count: Math.floor(totalCompanies * 0.2)\n                    },\n                    {\n                        size: \"ENTERPRISE\",\n                        count: Math.floor(totalCompanies * 0.1)\n                    }\n                ],\n                byIndustry: [\n                    {\n                        industry: \"Technology\",\n                        count: Math.floor(totalCompanies * 0.3)\n                    },\n                    {\n                        industry: \"Healthcare\",\n                        count: Math.floor(totalCompanies * 0.2)\n                    },\n                    {\n                        industry: \"Finance\",\n                        count: Math.floor(totalCompanies * 0.15)\n                    },\n                    {\n                        industry: \"Education\",\n                        count: Math.floor(totalCompanies * 0.1)\n                    },\n                    {\n                        industry: \"Other\",\n                        count: Math.floor(totalCompanies * 0.25)\n                    }\n                ],\n                topPerforming: topCompanies.map((company)=>({\n                        id: company.id,\n                        name: company.name,\n                        industry: company.industry || \"Technology\",\n                        size: \"MEDIUM\",\n                        userCount: company._count.users,\n                        quotationCount: company._count.quotations,\n                        invoiceCount: company._count.invoices,\n                        contractCount: 0,\n                        totalRevenue: 0\n                    }))\n            },\n            users: {\n                total: totalUsers,\n                active: activeUsers,\n                new: newUsers,\n                suspended: 0,\n                byRole: [\n                    {\n                        role: \"ADMIN\",\n                        count: Math.floor(totalUsers * 0.1)\n                    },\n                    {\n                        role: \"USER\",\n                        count: Math.floor(totalUsers * 0.8)\n                    },\n                    {\n                        role: \"MANAGER\",\n                        count: Math.floor(totalUsers * 0.1)\n                    }\n                ],\n                avgLoginCount: 5.2,\n                totalLogins: totalUsers * 5\n            },\n            activities: {\n                total: totalActivities,\n                byType: [\n                    {\n                        type: \"CREATE\",\n                        count: Math.floor(totalActivities * 0.4)\n                    },\n                    {\n                        type: \"UPDATE\",\n                        count: Math.floor(totalActivities * 0.3)\n                    },\n                    {\n                        type: \"DELETE\",\n                        count: Math.floor(totalActivities * 0.2)\n                    },\n                    {\n                        type: \"VIEW\",\n                        count: Math.floor(totalActivities * 0.1)\n                    }\n                ],\n                dailyTrend: [],\n                recent: recentActivities.map((activity)=>({\n                        id: activity.id,\n                        type: activity.type,\n                        title: activity.title,\n                        description: activity.description,\n                        company: activity.company?.name,\n                        createdBy: activity.createdBy?.name,\n                        createdAt: activity.createdAt\n                    }))\n            },\n            performance: {\n                newQuotations: Math.floor(totalQuotations * 0.1),\n                newInvoices: Math.floor(totalInvoices * 0.1),\n                newContracts: Math.floor(totalContracts * 0.1),\n                avgDocsPerCompany\n            },\n            security: {\n                suspendedUsers: 0,\n                suspendedCompanies: 0,\n                securityActivities: 0,\n                errorCount: 0\n            },\n            growth: {\n                companies: [],\n                users: []\n            },\n            systemHealth: {\n                totalCompanies,\n                totalUsers,\n                totalActivities,\n                errorRate: 0.1,\n                uptime: 99.9\n            },\n            featureAdoption: {\n                quotations: {\n                    companies: Math.floor(totalCompanies * 0.8),\n                    adoptionRate: 80\n                },\n                invoices: {\n                    companies: Math.floor(totalCompanies * 0.7),\n                    adoptionRate: 70\n                },\n                contracts: {\n                    companies: Math.floor(totalCompanies * 0.5),\n                    adoptionRate: 50\n                },\n                items: {\n                    companies: Math.floor(totalCompanies * 0.6),\n                    adoptionRate: 60\n                }\n            },\n            period\n        });\n    } catch (error) {\n        console.error(\"Error fetching super admin analytics:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to fetch analytics data\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/super-admin/analytics/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/github */ \"(rsc)/./node_modules/next-auth/providers/github.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n\nconst authOptions = {\n    // Using JWT strategy instead of database adapter for now\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                try {\n                    if (!credentials?.email || !credentials?.password) {\n                        console.log(\"Missing credentials\");\n                        return null;\n                    }\n                    console.log(\"Attempting to authenticate user:\", credentials.email);\n                    const user = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.findUnique({\n                        where: {\n                            email: credentials.email\n                        },\n                        select: {\n                            id: true,\n                            email: true,\n                            name: true,\n                            password: true,\n                            role: true,\n                            companyId: true\n                        }\n                    });\n                    // Get the company ID - either as member or owner\n                    let finalCompanyId = user?.companyId;\n                    if (!finalCompanyId && user) {\n                        const ownedCompany = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.company.findFirst({\n                            where: {\n                                ownerId: user.id\n                            },\n                            select: {\n                                id: true\n                            }\n                        });\n                        finalCompanyId = ownedCompany?.id;\n                        // If user is company owner, update their companyId for future queries\n                        if (finalCompanyId) {\n                            await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                                where: {\n                                    id: user.id\n                                },\n                                data: {\n                                    companyId: finalCompanyId\n                                }\n                            });\n                        }\n                    }\n                    if (!user) {\n                        console.log(\"User not found:\", credentials.email);\n                        return null;\n                    }\n                    if (!user.password) {\n                        console.log(\"User has no password set:\", credentials.email);\n                        return null;\n                    }\n                    const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_3___default().compare(credentials.password, user.password);\n                    if (!isPasswordValid) {\n                        console.log(\"Invalid password for user:\", credentials.email);\n                        return null;\n                    }\n                    // Update last login\n                    await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                        where: {\n                            id: user.id\n                        },\n                        data: {\n                            lastLoginAt: new Date(),\n                            loginCount: {\n                                increment: 1\n                            }\n                        }\n                    });\n                    console.log(\"User authenticated successfully:\", user.email);\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name,\n                        role: user.role,\n                        companyId: finalCompanyId\n                    };\n                } catch (error) {\n                    console.error(\"Authentication error:\", error);\n                    return null;\n                }\n            }\n        }),\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            clientId: process.env.GITHUB_CLIENT_ID,\n            clientSecret: process.env.GITHUB_CLIENT_SECRET\n        })\n    ],\n    session: {\n        strategy: \"jwt\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                console.log(\"JWT callback - user data:\", {\n                    id: user.id,\n                    email: user.email,\n                    role: user.role,\n                    companyId: user.companyId\n                });\n                token.role = user.role;\n                token.companyId = user.companyId;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n                session.user.companyId = token.companyId;\n                console.log(\"Session callback - final session:\", {\n                    id: session.user.id,\n                    email: session.user.email,\n                    role: session.user.role,\n                    companyId: session.user.companyId\n                });\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        signUp: \"/auth/signup\",\n        error: \"/auth/error\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zYWFzLWFwcC8uL2xpYi9wcmlzbWEudHM/OTgyMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/lru-cache","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Fanalytics%2Froute&page=%2Fapi%2Fsuper-admin%2Fanalytics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Fanalytics%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();