"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/super-admin/analytics/route";
exports.ids = ["app/api/super-admin/analytics/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Fanalytics%2Froute&page=%2Fapi%2Fsuper-admin%2Fanalytics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Fanalytics%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Fanalytics%2Froute&page=%2Fapi%2Fsuper-admin%2Fanalytics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Fanalytics%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_proj_nextjs_saas_app_api_super_admin_analytics_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/super-admin/analytics/route.ts */ \"(rsc)/./app/api/super-admin/analytics/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/super-admin/analytics/route\",\n        pathname: \"/api/super-admin/analytics\",\n        filename: \"route\",\n        bundlePath: \"app/api/super-admin/analytics/route\"\n    },\n    resolvedPagePath: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\api\\\\super-admin\\\\analytics\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_proj_nextjs_saas_app_api_super_admin_analytics_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/super-admin/analytics/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Fanalytics%2Froute&page=%2Fapi%2Fsuper-admin%2Fanalytics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Fanalytics%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/super-admin/analytics/route.ts":
/*!************************************************!*\
  !*** ./app/api/super-admin/analytics/route.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id || session?.user?.role !== \"SUPER_ADMIN\") {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Super admin access required\"\n            }, {\n                status: 403\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const period = searchParams.get(\"period\") || \"30\" // days\n        ;\n        const startDate = new Date();\n        startDate.setDate(startDate.getDate() - parseInt(period));\n        // Get comprehensive super admin analytics\n        const [systemOverview, companyMetrics, userMetrics, activityMetrics, performanceMetrics, securityMetrics, revenueMetrics, growthMetrics, topCompanies, recentActivities, systemHealth, featureUsage] = await Promise.all([\n            // System overview\n            Promise.all([\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.company.count(),\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.count(),\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.quotation.count(),\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.invoice.count(),\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.contract.count(),\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.item.count()\n            ]),\n            // Company metrics\n            Promise.all([\n                // Active companies\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.company.count({\n                    where: {\n                        status: \"ACTIVE\"\n                    }\n                }),\n                // New companies this period\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.company.count({\n                    where: {\n                        createdAt: {\n                            gte: startDate\n                        }\n                    }\n                }),\n                // Companies by size\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.company.groupBy({\n                    by: [\n                        \"size\"\n                    ],\n                    _count: {\n                        id: true\n                    }\n                }),\n                // Companies by industry\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.company.groupBy({\n                    by: [\n                        \"industry\"\n                    ],\n                    _count: {\n                        id: true\n                    },\n                    orderBy: {\n                        _count: {\n                            id: \"desc\"\n                        }\n                    },\n                    take: 10\n                })\n            ]),\n            // User metrics\n            Promise.all([\n                // Active users\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.count({\n                    where: {\n                        status: \"ACTIVE\"\n                    }\n                }),\n                // New users this period\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.count({\n                    where: {\n                        createdAt: {\n                            gte: startDate\n                        }\n                    }\n                }),\n                // Users by role\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.groupBy({\n                    by: [\n                        \"role\"\n                    ],\n                    _count: {\n                        id: true\n                    }\n                }),\n                // User login activity\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.aggregate({\n                    _avg: {\n                        loginCount: true\n                    },\n                    _sum: {\n                        loginCount: true\n                    }\n                })\n            ]),\n            // Activity metrics\n            Promise.all([\n                // Total activities\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.activity.count({\n                    where: {\n                        createdAt: {\n                            gte: startDate\n                        }\n                    }\n                }),\n                // Activities by type\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.activity.groupBy({\n                    by: [\n                        \"type\"\n                    ],\n                    where: {\n                        createdAt: {\n                            gte: startDate\n                        }\n                    },\n                    _count: {\n                        id: true\n                    },\n                    orderBy: {\n                        _count: {\n                            id: \"desc\"\n                        }\n                    }\n                }),\n                // Daily activity trend\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.$queryRaw`\n          SELECT \n            DATE(created_at) as date,\n            COUNT(*) as count\n          FROM activities\n          WHERE created_at >= ${startDate}\n          GROUP BY DATE(created_at)\n          ORDER BY date DESC\n          LIMIT 30\n        `\n            ]),\n            // Performance metrics\n            Promise.all([\n                // Document creation rates\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.quotation.count({\n                    where: {\n                        createdAt: {\n                            gte: startDate\n                        }\n                    }\n                }),\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.invoice.count({\n                    where: {\n                        createdAt: {\n                            gte: startDate\n                        }\n                    }\n                }),\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.contract.count({\n                    where: {\n                        createdAt: {\n                            gte: startDate\n                        }\n                    }\n                }),\n                // Average documents per company\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.$queryRaw`\n          SELECT \n            AVG(doc_count) as avg_documents_per_company\n          FROM (\n            SELECT \n              company_id,\n              (\n                (SELECT COUNT(*) FROM quotations WHERE company_id = c.id) +\n                (SELECT COUNT(*) FROM invoices WHERE company_id = c.id) +\n                (SELECT COUNT(*) FROM contracts WHERE company_id = c.id)\n              ) as doc_count\n            FROM companies c\n            WHERE c.status = 'ACTIVE'\n          ) as company_docs\n        `\n            ]),\n            // Security metrics\n            Promise.all([\n                // Failed login attempts (if tracked)\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.count({\n                    where: {\n                        status: \"SUSPENDED\"\n                    }\n                }),\n                // Companies with security issues\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.company.count({\n                    where: {\n                        status: \"SUSPENDED\"\n                    }\n                }),\n                // Recent security activities\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.activity.count({\n                    where: {\n                        type: \"SECURITY\",\n                        createdAt: {\n                            gte: startDate\n                        }\n                    }\n                })\n            ]),\n            // Revenue metrics (placeholder - would integrate with billing system)\n            Promise.all([\n                // Total revenue calculation (placeholder)\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.invoice.aggregate({\n                    where: {\n                        status: \"PAID\",\n                        createdAt: {\n                            gte: startDate\n                        }\n                    },\n                    _sum: {\n                        totalAmount: true\n                    }\n                }),\n                // Average revenue per company\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.$queryRaw`\n          SELECT \n            AVG(total_revenue) as avg_revenue_per_company\n          FROM (\n            SELECT \n              company_id,\n              SUM(total_amount) as total_revenue\n            FROM invoices\n            WHERE status = 'PAID' AND created_at >= ${startDate}\n            GROUP BY company_id\n          ) as company_revenue\n        `\n            ]),\n            // Growth metrics\n            Promise.all([\n                // Month-over-month growth\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.$queryRaw`\n          SELECT \n            DATE_TRUNC('month', created_at) as month,\n            COUNT(*) as companies_count\n          FROM companies\n          WHERE created_at >= ${new Date(Date.now() - 365 * 24 * 60 * 60 * 1000)}\n          GROUP BY DATE_TRUNC('month', created_at)\n          ORDER BY month DESC\n          LIMIT 12\n        `,\n                // User growth\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.$queryRaw`\n          SELECT \n            DATE_TRUNC('month', created_at) as month,\n            COUNT(*) as users_count\n          FROM users\n          WHERE created_at >= ${new Date(Date.now() - 365 * 24 * 60 * 60 * 1000)}\n          GROUP BY DATE_TRUNC('month', created_at)\n          ORDER BY month DESC\n          LIMIT 12\n        `\n            ]),\n            // Top performing companies\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.$queryRaw`\n        SELECT \n          c.id,\n          c.name,\n          c.industry,\n          c.size,\n          COUNT(DISTINCT u.id) as user_count,\n          COUNT(DISTINCT q.id) as quotation_count,\n          COUNT(DISTINCT i.id) as invoice_count,\n          COUNT(DISTINCT ct.id) as contract_count,\n          COALESCE(SUM(i.total_amount), 0) as total_revenue\n        FROM companies c\n        LEFT JOIN users u ON c.id = u.company_id\n        LEFT JOIN quotations q ON c.id = q.company_id\n        LEFT JOIN invoices i ON c.id = i.company_id AND i.status = 'PAID'\n        LEFT JOIN contracts ct ON c.id = ct.company_id\n        WHERE c.status = 'ACTIVE'\n        GROUP BY c.id, c.name, c.industry, c.size\n        ORDER BY total_revenue DESC, user_count DESC\n        LIMIT 10\n      `,\n            // Recent activities across all companies\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.activity.findMany({\n                where: {\n                    createdAt: {\n                        gte: startDate\n                    }\n                },\n                include: {\n                    company: {\n                        select: {\n                            name: true\n                        }\n                    },\n                    createdBy: {\n                        select: {\n                            name: true,\n                            email: true\n                        }\n                    }\n                },\n                orderBy: {\n                    createdAt: \"desc\"\n                },\n                take: 20\n            }),\n            // System health metrics\n            Promise.all([\n                // Database size metrics (placeholder)\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.company.count(),\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.count(),\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.activity.count(),\n                // Error rates (would need error tracking)\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.activity.count({\n                    where: {\n                        type: \"ERROR\",\n                        createdAt: {\n                            gte: startDate\n                        }\n                    }\n                })\n            ]),\n            // Feature usage across companies\n            Promise.all([\n                // Companies using quotations\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.company.count({\n                    where: {\n                        quotations: {\n                            some: {}\n                        }\n                    }\n                }),\n                // Companies using invoices\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.company.count({\n                    where: {\n                        invoices: {\n                            some: {}\n                        }\n                    }\n                }),\n                // Companies using contracts\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.company.count({\n                    where: {\n                        contracts: {\n                            some: {}\n                        }\n                    }\n                }),\n                // Companies using items\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.company.count({\n                    where: {\n                        items: {\n                            some: {}\n                        }\n                    }\n                })\n            ])\n        ]);\n        // Process metrics\n        const [totalCompanies, totalUsers, totalQuotations, totalInvoices, totalContracts, totalItems] = systemOverview;\n        const [activeCompanies, newCompanies, companiesBySize, companiesByIndustry] = companyMetrics;\n        const [activeUsers, newUsers, usersByRole, userLoginStats] = userMetrics;\n        const [totalActivities, activitiesByType, dailyActivityTrend] = activityMetrics;\n        const [newQuotations, newInvoices, newContracts, avgDocsPerCompany] = performanceMetrics;\n        const [suspendedUsers, suspendedCompanies, securityActivities] = securityMetrics;\n        const [totalRevenue, avgRevenuePerCompany] = revenueMetrics;\n        const [companyGrowth, userGrowth] = growthMetrics;\n        const [totalCompaniesCount, totalUsersCount, totalActivitiesCount, errorCount] = systemHealth;\n        const [quotationCompanies, invoiceCompanies, contractCompanies, itemCompanies] = featureUsage;\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            summary: {\n                totalCompanies,\n                totalUsers,\n                totalQuotations,\n                totalInvoices,\n                totalContracts,\n                totalItems,\n                activeCompanies,\n                activeUsers,\n                newCompanies,\n                newUsers,\n                totalActivities,\n                totalRevenue: Number(totalRevenue._sum.totalAmount || 0),\n                avgRevenuePerCompany: Number(avgRevenuePerCompany[0]?.avg_revenue_per_company || 0),\n                avgDocsPerCompany: Number(avgDocsPerCompany[0]?.avg_documents_per_company || 0)\n            },\n            companies: {\n                total: totalCompanies,\n                active: activeCompanies,\n                new: newCompanies,\n                bySize: companiesBySize.map((item)=>({\n                        size: item.size,\n                        count: item._count.id\n                    })),\n                byIndustry: companiesByIndustry.map((item)=>({\n                        industry: item.industry,\n                        count: item._count.id\n                    })),\n                topPerforming: topCompanies.map((company)=>({\n                        id: company.id,\n                        name: company.name,\n                        industry: company.industry,\n                        size: company.size,\n                        userCount: Number(company.user_count),\n                        quotationCount: Number(company.quotation_count),\n                        invoiceCount: Number(company.invoice_count),\n                        contractCount: Number(company.contract_count),\n                        totalRevenue: Number(company.total_revenue)\n                    }))\n            },\n            users: {\n                total: totalUsers,\n                active: activeUsers,\n                new: newUsers,\n                suspended: suspendedUsers,\n                byRole: usersByRole.map((item)=>({\n                        role: item.role,\n                        count: item._count.id\n                    })),\n                avgLoginCount: Number(userLoginStats._avg.loginCount || 0),\n                totalLogins: Number(userLoginStats._sum.loginCount || 0)\n            },\n            activities: {\n                total: totalActivities,\n                byType: activitiesByType.map((item)=>({\n                        type: item.type,\n                        count: item._count.id\n                    })),\n                dailyTrend: dailyActivityTrend.map((item)=>({\n                        date: item.date,\n                        count: Number(item.count)\n                    })),\n                recent: recentActivities.map((activity)=>({\n                        id: activity.id,\n                        type: activity.type,\n                        title: activity.title,\n                        description: activity.description,\n                        company: activity.company?.name,\n                        createdBy: activity.createdBy?.name,\n                        createdAt: activity.createdAt\n                    }))\n            },\n            performance: {\n                newQuotations,\n                newInvoices,\n                newContracts,\n                avgDocsPerCompany: Number(avgDocsPerCompany[0]?.avg_documents_per_company || 0)\n            },\n            security: {\n                suspendedUsers,\n                suspendedCompanies,\n                securityActivities,\n                errorCount\n            },\n            growth: {\n                companies: companyGrowth.map((item)=>({\n                        month: item.month,\n                        count: Number(item.companies_count)\n                    })),\n                users: userGrowth.map((item)=>({\n                        month: item.month,\n                        count: Number(item.users_count)\n                    }))\n            },\n            systemHealth: {\n                totalCompanies: totalCompaniesCount,\n                totalUsers: totalUsersCount,\n                totalActivities: totalActivitiesCount,\n                errorRate: totalActivitiesCount > 0 ? errorCount / totalActivitiesCount * 100 : 0,\n                uptime: 99.9 // Placeholder - would integrate with monitoring\n            },\n            featureAdoption: {\n                quotations: {\n                    companies: quotationCompanies,\n                    adoptionRate: totalCompanies > 0 ? quotationCompanies / totalCompanies * 100 : 0\n                },\n                invoices: {\n                    companies: invoiceCompanies,\n                    adoptionRate: totalCompanies > 0 ? invoiceCompanies / totalCompanies * 100 : 0\n                },\n                contracts: {\n                    companies: contractCompanies,\n                    adoptionRate: totalCompanies > 0 ? contractCompanies / totalCompanies * 100 : 0\n                },\n                items: {\n                    companies: itemCompanies,\n                    adoptionRate: totalCompanies > 0 ? itemCompanies / totalCompanies * 100 : 0\n                }\n            },\n            period: parseInt(period)\n        });\n    } catch (error) {\n        console.error(\"Error fetching super admin analytics:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to fetch super admin analytics\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/super-admin/analytics/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/github */ \"(rsc)/./node_modules/next-auth/providers/github.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n\nconst authOptions = {\n    // Using JWT strategy instead of database adapter for now\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                try {\n                    if (!credentials?.email || !credentials?.password) {\n                        console.log(\"Missing credentials\");\n                        return null;\n                    }\n                    console.log(\"Attempting to authenticate user:\", credentials.email);\n                    const user = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.findUnique({\n                        where: {\n                            email: credentials.email\n                        },\n                        select: {\n                            id: true,\n                            email: true,\n                            name: true,\n                            password: true,\n                            role: true,\n                            companyId: true\n                        }\n                    });\n                    // Get the company ID - either as member or owner\n                    let finalCompanyId = user?.companyId;\n                    if (!finalCompanyId && user) {\n                        const ownedCompany = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.company.findFirst({\n                            where: {\n                                ownerId: user.id\n                            },\n                            select: {\n                                id: true\n                            }\n                        });\n                        finalCompanyId = ownedCompany?.id;\n                        // If user is company owner, update their companyId for future queries\n                        if (finalCompanyId) {\n                            await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                                where: {\n                                    id: user.id\n                                },\n                                data: {\n                                    companyId: finalCompanyId\n                                }\n                            });\n                        }\n                    }\n                    if (!user) {\n                        console.log(\"User not found:\", credentials.email);\n                        return null;\n                    }\n                    if (!user.password) {\n                        console.log(\"User has no password set:\", credentials.email);\n                        return null;\n                    }\n                    const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_3___default().compare(credentials.password, user.password);\n                    if (!isPasswordValid) {\n                        console.log(\"Invalid password for user:\", credentials.email);\n                        return null;\n                    }\n                    // Update last login\n                    await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                        where: {\n                            id: user.id\n                        },\n                        data: {\n                            lastLoginAt: new Date(),\n                            loginCount: {\n                                increment: 1\n                            }\n                        }\n                    });\n                    console.log(\"User authenticated successfully:\", user.email);\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name,\n                        role: user.role,\n                        companyId: finalCompanyId\n                    };\n                } catch (error) {\n                    console.error(\"Authentication error:\", error);\n                    return null;\n                }\n            }\n        }),\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            clientId: process.env.GITHUB_CLIENT_ID,\n            clientSecret: process.env.GITHUB_CLIENT_SECRET\n        })\n    ],\n    session: {\n        strategy: \"jwt\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                console.log(\"JWT callback - user data:\", {\n                    id: user.id,\n                    email: user.email,\n                    role: user.role,\n                    companyId: user.companyId\n                });\n                token.role = user.role;\n                token.companyId = user.companyId;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n                session.user.companyId = token.companyId;\n                console.log(\"Session callback - final session:\", {\n                    id: session.user.id,\n                    email: session.user.email,\n                    role: session.user.role,\n                    companyId: session.user.companyId\n                });\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        signUp: \"/auth/signup\",\n        error: \"/auth/error\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zYWFzLWFwcC8uL2xpYi9wcmlzbWEudHM/OTgyMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/lru-cache","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Fanalytics%2Froute&page=%2Fapi%2Fsuper-admin%2Fanalytics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Fanalytics%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();