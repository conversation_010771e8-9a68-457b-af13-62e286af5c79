import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const includeInactive = searchParams.get('includeInactive') === 'true'
    const publicOnly = searchParams.get('publicOnly') === 'true'

    // Build where clause
    const where: any = {}
    
    if (!includeInactive) {
      where.isActive = true
    }
    
    if (publicOnly) {
      where.isPublic = true
    }

    const plans = await prisma.pricingPlan.findMany({
      where,
      orderBy: { sortOrder: 'asc' },
      select: {
        id: true,
        name: true,
        description: true,
        monthlyPrice: true,
        yearlyPrice: true,
        currency: true,
        maxUsers: true,
        maxCompanies: true,
        maxCustomers: true,
        maxQuotations: true,
        maxInvoices: true,
        maxContracts: true,
        maxStorage: true,
        features: true,
        isActive: true,
        isPublic: true,
        trialDays: true,
        stripeProductId: true,
        stripePriceId: true,
        stripeYearlyPriceId: true,
        createdAt: true,
        updatedAt: true
      }
    })

    // Format the response with calculated values
    const formattedPlans = plans.map(plan => ({
      ...plan,
      monthlyPrice: Number(plan.monthlyPrice),
      yearlyPrice: plan.yearlyPrice ? Number(plan.yearlyPrice) : null,
      maxStorage: Number(plan.maxStorage),
      yearlyDiscount: plan.yearlyPrice && plan.monthlyPrice 
        ? Math.round((1 - (Number(plan.yearlyPrice) / 12) / Number(plan.monthlyPrice)) * 100)
        : 0,
      formattedStorage: formatBytes(Number(plan.maxStorage)),
      isPopular: plan.name.toLowerCase() === 'pro', // Mark Pro as popular
      features: plan.features || {}
    }))

    return NextResponse.json({
      success: true,
      data: formattedPlans
    })

  } catch (error) {
    console.error('Error fetching pricing plans:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch pricing plans' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      name,
      description,
      monthlyPrice,
      yearlyPrice,
      currency = 'USD',
      maxUsers,
      maxCompanies,
      maxCustomers,
      maxQuotations,
      maxInvoices,
      maxContracts,
      maxStorage,
      features,
      isActive = true,
      isPublic = true,
      trialDays = 14,
      stripeProductId,
      stripePriceId,
      stripeYearlyPriceId
    } = body

    // Validation
    if (!name || !monthlyPrice || !maxUsers) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Get the next sort order
    const lastPlan = await prisma.pricingPlan.findFirst({
      orderBy: { sortOrder: 'desc' },
      select: { sortOrder: true }
    })
    const sortOrder = (lastPlan?.sortOrder || 0) + 1

    const plan = await prisma.pricingPlan.create({
      data: {
        name,
        description,
        monthlyPrice,
        yearlyPrice,
        currency,
        maxUsers,
        maxCompanies,
        maxCustomers,
        maxQuotations,
        maxInvoices,
        maxContracts,
        maxStorage: BigInt(maxStorage),
        features,
        isActive,
        isPublic,
        trialDays,
        sortOrder,
        stripeProductId,
        stripePriceId,
        stripeYearlyPriceId
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        ...plan,
        monthlyPrice: Number(plan.monthlyPrice),
        yearlyPrice: plan.yearlyPrice ? Number(plan.yearlyPrice) : null,
        maxStorage: Number(plan.maxStorage)
      }
    })

  } catch (error) {
    console.error('Error creating pricing plan:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create pricing plan' },
      { status: 500 }
    )
  }
}

// Helper function to format bytes
function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
