(()=>{var e={};e.id=1094,e.ids=[1094],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},86347:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=s(50482),n=s(69108),a=s(62563),o=s.n(a),i=s(68300),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(t,l);let d=["",{children:["test-styles",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,38193)),"C:\\proj\\nextjs-saas\\app\\test-styles\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,59504)),"C:\\proj\\nextjs-saas\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\proj\\nextjs-saas\\app\\test-styles\\page.tsx"],m="/test-styles/page",u={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/test-styles/page",pathname:"/test-styles",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},64588:(e,t,s)=>{Promise.resolve().then(s.bind(s,56189)),Promise.resolve().then(s.bind(s,44669))},19634:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2583,23)),Promise.resolve().then(s.t.bind(s,26840,23)),Promise.resolve().then(s.t.bind(s,38771,23)),Promise.resolve().then(s.t.bind(s,13225,23)),Promise.resolve().then(s.t.bind(s,9295,23)),Promise.resolve().then(s.t.bind(s,43982,23))},35303:()=>{},56189:(e,t,s)=>{"use strict";s.r(t),s.d(t,{Providers:()=>c});var r=s(95344),n=s(47674),a=s(6256),o=s(19115),i=s(26274),l=s(3729),d=s(66091);function c({children:e}){let[t]=(0,l.useState)(()=>new o.S({defaultOptions:{queries:{staleTime:6e4,retry:1}}}));return r.jsx(n.SessionProvider,{children:r.jsx(i.aH,{client:t,children:r.jsx(d.lY,{children:r.jsx(a.f,{attribute:"class",defaultTheme:"light",enableSystem:!0,disableTransitionOnChange:!0,children:e})})})})}},66091:(e,t,s)=>{"use strict";s.d(t,{TC:()=>l,lY:()=>i});var r=s(95344),n=s(3729);let a={appName:"SaaS Platform",logoUrl:"",faviconUrl:"",primaryColor:"#3b82f6",secondaryColor:"#64748b",accentColor:"#10b981",backgroundColor:"#ffffff",textColor:"#1f2937",theme:"light",fontFamily:"Inter, sans-serif",customCss:""},o=(0,n.createContext)(void 0);function i({children:e}){let[t,s]=(0,n.useState)(a),[i,l]=(0,n.useState)(!0);(0,n.useEffect)(()=>{d()},[]);let d=async()=>{try{let e=await fetch("/api/global-config/branding"),t=await e.json();t.success&&t.branding?(s({...a,...t.branding}),c({...a,...t.branding})):c(a)}catch(e){console.error("Error fetching branding config:",e),c(a)}finally{l(!1)}},c=e=>{let t=document.documentElement;if(t.style.setProperty("--primary-color",e.primaryColor),t.style.setProperty("--secondary-color",e.secondaryColor),t.style.setProperty("--accent-color",e.accentColor),t.style.setProperty("--background-color",e.backgroundColor),t.style.setProperty("--text-color",e.textColor),t.style.setProperty("--font-family",e.fontFamily),document.body.className=document.body.className.replace(/theme-\w+/g,""),document.body.classList.add(`theme-${e.theme}`),document.title=e.appName,e.faviconUrl){let t=document.querySelector('link[rel="icon"]');t||((t=document.createElement("link")).rel="icon",document.head.appendChild(t)),t.href=e.faviconUrl}let s=document.getElementById("custom-branding-css");e.customCss?(s||((s=document.createElement("style")).id="custom-branding-css",document.head.appendChild(s)),s.textContent=e.customCss):s&&s.remove();let r=document.querySelector('meta[name="theme-color"]');r||((r=document.createElement("meta")).name="theme-color",document.head.appendChild(r)),r.content=e.primaryColor};return r.jsx(o.Provider,{value:{branding:t,updateBranding:e=>{let r={...t,...e};s(r),c(r)},loading:i},children:e})}function l(){let e=(0,n.useContext)(o);if(void 0===e)throw Error("useBranding must be used within a BrandingProvider");return e}},59504:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>x,metadata:()=>u});var r=s(25036),n=s(80265),a=s.n(n),o=s(86843);let i=(0,o.createProxy)(String.raw`C:\proj\nextjs-saas\components\providers.tsx`),{__esModule:l,$$typeof:d}=i;i.default;let c=(0,o.createProxy)(String.raw`C:\proj\nextjs-saas\components\providers.tsx#Providers`);var m=s(69636);s(67272);let u={title:{default:"Business SaaS - Complete Business Management Solution",template:"%s | Business SaaS"},description:"Complete SaaS solution for business management including CRM, invoicing, quotations, and more.",keywords:["SaaS","Business Management","CRM","Invoicing","Quotations"],authors:[{name:"Business SaaS Team"}],creator:"Business SaaS",openGraph:{type:"website",locale:"en_US",url:process.env.NEXT_PUBLIC_APP_URL,title:"Business SaaS - Complete Business Management Solution",description:"Complete SaaS solution for business management including CRM, invoicing, quotations, and more.",siteName:"Business SaaS"},twitter:{card:"summary_large_image",title:"Business SaaS - Complete Business Management Solution",description:"Complete SaaS solution for business management including CRM, invoicing, quotations, and more.",creator:"@businesssaas"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function x({children:e}){return r.jsx("html",{lang:"en",suppressHydrationWarning:!0,children:r.jsx("body",{className:a().className,children:(0,r.jsxs)(c,{children:[e,r.jsx(m.x7,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#4ade80",secondary:"#fff"}},error:{duration:4e3,iconTheme:{primary:"#ef4444",secondary:"#fff"}}}})]})})})}},38193:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(25036);function n(){return r.jsx("div",{className:"min-h-screen bg-blue-500 p-8",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[r.jsx("h1",{className:"text-4xl font-bold text-white mb-8",children:"Style Test Page"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[r.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Card 1"}),r.jsx("p",{className:"text-gray-600",children:"This should be a white card with shadow"}),r.jsx("button",{className:"mt-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700",children:"Button"})]}),(0,r.jsxs)("div",{className:"bg-green-100 rounded-lg border-2 border-green-500 p-6",children:[r.jsx("h2",{className:"text-xl font-semibold text-green-800 mb-4",children:"Card 2"}),r.jsx("p",{className:"text-green-700",children:"This should be a green card"}),(0,r.jsxs)("div",{className:"flex space-x-2 mt-4",children:[r.jsx("div",{className:"w-4 h-4 bg-red-500 rounded-full"}),r.jsx("div",{className:"w-4 h-4 bg-yellow-500 rounded-full"}),r.jsx("div",{className:"w-4 h-4 bg-green-500 rounded-full"})]})]}),(0,r.jsxs)("div",{className:"bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg p-6 text-white",children:[r.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Card 3"}),r.jsx("p",{children:"This should be a gradient card"}),(0,r.jsxs)("div",{className:"mt-4 flex items-center space-x-2",children:[r.jsx("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-white"}),r.jsx("span",{children:"Loading..."})]})]})]}),(0,r.jsxs)("div",{className:"mt-8 bg-white rounded-lg p-6",children:[r.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Typography Test"}),r.jsx("p",{className:"text-lg text-gray-700 mb-2",children:"Large text"}),r.jsx("p",{className:"text-base text-gray-600 mb-2",children:"Base text"}),r.jsx("p",{className:"text-sm text-gray-500 mb-2",children:"Small text"}),r.jsx("p",{className:"text-xs text-gray-400",children:"Extra small text"})]})]})})}},67272:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[1638,7948],()=>s(86347));module.exports=r})();