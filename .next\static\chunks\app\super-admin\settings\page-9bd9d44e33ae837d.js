(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3229],{92853:function(e,t,s){Promise.resolve().then(s.bind(s,85473))},85473:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return T}});var r=s(57437),a=s(2265),n=s(82749),i=s(24033),l=s(27815),d=s(85754),o=s(45179),c=s(23444),u=s(31478),f=s(86443),x=s(49842),m=s(19160),h=s(45509),p=s(42706),g=s(40110),b=s(49036),j=s(97332),v=s(1295),y=s(3021),N=s(20597),w=s(92455),k=s(29409),C=s(64280),R=s(92295),S=s(9883),Z=s(28956),E=s(41827),_=s(99670),P=s(77216),z=s(87293),V=s(5589),O=s(45367);function T(){var e;let{data:t,status:s}=(0,n.useSession)(),[T,A]=(0,a.useState)({}),[F,D]=(0,a.useState)([]),[Q,Y]=(0,a.useState)(!0),[U,B]=(0,a.useState)(!1),[I,J]=(0,a.useState)(""),[H,K]=(0,a.useState)("all"),[L,$]=(0,a.useState)({}),[G,M]=(0,a.useState)({key:"",value:"",category:"",description:"",isPublic:!1,isEditable:!0}),[q,W]=(0,a.useState)(!1);if("loading"===s)return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})});"unauthenticated"===s&&(0,i.redirect)("/auth/signin"),(null==t?void 0:null===(e=t.user)||void 0===e?void 0:e.role)!=="SUPER_ADMIN"&&(0,i.redirect)("/dashboard");let X=async()=>{try{Y(!0);let e=new URLSearchParams({...H&&"all"!==H&&{category:H},...I&&{search:I}}),t=await fetch("/api/super-admin/settings?".concat(e));if(!t.ok)throw Error("Failed to fetch settings");let s=await t.json();A(s.settings),D(s.categories)}catch(e){console.error("Error fetching settings:",e)}finally{Y(!1)}};(0,a.useEffect)(()=>{X()},[H,I]);let ee=(e,t,s)=>{$(r=>({...r,[e]:{...r[e],[t]:s}}))},et=async()=>{try{B(!0);let e=Object.entries(L).map(e=>{let[t,s]=e;return{id:t,...s}});if(!(await fetch("/api/super-admin/settings",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({settings:e})})).ok)throw Error("Failed to save settings");$({}),await X()}catch(e){console.error("Error saving settings:",e)}finally{B(!1)}},es=async()=>{try{if(!(await fetch("/api/super-admin/settings",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(G)})).ok)throw Error("Failed to create setting");M({key:"",value:"",category:"",description:"",isPublic:!1,isEditable:!0}),W(!1),await X()}catch(e){console.error("Error creating setting:",e)}},er=async e=>{if(confirm("Are you sure you want to delete this setting?"))try{if(!(await fetch("/api/super-admin/settings?id=".concat(e),{method:"DELETE"})).ok)throw Error("Failed to delete setting");await X()}catch(e){console.error("Error deleting setting:",e)}},ea=e=>({security:b.Z,database:j.Z,email:v.Z,notifications:y.Z,ui:N.Z,api:w.Z,general:k.Z})[e.toLowerCase()]||k.Z,en=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return"boolean"==typeof e?e?"true":"false":"object"==typeof e?t?JSON.stringify(e,null,2):JSON.stringify(e):String(e)},ei=(e,t)=>{if("boolean"==typeof t)return"true"===e;if("number"==typeof t)return Number(e);if("object"==typeof t)try{return JSON.parse(e)}catch(e){}return e},el=Object.keys(L).length>0;return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(k.Z,{className:"h-8 w-8 text-blue-600"}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"System Settings"})]}),(0,r.jsx)("p",{className:"text-gray-500 mt-1",children:"Configure global application settings"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)(d.z,{variant:"outline",onClick:X,disabled:Q,children:[(0,r.jsx)(C.Z,{className:"h-4 w-4 mr-2 ".concat(Q?"animate-spin":"")}),"Refresh"]}),el&&(0,r.jsxs)(d.z,{onClick:et,disabled:U,children:[(0,r.jsx)(R.Z,{className:"h-4 w-4 mr-2 ".concat(U?"animate-spin":"")}),"Save Changes"]}),(0,r.jsxs)(p.Vq,{open:q,onOpenChange:W,children:[(0,r.jsx)(p.hg,{asChild:!0,children:(0,r.jsxs)(d.z,{children:[(0,r.jsx)(S.Z,{className:"h-4 w-4 mr-2"}),"Add Setting"]})}),(0,r.jsxs)(p.cZ,{children:[(0,r.jsxs)(p.fK,{children:[(0,r.jsx)(p.$N,{children:"Create New Setting"}),(0,r.jsx)(p.Be,{children:"Add a new system setting to configure application behavior."})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(x._,{htmlFor:"key",children:"Key"}),(0,r.jsx)(o.I,{id:"key",value:G.key,onChange:e=>M(t=>({...t,key:e.target.value})),placeholder:"e.g., app.max_users_per_company"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(x._,{htmlFor:"category",children:"Category"}),(0,r.jsxs)(h.Ph,{value:G.category,onValueChange:e=>M(t=>({...t,category:e})),children:[(0,r.jsx)(h.i4,{children:(0,r.jsx)(h.ki,{placeholder:"Select category"})}),(0,r.jsxs)(h.Bw,{children:[(0,r.jsx)(h.Ql,{value:"general",children:"General"}),(0,r.jsx)(h.Ql,{value:"security",children:"Security"}),(0,r.jsx)(h.Ql,{value:"database",children:"Database"}),(0,r.jsx)(h.Ql,{value:"email",children:"Email"}),(0,r.jsx)(h.Ql,{value:"notifications",children:"Notifications"}),(0,r.jsx)(h.Ql,{value:"ui",children:"UI/UX"}),(0,r.jsx)(h.Ql,{value:"api",children:"API"})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(x._,{htmlFor:"value",children:"Value"}),(0,r.jsx)(c.g,{id:"value",value:G.value,onChange:e=>M(t=>({...t,value:e.target.value})),placeholder:"Setting value (JSON for objects)"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(x._,{htmlFor:"description",children:"Description"}),(0,r.jsx)(c.g,{id:"description",value:G.description,onChange:e=>M(t=>({...t,description:e.target.value})),placeholder:"Describe what this setting does"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(f.r,{id:"isPublic",checked:G.isPublic,onCheckedChange:e=>M(t=>({...t,isPublic:e}))}),(0,r.jsx)(x._,{htmlFor:"isPublic",children:"Public"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(f.r,{id:"isEditable",checked:G.isEditable,onCheckedChange:e=>M(t=>({...t,isEditable:e}))}),(0,r.jsx)(x._,{htmlFor:"isEditable",children:"Editable"})]})]})]}),(0,r.jsxs)(p.cN,{children:[(0,r.jsx)(d.z,{variant:"outline",onClick:()=>W(!1),children:"Cancel"}),(0,r.jsx)(d.z,{onClick:es,children:"Create Setting"})]})]})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsx)(l.Zb,{children:(0,r.jsx)(l.aY,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Total Settings"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:Object.values(T).flat().length})]}),(0,r.jsx)(k.Z,{className:"h-8 w-8 text-blue-600"})]})})}),(0,r.jsx)(l.Zb,{children:(0,r.jsx)(l.aY,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Categories"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:F.length})]}),(0,r.jsx)(j.Z,{className:"h-8 w-8 text-green-600"})]})})}),(0,r.jsx)(l.Zb,{children:(0,r.jsx)(l.aY,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Public Settings"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:Object.values(T).flat().filter(e=>e.isPublic).length})]}),(0,r.jsx)(Z.Z,{className:"h-8 w-8 text-purple-600"})]})})})]}),(0,r.jsx)(l.Zb,{children:(0,r.jsx)(l.aY,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(E.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,r.jsx)(o.I,{placeholder:"Search settings...",value:I,onChange:e=>J(e.target.value),className:"pl-10"})]})}),(0,r.jsxs)(h.Ph,{value:H,onValueChange:K,children:[(0,r.jsx)(h.i4,{className:"w-full sm:w-48",children:(0,r.jsx)(h.ki,{placeholder:"All Categories"})}),(0,r.jsxs)(h.Bw,{children:[(0,r.jsx)(h.Ql,{value:"all",children:"All Categories"}),F.map(e=>(0,r.jsxs)(h.Ql,{value:e.name,children:[e.name," (",e.count,")"]},e.name))]})]})]})})}),(0,r.jsxs)(g.mQ,{value:H,onValueChange:K,children:[(0,r.jsxs)(g.dr,{className:"grid w-full grid-cols-4 lg:grid-cols-8",children:[(0,r.jsx)(g.SP,{value:"all",children:"All"}),F.slice(0,7).map(e=>{let t=ea(e.name);return(0,r.jsxs)(g.SP,{value:e.name,className:"flex items-center space-x-1",children:[(0,r.jsx)(t,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"hidden sm:inline",children:e.name})]},e.name)})]}),(0,r.jsx)(g.nU,{value:H,className:"space-y-6",children:Q?(0,r.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):Object.entries(T).map(e=>{let[t,s]=e;return(0,r.jsxs)(l.Zb,{children:[(0,r.jsx)(l.Ol,{children:(0,r.jsxs)(l.ll,{className:"flex items-center space-x-2",children:[(()=>{let e=ea(t);return(0,r.jsx)(e,{className:"h-5 w-5"})})(),(0,r.jsx)("span",{children:t}),(0,r.jsx)(u.C,{variant:"secondary",children:s.length})]})}),(0,r.jsx)(l.aY,{children:(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)(m.iA,{children:[(0,r.jsx)(m.xD,{children:(0,r.jsxs)(m.SC,{children:[(0,r.jsx)(m.ss,{children:"Key"}),(0,r.jsx)(m.ss,{children:"Value"}),(0,r.jsx)(m.ss,{children:"Description"}),(0,r.jsx)(m.ss,{children:"Flags"}),(0,r.jsx)(m.ss,{children:"Actions"})]})}),(0,r.jsx)(m.RM,{children:s.map(e=>{let t=L[e.id],s=(null==t?void 0:t.value)!==void 0?t.value:e.value;return(0,r.jsxs)(m.SC,{children:[(0,r.jsx)(m.pj,{children:(0,r.jsx)("code",{className:"text-sm bg-gray-100 px-2 py-1 rounded",children:e.key})}),(0,r.jsx)(m.pj,{children:e.isEditable?(0,r.jsx)(c.g,{value:en(s,!0),onChange:t=>ee(e.id,"value",ei(t.target.value,e.value)),className:"min-h-[60px] font-mono text-sm"}):(0,r.jsx)("code",{className:"text-sm bg-gray-50 px-2 py-1 rounded block max-w-xs overflow-hidden",children:en(e.value)})}),(0,r.jsx)(m.pj,{children:e.isEditable?(0,r.jsx)(o.I,{value:(null==t?void 0:t.description)!==void 0?t.description:e.description||"",onChange:t=>ee(e.id,"description",t.target.value),placeholder:"Add description..."}):(0,r.jsx)("span",{className:"text-sm text-gray-600",children:e.description||"No description"})}),(0,r.jsx)(m.pj,{children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[e.isPublic?(0,r.jsx)(_.Z,{className:"h-4 w-4 text-green-600"}):(0,r.jsx)(P.Z,{className:"h-4 w-4 text-gray-400"}),(0,r.jsx)("span",{className:"text-xs",children:e.isPublic?"Public":"Private"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[e.isEditable?(0,r.jsx)(z.Z,{className:"h-4 w-4 text-blue-600"}):(0,r.jsx)(V.Z,{className:"h-4 w-4 text-gray-400"}),(0,r.jsx)("span",{className:"text-xs",children:e.isEditable?"Editable":"Locked"})]})]})}),(0,r.jsx)(m.pj,{children:e.isEditable&&(0,r.jsx)(d.z,{variant:"ghost",size:"sm",onClick:()=>er(e.id),children:(0,r.jsx)(O.Z,{className:"h-4 w-4"})})})]},e.id)})})]})})})]},t)})})]})]})}},31478:function(e,t,s){"use strict";s.d(t,{C:function(){return l}});var r=s(57437);s(2265);var a=s(96061),n=s(1657);let i=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600",info:"border-transparent bg-blue-500 text-white hover:bg-blue-600"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:s,...a}=e;return(0,r.jsx)("div",{className:(0,n.cn)(i({variant:s}),t),...a})}},85754:function(e,t,s){"use strict";s.d(t,{z:function(){return o}});var r=s(57437),a=s(2265),n=s(67256),i=s(96061),l=s(1657);let d=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=a.forwardRef((e,t)=>{let{className:s,variant:a,size:i,asChild:o=!1,...c}=e,u=o?n.g7:"button";return(0,r.jsx)(u,{className:(0,l.cn)(d({variant:a,size:i,className:s})),ref:t,...c})});o.displayName="Button"},27815:function(e,t,s){"use strict";s.d(t,{Ol:function(){return l},SZ:function(){return o},Zb:function(){return i},aY:function(){return c},eW:function(){return u},ll:function(){return d}});var r=s(57437),a=s(2265),n=s(1657);let i=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...a})});i.displayName="Card";let l=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",s),...a})});l.displayName="CardHeader";let d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",s),...a})});d.displayName="CardTitle";let o=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",s),...a})});o.displayName="CardDescription";let c=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",s),...a})});c.displayName="CardContent";let u=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",s),...a})});u.displayName="CardFooter"},42706:function(e,t,s){"use strict";s.d(t,{$N:function(){return h},Be:function(){return p},Vq:function(){return d},cN:function(){return m},cZ:function(){return f},fK:function(){return x},hg:function(){return o},t9:function(){return u}});var r=s(57437),a=s(2265),n=s(28712),i=s(82549),l=s(1657);let d=n.fC,o=n.xz,c=n.h_;n.x8;let u=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(n.aV,{ref:t,className:(0,l.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...a})});u.displayName=n.aV.displayName;let f=a.forwardRef((e,t)=>{let{className:s,children:a,...d}=e;return(0,r.jsxs)(c,{children:[(0,r.jsx)(u,{}),(0,r.jsxs)(n.VY,{ref:t,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",s),...d,children:[a,(0,r.jsxs)(n.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,r.jsx)(i.Z,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});f.displayName=n.VY.displayName;let x=e=>{let{className:t,...s}=e;return(0,r.jsx)("div",{className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...s})};x.displayName="DialogHeader";let m=e=>{let{className:t,...s}=e;return(0,r.jsx)("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...s})};m.displayName="DialogFooter";let h=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(n.Dx,{ref:t,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",s),...a})});h.displayName=n.Dx.displayName;let p=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(n.dk,{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",s),...a})});p.displayName=n.dk.displayName},45179:function(e,t,s){"use strict";s.d(t,{I:function(){return i}});var r=s(57437),a=s(2265),n=s(1657);let i=a.forwardRef((e,t)=>{let{className:s,type:a,...i}=e;return(0,r.jsx)("input",{type:a,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:t,...i})});i.displayName="Input"},49842:function(e,t,s){"use strict";s.d(t,{_:function(){return o}});var r=s(57437),a=s(2265),n=s(36743),i=s(96061),l=s(1657);let d=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(n.f,{ref:t,className:(0,l.cn)(d(),s),...a})});o.displayName=n.f.displayName},45509:function(e,t,s){"use strict";s.d(t,{Bw:function(){return h},Ph:function(){return c},Ql:function(){return p},i4:function(){return f},ki:function(){return u}});var r=s(57437),a=s(2265),n=s(99530),i=s(83523),l=s(9224),d=s(62442),o=s(1657);let c=n.fC;n.ZA;let u=n.B4,f=a.forwardRef((e,t)=>{let{className:s,children:a,...l}=e;return(0,r.jsxs)(n.xz,{ref:t,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",s),...l,children:[a,(0,r.jsx)(n.JO,{asChild:!0,children:(0,r.jsx)(i.Z,{className:"h-4 w-4 opacity-50"})})]})});f.displayName=n.xz.displayName;let x=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(n.u_,{ref:t,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",s),...a,children:(0,r.jsx)(l.Z,{className:"h-4 w-4"})})});x.displayName=n.u_.displayName;let m=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(n.$G,{ref:t,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",s),...a,children:(0,r.jsx)(i.Z,{className:"h-4 w-4"})})});m.displayName=n.$G.displayName;let h=a.forwardRef((e,t)=>{let{className:s,children:a,position:i="popper",...l}=e;return(0,r.jsx)(n.h_,{children:(0,r.jsxs)(n.VY,{ref:t,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:i,...l,children:[(0,r.jsx)(x,{}),(0,r.jsx)(n.l_,{className:(0,o.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),(0,r.jsx)(m,{})]})})});h.displayName=n.VY.displayName,a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(n.__,{ref:t,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",s),...a})}).displayName=n.__.displayName;let p=a.forwardRef((e,t)=>{let{className:s,children:a,...i}=e;return(0,r.jsxs)(n.ck,{ref:t,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...i,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(n.wU,{children:(0,r.jsx)(d.Z,{className:"h-4 w-4"})})}),(0,r.jsx)(n.eT,{children:a})]})});p.displayName=n.ck.displayName,a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(n.Z0,{ref:t,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",s),...a})}).displayName=n.Z0.displayName},86443:function(e,t,s){"use strict";s.d(t,{r:function(){return l}});var r=s(57437),a=s(2265),n=s(92376),i=s(1657);let l=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(n.fC,{className:(0,i.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",s),...a,ref:t,children:(0,r.jsx)(n.bU,{className:(0,i.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});l.displayName=n.fC.displayName},19160:function(e,t,s){"use strict";s.d(t,{RM:function(){return d},SC:function(){return o},iA:function(){return i},pj:function(){return u},ss:function(){return c},xD:function(){return l}});var r=s(57437),a=s(2265),n=s(1657);let i=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{className:"relative w-full overflow-auto",children:(0,r.jsx)("table",{ref:t,className:(0,n.cn)("w-full caption-bottom text-sm",s),...a})})});i.displayName="Table";let l=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("thead",{ref:t,className:(0,n.cn)("[&_tr]:border-b",s),...a})});l.displayName="TableHeader";let d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("tbody",{ref:t,className:(0,n.cn)("[&_tr:last-child]:border-0",s),...a})});d.displayName="TableBody",a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("tfoot",{ref:t,className:(0,n.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",s),...a})}).displayName="TableFooter";let o=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("tr",{ref:t,className:(0,n.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",s),...a})});o.displayName="TableRow";let c=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("th",{ref:t,className:(0,n.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",s),...a})});c.displayName="TableHead";let u=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("td",{ref:t,className:(0,n.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",s),...a})});u.displayName="TableCell",a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("caption",{ref:t,className:(0,n.cn)("mt-4 text-sm text-muted-foreground",s),...a})}).displayName="TableCaption"},40110:function(e,t,s){"use strict";s.d(t,{SP:function(){return o},dr:function(){return d},mQ:function(){return l},nU:function(){return c}});var r=s(57437),a=s(2265),n=s(34522),i=s(1657);let l=n.fC,d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(n.aV,{ref:t,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",s),...a})});d.displayName=n.aV.displayName;let o=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(n.xz,{ref:t,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",s),...a})});o.displayName=n.xz.displayName;let c=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(n.VY,{ref:t,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",s),...a})});c.displayName=n.VY.displayName},23444:function(e,t,s){"use strict";s.d(t,{g:function(){return i}});var r=s(57437),a=s(2265),n=s(1657);let i=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:t,...a})});i.displayName="Textarea"},1657:function(e,t,s){"use strict";s.d(t,{cn:function(){return n}});var r=s(57042),a=s(74769);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.m6)((0,r.W)(t))}}},function(e){e.O(0,[6723,9502,2749,1706,4138,4997,4522,4987,2971,4938,1744],function(){return e(e.s=92853)}),_N_E=e.O()}]);