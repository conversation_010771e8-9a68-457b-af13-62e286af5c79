{"name": "@floating-ui/utils", "version": "0.2.10", "description": "Utilities for Floating UI", "publishConfig": {"access": "public"}, "main": "./dist/floating-ui.utils.umd.js", "module": "./dist/floating-ui.utils.esm.js", "types": "./dist/floating-ui.utils.d.ts", "sideEffects": false, "files": ["dist", "dom"], "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/floating-ui.utils.d.mts", "default": "./dist/floating-ui.utils.mjs"}, "types": "./dist/floating-ui.utils.d.ts", "module": "./dist/floating-ui.utils.esm.js", "default": "./dist/floating-ui.utils.umd.js"}, "./dom": {"import": {"types": "./dist/floating-ui.utils.dom.d.mts", "default": "./dist/floating-ui.utils.dom.mjs"}, "types": "./dist/floating-ui.utils.dom.d.ts", "module": "./dist/floating-ui.utils.dom.esm.js", "default": "./dist/floating-ui.utils.dom.umd.js"}}, "author": "atomiks", "license": "MIT", "bugs": "https://github.com/floating-ui/floating-ui", "repository": {"type": "git", "url": "https://github.com/floating-ui/floating-ui.git", "directory": "packages/utils"}, "homepage": "https://floating-ui.com", "keywords": ["tooltip", "popover", "dropdown", "menu", "popup", "positioning"], "devDependencies": {"@testing-library/jest-dom": "^6.1.6", "config": "0.0.0"}, "scripts": {"lint": "eslint .", "format": "prettier --write .", "clean": "rim<PERSON>f dist out-tsc dom react", "test": "vitest run --globals", "test:watch": "vitest watch --globals", "dev": "rollup -c -w", "build": "rollup -c", "build:api": "build-api --tsc tsconfig.lib.json --aec api-extractor.json --aec api-extractor.dom.json --aec api-extractor.react.json", "publint": "publint", "typecheck": "tsc -b"}}