"use strict";(()=>{var e={};e.id=1085,e.ids=[1085],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},27150:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>E,originalPathname:()=>_,patchFetch:()=>U,requestAsyncStorage:()=>I,routeModule:()=>g,serverHooks:()=>h,staticGenerationAsyncStorage:()=>f,staticGenerationBailout:()=>A});var s={};t.r(s),t.d(s,{DELETE:()=>w,GET:()=>m,PATCH:()=>y});var a=t(95419),i=t(69108),n=t(99678),o=t(78070),d=t(81355),u=t(3205),l=t(9108),c=t(6521),p=t.n(c);async function m(e,{params:r}){try{let e=await (0,d.getServerSession)(u.L);if(!e?.user?.id||e?.user?.role!=="SUPER_ADMIN")return o.Z.json({error:"Super admin access required"},{status:403});let t=r.id,s=await l._.user.findUnique({where:{id:t},include:{company:{select:{id:!0,name:!0,status:!0,industry:!0,size:!0,createdAt:!0}},ownedCompany:{select:{id:!0,name:!0,status:!0,industry:!0,size:!0,createdAt:!0}},userSettings:!0,_count:{select:{createdLeads:!0,assignedLeads:!0,createdCustomers:!0,assignedCustomers:!0,createdQuotations:!0,assignedQuotations:!0,createdInvoices:!0,assignedInvoices:!0,createdContracts:!0,assignedContracts:!0,createdTasks:!0,assignedTasks:!0,activities:!0,notes:!0,documents:!0}}}});if(!s)return o.Z.json({error:"User not found"},{status:404});let a=await l._.activity.findMany({where:{createdById:t},take:10,orderBy:{createdAt:"desc"},include:{company:{select:{name:!0}}}}),i=await l._.auditLog.findMany({where:{OR:[{userId:t},{entityType:"User",entityId:t}]},take:20,orderBy:{createdAt:"desc"}}),n=await l._.auditLog.findMany({where:{userId:t,action:{in:["LOGIN","LOGOUT","LOGIN_FAILED"]}},take:10,orderBy:{createdAt:"desc"}}),{password:c,...p}=s;return o.Z.json({user:p,recentActivities:a.map(e=>({id:e.id,type:e.type,title:e.title,description:e.description,company:e.company?.name,createdAt:e.createdAt})),auditLogs:i.map(e=>({id:e.id,action:e.action,entityType:e.entityType,userEmail:e.userEmail,ipAddress:e.ipAddress,metadata:e.metadata,createdAt:e.createdAt})),loginHistory:n.map(e=>({id:e.id,action:e.action,ipAddress:e.ipAddress,userAgent:e.userAgent,metadata:e.metadata,createdAt:e.createdAt}))})}catch(e){return console.error("Error fetching user details:",e),o.Z.json({error:"Failed to fetch user details"},{status:500})}}async function y(e,{params:r}){try{let t=await (0,d.getServerSession)(u.L);if(!t?.user?.id||t?.user?.role!=="SUPER_ADMIN")return o.Z.json({error:"Super admin access required"},{status:403});let s=r.id,a=await e.json(),i=await l._.user.findUnique({where:{id:s},select:{email:!0,name:!0,role:!0,status:!0,companyId:!0}});if(!i)return o.Z.json({error:"User not found"},{status:404});let n={};["name","firstName","lastName","phone","role","status","title","department","timezone","language","companyId"].forEach(e=>{void 0!==a[e]&&(n[e]=a[e])}),a.password&&(n.password=await p().hash(a.password,12));let c=await l._.user.update({where:{id:s},data:n,include:{company:{select:{id:!0,name:!0,status:!0}}}});await l._.auditLog.create({data:{action:"USER_UPDATED",entityType:"User",entityId:s,userId:t.user.id,userEmail:t.user.email,userRole:t.user.role,oldValues:i,newValues:n,metadata:{updatedByAdmin:!0,adminId:t.user.id,fieldsUpdated:Object.keys(n)}}});let{password:m,...y}=c;return o.Z.json({user:y,message:"User updated successfully"})}catch(e){return console.error("Error updating user:",e),o.Z.json({error:"Failed to update user"},{status:500})}}async function w(e,{params:r}){try{let e=await (0,d.getServerSession)(u.L);if(!e?.user?.id||e?.user?.role!=="SUPER_ADMIN")return o.Z.json({error:"Super admin access required"},{status:403});let t=r.id;if(t===e.user.id)return o.Z.json({error:"Cannot delete your own account"},{status:400});let s=await l._.user.findUnique({where:{id:t},select:{email:!0,name:!0,role:!0,status:!0}});if(!s)return o.Z.json({error:"User not found"},{status:404});return await l._.user.update({where:{id:t},data:{status:"INACTIVE",email:`deleted_${Date.now()}_${s.email}`}}),await l._.auditLog.create({data:{action:"USER_DELETED",entityType:"User",entityId:t,userId:e.user.id,userEmail:e.user.email,userRole:e.user.role,oldValues:s,metadata:{deletedByAdmin:!0,adminId:e.user.id,deletionType:"soft"}}}),o.Z.json({message:"User deleted successfully"})}catch(e){return console.error("Error deleting user:",e),o.Z.json({error:"Failed to delete user"},{status:500})}}let g=new a.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/super-admin/users/[id]/route",pathname:"/api/super-admin/users/[id]",filename:"route",bundlePath:"app/api/super-admin/users/[id]/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\super-admin\\users\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:I,staticGenerationAsyncStorage:f,serverHooks:h,headerHooks:E,staticGenerationBailout:A}=g,_="/api/super-admin/users/[id]/route";function U(){return(0,n.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:f})}},3205:(e,r,t)=>{t.d(r,{L:()=>u});var s=t(86485),a=t(10375),i=t(50694),n=t(6521),o=t.n(n),d=t(9108);let u={providers:[(0,s.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let r=await d._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),t=r?.companyId;if(!t&&r){let e=await d._.company.findFirst({where:{ownerId:r.id},select:{id:!0}});(t=e?.id)&&await d._.user.update({where:{id:r.id},data:{companyId:t}})}if(!r)return console.log("User not found:",e.email),null;if(!r.password)return console.log("User has no password set:",e.email),null;if(!await o().compare(e.password,r.password))return console.log("Invalid password for user:",e.email),null;return await d._.user.update({where:{id:r.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",r.email),{id:r.id,email:r.email,name:r.name,role:r.role,companyId:t}}catch(e){return console.error("Authentication error:",e),null}}}),(0,a.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,i.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(console.log("JWT callback - user data:",{id:r.id,email:r.email,role:r.role,companyId:r.companyId}),e.role=r.role,e.companyId=r.companyId),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role,e.user.companyId=r.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,r,t)=>{t.d(r,{_:()=>a});let s=require("@prisma/client"),a=globalThis.prisma??new s.PrismaClient}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[1638,6206,6521,2455,4520],()=>t(27150));module.exports=s})();