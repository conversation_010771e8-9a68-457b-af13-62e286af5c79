'use client'

import { Badge } from '@/components/ui/badge'
import { Thermometer, Flame, Sun, Snowflake } from 'lucide-react'

interface LeadTemperatureProps {
  score: number
  size?: 'sm' | 'md' | 'lg'
  showIcon?: boolean
  showLabel?: boolean
}

export function LeadTemperature({ 
  score, 
  size = 'md', 
  showIcon = true, 
  showLabel = true 
}: LeadTemperatureProps) {
  const getTemperature = (score: number): 'HOT' | 'WARM' | 'COLD' => {
    if (score >= 70) return 'HOT'
    if (score >= 40) return 'WARM'
    return 'COLD'
  }

  const getTemperatureConfig = (temperature: string) => {
    switch (temperature) {
      case 'HOT':
        return {
          color: 'text-red-600 bg-red-100 border-red-200',
          icon: Flame,
          label: 'Hot Lead',
          description: 'High priority, ready to convert'
        }
      case 'WARM':
        return {
          color: 'text-orange-600 bg-orange-100 border-orange-200',
          icon: Sun,
          label: 'Warm Lead',
          description: 'Good potential, needs nurturing'
        }
      case 'COLD':
        return {
          color: 'text-blue-600 bg-blue-100 border-blue-200',
          icon: Snowflake,
          label: 'Cold Lead',
          description: 'Low engagement, requires attention'
        }
      default:
        return {
          color: 'text-gray-600 bg-gray-100 border-gray-200',
          icon: Thermometer,
          label: 'Unknown',
          description: 'Temperature not determined'
        }
    }
  }

  const getSizeClasses = (size: string) => {
    switch (size) {
      case 'sm':
        return {
          badge: 'text-xs px-2 py-1',
          icon: 'h-3 w-3',
          text: 'text-xs'
        }
      case 'lg':
        return {
          badge: 'text-base px-4 py-2',
          icon: 'h-5 w-5',
          text: 'text-base'
        }
      default: // md
        return {
          badge: 'text-sm px-3 py-1',
          icon: 'h-4 w-4',
          text: 'text-sm'
        }
    }
  }

  const temperature = getTemperature(score)
  const config = getTemperatureConfig(temperature)
  const sizeClasses = getSizeClasses(size)
  const Icon = config.icon

  return (
    <div className="flex items-center space-x-2">
      <Badge 
        className={`${config.color} border ${sizeClasses.badge} font-medium`}
        variant="outline"
      >
        <div className="flex items-center space-x-1">
          {showIcon && <Icon className={sizeClasses.icon} />}
          <span>{temperature}</span>
        </div>
      </Badge>
      
      {showLabel && (
        <div className="flex flex-col">
          <span className={`${sizeClasses.text} font-medium text-gray-900`}>
            {config.label}
          </span>
          {size === 'lg' && (
            <span className="text-xs text-gray-500">
              {config.description}
            </span>
          )}
        </div>
      )}
    </div>
  )
}

// Compact version for use in tables and lists
export function LeadTemperatureCompact({ score }: { score: number }) {
  return <LeadTemperature score={score} size="sm" showLabel={false} />
}

// Detailed version for lead detail pages
export function LeadTemperatureDetailed({ score }: { score: number }) {
  return <LeadTemperature score={score} size="lg" showIcon={true} showLabel={true} />
}
