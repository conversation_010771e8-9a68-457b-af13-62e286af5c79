import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Validation schema for customer creation/update
const customerSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Invalid email').optional().nullable().or(z.literal('')),
  phone: z.string().optional().nullable().or(z.literal('')),
  company: z.string().optional().nullable().or(z.literal('')),
  address: z.string().optional().nullable().or(z.literal('')),
  city: z.string().optional().nullable().or(z.literal('')),
  state: z.string().optional().nullable().or(z.literal('')),
  country: z.string().optional().nullable().or(z.literal('')),
  postalCode: z.string().optional().nullable().or(z.literal('')),
  industry: z.string().optional().nullable().or(z.literal('')),
  website: z.string().url('Invalid website URL').optional().nullable().or(z.literal('')),
  notes: z.string().optional().nullable().or(z.literal('')),
  tags: z.array(z.string()).optional().default([]),
  status: z.enum(['ACTIVE', 'INACTIVE', 'PROSPECT']).default('ACTIVE')
})

// GET /api/customers - List customers with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') || ''
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {
      companyId: session.user.companyId || undefined
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
        { companyName: { contains: search, mode: 'insensitive' } },
        { phone: { contains: search, mode: 'insensitive' } }
      ]
    }

    if (status) {
      where.status = status
    }

    // Get customers with pagination
    const [customers, total] = await Promise.all([
      prisma.customer.findMany({
        where,
        skip,
        take: limit,
        orderBy: { [sortBy]: sortOrder },
        include: {
          createdBy: {
            select: { name: true, email: true }
          },
          _count: {
            select: {
              leads: true,
              quotations: true,
              invoices: true,
              activities: true
            }
          }
        }
      }),
      prisma.customer.count({ where })
    ])

    return NextResponse.json({
      customers,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Error fetching customers:', error)
    return NextResponse.json(
      { error: 'Failed to fetch customers' },
      { status: 500 }
    )
  }
}

// POST /api/customers - Create new customer
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    console.log('Customer creation request body:', JSON.stringify(body, null, 2))
    console.log('About to transform data...')

    const validatedData = customerSchema.parse(body)
    console.log('Validated customer data:', JSON.stringify(validatedData, null, 2))

    // Transform empty strings to null for database and map company to companyName
    const transformedData: any = {}
    Object.entries(validatedData).forEach(([key, value]) => {
      if (key === 'company') {
        transformedData.companyName = value === '' ? null : value
      } else {
        transformedData[key] = value === '' ? null : value
      }
    })
    console.log('Transformed data:', JSON.stringify(transformedData, null, 2))

    // Check if customer with same email already exists
    if (transformedData.email) {
      const existingCustomer = await prisma.customer.findFirst({
        where: {
          email: transformedData.email,
          companyId: session.user.companyId || undefined
        }
      })

      if (existingCustomer) {
        return NextResponse.json(
          { error: 'Customer with this email already exists' },
          { status: 400 }
        )
      }
    }

    const customer = await prisma.customer.create({
      data: {
        ...transformedData,
        tags: transformedData.tags || [],
        companyId: session.user.companyId!,
        createdById: session.user.id
      },
      include: {
        createdBy: {
          select: { name: true, email: true }
        },
        _count: {
          select: {
            leads: true,
            quotations: true,
            invoices: true,
            activities: true
          }
        }
      }
    })

    // Log activity
    await prisma.activity.create({
      data: {
        type: 'NOTE',
        title: 'Customer Created',
        description: `Customer "${customer.name}" was created`,
        customerId: customer.id,
        companyId: session.user.companyId!,
        createdById: session.user.id
      }
    })

    return NextResponse.json(customer, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error('Customer creation validation error:', error.errors)
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: error.errors,
          message: error.errors.map(e => `${e.path.join('.')}: ${e.message}`).join(', ')
        },
        { status: 400 }
      )
    }

    console.error('Error creating customer:', error)
    return NextResponse.json(
      { error: 'Failed to create customer' },
      { status: 500 }
    )
  }
}
