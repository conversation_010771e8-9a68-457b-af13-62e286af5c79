'use client'

import { useSession } from 'next-auth/react'
import { redirect } from 'next/navigation'
import { SuperAdminLayout as Layout } from '@/components/super-admin/super-admin-layout'

export default function SuperAdminLayoutWrapper({
  children,
}: {
  children: React.ReactNode
}) {
  const { data: session, status } = useSession()

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  if (status === 'unauthenticated') {
    redirect('/auth/signin')
  }

  if (session?.user?.role !== 'SUPER_ADMIN') {
    redirect('/dashboard')
  }

  const user = session?.user ? {
    name: session.user.name,
    email: session.user.email,
    image: session.user.image,
    role: session.user.role,
    company: session.user.company && typeof session.user.company === 'object'
      ? { name: session.user.company.name }
      : undefined
  } : undefined

  return <Layout user={user}>{children}</Layout>
}
