"use strict";(()=>{var e={};e.id=6585,e.ids=[6585],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},75451:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>x,originalPathname:()=>_,patchFetch:()=>C,requestAsyncStorage:()=>b,routeModule:()=>f,serverHooks:()=>w,staticGenerationAsyncStorage:()=>I,staticGenerationBailout:()=>h});var o={};a.r(o),a.d(o,{GET:()=>y,PUT:()=>g});var n=a(95419),i=a(69108),r=a(99678),s=a(78070),l=a(81355),c=a(3205),p=a(9108),u=a(25252),d=a(52178);let m=u.Ry({companyName:u.Z_().optional(),companyEmail:u.Z_().email().optional().or(u.i0("")),companyPhone:u.Z_().optional(),companyAddress:u.Z_().optional(),companyCity:u.Z_().optional(),companyState:u.Z_().optional(),companyCountry:u.Z_().optional(),companyPostalCode:u.Z_().optional(),companyWebsite:u.Z_().url().optional().or(u.i0("")),companyLogo:u.Z_().optional(),industry:u.Z_().optional(),businessType:u.Z_().optional(),taxId:u.Z_().optional(),registrationNumber:u.Z_().optional(),defaultCurrency:u.Z_().default("USD"),taxRate:u.Rx().min(0).max(100).default(0),timezone:u.Z_().default("UTC"),dateFormat:u.Z_().default("MM/dd/yyyy"),timeFormat:u.Z_().default("12"),language:u.Z_().default("en"),primaryColor:u.Z_().default("#3b82f6"),secondaryColor:u.Z_().default("#1e3a8a"),accentColor:u.Z_().default("#f59e0b"),fontFamily:u.Z_().default("Inter"),invoicePrefix:u.Z_().default("INV"),quotationPrefix:u.Z_().default("QUO"),contractPrefix:u.Z_().default("CON"),invoiceNumbering:u.Z_().default("sequential"),defaultPaymentTerms:u.Z_().optional(),bankDetails:u.Yj().optional(),paymentMethods:u.Yj().optional(),emailSettings:u.Yj().optional(),notificationSettings:u.Yj().optional(),securitySettings:u.Yj().optional(),integrationSettings:u.Yj().optional(),featureSettings:u.Yj().optional(),customSettings:u.Yj().optional()});async function y(e){try{let e=await (0,l.getServerSession)(c.L);if(!e?.user?.id||!e?.user?.companyId)return s.Z.json({error:"Unauthorized"},{status:401});let t=await p._.companySettings.findUnique({where:{companyId:e.user.companyId},include:{company:{select:{id:!0,name:!0,email:!0,phone:!0,address:!0,city:!0,state:!0,country:!0,postalCode:!0,website:!0,logo:!0,industry:!0,size:!0,businessType:!0,taxId:!0,registrationNumber:!0}}}});if(!t){let t=await p._.companySettings.create({data:{companyId:e.user.companyId,companyName:e.user.company?.name||"",defaultCurrency:"USD",timezone:"UTC",dateFormat:"MM/dd/yyyy",timeFormat:"12",language:"en",primaryColor:"#3b82f6",secondaryColor:"#1e3a8a",accentColor:"#f59e0b",fontFamily:"Inter",invoicePrefix:"INV",quotationPrefix:"QUO",contractPrefix:"CON",invoiceNumbering:"sequential",emailSettings:{enabled:!1,smtpHost:"",smtpPort:587,smtpUsername:"",smtpPassword:"",smtpEncryption:"tls",fromEmail:"",fromName:"",replyTo:""},notificationSettings:{emailNotifications:!0,pushNotifications:!0,smsNotifications:!1,invoiceNotifications:!0,quotationNotifications:!0,contractNotifications:!0,paymentNotifications:!0,reminderNotifications:!0},securitySettings:{twoFactorRequired:!1,sessionTimeout:30,passwordPolicy:{minLength:8,requireUppercase:!0,requireLowercase:!0,requireNumbers:!0,requireSymbols:!1},ipWhitelist:[],allowedDomains:[]},integrationSettings:{webhooks:[],apiKeys:{},connectedServices:{}},featureSettings:{enableQuotations:!0,enableInvoices:!0,enableContracts:!0,enableTasks:!0,enableReports:!0,enableAnalytics:!0,enableFileStorage:!0,enableTeamCollaboration:!0}},include:{company:{select:{id:!0,name:!0,email:!0,phone:!0,address:!0,city:!0,state:!0,country:!0,postalCode:!0,website:!0,logo:!0,industry:!0,size:!0,businessType:!0,taxId:!0,registrationNumber:!0}}}});return s.Z.json({settings:{...t,taxRate:Number(t.taxRate)}})}return s.Z.json({settings:{...t,taxRate:Number(t.taxRate)}})}catch(e){return console.error("Error fetching company settings:",e),s.Z.json({error:"Failed to fetch company settings"},{status:500})}}async function g(e){try{let t=await (0,l.getServerSession)(c.L);if(!t?.user?.id||!t?.user?.companyId)return s.Z.json({error:"Unauthorized"},{status:401});let a=await e.json(),o=m.parse(a),n=await p._.$transaction(async e=>{let a=await e.companySettings.upsert({where:{companyId:t.user.companyId},update:{...o,updatedAt:new Date},create:{...o,companyId:t.user.companyId},include:{company:{select:{id:!0,name:!0,email:!0,phone:!0,address:!0,city:!0,state:!0,country:!0,postalCode:!0,website:!0,logo:!0,industry:!0,size:!0,businessType:!0,taxId:!0,registrationNumber:!0}}}});return(o.companyName||o.companyEmail||o.companyPhone)&&await e.company.update({where:{id:t.user.companyId},data:{...o.companyName&&{name:o.companyName},...o.companyEmail&&{email:o.companyEmail},...o.companyPhone&&{phone:o.companyPhone},...o.companyAddress&&{address:o.companyAddress},...o.companyCity&&{city:o.companyCity},...o.companyState&&{state:o.companyState},...o.companyCountry&&{country:o.companyCountry},...o.companyPostalCode&&{postalCode:o.companyPostalCode},...o.companyWebsite&&{website:o.companyWebsite},...o.companyLogo&&{logo:o.companyLogo},...o.industry&&{industry:o.industry},...o.businessType&&{businessType:o.businessType},...o.taxId&&{taxId:o.taxId},...o.registrationNumber&&{registrationNumber:o.registrationNumber}}}),await e.activity.create({data:{type:"SETTINGS",title:"Company Settings Updated",description:"Company settings were updated",companyId:t.user.companyId,createdById:t.user.id}}),a});return s.Z.json({settings:{...n,taxRate:Number(n.taxRate)},message:"Company settings updated successfully"})}catch(e){if(e instanceof d.jm)return s.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("Error updating company settings:",e),s.Z.json({error:"Failed to update company settings"},{status:500})}}let f=new n.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/settings/company/route",pathname:"/api/settings/company",filename:"route",bundlePath:"app/api/settings/company/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\settings\\company\\route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:b,staticGenerationAsyncStorage:I,serverHooks:w,headerHooks:x,staticGenerationBailout:h}=f,_="/api/settings/company/route";function C(){return(0,r.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:I})}},3205:(e,t,a)=>{a.d(t,{L:()=>c});var o=a(86485),n=a(10375),i=a(50694),r=a(6521),s=a.n(r),l=a(9108);let c={providers:[(0,o.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await l._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),a=t?.companyId;if(!a&&t){let e=await l._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(a=e?.id)&&await l._.user.update({where:{id:t.id},data:{companyId:a}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await s().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await l._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:a}}catch(e){return console.error("Authentication error:",e),null}}}),(0,n.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,i.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,a)=>{a.d(t,{_:()=>n});let o=require("@prisma/client"),n=globalThis.prisma??new o.PrismaClient}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),o=t.X(0,[1638,6206,6521,2455,4520,5252],()=>a(75451));module.exports=o})();