"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/facebook.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/facebook.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Facebook; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst Facebook = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Facebook\", [\n  [\n    \"path\",\n    { d: \"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z\", key: \"1jg4f8\" }\n  ]\n]);\n\n\n//# sourceMappingURL=facebook.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvZmFjZWJvb2suanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXNEOztBQUV0RCxpQkFBaUIsZ0VBQWdCO0FBQ2pDO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTs7QUFFK0I7QUFDL0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy9mYWNlYm9vay5qcz81MmVkIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2UgbHVjaWRlLXJlYWN0IHYwLjMwMy4wIC0gSVNDXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgSVNDIGxpY2Vuc2UuXG4gKiBTZWUgdGhlIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqL1xuXG5pbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uLmpzJztcblxuY29uc3QgRmFjZWJvb2sgPSBjcmVhdGVMdWNpZGVJY29uKFwiRmFjZWJvb2tcIiwgW1xuICBbXG4gICAgXCJwYXRoXCIsXG4gICAgeyBkOiBcIk0xOCAyaC0zYTUgNSAwIDAgMC01IDV2M0g3djRoM3Y4aDR2LThoM2wxLTRoLTRWN2ExIDEgMCAwIDEgMS0xaDN6XCIsIGtleTogXCIxamc0ZjhcIiB9XG4gIF1cbl0pO1xuXG5leHBvcnQgeyBGYWNlYm9vayBhcyBkZWZhdWx0IH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1mYWNlYm9vay5qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/facebook.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/github.js":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/github.js ***!
  \************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Github; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst Github = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Github\", [\n  [\n    \"path\",\n    {\n      d: \"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4\",\n      key: \"tonef\"\n    }\n  ],\n  [\"path\", { d: \"M9 18c-4.51 2-5-2-7-2\", key: \"9comsn\" }]\n]);\n\n\n//# sourceMappingURL=github.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvZ2l0aHViLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVzRDs7QUFFdEQsZUFBZSxnRUFBZ0I7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLDJDQUEyQztBQUN4RDs7QUFFNkI7QUFDN0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy9naXRodWIuanM/OTA3MSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlIGx1Y2lkZS1yZWFjdCB2MC4zMDMuMCAtIElTQ1xuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIElTQyBsaWNlbnNlLlxuICogU2VlIHRoZSBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKi9cblxuaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbi5qcyc7XG5cbmNvbnN0IEdpdGh1YiA9IGNyZWF0ZUx1Y2lkZUljb24oXCJHaXRodWJcIiwgW1xuICBbXG4gICAgXCJwYXRoXCIsXG4gICAge1xuICAgICAgZDogXCJNMTUgMjJ2LTRhNC44IDQuOCAwIDAgMC0xLTMuNWMzIDAgNi0yIDYtNS41LjA4LTEuMjUtLjI3LTIuNDgtMS0zLjUuMjgtMS4xNS4yOC0yLjM1IDAtMy41IDAgMC0xIDAtMyAxLjUtMi42NC0uNS01LjM2LS41LTggMEM2IDIgNSAyIDUgMmMtLjMgMS4xNS0uMyAyLjM1IDAgMy41QTUuNDAzIDUuNDAzIDAgMCAwIDQgOWMwIDMuNSAzIDUuNSA2IDUuNS0uMzkuNDktLjY4IDEuMDUtLjg1IDEuNjUtLjE3LjYtLjIyIDEuMjMtLjE1IDEuODV2NFwiLFxuICAgICAga2V5OiBcInRvbmVmXCJcbiAgICB9XG4gIF0sXG4gIFtcInBhdGhcIiwgeyBkOiBcIk05IDE4Yy00LjUxIDItNS0yLTctMlwiLCBrZXk6IFwiOWNvbXNuXCIgfV1cbl0pO1xuXG5leHBvcnQgeyBHaXRodWIgYXMgZGVmYXVsdCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Z2l0aHViLmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/github.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/instagram.js":
/*!***************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/instagram.js ***!
  \***************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Instagram; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst Instagram = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Instagram\", [\n  [\"rect\", { width: \"20\", height: \"20\", x: \"2\", y: \"2\", rx: \"5\", ry: \"5\", key: \"2e1cvw\" }],\n  [\"path\", { d: \"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z\", key: \"9exkf1\" }],\n  [\"line\", { x1: \"17.5\", x2: \"17.51\", y1: \"6.5\", y2: \"6.5\", key: \"r4j83e\" }]\n]);\n\n\n//# sourceMappingURL=instagram.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvaW5zdGFncmFtLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVzRDs7QUFFdEQsa0JBQWtCLGdFQUFnQjtBQUNsQyxhQUFhLDRFQUE0RTtBQUN6RixhQUFhLHFFQUFxRTtBQUNsRixhQUFhLDhEQUE4RDtBQUMzRTs7QUFFZ0M7QUFDaEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy9pbnN0YWdyYW0uanM/NzM5ZiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlIGx1Y2lkZS1yZWFjdCB2MC4zMDMuMCAtIElTQ1xuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIElTQyBsaWNlbnNlLlxuICogU2VlIHRoZSBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKi9cblxuaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbi5qcyc7XG5cbmNvbnN0IEluc3RhZ3JhbSA9IGNyZWF0ZUx1Y2lkZUljb24oXCJJbnN0YWdyYW1cIiwgW1xuICBbXCJyZWN0XCIsIHsgd2lkdGg6IFwiMjBcIiwgaGVpZ2h0OiBcIjIwXCIsIHg6IFwiMlwiLCB5OiBcIjJcIiwgcng6IFwiNVwiLCByeTogXCI1XCIsIGtleTogXCIyZTFjdndcIiB9XSxcbiAgW1wicGF0aFwiLCB7IGQ6IFwiTTE2IDExLjM3QTQgNCAwIDEgMSAxMi42MyA4IDQgNCAwIDAgMSAxNiAxMS4zN3pcIiwga2V5OiBcIjlleGtmMVwiIH1dLFxuICBbXCJsaW5lXCIsIHsgeDE6IFwiMTcuNVwiLCB4MjogXCIxNy41MVwiLCB5MTogXCI2LjVcIiwgeTI6IFwiNi41XCIsIGtleTogXCJyNGo4M2VcIiB9XVxuXSk7XG5cbmV4cG9ydCB7IEluc3RhZ3JhbSBhcyBkZWZhdWx0IH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbnN0YWdyYW0uanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/instagram.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/linkedin.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/linkedin.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Linkedin; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst Linkedin = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Linkedin\", [\n  [\n    \"path\",\n    {\n      d: \"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z\",\n      key: \"c2jq9f\"\n    }\n  ],\n  [\"rect\", { width: \"4\", height: \"12\", x: \"2\", y: \"9\", key: \"mk3on5\" }],\n  [\"circle\", { cx: \"4\", cy: \"4\", r: \"2\", key: \"bt5ra8\" }]\n]);\n\n\n//# sourceMappingURL=linkedin.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvbGlua2VkaW4uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXNEOztBQUV0RCxpQkFBaUIsZ0VBQWdCO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYSx5REFBeUQ7QUFDdEUsZUFBZSx5Q0FBeUM7QUFDeEQ7O0FBRStCO0FBQy9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvbGlua2VkaW4uanM/ZTJiYiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlIGx1Y2lkZS1yZWFjdCB2MC4zMDMuMCAtIElTQ1xuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIElTQyBsaWNlbnNlLlxuICogU2VlIHRoZSBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKi9cblxuaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbi5qcyc7XG5cbmNvbnN0IExpbmtlZGluID0gY3JlYXRlTHVjaWRlSWNvbihcIkxpbmtlZGluXCIsIFtcbiAgW1xuICAgIFwicGF0aFwiLFxuICAgIHtcbiAgICAgIGQ6IFwiTTE2IDhhNiA2IDAgMCAxIDYgNnY3aC00di03YTIgMiAwIDAgMC0yLTIgMiAyIDAgMCAwLTIgMnY3aC00di03YTYgNiAwIDAgMSA2LTZ6XCIsXG4gICAgICBrZXk6IFwiYzJqcTlmXCJcbiAgICB9XG4gIF0sXG4gIFtcInJlY3RcIiwgeyB3aWR0aDogXCI0XCIsIGhlaWdodDogXCIxMlwiLCB4OiBcIjJcIiwgeTogXCI5XCIsIGtleTogXCJtazNvbjVcIiB9XSxcbiAgW1wiY2lyY2xlXCIsIHsgY3g6IFwiNFwiLCBjeTogXCI0XCIsIHI6IFwiMlwiLCBrZXk6IFwiYnQ1cmE4XCIgfV1cbl0pO1xuXG5leHBvcnQgeyBMaW5rZWRpbiBhcyBkZWZhdWx0IH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1saW5rZWRpbi5qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js":
/*!*************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/map-pin.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MapPin; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst MapPin = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"MapPin\", [\n  [\"path\", { d: \"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z\", key: \"2oe9fu\" }],\n  [\"circle\", { cx: \"12\", cy: \"10\", r: \"3\", key: \"ilqhr7\" }]\n]);\n\n\n//# sourceMappingURL=map-pin.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvbWFwLXBpbi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFc0Q7O0FBRXRELGVBQWUsZ0VBQWdCO0FBQy9CLGFBQWEsb0VBQW9FO0FBQ2pGLGVBQWUsMkNBQTJDO0FBQzFEOztBQUU2QjtBQUM3QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL21hcC1waW4uanM/NWJmZCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlIGx1Y2lkZS1yZWFjdCB2MC4zMDMuMCAtIElTQ1xuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIElTQyBsaWNlbnNlLlxuICogU2VlIHRoZSBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKi9cblxuaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbi5qcyc7XG5cbmNvbnN0IE1hcFBpbiA9IGNyZWF0ZUx1Y2lkZUljb24oXCJNYXBQaW5cIiwgW1xuICBbXCJwYXRoXCIsIHsgZDogXCJNMjAgMTBjMCA2LTggMTItOCAxMnMtOC02LTgtMTJhOCA4IDAgMCAxIDE2IDBaXCIsIGtleTogXCIyb2U5ZnVcIiB9XSxcbiAgW1wiY2lyY2xlXCIsIHsgY3g6IFwiMTJcIiwgY3k6IFwiMTBcIiwgcjogXCIzXCIsIGtleTogXCJpbHFocjdcIiB9XVxuXSk7XG5cbmV4cG9ydCB7IE1hcFBpbiBhcyBkZWZhdWx0IH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1tYXAtcGluLmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/twitter.js":
/*!*************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/twitter.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Twitter; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst Twitter = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Twitter\", [\n  [\n    \"path\",\n    {\n      d: \"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z\",\n      key: \"pff0z6\"\n    }\n  ]\n]);\n\n\n//# sourceMappingURL=twitter.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvdHdpdHRlci5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFc0Q7O0FBRXRELGdCQUFnQixnRUFBZ0I7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFOEI7QUFDOUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy90d2l0dGVyLmpzPzhhZGIiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZSBsdWNpZGUtcmVhY3QgdjAuMzAzLjAgLSBJU0NcbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBJU0MgbGljZW5zZS5cbiAqIFNlZSB0aGUgTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICovXG5cbmltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24uanMnO1xuXG5jb25zdCBUd2l0dGVyID0gY3JlYXRlTHVjaWRlSWNvbihcIlR3aXR0ZXJcIiwgW1xuICBbXG4gICAgXCJwYXRoXCIsXG4gICAge1xuICAgICAgZDogXCJNMjIgNHMtLjcgMi4xLTIgMy40YzEuNiAxMC05LjQgMTcuMy0xOCAxMS42IDIuMi4xIDQuNC0uNiA2LTJDMyAxNS41LjUgOS42IDMgNWMyLjIgMi42IDUuNiA0LjEgOSA0LS45LTQuMiA0LTYuNiA3LTMuOCAxLjEgMCAzLTEuMiAzLTEuMnpcIixcbiAgICAgIGtleTogXCJwZmYwejZcIlxuICAgIH1cbiAgXVxuXSk7XG5cbmV4cG9ydCB7IFR3aXR0ZXIgYXMgZGVmYXVsdCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dHdpdHRlci5qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/twitter.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/youtube.js":
/*!*************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/youtube.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Youtube; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst Youtube = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Youtube\", [\n  [\n    \"path\",\n    {\n      d: \"M2.5 17a24.12 24.12 0 0 1 0-10 2 2 0 0 1 1.4-1.4 49.56 49.56 0 0 1 16.2 0A2 2 0 0 1 21.5 7a24.12 24.12 0 0 1 0 10 2 2 0 0 1-1.4 1.4 49.55 49.55 0 0 1-16.2 0A2 2 0 0 1 2.5 17\",\n      key: \"1q2vi4\"\n    }\n  ],\n  [\"path\", { d: \"m10 15 5-3-5-3z\", key: \"1jp15x\" }]\n]);\n\n\n//# sourceMappingURL=youtube.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMveW91dHViZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFc0Q7O0FBRXRELGdCQUFnQixnRUFBZ0I7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLHFDQUFxQztBQUNsRDs7QUFFOEI7QUFDOUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy95b3V0dWJlLmpzPzZjMGQiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZSBsdWNpZGUtcmVhY3QgdjAuMzAzLjAgLSBJU0NcbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBJU0MgbGljZW5zZS5cbiAqIFNlZSB0aGUgTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICovXG5cbmltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24uanMnO1xuXG5jb25zdCBZb3V0dWJlID0gY3JlYXRlTHVjaWRlSWNvbihcIllvdXR1YmVcIiwgW1xuICBbXG4gICAgXCJwYXRoXCIsXG4gICAge1xuICAgICAgZDogXCJNMi41IDE3YTI0LjEyIDI0LjEyIDAgMCAxIDAtMTAgMiAyIDAgMCAxIDEuNC0xLjQgNDkuNTYgNDkuNTYgMCAwIDEgMTYuMiAwQTIgMiAwIDAgMSAyMS41IDdhMjQuMTIgMjQuMTIgMCAwIDEgMCAxMCAyIDIgMCAwIDEtMS40IDEuNCA0OS41NSA0OS41NSAwIDAgMS0xNi4yIDBBMiAyIDAgMCAxIDIuNSAxN1wiLFxuICAgICAga2V5OiBcIjFxMnZpNFwiXG4gICAgfVxuICBdLFxuICBbXCJwYXRoXCIsIHsgZDogXCJtMTAgMTUgNS0zLTUtM3pcIiwga2V5OiBcIjFqcDE1eFwiIH1dXG5dKTtcblxuZXhwb3J0IHsgWW91dHViZSBhcyBkZWZhdWx0IH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD15b3V0dWJlLmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/youtube.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/landing/landing-page-content.tsx":
/*!*****************************************************!*\
  !*** ./components/landing/landing-page-content.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LandingPageContent: function() { return /* binding */ LandingPageContent; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/quote.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/facebook.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/instagram.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/youtube.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_6__);\n/* __next_internal_client_entry_do_not_use__ LandingPageContent auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Default fallback content\nconst defaultContent = {\n    hero: {\n        enabled: true,\n        title: \"Build Your SaaS Business\",\n        subtitle: \"The Complete Platform\",\n        description: \"Everything you need to launch, grow, and scale your SaaS business. From customer management to billing, we've got you covered.\",\n        primaryCTA: {\n            text: \"Start Free Trial\",\n            link: \"/auth/signup\"\n        },\n        secondaryCTA: {\n            text: \"Watch Demo\",\n            link: \"/demo\"\n        },\n        backgroundImage: \"\",\n        backgroundVideo: \"\"\n    },\n    features: {\n        enabled: true,\n        title: \"Everything You Need\",\n        subtitle: \"Powerful Features\",\n        items: [\n            {\n                id: \"1\",\n                title: \"Customer Management\",\n                description: \"Manage your customers, track interactions, and build lasting relationships.\",\n                icon: \"users\",\n                image: \"\"\n            },\n            {\n                id: \"2\",\n                title: \"Subscription Billing\",\n                description: \"Automated billing, invoicing, and payment processing for recurring revenue.\",\n                icon: \"credit-card\",\n                image: \"\"\n            },\n            {\n                id: \"3\",\n                title: \"Analytics & Reports\",\n                description: \"Comprehensive analytics to track your business performance and growth.\",\n                icon: \"bar-chart\",\n                image: \"\"\n            },\n            {\n                id: \"4\",\n                title: \"Multi-Tenant Architecture\",\n                description: \"Secure data isolation with company-based access control and team management.\",\n                icon: \"building\",\n                image: \"\"\n            },\n            {\n                id: \"5\",\n                title: \"Enterprise Security\",\n                description: \"Role-based access control with audit logs and data encryption.\",\n                icon: \"shield\",\n                image: \"\"\n            },\n            {\n                id: \"6\",\n                title: \"Global Ready\",\n                description: \"Multi-currency support and localization for worldwide businesses.\",\n                icon: \"globe\",\n                image: \"\"\n            }\n        ]\n    },\n    pricing: {\n        enabled: true,\n        title: \"Simple, Transparent Pricing\",\n        subtitle: \"Choose the plan that fits your needs\",\n        showPricingTable: true,\n        customMessage: \"\"\n    },\n    testimonials: {\n        enabled: true,\n        title: \"What Our Customers Say\",\n        subtitle: \"Trusted by thousands of businesses\",\n        items: [\n            {\n                id: \"1\",\n                name: \"John Smith\",\n                role: \"CEO\",\n                company: \"TechCorp\",\n                content: \"This platform has transformed how we manage our SaaS business. The automation features alone have saved us countless hours.\",\n                avatar: \"\",\n                rating: 5\n            },\n            {\n                id: \"2\",\n                name: \"Sarah Johnson\",\n                role: \"Founder\",\n                company: \"StartupXYZ\",\n                content: \"The best investment we've made for our business. The customer management features are incredibly powerful.\",\n                avatar: \"\",\n                rating: 5\n            },\n            {\n                id: \"3\",\n                name: \"Mike Chen\",\n                role: \"CTO\",\n                company: \"InnovateLab\",\n                content: \"Excellent platform with great support. The analytics help us make data-driven decisions every day.\",\n                avatar: \"\",\n                rating: 5\n            }\n        ]\n    },\n    faq: {\n        enabled: true,\n        title: \"Frequently Asked Questions\",\n        subtitle: \"Everything you need to know\",\n        items: [\n            {\n                id: \"1\",\n                question: \"How do I get started?\",\n                answer: \"Simply sign up for a free trial and follow our onboarding guide to set up your account. Our team is here to help you every step of the way.\"\n            },\n            {\n                id: \"2\",\n                question: \"Can I cancel anytime?\",\n                answer: \"Yes, you can cancel your subscription at any time. No long-term contracts or cancellation fees. Your data will remain accessible during the notice period.\"\n            },\n            {\n                id: \"3\",\n                question: \"Is my data secure?\",\n                answer: \"Absolutely. We use enterprise-grade security measures including encryption, regular backups, and compliance with industry standards like SOC 2 and GDPR.\"\n            },\n            {\n                id: \"4\",\n                question: \"Do you offer customer support?\",\n                answer: \"Yes, we provide 24/7 customer support via email, chat, and phone. Our premium plans also include dedicated account managers.\"\n            },\n            {\n                id: \"5\",\n                question: \"Can I integrate with other tools?\",\n                answer: \"Yes, we offer integrations with popular tools like Slack, Zapier, QuickBooks, and many more. We also provide a robust API for custom integrations.\"\n            }\n        ]\n    },\n    cta: {\n        enabled: true,\n        title: \"Ready to Get Started?\",\n        description: \"Join thousands of businesses already using our platform to grow their SaaS.\",\n        buttonText: \"Start Your Free Trial\",\n        buttonLink: \"/auth/signup\",\n        backgroundImage: \"\"\n    },\n    footer: {\n        enabled: true,\n        companyDescription: \"The complete SaaS platform for modern businesses.\",\n        links: [\n            {\n                id: \"1\",\n                title: \"Product\",\n                items: [\n                    {\n                        id: \"1\",\n                        text: \"Features\",\n                        link: \"/features\"\n                    },\n                    {\n                        id: \"2\",\n                        text: \"Pricing\",\n                        link: \"/pricing\"\n                    },\n                    {\n                        id: \"3\",\n                        text: \"Security\",\n                        link: \"/security\"\n                    },\n                    {\n                        id: \"4\",\n                        text: \"Integrations\",\n                        link: \"/integrations\"\n                    }\n                ]\n            },\n            {\n                id: \"2\",\n                title: \"Company\",\n                items: [\n                    {\n                        id: \"1\",\n                        text: \"About\",\n                        link: \"/about\"\n                    },\n                    {\n                        id: \"2\",\n                        text: \"Blog\",\n                        link: \"/blog\"\n                    },\n                    {\n                        id: \"3\",\n                        text: \"Careers\",\n                        link: \"/careers\"\n                    },\n                    {\n                        id: \"4\",\n                        text: \"Contact\",\n                        link: \"/contact\"\n                    }\n                ]\n            },\n            {\n                id: \"3\",\n                title: \"Support\",\n                items: [\n                    {\n                        id: \"1\",\n                        text: \"Help Center\",\n                        link: \"/help\"\n                    },\n                    {\n                        id: \"2\",\n                        text: \"Documentation\",\n                        link: \"/docs\"\n                    },\n                    {\n                        id: \"3\",\n                        text: \"API Reference\",\n                        link: \"/api\"\n                    },\n                    {\n                        id: \"4\",\n                        text: \"Status\",\n                        link: \"/status\"\n                    }\n                ]\n            }\n        ],\n        socialLinks: {\n            twitter: \"https://twitter.com/yourcompany\",\n            linkedin: \"https://linkedin.com/company/yourcompany\",\n            facebook: \"https://facebook.com/yourcompany\",\n            instagram: \"https://instagram.com/yourcompany\"\n        },\n        copyrightText: \"\\xa9 2024 Your Company. All rights reserved.\"\n    },\n    seo: {\n        title: \"SaaS Platform - Build Your Business\",\n        description: \"The complete SaaS platform for modern businesses. Customer management, billing, analytics, and more.\",\n        keywords: \"saas, platform, business, customer management, billing, analytics\",\n        ogImage: \"\"\n    }\n};\nconst getIconComponent = (iconName)=>{\n    const icons = {\n        users: _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        \"credit-card\": _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        \"bar-chart\": _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        building: _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        shield: _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        globe: _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        zap: _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        \"file-text\": _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n    };\n    return icons[iconName] || _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n};\nconst formatStorage = (bytes)=>{\n    const gb = bytes / (1024 * 1024 * 1024);\n    return gb >= 1 ? \"\".concat(gb, \"GB\") : \"\".concat(Math.round(gb * 1024), \"MB\");\n};\nconst getFeatureList = (plan)=>{\n    const features = [];\n    // Add usage limits\n    features.push(\"Up to \".concat(plan.maxUsers, \" users\"));\n    features.push(\"\".concat(plan.maxCompanies, \" \").concat(plan.maxCompanies === 1 ? \"company\" : \"companies\"));\n    features.push(\"\".concat(plan.maxCustomers, \" customers\"));\n    features.push(\"\".concat(plan.maxQuotations, \" quotations/month\"));\n    features.push(\"\".concat(plan.maxInvoices, \" invoices/month\"));\n    features.push(\"\".concat(formatStorage(plan.maxStorage), \" storage\"));\n    // Add feature flags\n    if (plan.features.basicReporting) features.push(\"Basic reporting\");\n    if (plan.features.emailSupport) features.push(\"Email support\");\n    if (plan.features.mobileApp) features.push(\"Mobile app access\");\n    if (plan.features.advancedAnalytics) features.push(\"Advanced analytics\");\n    if (plan.features.customBranding) features.push(\"Custom branding\");\n    if (plan.features.apiAccess) features.push(\"API access\");\n    if (plan.features.prioritySupport) features.push(\"Priority support\");\n    if (plan.features.customIntegrations) features.push(\"Custom integrations\");\n    if (plan.features.advancedSecurity) features.push(\"Advanced security\");\n    if (plan.features.dedicatedManager) features.push(\"Dedicated account manager\");\n    return features;\n};\nfunction LandingPageContent() {\n    var _content_hero, _content_features, _content_pricing, _content_testimonials, _content_faq, _content_cta, _content_footer;\n    _s();\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultContent);\n    const [plans, setPlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [openFAQ, setOpenFAQ] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isYearly, setIsYearly] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchData = async ()=>{\n            try {\n                // Fetch CMS content\n                const cmsResponse = await fetch(\"/api/super-admin/cms\");\n                const cmsData = await cmsResponse.json();\n                if (cmsData.success && cmsData.content) {\n                    // Merge with default content to ensure all sections exist\n                    setContent({\n                        ...defaultContent,\n                        ...cmsData.content\n                    });\n                }\n                // Fetch pricing plans\n                const plansResponse = await fetch(\"/api/pricing-plans?publicOnly=true\");\n                const plansData = await plansResponse.json();\n                if (plansData.success) {\n                    // Sort plans by sortOrder and filter active public plans\n                    const activePlans = plansData.data.filter((plan)=>plan.isActive && plan.isPublic).sort((a, b)=>a.sortOrder - b.sortOrder);\n                    setPlans(activePlans);\n                }\n            } catch (error) {\n                console.error(\"Error fetching data:\", error);\n            // Use default content on error\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchData();\n    }, []);\n    const getPrice = (plan)=>{\n        if (isYearly && plan.yearlyPrice) {\n            return plan.yearlyPrice / 12 // Show monthly equivalent\n            ;\n        }\n        return plan.monthlyPrice;\n    };\n    const getYearlyDiscount = (plan)=>{\n        if (!plan.yearlyPrice || !plan.monthlyPrice) return 0;\n        const yearlyMonthly = plan.yearlyPrice / 12;\n        const discount = (plan.monthlyPrice - yearlyMonthly) / plan.monthlyPrice * 100;\n        return Math.round(discount);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 469,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n            lineNumber: 468,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"border-b bg-white/95 backdrop-blur-sm sticky top-0 z-50 shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-8 w-8 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: \"SaaS Platform\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"hidden md:flex items-center space-x-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#features\",\n                                            className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                            children: \"Features\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#pricing\",\n                                            className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                            children: \"Pricing\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#testimonials\",\n                                            className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                            children: \"Testimonials\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#faq\",\n                                            className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                            children: \"FAQ\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 497,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/contact\",\n                                            className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                            children: \"Contact\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 487,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/auth/signin\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"ghost\",\n                                                className: \"text-gray-600 hover:text-gray-900\",\n                                                children: \"Sign In\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/auth/signup\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                className: \"bg-blue-600 hover:bg-blue-700\",\n                                                children: \"Get Started Free\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 506,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"md:hidden p-2\",\n                                    onClick: ()=>setMobileMenuOpen(!mobileMenuOpen),\n                                    children: mobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-6 w-6 text-gray-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-6 w-6 text-gray-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 527,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 479,\n                            columnNumber: 11\n                        }, this),\n                        mobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden mt-4 pb-4 border-t\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex flex-col space-y-4 pt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#features\",\n                                        className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"Features\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#pricing\",\n                                        className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"Pricing\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 543,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#testimonials\",\n                                        className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"Testimonials\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 550,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#faq\",\n                                        className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"FAQ\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 557,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                        href: \"/contact\",\n                                        className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"Contact\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 564,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col space-y-2 pt-4 border-t\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                href: \"/auth/signin\",\n                                                onClick: ()=>setMobileMenuOpen(false),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"ghost\",\n                                                    className: \"w-full justify-start\",\n                                                    children: \"Sign In\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 573,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 572,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                href: \"/auth/signup\",\n                                                onClick: ()=>setMobileMenuOpen(false),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    className: \"w-full bg-blue-600 hover:bg-blue-700\",\n                                                    children: \"Get Started Free\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 578,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 577,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 571,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                lineNumber: 535,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 534,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 478,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 477,\n                columnNumber: 7\n            }, this),\n            ((_content_hero = content.hero) === null || _content_hero === void 0 ? void 0 : _content_hero.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4 relative overflow-hidden\",\n                children: [\n                    content.hero.backgroundImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 z-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_6___default()), {\n                            src: content.hero.backgroundImage,\n                            alt: \"Hero Background\",\n                            fill: true,\n                            className: \"object-cover opacity-20\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 594,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                        lineNumber: 593,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto text-center relative z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto\",\n                            children: [\n                                content.hero.subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                    className: \"mb-4 text-sm px-4 py-2\",\n                                    children: content.hero.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 605,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-5xl md:text-6xl font-bold text-gray-900 mb-6\",\n                                    children: content.hero.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 609,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n                                    children: content.hero.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 612,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-4 justify-center mb-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: content.hero.primaryCTA.link,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                size: \"lg\",\n                                                className: \"text-lg px-8 py-3\",\n                                                children: [\n                                                    content.hero.primaryCTA.text,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"ml-2 h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 619,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 617,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 616,\n                                            columnNumber: 17\n                                        }, this),\n                                        content.hero.secondaryCTA.text && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: content.hero.secondaryCTA.link,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                size: \"lg\",\n                                                variant: \"outline\",\n                                                className: \"text-lg px-8 py-3\",\n                                                children: content.hero.secondaryCTA.text\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 623,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 615,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 603,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                        lineNumber: 602,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 591,\n                columnNumber: 9\n            }, this),\n            ((_content_features = content.features) === null || _content_features === void 0 ? void 0 : _content_features.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"features\",\n                className: \"py-20 px-4 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                    children: content.features.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 640,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                                    children: content.features.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 643,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 639,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: content.features.items.map((feature)=>{\n                                const IconComponent = getIconComponent(feature.icon);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"border-0 shadow-lg hover:shadow-xl transition-shadow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                        className: \"h-6 w-6 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 655,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 654,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-xl\",\n                                                    children: feature.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 657,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 653,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                className: \"text-gray-600\",\n                                                children: feature.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 660,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 659,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, feature.id, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 652,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 648,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 638,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 637,\n                columnNumber: 9\n            }, this),\n            ((_content_pricing = content.pricing) === null || _content_pricing === void 0 ? void 0 : _content_pricing.enabled) && plans.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"pricing\",\n                className: \"py-20 px-4 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                    children: content.pricing.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 677,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 max-w-2xl mx-auto mb-8\",\n                                    children: content.pricing.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 680,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-4 mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm \".concat(!isYearly ? \"text-gray-900 font-medium\" : \"text-gray-500\"),\n                                            children: \"Monthly\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 686,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Switch, {\n                                            checked: isYearly,\n                                            onCheckedChange: setIsYearly\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 689,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm \".concat(isYearly ? \"text-gray-900 font-medium\" : \"text-gray-500\"),\n                                            children: \"Yearly\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 693,\n                                            columnNumber: 17\n                                        }, this),\n                                        plans.some((plan)=>getYearlyDiscount(plan) > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            className: \"ml-2 bg-green-100 text-green-800\",\n                                            children: [\n                                                \"Save up to \",\n                                                Math.max(...plans.map(getYearlyDiscount)),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 697,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 685,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 676,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto\",\n                            children: plans.map((plan, index)=>{\n                                const features = getFeatureList(plan);\n                                const price = getPrice(plan);\n                                const discount = getYearlyDiscount(plan);\n                                const isPopular = index === 1 // Middle plan is popular\n                                ;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"relative \".concat(isPopular ? \"border-blue-500 shadow-xl scale-105\" : \"border-gray-200 shadow-lg\", \" bg-white\"),\n                                    children: [\n                                        isPopular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -top-4 left-1/2 transform -translate-x-1/2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                className: \"bg-blue-500 text-white px-4 py-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 717,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    \"Most Popular\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 716,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 715,\n                                            columnNumber: 23\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            className: \"text-center pb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: plan.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 724,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                    className: \"text-gray-600 mt-2\",\n                                                    children: plan.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 725,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-baseline justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-5xl font-bold text-gray-900\",\n                                                                    children: [\n                                                                        \"$\",\n                                                                        price.toFixed(0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                    lineNumber: 731,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-500 ml-1\",\n                                                                    children: \"/month\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                    lineNumber: 734,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 730,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        isYearly && discount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-green-600 mt-2\",\n                                                            children: [\n                                                                \"Save \",\n                                                                discount,\n                                                                \"% with yearly billing\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 737,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        plan.trialDays > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-blue-600 mt-2\",\n                                                            children: [\n                                                                plan.trialDays,\n                                                                \"-day free trial\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 742,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 729,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 723,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        features.slice(0, 8).map((feature, featureIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-green-500 flex-shrink-0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                        lineNumber: 753,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-700 text-sm\",\n                                                                        children: feature\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                        lineNumber: 754,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, featureIndex, true, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                lineNumber: 752,\n                                                                columnNumber: 27\n                                                            }, this)),\n                                                        features.length > 8 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500 italic\",\n                                                            children: [\n                                                                \"+\",\n                                                                features.length - 8,\n                                                                \" more features\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 758,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 750,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                    href: \"/auth/signup\",\n                                                    className: \"block\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        className: \"w-full \".concat(isPopular ? \"bg-blue-600 hover:bg-blue-700\" : \"\"),\n                                                        variant: isPopular ? \"default\" : \"outline\",\n                                                        size: \"lg\",\n                                                        children: [\n                                                            \"Get Started\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"ml-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                lineNumber: 771,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 765,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 764,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 749,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, plan.id, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 713,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 705,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mt-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"max-w-2xl mx-auto border-gray-200 shadow-lg bg-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                            children: \"Need something custom?\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 784,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-6\",\n                                            children: \"Contact our sales team for enterprise pricing, custom features, and dedicated support.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 787,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    size: \"lg\",\n                                                    variant: \"outline\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 792,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Contact Sales\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 791,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    size: \"lg\",\n                                                    variant: \"ghost\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 796,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Schedule Demo\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 795,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 790,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 783,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                lineNumber: 782,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 781,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 675,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 674,\n                columnNumber: 9\n            }, this),\n            ((_content_testimonials = content.testimonials) === null || _content_testimonials === void 0 ? void 0 : _content_testimonials.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"testimonials\",\n                className: \"py-20 px-4 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                    children: content.testimonials.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 812,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                                    children: content.testimonials.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 815,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 811,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: content.testimonials.items.map((testimonial)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"border-0 shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    ...Array(testimonial.rating)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-5 w-5 text-yellow-400 fill-current\"\n                                                    }, i, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 826,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 824,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"h-8 w-8 text-gray-300 mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 829,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-6 italic\",\n                                                children: [\n                                                    '\"',\n                                                    testimonial.content,\n                                                    '\"'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 830,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    testimonial.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                                        src: testimonial.avatar,\n                                                        alt: testimonial.name,\n                                                        width: 48,\n                                                        height: 48,\n                                                        className: \"rounded-full mr-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 835,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-gray-200 rounded-full mr-4 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-6 w-6 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 844,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 843,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-semibold text-gray-900\",\n                                                                children: testimonial.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                lineNumber: 848,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: [\n                                                                    testimonial.role,\n                                                                    \", \",\n                                                                    testimonial.company\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                lineNumber: 849,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 847,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 833,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 823,\n                                        columnNumber: 19\n                                    }, this)\n                                }, testimonial.id, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 822,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 820,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 810,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 809,\n                columnNumber: 9\n            }, this),\n            ((_content_faq = content.faq) === null || _content_faq === void 0 ? void 0 : _content_faq.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"faq\",\n                className: \"py-20 px-4 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto max-w-4xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                    children: content.faq.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 865,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600\",\n                                    children: content.faq.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 868,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 864,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: content.faq.items.map((faq)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"border shadow-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"p-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"w-full p-6 text-left flex items-center justify-between hover:bg-gray-50\",\n                                                onClick: ()=>setOpenFAQ(openFAQ === faq.id ? null : faq.id),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold text-gray-900\",\n                                                        children: faq.question\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 881,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    openFAQ === faq.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 883,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 885,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 877,\n                                                columnNumber: 21\n                                            }, this),\n                                            openFAQ === faq.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-6 pb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: faq.answer\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 890,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 889,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 876,\n                                        columnNumber: 19\n                                    }, this)\n                                }, faq.id, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 875,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 873,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 863,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 862,\n                columnNumber: 9\n            }, this),\n            ((_content_cta = content.cta) === null || _content_cta === void 0 ? void 0 : _content_cta.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4 bg-blue-600 relative overflow-hidden\",\n                children: [\n                    content.cta.backgroundImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 z-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_6___default()), {\n                            src: content.cta.backgroundImage,\n                            alt: \"CTA Background\",\n                            fill: true,\n                            className: \"object-cover opacity-20\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 906,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                        lineNumber: 905,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto text-center relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl font-bold text-white mb-6\",\n                                children: content.cta.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                lineNumber: 915,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-blue-100 mb-8 max-w-2xl mx-auto\",\n                                children: content.cta.description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                lineNumber: 918,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                href: content.cta.buttonLink,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    size: \"lg\",\n                                    variant: \"secondary\",\n                                    className: \"text-lg px-8 py-3\",\n                                    children: [\n                                        content.cta.buttonText,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"ml-2 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 924,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 922,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                lineNumber: 921,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                        lineNumber: 914,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 903,\n                columnNumber: 9\n            }, this),\n            ((_content_footer = content.footer) === null || _content_footer === void 0 ? void 0 : _content_footer.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-gray-900 text-white py-16 px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-5 gap-8 mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-8 w-8 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 939,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: \"SaaS Platform\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 940,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 938,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 mb-6 leading-relaxed\",\n                                            children: content.footer.companyDescription\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 942,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3 text-gray-400\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 949,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"<EMAIL>\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 950,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 948,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3 text-gray-400\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 953,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"+****************\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 954,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 952,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3 text-gray-400\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 957,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"San Francisco, CA\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 958,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 956,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 947,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-4\",\n                                            children: [\n                                                content.footer.socialLinks.twitter && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: content.footer.socialLinks.twitter,\n                                                    className: \"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-blue-600 transition-colors\",\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 971,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 965,\n                                                    columnNumber: 21\n                                                }, this),\n                                                content.footer.socialLinks.linkedin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: content.footer.socialLinks.linkedin,\n                                                    className: \"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-blue-700 transition-colors\",\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 981,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 975,\n                                                    columnNumber: 21\n                                                }, this),\n                                                content.footer.socialLinks.facebook && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: content.footer.socialLinks.facebook,\n                                                    className: \"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-blue-800 transition-colors\",\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 991,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 985,\n                                                    columnNumber: 21\n                                                }, this),\n                                                content.footer.socialLinks.instagram && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: content.footer.socialLinks.instagram,\n                                                    className: \"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-pink-600 transition-colors\",\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 1001,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 995,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"https://github.com\",\n                                                    className: \"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 transition-colors\",\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 1010,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 1004,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"https://youtube.com\",\n                                                    className: \"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-red-600 transition-colors\",\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 1018,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 1012,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 963,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 937,\n                                    columnNumber: 15\n                                }, this),\n                                content.footer.links.map((linkGroup)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-4 text-white\",\n                                                children: linkGroup.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 1026,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-3\",\n                                                children: linkGroup.items.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                            href: link.link,\n                                                            className: \"text-gray-400 hover:text-white transition-colors\",\n                                                            children: link.text\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 1030,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, link.id, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 1029,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 1027,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, linkGroup.id, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 1025,\n                                        columnNumber: 17\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 935,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-800 pt-8 mb-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold mb-4 text-white\",\n                                        children: \"Stay Updated\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 1046,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-4\",\n                                        children: \"Get the latest updates, tips, and insights delivered to your inbox.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 1047,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"email\",\n                                                placeholder: \"Enter your email\",\n                                                className: \"flex-1 px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 1051,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                className: \"bg-blue-600 hover:bg-blue-700\",\n                                                children: \"Subscribe\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 1056,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 1050,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                lineNumber: 1045,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 1044,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-4 md:mb-0\",\n                                    children: content.footer.copyrightText\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 1065,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-6 text-gray-400 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/privacy\",\n                                            className: \"hover:text-white transition-colors\",\n                                            children: \"Privacy Policy\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 1069,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/terms\",\n                                            className: \"hover:text-white transition-colors\",\n                                            children: \"Terms of Service\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 1072,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/cookies\",\n                                            className: \"hover:text-white transition-colors\",\n                                            children: \"Cookie Policy\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 1075,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/security\",\n                                            className: \"hover:text-white transition-colors\",\n                                            children: \"Security\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 1078,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 1068,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 1064,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 934,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 933,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n        lineNumber: 475,\n        columnNumber: 5\n    }, this);\n}\n_s(LandingPageContent, \"Ob77mu/u4HMnkCjOlnHJhwEopZQ=\");\n_c = LandingPageContent;\nvar _c;\n$RefreshReg$(_c, \"LandingPageContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/landing/landing-page-content.tsx\n"));

/***/ })

});