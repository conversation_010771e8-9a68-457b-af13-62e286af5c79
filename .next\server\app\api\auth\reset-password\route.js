"use strict";(()=>{var e={};e.id=9436,e.ids=[9436],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6113:e=>{e.exports=require("crypto")},99918:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>v,originalPathname:()=>A,patchFetch:()=>j,requestAsyncStorage:()=>g,routeModule:()=>m,serverHooks:()=>f,staticGenerationAsyncStorage:()=>P,staticGenerationBailout:()=>x});var s={};t.r(s),t.d(s,{POST:()=>h});var a=t(95419),o=t(69108),n=t(99678),i=t(78070),u=t(6521),d=t.n(u),p=t(9108),l=t(25252),w=t(52178);let c=l.Ry({token:l.Z_().min(1,"Token is required"),password:l.Z_().min(8,"Password must be at least 8 characters").regex(/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,"Password must contain at least one uppercase letter, one lowercase letter, and one number")});async function h(e){try{let r=await e.json(),{token:t,password:s}=c.parse(r),a=await p._.passwordReset.findFirst({where:{token:t,expiresAt:{gt:new Date},usedAt:null},include:{user:!0}});if(!a)return i.Z.json({error:"Invalid or expired reset token"},{status:400});let o=await d().hash(s,12);return await p._.$transaction(async e=>{await e.user.update({where:{id:a.userId},data:{password:o,passwordChangedAt:new Date}}),await e.passwordReset.update({where:{id:a.id},data:{usedAt:new Date}}),await e.passwordReset.updateMany({where:{userId:a.userId,id:{not:a.id},usedAt:null},data:{usedAt:new Date}})}),console.log(`Password reset completed for user: ${a.user.email}`),i.Z.json({message:"Password reset successfully"})}catch(e){if(e instanceof w.jm)return i.Z.json({error:"Invalid request data",details:e.errors},{status:400});return console.error("Reset password error:",e),i.Z.json({error:"Internal server error"},{status:500})}}let m=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/auth/reset-password/route",pathname:"/api/auth/reset-password",filename:"route",bundlePath:"app/api/auth/reset-password/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\auth\\reset-password\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:g,staticGenerationAsyncStorage:P,serverHooks:f,headerHooks:v,staticGenerationBailout:x}=m,A="/api/auth/reset-password/route";function j(){return(0,n.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:P})}},9108:(e,r,t)=>{t.d(r,{_:()=>a});let s=require("@prisma/client"),a=globalThis.prisma??new s.PrismaClient}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[1638,6206,6521,5252],()=>t(99918));module.exports=s})();