"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/super-admin/subscriptions/page",{

/***/ "(app-pages-browser)/./app/super-admin/subscriptions/page.tsx":
/*!************************************************!*\
  !*** ./app/super-admin/subscriptions/page.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SubscriptionManagementPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,CheckCircle,Clock,CreditCard,Crown,Edit,Eye,Filter,MoreHorizontal,Plus,RefreshCw,Search,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,CheckCircle,Clock,CreditCard,Crown,Edit,Eye,Filter,MoreHorizontal,Plus,RefreshCw,Search,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,CheckCircle,Clock,CreditCard,Crown,Edit,Eye,Filter,MoreHorizontal,Plus,RefreshCw,Search,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,CheckCircle,Clock,CreditCard,Crown,Edit,Eye,Filter,MoreHorizontal,Plus,RefreshCw,Search,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,CheckCircle,Clock,CreditCard,Crown,Edit,Eye,Filter,MoreHorizontal,Plus,RefreshCw,Search,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,CheckCircle,Clock,CreditCard,Crown,Edit,Eye,Filter,MoreHorizontal,Plus,RefreshCw,Search,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,CheckCircle,Clock,CreditCard,Crown,Edit,Eye,Filter,MoreHorizontal,Plus,RefreshCw,Search,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,CheckCircle,Clock,CreditCard,Crown,Edit,Eye,Filter,MoreHorizontal,Plus,RefreshCw,Search,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,CheckCircle,Clock,CreditCard,Crown,Edit,Eye,Filter,MoreHorizontal,Plus,RefreshCw,Search,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,CheckCircle,Clock,CreditCard,Crown,Edit,Eye,Filter,MoreHorizontal,Plus,RefreshCw,Search,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,CheckCircle,Clock,CreditCard,Crown,Edit,Eye,Filter,MoreHorizontal,Plus,RefreshCw,Search,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,CheckCircle,Clock,CreditCard,Crown,Edit,Eye,Filter,MoreHorizontal,Plus,RefreshCw,Search,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,CheckCircle,Clock,CreditCard,Crown,Edit,Eye,Filter,MoreHorizontal,Plus,RefreshCw,Search,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,CheckCircle,Clock,CreditCard,Crown,Edit,Eye,Filter,MoreHorizontal,Plus,RefreshCw,Search,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,CheckCircle,Clock,CreditCard,Crown,Edit,Eye,Filter,MoreHorizontal,Plus,RefreshCw,Search,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,CheckCircle,Clock,CreditCard,Crown,Edit,Eye,Filter,MoreHorizontal,Plus,RefreshCw,Search,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/more-horizontal.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction SubscriptionManagementPage() {\n    var _session_user;\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [subscriptions, setSubscriptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [planFilter, setPlanFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [showCreateDialog, setShowCreateDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [pricingPlans, setPricingPlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        companyId: \"\",\n        planId: \"\",\n        status: \"ACTIVE\",\n        billingCycle: \"MONTHLY\",\n        amount: 0,\n        startDate: new Date().toISOString().split(\"T\")[0],\n        endDate: \"\",\n        trialEndDate: \"\",\n        stripeSubscriptionId: \"\",\n        stripeCustomerId: \"\"\n    });\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                lineNumber: 151,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n            lineNumber: 150,\n            columnNumber: 7\n        }, this);\n    }\n    if (status === \"unauthenticated\") {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.redirect)(\"/auth/signin\");\n    }\n    if ((session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.role) !== \"SUPER_ADMIN\") {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.redirect)(\"/dashboard\");\n    }\n    const fetchSubscriptions = async ()=>{\n        try {\n            setLoading(true);\n            const params = new URLSearchParams({\n                page: page.toString(),\n                limit: \"20\",\n                ...searchTerm && {\n                    search: searchTerm\n                },\n                ...statusFilter && statusFilter !== \"all\" && {\n                    status: statusFilter\n                },\n                ...planFilter && planFilter !== \"all\" && {\n                    plan: planFilter\n                }\n            });\n            const response = await fetch(\"/api/super-admin/subscriptions?\".concat(params));\n            if (!response.ok) throw new Error(\"Failed to fetch subscriptions\");\n            const data = await response.json();\n            setSubscriptions(data.subscriptions);\n            setStats(data.stats);\n            setTotalPages(data.pagination.pages);\n        } catch (error) {\n            console.error(\"Error fetching subscriptions:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchCompanies = async ()=>{\n        try {\n            const response = await fetch(\"/api/super-admin/companies?limit=1000\");\n            const data = await response.json();\n            if (data.companies) {\n                setCompanies(data.companies);\n            }\n        } catch (error) {\n            console.error(\"Error fetching companies:\", error);\n        }\n    };\n    const fetchPricingPlans = async ()=>{\n        try {\n            const response = await fetch(\"/api/pricing-plans\");\n            const data = await response.json();\n            if (data.success) {\n                setPricingPlans(data.data);\n            }\n        } catch (error) {\n            console.error(\"Error fetching pricing plans:\", error);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchSubscriptions();\n    }, [\n        page,\n        searchTerm,\n        statusFilter,\n        planFilter\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (showCreateDialog) {\n            fetchCompanies();\n            fetchPricingPlans();\n        }\n    }, [\n        showCreateDialog\n    ]);\n    const getStatusBadge = (status)=>{\n        const variants = {\n            ACTIVE: {\n                variant: \"default\",\n                icon: _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                color: \"text-green-600\"\n            },\n            TRIAL: {\n                variant: \"secondary\",\n                icon: _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                color: \"text-blue-600\"\n            },\n            CANCELLED: {\n                variant: \"destructive\",\n                icon: _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                color: \"text-red-600\"\n            },\n            EXPIRED: {\n                variant: \"outline\",\n                icon: _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                color: \"text-orange-600\"\n            },\n            SUSPENDED: {\n                variant: \"destructive\",\n                icon: _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                color: \"text-red-600\"\n            }\n        };\n        const config = variants[status] || variants.CANCELLED;\n        const Icon = config.icon;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n            variant: config.variant,\n            className: \"flex items-center space-x-1\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                    className: \"h-3 w-3\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                    lineNumber: 238,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: status\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n            lineNumber: 237,\n            columnNumber: 7\n        }, this);\n    };\n    const getPlanBadge = (plan)=>{\n        const colors = {\n            BASIC: \"bg-gray-100 text-gray-800\",\n            PROFESSIONAL: \"bg-blue-100 text-blue-800\",\n            PREMIUM: \"bg-purple-100 text-purple-800\",\n            ENTERPRISE: \"bg-orange-100 text-orange-800\"\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(colors[plan] || \"bg-gray-100 text-gray-800\"),\n            children: plan\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n            lineNumber: 253,\n            columnNumber: 7\n        }, this);\n    };\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\",\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 0\n        }).format(amount);\n    };\n    const formatDate = (date)=>{\n        if (!date) return \"N/A\";\n        return new Date(date).toLocaleDateString(\"en-US\", {\n            year: \"numeric\",\n            month: \"short\",\n            day: \"numeric\"\n        });\n    };\n    const handleCreateSubscription = async ()=>{\n        try {\n            const selectedPlan = pricingPlans.find((p)=>p.id === formData.planId);\n            if (!selectedPlan) {\n                toast.error(\"Please select a pricing plan\");\n                return;\n            }\n            const amount = formData.billingCycle === \"YEARLY\" ? selectedPlan.yearlyPrice || selectedPlan.monthlyPrice * 12 : selectedPlan.monthlyPrice;\n            const response = await fetch(\"/api/super-admin/subscriptions\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    ...formData,\n                    amount,\n                    plan: selectedPlan.name.toUpperCase(),\n                    startDate: formData.startDate ? new Date(formData.startDate) : new Date(),\n                    endDate: formData.endDate ? new Date(formData.endDate) : undefined,\n                    trialEndDate: formData.trialEndDate ? new Date(formData.trialEndDate) : undefined\n                })\n            });\n            const data = await response.json();\n            if (response.ok) {\n                toast.success(data.message || \"Subscription created successfully\");\n                setShowCreateDialog(false);\n                setFormData({\n                    companyId: \"\",\n                    planId: \"\",\n                    status: \"ACTIVE\",\n                    billingCycle: \"MONTHLY\",\n                    amount: 0,\n                    startDate: new Date().toISOString().split(\"T\")[0],\n                    endDate: \"\",\n                    trialEndDate: \"\",\n                    stripeSubscriptionId: \"\",\n                    stripeCustomerId: \"\"\n                });\n                fetchSubscriptions();\n            } else {\n                toast.error(data.error || \"Failed to create subscription\");\n            }\n        } catch (error) {\n            console.error(\"Error creating subscription:\", error);\n            toast.error(\"Failed to create subscription\");\n        }\n    };\n    const handlePlanChange = (planId)=>{\n        const selectedPlan = pricingPlans.find((p)=>p.id === planId);\n        if (selectedPlan) {\n            const amount = formData.billingCycle === \"YEARLY\" ? selectedPlan.yearlyPrice || selectedPlan.monthlyPrice * 12 : selectedPlan.monthlyPrice;\n            setFormData((prev)=>({\n                    ...prev,\n                    planId,\n                    amount\n                }));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row sm:items-center justify-between gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"Subscription Management\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 mt-1\",\n                                children: \"Manage subscriptions, billing, and revenue analytics\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                        lineNumber: 350,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                variant: \"outline\",\n                                onClick: fetchSubscriptions,\n                                disabled: loading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 \".concat(loading ? \"animate-spin\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                open: showCreateDialog,\n                                onOpenChange: setShowCreateDialog,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTrigger, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Add Subscription\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                        className: \"max-w-2xl\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                        children: \"Create New Subscription\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogDescription, {\n                                                        children: \"Add or modify a company's subscription plan.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 py-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                htmlFor: \"company\",\n                                                                children: \"Company\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                lineNumber: 378,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                                value: formData.companyId,\n                                                                onValueChange: (value)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            companyId: value\n                                                                        })),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                                            placeholder: \"Select company\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                            lineNumber: 381,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                        lineNumber: 380,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                                                        children: companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                                value: company.id,\n                                                                                children: [\n                                                                                    company.name,\n                                                                                    \" (\",\n                                                                                    company.email,\n                                                                                    \")\"\n                                                                                ]\n                                                                            }, company.id, true, {\n                                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                                lineNumber: 385,\n                                                                                columnNumber: 25\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                        lineNumber: 383,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                htmlFor: \"plan\",\n                                                                children: \"Pricing Plan\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                lineNumber: 394,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                                value: formData.planId,\n                                                                onValueChange: handlePlanChange,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                                            placeholder: \"Select plan\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                            lineNumber: 397,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                        lineNumber: 396,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                                                        children: pricingPlans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                                value: plan.id,\n                                                                                children: [\n                                                                                    plan.name,\n                                                                                    \" - $\",\n                                                                                    plan.monthlyPrice,\n                                                                                    \"/month\"\n                                                                                ]\n                                                                            }, plan.id, true, {\n                                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                                lineNumber: 401,\n                                                                                columnNumber: 25\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                        lineNumber: 399,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                lineNumber: 395,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                htmlFor: \"status\",\n                                                                children: \"Status\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                lineNumber: 410,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                                value: formData.status,\n                                                                onValueChange: (value)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            status: value\n                                                                        })),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {}, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                            lineNumber: 413,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                        lineNumber: 412,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                                value: \"ACTIVE\",\n                                                                                children: \"Active\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                                lineNumber: 416,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                                value: \"TRIALING\",\n                                                                                children: \"Trial\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                                lineNumber: 417,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                                value: \"CANCELED\",\n                                                                                children: \"Canceled\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                                lineNumber: 418,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                                value: \"EXPIRED\",\n                                                                                children: \"Expired\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                                lineNumber: 419,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                                value: \"SUSPENDED\",\n                                                                                children: \"Suspended\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                                lineNumber: 420,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                        lineNumber: 415,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                lineNumber: 411,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                htmlFor: \"billingCycle\",\n                                                                children: \"Billing Cycle\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                lineNumber: 426,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                                value: formData.billingCycle,\n                                                                onValueChange: (value)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            billingCycle: value\n                                                                        })),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {}, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                            lineNumber: 429,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                        lineNumber: 428,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                                value: \"MONTHLY\",\n                                                                                children: \"Monthly\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                                lineNumber: 432,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                                value: \"YEARLY\",\n                                                                                children: \"Yearly\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                                lineNumber: 433,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                        lineNumber: 431,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                lineNumber: 427,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                htmlFor: \"startDate\",\n                                                                children: \"Start Date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                lineNumber: 439,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                id: \"startDate\",\n                                                                type: \"date\",\n                                                                value: formData.startDate,\n                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            startDate: e.target.value\n                                                                        }))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                lineNumber: 440,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                htmlFor: \"endDate\",\n                                                                children: \"End Date (Optional)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                lineNumber: 449,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                id: \"endDate\",\n                                                                type: \"date\",\n                                                                value: formData.endDate,\n                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            endDate: e.target.value\n                                                                        }))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                lineNumber: 450,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                htmlFor: \"trialEndDate\",\n                                                                children: \"Trial End Date (Optional)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                lineNumber: 459,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                id: \"trialEndDate\",\n                                                                type: \"date\",\n                                                                value: formData.trialEndDate,\n                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            trialEndDate: e.target.value\n                                                                        }))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                lineNumber: 460,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                htmlFor: \"amount\",\n                                                                children: \"Amount\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                lineNumber: 469,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                id: \"amount\",\n                                                                type: \"number\",\n                                                                value: formData.amount,\n                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            amount: parseFloat(e.target.value) || 0\n                                                                        })),\n                                                                placeholder: \"0.00\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                lineNumber: 470,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                        lineNumber: 468,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 md:col-span-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                htmlFor: \"stripeSubscriptionId\",\n                                                                children: \"Stripe Subscription ID (Optional)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                lineNumber: 480,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                id: \"stripeSubscriptionId\",\n                                                                value: formData.stripeSubscriptionId,\n                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            stripeSubscriptionId: e.target.value\n                                                                        })),\n                                                                placeholder: \"sub_...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                lineNumber: 481,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 md:col-span-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                                htmlFor: \"stripeCustomerId\",\n                                                                children: \"Stripe Customer ID (Optional)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                lineNumber: 490,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                id: \"stripeCustomerId\",\n                                                                value: formData.stripeCustomerId,\n                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            stripeCustomerId: e.target.value\n                                                                        })),\n                                                                placeholder: \"cus_...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                lineNumber: 491,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                        lineNumber: 489,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogFooter, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: ()=>setShowCreateDialog(false),\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                        lineNumber: 500,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        onClick: handleCreateSubscription,\n                                                        disabled: !formData.companyId || !formData.planId,\n                                                        children: \"Create Subscription\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                        lineNumber: 503,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                lineNumber: 499,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                lineNumber: 349,\n                columnNumber: 7\n            }, this),\n            stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"hover:shadow-lg transition-shadow duration-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-500\",\n                                                    children: \"Monthly Recurring Revenue\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 519,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-green-600\",\n                                                    children: formatCurrency(stats.revenue.mrr)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 520,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                            lineNumber: 518,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-8 w-8 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                            lineNumber: 524,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                    lineNumber: 517,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500\",\n                                                children: \"Growth Rate:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                lineNumber: 528,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-green-600\",\n                                                children: [\n                                                    \"+\",\n                                                    stats.metrics.growthRate.toFixed(1),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                lineNumber: 529,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                        lineNumber: 527,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                    lineNumber: 526,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                            lineNumber: 516,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                        lineNumber: 515,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"hover:shadow-lg transition-shadow duration-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-500\",\n                                                    children: \"Annual Recurring Revenue\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 541,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-blue-600\",\n                                                    children: formatCurrency(stats.revenue.arr)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 542,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"h-8 w-8 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                            lineNumber: 546,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                    lineNumber: 539,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500\",\n                                                children: \"Avg/Customer:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                lineNumber: 550,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-blue-600\",\n                                                children: formatCurrency(stats.revenue.average)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                lineNumber: 551,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                        lineNumber: 549,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                    lineNumber: 548,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                            lineNumber: 538,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                        lineNumber: 537,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"hover:shadow-lg transition-shadow duration-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-500\",\n                                                    children: \"Active Subscriptions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 563,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-purple-600\",\n                                                    children: stats.revenue.activeSubscriptions\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 564,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                            lineNumber: 562,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"h-8 w-8 text-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                            lineNumber: 566,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                    lineNumber: 561,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500\",\n                                                children: \"New (30d):\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                lineNumber: 570,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-purple-600\",\n                                                children: [\n                                                    \"+\",\n                                                    stats.metrics.newSubscriptions\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                lineNumber: 571,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                        lineNumber: 569,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                    lineNumber: 568,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                            lineNumber: 560,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                        lineNumber: 559,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"hover:shadow-lg transition-shadow duration-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-500\",\n                                                    children: \"Churn Rate\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 581,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-red-600\",\n                                                    children: [\n                                                        stats.metrics.churnRate.toFixed(1),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 582,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                            lineNumber: 580,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"h-8 w-8 text-red-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                            lineNumber: 586,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                    lineNumber: 579,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500\",\n                                                children: \"Churned (30d):\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                lineNumber: 590,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-red-600\",\n                                                children: stats.metrics.churnCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                lineNumber: 591,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                        lineNumber: 589,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                    lineNumber: 588,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                            lineNumber: 578,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                        lineNumber: 577,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                lineNumber: 514,\n                columnNumber: 9\n            }, this),\n            stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                    children: \"Subscription Status\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                    lineNumber: 604,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                lineNumber: 603,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: stats.byStatus.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-3 h-3 rounded-full \".concat(item.status === \"ACTIVE\" ? \"bg-green-500\" : item.status === \"TRIAL\" ? \"bg-blue-500\" : item.status === \"CANCELLED\" ? \"bg-red-500\" : item.status === \"EXPIRED\" ? \"bg-orange-500\" : \"bg-gray-500\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                            lineNumber: 611,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: item.status\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                            lineNumber: 617,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 610,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: item.count\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                            lineNumber: 620,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-400\",\n                                                            children: [\n                                                                \"(\",\n                                                                (item.count / stats.total * 100).toFixed(1),\n                                                                \"%)\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                            lineNumber: 621,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 619,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, item.status, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                            lineNumber: 609,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                    lineNumber: 607,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                lineNumber: 606,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                        lineNumber: 602,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                    children: \"Plan Distribution\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                    lineNumber: 633,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                lineNumber: 632,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: stats.byPlan.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-3 h-3 rounded-full \".concat(index === 0 ? \"bg-purple-500\" : index === 1 ? \"bg-blue-500\" : index === 2 ? \"bg-green-500\" : \"bg-orange-500\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                            lineNumber: 640,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: item.plan\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                            lineNumber: 645,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 639,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: item.count\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                            lineNumber: 648,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-400\",\n                                                            children: [\n                                                                \"(\",\n                                                                (item.count / stats.total * 100).toFixed(1),\n                                                                \"%)\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                            lineNumber: 649,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 647,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, item.plan, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                            lineNumber: 638,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                    lineNumber: 636,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                lineNumber: 635,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                        lineNumber: 631,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                lineNumber: 601,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    className: \"p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                            lineNumber: 667,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                            placeholder: \"Search companies...\",\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            className: \"pl-10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                            lineNumber: 668,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                    lineNumber: 666,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                lineNumber: 665,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                value: statusFilter,\n                                onValueChange: setStatusFilter,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                            placeholder: \"All Statuses\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                            lineNumber: 678,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                        lineNumber: 677,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                value: \"all\",\n                                                children: \"All Statuses\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                lineNumber: 681,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                value: \"ACTIVE\",\n                                                children: \"Active\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                lineNumber: 682,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                value: \"TRIAL\",\n                                                children: \"Trial\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                lineNumber: 683,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                value: \"CANCELLED\",\n                                                children: \"Cancelled\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                lineNumber: 684,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                value: \"EXPIRED\",\n                                                children: \"Expired\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                lineNumber: 685,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                value: \"SUSPENDED\",\n                                                children: \"Suspended\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                lineNumber: 686,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                        lineNumber: 680,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                lineNumber: 676,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                value: planFilter,\n                                onValueChange: setPlanFilter,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                            placeholder: \"All Plans\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                            lineNumber: 691,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                        lineNumber: 690,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                value: \"all\",\n                                                children: \"All Plans\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                lineNumber: 694,\n                                                columnNumber: 17\n                                            }, this),\n                                            stats === null || stats === void 0 ? void 0 : stats.byPlan.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: item.plan,\n                                                    children: [\n                                                        item.plan,\n                                                        \" (\",\n                                                        item.count,\n                                                        \")\"\n                                                    ]\n                                                }, item.plan, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 696,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                        lineNumber: 693,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                lineNumber: 689,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                variant: \"outline\",\n                                className: \"w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                        lineNumber: 703,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"More Filters\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                lineNumber: 702,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                        lineNumber: 664,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                    lineNumber: 663,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                lineNumber: 662,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                            children: [\n                                \"Subscriptions (\",\n                                subscriptions.length,\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                            lineNumber: 713,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                        lineNumber: 712,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center py-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                lineNumber: 718,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                            lineNumber: 717,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.Table, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableRow, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                    children: \"Company\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 725,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                    children: \"Plan\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 726,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 727,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                    children: \"Amount\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 728,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                    children: \"Billing\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 729,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                    children: \"Start Date\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 730,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                    children: \"Usage\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 731,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                    children: \"Actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 732,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                            lineNumber: 724,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                        lineNumber: 723,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableBody, {\n                                        children: subscriptions.map((subscription)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableRow, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold\",\n                                                                    children: subscription.company.name.charAt(0).toUpperCase()\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                    lineNumber: 740,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium text-gray-900\",\n                                                                            children: subscription.company.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                            lineNumber: 744,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2 text-sm text-gray-500\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: subscription.company.industry\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                                    lineNumber: 746,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                subscription.company.size && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: \"•\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                                            lineNumber: 749,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: subscription.company.size\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                                            lineNumber: 750,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                            lineNumber: 745,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                    lineNumber: 743,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                            lineNumber: 739,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                        lineNumber: 738,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: getPlanBadge(subscription.plan)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                        lineNumber: 757,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: getStatusBadge(subscription.status)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                        lineNumber: 758,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: formatCurrency(subscription.amount)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                    lineNumber: 761,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: [\n                                                                        \"per \",\n                                                                        subscription.billingCycle.toLowerCase()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                    lineNumber: 762,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                            lineNumber: 760,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                        lineNumber: 759,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                            variant: \"outline\",\n                                                            children: subscription.billingCycle\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                            lineNumber: 768,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                        lineNumber: 767,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: formatDate(subscription.startDate)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                            lineNumber: 773,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                        lineNumber: 772,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        subscription.company.metrics.totalUsers,\n                                                                        \" users\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                    lineNumber: 779,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: [\n                                                                        subscription.company.metrics.totalCustomers,\n                                                                        \" customers\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                    lineNumber: 780,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                            lineNumber: 778,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                        lineNumber: 777,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"sm\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                        lineNumber: 788,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                    lineNumber: 787,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"sm\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                        lineNumber: 791,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                    lineNumber: 790,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"sm\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                        lineNumber: 794,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                    lineNumber: 793,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                            lineNumber: 786,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                        lineNumber: 785,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, subscription.id, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                lineNumber: 737,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                        lineNumber: 735,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                lineNumber: 722,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                            lineNumber: 721,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                        lineNumber: 715,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                lineNumber: 711,\n                columnNumber: 7\n            }, this),\n            totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [\n                            \"Page \",\n                            page,\n                            \" of \",\n                            totalPages\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                        lineNumber: 810,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>setPage(page - 1),\n                                disabled: page === 1,\n                                children: \"Previous\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                lineNumber: 814,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>setPage(page + 1),\n                                disabled: page === totalPages,\n                                children: \"Next\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                lineNumber: 821,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                        lineNumber: 813,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                lineNumber: 809,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n        lineNumber: 347,\n        columnNumber: 5\n    }, this);\n}\n_s(SubscriptionManagementPage, \"uQSQwdXbeNZX3deJLbtPZjsLpNE=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession\n    ];\n});\n_c = SubscriptionManagementPage;\nvar _c;\n$RefreshReg$(_c, \"SubscriptionManagementPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/super-admin/subscriptions/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/ui/label.tsx":
/*!*********************************!*\
  !*** ./components/ui/label.tsx ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: function() { return /* binding */ Label; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-label */ \"(app-pages-browser)/./node_modules/@radix-ui/react-label/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(app-pages-browser)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Label auto */ \n\n\n\n\nconst labelVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\");\nconst Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(labelVariants(), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\ui\\\\label.tsx\",\n        lineNumber: 18,\n        columnNumber: 3\n    }, undefined);\n});\n_c1 = Label;\nLabel.displayName = _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\n\nvar _c, _c1;\n$RefreshReg$(_c, \"Label$React.forwardRef\");\n$RefreshReg$(_c1, \"Label\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvdWkvbGFiZWwudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRThCO0FBQ3lCO0FBQ1U7QUFFakM7QUFFaEMsTUFBTUksZ0JBQWdCRiw2REFBR0EsQ0FDdkI7QUFHRixNQUFNRyxzQkFBUUwsNkNBQWdCLE1BSTVCLFFBQTBCTztRQUF6QixFQUFFQyxTQUFTLEVBQUUsR0FBR0MsT0FBTzt5QkFDeEIsOERBQUNSLHVEQUFtQjtRQUNsQk0sS0FBS0E7UUFDTEMsV0FBV0wsOENBQUVBLENBQUNDLGlCQUFpQkk7UUFDOUIsR0FBR0MsS0FBSzs7Ozs7Ozs7QUFHYkosTUFBTU0sV0FBVyxHQUFHVix1REFBbUIsQ0FBQ1UsV0FBVztBQUVuQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9jb21wb25lbnRzL3VpL2xhYmVsLnRzeD84OGVkIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgKiBhcyBMYWJlbFByaW1pdGl2ZSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LWxhYmVsXCJcbmltcG9ydCB7IGN2YSwgdHlwZSBWYXJpYW50UHJvcHMgfSBmcm9tIFwiY2xhc3MtdmFyaWFuY2UtYXV0aG9yaXR5XCJcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5jb25zdCBsYWJlbFZhcmlhbnRzID0gY3ZhKFxuICBcInRleHQtc20gZm9udC1tZWRpdW0gbGVhZGluZy1ub25lIHBlZXItZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIHBlZXItZGlzYWJsZWQ6b3BhY2l0eS03MFwiXG4pXG5cbmNvbnN0IExhYmVsID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgTGFiZWxQcmltaXRpdmUuUm9vdD4sXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgTGFiZWxQcmltaXRpdmUuUm9vdD4gJlxuICAgIFZhcmlhbnRQcm9wczx0eXBlb2YgbGFiZWxWYXJpYW50cz5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPExhYmVsUHJpbWl0aXZlLlJvb3RcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKGxhYmVsVmFyaWFudHMoKSwgY2xhc3NOYW1lKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKVxuTGFiZWwuZGlzcGxheU5hbWUgPSBMYWJlbFByaW1pdGl2ZS5Sb290LmRpc3BsYXlOYW1lXG5cbmV4cG9ydCB7IExhYmVsIH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkxhYmVsUHJpbWl0aXZlIiwiY3ZhIiwiY24iLCJsYWJlbFZhcmlhbnRzIiwiTGFiZWwiLCJmb3J3YXJkUmVmIiwicmVmIiwiY2xhc3NOYW1lIiwicHJvcHMiLCJSb290IiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/label.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@radix-ui/react-label/dist/index.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/@radix-ui/react-label/dist/index.mjs ***!
  \***********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: function() { return /* binding */ Label; },\n/* harmony export */   Root: function() { return /* binding */ Root; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(app-pages-browser)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n\"use client\";\n\n// src/label.tsx\n\n\n\nvar NAME = \"Label\";\nvar Label = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n    _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.label,\n    {\n      ...props,\n      ref: forwardedRef,\n      onMouseDown: (event) => {\n        const target = event.target;\n        if (target.closest(\"button, input, select, textarea\")) return;\n        props.onMouseDown?.(event);\n        if (!event.defaultPrevented && event.detail > 1) event.preventDefault();\n      }\n    }\n  );\n});\nLabel.displayName = NAME;\nvar Root = Label;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtbGFiZWwvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBQTs7QUFFQTtBQUMrQjtBQUN1QjtBQUNkO0FBQ3hDO0FBQ0EsWUFBWSw2Q0FBZ0I7QUFDNUIseUJBQXlCLHNEQUFHO0FBQzVCLElBQUksZ0VBQVM7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFJRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtbGFiZWwvZGlzdC9pbmRleC5tanM/MDhjYyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuLy8gc3JjL2xhYmVsLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyBQcmltaXRpdmUgfSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LXByaW1pdGl2ZVwiO1xuaW1wb3J0IHsganN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG52YXIgTkFNRSA9IFwiTGFiZWxcIjtcbnZhciBMYWJlbCA9IFJlYWN0LmZvcndhcmRSZWYoKHByb3BzLCBmb3J3YXJkZWRSZWYpID0+IHtcbiAgcmV0dXJuIC8qIEBfX1BVUkVfXyAqLyBqc3goXG4gICAgUHJpbWl0aXZlLmxhYmVsLFxuICAgIHtcbiAgICAgIC4uLnByb3BzLFxuICAgICAgcmVmOiBmb3J3YXJkZWRSZWYsXG4gICAgICBvbk1vdXNlRG93bjogKGV2ZW50KSA9PiB7XG4gICAgICAgIGNvbnN0IHRhcmdldCA9IGV2ZW50LnRhcmdldDtcbiAgICAgICAgaWYgKHRhcmdldC5jbG9zZXN0KFwiYnV0dG9uLCBpbnB1dCwgc2VsZWN0LCB0ZXh0YXJlYVwiKSkgcmV0dXJuO1xuICAgICAgICBwcm9wcy5vbk1vdXNlRG93bj8uKGV2ZW50KTtcbiAgICAgICAgaWYgKCFldmVudC5kZWZhdWx0UHJldmVudGVkICYmIGV2ZW50LmRldGFpbCA+IDEpIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XG4gICAgICB9XG4gICAgfVxuICApO1xufSk7XG5MYWJlbC5kaXNwbGF5TmFtZSA9IE5BTUU7XG52YXIgUm9vdCA9IExhYmVsO1xuZXhwb3J0IHtcbiAgTGFiZWwsXG4gIFJvb3Rcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@radix-ui/react-label/dist/index.mjs\n"));

/***/ })

});