(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4348],{36989:function(e,s,a){Promise.resolve().then(a.bind(a,61606))},61606:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return G}});var t=a(57437),i=a(2265),l=a(82749),c=a(27815),r=a(85754),n=a(31478),d=a(25462),o=a(56950),h=a(45179),x=a(49842),m=a(54900),u=a(42706),j=a(91927);let v=j.fC,f=j.wy,p=j.Fw;var N=a(42482),g=a(92295),y=a(82549),C=a(55340),w=a(45367),b=a(41827),A=a(9224),F=a(83523),k=a(64280),E=a(35817),O=a(5925);let S={search:"",status:[],priority:[],source:[],industry:[],companySize:[],scoreMin:void 0,scoreMax:void 0,temperature:[],createdAfter:"",createdBefore:"",lastActivityAfter:"",lastActivityBefore:"",convertedAfter:"",convertedBefore:"",budgetMin:void 0,budgetMax:void 0,hasBudget:void 0,hasActivities:void 0,activityCountMin:void 0,activityCountMax:void 0,hasRecentActivity:void 0,assignedTo:[],unassigned:void 0,isQualified:void 0,hasPhone:void 0,hasEmail:void 0,hasWebsite:void 0,hasTimeline:void 0,isConverted:void 0,conversionType:[]};function T(e){let{onFilterChange:s,onApplyFilters:a,initialFilters:l={},loading:d=!1}=e,[o,j]=(0,i.useState)({...S,...l}),[T,_]=(0,i.useState)(!1),[M,Z]=(0,i.useState)([]),[L,I]=(0,i.useState)(!1),[z,D]=(0,i.useState)(""),[R,B]=(0,i.useState)(""),[P,W]=(0,i.useState)(!1),[X,H]=(0,i.useState)(!1),[U,V]=(0,i.useState)({basic:!0,scoring:!1,dates:!1,budget:!1,activity:!1,assignment:!1,qualification:!1,conversion:!1});(0,i.useEffect)(()=>{q()},[]);let q=async()=>{try{let e=await fetch("/api/leads/saved-filters?includePublic=true");if(e.ok){let s=await e.json();Z(s.savedFilters)}}catch(e){console.error("Error fetching saved filters:",e)}},G=(e,a)=>{let t={...o,[e]:a};j(t),s(t)},Q=(e,s,a)=>{let t=o[e];G(e,a?[...t,s]:t.filter(e=>e!==s))},K=()=>{let e={...S};j(e),s(e),a(e)},Y=e=>{let t={...S,...e.filters};j(t),s(t),a(t),O.toast.success("Loaded filter: ".concat(e.name))},$=async()=>{if(!z.trim()){O.toast.error("Filter name is required");return}try{if((await fetch("/api/leads/saved-filters",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:z,description:R||void 0,filters:o,isPublic:P,isDefault:X})})).ok)O.toast.success("Filter saved successfully"),I(!1),D(""),B(""),W(!1),H(!1),q();else throw Error("Failed to save filter")}catch(e){O.toast.error("Failed to save filter")}},J=async e=>{if(confirm("Are you sure you want to delete this saved filter?"))try{if((await fetch("/api/leads/saved-filters?id=".concat(e),{method:"DELETE"})).ok)O.toast.success("Filter deleted successfully"),q();else throw Error("Failed to delete filter")}catch(e){O.toast.error("Failed to delete filter")}},ee=e=>{V(s=>({...s,[e]:!s[e]}))},es=()=>{let e=0;return o.search&&e++,o.status.length>0&&e++,o.priority.length>0&&e++,o.source.length>0&&e++,o.industry.length>0&&e++,o.companySize.length>0&&e++,(void 0!==o.scoreMin||void 0!==o.scoreMax)&&e++,o.temperature.length>0&&e++,(o.createdAfter||o.createdBefore)&&e++,(o.lastActivityAfter||o.lastActivityBefore)&&e++,(o.convertedAfter||o.convertedBefore)&&e++,(void 0!==o.budgetMin||void 0!==o.budgetMax)&&e++,void 0!==o.hasBudget&&e++,void 0!==o.hasActivities&&e++,(void 0!==o.activityCountMin||void 0!==o.activityCountMax)&&e++,void 0!==o.hasRecentActivity&&e++,o.assignedTo.length>0&&e++,void 0!==o.unassigned&&e++,void 0!==o.isQualified&&e++,void 0!==o.hasPhone&&e++,void 0!==o.hasEmail&&e++,void 0!==o.hasWebsite&&e++,void 0!==o.hasTimeline&&e++,void 0!==o.isConverted&&e++,o.conversionType.length>0&&e++,e};return(0,t.jsxs)(c.Zb,{children:[(0,t.jsx)(c.Ol,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)(c.ll,{className:"flex items-center",children:[(0,t.jsx)(N.Z,{className:"h-5 w-5 mr-2"}),"Advanced Filters",es()>0&&(0,t.jsxs)(n.C,{variant:"secondary",className:"ml-2",children:[es()," active"]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(r.z,{variant:"outline",size:"sm",onClick:()=>I(!0),children:[(0,t.jsx)(g.Z,{className:"h-4 w-4 mr-2"}),"Save Filter"]}),(0,t.jsxs)(r.z,{variant:"outline",size:"sm",onClick:K,children:[(0,t.jsx)(y.Z,{className:"h-4 w-4 mr-2"}),"Clear All"]})]})]})}),(0,t.jsxs)(c.aY,{className:"space-y-4",children:[M.length>0&&(0,t.jsxs)("div",{children:[(0,t.jsx)(x._,{className:"text-sm font-medium mb-2 block",children:"Saved Filters"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:M.map(e=>(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsxs)(r.z,{variant:"outline",size:"sm",onClick:()=>Y(e),className:"flex items-center space-x-1",children:[e.isDefault&&(0,t.jsx)(C.Z,{className:"h-3 w-3 text-yellow-500"}),(0,t.jsx)("span",{children:e.name})]}),(0,t.jsx)(r.z,{variant:"ghost",size:"sm",onClick:()=>J(e.id),className:"h-8 w-8 p-0 text-red-600 hover:text-red-700",children:(0,t.jsx)(w.Z,{className:"h-3 w-3"})})]},e.id))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(x._,{htmlFor:"search",children:"Quick Search"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(b.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(h.I,{id:"search",placeholder:"Search leads by name, email, company...",value:o.search,onChange:e=>G("search",e.target.value),className:"pl-10"})]})]}),(0,t.jsxs)(v,{open:U.basic,onOpenChange:()=>ee("basic"),children:[(0,t.jsx)(f,{asChild:!0,children:(0,t.jsxs)(r.z,{variant:"ghost",className:"w-full justify-between p-0 h-auto",children:[(0,t.jsx)("span",{className:"font-medium",children:"Basic Filters"}),U.basic?(0,t.jsx)(A.Z,{className:"h-4 w-4"}):(0,t.jsx)(F.Z,{className:"h-4 w-4"})]})}),(0,t.jsxs)(p,{className:"space-y-4 mt-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(x._,{children:"Status"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:["NEW","CONTACTED","QUALIFIED","PROPOSAL","NEGOTIATION","CONVERTED","LOST"].map(e=>(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(m.X,{id:"status-".concat(e),checked:o.status.includes(e),onCheckedChange:s=>Q("status",e,!!s)}),(0,t.jsx)(x._,{htmlFor:"status-".concat(e),className:"text-sm",children:e})]},e))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(x._,{children:"Priority"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:["LOW","MEDIUM","HIGH","URGENT"].map(e=>(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(m.X,{id:"priority-".concat(e),checked:o.priority.includes(e),onCheckedChange:s=>Q("priority",e,!!s)}),(0,t.jsx)(x._,{htmlFor:"priority-".concat(e),className:"text-sm",children:e})]},e))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(x._,{children:"Lead Source"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:["WEBSITE","REFERRAL","SOCIAL_MEDIA","EMAIL_CAMPAIGN","COLD_CALL","TRADE_SHOW","PARTNER","OTHER"].map(e=>(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(m.X,{id:"source-".concat(e),checked:o.source.includes(e),onCheckedChange:s=>Q("source",e,!!s)}),(0,t.jsx)(x._,{htmlFor:"source-".concat(e),className:"text-sm",children:e.replace("_"," ")})]},e))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(x._,{children:"Industry"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:["TECHNOLOGY","FINANCE","HEALTHCARE","MANUFACTURING","RETAIL","EDUCATION","OTHER"].map(e=>(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(m.X,{id:"industry-".concat(e),checked:o.industry.includes(e),onCheckedChange:s=>Q("industry",e,!!s)}),(0,t.jsx)(x._,{htmlFor:"industry-".concat(e),className:"text-sm",children:e})]},e))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(x._,{children:"Company Size"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:["STARTUP","SMALL","MEDIUM","LARGE","ENTERPRISE"].map(e=>(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(m.X,{id:"size-".concat(e),checked:o.companySize.includes(e),onCheckedChange:s=>Q("companySize",e,!!s)}),(0,t.jsx)(x._,{htmlFor:"size-".concat(e),className:"text-sm",children:e})]},e))})]})]})]}),(0,t.jsxs)(v,{open:U.scoring,onOpenChange:()=>ee("scoring"),children:[(0,t.jsx)(f,{asChild:!0,children:(0,t.jsxs)(r.z,{variant:"ghost",className:"w-full justify-between p-0 h-auto",children:[(0,t.jsx)("span",{className:"font-medium",children:"Scoring & Temperature"}),U.scoring?(0,t.jsx)(A.Z,{className:"h-4 w-4"}):(0,t.jsx)(F.Z,{className:"h-4 w-4"})]})}),(0,t.jsxs)(p,{className:"space-y-4 mt-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(x._,{htmlFor:"scoreMin",children:"Min Score"}),(0,t.jsx)(h.I,{id:"scoreMin",type:"number",min:"0",max:"100",placeholder:"0",value:o.scoreMin||"",onChange:e=>G("scoreMin",e.target.value?parseInt(e.target.value):void 0)})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(x._,{htmlFor:"scoreMax",children:"Max Score"}),(0,t.jsx)(h.I,{id:"scoreMax",type:"number",min:"0",max:"100",placeholder:"100",value:o.scoreMax||"",onChange:e=>G("scoreMax",e.target.value?parseInt(e.target.value):void 0)})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(x._,{children:"Lead Temperature"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:["HOT","WARM","COLD"].map(e=>(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(m.X,{id:"temp-".concat(e),checked:o.temperature.includes(e),onCheckedChange:s=>Q("temperature",e,!!s)}),(0,t.jsx)(x._,{htmlFor:"temp-".concat(e),className:"text-sm",children:e})]},e))})]})]})]}),(0,t.jsxs)(v,{open:U.dates,onOpenChange:()=>ee("dates"),children:[(0,t.jsx)(f,{asChild:!0,children:(0,t.jsxs)(r.z,{variant:"ghost",className:"w-full justify-between p-0 h-auto",children:[(0,t.jsx)("span",{className:"font-medium",children:"Date Filters"}),U.dates?(0,t.jsx)(A.Z,{className:"h-4 w-4"}):(0,t.jsx)(F.Z,{className:"h-4 w-4"})]})}),(0,t.jsxs)(p,{className:"space-y-4 mt-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(x._,{children:"Created Date Range"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 mt-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(x._,{htmlFor:"createdAfter",className:"text-sm",children:"From"}),(0,t.jsx)(h.I,{id:"createdAfter",type:"date",value:o.createdAfter,onChange:e=>G("createdAfter",e.target.value)})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(x._,{htmlFor:"createdBefore",className:"text-sm",children:"To"}),(0,t.jsx)(h.I,{id:"createdBefore",type:"date",value:o.createdBefore,onChange:e=>G("createdBefore",e.target.value)})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(x._,{children:"Last Activity Date Range"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 mt-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(x._,{htmlFor:"lastActivityAfter",className:"text-sm",children:"From"}),(0,t.jsx)(h.I,{id:"lastActivityAfter",type:"date",value:o.lastActivityAfter,onChange:e=>G("lastActivityAfter",e.target.value)})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(x._,{htmlFor:"lastActivityBefore",className:"text-sm",children:"To"}),(0,t.jsx)(h.I,{id:"lastActivityBefore",type:"date",value:o.lastActivityBefore,onChange:e=>G("lastActivityBefore",e.target.value)})]})]})]})]})]}),(0,t.jsxs)(v,{open:U.budget,onOpenChange:()=>ee("budget"),children:[(0,t.jsx)(f,{asChild:!0,children:(0,t.jsxs)(r.z,{variant:"ghost",className:"w-full justify-between p-0 h-auto",children:[(0,t.jsx)("span",{className:"font-medium",children:"Budget Filters"}),U.budget?(0,t.jsx)(A.Z,{className:"h-4 w-4"}):(0,t.jsx)(F.Z,{className:"h-4 w-4"})]})}),(0,t.jsxs)(p,{className:"space-y-4 mt-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(x._,{htmlFor:"budgetMin",children:"Min Budget ($)"}),(0,t.jsx)(h.I,{id:"budgetMin",type:"number",min:"0",placeholder:"0",value:o.budgetMin||"",onChange:e=>G("budgetMin",e.target.value?parseFloat(e.target.value):void 0)})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(x._,{htmlFor:"budgetMax",children:"Max Budget ($)"}),(0,t.jsx)(h.I,{id:"budgetMax",type:"number",min:"0",placeholder:"No limit",value:o.budgetMax||"",onChange:e=>G("budgetMax",e.target.value?parseFloat(e.target.value):void 0)})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(x._,{children:"Budget Status"}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 mt-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(m.X,{id:"hasBudget",checked:!0===o.hasBudget,onCheckedChange:e=>G("hasBudget",!!e||void 0)}),(0,t.jsx)(x._,{htmlFor:"hasBudget",className:"text-sm",children:"Has Budget"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(m.X,{id:"noBudget",checked:!1===o.hasBudget,onCheckedChange:e=>G("hasBudget",!e&&void 0)}),(0,t.jsx)(x._,{htmlFor:"noBudget",className:"text-sm",children:"No Budget"})]})]})]})]})]}),(0,t.jsxs)(v,{open:U.activity,onOpenChange:()=>ee("activity"),children:[(0,t.jsx)(f,{asChild:!0,children:(0,t.jsxs)(r.z,{variant:"ghost",className:"w-full justify-between p-0 h-auto",children:[(0,t.jsx)("span",{className:"font-medium",children:"Activity Filters"}),U.activity?(0,t.jsx)(A.Z,{className:"h-4 w-4"}):(0,t.jsx)(F.Z,{className:"h-4 w-4"})]})}),(0,t.jsxs)(p,{className:"space-y-4 mt-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(x._,{htmlFor:"activityCountMin",children:"Min Activities"}),(0,t.jsx)(h.I,{id:"activityCountMin",type:"number",min:"0",placeholder:"0",value:o.activityCountMin||"",onChange:e=>G("activityCountMin",e.target.value?parseInt(e.target.value):void 0)})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(x._,{htmlFor:"activityCountMax",children:"Max Activities"}),(0,t.jsx)(h.I,{id:"activityCountMax",type:"number",min:"0",placeholder:"No limit",value:o.activityCountMax||"",onChange:e=>G("activityCountMax",e.target.value?parseInt(e.target.value):void 0)})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(x._,{children:"Activity Status"}),(0,t.jsxs)("div",{className:"flex flex-wrap gap-4 mt-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(m.X,{id:"hasActivities",checked:!0===o.hasActivities,onCheckedChange:e=>G("hasActivities",!!e||void 0)}),(0,t.jsx)(x._,{htmlFor:"hasActivities",className:"text-sm",children:"Has Activities"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(m.X,{id:"noActivities",checked:!1===o.hasActivities,onCheckedChange:e=>G("hasActivities",!e&&void 0)}),(0,t.jsx)(x._,{htmlFor:"noActivities",className:"text-sm",children:"No Activities"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(m.X,{id:"hasRecentActivity",checked:!0===o.hasRecentActivity,onCheckedChange:e=>G("hasRecentActivity",!!e||void 0)}),(0,t.jsx)(x._,{htmlFor:"hasRecentActivity",className:"text-sm",children:"Recent Activity (7 days)"})]})]})]})]})]}),(0,t.jsxs)(v,{open:U.qualification,onOpenChange:()=>ee("qualification"),children:[(0,t.jsx)(f,{asChild:!0,children:(0,t.jsxs)(r.z,{variant:"ghost",className:"w-full justify-between p-0 h-auto",children:[(0,t.jsx)("span",{className:"font-medium",children:"Qualification Filters"}),U.qualification?(0,t.jsx)(A.Z,{className:"h-4 w-4"}):(0,t.jsx)(F.Z,{className:"h-4 w-4"})]})}),(0,t.jsx)(p,{className:"space-y-4 mt-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(m.X,{id:"hasPhone",checked:!0===o.hasPhone,onCheckedChange:e=>G("hasPhone",!!e||void 0)}),(0,t.jsx)(x._,{htmlFor:"hasPhone",className:"text-sm",children:"Has Phone"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(m.X,{id:"hasEmail",checked:!0===o.hasEmail,onCheckedChange:e=>G("hasEmail",!!e||void 0)}),(0,t.jsx)(x._,{htmlFor:"hasEmail",className:"text-sm",children:"Has Email"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(m.X,{id:"hasWebsite",checked:!0===o.hasWebsite,onCheckedChange:e=>G("hasWebsite",!!e||void 0)}),(0,t.jsx)(x._,{htmlFor:"hasWebsite",className:"text-sm",children:"Has Website"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(m.X,{id:"hasTimeline",checked:!0===o.hasTimeline,onCheckedChange:e=>G("hasTimeline",!!e||void 0)}),(0,t.jsx)(x._,{htmlFor:"hasTimeline",className:"text-sm",children:"Has Timeline"})]})]})})]}),(0,t.jsxs)(v,{open:U.conversion,onOpenChange:()=>ee("conversion"),children:[(0,t.jsx)(f,{asChild:!0,children:(0,t.jsxs)(r.z,{variant:"ghost",className:"w-full justify-between p-0 h-auto",children:[(0,t.jsx)("span",{className:"font-medium",children:"Conversion Filters"}),U.conversion?(0,t.jsx)(A.Z,{className:"h-4 w-4"}):(0,t.jsx)(F.Z,{className:"h-4 w-4"})]})}),(0,t.jsxs)(p,{className:"space-y-4 mt-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(x._,{children:"Conversion Status"}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 mt-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(m.X,{id:"isConverted",checked:!0===o.isConverted,onCheckedChange:e=>G("isConverted",!!e||void 0)}),(0,t.jsx)(x._,{htmlFor:"isConverted",className:"text-sm",children:"Converted"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(m.X,{id:"notConverted",checked:!1===o.isConverted,onCheckedChange:e=>G("isConverted",!e&&void 0)}),(0,t.jsx)(x._,{htmlFor:"notConverted",className:"text-sm",children:"Not Converted"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(x._,{children:"Conversion Type"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:["DIRECT","QUOTATION","PROPOSAL","TRIAL","DEMO"].map(e=>(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(m.X,{id:"conversion-".concat(e),checked:o.conversionType.includes(e),onCheckedChange:s=>Q("conversionType",e,!!s)}),(0,t.jsx)(x._,{htmlFor:"conversion-".concat(e),className:"text-sm",children:e})]},e))})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(r.z,{onClick:()=>{a(o)},disabled:d,children:[d?(0,t.jsx)(k.Z,{className:"h-4 w-4 mr-2 animate-spin"}):(0,t.jsx)(N.Z,{className:"h-4 w-4 mr-2"}),"Apply Filters"]}),(0,t.jsxs)(r.z,{variant:"outline",onClick:K,children:[(0,t.jsx)(y.Z,{className:"h-4 w-4 mr-2"}),"Clear All"]})]}),(0,t.jsx)("div",{className:"flex items-center space-x-2",children:(0,t.jsxs)(r.z,{variant:"outline",size:"sm",children:[(0,t.jsx)(E.Z,{className:"h-4 w-4 mr-2"}),"Export"]})})]})]}),(0,t.jsx)(u.Vq,{open:L,onOpenChange:I,children:(0,t.jsxs)(u.cZ,{children:[(0,t.jsx)(u.fK,{children:(0,t.jsx)(u.$N,{children:"Save Filter"})}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(x._,{htmlFor:"filterName",children:"Filter Name *"}),(0,t.jsx)(h.I,{id:"filterName",value:z,onChange:e=>D(e.target.value),placeholder:"Enter filter name"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(x._,{htmlFor:"filterDescription",children:"Description"}),(0,t.jsx)(h.I,{id:"filterDescription",value:R,onChange:e=>B(e.target.value),placeholder:"Optional description"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(m.X,{id:"saveAsPublic",checked:P,onCheckedChange:e=>W(!!e)}),(0,t.jsx)(x._,{htmlFor:"saveAsPublic",className:"text-sm",children:"Make this filter available to all team members"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(m.X,{id:"saveAsDefault",checked:X,onCheckedChange:e=>H(!!e)}),(0,t.jsx)(x._,{htmlFor:"saveAsDefault",className:"text-sm",children:"Set as my default filter"})]})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,t.jsx)(r.z,{variant:"outline",onClick:()=>I(!1),children:"Cancel"}),(0,t.jsx)(r.z,{onClick:$,children:"Save Filter"})]})]})]})})]})}var _=a(85790),M=a(1295),Z=a(12741),L=a(52431),I=a(41298),z=a(17472),D=a(99670),R=a(49617),B=a(66654),P=a(9883),W=a(74527),X=a(6141),H=a(93930),U=a(61396),V=a.n(U),q=a(89673);function G(){let{data:e}=(0,l.useSession)(),[s,a]=(0,i.useState)([]),[h,x]=(0,i.useState)([]),[m,u]=(0,i.useState)(!0),[j,v]=(0,i.useState)(!1),[f,p]=(0,i.useState)(!1),[g,y]=(0,i.useState)(null),[C,b]=(0,i.useState)(!1),[A,F]=(0,i.useState)({}),[k,E]=(0,i.useState)({total:0,new:0,qualified:0,closedWon:0,totalValue:0}),S=async()=>{try{let e=await fetch("/api/leads");if(!e.ok)throw Error("Failed to fetch leads");let s=await e.json();a(s.leads),x(s.leads);let t=s.leads.length,i=s.leads.filter(e=>"NEW"===e.status).length,l=s.leads.filter(e=>"QUALIFIED"===e.status).length,c=s.leads.filter(e=>"CLOSED_WON"===e.status).length,r=s.leads.reduce((e,s)=>e+(s.budget||0),0);E({total:t,new:i,qualified:l,closedWon:c,totalValue:r})}catch(e){O.toast.error("Failed to load leads"),console.error("Error fetching leads:",e)}finally{u(!1)}},U=async e=>{try{v(!0);let s=await fetch("/api/leads/filter",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok)throw Error("Failed to apply filters");let a=await s.json();x(a.leads),O.toast.success("Found ".concat(a.leads.length," leads matching your criteria"))}catch(e){O.toast.error("Failed to apply filters"),console.error("Error applying filters:",e)}finally{v(!1)}};(0,i.useEffect)(()=>{S()},[]);let G=async e=>{if(confirm('Are you sure you want to delete "'.concat(e.firstName," ").concat(e.lastName,'"?')))try{let s=await fetch("/api/leads/".concat(e.id),{method:"DELETE"});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to delete lead")}O.toast.success("Lead deleted successfully"),S()}catch(e){O.toast.error(e instanceof Error?e.message:"Failed to delete lead")}},Q=e=>{y(e),p(!0)},K=e=>{if(!e)return(0,t.jsx)(n.C,{variant:"secondary",children:"Unknown"});switch(e){case"NEW":return(0,t.jsx)(n.C,{variant:"secondary",children:"New"});case"CONTACTED":return(0,t.jsx)(n.C,{variant:"info",children:"Contacted"});case"QUALIFIED":return(0,t.jsx)(n.C,{variant:"success",children:"Qualified"});case"PROPOSAL":return(0,t.jsx)(n.C,{variant:"warning",children:"Proposal"});case"NEGOTIATION":return(0,t.jsx)(n.C,{variant:"warning",children:"Negotiation"});case"CLOSED_WON":return(0,t.jsx)(n.C,{variant:"success",children:"Closed Won"});case"CLOSED_LOST":return(0,t.jsx)(n.C,{variant:"destructive",children:"Closed Lost"});default:return(0,t.jsx)(n.C,{variant:"secondary",children:e})}},Y=e=>{if(!e)return(0,t.jsx)(n.C,{variant:"secondary",className:"text-xs",children:"Unknown"});switch(e){case"LOW":return(0,t.jsx)(n.C,{variant:"secondary",className:"text-xs",children:"Low"});case"MEDIUM":return(0,t.jsx)(n.C,{variant:"info",className:"text-xs",children:"Medium"});case"HIGH":return(0,t.jsx)(n.C,{variant:"warning",className:"text-xs",children:"High"});case"URGENT":return(0,t.jsx)(n.C,{variant:"destructive",className:"text-xs",children:"Urgent"});default:return(0,t.jsx)(n.C,{variant:"secondary",className:"text-xs",children:e})}},$=[{accessorKey:"firstName",header:"Lead",cell:e=>{let{row:s}=e,a=s.original;return(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(_.Z,{className:"h-4 w-4 text-green-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"font-medium",children:[a.firstName," ",a.lastName]}),a.companyName&&(0,t.jsx)("div",{className:"text-sm text-gray-500",children:a.companyName}),a.title&&(0,t.jsx)("div",{className:"text-xs text-gray-400",children:a.title})]})]})}},{accessorKey:"email",header:"Contact",cell:e=>{let{row:s}=e,a=s.original;return(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,t.jsx)(M.Z,{className:"h-3 w-3 text-gray-400"}),(0,t.jsx)("span",{children:a.email})]}),a.phone&&(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,t.jsx)(Z.Z,{className:"h-3 w-3 text-gray-400"}),(0,t.jsx)("span",{children:a.phone})]})]})}},{accessorKey:"status",header:"Status",cell:e=>{let{row:s}=e,a=s.getValue("status");return(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[K(a),"CONVERTED"===a&&(0,t.jsx)(L.Z,{className:"h-3 w-3 text-green-600",title:"Converted to Customer"})]})}},{accessorKey:"priority",header:"Priority",cell:e=>{let{row:s}=e;return Y(s.getValue("priority"))}},{accessorKey:"budget",header:"Budget",cell:e=>{let{row:s}=e,a=s.getValue("budget");return a?(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(I.Z,{className:"h-3 w-3 text-green-600"}),(0,t.jsxs)("span",{className:"font-medium",children:["$",a.toLocaleString()]})]}):(0,t.jsx)("span",{className:"text-gray-400 text-sm",children:"-"})}},{accessorKey:"score",header:"Score",cell:e=>{let{row:s}=e,a=s.getValue("score");return(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(q.q9,{score:a}),(0,t.jsxs)("span",{className:"font-medium",children:[a,"/100"]})]})}},{accessorKey:"source",header:"Source",cell:e=>{let{row:s}=e,a=s.getValue("source");return a?(0,t.jsx)(n.C,{variant:"outline",className:"text-xs",children:a}):(0,t.jsx)("span",{className:"text-gray-400 text-sm",children:"-"})}},{id:"actions",cell:e=>{let{row:s}=e,a=s.original;return(0,t.jsxs)(H.h_,{children:[(0,t.jsx)(H.$F,{asChild:!0,children:(0,t.jsx)(r.z,{variant:"ghost",className:"h-8 w-8 p-0",children:(0,t.jsx)(z.Z,{className:"h-4 w-4"})})}),(0,t.jsxs)(H.AW,{align:"end",children:[(0,t.jsx)(H.Ju,{children:"Actions"}),(0,t.jsx)(H.Xi,{asChild:!0,children:(0,t.jsxs)(V(),{href:"/dashboard/leads/".concat(a.id),children:[(0,t.jsx)(D.Z,{className:"mr-2 h-4 w-4"}),"View Details"]})}),(0,t.jsxs)(H.Xi,{onClick:()=>Q(a),children:[(0,t.jsx)(R.Z,{className:"mr-2 h-4 w-4"}),"Edit"]}),(0,t.jsx)(H.VD,{}),(0,t.jsxs)(H.Xi,{onClick:()=>G(a),className:"text-red-600",children:[(0,t.jsx)(w.Z,{className:"mr-2 h-4 w-4"}),"Delete"]})]})]})}}];return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Leads"}),(0,t.jsx)("p",{className:"text-gray-600 mt-1",children:"Track and convert your business leads"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(r.z,{variant:"outline",asChild:!0,children:(0,t.jsxs)(V(),{href:"/dashboard/leads/pipeline",children:[(0,t.jsx)(_.Z,{className:"h-4 w-4 mr-2"}),"Pipeline View"]})}),(0,t.jsx)(r.z,{variant:"outline",asChild:!0,children:(0,t.jsxs)(V(),{href:"/dashboard/leads/scoring",children:[(0,t.jsx)(B.Z,{className:"h-4 w-4 mr-2"}),"Scoring Analytics"]})}),(0,t.jsx)(r.z,{variant:"outline",asChild:!0,children:(0,t.jsxs)(V(),{href:"/dashboard/leads/conversions",children:[(0,t.jsx)(L.Z,{className:"h-4 w-4 mr-2"}),"Conversion Analytics"]})}),(0,t.jsxs)(r.z,{variant:"outline",onClick:()=>b(!C),children:[(0,t.jsx)(N.Z,{className:"h-4 w-4 mr-2"}),"Advanced Filter",Object.keys(A).length>0&&(0,t.jsx)(n.C,{variant:"secondary",className:"ml-2",children:Object.keys(A).length})]}),(0,t.jsxs)(r.z,{className:"flex items-center space-x-2",onClick:()=>p(!0),children:[(0,t.jsx)(P.Z,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Add Lead"})]})]})]}),C&&(0,t.jsx)(T,{onFilterChange:e=>{F(e)},onApplyFilters:U,initialFilters:A,loading:j}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-6",children:[(0,t.jsxs)(c.Zb,{children:[(0,t.jsxs)(c.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(c.ll,{className:"text-sm font-medium",children:"Total Leads"}),(0,t.jsx)(W.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(c.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:k.total}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"All leads"})]})]}),(0,t.jsxs)(c.Zb,{children:[(0,t.jsxs)(c.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(c.ll,{className:"text-sm font-medium",children:"New"}),(0,t.jsx)(B.Z,{className:"h-4 w-4 text-blue-600"})]}),(0,t.jsxs)(c.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:k.new}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"New leads"})]})]}),(0,t.jsxs)(c.Zb,{children:[(0,t.jsxs)(c.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(c.ll,{className:"text-sm font-medium",children:"Qualified"}),(0,t.jsx)(_.Z,{className:"h-4 w-4 text-green-600"})]}),(0,t.jsxs)(c.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:k.qualified}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Qualified leads"})]})]}),(0,t.jsxs)(c.Zb,{children:[(0,t.jsxs)(c.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(c.ll,{className:"text-sm font-medium",children:"Closed Won"}),(0,t.jsx)(X.Z,{className:"h-4 w-4 text-purple-600"})]}),(0,t.jsxs)(c.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:k.closedWon}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Successful conversions"})]})]}),(0,t.jsxs)(c.Zb,{children:[(0,t.jsxs)(c.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(c.ll,{className:"text-sm font-medium",children:"Pipeline Value"}),(0,t.jsx)(I.Z,{className:"h-4 w-4 text-green-600"})]}),(0,t.jsxs)(c.aY,{children:[(0,t.jsxs)("div",{className:"text-2xl font-bold",children:["$",k.totalValue.toLocaleString()]}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Total estimated value"})]})]})]}),(0,t.jsxs)(c.Zb,{children:[(0,t.jsx)(c.Ol,{children:(0,t.jsx)(c.ll,{children:"Lead Pipeline"})}),(0,t.jsx)(c.aY,{children:m?(0,t.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,t.jsx)(d.w,{columns:$,data:h,searchPlaceholder:"Search leads..."})})]}),(0,t.jsx)(o.p,{isOpen:f,onClose:()=>{p(!1),y(null)},onSuccess:S,lead:g,mode:g?"edit":"create"})]})}},89673:function(e,s,a){"use strict";a.d(s,{OT:function(){return h},q9:function(){return o}});var t=a(57437),i=a(31478),l=a(81016),c=a(44135),r=a(3928),n=a(89275);function d(e){let{score:s,size:a="md",showIcon:d=!0,showLabel:o=!0}=e,h=s>=70?"HOT":s>=40?"WARM":"COLD",x=(e=>{switch(e){case"HOT":return{color:"text-red-600 bg-red-100 border-red-200",icon:l.Z,label:"Hot Lead",description:"High priority, ready to convert"};case"WARM":return{color:"text-orange-600 bg-orange-100 border-orange-200",icon:c.Z,label:"Warm Lead",description:"Good potential, needs nurturing"};case"COLD":return{color:"text-blue-600 bg-blue-100 border-blue-200",icon:r.Z,label:"Cold Lead",description:"Low engagement, requires attention"};default:return{color:"text-gray-600 bg-gray-100 border-gray-200",icon:n.Z,label:"Unknown",description:"Temperature not determined"}}})(h),m=(e=>{switch(e){case"sm":return{badge:"text-xs px-2 py-1",icon:"h-3 w-3",text:"text-xs"};case"lg":return{badge:"text-base px-4 py-2",icon:"h-5 w-5",text:"text-base"};default:return{badge:"text-sm px-3 py-1",icon:"h-4 w-4",text:"text-sm"}}})(a),u=x.icon;return(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(i.C,{className:"".concat(x.color," border ").concat(m.badge," font-medium"),variant:"outline",children:(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[d&&(0,t.jsx)(u,{className:m.icon}),(0,t.jsx)("span",{children:h})]})}),o&&(0,t.jsxs)("div",{className:"flex flex-col",children:[(0,t.jsx)("span",{className:"".concat(m.text," font-medium text-gray-900"),children:x.label}),"lg"===a&&(0,t.jsx)("span",{className:"text-xs text-gray-500",children:x.description})]})]})}function o(e){let{score:s}=e;return(0,t.jsx)(d,{score:s,size:"sm",showLabel:!1})}function h(e){let{score:s}=e;return(0,t.jsx)(d,{score:s,size:"lg",showIcon:!0,showLabel:!0})}},54900:function(e,s,a){"use strict";a.d(s,{X:function(){return n}});var t=a(57437),i=a(2265),l=a(66062),c=a(62442),r=a(1657);let n=i.forwardRef((e,s)=>{let{className:a,...i}=e;return(0,t.jsx)(l.fC,{ref:s,className:(0,r.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",a),...i,children:(0,t.jsx)(l.z$,{className:(0,r.cn)("flex items-center justify-center text-current"),children:(0,t.jsx)(c.Z,{className:"h-4 w-4"})})})});n.displayName=l.fC.displayName}},function(e){e.O(0,[6723,9502,2749,1706,4138,1396,4997,2881,2012,5385,774,528,6950,2971,4938,1744],function(){return e(e.s=36989)}),_N_E=e.O()}]);