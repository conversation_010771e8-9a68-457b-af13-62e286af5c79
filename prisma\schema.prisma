generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// ============================================================================
// ENTERPRISE BUSINESS MANAGEMENT SYSTEM SCHEMA
// Complete CRM, Sales, Invoicing, Contracts, and Project Management
// ============================================================================

// ============================================================================
// ENUMS
// ============================================================================

enum UserRole {
  SUPER_ADMIN
  ADMIN
  MANAGER
  SALES
  ACCOUNTANT
  USER
}

enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  PENDING
}

enum CompanyStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  TRIAL
}

enum CompanySize {
  STARTUP      // 1-10 employees
  SMALL        // 11-50 employees
  MEDIUM       // 51-200 employees
  LARGE        // 201-1000 employees
  ENTERPRISE   // 1000+ employees
}

enum SubscriptionPlan {
  FREE
  STARTER
  PROFESSIONAL
  ENTERPRISE
  CUSTOM
}

enum SubscriptionStatus {
  ACTIVE
  CANCELLED
  EXPIRED
  TRIAL
  PAST_DUE
}

enum LeadStatus {
  NEW
  CONTACTED
  QUALIFIED
  PROPOSAL
  NEGOTIATION
  CLOSED_WON
  CLOSED_LOST
}

enum LeadSource {
  WEBSITE
  REFERRAL
  SOCIAL_MEDIA
  EMAIL_CAMPAIGN
  COLD_CALL
  TRADE_SHOW
  ADVERTISEMENT
  PARTNER
  OTHER
}

enum CustomerStatus {
  ACTIVE
  INACTIVE
  PROSPECT
  CHURNED
}

enum QuotationStatus {
  DRAFT
  SENT
  VIEWED
  ACCEPTED
  REJECTED
  EXPIRED
  REVISED
}

enum InvoiceStatus {
  DRAFT
  SENT
  VIEWED
  PAID
  PARTIALLY_PAID
  OVERDUE
  CANCELLED
  REFUNDED
}

enum ContractStatus {
  DRAFT
  REVIEW
  SENT
  SIGNED
  ACTIVE
  COMPLETED
  CANCELLED
  EXPIRED
  RENEWED
}

enum ContractType {
  SERVICE
  PRODUCT
  SUBSCRIPTION
  MAINTENANCE
  CONSULTING
  LICENSE
  PARTNERSHIP
  NDA
  OTHER
}

enum Priority {
  LOW
  MEDIUM
  HIGH
  URGENT
  CRITICAL
}

enum TaskStatus {
  TODO
  IN_PROGRESS
  REVIEW
  DONE
  CANCELLED
}

enum ActivityType {
  NOTE
  CALL
  EMAIL
  MEETING
  TASK
  STATUS_CHANGE
  DOCUMENT_UPLOAD
  PAYMENT_RECEIVED
  CONTRACT_SIGNED
  SYSTEM
}

enum TransactionType {
  PAYMENT
  REFUND
  ADJUSTMENT
  FEE
  DISCOUNT
}

enum PaymentMethod {
  CASH
  CHECK
  BANK_TRANSFER
  CREDIT_CARD
  DEBIT_CARD
  PAYPAL
  STRIPE
  CRYPTO
  OTHER
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  CANCELLED
  REFUNDED
}

enum DocumentType {
  CONTRACT
  INVOICE
  QUOTATION
  RECEIPT
  AGREEMENT
  PROPOSAL
  REPORT
  IMAGE
  OTHER
}

// ============================================================================
// AUTHENTICATION MODELS (NextAuth.js)
// ============================================================================

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

// ============================================================================
// CORE BUSINESS MODELS
// ============================================================================

model User {
  id                String      @id @default(cuid())
  email             String      @unique
  password          String?
  name              String?
  firstName         String?
  lastName          String?
  avatar            String?
  phone             String?
  role              UserRole    @default(USER)
  status            UserStatus  @default(ACTIVE)

  // Profile
  title             String?
  department        String?
  bio               String?
  timezone          String?
  language          String      @default("en")

  // Authentication
  emailVerified     DateTime?
  emailVerificationToken String?
  passwordResetToken String?
  passwordResetExpires DateTime?
  twoFactorEnabled  Boolean     @default(false)
  twoFactorSecret   String?

  // Activity tracking
  lastLoginAt       DateTime?
  loginCount        Int         @default(0)
  lastActiveAt      DateTime?

  // Company relationship
  companyId         String?
  company           Company?    @relation("CompanyMembers", fields: [companyId], references: [id])
  ownedCompany      Company?    @relation("CompanyOwner")

  // Permissions and settings
  permissions       Json?
  settings          Json?
  preferences       Json?

  // Timestamps
  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt

  // Relations
  accounts          Account[]
  sessions          Session[]

  // Business relations
  createdLeads      Lead[]      @relation("LeadCreatedBy")
  assignedLeads     Lead[]      @relation("LeadAssignedTo")
  createdCustomers  Customer[]  @relation("CustomerCreatedBy")
  assignedCustomers Customer[]  @relation("CustomerAssignedTo")
  createdQuotations Quotation[] @relation("QuotationCreatedBy")
  assignedQuotations Quotation[] @relation("QuotationAssignedTo")
  createdInvoices   Invoice[]   @relation("InvoiceCreatedBy")
  assignedInvoices  Invoice[]   @relation("InvoiceAssignedTo")
  createdContracts  Contract[]  @relation("ContractCreatedBy")
  assignedContracts Contract[]  @relation("ContractAssignedTo")
  createdTasks      Task[]      @relation("TaskCreatedBy")
  assignedTasks     Task[]      @relation("TaskAssignedTo")
  activities        Activity[]
  notes             Note[]
  documents         Document[]
  signatures        Signature[]
  financialAccounts FinancialAccount[]
  accountTransactions AccountTransaction[]
  budgets           Budget[]
  userSettings      UserSettings?
  auditLogs         AuditLog[]
  systemLogs        SystemLog[]
  notifications     Notification[]
  securityAlerts    SecurityAlert[]

  @@map("users")
}

model Company {
  id                String          @id @default(cuid())
  name              String
  email             String?
  phone             String?
  address           String?
  city              String?
  state             String?
  country           String?
  postalCode        String?
  website           String?
  logo              String?

  // Business details
  industry          String?
  size              CompanySize?
  businessType      String?
  taxId             String?
  registrationNumber String?

  // Settings and branding
  settings          Json?
  branding          Json?
  theme             Json?

  // Subscription
  subscriptionId    String?         @unique
  subscription      Subscription?   @relation(fields: [subscriptionId], references: [id])

  // Owner and members
  ownerId           String          @unique
  owner             User            @relation("CompanyOwner", fields: [ownerId], references: [id])
  members           User[]          @relation("CompanyMembers")

  // Status and limits
  status            CompanyStatus   @default(ACTIVE)
  maxUsers          Int             @default(5)
  maxCustomers      Int             @default(100)
  maxQuotations     Int             @default(50)
  maxInvoices       Int             @default(50)
  maxContracts      Int             @default(25)
  maxStorage        Int             @default(1073741824) // 1GB in bytes

  // Timestamps
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt

  // Business relations
  leads             Lead[]
  customers         Customer[]
  quotations        Quotation[]
  invoices          Invoice[]
  contracts         Contract[]
  items             Item[]
  tasks             Task[]
  activities        Activity[]
  notes             Note[]
  documents         Document[]
  signatures        Signature[]
  transactions      Transaction[]
  financialAccounts FinancialAccount[]
  accountTransactions AccountTransaction[]
  budgets           Budget[]
  companySettings   CompanySettings?
  auditLogs         AuditLog[]
  systemLogs        SystemLog[]
  notifications     Notification[]
  securityAlerts    SecurityAlert[]

  @@map("companies")
}

model Subscription {
  id                String              @id @default(cuid())
  plan              SubscriptionPlan
  status            SubscriptionStatus  @default(TRIAL)

  // Plan reference
  planId            String?
  pricingPlan       PricingPlan?        @relation(fields: [planId], references: [id])

  // Billing
  priceId           String?
  customerId        String?             // Stripe customer ID
  subscriptionId    String?             // Stripe subscription ID

  // Dates
  startDate         DateTime            @default(now())
  endDate           DateTime?
  trialEndDate      DateTime?
  cancelledAt       DateTime?

  // Billing cycle
  billingCycle      String              @default("monthly") // monthly, yearly
  amount            Decimal             @default(0)
  currency          String              @default("USD")

  // Features
  features          Json?
  limits            Json?

  // Timestamps
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  // Relations
  company           Company?

  @@map("subscriptions")
}

// ============================================================================
// CRM MODELS
// ============================================================================

model Lead {
  id                String      @id @default(cuid())
  firstName         String
  lastName          String
  email             String
  phone             String?
  companyName       String?
  title             String?
  website           String?

  // Lead details
  source            LeadSource  @default(OTHER)
  status            LeadStatus  @default(NEW)
  priority          Priority    @default(MEDIUM)

  // Address
  address           String?
  city              String?
  state             String?
  country           String?
  postalCode        String?

  // Business info
  industry          String?
  companySize       String?
  budget            Decimal?
  timeline          String?

  // Lead scoring and qualification
  score             Int         @default(0)
  qualified         Boolean     @default(false)
  qualifiedAt       DateTime?

  // Conversion
  convertedAt       DateTime?
  customerId        String?
  customer          Customer?   @relation(fields: [customerId], references: [id])

  // Assignment and ownership
  assignedToId      String?
  assignedTo        User?       @relation("LeadAssignedTo", fields: [assignedToId], references: [id])
  createdById       String
  createdBy         User        @relation("LeadCreatedBy", fields: [createdById], references: [id])

  // Company relationship
  companyId         String
  company           Company     @relation(fields: [companyId], references: [id])

  // Additional data
  description       String?
  tags              Json?
  customFields      Json?

  // Timestamps
  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt

  // Relations
  activities        Activity[]
  leadNotes         Note[]      @relation("LeadNotes")
  tasks             Task[]
  documents         Document[]

  @@map("leads")
}

model Customer {
  id                String          @id @default(cuid())
  name              String
  email             String?
  phone             String?
  companyName       String?
  title             String?
  website           String?

  // Address
  address           String?
  city              String?
  state             String?
  country           String?
  postalCode        String?

  // Business info
  industry          String?
  companySize       String?
  taxId             String?

  // Customer details
  status            CustomerStatus  @default(ACTIVE)
  priority          Priority        @default(MEDIUM)

  // Financial
  creditLimit       Decimal?
  paymentTerms      String?

  // Assignment and ownership
  assignedToId      String?
  assignedTo        User?           @relation("CustomerAssignedTo", fields: [assignedToId], references: [id])
  createdById       String
  createdBy         User            @relation("CustomerCreatedBy", fields: [createdById], references: [id])

  // Company relationship
  companyId         String
  company           Company         @relation(fields: [companyId], references: [id])

  // Additional data
  description       String?
  tags              Json?
  customFields      Json?

  // Timestamps
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt

  // Relations
  leads             Lead[]
  quotations        Quotation[]
  invoices          Invoice[]
  contracts         Contract[]
  activities        Activity[]
  customerNotes     Note[]      @relation("CustomerNotes")
  tasks             Task[]
  documents         Document[]
  accountTransactions AccountTransaction[]

  @@map("customers")
}

// ============================================================================
// PRODUCT/SERVICE MODELS
// ============================================================================

model Item {
  id                String      @id @default(cuid())
  name              String
  description       String?
  sku               String?     @unique
  category          String?

  // Pricing
  unitPrice         Decimal     @default(0)
  costPrice         Decimal?
  currency          String      @default("USD")

  // Inventory
  trackInventory    Boolean     @default(false)
  stockQuantity     Int?
  lowStockAlert     Int?

  // Tax and accounting
  taxable           Boolean     @default(true)
  taxRate           Decimal     @default(0)
  accountingCode    String?

  // Status
  active            Boolean     @default(true)

  // Company relationship
  companyId         String
  company           Company     @relation(fields: [companyId], references: [id])

  // Additional data
  tags              Json?
  customFields      Json?

  // Timestamps
  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt

  // Relations
  quotationItems    QuotationItem[]
  invoiceItems      InvoiceItem[]

  @@map("items")
}

// ============================================================================
// SALES MODELS
// ============================================================================

model Quotation {
  id                String          @id @default(cuid())
  quotationNumber   String          @unique
  title             String
  description       String?

  // Customer relationship
  customerId        String
  customer          Customer        @relation(fields: [customerId], references: [id])

  // Status and dates
  status            QuotationStatus @default(DRAFT)
  validUntil        DateTime?
  acceptedAt        DateTime?
  rejectedAt        DateTime?

  // Financial
  subtotal          Decimal         @default(0)
  taxRate           Decimal         @default(0)
  taxAmount         Decimal         @default(0)
  discountType      String          @default("PERCENTAGE") // PERCENTAGE or FIXED
  discountValue     Decimal         @default(0)
  discountAmount    Decimal         @default(0)
  total             Decimal         @default(0)
  currency          String          @default("USD")

  // Terms and conditions
  terms             String?
  internalNotes     String?
  paymentTerms      String?

  // Assignment and ownership
  assignedToId      String?
  assignedTo        User?           @relation("QuotationAssignedTo", fields: [assignedToId], references: [id])
  createdById       String
  createdBy         User            @relation("QuotationCreatedBy", fields: [createdById], references: [id])

  // Company relationship
  companyId         String
  company           Company         @relation(fields: [companyId], references: [id])

  // Additional data
  tags              Json?
  customFields      Json?

  // Timestamps
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt

  // Relations
  items             QuotationItem[]
  invoices          Invoice[]
  contracts         Contract[]
  activities        Activity[]
  quotationNotes    Note[]      @relation("QuotationNotes")
  tasks             Task[]
  documents         Document[]
  signatures        Signature[]
  accountTransactions AccountTransaction[]

  @@map("quotations")
}

model QuotationItem {
  id                String      @id @default(cuid())
  name              String
  description       String?
  quantity          Int         @default(1)
  unitPrice         Decimal     @default(0)
  discount          Decimal     @default(0) // Percentage
  taxRate           Decimal     @default(0) // Percentage
  total             Decimal     @default(0)

  // Item relationship
  itemId            String?
  item              Item?       @relation(fields: [itemId], references: [id])

  // Quotation relationship
  quotationId       String
  quotation         Quotation   @relation(fields: [quotationId], references: [id], onDelete: Cascade)

  // Timestamps
  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt

  @@map("quotation_items")
}

// ============================================================================
// INVOICING MODELS
// ============================================================================

model Invoice {
  id                String        @id @default(cuid())
  invoiceNumber     String        @unique
  title             String?
  description       String?

  // Customer relationship
  customerId        String
  customer          Customer      @relation(fields: [customerId], references: [id])

  // Quotation relationship
  quotationId       String?
  quotation         Quotation?    @relation(fields: [quotationId], references: [id])

  // Status and dates
  status            InvoiceStatus @default(DRAFT)
  issueDate         DateTime      @default(now())
  dueDate           DateTime?
  paidAt            DateTime?

  // Financial
  subtotal          Decimal       @default(0)
  taxRate           Decimal       @default(0)
  taxAmount         Decimal       @default(0)
  discountType      String        @default("PERCENTAGE") // PERCENTAGE or FIXED
  discountValue     Decimal       @default(0)
  discountAmount    Decimal       @default(0)
  total             Decimal       @default(0)
  paidAmount        Decimal       @default(0)
  balanceAmount     Decimal       @default(0)
  currency          String        @default("USD")

  // Terms and conditions
  terms             String?
  internalNotes     String?
  paymentTerms      String?
  paymentMethod     String?

  // Assignment and ownership
  assignedToId      String?
  assignedTo        User?         @relation("InvoiceAssignedTo", fields: [assignedToId], references: [id])
  createdById       String
  createdBy         User          @relation("InvoiceCreatedBy", fields: [createdById], references: [id])

  // Company relationship
  companyId         String
  company           Company       @relation(fields: [companyId], references: [id])

  // Additional data
  tags              Json?
  customFields      Json?

  // Timestamps
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt

  // Relations
  items             InvoiceItem[]
  contracts         Contract[]
  transactions      Transaction[]
  activities        Activity[]
  invoiceNotes      Note[]      @relation("InvoiceNotes")
  tasks             Task[]
  documents         Document[]
  accountTransactions AccountTransaction[]

  @@map("invoices")
}

model InvoiceItem {
  id                String      @id @default(cuid())
  name              String
  description       String?
  quantity          Int         @default(1)
  unitPrice         Decimal     @default(0)
  discount          Decimal     @default(0) // Percentage
  taxRate           Decimal     @default(0) // Percentage
  total             Decimal     @default(0)

  // Item relationship
  itemId            String?
  item              Item?       @relation(fields: [itemId], references: [id])

  // Invoice relationship
  invoiceId         String
  invoice           Invoice     @relation(fields: [invoiceId], references: [id], onDelete: Cascade)

  // Timestamps
  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt

  @@map("invoice_items")
}

// ============================================================================
// CONTRACT MODELS
// ============================================================================

model Contract {
  id                String          @id @default(cuid())
  contractNumber    String          @unique
  title             String
  description       String?
  type              ContractType    @default(SERVICE)

  // Customer relationship
  customerId        String
  customer          Customer        @relation(fields: [customerId], references: [id])

  // Related documents
  quotationId       String?
  quotation         Quotation?      @relation(fields: [quotationId], references: [id])
  invoiceId         String?
  invoice           Invoice?        @relation(fields: [invoiceId], references: [id])

  // Status and dates
  status            ContractStatus  @default(DRAFT)
  priority          Priority        @default(MEDIUM)
  startDate         DateTime?
  endDate           DateTime?
  renewalDate       DateTime?
  autoRenewal       Boolean         @default(false)
  renewalPeriod     Int?            // in months
  signedAt          DateTime?

  // Financial
  value             Decimal?
  currency          String          @default("USD")

  // Terms and conditions
  terms             String?
  conditions        String?
  internalNotes     String?

  // Signature requirements
  signatureRequired Boolean         @default(true)

  // Assignment and ownership
  assignedToId      String?
  assignedTo        User?           @relation("ContractAssignedTo", fields: [assignedToId], references: [id])
  createdById       String
  createdBy         User            @relation("ContractCreatedBy", fields: [createdById], references: [id])

  // Company relationship
  companyId         String
  company           Company         @relation(fields: [companyId], references: [id])

  // Additional data
  tags              Json?
  customFields      Json?

  // Timestamps
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt

  // Relations
  signatures        Signature[]
  activities        Activity[]
  contractNotes     Note[]      @relation("ContractNotes")
  tasks             Task[]
  documents         Document[]
  accountTransactions AccountTransaction[]

  @@map("contracts")
}

// ============================================================================
// TASK MANAGEMENT MODELS
// ============================================================================

model Task {
  id                String      @id @default(cuid())
  title             String
  description       String?
  status            TaskStatus  @default(TODO)
  priority          Priority    @default(MEDIUM)

  // Dates
  dueDate           DateTime?
  startDate         DateTime?
  completedAt       DateTime?

  // Assignment and ownership
  assignedToId      String?
  assignedTo        User?       @relation("TaskAssignedTo", fields: [assignedToId], references: [id])
  createdById       String
  createdBy         User        @relation("TaskCreatedBy", fields: [createdById], references: [id])

  // Company relationship
  companyId         String
  company           Company     @relation(fields: [companyId], references: [id])

  // Related entities
  leadId            String?
  lead              Lead?       @relation(fields: [leadId], references: [id])
  customerId        String?
  customer          Customer?   @relation(fields: [customerId], references: [id])
  quotationId       String?
  quotation         Quotation?  @relation(fields: [quotationId], references: [id])
  invoiceId         String?
  invoice           Invoice?    @relation(fields: [invoiceId], references: [id])
  contractId        String?
  contract          Contract?   @relation(fields: [contractId], references: [id])

  // Additional data
  tags              Json?
  customFields      Json?

  // Timestamps
  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt

  // Relations
  activities        Activity[]
  notes             Note[]
  documents         Document[]

  @@map("tasks")
}

// ============================================================================
// ACTIVITY AND COMMUNICATION MODELS
// ============================================================================

model Activity {
  id                String        @id @default(cuid())
  type              ActivityType
  title             String
  description       String?

  // User relationship
  createdById       String
  createdBy         User          @relation(fields: [createdById], references: [id])

  // Company relationship
  companyId         String
  company           Company       @relation(fields: [companyId], references: [id])

  // Related entities
  leadId            String?
  lead              Lead?         @relation(fields: [leadId], references: [id])
  customerId        String?
  customer          Customer?     @relation(fields: [customerId], references: [id])
  quotationId       String?
  quotation         Quotation?    @relation(fields: [quotationId], references: [id])
  invoiceId         String?
  invoice           Invoice?      @relation(fields: [invoiceId], references: [id])
  contractId        String?
  contract          Contract?     @relation(fields: [contractId], references: [id])
  taskId            String?
  task              Task?         @relation(fields: [taskId], references: [id])

  // Additional data
  metadata          Json?

  // Timestamps
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt

  @@map("activities")
}

model Note {
  id                String      @id @default(cuid())
  title             String?
  content           String

  // User relationship
  createdById       String
  createdBy         User        @relation(fields: [createdById], references: [id])

  // Company relationship
  companyId         String
  company           Company     @relation(fields: [companyId], references: [id])

  // Related entities
  leadId            String?
  lead              Lead?       @relation("LeadNotes", fields: [leadId], references: [id])
  customerId        String?
  customer          Customer?   @relation("CustomerNotes", fields: [customerId], references: [id])
  quotationId       String?
  quotation         Quotation?  @relation("QuotationNotes", fields: [quotationId], references: [id])
  invoiceId         String?
  invoice           Invoice?    @relation("InvoiceNotes", fields: [invoiceId], references: [id])
  contractId        String?
  contract          Contract?   @relation("ContractNotes", fields: [contractId], references: [id])
  taskId            String?
  task              Task?       @relation(fields: [taskId], references: [id])

  // Timestamps
  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt

  @@map("notes")
}

// ============================================================================
// DOCUMENT MANAGEMENT MODELS
// ============================================================================

model Document {
  id                String        @id @default(cuid())
  name              String
  originalName      String
  type              DocumentType  @default(OTHER)
  mimeType          String
  size              Int
  url               String

  // Storage
  storageProvider   String        @default("local") // local, s3, gcs, etc.
  storagePath       String

  // User relationship
  uploadedById      String
  uploadedBy        User          @relation(fields: [uploadedById], references: [id])

  // Company relationship
  companyId         String
  company           Company       @relation(fields: [companyId], references: [id])

  // Related entities
  leadId            String?
  lead              Lead?         @relation(fields: [leadId], references: [id])
  customerId        String?
  customer          Customer?     @relation(fields: [customerId], references: [id])
  quotationId       String?
  quotation         Quotation?    @relation(fields: [quotationId], references: [id])
  invoiceId         String?
  invoice           Invoice?      @relation(fields: [invoiceId], references: [id])
  contractId        String?
  contract          Contract?     @relation(fields: [contractId], references: [id])
  taskId            String?
  task              Task?         @relation(fields: [taskId], references: [id])

  // Additional data
  tags              Json?
  metadata          Json?

  // Timestamps
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt

  @@map("documents")
}

model Signature {
  id                String      @id @default(cuid())
  signatureData     String      // Base64 encoded signature or signature provider ID
  signatureType     String      @default("electronic") // electronic, digital, wet

  // Signer information
  signerName        String
  signerEmail       String
  signerIp          String?
  signedAt          DateTime    @default(now())

  // Signature provider (DocuSign, Adobe Sign, etc.)
  provider          String?
  providerSignatureId String?

  // User relationship (internal user who requested signature)
  requestedById     String
  requestedBy       User        @relation(fields: [requestedById], references: [id])

  // Company relationship
  companyId         String
  company           Company     @relation(fields: [companyId], references: [id])

  // Related entities
  quotationId       String?
  quotation         Quotation?  @relation(fields: [quotationId], references: [id])
  contractId        String?
  contract          Contract?   @relation(fields: [contractId], references: [id])

  // Additional data
  metadata          Json?

  // Timestamps
  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt

  @@map("signatures")
}

// ============================================================================
// FINANCIAL MODELS
// ============================================================================

model Transaction {
  id                String          @id @default(cuid())
  type              TransactionType
  amount            Decimal
  currency          String          @default("USD")

  // Payment details
  paymentMethod     PaymentMethod?
  paymentStatus     PaymentStatus   @default(PENDING)

  // Transaction details
  reference         String?
  description       String?
  notes             String?

  // External payment provider
  providerId        String?         // Stripe payment intent ID, etc.
  providerData      Json?

  // Dates
  transactionDate   DateTime        @default(now())
  processedAt       DateTime?

  // Company relationship
  companyId         String
  company           Company         @relation(fields: [companyId], references: [id])

  // Related entities
  invoiceId         String?
  invoice           Invoice?        @relation(fields: [invoiceId], references: [id])

  // Additional data
  metadata          Json?

  // Timestamps
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt

  @@map("transactions")
}

// ============================================================================
// FINANCIAL ACCOUNTS MODELS
// ============================================================================

model FinancialAccount {
  id                String              @id @default(cuid())
  name              String
  accountNumber     String?
  accountType       AccountType
  accountSubType    String?
  description       String?

  // Financial details
  balance           Decimal             @default(0) @db.Decimal(15, 2)
  currency          String              @default("USD")
  isActive          Boolean             @default(true)

  // Bank details (for bank accounts)
  bankName          String?
  routingNumber     String?
  accountNumberMask String?

  // Chart of accounts
  parentAccountId   String?
  parentAccount     FinancialAccount?   @relation("AccountHierarchy", fields: [parentAccountId], references: [id])
  childAccounts     FinancialAccount[]  @relation("AccountHierarchy")

  // Accounting details
  normalBalance     BalanceType         @default(DEBIT)
  taxReportingCode  String?
  isSystemAccount   Boolean             @default(false)

  // Company and user relations
  companyId         String
  company           Company             @relation(fields: [companyId], references: [id], onDelete: Cascade)
  createdById       String
  createdBy         User                @relation(fields: [createdById], references: [id])

  // Relations
  debitTransactions   AccountTransaction[] @relation("DebitAccount")
  creditTransactions  AccountTransaction[] @relation("CreditAccount")
  budgets           Budget[]

  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@unique([companyId, accountNumber])
  @@map("financial_accounts")
}

model AccountTransaction {
  id                String              @id @default(cuid())
  transactionNumber String
  description       String
  reference         String?

  // Transaction details
  amount            Decimal             @db.Decimal(15, 2)
  currency          String              @default("USD")
  transactionDate   DateTime

  // Double-entry bookkeeping
  debitAccountId    String
  debitAccount      FinancialAccount    @relation("DebitAccount", fields: [debitAccountId], references: [id])
  creditAccountId   String?
  creditAccount     FinancialAccount?   @relation("CreditAccount", fields: [creditAccountId], references: [id])

  // Transaction type and status
  transactionType   TransactionType
  status            TransactionStatus   @default(PENDING)

  // Related entities
  customerId        String?
  customer          Customer?           @relation(fields: [customerId], references: [id])
  invoiceId         String?
  invoice           Invoice?            @relation(fields: [invoiceId], references: [id])
  quotationId       String?
  quotation         Quotation?          @relation(fields: [quotationId], references: [id])
  contractId        String?
  contract          Contract?           @relation(fields: [contractId], references: [id])

  // Reconciliation
  isReconciled      Boolean             @default(false)
  reconciledAt      DateTime?
  reconciledBy      String?

  // Company and user relations
  companyId         String
  company           Company             @relation(fields: [companyId], references: [id], onDelete: Cascade)
  createdById       String
  createdBy         User                @relation(fields: [createdById], references: [id])

  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@unique([companyId, transactionNumber])
  @@map("account_transactions")
}

model Budget {
  id                String              @id @default(cuid())
  name              String
  description       String?

  // Budget period
  startDate         DateTime
  endDate           DateTime
  budgetType        BudgetType          @default(ANNUAL)

  // Budget amounts
  budgetedAmount    Decimal             @db.Decimal(15, 2)
  actualAmount      Decimal             @default(0) @db.Decimal(15, 2)
  variance          Decimal             @default(0) @db.Decimal(15, 2)

  // Status and settings
  status            BudgetStatus        @default(DRAFT)
  currency          String              @default("USD")
  isActive          Boolean             @default(true)

  // Account relation
  accountId         String
  account           FinancialAccount    @relation(fields: [accountId], references: [id])

  // Company and user relations
  companyId         String
  company           Company             @relation(fields: [companyId], references: [id], onDelete: Cascade)
  createdById       String
  createdBy         User                @relation(fields: [createdById], references: [id])

  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@map("budgets")
}

// ============================================================================
// FINANCIAL ENUMS
// ============================================================================

enum AccountType {
  ASSET
  LIABILITY
  EQUITY
  REVENUE
  EXPENSE
}

enum BalanceType {
  DEBIT
  CREDIT
}



enum TransactionStatus {
  PENDING
  COMPLETED
  CANCELLED
  FAILED
}

enum BudgetType {
  MONTHLY
  QUARTERLY
  ANNUAL
  CUSTOM
}

enum BudgetStatus {
  DRAFT
  ACTIVE
  COMPLETED
  CANCELLED
}

// ============================================================================
// SETTINGS MODELS
// ============================================================================

model CompanySettings {
  id                String              @id @default(cuid())

  // Company Information
  companyName       String?
  companyEmail      String?
  companyPhone      String?
  companyAddress    String?
  companyCity       String?
  companyState      String?
  companyCountry    String?
  companyPostalCode String?
  companyWebsite    String?
  companyLogo       String?

  // Business Details
  industry          String?
  businessType      String?
  taxId             String?
  registrationNumber String?

  // Financial Settings
  defaultCurrency   String              @default("USD")
  taxRate           Decimal             @default(0) @db.Decimal(5, 2)

  // Localization
  timezone          String              @default("UTC")
  dateFormat        String              @default("MM/dd/yyyy")
  timeFormat        String              @default("12")
  language          String              @default("en")

  // Branding
  primaryColor      String              @default("#3b82f6")
  secondaryColor    String              @default("#1e3a8a")
  accentColor       String              @default("#f59e0b")
  fontFamily        String              @default("Inter")

  // Document Settings
  invoicePrefix     String              @default("INV")
  quotationPrefix   String              @default("QUO")
  contractPrefix    String              @default("CON")
  invoiceNumbering  String              @default("sequential")

  // Payment Settings
  defaultPaymentTerms String?
  bankDetails       Json?
  paymentMethods    Json?

  // Email Settings
  emailSettings     Json?

  // Notification Settings
  notificationSettings Json?

  // Security Settings
  securitySettings  Json?

  // Integration Settings
  integrationSettings Json?

  // Feature Settings
  featureSettings   Json?

  // Custom Settings
  customSettings    Json?

  // Company relationship
  companyId         String              @unique
  company           Company             @relation(fields: [companyId], references: [id], onDelete: Cascade)

  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@map("company_settings")
}

model UserSettings {
  id                String              @id @default(cuid())

  // Display Preferences
  theme             String              @default("light")
  language          String              @default("en")
  timezone          String              @default("UTC")
  dateFormat        String              @default("MM/dd/yyyy")
  timeFormat        String              @default("12")

  // Dashboard Settings
  dashboardLayout   Json?
  defaultView       String              @default("dashboard")
  itemsPerPage      Int                 @default(25)

  // Notification Preferences
  emailNotifications Boolean            @default(true)
  pushNotifications Boolean             @default(true)
  smsNotifications  Boolean             @default(false)
  notificationTypes Json?

  // Privacy Settings
  profileVisibility String              @default("company")
  showEmail         Boolean             @default(false)
  showPhone         Boolean             @default(false)

  // Accessibility Settings
  fontSize          String              @default("medium")
  highContrast      Boolean             @default(false)
  reducedMotion     Boolean             @default(false)

  // Workflow Settings
  autoSave          Boolean             @default(true)
  confirmActions    Boolean             @default(true)
  shortcuts         Json?

  // Custom Preferences
  customPreferences Json?

  // User relationship
  userId            String              @unique
  user              User                @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@map("user_settings")
}

model SystemSettings {
  id                String              @id @default(cuid())
  key               String              @unique
  value             Json
  category          String
  description       String?
  isPublic          Boolean             @default(false)
  isEditable        Boolean             @default(true)

  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@map("system_settings")
}

// ============================================================================
// AUDIT AND LOGGING MODELS
// ============================================================================

model AuditLog {
  id                String              @id @default(cuid())

  // Action details
  action            String
  entityType        String
  entityId          String?

  // User details
  userId            String?
  user              User?               @relation(fields: [userId], references: [id])
  userEmail         String?
  userRole          String?

  // Request details
  ipAddress         String?
  userAgent         String?
  requestUrl        String?
  requestMethod     String?

  // Changes
  oldValues         Json?
  newValues         Json?

  // Metadata
  metadata          Json?
  severity          AuditSeverity       @default(INFO)

  // Company context
  companyId         String?
  company           Company?            @relation(fields: [companyId], references: [id])

  // Timestamps
  createdAt         DateTime            @default(now())

  @@index([userId])
  @@index([companyId])
  @@index([entityType, entityId])
  @@index([action])
  @@index([createdAt])
  @@map("audit_logs")
}

model SystemLog {
  id                String              @id @default(cuid())
  level             LogLevel
  message           String
  source            String              // e.g., 'api', 'auth', 'database'
  category          String?             // e.g., 'security', 'performance', 'error'

  // Context
  userId            String?
  user              User?               @relation(fields: [userId], references: [id])
  companyId         String?
  company           Company?            @relation(fields: [companyId], references: [id])

  // Request context
  requestId         String?
  ipAddress         String?
  userAgent         String?

  // Error details
  errorCode         String?
  stackTrace        String?

  // Additional data
  metadata          Json?

  // Timestamps
  createdAt         DateTime            @default(now())

  @@index([level])
  @@index([source])
  @@index([category])
  @@index([userId])
  @@index([companyId])
  @@index([createdAt])
  @@map("system_logs")
}

// ============================================================================
// SUBSCRIPTION AND BILLING MODELS
// ============================================================================

model PricingPlan {
  id                String              @id @default(cuid())
  name              String
  description       String?

  // Pricing
  monthlyPrice      Decimal             @db.Decimal(10, 2)
  yearlyPrice       Decimal?            @db.Decimal(10, 2)
  currency          String              @default("USD")

  // Features and limits
  maxUsers          Int                 @default(5)
  maxCompanies      Int                 @default(1)
  maxCustomers      Int                 @default(100)
  maxQuotations     Int                 @default(50)
  maxInvoices       Int                 @default(50)
  maxContracts      Int                 @default(25)
  maxStorage        BigInt              @default(1073741824) // 1GB in bytes

  // Feature flags
  features          Json?               // JSON object with feature flags

  // Plan settings
  isActive          Boolean             @default(true)
  isPublic          Boolean             @default(true)
  sortOrder         Int                 @default(0)

  // Trial settings
  trialDays         Int                 @default(14)

  // Stripe integration
  stripeProductId   String?
  stripePriceId     String?
  stripeYearlyPriceId String?

  // Timestamps
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  // Relations
  subscriptions     Subscription[]

  @@map("pricing_plans")
}

// ============================================================================
// NOTIFICATION MODELS
// ============================================================================

model Notification {
  id                String              @id @default(cuid())
  title             String
  message           String
  type              NotificationType    @default(INFO)

  // Targeting
  userId            String?
  user              User?               @relation(fields: [userId], references: [id])
  companyId         String?
  company           Company?            @relation(fields: [companyId], references: [id])
  isGlobal          Boolean             @default(false)

  // Status
  isRead            Boolean             @default(false)
  readAt            DateTime?

  // Action
  actionUrl         String?
  actionText        String?

  // Metadata
  metadata          Json?

  // Timestamps
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([userId])
  @@index([companyId])
  @@index([isGlobal])
  @@index([isRead])
  @@index([createdAt])
  @@map("notifications")
}

// ============================================================================
// SYSTEM HEALTH AND MONITORING MODELS
// ============================================================================

// SystemHealth model temporarily removed to avoid database compatibility issues
// In production, this would track system health metrics over time

model SecurityAlert {
  id                String              @id @default(cuid())
  type              SecurityAlertType
  severity          AlertSeverity       @default(MEDIUM)
  title             String
  description       String

  // Context
  userId            String?
  user              User?               @relation(fields: [userId], references: [id])
  companyId         String?
  company           Company?            @relation(fields: [companyId], references: [id])

  // Request details
  ipAddress         String?
  userAgent         String?
  requestUrl        String?

  // Status
  status            AlertStatus         @default(OPEN)
  resolvedAt        DateTime?
  resolvedBy        String?

  // Additional data
  metadata          Json?

  // Timestamps
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([type])
  @@index([severity])
  @@index([status])
  @@index([userId])
  @@index([companyId])
  @@index([createdAt])
  @@map("security_alerts")
}

// ============================================================================
// FEATURE FLAGS AND CONFIGURATION
// ============================================================================

model FeatureFlag {
  id                String              @id @default(cuid())
  key               String              @unique
  name              String
  description       String?

  // Flag settings
  isEnabled         Boolean             @default(false)
  rolloutPercentage Float               @default(0) // 0-100

  // Targeting
  targetUsers       Json?               // Array of user IDs
  targetCompanies   Json?               // Array of company IDs
  targetRoles       Json?               // Array of user roles

  // Environment
  environment       String              @default("production")

  // Metadata
  metadata          Json?

  // Timestamps
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@map("feature_flags")
}

// ============================================================================
// ADDITIONAL ENUMS FOR SUPER ADMIN FEATURES
// ============================================================================

enum AuditSeverity {
  LOW
  INFO
  MEDIUM
  HIGH
  CRITICAL
}

enum LogLevel {
  DEBUG
  INFO
  WARN
  ERROR
  FATAL
}

enum NotificationType {
  INFO
  SUCCESS
  WARNING
  ERROR
  SECURITY
  SYSTEM
}

// SystemStatus enum temporarily removed to avoid database compatibility issues
// In production, this would define system health status values

enum SecurityAlertType {
  FAILED_LOGIN
  SUSPICIOUS_ACTIVITY
  DATA_BREACH
  UNAUTHORIZED_ACCESS
  MALWARE_DETECTED
  POLICY_VIOLATION
  ACCOUNT_COMPROMISE
  SYSTEM_INTRUSION
}

enum AlertSeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum AlertStatus {
  OPEN
  INVESTIGATING
  RESOLVED
  FALSE_POSITIVE
  IGNORED
}