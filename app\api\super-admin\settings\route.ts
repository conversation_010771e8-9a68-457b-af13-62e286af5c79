import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET /api/super-admin/settings - Get all system settings
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || session?.user?.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Super admin access required' }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category') || ''
    const search = searchParams.get('search') || ''
    const publicOnly = searchParams.get('publicOnly') === 'true'

    // Build where clause
    const where: any = {}
    
    if (category && category !== 'all') {
      where.category = category
    }

    if (search) {
      where.OR = [
        { key: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ]
    }

    if (publicOnly) {
      where.isPublic = true
    }

    const settings = await prisma.systemSettings.findMany({
      where,
      orderBy: [
        { category: 'asc' },
        { key: 'asc' }
      ]
    })

    // Group settings by category
    const groupedSettings = settings.reduce((acc, setting) => {
      if (!acc[setting.category]) {
        acc[setting.category] = []
      }
      acc[setting.category].push(setting)
      return acc
    }, {} as Record<string, typeof settings>)

    // Get categories with counts
    const categories = await prisma.systemSettings.groupBy({
      by: ['category'],
      _count: { id: true },
      orderBy: { category: 'asc' }
    })

    return NextResponse.json({
      settings: groupedSettings,
      categories: categories.map(cat => ({
        name: cat.category,
        count: cat._count.id
      })),
      total: settings.length
    })

  } catch (error) {
    console.error('Error fetching system settings:', error)
    return NextResponse.json(
      { error: 'Failed to fetch system settings' },
      { status: 500 }
    )
  }
}

// POST /api/super-admin/settings - Create new system setting
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || session?.user?.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Super admin access required' }, { status: 403 })
    }

    const body = await request.json()
    const {
      key,
      value,
      category,
      description,
      isPublic = false,
      isEditable = true
    } = body

    // Validate required fields
    if (!key || !category || value === undefined) {
      return NextResponse.json(
        { error: 'Key, category, and value are required' },
        { status: 400 }
      )
    }

    // Check if setting already exists
    const existingSetting = await prisma.systemSettings.findUnique({
      where: { key }
    })

    if (existingSetting) {
      return NextResponse.json(
        { error: 'Setting with this key already exists' },
        { status: 400 }
      )
    }

    // Create setting
    const setting = await prisma.systemSettings.create({
      data: {
        key,
        value,
        category,
        description,
        isPublic,
        isEditable
      }
    })

    // Log the action
    await prisma.auditLog.create({
      data: {
        action: 'SYSTEM_SETTING_CREATED',
        entityType: 'SystemSettings',
        entityId: setting.id,
        userId: session.user.id,
        userEmail: session.user.email,
        userRole: session.user.role,
        newValues: {
          key: setting.key,
          value: setting.value,
          category: setting.category,
          isPublic: setting.isPublic,
          isEditable: setting.isEditable
        },
        metadata: {
          createdByAdmin: true,
          adminId: session.user.id
        }
      }
    })

    return NextResponse.json({
      setting,
      message: 'System setting created successfully'
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating system setting:', error)
    return NextResponse.json(
      { error: 'Failed to create system setting' },
      { status: 500 }
    )
  }
}

// PUT /api/super-admin/settings - Bulk update settings
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || session?.user?.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Super admin access required' }, { status: 403 })
    }

    const body = await request.json()
    const { settings } = body

    if (!Array.isArray(settings)) {
      return NextResponse.json(
        { error: 'Settings must be an array' },
        { status: 400 }
      )
    }

    const updatedSettings = []
    const auditLogs = []

    for (const settingUpdate of settings) {
      const { id, value, description, isPublic, isEditable } = settingUpdate

      if (!id) continue

      // Get current setting for audit log
      const currentSetting = await prisma.systemSettings.findUnique({
        where: { id }
      })

      if (!currentSetting) continue
      if (!currentSetting.isEditable) continue // Skip non-editable settings

      // Update setting
      const updatedSetting = await prisma.systemSettings.update({
        where: { id },
        data: {
          ...(value !== undefined && { value }),
          ...(description !== undefined && { description }),
          ...(isPublic !== undefined && { isPublic }),
          ...(isEditable !== undefined && { isEditable })
        }
      })

      updatedSettings.push(updatedSetting)

      // Prepare audit log
      auditLogs.push({
        action: 'SYSTEM_SETTING_UPDATED',
        entityType: 'SystemSettings',
        entityId: id,
        userId: session.user.id,
        userEmail: session.user.email,
        userRole: session.user.role,
        oldValues: {
          value: currentSetting.value,
          description: currentSetting.description,
          isPublic: currentSetting.isPublic,
          isEditable: currentSetting.isEditable
        },
        newValues: {
          value: updatedSetting.value,
          description: updatedSetting.description,
          isPublic: updatedSetting.isPublic,
          isEditable: updatedSetting.isEditable
        },
        metadata: {
          updatedByAdmin: true,
          adminId: session.user.id,
          bulkUpdate: true
        }
      })
    }

    // Create audit logs
    if (auditLogs.length > 0) {
      await prisma.auditLog.createMany({
        data: auditLogs
      })
    }

    return NextResponse.json({
      updatedSettings,
      message: `Updated ${updatedSettings.length} settings successfully`
    })

  } catch (error) {
    console.error('Error updating system settings:', error)
    return NextResponse.json(
      { error: 'Failed to update system settings' },
      { status: 500 }
    )
  }
}

// DELETE /api/super-admin/settings - Delete system setting
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || session?.user?.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Super admin access required' }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const settingId = searchParams.get('id')

    if (!settingId) {
      return NextResponse.json(
        { error: 'Setting ID is required' },
        { status: 400 }
      )
    }

    // Get setting for audit log
    const setting = await prisma.systemSettings.findUnique({
      where: { id: settingId }
    })

    if (!setting) {
      return NextResponse.json({ error: 'Setting not found' }, { status: 404 })
    }

    if (!setting.isEditable) {
      return NextResponse.json(
        { error: 'This setting cannot be deleted' },
        { status: 400 }
      )
    }

    // Delete setting
    await prisma.systemSettings.delete({
      where: { id: settingId }
    })

    // Log the action
    await prisma.auditLog.create({
      data: {
        action: 'SYSTEM_SETTING_DELETED',
        entityType: 'SystemSettings',
        entityId: settingId,
        userId: session.user.id,
        userEmail: session.user.email,
        userRole: session.user.role,
        oldValues: {
          key: setting.key,
          value: setting.value,
          category: setting.category,
          description: setting.description
        },
        metadata: {
          deletedByAdmin: true,
          adminId: session.user.id
        }
      }
    })

    return NextResponse.json({
      message: 'System setting deleted successfully'
    })

  } catch (error) {
    console.error('Error deleting system setting:', error)
    return NextResponse.json(
      { error: 'Failed to delete system setting' },
      { status: 500 }
    )
  }
}
