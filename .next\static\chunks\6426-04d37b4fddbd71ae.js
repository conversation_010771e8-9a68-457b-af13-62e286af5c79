"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6426],{76426:function(e,t,s){s.d(t,{q:function(){return f}});var i=s(57437),a=s(2265),n=s(61865),r=s(37570),l=s(92160),o=s(85754),c=s(45179),d=s(49842),u=s(42706),m=s(9883),x=s(45367),h=s(92919),p=s(5925);let j=l.Ry({name:l.Z_().min(1,"Name is required"),description:l.Z_().optional(),quantity:l.Rx().min(1,"Quantity must be at least 1"),unitPrice:l.Rx().min(0,"Unit price must be positive"),itemId:l.Z_().optional()}),v=l.Ry({title:l.Z_().optional(),customerId:l.Z_().min(1,"Customer is required"),quotationId:l.Z_().optional(),status:l.Km(["DRAFT","SENT","PAID","OVERDUE","CANCELLED"]).default("DRAFT"),dueDate:l.Z_().optional(),terms:l.Z_().optional(),notes:l.Z_().optional(),items:l.IX(j).min(1,"At least one item is required"),taxRate:l.Rx().min(0).max(100).default(0)});function f(e){var t;let{isOpen:s,onClose:l,onSuccess:j,invoice:f,mode:y,preselectedCustomerId:g,preselectedQuotationId:b}=e,[N,I]=(0,a.useState)(!1),[q,w]=(0,a.useState)([]),[D,_]=(0,a.useState)([]),{register:R,handleSubmit:T,formState:{errors:F},reset:S,watch:C,control:E,setValue:P}=(0,n.cI)({resolver:(0,r.F)(v),defaultValues:f?{title:f.title||"",customerId:f.customerId||g||"",quotationId:f.quotationId||b||"",status:f.status||"DRAFT",dueDate:f.dueDate?new Date(f.dueDate).toISOString().split("T")[0]:(()=>{let e=new Date;return e.setDate(e.getDate()+30),e.toISOString().split("T")[0]})(),terms:f.terms||"",notes:f.notes||"",items:(null===(t=f.items)||void 0===t?void 0:t.map(e=>({name:e.name||e.description||"",description:e.description||"",quantity:e.quantity||1,unitPrice:e.unitPrice||0,itemId:e.itemId||""})))||[{name:"",description:"",quantity:1,unitPrice:0,itemId:""}],taxRate:f.taxRate||0}:{status:"DRAFT",customerId:g||"",quotationId:b||"",dueDate:(()=>{let e=new Date;return e.setDate(e.getDate()+30),e.toISOString().split("T")[0]})(),items:[{name:"",description:"",quantity:1,unitPrice:0,itemId:""}],taxRate:0}}),{fields:A,append:Z,remove:k}=(0,n.Dq)({control:E,name:"items"}),O=C("items"),U=C("taxRate");(0,a.useEffect)(()=>{let e=async()=>{try{let[e,t]=await Promise.all([fetch("/api/customers?limit=100"),fetch("/api/quotations?limit=100&status=ACCEPTED")]);if(e.ok){let t=await e.json();w(t.customers)}if(t.ok){let e=await t.json();_(e.quotations)}}catch(e){console.error("Error fetching data:",e)}};s&&e()},[s]);let z=C("quotationId");(0,a.useEffect)(()=>{z&&"create"===y&&(async()=>{try{let e=await fetch("/api/quotations/".concat(z));if(e.ok){let t=await e.json();t.items&&t.items.length>0&&(P("items",t.items.map(e=>({description:e.description,quantity:e.quantity,unitPrice:e.unitPrice,discount:e.discount,taxRate:e.taxRate}))),P("taxRate",t.taxRate),P("discountType",t.discountType),P("discountValue",t.discountValue),P("terms",t.terms||""))}}catch(e){console.error("Error loading quotation items:",e)}})()},[z,y,P]);let V=(()=>{let e=O.reduce((e,t)=>e+(t.quantity||0)*(t.unitPrice||0),0),t=e*(U||0)/100;return{subtotal:Math.round(100*e)/100,total:Math.round(100*(e+t))/100,taxAmount:Math.round(100*t)/100}})(),$=e=>{A.length>1&&k(e)},L=async e=>{I(!0);try{let t="create"===y?"/api/invoices":"/api/invoices/".concat(f.id),s=await fetch(t,{method:"create"===y?"POST":"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to save invoice")}p.toast.success("Invoice ".concat("create"===y?"created":"updated"," successfully")),S(),j(),l()}catch(e){p.toast.error(e instanceof Error?e.message:"An error occurred")}finally{I(!1)}},M=()=>{S(),l()};return(0,i.jsx)(u.Vq,{open:s,onOpenChange:M,children:(0,i.jsxs)(u.cZ,{className:"max-w-6xl max-h-[90vh] overflow-y-auto",children:[(0,i.jsxs)(u.fK,{children:[(0,i.jsx)(u.$N,{children:"create"===y?"Create New Invoice":"Edit Invoice"}),(0,i.jsx)(u.Be,{children:"create"===y?"Create a new invoice with items, pricing, and payment terms.":"Update the invoice information and items."})]}),(0,i.jsxs)("form",{onSubmit:T(L),className:"space-y-6",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"md:col-span-2",children:[(0,i.jsx)(d._,{htmlFor:"title",children:"Invoice Title"}),(0,i.jsx)(c.I,{id:"title",...R("title"),placeholder:"Invoice title (optional)"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(d._,{htmlFor:"customerId",children:"Customer *"}),(0,i.jsxs)("select",{id:"customerId",...R("customerId"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,i.jsx)("option",{value:"",children:"Select a customer"}),q.map(e=>(0,i.jsxs)("option",{value:e.id,children:[e.name," ",e.company&&"(".concat(e.company,")")]},e.id))]}),F.customerId&&(0,i.jsx)("p",{className:"text-sm text-red-600 mt-1",children:F.customerId.message})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(d._,{htmlFor:"quotationId",children:"Related Quotation"}),(0,i.jsxs)("select",{id:"quotationId",...R("quotationId"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,i.jsx)("option",{value:"",children:"Select a quotation (optional)"}),D.map(e=>(0,i.jsxs)("option",{value:e.id,children:[e.quotationNumber," - ",e.title]},e.id))]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(d._,{htmlFor:"status",children:"Status"}),(0,i.jsx)("select",{id:"status",...R("status"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[{value:"DRAFT",label:"Draft"},{value:"SENT",label:"Sent"},{value:"PAID",label:"Paid"},{value:"OVERDUE",label:"Overdue"},{value:"CANCELLED",label:"Cancelled"}].map(e=>(0,i.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(d._,{htmlFor:"dueDate",children:"Due Date"}),(0,i.jsx)(c.I,{id:"dueDate",type:"date",...R("dueDate")})]})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("h3",{className:"text-lg font-medium",children:"Items"}),(0,i.jsxs)(o.z,{type:"button",onClick:()=>{Z({name:"",description:"",quantity:1,unitPrice:0,itemId:""})},size:"sm",children:[(0,i.jsx)(m.Z,{className:"h-4 w-4 mr-2"}),"Add Item"]})]}),(0,i.jsx)("div",{className:"space-y-4",children:A.map((e,t)=>{var s,a,n,r;return(0,i.jsxs)("div",{className:"grid grid-cols-12 gap-2 items-end p-4 border rounded-lg",children:[(0,i.jsxs)("div",{className:"col-span-3",children:[(0,i.jsx)(d._,{htmlFor:"items.".concat(t,".name"),children:"Name *"}),(0,i.jsx)(c.I,{...R("items.".concat(t,".name")),placeholder:"Item name"}),(null===(a=F.items)||void 0===a?void 0:null===(s=a[t])||void 0===s?void 0:s.name)&&(0,i.jsx)("p",{className:"text-sm text-red-600 mt-1",children:null===(r=F.items[t])||void 0===r?void 0:null===(n=r.name)||void 0===n?void 0:n.message})]}),(0,i.jsxs)("div",{className:"col-span-3",children:[(0,i.jsx)(d._,{htmlFor:"items.".concat(t,".description"),children:"Description"}),(0,i.jsx)(c.I,{...R("items.".concat(t,".description")),placeholder:"Item description"})]}),(0,i.jsxs)("div",{className:"col-span-2",children:[(0,i.jsx)(d._,{htmlFor:"items.".concat(t,".quantity"),children:"Qty *"}),(0,i.jsx)(c.I,{type:"number",min:"1",...R("items.".concat(t,".quantity"),{valueAsNumber:!0}),placeholder:"1"})]}),(0,i.jsxs)("div",{className:"col-span-2",children:[(0,i.jsx)(d._,{htmlFor:"items.".concat(t,".unitPrice"),children:"Unit Price *"}),(0,i.jsx)(c.I,{type:"number",min:"0",step:"0.01",...R("items.".concat(t,".unitPrice"),{valueAsNumber:!0}),placeholder:"0.00"})]}),(0,i.jsxs)("div",{className:"col-span-1",children:[(0,i.jsx)(d._,{children:"Total"}),(0,i.jsxs)("div",{className:"px-3 py-2 bg-gray-50 rounded-md text-sm",children:["$",(()=>{let e=O[t];return e?((e.quantity||0)*(e.unitPrice||0)).toFixed(2):"0.00"})()]})]}),(0,i.jsx)("div",{className:"col-span-1",children:(0,i.jsx)(o.z,{type:"button",variant:"destructive",size:"sm",onClick:()=>$(t),disabled:1===A.length,children:(0,i.jsx)(x.Z,{className:"h-4 w-4"})})})]},e.id)})})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("h3",{className:"text-lg font-medium",children:"Tax Settings"}),(0,i.jsxs)("div",{children:[(0,i.jsx)(d._,{htmlFor:"taxRate",children:"Tax Rate (%)"}),(0,i.jsx)(c.I,{type:"number",min:"0",max:"100",step:"0.01",...R("taxRate",{valueAsNumber:!0}),placeholder:"0"})]})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("h3",{className:"text-lg font-medium flex items-center",children:[(0,i.jsx)(h.Z,{className:"h-5 w-5 mr-2"}),"Totals"]}),(0,i.jsxs)("div",{className:"space-y-2 p-4 bg-gray-50 rounded-lg",children:[(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{children:"Subtotal:"}),(0,i.jsxs)("span",{children:["$",V.subtotal.toFixed(2)]})]}),V.taxAmount>0&&(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{children:"Tax:"}),(0,i.jsxs)("span",{children:["$",V.taxAmount.toFixed(2)]})]}),(0,i.jsxs)("div",{className:"flex justify-between font-bold text-lg border-t pt-2",children:[(0,i.jsx)("span",{children:"Total:"}),(0,i.jsxs)("span",{children:["$",V.total.toFixed(2)]})]})]})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(d._,{htmlFor:"terms",children:"Terms & Conditions"}),(0,i.jsx)("textarea",{id:"terms",...R("terms"),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Payment terms, late fees, etc..."})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(d._,{htmlFor:"notes",children:"Internal Notes"}),(0,i.jsx)("textarea",{id:"notes",...R("notes"),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Internal notes (not visible to customer)..."})]})]}),(0,i.jsxs)(u.cN,{children:[(0,i.jsx)(o.z,{type:"button",variant:"outline",onClick:M,children:"Cancel"}),(0,i.jsx)(o.z,{type:"submit",disabled:N,children:N?"Saving...":"create"===y?"Create Invoice":"Update Invoice"})]})]})]})})}}}]);