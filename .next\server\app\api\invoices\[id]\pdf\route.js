"use strict";(()=>{var e={};e.id=3083,e.ids=[3083],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},24430:(e,t,o)=>{o.r(t),o.d(t,{headerHooks:()=>x,originalPathname:()=>v,patchFetch:()=>h,requestAsyncStorage:()=>m,routeModule:()=>u,serverHooks:()=>g,staticGenerationAsyncStorage:()=>b,staticGenerationBailout:()=>f});var a={};o.r(a),o.d(a,{GET:()=>p});var i=o(95419),r=o(69108),s=o(99678),n=o(78070),d=o(81355),l=o(3205),c=o(9108);async function p(e,{params:t}){try{let e=await (0,d.getServerSession)(l.L);if(!e?.user?.id||!e?.user?.companyId)return n.Z.json({error:"Unauthorized"},{status:401});let o=await c._.invoice.findFirst({where:{id:t.id,companyId:e.user.companyId},include:{customer:{select:{id:!0,name:!0,email:!0,phone:!0,company:!0,address:!0,city:!0,state:!0,country:!0,postalCode:!0}},items:{orderBy:{createdAt:"asc"}},createdBy:{select:{name:!0,email:!0}},company:{select:{name:!0,email:!0,phone:!0,address:!0,city:!0,state:!0,country:!0,postalCode:!0,website:!0,logo:!0}},transactions:{where:{type:"PAYMENT"},orderBy:{createdAt:"desc"}}}});if(!o)return n.Z.json({error:"Invoice not found"},{status:404});let a=o.items.map(e=>{let t=Number(e.quantity)*Number(e.unitPrice),o=t*Number(e.discount)/100,a=t-o,i=a*Number(e.taxRate)/100;return{...e,lineTotal:Math.round(100*t)/100,discountAmount:Math.round(100*o)/100,subtotal:Math.round(100*a)/100,taxAmount:Math.round(100*i)/100,total:Math.round(100*(a+i))/100}}),i=a.reduce((e,t)=>e+t.subtotal,0),r=a.reduce((e,t)=>e+t.taxAmount,0),s=Number(o.total),p=Number(o.paidAmount),u=o.transactions.map(e=>({id:e.id,amount:Number(e.amount),date:e.date,method:e.method,reference:e.reference})),m=function(e){let{invoice:t,customer:o,company:a,items:i,payments:r,totals:s}=e;return`
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice ${t.invoiceNumber}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 40px;
            border-bottom: 3px solid #e5e7eb;
            padding-bottom: 20px;
        }
        .company-info {
            flex: 1;
        }
        .company-name {
            font-size: 28px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 10px;
        }
        .invoice-info {
            text-align: right;
            flex: 1;
        }
        .invoice-title {
            font-size: 32px;
            font-weight: bold;
            color: #dc2626;
            margin-bottom: 10px;
        }
        .invoice-number {
            font-size: 20px;
            color: #6b7280;
            margin-bottom: 5px;
        }
        .customer-section {
            margin: 30px 0;
            padding: 20px;
            background-color: #f9fafb;
            border-radius: 8px;
            border-left: 4px solid #3b82f6;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 15px;
            border-bottom: 1px solid #d1d5db;
            padding-bottom: 5px;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .items-table th,
        .items-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        .items-table th {
            background-color: #f3f4f6;
            font-weight: bold;
            color: #374151;
            border-bottom: 2px solid #d1d5db;
        }
        .items-table .number {
            text-align: right;
        }
        .totals-section {
            margin-top: 30px;
            padding: 20px;
            background-color: #f9fafb;
            border-radius: 8px;
        }
        .totals-table {
            width: 100%;
            max-width: 400px;
            margin-left: auto;
        }
        .totals-table td {
            padding: 8px 12px;
            border: none;
        }
        .totals-table .label {
            text-align: right;
            font-weight: 500;
        }
        .totals-table .amount {
            text-align: right;
            font-weight: bold;
        }
        .grand-total {
            border-top: 2px solid #374151;
            font-size: 18px;
            color: #1f2937;
        }
        .balance-due {
            background-color: #fee2e2;
            border: 2px solid #fca5a5;
            font-size: 20px;
            color: #dc2626;
        }
        .paid-in-full {
            background-color: #d1fae5;
            border: 2px solid #86efac;
            font-size: 20px;
            color: #065f46;
        }
        .payments-section {
            margin-top: 30px;
            padding: 20px;
            background-color: #f0f9ff;
            border-radius: 8px;
            border-left: 4px solid #0ea5e9;
        }
        .payment-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #e0f2fe;
        }
        .payment-item:last-child {
            border-bottom: none;
        }
        .terms-section {
            margin-top: 40px;
            padding: 20px;
            background-color: #fef3c7;
            border-radius: 8px;
            border-left: 4px solid #f59e0b;
        }
        .notes-section {
            margin-top: 20px;
            padding: 20px;
            background-color: #eff6ff;
            border-radius: 8px;
            border-left: 4px solid #3b82f6;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            color: #6b7280;
            font-size: 14px;
            border-top: 1px solid #e5e7eb;
            padding-top: 20px;
        }
        .status-badge {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-draft { background-color: #f3f4f6; color: #374151; }
        .status-sent { background-color: #dbeafe; color: #1d4ed8; }
        .status-paid { background-color: #d1fae5; color: #065f46; }
        .status-overdue { background-color: #fee2e2; color: #dc2626; }
        .status-cancelled { background-color: #fed7aa; color: #ea580c; }
        .overdue-notice {
            background-color: #fee2e2;
            border: 2px solid #fca5a5;
            color: #dc2626;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-info">
            <div class="company-name">${a.name}</div>
            <div>${a.address||""}</div>
            <div>${a.city||""}, ${a.state||""} ${a.postalCode||""}</div>
            <div>${a.country||""}</div>
            <div>Email: ${a.email||""}</div>
            <div>Phone: ${a.phone||""}</div>
            ${a.website?`<div>Website: ${a.website}</div>`:""}
        </div>
        <div class="invoice-info">
            <div class="invoice-title">INVOICE</div>
            <div class="invoice-number">${t.invoiceNumber}</div>
            <div>Issue Date: ${new Date(t.issueDate||t.createdAt).toLocaleDateString()}</div>
            ${t.dueDate?`<div>Due Date: ${new Date(t.dueDate).toLocaleDateString()}</div>`:""}
            <div class="status-badge status-${t.status.toLowerCase()}">${t.status}</div>
        </div>
    </div>

    ${"OVERDUE"===t.status?`
    <div class="overdue-notice">
        ⚠️ THIS INVOICE IS OVERDUE ⚠️<br>
        Please remit payment immediately to avoid additional charges.
    </div>
    `:""}

    <div class="customer-section">
        <div class="section-title">Bill To:</div>
        <div><strong>${o.name}</strong></div>
        ${o.company?`<div>${o.company}</div>`:""}
        ${o.address?`<div>${o.address}</div>`:""}
        ${o.city||o.state||o.postalCode?`<div>${o.city||""}, ${o.state||""} ${o.postalCode||""}</div>`:""}
        ${o.country?`<div>${o.country}</div>`:""}
        ${o.email?`<div>Email: ${o.email}</div>`:""}
        ${o.phone?`<div>Phone: ${o.phone}</div>`:""}
    </div>

    ${t.title?`
    <div class="section-title">Project: ${t.title}</div>
    `:""}

    <table class="items-table">
        <thead>
            <tr>
                <th>Description</th>
                <th class="number">Qty</th>
                <th class="number">Unit Price</th>
                <th class="number">Discount</th>
                <th class="number">Tax</th>
                <th class="number">Total</th>
            </tr>
        </thead>
        <tbody>
            ${i.map(e=>`
                <tr>
                    <td>
                        <strong>${e.name}</strong>
                        ${e.description?`<br><small>${e.description}</small>`:""}
                    </td>
                    <td class="number">${Number(e.quantity)}</td>
                    <td class="number">$${Number(e.unitPrice).toFixed(2)}</td>
                    <td class="number">${Number(e.discount)}%</td>
                    <td class="number">${Number(e.taxRate)}%</td>
                    <td class="number">$${e.total.toFixed(2)}</td>
                </tr>
            `).join("")}
        </tbody>
    </table>

    <div class="totals-section">
        <table class="totals-table">
            <tr>
                <td class="label">Subtotal:</td>
                <td class="amount">$${s.subtotal.toFixed(2)}</td>
            </tr>
            <tr>
                <td class="label">Tax:</td>
                <td class="amount">$${s.totalTax.toFixed(2)}</td>
            </tr>
            <tr class="grand-total">
                <td class="label">Total:</td>
                <td class="amount">$${s.grandTotal.toFixed(2)}</td>
            </tr>
            ${s.paidAmount>0?`
            <tr>
                <td class="label">Paid:</td>
                <td class="amount">-$${s.paidAmount.toFixed(2)}</td>
            </tr>
            `:""}
            <tr class="${s.balanceDue<=0?"paid-in-full":"balance-due"}">
                <td class="label">${s.balanceDue<=0?"PAID IN FULL":"Balance Due"}:</td>
                <td class="amount">$${Math.max(0,s.balanceDue).toFixed(2)}</td>
            </tr>
        </table>
    </div>

    ${r.length>0?`
    <div class="payments-section">
        <div class="section-title">Payment History:</div>
        ${r.map(e=>`
            <div class="payment-item">
                <div>
                    <strong>${new Date(e.date).toLocaleDateString()}</strong>
                    <span> - ${e.method}</span>
                    ${e.reference?`<span> (${e.reference})</span>`:""}
                </div>
                <div><strong>$${e.amount.toFixed(2)}</strong></div>
            </div>
        `).join("")}
    </div>
    `:""}

    ${t.terms?`
    <div class="terms-section">
        <div class="section-title">Terms & Conditions:</div>
        <p>${t.terms}</p>
    </div>
    `:""}

    ${t.notes?`
    <div class="notes-section">
        <div class="section-title">Notes:</div>
        <p>${t.notes}</p>
    </div>
    `:""}

    <div class="footer">
        <p>Thank you for your business!</p>
        <p>Generated on ${new Date().toLocaleDateString()} by ${t.createdBy.name||t.createdBy.email}</p>
        ${s.balanceDue>0?`
        <p><strong>Please remit payment by the due date to avoid late fees.</strong></p>
        `:""}
    </div>
</body>
</html>
  `}({invoice:o,customer:o.customer,company:o.company,items:a,payments:u,totals:{subtotal:Math.round(100*i)/100,totalTax:Math.round(100*r)/100,grandTotal:Math.round(100*s)/100,paidAmount:Math.round(100*p)/100,balanceDue:Math.round(100*(s-p))/100}});return new Response(m,{headers:{"Content-Type":"text/html","Content-Disposition":`inline; filename="invoice-${o.invoiceNumber}.html"`}})}catch(e){return console.error("Error generating invoice PDF:",e),n.Z.json({error:"Failed to generate PDF"},{status:500})}}let u=new i.AppRouteRouteModule({definition:{kind:r.x.APP_ROUTE,page:"/api/invoices/[id]/pdf/route",pathname:"/api/invoices/[id]/pdf",filename:"route",bundlePath:"app/api/invoices/[id]/pdf/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\invoices\\[id]\\pdf\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:m,staticGenerationAsyncStorage:b,serverHooks:g,headerHooks:x,staticGenerationBailout:f}=u,v="/api/invoices/[id]/pdf/route";function h(){return(0,s.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:b})}},3205:(e,t,o)=>{o.d(t,{L:()=>l});var a=o(86485),i=o(10375),r=o(50694),s=o(6521),n=o.n(s),d=o(9108);let l={providers:[(0,a.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await d._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),o=t?.companyId;if(!o&&t){let e=await d._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(o=e?.id)&&await d._.user.update({where:{id:t.id},data:{companyId:o}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await n().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await d._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:o}}catch(e){return console.error("Authentication error:",e),null}}}),(0,i.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,r.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,o)=>{o.d(t,{_:()=>i});let a=require("@prisma/client"),i=globalThis.prisma??new a.PrismaClient}};var t=require("../../../../../webpack-runtime.js");t.C(e);var o=e=>t(t.s=e),a=t.X(0,[1638,6206,6521,2455,4520],()=>o(24430));module.exports=a})();