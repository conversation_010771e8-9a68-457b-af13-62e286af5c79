'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { redirect } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { 
  Palette, 
  Save, 
  RefreshCw, 
  Upload,
  Eye,
  Download,
  Image as ImageIcon,
  Type,
  Monitor,
  Smartphone,
  Tablet
} from 'lucide-react'
import { toast } from 'react-hot-toast'

interface BrandingConfig {
  // Logo & Images
  logoUrl: string
  logoUrlDark: string
  faviconUrl: string
  loginBackgroundUrl: string
  
  // Colors
  primaryColor: string
  secondaryColor: string
  accentColor: string
  backgroundColor: string
  textColor: string
  
  // Typography
  fontFamily: string
  headingFont: string
  fontSize: string
  
  // Theme
  theme: 'light' | 'dark' | 'auto'
  borderRadius: string
  
  // Custom CSS
  customCss: string
  
  // Footer
  footerText: string
  copyrightText: string
  
  // Social Links
  socialLinks: {
    website: string
    twitter: string
    linkedin: string
    facebook: string
    instagram: string
    youtube: string
  }
}

export default function BrandingPage() {
  const { data: session, status } = useSession()
  const [branding, setBranding] = useState<BrandingConfig>({
    logoUrl: '',
    logoUrlDark: '',
    faviconUrl: '',
    loginBackgroundUrl: '',
    primaryColor: '#3b82f6',
    secondaryColor: '#64748b',
    accentColor: '#10b981',
    backgroundColor: '#ffffff',
    textColor: '#1f2937',
    fontFamily: 'Inter',
    headingFont: 'Inter',
    fontSize: '14px',
    theme: 'light',
    borderRadius: '8px',
    customCss: '',
    footerText: 'Built with ❤️ by Your Company',
    copyrightText: '© 2024 Your Company. All rights reserved.',
    socialLinks: {
      website: '',
      twitter: '',
      linkedin: '',
      facebook: '',
      instagram: '',
      youtube: ''
    }
  })
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [previewMode, setPreviewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop')

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (status === 'unauthenticated') {
    redirect('/auth/signin')
  }

  if (session?.user?.role !== 'SUPER_ADMIN') {
    redirect('/dashboard')
  }

  const fetchBranding = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/super-admin/branding')
      const data = await response.json()
      
      if (data.success) {
        setBranding({ ...branding, ...data.branding })
      }
    } catch (error) {
      console.error('Error fetching branding:', error)
      toast.error('Failed to load branding configuration')
    } finally {
      setLoading(false)
    }
  }

  const saveBranding = async () => {
    try {
      setSaving(true)
      const response = await fetch('/api/super-admin/branding', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(branding)
      })

      const data = await response.json()
      
      if (data.success) {
        toast.success('Branding configuration saved successfully')
      } else {
        toast.error(data.error || 'Failed to save branding configuration')
      }
    } catch (error) {
      console.error('Error saving branding:', error)
      toast.error('Failed to save branding configuration')
    } finally {
      setSaving(false)
    }
  }

  useEffect(() => {
    fetchBranding()
  }, [])

  const updateBranding = (key: keyof BrandingConfig, value: any) => {
    setBranding(prev => ({ ...prev, [key]: value }))
  }

  const updateSocialLinks = (platform: keyof BrandingConfig['socialLinks'], value: string) => {
    setBranding(prev => ({
      ...prev,
      socialLinks: { ...prev.socialLinks, [platform]: value }
    }))
  }

  const handleFileUpload = async (file: File, type: 'logo' | 'logoDark' | 'favicon' | 'background') => {
    try {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('type', type)

      const response = await fetch('/api/super-admin/branding/upload', {
        method: 'POST',
        body: formData
      })

      const data = await response.json()
      
      if (data.success) {
        const urlKey = type === 'logo' ? 'logoUrl' : 
                      type === 'logoDark' ? 'logoUrlDark' :
                      type === 'favicon' ? 'faviconUrl' : 'loginBackgroundUrl'
        updateBranding(urlKey, data.url)
        toast.success('File uploaded successfully')
      } else {
        toast.error(data.error || 'Failed to upload file')
      }
    } catch (error) {
      console.error('Error uploading file:', error)
      toast.error('Failed to upload file')
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <div className="flex items-center space-x-3">
            <Palette className="h-8 w-8 text-purple-600" />
            <h1 className="text-3xl font-bold text-gray-900">Branding & Customization</h1>
          </div>
          <p className="text-gray-500 mt-1">Customize your application's look and feel</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={fetchBranding} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline">
            <Eye className="h-4 w-4 mr-2" />
            Preview
          </Button>
          <Button onClick={saveBranding} disabled={saving}>
            <Save className={`h-4 w-4 mr-2 ${saving ? 'animate-spin' : ''}`} />
            Save Changes
          </Button>
        </div>
      </div>

      {/* Preview Mode Selector */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Monitor className="h-5 w-5 mr-2" />
            Preview Mode
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2">
            <Button
              variant={previewMode === 'desktop' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setPreviewMode('desktop')}
            >
              <Monitor className="h-4 w-4 mr-2" />
              Desktop
            </Button>
            <Button
              variant={previewMode === 'tablet' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setPreviewMode('tablet')}
            >
              <Tablet className="h-4 w-4 mr-2" />
              Tablet
            </Button>
            <Button
              variant={previewMode === 'mobile' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setPreviewMode('mobile')}
            >
              <Smartphone className="h-4 w-4 mr-2" />
              Mobile
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Branding Configuration */}
      <Tabs defaultValue="logos" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="logos">Logos & Images</TabsTrigger>
          <TabsTrigger value="colors">Colors</TabsTrigger>
          <TabsTrigger value="typography">Typography</TabsTrigger>
          <TabsTrigger value="theme">Theme</TabsTrigger>
          <TabsTrigger value="footer">Footer & Social</TabsTrigger>
        </TabsList>

        {/* Logos & Images */}
        <TabsContent value="logos">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <ImageIcon className="h-5 w-5 mr-2" />
                Logos & Images
              </CardTitle>
              <CardDescription>
                Upload and manage your brand assets
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Main Logo */}
                <div className="space-y-4">
                  <Label>Main Logo (Light Theme)</Label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    {branding.logoUrl ? (
                      <div className="space-y-2">
                        <img src={branding.logoUrl} alt="Logo" className="max-h-16 mx-auto" />
                        <p className="text-sm text-gray-500">Current logo</p>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        <ImageIcon className="h-12 w-12 mx-auto text-gray-400" />
                        <p className="text-sm text-gray-500">No logo uploaded</p>
                      </div>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-2"
                      onClick={() => document.getElementById('logo-upload')?.click()}
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Upload Logo
                    </Button>
                    <input
                      id="logo-upload"
                      type="file"
                      accept="image/*"
                      className="hidden"
                      onChange={(e) => {
                        const file = e.target.files?.[0]
                        if (file) handleFileUpload(file, 'logo')
                      }}
                    />
                  </div>
                </div>

                {/* Dark Logo */}
                <div className="space-y-4">
                  <Label>Logo (Dark Theme)</Label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center bg-gray-900">
                    {branding.logoUrlDark ? (
                      <div className="space-y-2">
                        <img src={branding.logoUrlDark} alt="Dark Logo" className="max-h-16 mx-auto" />
                        <p className="text-sm text-gray-400">Current dark logo</p>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        <ImageIcon className="h-12 w-12 mx-auto text-gray-400" />
                        <p className="text-sm text-gray-400">No dark logo uploaded</p>
                      </div>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-2"
                      onClick={() => document.getElementById('logo-dark-upload')?.click()}
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Upload Dark Logo
                    </Button>
                    <input
                      id="logo-dark-upload"
                      type="file"
                      accept="image/*"
                      className="hidden"
                      onChange={(e) => {
                        const file = e.target.files?.[0]
                        if (file) handleFileUpload(file, 'logoDark')
                      }}
                    />
                  </div>
                </div>

                {/* Favicon */}
                <div className="space-y-4">
                  <Label>Favicon</Label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    {branding.faviconUrl ? (
                      <div className="space-y-2">
                        <img src={branding.faviconUrl} alt="Favicon" className="w-8 h-8 mx-auto" />
                        <p className="text-sm text-gray-500">Current favicon</p>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        <ImageIcon className="h-8 w-8 mx-auto text-gray-400" />
                        <p className="text-sm text-gray-500">No favicon uploaded</p>
                      </div>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-2"
                      onClick={() => document.getElementById('favicon-upload')?.click()}
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Upload Favicon
                    </Button>
                    <input
                      id="favicon-upload"
                      type="file"
                      accept="image/x-icon,image/png"
                      className="hidden"
                      onChange={(e) => {
                        const file = e.target.files?.[0]
                        if (file) handleFileUpload(file, 'favicon')
                      }}
                    />
                  </div>
                  <p className="text-xs text-gray-500">Recommended: 32x32px ICO or PNG</p>
                </div>

                {/* Login Background */}
                <div className="space-y-4">
                  <Label>Login Background</Label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    {branding.loginBackgroundUrl ? (
                      <div className="space-y-2">
                        <img src={branding.loginBackgroundUrl} alt="Background" className="max-h-16 mx-auto rounded" />
                        <p className="text-sm text-gray-500">Current background</p>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        <ImageIcon className="h-12 w-12 mx-auto text-gray-400" />
                        <p className="text-sm text-gray-500">No background uploaded</p>
                      </div>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-2"
                      onClick={() => document.getElementById('background-upload')?.click()}
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Upload Background
                    </Button>
                    <input
                      id="background-upload"
                      type="file"
                      accept="image/*"
                      className="hidden"
                      onChange={(e) => {
                        const file = e.target.files?.[0]
                        if (file) handleFileUpload(file, 'background')
                      }}
                    />
                  </div>
                  <p className="text-xs text-gray-500">Recommended: 1920x1080px or higher</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Colors */}
        <TabsContent value="colors">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Palette className="h-5 w-5 mr-2" />
                Color Scheme
              </CardTitle>
              <CardDescription>
                Customize your application's color palette
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="primaryColor">Primary Color</Label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="color"
                      id="primaryColor"
                      value={branding.primaryColor}
                      onChange={(e) => updateBranding('primaryColor', e.target.value)}
                      className="w-12 h-10 rounded border border-gray-300"
                    />
                    <Input
                      value={branding.primaryColor}
                      onChange={(e) => updateBranding('primaryColor', e.target.value)}
                      placeholder="#3b82f6"
                    />
                  </div>
                  <p className="text-xs text-gray-500">Main brand color for buttons, links, etc.</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="secondaryColor">Secondary Color</Label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="color"
                      id="secondaryColor"
                      value={branding.secondaryColor}
                      onChange={(e) => updateBranding('secondaryColor', e.target.value)}
                      className="w-12 h-10 rounded border border-gray-300"
                    />
                    <Input
                      value={branding.secondaryColor}
                      onChange={(e) => updateBranding('secondaryColor', e.target.value)}
                      placeholder="#64748b"
                    />
                  </div>
                  <p className="text-xs text-gray-500">Secondary elements and borders</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="accentColor">Accent Color</Label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="color"
                      id="accentColor"
                      value={branding.accentColor}
                      onChange={(e) => updateBranding('accentColor', e.target.value)}
                      className="w-12 h-10 rounded border border-gray-300"
                    />
                    <Input
                      value={branding.accentColor}
                      onChange={(e) => updateBranding('accentColor', e.target.value)}
                      placeholder="#10b981"
                    />
                  </div>
                  <p className="text-xs text-gray-500">Success states and highlights</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="backgroundColor">Background Color</Label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="color"
                      id="backgroundColor"
                      value={branding.backgroundColor}
                      onChange={(e) => updateBranding('backgroundColor', e.target.value)}
                      className="w-12 h-10 rounded border border-gray-300"
                    />
                    <Input
                      value={branding.backgroundColor}
                      onChange={(e) => updateBranding('backgroundColor', e.target.value)}
                      placeholder="#ffffff"
                    />
                  </div>
                  <p className="text-xs text-gray-500">Main background color</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="textColor">Text Color</Label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="color"
                      id="textColor"
                      value={branding.textColor}
                      onChange={(e) => updateBranding('textColor', e.target.value)}
                      className="w-12 h-10 rounded border border-gray-300"
                    />
                    <Input
                      value={branding.textColor}
                      onChange={(e) => updateBranding('textColor', e.target.value)}
                      placeholder="#1f2937"
                    />
                  </div>
                  <p className="text-xs text-gray-500">Primary text color</p>
                </div>
              </div>

              {/* Color Preview */}
              <div className="mt-8">
                <Label className="text-base font-medium">Color Preview</Label>
                <div className="mt-4 p-6 rounded-lg border" style={{ backgroundColor: branding.backgroundColor }}>
                  <div className="space-y-4">
                    <h3 style={{ color: branding.textColor }} className="text-xl font-semibold">
                      Sample Heading
                    </h3>
                    <p style={{ color: branding.textColor }} className="opacity-80">
                      This is how your text will look with the selected colors.
                    </p>
                    <div className="flex items-center space-x-3">
                      <button
                        style={{ backgroundColor: branding.primaryColor }}
                        className="px-4 py-2 text-white rounded-md"
                      >
                        Primary Button
                      </button>
                      <button
                        style={{
                          backgroundColor: 'transparent',
                          color: branding.secondaryColor,
                          borderColor: branding.secondaryColor
                        }}
                        className="px-4 py-2 border rounded-md"
                      >
                        Secondary Button
                      </button>
                      <span
                        style={{ color: branding.accentColor }}
                        className="font-medium"
                      >
                        Accent Text
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Typography */}
        <TabsContent value="typography">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Type className="h-5 w-5 mr-2" />
                Typography
              </CardTitle>
              <CardDescription>
                Configure fonts and text styling
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="fontFamily">Body Font</Label>
                  <select
                    id="fontFamily"
                    value={branding.fontFamily}
                    onChange={(e) => updateBranding('fontFamily', e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md"
                  >
                    <option value="Inter">Inter</option>
                    <option value="Roboto">Roboto</option>
                    <option value="Open Sans">Open Sans</option>
                    <option value="Lato">Lato</option>
                    <option value="Poppins">Poppins</option>
                    <option value="Montserrat">Montserrat</option>
                    <option value="Source Sans Pro">Source Sans Pro</option>
                    <option value="system-ui">System UI</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="headingFont">Heading Font</Label>
                  <select
                    id="headingFont"
                    value={branding.headingFont}
                    onChange={(e) => updateBranding('headingFont', e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md"
                  >
                    <option value="Inter">Inter</option>
                    <option value="Roboto">Roboto</option>
                    <option value="Open Sans">Open Sans</option>
                    <option value="Lato">Lato</option>
                    <option value="Poppins">Poppins</option>
                    <option value="Montserrat">Montserrat</option>
                    <option value="Playfair Display">Playfair Display</option>
                    <option value="Merriweather">Merriweather</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="fontSize">Base Font Size</Label>
                  <select
                    id="fontSize"
                    value={branding.fontSize}
                    onChange={(e) => updateBranding('fontSize', e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md"
                  >
                    <option value="12px">12px - Small</option>
                    <option value="14px">14px - Default</option>
                    <option value="16px">16px - Large</option>
                    <option value="18px">18px - Extra Large</option>
                  </select>
                </div>
              </div>

              {/* Typography Preview */}
              <div className="mt-8">
                <Label className="text-base font-medium">Typography Preview</Label>
                <div className="mt-4 p-6 rounded-lg border bg-white">
                  <div className="space-y-4">
                    <h1
                      style={{
                        fontFamily: branding.headingFont,
                        fontSize: '2rem',
                        color: branding.textColor
                      }}
                      className="font-bold"
                    >
                      Main Heading (H1)
                    </h1>
                    <h2
                      style={{
                        fontFamily: branding.headingFont,
                        fontSize: '1.5rem',
                        color: branding.textColor
                      }}
                      className="font-semibold"
                    >
                      Section Heading (H2)
                    </h2>
                    <h3
                      style={{
                        fontFamily: branding.headingFont,
                        fontSize: '1.25rem',
                        color: branding.textColor
                      }}
                      className="font-medium"
                    >
                      Subsection Heading (H3)
                    </h3>
                    <p
                      style={{
                        fontFamily: branding.fontFamily,
                        fontSize: branding.fontSize,
                        color: branding.textColor
                      }}
                    >
                      This is body text using the selected font family and size. It demonstrates how regular paragraph text will appear throughout your application.
                    </p>
                    <p
                      style={{
                        fontFamily: branding.fontFamily,
                        fontSize: branding.fontSize,
                        color: branding.secondaryColor
                      }}
                      className="text-sm"
                    >
                      This is secondary text, often used for descriptions and less important information.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Theme */}
        <TabsContent value="theme">
          <Card>
            <CardHeader>
              <CardTitle>Theme Settings</CardTitle>
              <CardDescription>
                Configure theme preferences and custom styling
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="theme">Default Theme</Label>
                  <select
                    id="theme"
                    value={branding.theme}
                    onChange={(e) => updateBranding('theme', e.target.value as 'light' | 'dark' | 'auto')}
                    className="w-full p-2 border border-gray-300 rounded-md"
                  >
                    <option value="light">Light</option>
                    <option value="dark">Dark</option>
                    <option value="auto">Auto (System Preference)</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="borderRadius">Border Radius</Label>
                  <select
                    id="borderRadius"
                    value={branding.borderRadius}
                    onChange={(e) => updateBranding('borderRadius', e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md"
                  >
                    <option value="0px">Sharp (0px)</option>
                    <option value="4px">Small (4px)</option>
                    <option value="8px">Medium (8px)</option>
                    <option value="12px">Large (12px)</option>
                    <option value="16px">Extra Large (16px)</option>
                    <option value="9999px">Pill (9999px)</option>
                  </select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="customCss">Custom CSS</Label>
                <Textarea
                  id="customCss"
                  value={branding.customCss}
                  onChange={(e) => updateBranding('customCss', e.target.value)}
                  placeholder="/* Add your custom CSS here */
.custom-class {
  /* Your styles */
}"
                  rows={10}
                  className="font-mono text-sm"
                />
                <p className="text-xs text-gray-500">
                  Add custom CSS to override default styles. Use with caution as this can affect application functionality.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Footer & Social */}
        <TabsContent value="footer">
          <Card>
            <CardHeader>
              <CardTitle>Footer & Social Links</CardTitle>
              <CardDescription>
                Configure footer content and social media links
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="footerText">Footer Text</Label>
                  <Input
                    id="footerText"
                    value={branding.footerText}
                    onChange={(e) => updateBranding('footerText', e.target.value)}
                    placeholder="Built with ❤️ by Your Company"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="copyrightText">Copyright Text</Label>
                  <Input
                    id="copyrightText"
                    value={branding.copyrightText}
                    onChange={(e) => updateBranding('copyrightText', e.target.value)}
                    placeholder="© 2024 Your Company. All rights reserved."
                  />
                </div>
              </div>

              <div className="space-y-4">
                <Label className="text-base font-medium">Social Media Links</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="website">Website</Label>
                    <Input
                      id="website"
                      value={branding.socialLinks.website}
                      onChange={(e) => updateSocialLinks('website', e.target.value)}
                      placeholder="https://yourcompany.com"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="twitter">Twitter</Label>
                    <Input
                      id="twitter"
                      value={branding.socialLinks.twitter}
                      onChange={(e) => updateSocialLinks('twitter', e.target.value)}
                      placeholder="https://twitter.com/yourcompany"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="linkedin">LinkedIn</Label>
                    <Input
                      id="linkedin"
                      value={branding.socialLinks.linkedin}
                      onChange={(e) => updateSocialLinks('linkedin', e.target.value)}
                      placeholder="https://linkedin.com/company/yourcompany"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="facebook">Facebook</Label>
                    <Input
                      id="facebook"
                      value={branding.socialLinks.facebook}
                      onChange={(e) => updateSocialLinks('facebook', e.target.value)}
                      placeholder="https://facebook.com/yourcompany"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="instagram">Instagram</Label>
                    <Input
                      id="instagram"
                      value={branding.socialLinks.instagram}
                      onChange={(e) => updateSocialLinks('instagram', e.target.value)}
                      placeholder="https://instagram.com/yourcompany"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="youtube">YouTube</Label>
                    <Input
                      id="youtube"
                      value={branding.socialLinks.youtube}
                      onChange={(e) => updateSocialLinks('youtube', e.target.value)}
                      placeholder="https://youtube.com/c/yourcompany"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
