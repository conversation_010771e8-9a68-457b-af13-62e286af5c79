import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const companyId = session.user.companyId

    // Get date ranges for comparison
    const now = new Date()
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
    const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
    const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0)

    // Parallel queries for better performance
    const [
      // Current month stats
      customersCount,
      leadsCount,
      quotationsCount,
      invoicesCount,
      contractsCount,
      tasksCount,
      
      // Last month stats for comparison
      lastMonthCustomers,
      lastMonthLeads,
      lastMonthQuotations,
      lastMonthInvoices,
      
      // Revenue calculations
      totalRevenue,
      lastMonthRevenue,
      pendingRevenue,
      
      // Recent activities
      recentActivities,
      
      // Lead pipeline
      leadsByStatus,
      
      // Invoice status breakdown
      invoicesByStatus,
      
      // Top customers by revenue
      topCustomers,
      
      // Upcoming tasks
      upcomingTasks,
      
      // Contract renewals
      upcomingRenewals
    ] = await Promise.all([
      // Current month counts
      prisma.customer.count({
        where: { 
          companyId,
          createdAt: { gte: startOfMonth }
        }
      }),
      
      prisma.lead.count({
        where: { 
          companyId,
          status: { not: 'CLOSED_LOST' }
        }
      }),
      
      prisma.quotation.count({
        where: { 
          companyId,
          createdAt: { gte: startOfMonth }
        }
      }),
      
      prisma.invoice.count({
        where: { 
          companyId,
          createdAt: { gte: startOfMonth }
        }
      }),
      
      prisma.contract.count({
        where: { 
          companyId,
          status: 'ACTIVE'
        }
      }),
      
      prisma.task.count({
        where: { 
          companyId,
          status: { in: ['TODO', 'IN_PROGRESS'] }
        }
      }),
      
      // Last month counts
      prisma.customer.count({
        where: { 
          companyId,
          createdAt: { 
            gte: startOfLastMonth,
            lte: endOfLastMonth
          }
        }
      }),
      
      prisma.lead.count({
        where: { 
          companyId,
          createdAt: { 
            gte: startOfLastMonth,
            lte: endOfLastMonth
          }
        }
      }),
      
      prisma.quotation.count({
        where: { 
          companyId,
          createdAt: { 
            gte: startOfLastMonth,
            lte: endOfLastMonth
          }
        }
      }),
      
      prisma.invoice.count({
        where: { 
          companyId,
          createdAt: { 
            gte: startOfLastMonth,
            lte: endOfLastMonth
          }
        }
      }),
      
      // Revenue calculations
      prisma.invoice.aggregate({
        where: {
          companyId,
          status: 'PAID'
        },
        _sum: {
          total: true
        }
      }),
      
      prisma.invoice.aggregate({
        where: {
          companyId,
          status: 'PAID',
          paidAt: {
            gte: startOfLastMonth,
            lte: endOfLastMonth
          }
        },
        _sum: {
          total: true
        }
      }),
      
      prisma.invoice.aggregate({
        where: {
          companyId,
          status: { in: ['SENT', 'VIEWED'] }
        },
        _sum: {
          total: true
        }
      }),
      
      // Recent activities
      prisma.activity.findMany({
        where: { companyId },
        orderBy: { createdAt: 'desc' },
        take: 10,
        include: {
          createdBy: {
            select: { name: true, email: true }
          },
          lead: {
            select: { firstName: true, lastName: true }
          },
          customer: {
            select: { name: true }
          },
          quotation: {
            select: { quotationNumber: true, title: true }
          },
          invoice: {
            select: { invoiceNumber: true, title: true }
          },
          contract: {
            select: { contractNumber: true, title: true }
          }
        }
      }),
      
      // Lead pipeline
      prisma.lead.groupBy({
        by: ['status'],
        where: { companyId },
        _count: {
          status: true
        }
      }),
      
      // Invoice status breakdown
      prisma.invoice.groupBy({
        by: ['status'],
        where: { companyId },
        _count: {
          status: true
        },
        _sum: {
          total: true
        }
      }),
      
      // Top customers by revenue
      prisma.customer.findMany({
        where: { companyId },
        select: {
          id: true,
          name: true,
          companyName: true,
          invoices: {
            where: { status: 'PAID' },
            select: { total: true }
          }
        },
        take: 5
      }),
      
      // Upcoming tasks
      prisma.task.findMany({
        where: {
          companyId,
          status: { in: ['TODO', 'IN_PROGRESS'] },
          dueDate: {
            gte: now,
            lte: new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000) // Next 7 days
          }
        },
        orderBy: { dueDate: 'asc' },
        take: 5,
        include: {
          assignedTo: {
            select: { name: true, email: true }
          },
          customer: {
            select: { name: true }
          }
        }
      }),
      
      // Upcoming contract renewals
      prisma.contract.findMany({
        where: {
          companyId,
          status: 'ACTIVE',
          renewalDate: {
            gte: now,
            lte: new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000) // Next 30 days
          }
        },
        orderBy: { renewalDate: 'asc' },
        take: 5,
        include: {
          customer: {
            select: { name: true }
          }
        }
      })
    ])

    // Calculate percentage changes
    const calculateChange = (current: number, previous: number) => {
      if (previous === 0) return current > 0 ? 100 : 0
      return ((current - previous) / previous) * 100
    }

    // Process top customers with revenue
    const processedTopCustomers = topCustomers.map(customer => ({
      ...customer,
      totalRevenue: customer.invoices.reduce((sum, invoice) => sum + Number(invoice.total), 0)
    })).sort((a, b) => b.totalRevenue - a.totalRevenue)

    const stats = {
      customers: {
        current: customersCount,
        change: calculateChange(customersCount, lastMonthCustomers),
        total: await prisma.customer.count({ where: { companyId } })
      },
      leads: {
        current: leadsCount,
        change: calculateChange(leadsCount, lastMonthLeads),
        total: await prisma.lead.count({ where: { companyId } })
      },
      quotations: {
        current: quotationsCount,
        change: calculateChange(quotationsCount, lastMonthQuotations),
        total: await prisma.quotation.count({ where: { companyId } })
      },
      invoices: {
        current: invoicesCount,
        change: calculateChange(invoicesCount, lastMonthInvoices),
        total: await prisma.invoice.count({ where: { companyId } })
      },
      contracts: {
        current: contractsCount,
        total: await prisma.contract.count({ where: { companyId } })
      },
      tasks: {
        current: tasksCount,
        total: await prisma.task.count({ where: { companyId } })
      },
      revenue: {
        total: Number(totalRevenue._sum.total || 0),
        lastMonth: Number(lastMonthRevenue._sum.total || 0),
        pending: Number(pendingRevenue._sum.total || 0),
        change: calculateChange(
          Number(totalRevenue._sum.total || 0),
          Number(lastMonthRevenue._sum.total || 0)
        )
      }
    }

    return NextResponse.json({
      stats,
      recentActivities,
      leadsByStatus,
      invoicesByStatus,
      topCustomers: processedTopCustomers,
      upcomingTasks,
      upcomingRenewals
    })

  } catch (error) {
    console.error('Error fetching dashboard data:', error)
    return NextResponse.json(
      { error: 'Failed to fetch dashboard data' },
      { status: 500 }
    )
  }
}
