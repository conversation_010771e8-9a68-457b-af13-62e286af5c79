"use strict";exports.id=5904,exports.ids=[5904],exports.modules={11880:(e,s,a)=>{a.d(s,{t:()=>p});var t=a(95344),l=a(3729),r=a(16212),i=a(92549),d=a(1586),n=a(93601),c=a(17470),o=a(16802),u=a(69436),h=a(14513),x=a(44669);function p({task:e,onClose:s,onSuccess:a}){let[p,j]=(0,l.useState)({title:"",description:"",status:"TODO",priority:"MEDIUM",dueDate:"",startDate:"",assignedToId:"",leadId:"",customerId:"",type:"GENERAL",category:"",estimatedHours:"",actualHours:"",tags:[],customFields:{}}),[m,v]=(0,l.useState)([]),[g,y]=(0,l.useState)([]),[f,k]=(0,l.useState)([]),[I,D]=(0,l.useState)(!1),[N,w]=(0,l.useState)("");(0,l.useEffect)(()=>{e&&j({title:e.title,description:e.description||"",status:e.status,priority:e.priority,dueDate:e.dueDate?new Date(e.dueDate).toISOString().slice(0,16):"",startDate:e.startDate?new Date(e.startDate).toISOString().slice(0,16):"",assignedToId:e.assignedToId||"",leadId:"",customerId:"",type:e.type,category:e.category||"",estimatedHours:e.estimatedHours?.toString()||"",actualHours:e.actualHours?.toString()||"",tags:e.tags,customFields:e.customFields}),C(),E(),T()},[e]);let C=async()=>{try{v([])}catch(e){console.error("Error fetching users:",e)}},E=async()=>{try{let e=await fetch("/api/leads?limit=100");if(e.ok){let s=await e.json();y(s.leads||[])}}catch(e){console.error("Error fetching leads:",e)}},T=async()=>{try{let e=await fetch("/api/customers?limit=100");if(e.ok){let s=await e.json();k(s.customers||[])}}catch(e){console.error("Error fetching customers:",e)}},b=(e,s)=>{j(a=>({...a,[e]:s}))},H=()=>{N.trim()&&!p.tags.includes(N.trim())&&(j(e=>({...e,tags:[...e.tags,N.trim()]})),w(""))},S=e=>{j(s=>({...s,tags:s.tags.filter(s=>s!==e)}))},Q=async s=>{s.preventDefault(),D(!0);try{let s={...p,estimatedHours:p.estimatedHours?parseFloat(p.estimatedHours):void 0,actualHours:p.actualHours?parseFloat(p.actualHours):void 0,dueDate:p.dueDate||void 0,startDate:p.startDate||void 0,assignedToId:"unassigned"===p.assignedToId?null:p.assignedToId||void 0,leadId:"none"===p.leadId?null:p.leadId||void 0,customerId:"none"===p.customerId?null:p.customerId||void 0,category:p.category||void 0},t=e?`/api/tasks/${e.id}`:"/api/tasks",l=await fetch(t,{method:e?"PUT":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(!l.ok){let e=await l.json();throw Error(e.error||"Failed to save task")}x.toast.success(e?"Task updated successfully!":"Task created successfully!"),a()}catch(e){x.toast.error(e instanceof Error?e.message:"Failed to save task")}finally{D(!1)}};return t.jsx(o.Vq,{open:!0,onOpenChange:s,children:(0,t.jsxs)(o.cZ,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[t.jsx(o.fK,{children:t.jsx(o.$N,{children:e?"Edit Task":"Create New Task"})}),(0,t.jsxs)("form",{onSubmit:Q,className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[t.jsx(d._,{htmlFor:"title",children:"Title *"}),t.jsx(i.I,{id:"title",value:p.title,onChange:e=>b("title",e.target.value),placeholder:"Enter task title",required:!0})]}),(0,t.jsxs)("div",{children:[t.jsx(d._,{htmlFor:"description",children:"Description"}),t.jsx(n.g,{id:"description",value:p.description,onChange:e=>b("description",e.target.value),placeholder:"Enter task description",rows:3})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[t.jsx(d._,{htmlFor:"status",children:"Status"}),(0,t.jsxs)(c.Ph,{value:p.status,onValueChange:e=>b("status",e),children:[t.jsx(c.i4,{children:t.jsx(c.ki,{})}),(0,t.jsxs)(c.Bw,{children:[t.jsx(c.Ql,{value:"TODO",children:"To Do"}),t.jsx(c.Ql,{value:"IN_PROGRESS",children:"In Progress"}),t.jsx(c.Ql,{value:"REVIEW",children:"Review"}),t.jsx(c.Ql,{value:"DONE",children:"Done"}),t.jsx(c.Ql,{value:"CANCELLED",children:"Cancelled"})]})]})]}),(0,t.jsxs)("div",{children:[t.jsx(d._,{htmlFor:"priority",children:"Priority"}),(0,t.jsxs)(c.Ph,{value:p.priority,onValueChange:e=>b("priority",e),children:[t.jsx(c.i4,{children:t.jsx(c.ki,{})}),(0,t.jsxs)(c.Bw,{children:[t.jsx(c.Ql,{value:"LOW",children:"Low"}),t.jsx(c.Ql,{value:"MEDIUM",children:"Medium"}),t.jsx(c.Ql,{value:"HIGH",children:"High"}),t.jsx(c.Ql,{value:"URGENT",children:"Urgent"}),t.jsx(c.Ql,{value:"CRITICAL",children:"Critical"})]})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[t.jsx(d._,{htmlFor:"type",children:"Type"}),(0,t.jsxs)(c.Ph,{value:p.type,onValueChange:e=>b("type",e),children:[t.jsx(c.i4,{children:t.jsx(c.ki,{})}),(0,t.jsxs)(c.Bw,{children:[t.jsx(c.Ql,{value:"GENERAL",children:"General"}),t.jsx(c.Ql,{value:"ONBOARDING",children:"Onboarding"}),t.jsx(c.Ql,{value:"FOLLOW_UP",children:"Follow Up"}),t.jsx(c.Ql,{value:"REVIEW",children:"Review"}),t.jsx(c.Ql,{value:"SUPPORT",children:"Support"}),t.jsx(c.Ql,{value:"DEVELOPMENT",children:"Development"}),t.jsx(c.Ql,{value:"MARKETING",children:"Marketing"}),t.jsx(c.Ql,{value:"SALES",children:"Sales"})]})]})]}),(0,t.jsxs)("div",{children:[t.jsx(d._,{htmlFor:"category",children:"Category"}),t.jsx(i.I,{id:"category",value:p.category,onChange:e=>b("category",e.target.value),placeholder:"Enter category"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[t.jsx(d._,{htmlFor:"assignedToId",children:"Assigned To"}),(0,t.jsxs)(c.Ph,{value:p.assignedToId,onValueChange:e=>b("assignedToId",e),children:[t.jsx(c.i4,{children:t.jsx(c.ki,{placeholder:"Select assignee"})}),(0,t.jsxs)(c.Bw,{children:[t.jsx(c.Ql,{value:"unassigned",children:"Unassigned"}),m.map(e=>t.jsx(c.Ql,{value:e.id,children:e.name||e.email},e.id))]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[t.jsx(d._,{htmlFor:"startDate",children:"Start Date"}),t.jsx(i.I,{id:"startDate",type:"datetime-local",value:p.startDate,onChange:e=>b("startDate",e.target.value)})]}),(0,t.jsxs)("div",{children:[t.jsx(d._,{htmlFor:"dueDate",children:"Due Date"}),t.jsx(i.I,{id:"dueDate",type:"datetime-local",value:p.dueDate,onChange:e=>b("dueDate",e.target.value)})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[t.jsx(d._,{htmlFor:"estimatedHours",children:"Estimated Hours"}),t.jsx(i.I,{id:"estimatedHours",type:"number",step:"0.5",min:"0",value:p.estimatedHours,onChange:e=>b("estimatedHours",e.target.value),placeholder:"0"})]}),(0,t.jsxs)("div",{children:[t.jsx(d._,{htmlFor:"actualHours",children:"Actual Hours"}),t.jsx(i.I,{id:"actualHours",type:"number",step:"0.5",min:"0",value:p.actualHours,onChange:e=>b("actualHours",e.target.value),placeholder:"0"})]})]})]}),t.jsx("div",{className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[t.jsx(d._,{htmlFor:"leadId",children:"Related Lead"}),(0,t.jsxs)(c.Ph,{value:p.leadId,onValueChange:e=>b("leadId",e),children:[t.jsx(c.i4,{children:t.jsx(c.ki,{placeholder:"Select lead"})}),(0,t.jsxs)(c.Bw,{children:[t.jsx(c.Ql,{value:"none",children:"No lead"}),g.map(e=>(0,t.jsxs)(c.Ql,{value:e.id,children:[e.firstName," ",e.lastName," ",e.companyName&&`(${e.companyName})`]},e.id))]})]})]}),(0,t.jsxs)("div",{children:[t.jsx(d._,{htmlFor:"customerId",children:"Related Customer"}),(0,t.jsxs)(c.Ph,{value:p.customerId,onValueChange:e=>b("customerId",e),children:[t.jsx(c.i4,{children:t.jsx(c.ki,{placeholder:"Select customer"})}),(0,t.jsxs)(c.Bw,{children:[t.jsx(c.Ql,{value:"none",children:"No customer"}),f.map(e=>(0,t.jsxs)(c.Ql,{value:e.id,children:[e.name," ",e.company&&`(${e.company})`]},e.id))]})]})]})]})}),t.jsx("div",{className:"space-y-4",children:(0,t.jsxs)("div",{children:[t.jsx(d._,{children:"Tags"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[t.jsx(i.I,{value:N,onChange:e=>w(e.target.value),placeholder:"Add a tag",onKeyPress:e=>{"Enter"===e.key&&(e.preventDefault(),H())}}),t.jsx(r.z,{type:"button",onClick:H,size:"sm",children:"Add"})]}),t.jsx("div",{className:"flex flex-wrap gap-2",children:p.tags.map((e,s)=>(0,t.jsxs)(u.C,{variant:"secondary",className:"flex items-center space-x-1",children:[t.jsx("span",{children:e}),t.jsx(h.Z,{className:"h-3 w-3 cursor-pointer",onClick:()=>S(e)})]},s))})]})}),(0,t.jsxs)("div",{className:"flex justify-end space-x-2 pt-4 border-t",children:[t.jsx(r.z,{type:"button",variant:"outline",onClick:s,children:"Cancel"}),t.jsx(r.z,{type:"submit",disabled:I,children:I?"Saving...":e?"Update Task":"Create Task"})]})]})]})})}},93601:(e,s,a)=>{a.d(s,{g:()=>i});var t=a(95344),l=a(3729),r=a(91626);let i=l.forwardRef(({className:e,...s},a)=>t.jsx("textarea",{className:(0,r.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...s}));i.displayName="Textarea"},66138:(e,s,a)=>{a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},2246:(e,s,a)=>{a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("CheckSquare",[["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}],["path",{d:"M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11",key:"1jnkn4"}]])},71542:(e,s,a)=>{a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("FileCheck",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["path",{d:"m9 15 2 2 4-4",key:"1grp1n"}]])},91917:(e,s,a)=>{a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},74243:(e,s,a)=>{a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("Receipt",[["path",{d:"M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1-2-1Z",key:"wqdwcb"}],["path",{d:"M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8",key:"1h4pet"}],["path",{d:"M12 17V7",key:"pyj7ub"}]])},28240:(e,s,a)=>{a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},14217:(e,s,a)=>{a.d(s,{f:()=>d});var t=a(3729),l=a(62409),r=a(95344),i=t.forwardRef((e,s)=>(0,r.jsx)(l.WV.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));i.displayName="Label";var d=i}};