import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get CMS content from database
    let content = {}
    
    try {
      const cmsContents = await prisma.cMSContent.findMany()
      
      // Convert to content object
      content = cmsContents.reduce((acc, item) => {
        acc[item.section] = item.content
        return acc
      }, {} as Record<string, any>)
    } catch (error) {
      console.error('Error fetching CMS content:', error)
      // Return default content if table doesn't exist yet
      content = {
        hero: {
          enabled: true,
          title: 'Build Your SaaS Business',
          subtitle: 'The Complete Platform',
          description: 'Everything you need to launch, grow, and scale your SaaS business. From customer management to billing, we\'ve got you covered.',
          primaryCTA: {
            text: 'Start Free Trial',
            link: '/auth/signup'
          },
          secondaryCTA: {
            text: 'Watch Demo',
            link: '/demo'
          },
          backgroundImage: '',
          backgroundVideo: ''
        },
        features: {
          enabled: true,
          title: 'Everything You Need',
          subtitle: 'Powerful Features',
          items: [
            {
              id: '1',
              title: 'Customer Management',
              description: 'Manage your customers, track interactions, and build lasting relationships.',
              icon: 'users',
              image: ''
            },
            {
              id: '2',
              title: 'Subscription Billing',
              description: 'Automated billing, invoicing, and payment processing for recurring revenue.',
              icon: 'credit-card',
              image: ''
            },
            {
              id: '3',
              title: 'Analytics & Reports',
              description: 'Comprehensive analytics to track your business performance and growth.',
              icon: 'bar-chart',
              image: ''
            }
          ]
        },
        pricing: {
          enabled: true,
          title: 'Simple, Transparent Pricing',
          subtitle: 'Choose the plan that fits your needs',
          showPricingTable: true,
          customMessage: ''
        },
        seo: {
          title: 'SaaS Platform - Build Your Business',
          description: 'The complete SaaS platform for modern businesses. Customer management, billing, analytics, and more.',
          keywords: 'saas, platform, business, customer management, billing, analytics',
          ogImage: ''
        }
      }
    }

    return NextResponse.json({
      success: true,
      content: content
    })
  } catch (error) {
    console.error('Error fetching CMS content:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const content = await request.json()

    // Update or create CMS content entries
    const updates = []
    
    for (const [section, sectionContent] of Object.entries(content)) {
      try {
        updates.push(
          prisma.cMSContent.upsert({
            where: { section },
            update: {
              content: sectionContent,
              updatedAt: new Date()
            },
            create: {
              section,
              content: sectionContent,
              isEnabled: true
            }
          })
        )
      } catch (error) {
        console.error(`Error updating CMS content for ${section}:`, error)
      }
    }

    try {
      await Promise.all(updates)
    } catch (error) {
      console.error('Error saving CMS content:', error)
      // Continue even if some updates fail
    }

    // Log the configuration change
    try {
      await prisma.auditLog.create({
        data: {
          action: 'UPDATE_CMS_CONTENT',
          entityType: 'CMS_CONTENT',
          entityId: 'cms',
          userId: session.user.id,
          details: {
            updatedSections: Object.keys(content),
            timestamp: new Date().toISOString()
          }
        }
      })
    } catch (error) {
      // Ignore audit log errors
      console.error('Error creating audit log:', error)
    }

    return NextResponse.json({
      success: true,
      message: 'CMS content updated successfully'
    })
  } catch (error) {
    console.error('Error updating CMS content:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
