"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/super-admin/subscriptions/page",{

/***/ "(app-pages-browser)/./app/super-admin/subscriptions/page.tsx":
/*!************************************************!*\
  !*** ./app/super-admin/subscriptions/page.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SubscriptionManagementPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,CheckCircle,Clock,CreditCard,Crown,Edit,Eye,Filter,MoreHorizontal,Plus,RefreshCw,Search,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,CheckCircle,Clock,CreditCard,Crown,Edit,Eye,Filter,MoreHorizontal,Plus,RefreshCw,Search,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,CheckCircle,Clock,CreditCard,Crown,Edit,Eye,Filter,MoreHorizontal,Plus,RefreshCw,Search,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,CheckCircle,Clock,CreditCard,Crown,Edit,Eye,Filter,MoreHorizontal,Plus,RefreshCw,Search,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,CheckCircle,Clock,CreditCard,Crown,Edit,Eye,Filter,MoreHorizontal,Plus,RefreshCw,Search,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,CheckCircle,Clock,CreditCard,Crown,Edit,Eye,Filter,MoreHorizontal,Plus,RefreshCw,Search,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,CheckCircle,Clock,CreditCard,Crown,Edit,Eye,Filter,MoreHorizontal,Plus,RefreshCw,Search,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,CheckCircle,Clock,CreditCard,Crown,Edit,Eye,Filter,MoreHorizontal,Plus,RefreshCw,Search,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,CheckCircle,Clock,CreditCard,Crown,Edit,Eye,Filter,MoreHorizontal,Plus,RefreshCw,Search,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,CheckCircle,Clock,CreditCard,Crown,Edit,Eye,Filter,MoreHorizontal,Plus,RefreshCw,Search,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,CheckCircle,Clock,CreditCard,Crown,Edit,Eye,Filter,MoreHorizontal,Plus,RefreshCw,Search,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,CheckCircle,Clock,CreditCard,Crown,Edit,Eye,Filter,MoreHorizontal,Plus,RefreshCw,Search,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,CheckCircle,Clock,CreditCard,Crown,Edit,Eye,Filter,MoreHorizontal,Plus,RefreshCw,Search,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,CheckCircle,Clock,CreditCard,Crown,Edit,Eye,Filter,MoreHorizontal,Plus,RefreshCw,Search,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,CheckCircle,Clock,CreditCard,Crown,Edit,Eye,Filter,MoreHorizontal,Plus,RefreshCw,Search,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,CheckCircle,Clock,CreditCard,Crown,Edit,Eye,Filter,MoreHorizontal,Plus,RefreshCw,Search,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/more-horizontal.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction SubscriptionManagementPage() {\n    var _session_user;\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [subscriptions, setSubscriptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [planFilter, setPlanFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [showCreateDialog, setShowCreateDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [pricingPlans, setPricingPlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        companyId: \"\",\n        planId: \"\",\n        status: \"ACTIVE\",\n        billingCycle: \"MONTHLY\",\n        amount: 0,\n        startDate: new Date().toISOString().split(\"T\")[0],\n        endDate: \"\",\n        trialEndDate: \"\",\n        stripeSubscriptionId: \"\",\n        stripeCustomerId: \"\"\n    });\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                lineNumber: 151,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n            lineNumber: 150,\n            columnNumber: 7\n        }, this);\n    }\n    if (status === \"unauthenticated\") {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.redirect)(\"/auth/signin\");\n    }\n    if ((session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.role) !== \"SUPER_ADMIN\") {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.redirect)(\"/dashboard\");\n    }\n    const fetchSubscriptions = async ()=>{\n        try {\n            setLoading(true);\n            const params = new URLSearchParams({\n                page: page.toString(),\n                limit: \"20\",\n                ...searchTerm && {\n                    search: searchTerm\n                },\n                ...statusFilter && statusFilter !== \"all\" && {\n                    status: statusFilter\n                },\n                ...planFilter && planFilter !== \"all\" && {\n                    plan: planFilter\n                }\n            });\n            const response = await fetch(\"/api/super-admin/subscriptions?\".concat(params));\n            if (!response.ok) throw new Error(\"Failed to fetch subscriptions\");\n            const data = await response.json();\n            setSubscriptions(data.subscriptions);\n            setStats(data.stats);\n            setTotalPages(data.pagination.pages);\n        } catch (error) {\n            console.error(\"Error fetching subscriptions:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchCompanies = async ()=>{\n        try {\n            const response = await fetch(\"/api/super-admin/companies?limit=1000\");\n            const data = await response.json();\n            if (data.companies) {\n                setCompanies(data.companies);\n            }\n        } catch (error) {\n            console.error(\"Error fetching companies:\", error);\n        }\n    };\n    const fetchPricingPlans = async ()=>{\n        try {\n            const response = await fetch(\"/api/pricing-plans\");\n            const data = await response.json();\n            if (data.success) {\n                setPricingPlans(data.data);\n            }\n        } catch (error) {\n            console.error(\"Error fetching pricing plans:\", error);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchSubscriptions();\n    }, [\n        page,\n        searchTerm,\n        statusFilter,\n        planFilter\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (showCreateDialog) {\n            fetchCompanies();\n            fetchPricingPlans();\n        }\n    }, [\n        showCreateDialog\n    ]);\n    const getStatusBadge = (status)=>{\n        const variants = {\n            ACTIVE: {\n                variant: \"default\",\n                icon: _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                color: \"text-green-600\"\n            },\n            TRIAL: {\n                variant: \"secondary\",\n                icon: _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                color: \"text-blue-600\"\n            },\n            CANCELLED: {\n                variant: \"destructive\",\n                icon: _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                color: \"text-red-600\"\n            },\n            EXPIRED: {\n                variant: \"outline\",\n                icon: _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                color: \"text-orange-600\"\n            },\n            SUSPENDED: {\n                variant: \"destructive\",\n                icon: _barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                color: \"text-red-600\"\n            }\n        };\n        const config = variants[status] || variants.CANCELLED;\n        const Icon = config.icon;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n            variant: config.variant,\n            className: \"flex items-center space-x-1\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                    className: \"h-3 w-3\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                    lineNumber: 238,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: status\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n            lineNumber: 237,\n            columnNumber: 7\n        }, this);\n    };\n    const getPlanBadge = (plan)=>{\n        const colors = {\n            BASIC: \"bg-gray-100 text-gray-800\",\n            PROFESSIONAL: \"bg-blue-100 text-blue-800\",\n            PREMIUM: \"bg-purple-100 text-purple-800\",\n            ENTERPRISE: \"bg-orange-100 text-orange-800\"\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(colors[plan] || \"bg-gray-100 text-gray-800\"),\n            children: plan\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n            lineNumber: 253,\n            columnNumber: 7\n        }, this);\n    };\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\",\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 0\n        }).format(amount);\n    };\n    const formatDate = (date)=>{\n        if (!date) return \"N/A\";\n        return new Date(date).toLocaleDateString(\"en-US\", {\n            year: \"numeric\",\n            month: \"short\",\n            day: \"numeric\"\n        });\n    };\n    const handleCreateSubscription = async ()=>{\n        try {\n            const selectedPlan = pricingPlans.find((p)=>p.id === formData.planId);\n            if (!selectedPlan) {\n                toast.error(\"Please select a pricing plan\");\n                return;\n            }\n            const amount = formData.billingCycle === \"YEARLY\" ? selectedPlan.yearlyPrice || selectedPlan.monthlyPrice * 12 : selectedPlan.monthlyPrice;\n            const response = await fetch(\"/api/super-admin/subscriptions\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    ...formData,\n                    amount,\n                    plan: selectedPlan.name.toUpperCase(),\n                    startDate: formData.startDate ? new Date(formData.startDate) : new Date(),\n                    endDate: formData.endDate ? new Date(formData.endDate) : undefined,\n                    trialEndDate: formData.trialEndDate ? new Date(formData.trialEndDate) : undefined\n                })\n            });\n            const data = await response.json();\n            if (response.ok) {\n                toast.success(data.message || \"Subscription created successfully\");\n                setShowCreateDialog(false);\n                setFormData({\n                    companyId: \"\",\n                    planId: \"\",\n                    status: \"ACTIVE\",\n                    billingCycle: \"MONTHLY\",\n                    amount: 0,\n                    startDate: new Date().toISOString().split(\"T\")[0],\n                    endDate: \"\",\n                    trialEndDate: \"\",\n                    stripeSubscriptionId: \"\",\n                    stripeCustomerId: \"\"\n                });\n                fetchSubscriptions();\n            } else {\n                toast.error(data.error || \"Failed to create subscription\");\n            }\n        } catch (error) {\n            console.error(\"Error creating subscription:\", error);\n            toast.error(\"Failed to create subscription\");\n        }\n    };\n    const handlePlanChange = (planId)=>{\n        const selectedPlan = pricingPlans.find((p)=>p.id === planId);\n        if (selectedPlan) {\n            const amount = formData.billingCycle === \"YEARLY\" ? selectedPlan.yearlyPrice || selectedPlan.monthlyPrice * 12 : selectedPlan.monthlyPrice;\n            setFormData((prev)=>({\n                    ...prev,\n                    planId,\n                    amount\n                }));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row sm:items-center justify-between gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"Subscription Management\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 mt-1\",\n                                children: \"Manage subscriptions, billing, and revenue analytics\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                        lineNumber: 350,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                variant: \"outline\",\n                                onClick: fetchSubscriptions,\n                                disabled: loading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 \".concat(loading ? \"animate-spin\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTrigger, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Add Subscription\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                        children: \"Create New Subscription\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogDescription, {\n                                                        children: \"Add or modify a company's subscription plan.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8 text-gray-500\",\n                                                children: \"Subscription management form coming soon...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                lineNumber: 349,\n                columnNumber: 7\n            }, this),\n            stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"hover:shadow-lg transition-shadow duration-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-500\",\n                                                    children: \"Monthly Recurring Revenue\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-green-600\",\n                                                    children: formatCurrency(stats.revenue.mrr)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-8 w-8 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500\",\n                                                children: \"Growth Rate:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-green-600\",\n                                                children: [\n                                                    \"+\",\n                                                    stats.metrics.growthRate.toFixed(1),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                            lineNumber: 388,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                        lineNumber: 387,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"hover:shadow-lg transition-shadow duration-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-500\",\n                                                    children: \"Annual Recurring Revenue\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-blue-600\",\n                                                    children: formatCurrency(stats.revenue.arr)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-8 w-8 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500\",\n                                                children: \"Avg/Customer:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-blue-600\",\n                                                children: formatCurrency(stats.revenue.average)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                    lineNumber: 420,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                            lineNumber: 410,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                        lineNumber: 409,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"hover:shadow-lg transition-shadow duration-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-500\",\n                                                    children: \"Active Subscriptions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-purple-600\",\n                                                    children: stats.revenue.activeSubscriptions\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                            lineNumber: 434,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"h-8 w-8 text-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                            lineNumber: 438,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500\",\n                                                children: \"New (30d):\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-purple-600\",\n                                                children: [\n                                                    \"+\",\n                                                    stats.metrics.newSubscriptions\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                    lineNumber: 440,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                            lineNumber: 432,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                        lineNumber: 431,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"hover:shadow-lg transition-shadow duration-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-500\",\n                                                    children: \"Churn Rate\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-red-600\",\n                                                    children: [\n                                                        stats.metrics.churnRate.toFixed(1),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 454,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                            lineNumber: 452,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"h-8 w-8 text-red-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                            lineNumber: 458,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                    lineNumber: 451,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500\",\n                                                children: \"Churned (30d):\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-red-600\",\n                                                children: stats.metrics.churnCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                    lineNumber: 460,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                            lineNumber: 450,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                        lineNumber: 449,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                lineNumber: 386,\n                columnNumber: 9\n            }, this),\n            stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                    children: \"Subscription Status\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                    lineNumber: 476,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                lineNumber: 475,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: stats.byStatus.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-3 h-3 rounded-full \".concat(item.status === \"ACTIVE\" ? \"bg-green-500\" : item.status === \"TRIAL\" ? \"bg-blue-500\" : item.status === \"CANCELLED\" ? \"bg-red-500\" : item.status === \"EXPIRED\" ? \"bg-orange-500\" : \"bg-gray-500\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                            lineNumber: 483,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: item.status\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: item.count\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                            lineNumber: 492,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-400\",\n                                                            children: [\n                                                                \"(\",\n                                                                (item.count / stats.total * 100).toFixed(1),\n                                                                \"%)\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                            lineNumber: 493,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 491,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, item.status, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                    lineNumber: 479,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                lineNumber: 478,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                        lineNumber: 474,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                    children: \"Plan Distribution\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                lineNumber: 504,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: stats.byPlan.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-3 h-3 rounded-full \".concat(index === 0 ? \"bg-purple-500\" : index === 1 ? \"bg-blue-500\" : index === 2 ? \"bg-green-500\" : \"bg-orange-500\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                            lineNumber: 512,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: item.plan\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                            lineNumber: 517,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 511,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: item.count\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                            lineNumber: 520,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-400\",\n                                                            children: [\n                                                                \"(\",\n                                                                (item.count / stats.total * 100).toFixed(1),\n                                                                \"%)\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                            lineNumber: 521,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 519,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, item.plan, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                            lineNumber: 510,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                    lineNumber: 508,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                lineNumber: 507,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                        lineNumber: 503,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                lineNumber: 473,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    className: \"p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                            lineNumber: 539,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                            placeholder: \"Search companies...\",\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            className: \"pl-10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                    lineNumber: 538,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                lineNumber: 537,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                value: statusFilter,\n                                onValueChange: setStatusFilter,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                            placeholder: \"All Statuses\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                            lineNumber: 550,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                        lineNumber: 549,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                value: \"all\",\n                                                children: \"All Statuses\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                lineNumber: 553,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                value: \"ACTIVE\",\n                                                children: \"Active\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                lineNumber: 554,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                value: \"TRIAL\",\n                                                children: \"Trial\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                lineNumber: 555,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                value: \"CANCELLED\",\n                                                children: \"Cancelled\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                value: \"EXPIRED\",\n                                                children: \"Expired\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                lineNumber: 557,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                value: \"SUSPENDED\",\n                                                children: \"Suspended\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                lineNumber: 558,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                        lineNumber: 552,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                lineNumber: 548,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                value: planFilter,\n                                onValueChange: setPlanFilter,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                            placeholder: \"All Plans\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                            lineNumber: 563,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                        lineNumber: 562,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                value: \"all\",\n                                                children: \"All Plans\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                lineNumber: 566,\n                                                columnNumber: 17\n                                            }, this),\n                                            stats === null || stats === void 0 ? void 0 : stats.byPlan.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: item.plan,\n                                                    children: [\n                                                        item.plan,\n                                                        \" (\",\n                                                        item.count,\n                                                        \")\"\n                                                    ]\n                                                }, item.plan, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 568,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                        lineNumber: 565,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                lineNumber: 561,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                variant: \"outline\",\n                                className: \"w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                        lineNumber: 575,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"More Filters\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                lineNumber: 574,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                        lineNumber: 536,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                    lineNumber: 535,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                lineNumber: 534,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                            children: [\n                                \"Subscriptions (\",\n                                subscriptions.length,\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                            lineNumber: 585,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                        lineNumber: 584,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center py-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                lineNumber: 590,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                            lineNumber: 589,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.Table, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableRow, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                    children: \"Company\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 597,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                    children: \"Plan\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 598,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 599,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                    children: \"Amount\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 600,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                    children: \"Billing\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 601,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                    children: \"Start Date\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 602,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                    children: \"Usage\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 603,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                    children: \"Actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                    lineNumber: 604,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                        lineNumber: 595,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableBody, {\n                                        children: subscriptions.map((subscription)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableRow, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold\",\n                                                                    children: subscription.company.name.charAt(0).toUpperCase()\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                    lineNumber: 612,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium text-gray-900\",\n                                                                            children: subscription.company.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                            lineNumber: 616,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2 text-sm text-gray-500\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: subscription.company.industry\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                                    lineNumber: 618,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                subscription.company.size && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: \"•\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                                            lineNumber: 621,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: subscription.company.size\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                                            lineNumber: 622,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                            lineNumber: 617,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                    lineNumber: 615,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                            lineNumber: 611,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                        lineNumber: 610,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: getPlanBadge(subscription.plan)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                        lineNumber: 629,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: getStatusBadge(subscription.status)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                        lineNumber: 630,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: formatCurrency(subscription.amount)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                    lineNumber: 633,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: [\n                                                                        \"per \",\n                                                                        subscription.billingCycle.toLowerCase()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                    lineNumber: 634,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                            lineNumber: 632,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                        lineNumber: 631,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                            variant: \"outline\",\n                                                            children: subscription.billingCycle\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                            lineNumber: 640,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                        lineNumber: 639,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: formatDate(subscription.startDate)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                            lineNumber: 645,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                        lineNumber: 644,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        subscription.company.metrics.totalUsers,\n                                                                        \" users\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                    lineNumber: 651,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: [\n                                                                        subscription.company.metrics.totalCustomers,\n                                                                        \" customers\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                    lineNumber: 652,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                            lineNumber: 650,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                        lineNumber: 649,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"sm\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                        lineNumber: 660,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                    lineNumber: 659,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"sm\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                        lineNumber: 663,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                    lineNumber: 662,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"sm\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_CheckCircle_Clock_CreditCard_Crown_Edit_Eye_Filter_MoreHorizontal_Plus_RefreshCw_Search_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                        lineNumber: 666,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                                    lineNumber: 665,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                            lineNumber: 658,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                        lineNumber: 657,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, subscription.id, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                        lineNumber: 607,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                lineNumber: 594,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                            lineNumber: 593,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                        lineNumber: 587,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                lineNumber: 583,\n                columnNumber: 7\n            }, this),\n            totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [\n                            \"Page \",\n                            page,\n                            \" of \",\n                            totalPages\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                        lineNumber: 682,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>setPage(page - 1),\n                                disabled: page === 1,\n                                children: \"Previous\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                lineNumber: 686,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>setPage(page + 1),\n                                disabled: page === totalPages,\n                                children: \"Next\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                                lineNumber: 693,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                        lineNumber: 685,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n                lineNumber: 681,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\subscriptions\\\\page.tsx\",\n        lineNumber: 347,\n        columnNumber: 5\n    }, this);\n}\n_s(SubscriptionManagementPage, \"uQSQwdXbeNZX3deJLbtPZjsLpNE=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession\n    ];\n});\n_c = SubscriptionManagementPage;\nvar _c;\n$RefreshReg$(_c, \"SubscriptionManagementPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/super-admin/subscriptions/page.tsx\n"));

/***/ })

});