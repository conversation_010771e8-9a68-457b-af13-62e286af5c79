'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { Package, DollarSign, Warehouse, Calculator } from 'lucide-react'
import { toast } from 'react-hot-toast'

interface Item {
  id: string
  name: string
  description: string | null
  sku: string | null
  category: string | null
  unitPrice: number
  costPrice: number | null
  currency: string
  trackInventory: boolean
  stockQuantity: number | null
  lowStockAlert: number | null
  taxable: boolean
  taxRate: number
  accountingCode: string | null
  active: boolean
}

interface ItemFormProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  item?: Item | null
  mode: 'create' | 'edit'
}

const categories = [
  'Electronics',
  'Software',
  'Hardware',
  'Services',
  'Consulting',
  'Training',
  'Support',
  'Maintenance',
  'Subscription',
  'Digital Products',
  'Physical Products',
  'Other'
]

const currencies = [
  { value: 'USD', label: 'USD ($)' },
  { value: 'EUR', label: 'EUR (€)' },
  { value: 'GBP', label: 'GBP (£)' },
  { value: 'CAD', label: 'CAD (C$)' },
  { value: 'AUD', label: 'AUD (A$)' }
]

export function ItemForm({ isOpen, onClose, onSuccess, item, mode }: ItemFormProps) {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    sku: '',
    category: '',
    unitPrice: 0,
    costPrice: '',
    currency: 'USD',
    trackInventory: false,
    stockQuantity: '',
    lowStockAlert: '',
    taxable: true,
    taxRate: 0,
    accountingCode: '',
    active: true
  })

  useEffect(() => {
    if (item && mode === 'edit') {
      setFormData({
        name: item.name,
        description: item.description || '',
        sku: item.sku || '',
        category: item.category || '',
        unitPrice: item.unitPrice,
        costPrice: item.costPrice?.toString() || '',
        currency: item.currency,
        trackInventory: item.trackInventory,
        stockQuantity: item.stockQuantity?.toString() || '',
        lowStockAlert: item.lowStockAlert?.toString() || '',
        taxable: item.taxable,
        taxRate: item.taxRate,
        accountingCode: item.accountingCode || '',
        active: item.active
      })
    } else {
      setFormData({
        name: '',
        description: '',
        sku: '',
        category: '',
        unitPrice: 0,
        costPrice: '',
        currency: 'USD',
        trackInventory: false,
        stockQuantity: '',
        lowStockAlert: '',
        taxable: true,
        taxRate: 0,
        accountingCode: '',
        active: true
      })
    }
  }, [item, mode, isOpen])

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.name.trim()) {
      toast.error('Item name is required')
      return
    }

    if (formData.unitPrice < 0) {
      toast.error('Unit price must be positive')
      return
    }

    if (formData.costPrice && parseFloat(formData.costPrice) < 0) {
      toast.error('Cost price must be positive')
      return
    }

    if (formData.trackInventory && !formData.stockQuantity) {
      toast.error('Stock quantity is required when inventory tracking is enabled')
      return
    }

    setLoading(true)
    try {
      const payload = {
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        sku: formData.sku.trim() || undefined,
        category: formData.category || undefined,
        unitPrice: formData.unitPrice,
        costPrice: formData.costPrice ? parseFloat(formData.costPrice) : undefined,
        currency: formData.currency,
        trackInventory: formData.trackInventory,
        stockQuantity: formData.trackInventory && formData.stockQuantity 
          ? parseInt(formData.stockQuantity) 
          : undefined,
        lowStockAlert: formData.trackInventory && formData.lowStockAlert 
          ? parseInt(formData.lowStockAlert) 
          : undefined,
        taxable: formData.taxable,
        taxRate: formData.taxRate,
        accountingCode: formData.accountingCode.trim() || undefined,
        active: formData.active
      }

      const url = mode === 'edit' ? `/api/items/${item?.id}` : '/api/items'
      const method = mode === 'edit' ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || `Failed to ${mode} item`)
      }

      toast.success(`Item ${mode === 'edit' ? 'updated' : 'created'} successfully!`)
      onSuccess()
      onClose()
    } catch (error) {
      toast.error(error instanceof Error ? error.message : `Failed to ${mode} item`)
    } finally {
      setLoading(false)
    }
  }

  const profitMargin = formData.costPrice && formData.unitPrice > 0
    ? ((formData.unitPrice - parseFloat(formData.costPrice)) / formData.unitPrice * 100)
    : 0

  const stockValue = formData.trackInventory && formData.stockQuantity
    ? formData.unitPrice * parseInt(formData.stockQuantity)
    : 0

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Package className="h-5 w-5 mr-2" />
            {mode === 'edit' ? 'Edit Item' : 'Create New Item'}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center">
              <Package className="h-4 w-4 mr-2" />
              Basic Information
            </h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Item Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="Enter item name"
                  required
                />
              </div>
              <div>
                <Label htmlFor="sku">SKU</Label>
                <Input
                  id="sku"
                  value={formData.sku}
                  onChange={(e) => handleInputChange('sku', e.target.value)}
                  placeholder="Stock Keeping Unit"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Item description..."
                rows={3}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="category">Category</Label>
                <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="active"
                  checked={formData.active}
                  onCheckedChange={(checked) => handleInputChange('active', checked)}
                />
                <Label htmlFor="active">Active</Label>
              </div>
            </div>
          </div>

          <Separator />

          {/* Pricing */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center">
              <DollarSign className="h-4 w-4 mr-2" />
              Pricing
            </h3>
            
            <div className="grid grid-cols-3 gap-4">
              <div>
                <Label htmlFor="unitPrice">Unit Price *</Label>
                <Input
                  id="unitPrice"
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.unitPrice}
                  onChange={(e) => handleInputChange('unitPrice', parseFloat(e.target.value) || 0)}
                  placeholder="0.00"
                  required
                />
              </div>
              <div>
                <Label htmlFor="costPrice">Cost Price</Label>
                <Input
                  id="costPrice"
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.costPrice}
                  onChange={(e) => handleInputChange('costPrice', e.target.value)}
                  placeholder="0.00"
                />
              </div>
              <div>
                <Label htmlFor="currency">Currency</Label>
                <Select value={formData.currency} onValueChange={(value) => handleInputChange('currency', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {currencies.map((currency) => (
                      <SelectItem key={currency.value} value={currency.value}>
                        {currency.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {formData.costPrice && (
              <div className="p-3 bg-green-50 rounded-lg border border-green-200">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-green-800">Profit Margin:</span>
                  <span className="text-lg font-bold text-green-600">
                    {profitMargin.toFixed(1)}%
                  </span>
                </div>
                <div className="flex items-center justify-between mt-1">
                  <span className="text-sm text-green-700">Profit per Unit:</span>
                  <span className="text-sm font-semibold text-green-600">
                    {new Intl.NumberFormat('en-US', { style: 'currency', currency: formData.currency }).format(
                      formData.unitPrice - parseFloat(formData.costPrice)
                    )}
                  </span>
                </div>
              </div>
            )}
          </div>

          <Separator />

          {/* Inventory Management */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold flex items-center">
                <Warehouse className="h-4 w-4 mr-2" />
                Inventory Management
              </h3>
              <div className="flex items-center space-x-2">
                <Switch
                  id="trackInventory"
                  checked={formData.trackInventory}
                  onCheckedChange={(checked) => handleInputChange('trackInventory', checked)}
                />
                <Label htmlFor="trackInventory">Track Inventory</Label>
              </div>
            </div>

            {formData.trackInventory && (
              <div className="space-y-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="stockQuantity">Stock Quantity *</Label>
                    <Input
                      id="stockQuantity"
                      type="number"
                      min="0"
                      value={formData.stockQuantity}
                      onChange={(e) => handleInputChange('stockQuantity', e.target.value)}
                      placeholder="0"
                      required={formData.trackInventory}
                    />
                  </div>
                  <div>
                    <Label htmlFor="lowStockAlert">Low Stock Alert</Label>
                    <Input
                      id="lowStockAlert"
                      type="number"
                      min="0"
                      value={formData.lowStockAlert}
                      onChange={(e) => handleInputChange('lowStockAlert', e.target.value)}
                      placeholder="10"
                    />
                  </div>
                </div>

                {formData.stockQuantity && (
                  <div className="p-3 bg-blue-100 rounded-lg">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-blue-800">Stock Value:</span>
                      <span className="text-lg font-bold text-blue-600">
                        {new Intl.NumberFormat('en-US', { style: 'currency', currency: formData.currency }).format(stockValue)}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          <Separator />

          {/* Tax & Accounting */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center">
              <Calculator className="h-4 w-4 mr-2" />
              Tax & Accounting
            </h3>
            
            <div className="grid grid-cols-3 gap-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="taxable"
                  checked={formData.taxable}
                  onCheckedChange={(checked) => handleInputChange('taxable', checked)}
                />
                <Label htmlFor="taxable">Taxable</Label>
              </div>
              <div>
                <Label htmlFor="taxRate">Tax Rate (%)</Label>
                <Input
                  id="taxRate"
                  type="number"
                  step="0.01"
                  min="0"
                  max="100"
                  value={formData.taxRate}
                  onChange={(e) => handleInputChange('taxRate', parseFloat(e.target.value) || 0)}
                  placeholder="0.00"
                  disabled={!formData.taxable}
                />
              </div>
              <div>
                <Label htmlFor="accountingCode">Accounting Code</Label>
                <Input
                  id="accountingCode"
                  value={formData.accountingCode}
                  onChange={(e) => handleInputChange('accountingCode', e.target.value)}
                  placeholder="e.g., 4000"
                />
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-2 pt-4 border-t">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {mode === 'edit' ? 'Updating...' : 'Creating...'}
                </>
              ) : (
                <>
                  <Package className="h-4 w-4 mr-2" />
                  {mode === 'edit' ? 'Update Item' : 'Create Item'}
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
