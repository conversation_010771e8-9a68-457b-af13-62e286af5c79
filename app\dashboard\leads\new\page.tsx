'use client'

import { useState } from 'react'
import { useR<PERSON><PERSON> } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { LeadForm } from '@/components/leads/lead-form'
import { ArrowLeft, UserPlus } from 'lucide-react'
import Link from 'next/link'

export default function NewLeadPage() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSuccess = () => {
    router.push('/dashboard/leads')
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/dashboard/leads">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Leads
            </Link>
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Add New Lead</h1>
            <p className="text-gray-500">Create a new lead in your sales pipeline</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" asChild>
            <Link href="/dashboard/leads/pipeline">
              Pipeline View
            </Link>
          </Button>
        </div>
      </div>

      {/* Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <UserPlus className="h-5 w-5 mr-2" />
            Lead Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <LeadForm
            isOpen={true}
            onClose={() => router.push('/dashboard/leads')}
            onSuccess={handleSuccess}
            mode="create"
          />
        </CardContent>
      </Card>
    </div>
  )
}
