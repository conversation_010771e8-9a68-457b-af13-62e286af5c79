'use client'

import { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import {
  Settings,
  Users,
  Shield,
  Palette,
  Globe,
  Bell,
  Zap,
  RefreshCw,
  CheckCircle,
  XCircle,
  AlertTriangle,
  TrendingUp,
  Database,
  Lock,
  Plug
} from 'lucide-react'
import { toast } from 'react-hot-toast'

interface SettingsAnalytics {
  summary: {
    totalUsers: number
    usersWithSettings: number
    settingsConfigurationRate: number
    totalSystemSettings: number
    companySettingsConfigured: boolean
    lastSettingsUpdate: string | null
  }
  companySettings: {
    configured: boolean
    currency: string
    timezone: string
    language: string
    taxRate: number
    branding: {
      primaryColor: string
      secondaryColor: string
      accentColor: string
      fontFamily: string
    }
    documentSettings: {
      invoicePrefix: string
      quotationPrefix: string
      contractPrefix: string
      invoiceNumbering: string
    }
  }
  userPreferences: {
    themes: Array<{
      theme: string
      count: number
      percentage: number
    }>
    languages: Array<{
      language: string
      count: number
      percentage: number
    }>
    timezones: Array<{
      timezone: string
      count: number
      percentage: number
    }>
    notifications: {
      emailEnabled: number
      pushEnabled: number
      smsEnabled: number
    }
  }
  systemSettings: {
    total: number
    categories: Array<{
      category: string
      count: number
    }>
    public: number
    private: number
    editable: number
    readonly: number
  }
  features: {
    enabled: number
    disabled: number
    usage: Array<{
      feature: string
      enabled: boolean
    }>
  }
  security: {
    twoFactorEnabled: boolean
    sessionTimeout: number
    passwordPolicyEnabled: boolean
    ipWhitelistCount: number
    allowedDomainsCount: number
    securityScore: number
  } | null
  integrations: {
    webhooksCount: number
    apiKeysCount: number
    connectedServicesCount: number
    integrationScore: number
  } | null
  notifications: {
    types: Array<{
      type: string
      enabled: boolean
    }>
    enabledCount: number
    disabledCount: number
  }
}

export function SettingsAnalytics() {
  const [analytics, setAnalytics] = useState<SettingsAnalytics | null>(null)
  const [loading, setLoading] = useState(true)

  const fetchAnalytics = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/settings/analytics')
      if (!response.ok) {
        throw new Error('Failed to fetch analytics')
      }

      const data = await response.json()
      setAnalytics(data)
    } catch (error) {
      toast.error('Failed to load settings analytics')
      console.error('Error fetching analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchAnalytics()
  }, [])

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`
  }

  const getSecurityScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getSecurityScoreBadge = (score: number) => {
    if (score >= 80) return 'bg-green-100 text-green-800'
    if (score >= 60) return 'bg-yellow-100 text-yellow-800'
    return 'bg-red-100 text-red-800'
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!analytics) {
    return (
      <div className="text-center py-8 text-gray-500">
        <Settings className="h-12 w-12 mx-auto mb-4 text-gray-300" />
        <p>Failed to load analytics data</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Settings Analytics</h3>
        <Button variant="outline" onClick={fetchAnalytics} size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-blue-100 rounded-full">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Total Users</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.summary.totalUsers}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-green-100 rounded-full">
                <Settings className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Settings Configured</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatPercentage(analytics.summary.settingsConfigurationRate)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-purple-100 rounded-full">
                <Database className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">System Settings</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.systemSettings.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className={`p-3 rounded-full ${analytics.summary.companySettingsConfigured ? 'bg-green-100' : 'bg-red-100'}`}>
                {analytics.summary.companySettingsConfigured ? (
                  <CheckCircle className="h-6 w-6 text-green-600" />
                ) : (
                  <XCircle className="h-6 w-6 text-red-600" />
                )}
              </div>
              <div>
                <p className="text-sm text-gray-500">Company Setup</p>
                <p className={`text-2xl font-bold ${analytics.summary.companySettingsConfigured ? 'text-green-600' : 'text-red-600'}`}>
                  {analytics.summary.companySettingsConfigured ? 'Complete' : 'Incomplete'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Company Settings Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Settings className="h-5 w-5 mr-2" />
            Company Settings Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-sm text-gray-500">Currency</p>
              <p className="font-semibold">{analytics.companySettings.currency}</p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-500">Timezone</p>
              <p className="font-semibold">{analytics.companySettings.timezone}</p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-500">Language</p>
              <p className="font-semibold">{analytics.companySettings.language.toUpperCase()}</p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-500">Tax Rate</p>
              <p className="font-semibold">{analytics.companySettings.taxRate}%</p>
            </div>
          </div>
          
          <div className="mt-6">
            <h4 className="font-medium mb-3">Document Prefixes</h4>
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-500">Invoices</p>
                <p className="font-semibold">{analytics.companySettings.documentSettings.invoicePrefix}</p>
              </div>
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-500">Quotations</p>
                <p className="font-semibold">{analytics.companySettings.documentSettings.quotationPrefix}</p>
              </div>
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-500">Contracts</p>
                <p className="font-semibold">{analytics.companySettings.documentSettings.contractPrefix}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Security & Integrations */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Security Score */}
        {analytics.security && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Shield className="h-5 w-5 mr-2" />
                Security Score
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center mb-4">
                <div className={`text-4xl font-bold ${getSecurityScoreColor(analytics.security.securityScore)}`}>
                  {analytics.security.securityScore}%
                </div>
                <Badge className={getSecurityScoreBadge(analytics.security.securityScore)}>
                  {analytics.security.securityScore >= 80 ? 'Excellent' : 
                   analytics.security.securityScore >= 60 ? 'Good' : 'Needs Improvement'}
                </Badge>
              </div>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Two-Factor Auth</span>
                  {analytics.security.twoFactorEnabled ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-600" />
                  )}
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Password Policy</span>
                  {analytics.security.passwordPolicyEnabled ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-600" />
                  )}
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Session Timeout</span>
                  <span className="text-sm font-medium">{analytics.security.sessionTimeout}min</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">IP Whitelist</span>
                  <span className="text-sm font-medium">{analytics.security.ipWhitelistCount} IPs</span>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Integration Status */}
        {analytics.integrations && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Plug className="h-5 w-5 mr-2" />
                Integration Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center mb-4">
                <div className="text-4xl font-bold text-blue-600">
                  {analytics.integrations.integrationScore}%
                </div>
                <p className="text-sm text-gray-500">Integration Coverage</p>
              </div>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Webhooks</span>
                  <Badge variant="outline">{analytics.integrations.webhooksCount}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">API Keys</span>
                  <Badge variant="outline">{analytics.integrations.apiKeysCount}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Connected Services</span>
                  <Badge variant="outline">{analytics.integrations.connectedServicesCount}</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* User Preferences */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Theme Preferences */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Palette className="h-5 w-5 mr-2" />
              Theme Preferences
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.userPreferences.themes.map((theme) => (
                <div key={theme.theme} className="flex items-center justify-between">
                  <span className="text-sm capitalize">{theme.theme}</span>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium">{theme.count}</span>
                    <div className="w-16">
                      <Progress value={theme.percentage} className="h-2" />
                    </div>
                    <span className="text-xs text-gray-500 w-10">
                      {formatPercentage(theme.percentage)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Language Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Globe className="h-5 w-5 mr-2" />
              Language Distribution
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.userPreferences.languages.map((lang) => (
                <div key={lang.language} className="flex items-center justify-between">
                  <span className="text-sm uppercase">{lang.language}</span>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium">{lang.count}</span>
                    <div className="w-16">
                      <Progress value={lang.percentage} className="h-2" />
                    </div>
                    <span className="text-xs text-gray-500 w-10">
                      {formatPercentage(lang.percentage)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Notification Preferences */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Bell className="h-5 w-5 mr-2" />
              Notification Preferences
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Email</span>
                <div className="flex items-center space-x-2">
                  <div className="w-16">
                    <Progress value={analytics.userPreferences.notifications.emailEnabled} className="h-2" />
                  </div>
                  <span className="text-xs text-gray-500 w-10">
                    {formatPercentage(analytics.userPreferences.notifications.emailEnabled)}
                  </span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Push</span>
                <div className="flex items-center space-x-2">
                  <div className="w-16">
                    <Progress value={analytics.userPreferences.notifications.pushEnabled} className="h-2" />
                  </div>
                  <span className="text-xs text-gray-500 w-10">
                    {formatPercentage(analytics.userPreferences.notifications.pushEnabled)}
                  </span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">SMS</span>
                <div className="flex items-center space-x-2">
                  <div className="w-16">
                    <Progress value={analytics.userPreferences.notifications.smsEnabled} className="h-2" />
                  </div>
                  <span className="text-xs text-gray-500 w-10">
                    {formatPercentage(analytics.userPreferences.notifications.smsEnabled)}
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Features & System Settings */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Feature Usage */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Zap className="h-5 w-5 mr-2" />
              Feature Usage
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-center mb-4">
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600">{analytics.features.enabled}</div>
                <p className="text-sm text-gray-500">Enabled Features</p>
              </div>
              <div className="mx-8 text-gray-300">/</div>
              <div className="text-center">
                <div className="text-3xl font-bold text-gray-400">{analytics.features.disabled}</div>
                <p className="text-sm text-gray-500">Disabled Features</p>
              </div>
            </div>
            
            <div className="space-y-2">
              {analytics.features.usage.slice(0, 6).map((feature) => (
                <div key={feature.feature} className="flex items-center justify-between">
                  <span className="text-sm capitalize">{feature.feature.replace(/([A-Z])/g, ' $1').trim()}</span>
                  {feature.enabled ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <XCircle className="h-4 w-4 text-gray-400" />
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* System Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Database className="h-5 w-5 mr-2" />
              System Settings
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{analytics.systemSettings.public}</div>
                <p className="text-sm text-gray-500">Public</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-600">{analytics.systemSettings.private}</div>
                <p className="text-sm text-gray-500">Private</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{analytics.systemSettings.editable}</div>
                <p className="text-sm text-gray-500">Editable</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">{analytics.systemSettings.readonly}</div>
                <p className="text-sm text-gray-500">Read-only</p>
              </div>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium text-sm">Categories</h4>
              {analytics.systemSettings.categories.slice(0, 4).map((category) => (
                <div key={category.category} className="flex items-center justify-between">
                  <span className="text-sm capitalize">{category.category}</span>
                  <Badge variant="outline">{category.count}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Notification Types */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Bell className="h-5 w-5 mr-2" />
            Notification Configuration
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center mb-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600">{analytics.notifications.enabledCount}</div>
              <p className="text-sm text-gray-500">Enabled</p>
            </div>
            <div className="mx-8 text-gray-300">/</div>
            <div className="text-center">
              <div className="text-3xl font-bold text-gray-400">{analytics.notifications.disabledCount}</div>
              <p className="text-sm text-gray-500">Disabled</p>
            </div>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
            {analytics.notifications.types.map((notification) => (
              <div key={notification.type} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="text-sm capitalize">
                  {notification.type.replace(/([A-Z])/g, ' $1').trim()}
                </span>
                {notification.enabled ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <XCircle className="h-4 w-4 text-gray-400" />
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
