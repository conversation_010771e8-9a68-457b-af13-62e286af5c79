"use strict";(()=>{var e={};e.id=798,e.ids=[798],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},32081:e=>{e.exports=require("child_process")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},73837:e=>{e.exports=require("util")},96548:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>I,originalPathname:()=>v,patchFetch:()=>T,requestAsyncStorage:()=>g,routeModule:()=>y,serverHooks:()=>f,staticGenerationAsyncStorage:()=>P,staticGenerationBailout:()=>S});var i={};t.r(i),t.d(i,{POST:()=>l});var a=t(95419),n=t(69108),s=t(99678),o=t(78070),c=t(32455),d=t(15922),u=t(9108);async function l(e){try{let r;let t=await e.text(),i=(0,c.headers)().get("stripe-signature");if(!i)return console.error("Missing Stripe signature"),o.Z.json({error:"Missing signature"},{status:400});if(!process.env.STRIPE_WEBHOOK_SECRET)return console.error("Missing STRIPE_WEBHOOK_SECRET"),o.Z.json({error:"Webhook secret not configured"},{status:500});try{r=(0,d.$t)(t,i,process.env.STRIPE_WEBHOOK_SECRET)}catch(e){return console.error("Webhook signature verification failed:",e),o.Z.json({error:"Invalid signature"},{status:400})}switch(console.log("Processing Stripe webhook:",r.type),r.type){case"customer.subscription.created":await p(r.data.object);break;case"customer.subscription.updated":await _(r.data.object);break;case"customer.subscription.deleted":await m(r.data.object);break;case"invoice.payment_succeeded":await b(r.data.object);break;case"invoice.payment_failed":await h(r.data.object);break;case"customer.subscription.trial_will_end":await w(r.data.object);break;default:console.log(`Unhandled event type: ${r.type}`)}return o.Z.json({received:!0})}catch(e){return console.error("Webhook error:",e),o.Z.json({error:"Webhook handler failed"},{status:500})}}async function p(e){try{let r=await d.Ag.customers.retrieve(e.customer);if(r.deleted){console.error("Customer was deleted");return}let t=r.metadata?.companyId;if(!t){console.error("No companyId found in customer metadata");return}let i=e.items.data[0]?.price.id,a=await u._.pricingPlan.findFirst({where:{OR:[{stripePriceId:i},{stripeYearlyPriceId:i}]}});if(!a){console.error("No pricing plan found for price ID:",i);return}let n=e.items.data[0]?.price.recurring?.interval==="year"?"YEARLY":"MONTHLY";await u._.subscription.upsert({where:{stripeSubscriptionId:e.id},update:{status:E(e.status),currentPeriodStart:new Date(1e3*e.current_period_start),currentPeriodEnd:new Date(1e3*e.current_period_end),trialEnd:e.trial_end?new Date(1e3*e.trial_end):null,cancelAtPeriodEnd:e.cancel_at_period_end,billingCycle:n},create:{companyId:t,pricingPlanId:a.id,stripeSubscriptionId:e.id,stripeCustomerId:e.customer,status:E(e.status),billingCycle:n,currentPeriodStart:new Date(1e3*e.current_period_start),currentPeriodEnd:new Date(1e3*e.current_period_end),trialEnd:e.trial_end?new Date(1e3*e.trial_end):null,cancelAtPeriodEnd:e.cancel_at_period_end}}),console.log("Subscription created/updated:",e.id)}catch(e){console.error("Error handling subscription created:",e)}}async function _(e){try{let r=e.items.data[0]?.price.id,t=await u._.pricingPlan.findFirst({where:{OR:[{stripePriceId:r},{stripeYearlyPriceId:r}]}}),i=e.items.data[0]?.price.recurring?.interval==="year"?"YEARLY":"MONTHLY";await u._.subscription.update({where:{stripeSubscriptionId:e.id},data:{pricingPlanId:t?.id,status:E(e.status),billingCycle:i,currentPeriodStart:new Date(1e3*e.current_period_start),currentPeriodEnd:new Date(1e3*e.current_period_end),trialEnd:e.trial_end?new Date(1e3*e.trial_end):null,cancelAtPeriodEnd:e.cancel_at_period_end}}),console.log("Subscription updated:",e.id)}catch(e){console.error("Error handling subscription updated:",e)}}async function m(e){try{await u._.subscription.update({where:{stripeSubscriptionId:e.id},data:{status:"CANCELED",canceledAt:new Date}}),console.log("Subscription canceled:",e.id)}catch(e){console.error("Error handling subscription deleted:",e)}}async function b(e){try{e.subscription&&await u._.subscription.update({where:{stripeSubscriptionId:e.subscription},data:{status:"ACTIVE"}}),console.log("Payment succeeded for invoice:",e.id)}catch(e){console.error("Error handling payment succeeded:",e)}}async function h(e){try{e.subscription&&await u._.subscription.update({where:{stripeSubscriptionId:e.subscription},data:{status:"PAST_DUE"}}),console.log("Payment failed for invoice:",e.id)}catch(e){console.error("Error handling payment failed:",e)}}async function w(e){try{console.log("Trial will end for subscription:",e.id)}catch(e){console.error("Error handling trial will end:",e)}}function E(e){switch(e){case"active":return"ACTIVE";case"trialing":return"TRIALING";case"past_due":case"unpaid":return"PAST_DUE";case"canceled":case"incomplete_expired":return"CANCELED";case"incomplete":return"INCOMPLETE";case"paused":return"PAUSED";default:return"UNKNOWN"}}let y=new a.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/webhooks/stripe/route",pathname:"/api/webhooks/stripe",filename:"route",bundlePath:"app/api/webhooks/stripe/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\webhooks\\stripe\\route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:g,staticGenerationAsyncStorage:P,serverHooks:f,headerHooks:I,staticGenerationBailout:S}=y,v="/api/webhooks/stripe/route";function T(){return(0,s.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:P})}},9108:(e,r,t)=>{t.d(r,{_:()=>a});let i=require("@prisma/client"),a=globalThis.prisma??new i.PrismaClient},15922:(e,r,t)=>{t.d(r,{$t:()=>d,Ag:()=>a,FL:()=>c,R:()=>o,Sh:()=>s,hT:()=>n});var i=t(91211);if(!process.env.STRIPE_SECRET_KEY)throw Error("STRIPE_SECRET_KEY is not set in environment variables");let a=new i.Z(process.env.STRIPE_SECRET_KEY,{apiVersion:"2024-06-20",typescript:!0}),n=async e=>(await a.customers.list({email:e,limit:1})).data[0]||null,s=async e=>await a.customers.create({email:e.email,name:e.name,metadata:{companyId:e.companyId}}),o=async e=>{let r={customer:e.customerId,payment_method_types:["card"],line_items:[{price:e.priceId,quantity:1}],mode:"subscription",success_url:e.successUrl,cancel_url:e.cancelUrl,allow_promotion_codes:!0};return e.trialPeriodDays&&e.trialPeriodDays>0&&(r.subscription_data={trial_period_days:e.trialPeriodDays}),await a.checkout.sessions.create(r)},c=async e=>await a.billingPortal.sessions.create({customer:e.customerId,return_url:e.returnUrl}),d=(e,r,t)=>a.webhooks.constructEvent(e,r,t)}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),i=r.X(0,[1638,6206,2455,1211],()=>t(96548));module.exports=i})();