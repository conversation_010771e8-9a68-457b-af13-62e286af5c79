import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    // Public endpoint for fetching branding configuration
    let branding = {}
    
    try {
      const globalConfig = await prisma.globalConfiguration.findFirst()
      
      if (globalConfig && globalConfig.config) {
        const config = globalConfig.config as any
        
        // Extract branding-related configuration
        branding = {
          appName: config.appName || 'SaaS Platform',
          logoUrl: config.logoUrl || '',
          faviconUrl: config.faviconUrl || '',
          primaryColor: config.primaryColor || '#3b82f6',
          secondaryColor: config.secondaryColor || '#64748b',
          accentColor: config.accentColor || '#10b981',
          backgroundColor: config.backgroundColor || '#ffffff',
          textColor: config.textColor || '#1f2937',
          theme: config.theme || 'light',
          fontFamily: config.fontFamily || 'Inter, sans-serif',
          customCss: config.customCss || ''
        }
      } else {
        // Return default branding if no configuration exists
        branding = {
          appName: 'SaaS Platform',
          logoUrl: '',
          faviconUrl: '',
          primaryColor: '#3b82f6',
          secondaryColor: '#64748b',
          accentColor: '#10b981',
          backgroundColor: '#ffffff',
          textColor: '#1f2937',
          theme: 'light',
          fontFamily: 'Inter, sans-serif',
          customCss: ''
        }
      }
    } catch (error) {
      console.error('Error fetching branding configuration:', error)
      // Return default branding on error
      branding = {
        appName: 'SaaS Platform',
        logoUrl: '',
        faviconUrl: '',
        primaryColor: '#3b82f6',
        secondaryColor: '#64748b',
        accentColor: '#10b981',
        backgroundColor: '#ffffff',
        textColor: '#1f2937',
        theme: 'light',
        fontFamily: 'Inter, sans-serif',
        customCss: ''
      }
    }

    return NextResponse.json({
      success: true,
      branding
    })
  } catch (error) {
    console.error('Error in branding API:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
