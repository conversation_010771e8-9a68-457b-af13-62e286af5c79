import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    // Public endpoint for fetching branding configuration
    let branding = {}
    
    try {
      const configs = await prisma.globalConfig.findMany()

      if (configs && configs.length > 0) {
        // Convert array to object for easier access
        const configObject = configs.reduce((acc, config) => {
          let value = config.value

          // Parse JSON values
          if (config.type === 'boolean') {
            value = value === 'true'
          } else if (config.type === 'number') {
            value = parseInt(value)
          } else if (config.type === 'json') {
            try {
              value = JSON.parse(value)
            } catch (e) {
              value = {}
            }
          }

          acc[config.key] = value
          return acc
        }, {} as Record<string, any>)

        // Extract branding-related configuration
        branding = {
          appName: configObject.appName || 'SaaS Platform',
          logoUrl: configObject.logoUrl || '',
          faviconUrl: configObject.faviconUrl || '',
          primaryColor: configObject.primaryColor || '#3b82f6',
          secondaryColor: configObject.secondaryColor || '#64748b',
          accentColor: configObject.accentColor || '#10b981',
          backgroundColor: configObject.backgroundColor || '#ffffff',
          textColor: configObject.textColor || '#1f2937',
          theme: configObject.theme || 'light',
          fontFamily: configObject.fontFamily || 'Inter, sans-serif',
          customCss: configObject.customCss || ''
        }
      } else {
        // Return default branding if no configuration exists
        branding = {
          appName: 'SaaS Platform',
          logoUrl: '',
          faviconUrl: '',
          primaryColor: '#3b82f6',
          secondaryColor: '#64748b',
          accentColor: '#10b981',
          backgroundColor: '#ffffff',
          textColor: '#1f2937',
          theme: 'light',
          fontFamily: 'Inter, sans-serif',
          customCss: ''
        }
      }
    } catch (error) {
      console.error('Error fetching branding configuration:', error)
      // Return default branding on error
      branding = {
        appName: 'SaaS Platform',
        logoUrl: '',
        faviconUrl: '',
        primaryColor: '#3b82f6',
        secondaryColor: '#64748b',
        accentColor: '#10b981',
        backgroundColor: '#ffffff',
        textColor: '#1f2937',
        theme: 'light',
        fontFamily: 'Inter, sans-serif',
        customCss: ''
      }
    }

    return NextResponse.json({
      success: true,
      branding
    })
  } catch (error) {
    console.error('Error in branding API:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
