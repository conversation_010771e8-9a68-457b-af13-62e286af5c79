var e=require("@hookform/resolvers");function r(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var t=/*#__PURE__*/r(require("vest/promisify")),o=function(e,r){var t={};for(var o in e)t[o]||(t[o]={message:e[o][0],type:""}),r&&(t[o].types=e[o].reduce(function(e,r,t){return(e[t]=r)&&e},{}));return t};exports.vestResolver=function(r,s,i){return void 0===i&&(i={}),function(s,n,a){try{var u=function(r){return r.hasErrors()?{values:{},errors:e.toNestErrors(o(r.getErrors(),!a.shouldUseNativeValidation&&"all"===a.criteriaMode),a)}:(a.shouldUseNativeValidation&&e.validateFieldsNatively({},a),{values:s,errors:{}})};return Promise.resolve("sync"===i.mode?u(r(s,a.names,n)):Promise.resolve(t.default(r)(s,a.names,n)).then(u))}catch(e){return Promise.reject(e)}}};
//# sourceMappingURL=vest.js.map
