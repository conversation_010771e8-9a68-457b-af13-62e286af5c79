import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    // Check if plans already exist first
    const existingPlans = await prisma.pricingPlan.count()
    if (existingPlans > 0) {
      return NextResponse.json(
        { success: false, error: 'Pricing plans already exist' },
        { status: 400 }
      )
    }

    // If no plans exist, allow seeding without authentication for initial setup
    // Otherwise, require super admin authentication
    if (existingPlans === 0) {
      // Allow initial seeding without authentication
    } else {
      const session = await getServerSession(authOptions)

      if (!session?.user || session.user.role !== 'SUPER_ADMIN') {
        return NextResponse.json(
          { success: false, error: 'Unauthorized' },
          { status: 401 }
        )
      }
    }

    // Default pricing plans
    const defaultPlans = [
      {
        name: 'Free',
        description: 'Perfect for getting started with basic features',
        monthlyPrice: 0,
        yearlyPrice: 0,
        currency: 'USD',
        maxUsers: 2,
        maxCompanies: 1,
        maxCustomers: 50,
        maxQuotations: 10,
        maxInvoices: 10,
        maxContracts: 5,
        maxStorage: BigInt(1073741824), // 1GB
        features: {
          basicReporting: true,
          emailSupport: true,
          mobileApp: false,
          advancedAnalytics: false,
          customBranding: false,
          apiAccess: false,
          prioritySupport: false,
          customIntegrations: false,
          advancedSecurity: false,
          dedicatedManager: false
        },
        isActive: true,
        isPublic: true,
        trialDays: 0,
        sortOrder: 1
      },
      {
        name: 'Pro',
        description: 'Best for growing businesses with advanced features',
        monthlyPrice: 29,
        yearlyPrice: 290, // 2 months free
        currency: 'USD',
        maxUsers: 10,
        maxCompanies: 3,
        maxCustomers: 500,
        maxQuotations: 100,
        maxInvoices: 100,
        maxContracts: 50,
        maxStorage: BigInt(***********), // 10GB
        features: {
          basicReporting: true,
          emailSupport: true,
          mobileApp: true,
          advancedAnalytics: true,
          customBranding: true,
          apiAccess: true,
          prioritySupport: false,
          customIntegrations: false,
          advancedSecurity: false,
          dedicatedManager: false
        },
        isActive: true,
        isPublic: true,
        trialDays: 14,
        sortOrder: 2
      },
      {
        name: 'Enterprise',
        description: 'For large organizations with custom requirements',
        monthlyPrice: 99,
        yearlyPrice: 990, // 2 months free
        currency: 'USD',
        maxUsers: 50,
        maxCompanies: 10,
        maxCustomers: 5000,
        maxQuotations: 1000,
        maxInvoices: 1000,
        maxContracts: 500,
        maxStorage: BigInt(***********0), // 100GB
        features: {
          basicReporting: true,
          emailSupport: true,
          mobileApp: true,
          advancedAnalytics: true,
          customBranding: true,
          apiAccess: true,
          prioritySupport: true,
          customIntegrations: true,
          advancedSecurity: true,
          dedicatedManager: true
        },
        isActive: true,
        isPublic: true,
        trialDays: 30,
        sortOrder: 3
      }
    ]

    // Create all plans
    const createdPlans = await Promise.all(
      defaultPlans.map(plan => 
        prisma.pricingPlan.create({
          data: plan
        })
      )
    )

    return NextResponse.json({
      success: true,
      message: 'Default pricing plans created successfully',
      data: createdPlans.map(plan => ({
        ...plan,
        monthlyPrice: Number(plan.monthlyPrice),
        yearlyPrice: plan.yearlyPrice ? Number(plan.yearlyPrice) : null,
        maxStorage: Number(plan.maxStorage)
      }))
    })

  } catch (error) {
    console.error('Error seeding pricing plans:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to seed pricing plans' },
      { status: 500 }
    )
  }
}
