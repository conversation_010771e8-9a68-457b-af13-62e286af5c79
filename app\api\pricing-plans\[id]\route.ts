import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const plan = await prisma.pricingPlan.findUnique({
      where: { id: params.id },
      select: {
        id: true,
        name: true,
        description: true,
        monthlyPrice: true,
        yearlyPrice: true,
        currency: true,
        maxUsers: true,
        maxCompanies: true,
        maxCustomers: true,
        maxQuotations: true,
        maxInvoices: true,
        maxContracts: true,
        maxStorage: true,
        features: true,
        isActive: true,
        isPublic: true,
        trialDays: true,
        sortOrder: true,
        stripeProductId: true,
        stripePriceId: true,
        stripeYearlyPriceId: true,
        createdAt: true,
        updatedAt: true
      }
    })

    if (!plan) {
      return NextResponse.json(
        { success: false, error: 'Pricing plan not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        ...plan,
        monthlyPrice: Number(plan.monthlyPrice),
        yearlyPrice: plan.yearlyPrice ? Number(plan.yearlyPrice) : null,
        maxStorage: Number(plan.maxStorage)
      }
    })

  } catch (error) {
    console.error('Error fetching pricing plan:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch pricing plan' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      name,
      description,
      monthlyPrice,
      yearlyPrice,
      currency,
      maxUsers,
      maxCompanies,
      maxCustomers,
      maxQuotations,
      maxInvoices,
      maxContracts,
      maxStorage,
      features,
      isActive,
      isPublic,
      trialDays,
      stripeProductId,
      stripePriceId,
      stripeYearlyPriceId
    } = body

    // Check if plan exists
    const existingPlan = await prisma.pricingPlan.findUnique({
      where: { id: params.id }
    })

    if (!existingPlan) {
      return NextResponse.json(
        { success: false, error: 'Pricing plan not found' },
        { status: 404 }
      )
    }

    const updatedPlan = await prisma.pricingPlan.update({
      where: { id: params.id },
      data: {
        name,
        description,
        monthlyPrice,
        yearlyPrice,
        currency,
        maxUsers,
        maxCompanies,
        maxCustomers,
        maxQuotations,
        maxInvoices,
        maxContracts,
        maxStorage: maxStorage ? BigInt(maxStorage) : undefined,
        features,
        isActive,
        isPublic,
        trialDays,
        stripeProductId,
        stripePriceId,
        stripeYearlyPriceId
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        ...updatedPlan,
        monthlyPrice: Number(updatedPlan.monthlyPrice),
        yearlyPrice: updatedPlan.yearlyPrice ? Number(updatedPlan.yearlyPrice) : null,
        maxStorage: Number(updatedPlan.maxStorage)
      }
    })

  } catch (error) {
    console.error('Error updating pricing plan:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update pricing plan' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check if plan exists
    const existingPlan = await prisma.pricingPlan.findUnique({
      where: { id: params.id }
    })

    if (!existingPlan) {
      return NextResponse.json(
        { success: false, error: 'Pricing plan not found' },
        { status: 404 }
      )
    }

    // Check if any subscriptions are using this plan
    // Note: This would need to be implemented when subscription-plan relations are added
    // For now, we'll just delete the plan

    await prisma.pricingPlan.delete({
      where: { id: params.id }
    })

    return NextResponse.json({
      success: true,
      message: 'Pricing plan deleted successfully'
    })

  } catch (error) {
    console.error('Error deleting pricing plan:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete pricing plan' },
      { status: 500 }
    )
  }
}
