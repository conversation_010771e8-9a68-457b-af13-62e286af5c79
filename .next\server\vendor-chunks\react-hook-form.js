"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-hook-form";
exports.ids = ["vendor-chunks/react-hook-form"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/react-hook-form/dist/index.esm.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Controller: () => (/* binding */ Controller),\n/* harmony export */   Form: () => (/* binding */ Form),\n/* harmony export */   FormProvider: () => (/* binding */ FormProvider),\n/* harmony export */   appendErrors: () => (/* binding */ appendErrors),\n/* harmony export */   createFormControl: () => (/* binding */ createFormControl),\n/* harmony export */   get: () => (/* binding */ get),\n/* harmony export */   set: () => (/* binding */ set),\n/* harmony export */   useController: () => (/* binding */ useController),\n/* harmony export */   useFieldArray: () => (/* binding */ useFieldArray),\n/* harmony export */   useForm: () => (/* binding */ useForm),\n/* harmony export */   useFormContext: () => (/* binding */ useFormContext),\n/* harmony export */   useFormState: () => (/* binding */ useFormState),\n/* harmony export */   useWatch: () => (/* binding */ useWatch)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\n\nvar isCheckBoxInput = (element)=>element.type === \"checkbox\";\nvar isDateObject = (value1)=>value1 instanceof Date;\nvar isNullOrUndefined = (value1)=>value1 == null;\nconst isObjectType = (value1)=>typeof value1 === \"object\";\nvar isObject = (value1)=>!isNullOrUndefined(value1) && !Array.isArray(value1) && isObjectType(value1) && !isDateObject(value1);\nvar getEventValue = (event)=>isObject(event) && event.target ? isCheckBoxInput(event.target) ? event.target.checked : event.target.value : event;\nvar getNodeParentName = (name)=>name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\nvar isNameInFieldArray = (names, name)=>names.has(getNodeParentName(name));\nvar isPlainObject = (tempObject)=>{\n    const prototypeCopy = tempObject.constructor && tempObject.constructor.prototype;\n    return isObject(prototypeCopy) && prototypeCopy.hasOwnProperty(\"isPrototypeOf\");\n};\nvar isWeb =  false && 0;\nfunction cloneObject(data) {\n    let copy;\n    const isArray = Array.isArray(data);\n    const isFileListInstance = typeof FileList !== \"undefined\" ? data instanceof FileList : false;\n    if (data instanceof Date) {\n        copy = new Date(data);\n    } else if (!(isWeb && (data instanceof Blob || isFileListInstance)) && (isArray || isObject(data))) {\n        copy = isArray ? [] : {};\n        if (!isArray && !isPlainObject(data)) {\n            copy = data;\n        } else {\n            for(const key in data){\n                if (data.hasOwnProperty(key)) {\n                    copy[key] = cloneObject(data[key]);\n                }\n            }\n        }\n    } else {\n        return data;\n    }\n    return copy;\n}\nvar isKey = (value1)=>/^\\w*$/.test(value1);\nvar isUndefined = (val)=>val === undefined;\nvar compact = (value1)=>Array.isArray(value1) ? value1.filter(Boolean) : [];\nvar stringToPath = (input)=>compact(input.replace(/[\"|']|\\]/g, \"\").split(/\\.|\\[/));\nvar get = (object, path, defaultValue)=>{\n    if (!path || !isObject(object)) {\n        return defaultValue;\n    }\n    const result = (isKey(path) ? [\n        path\n    ] : stringToPath(path)).reduce((result, key)=>isNullOrUndefined(result) ? result : result[key], object);\n    return isUndefined(result) || result === object ? isUndefined(object[path]) ? defaultValue : object[path] : result;\n};\nvar isBoolean = (value1)=>typeof value1 === \"boolean\";\nvar set = (object, path, value1)=>{\n    let index = -1;\n    const tempPath = isKey(path) ? [\n        path\n    ] : stringToPath(path);\n    const length = tempPath.length;\n    const lastIndex = length - 1;\n    while(++index < length){\n        const key = tempPath[index];\n        let newValue = value1;\n        if (index !== lastIndex) {\n            const objValue = object[key];\n            newValue = isObject(objValue) || Array.isArray(objValue) ? objValue : !isNaN(+tempPath[index + 1]) ? [] : {};\n        }\n        if (key === \"__proto__\" || key === \"constructor\" || key === \"prototype\") {\n            return;\n        }\n        object[key] = newValue;\n        object = object[key];\n    }\n};\nconst EVENTS = {\n    BLUR: \"blur\",\n    FOCUS_OUT: \"focusout\",\n    CHANGE: \"change\"\n};\nconst VALIDATION_MODE = {\n    onBlur: \"onBlur\",\n    onChange: \"onChange\",\n    onSubmit: \"onSubmit\",\n    onTouched: \"onTouched\",\n    all: \"all\"\n};\nconst INPUT_VALIDATION_RULES = {\n    max: \"max\",\n    min: \"min\",\n    maxLength: \"maxLength\",\n    minLength: \"minLength\",\n    pattern: \"pattern\",\n    required: \"required\",\n    validate: \"validate\"\n};\nconst HookFormContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nHookFormContext.displayName = \"HookFormContext\";\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */ const useFormContext = ()=>react__WEBPACK_IMPORTED_MODULE_0__.useContext(HookFormContext);\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */ const FormProvider = (props)=>{\n    const { children, ...data } = props;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(HookFormContext.Provider, {\n        value: data\n    }, children);\n};\nvar getProxyFormState = (formState, control, localProxyFormState, isRoot = true)=>{\n    const result = {\n        defaultValues: control._defaultValues\n    };\n    for(const key in formState){\n        Object.defineProperty(result, key, {\n            get: ()=>{\n                const _key = key;\n                if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n                    control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n                }\n                localProxyFormState && (localProxyFormState[_key] = true);\n                return formState[_key];\n            }\n        });\n    }\n    return result;\n};\nconst useIsomorphicLayoutEffect =  false ? 0 : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */ function useFormState(props) {\n    const methods = useFormContext();\n    const { control = methods.control, disabled, name, exact } = props || {};\n    const [formState, updateFormState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._formState);\n    const _localProxyFormState = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n        isDirty: false,\n        isLoading: false,\n        dirtyFields: false,\n        touchedFields: false,\n        validatingFields: false,\n        isValidating: false,\n        isValid: false,\n        errors: false\n    });\n    useIsomorphicLayoutEffect(()=>control._subscribe({\n            name,\n            formState: _localProxyFormState.current,\n            exact,\n            callback: (formState)=>{\n                !disabled && updateFormState({\n                    ...control._formState,\n                    ...formState\n                });\n            }\n        }), [\n        name,\n        disabled,\n        exact\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        _localProxyFormState.current.isValid && control._setValid(true);\n    }, [\n        control\n    ]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>getProxyFormState(formState, control, _localProxyFormState.current, false), [\n        formState,\n        control\n    ]);\n}\nvar isString = (value1)=>typeof value1 === \"string\";\nvar generateWatchOutput = (names, _names, formValues, isGlobal, defaultValue)=>{\n    if (isString(names)) {\n        isGlobal && _names.watch.add(names);\n        return get(formValues, names, defaultValue);\n    }\n    if (Array.isArray(names)) {\n        return names.map((fieldName)=>(isGlobal && _names.watch.add(fieldName), get(formValues, fieldName)));\n    }\n    isGlobal && (_names.watchAll = true);\n    return formValues;\n};\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */ function useWatch(props) {\n    const methods = useFormContext();\n    const { control = methods.control, name, defaultValue, disabled, exact } = props || {};\n    const _defaultValue = react__WEBPACK_IMPORTED_MODULE_0__.useRef(defaultValue);\n    const [value1, updateValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._getWatch(name, _defaultValue.current));\n    useIsomorphicLayoutEffect(()=>control._subscribe({\n            name,\n            formState: {\n                values: true\n            },\n            exact,\n            callback: (formState)=>!disabled && updateValue(generateWatchOutput(name, control._names, formState.values || control._formValues, false, _defaultValue.current))\n        }), [\n        name,\n        control,\n        disabled,\n        exact\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>control._removeUnmounted());\n    return value1;\n}\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */ function useController(props) {\n    const methods = useFormContext();\n    const { name, disabled, control = methods.control, shouldUnregister } = props;\n    const isArrayField = isNameInFieldArray(control._names.array, name);\n    const value1 = useWatch({\n        control,\n        name,\n        defaultValue: get(control._formValues, name, get(control._defaultValues, name, props.defaultValue)),\n        exact: true\n    });\n    const formState = useFormState({\n        control,\n        name,\n        exact: true\n    });\n    const _props = react__WEBPACK_IMPORTED_MODULE_0__.useRef(props);\n    const _registerProps = react__WEBPACK_IMPORTED_MODULE_0__.useRef(control.register(name, {\n        ...props.rules,\n        value: value1,\n        ...isBoolean(props.disabled) ? {\n            disabled: props.disabled\n        } : {}\n    }));\n    const fieldState = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>Object.defineProperties({}, {\n            invalid: {\n                enumerable: true,\n                get: ()=>!!get(formState.errors, name)\n            },\n            isDirty: {\n                enumerable: true,\n                get: ()=>!!get(formState.dirtyFields, name)\n            },\n            isTouched: {\n                enumerable: true,\n                get: ()=>!!get(formState.touchedFields, name)\n            },\n            isValidating: {\n                enumerable: true,\n                get: ()=>!!get(formState.validatingFields, name)\n            },\n            error: {\n                enumerable: true,\n                get: ()=>get(formState.errors, name)\n            }\n        }), [\n        formState,\n        name\n    ]);\n    const onChange = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>_registerProps.current.onChange({\n            target: {\n                value: getEventValue(event),\n                name: name\n            },\n            type: EVENTS.CHANGE\n        }), [\n        name\n    ]);\n    const onBlur = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>_registerProps.current.onBlur({\n            target: {\n                value: get(control._formValues, name),\n                name: name\n            },\n            type: EVENTS.BLUR\n        }), [\n        name,\n        control._formValues\n    ]);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((elm)=>{\n        const field = get(control._fields, name);\n        if (field && elm) {\n            field._f.ref = {\n                focus: ()=>elm.focus && elm.focus(),\n                select: ()=>elm.select && elm.select(),\n                setCustomValidity: (message)=>elm.setCustomValidity(message),\n                reportValidity: ()=>elm.reportValidity()\n            };\n        }\n    }, [\n        control._fields,\n        name\n    ]);\n    const field = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n            name,\n            value: value1,\n            ...isBoolean(disabled) || formState.disabled ? {\n                disabled: formState.disabled || disabled\n            } : {},\n            onChange,\n            onBlur,\n            ref\n        }), [\n        name,\n        disabled,\n        formState.disabled,\n        onChange,\n        onBlur,\n        ref,\n        value1\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const _shouldUnregisterField = control._options.shouldUnregister || shouldUnregister;\n        control.register(name, {\n            ..._props.current.rules,\n            ...isBoolean(_props.current.disabled) ? {\n                disabled: _props.current.disabled\n            } : {}\n        });\n        const updateMounted = (name, value1)=>{\n            const field = get(control._fields, name);\n            if (field && field._f) {\n                field._f.mount = value1;\n            }\n        };\n        updateMounted(name, true);\n        if (_shouldUnregisterField) {\n            const value1 = cloneObject(get(control._options.defaultValues, name));\n            set(control._defaultValues, name, value1);\n            if (isUndefined(get(control._formValues, name))) {\n                set(control._formValues, name, value1);\n            }\n        }\n        !isArrayField && control.register(name);\n        return ()=>{\n            (isArrayField ? _shouldUnregisterField && !control._state.action : _shouldUnregisterField) ? control.unregister(name) : updateMounted(name, false);\n        };\n    }, [\n        name,\n        control,\n        isArrayField,\n        shouldUnregister\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        control._setDisabledField({\n            disabled,\n            name\n        });\n    }, [\n        disabled,\n        name,\n        control\n    ]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n            field,\n            formState,\n            fieldState\n        }), [\n        field,\n        formState,\n        fieldState\n    ]);\n}\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */ const Controller = (props)=>props.render(useController(props));\nconst flatten = (obj)=>{\n    const output = {};\n    for (const key of Object.keys(obj)){\n        if (isObjectType(obj[key]) && obj[key] !== null) {\n            const nested = flatten(obj[key]);\n            for (const nestedKey of Object.keys(nested)){\n                output[`${key}.${nestedKey}`] = nested[nestedKey];\n            }\n        } else {\n            output[key] = obj[key];\n        }\n    }\n    return output;\n};\nconst POST_REQUEST = \"post\";\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */ function Form(props) {\n    const methods = useFormContext();\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const { control = methods.control, onSubmit, children, action, method = POST_REQUEST, headers, encType, onError, render, onSuccess, validateStatus, ...rest } = props;\n    const submit = async (event)=>{\n        let hasError = false;\n        let type = \"\";\n        await control.handleSubmit(async (data)=>{\n            const formData = new FormData();\n            let formDataJson = \"\";\n            try {\n                formDataJson = JSON.stringify(data);\n            } catch (_a) {}\n            const flattenFormValues = flatten(control._formValues);\n            for(const key in flattenFormValues){\n                formData.append(key, flattenFormValues[key]);\n            }\n            if (onSubmit) {\n                await onSubmit({\n                    data,\n                    event,\n                    method,\n                    formData,\n                    formDataJson\n                });\n            }\n            if (action) {\n                try {\n                    const shouldStringifySubmissionData = [\n                        headers && headers[\"Content-Type\"],\n                        encType\n                    ].some((value1)=>value1 && value1.includes(\"json\"));\n                    const response = await fetch(String(action), {\n                        method,\n                        headers: {\n                            ...headers,\n                            ...encType ? {\n                                \"Content-Type\": encType\n                            } : {}\n                        },\n                        body: shouldStringifySubmissionData ? formDataJson : formData\n                    });\n                    if (response && (validateStatus ? !validateStatus(response.status) : response.status < 200 || response.status >= 300)) {\n                        hasError = true;\n                        onError && onError({\n                            response\n                        });\n                        type = String(response.status);\n                    } else {\n                        onSuccess && onSuccess({\n                            response\n                        });\n                    }\n                } catch (error) {\n                    hasError = true;\n                    onError && onError({\n                        error\n                    });\n                }\n            }\n        })(event);\n        if (hasError && props.control) {\n            props.control._subjects.state.next({\n                isSubmitSuccessful: false\n            });\n            props.control.setError(\"root.server\", {\n                type\n            });\n        }\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        setMounted(true);\n    }, []);\n    return render ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, render({\n        submit\n    })) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"form\", {\n        noValidate: mounted,\n        action: action,\n        method: method,\n        encType: encType,\n        onSubmit: submit,\n        ...rest\n    }, children);\n}\nvar appendErrors = (name, validateAllFieldCriteria, errors, type, message)=>validateAllFieldCriteria ? {\n        ...errors[name],\n        types: {\n            ...errors[name] && errors[name].types ? errors[name].types : {},\n            [type]: message || true\n        }\n    } : {};\nvar convertToArrayPayload = (value1)=>Array.isArray(value1) ? value1 : [\n        value1\n    ];\nvar createSubject = ()=>{\n    let _observers = [];\n    const next = (value1)=>{\n        for (const observer of _observers){\n            observer.next && observer.next(value1);\n        }\n    };\n    const subscribe = (observer)=>{\n        _observers.push(observer);\n        return {\n            unsubscribe: ()=>{\n                _observers = _observers.filter((o)=>o !== observer);\n            }\n        };\n    };\n    const unsubscribe = ()=>{\n        _observers = [];\n    };\n    return {\n        get observers () {\n            return _observers;\n        },\n        next,\n        subscribe,\n        unsubscribe\n    };\n};\nvar isPrimitive = (value1)=>isNullOrUndefined(value1) || !isObjectType(value1);\nfunction deepEqual(object1, object2, _internal_visited = new WeakSet()) {\n    if (isPrimitive(object1) || isPrimitive(object2)) {\n        return object1 === object2;\n    }\n    if (isDateObject(object1) && isDateObject(object2)) {\n        return object1.getTime() === object2.getTime();\n    }\n    const keys1 = Object.keys(object1);\n    const keys2 = Object.keys(object2);\n    if (keys1.length !== keys2.length) {\n        return false;\n    }\n    if (_internal_visited.has(object1) || _internal_visited.has(object2)) {\n        return true;\n    }\n    _internal_visited.add(object1);\n    _internal_visited.add(object2);\n    for (const key of keys1){\n        const val1 = object1[key];\n        if (!keys2.includes(key)) {\n            return false;\n        }\n        if (key !== \"ref\") {\n            const val2 = object2[key];\n            if (isDateObject(val1) && isDateObject(val2) || isObject(val1) && isObject(val2) || Array.isArray(val1) && Array.isArray(val2) ? !deepEqual(val1, val2, _internal_visited) : val1 !== val2) {\n                return false;\n            }\n        }\n    }\n    return true;\n}\nvar isEmptyObject = (value1)=>isObject(value1) && !Object.keys(value1).length;\nvar isFileInput = (element)=>element.type === \"file\";\nvar isFunction = (value1)=>typeof value1 === \"function\";\nvar isHTMLElement = (value1)=>{\n    if (!isWeb) {\n        return false;\n    }\n    const owner = value1 ? value1.ownerDocument : 0;\n    return value1 instanceof (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement);\n};\nvar isMultipleSelect = (element)=>element.type === `select-multiple`;\nvar isRadioInput = (element)=>element.type === \"radio\";\nvar isRadioOrCheckbox = (ref)=>isRadioInput(ref) || isCheckBoxInput(ref);\nvar live = (ref)=>isHTMLElement(ref) && ref.isConnected;\nfunction baseGet(object, updatePath) {\n    const length = updatePath.slice(0, -1).length;\n    let index = 0;\n    while(index < length){\n        object = isUndefined(object) ? index++ : object[updatePath[index++]];\n    }\n    return object;\n}\nfunction isEmptyArray(obj) {\n    for(const key in obj){\n        if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction unset(object, path) {\n    const paths = Array.isArray(path) ? path : isKey(path) ? [\n        path\n    ] : stringToPath(path);\n    const childObject = paths.length === 1 ? object : baseGet(object, paths);\n    const index = paths.length - 1;\n    const key = paths[index];\n    if (childObject) {\n        delete childObject[key];\n    }\n    if (index !== 0 && (isObject(childObject) && isEmptyObject(childObject) || Array.isArray(childObject) && isEmptyArray(childObject))) {\n        unset(object, paths.slice(0, -1));\n    }\n    return object;\n}\nvar objectHasFunction = (data)=>{\n    for(const key in data){\n        if (isFunction(data[key])) {\n            return true;\n        }\n    }\n    return false;\n};\nfunction markFieldsDirty(data, fields = {}) {\n    const isParentNodeArray = Array.isArray(data);\n    if (isObject(data) || isParentNodeArray) {\n        for(const key in data){\n            if (Array.isArray(data[key]) || isObject(data[key]) && !objectHasFunction(data[key])) {\n                fields[key] = Array.isArray(data[key]) ? [] : {};\n                markFieldsDirty(data[key], fields[key]);\n            } else if (!isNullOrUndefined(data[key])) {\n                fields[key] = true;\n            }\n        }\n    }\n    return fields;\n}\nfunction getDirtyFieldsFromDefaultValues(data, formValues, dirtyFieldsFromValues) {\n    const isParentNodeArray = Array.isArray(data);\n    if (isObject(data) || isParentNodeArray) {\n        for(const key in data){\n            if (Array.isArray(data[key]) || isObject(data[key]) && !objectHasFunction(data[key])) {\n                if (isUndefined(formValues) || isPrimitive(dirtyFieldsFromValues[key])) {\n                    dirtyFieldsFromValues[key] = Array.isArray(data[key]) ? markFieldsDirty(data[key], []) : {\n                        ...markFieldsDirty(data[key])\n                    };\n                } else {\n                    getDirtyFieldsFromDefaultValues(data[key], isNullOrUndefined(formValues) ? {} : formValues[key], dirtyFieldsFromValues[key]);\n                }\n            } else {\n                dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n            }\n        }\n    }\n    return dirtyFieldsFromValues;\n}\nvar getDirtyFields = (defaultValues, formValues)=>getDirtyFieldsFromDefaultValues(defaultValues, formValues, markFieldsDirty(formValues));\nconst defaultResult = {\n    value: false,\n    isValid: false\n};\nconst validResult = {\n    value: true,\n    isValid: true\n};\nvar getCheckboxValue = (options)=>{\n    if (Array.isArray(options)) {\n        if (options.length > 1) {\n            const values = options.filter((option)=>option && option.checked && !option.disabled).map((option)=>option.value);\n            return {\n                value: values,\n                isValid: !!values.length\n            };\n        }\n        return options[0].checked && !options[0].disabled ? options[0].attributes && !isUndefined(options[0].attributes.value) ? isUndefined(options[0].value) || options[0].value === \"\" ? validResult : {\n            value: options[0].value,\n            isValid: true\n        } : validResult : defaultResult;\n    }\n    return defaultResult;\n};\nvar getFieldValueAs = (value1, { valueAsNumber, valueAsDate, setValueAs })=>isUndefined(value1) ? value1 : valueAsNumber ? value1 === \"\" ? NaN : value1 ? +value1 : value1 : valueAsDate && isString(value1) ? new Date(value1) : setValueAs ? setValueAs(value1) : value1;\nconst defaultReturn = {\n    isValid: false,\n    value: null\n};\nvar getRadioValue = (options)=>Array.isArray(options) ? options.reduce((previous, option)=>option && option.checked && !option.disabled ? {\n            isValid: true,\n            value: option.value\n        } : previous, defaultReturn) : defaultReturn;\nfunction getFieldValue(_f) {\n    const ref = _f.ref;\n    if (isFileInput(ref)) {\n        return ref.files;\n    }\n    if (isRadioInput(ref)) {\n        return getRadioValue(_f.refs).value;\n    }\n    if (isMultipleSelect(ref)) {\n        return [\n            ...ref.selectedOptions\n        ].map(({ value: value1 })=>value1);\n    }\n    if (isCheckBoxInput(ref)) {\n        return getCheckboxValue(_f.refs).value;\n    }\n    return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\nvar getResolverOptions = (fieldsNames, _fields, criteriaMode, shouldUseNativeValidation)=>{\n    const fields = {};\n    for (const name of fieldsNames){\n        const field = get(_fields, name);\n        field && set(fields, name, field._f);\n    }\n    return {\n        criteriaMode,\n        names: [\n            ...fieldsNames\n        ],\n        fields,\n        shouldUseNativeValidation\n    };\n};\nvar isRegex = (value1)=>value1 instanceof RegExp;\nvar getRuleValue = (rule)=>isUndefined(rule) ? rule : isRegex(rule) ? rule.source : isObject(rule) ? isRegex(rule.value) ? rule.value.source : rule.value : rule;\nvar getValidationModes = (mode)=>({\n        isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n        isOnBlur: mode === VALIDATION_MODE.onBlur,\n        isOnChange: mode === VALIDATION_MODE.onChange,\n        isOnAll: mode === VALIDATION_MODE.all,\n        isOnTouch: mode === VALIDATION_MODE.onTouched\n    });\nconst ASYNC_FUNCTION = \"AsyncFunction\";\nvar hasPromiseValidation = (fieldReference)=>!!fieldReference && !!fieldReference.validate && !!(isFunction(fieldReference.validate) && fieldReference.validate.constructor.name === ASYNC_FUNCTION || isObject(fieldReference.validate) && Object.values(fieldReference.validate).find((validateFunction)=>validateFunction.constructor.name === ASYNC_FUNCTION));\nvar hasValidation = (options)=>options.mount && (options.required || options.min || options.max || options.maxLength || options.minLength || options.pattern || options.validate);\nvar isWatched = (name, _names, isBlurEvent)=>!isBlurEvent && (_names.watchAll || _names.watch.has(name) || [\n        ..._names.watch\n    ].some((watchName)=>name.startsWith(watchName) && /^\\.\\w+/.test(name.slice(watchName.length))));\nconst iterateFieldsByAction = (fields, action, fieldsNames, abortEarly)=>{\n    for (const key of fieldsNames || Object.keys(fields)){\n        const field = get(fields, key);\n        if (field) {\n            const { _f, ...currentField } = field;\n            if (_f) {\n                if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n                    return true;\n                } else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n                    return true;\n                } else {\n                    if (iterateFieldsByAction(currentField, action)) {\n                        break;\n                    }\n                }\n            } else if (isObject(currentField)) {\n                if (iterateFieldsByAction(currentField, action)) {\n                    break;\n                }\n            }\n        }\n    }\n    return;\n};\nfunction schemaErrorLookup(errors, _fields, name) {\n    const error = get(errors, name);\n    if (error || isKey(name)) {\n        return {\n            error,\n            name\n        };\n    }\n    const names = name.split(\".\");\n    while(names.length){\n        const fieldName = names.join(\".\");\n        const field = get(_fields, fieldName);\n        const foundError = get(errors, fieldName);\n        if (field && !Array.isArray(field) && name !== fieldName) {\n            return {\n                name\n            };\n        }\n        if (foundError && foundError.type) {\n            return {\n                name: fieldName,\n                error: foundError\n            };\n        }\n        if (foundError && foundError.root && foundError.root.type) {\n            return {\n                name: `${fieldName}.root`,\n                error: foundError.root\n            };\n        }\n        names.pop();\n    }\n    return {\n        name\n    };\n}\nvar shouldRenderFormState = (formStateData, _proxyFormState, updateFormState, isRoot)=>{\n    updateFormState(formStateData);\n    const { name, ...formState } = formStateData;\n    return isEmptyObject(formState) || Object.keys(formState).length >= Object.keys(_proxyFormState).length || Object.keys(formState).find((key)=>_proxyFormState[key] === (!isRoot || VALIDATION_MODE.all));\n};\nvar shouldSubscribeByName = (name, signalName, exact)=>!name || !signalName || name === signalName || convertToArrayPayload(name).some((currentName)=>currentName && (exact ? currentName === signalName : currentName.startsWith(signalName) || signalName.startsWith(currentName)));\nvar skipValidation = (isBlurEvent, isTouched, isSubmitted, reValidateMode, mode)=>{\n    if (mode.isOnAll) {\n        return false;\n    } else if (!isSubmitted && mode.isOnTouch) {\n        return !(isTouched || isBlurEvent);\n    } else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n        return !isBlurEvent;\n    } else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n        return isBlurEvent;\n    }\n    return true;\n};\nvar unsetEmptyArray = (ref, name)=>!compact(get(ref, name)).length && unset(ref, name);\nvar updateFieldArrayRootError = (errors, error, name)=>{\n    const fieldArrayErrors = convertToArrayPayload(get(errors, name));\n    set(fieldArrayErrors, \"root\", error[name]);\n    set(errors, name, fieldArrayErrors);\n    return errors;\n};\nvar isMessage = (value1)=>isString(value1);\nfunction getValidateError(result, ref, type = \"validate\") {\n    if (isMessage(result) || Array.isArray(result) && result.every(isMessage) || isBoolean(result) && !result) {\n        return {\n            type,\n            message: isMessage(result) ? result : \"\",\n            ref\n        };\n    }\n}\nvar getValueAndMessage = (validationData)=>isObject(validationData) && !isRegex(validationData) ? validationData : {\n        value: validationData,\n        message: \"\"\n    };\nvar validateField = async (field, disabledFieldNames, formValues, validateAllFieldCriteria, shouldUseNativeValidation, isFieldArray)=>{\n    const { ref, refs, required, maxLength, minLength, min, max, pattern, validate, name, valueAsNumber, mount } = field._f;\n    const inputValue = get(formValues, name);\n    if (!mount || disabledFieldNames.has(name)) {\n        return {};\n    }\n    const inputRef = refs ? refs[0] : ref;\n    const setCustomValidity = (message)=>{\n        if (shouldUseNativeValidation && inputRef.reportValidity) {\n            inputRef.setCustomValidity(isBoolean(message) ? \"\" : message || \"\");\n            inputRef.reportValidity();\n        }\n    };\n    const error = {};\n    const isRadio = isRadioInput(ref);\n    const isCheckBox = isCheckBoxInput(ref);\n    const isRadioOrCheckbox = isRadio || isCheckBox;\n    const isEmpty = (valueAsNumber || isFileInput(ref)) && isUndefined(ref.value) && isUndefined(inputValue) || isHTMLElement(ref) && ref.value === \"\" || inputValue === \"\" || Array.isArray(inputValue) && !inputValue.length;\n    const appendErrorsCurry = appendErrors.bind(null, name, validateAllFieldCriteria, error);\n    const getMinMaxMessage = (exceedMax, maxLengthMessage, minLengthMessage, maxType = INPUT_VALIDATION_RULES.maxLength, minType = INPUT_VALIDATION_RULES.minLength)=>{\n        const message = exceedMax ? maxLengthMessage : minLengthMessage;\n        error[name] = {\n            type: exceedMax ? maxType : minType,\n            message,\n            ref,\n            ...appendErrorsCurry(exceedMax ? maxType : minType, message)\n        };\n    };\n    if (isFieldArray ? !Array.isArray(inputValue) || !inputValue.length : required && (!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue)) || isBoolean(inputValue) && !inputValue || isCheckBox && !getCheckboxValue(refs).isValid || isRadio && !getRadioValue(refs).isValid)) {\n        const { value: value1, message } = isMessage(required) ? {\n            value: !!required,\n            message: required\n        } : getValueAndMessage(required);\n        if (value1) {\n            error[name] = {\n                type: INPUT_VALIDATION_RULES.required,\n                message,\n                ref: inputRef,\n                ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message)\n            };\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(message);\n                return error;\n            }\n        }\n    }\n    if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n        let exceedMax;\n        let exceedMin;\n        const maxOutput = getValueAndMessage(max);\n        const minOutput = getValueAndMessage(min);\n        if (!isNullOrUndefined(inputValue) && !isNaN(inputValue)) {\n            const valueNumber = ref.valueAsNumber || (inputValue ? +inputValue : inputValue);\n            if (!isNullOrUndefined(maxOutput.value)) {\n                exceedMax = valueNumber > maxOutput.value;\n            }\n            if (!isNullOrUndefined(minOutput.value)) {\n                exceedMin = valueNumber < minOutput.value;\n            }\n        } else {\n            const valueDate = ref.valueAsDate || new Date(inputValue);\n            const convertTimeToDate = (time)=>new Date(new Date().toDateString() + \" \" + time);\n            const isTime = ref.type == \"time\";\n            const isWeek = ref.type == \"week\";\n            if (isString(maxOutput.value) && inputValue) {\n                exceedMax = isTime ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value) : isWeek ? inputValue > maxOutput.value : valueDate > new Date(maxOutput.value);\n            }\n            if (isString(minOutput.value) && inputValue) {\n                exceedMin = isTime ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value) : isWeek ? inputValue < minOutput.value : valueDate < new Date(minOutput.value);\n            }\n        }\n        if (exceedMax || exceedMin) {\n            getMinMaxMessage(!!exceedMax, maxOutput.message, minOutput.message, INPUT_VALIDATION_RULES.max, INPUT_VALIDATION_RULES.min);\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(error[name].message);\n                return error;\n            }\n        }\n    }\n    if ((maxLength || minLength) && !isEmpty && (isString(inputValue) || isFieldArray && Array.isArray(inputValue))) {\n        const maxLengthOutput = getValueAndMessage(maxLength);\n        const minLengthOutput = getValueAndMessage(minLength);\n        const exceedMax = !isNullOrUndefined(maxLengthOutput.value) && inputValue.length > +maxLengthOutput.value;\n        const exceedMin = !isNullOrUndefined(minLengthOutput.value) && inputValue.length < +minLengthOutput.value;\n        if (exceedMax || exceedMin) {\n            getMinMaxMessage(exceedMax, maxLengthOutput.message, minLengthOutput.message);\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(error[name].message);\n                return error;\n            }\n        }\n    }\n    if (pattern && !isEmpty && isString(inputValue)) {\n        const { value: patternValue, message } = getValueAndMessage(pattern);\n        if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n            error[name] = {\n                type: INPUT_VALIDATION_RULES.pattern,\n                message,\n                ref,\n                ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message)\n            };\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(message);\n                return error;\n            }\n        }\n    }\n    if (validate) {\n        if (isFunction(validate)) {\n            const result = await validate(inputValue, formValues);\n            const validateError = getValidateError(result, inputRef);\n            if (validateError) {\n                error[name] = {\n                    ...validateError,\n                    ...appendErrorsCurry(INPUT_VALIDATION_RULES.validate, validateError.message)\n                };\n                if (!validateAllFieldCriteria) {\n                    setCustomValidity(validateError.message);\n                    return error;\n                }\n            }\n        } else if (isObject(validate)) {\n            let validationResult = {};\n            for(const key in validate){\n                if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n                    break;\n                }\n                const validateError = getValidateError(await validate[key](inputValue, formValues), inputRef, key);\n                if (validateError) {\n                    validationResult = {\n                        ...validateError,\n                        ...appendErrorsCurry(key, validateError.message)\n                    };\n                    setCustomValidity(validateError.message);\n                    if (validateAllFieldCriteria) {\n                        error[name] = validationResult;\n                    }\n                }\n            }\n            if (!isEmptyObject(validationResult)) {\n                error[name] = {\n                    ref: inputRef,\n                    ...validationResult\n                };\n                if (!validateAllFieldCriteria) {\n                    return error;\n                }\n            }\n        }\n    }\n    setCustomValidity(true);\n    return error;\n};\nconst defaultOptions = {\n    mode: VALIDATION_MODE.onSubmit,\n    reValidateMode: VALIDATION_MODE.onChange,\n    shouldFocusError: true\n};\nfunction createFormControl(props = {}) {\n    let _options = {\n        ...defaultOptions,\n        ...props\n    };\n    let _formState = {\n        submitCount: 0,\n        isDirty: false,\n        isReady: false,\n        isLoading: isFunction(_options.defaultValues),\n        isValidating: false,\n        isSubmitted: false,\n        isSubmitting: false,\n        isSubmitSuccessful: false,\n        isValid: false,\n        touchedFields: {},\n        dirtyFields: {},\n        validatingFields: {},\n        errors: _options.errors || {},\n        disabled: _options.disabled || false\n    };\n    let _fields = {};\n    let _defaultValues = isObject(_options.defaultValues) || isObject(_options.values) ? cloneObject(_options.defaultValues || _options.values) || {} : {};\n    let _formValues = _options.shouldUnregister ? {} : cloneObject(_defaultValues);\n    let _state = {\n        action: false,\n        mount: false,\n        watch: false\n    };\n    let _names = {\n        mount: new Set(),\n        disabled: new Set(),\n        unMount: new Set(),\n        array: new Set(),\n        watch: new Set()\n    };\n    let delayErrorCallback;\n    let timer = 0;\n    const _proxyFormState = {\n        isDirty: false,\n        dirtyFields: false,\n        validatingFields: false,\n        touchedFields: false,\n        isValidating: false,\n        isValid: false,\n        errors: false\n    };\n    let _proxySubscribeFormState = {\n        ..._proxyFormState\n    };\n    const _subjects = {\n        array: createSubject(),\n        state: createSubject()\n    };\n    const shouldDisplayAllAssociatedErrors = _options.criteriaMode === VALIDATION_MODE.all;\n    const debounce = (callback)=>(wait)=>{\n            clearTimeout(timer);\n            timer = setTimeout(callback, wait);\n        };\n    const _setValid = async (shouldUpdateValid)=>{\n        if (!_options.disabled && (_proxyFormState.isValid || _proxySubscribeFormState.isValid || shouldUpdateValid)) {\n            const isValid = _options.resolver ? isEmptyObject((await _runSchema()).errors) : await executeBuiltInValidation(_fields, true);\n            if (isValid !== _formState.isValid) {\n                _subjects.state.next({\n                    isValid\n                });\n            }\n        }\n    };\n    const _updateIsValidating = (names, isValidating)=>{\n        if (!_options.disabled && (_proxyFormState.isValidating || _proxyFormState.validatingFields || _proxySubscribeFormState.isValidating || _proxySubscribeFormState.validatingFields)) {\n            (names || Array.from(_names.mount)).forEach((name)=>{\n                if (name) {\n                    isValidating ? set(_formState.validatingFields, name, isValidating) : unset(_formState.validatingFields, name);\n                }\n            });\n            _subjects.state.next({\n                validatingFields: _formState.validatingFields,\n                isValidating: !isEmptyObject(_formState.validatingFields)\n            });\n        }\n    };\n    const _setFieldArray = (name, values = [], method, args, shouldSetValues = true, shouldUpdateFieldsAndState = true)=>{\n        if (args && method && !_options.disabled) {\n            _state.action = true;\n            if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n                const fieldValues = method(get(_fields, name), args.argA, args.argB);\n                shouldSetValues && set(_fields, name, fieldValues);\n            }\n            if (shouldUpdateFieldsAndState && Array.isArray(get(_formState.errors, name))) {\n                const errors = method(get(_formState.errors, name), args.argA, args.argB);\n                shouldSetValues && set(_formState.errors, name, errors);\n                unsetEmptyArray(_formState.errors, name);\n            }\n            if ((_proxyFormState.touchedFields || _proxySubscribeFormState.touchedFields) && shouldUpdateFieldsAndState && Array.isArray(get(_formState.touchedFields, name))) {\n                const touchedFields = method(get(_formState.touchedFields, name), args.argA, args.argB);\n                shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n            }\n            if (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) {\n                _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n            }\n            _subjects.state.next({\n                name,\n                isDirty: _getDirty(name, values),\n                dirtyFields: _formState.dirtyFields,\n                errors: _formState.errors,\n                isValid: _formState.isValid\n            });\n        } else {\n            set(_formValues, name, values);\n        }\n    };\n    const updateErrors = (name, error)=>{\n        set(_formState.errors, name, error);\n        _subjects.state.next({\n            errors: _formState.errors\n        });\n    };\n    const _setErrors = (errors)=>{\n        _formState.errors = errors;\n        _subjects.state.next({\n            errors: _formState.errors,\n            isValid: false\n        });\n    };\n    const updateValidAndValue = (name, shouldSkipSetValueAs, value1, ref)=>{\n        const field = get(_fields, name);\n        if (field) {\n            const defaultValue = get(_formValues, name, isUndefined(value1) ? get(_defaultValues, name) : value1);\n            isUndefined(defaultValue) || ref && ref.defaultChecked || shouldSkipSetValueAs ? set(_formValues, name, shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f)) : setFieldValue(name, defaultValue);\n            _state.mount && _setValid();\n        }\n    };\n    const updateTouchAndDirty = (name, fieldValue, isBlurEvent, shouldDirty, shouldRender)=>{\n        let shouldUpdateField = false;\n        let isPreviousDirty = false;\n        const output = {\n            name\n        };\n        if (!_options.disabled) {\n            if (!isBlurEvent || shouldDirty) {\n                if (_proxyFormState.isDirty || _proxySubscribeFormState.isDirty) {\n                    isPreviousDirty = _formState.isDirty;\n                    _formState.isDirty = output.isDirty = _getDirty();\n                    shouldUpdateField = isPreviousDirty !== output.isDirty;\n                }\n                const isCurrentFieldPristine = deepEqual(get(_defaultValues, name), fieldValue);\n                isPreviousDirty = !!get(_formState.dirtyFields, name);\n                isCurrentFieldPristine ? unset(_formState.dirtyFields, name) : set(_formState.dirtyFields, name, true);\n                output.dirtyFields = _formState.dirtyFields;\n                shouldUpdateField = shouldUpdateField || (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) && isPreviousDirty !== !isCurrentFieldPristine;\n            }\n            if (isBlurEvent) {\n                const isPreviousFieldTouched = get(_formState.touchedFields, name);\n                if (!isPreviousFieldTouched) {\n                    set(_formState.touchedFields, name, isBlurEvent);\n                    output.touchedFields = _formState.touchedFields;\n                    shouldUpdateField = shouldUpdateField || (_proxyFormState.touchedFields || _proxySubscribeFormState.touchedFields) && isPreviousFieldTouched !== isBlurEvent;\n                }\n            }\n            shouldUpdateField && shouldRender && _subjects.state.next(output);\n        }\n        return shouldUpdateField ? output : {};\n    };\n    const shouldRenderByError = (name, isValid, error, fieldState)=>{\n        const previousFieldError = get(_formState.errors, name);\n        const shouldUpdateValid = (_proxyFormState.isValid || _proxySubscribeFormState.isValid) && isBoolean(isValid) && _formState.isValid !== isValid;\n        if (_options.delayError && error) {\n            delayErrorCallback = debounce(()=>updateErrors(name, error));\n            delayErrorCallback(_options.delayError);\n        } else {\n            clearTimeout(timer);\n            delayErrorCallback = null;\n            error ? set(_formState.errors, name, error) : unset(_formState.errors, name);\n        }\n        if ((error ? !deepEqual(previousFieldError, error) : previousFieldError) || !isEmptyObject(fieldState) || shouldUpdateValid) {\n            const updatedFormState = {\n                ...fieldState,\n                ...shouldUpdateValid && isBoolean(isValid) ? {\n                    isValid\n                } : {},\n                errors: _formState.errors,\n                name\n            };\n            _formState = {\n                ..._formState,\n                ...updatedFormState\n            };\n            _subjects.state.next(updatedFormState);\n        }\n    };\n    const _runSchema = async (name)=>{\n        _updateIsValidating(name, true);\n        const result = await _options.resolver(_formValues, _options.context, getResolverOptions(name || _names.mount, _fields, _options.criteriaMode, _options.shouldUseNativeValidation));\n        _updateIsValidating(name);\n        return result;\n    };\n    const executeSchemaAndUpdateState = async (names)=>{\n        const { errors } = await _runSchema(names);\n        if (names) {\n            for (const name of names){\n                const error = get(errors, name);\n                error ? set(_formState.errors, name, error) : unset(_formState.errors, name);\n            }\n        } else {\n            _formState.errors = errors;\n        }\n        return errors;\n    };\n    const executeBuiltInValidation = async (fields, shouldOnlyCheckValid, context = {\n        valid: true\n    })=>{\n        for(const name in fields){\n            const field = fields[name];\n            if (field) {\n                const { _f, ...fieldValue } = field;\n                if (_f) {\n                    const isFieldArrayRoot = _names.array.has(_f.name);\n                    const isPromiseFunction = field._f && hasPromiseValidation(field._f);\n                    if (isPromiseFunction && _proxyFormState.validatingFields) {\n                        _updateIsValidating([\n                            name\n                        ], true);\n                    }\n                    const fieldError = await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation && !shouldOnlyCheckValid, isFieldArrayRoot);\n                    if (isPromiseFunction && _proxyFormState.validatingFields) {\n                        _updateIsValidating([\n                            name\n                        ]);\n                    }\n                    if (fieldError[_f.name]) {\n                        context.valid = false;\n                        if (shouldOnlyCheckValid) {\n                            break;\n                        }\n                    }\n                    !shouldOnlyCheckValid && (get(fieldError, _f.name) ? isFieldArrayRoot ? updateFieldArrayRootError(_formState.errors, fieldError, _f.name) : set(_formState.errors, _f.name, fieldError[_f.name]) : unset(_formState.errors, _f.name));\n                }\n                !isEmptyObject(fieldValue) && await executeBuiltInValidation(fieldValue, shouldOnlyCheckValid, context);\n            }\n        }\n        return context.valid;\n    };\n    const _removeUnmounted = ()=>{\n        for (const name of _names.unMount){\n            const field = get(_fields, name);\n            field && (field._f.refs ? field._f.refs.every((ref)=>!live(ref)) : !live(field._f.ref)) && unregister(name);\n        }\n        _names.unMount = new Set();\n    };\n    const _getDirty = (name, data)=>!_options.disabled && (name && data && set(_formValues, name, data), !deepEqual(getValues(), _defaultValues));\n    const _getWatch = (names, defaultValue, isGlobal)=>generateWatchOutput(names, _names, {\n            ..._state.mount ? _formValues : isUndefined(defaultValue) ? _defaultValues : isString(names) ? {\n                [names]: defaultValue\n            } : defaultValue\n        }, isGlobal, defaultValue);\n    const _getFieldArray = (name)=>compact(get(_state.mount ? _formValues : _defaultValues, name, _options.shouldUnregister ? get(_defaultValues, name, []) : []));\n    const setFieldValue = (name, value1, options = {})=>{\n        const field = get(_fields, name);\n        let fieldValue = value1;\n        if (field) {\n            const fieldReference = field._f;\n            if (fieldReference) {\n                !fieldReference.disabled && set(_formValues, name, getFieldValueAs(value1, fieldReference));\n                fieldValue = isHTMLElement(fieldReference.ref) && isNullOrUndefined(value1) ? \"\" : value1;\n                if (isMultipleSelect(fieldReference.ref)) {\n                    [\n                        ...fieldReference.ref.options\n                    ].forEach((optionRef)=>optionRef.selected = fieldValue.includes(optionRef.value));\n                } else if (fieldReference.refs) {\n                    if (isCheckBoxInput(fieldReference.ref)) {\n                        fieldReference.refs.forEach((checkboxRef)=>{\n                            if (!checkboxRef.defaultChecked || !checkboxRef.disabled) {\n                                if (Array.isArray(fieldValue)) {\n                                    checkboxRef.checked = !!fieldValue.find((data)=>data === checkboxRef.value);\n                                } else {\n                                    checkboxRef.checked = fieldValue === checkboxRef.value || !!fieldValue;\n                                }\n                            }\n                        });\n                    } else {\n                        fieldReference.refs.forEach((radioRef)=>radioRef.checked = radioRef.value === fieldValue);\n                    }\n                } else if (isFileInput(fieldReference.ref)) {\n                    fieldReference.ref.value = \"\";\n                } else {\n                    fieldReference.ref.value = fieldValue;\n                    if (!fieldReference.ref.type) {\n                        _subjects.state.next({\n                            name,\n                            values: cloneObject(_formValues)\n                        });\n                    }\n                }\n            }\n        }\n        (options.shouldDirty || options.shouldTouch) && updateTouchAndDirty(name, fieldValue, options.shouldTouch, options.shouldDirty, true);\n        options.shouldValidate && trigger(name);\n    };\n    const setValues = (name, value1, options)=>{\n        for(const fieldKey in value1){\n            if (!value1.hasOwnProperty(fieldKey)) {\n                return;\n            }\n            const fieldValue = value1[fieldKey];\n            const fieldName = name + \".\" + fieldKey;\n            const field = get(_fields, fieldName);\n            (_names.array.has(name) || isObject(fieldValue) || field && !field._f) && !isDateObject(fieldValue) ? setValues(fieldName, fieldValue, options) : setFieldValue(fieldName, fieldValue, options);\n        }\n    };\n    const setValue = (name, value1, options = {})=>{\n        const field = get(_fields, name);\n        const isFieldArray = _names.array.has(name);\n        const cloneValue = cloneObject(value1);\n        set(_formValues, name, cloneValue);\n        if (isFieldArray) {\n            _subjects.array.next({\n                name,\n                values: cloneObject(_formValues)\n            });\n            if ((_proxyFormState.isDirty || _proxyFormState.dirtyFields || _proxySubscribeFormState.isDirty || _proxySubscribeFormState.dirtyFields) && options.shouldDirty) {\n                _subjects.state.next({\n                    name,\n                    dirtyFields: getDirtyFields(_defaultValues, _formValues),\n                    isDirty: _getDirty(name, cloneValue)\n                });\n            }\n        } else {\n            field && !field._f && !isNullOrUndefined(cloneValue) ? setValues(name, cloneValue, options) : setFieldValue(name, cloneValue, options);\n        }\n        isWatched(name, _names) && _subjects.state.next({\n            ..._formState\n        });\n        _subjects.state.next({\n            name: _state.mount ? name : undefined,\n            values: cloneObject(_formValues)\n        });\n    };\n    const onChange = async (event)=>{\n        _state.mount = true;\n        const target = event.target;\n        let name = target.name;\n        let isFieldValueUpdated = true;\n        const field = get(_fields, name);\n        const _updateIsFieldValueUpdated = (fieldValue)=>{\n            isFieldValueUpdated = Number.isNaN(fieldValue) || isDateObject(fieldValue) && isNaN(fieldValue.getTime()) || deepEqual(fieldValue, get(_formValues, name, fieldValue));\n        };\n        const validationModeBeforeSubmit = getValidationModes(_options.mode);\n        const validationModeAfterSubmit = getValidationModes(_options.reValidateMode);\n        if (field) {\n            let error;\n            let isValid;\n            const fieldValue = target.type ? getFieldValue(field._f) : getEventValue(event);\n            const isBlurEvent = event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n            const shouldSkipValidation = !hasValidation(field._f) && !_options.resolver && !get(_formState.errors, name) && !field._f.deps || skipValidation(isBlurEvent, get(_formState.touchedFields, name), _formState.isSubmitted, validationModeAfterSubmit, validationModeBeforeSubmit);\n            const watched = isWatched(name, _names, isBlurEvent);\n            set(_formValues, name, fieldValue);\n            if (isBlurEvent) {\n                field._f.onBlur && field._f.onBlur(event);\n                delayErrorCallback && delayErrorCallback(0);\n            } else if (field._f.onChange) {\n                field._f.onChange(event);\n            }\n            const fieldState = updateTouchAndDirty(name, fieldValue, isBlurEvent);\n            const shouldRender = !isEmptyObject(fieldState) || watched;\n            !isBlurEvent && _subjects.state.next({\n                name,\n                type: event.type,\n                values: cloneObject(_formValues)\n            });\n            if (shouldSkipValidation) {\n                if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n                    if (_options.mode === \"onBlur\") {\n                        if (isBlurEvent) {\n                            _setValid();\n                        }\n                    } else if (!isBlurEvent) {\n                        _setValid();\n                    }\n                }\n                return shouldRender && _subjects.state.next({\n                    name,\n                    ...watched ? {} : fieldState\n                });\n            }\n            !isBlurEvent && watched && _subjects.state.next({\n                ..._formState\n            });\n            if (_options.resolver) {\n                const { errors } = await _runSchema([\n                    name\n                ]);\n                _updateIsFieldValueUpdated(fieldValue);\n                if (isFieldValueUpdated) {\n                    const previousErrorLookupResult = schemaErrorLookup(_formState.errors, _fields, name);\n                    const errorLookupResult = schemaErrorLookup(errors, _fields, previousErrorLookupResult.name || name);\n                    error = errorLookupResult.error;\n                    name = errorLookupResult.name;\n                    isValid = isEmptyObject(errors);\n                }\n            } else {\n                _updateIsValidating([\n                    name\n                ], true);\n                error = (await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation))[name];\n                _updateIsValidating([\n                    name\n                ]);\n                _updateIsFieldValueUpdated(fieldValue);\n                if (isFieldValueUpdated) {\n                    if (error) {\n                        isValid = false;\n                    } else if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n                        isValid = await executeBuiltInValidation(_fields, true);\n                    }\n                }\n            }\n            if (isFieldValueUpdated) {\n                field._f.deps && trigger(field._f.deps);\n                shouldRenderByError(name, isValid, error, fieldState);\n            }\n        }\n    };\n    const _focusInput = (ref, key)=>{\n        if (get(_formState.errors, key) && ref.focus) {\n            ref.focus();\n            return 1;\n        }\n        return;\n    };\n    const trigger = async (name, options = {})=>{\n        let isValid;\n        let validationResult;\n        const fieldNames = convertToArrayPayload(name);\n        if (_options.resolver) {\n            const errors = await executeSchemaAndUpdateState(isUndefined(name) ? name : fieldNames);\n            isValid = isEmptyObject(errors);\n            validationResult = name ? !fieldNames.some((name)=>get(errors, name)) : isValid;\n        } else if (name) {\n            validationResult = (await Promise.all(fieldNames.map(async (fieldName)=>{\n                const field = get(_fields, fieldName);\n                return await executeBuiltInValidation(field && field._f ? {\n                    [fieldName]: field\n                } : field);\n            }))).every(Boolean);\n            !(!validationResult && !_formState.isValid) && _setValid();\n        } else {\n            validationResult = isValid = await executeBuiltInValidation(_fields);\n        }\n        _subjects.state.next({\n            ...!isString(name) || (_proxyFormState.isValid || _proxySubscribeFormState.isValid) && isValid !== _formState.isValid ? {} : {\n                name\n            },\n            ..._options.resolver || !name ? {\n                isValid\n            } : {},\n            errors: _formState.errors\n        });\n        options.shouldFocus && !validationResult && iterateFieldsByAction(_fields, _focusInput, name ? fieldNames : _names.mount);\n        return validationResult;\n    };\n    const getValues = (fieldNames)=>{\n        const values = {\n            ..._state.mount ? _formValues : _defaultValues\n        };\n        return isUndefined(fieldNames) ? values : isString(fieldNames) ? get(values, fieldNames) : fieldNames.map((name)=>get(values, name));\n    };\n    const getFieldState = (name, formState)=>({\n            invalid: !!get((formState || _formState).errors, name),\n            isDirty: !!get((formState || _formState).dirtyFields, name),\n            error: get((formState || _formState).errors, name),\n            isValidating: !!get(_formState.validatingFields, name),\n            isTouched: !!get((formState || _formState).touchedFields, name)\n        });\n    const clearErrors = (name)=>{\n        name && convertToArrayPayload(name).forEach((inputName)=>unset(_formState.errors, inputName));\n        _subjects.state.next({\n            errors: name ? _formState.errors : {}\n        });\n    };\n    const setError = (name, error, options)=>{\n        const ref = (get(_fields, name, {\n            _f: {}\n        })._f || {}).ref;\n        const currentError = get(_formState.errors, name) || {};\n        // Don't override existing error messages elsewhere in the object tree.\n        const { ref: currentRef, message, type, ...restOfErrorTree } = currentError;\n        set(_formState.errors, name, {\n            ...restOfErrorTree,\n            ...error,\n            ref\n        });\n        _subjects.state.next({\n            name,\n            errors: _formState.errors,\n            isValid: false\n        });\n        options && options.shouldFocus && ref && ref.focus && ref.focus();\n    };\n    const watch = (name, defaultValue)=>isFunction(name) ? _subjects.state.subscribe({\n            next: (payload)=>name(_getWatch(undefined, defaultValue), payload)\n        }) : _getWatch(name, defaultValue, true);\n    const _subscribe = (props)=>_subjects.state.subscribe({\n            next: (formState)=>{\n                if (shouldSubscribeByName(props.name, formState.name, props.exact) && shouldRenderFormState(formState, props.formState || _proxyFormState, _setFormState, props.reRenderRoot)) {\n                    props.callback({\n                        values: {\n                            ..._formValues\n                        },\n                        ..._formState,\n                        ...formState\n                    });\n                }\n            }\n        }).unsubscribe;\n    const subscribe = (props)=>{\n        _state.mount = true;\n        _proxySubscribeFormState = {\n            ..._proxySubscribeFormState,\n            ...props.formState\n        };\n        return _subscribe({\n            ...props,\n            formState: _proxySubscribeFormState\n        });\n    };\n    const unregister = (name, options = {})=>{\n        for (const fieldName of name ? convertToArrayPayload(name) : _names.mount){\n            _names.mount.delete(fieldName);\n            _names.array.delete(fieldName);\n            if (!options.keepValue) {\n                unset(_fields, fieldName);\n                unset(_formValues, fieldName);\n            }\n            !options.keepError && unset(_formState.errors, fieldName);\n            !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n            !options.keepTouched && unset(_formState.touchedFields, fieldName);\n            !options.keepIsValidating && unset(_formState.validatingFields, fieldName);\n            !_options.shouldUnregister && !options.keepDefaultValue && unset(_defaultValues, fieldName);\n        }\n        _subjects.state.next({\n            values: cloneObject(_formValues)\n        });\n        _subjects.state.next({\n            ..._formState,\n            ...!options.keepDirty ? {} : {\n                isDirty: _getDirty()\n            }\n        });\n        !options.keepIsValid && _setValid();\n    };\n    const _setDisabledField = ({ disabled, name })=>{\n        if (isBoolean(disabled) && _state.mount || !!disabled || _names.disabled.has(name)) {\n            disabled ? _names.disabled.add(name) : _names.disabled.delete(name);\n        }\n    };\n    const register = (name, options = {})=>{\n        let field = get(_fields, name);\n        const disabledIsDefined = isBoolean(options.disabled) || isBoolean(_options.disabled);\n        set(_fields, name, {\n            ...field || {},\n            _f: {\n                ...field && field._f ? field._f : {\n                    ref: {\n                        name\n                    }\n                },\n                name,\n                mount: true,\n                ...options\n            }\n        });\n        _names.mount.add(name);\n        if (field) {\n            _setDisabledField({\n                disabled: isBoolean(options.disabled) ? options.disabled : _options.disabled,\n                name\n            });\n        } else {\n            updateValidAndValue(name, true, options.value);\n        }\n        return {\n            ...disabledIsDefined ? {\n                disabled: options.disabled || _options.disabled\n            } : {},\n            ..._options.progressive ? {\n                required: !!options.required,\n                min: getRuleValue(options.min),\n                max: getRuleValue(options.max),\n                minLength: getRuleValue(options.minLength),\n                maxLength: getRuleValue(options.maxLength),\n                pattern: getRuleValue(options.pattern)\n            } : {},\n            name,\n            onChange,\n            onBlur: onChange,\n            ref: (ref)=>{\n                if (ref) {\n                    register(name, options);\n                    field = get(_fields, name);\n                    const fieldRef = isUndefined(ref.value) ? ref.querySelectorAll ? ref.querySelectorAll(\"input,select,textarea\")[0] || ref : ref : ref;\n                    const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n                    const refs = field._f.refs || [];\n                    if (radioOrCheckbox ? refs.find((option)=>option === fieldRef) : fieldRef === field._f.ref) {\n                        return;\n                    }\n                    set(_fields, name, {\n                        _f: {\n                            ...field._f,\n                            ...radioOrCheckbox ? {\n                                refs: [\n                                    ...refs.filter(live),\n                                    fieldRef,\n                                    ...Array.isArray(get(_defaultValues, name)) ? [\n                                        {}\n                                    ] : []\n                                ],\n                                ref: {\n                                    type: fieldRef.type,\n                                    name\n                                }\n                            } : {\n                                ref: fieldRef\n                            }\n                        }\n                    });\n                    updateValidAndValue(name, false, undefined, fieldRef);\n                } else {\n                    field = get(_fields, name, {});\n                    if (field._f) {\n                        field._f.mount = false;\n                    }\n                    (_options.shouldUnregister || options.shouldUnregister) && !(isNameInFieldArray(_names.array, name) && _state.action) && _names.unMount.add(name);\n                }\n            }\n        };\n    };\n    const _focusError = ()=>_options.shouldFocusError && iterateFieldsByAction(_fields, _focusInput, _names.mount);\n    const _disableForm = (disabled)=>{\n        if (isBoolean(disabled)) {\n            _subjects.state.next({\n                disabled\n            });\n            iterateFieldsByAction(_fields, (ref, name)=>{\n                const currentField = get(_fields, name);\n                if (currentField) {\n                    ref.disabled = currentField._f.disabled || disabled;\n                    if (Array.isArray(currentField._f.refs)) {\n                        currentField._f.refs.forEach((inputRef)=>{\n                            inputRef.disabled = currentField._f.disabled || disabled;\n                        });\n                    }\n                }\n            }, 0, false);\n        }\n    };\n    const handleSubmit = (onValid, onInvalid)=>async (e)=>{\n            let onValidError = undefined;\n            if (e) {\n                e.preventDefault && e.preventDefault();\n                e.persist && e.persist();\n            }\n            let fieldValues = cloneObject(_formValues);\n            _subjects.state.next({\n                isSubmitting: true\n            });\n            if (_options.resolver) {\n                const { errors, values } = await _runSchema();\n                _formState.errors = errors;\n                fieldValues = cloneObject(values);\n            } else {\n                await executeBuiltInValidation(_fields);\n            }\n            if (_names.disabled.size) {\n                for (const name of _names.disabled){\n                    unset(fieldValues, name);\n                }\n            }\n            unset(_formState.errors, \"root\");\n            if (isEmptyObject(_formState.errors)) {\n                _subjects.state.next({\n                    errors: {}\n                });\n                try {\n                    await onValid(fieldValues, e);\n                } catch (error) {\n                    onValidError = error;\n                }\n            } else {\n                if (onInvalid) {\n                    await onInvalid({\n                        ..._formState.errors\n                    }, e);\n                }\n                _focusError();\n                setTimeout(_focusError);\n            }\n            _subjects.state.next({\n                isSubmitted: true,\n                isSubmitting: false,\n                isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,\n                submitCount: _formState.submitCount + 1,\n                errors: _formState.errors\n            });\n            if (onValidError) {\n                throw onValidError;\n            }\n        };\n    const resetField = (name, options = {})=>{\n        if (get(_fields, name)) {\n            if (isUndefined(options.defaultValue)) {\n                setValue(name, cloneObject(get(_defaultValues, name)));\n            } else {\n                setValue(name, options.defaultValue);\n                set(_defaultValues, name, cloneObject(options.defaultValue));\n            }\n            if (!options.keepTouched) {\n                unset(_formState.touchedFields, name);\n            }\n            if (!options.keepDirty) {\n                unset(_formState.dirtyFields, name);\n                _formState.isDirty = options.defaultValue ? _getDirty(name, cloneObject(get(_defaultValues, name))) : _getDirty();\n            }\n            if (!options.keepError) {\n                unset(_formState.errors, name);\n                _proxyFormState.isValid && _setValid();\n            }\n            _subjects.state.next({\n                ..._formState\n            });\n        }\n    };\n    const _reset = (formValues, keepStateOptions = {})=>{\n        const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n        const cloneUpdatedValues = cloneObject(updatedValues);\n        const isEmptyResetValues = isEmptyObject(formValues);\n        const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;\n        if (!keepStateOptions.keepDefaultValues) {\n            _defaultValues = updatedValues;\n        }\n        if (!keepStateOptions.keepValues) {\n            if (keepStateOptions.keepDirtyValues) {\n                const fieldsToCheck = new Set([\n                    ..._names.mount,\n                    ...Object.keys(getDirtyFields(_defaultValues, _formValues))\n                ]);\n                for (const fieldName of Array.from(fieldsToCheck)){\n                    get(_formState.dirtyFields, fieldName) ? set(values, fieldName, get(_formValues, fieldName)) : setValue(fieldName, get(values, fieldName));\n                }\n            } else {\n                if (isWeb && isUndefined(formValues)) {\n                    for (const name of _names.mount){\n                        const field = get(_fields, name);\n                        if (field && field._f) {\n                            const fieldReference = Array.isArray(field._f.refs) ? field._f.refs[0] : field._f.ref;\n                            if (isHTMLElement(fieldReference)) {\n                                const form = fieldReference.closest(\"form\");\n                                if (form) {\n                                    form.reset();\n                                    break;\n                                }\n                            }\n                        }\n                    }\n                }\n                if (keepStateOptions.keepFieldsRef) {\n                    for (const fieldName of _names.mount){\n                        setValue(fieldName, get(values, fieldName));\n                    }\n                } else {\n                    _fields = {};\n                }\n            }\n            _formValues = _options.shouldUnregister ? keepStateOptions.keepDefaultValues ? cloneObject(_defaultValues) : {} : cloneObject(values);\n            _subjects.array.next({\n                values: {\n                    ...values\n                }\n            });\n            _subjects.state.next({\n                values: {\n                    ...values\n                }\n            });\n        }\n        _names = {\n            mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),\n            unMount: new Set(),\n            array: new Set(),\n            disabled: new Set(),\n            watch: new Set(),\n            watchAll: false,\n            focus: \"\"\n        };\n        _state.mount = !_proxyFormState.isValid || !!keepStateOptions.keepIsValid || !!keepStateOptions.keepDirtyValues;\n        _state.watch = !!_options.shouldUnregister;\n        _subjects.state.next({\n            submitCount: keepStateOptions.keepSubmitCount ? _formState.submitCount : 0,\n            isDirty: isEmptyResetValues ? false : keepStateOptions.keepDirty ? _formState.isDirty : !!(keepStateOptions.keepDefaultValues && !deepEqual(formValues, _defaultValues)),\n            isSubmitted: keepStateOptions.keepIsSubmitted ? _formState.isSubmitted : false,\n            dirtyFields: isEmptyResetValues ? {} : keepStateOptions.keepDirtyValues ? keepStateOptions.keepDefaultValues && _formValues ? getDirtyFields(_defaultValues, _formValues) : _formState.dirtyFields : keepStateOptions.keepDefaultValues && formValues ? getDirtyFields(_defaultValues, formValues) : keepStateOptions.keepDirty ? _formState.dirtyFields : {},\n            touchedFields: keepStateOptions.keepTouched ? _formState.touchedFields : {},\n            errors: keepStateOptions.keepErrors ? _formState.errors : {},\n            isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful ? _formState.isSubmitSuccessful : false,\n            isSubmitting: false\n        });\n    };\n    const reset = (formValues, keepStateOptions)=>_reset(isFunction(formValues) ? formValues(_formValues) : formValues, keepStateOptions);\n    const setFocus = (name, options = {})=>{\n        const field = get(_fields, name);\n        const fieldReference = field && field._f;\n        if (fieldReference) {\n            const fieldRef = fieldReference.refs ? fieldReference.refs[0] : fieldReference.ref;\n            if (fieldRef.focus) {\n                fieldRef.focus();\n                options.shouldSelect && isFunction(fieldRef.select) && fieldRef.select();\n            }\n        }\n    };\n    const _setFormState = (updatedFormState)=>{\n        _formState = {\n            ..._formState,\n            ...updatedFormState\n        };\n    };\n    const _resetDefaultValues = ()=>isFunction(_options.defaultValues) && _options.defaultValues().then((values)=>{\n            reset(values, _options.resetOptions);\n            _subjects.state.next({\n                isLoading: false\n            });\n        });\n    const methods = {\n        control: {\n            register,\n            unregister,\n            getFieldState,\n            handleSubmit,\n            setError,\n            _subscribe,\n            _runSchema,\n            _focusError,\n            _getWatch,\n            _getDirty,\n            _setValid,\n            _setFieldArray,\n            _setDisabledField,\n            _setErrors,\n            _getFieldArray,\n            _reset,\n            _resetDefaultValues,\n            _removeUnmounted,\n            _disableForm,\n            _subjects,\n            _proxyFormState,\n            get _fields () {\n                return _fields;\n            },\n            get _formValues () {\n                return _formValues;\n            },\n            get _state () {\n                return _state;\n            },\n            set _state (value){\n                _state = value;\n            },\n            get _defaultValues () {\n                return _defaultValues;\n            },\n            get _names () {\n                return _names;\n            },\n            set _names (value){\n                _names = value;\n            },\n            get _formState () {\n                return _formState;\n            },\n            get _options () {\n                return _options;\n            },\n            set _options (value){\n                _options = {\n                    ..._options,\n                    ...value\n                };\n            }\n        },\n        subscribe,\n        trigger,\n        register,\n        handleSubmit,\n        watch,\n        setValue,\n        getValues,\n        reset,\n        resetField,\n        clearErrors,\n        unregister,\n        setError,\n        setFocus,\n        getFieldState\n    };\n    return {\n        ...methods,\n        formControl: methods\n    };\n}\nvar generateId = ()=>{\n    if (typeof crypto !== \"undefined\" && crypto.randomUUID) {\n        return crypto.randomUUID();\n    }\n    const d = typeof performance === \"undefined\" ? Date.now() : performance.now() * 1000;\n    return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(/[xy]/g, (c)=>{\n        const r = (Math.random() * 16 + d) % 16 | 0;\n        return (c == \"x\" ? r : r & 0x3 | 0x8).toString(16);\n    });\n};\nvar getFocusFieldName = (name, index, options = {})=>options.shouldFocus || isUndefined(options.shouldFocus) ? options.focusName || `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.` : \"\";\nvar appendAt = (data, value1)=>[\n        ...data,\n        ...convertToArrayPayload(value1)\n    ];\nvar fillEmptyArray = (value1)=>Array.isArray(value1) ? value1.map(()=>undefined) : undefined;\nfunction insert(data, index, value1) {\n    return [\n        ...data.slice(0, index),\n        ...convertToArrayPayload(value1),\n        ...data.slice(index)\n    ];\n}\nvar moveArrayAt = (data, from, to)=>{\n    if (!Array.isArray(data)) {\n        return [];\n    }\n    if (isUndefined(data[to])) {\n        data[to] = undefined;\n    }\n    data.splice(to, 0, data.splice(from, 1)[0]);\n    return data;\n};\nvar prependAt = (data, value1)=>[\n        ...convertToArrayPayload(value1),\n        ...convertToArrayPayload(data)\n    ];\nfunction removeAtIndexes(data, indexes) {\n    let i = 0;\n    const temp = [\n        ...data\n    ];\n    for (const index of indexes){\n        temp.splice(index - i, 1);\n        i++;\n    }\n    return compact(temp).length ? temp : [];\n}\nvar removeArrayAt = (data, index)=>isUndefined(index) ? [] : removeAtIndexes(data, convertToArrayPayload(index).sort((a, b)=>a - b));\nvar swapArrayAt = (data, indexA, indexB)=>{\n    [data[indexA], data[indexB]] = [\n        data[indexB],\n        data[indexA]\n    ];\n};\nvar updateAt = (fieldValues, index, value1)=>{\n    fieldValues[index] = value1;\n    return fieldValues;\n};\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */ function useFieldArray(props) {\n    const methods = useFormContext();\n    const { control = methods.control, name, keyName = \"id\", shouldUnregister, rules } = props;\n    const [fields, setFields] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._getFieldArray(name));\n    const ids = react__WEBPACK_IMPORTED_MODULE_0__.useRef(control._getFieldArray(name).map(generateId));\n    const _fieldIds = react__WEBPACK_IMPORTED_MODULE_0__.useRef(fields);\n    const _name = react__WEBPACK_IMPORTED_MODULE_0__.useRef(name);\n    const _actioned = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    _name.current = name;\n    _fieldIds.current = fields;\n    control._names.array.add(name);\n    rules && control.register(name, rules);\n    useIsomorphicLayoutEffect(()=>control._subjects.array.subscribe({\n            next: ({ values, name: fieldArrayName })=>{\n                if (fieldArrayName === _name.current || !fieldArrayName) {\n                    const fieldValues = get(values, _name.current);\n                    if (Array.isArray(fieldValues)) {\n                        setFields(fieldValues);\n                        ids.current = fieldValues.map(generateId);\n                    }\n                }\n            }\n        }).unsubscribe, [\n        control\n    ]);\n    const updateValues = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((updatedFieldArrayValues)=>{\n        _actioned.current = true;\n        control._setFieldArray(name, updatedFieldArrayValues);\n    }, [\n        control,\n        name\n    ]);\n    const append = (value1, options)=>{\n        const appendValue = convertToArrayPayload(cloneObject(value1));\n        const updatedFieldArrayValues = appendAt(control._getFieldArray(name), appendValue);\n        control._names.focus = getFocusFieldName(name, updatedFieldArrayValues.length - 1, options);\n        ids.current = appendAt(ids.current, appendValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, appendAt, {\n            argA: fillEmptyArray(value1)\n        });\n    };\n    const prepend = (value1, options)=>{\n        const prependValue = convertToArrayPayload(cloneObject(value1));\n        const updatedFieldArrayValues = prependAt(control._getFieldArray(name), prependValue);\n        control._names.focus = getFocusFieldName(name, 0, options);\n        ids.current = prependAt(ids.current, prependValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, prependAt, {\n            argA: fillEmptyArray(value1)\n        });\n    };\n    const remove = (index)=>{\n        const updatedFieldArrayValues = removeArrayAt(control._getFieldArray(name), index);\n        ids.current = removeArrayAt(ids.current, index);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        !Array.isArray(get(control._fields, name)) && set(control._fields, name, undefined);\n        control._setFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n            argA: index\n        });\n    };\n    const insert$1 = (index, value1, options)=>{\n        const insertValue = convertToArrayPayload(cloneObject(value1));\n        const updatedFieldArrayValues = insert(control._getFieldArray(name), index, insertValue);\n        control._names.focus = getFocusFieldName(name, index, options);\n        ids.current = insert(ids.current, index, insertValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, insert, {\n            argA: index,\n            argB: fillEmptyArray(value1)\n        });\n    };\n    const swap = (indexA, indexB)=>{\n        const updatedFieldArrayValues = control._getFieldArray(name);\n        swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n        swapArrayAt(ids.current, indexA, indexB);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, swapArrayAt, {\n            argA: indexA,\n            argB: indexB\n        }, false);\n    };\n    const move = (from, to)=>{\n        const updatedFieldArrayValues = control._getFieldArray(name);\n        moveArrayAt(updatedFieldArrayValues, from, to);\n        moveArrayAt(ids.current, from, to);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, moveArrayAt, {\n            argA: from,\n            argB: to\n        }, false);\n    };\n    const update = (index, value1)=>{\n        const updateValue = cloneObject(value1);\n        const updatedFieldArrayValues = updateAt(control._getFieldArray(name), index, updateValue);\n        ids.current = [\n            ...updatedFieldArrayValues\n        ].map((item, i)=>!item || i === index ? generateId() : ids.current[i]);\n        updateValues(updatedFieldArrayValues);\n        setFields([\n            ...updatedFieldArrayValues\n        ]);\n        control._setFieldArray(name, updatedFieldArrayValues, updateAt, {\n            argA: index,\n            argB: updateValue\n        }, true, false);\n    };\n    const replace = (value1)=>{\n        const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value1));\n        ids.current = updatedFieldArrayValues.map(generateId);\n        updateValues([\n            ...updatedFieldArrayValues\n        ]);\n        setFields([\n            ...updatedFieldArrayValues\n        ]);\n        control._setFieldArray(name, [\n            ...updatedFieldArrayValues\n        ], (data)=>data, {}, true, false);\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        control._state.action = false;\n        isWatched(name, control._names) && control._subjects.state.next({\n            ...control._formState\n        });\n        if (_actioned.current && (!getValidationModes(control._options.mode).isOnSubmit || control._formState.isSubmitted) && !getValidationModes(control._options.reValidateMode).isOnSubmit) {\n            if (control._options.resolver) {\n                control._runSchema([\n                    name\n                ]).then((result)=>{\n                    const error = get(result.errors, name);\n                    const existingError = get(control._formState.errors, name);\n                    if (existingError ? !error && existingError.type || error && (existingError.type !== error.type || existingError.message !== error.message) : error && error.type) {\n                        error ? set(control._formState.errors, name, error) : unset(control._formState.errors, name);\n                        control._subjects.state.next({\n                            errors: control._formState.errors\n                        });\n                    }\n                });\n            } else {\n                const field = get(control._fields, name);\n                if (field && field._f && !(getValidationModes(control._options.reValidateMode).isOnSubmit && getValidationModes(control._options.mode).isOnSubmit)) {\n                    validateField(field, control._names.disabled, control._formValues, control._options.criteriaMode === VALIDATION_MODE.all, control._options.shouldUseNativeValidation, true).then((error)=>!isEmptyObject(error) && control._subjects.state.next({\n                            errors: updateFieldArrayRootError(control._formState.errors, error, name)\n                        }));\n                }\n            }\n        }\n        control._subjects.state.next({\n            name,\n            values: cloneObject(control._formValues)\n        });\n        control._names.focus && iterateFieldsByAction(control._fields, (ref, key)=>{\n            if (control._names.focus && key.startsWith(control._names.focus) && ref.focus) {\n                ref.focus();\n                return 1;\n            }\n            return;\n        });\n        control._names.focus = \"\";\n        control._setValid();\n        _actioned.current = false;\n    }, [\n        fields,\n        name,\n        control\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        !get(control._formValues, name) && control._setFieldArray(name);\n        return ()=>{\n            const updateMounted = (name, value1)=>{\n                const field = get(control._fields, name);\n                if (field && field._f) {\n                    field._f.mount = value1;\n                }\n            };\n            control._options.shouldUnregister || shouldUnregister ? control.unregister(name) : updateMounted(name, false);\n        };\n    }, [\n        name,\n        control,\n        keyName,\n        shouldUnregister\n    ]);\n    return {\n        swap: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(swap, [\n            updateValues,\n            name,\n            control\n        ]),\n        move: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(move, [\n            updateValues,\n            name,\n            control\n        ]),\n        prepend: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(prepend, [\n            updateValues,\n            name,\n            control\n        ]),\n        append: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(append, [\n            updateValues,\n            name,\n            control\n        ]),\n        remove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(remove, [\n            updateValues,\n            name,\n            control\n        ]),\n        insert: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(insert$1, [\n            updateValues,\n            name,\n            control\n        ]),\n        update: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(update, [\n            updateValues,\n            name,\n            control\n        ]),\n        replace: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(replace, [\n            updateValues,\n            name,\n            control\n        ]),\n        fields: react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>fields.map((field, index)=>({\n                    ...field,\n                    [keyName]: ids.current[index] || generateId()\n                })), [\n            fields,\n            keyName\n        ])\n    };\n}\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */ function useForm(props = {}) {\n    const _formControl = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    const _values = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    const [formState, updateFormState] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        isDirty: false,\n        isValidating: false,\n        isLoading: isFunction(props.defaultValues),\n        isSubmitted: false,\n        isSubmitting: false,\n        isSubmitSuccessful: false,\n        isValid: false,\n        submitCount: 0,\n        dirtyFields: {},\n        touchedFields: {},\n        validatingFields: {},\n        errors: props.errors || {},\n        disabled: props.disabled || false,\n        isReady: false,\n        defaultValues: isFunction(props.defaultValues) ? undefined : props.defaultValues\n    });\n    if (!_formControl.current) {\n        if (props.formControl) {\n            _formControl.current = {\n                ...props.formControl,\n                formState\n            };\n            if (props.defaultValues && !isFunction(props.defaultValues)) {\n                props.formControl.reset(props.defaultValues, props.resetOptions);\n            }\n        } else {\n            const { formControl, ...rest } = createFormControl(props);\n            _formControl.current = {\n                ...rest,\n                formState\n            };\n        }\n    }\n    const control = _formControl.current.control;\n    control._options = props;\n    useIsomorphicLayoutEffect(()=>{\n        const sub = control._subscribe({\n            formState: control._proxyFormState,\n            callback: ()=>updateFormState({\n                    ...control._formState\n                }),\n            reRenderRoot: true\n        });\n        updateFormState((data)=>({\n                ...data,\n                isReady: true\n            }));\n        control._formState.isReady = true;\n        return sub;\n    }, [\n        control\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>control._disableForm(props.disabled), [\n        control,\n        props.disabled\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (props.mode) {\n            control._options.mode = props.mode;\n        }\n        if (props.reValidateMode) {\n            control._options.reValidateMode = props.reValidateMode;\n        }\n    }, [\n        control,\n        props.mode,\n        props.reValidateMode\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (props.errors) {\n            control._setErrors(props.errors);\n            control._focusError();\n        }\n    }, [\n        control,\n        props.errors\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        props.shouldUnregister && control._subjects.state.next({\n            values: control._getWatch()\n        });\n    }, [\n        control,\n        props.shouldUnregister\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (control._proxyFormState.isDirty) {\n            const isDirty = control._getDirty();\n            if (isDirty !== formState.isDirty) {\n                control._subjects.state.next({\n                    isDirty\n                });\n            }\n        }\n    }, [\n        control,\n        formState.isDirty\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (props.values && !deepEqual(props.values, _values.current)) {\n            control._reset(props.values, {\n                keepFieldsRef: true,\n                ...control._options.resetOptions\n            });\n            _values.current = props.values;\n            updateFormState((state)=>({\n                    ...state\n                }));\n        } else {\n            control._resetDefaultValues();\n        }\n    }, [\n        control,\n        props.values\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (!control._state.mount) {\n            control._setValid();\n            control._state.mount = true;\n        }\n        if (control._state.watch) {\n            control._state.watch = false;\n            control._subjects.state.next({\n                ...control._formState\n            });\n        }\n        control._removeUnmounted();\n    });\n    _formControl.current.formState = getProxyFormState(formState, control);\n    return _formControl.current;\n}\n //# sourceMappingURL=index.esm.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\n");

/***/ })

};
;