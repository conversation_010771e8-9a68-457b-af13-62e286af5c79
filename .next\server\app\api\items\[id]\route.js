"use strict";(()=>{var e={};e.id=3616,e.ids=[3616],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},15592:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>h,originalPathname:()=>b,patchFetch:()=>x,requestAsyncStorage:()=>g,routeModule:()=>w,serverHooks:()=>f,staticGenerationAsyncStorage:()=>v,staticGenerationBailout:()=>q});var i={};r.r(i),r.d(i,{DELETE:()=>k,GET:()=>y,PUT:()=>I});var n=r(95419),a=r(69108),o=r(99678),s=r(78070),c=r(81355),u=r(3205),l=r(9108),d=r(25252),m=r(52178);let p=d.Ry({name:d.Z_().min(1,"Name is required").optional(),description:d.Z_().optional(),sku:d.Z_().optional(),category:d.Z_().optional(),unitPrice:d.Rx().min(0,"Unit price must be positive").optional(),costPrice:d.Rx().min(0,"Cost price must be positive").optional(),currency:d.Z_().optional(),trackInventory:d.O7().optional(),stockQuantity:d.Rx().int().min(0).optional(),lowStockAlert:d.Rx().int().min(0).optional(),taxable:d.O7().optional(),taxRate:d.Rx().min(0).max(100).optional(),accountingCode:d.Z_().optional(),active:d.O7().optional()});async function y(e,{params:t}){try{let e=await (0,c.getServerSession)(u.L);if(!e?.user?.id||!e?.user?.companyId)return s.Z.json({error:"Unauthorized"},{status:401});let r=await l._.item.findFirst({where:{id:t.id,companyId:e.user.companyId},include:{createdBy:{select:{name:!0,email:!0}},quotationItems:{include:{quotation:{select:{id:!0,quotationNumber:!0,title:!0,status:!0,total:!0,createdAt:!0,customer:{select:{name:!0,company:!0}}}}},orderBy:{createdAt:"desc"},take:10},invoiceItems:{include:{invoice:{select:{id:!0,invoiceNumber:!0,title:!0,status:!0,total:!0,createdAt:!0,customer:{select:{name:!0,company:!0}}}}},orderBy:{createdAt:"desc"},take:10}}});if(!r)return s.Z.json({error:"Item not found"},{status:404});let i=r.quotationItems.reduce((e,t)=>e+t.quantity,0),n=r.invoiceItems.reduce((e,t)=>e+t.quantity,0),a=r.quotationItems.length+r.invoiceItems.length,o=r.quotationItems.reduce((e,t)=>e+t.quantity*Number(t.unitPrice),0),d=r.invoiceItems.reduce((e,t)=>e+t.quantity*Number(t.unitPrice),0);return s.Z.json({item:{id:r.id,name:r.name,description:r.description,sku:r.sku,category:r.category,unitPrice:Number(r.unitPrice),costPrice:r.costPrice?Number(r.costPrice):null,currency:r.currency,trackInventory:r.trackInventory,stockQuantity:r.stockQuantity,lowStockAlert:r.lowStockAlert,taxable:r.taxable,taxRate:Number(r.taxRate),accountingCode:r.accountingCode,active:r.active,createdBy:r.createdBy,createdAt:r.createdAt,updatedAt:r.updatedAt,usage:{totalQuantity:i+n,usageCount:a,quotationUsage:i,invoiceUsage:n,totalRevenue:o+d,quotationRevenue:o,invoiceRevenue:d,quotations:r.quotationItems.map(e=>({id:e.id,quantity:e.quantity,unitPrice:Number(e.unitPrice),total:e.quantity*Number(e.unitPrice),quotation:e.quotation})),invoices:r.invoiceItems.map(e=>({id:e.id,quantity:e.quantity,unitPrice:Number(e.unitPrice),total:e.quantity*Number(e.unitPrice),invoice:e.invoice}))},stockValue:r.trackInventory&&r.stockQuantity?Number(r.unitPrice)*r.stockQuantity:0,profitMargin:r.costPrice?(Number(r.unitPrice)-Number(r.costPrice))/Number(r.unitPrice)*100:null,profitPerUnit:r.costPrice?Number(r.unitPrice)-Number(r.costPrice):null,isLowStock:!!r.trackInventory&&null!==r.stockQuantity&&null!==r.lowStockAlert&&r.stockQuantity<=r.lowStockAlert}})}catch(e){return console.error("Error fetching item:",e),s.Z.json({error:"Failed to fetch item"},{status:500})}}async function I(e,{params:t}){try{let r=await (0,c.getServerSession)(u.L);if(!r?.user?.id||!r?.user?.companyId)return s.Z.json({error:"Unauthorized"},{status:401});let i=await e.json(),n=p.parse(i),a=await l._.item.findFirst({where:{id:t.id,companyId:r.user.companyId}});if(!a)return s.Z.json({error:"Item not found"},{status:404});if(n.sku&&n.sku!==a.sku&&await l._.item.findFirst({where:{sku:n.sku,companyId:r.user.companyId,id:{not:t.id}}}))return s.Z.json({error:"An item with this SKU already exists"},{status:400});if(void 0!==n.trackInventory){if(n.trackInventory&&void 0===n.stockQuantity&&null===a.stockQuantity)return s.Z.json({error:"Stock quantity is required when enabling inventory tracking"},{status:400});n.trackInventory||(n.stockQuantity=null,n.lowStockAlert=null)}let o=await l._.$transaction(async e=>{let i=await e.item.update({where:{id:t.id},data:n,include:{createdBy:{select:{name:!0,email:!0}}}});return await e.activity.create({data:{type:"ITEM",title:"Item Updated",description:`Item "${i.name}" was updated`,itemId:t.id,companyId:r.user.companyId,createdById:r.user.id}}),i});return s.Z.json({item:{id:o.id,name:o.name,description:o.description,sku:o.sku,category:o.category,unitPrice:Number(o.unitPrice),costPrice:o.costPrice?Number(o.costPrice):null,currency:o.currency,trackInventory:o.trackInventory,stockQuantity:o.stockQuantity,lowStockAlert:o.lowStockAlert,taxable:o.taxable,taxRate:Number(o.taxRate),accountingCode:o.accountingCode,active:o.active,createdBy:o.createdBy,createdAt:o.createdAt,updatedAt:o.updatedAt,stockValue:o.trackInventory&&o.stockQuantity?Number(o.unitPrice)*o.stockQuantity:0,profitMargin:o.costPrice?(Number(o.unitPrice)-Number(o.costPrice))/Number(o.unitPrice)*100:null,isLowStock:!!o.trackInventory&&null!==o.stockQuantity&&null!==o.lowStockAlert&&o.stockQuantity<=o.lowStockAlert},message:"Item updated successfully"})}catch(e){if(e instanceof m.jm)return s.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("Error updating item:",e),s.Z.json({error:"Failed to update item"},{status:500})}}async function k(e,{params:t}){try{let e=await (0,c.getServerSession)(u.L);if(!e?.user?.id||!e?.user?.companyId)return s.Z.json({error:"Unauthorized"},{status:401});let r=await l._.item.findFirst({where:{id:t.id,companyId:e.user.companyId},include:{quotationItems:!0,invoiceItems:!0}});if(!r)return s.Z.json({error:"Item not found"},{status:404});if(r.quotationItems.length>0||r.invoiceItems.length>0)return s.Z.json({error:"Cannot delete item that is used in quotations or invoices. Consider deactivating it instead."},{status:400});return await l._.$transaction(async i=>{await i.item.delete({where:{id:t.id}}),await i.activity.create({data:{type:"ITEM",title:"Item Deleted",description:`Item "${r.name}" was deleted`,companyId:e.user.companyId,createdById:e.user.id}})}),s.Z.json({message:"Item deleted successfully"})}catch(e){return console.error("Error deleting item:",e),s.Z.json({error:"Failed to delete item"},{status:500})}}let w=new n.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/items/[id]/route",pathname:"/api/items/[id]",filename:"route",bundlePath:"app/api/items/[id]/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\items\\[id]\\route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:g,staticGenerationAsyncStorage:v,serverHooks:f,headerHooks:h,staticGenerationBailout:q}=w,b="/api/items/[id]/route";function x(){return(0,o.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:v})}},3205:(e,t,r)=>{r.d(t,{L:()=>u});var i=r(86485),n=r(10375),a=r(50694),o=r(6521),s=r.n(o),c=r(9108);let u={providers:[(0,i.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await c._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),r=t?.companyId;if(!r&&t){let e=await c._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(r=e?.id)&&await c._.user.update({where:{id:t.id},data:{companyId:r}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await s().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await c._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:r}}catch(e){return console.error("Authentication error:",e),null}}}),(0,n.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,a.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,r)=>{r.d(t,{_:()=>n});let i=require("@prisma/client"),n=globalThis.prisma??new i.PrismaClient}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[1638,6206,6521,2455,4520,5252],()=>r(15592));module.exports=i})();