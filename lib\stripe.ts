import Stripe from 'stripe'

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('STRIPE_SECRET_KEY is not set in environment variables')
}

export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2024-06-20',
  typescript: true,
})

export const getStripeCustomerByEmail = async (email: string) => {
  const customers = await stripe.customers.list({
    email,
    limit: 1,
  })
  return customers.data[0] || null
}

export const createStripeCustomer = async (data: {
  email: string
  name?: string
  companyId: string
}) => {
  return await stripe.customers.create({
    email: data.email,
    name: data.name,
    metadata: {
      companyId: data.companyId,
    },
  })
}

export const createStripeSubscription = async (data: {
  customerId: string
  priceId: string
  trialPeriodDays?: number
}) => {
  return await stripe.subscriptions.create({
    customer: data.customerId,
    items: [{ price: data.priceId }],
    trial_period_days: data.trialPeriodDays,
    payment_behavior: 'default_incomplete',
    payment_settings: { save_default_payment_method: 'on_subscription' },
    expand: ['latest_invoice.payment_intent'],
  })
}

export const createStripeCheckoutSession = async (data: {
  customerId: string
  priceId: string
  successUrl: string
  cancelUrl: string
  trialPeriodDays?: number
}) => {
  const sessionData: Stripe.Checkout.SessionCreateParams = {
    customer: data.customerId,
    payment_method_types: ['card'],
    line_items: [
      {
        price: data.priceId,
        quantity: 1,
      },
    ],
    mode: 'subscription',
    success_url: data.successUrl,
    cancel_url: data.cancelUrl,
    allow_promotion_codes: true,
  }

  if (data.trialPeriodDays && data.trialPeriodDays > 0) {
    sessionData.subscription_data = {
      trial_period_days: data.trialPeriodDays,
    }
  }

  return await stripe.checkout.sessions.create(sessionData)
}

export const updateStripeSubscription = async (
  subscriptionId: string,
  data: {
    priceId?: string
    cancelAtPeriodEnd?: boolean
  }
) => {
  const updateData: Stripe.SubscriptionUpdateParams = {}

  if (data.priceId) {
    const subscription = await stripe.subscriptions.retrieve(subscriptionId)
    updateData.items = [
      {
        id: subscription.items.data[0].id,
        price: data.priceId,
      },
    ]
  }

  if (data.cancelAtPeriodEnd !== undefined) {
    updateData.cancel_at_period_end = data.cancelAtPeriodEnd
  }

  return await stripe.subscriptions.update(subscriptionId, updateData)
}

export const cancelStripeSubscription = async (subscriptionId: string) => {
  return await stripe.subscriptions.cancel(subscriptionId)
}

export const getStripeSubscription = async (subscriptionId: string) => {
  return await stripe.subscriptions.retrieve(subscriptionId)
}

export const createStripePortalSession = async (data: {
  customerId: string
  returnUrl: string
}) => {
  return await stripe.billingPortal.sessions.create({
    customer: data.customerId,
    return_url: data.returnUrl,
  })
}

// Webhook signature verification
export const verifyStripeWebhook = (
  payload: string | Buffer,
  signature: string,
  secret: string
) => {
  return stripe.webhooks.constructEvent(payload, signature, secret)
}

// Price formatting utilities
export const formatStripeAmount = (amount: number, currency: string = 'usd') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency.toUpperCase(),
  }).format(amount / 100)
}

export const convertToStripeAmount = (amount: number) => {
  return Math.round(amount * 100)
}

// Product and price management
export const createStripeProduct = async (data: {
  name: string
  description?: string
  metadata?: Record<string, string>
}) => {
  return await stripe.products.create({
    name: data.name,
    description: data.description,
    metadata: data.metadata,
  })
}

export const createStripePrice = async (data: {
  productId: string
  unitAmount: number
  currency: string
  interval: 'month' | 'year'
  metadata?: Record<string, string>
}) => {
  return await stripe.prices.create({
    product: data.productId,
    unit_amount: data.unitAmount,
    currency: data.currency,
    recurring: {
      interval: data.interval,
    },
    metadata: data.metadata,
  })
}

export const syncPricingPlanWithStripe = async (plan: {
  id: string
  name: string
  description: string
  monthlyPrice: number
  yearlyPrice?: number
  currency: string
}) => {
  try {
    // Create or update Stripe product
    const product = await createStripeProduct({
      name: plan.name,
      description: plan.description,
      metadata: {
        planId: plan.id,
      },
    })

    // Create monthly price
    const monthlyPrice = await createStripePrice({
      productId: product.id,
      unitAmount: convertToStripeAmount(plan.monthlyPrice),
      currency: plan.currency.toLowerCase(),
      interval: 'month',
      metadata: {
        planId: plan.id,
        billingCycle: 'monthly',
      },
    })

    let yearlyPrice = null
    if (plan.yearlyPrice) {
      yearlyPrice = await createStripePrice({
        productId: product.id,
        unitAmount: convertToStripeAmount(plan.yearlyPrice),
        currency: plan.currency.toLowerCase(),
        interval: 'year',
        metadata: {
          planId: plan.id,
          billingCycle: 'yearly',
        },
      })
    }

    return {
      productId: product.id,
      monthlyPriceId: monthlyPrice.id,
      yearlyPriceId: yearlyPrice?.id || null,
    }
  } catch (error) {
    console.error('Error syncing pricing plan with Stripe:', error)
    throw error
  }
}
