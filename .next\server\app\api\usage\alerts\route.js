"use strict";(()=>{var e={};e.id=2406,e.ids=[2406],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},93888:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>w,originalPathname:()=>f,patchFetch:()=>x,requestAsyncStorage:()=>g,routeModule:()=>m,serverHooks:()=>h,staticGenerationAsyncStorage:()=>y,staticGenerationBailout:()=>I});var a={};t.r(a),t.d(a,{GET:()=>d,POST:()=>p});var i=t(95419),s=t(69108),n=t(99678),o=t(78070),l=t(81355),u=t(3205),c=t(9108);async function d(e){try{let e=await (0,l.getServerSession)(u.L);if(!e?.user?.companyId)return o.Z.json({success:!1,error:"Unauthorized"},{status:401});let r=await c._.subscription.findFirst({where:{companyId:e.user.companyId,status:{in:["ACTIVE","TRIALING","PAST_DUE"]}},include:{pricingPlan:!0}});if(!r)return o.Z.json({success:!0,data:{alerts:[]}});let[t,a,i,s,n]=await Promise.all([c._.user.count({where:{companyId:e.user.companyId}}),c._.customer.count({where:{companyId:e.user.companyId}}),c._.quotation.count({where:{companyId:e.user.companyId}}),c._.invoice.count({where:{companyId:e.user.companyId}}),c._.contract.count({where:{companyId:e.user.companyId}})]),d=[];for(let e of[{name:"Users",current:t,limit:r.pricingPlan.maxUsers,type:"users"},{name:"Customers",current:a,limit:r.pricingPlan.maxCustomers,type:"customers"},{name:"Quotations",current:i,limit:r.pricingPlan.maxQuotations,type:"quotations"},{name:"Invoices",current:s,limit:r.pricingPlan.maxInvoices,type:"invoices"},{name:"Contracts",current:n,limit:r.pricingPlan.maxContracts,type:"contracts"}]){let r=e.current/e.limit*100;e.current>=e.limit?d.push({id:`${e.type}-exceeded`,type:"error",title:`${e.name} Limit Exceeded`,message:`You've reached your ${e.name.toLowerCase()} limit (${e.current}/${e.limit}). Upgrade your plan to add more.`,percentage:100,action:{label:"Upgrade Plan",url:"/pricing"},priority:"high"}):r>=90?d.push({id:`${e.type}-critical`,type:"warning",title:`${e.name} Limit Almost Reached`,message:`You're using ${Math.round(r)}% of your ${e.name.toLowerCase()} limit (${e.current}/${e.limit}).`,percentage:Math.round(r),action:{label:"Upgrade Plan",url:"/pricing"},priority:"high"}):r>=75&&d.push({id:`${e.type}-warning`,type:"info",title:`${e.name} Usage High`,message:`You're using ${Math.round(r)}% of your ${e.name.toLowerCase()} limit (${e.current}/${e.limit}).`,percentage:Math.round(r),action:{label:"View Usage",url:"/subscription?tab=usage"},priority:"medium"})}if("TRIALING"===r.status&&r.trialEnd){let e=Math.ceil((r.trialEnd.getTime()-new Date().getTime())/864e5);e<=3?d.push({id:"trial-ending",type:"warning",title:"Trial Ending Soon",message:`Your free trial ends in ${e} day${1!==e?"s":""}. Add a payment method to continue using the service.`,percentage:null,action:{label:"Add Payment Method",url:"/subscription?tab=billing"},priority:"high"}):e<=7&&d.push({id:"trial-reminder",type:"info",title:"Trial Reminder",message:`Your free trial ends in ${e} days. Consider upgrading to continue using all features.`,percentage:null,action:{label:"View Plans",url:"/pricing"},priority:"medium"})}let p={high:3,medium:2,low:1};return d.sort((e,r)=>p[r.priority]-p[e.priority]),o.Z.json({success:!0,data:{alerts:d,subscription:{planName:r.pricingPlan.name,status:r.status,trialEnd:r.trialEnd}}})}catch(e){return console.error("Error fetching usage alerts:",e),o.Z.json({success:!1,error:"Failed to fetch usage alerts"},{status:500})}}async function p(e){try{let r=await (0,l.getServerSession)(u.L);if(!r?.user?.companyId)return o.Z.json({success:!1,error:"Unauthorized"},{status:401});let{alertId:t,action:a}=await e.json();if("dismiss"===a)return o.Z.json({success:!0,message:"Alert dismissed"});return o.Z.json({success:!1,error:"Invalid action"},{status:400})}catch(e){return console.error("Error handling alert action:",e),o.Z.json({success:!1,error:"Failed to handle alert action"},{status:500})}}let m=new i.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/usage/alerts/route",pathname:"/api/usage/alerts",filename:"route",bundlePath:"app/api/usage/alerts/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\usage\\alerts\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:g,staticGenerationAsyncStorage:y,serverHooks:h,headerHooks:w,staticGenerationBailout:I}=m,f="/api/usage/alerts/route";function x(){return(0,n.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:y})}},3205:(e,r,t)=>{t.d(r,{L:()=>u});var a=t(86485),i=t(10375),s=t(50694),n=t(6521),o=t.n(n),l=t(9108);let u={providers:[(0,a.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let r=await l._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),t=r?.companyId;if(!t&&r){let e=await l._.company.findFirst({where:{ownerId:r.id},select:{id:!0}});(t=e?.id)&&await l._.user.update({where:{id:r.id},data:{companyId:t}})}if(!r)return console.log("User not found:",e.email),null;if(!r.password)return console.log("User has no password set:",e.email),null;if(!await o().compare(e.password,r.password))return console.log("Invalid password for user:",e.email),null;return await l._.user.update({where:{id:r.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",r.email),{id:r.id,email:r.email,name:r.name,role:r.role,companyId:t}}catch(e){return console.error("Authentication error:",e),null}}}),(0,i.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,s.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(console.log("JWT callback - user data:",{id:r.id,email:r.email,role:r.role,companyId:r.companyId}),e.role=r.role,e.companyId=r.companyId),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role,e.user.companyId=r.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,r,t)=>{t.d(r,{_:()=>i});let a=require("@prisma/client"),i=globalThis.prisma??new a.PrismaClient}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[1638,6206,6521,2455,4520],()=>t(93888));module.exports=a})();