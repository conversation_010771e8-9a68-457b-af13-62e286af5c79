'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Mail, Send, FileText, User } from 'lucide-react'
import { toast } from 'react-hot-toast'

interface Quotation {
  id: string
  quotationNumber: string
  title: string
  total: number
  status: string
  customer: {
    id: string
    name: string
    email: string | null
    company: string | null
  }
}

interface SendQuotationModalProps {
  open: boolean
  quotation: Quotation | null
  onClose: () => void
  onSuccess: () => void
}

export function SendQuotationModal({ open, quotation, onClose, onSuccess }: SendQuotationModalProps) {
  const [formData, setFormData] = useState({
    to: '',
    cc: '',
    subject: '',
    message: '',
    includePDF: true
  })
  const [sending, setSending] = useState(false)

  // Initialize form data when quotation changes
  useState(() => {
    if (quotation) {
      setFormData({
        to: quotation.customer.email || '',
        cc: '',
        subject: `Quotation ${quotation.quotationNumber} - ${quotation.title}`,
        message: `Dear ${quotation.customer.name},

I hope this email finds you well. Please find attached our quotation for your review.

Quotation Details:
- Quotation Number: ${quotation.quotationNumber}
- Project: ${quotation.title}
- Total Amount: $${quotation.total.toLocaleString()}

This quotation is valid for 30 days from the date of issue. If you have any questions or need clarification on any items, please don't hesitate to contact me.

We look forward to working with you on this project.

Best regards,
[Your Name]`,
        includePDF: true
      })
    }
  }, [quotation])

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSend = async () => {
    if (!quotation) return

    if (!formData.to) {
      toast.error('Recipient email is required')
      return
    }

    if (!formData.subject) {
      toast.error('Subject is required')
      return
    }

    if (!formData.message) {
      toast.error('Message is required')
      return
    }

    setSending(true)
    try {
      const ccEmails = formData.cc
        .split(',')
        .map(email => email.trim())
        .filter(email => email.length > 0)

      const response = await fetch(`/api/quotations/${quotation.id}/send`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          to: formData.to,
          cc: ccEmails,
          subject: formData.subject,
          message: formData.message,
          includePDF: formData.includePDF
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to send quotation')
      }

      const result = await response.json()
      
      // In development mode, show email preview
      if (result.emailPreview) {
        console.log('Email Preview:', result.emailPreview)
        toast.success('Quotation sent successfully (development mode)')
      } else {
        toast.success('Quotation sent successfully!')
      }

      onSuccess()
      onClose()
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to send quotation')
    } finally {
      setSending(false)
    }
  }

  const handleClose = () => {
    setFormData({
      to: '',
      cc: '',
      subject: '',
      message: '',
      includePDF: true
    })
    onClose()
  }

  if (!quotation) return null

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DRAFT':
        return 'bg-gray-100 text-gray-800'
      case 'SENT':
        return 'bg-blue-100 text-blue-800'
      case 'VIEWED':
        return 'bg-yellow-100 text-yellow-800'
      case 'ACCEPTED':
        return 'bg-green-100 text-green-800'
      case 'REJECTED':
        return 'bg-red-100 text-red-800'
      case 'EXPIRED':
        return 'bg-orange-100 text-orange-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Mail className="h-5 w-5 mr-2" />
            Send Quotation
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Quotation Summary */}
          <div className="p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-2">
                <FileText className="h-5 w-5 text-blue-600" />
                <span className="font-semibold">{quotation.quotationNumber}</span>
                <Badge className={getStatusColor(quotation.status)}>
                  {quotation.status}
                </Badge>
              </div>
              <span className="text-lg font-bold text-green-600">
                {formatCurrency(quotation.total)}
              </span>
            </div>
            <div className="text-sm text-gray-600">
              <p className="font-medium">{quotation.title}</p>
              <div className="flex items-center mt-1">
                <User className="h-4 w-4 mr-1" />
                <span>{quotation.customer.name}</span>
                {quotation.customer.company && (
                  <span className="ml-1">({quotation.customer.company})</span>
                )}
              </div>
            </div>
          </div>

          {/* Email Form */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="to">To *</Label>
              <Input
                id="to"
                type="email"
                value={formData.to}
                onChange={(e) => handleInputChange('to', e.target.value)}
                placeholder="<EMAIL>"
                required
              />
            </div>

            <div>
              <Label htmlFor="cc">CC (optional)</Label>
              <Input
                id="cc"
                type="email"
                value={formData.cc}
                onChange={(e) => handleInputChange('cc', e.target.value)}
                placeholder="<EMAIL>, <EMAIL>"
              />
              <p className="text-sm text-gray-500 mt-1">
                Separate multiple emails with commas
              </p>
            </div>

            <div>
              <Label htmlFor="subject">Subject *</Label>
              <Input
                id="subject"
                value={formData.subject}
                onChange={(e) => handleInputChange('subject', e.target.value)}
                placeholder="Quotation subject"
                required
              />
            </div>

            <div>
              <Label htmlFor="message">Message *</Label>
              <Textarea
                id="message"
                value={formData.message}
                onChange={(e) => handleInputChange('message', e.target.value)}
                placeholder="Enter your message here..."
                rows={8}
                required
              />
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="includePDF"
                checked={formData.includePDF}
                onCheckedChange={(checked) => handleInputChange('includePDF', checked as boolean)}
              />
              <Label htmlFor="includePDF">Include PDF attachment</Label>
            </div>
          </div>

          <Separator />

          {/* Preview Note */}
          <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
            <p className="text-sm text-blue-800">
              <strong>Note:</strong> In development mode, emails are not actually sent. 
              The email content will be logged to the console for preview.
            </p>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-2 pt-4 border-t">
            <Button variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button onClick={handleSend} disabled={sending}>
              {sending ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Sending...
                </>
              ) : (
                <>
                  <Send className="h-4 w-4 mr-2" />
                  Send Quotation
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
