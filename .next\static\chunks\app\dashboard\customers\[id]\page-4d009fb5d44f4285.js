(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[541],{93279:function(e,s,t){Promise.resolve().then(t.bind(t,10263))},10263:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return A}});var a=t(57437),r=t(2265),n=t(24033),i=t(27815),c=t(85754),l=t(31478),d=t(5129),o=t(73067),m=t(49617),u=t(45367),x=t(67972),f=t(1295),h=t(12741),p=t(28956),j=t(98253),g=t(96142),N=t(90998),v=t(9883),y=t(85790),b=t(76637),w=t(15713),C=t(28203),Z=t(5925),k=t(61396),z=t.n(k);function A(){let e=(0,n.useParams)(),s=(0,n.useRouter)(),[t,k]=(0,r.useState)(null),[A,E]=(0,r.useState)(!0),[R,I]=(0,r.useState)(!1),_=async()=>{try{let t=await fetch("/api/customers/".concat(e.id));if(!t.ok){if(404===t.status){Z.toast.error("Customer not found"),s.push("/dashboard/customers");return}throw Error("Failed to fetch customer")}let a=await t.json();k(a)}catch(e){Z.toast.error("Failed to load customer details"),console.error("Error fetching customer:",e)}finally{E(!1)}};(0,r.useEffect)(()=>{e.id&&_()},[e.id]);let D=async()=>{if(t&&confirm("Are you sure you want to delete ".concat(t.name,"?")))try{let e=await fetch("/api/customers/".concat(t.id),{method:"DELETE"});if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to delete customer")}Z.toast.success("Customer deleted successfully"),s.push("/dashboard/customers")}catch(e){Z.toast.error(e instanceof Error?e.message:"Failed to delete customer")}};return A?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):t?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(c.z,{variant:"ghost",size:"sm",asChild:!0,children:(0,a.jsxs)(z(),{href:"/dashboard/customers",children:[(0,a.jsx)(o.Z,{className:"h-4 w-4 mr-2"}),"Back to Customers"]})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:t.name}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(e=>{switch(e){case"ACTIVE":return(0,a.jsx)(l.C,{variant:"success",children:"Active"});case"INACTIVE":return(0,a.jsx)(l.C,{variant:"secondary",children:"Inactive"});case"PROSPECT":return(0,a.jsx)(l.C,{variant:"warning",children:"Prospect"});default:return(0,a.jsx)(l.C,{variant:"secondary",children:e})}})(t.status),t.company&&(0,a.jsxs)("span",{className:"text-gray-500",children:["• ",t.company]})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(c.z,{variant:"outline",onClick:()=>I(!0),children:[(0,a.jsx)(m.Z,{className:"h-4 w-4 mr-2"}),"Edit"]}),(0,a.jsxs)(c.z,{variant:"destructive",onClick:D,children:[(0,a.jsx)(u.Z,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,a.jsxs)(i.Zb,{children:[(0,a.jsx)(i.Ol,{children:(0,a.jsxs)(i.ll,{className:"flex items-center",children:[(0,a.jsx)(x.Z,{className:"h-5 w-5 mr-2"}),"Contact Information"]})}),(0,a.jsxs)(i.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[t.email&&(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(f.Z,{className:"h-4 w-4 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Email"}),(0,a.jsx)("p",{className:"font-medium",children:t.email})]})]}),t.phone&&(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(h.Z,{className:"h-4 w-4 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Phone"}),(0,a.jsx)("p",{className:"font-medium",children:t.phone})]})]}),t.website&&(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(p.Z,{className:"h-4 w-4 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Website"}),(0,a.jsx)("a",{href:t.website,target:"_blank",rel:"noopener noreferrer",className:"font-medium text-blue-600 hover:underline",children:t.website})]})]}),t.industry&&(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(j.Z,{className:"h-4 w-4 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Industry"}),(0,a.jsx)("p",{className:"font-medium",children:t.industry})]})]})]}),(t.address||t.city||t.state||t.country)&&(0,a.jsxs)("div",{className:"flex items-start space-x-3 pt-4 border-t",children:[(0,a.jsx)(g.Z,{className:"h-4 w-4 text-gray-400 mt-1"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Address"}),(0,a.jsxs)("div",{className:"font-medium",children:[t.address&&(0,a.jsx)("p",{children:t.address}),(0,a.jsx)("p",{children:[t.city,t.state,t.postalCode].filter(Boolean).join(", ")}),t.country&&(0,a.jsx)("p",{children:t.country})]})]})]}),t.tags&&Array.isArray(t.tags)&&t.tags.length>0&&(0,a.jsxs)("div",{className:"pt-4 border-t",children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 mb-2",children:"Tags"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:t.tags.map(e=>(0,a.jsx)(l.C,{variant:"outline",children:e},e))})]}),t.notes&&(0,a.jsxs)("div",{className:"pt-4 border-t",children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 mb-2",children:"Notes"}),(0,a.jsx)("p",{className:"text-gray-900 whitespace-pre-wrap",children:t.notes})]})]})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsx)(i.Ol,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(i.ll,{className:"flex items-center",children:[(0,a.jsx)(N.Z,{className:"h-5 w-5 mr-2"}),"Recent Activity"]}),(0,a.jsxs)(c.z,{variant:"outline",size:"sm",children:[(0,a.jsx)(v.Z,{className:"h-4 w-4 mr-2"}),"Add Note"]})]})}),(0,a.jsx)(i.aY,{children:t.activities&&Array.isArray(t.activities)&&t.activities.length>0?(0,a.jsx)("div",{className:"space-y-4",children:t.activities.map(e=>(0,a.jsxs)("div",{className:"flex items-start space-x-3 pb-4 border-b last:border-b-0",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(N.Z,{className:"h-4 w-4 text-blue-600"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"font-medium",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:e.description}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[new Date(e.createdAt).toLocaleDateString()," by ",e.createdBy.name]})]})]},e.id))}):(0,a.jsx)("p",{className:"text-gray-500 text-center py-4",children:"No activity recorded yet"})})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(i.Zb,{children:[(0,a.jsx)(i.Ol,{children:(0,a.jsx)(i.ll,{children:"Overview"})}),(0,a.jsxs)(i.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(y.Z,{className:"h-4 w-4 text-green-600"}),(0,a.jsx)("span",{className:"text-sm",children:"Leads"})]}),(0,a.jsx)("span",{className:"font-medium",children:t._count.leads})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(b.Z,{className:"h-4 w-4 text-blue-600"}),(0,a.jsx)("span",{className:"text-sm",children:"Quotations"})]}),(0,a.jsx)("span",{className:"font-medium",children:t._count.quotations})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(w.Z,{className:"h-4 w-4 text-purple-600"}),(0,a.jsx)("span",{className:"text-sm",children:"Invoices"})]}),(0,a.jsx)("span",{className:"font-medium",children:t._count.invoices})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(C.Z,{className:"h-4 w-4 text-gray-600"}),(0,a.jsx)("span",{className:"text-sm",children:"Customer Since"})]}),(0,a.jsx)("span",{className:"font-medium text-sm",children:new Date(t.createdAt).toLocaleDateString()})]})]})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsx)(i.Ol,{children:(0,a.jsx)(i.ll,{children:"Quick Actions"})}),(0,a.jsxs)(i.aY,{className:"space-y-2",children:[(0,a.jsx)(c.z,{variant:"outline",className:"w-full justify-start",asChild:!0,children:(0,a.jsxs)(z(),{href:"/dashboard/leads/new?customerId=".concat(t.id),children:[(0,a.jsx)(y.Z,{className:"h-4 w-4 mr-2"}),"Create Lead"]})}),(0,a.jsx)(c.z,{variant:"outline",className:"w-full justify-start",asChild:!0,children:(0,a.jsxs)(z(),{href:"/dashboard/quotations/new?customerId=".concat(t.id),children:[(0,a.jsx)(b.Z,{className:"h-4 w-4 mr-2"}),"New Quotation"]})}),(0,a.jsx)(c.z,{variant:"outline",className:"w-full justify-start",asChild:!0,children:(0,a.jsxs)(z(),{href:"/dashboard/invoices/new?customerId=".concat(t.id),children:[(0,a.jsx)(w.Z,{className:"h-4 w-4 mr-2"}),"Create Invoice"]})})]})]})]})]}),(0,a.jsx)(d.A,{isOpen:R,onClose:()=>I(!1),onSuccess:_,customer:t,mode:"edit"})]}):(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("p",{className:"text-gray-500",children:"Customer not found"}),(0,a.jsx)(c.z,{asChild:!0,className:"mt-4",children:(0,a.jsx)(z(),{href:"/dashboard/customers",children:"Back to Customers"})})]})}},31478:function(e,s,t){"use strict";t.d(s,{C:function(){return c}});var a=t(57437);t(2265);var r=t(96061),n=t(1657);let i=(0,r.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600",info:"border-transparent bg-blue-500 text-white hover:bg-blue-600"}},defaultVariants:{variant:"default"}});function c(e){let{className:s,variant:t,...r}=e;return(0,a.jsx)("div",{className:(0,n.cn)(i({variant:t}),s),...r})}},85754:function(e,s,t){"use strict";t.d(s,{z:function(){return d}});var a=t(57437),r=t(2265),n=t(67256),i=t(96061),c=t(1657);let l=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=r.forwardRef((e,s)=>{let{className:t,variant:r,size:i,asChild:d=!1,...o}=e,m=d?n.g7:"button";return(0,a.jsx)(m,{className:(0,c.cn)(l({variant:r,size:i,className:t})),ref:s,...o})});d.displayName="Button"},27815:function(e,s,t){"use strict";t.d(s,{Ol:function(){return c},SZ:function(){return d},Zb:function(){return i},aY:function(){return o},eW:function(){return m},ll:function(){return l}});var a=t(57437),r=t(2265),n=t(1657);let i=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...r})});i.displayName="Card";let c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",t),...r})});c.displayName="CardHeader";let l=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("h3",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",t),...r})});l.displayName="CardTitle";let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",t),...r})});d.displayName="CardDescription";let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("p-6 pt-0",t),...r})});o.displayName="CardContent";let m=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",t),...r})});m.displayName="CardFooter"},42706:function(e,s,t){"use strict";t.d(s,{$N:function(){return h},Be:function(){return p},Vq:function(){return l},cN:function(){return f},cZ:function(){return u},fK:function(){return x},hg:function(){return d},t9:function(){return m}});var a=t(57437),r=t(2265),n=t(28712),i=t(82549),c=t(1657);let l=n.fC,d=n.xz,o=n.h_;n.x8;let m=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(n.aV,{ref:s,className:(0,c.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...r})});m.displayName=n.aV.displayName;let u=r.forwardRef((e,s)=>{let{className:t,children:r,...l}=e;return(0,a.jsxs)(o,{children:[(0,a.jsx)(m,{}),(0,a.jsxs)(n.VY,{ref:s,className:(0,c.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",t),...l,children:[r,(0,a.jsxs)(n.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(i.Z,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});u.displayName=n.VY.displayName;let x=e=>{let{className:s,...t}=e;return(0,a.jsx)("div",{className:(0,c.cn)("flex flex-col space-y-1.5 text-center sm:text-left",s),...t})};x.displayName="DialogHeader";let f=e=>{let{className:s,...t}=e;return(0,a.jsx)("div",{className:(0,c.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",s),...t})};f.displayName="DialogFooter";let h=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(n.Dx,{ref:s,className:(0,c.cn)("text-lg font-semibold leading-none tracking-tight",t),...r})});h.displayName=n.Dx.displayName;let p=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(n.dk,{ref:s,className:(0,c.cn)("text-sm text-muted-foreground",t),...r})});p.displayName=n.dk.displayName},45179:function(e,s,t){"use strict";t.d(s,{I:function(){return i}});var a=t(57437),r=t(2265),n=t(1657);let i=r.forwardRef((e,s)=>{let{className:t,type:r,...i}=e;return(0,a.jsx)("input",{type:r,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...i})});i.displayName="Input"},49842:function(e,s,t){"use strict";t.d(s,{_:function(){return d}});var a=t(57437),r=t(2265),n=t(36743),i=t(96061),c=t(1657);let l=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(n.f,{ref:s,className:(0,c.cn)(l(),t),...r})});d.displayName=n.f.displayName},1657:function(e,s,t){"use strict";t.d(s,{cn:function(){return n}});var a=t(57042),r=t(74769);function n(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,r.m6)((0,a.W)(s))}}},function(e){e.O(0,[6723,1706,1396,2881,9580,5129,2971,4938,1744],function(){return e(e.s=93279)}),_N_E=e.O()}]);