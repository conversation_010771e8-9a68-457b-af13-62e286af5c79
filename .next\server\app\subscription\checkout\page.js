(()=>{var e={};e.id=4534,e.ids=[4534],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},14922:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=t(50482),a=t(69108),n=t(62563),i=t.n(n),l=t(68300),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(r,o);let c=["",{children:["subscription",{children:["checkout",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,55367)),"C:\\proj\\nextjs-saas\\app\\subscription\\checkout\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,59504)),"C:\\proj\\nextjs-saas\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\proj\\nextjs-saas\\app\\subscription\\checkout\\page.tsx"],u="/subscription/checkout/page",m={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/subscription/checkout/page",pathname:"/subscription/checkout",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},3383:(e,r,t)=>{Promise.resolve().then(t.bind(t,10328))},64588:(e,r,t)=>{Promise.resolve().then(t.bind(t,56189)),Promise.resolve().then(t.bind(t,44669))},19634:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,2583,23)),Promise.resolve().then(t.t.bind(t,26840,23)),Promise.resolve().then(t.t.bind(t,38771,23)),Promise.resolve().then(t.t.bind(t,13225,23)),Promise.resolve().then(t.t.bind(t,9295,23)),Promise.resolve().then(t.t.bind(t,43982,23))},10328:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>es});var s=t(95344),a=t(3729),n=t(47674),i=t(22254),l=t(61351),o=t(16212),c=t(69436),d=t(57341),u=t(85222),m=t(31405),p=t(98462),x=t(62409),f=t(34504),h=t(33183),y=t(3975),g=t(63085),b=t(92062),v=t(43234),j="Radio",[N,w]=(0,p.b)(j),[C,k]=N(j),S=a.forwardRef((e,r)=>{let{__scopeRadio:t,name:n,checked:i=!1,required:l,disabled:o,value:c="on",onCheck:d,form:p,...f}=e,[h,y]=a.useState(null),g=(0,m.e)(r,e=>y(e)),b=a.useRef(!1),v=!h||p||!!h.closest("form");return(0,s.jsxs)(C,{scope:t,checked:i,disabled:o,children:[(0,s.jsx)(x.WV.button,{type:"button",role:"radio","aria-checked":i,"data-state":M(i),"data-disabled":o?"":void 0,disabled:o,value:c,...f,ref:g,onClick:(0,u.M)(e.onClick,e=>{i||d?.(),v&&(b.current=e.isPropagationStopped(),b.current||e.stopPropagation())})}),v&&(0,s.jsx)(Z,{control:h,bubbles:!b.current,name:n,value:c,checked:i,required:l,disabled:o,form:p,style:{transform:"translateX(-100%)"}})]})});S.displayName=j;var P="RadioIndicator",R=a.forwardRef((e,r)=>{let{__scopeRadio:t,forceMount:a,...n}=e,i=k(P,t);return(0,s.jsx)(v.z,{present:a||i.checked,children:(0,s.jsx)(x.WV.span,{"data-state":M(i.checked),"data-disabled":i.disabled?"":void 0,...n,ref:r})})});R.displayName=P;var Z=a.forwardRef(({__scopeRadio:e,control:r,checked:t,bubbles:n=!0,...i},l)=>{let o=a.useRef(null),c=(0,m.e)(o,l),d=(0,b.D)(t),u=(0,g.t)(r);return a.useEffect(()=>{let e=o.current;if(!e)return;let r=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(d!==t&&r){let s=new Event("click",{bubbles:n});r.call(e,t),e.dispatchEvent(s)}},[d,t,n]),(0,s.jsx)(x.WV.input,{type:"radio","aria-hidden":!0,defaultChecked:t,...i,tabIndex:-1,ref:c,style:{...i.style,...u,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function M(e){return e?"checked":"unchecked"}Z.displayName="RadioBubbleInput";var E=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],_="RadioGroup",[B,A]=(0,p.b)(_,[f.Pc,w]),q=(0,f.Pc)(),z=w(),[D,T]=B(_),F=a.forwardRef((e,r)=>{let{__scopeRadioGroup:t,name:a,defaultValue:n,value:i,required:l=!1,disabled:o=!1,orientation:c,dir:d,loop:u=!0,onValueChange:m,...p}=e,g=q(t),b=(0,y.gm)(d),[v,j]=(0,h.T)({prop:i,defaultProp:n??null,onChange:m,caller:_});return(0,s.jsx)(D,{scope:t,name:a,required:l,disabled:o,value:v,onValueChange:j,children:(0,s.jsx)(f.fC,{asChild:!0,...g,orientation:c,dir:b,loop:u,children:(0,s.jsx)(x.WV.div,{role:"radiogroup","aria-required":l,"aria-orientation":c,"data-disabled":o?"":void 0,dir:b,...p,ref:r})})})});F.displayName=_;var U="RadioGroupItem",O=a.forwardRef((e,r)=>{let{__scopeRadioGroup:t,disabled:n,...i}=e,l=T(U,t),o=l.disabled||n,c=q(t),d=z(t),p=a.useRef(null),x=(0,m.e)(r,p),h=l.value===i.value,y=a.useRef(!1);return a.useEffect(()=>{let e=e=>{E.includes(e.key)&&(y.current=!0)},r=()=>y.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",r),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",r)}},[]),(0,s.jsx)(f.ck,{asChild:!0,...c,focusable:!o,active:h,children:(0,s.jsx)(S,{disabled:o,required:l.required,checked:h,...d,...i,name:l.name,ref:x,onCheck:()=>l.onValueChange(i.value),onKeyDown:(0,u.M)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,u.M)(i.onFocus,()=>{y.current&&p.current?.click()})})})});O.displayName=U;var V=a.forwardRef((e,r)=>{let{__scopeRadioGroup:t,...a}=e,n=z(t);return(0,s.jsx)(R,{...n,...a,ref:r})});V.displayName="RadioGroupIndicator";var L=t(82958),W=t(91626);let I=a.forwardRef(({className:e,...r},t)=>s.jsx(F,{className:(0,W.cn)("grid gap-2",e),...r,ref:t}));I.displayName=F.displayName;let $=a.forwardRef(({className:e,...r},t)=>s.jsx(O,{ref:t,className:(0,W.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),...r,children:s.jsx(V,{className:"flex items-center justify-center",children:s.jsx(L.Z,{className:"h-2.5 w-2.5 fill-current text-current"})})}));$.displayName=O.displayName;var Y=t(1586),G=t(63024),H=t(89895),X=t(99046),J=t(62312),K=t(25545),Q=t(23485),ee=t(85674),er=t(34755);function et(){let{data:e}=(0,n.useSession)(),r=(0,i.useRouter)(),t=(0,i.useSearchParams)(),[u,m]=(0,a.useState)(null),[p,x]=(0,a.useState)("monthly"),[f,h]=(0,a.useState)(!0),[y,g]=(0,a.useState)(!1),b=t.get("plan"),v=t.get("billing")||"monthly";(0,a.useEffect)(()=>{x(v)},[v]),(0,a.useEffect)(()=>{b?j():r.push("/pricing")},[b]);let j=async()=>{try{let e=await fetch(`/api/pricing-plans/${b}`),t=await e.json();t.success?m(t.data):(er.Am.error("Plan not found"),r.push("/pricing"))}catch(e){console.error("Error fetching plan:",e),er.Am.error("Failed to load plan details"),r.push("/pricing")}finally{h(!1)}},N=async()=>{if(u&&e?.user){g(!0);try{let e=await fetch("/api/subscription",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({planId:u.id,billingCycle:p.toUpperCase(),useStripe:u.monthlyPrice>0})}),t=await e.json();t.success?t.data.checkoutUrl?window.location.href=t.data.checkoutUrl:(er.Am.success("Subscription created successfully!"),r.push("/subscription")):er.Am.error(t.error||"Failed to create subscription")}catch(e){console.error("Error creating subscription:",e),er.Am.error("Failed to create subscription")}finally{g(!1)}}};if(f)return s.jsx("div",{className:"container mx-auto p-6",children:s.jsx("div",{className:"flex items-center justify-center h-64",children:s.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})})});if(!u)return s.jsx("div",{className:"container mx-auto p-6",children:(0,s.jsxs)("div",{className:"text-center py-12",children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Plan not found"}),s.jsx(o.z,{onClick:()=>r.push("/pricing"),children:"Back to Pricing"})]})});let w="yearly"===p&&u.yearlyPrice?u.yearlyPrice/12:u.monthlyPrice,C="yearly"===p&&u.yearlyPrice?u.yearlyPrice:u.monthlyPrice,k="yearly"===p&&u.yearlyDiscount>0;return(0,s.jsxs)("div",{className:"container mx-auto p-6 max-w-4xl",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4 mb-8",children:[(0,s.jsxs)(o.z,{variant:"ghost",size:"sm",onClick:()=>r.back(),children:[s.jsx(G.Z,{className:"h-4 w-4 mr-2"}),"Back"]}),(0,s.jsxs)("div",{children:[s.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Complete Your Subscription"}),s.jsx("p",{className:"text-gray-600",children:"Review your plan and start your subscription"})]})]}),(0,s.jsxs)("div",{className:"grid lg:grid-cols-2 gap-8",children:[s.jsx("div",{className:"space-y-6",children:(0,s.jsxs)(l.Zb,{children:[(0,s.jsxs)(l.Ol,{children:[(0,s.jsxs)(l.ll,{className:"flex items-center justify-between",children:[u.name,"pro"===u.name.toLowerCase()&&s.jsx(c.C,{children:"Most Popular"})]}),s.jsx(l.SZ,{children:u.description})]}),(0,s.jsxs)(l.aY,{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[s.jsx(Y._,{className:"text-base font-medium",children:"Billing Cycle"}),(0,s.jsxs)(I,{value:p,onValueChange:e=>x(e),className:"mt-2",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 p-3 border rounded-lg",children:[s.jsx($,{value:"monthly",id:"monthly"}),s.jsx(Y._,{htmlFor:"monthly",className:"flex-1 cursor-pointer",children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[s.jsx("span",{children:"Monthly"}),(0,s.jsxs)("span",{className:"font-medium",children:["$",u.monthlyPrice,"/month"]})]})})]}),u.yearlyPrice&&(0,s.jsxs)("div",{className:"flex items-center space-x-2 p-3 border rounded-lg",children:[s.jsx($,{value:"yearly",id:"yearly"}),s.jsx(Y._,{htmlFor:"yearly",className:"flex-1 cursor-pointer",children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[s.jsx("span",{children:"Yearly"}),k&&(0,s.jsxs)(c.C,{variant:"secondary",className:"text-xs",children:[u.yearlyDiscount,"% off"]})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsxs)("div",{className:"font-medium",children:["$",(u.yearlyPrice/12).toFixed(0),"/month"]}),(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:["$",u.yearlyPrice,"/year"]})]})]})})]})]})]}),s.jsx(d.Z,{}),(0,s.jsxs)("div",{children:[s.jsx("h4",{className:"font-medium mb-3",children:"What's included:"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,s.jsxs)("span",{className:"flex items-center",children:[s.jsx(H.Z,{className:"h-4 w-4 mr-2 text-gray-400"}),"Users"]}),s.jsx("span",{className:"font-medium",children:u.maxUsers})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,s.jsxs)("span",{className:"flex items-center",children:[s.jsx(X.Z,{className:"h-4 w-4 mr-2 text-gray-400"}),"Storage"]}),s.jsx("span",{className:"font-medium",children:u.formattedStorage})]}),Object.entries(u.features).map(([e,r])=>r&&(0,s.jsxs)("div",{className:"flex items-center text-sm",children:[s.jsx(J.Z,{className:"h-4 w-4 mr-2 text-green-500"}),s.jsx("span",{children:e.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase())})]},e))]})]}),u.trialDays>0&&(0,s.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center text-blue-700",children:[s.jsx(K.Z,{className:"h-4 w-4 mr-2"}),(0,s.jsxs)("span",{className:"text-sm font-medium",children:[u.trialDays,"-day free trial included"]})]}),s.jsx("p",{className:"text-xs text-blue-600 mt-1",children:"You won't be charged until your trial ends"})]})]})]})}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)(l.Zb,{children:[s.jsx(l.Ol,{children:s.jsx(l.ll,{children:"Order Summary"})}),(0,s.jsxs)(l.aY,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsxs)("span",{children:[u.name," Plan"]}),(0,s.jsxs)("span",{children:["$",w.toFixed(0),"/month"]})]}),"yearly"===p&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"flex justify-between text-sm text-gray-600",children:[s.jsx("span",{children:"Billing cycle"}),s.jsx("span",{children:"Annual"})]}),k&&(0,s.jsxs)("div",{className:"flex justify-between text-sm text-green-600",children:[s.jsx("span",{children:"Annual discount"}),(0,s.jsxs)("span",{children:["-",u.yearlyDiscount,"%"]})]})]}),s.jsx(d.Z,{}),(0,s.jsxs)("div",{className:"flex justify-between font-medium text-lg",children:[(0,s.jsxs)("span",{children:["Total ","yearly"===p?"per year":"per month"]}),(0,s.jsxs)("span",{children:["$",C]})]}),u.trialDays>0&&s.jsx("div",{className:"text-sm text-gray-600",children:(0,s.jsxs)("p",{children:["Free for ",u.trialDays," days, then $",C," ","yearly"===p?"per year":"per month"]})})]})]}),s.jsx(l.Zb,{children:s.jsx(l.aY,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[s.jsx(Q.Z,{className:"h-4 w-4 mr-2 text-green-500"}),s.jsx("span",{children:"Secure checkout powered by industry-standard encryption"})]})})}),s.jsx(o.z,{className:"w-full",size:"lg",onClick:N,disabled:y,children:y?(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Processing..."]}):(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx(ee.Z,{className:"h-4 w-4 mr-2"}),u.trialDays>0?"Start Free Trial":"Subscribe Now"]})}),s.jsx("p",{className:"text-xs text-gray-500 text-center",children:"By subscribing, you agree to our Terms of Service and Privacy Policy. You can cancel anytime."})]})]})]})}function es(){return s.jsx(a.Suspense,{fallback:s.jsx("div",{className:"container mx-auto p-6",children:s.jsx("div",{className:"flex items-center justify-center h-64",children:s.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})})}),children:s.jsx(et,{})})}},56189:(e,r,t)=>{"use strict";t.r(r),t.d(r,{Providers:()=>d});var s=t(95344),a=t(47674),n=t(6256),i=t(19115),l=t(26274),o=t(3729),c=t(66091);function d({children:e}){let[r]=(0,o.useState)(()=>new i.S({defaultOptions:{queries:{staleTime:6e4,retry:1}}}));return s.jsx(a.SessionProvider,{children:s.jsx(l.aH,{client:r,children:s.jsx(c.lY,{children:s.jsx(n.f,{attribute:"class",defaultTheme:"light",enableSystem:!0,disableTransitionOnChange:!0,children:e})})})})}},69436:(e,r,t)=>{"use strict";t.d(r,{C:()=>l});var s=t(95344);t(3729);var a=t(49247),n=t(91626);let i=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600",info:"border-transparent bg-blue-500 text-white hover:bg-blue-600"}},defaultVariants:{variant:"default"}});function l({className:e,variant:r,...t}){return s.jsx("div",{className:(0,n.cn)(i({variant:r}),e),...t})}},16212:(e,r,t)=>{"use strict";t.d(r,{z:()=>c});var s=t(95344),a=t(3729),n=t(32751),i=t(49247),l=t(91626);let o=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef(({className:e,variant:r,size:t,asChild:a=!1,...i},c)=>{let d=a?n.g7:"button";return s.jsx(d,{className:(0,l.cn)(o({variant:r,size:t,className:e})),ref:c,...i})});c.displayName="Button"},61351:(e,r,t)=>{"use strict";t.d(r,{Ol:()=>l,SZ:()=>c,Zb:()=>i,aY:()=>d,eW:()=>u,ll:()=>o});var s=t(95344),a=t(3729),n=t(91626);let i=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));i.displayName="Card";let l=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...r}));l.displayName="CardHeader";let o=a.forwardRef(({className:e,...r},t)=>s.jsx("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));o.displayName="CardTitle";let c=a.forwardRef(({className:e,...r},t)=>s.jsx("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",e),...r}));c.displayName="CardDescription";let d=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,n.cn)("p-6 pt-0",e),...r}));d.displayName="CardContent";let u=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",e),...r}));u.displayName="CardFooter"},1586:(e,r,t)=>{"use strict";t.d(r,{_:()=>c});var s=t(95344),a=t(3729),n=t(14217),i=t(49247),l=t(91626);let o=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=a.forwardRef(({className:e,...r},t)=>s.jsx(n.f,{ref:t,className:(0,l.cn)(o(),e),...r}));c.displayName=n.f.displayName},57341:(e,r,t)=>{"use strict";t.d(r,{Z:()=>d});var s=t(95344),a=t(3729),n=t(62409),i="horizontal",l=["horizontal","vertical"],o=a.forwardRef((e,r)=>{let{decorative:t,orientation:a=i,...o}=e,c=l.includes(a)?a:i;return(0,s.jsx)(n.WV.div,{"data-orientation":c,...t?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...o,ref:r})});o.displayName="Separator";var c=t(91626);let d=a.forwardRef(({className:e,orientation:r="horizontal",decorative:t=!0,...a},n)=>s.jsx(o,{ref:n,decorative:t,orientation:r,className:(0,c.cn)("shrink-0 bg-border","horizontal"===r?"h-[1px] w-full":"h-full w-[1px]",e),...a}));d.displayName=o.displayName},66091:(e,r,t)=>{"use strict";t.d(r,{TC:()=>o,lY:()=>l});var s=t(95344),a=t(3729);let n={appName:"SaaS Platform",logoUrl:"",faviconUrl:"",primaryColor:"#3b82f6",secondaryColor:"#64748b",accentColor:"#10b981",backgroundColor:"#ffffff",textColor:"#1f2937",theme:"light",fontFamily:"Inter, sans-serif",customCss:""},i=(0,a.createContext)(void 0);function l({children:e}){let[r,t]=(0,a.useState)(n),[l,o]=(0,a.useState)(!0);(0,a.useEffect)(()=>{c()},[]);let c=async()=>{try{let e=await fetch("/api/global-config/branding"),r=await e.json();r.success&&r.branding?(t({...n,...r.branding}),d({...n,...r.branding})):d(n)}catch(e){console.error("Error fetching branding config:",e),d(n)}finally{o(!1)}},d=e=>{let r=document.documentElement;if(r.style.setProperty("--primary-color",e.primaryColor),r.style.setProperty("--secondary-color",e.secondaryColor),r.style.setProperty("--accent-color",e.accentColor),r.style.setProperty("--background-color",e.backgroundColor),r.style.setProperty("--text-color",e.textColor),r.style.setProperty("--font-family",e.fontFamily),document.body.className=document.body.className.replace(/theme-\w+/g,""),document.body.classList.add(`theme-${e.theme}`),document.title=e.appName,e.faviconUrl){let r=document.querySelector('link[rel="icon"]');r||((r=document.createElement("link")).rel="icon",document.head.appendChild(r)),r.href=e.faviconUrl}let t=document.getElementById("custom-branding-css");e.customCss?(t||((t=document.createElement("style")).id="custom-branding-css",document.head.appendChild(t)),t.textContent=e.customCss):t&&t.remove();let s=document.querySelector('meta[name="theme-color"]');s||((s=document.createElement("meta")).name="theme-color",document.head.appendChild(s)),s.content=e.primaryColor};return s.jsx(i.Provider,{value:{branding:r,updateBranding:e=>{let s={...r,...e};t(s),d(s)},loading:l},children:e})}function o(){let e=(0,a.useContext)(i);if(void 0===e)throw Error("useBranding must be used within a BrandingProvider");return e}},91626:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n});var s=t(56815),a=t(79377);function n(...e){return(0,a.m6)((0,s.W)(e))}},63024:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(69224).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},62312:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(69224).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},82958:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(69224).Z)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},25545:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(69224).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},99046:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(69224).Z)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},23485:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(69224).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},89895:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(69224).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},22254:(e,r,t)=>{e.exports=t(14767)},59504:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p,metadata:()=>m});var s=t(25036),a=t(80265),n=t.n(a),i=t(86843);let l=(0,i.createProxy)(String.raw`C:\proj\nextjs-saas\components\providers.tsx`),{__esModule:o,$$typeof:c}=l;l.default;let d=(0,i.createProxy)(String.raw`C:\proj\nextjs-saas\components\providers.tsx#Providers`);var u=t(69636);t(67272);let m={title:{default:"Business SaaS - Complete Business Management Solution",template:"%s | Business SaaS"},description:"Complete SaaS solution for business management including CRM, invoicing, quotations, and more.",keywords:["SaaS","Business Management","CRM","Invoicing","Quotations"],authors:[{name:"Business SaaS Team"}],creator:"Business SaaS",openGraph:{type:"website",locale:"en_US",url:process.env.NEXT_PUBLIC_APP_URL,title:"Business SaaS - Complete Business Management Solution",description:"Complete SaaS solution for business management including CRM, invoicing, quotations, and more.",siteName:"Business SaaS"},twitter:{card:"summary_large_image",title:"Business SaaS - Complete Business Management Solution",description:"Complete SaaS solution for business management including CRM, invoicing, quotations, and more.",creator:"@businesssaas"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function p({children:e}){return s.jsx("html",{lang:"en",suppressHydrationWarning:!0,children:s.jsx("body",{className:n().className,children:(0,s.jsxs)(d,{children:[e,s.jsx(u.x7,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#4ade80",secondary:"#fff"}},error:{duration:4e3,iconTheme:{primary:"#ef4444",secondary:"#fff"}}}})]})})})}},55367:(e,r,t)=>{"use strict";t.r(r),t.d(r,{$$typeof:()=>n,__esModule:()=>a,default:()=>i});let s=(0,t(86843).createProxy)(String.raw`C:\proj\nextjs-saas\app\subscription\checkout\page.tsx`),{__esModule:a,$$typeof:n}=s,i=s.default},67272:()=>{},14217:(e,r,t)=>{"use strict";t.d(r,{f:()=>l});var s=t(3729),a=t(62409),n=t(95344),i=s.forwardRef((e,r)=>(0,n.jsx)(a.WV.label,{...e,ref:r,onMouseDown:r=>{r.target.closest("button, input, select, textarea")||(e.onMouseDown?.(r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));i.displayName="Label";var l=i},92062:(e,r,t)=>{"use strict";t.d(r,{D:()=>a});var s=t(3729);function a(e){let r=s.useRef({value:e,previous:e});return s.useMemo(()=>(r.current.value!==e&&(r.current.previous=r.current.value,r.current.value=e),r.current.previous),[e])}},63085:(e,r,t)=>{"use strict";t.d(r,{t:()=>n});var s=t(3729),a=t(16069);function n(e){let[r,t]=s.useState(void 0);return(0,a.b)(()=>{if(e){t({width:e.offsetWidth,height:e.offsetHeight});let r=new ResizeObserver(r=>{let s,a;if(!Array.isArray(r)||!r.length)return;let n=r[0];if("borderBoxSize"in n){let e=n.borderBoxSize,r=Array.isArray(e)?e[0]:e;s=r.inlineSize,a=r.blockSize}else s=e.offsetWidth,a=e.offsetHeight;t({width:s,height:a})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}t(void 0)},[e]),r}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[1638,7948,6671,7792,4755],()=>t(14922));module.exports=s})();