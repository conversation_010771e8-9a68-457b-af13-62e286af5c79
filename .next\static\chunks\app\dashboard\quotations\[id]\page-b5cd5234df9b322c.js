(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5822],{67176:function(e,s,t){Promise.resolve().then(t.bind(t,53011))},53011:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return D}});var a=t(57437),r=t(2265),n=t(24033),i=t(27815),l=t(85754),c=t(31478),d=t(47832),o=t(73067),x=t(56224),m=t(76020),u=t(35817),h=t(49617),f=t(45367),j=t(67972),p=t(76637),N=t(90998),y=t(9883),g=t(92919),b=t(28203),v=t(41298),w=t(5925),C=t(61396),Z=t.n(C);function D(){let e=(0,n.useParams)(),s=(0,n.useRouter)(),[t,C]=(0,r.useState)(null),[D,k]=(0,r.useState)(!0),[E,z]=(0,r.useState)(!1),R=async()=>{try{let t=await fetch("/api/quotations/".concat(e.id));if(!t.ok){if(404===t.status){w.toast.error("Quotation not found"),s.push("/dashboard/quotations");return}throw Error("Failed to fetch quotation")}let a=await t.json();C(a)}catch(e){w.toast.error("Failed to load quotation details"),console.error("Error fetching quotation:",e)}finally{k(!1)}};(0,r.useEffect)(()=>{e.id&&R()},[e.id]);let A=async()=>{if(t&&confirm('Are you sure you want to delete quotation "'.concat(t.quotationNumber,'"?')))try{let e=await fetch("/api/quotations/".concat(t.id),{method:"DELETE"});if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to delete quotation")}w.toast.success("Quotation deleted successfully"),s.push("/dashboard/quotations")}catch(e){w.toast.error(e instanceof Error?e.message:"Failed to delete quotation")}};return D?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):t?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(l.z,{variant:"ghost",size:"sm",asChild:!0,children:(0,a.jsxs)(Z(),{href:"/dashboard/quotations",children:[(0,a.jsx)(o.Z,{className:"h-4 w-4 mr-2"}),"Back to Quotations"]})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:t.quotationNumber}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(e=>{switch(e){case"DRAFT":return(0,a.jsx)(c.C,{variant:"secondary",children:"Draft"});case"SENT":return(0,a.jsx)(c.C,{variant:"info",children:"Sent"});case"VIEWED":return(0,a.jsx)(c.C,{variant:"warning",children:"Viewed"});case"ACCEPTED":return(0,a.jsx)(c.C,{variant:"success",children:"Accepted"});case"REJECTED":return(0,a.jsx)(c.C,{variant:"destructive",children:"Rejected"});case"EXPIRED":return(0,a.jsx)(c.C,{variant:"secondary",children:"Expired"});default:return(0,a.jsx)(c.C,{variant:"secondary",children:e})}})(t.status),(0,a.jsxs)("span",{className:"text-gray-500",children:["• ",t.title]})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(l.z,{variant:"outline",children:[(0,a.jsx)(x.Z,{className:"h-4 w-4 mr-2"}),"Duplicate"]}),(0,a.jsxs)(l.z,{variant:"outline",children:[(0,a.jsx)(m.Z,{className:"h-4 w-4 mr-2"}),"Send"]}),(0,a.jsxs)(l.z,{variant:"outline",children:[(0,a.jsx)(u.Z,{className:"h-4 w-4 mr-2"}),"PDF"]}),(0,a.jsxs)(l.z,{variant:"outline",onClick:()=>z(!0),children:[(0,a.jsx)(h.Z,{className:"h-4 w-4 mr-2"}),"Edit"]}),(0,a.jsxs)(l.z,{variant:"destructive",onClick:A,children:[(0,a.jsx)(f.Z,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,a.jsxs)(i.Zb,{children:[(0,a.jsx)(i.Ol,{children:(0,a.jsxs)(i.ll,{className:"flex items-center",children:[(0,a.jsx)(j.Z,{className:"h-5 w-5 mr-2"}),"Customer Information"]})}),(0,a.jsxs)(i.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Customer Name"}),(0,a.jsx)("p",{className:"font-medium",children:t.customer.name})]}),t.customer.company&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Company"}),(0,a.jsx)("p",{className:"font-medium",children:t.customer.company})]}),t.customer.email&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Email"}),(0,a.jsx)("p",{className:"font-medium",children:t.customer.email})]}),t.customer.phone&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Phone"}),(0,a.jsx)("p",{className:"font-medium",children:t.customer.phone})]})]}),(t.customer.address||t.customer.city)&&(0,a.jsxs)("div",{className:"pt-4 border-t",children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 mb-2",children:"Address"}),(0,a.jsxs)("div",{className:"text-gray-900",children:[t.customer.address&&(0,a.jsx)("p",{children:t.customer.address}),(0,a.jsx)("p",{children:[t.customer.city,t.customer.state,t.customer.postalCode].filter(Boolean).join(", ")}),t.customer.country&&(0,a.jsx)("p",{children:t.customer.country})]})]})]})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsx)(i.Ol,{children:(0,a.jsxs)(i.ll,{className:"flex items-center",children:[(0,a.jsx)(p.Z,{className:"h-5 w-5 mr-2"}),"Items"]})}),(0,a.jsx)(i.aY,{children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b",children:[(0,a.jsx)("th",{className:"text-left py-2",children:"Description"}),(0,a.jsx)("th",{className:"text-right py-2",children:"Qty"}),(0,a.jsx)("th",{className:"text-right py-2",children:"Unit Price"}),(0,a.jsx)("th",{className:"text-right py-2",children:"Discount"}),(0,a.jsx)("th",{className:"text-right py-2",children:"Tax"}),(0,a.jsx)("th",{className:"text-right py-2",children:"Total"})]})}),(0,a.jsx)("tbody",{children:t.items&&Array.isArray(t.items)?t.items.map((e,s)=>{let t=e.quantity*e.unitPrice,r=t*e.discount/100,n=t-r,i=n*e.taxRate/100;return(0,a.jsxs)("tr",{className:"border-b",children:[(0,a.jsx)("td",{className:"py-3",children:e.description}),(0,a.jsx)("td",{className:"text-right py-3",children:e.quantity}),(0,a.jsxs)("td",{className:"text-right py-3",children:["$",e.unitPrice.toFixed(2)]}),(0,a.jsxs)("td",{className:"text-right py-3",children:[e.discount,"%"]}),(0,a.jsxs)("td",{className:"text-right py-3",children:[e.taxRate,"%"]}),(0,a.jsxs)("td",{className:"text-right py-3 font-medium",children:["$",(n+i).toFixed(2)]})]},s)}):(0,a.jsx)("tr",{children:(0,a.jsx)("td",{colSpan:6,className:"text-center py-4 text-gray-500",children:"No items found"})})})]})})})]}),(t.terms||t.notes)&&(0,a.jsxs)(i.Zb,{children:[(0,a.jsx)(i.Ol,{children:(0,a.jsx)(i.ll,{children:"Additional Information"})}),(0,a.jsxs)(i.aY,{className:"space-y-4",children:[t.terms&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 mb-2",children:"Terms & Conditions"}),(0,a.jsx)("p",{className:"text-gray-900 whitespace-pre-wrap",children:t.terms})]}),t.notes&&(0,a.jsxs)("div",{className:"pt-4 border-t",children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 mb-2",children:"Internal Notes"}),(0,a.jsx)("p",{className:"text-gray-900 whitespace-pre-wrap",children:t.notes})]})]})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsx)(i.Ol,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(i.ll,{className:"flex items-center",children:[(0,a.jsx)(N.Z,{className:"h-5 w-5 mr-2"}),"Activity Timeline"]}),(0,a.jsxs)(l.z,{variant:"outline",size:"sm",children:[(0,a.jsx)(y.Z,{className:"h-4 w-4 mr-2"}),"Add Note"]})]})}),(0,a.jsx)(i.aY,{children:t.activities&&Array.isArray(t.activities)&&t.activities.length>0?(0,a.jsx)("div",{className:"space-y-4",children:t.activities.map(e=>(0,a.jsxs)("div",{className:"flex items-start space-x-3 pb-4 border-b last:border-b-0",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(N.Z,{className:"h-4 w-4 text-blue-600"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"font-medium",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:e.description}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[new Date(e.createdAt).toLocaleDateString()," by ",e.createdBy.name]})]})]},e.id))}):(0,a.jsx)("p",{className:"text-gray-500 text-center py-4",children:"No activity recorded yet"})})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(i.Zb,{children:[(0,a.jsx)(i.Ol,{children:(0,a.jsxs)(i.ll,{className:"flex items-center",children:[(0,a.jsx)(g.Z,{className:"h-5 w-5 mr-2"}),"Summary"]})}),(0,a.jsx)(i.aY,{className:"space-y-4",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Subtotal:"}),(0,a.jsxs)("span",{children:["$",t.subtotal.toFixed(2)]})]}),t.discountAmount>0&&(0,a.jsxs)("div",{className:"flex justify-between text-red-600",children:[(0,a.jsx)("span",{children:"Discount:"}),(0,a.jsxs)("span",{children:["-$",t.discountAmount.toFixed(2)]})]}),t.taxAmount>0&&(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Tax:"}),(0,a.jsxs)("span",{children:["$",t.taxAmount.toFixed(2)]})]}),(0,a.jsxs)("div",{className:"flex justify-between font-bold text-lg border-t pt-2",children:[(0,a.jsx)("span",{children:"Total:"}),(0,a.jsxs)("span",{children:["$",t.total.toFixed(2)]})]})]})})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsx)(i.Ol,{children:(0,a.jsx)(i.ll,{children:"Details"})}),(0,a.jsxs)(i.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(b.Z,{className:"h-4 w-4 text-gray-400"}),(0,a.jsx)("span",{className:"text-sm",children:"Created"})]}),(0,a.jsx)("span",{className:"font-medium text-sm",children:new Date(t.createdAt).toLocaleDateString()})]}),t.validUntil&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(b.Z,{className:"h-4 w-4 text-gray-400"}),(0,a.jsx)("span",{className:"text-sm",children:"Valid Until"})]}),(0,a.jsx)("span",{className:"font-medium text-sm",children:new Date(t.validUntil).toLocaleDateString()})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(j.Z,{className:"h-4 w-4 text-gray-400"}),(0,a.jsx)("span",{className:"text-sm",children:"Created By"})]}),(0,a.jsx)("span",{className:"font-medium text-sm",children:t.createdBy.name||"Unknown"})]}),t.lead&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(p.Z,{className:"h-4 w-4 text-gray-400"}),(0,a.jsx)("span",{className:"text-sm",children:"Related Lead"})]}),(0,a.jsx)(Z(),{href:"/dashboard/leads/".concat(t.lead.id),className:"font-medium text-sm text-blue-600 hover:underline",children:t.lead.name})]})]})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsx)(i.Ol,{children:(0,a.jsx)(i.ll,{children:"Quick Actions"})}),(0,a.jsxs)(i.aY,{className:"space-y-2",children:[(0,a.jsxs)(l.z,{variant:"outline",className:"w-full justify-start",children:[(0,a.jsx)(m.Z,{className:"h-4 w-4 mr-2"}),"Send to Customer"]}),(0,a.jsxs)(l.z,{variant:"outline",className:"w-full justify-start",children:[(0,a.jsx)(u.Z,{className:"h-4 w-4 mr-2"}),"Download PDF"]}),(0,a.jsx)(l.z,{variant:"outline",className:"w-full justify-start",asChild:!0,children:(0,a.jsxs)(Z(),{href:"/dashboard/invoices/new?quotationId=".concat(t.id),children:[(0,a.jsx)(v.Z,{className:"h-4 w-4 mr-2"}),"Convert to Invoice"]})}),(0,a.jsxs)(l.z,{variant:"outline",className:"w-full justify-start",children:[(0,a.jsx)(x.Z,{className:"h-4 w-4 mr-2"}),"Duplicate Quotation"]})]})]})]})]}),(0,a.jsx)(d.d,{isOpen:E,onClose:()=>z(!1),onSuccess:R,quotation:t,mode:"edit"})]}):(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("p",{className:"text-gray-500",children:"Quotation not found"}),(0,a.jsx)(l.z,{asChild:!0,className:"mt-4",children:(0,a.jsx)(Z(),{href:"/dashboard/quotations",children:"Back to Quotations"})})]})}},31478:function(e,s,t){"use strict";t.d(s,{C:function(){return l}});var a=t(57437);t(2265);var r=t(96061),n=t(1657);let i=(0,r.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600",info:"border-transparent bg-blue-500 text-white hover:bg-blue-600"}},defaultVariants:{variant:"default"}});function l(e){let{className:s,variant:t,...r}=e;return(0,a.jsx)("div",{className:(0,n.cn)(i({variant:t}),s),...r})}},85754:function(e,s,t){"use strict";t.d(s,{z:function(){return d}});var a=t(57437),r=t(2265),n=t(67256),i=t(96061),l=t(1657);let c=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=r.forwardRef((e,s)=>{let{className:t,variant:r,size:i,asChild:d=!1,...o}=e,x=d?n.g7:"button";return(0,a.jsx)(x,{className:(0,l.cn)(c({variant:r,size:i,className:t})),ref:s,...o})});d.displayName="Button"},27815:function(e,s,t){"use strict";t.d(s,{Ol:function(){return l},SZ:function(){return d},Zb:function(){return i},aY:function(){return o},eW:function(){return x},ll:function(){return c}});var a=t(57437),r=t(2265),n=t(1657);let i=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...r})});i.displayName="Card";let l=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",t),...r})});l.displayName="CardHeader";let c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("h3",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",t),...r})});c.displayName="CardTitle";let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",t),...r})});d.displayName="CardDescription";let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("p-6 pt-0",t),...r})});o.displayName="CardContent";let x=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",t),...r})});x.displayName="CardFooter"},42706:function(e,s,t){"use strict";t.d(s,{$N:function(){return f},Be:function(){return j},Vq:function(){return c},cN:function(){return h},cZ:function(){return m},fK:function(){return u},hg:function(){return d},t9:function(){return x}});var a=t(57437),r=t(2265),n=t(28712),i=t(82549),l=t(1657);let c=n.fC,d=n.xz,o=n.h_;n.x8;let x=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(n.aV,{ref:s,className:(0,l.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...r})});x.displayName=n.aV.displayName;let m=r.forwardRef((e,s)=>{let{className:t,children:r,...c}=e;return(0,a.jsxs)(o,{children:[(0,a.jsx)(x,{}),(0,a.jsxs)(n.VY,{ref:s,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",t),...c,children:[r,(0,a.jsxs)(n.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(i.Z,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});m.displayName=n.VY.displayName;let u=e=>{let{className:s,...t}=e;return(0,a.jsx)("div",{className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",s),...t})};u.displayName="DialogHeader";let h=e=>{let{className:s,...t}=e;return(0,a.jsx)("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",s),...t})};h.displayName="DialogFooter";let f=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(n.Dx,{ref:s,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",t),...r})});f.displayName=n.Dx.displayName;let j=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(n.dk,{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",t),...r})});j.displayName=n.dk.displayName},45179:function(e,s,t){"use strict";t.d(s,{I:function(){return i}});var a=t(57437),r=t(2265),n=t(1657);let i=r.forwardRef((e,s)=>{let{className:t,type:r,...i}=e;return(0,a.jsx)("input",{type:r,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...i})});i.displayName="Input"},49842:function(e,s,t){"use strict";t.d(s,{_:function(){return d}});var a=t(57437),r=t(2265),n=t(36743),i=t(96061),l=t(1657);let c=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(n.f,{ref:s,className:(0,l.cn)(c(),t),...r})});d.displayName=n.f.displayName},1657:function(e,s,t){"use strict";t.d(s,{cn:function(){return n}});var a=t(57042),r=t(74769);function n(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,r.m6)((0,a.W)(s))}}},function(e){e.O(0,[6723,1706,1396,2881,2634,7832,2971,4938,1744],function(){return e(e.s=67176)}),_N_E=e.O()}]);