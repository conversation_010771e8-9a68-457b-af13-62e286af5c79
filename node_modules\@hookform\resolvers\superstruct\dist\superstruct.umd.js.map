{"version": 3, "file": "superstruct.umd.js", "sources": ["../src/superstruct.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport { FieldError, FieldValues, Resolver } from 'react-hook-form';\nimport { Infer, Struct, StructError, validate } from 'superstruct';\n\nfunction parseErrorSchema(error: StructError) {\n  return error.failures().reduce<Record<string, FieldError>>(\n    (previous, error) =>\n      (previous[error.path.join('.')] = {\n        message: error.message,\n        type: error.type,\n      }) && previous,\n    {},\n  );\n}\n\nexport function superstructResolver<Input extends FieldValues, Context, Output>(\n  schema: Struct<Input, any>,\n  schemaOptions?: Parameters<typeof validate>[2],\n  resolverOptions?: {\n    raw?: false;\n  },\n): Resolver<Input, Context, Infer<typeof schema>>;\n\nexport function superstructResolver<Input extends FieldValues, Context, Output>(\n  schema: Struct<Input, any>,\n  schemaOptions: Parameters<typeof validate>[2] | undefined,\n  resolverOptions: {\n    raw: true;\n  },\n): Resolver<Input, Context, Input>;\n\n/**\n * Creates a resolver for react-hook-form using Superstruct schema validation\n * @param {Struct<TFieldValues, any>} schema - The Superstruct schema to validate against\n * @param {Parameters<typeof validate>[2]} [schemaOptions] - Optional Superstruct validation options\n * @param {Object} resolverOptions - Additional resolver configuration\n * @param {boolean} [resolverOptions.raw=false] - If true, returns raw values rather than validated results\n * @returns {Resolver<Infer<typeof schema>>} A resolver function compatible with react-hook-form\n * @example\n * const schema = struct({\n *   name: string(),\n *   age: number()\n * });\n *\n * useForm({\n *   resolver: superstructResolver(schema)\n * });\n */\nexport function superstructResolver<Input extends FieldValues, Context, Output>(\n  schema: Struct<Input, any>,\n  schemaOptions?: Parameters<typeof validate>[2],\n  resolverOptions: {\n    raw?: boolean;\n  } = {},\n): Resolver<Input, Context, Input | Output> {\n  return (values: Input, _, options) => {\n    const result = validate(values, schema, schemaOptions);\n\n    if (result[0]) {\n      return {\n        values: {},\n        errors: toNestErrors(parseErrorSchema(result[0]), options),\n      };\n    }\n\n    options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n    return {\n      values: resolverOptions.raw ? Object.assign({}, values) : result[1],\n      errors: {},\n    };\n  };\n}\n"], "names": ["schema", "schemaOptions", "resolverOptions", "values", "_", "options", "error", "result", "validate", "errors", "toNestErrors", "failures", "reduce", "previous", "path", "join", "message", "type", "shouldUseNativeValidation", "validateFieldsNatively", "raw", "Object", "assign"], "mappings": "qZAiDEA,EACAC,EACAC,GAIA,gBAJAA,IAAAA,EAEI,CAAE,GAEEC,SAAAA,EAAeC,EAAGC,GACxB,IApDsBC,EAoDhBC,EAASC,EAAAA,SAASL,EAAQH,EAAQC,GAExC,OAAIM,EAAO,GACF,CACLJ,OAAQ,CAAA,EACRM,OAAQC,EAAYA,cAzDFJ,EAyDoBC,EAAO,GAxD5CD,EAAMK,WAAWC,OACtB,SAACC,EAAUP,GACT,OAACO,EAASP,EAAMQ,KAAKC,KAAK,MAAQ,CAChCC,QAASV,EAAMU,QACfC,KAAMX,EAAMW,QACRJ,CAAQ,EAChB,CAAA,IAkDsDR,KAItDA,EAAQa,2BAA6BC,yBAAuB,CAAA,EAAId,GAEzD,CACLF,OAAQD,EAAgBkB,IAAMC,OAAOC,OAAO,CAAE,EAAEnB,GAAUI,EAAO,GACjEE,OAAQ,CAAA,GAEZ,CACF"}