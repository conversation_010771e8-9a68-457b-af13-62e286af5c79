(()=>{var e={};e.id=5822,e.ids=[5822],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},1502:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>d});var a=t(50482),r=t(69108),n=t(62563),i=t.n(n),l=t(68300),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let d=["",{children:["dashboard",{children:["quotations",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,36985)),"C:\\proj\\nextjs-saas\\app\\dashboard\\quotations\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,96339)),"C:\\proj\\nextjs-saas\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,59504)),"C:\\proj\\nextjs-saas\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\proj\\nextjs-saas\\app\\dashboard\\quotations\\[id]\\page.tsx"],x="/dashboard/quotations/[id]/page",u={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/quotations/[id]/page",pathname:"/dashboard/quotations/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},78168:(e,s,t)=>{Promise.resolve().then(t.bind(t,11202))},11202:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>Z});var a=t(95344),r=t(3729),n=t(22254),i=t(61351),l=t(16212),o=t(69436),d=t(7004),c=t(63024),x=t(1960),u=t(36135),m=t(96885),h=t(75695),p=t(38271),f=t(18822),j=t(37121),y=t(88534),g=t(51838),N=t(55329),v=t(55794),b=t(48411),w=t(44669),D=t(20783),C=t.n(D);function Z(){let e=(0,n.useParams)(),s=(0,n.useRouter)(),[t,D]=(0,r.useState)(null),[Z,k]=(0,r.useState)(!0),[q,R]=(0,r.useState)(!1),E=async()=>{try{let t=await fetch(`/api/quotations/${e.id}`);if(!t.ok){if(404===t.status){w.toast.error("Quotation not found"),s.push("/dashboard/quotations");return}throw Error("Failed to fetch quotation")}let a=await t.json();D(a)}catch(e){w.toast.error("Failed to load quotation details"),console.error("Error fetching quotation:",e)}finally{k(!1)}};(0,r.useEffect)(()=>{e.id&&E()},[e.id]);let P=async()=>{if(t&&confirm(`Are you sure you want to delete quotation "${t.quotationNumber}"?`))try{let e=await fetch(`/api/quotations/${t.id}`,{method:"DELETE"});if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to delete quotation")}w.toast.success("Quotation deleted successfully"),s.push("/dashboard/quotations")}catch(e){w.toast.error(e instanceof Error?e.message:"Failed to delete quotation")}};return Z?a.jsx("div",{className:"flex items-center justify-center py-8",children:a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):t?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx(l.z,{variant:"ghost",size:"sm",asChild:!0,children:(0,a.jsxs)(C(),{href:"/dashboard/quotations",children:[a.jsx(c.Z,{className:"h-4 w-4 mr-2"}),"Back to Quotations"]})}),(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:t.quotationNumber}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(e=>{switch(e){case"DRAFT":return a.jsx(o.C,{variant:"secondary",children:"Draft"});case"SENT":return a.jsx(o.C,{variant:"info",children:"Sent"});case"VIEWED":return a.jsx(o.C,{variant:"warning",children:"Viewed"});case"ACCEPTED":return a.jsx(o.C,{variant:"success",children:"Accepted"});case"REJECTED":return a.jsx(o.C,{variant:"destructive",children:"Rejected"});case"EXPIRED":return a.jsx(o.C,{variant:"secondary",children:"Expired"});default:return a.jsx(o.C,{variant:"secondary",children:e})}})(t.status),(0,a.jsxs)("span",{className:"text-gray-500",children:["• ",t.title]})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(l.z,{variant:"outline",children:[a.jsx(x.Z,{className:"h-4 w-4 mr-2"}),"Duplicate"]}),(0,a.jsxs)(l.z,{variant:"outline",children:[a.jsx(u.Z,{className:"h-4 w-4 mr-2"}),"Send"]}),(0,a.jsxs)(l.z,{variant:"outline",children:[a.jsx(m.Z,{className:"h-4 w-4 mr-2"}),"PDF"]}),(0,a.jsxs)(l.z,{variant:"outline",onClick:()=>R(!0),children:[a.jsx(h.Z,{className:"h-4 w-4 mr-2"}),"Edit"]}),(0,a.jsxs)(l.z,{variant:"destructive",onClick:P,children:[a.jsx(p.Z,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,a.jsxs)(i.Zb,{children:[a.jsx(i.Ol,{children:(0,a.jsxs)(i.ll,{className:"flex items-center",children:[a.jsx(f.Z,{className:"h-5 w-5 mr-2"}),"Customer Information"]})}),(0,a.jsxs)(i.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Customer Name"}),a.jsx("p",{className:"font-medium",children:t.customer.name})]}),t.customer.company&&(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Company"}),a.jsx("p",{className:"font-medium",children:t.customer.company})]}),t.customer.email&&(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Email"}),a.jsx("p",{className:"font-medium",children:t.customer.email})]}),t.customer.phone&&(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Phone"}),a.jsx("p",{className:"font-medium",children:t.customer.phone})]})]}),(t.customer.address||t.customer.city)&&(0,a.jsxs)("div",{className:"pt-4 border-t",children:[a.jsx("p",{className:"text-sm text-gray-500 mb-2",children:"Address"}),(0,a.jsxs)("div",{className:"text-gray-900",children:[t.customer.address&&a.jsx("p",{children:t.customer.address}),a.jsx("p",{children:[t.customer.city,t.customer.state,t.customer.postalCode].filter(Boolean).join(", ")}),t.customer.country&&a.jsx("p",{children:t.customer.country})]})]})]})]}),(0,a.jsxs)(i.Zb,{children:[a.jsx(i.Ol,{children:(0,a.jsxs)(i.ll,{className:"flex items-center",children:[a.jsx(j.Z,{className:"h-5 w-5 mr-2"}),"Items"]})}),a.jsx(i.aY,{children:a.jsx("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[a.jsx("thead",{children:(0,a.jsxs)("tr",{className:"border-b",children:[a.jsx("th",{className:"text-left py-2",children:"Description"}),a.jsx("th",{className:"text-right py-2",children:"Qty"}),a.jsx("th",{className:"text-right py-2",children:"Unit Price"}),a.jsx("th",{className:"text-right py-2",children:"Discount"}),a.jsx("th",{className:"text-right py-2",children:"Tax"}),a.jsx("th",{className:"text-right py-2",children:"Total"})]})}),a.jsx("tbody",{children:t.items&&Array.isArray(t.items)?t.items.map((e,s)=>{let t=e.quantity*e.unitPrice,r=t*e.discount/100,n=t-r,i=n*e.taxRate/100;return(0,a.jsxs)("tr",{className:"border-b",children:[a.jsx("td",{className:"py-3",children:e.description}),a.jsx("td",{className:"text-right py-3",children:e.quantity}),(0,a.jsxs)("td",{className:"text-right py-3",children:["$",e.unitPrice.toFixed(2)]}),(0,a.jsxs)("td",{className:"text-right py-3",children:[e.discount,"%"]}),(0,a.jsxs)("td",{className:"text-right py-3",children:[e.taxRate,"%"]}),(0,a.jsxs)("td",{className:"text-right py-3 font-medium",children:["$",(n+i).toFixed(2)]})]},s)}):a.jsx("tr",{children:a.jsx("td",{colSpan:6,className:"text-center py-4 text-gray-500",children:"No items found"})})})]})})})]}),(t.terms||t.notes)&&(0,a.jsxs)(i.Zb,{children:[a.jsx(i.Ol,{children:a.jsx(i.ll,{children:"Additional Information"})}),(0,a.jsxs)(i.aY,{className:"space-y-4",children:[t.terms&&(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500 mb-2",children:"Terms & Conditions"}),a.jsx("p",{className:"text-gray-900 whitespace-pre-wrap",children:t.terms})]}),t.notes&&(0,a.jsxs)("div",{className:"pt-4 border-t",children:[a.jsx("p",{className:"text-sm text-gray-500 mb-2",children:"Internal Notes"}),a.jsx("p",{className:"text-gray-900 whitespace-pre-wrap",children:t.notes})]})]})]}),(0,a.jsxs)(i.Zb,{children:[a.jsx(i.Ol,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(i.ll,{className:"flex items-center",children:[a.jsx(y.Z,{className:"h-5 w-5 mr-2"}),"Activity Timeline"]}),(0,a.jsxs)(l.z,{variant:"outline",size:"sm",children:[a.jsx(g.Z,{className:"h-4 w-4 mr-2"}),"Add Note"]})]})}),a.jsx(i.aY,{children:t.activities&&Array.isArray(t.activities)&&t.activities.length>0?a.jsx("div",{className:"space-y-4",children:t.activities.map(e=>(0,a.jsxs)("div",{className:"flex items-start space-x-3 pb-4 border-b last:border-b-0",children:[a.jsx("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:a.jsx(y.Z,{className:"h-4 w-4 text-blue-600"})}),(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("p",{className:"font-medium",children:e.title}),a.jsx("p",{className:"text-sm text-gray-600",children:e.description}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[new Date(e.createdAt).toLocaleDateString()," by ",e.createdBy.name]})]})]},e.id))}):a.jsx("p",{className:"text-gray-500 text-center py-4",children:"No activity recorded yet"})})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(i.Zb,{children:[a.jsx(i.Ol,{children:(0,a.jsxs)(i.ll,{className:"flex items-center",children:[a.jsx(N.Z,{className:"h-5 w-5 mr-2"}),"Summary"]})}),a.jsx(i.aY,{className:"space-y-4",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{children:"Subtotal:"}),(0,a.jsxs)("span",{children:["$",t.subtotal.toFixed(2)]})]}),t.discountAmount>0&&(0,a.jsxs)("div",{className:"flex justify-between text-red-600",children:[a.jsx("span",{children:"Discount:"}),(0,a.jsxs)("span",{children:["-$",t.discountAmount.toFixed(2)]})]}),t.taxAmount>0&&(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{children:"Tax:"}),(0,a.jsxs)("span",{children:["$",t.taxAmount.toFixed(2)]})]}),(0,a.jsxs)("div",{className:"flex justify-between font-bold text-lg border-t pt-2",children:[a.jsx("span",{children:"Total:"}),(0,a.jsxs)("span",{children:["$",t.total.toFixed(2)]})]})]})})]}),(0,a.jsxs)(i.Zb,{children:[a.jsx(i.Ol,{children:a.jsx(i.ll,{children:"Details"})}),(0,a.jsxs)(i.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(v.Z,{className:"h-4 w-4 text-gray-400"}),a.jsx("span",{className:"text-sm",children:"Created"})]}),a.jsx("span",{className:"font-medium text-sm",children:new Date(t.createdAt).toLocaleDateString()})]}),t.validUntil&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(v.Z,{className:"h-4 w-4 text-gray-400"}),a.jsx("span",{className:"text-sm",children:"Valid Until"})]}),a.jsx("span",{className:"font-medium text-sm",children:new Date(t.validUntil).toLocaleDateString()})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(f.Z,{className:"h-4 w-4 text-gray-400"}),a.jsx("span",{className:"text-sm",children:"Created By"})]}),a.jsx("span",{className:"font-medium text-sm",children:t.createdBy.name||"Unknown"})]}),t.lead&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(j.Z,{className:"h-4 w-4 text-gray-400"}),a.jsx("span",{className:"text-sm",children:"Related Lead"})]}),a.jsx(C(),{href:`/dashboard/leads/${t.lead.id}`,className:"font-medium text-sm text-blue-600 hover:underline",children:t.lead.name})]})]})]}),(0,a.jsxs)(i.Zb,{children:[a.jsx(i.Ol,{children:a.jsx(i.ll,{children:"Quick Actions"})}),(0,a.jsxs)(i.aY,{className:"space-y-2",children:[(0,a.jsxs)(l.z,{variant:"outline",className:"w-full justify-start",children:[a.jsx(u.Z,{className:"h-4 w-4 mr-2"}),"Send to Customer"]}),(0,a.jsxs)(l.z,{variant:"outline",className:"w-full justify-start",children:[a.jsx(m.Z,{className:"h-4 w-4 mr-2"}),"Download PDF"]}),a.jsx(l.z,{variant:"outline",className:"w-full justify-start",asChild:!0,children:(0,a.jsxs)(C(),{href:`/dashboard/invoices/new?quotationId=${t.id}`,children:[a.jsx(b.Z,{className:"h-4 w-4 mr-2"}),"Convert to Invoice"]})}),(0,a.jsxs)(l.z,{variant:"outline",className:"w-full justify-start",children:[a.jsx(x.Z,{className:"h-4 w-4 mr-2"}),"Duplicate Quotation"]})]})]})]})]}),a.jsx(d.d,{isOpen:q,onClose:()=>R(!1),onSuccess:E,quotation:t,mode:"edit"})]}):(0,a.jsxs)("div",{className:"text-center py-8",children:[a.jsx("p",{className:"text-gray-500",children:"Quotation not found"}),a.jsx(l.z,{asChild:!0,className:"mt-4",children:a.jsx(C(),{href:"/dashboard/quotations",children:"Back to Quotations"})})]})}},16802:(e,s,t)=>{"use strict";t.d(s,{$N:()=>p,Be:()=>f,Vq:()=>o,cN:()=>h,cZ:()=>u,fK:()=>m,hg:()=>d,t9:()=>x});var a=t(95344),r=t(3729),n=t(88794),i=t(14513),l=t(91626);let o=n.fC,d=n.xz,c=n.h_;n.x8;let x=r.forwardRef(({className:e,...s},t)=>a.jsx(n.aV,{ref:t,className:(0,l.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...s}));x.displayName=n.aV.displayName;let u=r.forwardRef(({className:e,children:s,...t},r)=>(0,a.jsxs)(c,{children:[a.jsx(x,{}),(0,a.jsxs)(n.VY,{ref:r,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...t,children:[s,(0,a.jsxs)(n.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[a.jsx(i.Z,{className:"h-4 w-4"}),a.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));u.displayName=n.VY.displayName;let m=({className:e,...s})=>a.jsx("div",{className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...s});m.displayName="DialogHeader";let h=({className:e,...s})=>a.jsx("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...s});h.displayName="DialogFooter";let p=r.forwardRef(({className:e,...s},t)=>a.jsx(n.Dx,{ref:t,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",e),...s}));p.displayName=n.Dx.displayName;let f=r.forwardRef(({className:e,...s},t)=>a.jsx(n.dk,{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",e),...s}));f.displayName=n.dk.displayName},92549:(e,s,t)=>{"use strict";t.d(s,{I:()=>i});var a=t(95344),r=t(3729),n=t(91626);let i=r.forwardRef(({className:e,type:s,...t},r)=>a.jsx("input",{type:s,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...t}));i.displayName="Input"},1586:(e,s,t)=>{"use strict";t.d(s,{_:()=>d});var a=t(95344),r=t(3729),n=t(14217),i=t(49247),l=t(91626);let o=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef(({className:e,...s},t)=>a.jsx(n.f,{ref:t,className:(0,l.cn)(o(),e),...s}));d.displayName=n.f.displayName},88534:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]])},63024:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},75695:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},51838:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},38271:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},36985:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>n,__esModule:()=>r,default:()=>i});let a=(0,t(86843).createProxy)(String.raw`C:\proj\nextjs-saas\app\dashboard\quotations\[id]\page.tsx`),{__esModule:r,$$typeof:n}=a,i=a.default},88794:(e,s,t)=>{"use strict";t.d(s,{Dx:()=>ea,VY:()=>et,aV:()=>es,dk:()=>er,fC:()=>K,h_:()=>ee,x8:()=>en,xz:()=>J});var a=t(3729),r=t(85222),n=t(31405),i=t(98462),l=t(99048),o=t(33183),d=t(44155),c=t(27386),x=t(31179),u=t(43234),m=t(62409),h=t(1106),p=t(71210),f=t(45904),j=t(32751),y=t(95344),g="Dialog",[N,v]=(0,i.b)(g),[b,w]=N(g),D=e=>{let{__scopeDialog:s,children:t,open:r,defaultOpen:n,onOpenChange:i,modal:d=!0}=e,c=a.useRef(null),x=a.useRef(null),[u,m]=(0,o.T)({prop:r,defaultProp:n??!1,onChange:i,caller:g});return(0,y.jsx)(b,{scope:s,triggerRef:c,contentRef:x,contentId:(0,l.M)(),titleId:(0,l.M)(),descriptionId:(0,l.M)(),open:u,onOpenChange:m,onOpenToggle:a.useCallback(()=>m(e=>!e),[m]),modal:d,children:t})};D.displayName=g;var C="DialogTrigger",Z=a.forwardRef((e,s)=>{let{__scopeDialog:t,...a}=e,i=w(C,t),l=(0,n.e)(s,i.triggerRef);return(0,y.jsx)(m.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":L(i.open),...a,ref:l,onClick:(0,r.M)(e.onClick,i.onOpenToggle)})});Z.displayName=C;var k="DialogPortal",[q,R]=N(k,{forceMount:void 0}),E=e=>{let{__scopeDialog:s,forceMount:t,children:r,container:n}=e,i=w(k,s);return(0,y.jsx)(q,{scope:s,forceMount:t,children:a.Children.map(r,e=>(0,y.jsx)(u.z,{present:t||i.open,children:(0,y.jsx)(x.h,{asChild:!0,container:n,children:e})}))})};E.displayName=k;var P="DialogOverlay",A=a.forwardRef((e,s)=>{let t=R(P,e.__scopeDialog),{forceMount:a=t.forceMount,...r}=e,n=w(P,e.__scopeDialog);return n.modal?(0,y.jsx)(u.z,{present:a||n.open,children:(0,y.jsx)(I,{...r,ref:s})}):null});A.displayName=P;var _=(0,j.Z8)("DialogOverlay.RemoveScroll"),I=a.forwardRef((e,s)=>{let{__scopeDialog:t,...a}=e,r=w(P,t);return(0,y.jsx)(p.Z,{as:_,allowPinchZoom:!0,shards:[r.contentRef],children:(0,y.jsx)(m.WV.div,{"data-state":L(r.open),...a,ref:s,style:{pointerEvents:"auto",...a.style}})})}),F="DialogContent",M=a.forwardRef((e,s)=>{let t=R(F,e.__scopeDialog),{forceMount:a=t.forceMount,...r}=e,n=w(F,e.__scopeDialog);return(0,y.jsx)(u.z,{present:a||n.open,children:n.modal?(0,y.jsx)(z,{...r,ref:s}):(0,y.jsx)(O,{...r,ref:s})})});M.displayName=F;var z=a.forwardRef((e,s)=>{let t=w(F,e.__scopeDialog),i=a.useRef(null),l=(0,n.e)(s,t.contentRef,i);return a.useEffect(()=>{let e=i.current;if(e)return(0,f.Ry)(e)},[]),(0,y.jsx)(S,{...e,ref:l,trapFocus:t.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,r.M)(e.onCloseAutoFocus,e=>{e.preventDefault(),t.triggerRef.current?.focus()}),onPointerDownOutside:(0,r.M)(e.onPointerDownOutside,e=>{let s=e.detail.originalEvent,t=0===s.button&&!0===s.ctrlKey;(2===s.button||t)&&e.preventDefault()}),onFocusOutside:(0,r.M)(e.onFocusOutside,e=>e.preventDefault())})}),O=a.forwardRef((e,s)=>{let t=w(F,e.__scopeDialog),r=a.useRef(!1),n=a.useRef(!1);return(0,y.jsx)(S,{...e,ref:s,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:s=>{e.onCloseAutoFocus?.(s),s.defaultPrevented||(r.current||t.triggerRef.current?.focus(),s.preventDefault()),r.current=!1,n.current=!1},onInteractOutside:s=>{e.onInteractOutside?.(s),s.defaultPrevented||(r.current=!0,"pointerdown"!==s.detail.originalEvent.type||(n.current=!0));let a=s.target;t.triggerRef.current?.contains(a)&&s.preventDefault(),"focusin"===s.detail.originalEvent.type&&n.current&&s.preventDefault()}})}),S=a.forwardRef((e,s)=>{let{__scopeDialog:t,trapFocus:r,onOpenAutoFocus:i,onCloseAutoFocus:l,...o}=e,x=w(F,t),u=a.useRef(null),m=(0,n.e)(s,u);return(0,h.EW)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(c.M,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:i,onUnmountAutoFocus:l,children:(0,y.jsx)(d.XB,{role:"dialog",id:x.contentId,"aria-describedby":x.descriptionId,"aria-labelledby":x.titleId,"data-state":L(x.open),...o,ref:m,onDismiss:()=>x.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(G,{titleId:x.titleId}),(0,y.jsx)(X,{contentRef:u,descriptionId:x.descriptionId})]})]})}),T="DialogTitle",$=a.forwardRef((e,s)=>{let{__scopeDialog:t,...a}=e,r=w(T,t);return(0,y.jsx)(m.WV.h2,{id:r.titleId,...a,ref:s})});$.displayName=T;var V="DialogDescription",B=a.forwardRef((e,s)=>{let{__scopeDialog:t,...a}=e,r=w(V,t);return(0,y.jsx)(m.WV.p,{id:r.descriptionId,...a,ref:s})});B.displayName=V;var W="DialogClose",Y=a.forwardRef((e,s)=>{let{__scopeDialog:t,...a}=e,n=w(W,t);return(0,y.jsx)(m.WV.button,{type:"button",...a,ref:s,onClick:(0,r.M)(e.onClick,()=>n.onOpenChange(!1))})});function L(e){return e?"open":"closed"}Y.displayName=W;var Q="DialogTitleWarning",[H,U]=(0,i.k)(Q,{contentName:F,titleName:T,docsSlug:"dialog"}),G=({titleId:e})=>{let s=U(Q),t=`\`${s.contentName}\` requires a \`${s.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${s.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${s.docsSlug}`;return a.useEffect(()=>{e&&!document.getElementById(e)&&console.error(t)},[t,e]),null},X=({contentRef:e,descriptionId:s})=>{let t=U("DialogDescriptionWarning"),r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${t.contentName}}.`;return a.useEffect(()=>{let t=e.current?.getAttribute("aria-describedby");s&&t&&!document.getElementById(s)&&console.warn(r)},[r,e,s]),null},K=D,J=Z,ee=E,es=A,et=M,ea=$,er=B,en=Y}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[1638,7948,6671,4626,7792,2506,7150,2125,5045,9989],()=>t(1502));module.exports=a})();