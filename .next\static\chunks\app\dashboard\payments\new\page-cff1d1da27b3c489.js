(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4977],{73067:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(62898).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},71738:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(62898).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},41298:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(62898).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},99844:function(e,t,r){Promise.resolve().then(r.bind(r,99696))},99696:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return N}});var s=r(57437),n=r(24033),a=r(2265),i=r(61865),o=r(37570),l=r(92160),d=r(85754),c=r(45179),u=r(49842),m=r(27815),f=r(45509),p=r(71738),h=r(41298),x=r(5925);let y=l.Ry({amount:l.Rx().min(.01,"Amount must be greater than 0"),paymentMethod:l.Km(["CASH","CHECK","CREDIT_CARD","BANK_TRANSFER","PAYPAL","OTHER"]),reference:l.Z_().optional(),notes:l.Z_().optional(),customerId:l.Z_().optional(),description:l.Z_().min(1,"Description is required")});function j(e){let{onSuccess:t,onCancel:r,initialData:n}=e,[l,j]=(0,a.useState)([]),[b,g]=(0,a.useState)(!1),[v,N]=(0,a.useState)(!0),{register:w,handleSubmit:C,setValue:R,watch:k,formState:{errors:_}}=(0,i.cI)({resolver:(0,o.F)(y),defaultValues:{paymentMethod:"CASH",description:"Payment received",...n}}),Z=k("customerId");(0,a.useEffect)(()=>{A()},[]);let A=async()=>{try{N(!0);let e=await fetch("/api/customers");if(!e.ok)throw Error("Failed to fetch customers");let t=await e.json();j(t.customers)}catch(e){console.error("Error fetching customers:",e),x.toast.error("Failed to load customers")}finally{N(!1)}},P=async e=>{try{g(!0),"none"===e.customerId&&(e.customerId=null);let r=await fetch("/api/payments",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!r.ok){let e=await r.json();throw Error(e.error||"Failed to record payment")}await r.json(),x.toast.success("Payment recorded successfully"),t&&t()}catch(e){console.error("Error recording payment:",e),x.toast.error(e instanceof Error?e.message:"Failed to record payment")}finally{g(!1)}};return(0,s.jsxs)(m.Zb,{className:"w-full max-w-2xl mx-auto",children:[(0,s.jsx)(m.Ol,{children:(0,s.jsxs)(m.ll,{className:"flex items-center",children:[(0,s.jsx)(p.Z,{className:"h-5 w-5 mr-2"}),"Record Payment"]})}),(0,s.jsx)(m.aY,{children:(0,s.jsxs)("form",{onSubmit:C(P),className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(u._,{htmlFor:"amount",children:"Amount *"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(h.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,s.jsx)(c.I,{id:"amount",type:"number",step:"0.01",min:"0.01",...w("amount",{valueAsNumber:!0}),className:"pl-10",placeholder:"0.00"})]}),_.amount&&(0,s.jsx)("p",{className:"text-sm text-red-600 mt-1",children:_.amount.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(u._,{htmlFor:"paymentMethod",children:"Payment Method *"}),(0,s.jsxs)(f.Ph,{value:k("paymentMethod"),onValueChange:e=>R("paymentMethod",e),children:[(0,s.jsx)(f.i4,{children:(0,s.jsx)(f.ki,{placeholder:"Select payment method"})}),(0,s.jsxs)(f.Bw,{children:[(0,s.jsx)(f.Ql,{value:"CASH",children:"Cash"}),(0,s.jsx)(f.Ql,{value:"CHECK",children:"Check"}),(0,s.jsx)(f.Ql,{value:"CREDIT_CARD",children:"Credit Card"}),(0,s.jsx)(f.Ql,{value:"BANK_TRANSFER",children:"Bank Transfer"}),(0,s.jsx)(f.Ql,{value:"PAYPAL",children:"PayPal"}),(0,s.jsx)(f.Ql,{value:"OTHER",children:"Other"})]})]}),_.paymentMethod&&(0,s.jsx)("p",{className:"text-sm text-red-600 mt-1",children:_.paymentMethod.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(u._,{htmlFor:"customerId",children:"Customer (Optional)"}),(0,s.jsxs)(f.Ph,{value:Z||"",onValueChange:e=>R("customerId",e||void 0),children:[(0,s.jsx)(f.i4,{children:(0,s.jsx)(f.ki,{placeholder:"Select customer (optional)"})}),(0,s.jsxs)(f.Bw,{children:[(0,s.jsx)(f.Ql,{value:"none",children:"No customer"}),v?(0,s.jsx)(f.Ql,{value:"loading",disabled:!0,children:"Loading customers..."}):l.map(e=>(0,s.jsxs)(f.Ql,{value:e.id,children:[e.name," ",e.company&&"(".concat(e.company,")")]},e.id))]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(u._,{htmlFor:"description",children:"Description *"}),(0,s.jsx)(c.I,{id:"description",...w("description"),placeholder:"Payment description"}),_.description&&(0,s.jsx)("p",{className:"text-sm text-red-600 mt-1",children:_.description.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(u._,{htmlFor:"reference",children:"Reference Number"}),(0,s.jsx)(c.I,{id:"reference",...w("reference"),placeholder:"Check number, transaction ID, etc."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(u._,{htmlFor:"notes",children:"Notes"}),(0,s.jsx)("textarea",{id:"notes",...w("notes"),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Additional notes about this payment..."})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-4",children:[r&&(0,s.jsx)(d.z,{type:"button",variant:"outline",onClick:r,children:"Cancel"}),(0,s.jsx)(d.z,{type:"submit",disabled:b,children:b?"Recording...":"Record Payment"})]})]})})]})}var b=r(73067),g=r(61396),v=r.n(g);function N(){let e=(0,n.useRouter)();return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)(d.z,{variant:"ghost",size:"sm",asChild:!0,children:(0,s.jsxs)(v(),{href:"/dashboard/payments",children:[(0,s.jsx)(b.Z,{className:"h-4 w-4 mr-2"}),"Back to Payments"]})}),(0,s.jsx)("h1",{className:"text-3xl font-bold",children:"Record New Payment"})]})}),(0,s.jsx)(j,{onSuccess:()=>{e.push("/dashboard/payments")},onCancel:()=>{e.push("/dashboard/payments")}})]})}},85754:function(e,t,r){"use strict";r.d(t,{z:function(){return d}});var s=r(57437),n=r(2265),a=r(67256),i=r(96061),o=r(1657);let l=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=n.forwardRef((e,t)=>{let{className:r,variant:n,size:i,asChild:d=!1,...c}=e,u=d?a.g7:"button";return(0,s.jsx)(u,{className:(0,o.cn)(l({variant:n,size:i,className:r})),ref:t,...c})});d.displayName="Button"},27815:function(e,t,r){"use strict";r.d(t,{Ol:function(){return o},SZ:function(){return d},Zb:function(){return i},aY:function(){return c},eW:function(){return u},ll:function(){return l}});var s=r(57437),n=r(2265),a=r(1657);let i=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...n})});i.displayName="Card";let o=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",r),...n})});o.displayName="CardHeader";let l=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("h3",{ref:t,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",r),...n})});l.displayName="CardTitle";let d=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("p",{ref:t,className:(0,a.cn)("text-sm text-muted-foreground",r),...n})});d.displayName="CardDescription";let c=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,a.cn)("p-6 pt-0",r),...n})});c.displayName="CardContent";let u=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,a.cn)("flex items-center p-6 pt-0",r),...n})});u.displayName="CardFooter"},45179:function(e,t,r){"use strict";r.d(t,{I:function(){return i}});var s=r(57437),n=r(2265),a=r(1657);let i=n.forwardRef((e,t)=>{let{className:r,type:n,...i}=e;return(0,s.jsx)("input",{type:n,className:(0,a.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...i})});i.displayName="Input"},49842:function(e,t,r){"use strict";r.d(t,{_:function(){return d}});var s=r(57437),n=r(2265),a=r(36743),i=r(96061),o=r(1657);let l=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)(a.f,{ref:t,className:(0,o.cn)(l(),r),...n})});d.displayName=a.f.displayName},45509:function(e,t,r){"use strict";r.d(t,{Bw:function(){return h},Ph:function(){return c},Ql:function(){return x},i4:function(){return m},ki:function(){return u}});var s=r(57437),n=r(2265),a=r(99530),i=r(83523),o=r(9224),l=r(62442),d=r(1657);let c=a.fC;a.ZA;let u=a.B4,m=n.forwardRef((e,t)=>{let{className:r,children:n,...o}=e;return(0,s.jsxs)(a.xz,{ref:t,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",r),...o,children:[n,(0,s.jsx)(a.JO,{asChild:!0,children:(0,s.jsx)(i.Z,{className:"h-4 w-4 opacity-50"})})]})});m.displayName=a.xz.displayName;let f=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)(a.u_,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",r),...n,children:(0,s.jsx)(o.Z,{className:"h-4 w-4"})})});f.displayName=a.u_.displayName;let p=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)(a.$G,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",r),...n,children:(0,s.jsx)(i.Z,{className:"h-4 w-4"})})});p.displayName=a.$G.displayName;let h=n.forwardRef((e,t)=>{let{className:r,children:n,position:i="popper",...o}=e;return(0,s.jsx)(a.h_,{children:(0,s.jsxs)(a.VY,{ref:t,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",r),position:i,...o,children:[(0,s.jsx)(f,{}),(0,s.jsx)(a.l_,{className:(0,d.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:n}),(0,s.jsx)(p,{})]})})});h.displayName=a.VY.displayName,n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)(a.__,{ref:t,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",r),...n})}).displayName=a.__.displayName;let x=n.forwardRef((e,t)=>{let{className:r,children:n,...i}=e;return(0,s.jsxs)(a.ck,{ref:t,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),...i,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(a.wU,{children:(0,s.jsx)(l.Z,{className:"h-4 w-4"})})}),(0,s.jsx)(a.eT,{children:n})]})});x.displayName=a.ck.displayName,n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)(a.Z0,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",r),...n})}).displayName=a.Z0.displayName},1657:function(e,t,r){"use strict";r.d(t,{cn:function(){return a}});var s=r(57042),n=r(74769);function a(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.m6)((0,s.W)(t))}},24033:function(e,t,r){e.exports=r(15313)}},function(e){e.O(0,[6723,9502,1706,4138,1396,4997,2881,2971,4938,1744],function(){return e(e.s=99844)}),_N_E=e.O()}]);