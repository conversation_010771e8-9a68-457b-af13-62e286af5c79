(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2616],{11981:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(62898).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},68291:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(62898).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},98253:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(62898).Z)("Building2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},13008:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(62898).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1295:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(62898).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},64280:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(62898).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},31077:function(e,t,r){Promise.resolve().then(r.bind(r,8849))},8849:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return g}});var a=r(57437),i=r(2265),s=r(24033),n=r(61396),o=r.n(n),l=r(85754),c=r(27815),d=r(64280),u=r(13008),f=r(11981),p=r(1295),m=r(98253),h=r(68291),y=r(5925);function g(){let[e,t]=(0,i.useState)("loading"),[r,n]=(0,i.useState)(!1),[g,x]=(0,i.useState)(""),b=(0,s.useRouter)(),v=(0,s.useSearchParams)(),w=v.get("token"),j=v.get("email");(0,i.useEffect)(()=>{w?k(w):j||(t("error"),x("Invalid verification link"))},[w]);let k=async e=>{try{let r=await fetch("/api/auth/verify-email",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({token:e})}),a=await r.json();r.ok?(t("success"),x("Your email has been verified successfully!"),setTimeout(()=>{b.push("/auth/signin?verified=true")},3e3)):"Token expired"===a.error?(t("expired"),x("Your verification link has expired. Please request a new one.")):(t("error"),x(a.error||"Failed to verify email"))}catch(e){console.error("Verification error:",e),t("error"),x("An error occurred during verification")}},N=async()=>{if(!j){y.toast.error("Email address is required");return}n(!0);try{let e=await fetch("/api/auth/resend-verification",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:j})}),r=await e.json();e.ok?(t("resend"),x("A new verification email has been sent to your inbox."),y.toast.success("Verification email sent!")):y.toast.error(r.error||"Failed to resend verification email")}catch(e){console.error("Resend error:",e),y.toast.error("An error occurred while resending the email")}finally{n(!1)}};return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 p-4",children:(0,a.jsxs)("div",{className:"w-full max-w-md",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center mb-8",children:[(0,a.jsx)(m.Z,{className:"h-8 w-8 text-blue-600 mr-2"}),(0,a.jsx)("span",{className:"text-2xl font-bold text-gray-900",children:"Business SaaS"})]}),(0,a.jsxs)(c.Zb,{children:[(0,a.jsxs)(c.Ol,{className:"text-center",children:[(0,a.jsx)("div",{className:"flex justify-center mb-4",children:(()=>{switch(e){case"loading":default:return(0,a.jsx)(d.Z,{className:"h-12 w-12 text-blue-600 animate-spin"});case"success":return(0,a.jsx)(u.Z,{className:"h-12 w-12 text-green-600"});case"error":case"expired":return(0,a.jsx)(f.Z,{className:"h-12 w-12 text-red-600"});case"resend":return(0,a.jsx)(p.Z,{className:"h-12 w-12 text-blue-600"})}})()}),(0,a.jsx)(c.ll,{className:"text-2xl",children:(()=>{switch(e){case"loading":default:return"Verifying your email...";case"success":return"Email verified successfully!";case"error":return"Verification failed";case"expired":return"Verification link expired";case"resend":return"New verification email sent"}})()}),(0,a.jsx)(c.SZ,{children:(()=>{switch(e){case"loading":default:return"Please wait while we verify your email address...";case"success":return"Your account is now active. You will be redirected to the sign-in page shortly.";case"error":return"We encountered an issue verifying your email address.";case"expired":return"Your verification link has expired. Please request a new one below.";case"resend":return"Please check your inbox and click the verification link in the new email."}})()})]}),(0,a.jsxs)(c.aY,{className:"space-y-4",children:[g&&(0,a.jsx)("div",{className:"p-4 rounded-lg text-center ".concat("success"===e?"bg-green-50 text-green-800 border border-green-200":"resend"===e?"bg-blue-50 text-blue-800 border border-blue-200":"bg-red-50 text-red-800 border border-red-200"),children:g}),(0,a.jsxs)("div",{className:"space-y-3",children:["success"===e&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(l.z,{asChild:!0,className:"w-full",children:(0,a.jsxs)(o(),{href:"/auth/signin?verified=true",children:["Continue to Sign In",(0,a.jsx)(h.Z,{className:"h-4 w-4 ml-2"})]})}),(0,a.jsx)("p",{className:"text-sm text-center text-gray-500",children:"Redirecting automatically in 3 seconds..."})]}),("expired"===e||"error"===e)&&j&&(0,a.jsx)(l.z,{onClick:N,disabled:r,className:"w-full",children:r?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.Z,{className:"h-4 w-4 mr-2 animate-spin"}),"Sending..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.Z,{className:"h-4 w-4 mr-2"}),"Resend Verification Email"]})}),"resend"===e&&(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:"Didn't receive the email? Check your spam folder or try again."}),(0,a.jsx)(l.z,{variant:"outline",onClick:N,disabled:r,size:"sm",children:r?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.Z,{className:"h-4 w-4 mr-2 animate-spin"}),"Sending..."]}):"Send Again"})]}),(0,a.jsx)("div",{className:"text-center pt-4 border-t",children:(0,a.jsx)(o(),{href:"/auth/signin",className:"text-sm text-blue-600 hover:underline",children:"Back to Sign In"})})]}),("error"===e||"expired"===e)&&(0,a.jsxs)("div",{className:"mt-6 p-4 bg-gray-50 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Need help?"}),(0,a.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[(0,a.jsx)("li",{children:"• Make sure you're using the latest verification email"}),(0,a.jsx)("li",{children:"• Check your spam or junk folder"}),(0,a.jsx)("li",{children:"• Verification links expire after 24 hours"}),(0,a.jsx)("li",{children:"• Contact support if you continue having issues"})]})]})]})]})]})})}},85754:function(e,t,r){"use strict";r.d(t,{z:function(){return c}});var a=r(57437),i=r(2265),s=r(67256),n=r(96061),o=r(1657);let l=(0,n.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=i.forwardRef((e,t)=>{let{className:r,variant:i,size:n,asChild:c=!1,...d}=e,u=c?s.g7:"button";return(0,a.jsx)(u,{className:(0,o.cn)(l({variant:i,size:n,className:r})),ref:t,...d})});c.displayName="Button"},27815:function(e,t,r){"use strict";r.d(t,{Ol:function(){return o},SZ:function(){return c},Zb:function(){return n},aY:function(){return d},eW:function(){return u},ll:function(){return l}});var a=r(57437),i=r(2265),s=r(1657);let n=i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,a.jsx)("div",{ref:t,className:(0,s.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...i})});n.displayName="Card";let o=i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,a.jsx)("div",{ref:t,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",r),...i})});o.displayName="CardHeader";let l=i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,a.jsx)("h3",{ref:t,className:(0,s.cn)("text-2xl font-semibold leading-none tracking-tight",r),...i})});l.displayName="CardTitle";let c=i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,a.jsx)("p",{ref:t,className:(0,s.cn)("text-sm text-muted-foreground",r),...i})});c.displayName="CardDescription";let d=i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,a.jsx)("div",{ref:t,className:(0,s.cn)("p-6 pt-0",r),...i})});d.displayName="CardContent";let u=i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,a.jsx)("div",{ref:t,className:(0,s.cn)("flex items-center p-6 pt-0",r),...i})});u.displayName="CardFooter"},1657:function(e,t,r){"use strict";r.d(t,{cn:function(){return s}});var a=r(57042),i=r(74769);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,i.m6)((0,a.W)(t))}},24033:function(e,t,r){e.exports=r(15313)},5925:function(e,t,r){"use strict";let a,i;r.r(t),r.d(t,{CheckmarkIcon:function(){return G},ErrorIcon:function(){return Y},LoaderIcon:function(){return W},ToastBar:function(){return eo},ToastIcon:function(){return et},Toaster:function(){return eu},default:function(){return ef},resolveValue:function(){return N},toast:function(){return D},useToaster:function(){return _},useToasterStore:function(){return $}});var s,n=r(2265);let o={data:""},l=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||o,c=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,d=/\/\*[^]*?\*\/|  +/g,u=/\n+/g,f=(e,t)=>{let r="",a="",i="";for(let s in e){let n=e[s];"@"==s[0]?"i"==s[1]?r=s+" "+n+";":a+="f"==s[1]?f(n,s):s+"{"+f(n,"k"==s[1]?"":t)+"}":"object"==typeof n?a+=f(n,t?t.replace(/([^,])+/g,e=>s.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)):s):null!=n&&(s=/^--/.test(s)?s:s.replace(/[A-Z]/g,"-$&").toLowerCase(),i+=f.p?f.p(s,n):s+":"+n+";")}return r+(t&&i?t+"{"+i+"}":i)+a},p={},m=e=>{if("object"==typeof e){let t="";for(let r in e)t+=r+m(e[r]);return t}return e},h=(e,t,r,a,i)=>{var s;let n=m(e),o=p[n]||(p[n]=(e=>{let t=0,r=11;for(;t<e.length;)r=101*r+e.charCodeAt(t++)>>>0;return"go"+r})(n));if(!p[o]){let t=n!==e?e:(e=>{let t,r,a=[{}];for(;t=c.exec(e.replace(d,""));)t[4]?a.shift():t[3]?(r=t[3].replace(u," ").trim(),a.unshift(a[0][r]=a[0][r]||{})):a[0][t[1]]=t[2].replace(u," ").trim();return a[0]})(e);p[o]=f(i?{["@keyframes "+o]:t}:t,r?"":"."+o)}let l=r&&p.g?p.g:null;return r&&(p.g=p[o]),s=p[o],l?t.data=t.data.replace(l,s):-1===t.data.indexOf(s)&&(t.data=a?s+t.data:t.data+s),o},y=(e,t,r)=>e.reduce((e,a,i)=>{let s=t[i];if(s&&s.call){let e=s(r),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;s=t?"."+t:e&&"object"==typeof e?e.props?"":f(e,""):!1===e?"":e}return e+a+(null==s?"":s)},"");function g(e){let t=this||{},r=e.call?e(t.p):e;return h(r.unshift?r.raw?y(r,[].slice.call(arguments,1),t.p):r.reduce((e,r)=>Object.assign(e,r&&r.call?r(t.p):r),{}):r,l(t.target),t.g,t.o,t.k)}g.bind({g:1});let x,b,v,w=g.bind({k:1});function j(e,t){let r=this||{};return function(){let a=arguments;function i(s,n){let o=Object.assign({},s),l=o.className||i.className;r.p=Object.assign({theme:b&&b()},o),r.o=/ *go\d+/.test(l),o.className=g.apply(r,a)+(l?" "+l:""),t&&(o.ref=n);let c=e;return e[0]&&(c=o.as||e,delete o.as),v&&c[0]&&v(o),x(c,o)}return t?t(i):i}}var k=e=>"function"==typeof e,N=(e,t)=>k(e)?e(t):e,C=(a=0,()=>(++a).toString()),E=()=>{if(void 0===i&&"u">typeof window){let e=matchMedia("(prefers-reduced-motion: reduce)");i=!e||e.matches}return i},Z=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case 2:let{toast:r}=t;return Z(e,{type:e.toasts.find(e=>e.id===r.id)?1:0,toast:r});case 3:let{toastId:a}=t;return{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,dismissed:!0,visible:!1}:e)};case 4:return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let i=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(e=>({...e,pauseDuration:e.pauseDuration+i}))}}},S=[],M={toasts:[],pausedAt:void 0},O=e=>{M=Z(M,e),S.forEach(e=>{e(M)})},z={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},$=(e={})=>{let[t,r]=(0,n.useState)(M),a=(0,n.useRef)(M);(0,n.useEffect)(()=>(a.current!==M&&r(M),S.push(r),()=>{let e=S.indexOf(r);e>-1&&S.splice(e,1)}),[]);let i=t.toasts.map(t=>{var r,a,i;return{...e,...e[t.type],...t,removeDelay:t.removeDelay||(null==(r=e[t.type])?void 0:r.removeDelay)||(null==e?void 0:e.removeDelay),duration:t.duration||(null==(a=e[t.type])?void 0:a.duration)||(null==e?void 0:e.duration)||z[t.type],style:{...e.style,...null==(i=e[t.type])?void 0:i.style,...t.style}}});return{...t,toasts:i}},T=(e,t="blank",r)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...r,id:(null==r?void 0:r.id)||C()}),A=e=>(t,r)=>{let a=T(t,e,r);return O({type:2,toast:a}),a.id},D=(e,t)=>A("blank")(e,t);D.error=A("error"),D.success=A("success"),D.loading=A("loading"),D.custom=A("custom"),D.dismiss=e=>{O({type:3,toastId:e})},D.remove=e=>O({type:4,toastId:e}),D.promise=(e,t,r)=>{let a=D.loading(t.loading,{...r,...null==r?void 0:r.loading});return"function"==typeof e&&(e=e()),e.then(e=>{let i=t.success?N(t.success,e):void 0;return i?D.success(i,{id:a,...r,...null==r?void 0:r.success}):D.dismiss(a),e}).catch(e=>{let i=t.error?N(t.error,e):void 0;i?D.error(i,{id:a,...r,...null==r?void 0:r.error}):D.dismiss(a)}),e};var P=(e,t)=>{O({type:1,toast:{id:e,height:t}})},I=()=>{O({type:5,time:Date.now()})},R=new Map,F=1e3,V=(e,t=F)=>{if(R.has(e))return;let r=setTimeout(()=>{R.delete(e),O({type:4,toastId:e})},t);R.set(e,r)},_=e=>{let{toasts:t,pausedAt:r}=$(e);(0,n.useEffect)(()=>{if(r)return;let e=Date.now(),a=t.map(t=>{if(t.duration===1/0)return;let r=(t.duration||0)+t.pauseDuration-(e-t.createdAt);if(r<0){t.visible&&D.dismiss(t.id);return}return setTimeout(()=>D.dismiss(t.id),r)});return()=>{a.forEach(e=>e&&clearTimeout(e))}},[t,r]);let a=(0,n.useCallback)(()=>{r&&O({type:6,time:Date.now()})},[r]),i=(0,n.useCallback)((e,r)=>{let{reverseOrder:a=!1,gutter:i=8,defaultPosition:s}=r||{},n=t.filter(t=>(t.position||s)===(e.position||s)&&t.height),o=n.findIndex(t=>t.id===e.id),l=n.filter((e,t)=>t<o&&e.visible).length;return n.filter(e=>e.visible).slice(...a?[l+1]:[0,l]).reduce((e,t)=>e+(t.height||0)+i,0)},[t]);return(0,n.useEffect)(()=>{t.forEach(e=>{if(e.dismissed)V(e.id,e.removeDelay);else{let t=R.get(e.id);t&&(clearTimeout(t),R.delete(e.id))}})},[t]),{toasts:t,handlers:{updateHeight:P,startPause:I,endPause:a,calculateOffset:i}}},q=w`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,L=w`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,H=w`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,Y=j("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${q} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${L} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${H} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,B=w`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,W=j("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${B} 1s linear infinite;
`,J=w`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,U=w`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,G=j("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${J} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${U} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,K=j("div")`
  position: absolute;
`,Q=j("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,X=w`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,ee=j("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${X} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,et=({toast:e})=>{let{icon:t,type:r,iconTheme:a}=e;return void 0!==t?"string"==typeof t?n.createElement(ee,null,t):t:"blank"===r?null:n.createElement(Q,null,n.createElement(W,{...a}),"loading"!==r&&n.createElement(K,null,"error"===r?n.createElement(Y,{...a}):n.createElement(G,{...a})))},er=e=>`
0% {transform: translate3d(0,${-200*e}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,ea=e=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${-150*e}%,-1px) scale(.6); opacity:0;}
`,ei=j("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,es=j("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,en=(e,t)=>{let r=e.includes("top")?1:-1,[a,i]=E()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[er(r),ea(r)];return{animation:t?`${w(a)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${w(i)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},eo=n.memo(({toast:e,position:t,style:r,children:a})=>{let i=e.height?en(e.position||t||"top-center",e.visible):{opacity:0},s=n.createElement(et,{toast:e}),o=n.createElement(es,{...e.ariaProps},N(e.message,e));return n.createElement(ei,{className:e.className,style:{...i,...r,...e.style}},"function"==typeof a?a({icon:s,message:o}):n.createElement(n.Fragment,null,s,o))});s=n.createElement,f.p=void 0,x=s,b=void 0,v=void 0;var el=({id:e,className:t,style:r,onHeightUpdate:a,children:i})=>{let s=n.useCallback(t=>{if(t){let r=()=>{a(e,t.getBoundingClientRect().height)};r(),new MutationObserver(r).observe(t,{subtree:!0,childList:!0,characterData:!0})}},[e,a]);return n.createElement("div",{ref:s,className:t,style:r},i)},ec=(e,t)=>{let r=e.includes("top"),a=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:E()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(r?1:-1)}px)`,...r?{top:0}:{bottom:0},...a}},ed=g`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,eu=({reverseOrder:e,position:t="top-center",toastOptions:r,gutter:a,children:i,containerStyle:s,containerClassName:o})=>{let{toasts:l,handlers:c}=_(r);return n.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...s},className:o,onMouseEnter:c.startPause,onMouseLeave:c.endPause},l.map(r=>{let s=r.position||t,o=ec(s,c.calculateOffset(r,{reverseOrder:e,gutter:a,defaultPosition:t}));return n.createElement(el,{id:r.id,key:r.id,onHeightUpdate:c.updateHeight,className:r.visible?ed:"",style:o},"custom"===r.type?N(r.message,r):i?i(r):n.createElement(eo,{toast:r,position:s}))}))},ef=D}},function(e){e.O(0,[6723,1396,2971,4938,1744],function(){return e(e.s=31077)}),_N_E=e.O()}]);