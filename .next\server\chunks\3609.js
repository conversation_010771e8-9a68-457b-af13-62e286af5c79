"use strict";exports.id=3609,exports.ids=[3609],exports.modules={20298:(e,s,t)=>{t.d(s,{A:()=>y});var a=t(95344),r=t(3729),i=t(60708),l=t(55697),d=t(12374),o=t(16212),n=t(92549),c=t(1586),m=t(69436),h=t(16802),p=t(14513),x=t(51838),u=t(44669);let j=d.Ry({name:d.Z_().min(1,"Name is required"),email:d.Z_().email("Invalid email").optional().or(d.i0("")),phone:d.Z_().optional(),company:d.Z_().optional(),address:d.Z_().optional(),city:d.Z_().optional(),state:d.Z_().optional(),country:d.Z_().optional(),postalCode:d.Z_().optional(),industry:d.Z_().optional(),website:d.Z_().url("Invalid website URL").optional().or(d.i0("")),notes:d.Z_().optional(),status:d.Km(["ACTIVE","INACTIVE","PROSPECT"]).default("ACTIVE")});function y({isOpen:e,onClose:s,onSuccess:t,customer:d,mode:y}){let[v,g]=(0,r.useState)(!1),[C,f]=(0,r.useState)(d?.tags||[]),[w,I]=(0,r.useState)(""),{register:N,handleSubmit:_,formState:{errors:b},reset:Z,setValue:A}=(0,i.cI)({resolver:(0,l.F)(j),defaultValues:d?{name:d.name,email:d.email||"",phone:d.phone||"",company:d.company||"",address:d.address||"",city:d.city||"",state:d.state||"",country:d.country||"",postalCode:d.postalCode||"",industry:d.industry||"",website:d.website||"",notes:d.notes||"",status:d.status||"ACTIVE"}:{status:"ACTIVE"}}),F=()=>{w.trim()&&!C.includes(w.trim())&&(f([...C,w.trim()]),I(""))},T=e=>{f(C.filter(s=>s!==e))},E=async e=>{g(!0);try{let a={...e,tags:C},r="create"===y?"/api/customers":`/api/customers/${d.id}`,i=await fetch(r,{method:"create"===y?"POST":"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!i.ok){let e=await i.json();throw Error(e.error||"Failed to save customer")}u.toast.success(`Customer ${"create"===y?"created":"updated"} successfully`),Z(),f([]),t(),s()}catch(e){u.toast.error(e instanceof Error?e.message:"An error occurred")}finally{g(!1)}},S=()=>{Z(),f([]),I(""),s()};return a.jsx(h.Vq,{open:e,onOpenChange:S,children:(0,a.jsxs)(h.cZ,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)(h.fK,{children:[a.jsx(h.$N,{children:"create"===y?"Add New Customer":"Edit Customer"}),a.jsx(h.Be,{children:"create"===y?"Create a new customer profile with their contact information and details.":"Update the customer information and details."})]}),(0,a.jsxs)("form",{onSubmit:_(E),className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx(c._,{htmlFor:"name",children:"Name *"}),a.jsx(n.I,{id:"name",...N("name"),placeholder:"Customer name"}),b.name&&a.jsx("p",{className:"text-sm text-red-600 mt-1",children:b.name.message})]}),(0,a.jsxs)("div",{children:[a.jsx(c._,{htmlFor:"email",children:"Email"}),a.jsx(n.I,{id:"email",type:"email",...N("email"),placeholder:"<EMAIL>"}),b.email&&a.jsx("p",{className:"text-sm text-red-600 mt-1",children:b.email.message})]}),(0,a.jsxs)("div",{children:[a.jsx(c._,{htmlFor:"phone",children:"Phone"}),a.jsx(n.I,{id:"phone",...N("phone"),placeholder:"+****************"})]}),(0,a.jsxs)("div",{children:[a.jsx(c._,{htmlFor:"company",children:"Company"}),a.jsx(n.I,{id:"company",...N("company"),placeholder:"Company name"})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("h3",{className:"text-lg font-medium",children:"Address Information"}),(0,a.jsxs)("div",{children:[a.jsx(c._,{htmlFor:"address",children:"Address"}),a.jsx(n.I,{id:"address",...N("address"),placeholder:"Street address"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx(c._,{htmlFor:"city",children:"City"}),a.jsx(n.I,{id:"city",...N("city"),placeholder:"City"})]}),(0,a.jsxs)("div",{children:[a.jsx(c._,{htmlFor:"state",children:"State"}),a.jsx(n.I,{id:"state",...N("state"),placeholder:"State"})]}),(0,a.jsxs)("div",{children:[a.jsx(c._,{htmlFor:"postalCode",children:"Postal Code"}),a.jsx(n.I,{id:"postalCode",...N("postalCode"),placeholder:"12345"})]})]}),(0,a.jsxs)("div",{children:[a.jsx(c._,{htmlFor:"country",children:"Country"}),a.jsx(n.I,{id:"country",...N("country"),placeholder:"Country"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx(c._,{htmlFor:"industry",children:"Industry"}),a.jsx(n.I,{id:"industry",...N("industry"),placeholder:"Technology, Healthcare, etc."})]}),(0,a.jsxs)("div",{children:[a.jsx(c._,{htmlFor:"website",children:"Website"}),a.jsx(n.I,{id:"website",...N("website"),placeholder:"https://example.com"}),b.website&&a.jsx("p",{className:"text-sm text-red-600 mt-1",children:b.website.message})]})]}),(0,a.jsxs)("div",{children:[a.jsx(c._,{htmlFor:"status",children:"Status"}),(0,a.jsxs)("select",{id:"status",...N("status"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[a.jsx("option",{value:"ACTIVE",children:"Active"}),a.jsx("option",{value:"INACTIVE",children:"Inactive"}),a.jsx("option",{value:"PROSPECT",children:"Prospect"})]})]}),(0,a.jsxs)("div",{children:[a.jsx(c._,{children:"Tags"}),a.jsx("div",{className:"flex flex-wrap gap-2 mb-2",children:C.map(e=>(0,a.jsxs)(m.C,{variant:"secondary",className:"flex items-center gap-1",children:[e,a.jsx(p.Z,{className:"h-3 w-3 cursor-pointer",onClick:()=>T(e)})]},e))}),(0,a.jsxs)("div",{className:"flex gap-2",children:[a.jsx(n.I,{value:w,onChange:e=>I(e.target.value),placeholder:"Add a tag",onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),F())}),a.jsx(o.z,{type:"button",onClick:F,size:"sm",children:a.jsx(x.Z,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{children:[a.jsx(c._,{htmlFor:"notes",children:"Notes"}),a.jsx("textarea",{id:"notes",...N("notes"),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Additional notes about the customer..."})]}),(0,a.jsxs)(h.cN,{children:[a.jsx(o.z,{type:"button",variant:"outline",onClick:S,children:"Cancel"}),a.jsx(o.z,{type:"submit",disabled:v,children:v?"Saving...":"create"===y?"Create Customer":"Update Customer"})]})]})]})})}},88534:(e,s,t)=>{t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]])},71206:(e,s,t)=>{t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},40626:(e,s,t)=>{t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},46064:(e,s,t)=>{t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])}};