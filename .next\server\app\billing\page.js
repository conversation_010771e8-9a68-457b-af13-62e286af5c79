(()=>{var e={};e.id=8713,e.ids=[8713],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},34888:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d});var a=s(50482),r=s(69108),n=s(62563),i=s.n(n),o=s(68300),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let d=["",{children:["billing",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,21754)),"C:\\proj\\nextjs-saas\\app\\billing\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,59504)),"C:\\proj\\nextjs-saas\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\proj\\nextjs-saas\\app\\billing\\page.tsx"],m="/billing/page",u={require:s,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/billing/page",pathname:"/billing",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},22528:(e,t,s)=>{Promise.resolve().then(s.bind(s,29710))},64588:(e,t,s)=>{Promise.resolve().then(s.bind(s,56189)),Promise.resolve().then(s.bind(s,44669))},19634:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2583,23)),Promise.resolve().then(s.t.bind(s,26840,23)),Promise.resolve().then(s.t.bind(s,38771,23)),Promise.resolve().then(s.t.bind(s,13225,23)),Promise.resolve().then(s.t.bind(s,9295,23)),Promise.resolve().then(s.t.bind(s,43982,23))},29710:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>j});var a=s(95344),r=s(3729),n=s(47674),i=s(61351),o=s(16212),l=s(69436),d=s(25757),c=s(81036),m=s(7060),u=s(25545),p=s(66138),x=s(37121),f=s(33733),h=s(96885),y=s(2768),g=s(85674),b=s(51838),v=s(34755);function j(){let{data:e}=(0,n.useSession)(),[t,s]=(0,r.useState)([]),[j,N]=(0,r.useState)([]),[w,C]=(0,r.useState)(!0),[S,k]=(0,r.useState)(null);(0,r.useEffect)(()=>{e?.user&&P()},[e]);let P=async()=>{try{let[e,t]=await Promise.all([fetch("/api/billing/invoices"),fetch("/api/billing/payment-methods")]),[a,r]=await Promise.all([e.json(),t.json()]);a.success&&s(a.data.invoices),r.success&&N(r.data.paymentMethods)}catch(e){console.error("Error fetching billing data:",e),v.Am.error("Failed to load billing data")}finally{C(!1)}},M=async(e,t)=>{k(t);try{let s=await fetch("/api/billing/invoices",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:e,invoiceId:t})}),a=await s.json();a.success?"download_pdf"===e&&a.data.pdfUrl?window.open(a.data.pdfUrl,"_blank"):"retry_payment"===e&&(v.Am.success("Payment retry initiated"),P()):v.Am.error(a.error||"Action failed")}catch(e){console.error("Error handling invoice action:",e),v.Am.error("Failed to perform action")}finally{k(null)}},_=async(e,t)=>{k(t||"payment-method");try{let s=await fetch("/api/billing/payment-methods",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:e,paymentMethodId:t})}),a=await s.json();a.success?"create_setup_intent"===e?v.Am.info("Payment method setup would open here"):(v.Am.success(a.message),P()):v.Am.error(a.error||"Action failed")}catch(e){console.error("Error handling payment method action:",e),v.Am.error("Failed to perform action")}finally{k(null)}},Z=e=>{switch(e){case"paid":return a.jsx(m.Z,{className:"h-4 w-4 text-green-500"});case"open":return a.jsx(u.Z,{className:"h-4 w-4 text-yellow-500"});case"void":case"uncollectible":return a.jsx(p.Z,{className:"h-4 w-4 text-red-500"});default:return a.jsx(x.Z,{className:"h-4 w-4 text-gray-500"})}},R=e=>a.jsx(l.C,{variant:{paid:"default",open:"secondary",void:"destructive",uncollectible:"destructive"}[e]||"outline",children:e}),T=(e,t)=>new Intl.NumberFormat("en-US",{style:"currency",currency:t.toUpperCase()}).format(e/100),A=e=>new Date(e).toLocaleDateString();return w?a.jsx("div",{className:"container mx-auto p-6",children:a.jsx("div",{className:"flex items-center justify-center h-64",children:a.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})})}):(0,a.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Billing"}),a.jsx("p",{className:"text-gray-600",children:"Manage your billing and payment information"})]}),(0,a.jsxs)(o.z,{onClick:P,variant:"outline",children:[a.jsx(f.Z,{className:"h-4 w-4 mr-2"}),"Refresh"]})]}),(0,a.jsxs)(d.mQ,{defaultValue:"invoices",className:"space-y-6",children:[(0,a.jsxs)(d.dr,{children:[a.jsx(d.SP,{value:"invoices",children:"Invoices"}),a.jsx(d.SP,{value:"payment-methods",children:"Payment Methods"})]}),a.jsx(d.nU,{value:"invoices",className:"space-y-6",children:(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{children:[(0,a.jsxs)(i.ll,{className:"flex items-center gap-2",children:[a.jsx(x.Z,{className:"h-5 w-5"}),"Invoice History"]}),a.jsx(i.SZ,{children:"View and manage your billing invoices"})]}),a.jsx(i.aY,{children:0===t.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[a.jsx(x.Z,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No invoices found"}),a.jsx("p",{className:"text-gray-600",children:"Your billing invoices will appear here once you have an active subscription."})]}):(0,a.jsxs)(c.iA,{children:[a.jsx(c.xD,{children:(0,a.jsxs)(c.SC,{children:[a.jsx(c.ss,{children:"Invoice"}),a.jsx(c.ss,{children:"Status"}),a.jsx(c.ss,{children:"Amount"}),a.jsx(c.ss,{children:"Date"}),a.jsx(c.ss,{children:"Period"}),a.jsx(c.ss,{children:"Actions"})]})}),a.jsx(c.RM,{children:t.map(e=>(0,a.jsxs)(c.SC,{children:[a.jsx(c.pj,{children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[Z(e.status),a.jsx("span",{className:"font-medium",children:e.number||e.id.slice(-8)})]})}),a.jsx(c.pj,{children:R(e.status)}),a.jsx(c.pj,{className:"font-medium",children:T(e.amount,e.currency)}),a.jsx(c.pj,{children:A(e.created)}),(0,a.jsxs)(c.pj,{className:"text-sm text-gray-600",children:[A(e.periodStart)," - ",A(e.periodEnd)]}),a.jsx(c.pj,{children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(o.z,{size:"sm",variant:"outline",onClick:()=>M("download_pdf",e.id),disabled:S===e.id,children:[a.jsx(h.Z,{className:"h-3 w-3 mr-1"}),"PDF"]}),e.hostedInvoiceUrl&&(0,a.jsxs)(o.z,{size:"sm",variant:"outline",onClick:()=>window.open(e.hostedInvoiceUrl,"_blank"),children:[a.jsx(y.Z,{className:"h-3 w-3 mr-1"}),"View"]}),"open"===e.status&&(0,a.jsxs)(o.z,{size:"sm",onClick:()=>M("retry_payment",e.id),disabled:S===e.id,children:[a.jsx(f.Z,{className:"h-3 w-3 mr-1"}),"Retry"]})]})})]},e.id))})]})})]})}),a.jsx(d.nU,{value:"payment-methods",className:"space-y-6",children:(0,a.jsxs)(i.Zb,{children:[a.jsx(i.Ol,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)(i.ll,{className:"flex items-center gap-2",children:[a.jsx(g.Z,{className:"h-5 w-5"}),"Payment Methods"]}),a.jsx(i.SZ,{children:"Manage your payment methods and billing preferences"})]}),(0,a.jsxs)(o.z,{onClick:()=>_("create_setup_intent"),disabled:"payment-method"===S,children:[a.jsx(b.Z,{className:"h-4 w-4 mr-2"}),"Add Payment Method"]})]})}),a.jsx(i.aY,{children:0===j.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[a.jsx(g.Z,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No payment methods"}),a.jsx("p",{className:"text-gray-600 mb-4",children:"Add a payment method to manage your subscription billing."}),(0,a.jsxs)(o.z,{onClick:()=>_("create_setup_intent"),disabled:"payment-method"===S,children:[a.jsx(b.Z,{className:"h-4 w-4 mr-2"}),"Add Payment Method"]})]}):a.jsx("div",{className:"space-y-4",children:j.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[a.jsx(g.Z,{className:"h-8 w-8 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("span",{className:"font-medium",children:[e.card?.brand.toUpperCase()," •••• ",e.card?.last4]}),e.isDefault&&a.jsx(l.C,{variant:"secondary",children:"Default"})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Expires ",e.card?.expMonth,"/",e.card?.expYear]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[!e.isDefault&&a.jsx(o.z,{size:"sm",variant:"outline",onClick:()=>_("set_default",e.id),disabled:S===e.id,children:"Set Default"}),a.jsx(o.z,{size:"sm",variant:"outline",onClick:()=>_("delete",e.id),disabled:S===e.id||e.isDefault,children:"Remove"})]})]},e.id))})})]})})]})]})}},56189:(e,t,s)=>{"use strict";s.r(t),s.d(t,{Providers:()=>c});var a=s(95344),r=s(47674),n=s(6256),i=s(19115),o=s(26274),l=s(3729),d=s(66091);function c({children:e}){let[t]=(0,l.useState)(()=>new i.S({defaultOptions:{queries:{staleTime:6e4,retry:1}}}));return a.jsx(r.SessionProvider,{children:a.jsx(o.aH,{client:t,children:a.jsx(d.lY,{children:a.jsx(n.f,{attribute:"class",defaultTheme:"light",enableSystem:!0,disableTransitionOnChange:!0,children:e})})})})}},69436:(e,t,s)=>{"use strict";s.d(t,{C:()=>o});var a=s(95344);s(3729);var r=s(49247),n=s(91626);let i=(0,r.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600",info:"border-transparent bg-blue-500 text-white hover:bg-blue-600"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,...s}){return a.jsx("div",{className:(0,n.cn)(i({variant:t}),e),...s})}},16212:(e,t,s)=>{"use strict";s.d(t,{z:()=>d});var a=s(95344),r=s(3729),n=s(32751),i=s(49247),o=s(91626);let l=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=r.forwardRef(({className:e,variant:t,size:s,asChild:r=!1,...i},d)=>{let c=r?n.g7:"button";return a.jsx(c,{className:(0,o.cn)(l({variant:t,size:s,className:e})),ref:d,...i})});d.displayName="Button"},61351:(e,t,s)=>{"use strict";s.d(t,{Ol:()=>o,SZ:()=>d,Zb:()=>i,aY:()=>c,eW:()=>m,ll:()=>l});var a=s(95344),r=s(3729),n=s(91626);let i=r.forwardRef(({className:e,...t},s)=>a.jsx("div",{ref:s,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));i.displayName="Card";let o=r.forwardRef(({className:e,...t},s)=>a.jsx("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...t}));o.displayName="CardHeader";let l=r.forwardRef(({className:e,...t},s)=>a.jsx("h3",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let d=r.forwardRef(({className:e,...t},s)=>a.jsx("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let c=r.forwardRef(({className:e,...t},s)=>a.jsx("div",{ref:s,className:(0,n.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent";let m=r.forwardRef(({className:e,...t},s)=>a.jsx("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",e),...t}));m.displayName="CardFooter"},81036:(e,t,s)=>{"use strict";s.d(t,{RM:()=>l,SC:()=>d,iA:()=>i,pj:()=>m,ss:()=>c,xD:()=>o});var a=s(95344),r=s(3729),n=s(91626);let i=r.forwardRef(({className:e,...t},s)=>a.jsx("div",{className:"relative w-full overflow-auto",children:a.jsx("table",{ref:s,className:(0,n.cn)("w-full caption-bottom text-sm",e),...t})}));i.displayName="Table";let o=r.forwardRef(({className:e,...t},s)=>a.jsx("thead",{ref:s,className:(0,n.cn)("[&_tr]:border-b",e),...t}));o.displayName="TableHeader";let l=r.forwardRef(({className:e,...t},s)=>a.jsx("tbody",{ref:s,className:(0,n.cn)("[&_tr:last-child]:border-0",e),...t}));l.displayName="TableBody",r.forwardRef(({className:e,...t},s)=>a.jsx("tfoot",{ref:s,className:(0,n.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...t})).displayName="TableFooter";let d=r.forwardRef(({className:e,...t},s)=>a.jsx("tr",{ref:s,className:(0,n.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...t}));d.displayName="TableRow";let c=r.forwardRef(({className:e,...t},s)=>a.jsx("th",{ref:s,className:(0,n.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...t}));c.displayName="TableHead";let m=r.forwardRef(({className:e,...t},s)=>a.jsx("td",{ref:s,className:(0,n.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...t}));m.displayName="TableCell",r.forwardRef(({className:e,...t},s)=>a.jsx("caption",{ref:s,className:(0,n.cn)("mt-4 text-sm text-muted-foreground",e),...t})).displayName="TableCaption"},25757:(e,t,s)=>{"use strict";s.d(t,{mQ:()=>_,nU:()=>T,dr:()=>Z,SP:()=>R});var a=s(95344),r=s(3729),n=s(85222),i=s(98462),o=s(34504),l=s(43234),d=s(62409),c=s(3975),m=s(33183),u=s(99048),p="Tabs",[x,f]=(0,i.b)(p,[o.Pc]),h=(0,o.Pc)(),[y,g]=x(p),b=r.forwardRef((e,t)=>{let{__scopeTabs:s,value:r,onValueChange:n,defaultValue:i,orientation:o="horizontal",dir:l,activationMode:x="automatic",...f}=e,h=(0,c.gm)(l),[g,b]=(0,m.T)({prop:r,onChange:n,defaultProp:i??"",caller:p});return(0,a.jsx)(y,{scope:s,baseId:(0,u.M)(),value:g,onValueChange:b,orientation:o,dir:h,activationMode:x,children:(0,a.jsx)(d.WV.div,{dir:h,"data-orientation":o,...f,ref:t})})});b.displayName=p;var v="TabsList",j=r.forwardRef((e,t)=>{let{__scopeTabs:s,loop:r=!0,...n}=e,i=g(v,s),l=h(s);return(0,a.jsx)(o.fC,{asChild:!0,...l,orientation:i.orientation,dir:i.dir,loop:r,children:(0,a.jsx)(d.WV.div,{role:"tablist","aria-orientation":i.orientation,...n,ref:t})})});j.displayName=v;var N="TabsTrigger",w=r.forwardRef((e,t)=>{let{__scopeTabs:s,value:r,disabled:i=!1,...l}=e,c=g(N,s),m=h(s),u=k(c.baseId,r),p=P(c.baseId,r),x=r===c.value;return(0,a.jsx)(o.ck,{asChild:!0,...m,focusable:!i,active:x,children:(0,a.jsx)(d.WV.button,{type:"button",role:"tab","aria-selected":x,"aria-controls":p,"data-state":x?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:u,...l,ref:t,onMouseDown:(0,n.M)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(r)}),onKeyDown:(0,n.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(r)}),onFocus:(0,n.M)(e.onFocus,()=>{let e="manual"!==c.activationMode;x||i||!e||c.onValueChange(r)})})})});w.displayName=N;var C="TabsContent",S=r.forwardRef((e,t)=>{let{__scopeTabs:s,value:n,forceMount:i,children:o,...c}=e,m=g(C,s),u=k(m.baseId,n),p=P(m.baseId,n),x=n===m.value,f=r.useRef(x);return r.useEffect(()=>{let e=requestAnimationFrame(()=>f.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,a.jsx)(l.z,{present:i||x,children:({present:s})=>(0,a.jsx)(d.WV.div,{"data-state":x?"active":"inactive","data-orientation":m.orientation,role:"tabpanel","aria-labelledby":u,hidden:!s,id:p,tabIndex:0,...c,ref:t,style:{...e.style,animationDuration:f.current?"0s":void 0},children:s&&o})})});function k(e,t){return`${e}-trigger-${t}`}function P(e,t){return`${e}-content-${t}`}S.displayName=C;var M=s(91626);let _=b,Z=r.forwardRef(({className:e,...t},s)=>a.jsx(j,{ref:s,className:(0,M.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));Z.displayName=j.displayName;let R=r.forwardRef(({className:e,...t},s)=>a.jsx(w,{ref:s,className:(0,M.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));R.displayName=w.displayName;let T=r.forwardRef(({className:e,...t},s)=>a.jsx(S,{ref:s,className:(0,M.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));T.displayName=S.displayName},66091:(e,t,s)=>{"use strict";s.d(t,{TC:()=>l,lY:()=>o});var a=s(95344),r=s(3729);let n={appName:"SaaS Platform",logoUrl:"",faviconUrl:"",primaryColor:"#3b82f6",secondaryColor:"#64748b",accentColor:"#10b981",backgroundColor:"#ffffff",textColor:"#1f2937",theme:"light",fontFamily:"Inter, sans-serif",customCss:""},i=(0,r.createContext)(void 0);function o({children:e}){let[t,s]=(0,r.useState)(n),[o,l]=(0,r.useState)(!0);(0,r.useEffect)(()=>{d()},[]);let d=async()=>{try{let e=await fetch("/api/global-config/branding"),t=await e.json();t.success&&t.branding?(s({...n,...t.branding}),c({...n,...t.branding})):c(n)}catch(e){console.error("Error fetching branding config:",e),c(n)}finally{l(!1)}},c=e=>{let t=document.documentElement;if(t.style.setProperty("--primary-color",e.primaryColor),t.style.setProperty("--secondary-color",e.secondaryColor),t.style.setProperty("--accent-color",e.accentColor),t.style.setProperty("--background-color",e.backgroundColor),t.style.setProperty("--text-color",e.textColor),t.style.setProperty("--font-family",e.fontFamily),document.body.className=document.body.className.replace(/theme-\w+/g,""),document.body.classList.add(`theme-${e.theme}`),document.title=e.appName,e.faviconUrl){let t=document.querySelector('link[rel="icon"]');t||((t=document.createElement("link")).rel="icon",document.head.appendChild(t)),t.href=e.faviconUrl}let s=document.getElementById("custom-branding-css");e.customCss?(s||((s=document.createElement("style")).id="custom-branding-css",document.head.appendChild(s)),s.textContent=e.customCss):s&&s.remove();let a=document.querySelector('meta[name="theme-color"]');a||((a=document.createElement("meta")).name="theme-color",document.head.appendChild(a)),a.content=e.primaryColor};return a.jsx(i.Provider,{value:{branding:t,updateBranding:e=>{let a={...t,...e};s(a),c(a)},loading:o},children:e})}function l(){let e=(0,r.useContext)(i);if(void 0===e)throw Error("useBranding must be used within a BrandingProvider");return e}},91626:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n});var a=s(56815),r=s(79377);function n(...e){return(0,r.m6)((0,a.W)(e))}},66138:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(69224).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},7060:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(69224).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},25545:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(69224).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},96885:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(69224).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},2768:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(69224).Z)("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]])},37121:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(69224).Z)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]])},51838:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(69224).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},33733:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(69224).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},21754:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>n,__esModule:()=>r,default:()=>i});let a=(0,s(86843).createProxy)(String.raw`C:\proj\nextjs-saas\app\billing\page.tsx`),{__esModule:r,$$typeof:n}=a,i=a.default},59504:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p,metadata:()=>u});var a=s(25036),r=s(80265),n=s.n(r),i=s(86843);let o=(0,i.createProxy)(String.raw`C:\proj\nextjs-saas\components\providers.tsx`),{__esModule:l,$$typeof:d}=o;o.default;let c=(0,i.createProxy)(String.raw`C:\proj\nextjs-saas\components\providers.tsx#Providers`);var m=s(69636);s(67272);let u={title:{default:"Business SaaS - Complete Business Management Solution",template:"%s | Business SaaS"},description:"Complete SaaS solution for business management including CRM, invoicing, quotations, and more.",keywords:["SaaS","Business Management","CRM","Invoicing","Quotations"],authors:[{name:"Business SaaS Team"}],creator:"Business SaaS",openGraph:{type:"website",locale:"en_US",url:process.env.NEXT_PUBLIC_APP_URL,title:"Business SaaS - Complete Business Management Solution",description:"Complete SaaS solution for business management including CRM, invoicing, quotations, and more.",siteName:"Business SaaS"},twitter:{card:"summary_large_image",title:"Business SaaS - Complete Business Management Solution",description:"Complete SaaS solution for business management including CRM, invoicing, quotations, and more.",creator:"@businesssaas"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function p({children:e}){return a.jsx("html",{lang:"en",suppressHydrationWarning:!0,children:a.jsx("body",{className:n().className,children:(0,a.jsxs)(c,{children:[e,a.jsx(m.x7,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#4ade80",secondary:"#fff"}},error:{duration:4e3,iconTheme:{primary:"#ef4444",secondary:"#fff"}}}})]})})})}},67272:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[1638,7948,6671,7792,4755],()=>s(34888));module.exports=a})();