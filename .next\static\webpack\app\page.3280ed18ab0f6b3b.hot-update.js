"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/check.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Check; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst Check = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Check\", [[\"path\", { d: \"M20 6 9 17l-5-5\", key: \"1gmf2c\" }]]);\n\n\n//# sourceMappingURL=check.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2hlY2suanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXNEOztBQUV0RCxjQUFjLGdFQUFnQixzQkFBc0IscUNBQXFDOztBQUU3RDtBQUM1QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL2NoZWNrLmpzPzkwZGQiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZSBsdWNpZGUtcmVhY3QgdjAuMzAzLjAgLSBJU0NcbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBJU0MgbGljZW5zZS5cbiAqIFNlZSB0aGUgTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICovXG5cbmltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24uanMnO1xuXG5jb25zdCBDaGVjayA9IGNyZWF0ZUx1Y2lkZUljb24oXCJDaGVja1wiLCBbW1wicGF0aFwiLCB7IGQ6IFwiTTIwIDYgOSAxN2wtNS01XCIsIGtleTogXCIxZ21mMmNcIiB9XV0pO1xuXG5leHBvcnQgeyBDaGVjayBhcyBkZWZhdWx0IH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jaGVjay5qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/mail.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Mail; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst Mail = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Mail\", [\n  [\"rect\", { width: \"20\", height: \"16\", x: \"2\", y: \"4\", rx: \"2\", key: \"18n3k1\" }],\n  [\"path\", { d: \"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7\", key: \"1ocrg3\" }]\n]);\n\n\n//# sourceMappingURL=mail.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvbWFpbC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFc0Q7O0FBRXRELGFBQWEsZ0VBQWdCO0FBQzdCLGFBQWEsbUVBQW1FO0FBQ2hGLGFBQWEsK0RBQStEO0FBQzVFOztBQUUyQjtBQUMzQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL21haWwuanM/NzQzZSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlIGx1Y2lkZS1yZWFjdCB2MC4zMDMuMCAtIElTQ1xuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIElTQyBsaWNlbnNlLlxuICogU2VlIHRoZSBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKi9cblxuaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbi5qcyc7XG5cbmNvbnN0IE1haWwgPSBjcmVhdGVMdWNpZGVJY29uKFwiTWFpbFwiLCBbXG4gIFtcInJlY3RcIiwgeyB3aWR0aDogXCIyMFwiLCBoZWlnaHQ6IFwiMTZcIiwgeDogXCIyXCIsIHk6IFwiNFwiLCByeDogXCIyXCIsIGtleTogXCIxOG4zazFcIiB9XSxcbiAgW1wicGF0aFwiLCB7IGQ6IFwibTIyIDctOC45NyA1LjdhMS45NCAxLjk0IDAgMCAxLTIuMDYgMEwyIDdcIiwga2V5OiBcIjFvY3JnM1wiIH1dXG5dKTtcblxuZXhwb3J0IHsgTWFpbCBhcyBkZWZhdWx0IH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1tYWlsLmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/phone.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Phone; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst Phone = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Phone\", [\n  [\n    \"path\",\n    {\n      d: \"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\",\n      key: \"foiqr5\"\n    }\n  ]\n]);\n\n\n//# sourceMappingURL=phone.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvcGhvbmUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXNEOztBQUV0RCxjQUFjLGdFQUFnQjtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUU0QjtBQUM1QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL3Bob25lLmpzPzlmOTYiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZSBsdWNpZGUtcmVhY3QgdjAuMzAzLjAgLSBJU0NcbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBJU0MgbGljZW5zZS5cbiAqIFNlZSB0aGUgTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICovXG5cbmltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24uanMnO1xuXG5jb25zdCBQaG9uZSA9IGNyZWF0ZUx1Y2lkZUljb24oXCJQaG9uZVwiLCBbXG4gIFtcbiAgICBcInBhdGhcIixcbiAgICB7XG4gICAgICBkOiBcIk0yMiAxNi45MnYzYTIgMiAwIDAgMS0yLjE4IDIgMTkuNzkgMTkuNzkgMCAwIDEtOC42My0zLjA3IDE5LjUgMTkuNSAwIDAgMS02LTYgMTkuNzkgMTkuNzkgMCAwIDEtMy4wNy04LjY3QTIgMiAwIDAgMSA0LjExIDJoM2EyIDIgMCAwIDEgMiAxLjcyIDEyLjg0IDEyLjg0IDAgMCAwIC43IDIuODEgMiAyIDAgMCAxLS40NSAyLjExTDguMDkgOS45MWExNiAxNiAwIDAgMCA2IDZsMS4yNy0xLjI3YTIgMiAwIDAgMSAyLjExLS40NSAxMi44NCAxMi44NCAwIDAgMCAyLjgxLjdBMiAyIDAgMCAxIDIyIDE2LjkyelwiLFxuICAgICAga2V5OiBcImZvaXFyNVwiXG4gICAgfVxuICBdXG5dKTtcblxuZXhwb3J0IHsgUGhvbmUgYXMgZGVmYXVsdCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cGhvbmUuanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/landing/landing-page-content.tsx":
/*!*****************************************************!*\
  !*** ./components/landing/landing-page-content.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LandingPageContent: function() { return /* binding */ LandingPageContent; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/quote.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_6__);\n/* __next_internal_client_entry_do_not_use__ LandingPageContent auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Default fallback content\nconst defaultContent = {\n    hero: {\n        enabled: true,\n        title: \"Build Your SaaS Business\",\n        subtitle: \"The Complete Platform\",\n        description: \"Everything you need to launch, grow, and scale your SaaS business. From customer management to billing, we've got you covered.\",\n        primaryCTA: {\n            text: \"Start Free Trial\",\n            link: \"/auth/signup\"\n        },\n        secondaryCTA: {\n            text: \"Watch Demo\",\n            link: \"/demo\"\n        },\n        backgroundImage: \"\",\n        backgroundVideo: \"\"\n    },\n    features: {\n        enabled: true,\n        title: \"Everything You Need\",\n        subtitle: \"Powerful Features\",\n        items: [\n            {\n                id: \"1\",\n                title: \"Customer Management\",\n                description: \"Manage your customers, track interactions, and build lasting relationships.\",\n                icon: \"users\",\n                image: \"\"\n            },\n            {\n                id: \"2\",\n                title: \"Subscription Billing\",\n                description: \"Automated billing, invoicing, and payment processing for recurring revenue.\",\n                icon: \"credit-card\",\n                image: \"\"\n            },\n            {\n                id: \"3\",\n                title: \"Analytics & Reports\",\n                description: \"Comprehensive analytics to track your business performance and growth.\",\n                icon: \"bar-chart\",\n                image: \"\"\n            },\n            {\n                id: \"4\",\n                title: \"Multi-Tenant Architecture\",\n                description: \"Secure data isolation with company-based access control and team management.\",\n                icon: \"building\",\n                image: \"\"\n            },\n            {\n                id: \"5\",\n                title: \"Enterprise Security\",\n                description: \"Role-based access control with audit logs and data encryption.\",\n                icon: \"shield\",\n                image: \"\"\n            },\n            {\n                id: \"6\",\n                title: \"Global Ready\",\n                description: \"Multi-currency support and localization for worldwide businesses.\",\n                icon: \"globe\",\n                image: \"\"\n            }\n        ]\n    },\n    pricing: {\n        enabled: true,\n        title: \"Simple, Transparent Pricing\",\n        subtitle: \"Choose the plan that fits your needs\",\n        showPricingTable: true,\n        customMessage: \"\"\n    },\n    testimonials: {\n        enabled: true,\n        title: \"What Our Customers Say\",\n        subtitle: \"Trusted by thousands of businesses\",\n        items: [\n            {\n                id: \"1\",\n                name: \"John Smith\",\n                role: \"CEO\",\n                company: \"TechCorp\",\n                content: \"This platform has transformed how we manage our SaaS business. The automation features alone have saved us countless hours.\",\n                avatar: \"\",\n                rating: 5\n            },\n            {\n                id: \"2\",\n                name: \"Sarah Johnson\",\n                role: \"Founder\",\n                company: \"StartupXYZ\",\n                content: \"The best investment we've made for our business. The customer management features are incredibly powerful.\",\n                avatar: \"\",\n                rating: 5\n            },\n            {\n                id: \"3\",\n                name: \"Mike Chen\",\n                role: \"CTO\",\n                company: \"InnovateLab\",\n                content: \"Excellent platform with great support. The analytics help us make data-driven decisions every day.\",\n                avatar: \"\",\n                rating: 5\n            }\n        ]\n    },\n    faq: {\n        enabled: true,\n        title: \"Frequently Asked Questions\",\n        subtitle: \"Everything you need to know\",\n        items: [\n            {\n                id: \"1\",\n                question: \"How do I get started?\",\n                answer: \"Simply sign up for a free trial and follow our onboarding guide to set up your account. Our team is here to help you every step of the way.\"\n            },\n            {\n                id: \"2\",\n                question: \"Can I cancel anytime?\",\n                answer: \"Yes, you can cancel your subscription at any time. No long-term contracts or cancellation fees. Your data will remain accessible during the notice period.\"\n            },\n            {\n                id: \"3\",\n                question: \"Is my data secure?\",\n                answer: \"Absolutely. We use enterprise-grade security measures including encryption, regular backups, and compliance with industry standards like SOC 2 and GDPR.\"\n            },\n            {\n                id: \"4\",\n                question: \"Do you offer customer support?\",\n                answer: \"Yes, we provide 24/7 customer support via email, chat, and phone. Our premium plans also include dedicated account managers.\"\n            },\n            {\n                id: \"5\",\n                question: \"Can I integrate with other tools?\",\n                answer: \"Yes, we offer integrations with popular tools like Slack, Zapier, QuickBooks, and many more. We also provide a robust API for custom integrations.\"\n            }\n        ]\n    },\n    cta: {\n        enabled: true,\n        title: \"Ready to Get Started?\",\n        description: \"Join thousands of businesses already using our platform to grow their SaaS.\",\n        buttonText: \"Start Your Free Trial\",\n        buttonLink: \"/auth/signup\",\n        backgroundImage: \"\"\n    },\n    footer: {\n        enabled: true,\n        companyDescription: \"The complete SaaS platform for modern businesses.\",\n        links: [\n            {\n                id: \"1\",\n                title: \"Product\",\n                items: [\n                    {\n                        id: \"1\",\n                        text: \"Features\",\n                        link: \"/features\"\n                    },\n                    {\n                        id: \"2\",\n                        text: \"Pricing\",\n                        link: \"/pricing\"\n                    },\n                    {\n                        id: \"3\",\n                        text: \"Security\",\n                        link: \"/security\"\n                    },\n                    {\n                        id: \"4\",\n                        text: \"Integrations\",\n                        link: \"/integrations\"\n                    }\n                ]\n            },\n            {\n                id: \"2\",\n                title: \"Company\",\n                items: [\n                    {\n                        id: \"1\",\n                        text: \"About\",\n                        link: \"/about\"\n                    },\n                    {\n                        id: \"2\",\n                        text: \"Blog\",\n                        link: \"/blog\"\n                    },\n                    {\n                        id: \"3\",\n                        text: \"Careers\",\n                        link: \"/careers\"\n                    },\n                    {\n                        id: \"4\",\n                        text: \"Contact\",\n                        link: \"/contact\"\n                    }\n                ]\n            },\n            {\n                id: \"3\",\n                title: \"Support\",\n                items: [\n                    {\n                        id: \"1\",\n                        text: \"Help Center\",\n                        link: \"/help\"\n                    },\n                    {\n                        id: \"2\",\n                        text: \"Documentation\",\n                        link: \"/docs\"\n                    },\n                    {\n                        id: \"3\",\n                        text: \"API Reference\",\n                        link: \"/api\"\n                    },\n                    {\n                        id: \"4\",\n                        text: \"Status\",\n                        link: \"/status\"\n                    }\n                ]\n            }\n        ],\n        socialLinks: {\n            twitter: \"https://twitter.com/yourcompany\",\n            linkedin: \"https://linkedin.com/company/yourcompany\",\n            facebook: \"https://facebook.com/yourcompany\",\n            instagram: \"https://instagram.com/yourcompany\"\n        },\n        copyrightText: \"\\xa9 2024 Your Company. All rights reserved.\"\n    },\n    seo: {\n        title: \"SaaS Platform - Build Your Business\",\n        description: \"The complete SaaS platform for modern businesses. Customer management, billing, analytics, and more.\",\n        keywords: \"saas, platform, business, customer management, billing, analytics\",\n        ogImage: \"\"\n    }\n};\nconst getIconComponent = (iconName)=>{\n    const icons = {\n        users: _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        \"credit-card\": _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        \"bar-chart\": _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        building: _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        shield: _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        globe: _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        zap: _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        \"file-text\": _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n    };\n    return icons[iconName] || _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n};\nconst formatStorage = (bytes)=>{\n    const gb = bytes / (1024 * 1024 * 1024);\n    return gb >= 1 ? \"\".concat(gb, \"GB\") : \"\".concat(Math.round(gb * 1024), \"MB\");\n};\nconst getFeatureList = (plan)=>{\n    const features = [];\n    // Add usage limits\n    features.push(\"Up to \".concat(plan.maxUsers, \" users\"));\n    features.push(\"\".concat(plan.maxCompanies, \" \").concat(plan.maxCompanies === 1 ? \"company\" : \"companies\"));\n    features.push(\"\".concat(plan.maxCustomers, \" customers\"));\n    features.push(\"\".concat(plan.maxQuotations, \" quotations/month\"));\n    features.push(\"\".concat(plan.maxInvoices, \" invoices/month\"));\n    features.push(\"\".concat(formatStorage(plan.maxStorage), \" storage\"));\n    // Add feature flags\n    if (plan.features.basicReporting) features.push(\"Basic reporting\");\n    if (plan.features.emailSupport) features.push(\"Email support\");\n    if (plan.features.mobileApp) features.push(\"Mobile app access\");\n    if (plan.features.advancedAnalytics) features.push(\"Advanced analytics\");\n    if (plan.features.customBranding) features.push(\"Custom branding\");\n    if (plan.features.apiAccess) features.push(\"API access\");\n    if (plan.features.prioritySupport) features.push(\"Priority support\");\n    if (plan.features.customIntegrations) features.push(\"Custom integrations\");\n    if (plan.features.advancedSecurity) features.push(\"Advanced security\");\n    if (plan.features.dedicatedManager) features.push(\"Dedicated account manager\");\n    return features;\n};\nfunction LandingPageContent() {\n    var _content_hero, _content_features, _content_pricing, _content_testimonials, _content_faq, _content_cta, _content_footer;\n    _s();\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultContent);\n    const [plans, setPlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [openFAQ, setOpenFAQ] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isYearly, setIsYearly] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchData = async ()=>{\n            try {\n                // Fetch CMS content\n                const cmsResponse = await fetch(\"/api/super-admin/cms\");\n                const cmsData = await cmsResponse.json();\n                if (cmsData.success && cmsData.content) {\n                    // Merge with default content to ensure all sections exist\n                    setContent({\n                        ...defaultContent,\n                        ...cmsData.content\n                    });\n                }\n                // Fetch pricing plans\n                const plansResponse = await fetch(\"/api/pricing-plans?publicOnly=true\");\n                const plansData = await plansResponse.json();\n                if (plansData.success) {\n                    // Sort plans by sortOrder and filter active public plans\n                    const activePlans = plansData.data.filter((plan)=>plan.isActive && plan.isPublic).sort((a, b)=>a.sortOrder - b.sortOrder);\n                    setPlans(activePlans);\n                }\n            } catch (error) {\n                console.error(\"Error fetching data:\", error);\n            // Use default content on error\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchData();\n    }, []);\n    const getPrice = (plan)=>{\n        if (isYearly && plan.yearlyPrice) {\n            return plan.yearlyPrice / 12 // Show monthly equivalent\n            ;\n        }\n        return plan.monthlyPrice;\n    };\n    const getYearlyDiscount = (plan)=>{\n        if (!plan.yearlyPrice || !plan.monthlyPrice) return 0;\n        const yearlyMonthly = plan.yearlyPrice / 12;\n        const discount = (plan.monthlyPrice - yearlyMonthly) / plan.monthlyPrice * 100;\n        return Math.round(discount);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 469,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n            lineNumber: 468,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"border-b bg-white/95 backdrop-blur-sm sticky top-0 z-50 shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-8 w-8 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: \"SaaS Platform\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"hidden md:flex items-center space-x-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#features\",\n                                            className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                            children: \"Features\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#pricing\",\n                                            className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                            children: \"Pricing\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#testimonials\",\n                                            className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                            children: \"Testimonials\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#faq\",\n                                            className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                            children: \"FAQ\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 497,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/contact\",\n                                            className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                            children: \"Contact\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 487,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/auth/signin\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"ghost\",\n                                                className: \"text-gray-600 hover:text-gray-900\",\n                                                children: \"Sign In\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/auth/signup\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                className: \"bg-blue-600 hover:bg-blue-700\",\n                                                children: \"Get Started Free\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 506,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"md:hidden p-2\",\n                                    onClick: ()=>setMobileMenuOpen(!mobileMenuOpen),\n                                    children: mobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-6 w-6 text-gray-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-6 w-6 text-gray-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 527,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 479,\n                            columnNumber: 11\n                        }, this),\n                        mobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden mt-4 pb-4 border-t\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex flex-col space-y-4 pt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#features\",\n                                        className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"Features\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#pricing\",\n                                        className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"Pricing\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 543,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#testimonials\",\n                                        className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"Testimonials\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 550,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#faq\",\n                                        className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"FAQ\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 557,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                        href: \"/contact\",\n                                        className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"Contact\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 564,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col space-y-2 pt-4 border-t\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                href: \"/auth/signin\",\n                                                onClick: ()=>setMobileMenuOpen(false),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"ghost\",\n                                                    className: \"w-full justify-start\",\n                                                    children: \"Sign In\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 573,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 572,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                href: \"/auth/signup\",\n                                                onClick: ()=>setMobileMenuOpen(false),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    className: \"w-full bg-blue-600 hover:bg-blue-700\",\n                                                    children: \"Get Started Free\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 578,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 577,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 571,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                lineNumber: 535,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 534,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 478,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 477,\n                columnNumber: 7\n            }, this),\n            ((_content_hero = content.hero) === null || _content_hero === void 0 ? void 0 : _content_hero.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4 relative overflow-hidden\",\n                children: [\n                    content.hero.backgroundImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 z-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_6___default()), {\n                            src: content.hero.backgroundImage,\n                            alt: \"Hero Background\",\n                            fill: true,\n                            className: \"object-cover opacity-20\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 594,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                        lineNumber: 593,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto text-center relative z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto\",\n                            children: [\n                                content.hero.subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                    className: \"mb-4 text-sm px-4 py-2\",\n                                    children: content.hero.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 605,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-5xl md:text-6xl font-bold text-gray-900 mb-6\",\n                                    children: content.hero.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 609,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n                                    children: content.hero.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 612,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-4 justify-center mb-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: content.hero.primaryCTA.link,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                size: \"lg\",\n                                                className: \"text-lg px-8 py-3\",\n                                                children: [\n                                                    content.hero.primaryCTA.text,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"ml-2 h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 619,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 617,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 616,\n                                            columnNumber: 17\n                                        }, this),\n                                        content.hero.secondaryCTA.text && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: content.hero.secondaryCTA.link,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                size: \"lg\",\n                                                variant: \"outline\",\n                                                className: \"text-lg px-8 py-3\",\n                                                children: content.hero.secondaryCTA.text\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 623,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 615,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 603,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                        lineNumber: 602,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 591,\n                columnNumber: 9\n            }, this),\n            ((_content_features = content.features) === null || _content_features === void 0 ? void 0 : _content_features.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"features\",\n                className: \"py-20 px-4 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                    children: content.features.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 640,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                                    children: content.features.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 643,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 639,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: content.features.items.map((feature)=>{\n                                const IconComponent = getIconComponent(feature.icon);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"border-0 shadow-lg hover:shadow-xl transition-shadow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                        className: \"h-6 w-6 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 655,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 654,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-xl\",\n                                                    children: feature.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 657,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 653,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                className: \"text-gray-600\",\n                                                children: feature.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 660,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 659,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, feature.id, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 652,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 648,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 638,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 637,\n                columnNumber: 9\n            }, this),\n            ((_content_pricing = content.pricing) === null || _content_pricing === void 0 ? void 0 : _content_pricing.enabled) && plans.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"pricing\",\n                className: \"py-20 px-4 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                    children: content.pricing.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 677,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 max-w-2xl mx-auto mb-8\",\n                                    children: content.pricing.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 680,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-4 mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm \".concat(!isYearly ? \"text-gray-900 font-medium\" : \"text-gray-500\"),\n                                            children: \"Monthly\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 686,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Switch, {\n                                            checked: isYearly,\n                                            onCheckedChange: setIsYearly\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 689,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm \".concat(isYearly ? \"text-gray-900 font-medium\" : \"text-gray-500\"),\n                                            children: \"Yearly\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 693,\n                                            columnNumber: 17\n                                        }, this),\n                                        plans.some((plan)=>getYearlyDiscount(plan) > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            className: \"ml-2 bg-green-100 text-green-800\",\n                                            children: [\n                                                \"Save up to \",\n                                                Math.max(...plans.map(getYearlyDiscount)),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 697,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 685,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 676,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto\",\n                            children: plans.map((plan, index)=>{\n                                const features = getFeatureList(plan);\n                                const price = getPrice(plan);\n                                const discount = getYearlyDiscount(plan);\n                                const isPopular = index === 1 // Middle plan is popular\n                                ;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"relative \".concat(isPopular ? \"border-blue-500 shadow-xl scale-105\" : \"border-gray-200 shadow-lg\", \" bg-white\"),\n                                    children: [\n                                        isPopular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -top-4 left-1/2 transform -translate-x-1/2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                className: \"bg-blue-500 text-white px-4 py-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 717,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    \"Most Popular\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 716,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 715,\n                                            columnNumber: 23\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            className: \"text-center pb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: plan.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 724,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                    className: \"text-gray-600 mt-2\",\n                                                    children: plan.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 725,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-baseline justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-5xl font-bold text-gray-900\",\n                                                                    children: [\n                                                                        \"$\",\n                                                                        price.toFixed(0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                    lineNumber: 731,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-500 ml-1\",\n                                                                    children: \"/month\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                    lineNumber: 734,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 730,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        isYearly && discount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-green-600 mt-2\",\n                                                            children: [\n                                                                \"Save \",\n                                                                discount,\n                                                                \"% with yearly billing\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 737,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        plan.trialDays > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-blue-600 mt-2\",\n                                                            children: [\n                                                                plan.trialDays,\n                                                                \"-day free trial\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 742,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 729,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 723,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        features.slice(0, 8).map((feature, featureIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-green-500 flex-shrink-0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                        lineNumber: 753,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-700 text-sm\",\n                                                                        children: feature\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                        lineNumber: 754,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, featureIndex, true, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                lineNumber: 752,\n                                                                columnNumber: 27\n                                                            }, this)),\n                                                        features.length > 8 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500 italic\",\n                                                            children: [\n                                                                \"+\",\n                                                                features.length - 8,\n                                                                \" more features\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 758,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 750,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                    href: \"/auth/signup\",\n                                                    className: \"block\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        className: \"w-full \".concat(isPopular ? \"bg-blue-600 hover:bg-blue-700\" : \"\"),\n                                                        variant: isPopular ? \"default\" : \"outline\",\n                                                        size: \"lg\",\n                                                        children: [\n                                                            \"Get Started\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"ml-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                lineNumber: 771,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 765,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 764,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 749,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, plan.id, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 713,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 705,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mt-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"max-w-2xl mx-auto border-gray-200 shadow-lg bg-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                            children: \"Need something custom?\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 784,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-6\",\n                                            children: \"Contact our sales team for enterprise pricing, custom features, and dedicated support.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 787,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    size: \"lg\",\n                                                    variant: \"outline\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 792,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Contact Sales\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 791,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    size: \"lg\",\n                                                    variant: \"ghost\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 796,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Schedule Demo\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 795,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 790,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 783,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                lineNumber: 782,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 781,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 675,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 674,\n                columnNumber: 9\n            }, this),\n            ((_content_testimonials = content.testimonials) === null || _content_testimonials === void 0 ? void 0 : _content_testimonials.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                    children: content.testimonials.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 812,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                                    children: content.testimonials.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 815,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 811,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: content.testimonials.items.map((testimonial)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"border-0 shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    ...Array(testimonial.rating)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-5 w-5 text-yellow-400 fill-current\"\n                                                    }, i, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 826,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 824,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"h-8 w-8 text-gray-300 mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 829,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-6 italic\",\n                                                children: [\n                                                    '\"',\n                                                    testimonial.content,\n                                                    '\"'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 830,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    testimonial.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                                        src: testimonial.avatar,\n                                                        alt: testimonial.name,\n                                                        width: 48,\n                                                        height: 48,\n                                                        className: \"rounded-full mr-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 835,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-gray-200 rounded-full mr-4 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-6 w-6 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 844,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 843,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-semibold text-gray-900\",\n                                                                children: testimonial.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                lineNumber: 848,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: [\n                                                                    testimonial.role,\n                                                                    \", \",\n                                                                    testimonial.company\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                lineNumber: 849,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 847,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 833,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 823,\n                                        columnNumber: 19\n                                    }, this)\n                                }, testimonial.id, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 822,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 820,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 810,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 809,\n                columnNumber: 9\n            }, this),\n            ((_content_faq = content.faq) === null || _content_faq === void 0 ? void 0 : _content_faq.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto max-w-4xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                    children: content.faq.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 865,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600\",\n                                    children: content.faq.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 868,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 864,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: content.faq.items.map((faq)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"border shadow-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"p-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"w-full p-6 text-left flex items-center justify-between hover:bg-gray-50\",\n                                                onClick: ()=>setOpenFAQ(openFAQ === faq.id ? null : faq.id),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold text-gray-900\",\n                                                        children: faq.question\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 881,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    openFAQ === faq.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 883,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 885,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 877,\n                                                columnNumber: 21\n                                            }, this),\n                                            openFAQ === faq.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-6 pb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: faq.answer\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 890,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 889,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 876,\n                                        columnNumber: 19\n                                    }, this)\n                                }, faq.id, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 875,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 873,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 863,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 862,\n                columnNumber: 9\n            }, this),\n            ((_content_cta = content.cta) === null || _content_cta === void 0 ? void 0 : _content_cta.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4 bg-blue-600 relative overflow-hidden\",\n                children: [\n                    content.cta.backgroundImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 z-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_6___default()), {\n                            src: content.cta.backgroundImage,\n                            alt: \"CTA Background\",\n                            fill: true,\n                            className: \"object-cover opacity-20\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 906,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                        lineNumber: 905,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto text-center relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl font-bold text-white mb-6\",\n                                children: content.cta.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                lineNumber: 915,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-blue-100 mb-8 max-w-2xl mx-auto\",\n                                children: content.cta.description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                lineNumber: 918,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                href: content.cta.buttonLink,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    size: \"lg\",\n                                    variant: \"secondary\",\n                                    className: \"text-lg px-8 py-3\",\n                                    children: [\n                                        content.cta.buttonText,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"ml-2 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 924,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 922,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                lineNumber: 921,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                        lineNumber: 914,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 903,\n                columnNumber: 9\n            }, this),\n            ((_content_footer = content.footer) === null || _content_footer === void 0 ? void 0 : _content_footer.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-gray-900 text-white py-12 px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-4 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-6 w-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 938,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-lg font-bold\",\n                                                    children: \"SaaS Platform\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 939,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 937,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400\",\n                                            children: content.footer.companyDescription\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 941,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 936,\n                                    columnNumber: 15\n                                }, this),\n                                content.footer.links.map((linkGroup)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-4\",\n                                                children: linkGroup.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 947,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-2 text-gray-400\",\n                                                children: linkGroup.items.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                            href: link.link,\n                                                            className: \"hover:text-white\",\n                                                            children: link.text\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 951,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, link.id, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 950,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 948,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, linkGroup.id, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 946,\n                                        columnNumber: 17\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 935,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: content.footer.copyrightText\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                lineNumber: 961,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 960,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 934,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 933,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n        lineNumber: 475,\n        columnNumber: 5\n    }, this);\n}\n_s(LandingPageContent, \"Ob77mu/u4HMnkCjOlnHJhwEopZQ=\");\n_c = LandingPageContent;\nvar _c;\n$RefreshReg$(_c, \"LandingPageContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/landing/landing-page-content.tsx\n"));

/***/ })

});