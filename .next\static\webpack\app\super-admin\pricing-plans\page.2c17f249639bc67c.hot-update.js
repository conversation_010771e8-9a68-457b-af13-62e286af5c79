"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/super-admin/pricing-plans/page",{

/***/ "(app-pages-browser)/./app/super-admin/pricing-plans/page.tsx":
/*!************************************************!*\
  !*** ./app/super-admin/pricing-plans/page.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PricingPlansManagementPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Database,DollarSign,Edit,Eye,Plus,RefreshCw,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Database,DollarSign,Edit,Eye,Plus,RefreshCw,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Database,DollarSign,Edit,Eye,Plus,RefreshCw,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Database,DollarSign,Edit,Eye,Plus,RefreshCw,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Database,DollarSign,Edit,Eye,Plus,RefreshCw,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Database,DollarSign,Edit,Eye,Plus,RefreshCw,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Database,DollarSign,Edit,Eye,Plus,RefreshCw,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Database,DollarSign,Edit,Eye,Plus,RefreshCw,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Database,DollarSign,Edit,Eye,Plus,RefreshCw,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Database,DollarSign,Edit,Eye,Plus,RefreshCw,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction PricingPlansManagementPage() {\n    var _session_user;\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [plans, setPlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showCreateDialog, setShowCreateDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEditDialog, setShowEditDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingPlan, setEditingPlan] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        description: \"\",\n        monthlyPrice: 0,\n        yearlyPrice: 0,\n        currency: \"USD\",\n        maxUsers: 1,\n        maxCompanies: 1,\n        maxCustomers: 10,\n        maxQuotations: 5,\n        maxInvoices: 5,\n        maxContracts: 1,\n        maxStorage: 1073741824,\n        isActive: true,\n        isPublic: true,\n        trialDays: 0,\n        sortOrder: 0,\n        features: {\n            basicReporting: true,\n            emailSupport: true,\n            mobileApp: false,\n            advancedAnalytics: false,\n            customBranding: false,\n            apiAccess: false,\n            prioritySupport: false,\n            customIntegrations: false,\n            advancedSecurity: false,\n            dedicatedManager: false\n        }\n    });\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                lineNumber: 101,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n            lineNumber: 100,\n            columnNumber: 7\n        }, this);\n    }\n    if (status === \"unauthenticated\") {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.redirect)(\"/auth/signin\");\n    }\n    if ((session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.role) !== \"SUPER_ADMIN\") {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.redirect)(\"/dashboard\");\n    }\n    const fetchPlans = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/pricing-plans?includeInactive=true\");\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const data = await response.json();\n            console.log(\"Fetched plans data:\", data) // Debug log\n            ;\n            if (data.success && data.data) {\n                setPlans(data.data);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"Loaded \".concat(data.data.length, \" pricing plans\"));\n            } else {\n                console.error(\"API response error:\", data);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_9__.toast.error(data.error || \"Failed to load pricing plans\");\n            }\n        } catch (error) {\n            console.error(\"Error fetching plans:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Failed to load pricing plans\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchPlans();\n    }, []);\n    const handleToggleActive = async (planId, isActive)=>{\n        try {\n            const response = await fetch(\"/api/pricing-plans/\".concat(planId), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    isActive\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"Plan \".concat(isActive ? \"activated\" : \"deactivated\", \" successfully\"));\n                fetchPlans();\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_9__.toast.error(data.error || \"Failed to update plan\");\n            }\n        } catch (error) {\n            console.error(\"Error updating plan:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Failed to update plan\");\n        }\n    };\n    const handleCreatePlan = async ()=>{\n        try {\n            const response = await fetch(\"/api/pricing-plans\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(formData)\n            });\n            const data = await response.json();\n            if (data.success) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"Plan created successfully\");\n                setShowCreateDialog(false);\n                resetForm();\n                fetchPlans();\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_9__.toast.error(data.error || \"Failed to create plan\");\n            }\n        } catch (error) {\n            console.error(\"Error creating plan:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Failed to create plan\");\n        }\n    };\n    const handleEditPlan = (plan)=>{\n        setEditingPlan(plan);\n        setFormData({\n            name: plan.name,\n            description: plan.description,\n            monthlyPrice: plan.monthlyPrice,\n            yearlyPrice: plan.yearlyPrice || 0,\n            currency: plan.currency,\n            maxUsers: plan.maxUsers,\n            maxCompanies: plan.maxCompanies,\n            maxCustomers: plan.maxCustomers,\n            maxQuotations: plan.maxQuotations,\n            maxInvoices: plan.maxInvoices,\n            maxContracts: plan.maxContracts,\n            maxStorage: plan.maxStorage,\n            isActive: plan.isActive,\n            isPublic: plan.isPublic,\n            trialDays: plan.trialDays,\n            sortOrder: plan.sortOrder,\n            features: plan.features\n        });\n        setShowEditDialog(true);\n    };\n    const handleUpdatePlan = async ()=>{\n        if (!editingPlan) return;\n        try {\n            const response = await fetch(\"/api/pricing-plans/\".concat(editingPlan.id), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(formData)\n            });\n            const data = await response.json();\n            if (data.success) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"Plan updated successfully\");\n                setShowEditDialog(false);\n                setEditingPlan(null);\n                resetForm();\n                fetchPlans();\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_9__.toast.error(data.error || \"Failed to update plan\");\n            }\n        } catch (error) {\n            console.error(\"Error updating plan:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Failed to update plan\");\n        }\n    };\n    const handleDeletePlan = async (planId)=>{\n        if (!confirm(\"Are you sure you want to delete this pricing plan? This action cannot be undone.\")) {\n            return;\n        }\n        try {\n            const response = await fetch(\"/api/pricing-plans/\".concat(planId), {\n                method: \"DELETE\"\n            });\n            const data = await response.json();\n            if (data.success) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"Plan deleted successfully\");\n                fetchPlans();\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_9__.toast.error(data.error || \"Failed to delete plan\");\n            }\n        } catch (error) {\n            console.error(\"Error deleting plan:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Failed to delete plan\");\n        }\n    };\n    const resetForm = ()=>{\n        setFormData({\n            name: \"\",\n            description: \"\",\n            monthlyPrice: 0,\n            yearlyPrice: 0,\n            currency: \"USD\",\n            maxUsers: 1,\n            maxCompanies: 1,\n            maxCustomers: 10,\n            maxQuotations: 5,\n            maxInvoices: 5,\n            maxContracts: 1,\n            maxStorage: 1073741824,\n            isActive: true,\n            isPublic: true,\n            trialDays: 0,\n            sortOrder: 0,\n            features: {\n                basicReporting: true,\n                emailSupport: true,\n                mobileApp: false,\n                advancedAnalytics: false,\n                customBranding: false,\n                apiAccess: false,\n                prioritySupport: false,\n                customIntegrations: false,\n                advancedSecurity: false,\n                dedicatedManager: false\n            }\n        });\n    };\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\",\n            minimumFractionDigits: 0\n        }).format(amount);\n    };\n    const getFeatureCount = (features)=>{\n        return Object.values(features).filter(Boolean).length;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row sm:items-center justify-between gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"Pricing Plans Management\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 mt-1\",\n                                children: \"Manage subscription plans, pricing, and features\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                        lineNumber: 321,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                variant: \"outline\",\n                                onClick: fetchPlans,\n                                disabled: loading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 \".concat(loading ? \"animate-spin\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                onClick: ()=>setShowCreateDialog(true),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Add Plan\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                lineNumber: 320,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Total Plans\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: plans.length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                            lineNumber: 343,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                        lineNumber: 342,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Active Plans\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-green-600\",\n                                                children: plans.filter((p)=>p.isActive).length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-8 w-8 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                            lineNumber: 355,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Public Plans\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-purple-600\",\n                                                children: plans.filter((p)=>p.isPublic).length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-8 w-8 text-purple-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                            lineNumber: 369,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                        lineNumber: 368,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                lineNumber: 341,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                children: [\n                                    \"Pricing Plans (\",\n                                    plans.length,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                lineNumber: 386,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                children: \"Manage your subscription plans, pricing, and features\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                        lineNumber: 385,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center py-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                            lineNumber: 393,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.Table, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableRow, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                    children: \"Plan\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                    children: \"Pricing\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                    lineNumber: 402,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                    children: \"Limits\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                    children: \"Features\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                    children: \"Actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                    lineNumber: 406,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                            lineNumber: 400,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableBody, {\n                                        children: plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableRow, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold\",\n                                                                    children: plan.name.charAt(0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                    lineNumber: 414,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium text-gray-900\",\n                                                                                    children: plan.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                                    lineNumber: 419,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                plan.name.toLowerCase() === \"pro\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                    className: \"h-4 w-4 text-yellow-500\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                                    lineNumber: 421,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                            lineNumber: 418,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: plan.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                            lineNumber: 424,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                    lineNumber: 417,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                            lineNumber: 413,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: [\n                                                                        formatCurrency(plan.monthlyPrice),\n                                                                        \"/month\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                    lineNumber: 430,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                plan.yearlyPrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: [\n                                                                        formatCurrency(plan.yearlyPrice),\n                                                                        \"/year (\",\n                                                                        plan.yearlyDiscount,\n                                                                        \"% off)\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                    lineNumber: 432,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                plan.trialDays > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                    variant: \"secondary\",\n                                                                    className: \"text-xs mt-1\",\n                                                                    children: [\n                                                                        plan.trialDays,\n                                                                        \"-day trial\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                    lineNumber: 437,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                            lineNumber: 429,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                        lineNumber: 428,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-gray-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                            lineNumber: 446,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                plan.maxUsers,\n                                                                                \" users\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                            lineNumber: 447,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                    lineNumber: 445,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-gray-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                            lineNumber: 450,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: plan.formattedStorage\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                            lineNumber: 451,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                    lineNumber: 449,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: [\n                                                                        plan.maxCustomers,\n                                                                        \" customers, \",\n                                                                        plan.maxQuotations,\n                                                                        \" quotes\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                    lineNumber: 453,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                            variant: \"outline\",\n                                                            children: [\n                                                                getFeatureCount(plan.features),\n                                                                \" features\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                            lineNumber: 459,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_7__.Switch, {\n                                                                            checked: plan.isActive,\n                                                                            onCheckedChange: (checked)=>handleToggleActive(plan.id, checked)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                            lineNumber: 466,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm\",\n                                                                            children: plan.isActive ? \"Active\" : \"Inactive\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                            lineNumber: 470,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                    lineNumber: 465,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                plan.isPublic && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                    variant: \"secondary\",\n                                                                    className: \"text-xs\",\n                                                                    children: \"Public\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                    lineNumber: 475,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                            lineNumber: 464,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                        lineNumber: 463,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>handleEditPlan(plan),\n                                                                    title: \"Edit plan\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                        lineNumber: 489,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                    lineNumber: 483,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>handleDeletePlan(plan.id),\n                                                                    title: \"Delete plan\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                        lineNumber: 497,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                    lineNumber: 491,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, plan.id, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                            lineNumber: 397,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                        lineNumber: 391,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                lineNumber: 384,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Dialog, {\n                open: showCreateDialog,\n                onOpenChange: setShowCreateDialog,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogContent, {\n                    className: \"max-w-4xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogTitle, {\n                                    children: \"Create New Pricing Plan\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                    lineNumber: 514,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogDescription, {\n                                    children: \"Add a new subscription plan with custom pricing and features.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                    lineNumber: 515,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                            lineNumber: 513,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlanForm, {\n                            formData: formData,\n                            setFormData: setFormData,\n                            onSubmit: handleCreatePlan,\n                            onCancel: ()=>{\n                                setShowCreateDialog(false);\n                                resetForm();\n                            },\n                            submitLabel: \"Create Plan\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                            lineNumber: 519,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                    lineNumber: 512,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                lineNumber: 511,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Dialog, {\n                open: showEditDialog,\n                onOpenChange: setShowEditDialog,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogContent, {\n                    className: \"max-w-4xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogTitle, {\n                                    children: \"Edit Pricing Plan\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                    lineNumber: 536,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogDescription, {\n                                    children: \"Modify the pricing plan details and features.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                    lineNumber: 537,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                            lineNumber: 535,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlanForm, {\n                            formData: formData,\n                            setFormData: setFormData,\n                            onSubmit: handleUpdatePlan,\n                            onCancel: ()=>{\n                                setShowEditDialog(false);\n                                setEditingPlan(null);\n                                resetForm();\n                            },\n                            submitLabel: \"Update Plan\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                            lineNumber: 541,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                    lineNumber: 534,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                lineNumber: 533,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n        lineNumber: 318,\n        columnNumber: 5\n    }, this);\n}\n_s(PricingPlansManagementPage, \"y5y7foUXkRaM5jF1H2QQOnSNci4=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession\n    ];\n});\n_c = PricingPlansManagementPage;\nvar _c;\n$RefreshReg$(_c, \"PricingPlansManagementPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/super-admin/pricing-plans/page.tsx\n"));

/***/ })

});