"use strict";(()=>{var e={};e.id=108,e.ids=[108],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},96175:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>f,originalPathname:()=>q,patchFetch:()=>E,requestAsyncStorage:()=>h,routeModule:()=>g,serverHooks:()=>v,staticGenerationAsyncStorage:()=>A,staticGenerationBailout:()=>I});var a={};r.r(a),r.d(a,{DELETE:()=>_,GET:()=>y,PUT:()=>w});var o=r(95419),s=r(69108),n=r(99678),i=r(78070),c=r(81355),u=r(3205),l=r(9108),d=r(25252),p=r(52178);let m=d.Ry({name:d.Z_().optional(),email:d.Z_().email().optional(),phone:d.Z_().optional(),address:d.Z_().optional(),city:d.Z_().optional(),state:d.Z_().optional(),country:d.Z_().optional(),postalCode:d.Z_().optional(),website:d.Z_().url().optional().or(d.i0("")),industry:d.Z_().optional(),size:d.Km(["STARTUP","SMALL","MEDIUM","LARGE","ENTERPRISE"]).optional(),businessType:d.Z_().optional(),status:d.Km(["ACTIVE","SUSPENDED","INACTIVE"]).optional(),taxId:d.Z_().optional(),registrationNumber:d.Z_().optional()});async function y(e,{params:t}){try{let e=await (0,c.getServerSession)(u.L);if(!e?.user?.id||e?.user?.role!=="SUPER_ADMIN")return i.Z.json({error:"Super admin access required"},{status:403});let r=t.id,a=await l._.company.findUnique({where:{id:r},include:{owner:{select:{id:!0,name:!0,email:!0,role:!0,status:!0,lastLoginAt:!0,loginCount:!0,createdAt:!0}},members:{select:{id:!0,name:!0,email:!0,role:!0,status:!0,lastLoginAt:!0,loginCount:!0,createdAt:!0},orderBy:{createdAt:"desc"}},customers:{select:{id:!0,name:!0,email:!0,status:!0,createdAt:!0},orderBy:{createdAt:"desc"},take:10},quotations:{select:{id:!0,quotationNumber:!0,status:!0,total:!0,createdAt:!0},orderBy:{createdAt:"desc"},take:10},invoices:{select:{id:!0,invoiceNumber:!0,status:!0,total:!0,createdAt:!0},orderBy:{createdAt:"desc"},take:10},contracts:{select:{id:!0,contractNumber:!0,status:!0,value:!0,createdAt:!0},orderBy:{createdAt:"desc"},take:10},items:{select:{id:!0,name:!0,sku:!0,unitPrice:!0,stockQuantity:!0,createdAt:!0},orderBy:{createdAt:"desc"},take:10},activities:{select:{id:!0,type:!0,title:!0,description:!0,createdAt:!0,createdBy:{select:{name:!0,email:!0}}},orderBy:{createdAt:"desc"},take:20},_count:{select:{members:!0,customers:!0,quotations:!0,invoices:!0,contracts:!0,items:!0,activities:!0}}}});if(!a)return i.Z.json({error:"Company not found"},{status:404});let[o,s,n,d]=await Promise.all([Promise.all([l._.invoice.aggregate({where:{companyId:r,status:"PAID"},_sum:{total:!0},_count:!0}),l._.invoice.aggregate({where:{companyId:r,status:"PENDING"},_sum:{total:!0},_count:!0}),l._.quotation.aggregate({where:{companyId:r,status:"APPROVED"},_sum:{total:!0},_count:!0})]),Promise.all([l._.activity.count({where:{companyId:r,createdAt:{gte:new Date(Date.now()-2592e6)}}}),l._.activity.groupBy({by:["type"],where:{companyId:r},_count:{id:!0},orderBy:{_count:{id:"desc"}}})]),Promise.all([l._.user.count({where:{companyId:r,status:"ACTIVE"}}),l._.user.count({where:{companyId:r,lastLoginAt:{gte:new Date(Date.now()-2592e6)}}})]),Promise.all([l._.quotation.count({where:{companyId:r,createdAt:{gte:new Date(Date.now()-2592e6)}}}),l._.invoice.count({where:{companyId:r,createdAt:{gte:new Date(Date.now()-2592e6)}}}),l._.contract.count({where:{companyId:r,createdAt:{gte:new Date(Date.now()-2592e6)}}})])]),[p,m,y]=o,[w,_]=s,[g,h]=n,[A,v,f]=d;return i.Z.json({company:{...a,items:a.items.map(e=>({...e,unitPrice:Number(e.unitPrice),stockQuantity:Number(e.stockQuantity)})),quotations:a.quotations.map(e=>({...e,total:Number(e.total)})),invoices:a.invoices.map(e=>({...e,total:Number(e.total)})),contracts:a.contracts.map(e=>({...e,value:Number(e.value)}))},analytics:{revenue:{paid:{amount:Number(p._sum.total||0),count:p._count},pending:{amount:Number(m._sum.total||0),count:m._count},approved:{amount:Number(y._sum.total||0),count:y._count}},activity:{recent:w,byType:_.map(e=>({type:e.type,count:e._count.id}))},users:{active:g,recentlyActive:h},documents:{recentQuotations:A,recentInvoices:v,recentContracts:f}}})}catch(e){return console.error("Error fetching company details:",e),i.Z.json({error:"Failed to fetch company details"},{status:500})}}async function w(e,{params:t}){try{let r=await (0,c.getServerSession)(u.L);if(!r?.user?.id||r?.user?.role!=="SUPER_ADMIN")return i.Z.json({error:"Super admin access required"},{status:403});let a=t.id,o=await e.json(),s=m.parse(o);if(!await l._.company.findUnique({where:{id:a}}))return i.Z.json({error:"Company not found"},{status:404});let n=await l._.$transaction(async e=>{let t=await e.company.update({where:{id:a},data:s,include:{owner:{select:{id:!0,name:!0,email:!0,role:!0}},_count:{select:{users:!0,customers:!0,quotations:!0,invoices:!0,contracts:!0}}}});return await e.activity.create({data:{type:"ADMIN",title:"Company Updated by Super Admin",description:`Company "${t.name}" was updated by super admin`,companyId:a,createdById:r.user.id}}),t});return i.Z.json({company:n,message:"Company updated successfully"})}catch(e){if(e instanceof p.jm)return i.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("Error updating company:",e),i.Z.json({error:"Failed to update company"},{status:500})}}async function _(e,{params:t}){try{let e=await (0,c.getServerSession)(u.L);if(!e?.user?.id||e?.user?.role!=="SUPER_ADMIN")return i.Z.json({error:"Super admin access required"},{status:403});let r=t.id;if(!await l._.company.findUnique({where:{id:r},select:{name:!0}}))return i.Z.json({error:"Company not found"},{status:404});return await l._.$transaction(async e=>{await e.company.delete({where:{id:r}})}),i.Z.json({message:"Company deleted successfully"})}catch(e){return console.error("Error deleting company:",e),i.Z.json({error:"Failed to delete company"},{status:500})}}let g=new o.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/super-admin/companies/[id]/route",pathname:"/api/super-admin/companies/[id]",filename:"route",bundlePath:"app/api/super-admin/companies/[id]/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\super-admin\\companies\\[id]\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:h,staticGenerationAsyncStorage:A,serverHooks:v,headerHooks:f,staticGenerationBailout:I}=g,q="/api/super-admin/companies/[id]/route";function E(){return(0,n.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:A})}},3205:(e,t,r)=>{r.d(t,{L:()=>u});var a=r(86485),o=r(10375),s=r(50694),n=r(6521),i=r.n(n),c=r(9108);let u={providers:[(0,a.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await c._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),r=t?.companyId;if(!r&&t){let e=await c._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(r=e?.id)&&await c._.user.update({where:{id:t.id},data:{companyId:r}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await i().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await c._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:r}}catch(e){return console.error("Authentication error:",e),null}}}),(0,o.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,s.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,r)=>{r.d(t,{_:()=>o});let a=require("@prisma/client"),o=globalThis.prisma??new a.PrismaClient}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,6206,6521,2455,4520,5252],()=>r(96175));module.exports=a})();