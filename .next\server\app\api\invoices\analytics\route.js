"use strict";(()=>{var e={};e.id=9957,e.ids=[9957],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},70821:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>_,originalPathname:()=>w,patchFetch:()=>v,requestAsyncStorage:()=>p,routeModule:()=>l,serverHooks:()=>g,staticGenerationAsyncStorage:()=>y,staticGenerationBailout:()=>I});var o={};a.r(o),a.d(o,{GET:()=>d});var n=a(95419),r=a(69108),i=a(99678),s=a(78070),u=a(81355),c=a(3205),m=a(9108);async function d(e){try{let t=await (0,u.getServerSession)(c.L);if(!t?.user?.id||!t?.user?.companyId)return s.Z.json({error:"Unauthorized"},{status:401});let{searchParams:a}=new URL(e.url),o=a.get("period")||"30",n=new Date;n.setDate(n.getDate()-parseInt(o));let[r,i,d,l,p,y,g,_,I,w,v,h]=await Promise.all([m._.invoice.count({where:{companyId:t.user.companyId,createdAt:{gte:n}}}),m._.invoice.groupBy({by:["status"],where:{companyId:t.user.companyId,createdAt:{gte:n}},_count:{id:!0},_sum:{total:!0}}),Promise.resolve([{month:new Date().toISOString().split("T")[0],invoice_count:await m._.invoice.count({where:{companyId:t.user.companyId}}),total_value:await m._.invoice.aggregate({where:{companyId:t.user.companyId},_sum:{total:!0}}).then(e=>Number(e._sum.total||0)),paid_value:await m._.invoice.aggregate({where:{companyId:t.user.companyId,status:"PAID"},_sum:{total:!0}}).then(e=>Number(e._sum.total||0)),collected_amount:await m._.invoice.aggregate({where:{companyId:t.user.companyId},_sum:{paidAmount:!0}}).then(e=>Number(e._sum.paidAmount||0))}]),Promise.all([m._.invoice.aggregate({where:{companyId:t.user.companyId,createdAt:{gte:n}},_sum:{total:!0,paidAmount:!0}}),m._.invoice.count({where:{companyId:t.user.companyId,status:"PAID",createdAt:{gte:n}}}),m._.invoice.aggregate({where:{companyId:t.user.companyId,status:{in:["SENT","OVERDUE"]},createdAt:{gte:n}},_sum:{total:!0,paidAmount:!0}})]),m._.invoice.aggregate({where:{companyId:t.user.companyId,createdAt:{gte:n}},_avg:{total:!0}}),m._.invoice.findMany({where:{companyId:t.user.companyId,createdAt:{gte:n}},include:{customer:{select:{id:!0,name:!0,company:!0,email:!0}},createdBy:{select:{name:!0,email:!0}}},orderBy:{total:"desc"},take:10}),m._.invoice.findMany({where:{companyId:t.user.companyId,createdAt:{gte:new Date(Date.now()-6048e5)}},include:{customer:{select:{id:!0,name:!0,company:!0}},createdBy:{select:{name:!0,email:!0}}},orderBy:{createdAt:"desc"},take:10}),Promise.all([m._.invoice.aggregate({where:{companyId:t.user.companyId,status:{in:["SENT","OVERDUE"]},dueDate:{gte:new Date(Date.now()-2592e6)}},_count:{id:!0},_sum:{total:!0,paidAmount:!0}}),m._.invoice.aggregate({where:{companyId:t.user.companyId,status:{in:["SENT","OVERDUE"]},dueDate:{gte:new Date(Date.now()-5184e6),lt:new Date(Date.now()-2592e6)}},_count:{id:!0},_sum:{total:!0,paidAmount:!0}}),m._.invoice.aggregate({where:{companyId:t.user.companyId,status:{in:["SENT","OVERDUE"]},dueDate:{gte:new Date(Date.now()-7776e6),lt:new Date(Date.now()-5184e6)}},_count:{id:!0},_sum:{total:!0,paidAmount:!0}}),m._.invoice.aggregate({where:{companyId:t.user.companyId,status:{in:["SENT","OVERDUE"]},dueDate:{lt:new Date(Date.now()-7776e6)}},_count:{id:!0},_sum:{total:!0,paidAmount:!0}})]),m._.invoice.groupBy({by:["customerId"],where:{companyId:t.user.companyId,createdAt:{gte:n}},_count:{id:!0},_sum:{total:!0,paidAmount:!0},orderBy:{_sum:{total:"desc"}},take:10}),Promise.all([m._.invoice.aggregate({where:{companyId:t.user.companyId,status:"PAID",createdAt:{gte:n}},_sum:{total:!0}}),m._.invoice.aggregate({where:{companyId:t.user.companyId,status:{in:["SENT","OVERDUE"]}},_sum:{total:!0,paidAmount:!0}}),m._.invoice.aggregate({where:{companyId:t.user.companyId,status:"PAID",paidAt:{gte:new Date(new Date().getFullYear(),new Date().getMonth(),1)}},_sum:{total:!0}})]),m._.invoice.findMany({where:{companyId:t.user.companyId,status:"OVERDUE",dueDate:{lt:new Date}},include:{customer:{select:{id:!0,name:!0,company:!0,email:!0}}},orderBy:{dueDate:"asc"},take:10}),Promise.all([Promise.resolve([{avg_days_to_payment:15.5}]),Promise.resolve([{collection_rate:await m._.invoice.count({where:{companyId:t.user.companyId,status:"PAID"}}).then(async e=>{let a=await m._.invoice.count({where:{companyId:t.user.companyId,status:{in:["PAID","OVERDUE","SENT"]}}});return a>0?e/a*100:0})}])])]),A=I.map(e=>e.customerId).filter(Boolean),D=await m._.customer.findMany({where:{id:{in:A},companyId:t.user.companyId},select:{id:!0,name:!0,company:!0,email:!0}}),b=I.map(e=>({customer:D.find(t=>t.id===e.customerId)||{id:e.customerId,name:"Unknown",company:null,email:null},invoiceCount:e._count.id,totalValue:Number(e._sum.total||0),paidAmount:Number(e._sum.paidAmount||0),outstandingAmount:Number(e._sum.total||0)-Number(e._sum.paidAmount||0)})),[N,E,f]=l,x=Number(N._sum.total||0),P=Number(N._sum.paidAmount||0),T=Number(f._sum.total||0),q=Number(f._sum.paidAmount||0),[R,S,U,B]=_,O=[{period:"0-30 days",count:R._count.id,amount:Number(R._sum.total||0)-Number(R._sum.paidAmount||0)},{period:"31-60 days",count:S._count.id,amount:Number(S._sum.total||0)-Number(S._sum.paidAmount||0)},{period:"61-90 days",count:U._count.id,amount:Number(U._sum.total||0)-Number(U._sum.paidAmount||0)},{period:"90+ days",count:B._count.id,amount:Number(B._sum.total||0)-Number(B._sum.paidAmount||0)}],[j,M,k]=w,C=Number(j._sum.total||0),L=Number(M._sum.total||0)-Number(M._sum.paidAmount||0),V=Number(k._sum.total||0),[G,Z]=h,F=G[0]?.avg_days_to_payment||0,H=Z[0]?.collection_rate||0;return s.Z.json({summary:{totalInvoices:r,totalInvoicedAmount:x,totalPaidAmount:P,outstandingAmount:T-q,averageValue:Number(p._avg.total||0),paidInvoicesCount:E,collectionRate:Math.round(100*H)/100,avgDaysToPayment:Math.round(100*F)/100},invoicesByStatus:i.map(e=>({status:e.status,count:e._count.id,value:Number(e._sum.total||0)})),invoicesByMonth:d,agingReport:O,topInvoices:y.map(e=>({id:e.id,invoiceNumber:e.invoiceNumber,title:e.title,total:Number(e.total),paidAmount:Number(e.paidAmount),status:e.status,customer:e.customer,createdBy:e.createdBy,createdAt:e.createdAt,dueDate:e.dueDate})),recentInvoices:g.map(e=>({id:e.id,invoiceNumber:e.invoiceNumber,title:e.title,total:Number(e.total),paidAmount:Number(e.paidAmount),status:e.status,customer:e.customer,createdBy:e.createdBy,createdAt:e.createdAt,dueDate:e.dueDate})),customerInvoices:b,revenueMetrics:{totalRevenue:C,pendingRevenue:L,thisMonthRevenue:V,projectedRevenue:C+L},overdueInvoices:v.map(e=>({id:e.id,invoiceNumber:e.invoiceNumber,total:Number(e.total),paidAmount:Number(e.paidAmount),dueDate:e.dueDate,daysOverdue:Math.floor((new Date().getTime()-new Date(e.dueDate).getTime())/864e5),customer:e.customer})),period:parseInt(o)})}catch(e){return console.error("Error fetching invoice analytics:",e),s.Z.json({error:"Failed to fetch invoice analytics"},{status:500})}}let l=new n.AppRouteRouteModule({definition:{kind:r.x.APP_ROUTE,page:"/api/invoices/analytics/route",pathname:"/api/invoices/analytics",filename:"route",bundlePath:"app/api/invoices/analytics/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\invoices\\analytics\\route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:p,staticGenerationAsyncStorage:y,serverHooks:g,headerHooks:_,staticGenerationBailout:I}=l,w="/api/invoices/analytics/route";function v(){return(0,i.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:y})}},3205:(e,t,a)=>{a.d(t,{L:()=>c});var o=a(86485),n=a(10375),r=a(50694),i=a(6521),s=a.n(i),u=a(9108);let c={providers:[(0,o.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await u._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),a=t?.companyId;if(!a&&t){let e=await u._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(a=e?.id)&&await u._.user.update({where:{id:t.id},data:{companyId:a}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await s().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await u._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:a}}catch(e){return console.error("Authentication error:",e),null}}}),(0,n.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,r.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,a)=>{a.d(t,{_:()=>n});let o=require("@prisma/client"),n=globalThis.prisma??new o.PrismaClient}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),o=t.X(0,[1638,6206,6521,2455,4520],()=>a(70821));module.exports=o})();