import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Validation schema for lead conversion
const conversionSchema = z.object({
  customerData: z.object({
    name: z.string().min(1, 'Customer name is required'),
    email: z.string().email('Valid email is required'),
    phone: z.string().optional(),
    company: z.string().optional(),
    address: z.string().optional(),
    city: z.string().optional(),
    state: z.string().optional(),
    zipCode: z.string().optional(),
    country: z.string().optional(),
    website: z.string().optional(),
    industry: z.string().optional(),
    companySize: z.enum(['STARTUP', 'SMALL', 'MEDIUM', 'LARGE', 'ENTERPRISE']).optional(),
    notes: z.string().optional()
  }),
  conversionData: z.object({
    conversionType: z.enum(['DIRECT', 'QUOTATION', 'PROPOSAL', 'TRIAL', 'DEMO']),
    conversionReason: z.string().optional(),
    conversionValue: z.number().optional(),
    conversionDate: z.string().optional(),
    salesRepId: z.string().optional(),
    conversionNotes: z.string().optional(),
    followUpRequired: z.boolean().default(false),
    followUpDate: z.string().optional()
  }),
  createQuotation: z.boolean().default(false),
  quotationData: z.object({
    title: z.string().optional(),
    description: z.string().optional(),
    validUntil: z.string().optional(),
    items: z.array(z.object({
      description: z.string(),
      quantity: z.number(),
      unitPrice: z.number(),
      discount: z.number().default(0)
    })).optional()
  }).optional()
})

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = conversionSchema.parse(body)

    // Check if lead exists and belongs to company
    const lead = await prisma.lead.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId
      },
      include: {
        activities: true,
        leadNotes: true
      }
    })

    if (!lead) {
      return NextResponse.json({ error: 'Lead not found' }, { status: 404 })
    }

    // Check if lead is already converted
    if (lead.status === 'CONVERTED') {
      return NextResponse.json({ error: 'Lead is already converted' }, { status: 400 })
    }

    // Start transaction for conversion
    const result = await prisma.$transaction(async (tx) => {
      // 1. Create customer from lead data
      const customer = await tx.customer.create({
        data: {
          ...validatedData.customerData,
          companyId: session.user.companyId,
          createdById: session.user.id,
          leadId: params.id, // Reference to original lead
          source: 'LEAD_CONVERSION',
          status: 'ACTIVE'
        }
      })

      // 2. Create conversion record
      const conversion = await tx.leadConversion.create({
        data: {
          leadId: params.id,
          customerId: customer.id,
          companyId: session.user.companyId,
          conversionType: validatedData.conversionData.conversionType,
          conversionReason: validatedData.conversionData.conversionReason,
          conversionValue: validatedData.conversionData.conversionValue,
          conversionDate: validatedData.conversionData.conversionDate 
            ? new Date(validatedData.conversionData.conversionDate)
            : new Date(),
          salesRepId: validatedData.conversionData.salesRepId || session.user.id,
          conversionNotes: validatedData.conversionData.conversionNotes,
          followUpRequired: validatedData.conversionData.followUpRequired,
          followUpDate: validatedData.conversionData.followUpDate 
            ? new Date(validatedData.conversionData.followUpDate)
            : null,
          createdById: session.user.id
        }
      })

      // 3. Update lead status to converted
      const updatedLead = await tx.lead.update({
        where: { id: params.id },
        data: {
          status: 'CONVERTED',
          convertedAt: new Date(),
          convertedById: session.user.id,
          customerId: customer.id,
          updatedAt: new Date()
        }
      })

      // 4. Create conversion activity
      await tx.activity.create({
        data: {
          type: 'NOTE',
          title: 'Lead Converted to Customer',
          description: `Lead successfully converted to customer: ${customer.name}. Conversion type: ${validatedData.conversionData.conversionType}`,
          status: 'COMPLETED',
          priority: 'HIGH',
          leadId: params.id,
          companyId: session.user.companyId,
          createdById: session.user.id,
          completedAt: new Date()
        }
      })

      // 5. Transfer lead notes to customer notes (optional)
      if (lead.leadNotes.length > 0) {
        const customerNotes = lead.leadNotes.map(note => ({
          customerId: customer.id,
          title: `[Transferred from Lead] ${note.title}`,
          content: note.content,
          isPrivate: note.isPrivate,
          companyId: session.user.companyId,
          createdById: note.createdById,
          createdAt: note.createdAt
        }))

        await tx.customerNote.createMany({
          data: customerNotes
        })
      }

      // 6. Create quotation if requested
      let quotation = null
      if (validatedData.createQuotation && validatedData.quotationData) {
        quotation = await tx.quotation.create({
          data: {
            title: validatedData.quotationData.title || `Quotation for ${customer.name}`,
            description: validatedData.quotationData.description || 'Generated from lead conversion',
            customerId: customer.id,
            status: 'DRAFT',
            validUntil: validatedData.quotationData.validUntil 
              ? new Date(validatedData.quotationData.validUntil)
              : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
            companyId: session.user.companyId,
            createdById: session.user.id,
            items: validatedData.quotationData.items ? {
              create: validatedData.quotationData.items.map(item => ({
                description: item.description,
                quantity: item.quantity,
                unitPrice: item.unitPrice,
                discount: item.discount,
                total: (item.quantity * item.unitPrice) * (1 - item.discount / 100),
                companyId: session.user.companyId
              }))
            } : undefined
          },
          include: {
            items: true
          }
        })

        // Update quotation total
        if (quotation.items.length > 0) {
          const total = quotation.items.reduce((sum, item) => sum + item.total, 0)
          await tx.quotation.update({
            where: { id: quotation.id },
            data: { total }
          })
        }
      }

      // 7. Create follow-up task if required
      if (validatedData.conversionData.followUpRequired && validatedData.conversionData.followUpDate) {
        await tx.task.create({
          data: {
            title: 'Follow up with converted customer',
            description: `Follow up with ${customer.name} after lead conversion`,
            status: 'PENDING',
            priority: 'MEDIUM',
            dueDate: new Date(validatedData.conversionData.followUpDate),
            customerId: customer.id,
            assignedToId: validatedData.conversionData.salesRepId || session.user.id,
            companyId: session.user.companyId,
            createdById: session.user.id
          }
        })
      }

      return {
        conversion,
        customer,
        lead: updatedLead,
        quotation
      }
    })

    return NextResponse.json({
      message: 'Lead converted successfully',
      conversion: result.conversion,
      customer: result.customer,
      quotation: result.quotation
    }, { status: 201 })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error converting lead:', error)
    return NextResponse.json(
      { error: 'Failed to convert lead' },
      { status: 500 }
    )
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get lead with conversion data
    const lead = await prisma.lead.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId
      },
      include: {
        conversion: {
          include: {
            customer: true,
            salesRep: {
              select: {
                id: true,
                name: true,
                email: true
              }
            }
          }
        },
        customer: true
      }
    })

    if (!lead) {
      return NextResponse.json({ error: 'Lead not found' }, { status: 404 })
    }

    // Get conversion readiness score
    const conversionReadiness = calculateConversionReadiness(lead)

    return NextResponse.json({
      lead,
      conversionReadiness,
      isConverted: lead.status === 'CONVERTED',
      conversion: lead.conversion,
      customer: lead.customer
    })

  } catch (error) {
    console.error('Error fetching conversion data:', error)
    return NextResponse.json(
      { error: 'Failed to fetch conversion data' },
      { status: 500 }
    )
  }
}

// Helper function to calculate conversion readiness
function calculateConversionReadiness(lead: any) {
  let score = 0
  let maxScore = 100
  const factors = []

  // Lead score factor (30 points)
  const scorePoints = Math.min((lead.score / 100) * 30, 30)
  score += scorePoints
  factors.push({
    factor: 'Lead Score',
    points: scorePoints,
    maxPoints: 30,
    status: lead.score >= 70 ? 'GOOD' : lead.score >= 40 ? 'FAIR' : 'POOR'
  })

  // Contact information completeness (20 points)
  let contactPoints = 0
  if (lead.email) contactPoints += 7
  if (lead.phone) contactPoints += 7
  if (lead.companyName) contactPoints += 6
  score += contactPoints
  factors.push({
    factor: 'Contact Information',
    points: contactPoints,
    maxPoints: 20,
    status: contactPoints >= 15 ? 'GOOD' : contactPoints >= 10 ? 'FAIR' : 'POOR'
  })

  // Budget qualification (25 points)
  const budgetPoints = (lead.budget && lead.budget > 0) ? 25 : 0
  score += budgetPoints
  factors.push({
    factor: 'Budget Qualification',
    points: budgetPoints,
    maxPoints: 25,
    status: budgetPoints > 0 ? 'GOOD' : 'POOR'
  })

  // Timeline qualification (15 points)
  const timelinePoints = lead.timeline ? 15 : 0
  score += timelinePoints
  factors.push({
    factor: 'Timeline Qualification',
    points: timelinePoints,
    maxPoints: 15,
    status: timelinePoints > 0 ? 'GOOD' : 'POOR'
  })

  // Engagement level (10 points)
  const activityCount = lead._count?.activities || 0
  const engagementPoints = Math.min(activityCount * 2, 10)
  score += engagementPoints
  factors.push({
    factor: 'Engagement Level',
    points: engagementPoints,
    maxPoints: 10,
    status: engagementPoints >= 8 ? 'GOOD' : engagementPoints >= 4 ? 'FAIR' : 'POOR'
  })

  const readinessLevel = score >= 80 ? 'HIGH' : score >= 60 ? 'MEDIUM' : 'LOW'

  return {
    score,
    maxScore,
    percentage: Math.round((score / maxScore) * 100),
    level: readinessLevel,
    factors,
    recommendations: getConversionRecommendations(factors, readinessLevel)
  }
}

function getConversionRecommendations(factors: any[], level: string) {
  const recommendations = []

  factors.forEach(factor => {
    if (factor.status === 'POOR') {
      switch (factor.factor) {
        case 'Lead Score':
          recommendations.push({
            type: 'SCORING',
            priority: 'HIGH',
            message: 'Lead score is low. Focus on engagement and qualification activities.',
            action: 'Improve lead engagement'
          })
          break
        case 'Contact Information':
          recommendations.push({
            type: 'CONTACT',
            priority: 'HIGH',
            message: 'Missing contact information. Collect email, phone, and company details.',
            action: 'Complete contact information'
          })
          break
        case 'Budget Qualification':
          recommendations.push({
            type: 'QUALIFICATION',
            priority: 'URGENT',
            message: 'Budget not qualified. Determine budget range and decision-making process.',
            action: 'Qualify budget and authority'
          })
          break
        case 'Timeline Qualification':
          recommendations.push({
            type: 'QUALIFICATION',
            priority: 'HIGH',
            message: 'Timeline not established. Understand urgency and decision timeline.',
            action: 'Establish timeline and urgency'
          })
          break
        case 'Engagement Level':
          recommendations.push({
            type: 'ENGAGEMENT',
            priority: 'MEDIUM',
            message: 'Low engagement level. Schedule more touchpoints and activities.',
            action: 'Increase engagement activities'
          })
          break
      }
    }
  })

  if (level === 'HIGH') {
    recommendations.push({
      type: 'CONVERSION',
      priority: 'URGENT',
      message: 'Lead is ready for conversion. Schedule a closing call or send proposal.',
      action: 'Initiate conversion process'
    })
  }

  return recommendations
}
