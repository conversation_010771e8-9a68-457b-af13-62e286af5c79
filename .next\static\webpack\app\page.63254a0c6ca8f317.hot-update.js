"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/menu.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Menu; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst Menu = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Menu\", [\n  [\"line\", { x1: \"4\", x2: \"20\", y1: \"12\", y2: \"12\", key: \"1e0a9i\" }],\n  [\"line\", { x1: \"4\", x2: \"20\", y1: \"6\", y2: \"6\", key: \"1owob3\" }],\n  [\"line\", { x1: \"4\", x2: \"20\", y1: \"18\", y2: \"18\", key: \"yk5zj1\" }]\n]);\n\n\n//# sourceMappingURL=menu.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvbWVudS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFc0Q7O0FBRXRELGFBQWEsZ0VBQWdCO0FBQzdCLGFBQWEsc0RBQXNEO0FBQ25FLGFBQWEsb0RBQW9EO0FBQ2pFLGFBQWEsc0RBQXNEO0FBQ25FOztBQUUyQjtBQUMzQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL21lbnUuanM/MDRmOCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlIGx1Y2lkZS1yZWFjdCB2MC4zMDMuMCAtIElTQ1xuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIElTQyBsaWNlbnNlLlxuICogU2VlIHRoZSBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKi9cblxuaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbi5qcyc7XG5cbmNvbnN0IE1lbnUgPSBjcmVhdGVMdWNpZGVJY29uKFwiTWVudVwiLCBbXG4gIFtcImxpbmVcIiwgeyB4MTogXCI0XCIsIHgyOiBcIjIwXCIsIHkxOiBcIjEyXCIsIHkyOiBcIjEyXCIsIGtleTogXCIxZTBhOWlcIiB9XSxcbiAgW1wibGluZVwiLCB7IHgxOiBcIjRcIiwgeDI6IFwiMjBcIiwgeTE6IFwiNlwiLCB5MjogXCI2XCIsIGtleTogXCIxb3dvYjNcIiB9XSxcbiAgW1wibGluZVwiLCB7IHgxOiBcIjRcIiwgeDI6IFwiMjBcIiwgeTE6IFwiMThcIiwgeTI6IFwiMThcIiwga2V5OiBcInlrNXpqMVwiIH1dXG5dKTtcblxuZXhwb3J0IHsgTWVudSBhcyBkZWZhdWx0IH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1tZW51LmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js":
/*!*******************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/x.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ X; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst X = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"X\", [\n  [\"path\", { d: \"M18 6 6 18\", key: \"1bl5f8\" }],\n  [\"path\", { d: \"m6 6 12 12\", key: \"d8bk6v\" }]\n]);\n\n\n//# sourceMappingURL=x.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMveC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFc0Q7O0FBRXRELFVBQVUsZ0VBQWdCO0FBQzFCLGFBQWEsZ0NBQWdDO0FBQzdDLGFBQWEsZ0NBQWdDO0FBQzdDOztBQUV3QjtBQUN4QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL3guanM/OWU4OCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlIGx1Y2lkZS1yZWFjdCB2MC4zMDMuMCAtIElTQ1xuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIElTQyBsaWNlbnNlLlxuICogU2VlIHRoZSBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKi9cblxuaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbi5qcyc7XG5cbmNvbnN0IFggPSBjcmVhdGVMdWNpZGVJY29uKFwiWFwiLCBbXG4gIFtcInBhdGhcIiwgeyBkOiBcIk0xOCA2IDYgMThcIiwga2V5OiBcIjFibDVmOFwiIH1dLFxuICBbXCJwYXRoXCIsIHsgZDogXCJtNiA2IDEyIDEyXCIsIGtleTogXCJkOGJrNnZcIiB9XVxuXSk7XG5cbmV4cG9ydCB7IFggYXMgZGVmYXVsdCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9eC5qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/landing/landing-page-content.tsx":
/*!*****************************************************!*\
  !*** ./components/landing/landing-page-content.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LandingPageContent: function() { return /* binding */ LandingPageContent; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Menu_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Menu,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Menu_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Menu,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Menu_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Menu,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Menu_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Menu,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Menu_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Menu,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Menu_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Menu,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Menu_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Menu,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Menu_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Menu,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Menu_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Menu,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Menu_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Menu,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Menu_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Menu,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Menu_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Menu,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Menu_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Menu,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/quote.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Menu_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Menu,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Menu_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Menu,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_6__);\n/* __next_internal_client_entry_do_not_use__ LandingPageContent auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Default fallback content\nconst defaultContent = {\n    hero: {\n        enabled: true,\n        title: \"Build Your SaaS Business\",\n        subtitle: \"The Complete Platform\",\n        description: \"Everything you need to launch, grow, and scale your SaaS business. From customer management to billing, we've got you covered.\",\n        primaryCTA: {\n            text: \"Start Free Trial\",\n            link: \"/auth/signup\"\n        },\n        secondaryCTA: {\n            text: \"Watch Demo\",\n            link: \"/demo\"\n        },\n        backgroundImage: \"\",\n        backgroundVideo: \"\"\n    },\n    features: {\n        enabled: true,\n        title: \"Everything You Need\",\n        subtitle: \"Powerful Features\",\n        items: [\n            {\n                id: \"1\",\n                title: \"Customer Management\",\n                description: \"Manage your customers, track interactions, and build lasting relationships.\",\n                icon: \"users\",\n                image: \"\"\n            },\n            {\n                id: \"2\",\n                title: \"Subscription Billing\",\n                description: \"Automated billing, invoicing, and payment processing for recurring revenue.\",\n                icon: \"credit-card\",\n                image: \"\"\n            },\n            {\n                id: \"3\",\n                title: \"Analytics & Reports\",\n                description: \"Comprehensive analytics to track your business performance and growth.\",\n                icon: \"bar-chart\",\n                image: \"\"\n            },\n            {\n                id: \"4\",\n                title: \"Multi-Tenant Architecture\",\n                description: \"Secure data isolation with company-based access control and team management.\",\n                icon: \"building\",\n                image: \"\"\n            },\n            {\n                id: \"5\",\n                title: \"Enterprise Security\",\n                description: \"Role-based access control with audit logs and data encryption.\",\n                icon: \"shield\",\n                image: \"\"\n            },\n            {\n                id: \"6\",\n                title: \"Global Ready\",\n                description: \"Multi-currency support and localization for worldwide businesses.\",\n                icon: \"globe\",\n                image: \"\"\n            }\n        ]\n    },\n    pricing: {\n        enabled: true,\n        title: \"Simple, Transparent Pricing\",\n        subtitle: \"Choose the plan that fits your needs\",\n        showPricingTable: true,\n        customMessage: \"\"\n    },\n    testimonials: {\n        enabled: true,\n        title: \"What Our Customers Say\",\n        subtitle: \"Trusted by thousands of businesses\",\n        items: [\n            {\n                id: \"1\",\n                name: \"John Smith\",\n                role: \"CEO\",\n                company: \"TechCorp\",\n                content: \"This platform has transformed how we manage our SaaS business. The automation features alone have saved us countless hours.\",\n                avatar: \"\",\n                rating: 5\n            },\n            {\n                id: \"2\",\n                name: \"Sarah Johnson\",\n                role: \"Founder\",\n                company: \"StartupXYZ\",\n                content: \"The best investment we've made for our business. The customer management features are incredibly powerful.\",\n                avatar: \"\",\n                rating: 5\n            },\n            {\n                id: \"3\",\n                name: \"Mike Chen\",\n                role: \"CTO\",\n                company: \"InnovateLab\",\n                content: \"Excellent platform with great support. The analytics help us make data-driven decisions every day.\",\n                avatar: \"\",\n                rating: 5\n            }\n        ]\n    },\n    faq: {\n        enabled: true,\n        title: \"Frequently Asked Questions\",\n        subtitle: \"Everything you need to know\",\n        items: [\n            {\n                id: \"1\",\n                question: \"How do I get started?\",\n                answer: \"Simply sign up for a free trial and follow our onboarding guide to set up your account. Our team is here to help you every step of the way.\"\n            },\n            {\n                id: \"2\",\n                question: \"Can I cancel anytime?\",\n                answer: \"Yes, you can cancel your subscription at any time. No long-term contracts or cancellation fees. Your data will remain accessible during the notice period.\"\n            },\n            {\n                id: \"3\",\n                question: \"Is my data secure?\",\n                answer: \"Absolutely. We use enterprise-grade security measures including encryption, regular backups, and compliance with industry standards like SOC 2 and GDPR.\"\n            },\n            {\n                id: \"4\",\n                question: \"Do you offer customer support?\",\n                answer: \"Yes, we provide 24/7 customer support via email, chat, and phone. Our premium plans also include dedicated account managers.\"\n            },\n            {\n                id: \"5\",\n                question: \"Can I integrate with other tools?\",\n                answer: \"Yes, we offer integrations with popular tools like Slack, Zapier, QuickBooks, and many more. We also provide a robust API for custom integrations.\"\n            }\n        ]\n    },\n    cta: {\n        enabled: true,\n        title: \"Ready to Get Started?\",\n        description: \"Join thousands of businesses already using our platform to grow their SaaS.\",\n        buttonText: \"Start Your Free Trial\",\n        buttonLink: \"/auth/signup\",\n        backgroundImage: \"\"\n    },\n    footer: {\n        enabled: true,\n        companyDescription: \"The complete SaaS platform for modern businesses.\",\n        links: [\n            {\n                id: \"1\",\n                title: \"Product\",\n                items: [\n                    {\n                        id: \"1\",\n                        text: \"Features\",\n                        link: \"/features\"\n                    },\n                    {\n                        id: \"2\",\n                        text: \"Pricing\",\n                        link: \"/pricing\"\n                    },\n                    {\n                        id: \"3\",\n                        text: \"Security\",\n                        link: \"/security\"\n                    },\n                    {\n                        id: \"4\",\n                        text: \"Integrations\",\n                        link: \"/integrations\"\n                    }\n                ]\n            },\n            {\n                id: \"2\",\n                title: \"Company\",\n                items: [\n                    {\n                        id: \"1\",\n                        text: \"About\",\n                        link: \"/about\"\n                    },\n                    {\n                        id: \"2\",\n                        text: \"Blog\",\n                        link: \"/blog\"\n                    },\n                    {\n                        id: \"3\",\n                        text: \"Careers\",\n                        link: \"/careers\"\n                    },\n                    {\n                        id: \"4\",\n                        text: \"Contact\",\n                        link: \"/contact\"\n                    }\n                ]\n            },\n            {\n                id: \"3\",\n                title: \"Support\",\n                items: [\n                    {\n                        id: \"1\",\n                        text: \"Help Center\",\n                        link: \"/help\"\n                    },\n                    {\n                        id: \"2\",\n                        text: \"Documentation\",\n                        link: \"/docs\"\n                    },\n                    {\n                        id: \"3\",\n                        text: \"API Reference\",\n                        link: \"/api\"\n                    },\n                    {\n                        id: \"4\",\n                        text: \"Status\",\n                        link: \"/status\"\n                    }\n                ]\n            }\n        ],\n        socialLinks: {\n            twitter: \"https://twitter.com/yourcompany\",\n            linkedin: \"https://linkedin.com/company/yourcompany\",\n            facebook: \"https://facebook.com/yourcompany\",\n            instagram: \"https://instagram.com/yourcompany\"\n        },\n        copyrightText: \"\\xa9 2024 Your Company. All rights reserved.\"\n    },\n    seo: {\n        title: \"SaaS Platform - Build Your Business\",\n        description: \"The complete SaaS platform for modern businesses. Customer management, billing, analytics, and more.\",\n        keywords: \"saas, platform, business, customer management, billing, analytics\",\n        ogImage: \"\"\n    }\n};\nconst getIconComponent = (iconName)=>{\n    const icons = {\n        users: _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Menu_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        \"credit-card\": _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Menu_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        \"bar-chart\": _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Menu_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        building: _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Menu_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        shield: _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Menu_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        globe: _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Menu_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        zap: _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Menu_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        \"file-text\": _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Menu_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n    };\n    return icons[iconName] || _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Menu_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n};\nconst formatStorage = (bytes)=>{\n    const gb = bytes / (1024 * 1024 * 1024);\n    return gb >= 1 ? \"\".concat(gb, \"GB\") : \"\".concat(Math.round(gb * 1024), \"MB\");\n};\nconst getFeatureList = (plan)=>{\n    const features = [];\n    // Add usage limits\n    features.push(\"Up to \".concat(plan.maxUsers, \" users\"));\n    features.push(\"\".concat(plan.maxCompanies, \" \").concat(plan.maxCompanies === 1 ? \"company\" : \"companies\"));\n    features.push(\"\".concat(plan.maxCustomers, \" customers\"));\n    features.push(\"\".concat(plan.maxQuotations, \" quotations/month\"));\n    features.push(\"\".concat(plan.maxInvoices, \" invoices/month\"));\n    features.push(\"\".concat(formatStorage(plan.maxStorage), \" storage\"));\n    // Add feature flags\n    if (plan.features.basicReporting) features.push(\"Basic reporting\");\n    if (plan.features.emailSupport) features.push(\"Email support\");\n    if (plan.features.mobileApp) features.push(\"Mobile app access\");\n    if (plan.features.advancedAnalytics) features.push(\"Advanced analytics\");\n    if (plan.features.customBranding) features.push(\"Custom branding\");\n    if (plan.features.apiAccess) features.push(\"API access\");\n    if (plan.features.prioritySupport) features.push(\"Priority support\");\n    if (plan.features.customIntegrations) features.push(\"Custom integrations\");\n    if (plan.features.advancedSecurity) features.push(\"Advanced security\");\n    if (plan.features.dedicatedManager) features.push(\"Dedicated account manager\");\n    return features;\n};\nfunction LandingPageContent() {\n    var _content_hero, _content_features, _content_testimonials, _content_faq, _content_cta, _content_footer;\n    _s();\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultContent);\n    const [plans, setPlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [openFAQ, setOpenFAQ] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isYearly, setIsYearly] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchData = async ()=>{\n            try {\n                // Fetch CMS content\n                const cmsResponse = await fetch(\"/api/super-admin/cms\");\n                const cmsData = await cmsResponse.json();\n                if (cmsData.success && cmsData.content) {\n                    // Merge with default content to ensure all sections exist\n                    setContent({\n                        ...defaultContent,\n                        ...cmsData.content\n                    });\n                }\n                // Fetch pricing plans\n                const plansResponse = await fetch(\"/api/pricing-plans?publicOnly=true\");\n                const plansData = await plansResponse.json();\n                if (plansData.success) {\n                    // Sort plans by sortOrder and filter active public plans\n                    const activePlans = plansData.data.filter((plan)=>plan.isActive && plan.isPublic).sort((a, b)=>a.sortOrder - b.sortOrder);\n                    setPlans(activePlans);\n                }\n            } catch (error) {\n                console.error(\"Error fetching data:\", error);\n            // Use default content on error\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchData();\n    }, []);\n    const getPrice = (plan)=>{\n        if (isYearly && plan.yearlyPrice) {\n            return plan.yearlyPrice / 12 // Show monthly equivalent\n            ;\n        }\n        return plan.monthlyPrice;\n    };\n    const getYearlyDiscount = (plan)=>{\n        if (!plan.yearlyPrice || !plan.monthlyPrice) return 0;\n        const yearlyMonthly = plan.yearlyPrice / 12;\n        const discount = (plan.monthlyPrice - yearlyMonthly) / plan.monthlyPrice * 100;\n        return Math.round(discount);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 469,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n            lineNumber: 468,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"border-b bg-white/95 backdrop-blur-sm sticky top-0 z-50 shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Menu_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-8 w-8 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: \"SaaS Platform\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"hidden md:flex items-center space-x-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#features\",\n                                            className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                            children: \"Features\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#pricing\",\n                                            className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                            children: \"Pricing\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#testimonials\",\n                                            className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                            children: \"Testimonials\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#faq\",\n                                            className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                            children: \"FAQ\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 497,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/contact\",\n                                            className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                            children: \"Contact\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 487,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/auth/signin\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"ghost\",\n                                                className: \"text-gray-600 hover:text-gray-900\",\n                                                children: \"Sign In\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/auth/signup\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                className: \"bg-blue-600 hover:bg-blue-700\",\n                                                children: \"Get Started Free\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 506,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"md:hidden p-2\",\n                                    onClick: ()=>setMobileMenuOpen(!mobileMenuOpen),\n                                    children: mobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Menu_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-6 w-6 text-gray-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Menu_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-6 w-6 text-gray-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 527,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 479,\n                            columnNumber: 11\n                        }, this),\n                        mobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden mt-4 pb-4 border-t\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex flex-col space-y-4 pt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#features\",\n                                        className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"Features\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#pricing\",\n                                        className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"Pricing\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 543,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#testimonials\",\n                                        className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"Testimonials\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 550,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#faq\",\n                                        className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"FAQ\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 557,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                        href: \"/contact\",\n                                        className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"Contact\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 564,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col space-y-2 pt-4 border-t\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                href: \"/auth/signin\",\n                                                onClick: ()=>setMobileMenuOpen(false),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"ghost\",\n                                                    className: \"w-full justify-start\",\n                                                    children: \"Sign In\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 573,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 572,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                href: \"/auth/signup\",\n                                                onClick: ()=>setMobileMenuOpen(false),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    className: \"w-full bg-blue-600 hover:bg-blue-700\",\n                                                    children: \"Get Started Free\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 578,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 577,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 571,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                lineNumber: 535,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 534,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 478,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 477,\n                columnNumber: 7\n            }, this),\n            ((_content_hero = content.hero) === null || _content_hero === void 0 ? void 0 : _content_hero.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4 relative overflow-hidden\",\n                children: [\n                    content.hero.backgroundImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 z-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_6___default()), {\n                            src: content.hero.backgroundImage,\n                            alt: \"Hero Background\",\n                            fill: true,\n                            className: \"object-cover opacity-20\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 594,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                        lineNumber: 593,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto text-center relative z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto\",\n                            children: [\n                                content.hero.subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                    className: \"mb-4 text-sm px-4 py-2\",\n                                    children: content.hero.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 605,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-5xl md:text-6xl font-bold text-gray-900 mb-6\",\n                                    children: content.hero.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 609,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n                                    children: content.hero.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 612,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-4 justify-center mb-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: content.hero.primaryCTA.link,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                size: \"lg\",\n                                                className: \"text-lg px-8 py-3\",\n                                                children: [\n                                                    content.hero.primaryCTA.text,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Menu_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"ml-2 h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 619,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 617,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 616,\n                                            columnNumber: 17\n                                        }, this),\n                                        content.hero.secondaryCTA.text && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: content.hero.secondaryCTA.link,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                size: \"lg\",\n                                                variant: \"outline\",\n                                                className: \"text-lg px-8 py-3\",\n                                                children: content.hero.secondaryCTA.text\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 623,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 615,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 603,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                        lineNumber: 602,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 591,\n                columnNumber: 9\n            }, this),\n            ((_content_features = content.features) === null || _content_features === void 0 ? void 0 : _content_features.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                    children: content.features.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 640,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                                    children: content.features.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 643,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 639,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: content.features.items.map((feature)=>{\n                                const IconComponent = getIconComponent(feature.icon);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"border-0 shadow-lg hover:shadow-xl transition-shadow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                        className: \"h-6 w-6 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 655,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 654,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-xl\",\n                                                    children: feature.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 657,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 653,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                className: \"text-gray-600\",\n                                                children: feature.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 660,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 659,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, feature.id, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 652,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 648,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 638,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 637,\n                columnNumber: 9\n            }, this),\n            ((_content_testimonials = content.testimonials) === null || _content_testimonials === void 0 ? void 0 : _content_testimonials.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                    children: content.testimonials.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 677,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                                    children: content.testimonials.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 680,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 676,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: content.testimonials.items.map((testimonial)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"border-0 shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    ...Array(testimonial.rating)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Menu_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-5 w-5 text-yellow-400 fill-current\"\n                                                    }, i, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 691,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 689,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Menu_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-8 w-8 text-gray-300 mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 694,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-6 italic\",\n                                                children: [\n                                                    '\"',\n                                                    testimonial.content,\n                                                    '\"'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 695,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    testimonial.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                                        src: testimonial.avatar,\n                                                        alt: testimonial.name,\n                                                        width: 48,\n                                                        height: 48,\n                                                        className: \"rounded-full mr-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 700,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-gray-200 rounded-full mr-4 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Menu_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-6 w-6 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 709,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 708,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-semibold text-gray-900\",\n                                                                children: testimonial.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                lineNumber: 713,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: [\n                                                                    testimonial.role,\n                                                                    \", \",\n                                                                    testimonial.company\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                lineNumber: 714,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 712,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 698,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 688,\n                                        columnNumber: 19\n                                    }, this)\n                                }, testimonial.id, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 687,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 685,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 675,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 674,\n                columnNumber: 9\n            }, this),\n            ((_content_faq = content.faq) === null || _content_faq === void 0 ? void 0 : _content_faq.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto max-w-4xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                    children: content.faq.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 730,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600\",\n                                    children: content.faq.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 733,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 729,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: content.faq.items.map((faq)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"border shadow-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"p-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"w-full p-6 text-left flex items-center justify-between hover:bg-gray-50\",\n                                                onClick: ()=>setOpenFAQ(openFAQ === faq.id ? null : faq.id),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold text-gray-900\",\n                                                        children: faq.question\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 746,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    openFAQ === faq.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Menu_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 748,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Menu_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 750,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 742,\n                                                columnNumber: 21\n                                            }, this),\n                                            openFAQ === faq.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-6 pb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: faq.answer\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 755,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 754,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 741,\n                                        columnNumber: 19\n                                    }, this)\n                                }, faq.id, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 740,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 738,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 728,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 727,\n                columnNumber: 9\n            }, this),\n            ((_content_cta = content.cta) === null || _content_cta === void 0 ? void 0 : _content_cta.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4 bg-blue-600 relative overflow-hidden\",\n                children: [\n                    content.cta.backgroundImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 z-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_6___default()), {\n                            src: content.cta.backgroundImage,\n                            alt: \"CTA Background\",\n                            fill: true,\n                            className: \"object-cover opacity-20\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 771,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                        lineNumber: 770,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto text-center relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl font-bold text-white mb-6\",\n                                children: content.cta.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                lineNumber: 780,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-blue-100 mb-8 max-w-2xl mx-auto\",\n                                children: content.cta.description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                lineNumber: 783,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                href: content.cta.buttonLink,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    size: \"lg\",\n                                    variant: \"secondary\",\n                                    className: \"text-lg px-8 py-3\",\n                                    children: [\n                                        content.cta.buttonText,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Menu_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"ml-2 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 789,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 787,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                lineNumber: 786,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                        lineNumber: 779,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 768,\n                columnNumber: 9\n            }, this),\n            ((_content_footer = content.footer) === null || _content_footer === void 0 ? void 0 : _content_footer.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-gray-900 text-white py-12 px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-4 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Menu_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-6 w-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 803,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-lg font-bold\",\n                                                    children: \"SaaS Platform\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 804,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 802,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400\",\n                                            children: content.footer.companyDescription\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 806,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 801,\n                                    columnNumber: 15\n                                }, this),\n                                content.footer.links.map((linkGroup)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-4\",\n                                                children: linkGroup.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 812,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-2 text-gray-400\",\n                                                children: linkGroup.items.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                            href: link.link,\n                                                            className: \"hover:text-white\",\n                                                            children: link.text\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 816,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, link.id, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 815,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 813,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, linkGroup.id, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 811,\n                                        columnNumber: 17\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 800,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: content.footer.copyrightText\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                lineNumber: 826,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 825,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 799,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 798,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n        lineNumber: 475,\n        columnNumber: 5\n    }, this);\n}\n_s(LandingPageContent, \"Ob77mu/u4HMnkCjOlnHJhwEopZQ=\");\n_c = LandingPageContent;\nvar _c;\n$RefreshReg$(_c, \"LandingPageContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/landing/landing-page-content.tsx\n"));

/***/ })

});