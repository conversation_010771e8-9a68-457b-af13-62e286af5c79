"use strict";(()=>{var e={};e.id=1230,e.ids=[1230],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},32081:e=>{e.exports=require("child_process")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},92571:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>P,originalPathname:()=>_,patchFetch:()=>b,requestAsyncStorage:()=>h,routeModule:()=>g,serverHooks:()=>w,staticGenerationAsyncStorage:()=>I,staticGenerationBailout:()=>x});var a={};t.r(a),t.d(a,{GET:()=>d,POST:()=>m});var s=t(95419),i=t(69108),n=t(99678),o=t(78070),c=t(81355),l=t(3205),u=t(9108),p=t(15922);async function d(e){try{let e=await (0,c.getServerSession)(l.L);if(!e?.user?.companyId)return o.Z.json({success:!1,error:"Unauthorized"},{status:401});let r=await u._.subscription.findFirst({where:{companyId:e.user.companyId,status:{in:["ACTIVE","TRIALING","PAST_DUE"]}},include:{pricingPlan:{select:{id:!0,name:!0,description:!0,monthlyPrice:!0,yearlyPrice:!0,currency:!0,maxUsers:!0,maxCompanies:!0,maxCustomers:!0,maxQuotations:!0,maxInvoices:!0,maxContracts:!0,maxStorage:!0,features:!0,trialDays:!0}}},orderBy:{createdAt:"desc"}});if(!r)return o.Z.json({success:!0,data:{hasActiveSubscription:!1,subscription:null,usage:null}});let[t,a,s,i,n,p]=await Promise.all([u._.user.count({where:{companyId:e.user.companyId}}),u._.customer.count({where:{companyId:e.user.companyId}}),u._.quotation.count({where:{companyId:e.user.companyId}}),u._.invoice.count({where:{companyId:e.user.companyId}}),u._.contract.count({where:{companyId:e.user.companyId}}),Promise.resolve(Math.floor(1e9*Math.random()))]),d={users:{current:t,limit:r.pricingPlan.maxUsers,percentage:Math.round(t/r.pricingPlan.maxUsers*100)},customers:{current:a,limit:r.pricingPlan.maxCustomers,percentage:Math.round(a/r.pricingPlan.maxCustomers*100)},quotations:{current:s,limit:r.pricingPlan.maxQuotations,percentage:Math.round(s/r.pricingPlan.maxQuotations*100)},invoices:{current:i,limit:r.pricingPlan.maxInvoices,percentage:Math.round(i/r.pricingPlan.maxInvoices*100)},contracts:{current:n,limit:r.pricingPlan.maxContracts,percentage:Math.round(n/r.pricingPlan.maxContracts*100)},storage:{current:p,limit:Number(r.pricingPlan.maxStorage),percentage:Math.round(p/Number(r.pricingPlan.maxStorage)*100),currentFormatted:y(p),limitFormatted:y(Number(r.pricingPlan.maxStorage))}},m=new Date,g=r.currentPeriodEnd?Math.ceil((r.currentPeriodEnd.getTime()-m.getTime())/864e5):null;return o.Z.json({success:!0,data:{hasActiveSubscription:!0,subscription:{...r,pricingPlan:{...r.pricingPlan,monthlyPrice:Number(r.pricingPlan.monthlyPrice),yearlyPrice:r.pricingPlan.yearlyPrice?Number(r.pricingPlan.yearlyPrice):null,maxStorage:Number(r.pricingPlan.maxStorage)},daysUntilRenewal:g},usage:d}})}catch(e){return console.error("Error fetching subscription:",e),o.Z.json({success:!1,error:"Failed to fetch subscription"},{status:500})}}async function m(e){try{let r=await (0,c.getServerSession)(l.L);if(!r?.user?.companyId||!r?.user?.email)return o.Z.json({success:!1,error:"Unauthorized"},{status:401});let{planId:t,billingCycle:a="MONTHLY",useStripe:s=!0}=await e.json(),i=await u._.pricingPlan.findUnique({where:{id:t,isActive:!0}});if(!i)return o.Z.json({success:!1,error:"Invalid pricing plan"},{status:400});if(await u._.subscription.findFirst({where:{companyId:r.user.companyId,status:{in:["ACTIVE","TRIALING"]}}}))return o.Z.json({success:!1,error:"Company already has an active subscription"},{status:400});if(0===i.monthlyPrice||!s){let e=new Date,s=i.trialDays>0?new Date(e.getTime()+864e5*i.trialDays):e,n=new Date("YEARLY"===a?s.getTime()+31536e6:s.getTime()+2592e6),c=await u._.subscription.create({data:{companyId:r.user.companyId,pricingPlanId:t,status:i.trialDays>0?"TRIALING":"ACTIVE",billingCycle:a,currentPeriodStart:e,currentPeriodEnd:n,trialEnd:i.trialDays>0?s:null,stripeSubscriptionId:null,stripeCustomerId:null},include:{pricingPlan:!0}});return o.Z.json({success:!0,data:c})}try{let e=await (0,p.hT)(r.user.email);e||(e=await (0,p.Sh)({email:r.user.email,name:r.user.name||void 0,companyId:r.user.companyId}));let t="YEARLY"===a&&i.stripeYearlyPriceId?i.stripeYearlyPriceId:i.stripePriceId;if(!t)return o.Z.json({success:!1,error:"Stripe price not configured for this plan"},{status:400});let s=await (0,p.R)({customerId:e.id,priceId:t,successUrl:"http://localhost:3000/subscription?success=true",cancelUrl:"http://localhost:3000/pricing?canceled=true",trialPeriodDays:i.trialDays>0?i.trialDays:void 0});return o.Z.json({success:!0,data:{checkoutUrl:s.url,sessionId:s.id}})}catch(e){return console.error("Stripe error:",e),o.Z.json({success:!1,error:"Payment processing error"},{status:500})}}catch(e){return console.error("Error creating subscription:",e),o.Z.json({success:!1,error:"Failed to create subscription"},{status:500})}}function y(e){if(0===e)return"0 Bytes";let r=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,r)).toFixed(2))+" "+["Bytes","KB","MB","GB","TB"][r]}let g=new s.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/subscription/route",pathname:"/api/subscription",filename:"route",bundlePath:"app/api/subscription/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\subscription\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:h,staticGenerationAsyncStorage:I,serverHooks:w,headerHooks:P,staticGenerationBailout:x}=g,_="/api/subscription/route";function b(){return(0,n.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:I})}},3205:(e,r,t)=>{t.d(r,{L:()=>l});var a=t(86485),s=t(10375),i=t(50694),n=t(6521),o=t.n(n),c=t(9108);let l={providers:[(0,a.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let r=await c._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),t=r?.companyId;if(!t&&r){let e=await c._.company.findFirst({where:{ownerId:r.id},select:{id:!0}});(t=e?.id)&&await c._.user.update({where:{id:r.id},data:{companyId:t}})}if(!r)return console.log("User not found:",e.email),null;if(!r.password)return console.log("User has no password set:",e.email),null;if(!await o().compare(e.password,r.password))return console.log("Invalid password for user:",e.email),null;return await c._.user.update({where:{id:r.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",r.email),{id:r.id,email:r.email,name:r.name,role:r.role,companyId:t}}catch(e){return console.error("Authentication error:",e),null}}}),(0,s.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,i.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(console.log("JWT callback - user data:",{id:r.id,email:r.email,role:r.role,companyId:r.companyId}),e.role=r.role,e.companyId=r.companyId),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role,e.user.companyId=r.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,r,t)=>{t.d(r,{_:()=>s});let a=require("@prisma/client"),s=globalThis.prisma??new a.PrismaClient},15922:(e,r,t)=>{t.d(r,{$t:()=>l,Ag:()=>s,FL:()=>c,R:()=>o,Sh:()=>n,hT:()=>i});var a=t(91211);if(!process.env.STRIPE_SECRET_KEY)throw Error("STRIPE_SECRET_KEY is not set in environment variables");let s=new a.Z(process.env.STRIPE_SECRET_KEY,{apiVersion:"2024-06-20",typescript:!0}),i=async e=>(await s.customers.list({email:e,limit:1})).data[0]||null,n=async e=>await s.customers.create({email:e.email,name:e.name,metadata:{companyId:e.companyId}}),o=async e=>{let r={customer:e.customerId,payment_method_types:["card"],line_items:[{price:e.priceId,quantity:1}],mode:"subscription",success_url:e.successUrl,cancel_url:e.cancelUrl,allow_promotion_codes:!0};return e.trialPeriodDays&&e.trialPeriodDays>0&&(r.subscription_data={trial_period_days:e.trialPeriodDays}),await s.checkout.sessions.create(r)},c=async e=>await s.billingPortal.sessions.create({customer:e.customerId,return_url:e.returnUrl}),l=(e,r,t)=>s.webhooks.constructEvent(e,r,t)}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[1638,6206,6521,2455,4520,1211],()=>t(92571));module.exports=a})();