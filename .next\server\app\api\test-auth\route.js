"use strict";(()=>{var e={};e.id=6637,e.ids=[6637],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6113:e=>{e.exports=require("crypto")},39557:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>f,originalPathname:()=>v,patchFetch:()=>P,requestAsyncStorage:()=>h,routeModule:()=>c,serverHooks:()=>w,staticGenerationAsyncStorage:()=>m,staticGenerationBailout:()=>g});var a={};t.r(a),t.d(a,{POST:()=>d});var o=t(95419),s=t(69108),n=t(99678),i=t(78070),u=t(9108),l=t(6521),p=t.n(l);async function d(e){try{let{email:r,password:t}=await e.json();console.log("Testing auth for:",r);let a=await u._.user.findUnique({where:{email:r},include:{company:!0,ownedCompany:!0}});if(!a)return i.Z.json({error:"User not found"},{status:404});if(console.log("User found:",{id:a.id,email:a.email,hasPassword:!!a.password}),!a.password)return i.Z.json({error:"No password set"},{status:400});let o=await p().compare(t,a.password);return console.log("Password valid:",o),i.Z.json({success:!0,user:{id:a.id,email:a.email,name:a.name,role:a.role}})}catch(e){return console.error("Auth test error:",e),i.Z.json({error:"Server error"},{status:500})}}let c=new o.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/test-auth/route",pathname:"/api/test-auth",filename:"route",bundlePath:"app/api/test-auth/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\test-auth\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:h,staticGenerationAsyncStorage:m,serverHooks:w,headerHooks:f,staticGenerationBailout:g}=c,v="/api/test-auth/route";function P(){return(0,n.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:m})}},9108:(e,r,t)=>{t.d(r,{_:()=>o});let a=require("@prisma/client"),o=globalThis.prisma??new a.PrismaClient}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[1638,6206,6521],()=>t(39557));module.exports=a})();