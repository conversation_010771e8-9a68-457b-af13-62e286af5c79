import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET /api/super-admin/reports - Get comprehensive analytics and reports
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || session?.user?.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Super admin access required' }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '30' // days
    const reportType = searchParams.get('type') || 'overview'

    const periodDays = parseInt(period)
    const startDate = new Date(Date.now() - periodDays * 24 * 60 * 60 * 1000)

    // Base metrics
    const [
      totalUsers,
      totalCompanies,
      totalRevenue,
      totalActivities,
      activeUsers,
      activeCompanies
    ] = await Promise.all([
      prisma.user.count(),
      prisma.company.count(),
      prisma.subscription.aggregate({
        where: { status: 'ACTIVE' },
        _sum: { amount: true }
      }),
      prisma.activity.count(),
      prisma.user.count({ where: { status: 'ACTIVE' } }),
      prisma.company.count({ where: { status: 'ACTIVE' } })
    ])

    // Time series data for charts
    const timeSeriesData = await Promise.all([
      // User growth over time
      prisma.$queryRaw`
        SELECT 
          DATE(created_at) as date,
          COUNT(*) as count
        FROM users 
        WHERE created_at >= ${startDate}
        GROUP BY DATE(created_at)
        ORDER BY date ASC
      `,
      // Company growth over time
      prisma.$queryRaw`
        SELECT 
          DATE(created_at) as date,
          COUNT(*) as count
        FROM companies 
        WHERE created_at >= ${startDate}
        GROUP BY DATE(created_at)
        ORDER BY date ASC
      `,
      // Revenue over time
      prisma.$queryRaw`
        SELECT 
          DATE(s.created_at) as date,
          SUM(s.amount) as revenue
        FROM subscriptions s
        WHERE s.created_at >= ${startDate} AND s.status = 'ACTIVE'
        GROUP BY DATE(s.created_at)
        ORDER BY date ASC
      `,
      // Activity over time
      prisma.$queryRaw`
        SELECT 
          DATE(created_at) as date,
          COUNT(*) as count
        FROM activities 
        WHERE created_at >= ${startDate}
        GROUP BY DATE(created_at)
        ORDER BY date ASC
      `
    ])

    const [userGrowth, companyGrowth, revenueGrowth, activityGrowth] = timeSeriesData

    // Geographic distribution
    const geographicData = await prisma.company.groupBy({
      by: ['country'],
      _count: { id: true },
      where: { country: { not: null } },
      orderBy: { _count: { id: 'desc' } },
      take: 10
    })

    // Industry distribution
    const industryData = await prisma.company.groupBy({
      by: ['industry'],
      _count: { id: true },
      where: { industry: { not: null } },
      orderBy: { _count: { id: 'desc' } },
      take: 10
    })

    // User role distribution
    const roleData = await prisma.user.groupBy({
      by: ['role'],
      _count: { id: true },
      orderBy: { _count: { id: 'desc' } }
    })

    // Subscription plan distribution
    const planData = await prisma.subscription.groupBy({
      by: ['plan'],
      _count: { id: true },
      _sum: { amount: true },
      orderBy: { _count: { id: 'desc' } }
    })

    // Top performing companies
    const topCompanies = await prisma.company.findMany({
      take: 10,
      include: {
        subscription: {
          select: { amount: true, status: true }
        },
        _count: {
          select: {
            users: true,
            customers: true,
            quotations: true,
            invoices: true,
            activities: true
          }
        }
      },
      orderBy: {
        activities: {
          _count: 'desc'
        }
      }
    })

    // Recent activities summary
    const recentActivities = await prisma.activity.findMany({
      take: 20,
      where: {
        createdAt: { gte: startDate }
      },
      include: {
        company: {
          select: { name: true }
        },
        createdBy: {
          select: { name: true, email: true }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    // Conversion funnel data
    const funnelData = await Promise.all([
      prisma.company.count(), // Total companies
      prisma.company.count({ where: { status: 'ACTIVE' } }), // Active companies
      prisma.subscription.count({ where: { status: 'ACTIVE' } }), // Paid subscriptions
      prisma.subscription.count({ 
        where: { 
          status: 'ACTIVE',
          amount: { gt: 0 }
        } 
      }) // Revenue generating
    ])

    // Performance metrics
    const performanceMetrics = {
      userEngagement: {
        dailyActiveUsers: await prisma.user.count({
          where: {
            lastActiveAt: {
              gte: new Date(Date.now() - 24 * 60 * 60 * 1000)
            }
          }
        }),
        weeklyActiveUsers: await prisma.user.count({
          where: {
            lastActiveAt: {
              gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
            }
          }
        }),
        monthlyActiveUsers: await prisma.user.count({
          where: {
            lastActiveAt: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
            }
          }
        })
      },
      systemHealth: {
        errorRate: await prisma.systemLog.count({
          where: {
            level: 'ERROR',
            createdAt: { gte: startDate }
          }
        }) / Math.max(await prisma.systemLog.count({ where: { createdAt: { gte: startDate } } }), 1) * 100,
        avgResponseTime: 150, // This would come from actual monitoring
        uptime: 99.9 // This would come from actual monitoring
      }
    }

    // Calculate growth rates
    const previousPeriodStart = new Date(startDate.getTime() - periodDays * 24 * 60 * 60 * 1000)
    const previousPeriodUsers = await prisma.user.count({
      where: {
        createdAt: {
          gte: previousPeriodStart,
          lt: startDate
        }
      }
    })

    const currentPeriodUsers = await prisma.user.count({
      where: { createdAt: { gte: startDate } }
    })

    const userGrowthRate = previousPeriodUsers > 0 
      ? ((currentPeriodUsers - previousPeriodUsers) / previousPeriodUsers) * 100 
      : 0

    return NextResponse.json({
      overview: {
        totalUsers,
        totalCompanies,
        totalRevenue: Number(totalRevenue._sum.amount || 0),
        totalActivities,
        activeUsers,
        activeCompanies,
        userGrowthRate,
        conversionRate: totalCompanies > 0 ? (activeCompanies / totalCompanies) * 100 : 0
      },
      timeSeries: {
        userGrowth: (userGrowth as any[]).map(item => ({
          date: item.date,
          value: Number(item.count)
        })),
        companyGrowth: (companyGrowth as any[]).map(item => ({
          date: item.date,
          value: Number(item.count)
        })),
        revenueGrowth: (revenueGrowth as any[]).map(item => ({
          date: item.date,
          value: Number(item.revenue || 0)
        })),
        activityGrowth: (activityGrowth as any[]).map(item => ({
          date: item.date,
          value: Number(item.count)
        }))
      },
      distribution: {
        geographic: geographicData.map(item => ({
          country: item.country,
          count: item._count.id
        })),
        industry: industryData.map(item => ({
          industry: item.industry,
          count: item._count.id
        })),
        roles: roleData.map(item => ({
          role: item.role,
          count: item._count.id
        })),
        plans: planData.map(item => ({
          plan: item.plan,
          count: item._count.id,
          revenue: Number(item._sum.amount || 0)
        }))
      },
      topCompanies: topCompanies.map(company => ({
        id: company.id,
        name: company.name,
        industry: company.industry,
        subscription: company.subscription,
        metrics: {
          users: company._count.users,
          customers: company._count.customers,
          quotations: company._count.quotations,
          invoices: company._count.invoices,
          activities: company._count.activities
        }
      })),
      recentActivities: recentActivities.map(activity => ({
        id: activity.id,
        type: activity.type,
        title: activity.title,
        company: activity.company?.name,
        user: activity.createdBy?.name,
        createdAt: activity.createdAt
      })),
      funnel: {
        totalCompanies: funnelData[0],
        activeCompanies: funnelData[1],
        paidSubscriptions: funnelData[2],
        revenueGenerating: funnelData[3]
      },
      performance: performanceMetrics,
      period: periodDays,
      generatedAt: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error generating reports:', error)
    return NextResponse.json(
      { error: 'Failed to generate reports' },
      { status: 500 }
    )
  }
}
