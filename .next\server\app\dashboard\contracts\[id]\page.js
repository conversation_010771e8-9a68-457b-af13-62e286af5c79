(()=>{var e={};e.id=5724,e.ids=[5724],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},69180:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>o,pages:()=>x,routeModule:()=>h,tree:()=>d});var a=t(50482),r=t(69108),n=t(62563),i=t.n(n),c=t(68300),l={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>c[e]);t.d(s,l);let d=["",{children:["dashboard",{children:["contracts",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,97846)),"C:\\proj\\nextjs-saas\\app\\dashboard\\contracts\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,96339)),"C:\\proj\\nextjs-saas\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,59504)),"C:\\proj\\nextjs-saas\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],x=["C:\\proj\\nextjs-saas\\app\\dashboard\\contracts\\[id]\\page.tsx"],o="/dashboard/contracts/[id]/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/contracts/[id]/page",pathname:"/dashboard/contracts/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},35783:(e,s,t)=>{Promise.resolve().then(t.bind(t,89860))},89860:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>P});var a=t(95344),r=t(3729),n=t(22254),i=t(61351),c=t(16212),l=t(69436),d=t(32845),x=t(7060),o=t(45961),m=t(25545),h=t(98989),j=t(63024),u=t(1960),p=t(36135),N=t(96885),g=t(75695),v=t(38271),f=t(23485),y=t(36341),w=t(18822),b=t(37121),C=t(55794),E=t(66827),D=t(44669),Z=t(20783),S=t.n(Z);function P(){let e=(0,n.useParams)(),s=(0,n.useRouter)(),[t,Z]=(0,r.useState)(null),[P,T]=(0,r.useState)(!0),[A,q]=(0,r.useState)(!1),R=async()=>{try{let t=await fetch(`/api/contracts/${e.id}`);if(!t.ok){if(404===t.status){D.toast.error("Contract not found"),s.push("/dashboard/contracts");return}throw Error("Failed to fetch contract")}let a=await t.json();Z(a)}catch(e){D.toast.error("Failed to load contract details"),console.error("Error fetching contract:",e)}finally{T(!1)}};(0,r.useEffect)(()=>{e.id&&R()},[e.id]);let _=async()=>{if(t&&confirm(`Are you sure you want to delete contract "${t.contractNumber}"?`))try{let e=await fetch(`/api/contracts/${t.id}`,{method:"DELETE"});if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to delete contract")}D.toast.success("Contract deleted successfully"),s.push("/dashboard/contracts")}catch(e){D.toast.error(e instanceof Error?e.message:"Failed to delete contract")}},I=e=>{switch(e){case"DRAFT":return a.jsx(l.C,{variant:"secondary",children:"Draft"});case"REVIEW":return a.jsx(l.C,{variant:"warning",children:"Review"});case"SENT":return a.jsx(l.C,{variant:"info",children:"Sent"});case"SIGNED":return a.jsx(l.C,{variant:"success",children:"Signed"});case"ACTIVE":return a.jsx(l.C,{variant:"success",children:"Active"});case"COMPLETED":return a.jsx(l.C,{variant:"success",children:"Completed"});case"CANCELLED":return a.jsx(l.C,{variant:"destructive",children:"Cancelled"});case"EXPIRED":return a.jsx(l.C,{variant:"destructive",children:"Expired"});default:return a.jsx(l.C,{variant:"secondary",children:e})}},O=e=>{switch(e){case"URGENT":return a.jsx(l.C,{variant:"destructive",children:"Urgent"});case"HIGH":return a.jsx(l.C,{variant:"warning",children:"High"});case"MEDIUM":return a.jsx(l.C,{variant:"info",children:"Medium"});case"LOW":return a.jsx(l.C,{variant:"secondary",children:"Low"});default:return a.jsx(l.C,{variant:"secondary",children:e})}},z=e=>{let s={SERVICE:"bg-blue-100 text-blue-800",PRODUCT:"bg-green-100 text-green-800",SUBSCRIPTION:"bg-purple-100 text-purple-800",MAINTENANCE:"bg-orange-100 text-orange-800",CONSULTING:"bg-indigo-100 text-indigo-800",OTHER:"bg-gray-100 text-gray-800"};return a.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${s[e]||s.OTHER}`,children:e})};if(P)return a.jsx("div",{className:"flex items-center justify-center py-8",children:a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})});if(!t)return(0,a.jsxs)("div",{className:"text-center py-8",children:[a.jsx("p",{className:"text-gray-500",children:"Contract not found"}),a.jsx(c.z,{asChild:!0,className:"mt-4",children:a.jsx(S(),{href:"/dashboard/contracts",children:"Back to Contracts"})})]});let L=t.endDate&&new Date(t.endDate)<new Date,k=t.endDate&&new Date(t.endDate)<new Date(Date.now()+2592e6);return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx(c.z,{variant:"ghost",size:"sm",asChild:!0,children:(0,a.jsxs)(S(),{href:"/dashboard/contracts",children:[a.jsx(j.Z,{className:"h-4 w-4 mr-2"}),"Back to Contracts"]})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(e=>{switch(e){case"SIGNED":case"ACTIVE":case"COMPLETED":return a.jsx(x.Z,{className:"h-5 w-5 text-green-600"});case"EXPIRED":case"CANCELLED":return a.jsx(o.Z,{className:"h-5 w-5 text-red-600"});case"DRAFT":return a.jsx(m.Z,{className:"h-5 w-5 text-gray-600"});default:return a.jsx(h.Z,{className:"h-5 w-5 text-blue-600"})}})(t.status),a.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:t.contractNumber})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[I(t.status),O(t.priority),z(t.type),L&&a.jsx(l.C,{variant:"destructive",children:"Expired"}),k&&!L&&a.jsx(l.C,{variant:"warning",children:"Expiring Soon"})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(c.z,{variant:"outline",children:[a.jsx(u.Z,{className:"h-4 w-4 mr-2"}),"Duplicate"]}),(0,a.jsxs)(c.z,{variant:"outline",children:[a.jsx(p.Z,{className:"h-4 w-4 mr-2"}),"Send for Signature"]}),(0,a.jsxs)(c.z,{variant:"outline",children:[a.jsx(N.Z,{className:"h-4 w-4 mr-2"}),"PDF"]}),(0,a.jsxs)(c.z,{variant:"outline",children:[a.jsx(h.Z,{className:"h-4 w-4 mr-2"}),"Signatures"]}),(0,a.jsxs)(c.z,{variant:"outline",onClick:()=>q(!0),children:[a.jsx(g.Z,{className:"h-4 w-4 mr-2"}),"Edit"]}),(0,a.jsxs)(c.z,{variant:"destructive",onClick:_,disabled:"SIGNED"===t.status||"ACTIVE"===t.status||t._count.signatures>0,children:[a.jsx(v.Z,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,a.jsxs)(i.Zb,{children:[a.jsx(i.Ol,{children:(0,a.jsxs)(i.ll,{className:"flex items-center",children:[a.jsx(h.Z,{className:"h-5 w-5 mr-2"}),"Contract Overview"]})}),(0,a.jsxs)(i.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-semibold",children:t.title}),t.description&&a.jsx("p",{className:"text-gray-600 mt-2",children:t.description})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Contract Number"}),a.jsx("p",{className:"font-medium",children:t.contractNumber})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Type"}),a.jsx("div",{className:"mt-1",children:z(t.type)})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Status"}),a.jsx("div",{className:"mt-1",children:I(t.status)})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Priority"}),a.jsx("div",{className:"mt-1",children:O(t.priority)})]}),t.value&&(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Contract Value"}),(0,a.jsxs)("p",{className:"font-medium text-lg text-green-600",children:[t.currency," ",t.value.toLocaleString()]})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Signature Required"}),a.jsx("div",{className:"flex items-center space-x-2 mt-1",children:t.signatureRequired?(0,a.jsxs)(a.Fragment,{children:[a.jsx(f.Z,{className:"h-4 w-4 text-green-600"}),a.jsx("span",{className:"text-green-600",children:"Yes"})]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(f.Z,{className:"h-4 w-4 text-gray-400"}),a.jsx("span",{className:"text-gray-600",children:"No"})]})})]})]}),t.tags&&t.tags.length>0&&(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500 mb-2",children:"Tags"}),a.jsx("div",{className:"flex flex-wrap gap-2",children:t.tags.map(e=>(0,a.jsxs)(l.C,{variant:"outline",className:"flex items-center space-x-1",children:[a.jsx(y.Z,{className:"h-3 w-3"}),a.jsx("span",{children:e})]},e))})]})]})]}),(0,a.jsxs)(i.Zb,{children:[a.jsx(i.Ol,{children:(0,a.jsxs)(i.ll,{className:"flex items-center",children:[a.jsx(w.Z,{className:"h-5 w-5 mr-2"}),"Customer Information"]})}),(0,a.jsxs)(i.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Customer Name"}),a.jsx("p",{className:"font-medium",children:t.customer.name})]}),t.customer.company&&(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Company"}),a.jsx("p",{className:"font-medium",children:t.customer.company})]}),t.customer.email&&(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Email"}),a.jsx("p",{className:"font-medium",children:t.customer.email})]}),t.customer.phone&&(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Phone"}),a.jsx("p",{className:"font-medium",children:t.customer.phone})]})]}),(t.customer.address||t.customer.city)&&(0,a.jsxs)("div",{className:"pt-4 border-t",children:[a.jsx("p",{className:"text-sm text-gray-500 mb-2",children:"Address"}),(0,a.jsxs)("div",{className:"text-gray-900",children:[t.customer.address&&a.jsx("p",{children:t.customer.address}),a.jsx("p",{children:[t.customer.city,t.customer.state,t.customer.postalCode].filter(Boolean).join(", ")}),t.customer.country&&a.jsx("p",{children:t.customer.country})]})]})]})]}),(t.quotation||t.invoice)&&(0,a.jsxs)(i.Zb,{children:[a.jsx(i.Ol,{children:(0,a.jsxs)(i.ll,{className:"flex items-center",children:[a.jsx(b.Z,{className:"h-5 w-5 mr-2"}),"Related Documents"]})}),(0,a.jsxs)(i.aY,{className:"space-y-4",children:[t.quotation&&(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"font-medium",children:["Quotation: ",t.quotation.quotationNumber]}),a.jsx("p",{className:"text-sm text-gray-500",children:t.quotation.title}),(0,a.jsxs)("p",{className:"text-sm text-green-600 font-medium",children:["Total: $",t.quotation.total.toLocaleString()]})]}),a.jsx(c.z,{variant:"outline",size:"sm",asChild:!0,children:(0,a.jsxs)(S(),{href:`/dashboard/quotations/${t.quotation.id}`,children:[a.jsx(Eye,{className:"h-4 w-4 mr-2"}),"View"]})})]}),t.invoice&&(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"font-medium",children:["Invoice: ",t.invoice.invoiceNumber]}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Status: ",t.invoice.status]}),(0,a.jsxs)("p",{className:"text-sm text-green-600 font-medium",children:["Total: $",t.invoice.total.toLocaleString()]})]}),a.jsx(c.z,{variant:"outline",size:"sm",asChild:!0,children:(0,a.jsxs)(S(),{href:`/dashboard/invoices/${t.invoice.id}`,children:[a.jsx(Eye,{className:"h-4 w-4 mr-2"}),"View"]})})]})]})]}),(t.terms||t.conditions)&&(0,a.jsxs)(i.Zb,{children:[a.jsx(i.Ol,{children:a.jsx(i.ll,{children:"Terms & Conditions"})}),(0,a.jsxs)(i.aY,{className:"space-y-4",children:[t.terms&&(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500 mb-2",children:"Terms & Conditions"}),a.jsx("p",{className:"text-gray-900 whitespace-pre-wrap",children:t.terms})]}),t.conditions&&(0,a.jsxs)("div",{className:"pt-4 border-t",children:[a.jsx("p",{className:"text-sm text-gray-500 mb-2",children:"Additional Conditions"}),a.jsx("p",{className:"text-gray-900 whitespace-pre-wrap",children:t.conditions})]})]})]}),t.notes&&(0,a.jsxs)(i.Zb,{children:[a.jsx(i.Ol,{children:a.jsx(i.ll,{children:"Internal Notes"})}),a.jsx(i.aY,{children:a.jsx("p",{className:"text-gray-900 whitespace-pre-wrap",children:t.notes})})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(i.Zb,{children:[a.jsx(i.Ol,{children:a.jsx(i.ll,{children:"Contract Details"})}),(0,a.jsxs)(i.aY,{className:"space-y-4",children:[t.startDate&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(C.Z,{className:"h-4 w-4 text-gray-400"}),a.jsx("span",{className:"text-sm",children:"Start Date"})]}),a.jsx("span",{className:"font-medium text-sm",children:new Date(t.startDate).toLocaleDateString()})]}),t.endDate&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(C.Z,{className:"h-4 w-4 text-gray-400"}),a.jsx("span",{className:"text-sm",children:"End Date"})]}),a.jsx("span",{className:`font-medium text-sm ${L||k?"text-red-600":""}`,children:new Date(t.endDate).toLocaleDateString()})]}),t.renewalDate&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(E.Z,{className:"h-4 w-4 text-gray-400"}),a.jsx("span",{className:"text-sm",children:"Renewal Date"})]}),a.jsx("span",{className:"font-medium text-sm",children:new Date(t.renewalDate).toLocaleDateString()})]}),t.autoRenewal&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(E.Z,{className:"h-4 w-4 text-green-600"}),a.jsx("span",{className:"text-sm",children:"Auto Renewal"})]}),a.jsx("span",{className:"font-medium text-sm text-green-600",children:t.renewalPeriod?`Every ${t.renewalPeriod} months`:"Enabled"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(w.Z,{className:"h-4 w-4 text-gray-400"}),a.jsx("span",{className:"text-sm",children:"Created By"})]}),a.jsx("span",{className:"font-medium text-sm",children:t.createdBy.name||"Unknown"})]}),t.assignedTo&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(w.Z,{className:"h-4 w-4 text-gray-400"}),a.jsx("span",{className:"text-sm",children:"Assigned To"})]}),a.jsx("span",{className:"font-medium text-sm",children:t.assignedTo.name||"Unknown"})]})]})]}),(0,a.jsxs)(i.Zb,{children:[a.jsx(i.Ol,{children:a.jsx(i.ll,{children:"Activity Summary"})}),(0,a.jsxs)(i.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-sm",children:"Total Activities"}),a.jsx("span",{className:"font-medium",children:t._count.activities})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-sm",children:"Signatures"}),a.jsx("span",{className:"font-medium",children:t._count.signatures})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-sm",children:"Documents"}),a.jsx("span",{className:"font-medium",children:t._count.documents})]})]})]}),(0,a.jsxs)(i.Zb,{children:[a.jsx(i.Ol,{children:a.jsx(i.ll,{children:"Quick Actions"})}),(0,a.jsxs)(i.aY,{className:"space-y-2",children:[(0,a.jsxs)(c.z,{variant:"outline",className:"w-full justify-start",children:[a.jsx(p.Z,{className:"h-4 w-4 mr-2"}),"Send for Signature"]}),(0,a.jsxs)(c.z,{variant:"outline",className:"w-full justify-start",children:[a.jsx(N.Z,{className:"h-4 w-4 mr-2"}),"Download PDF"]}),(0,a.jsxs)(c.z,{variant:"outline",className:"w-full justify-start",children:[a.jsx(h.Z,{className:"h-4 w-4 mr-2"}),"Manage Signatures"]}),(0,a.jsxs)(c.z,{variant:"outline",className:"w-full justify-start",children:[a.jsx(u.Z,{className:"h-4 w-4 mr-2"}),"Duplicate Contract"]})]})]})]})]}),a.jsx(d.R,{isOpen:A,onClose:()=>q(!1),onSuccess:R,contract:t,mode:"edit"})]})}},97846:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>n,__esModule:()=>r,default:()=>i});let a=(0,t(86843).createProxy)(String.raw`C:\proj\nextjs-saas\app\dashboard\contracts\[id]\page.tsx`),{__esModule:r,$$typeof:n}=a,i=a.default}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[1638,7948,6671,4626,7792,2506,7150,2125,5045,5803,5374],()=>t(69180));module.exports=a})();