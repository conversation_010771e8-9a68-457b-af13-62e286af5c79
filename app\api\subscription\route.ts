import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import {
  createStripeCustomer,
  getStripeCustomerByEmail,
  createStripeCheckoutSession
} from '@/lib/stripe'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get current subscription
    const subscription = await prisma.subscription.findFirst({
      where: {
        companyId: session.user.companyId,
        status: { in: ['ACTIVE', 'TRIALING', 'PAST_DUE'] }
      },
      include: {
        pricingPlan: {
          select: {
            id: true,
            name: true,
            description: true,
            monthlyPrice: true,
            yearlyPrice: true,
            currency: true,
            maxUsers: true,
            maxCompanies: true,
            maxCustomers: true,
            maxQuotations: true,
            maxInvoices: true,
            maxContracts: true,
            maxStorage: true,
            features: true,
            trialDays: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    if (!subscription) {
      return NextResponse.json({
        success: true,
        data: {
          hasActiveSubscription: false,
          subscription: null,
          usage: null
        }
      })
    }

    // Calculate usage statistics
    const [
      userCount,
      customerCount,
      quotationCount,
      invoiceCount,
      contractCount,
      storageUsed
    ] = await Promise.all([
      prisma.user.count({
        where: { companyId: session.user.companyId }
      }),
      prisma.customer.count({
        where: { companyId: session.user.companyId }
      }),
      prisma.quotation.count({
        where: { companyId: session.user.companyId }
      }),
      prisma.invoice.count({
        where: { companyId: session.user.companyId }
      }),
      prisma.contract.count({
        where: { companyId: session.user.companyId }
      }),
      // Mock storage calculation - in real app, you'd calculate actual file sizes
      Promise.resolve(Math.floor(Math.random() * 1000000000)) // Random bytes for demo
    ])

    const usage = {
      users: {
        current: userCount,
        limit: subscription.pricingPlan.maxUsers,
        percentage: Math.round((userCount / subscription.pricingPlan.maxUsers) * 100)
      },
      customers: {
        current: customerCount,
        limit: subscription.pricingPlan.maxCustomers,
        percentage: Math.round((customerCount / subscription.pricingPlan.maxCustomers) * 100)
      },
      quotations: {
        current: quotationCount,
        limit: subscription.pricingPlan.maxQuotations,
        percentage: Math.round((quotationCount / subscription.pricingPlan.maxQuotations) * 100)
      },
      invoices: {
        current: invoiceCount,
        limit: subscription.pricingPlan.maxInvoices,
        percentage: Math.round((invoiceCount / subscription.pricingPlan.maxInvoices) * 100)
      },
      contracts: {
        current: contractCount,
        limit: subscription.pricingPlan.maxContracts,
        percentage: Math.round((contractCount / subscription.pricingPlan.maxContracts) * 100)
      },
      storage: {
        current: storageUsed,
        limit: Number(subscription.pricingPlan.maxStorage),
        percentage: Math.round((storageUsed / Number(subscription.pricingPlan.maxStorage)) * 100),
        currentFormatted: formatBytes(storageUsed),
        limitFormatted: formatBytes(Number(subscription.pricingPlan.maxStorage))
      }
    }

    // Calculate days until renewal/trial end
    const now = new Date()
    const daysUntilRenewal = subscription.currentPeriodEnd 
      ? Math.ceil((subscription.currentPeriodEnd.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
      : null

    return NextResponse.json({
      success: true,
      data: {
        hasActiveSubscription: true,
        subscription: {
          ...subscription,
          pricingPlan: {
            ...subscription.pricingPlan,
            monthlyPrice: Number(subscription.pricingPlan.monthlyPrice),
            yearlyPrice: subscription.pricingPlan.yearlyPrice ? Number(subscription.pricingPlan.yearlyPrice) : null,
            maxStorage: Number(subscription.pricingPlan.maxStorage)
          },
          daysUntilRenewal
        },
        usage
      }
    })

  } catch (error) {
    console.error('Error fetching subscription:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch subscription' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.companyId || !session?.user?.email) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { planId, billingCycle = 'MONTHLY', useStripe = true } = body

    // Validate pricing plan
    const pricingPlan = await prisma.pricingPlan.findUnique({
      where: { id: planId, isActive: true }
    })

    if (!pricingPlan) {
      return NextResponse.json(
        { success: false, error: 'Invalid pricing plan' },
        { status: 400 }
      )
    }

    // Check if company already has an active subscription
    const existingSubscription = await prisma.subscription.findFirst({
      where: {
        companyId: session.user.companyId,
        status: { in: ['ACTIVE', 'TRIALING'] }
      }
    })

    if (existingSubscription) {
      return NextResponse.json(
        { success: false, error: 'Company already has an active subscription' },
        { status: 400 }
      )
    }

    // For free plans or when Stripe is disabled, create subscription directly
    if (pricingPlan.monthlyPrice === 0 || !useStripe) {
      const now = new Date()
      const trialEnd = pricingPlan.trialDays > 0
        ? new Date(now.getTime() + pricingPlan.trialDays * 24 * 60 * 60 * 1000)
        : now

      const currentPeriodEnd = billingCycle === 'YEARLY'
        ? new Date(trialEnd.getTime() + 365 * 24 * 60 * 60 * 1000)
        : new Date(trialEnd.getTime() + 30 * 24 * 60 * 60 * 1000)

      const subscription = await prisma.subscription.create({
        data: {
          companyId: session.user.companyId,
          pricingPlanId: planId,
          status: pricingPlan.trialDays > 0 ? 'TRIALING' : 'ACTIVE',
          billingCycle,
          currentPeriodStart: now,
          currentPeriodEnd,
          trialEnd: pricingPlan.trialDays > 0 ? trialEnd : null,
          stripeSubscriptionId: null,
          stripeCustomerId: null
        },
        include: {
          pricingPlan: true
        }
      })

      return NextResponse.json({
        success: true,
        data: subscription
      })
    }

    // For paid plans with Stripe integration
    try {
      // Get or create Stripe customer
      let stripeCustomer = await getStripeCustomerByEmail(session.user.email)

      if (!stripeCustomer) {
        stripeCustomer = await createStripeCustomer({
          email: session.user.email,
          name: session.user.name || undefined,
          companyId: session.user.companyId
        })
      }

      // Determine the correct price ID
      const priceId = billingCycle === 'YEARLY' && pricingPlan.stripeYearlyPriceId
        ? pricingPlan.stripeYearlyPriceId
        : pricingPlan.stripePriceId

      if (!priceId) {
        return NextResponse.json(
          { success: false, error: 'Stripe price not configured for this plan' },
          { status: 400 }
        )
      }

      // Create Stripe checkout session
      const checkoutSession = await createStripeCheckoutSession({
        customerId: stripeCustomer.id,
        priceId,
        successUrl: `${process.env.NEXTAUTH_URL}/subscription?success=true`,
        cancelUrl: `${process.env.NEXTAUTH_URL}/pricing?canceled=true`,
        trialPeriodDays: pricingPlan.trialDays > 0 ? pricingPlan.trialDays : undefined
      })

      return NextResponse.json({
        success: true,
        data: {
          checkoutUrl: checkoutSession.url,
          sessionId: checkoutSession.id
        }
      })

    } catch (stripeError) {
      console.error('Stripe error:', stripeError)
      return NextResponse.json(
        { success: false, error: 'Payment processing error' },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('Error creating subscription:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create subscription' },
      { status: 500 }
    )
  }
}

// Helper function to format bytes
function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
