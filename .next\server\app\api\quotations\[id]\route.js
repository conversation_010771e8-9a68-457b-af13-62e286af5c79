"use strict";(()=>{var e={};e.id=6173,e.ids=[6173],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},27541:(e,t,o)=>{o.r(t),o.d(t,{headerHooks:()=>E,originalPathname:()=>_,patchFetch:()=>T,requestAsyncStorage:()=>f,routeModule:()=>I,serverHooks:()=>x,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>v});var a={};o.r(a),o.d(a,{DELETE:()=>h,GET:()=>q,PUT:()=>w});var i=o(95419),r=o(69108),n=o(99678),s=o(78070),u=o(81355),l=o(3205),d=o(9108),c=o(25252),p=o(52178);let m=c.Ry({id:c.Z_().optional(),description:c.Z_().min(1,"Description is required"),quantity:c.Rx().min(1,"Quantity must be at least 1"),unitPrice:c.Rx().min(0,"Unit price must be positive"),discount:c.Rx().min(0).max(100).optional().default(0),taxRate:c.Rx().min(0).max(100).optional().default(0)}),y=c.Ry({title:c.Z_().min(1,"Title is required").optional(),description:c.Z_().optional().nullable(),customerId:c.Z_().min(1,"Customer is required").optional(),status:c.Km(["DRAFT","SENT","VIEWED","ACCEPTED","REJECTED","EXPIRED"]).optional(),validUntil:c.Z_().optional().nullable(),terms:c.Z_().optional().nullable(),notes:c.Z_().optional().nullable(),items:c.IX(m).min(1,"At least one item is required").optional(),taxRate:c.Rx().min(0).max(100).optional(),discountType:c.Km(["PERCENTAGE","FIXED"]).optional(),discountValue:c.Rx().min(0).optional()});async function q(e,{params:t}){try{let e=await (0,u.getServerSession)(l.L);if(!e?.user?.id)return s.Z.json({error:"Unauthorized"},{status:401});let o=await d._.quotation.findFirst({where:{id:t.id,companyId:e.user.companyId||void 0},include:{customer:{select:{id:!0,name:!0,email:!0,company:!0,phone:!0,address:!0,city:!0,state:!0,country:!0,postalCode:!0}},createdBy:{select:{name:!0,email:!0}},items:{orderBy:{createdAt:"asc"}},activities:{orderBy:{createdAt:"desc"},take:10,include:{createdBy:{select:{name:!0}}}},_count:{select:{activities:!0}}}});if(!o)return s.Z.json({error:"Quotation not found"},{status:404});let a=o.items.reduce((e,t)=>{let o=t.quantity*t.unitPrice,a=o*t.discount/100,i=o-a,r=i*t.taxRate/100;return e+i+r},0),i=a,r=(i="PERCENTAGE"===o.discountType?a-a*o.discountValue/100:a-o.discountValue)*o.taxRate/100,n=i+r,c={...o,subtotal:Math.round(100*a)/100,total:Math.round(100*n)/100,taxAmount:Math.round(100*r)/100,discountAmount:"PERCENTAGE"===o.discountType?Math.round(a*o.discountValue/100*100)/100:o.discountValue};return s.Z.json(c)}catch(e){return console.error("Error fetching quotation:",e),s.Z.json({error:"Failed to fetch quotation"},{status:500})}}async function w(e,{params:t}){try{let o=await (0,u.getServerSession)(l.L);if(!o?.user?.id)return s.Z.json({error:"Unauthorized"},{status:401});let a=await e.json(),i=y.parse(a),r=await d._.quotation.findFirst({where:{id:t.id,companyId:o.user.companyId||void 0},include:{items:!0}});if(!r)return s.Z.json({error:"Quotation not found"},{status:404});let n={...i};i.validUntil&&(n.validUntil=new Date(i.validUntil));let c=await d._.$transaction(async e=>{let a=await e.quotation.update({where:{id:t.id},data:n,include:{customer:{select:{id:!0,name:!0,email:!0,company:!0}},createdBy:{select:{name:!0,email:!0}},items:{orderBy:{createdAt:"asc"}}}});if(i.items){await e.quotationItem.deleteMany({where:{quotationId:t.id}}),await e.quotationItem.createMany({data:i.items.map(e=>({...e,quotationId:t.id,companyId:o.user.companyId}))});let n=await e.quotation.findUnique({where:{id:t.id},include:{customer:{select:{id:!0,name:!0,email:!0,company:!0}},createdBy:{select:{name:!0,email:!0}},items:{orderBy:{createdAt:"asc"}}}});return i.status&&i.status!==r.status&&await e.activity.create({data:{type:"STATUS_CHANGE",title:"Quotation Status Updated",description:`Quotation status changed from ${r.status} to ${i.status}`,quotationId:t.id,customerId:a.customerId,companyId:o.user.companyId,createdById:o.user.id}}),n}return a});return s.Z.json(c)}catch(e){if(e instanceof p.jm)return s.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("Error updating quotation:",e),s.Z.json({error:"Failed to update quotation"},{status:500})}}async function h(e,{params:t}){try{let e=await (0,u.getServerSession)(l.L);if(!e?.user?.id)return s.Z.json({error:"Unauthorized"},{status:401});let o=await d._.quotation.findFirst({where:{id:t.id,companyId:e.user.companyId||void 0},include:{_count:{select:{invoices:!0}}}});if(!o)return s.Z.json({error:"Quotation not found"},{status:404});if(o._count.invoices>0)return console.log("Cannot delete quotation - has invoices:",o._count),s.Z.json({error:"Cannot delete quotation with existing invoices",details:o._count},{status:400});return console.log("Deleting quotation:",t.id),await d._.quotation.delete({where:{id:t.id}}),console.log("Quotation deleted successfully:",t.id),s.Z.json({message:"Quotation deleted successfully"})}catch(e){return console.error("Error deleting quotation:",e),s.Z.json({error:"Failed to delete quotation"},{status:500})}}let I=new i.AppRouteRouteModule({definition:{kind:r.x.APP_ROUTE,page:"/api/quotations/[id]/route",pathname:"/api/quotations/[id]",filename:"route",bundlePath:"app/api/quotations/[id]/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\quotations\\[id]\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:f,staticGenerationAsyncStorage:g,serverHooks:x,headerHooks:E,staticGenerationBailout:v}=I,_="/api/quotations/[id]/route";function T(){return(0,n.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:g})}},3205:(e,t,o)=>{o.d(t,{L:()=>l});var a=o(86485),i=o(10375),r=o(50694),n=o(6521),s=o.n(n),u=o(9108);let l={providers:[(0,a.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await u._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),o=t?.companyId;if(!o&&t){let e=await u._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(o=e?.id)&&await u._.user.update({where:{id:t.id},data:{companyId:o}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await s().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await u._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:o}}catch(e){return console.error("Authentication error:",e),null}}}),(0,i.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,r.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,o)=>{o.d(t,{_:()=>i});let a=require("@prisma/client"),i=globalThis.prisma??new a.PrismaClient}};var t=require("../../../../webpack-runtime.js");t.C(e);var o=e=>t(t.s=e),a=t.X(0,[1638,6206,6521,2455,4520,5252],()=>o(27541));module.exports=a})();