"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/quotations/[id]/route";
exports.ids = ["app/api/quotations/[id]/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fquotations%2F%5Bid%5D%2Froute&page=%2Fapi%2Fquotations%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fquotations%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fquotations%2F%5Bid%5D%2Froute&page=%2Fapi%2Fquotations%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fquotations%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_proj_nextjs_saas_app_api_quotations_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/quotations/[id]/route.ts */ \"(rsc)/./app/api/quotations/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/quotations/[id]/route\",\n        pathname: \"/api/quotations/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/quotations/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\api\\\\quotations\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_proj_nextjs_saas_app_api_quotations_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/quotations/[id]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fquotations%2F%5Bid%5D%2Froute&page=%2Fapi%2Fquotations%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fquotations%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/quotations/[id]/route.ts":
/*!******************************************!*\
  !*** ./app/api/quotations/[id]/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/ZodError.js\");\n\n\n\n\n\n// Validation schema for quotation items\nconst quotationItemSchema = zod__WEBPACK_IMPORTED_MODULE_4__.object({\n    id: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional(),\n    description: zod__WEBPACK_IMPORTED_MODULE_4__.string().min(1, \"Description is required\"),\n    quantity: zod__WEBPACK_IMPORTED_MODULE_4__.number().min(1, \"Quantity must be at least 1\"),\n    unitPrice: zod__WEBPACK_IMPORTED_MODULE_4__.number().min(0, \"Unit price must be positive\"),\n    discount: zod__WEBPACK_IMPORTED_MODULE_4__.number().min(0).max(100).optional().default(0),\n    taxRate: zod__WEBPACK_IMPORTED_MODULE_4__.number().min(0).max(100).optional().default(0)\n});\n// Validation schema for quotation update\nconst quotationUpdateSchema = zod__WEBPACK_IMPORTED_MODULE_4__.object({\n    title: zod__WEBPACK_IMPORTED_MODULE_4__.string().min(1, \"Title is required\").optional(),\n    description: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    customerId: zod__WEBPACK_IMPORTED_MODULE_4__.string().min(1, \"Customer is required\").optional(),\n    leadId: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    status: zod__WEBPACK_IMPORTED_MODULE_4__[\"enum\"]([\n        \"DRAFT\",\n        \"SENT\",\n        \"VIEWED\",\n        \"ACCEPTED\",\n        \"REJECTED\",\n        \"EXPIRED\"\n    ]).optional(),\n    validUntil: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    terms: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    notes: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    items: zod__WEBPACK_IMPORTED_MODULE_4__.array(quotationItemSchema).min(1, \"At least one item is required\").optional(),\n    taxRate: zod__WEBPACK_IMPORTED_MODULE_4__.number().min(0).max(100).optional(),\n    discountType: zod__WEBPACK_IMPORTED_MODULE_4__[\"enum\"]([\n        \"PERCENTAGE\",\n        \"FIXED\"\n    ]).optional(),\n    discountValue: zod__WEBPACK_IMPORTED_MODULE_4__.number().min(0).optional()\n});\n// GET /api/quotations/[id] - Get single quotation\nasync function GET(request, { params }) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const quotation = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.quotation.findFirst({\n            where: {\n                id: params.id,\n                companyId: session.user.companyId || undefined\n            },\n            include: {\n                customer: {\n                    select: {\n                        id: true,\n                        name: true,\n                        email: true,\n                        company: true,\n                        phone: true,\n                        address: true,\n                        city: true,\n                        state: true,\n                        country: true,\n                        postalCode: true\n                    }\n                },\n                lead: {\n                    select: {\n                        id: true,\n                        name: true,\n                        status: true\n                    }\n                },\n                createdBy: {\n                    select: {\n                        name: true,\n                        email: true\n                    }\n                },\n                items: {\n                    orderBy: {\n                        createdAt: \"asc\"\n                    }\n                },\n                activities: {\n                    orderBy: {\n                        createdAt: \"desc\"\n                    },\n                    take: 10,\n                    include: {\n                        createdBy: {\n                            select: {\n                                name: true\n                            }\n                        }\n                    }\n                },\n                _count: {\n                    select: {\n                        activities: true\n                    }\n                }\n            }\n        });\n        if (!quotation) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Quotation not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Calculate totals\n        const subtotal = quotation.items.reduce((sum, item)=>{\n            const itemTotal = item.quantity * item.unitPrice;\n            const discountAmount = itemTotal * item.discount / 100;\n            const afterDiscount = itemTotal - discountAmount;\n            const taxAmount = afterDiscount * item.taxRate / 100;\n            return sum + afterDiscount + taxAmount;\n        }, 0);\n        let total = subtotal;\n        if (quotation.discountType === \"PERCENTAGE\") {\n            total = subtotal - subtotal * quotation.discountValue / 100;\n        } else {\n            total = subtotal - quotation.discountValue;\n        }\n        const finalTaxAmount = total * quotation.taxRate / 100;\n        const finalTotal = total + finalTaxAmount;\n        const quotationWithTotals = {\n            ...quotation,\n            subtotal: Math.round(subtotal * 100) / 100,\n            total: Math.round(finalTotal * 100) / 100,\n            taxAmount: Math.round(finalTaxAmount * 100) / 100,\n            discountAmount: quotation.discountType === \"PERCENTAGE\" ? Math.round(subtotal * quotation.discountValue / 100 * 100) / 100 : quotation.discountValue\n        };\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(quotationWithTotals);\n    } catch (error) {\n        console.error(\"Error fetching quotation:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to fetch quotation\"\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT /api/quotations/[id] - Update quotation\nasync function PUT(request, { params }) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const validatedData = quotationUpdateSchema.parse(body);\n        // Check if quotation exists and belongs to user's company\n        const existingQuotation = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.quotation.findFirst({\n            where: {\n                id: params.id,\n                companyId: session.user.companyId || undefined\n            },\n            include: {\n                items: true\n            }\n        });\n        if (!existingQuotation) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Quotation not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Prepare update data\n        const updateData = {\n            ...validatedData\n        };\n        if (validatedData.validUntil) {\n            updateData.validUntil = new Date(validatedData.validUntil);\n        }\n        // Update quotation with items in a transaction\n        const quotation = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.$transaction(async (tx)=>{\n            // Update quotation\n            const updatedQuotation = await tx.quotation.update({\n                where: {\n                    id: params.id\n                },\n                data: updateData,\n                include: {\n                    customer: {\n                        select: {\n                            id: true,\n                            name: true,\n                            email: true,\n                            company: true\n                        }\n                    },\n                    lead: {\n                        select: {\n                            id: true,\n                            name: true,\n                            status: true\n                        }\n                    },\n                    createdBy: {\n                        select: {\n                            name: true,\n                            email: true\n                        }\n                    },\n                    items: {\n                        orderBy: {\n                            createdAt: \"asc\"\n                        }\n                    }\n                }\n            });\n            // Update items if provided\n            if (validatedData.items) {\n                // Delete existing items\n                await tx.quotationItem.deleteMany({\n                    where: {\n                        quotationId: params.id\n                    }\n                });\n                // Create new items\n                await tx.quotationItem.createMany({\n                    data: validatedData.items.map((item)=>({\n                            ...item,\n                            quotationId: params.id,\n                            companyId: session.user.companyId\n                        }))\n                });\n                // Fetch updated quotation with new items\n                const finalQuotation = await tx.quotation.findUnique({\n                    where: {\n                        id: params.id\n                    },\n                    include: {\n                        customer: {\n                            select: {\n                                id: true,\n                                name: true,\n                                email: true,\n                                company: true\n                            }\n                        },\n                        lead: {\n                            select: {\n                                id: true,\n                                name: true,\n                                status: true\n                            }\n                        },\n                        createdBy: {\n                            select: {\n                                name: true,\n                                email: true\n                            }\n                        },\n                        items: {\n                            orderBy: {\n                                createdAt: \"asc\"\n                            }\n                        }\n                    }\n                });\n                // Log activity if status changed\n                if (validatedData.status && validatedData.status !== existingQuotation.status) {\n                    await tx.activity.create({\n                        data: {\n                            type: \"STATUS_CHANGE\",\n                            title: \"Quotation Status Updated\",\n                            description: `Quotation status changed from ${existingQuotation.status} to ${validatedData.status}`,\n                            quotationId: params.id,\n                            customerId: updatedQuotation.customerId,\n                            leadId: updatedQuotation.leadId,\n                            companyId: session.user.companyId,\n                            createdById: session.user.id\n                        }\n                    });\n                }\n                return finalQuotation;\n            }\n            return updatedQuotation;\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(quotation);\n    } catch (error) {\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_5__.ZodError) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Validation failed\",\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        console.error(\"Error updating quotation:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to update quotation\"\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE /api/quotations/[id] - Delete quotation\nasync function DELETE(request, { params }) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        // Check if quotation exists and belongs to user's company\n        const quotation = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.quotation.findFirst({\n            where: {\n                id: params.id,\n                companyId: session.user.companyId || undefined\n            },\n            include: {\n                _count: {\n                    select: {\n                        invoices: true\n                    }\n                }\n            }\n        });\n        if (!quotation) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Quotation not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Check if quotation has related invoices\n        if (quotation._count.invoices > 0) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Cannot delete quotation with existing invoices\",\n                details: quotation._count\n            }, {\n                status: 400\n            });\n        }\n        await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.quotation.delete({\n            where: {\n                id: params.id\n            }\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            message: \"Quotation deleted successfully\"\n        });\n    } catch (error) {\n        console.error(\"Error deleting quotation:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to delete quotation\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvYXBpL3F1b3RhdGlvbnMvW2lkXS9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQXVEO0FBQ1g7QUFDSjtBQUNIO0FBQ2Q7QUFFdkIsd0NBQXdDO0FBQ3hDLE1BQU1LLHNCQUFzQkQsdUNBQVEsQ0FBQztJQUNuQ0csSUFBSUgsdUNBQVEsR0FBR0ssUUFBUTtJQUN2QkMsYUFBYU4sdUNBQVEsR0FBR08sR0FBRyxDQUFDLEdBQUc7SUFDL0JDLFVBQVVSLHVDQUFRLEdBQUdPLEdBQUcsQ0FBQyxHQUFHO0lBQzVCRyxXQUFXVix1Q0FBUSxHQUFHTyxHQUFHLENBQUMsR0FBRztJQUM3QkksVUFBVVgsdUNBQVEsR0FBR08sR0FBRyxDQUFDLEdBQUdLLEdBQUcsQ0FBQyxLQUFLUCxRQUFRLEdBQUdRLE9BQU8sQ0FBQztJQUN4REMsU0FBU2QsdUNBQVEsR0FBR08sR0FBRyxDQUFDLEdBQUdLLEdBQUcsQ0FBQyxLQUFLUCxRQUFRLEdBQUdRLE9BQU8sQ0FBQztBQUN6RDtBQUVBLHlDQUF5QztBQUN6QyxNQUFNRSx3QkFBd0JmLHVDQUFRLENBQUM7SUFDckNnQixPQUFPaEIsdUNBQVEsR0FBR08sR0FBRyxDQUFDLEdBQUcscUJBQXFCRixRQUFRO0lBQ3REQyxhQUFhTix1Q0FBUSxHQUFHSyxRQUFRLEdBQUdZLFFBQVE7SUFDM0NDLFlBQVlsQix1Q0FBUSxHQUFHTyxHQUFHLENBQUMsR0FBRyx3QkFBd0JGLFFBQVE7SUFDOURjLFFBQVFuQix1Q0FBUSxHQUFHSyxRQUFRLEdBQUdZLFFBQVE7SUFDdENHLFFBQVFwQix3Q0FBTSxDQUFDO1FBQUM7UUFBUztRQUFRO1FBQVU7UUFBWTtRQUFZO0tBQVUsRUFBRUssUUFBUTtJQUN2RmlCLFlBQVl0Qix1Q0FBUSxHQUFHSyxRQUFRLEdBQUdZLFFBQVE7SUFDMUNNLE9BQU92Qix1Q0FBUSxHQUFHSyxRQUFRLEdBQUdZLFFBQVE7SUFDckNPLE9BQU94Qix1Q0FBUSxHQUFHSyxRQUFRLEdBQUdZLFFBQVE7SUFDckNRLE9BQU96QixzQ0FBTyxDQUFDQyxxQkFBcUJNLEdBQUcsQ0FBQyxHQUFHLGlDQUFpQ0YsUUFBUTtJQUNwRlMsU0FBU2QsdUNBQVEsR0FBR08sR0FBRyxDQUFDLEdBQUdLLEdBQUcsQ0FBQyxLQUFLUCxRQUFRO0lBQzVDc0IsY0FBYzNCLHdDQUFNLENBQUM7UUFBQztRQUFjO0tBQVEsRUFBRUssUUFBUTtJQUN0RHVCLGVBQWU1Qix1Q0FBUSxHQUFHTyxHQUFHLENBQUMsR0FBR0YsUUFBUTtBQUMzQztBQUVBLGtEQUFrRDtBQUMzQyxlQUFld0IsSUFDcEJDLE9BQW9CLEVBQ3BCLEVBQUVDLE1BQU0sRUFBOEI7SUFFdEMsSUFBSTtRQUNGLE1BQU1DLFVBQVUsTUFBTW5DLDJEQUFnQkEsQ0FBQ0Msa0RBQVdBO1FBQ2xELElBQUksQ0FBQ2tDLFNBQVNDLE1BQU05QixJQUFJO1lBQ3RCLE9BQU9QLGtGQUFZQSxDQUFDc0MsSUFBSSxDQUFDO2dCQUFFQyxPQUFPO1lBQWUsR0FBRztnQkFBRWYsUUFBUTtZQUFJO1FBQ3BFO1FBRUEsTUFBTWdCLFlBQVksTUFBTXJDLCtDQUFNQSxDQUFDcUMsU0FBUyxDQUFDQyxTQUFTLENBQUM7WUFDakRDLE9BQU87Z0JBQ0xuQyxJQUFJNEIsT0FBTzVCLEVBQUU7Z0JBQ2JvQyxXQUFXUCxRQUFRQyxJQUFJLENBQUNNLFNBQVMsSUFBSUM7WUFDdkM7WUFDQUMsU0FBUztnQkFDUEMsVUFBVTtvQkFDUkMsUUFBUTt3QkFBRXhDLElBQUk7d0JBQU15QyxNQUFNO3dCQUFNQyxPQUFPO3dCQUFNQyxTQUFTO3dCQUFNQyxPQUFPO3dCQUFNQyxTQUFTO3dCQUFNQyxNQUFNO3dCQUFNQyxPQUFPO3dCQUFNQyxTQUFTO3dCQUFNQyxZQUFZO29CQUFLO2dCQUNuSjtnQkFDQUMsTUFBTTtvQkFDSlYsUUFBUTt3QkFBRXhDLElBQUk7d0JBQU15QyxNQUFNO3dCQUFNeEIsUUFBUTtvQkFBSztnQkFDL0M7Z0JBQ0FrQyxXQUFXO29CQUNUWCxRQUFRO3dCQUFFQyxNQUFNO3dCQUFNQyxPQUFPO29CQUFLO2dCQUNwQztnQkFDQXBCLE9BQU87b0JBQ0w4QixTQUFTO3dCQUFFQyxXQUFXO29CQUFNO2dCQUM5QjtnQkFDQUMsWUFBWTtvQkFDVkYsU0FBUzt3QkFBRUMsV0FBVztvQkFBTztvQkFDN0JFLE1BQU07b0JBQ05qQixTQUFTO3dCQUNQYSxXQUFXOzRCQUFFWCxRQUFRO2dDQUFFQyxNQUFNOzRCQUFLO3dCQUFFO29CQUN0QztnQkFDRjtnQkFDQWUsUUFBUTtvQkFDTmhCLFFBQVE7d0JBQ05jLFlBQVk7b0JBQ2Q7Z0JBQ0Y7WUFDRjtRQUNGO1FBRUEsSUFBSSxDQUFDckIsV0FBVztZQUNkLE9BQU94QyxrRkFBWUEsQ0FBQ3NDLElBQUksQ0FBQztnQkFBRUMsT0FBTztZQUFzQixHQUFHO2dCQUFFZixRQUFRO1lBQUk7UUFDM0U7UUFFQSxtQkFBbUI7UUFDbkIsTUFBTXdDLFdBQVd4QixVQUFVWCxLQUFLLENBQUNvQyxNQUFNLENBQUMsQ0FBQ0MsS0FBS0M7WUFDNUMsTUFBTUMsWUFBWUQsS0FBS3ZELFFBQVEsR0FBR3VELEtBQUtyRCxTQUFTO1lBQ2hELE1BQU11RCxpQkFBaUIsWUFBYUYsS0FBS3BELFFBQVEsR0FBSTtZQUNyRCxNQUFNdUQsZ0JBQWdCRixZQUFZQztZQUNsQyxNQUFNRSxZQUFZLGdCQUFpQkosS0FBS2pELE9BQU8sR0FBSTtZQUNuRCxPQUFPZ0QsTUFBTUksZ0JBQWdCQztRQUMvQixHQUFHO1FBRUgsSUFBSUMsUUFBUVI7UUFDWixJQUFJeEIsVUFBVVQsWUFBWSxLQUFLLGNBQWM7WUFDM0N5QyxRQUFRUixXQUFXLFdBQVl4QixVQUFVUixhQUFhLEdBQUk7UUFDNUQsT0FBTztZQUNMd0MsUUFBUVIsV0FBV3hCLFVBQVVSLGFBQWE7UUFDNUM7UUFFQSxNQUFNeUMsaUJBQWlCLFFBQVNqQyxVQUFVdEIsT0FBTyxHQUFJO1FBQ3JELE1BQU13RCxhQUFhRixRQUFRQztRQUUzQixNQUFNRSxzQkFBc0I7WUFDMUIsR0FBR25DLFNBQVM7WUFDWndCLFVBQVVZLEtBQUtDLEtBQUssQ0FBQ2IsV0FBVyxPQUFPO1lBQ3ZDUSxPQUFPSSxLQUFLQyxLQUFLLENBQUNILGFBQWEsT0FBTztZQUN0Q0gsV0FBV0ssS0FBS0MsS0FBSyxDQUFDSixpQkFBaUIsT0FBTztZQUM5Q0osZ0JBQWdCN0IsVUFBVVQsWUFBWSxLQUFLLGVBQ3ZDNkMsS0FBS0MsS0FBSyxDQUFDLFdBQVlyQyxVQUFVUixhQUFhLEdBQUcsTUFBTyxPQUFPLE1BQy9EUSxVQUFVUixhQUFhO1FBQzdCO1FBRUEsT0FBT2hDLGtGQUFZQSxDQUFDc0MsSUFBSSxDQUFDcUM7SUFDM0IsRUFBRSxPQUFPcEMsT0FBTztRQUNkdUMsUUFBUXZDLEtBQUssQ0FBQyw2QkFBNkJBO1FBQzNDLE9BQU92QyxrRkFBWUEsQ0FBQ3NDLElBQUksQ0FDdEI7WUFBRUMsT0FBTztRQUE0QixHQUNyQztZQUFFZixRQUFRO1FBQUk7SUFFbEI7QUFDRjtBQUVBLDhDQUE4QztBQUN2QyxlQUFldUQsSUFDcEI3QyxPQUFvQixFQUNwQixFQUFFQyxNQUFNLEVBQThCO0lBRXRDLElBQUk7UUFDRixNQUFNQyxVQUFVLE1BQU1uQywyREFBZ0JBLENBQUNDLGtEQUFXQTtRQUNsRCxJQUFJLENBQUNrQyxTQUFTQyxNQUFNOUIsSUFBSTtZQUN0QixPQUFPUCxrRkFBWUEsQ0FBQ3NDLElBQUksQ0FBQztnQkFBRUMsT0FBTztZQUFlLEdBQUc7Z0JBQUVmLFFBQVE7WUFBSTtRQUNwRTtRQUVBLE1BQU13RCxPQUFPLE1BQU05QyxRQUFRSSxJQUFJO1FBQy9CLE1BQU0yQyxnQkFBZ0I5RCxzQkFBc0IrRCxLQUFLLENBQUNGO1FBRWxELDBEQUEwRDtRQUMxRCxNQUFNRyxvQkFBb0IsTUFBTWhGLCtDQUFNQSxDQUFDcUMsU0FBUyxDQUFDQyxTQUFTLENBQUM7WUFDekRDLE9BQU87Z0JBQ0xuQyxJQUFJNEIsT0FBTzVCLEVBQUU7Z0JBQ2JvQyxXQUFXUCxRQUFRQyxJQUFJLENBQUNNLFNBQVMsSUFBSUM7WUFDdkM7WUFDQUMsU0FBUztnQkFDUGhCLE9BQU87WUFDVDtRQUNGO1FBRUEsSUFBSSxDQUFDc0QsbUJBQW1CO1lBQ3RCLE9BQU9uRixrRkFBWUEsQ0FBQ3NDLElBQUksQ0FBQztnQkFBRUMsT0FBTztZQUFzQixHQUFHO2dCQUFFZixRQUFRO1lBQUk7UUFDM0U7UUFFQSxzQkFBc0I7UUFDdEIsTUFBTTRELGFBQWtCO1lBQUUsR0FBR0gsYUFBYTtRQUFDO1FBRTNDLElBQUlBLGNBQWN2RCxVQUFVLEVBQUU7WUFDNUIwRCxXQUFXMUQsVUFBVSxHQUFHLElBQUkyRCxLQUFLSixjQUFjdkQsVUFBVTtRQUMzRDtRQUVBLCtDQUErQztRQUMvQyxNQUFNYyxZQUFZLE1BQU1yQywrQ0FBTUEsQ0FBQ21GLFlBQVksQ0FBQyxPQUFPQztZQUNqRCxtQkFBbUI7WUFDbkIsTUFBTUMsbUJBQW1CLE1BQU1ELEdBQUcvQyxTQUFTLENBQUNpRCxNQUFNLENBQUM7Z0JBQ2pEL0MsT0FBTztvQkFBRW5DLElBQUk0QixPQUFPNUIsRUFBRTtnQkFBQztnQkFDdkJtRixNQUFNTjtnQkFDTnZDLFNBQVM7b0JBQ1BDLFVBQVU7d0JBQ1JDLFFBQVE7NEJBQUV4QyxJQUFJOzRCQUFNeUMsTUFBTTs0QkFBTUMsT0FBTzs0QkFBTUMsU0FBUzt3QkFBSztvQkFDN0Q7b0JBQ0FPLE1BQU07d0JBQ0pWLFFBQVE7NEJBQUV4QyxJQUFJOzRCQUFNeUMsTUFBTTs0QkFBTXhCLFFBQVE7d0JBQUs7b0JBQy9DO29CQUNBa0MsV0FBVzt3QkFDVFgsUUFBUTs0QkFBRUMsTUFBTTs0QkFBTUMsT0FBTzt3QkFBSztvQkFDcEM7b0JBQ0FwQixPQUFPO3dCQUNMOEIsU0FBUzs0QkFBRUMsV0FBVzt3QkFBTTtvQkFDOUI7Z0JBQ0Y7WUFDRjtZQUVBLDJCQUEyQjtZQUMzQixJQUFJcUIsY0FBY3BELEtBQUssRUFBRTtnQkFDdkIsd0JBQXdCO2dCQUN4QixNQUFNMEQsR0FBR0ksYUFBYSxDQUFDQyxVQUFVLENBQUM7b0JBQ2hDbEQsT0FBTzt3QkFBRW1ELGFBQWExRCxPQUFPNUIsRUFBRTtvQkFBQztnQkFDbEM7Z0JBRUEsbUJBQW1CO2dCQUNuQixNQUFNZ0YsR0FBR0ksYUFBYSxDQUFDRyxVQUFVLENBQUM7b0JBQ2hDSixNQUFNVCxjQUFjcEQsS0FBSyxDQUFDa0UsR0FBRyxDQUFDNUIsQ0FBQUEsT0FBUzs0QkFDckMsR0FBR0EsSUFBSTs0QkFDUDBCLGFBQWExRCxPQUFPNUIsRUFBRTs0QkFDdEJvQyxXQUFXUCxRQUFRQyxJQUFJLENBQUNNLFNBQVM7d0JBQ25DO2dCQUNGO2dCQUVBLHlDQUF5QztnQkFDekMsTUFBTXFELGlCQUFpQixNQUFNVCxHQUFHL0MsU0FBUyxDQUFDeUQsVUFBVSxDQUFDO29CQUNuRHZELE9BQU87d0JBQUVuQyxJQUFJNEIsT0FBTzVCLEVBQUU7b0JBQUM7b0JBQ3ZCc0MsU0FBUzt3QkFDUEMsVUFBVTs0QkFDUkMsUUFBUTtnQ0FBRXhDLElBQUk7Z0NBQU15QyxNQUFNO2dDQUFNQyxPQUFPO2dDQUFNQyxTQUFTOzRCQUFLO3dCQUM3RDt3QkFDQU8sTUFBTTs0QkFDSlYsUUFBUTtnQ0FBRXhDLElBQUk7Z0NBQU15QyxNQUFNO2dDQUFNeEIsUUFBUTs0QkFBSzt3QkFDL0M7d0JBQ0FrQyxXQUFXOzRCQUNUWCxRQUFRO2dDQUFFQyxNQUFNO2dDQUFNQyxPQUFPOzRCQUFLO3dCQUNwQzt3QkFDQXBCLE9BQU87NEJBQ0w4QixTQUFTO2dDQUFFQyxXQUFXOzRCQUFNO3dCQUM5QjtvQkFDRjtnQkFDRjtnQkFFQSxpQ0FBaUM7Z0JBQ2pDLElBQUlxQixjQUFjekQsTUFBTSxJQUFJeUQsY0FBY3pELE1BQU0sS0FBSzJELGtCQUFrQjNELE1BQU0sRUFBRTtvQkFDN0UsTUFBTStELEdBQUdXLFFBQVEsQ0FBQ0MsTUFBTSxDQUFDO3dCQUN2QlQsTUFBTTs0QkFDSlUsTUFBTTs0QkFDTmhGLE9BQU87NEJBQ1BWLGFBQWEsQ0FBQyw4QkFBOEIsRUFBRXlFLGtCQUFrQjNELE1BQU0sQ0FBQyxJQUFJLEVBQUV5RCxjQUFjekQsTUFBTSxDQUFDLENBQUM7NEJBQ25HcUUsYUFBYTFELE9BQU81QixFQUFFOzRCQUN0QmUsWUFBWWtFLGlCQUFpQmxFLFVBQVU7NEJBQ3ZDQyxRQUFRaUUsaUJBQWlCakUsTUFBTTs0QkFDL0JvQixXQUFXUCxRQUFRQyxJQUFJLENBQUNNLFNBQVM7NEJBQ2pDMEQsYUFBYWpFLFFBQVFDLElBQUksQ0FBQzlCLEVBQUU7d0JBQzlCO29CQUNGO2dCQUNGO2dCQUVBLE9BQU95RjtZQUNUO1lBRUEsT0FBT1I7UUFDVDtRQUVBLE9BQU94RixrRkFBWUEsQ0FBQ3NDLElBQUksQ0FBQ0U7SUFDM0IsRUFBRSxPQUFPRCxPQUFPO1FBQ2QsSUFBSUEsaUJBQWlCbkMseUNBQVUsRUFBRTtZQUMvQixPQUFPSixrRkFBWUEsQ0FBQ3NDLElBQUksQ0FDdEI7Z0JBQUVDLE9BQU87Z0JBQXFCZ0UsU0FBU2hFLE1BQU1pRSxNQUFNO1lBQUMsR0FDcEQ7Z0JBQUVoRixRQUFRO1lBQUk7UUFFbEI7UUFFQXNELFFBQVF2QyxLQUFLLENBQUMsNkJBQTZCQTtRQUMzQyxPQUFPdkMsa0ZBQVlBLENBQUNzQyxJQUFJLENBQ3RCO1lBQUVDLE9BQU87UUFBNkIsR0FDdEM7WUFBRWYsUUFBUTtRQUFJO0lBRWxCO0FBQ0Y7QUFFQSxpREFBaUQ7QUFDMUMsZUFBZWlGLE9BQ3BCdkUsT0FBb0IsRUFDcEIsRUFBRUMsTUFBTSxFQUE4QjtJQUV0QyxJQUFJO1FBQ0YsTUFBTUMsVUFBVSxNQUFNbkMsMkRBQWdCQSxDQUFDQyxrREFBV0E7UUFDbEQsSUFBSSxDQUFDa0MsU0FBU0MsTUFBTTlCLElBQUk7WUFDdEIsT0FBT1Asa0ZBQVlBLENBQUNzQyxJQUFJLENBQUM7Z0JBQUVDLE9BQU87WUFBZSxHQUFHO2dCQUFFZixRQUFRO1lBQUk7UUFDcEU7UUFFQSwwREFBMEQ7UUFDMUQsTUFBTWdCLFlBQVksTUFBTXJDLCtDQUFNQSxDQUFDcUMsU0FBUyxDQUFDQyxTQUFTLENBQUM7WUFDakRDLE9BQU87Z0JBQ0xuQyxJQUFJNEIsT0FBTzVCLEVBQUU7Z0JBQ2JvQyxXQUFXUCxRQUFRQyxJQUFJLENBQUNNLFNBQVMsSUFBSUM7WUFDdkM7WUFDQUMsU0FBUztnQkFDUGtCLFFBQVE7b0JBQ05oQixRQUFRO3dCQUNOMkQsVUFBVTtvQkFDWjtnQkFDRjtZQUNGO1FBQ0Y7UUFFQSxJQUFJLENBQUNsRSxXQUFXO1lBQ2QsT0FBT3hDLGtGQUFZQSxDQUFDc0MsSUFBSSxDQUFDO2dCQUFFQyxPQUFPO1lBQXNCLEdBQUc7Z0JBQUVmLFFBQVE7WUFBSTtRQUMzRTtRQUVBLDBDQUEwQztRQUMxQyxJQUFJZ0IsVUFBVXVCLE1BQU0sQ0FBQzJDLFFBQVEsR0FBRyxHQUFHO1lBQ2pDLE9BQU8xRyxrRkFBWUEsQ0FBQ3NDLElBQUksQ0FDdEI7Z0JBQ0VDLE9BQU87Z0JBQ1BnRSxTQUFTL0QsVUFBVXVCLE1BQU07WUFDM0IsR0FDQTtnQkFBRXZDLFFBQVE7WUFBSTtRQUVsQjtRQUVBLE1BQU1yQiwrQ0FBTUEsQ0FBQ3FDLFNBQVMsQ0FBQ21FLE1BQU0sQ0FBQztZQUM1QmpFLE9BQU87Z0JBQUVuQyxJQUFJNEIsT0FBTzVCLEVBQUU7WUFBQztRQUN6QjtRQUVBLE9BQU9QLGtGQUFZQSxDQUFDc0MsSUFBSSxDQUFDO1lBQUVzRSxTQUFTO1FBQWlDO0lBQ3ZFLEVBQUUsT0FBT3JFLE9BQU87UUFDZHVDLFFBQVF2QyxLQUFLLENBQUMsNkJBQTZCQTtRQUMzQyxPQUFPdkMsa0ZBQVlBLENBQUNzQyxJQUFJLENBQ3RCO1lBQUVDLE9BQU87UUFBNkIsR0FDdEM7WUFBRWYsUUFBUTtRQUFJO0lBRWxCO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0anMtc2Fhcy1hcHAvLi9hcHAvYXBpL3F1b3RhdGlvbnMvW2lkXS9yb3V0ZS50cz9hODAxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRSZXF1ZXN0LCBOZXh0UmVzcG9uc2UgfSBmcm9tICduZXh0L3NlcnZlcidcbmltcG9ydCB7IGdldFNlcnZlclNlc3Npb24gfSBmcm9tICduZXh0LWF1dGgnXG5pbXBvcnQgeyBhdXRoT3B0aW9ucyB9IGZyb20gJ0AvbGliL2F1dGgnXG5pbXBvcnQgeyBwcmlzbWEgfSBmcm9tICdAL2xpYi9wcmlzbWEnXG5pbXBvcnQgeyB6IH0gZnJvbSAnem9kJ1xuXG4vLyBWYWxpZGF0aW9uIHNjaGVtYSBmb3IgcXVvdGF0aW9uIGl0ZW1zXG5jb25zdCBxdW90YXRpb25JdGVtU2NoZW1hID0gei5vYmplY3Qoe1xuICBpZDogei5zdHJpbmcoKS5vcHRpb25hbCgpLFxuICBkZXNjcmlwdGlvbjogei5zdHJpbmcoKS5taW4oMSwgJ0Rlc2NyaXB0aW9uIGlzIHJlcXVpcmVkJyksXG4gIHF1YW50aXR5OiB6Lm51bWJlcigpLm1pbigxLCAnUXVhbnRpdHkgbXVzdCBiZSBhdCBsZWFzdCAxJyksXG4gIHVuaXRQcmljZTogei5udW1iZXIoKS5taW4oMCwgJ1VuaXQgcHJpY2UgbXVzdCBiZSBwb3NpdGl2ZScpLFxuICBkaXNjb3VudDogei5udW1iZXIoKS5taW4oMCkubWF4KDEwMCkub3B0aW9uYWwoKS5kZWZhdWx0KDApLFxuICB0YXhSYXRlOiB6Lm51bWJlcigpLm1pbigwKS5tYXgoMTAwKS5vcHRpb25hbCgpLmRlZmF1bHQoMClcbn0pXG5cbi8vIFZhbGlkYXRpb24gc2NoZW1hIGZvciBxdW90YXRpb24gdXBkYXRlXG5jb25zdCBxdW90YXRpb25VcGRhdGVTY2hlbWEgPSB6Lm9iamVjdCh7XG4gIHRpdGxlOiB6LnN0cmluZygpLm1pbigxLCAnVGl0bGUgaXMgcmVxdWlyZWQnKS5vcHRpb25hbCgpLFxuICBkZXNjcmlwdGlvbjogei5zdHJpbmcoKS5vcHRpb25hbCgpLm51bGxhYmxlKCksXG4gIGN1c3RvbWVySWQ6IHouc3RyaW5nKCkubWluKDEsICdDdXN0b21lciBpcyByZXF1aXJlZCcpLm9wdGlvbmFsKCksXG4gIGxlYWRJZDogei5zdHJpbmcoKS5vcHRpb25hbCgpLm51bGxhYmxlKCksXG4gIHN0YXR1czogei5lbnVtKFsnRFJBRlQnLCAnU0VOVCcsICdWSUVXRUQnLCAnQUNDRVBURUQnLCAnUkVKRUNURUQnLCAnRVhQSVJFRCddKS5vcHRpb25hbCgpLFxuICB2YWxpZFVudGlsOiB6LnN0cmluZygpLm9wdGlvbmFsKCkubnVsbGFibGUoKSxcbiAgdGVybXM6IHouc3RyaW5nKCkub3B0aW9uYWwoKS5udWxsYWJsZSgpLFxuICBub3Rlczogei5zdHJpbmcoKS5vcHRpb25hbCgpLm51bGxhYmxlKCksXG4gIGl0ZW1zOiB6LmFycmF5KHF1b3RhdGlvbkl0ZW1TY2hlbWEpLm1pbigxLCAnQXQgbGVhc3Qgb25lIGl0ZW0gaXMgcmVxdWlyZWQnKS5vcHRpb25hbCgpLFxuICB0YXhSYXRlOiB6Lm51bWJlcigpLm1pbigwKS5tYXgoMTAwKS5vcHRpb25hbCgpLFxuICBkaXNjb3VudFR5cGU6IHouZW51bShbJ1BFUkNFTlRBR0UnLCAnRklYRUQnXSkub3B0aW9uYWwoKSxcbiAgZGlzY291bnRWYWx1ZTogei5udW1iZXIoKS5taW4oMCkub3B0aW9uYWwoKVxufSlcblxuLy8gR0VUIC9hcGkvcXVvdGF0aW9ucy9baWRdIC0gR2V0IHNpbmdsZSBxdW90YXRpb25cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBHRVQoXG4gIHJlcXVlc3Q6IE5leHRSZXF1ZXN0LFxuICB7IHBhcmFtcyB9OiB7IHBhcmFtczogeyBpZDogc3RyaW5nIH0gfVxuKSB7XG4gIHRyeSB7XG4gICAgY29uc3Qgc2Vzc2lvbiA9IGF3YWl0IGdldFNlcnZlclNlc3Npb24oYXV0aE9wdGlvbnMpXG4gICAgaWYgKCFzZXNzaW9uPy51c2VyPy5pZCkge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgZXJyb3I6ICdVbmF1dGhvcml6ZWQnIH0sIHsgc3RhdHVzOiA0MDEgfSlcbiAgICB9XG5cbiAgICBjb25zdCBxdW90YXRpb24gPSBhd2FpdCBwcmlzbWEucXVvdGF0aW9uLmZpbmRGaXJzdCh7XG4gICAgICB3aGVyZToge1xuICAgICAgICBpZDogcGFyYW1zLmlkLFxuICAgICAgICBjb21wYW55SWQ6IHNlc3Npb24udXNlci5jb21wYW55SWQgfHwgdW5kZWZpbmVkXG4gICAgICB9LFxuICAgICAgaW5jbHVkZToge1xuICAgICAgICBjdXN0b21lcjoge1xuICAgICAgICAgIHNlbGVjdDogeyBpZDogdHJ1ZSwgbmFtZTogdHJ1ZSwgZW1haWw6IHRydWUsIGNvbXBhbnk6IHRydWUsIHBob25lOiB0cnVlLCBhZGRyZXNzOiB0cnVlLCBjaXR5OiB0cnVlLCBzdGF0ZTogdHJ1ZSwgY291bnRyeTogdHJ1ZSwgcG9zdGFsQ29kZTogdHJ1ZSB9XG4gICAgICAgIH0sXG4gICAgICAgIGxlYWQ6IHtcbiAgICAgICAgICBzZWxlY3Q6IHsgaWQ6IHRydWUsIG5hbWU6IHRydWUsIHN0YXR1czogdHJ1ZSB9XG4gICAgICAgIH0sXG4gICAgICAgIGNyZWF0ZWRCeToge1xuICAgICAgICAgIHNlbGVjdDogeyBuYW1lOiB0cnVlLCBlbWFpbDogdHJ1ZSB9XG4gICAgICAgIH0sXG4gICAgICAgIGl0ZW1zOiB7XG4gICAgICAgICAgb3JkZXJCeTogeyBjcmVhdGVkQXQ6ICdhc2MnIH1cbiAgICAgICAgfSxcbiAgICAgICAgYWN0aXZpdGllczoge1xuICAgICAgICAgIG9yZGVyQnk6IHsgY3JlYXRlZEF0OiAnZGVzYycgfSxcbiAgICAgICAgICB0YWtlOiAxMCxcbiAgICAgICAgICBpbmNsdWRlOiB7XG4gICAgICAgICAgICBjcmVhdGVkQnk6IHsgc2VsZWN0OiB7IG5hbWU6IHRydWUgfSB9XG4gICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgICBfY291bnQ6IHtcbiAgICAgICAgICBzZWxlY3Q6IHtcbiAgICAgICAgICAgIGFjdGl2aXRpZXM6IHRydWVcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9KVxuXG4gICAgaWYgKCFxdW90YXRpb24pIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiAnUXVvdGF0aW9uIG5vdCBmb3VuZCcgfSwgeyBzdGF0dXM6IDQwNCB9KVxuICAgIH1cblxuICAgIC8vIENhbGN1bGF0ZSB0b3RhbHNcbiAgICBjb25zdCBzdWJ0b3RhbCA9IHF1b3RhdGlvbi5pdGVtcy5yZWR1Y2UoKHN1bSwgaXRlbSkgPT4ge1xuICAgICAgY29uc3QgaXRlbVRvdGFsID0gaXRlbS5xdWFudGl0eSAqIGl0ZW0udW5pdFByaWNlXG4gICAgICBjb25zdCBkaXNjb3VudEFtb3VudCA9IChpdGVtVG90YWwgKiBpdGVtLmRpc2NvdW50KSAvIDEwMFxuICAgICAgY29uc3QgYWZ0ZXJEaXNjb3VudCA9IGl0ZW1Ub3RhbCAtIGRpc2NvdW50QW1vdW50XG4gICAgICBjb25zdCB0YXhBbW91bnQgPSAoYWZ0ZXJEaXNjb3VudCAqIGl0ZW0udGF4UmF0ZSkgLyAxMDBcbiAgICAgIHJldHVybiBzdW0gKyBhZnRlckRpc2NvdW50ICsgdGF4QW1vdW50XG4gICAgfSwgMClcblxuICAgIGxldCB0b3RhbCA9IHN1YnRvdGFsXG4gICAgaWYgKHF1b3RhdGlvbi5kaXNjb3VudFR5cGUgPT09ICdQRVJDRU5UQUdFJykge1xuICAgICAgdG90YWwgPSBzdWJ0b3RhbCAtIChzdWJ0b3RhbCAqIHF1b3RhdGlvbi5kaXNjb3VudFZhbHVlKSAvIDEwMFxuICAgIH0gZWxzZSB7XG4gICAgICB0b3RhbCA9IHN1YnRvdGFsIC0gcXVvdGF0aW9uLmRpc2NvdW50VmFsdWVcbiAgICB9XG5cbiAgICBjb25zdCBmaW5hbFRheEFtb3VudCA9ICh0b3RhbCAqIHF1b3RhdGlvbi50YXhSYXRlKSAvIDEwMFxuICAgIGNvbnN0IGZpbmFsVG90YWwgPSB0b3RhbCArIGZpbmFsVGF4QW1vdW50XG5cbiAgICBjb25zdCBxdW90YXRpb25XaXRoVG90YWxzID0ge1xuICAgICAgLi4ucXVvdGF0aW9uLFxuICAgICAgc3VidG90YWw6IE1hdGgucm91bmQoc3VidG90YWwgKiAxMDApIC8gMTAwLFxuICAgICAgdG90YWw6IE1hdGgucm91bmQoZmluYWxUb3RhbCAqIDEwMCkgLyAxMDAsXG4gICAgICB0YXhBbW91bnQ6IE1hdGgucm91bmQoZmluYWxUYXhBbW91bnQgKiAxMDApIC8gMTAwLFxuICAgICAgZGlzY291bnRBbW91bnQ6IHF1b3RhdGlvbi5kaXNjb3VudFR5cGUgPT09ICdQRVJDRU5UQUdFJyBcbiAgICAgICAgPyBNYXRoLnJvdW5kKChzdWJ0b3RhbCAqIHF1b3RhdGlvbi5kaXNjb3VudFZhbHVlIC8gMTAwKSAqIDEwMCkgLyAxMDBcbiAgICAgICAgOiBxdW90YXRpb24uZGlzY291bnRWYWx1ZVxuICAgIH1cblxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihxdW90YXRpb25XaXRoVG90YWxzKVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIHF1b3RhdGlvbjonLCBlcnJvcilcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICB7IGVycm9yOiAnRmFpbGVkIHRvIGZldGNoIHF1b3RhdGlvbicgfSxcbiAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgIClcbiAgfVxufVxuXG4vLyBQVVQgL2FwaS9xdW90YXRpb25zL1tpZF0gLSBVcGRhdGUgcXVvdGF0aW9uXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gUFVUKFxuICByZXF1ZXN0OiBOZXh0UmVxdWVzdCxcbiAgeyBwYXJhbXMgfTogeyBwYXJhbXM6IHsgaWQ6IHN0cmluZyB9IH1cbikge1xuICB0cnkge1xuICAgIGNvbnN0IHNlc3Npb24gPSBhd2FpdCBnZXRTZXJ2ZXJTZXNzaW9uKGF1dGhPcHRpb25zKVxuICAgIGlmICghc2Vzc2lvbj8udXNlcj8uaWQpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiAnVW5hdXRob3JpemVkJyB9LCB7IHN0YXR1czogNDAxIH0pXG4gICAgfVxuXG4gICAgY29uc3QgYm9keSA9IGF3YWl0IHJlcXVlc3QuanNvbigpXG4gICAgY29uc3QgdmFsaWRhdGVkRGF0YSA9IHF1b3RhdGlvblVwZGF0ZVNjaGVtYS5wYXJzZShib2R5KVxuXG4gICAgLy8gQ2hlY2sgaWYgcXVvdGF0aW9uIGV4aXN0cyBhbmQgYmVsb25ncyB0byB1c2VyJ3MgY29tcGFueVxuICAgIGNvbnN0IGV4aXN0aW5nUXVvdGF0aW9uID0gYXdhaXQgcHJpc21hLnF1b3RhdGlvbi5maW5kRmlyc3Qoe1xuICAgICAgd2hlcmU6IHtcbiAgICAgICAgaWQ6IHBhcmFtcy5pZCxcbiAgICAgICAgY29tcGFueUlkOiBzZXNzaW9uLnVzZXIuY29tcGFueUlkIHx8IHVuZGVmaW5lZFxuICAgICAgfSxcbiAgICAgIGluY2x1ZGU6IHtcbiAgICAgICAgaXRlbXM6IHRydWVcbiAgICAgIH1cbiAgICB9KVxuXG4gICAgaWYgKCFleGlzdGluZ1F1b3RhdGlvbikge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgZXJyb3I6ICdRdW90YXRpb24gbm90IGZvdW5kJyB9LCB7IHN0YXR1czogNDA0IH0pXG4gICAgfVxuXG4gICAgLy8gUHJlcGFyZSB1cGRhdGUgZGF0YVxuICAgIGNvbnN0IHVwZGF0ZURhdGE6IGFueSA9IHsgLi4udmFsaWRhdGVkRGF0YSB9XG5cbiAgICBpZiAodmFsaWRhdGVkRGF0YS52YWxpZFVudGlsKSB7XG4gICAgICB1cGRhdGVEYXRhLnZhbGlkVW50aWwgPSBuZXcgRGF0ZSh2YWxpZGF0ZWREYXRhLnZhbGlkVW50aWwpXG4gICAgfVxuXG4gICAgLy8gVXBkYXRlIHF1b3RhdGlvbiB3aXRoIGl0ZW1zIGluIGEgdHJhbnNhY3Rpb25cbiAgICBjb25zdCBxdW90YXRpb24gPSBhd2FpdCBwcmlzbWEuJHRyYW5zYWN0aW9uKGFzeW5jICh0eCkgPT4ge1xuICAgICAgLy8gVXBkYXRlIHF1b3RhdGlvblxuICAgICAgY29uc3QgdXBkYXRlZFF1b3RhdGlvbiA9IGF3YWl0IHR4LnF1b3RhdGlvbi51cGRhdGUoe1xuICAgICAgICB3aGVyZTogeyBpZDogcGFyYW1zLmlkIH0sXG4gICAgICAgIGRhdGE6IHVwZGF0ZURhdGEsXG4gICAgICAgIGluY2x1ZGU6IHtcbiAgICAgICAgICBjdXN0b21lcjoge1xuICAgICAgICAgICAgc2VsZWN0OiB7IGlkOiB0cnVlLCBuYW1lOiB0cnVlLCBlbWFpbDogdHJ1ZSwgY29tcGFueTogdHJ1ZSB9XG4gICAgICAgICAgfSxcbiAgICAgICAgICBsZWFkOiB7XG4gICAgICAgICAgICBzZWxlY3Q6IHsgaWQ6IHRydWUsIG5hbWU6IHRydWUsIHN0YXR1czogdHJ1ZSB9XG4gICAgICAgICAgfSxcbiAgICAgICAgICBjcmVhdGVkQnk6IHtcbiAgICAgICAgICAgIHNlbGVjdDogeyBuYW1lOiB0cnVlLCBlbWFpbDogdHJ1ZSB9XG4gICAgICAgICAgfSxcbiAgICAgICAgICBpdGVtczoge1xuICAgICAgICAgICAgb3JkZXJCeTogeyBjcmVhdGVkQXQ6ICdhc2MnIH1cbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH0pXG5cbiAgICAgIC8vIFVwZGF0ZSBpdGVtcyBpZiBwcm92aWRlZFxuICAgICAgaWYgKHZhbGlkYXRlZERhdGEuaXRlbXMpIHtcbiAgICAgICAgLy8gRGVsZXRlIGV4aXN0aW5nIGl0ZW1zXG4gICAgICAgIGF3YWl0IHR4LnF1b3RhdGlvbkl0ZW0uZGVsZXRlTWFueSh7XG4gICAgICAgICAgd2hlcmU6IHsgcXVvdGF0aW9uSWQ6IHBhcmFtcy5pZCB9XG4gICAgICAgIH0pXG5cbiAgICAgICAgLy8gQ3JlYXRlIG5ldyBpdGVtc1xuICAgICAgICBhd2FpdCB0eC5xdW90YXRpb25JdGVtLmNyZWF0ZU1hbnkoe1xuICAgICAgICAgIGRhdGE6IHZhbGlkYXRlZERhdGEuaXRlbXMubWFwKGl0ZW0gPT4gKHtcbiAgICAgICAgICAgIC4uLml0ZW0sXG4gICAgICAgICAgICBxdW90YXRpb25JZDogcGFyYW1zLmlkLFxuICAgICAgICAgICAgY29tcGFueUlkOiBzZXNzaW9uLnVzZXIuY29tcGFueUlkIVxuICAgICAgICAgIH0pKVxuICAgICAgICB9KVxuXG4gICAgICAgIC8vIEZldGNoIHVwZGF0ZWQgcXVvdGF0aW9uIHdpdGggbmV3IGl0ZW1zXG4gICAgICAgIGNvbnN0IGZpbmFsUXVvdGF0aW9uID0gYXdhaXQgdHgucXVvdGF0aW9uLmZpbmRVbmlxdWUoe1xuICAgICAgICAgIHdoZXJlOiB7IGlkOiBwYXJhbXMuaWQgfSxcbiAgICAgICAgICBpbmNsdWRlOiB7XG4gICAgICAgICAgICBjdXN0b21lcjoge1xuICAgICAgICAgICAgICBzZWxlY3Q6IHsgaWQ6IHRydWUsIG5hbWU6IHRydWUsIGVtYWlsOiB0cnVlLCBjb21wYW55OiB0cnVlIH1cbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBsZWFkOiB7XG4gICAgICAgICAgICAgIHNlbGVjdDogeyBpZDogdHJ1ZSwgbmFtZTogdHJ1ZSwgc3RhdHVzOiB0cnVlIH1cbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBjcmVhdGVkQnk6IHtcbiAgICAgICAgICAgICAgc2VsZWN0OiB7IG5hbWU6IHRydWUsIGVtYWlsOiB0cnVlIH1cbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBpdGVtczoge1xuICAgICAgICAgICAgICBvcmRlckJ5OiB7IGNyZWF0ZWRBdDogJ2FzYycgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgfSlcblxuICAgICAgICAvLyBMb2cgYWN0aXZpdHkgaWYgc3RhdHVzIGNoYW5nZWRcbiAgICAgICAgaWYgKHZhbGlkYXRlZERhdGEuc3RhdHVzICYmIHZhbGlkYXRlZERhdGEuc3RhdHVzICE9PSBleGlzdGluZ1F1b3RhdGlvbi5zdGF0dXMpIHtcbiAgICAgICAgICBhd2FpdCB0eC5hY3Rpdml0eS5jcmVhdGUoe1xuICAgICAgICAgICAgZGF0YToge1xuICAgICAgICAgICAgICB0eXBlOiAnU1RBVFVTX0NIQU5HRScsXG4gICAgICAgICAgICAgIHRpdGxlOiAnUXVvdGF0aW9uIFN0YXR1cyBVcGRhdGVkJyxcbiAgICAgICAgICAgICAgZGVzY3JpcHRpb246IGBRdW90YXRpb24gc3RhdHVzIGNoYW5nZWQgZnJvbSAke2V4aXN0aW5nUXVvdGF0aW9uLnN0YXR1c30gdG8gJHt2YWxpZGF0ZWREYXRhLnN0YXR1c31gLFxuICAgICAgICAgICAgICBxdW90YXRpb25JZDogcGFyYW1zLmlkLFxuICAgICAgICAgICAgICBjdXN0b21lcklkOiB1cGRhdGVkUXVvdGF0aW9uLmN1c3RvbWVySWQsXG4gICAgICAgICAgICAgIGxlYWRJZDogdXBkYXRlZFF1b3RhdGlvbi5sZWFkSWQsXG4gICAgICAgICAgICAgIGNvbXBhbnlJZDogc2Vzc2lvbi51c2VyLmNvbXBhbnlJZCEsXG4gICAgICAgICAgICAgIGNyZWF0ZWRCeUlkOiBzZXNzaW9uLnVzZXIuaWRcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9KVxuICAgICAgICB9XG5cbiAgICAgICAgcmV0dXJuIGZpbmFsUXVvdGF0aW9uXG4gICAgICB9XG5cbiAgICAgIHJldHVybiB1cGRhdGVkUXVvdGF0aW9uXG4gICAgfSlcblxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihxdW90YXRpb24pXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgaWYgKGVycm9yIGluc3RhbmNlb2Ygei5ab2RFcnJvcikge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IGVycm9yOiAnVmFsaWRhdGlvbiBmYWlsZWQnLCBkZXRhaWxzOiBlcnJvci5lcnJvcnMgfSxcbiAgICAgICAgeyBzdGF0dXM6IDQwMCB9XG4gICAgICApXG4gICAgfVxuXG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgdXBkYXRpbmcgcXVvdGF0aW9uOicsIGVycm9yKVxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgZXJyb3I6ICdGYWlsZWQgdG8gdXBkYXRlIHF1b3RhdGlvbicgfSxcbiAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgIClcbiAgfVxufVxuXG4vLyBERUxFVEUgL2FwaS9xdW90YXRpb25zL1tpZF0gLSBEZWxldGUgcXVvdGF0aW9uXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gREVMRVRFKFxuICByZXF1ZXN0OiBOZXh0UmVxdWVzdCxcbiAgeyBwYXJhbXMgfTogeyBwYXJhbXM6IHsgaWQ6IHN0cmluZyB9IH1cbikge1xuICB0cnkge1xuICAgIGNvbnN0IHNlc3Npb24gPSBhd2FpdCBnZXRTZXJ2ZXJTZXNzaW9uKGF1dGhPcHRpb25zKVxuICAgIGlmICghc2Vzc2lvbj8udXNlcj8uaWQpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiAnVW5hdXRob3JpemVkJyB9LCB7IHN0YXR1czogNDAxIH0pXG4gICAgfVxuXG4gICAgLy8gQ2hlY2sgaWYgcXVvdGF0aW9uIGV4aXN0cyBhbmQgYmVsb25ncyB0byB1c2VyJ3MgY29tcGFueVxuICAgIGNvbnN0IHF1b3RhdGlvbiA9IGF3YWl0IHByaXNtYS5xdW90YXRpb24uZmluZEZpcnN0KHtcbiAgICAgIHdoZXJlOiB7XG4gICAgICAgIGlkOiBwYXJhbXMuaWQsXG4gICAgICAgIGNvbXBhbnlJZDogc2Vzc2lvbi51c2VyLmNvbXBhbnlJZCB8fCB1bmRlZmluZWRcbiAgICAgIH0sXG4gICAgICBpbmNsdWRlOiB7XG4gICAgICAgIF9jb3VudDoge1xuICAgICAgICAgIHNlbGVjdDoge1xuICAgICAgICAgICAgaW52b2ljZXM6IHRydWVcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9KVxuXG4gICAgaWYgKCFxdW90YXRpb24pIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiAnUXVvdGF0aW9uIG5vdCBmb3VuZCcgfSwgeyBzdGF0dXM6IDQwNCB9KVxuICAgIH1cblxuICAgIC8vIENoZWNrIGlmIHF1b3RhdGlvbiBoYXMgcmVsYXRlZCBpbnZvaWNlc1xuICAgIGlmIChxdW90YXRpb24uX2NvdW50Lmludm9pY2VzID4gMCkge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IFxuICAgICAgICAgIGVycm9yOiAnQ2Fubm90IGRlbGV0ZSBxdW90YXRpb24gd2l0aCBleGlzdGluZyBpbnZvaWNlcycsXG4gICAgICAgICAgZGV0YWlsczogcXVvdGF0aW9uLl9jb3VudFxuICAgICAgICB9LFxuICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgIClcbiAgICB9XG5cbiAgICBhd2FpdCBwcmlzbWEucXVvdGF0aW9uLmRlbGV0ZSh7XG4gICAgICB3aGVyZTogeyBpZDogcGFyYW1zLmlkIH1cbiAgICB9KVxuXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgbWVzc2FnZTogJ1F1b3RhdGlvbiBkZWxldGVkIHN1Y2Nlc3NmdWxseScgfSlcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBkZWxldGluZyBxdW90YXRpb246JywgZXJyb3IpXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgeyBlcnJvcjogJ0ZhaWxlZCB0byBkZWxldGUgcXVvdGF0aW9uJyB9LFxuICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgKVxuICB9XG59XG4iXSwibmFtZXMiOlsiTmV4dFJlc3BvbnNlIiwiZ2V0U2VydmVyU2Vzc2lvbiIsImF1dGhPcHRpb25zIiwicHJpc21hIiwieiIsInF1b3RhdGlvbkl0ZW1TY2hlbWEiLCJvYmplY3QiLCJpZCIsInN0cmluZyIsIm9wdGlvbmFsIiwiZGVzY3JpcHRpb24iLCJtaW4iLCJxdWFudGl0eSIsIm51bWJlciIsInVuaXRQcmljZSIsImRpc2NvdW50IiwibWF4IiwiZGVmYXVsdCIsInRheFJhdGUiLCJxdW90YXRpb25VcGRhdGVTY2hlbWEiLCJ0aXRsZSIsIm51bGxhYmxlIiwiY3VzdG9tZXJJZCIsImxlYWRJZCIsInN0YXR1cyIsImVudW0iLCJ2YWxpZFVudGlsIiwidGVybXMiLCJub3RlcyIsIml0ZW1zIiwiYXJyYXkiLCJkaXNjb3VudFR5cGUiLCJkaXNjb3VudFZhbHVlIiwiR0VUIiwicmVxdWVzdCIsInBhcmFtcyIsInNlc3Npb24iLCJ1c2VyIiwianNvbiIsImVycm9yIiwicXVvdGF0aW9uIiwiZmluZEZpcnN0Iiwid2hlcmUiLCJjb21wYW55SWQiLCJ1bmRlZmluZWQiLCJpbmNsdWRlIiwiY3VzdG9tZXIiLCJzZWxlY3QiLCJuYW1lIiwiZW1haWwiLCJjb21wYW55IiwicGhvbmUiLCJhZGRyZXNzIiwiY2l0eSIsInN0YXRlIiwiY291bnRyeSIsInBvc3RhbENvZGUiLCJsZWFkIiwiY3JlYXRlZEJ5Iiwib3JkZXJCeSIsImNyZWF0ZWRBdCIsImFjdGl2aXRpZXMiLCJ0YWtlIiwiX2NvdW50Iiwic3VidG90YWwiLCJyZWR1Y2UiLCJzdW0iLCJpdGVtIiwiaXRlbVRvdGFsIiwiZGlzY291bnRBbW91bnQiLCJhZnRlckRpc2NvdW50IiwidGF4QW1vdW50IiwidG90YWwiLCJmaW5hbFRheEFtb3VudCIsImZpbmFsVG90YWwiLCJxdW90YXRpb25XaXRoVG90YWxzIiwiTWF0aCIsInJvdW5kIiwiY29uc29sZSIsIlBVVCIsImJvZHkiLCJ2YWxpZGF0ZWREYXRhIiwicGFyc2UiLCJleGlzdGluZ1F1b3RhdGlvbiIsInVwZGF0ZURhdGEiLCJEYXRlIiwiJHRyYW5zYWN0aW9uIiwidHgiLCJ1cGRhdGVkUXVvdGF0aW9uIiwidXBkYXRlIiwiZGF0YSIsInF1b3RhdGlvbkl0ZW0iLCJkZWxldGVNYW55IiwicXVvdGF0aW9uSWQiLCJjcmVhdGVNYW55IiwibWFwIiwiZmluYWxRdW90YXRpb24iLCJmaW5kVW5pcXVlIiwiYWN0aXZpdHkiLCJjcmVhdGUiLCJ0eXBlIiwiY3JlYXRlZEJ5SWQiLCJab2RFcnJvciIsImRldGFpbHMiLCJlcnJvcnMiLCJERUxFVEUiLCJpbnZvaWNlcyIsImRlbGV0ZSIsIm1lc3NhZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/api/quotations/[id]/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/github */ \"(rsc)/./node_modules/next-auth/providers/github.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n\nconst authOptions = {\n    // Using JWT strategy instead of database adapter for now\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                try {\n                    if (!credentials?.email || !credentials?.password) {\n                        console.log(\"Missing credentials\");\n                        return null;\n                    }\n                    console.log(\"Attempting to authenticate user:\", credentials.email);\n                    const user = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.findUnique({\n                        where: {\n                            email: credentials.email\n                        },\n                        select: {\n                            id: true,\n                            email: true,\n                            name: true,\n                            password: true,\n                            role: true,\n                            companyId: true\n                        }\n                    });\n                    // Get the company ID - either as member or owner\n                    let finalCompanyId = user?.companyId;\n                    if (!finalCompanyId && user) {\n                        const ownedCompany = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.company.findFirst({\n                            where: {\n                                ownerId: user.id\n                            },\n                            select: {\n                                id: true\n                            }\n                        });\n                        finalCompanyId = ownedCompany?.id;\n                        // If user is company owner, update their companyId for future queries\n                        if (finalCompanyId) {\n                            await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                                where: {\n                                    id: user.id\n                                },\n                                data: {\n                                    companyId: finalCompanyId\n                                }\n                            });\n                        }\n                    }\n                    if (!user) {\n                        console.log(\"User not found:\", credentials.email);\n                        return null;\n                    }\n                    if (!user.password) {\n                        console.log(\"User has no password set:\", credentials.email);\n                        return null;\n                    }\n                    const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_3___default().compare(credentials.password, user.password);\n                    if (!isPasswordValid) {\n                        console.log(\"Invalid password for user:\", credentials.email);\n                        return null;\n                    }\n                    // Update last login\n                    await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                        where: {\n                            id: user.id\n                        },\n                        data: {\n                            lastLoginAt: new Date(),\n                            loginCount: {\n                                increment: 1\n                            }\n                        }\n                    });\n                    console.log(\"User authenticated successfully:\", user.email);\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name,\n                        role: user.role,\n                        companyId: finalCompanyId\n                    };\n                } catch (error) {\n                    console.error(\"Authentication error:\", error);\n                    return null;\n                }\n            }\n        }),\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            clientId: process.env.GITHUB_CLIENT_ID,\n            clientSecret: process.env.GITHUB_CLIENT_SECRET\n        })\n    ],\n    session: {\n        strategy: \"jwt\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                console.log(\"JWT callback - user data:\", {\n                    id: user.id,\n                    email: user.email,\n                    role: user.role,\n                    companyId: user.companyId\n                });\n                token.role = user.role;\n                token.companyId = user.companyId;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n                session.user.companyId = token.companyId;\n                console.log(\"Session callback - final session:\", {\n                    id: session.user.id,\n                    email: session.user.email,\n                    role: session.user.role,\n                    companyId: session.user.companyId\n                });\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        signUp: \"/auth/signup\",\n        error: \"/auth/error\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zYWFzLWFwcC8uL2xpYi9wcmlzbWEudHM/OTgyMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/zod","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/lru-cache","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fquotations%2F%5Bid%5D%2Froute&page=%2Fapi%2Fquotations%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fquotations%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();