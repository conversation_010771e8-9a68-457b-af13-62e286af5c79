import { NextRequest, NextResponse } from 'next/server'
import bcrypt from 'bcryptjs'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'
import { generateVerificationToken, sendVerificationEmail } from '@/lib/email'

// Validation schema
const signupSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  email: z.string().email('Invalid email address'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one uppercase letter, one lowercase letter, and one number'),
  companyName: z.string().min(1, 'Company name is required'),
  companySize: z.enum(['STARTUP', 'SMALL', 'MEDIUM', 'LARGE', 'ENTERPRISE']),
  industry: z.enum(['TECHNOLOGY', 'FINANCE', 'HEALTHCARE', 'MANUFACTURING', 'RETAIL', 'EDUCATION', 'CONSULTING', 'REAL_ESTATE', 'OTHER']),
  agreeToMarketing: z.boolean().default(false)
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = signupSchema.parse(body)

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: validatedData.email }
    })

    if (existingUser) {
      return NextResponse.json(
        { error: 'User already exists' },
        { status: 400 }
      )
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(validatedData.password, 12)

    // Generate email verification token
    const verificationToken = generateVerificationToken()
    const verificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours

    // Create user and company in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create company first
      const company = await tx.company.create({
        data: {
          name: validatedData.companyName,
          size: validatedData.companySize,
          industry: validatedData.industry,
          status: 'TRIAL', // Start with trial
          trialEndsAt: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days trial
          settings: {
            allowUserInvitations: true,
            requireEmailVerification: true,
            maxUsers: getMaxUsersForSize(validatedData.companySize)
          }
        }
      })

      // Create user as company owner
      const user = await tx.user.create({
        data: {
          firstName: validatedData.firstName,
          lastName: validatedData.lastName,
          name: `${validatedData.firstName} ${validatedData.lastName}`,
          email: validatedData.email,
          password: hashedPassword,
          role: 'OWNER',
          companyId: company.id,
          emailVerified: false,
          emailVerificationToken: verificationToken,
          emailVerificationExpires: verificationExpires,
          marketingOptIn: validatedData.agreeToMarketing,
          onboardingCompleted: false
        }
      })

      // Update company with owner ID
      await tx.company.update({
        where: { id: company.id },
        data: { ownerId: user.id }
      })

      // Create default lead statuses for the company
      await tx.leadStatus.createMany({
        data: [
          { name: 'New', color: '#3B82F6', order: 1, companyId: company.id },
          { name: 'Contacted', color: '#8B5CF6', order: 2, companyId: company.id },
          { name: 'Qualified', color: '#F59E0B', order: 3, companyId: company.id },
          { name: 'Proposal', color: '#10B981', order: 4, companyId: company.id },
          { name: 'Negotiation', color: '#EF4444', order: 5, companyId: company.id },
          { name: 'Converted', color: '#059669', order: 6, companyId: company.id },
          { name: 'Lost', color: '#6B7280', order: 7, companyId: company.id }
        ]
      })

      // Create welcome activity/task for onboarding
      await tx.task.create({
        data: {
          title: 'Welcome to Business SaaS!',
          description: 'Complete your profile setup and explore the dashboard features.',
          status: 'PENDING',
          priority: 'HIGH',
          dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
          assignedToId: user.id,
          companyId: company.id,
          createdById: user.id,
          type: 'ONBOARDING'
        }
      })

      return { user, company }
    })

    // Send verification email
    try {
      await sendVerificationEmail(
        validatedData.email,
        `${validatedData.firstName} ${validatedData.lastName}`,
        verificationToken
      )
    } catch (emailError) {
      console.error('Failed to send verification email:', emailError)
      // Don't fail the signup if email fails, but log it
    }

    // Log the signup event
    console.log(`New user signup: ${validatedData.email} for company: ${validatedData.companyName}`)

    return NextResponse.json({
      message: 'Account created successfully',
      user: {
        id: result.user.id,
        email: result.user.email,
        name: result.user.name,
        emailVerified: result.user.emailVerified
      },
      company: {
        id: result.company.id,
        name: result.company.name,
        status: result.company.status
      }
    }, { status: 201 })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Signup error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Helper function to determine max users based on company size
function getMaxUsersForSize(size: string): number {
  switch (size) {
    case 'STARTUP':
      return 5
    case 'SMALL':
      return 25
    case 'MEDIUM':
      return 100
    case 'LARGE':
      return 500
    case 'ENTERPRISE':
      return 1000
    default:
      return 5
  }
}
