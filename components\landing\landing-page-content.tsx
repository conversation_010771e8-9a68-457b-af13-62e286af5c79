'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import {
  Building2,
  Users,
  FileText,
  CreditCard,
  BarChart3,
  Shield,
  Zap,
  Globe,
  CheckCircle,
  ArrowRight,
  Star,
  Quote,
  ChevronDown,
  ChevronUp,
  Menu,
  X,
  Check,
  Phone,
  Mail,
  MapPin,
  Twitter,
  Linkedin,
  Facebook,
  Instagram,
  Youtube,
  Github,
  ChevronUp as ChevronUpIcon
} from 'lucide-react'
import Link from 'next/link'
import Image from 'next/image'

interface CMSContent {
  hero?: {
    enabled: boolean
    title: string
    subtitle: string
    description: string
    primaryCTA: {
      text: string
      link: string
    }
    secondaryCTA: {
      text: string
      link: string
    }
    backgroundImage: string
    backgroundVideo: string
  }
  features?: {
    enabled: boolean
    title: string
    subtitle: string
    items: Array<{
      id: string
      title: string
      description: string
      icon: string
      image: string
    }>
  }
  pricing?: {
    enabled: boolean
    title: string
    subtitle: string
    showPricingTable: boolean
    customMessage: string
  }
  testimonials?: {
    enabled: boolean
    title: string
    subtitle: string
    items: Array<{
      id: string
      name: string
      role: string
      company: string
      content: string
      avatar: string
      rating: number
    }>
  }
  faq?: {
    enabled: boolean
    title: string
    subtitle: string
    items: Array<{
      id: string
      question: string
      answer: string
    }>
  }
  cta?: {
    enabled: boolean
    title: string
    description: string
    buttonText: string
    buttonLink: string
    backgroundImage: string
  }
  footer?: {
    enabled: boolean
    companyDescription: string
    links: Array<{
      id: string
      title: string
      items: Array<{
        id: string
        text: string
        link: string
      }>
    }>
    socialLinks: {
      twitter: string
      linkedin: string
      facebook: string
      instagram: string
    }
    copyrightText: string
  }
  seo?: {
    title: string
    description: string
    keywords: string
    ogImage: string
  }
}

interface PricingPlan {
  id: string
  name: string
  description: string
  monthlyPrice: number
  yearlyPrice: number
  currency: string
  maxUsers: number
  maxCompanies: number
  maxCustomers: number
  maxQuotations: number
  maxInvoices: number
  maxContracts: number
  maxStorage: number
  isActive: boolean
  isPublic: boolean
  trialDays: number
  sortOrder: number
  features: {
    basicReporting: boolean
    emailSupport: boolean
    mobileApp: boolean
    advancedAnalytics: boolean
    customBranding: boolean
    apiAccess: boolean
    prioritySupport: boolean
    customIntegrations: boolean
    advancedSecurity: boolean
    dedicatedManager: boolean
  }
}

// Default fallback content
const defaultContent: CMSContent = {
  hero: {
    enabled: true,
    title: 'Build Your SaaS Business',
    subtitle: 'The Complete Platform',
    description: 'Everything you need to launch, grow, and scale your SaaS business. From customer management to billing, we\'ve got you covered.',
    primaryCTA: {
      text: 'Start Free Trial',
      link: '/auth/signup'
    },
    secondaryCTA: {
      text: 'Watch Demo',
      link: '/demo'
    },
    backgroundImage: '',
    backgroundVideo: ''
  },
  features: {
    enabled: true,
    title: 'Everything You Need',
    subtitle: 'Powerful Features',
    items: [
      {
        id: '1',
        title: 'Customer Management',
        description: 'Manage your customers, track interactions, and build lasting relationships.',
        icon: 'users',
        image: ''
      },
      {
        id: '2',
        title: 'Subscription Billing',
        description: 'Automated billing, invoicing, and payment processing for recurring revenue.',
        icon: 'credit-card',
        image: ''
      },
      {
        id: '3',
        title: 'Analytics & Reports',
        description: 'Comprehensive analytics to track your business performance and growth.',
        icon: 'bar-chart',
        image: ''
      },
      {
        id: '4',
        title: 'Multi-Tenant Architecture',
        description: 'Secure data isolation with company-based access control and team management.',
        icon: 'building',
        image: ''
      },
      {
        id: '5',
        title: 'Enterprise Security',
        description: 'Role-based access control with audit logs and data encryption.',
        icon: 'shield',
        image: ''
      },
      {
        id: '6',
        title: 'Global Ready',
        description: 'Multi-currency support and localization for worldwide businesses.',
        icon: 'globe',
        image: ''
      }
    ]
  },
  pricing: {
    enabled: true,
    title: 'Simple, Transparent Pricing',
    subtitle: 'Choose the plan that fits your needs',
    showPricingTable: true,
    customMessage: ''
  },
  testimonials: {
    enabled: true,
    title: 'What Our Customers Say',
    subtitle: 'Trusted by thousands of businesses',
    items: [
      {
        id: '1',
        name: 'John Smith',
        role: 'CEO',
        company: 'TechCorp',
        content: 'This platform has transformed how we manage our SaaS business. The automation features alone have saved us countless hours.',
        avatar: '',
        rating: 5
      },
      {
        id: '2',
        name: 'Sarah Johnson',
        role: 'Founder',
        company: 'StartupXYZ',
        content: 'The best investment we\'ve made for our business. The customer management features are incredibly powerful.',
        avatar: '',
        rating: 5
      },
      {
        id: '3',
        name: 'Mike Chen',
        role: 'CTO',
        company: 'InnovateLab',
        content: 'Excellent platform with great support. The analytics help us make data-driven decisions every day.',
        avatar: '',
        rating: 5
      }
    ]
  },
  faq: {
    enabled: true,
    title: 'Frequently Asked Questions',
    subtitle: 'Everything you need to know',
    items: [
      {
        id: '1',
        question: 'How do I get started?',
        answer: 'Simply sign up for a free trial and follow our onboarding guide to set up your account. Our team is here to help you every step of the way.'
      },
      {
        id: '2',
        question: 'Can I cancel anytime?',
        answer: 'Yes, you can cancel your subscription at any time. No long-term contracts or cancellation fees. Your data will remain accessible during the notice period.'
      },
      {
        id: '3',
        question: 'Is my data secure?',
        answer: 'Absolutely. We use enterprise-grade security measures including encryption, regular backups, and compliance with industry standards like SOC 2 and GDPR.'
      },
      {
        id: '4',
        question: 'Do you offer customer support?',
        answer: 'Yes, we provide 24/7 customer support via email, chat, and phone. Our premium plans also include dedicated account managers.'
      },
      {
        id: '5',
        question: 'Can I integrate with other tools?',
        answer: 'Yes, we offer integrations with popular tools like Slack, Zapier, QuickBooks, and many more. We also provide a robust API for custom integrations.'
      }
    ]
  },
  cta: {
    enabled: true,
    title: 'Ready to Get Started?',
    description: 'Join thousands of businesses already using our platform to grow their SaaS.',
    buttonText: 'Start Your Free Trial',
    buttonLink: '/auth/signup',
    backgroundImage: ''
  },
  footer: {
    enabled: true,
    companyDescription: 'The complete SaaS platform for modern businesses.',
    links: [
      {
        id: '1',
        title: 'Product',
        items: [
          { id: '1', text: 'Features', link: '/features' },
          { id: '2', text: 'Pricing', link: '/pricing' },
          { id: '3', text: 'Security', link: '/security' },
          { id: '4', text: 'Integrations', link: '/integrations' }
        ]
      },
      {
        id: '2',
        title: 'Company',
        items: [
          { id: '1', text: 'About', link: '/about' },
          { id: '2', text: 'Blog', link: '/blog' },
          { id: '3', text: 'Careers', link: '/careers' },
          { id: '4', text: 'Contact', link: '/contact' }
        ]
      },
      {
        id: '3',
        title: 'Support',
        items: [
          { id: '1', text: 'Help Center', link: '/help' },
          { id: '2', text: 'Documentation', link: '/docs' },
          { id: '3', text: 'API Reference', link: '/api' },
          { id: '4', text: 'Status', link: '/status' }
        ]
      }
    ],
    socialLinks: {
      twitter: 'https://twitter.com/yourcompany',
      linkedin: 'https://linkedin.com/company/yourcompany',
      facebook: 'https://facebook.com/yourcompany',
      instagram: 'https://instagram.com/yourcompany'
    },
    copyrightText: '© 2024 Your Company. All rights reserved.'
  },
  seo: {
    title: 'SaaS Platform - Build Your Business',
    description: 'The complete SaaS platform for modern businesses. Customer management, billing, analytics, and more.',
    keywords: 'saas, platform, business, customer management, billing, analytics',
    ogImage: ''
  }
}

const getIconComponent = (iconName: string) => {
  const icons: Record<string, any> = {
    users: Users,
    'credit-card': CreditCard,
    'bar-chart': BarChart3,
    building: Building2,
    shield: Shield,
    globe: Globe,
    zap: Zap,
    'file-text': FileText
  }
  return icons[iconName] || Users
}

const formatStorage = (bytes: number) => {
  const gb = bytes / (1024 * 1024 * 1024)
  return gb >= 1 ? `${gb}GB` : `${Math.round(gb * 1024)}MB`
}

const getFeatureList = (plan: PricingPlan) => {
  const features = []

  // Add usage limits
  features.push(`Up to ${plan.maxUsers} users`)
  features.push(`${plan.maxCompanies} ${plan.maxCompanies === 1 ? 'company' : 'companies'}`)
  features.push(`${plan.maxCustomers} customers`)
  features.push(`${plan.maxQuotations} quotations/month`)
  features.push(`${plan.maxInvoices} invoices/month`)
  features.push(`${formatStorage(plan.maxStorage)} storage`)

  // Add feature flags
  if (plan.features.basicReporting) features.push('Basic reporting')
  if (plan.features.emailSupport) features.push('Email support')
  if (plan.features.mobileApp) features.push('Mobile app access')
  if (plan.features.advancedAnalytics) features.push('Advanced analytics')
  if (plan.features.customBranding) features.push('Custom branding')
  if (plan.features.apiAccess) features.push('API access')
  if (plan.features.prioritySupport) features.push('Priority support')
  if (plan.features.customIntegrations) features.push('Custom integrations')
  if (plan.features.advancedSecurity) features.push('Advanced security')
  if (plan.features.dedicatedManager) features.push('Dedicated account manager')

  return features
}

export function LandingPageContent() {
  const [content, setContent] = useState<CMSContent>(defaultContent)
  const [plans, setPlans] = useState<PricingPlan[]>([])
  const [loading, setLoading] = useState(true)
  const [openFAQ, setOpenFAQ] = useState<string | null>(null)
  const [isYearly, setIsYearly] = useState(false)
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [showScrollTop, setShowScrollTop] = useState(false)

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch CMS content from public API
        const cmsResponse = await fetch('/api/cms')
        const cmsData = await cmsResponse.json()

        if (cmsData.success && cmsData.content) {
          // Merge with default content to ensure all sections exist
          setContent({ ...defaultContent, ...cmsData.content })
        }

        // Fetch pricing plans
        const plansResponse = await fetch('/api/pricing-plans?publicOnly=true')
        const plansData = await plansResponse.json()

        if (plansData.success) {
          // Sort plans by sortOrder and filter active public plans
          const activePlans = plansData.data
            .filter((plan: PricingPlan) => plan.isActive && plan.isPublic)
            .sort((a: PricingPlan, b: PricingPlan) => a.sortOrder - b.sortOrder)
          setPlans(activePlans)
        }
      } catch (error) {
        console.error('Error fetching data:', error)
        // Use default content on error
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  // Scroll to top functionality
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 400)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  const getPrice = (plan: PricingPlan) => {
    if (isYearly && plan.yearlyPrice) {
      return plan.yearlyPrice / 12 // Show monthly equivalent
    }
    return plan.monthlyPrice
  }

  const getYearlyDiscount = (plan: PricingPlan) => {
    if (!plan.yearlyPrice || !plan.monthlyPrice) return 0
    const yearlyMonthly = plan.yearlyPrice / 12
    const discount = ((plan.monthlyPrice - yearlyMonthly) / plan.monthlyPrice) * 100
    return Math.round(discount)
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="border-b bg-white/95 backdrop-blur-sm sticky top-0 z-50 shadow-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            {/* Logo */}
            <div className="flex items-center space-x-2">
              <Building2 className="h-8 w-8 text-blue-600" />
              <span className="text-2xl font-bold text-gray-900">SaaS Platform</span>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex items-center space-x-8">
              <a href="#features" className="text-gray-600 hover:text-gray-900 transition-colors">
                Features
              </a>
              <a href="#pricing" className="text-gray-600 hover:text-gray-900 transition-colors">
                Pricing
              </a>
              <a href="#testimonials" className="text-gray-600 hover:text-gray-900 transition-colors">
                Testimonials
              </a>
              <a href="#faq" className="text-gray-600 hover:text-gray-900 transition-colors">
                FAQ
              </a>
              <Link href="/contact" className="text-gray-600 hover:text-gray-900 transition-colors">
                Contact
              </Link>
            </nav>

            {/* Desktop CTA */}
            <div className="hidden md:flex items-center space-x-4">
              <Link href="/auth/signin">
                <Button variant="ghost" className="text-gray-600 hover:text-gray-900">
                  Sign In
                </Button>
              </Link>
              <Link href="/auth/signup">
                <Button className="bg-blue-600 hover:bg-blue-700">
                  Get Started Free
                </Button>
              </Link>
            </div>

            {/* Mobile Menu Button */}
            <button
              className="md:hidden p-2"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              {mobileMenuOpen ? (
                <X className="h-6 w-6 text-gray-600" />
              ) : (
                <Menu className="h-6 w-6 text-gray-600" />
              )}
            </button>
          </div>

          {/* Mobile Menu */}
          {mobileMenuOpen && (
            <div className="md:hidden mt-4 pb-4 border-t">
              <nav className="flex flex-col space-y-4 pt-4">
                <a
                  href="#features"
                  className="text-gray-600 hover:text-gray-900 transition-colors"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  Features
                </a>
                <a
                  href="#pricing"
                  className="text-gray-600 hover:text-gray-900 transition-colors"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  Pricing
                </a>
                <a
                  href="#testimonials"
                  className="text-gray-600 hover:text-gray-900 transition-colors"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  Testimonials
                </a>
                <a
                  href="#faq"
                  className="text-gray-600 hover:text-gray-900 transition-colors"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  FAQ
                </a>
                <Link
                  href="/contact"
                  className="text-gray-600 hover:text-gray-900 transition-colors"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  Contact
                </Link>
                <div className="flex flex-col space-y-2 pt-4 border-t">
                  <Link href="/auth/signin" onClick={() => setMobileMenuOpen(false)}>
                    <Button variant="ghost" className="w-full justify-start">
                      Sign In
                    </Button>
                  </Link>
                  <Link href="/auth/signup" onClick={() => setMobileMenuOpen(false)}>
                    <Button className="w-full bg-blue-600 hover:bg-blue-700">
                      Get Started Free
                    </Button>
                  </Link>
                </div>
              </nav>
            </div>
          )}
        </div>
      </header>

      {/* Hero Section */}
      {content.hero?.enabled && (
        <section className="py-20 px-4 relative overflow-hidden">
          {content.hero.backgroundImage && (
            <div className="absolute inset-0 z-0">
              <Image
                src={content.hero.backgroundImage}
                alt="Hero Background"
                fill
                className="object-cover opacity-20"
              />
            </div>
          )}
          <div className="container mx-auto text-center relative z-10">
            <div className="max-w-4xl mx-auto">
              {content.hero.subtitle && (
                <Badge className="mb-4 text-sm px-4 py-2">
                  {content.hero.subtitle}
                </Badge>
              )}
              <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
                {content.hero.title}
              </h1>
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                {content.hero.description}
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                <Link href={content.hero.primaryCTA.link}>
                  <Button size="lg" className="text-lg px-8 py-3">
                    {content.hero.primaryCTA.text}
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
                {content.hero.secondaryCTA.text && (
                  <Link href={content.hero.secondaryCTA.link}>
                    <Button size="lg" variant="outline" className="text-lg px-8 py-3">
                      {content.hero.secondaryCTA.text}
                    </Button>
                  </Link>
                )}
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Features Section */}
      {content.features?.enabled && (
        <section id="features" className="py-20 px-4 bg-white">
          <div className="container mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">
                {content.features.title}
              </h2>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                {content.features.subtitle}
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {content.features.items.map((feature) => {
                const IconComponent = getIconComponent(feature.icon)
                return (
                  <Card key={feature.id} className="border-0 shadow-lg hover:shadow-xl transition-shadow">
                    <CardHeader>
                      <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                        <IconComponent className="h-6 w-6 text-blue-600" />
                      </div>
                      <CardTitle className="text-xl">{feature.title}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <CardDescription className="text-gray-600">
                        {feature.description}
                      </CardDescription>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </div>
        </section>
      )}

      {/* Pricing Section */}
      {content.pricing?.enabled && plans.length > 0 && (
        <section id="pricing" className="py-20 px-4 bg-gray-50">
          <div className="container mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">
                {content.pricing.title}
              </h2>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-8">
                {content.pricing.subtitle}
              </p>

              {/* Billing Toggle */}
              <div className="flex items-center justify-center space-x-4 mb-8">
                <span className={`text-sm ${!isYearly ? 'text-gray-900 font-medium' : 'text-gray-500'}`}>
                  Monthly
                </span>
                <Switch
                  checked={isYearly}
                  onCheckedChange={setIsYearly}
                />
                <span className={`text-sm ${isYearly ? 'text-gray-900 font-medium' : 'text-gray-500'}`}>
                  Yearly
                </span>
                {plans.some(plan => getYearlyDiscount(plan) > 0) && (
                  <Badge className="ml-2 bg-green-100 text-green-800">
                    Save up to {Math.max(...plans.map(getYearlyDiscount))}%
                  </Badge>
                )}
              </div>
            </div>

            {/* Pricing Cards */}
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {plans.map((plan, index) => {
                const features = getFeatureList(plan)
                const price = getPrice(plan)
                const discount = getYearlyDiscount(plan)
                const isPopular = index === 1 // Middle plan is popular

                return (
                  <Card key={plan.id} className={`relative ${isPopular ? 'border-blue-500 shadow-xl scale-105' : 'border-gray-200 shadow-lg'} bg-white`}>
                    {isPopular && (
                      <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                        <Badge className="bg-blue-500 text-white px-4 py-1">
                          <Star className="h-4 w-4 mr-1" />
                          Most Popular
                        </Badge>
                      </div>
                    )}

                    <CardHeader className="text-center pb-8">
                      <CardTitle className="text-2xl font-bold">{plan.name}</CardTitle>
                      <CardDescription className="text-gray-600 mt-2">
                        {plan.description}
                      </CardDescription>

                      <div className="mt-6">
                        <div className="flex items-baseline justify-center">
                          <span className="text-5xl font-bold text-gray-900">
                            ${price.toFixed(0)}
                          </span>
                          <span className="text-gray-500 ml-1">/month</span>
                        </div>
                        {isYearly && discount > 0 && (
                          <p className="text-sm text-green-600 mt-2">
                            Save {discount}% with yearly billing
                          </p>
                        )}
                        {plan.trialDays > 0 && (
                          <p className="text-sm text-blue-600 mt-2">
                            {plan.trialDays}-day free trial
                          </p>
                        )}
                      </div>
                    </CardHeader>

                    <CardContent className="space-y-6">
                      <div className="space-y-3">
                        {features.slice(0, 8).map((feature, featureIndex) => (
                          <div key={featureIndex} className="flex items-center space-x-3">
                            <Check className="h-5 w-5 text-green-500 flex-shrink-0" />
                            <span className="text-gray-700 text-sm">{feature}</span>
                          </div>
                        ))}
                        {features.length > 8 && (
                          <p className="text-sm text-gray-500 italic">
                            +{features.length - 8} more features
                          </p>
                        )}
                      </div>

                      <Link href="/auth/signup" className="block">
                        <Button
                          className={`w-full ${isPopular ? 'bg-blue-600 hover:bg-blue-700' : ''}`}
                          variant={isPopular ? 'default' : 'outline'}
                          size="lg"
                        >
                          Get Started
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                      </Link>
                    </CardContent>
                  </Card>
                )
              })}
            </div>

            {/* Enterprise CTA */}
            <div className="text-center mt-16">
              <Card className="max-w-2xl mx-auto border-gray-200 shadow-lg bg-white">
                <CardContent className="p-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">
                    Need something custom?
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Contact our sales team for enterprise pricing, custom features, and dedicated support.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <Button size="lg" variant="outline">
                      <Mail className="h-4 w-4 mr-2" />
                      Contact Sales
                    </Button>
                    <Button size="lg" variant="ghost">
                      <Phone className="h-4 w-4 mr-2" />
                      Schedule Demo
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>
      )}

      {/* Testimonials Section */}
      {content.testimonials?.enabled && (
        <section id="testimonials" className="py-20 px-4 bg-white">
          <div className="container mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">
                {content.testimonials.title}
              </h2>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                {content.testimonials.subtitle}
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {content.testimonials.items.map((testimonial) => (
                <Card key={testimonial.id} className="border-0 shadow-lg">
                  <CardContent className="p-6">
                    <div className="flex items-center mb-4">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                      ))}
                    </div>
                    <Quote className="h-8 w-8 text-gray-300 mb-4" />
                    <p className="text-gray-600 mb-6 italic">
                      "{testimonial.content}"
                    </p>
                    <div className="flex items-center">
                      {testimonial.avatar ? (
                        <Image
                          src={testimonial.avatar}
                          alt={testimonial.name}
                          width={48}
                          height={48}
                          className="rounded-full mr-4"
                        />
                      ) : (
                        <div className="w-12 h-12 bg-gray-200 rounded-full mr-4 flex items-center justify-center">
                          <Users className="h-6 w-6 text-gray-400" />
                        </div>
                      )}
                      <div>
                        <p className="font-semibold text-gray-900">{testimonial.name}</p>
                        <p className="text-sm text-gray-500">{testimonial.role}, {testimonial.company}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* FAQ Section */}
      {content.faq?.enabled && (
        <section id="faq" className="py-20 px-4 bg-gray-50">
          <div className="container mx-auto max-w-4xl">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">
                {content.faq.title}
              </h2>
              <p className="text-xl text-gray-600">
                {content.faq.subtitle}
              </p>
            </div>

            <div className="space-y-4">
              {content.faq.items.map((faq) => (
                <Card key={faq.id} className="border shadow-sm">
                  <CardContent className="p-0">
                    <button
                      className="w-full p-6 text-left flex items-center justify-between hover:bg-gray-50"
                      onClick={() => setOpenFAQ(openFAQ === faq.id ? null : faq.id)}
                    >
                      <span className="font-semibold text-gray-900">{faq.question}</span>
                      {openFAQ === faq.id ? (
                        <ChevronUp className="h-5 w-5 text-gray-500" />
                      ) : (
                        <ChevronDown className="h-5 w-5 text-gray-500" />
                      )}
                    </button>
                    {openFAQ === faq.id && (
                      <div className="px-6 pb-6">
                        <p className="text-gray-600">{faq.answer}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* CTA Section */}
      {content.cta?.enabled && (
        <section className="py-20 px-4 bg-blue-600 relative overflow-hidden">
          {content.cta.backgroundImage && (
            <div className="absolute inset-0 z-0">
              <Image
                src={content.cta.backgroundImage}
                alt="CTA Background"
                fill
                className="object-cover opacity-20"
              />
            </div>
          )}
          <div className="container mx-auto text-center relative z-10">
            <h2 className="text-4xl font-bold text-white mb-6">
              {content.cta.title}
            </h2>
            <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
              {content.cta.description}
            </p>
            <Link href={content.cta.buttonLink}>
              <Button size="lg" variant="secondary" className="text-lg px-8 py-3">
                {content.cta.buttonText}
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
          </div>
        </section>
      )}

      {/* Footer */}
      {content.footer?.enabled && (
        <footer className="bg-gray-900 text-white py-16 px-4">
          <div className="container mx-auto">
            <div className="grid md:grid-cols-5 gap-8 mb-12">
              {/* Company Info */}
              <div className="md:col-span-2">
                <div className="flex items-center space-x-2 mb-6">
                  <Building2 className="h-8 w-8 text-blue-400" />
                  <span className="text-2xl font-bold">SaaS Platform</span>
                </div>
                <p className="text-gray-400 mb-6 leading-relaxed">
                  {content.footer.companyDescription}
                </p>

                {/* Contact Info */}
                <div className="space-y-3 mb-6">
                  <div className="flex items-center space-x-3 text-gray-400">
                    <Mail className="h-4 w-4" />
                    <span><EMAIL></span>
                  </div>
                  <div className="flex items-center space-x-3 text-gray-400">
                    <Phone className="h-4 w-4" />
                    <span>+****************</span>
                  </div>
                  <div className="flex items-center space-x-3 text-gray-400">
                    <MapPin className="h-4 w-4" />
                    <span>San Francisco, CA</span>
                  </div>
                </div>

                {/* Social Links */}
                <div className="flex space-x-4">
                  {content.footer.socialLinks.twitter && (
                    <a
                      href={content.footer.socialLinks.twitter}
                      className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-blue-600 transition-colors"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <Twitter className="h-5 w-5" />
                    </a>
                  )}
                  {content.footer.socialLinks.linkedin && (
                    <a
                      href={content.footer.socialLinks.linkedin}
                      className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-blue-700 transition-colors"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <Linkedin className="h-5 w-5" />
                    </a>
                  )}
                  {content.footer.socialLinks.facebook && (
                    <a
                      href={content.footer.socialLinks.facebook}
                      className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-blue-800 transition-colors"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <Facebook className="h-5 w-5" />
                    </a>
                  )}
                  {content.footer.socialLinks.instagram && (
                    <a
                      href={content.footer.socialLinks.instagram}
                      className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-pink-600 transition-colors"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <Instagram className="h-5 w-5" />
                    </a>
                  )}
                  <a
                    href="https://github.com"
                    className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 transition-colors"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <Github className="h-5 w-5" />
                  </a>
                  <a
                    href="https://youtube.com"
                    className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-red-600 transition-colors"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <Youtube className="h-5 w-5" />
                  </a>
                </div>
              </div>

              {/* Footer Links */}
              {content.footer.links.map((linkGroup) => (
                <div key={linkGroup.id}>
                  <h3 className="font-semibold mb-4 text-white">{linkGroup.title}</h3>
                  <ul className="space-y-3">
                    {linkGroup.items.map((link) => (
                      <li key={link.id}>
                        <Link
                          href={link.link}
                          className="text-gray-400 hover:text-white transition-colors"
                        >
                          {link.text}
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>

            {/* Newsletter Signup */}
            <div className="border-t border-gray-800 pt-8 mb-8">
              <div className="max-w-md">
                <h3 className="font-semibold mb-4 text-white">Stay Updated</h3>
                <p className="text-gray-400 mb-4">
                  Get the latest updates, tips, and insights delivered to your inbox.
                </p>
                <div className="flex space-x-2">
                  <input
                    type="email"
                    placeholder="Enter your email"
                    className="flex-1 px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
                  />
                  <Button className="bg-blue-600 hover:bg-blue-700">
                    Subscribe
                  </Button>
                </div>
              </div>
            </div>

            {/* Bottom Footer */}
            <div className="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
              <p className="text-gray-400 mb-4 md:mb-0">
                {content.footer.copyrightText}
              </p>
              <div className="flex space-x-6 text-gray-400 text-sm">
                <Link href="/privacy" className="hover:text-white transition-colors">
                  Privacy Policy
                </Link>
                <Link href="/terms" className="hover:text-white transition-colors">
                  Terms of Service
                </Link>
                <Link href="/cookies" className="hover:text-white transition-colors">
                  Cookie Policy
                </Link>
                <Link href="/security" className="hover:text-white transition-colors">
                  Security
                </Link>
              </div>
            </div>
          </div>
        </footer>
      )}

      {/* Scroll to Top Button */}
      {showScrollTop && (
        <button
          onClick={scrollToTop}
          className="fixed bottom-8 right-8 w-12 h-12 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg transition-all duration-300 z-50 flex items-center justify-center"
          aria-label="Scroll to top"
        >
          <ChevronUpIcon className="h-6 w-6" />
        </button>
      )}
    </div>
  )
}
