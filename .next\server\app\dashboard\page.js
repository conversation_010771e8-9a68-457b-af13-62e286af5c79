/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/page.tsx */ \"(rsc)/./app/dashboard/page.tsx\")), \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/layout.tsx */ \"(rsc)/./app/dashboard/layout.tsx\")), \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cproj%5Cnextjs-saas%5Capp%5Cdashboard%5Clayout.tsx&server=true!":
/*!***********************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cproj%5Cnextjs-saas%5Capp%5Cdashboard%5Clayout.tsx&server=true! ***!
  \***********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/layout.tsx */ \"(ssr)/./app/dashboard/layout.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q3Byb2olNUNuZXh0anMtc2FhcyU1Q2FwcCU1Q2Rhc2hib2FyZCU1Q2xheW91dC50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dGpzLXNhYXMtYXBwLz80YWQxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxccHJvalxcXFxuZXh0anMtc2Fhc1xcXFxhcHBcXFxcZGFzaGJvYXJkXFxcXGxheW91dC50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cproj%5Cnextjs-saas%5Capp%5Cdashboard%5Clayout.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cproj%5Cnextjs-saas%5Capp%5Cdashboard%5Cpage.tsx&server=true!":
/*!*********************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cproj%5Cnextjs-saas%5Capp%5Cdashboard%5Cpage.tsx&server=true! ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/page.tsx */ \"(ssr)/./app/dashboard/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q3Byb2olNUNuZXh0anMtc2FhcyU1Q2FwcCU1Q2Rhc2hib2FyZCU1Q3BhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zYWFzLWFwcC8/N2M2MiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXHByb2pcXFxcbmV4dGpzLXNhYXNcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxwYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cproj%5Cnextjs-saas%5Capp%5Cdashboard%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cproj%5Cnextjs-saas%5Ccomponents%5Cproviders.tsx&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5Cproj%5Cnextjs-saas%5Capp%5Cglobals.css&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cproj%5Cnextjs-saas%5Ccomponents%5Cproviders.tsx&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5Cproj%5Cnextjs-saas%5Capp%5Cglobals.css&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/providers.tsx */ \"(ssr)/./components/providers.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q3Byb2olNUNuZXh0anMtc2FhcyU1Q2NvbXBvbmVudHMlNUNwcm92aWRlcnMudHN4Jm1vZHVsZXM9QyUzQSU1Q3Byb2olNUNuZXh0anMtc2FhcyU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNmb250JTVDZ29vZ2xlJTVDdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIySW50ZXIlMjIlMkMlMjJhcmd1bWVudHMlMjIlM0ElNUIlN0IlMjJzdWJzZXRzJTIyJTNBJTVCJTIybGF0aW4lMjIlNUQlN0QlNUQlMkMlMjJ2YXJpYWJsZU5hbWUlMjIlM0ElMjJpbnRlciUyMiU3RCZtb2R1bGVzPUMlM0ElNUNwcm9qJTVDbmV4dGpzLXNhYXMlNUNhcHAlNUNnbG9iYWxzLmNzcyZtb2R1bGVzPUMlM0ElNUNwcm9qJTVDbmV4dGpzLXNhYXMlNUNub2RlX21vZHVsZXMlNUNyZWFjdC1ob3QtdG9hc3QlNUNkaXN0JTVDaW5kZXgubWpzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnS0FBcUY7QUFDckYiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0anMtc2Fhcy1hcHAvP2Q0ZTUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxwcm9qXFxcXG5leHRqcy1zYWFzXFxcXGNvbXBvbmVudHNcXFxccHJvdmlkZXJzLnRzeFwiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxccHJvalxcXFxuZXh0anMtc2Fhc1xcXFxub2RlX21vZHVsZXNcXFxccmVhY3QtaG90LXRvYXN0XFxcXGRpc3RcXFxcaW5kZXgubWpzXCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cproj%5Cnextjs-saas%5Ccomponents%5Cproviders.tsx&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5Cproj%5Cnextjs-saas%5Capp%5Cglobals.css&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/dashboard/layout.tsx":
/*!**********************************!*\
  !*** ./app/dashboard/layout.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_dashboard_dashboard_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/dashboard/dashboard-layout */ \"(ssr)/./components/dashboard/dashboard-layout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction DashboardLayout({ children }) {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\layout.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\layout.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 17,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\layout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this);\n    }\n    // Temporarily disable authentication check for debugging\n    // if (status === 'unauthenticated') {\n    //   redirect('/auth/signin')\n    // }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_dashboard_layout__WEBPACK_IMPORTED_MODULE_2__.DashboardLayout, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\layout.tsx\",\n        lineNumber: 30,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/dashboard/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-check.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/receipt.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-up-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-down-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n// import { Skeleton } from '@/components/ui/skeleton'\n\nfunction DashboardPage() {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const [dashboardData, setDashboardData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const fetchDashboardData = async ()=>{\n            try {\n                setLoading(true);\n                setError(null);\n                const response = await fetch(\"/api/dashboard\");\n                if (!response.ok) {\n                    throw new Error(\"Failed to fetch dashboard data\");\n                }\n                const data = await response.json();\n                setDashboardData(data);\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"An error occurred\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        if (status === \"loading\") {\n            return; // Don't do anything while session is loading\n        }\n        if (session?.user?.companyId) {\n            fetchDashboardData();\n        } else if (session && !session.user?.companyId) {\n            setLoading(false);\n            setError(\"No company associated with user\");\n        } else if (status === \"unauthenticated\") {\n            setLoading(false);\n            setError(\"Please log in to view dashboard\");\n        }\n    }, [\n        session?.user?.companyId,\n        status\n    ]);\n    if (status === \"loading\" || loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"Loading Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500\",\n                        children: \"Please wait while we fetch your data...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 90,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 89,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !dashboardData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-12 w-12 text-red-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"Error Loading Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500\",\n                        children: error || \"Failed to load dashboard data\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        onClick: ()=>window.location.reload(),\n                        className: \"mt-4\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 102,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 101,\n            columnNumber: 7\n        }, this);\n    }\n    const stats = [\n        {\n            title: \"Total Customers\",\n            value: dashboardData.stats.customers.total.toLocaleString(),\n            change: `${dashboardData.stats.customers.change >= 0 ? \"+\" : \"\"}${dashboardData.stats.customers.change.toFixed(1)}%`,\n            changeType: dashboardData.stats.customers.change >= 0 ? \"increase\" : \"decrease\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            color: \"text-blue-600\",\n            subtitle: `${dashboardData.stats.customers.current} this month`\n        },\n        {\n            title: \"Active Leads\",\n            value: dashboardData.stats.leads.current.toLocaleString(),\n            change: `${dashboardData.stats.leads.change >= 0 ? \"+\" : \"\"}${dashboardData.stats.leads.change.toFixed(1)}%`,\n            changeType: dashboardData.stats.leads.change >= 0 ? \"increase\" : \"decrease\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            color: \"text-green-600\",\n            subtitle: `${dashboardData.stats.leads.total} total leads`\n        },\n        {\n            title: \"Quotations\",\n            value: dashboardData.stats.quotations.current.toLocaleString(),\n            change: `${dashboardData.stats.quotations.change >= 0 ? \"+\" : \"\"}${dashboardData.stats.quotations.change.toFixed(1)}%`,\n            changeType: dashboardData.stats.quotations.change >= 0 ? \"increase\" : \"decrease\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            color: \"text-yellow-600\",\n            subtitle: `${dashboardData.stats.quotations.total} total`\n        },\n        {\n            title: \"Revenue\",\n            value: `$${dashboardData.stats.revenue.total.toLocaleString()}`,\n            change: `${dashboardData.stats.revenue.change >= 0 ? \"+\" : \"\"}${dashboardData.stats.revenue.change.toFixed(1)}%`,\n            changeType: dashboardData.stats.revenue.change >= 0 ? \"increase\" : \"decrease\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            color: \"text-purple-600\",\n            subtitle: `$${dashboardData.stats.revenue.pending.toLocaleString()} pending`\n        },\n        {\n            title: \"Active Contracts\",\n            value: dashboardData.stats.contracts.current.toLocaleString(),\n            change: \"\",\n            changeType: \"neutral\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            color: \"text-indigo-600\",\n            subtitle: `${dashboardData.stats.contracts.total} total`\n        },\n        {\n            title: \"Open Tasks\",\n            value: dashboardData.stats.tasks.current.toLocaleString(),\n            change: \"\",\n            changeType: \"neutral\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            color: \"text-orange-600\",\n            subtitle: `${dashboardData.stats.tasks.total} total tasks`\n        }\n    ];\n    // Process recent activities\n    const processedActivities = dashboardData.recentActivities.map((activity)=>{\n        let message = activity.description;\n        let icon = _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"];\n        let status = \"info\";\n        // Customize based on activity type\n        switch(activity.type){\n            case \"NOTE\":\n                icon = _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"];\n                status = \"info\";\n                break;\n            case \"CALL\":\n                icon = _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n                status = \"success\";\n                break;\n            case \"EMAIL\":\n                icon = _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"];\n                status = \"info\";\n                break;\n            case \"MEETING\":\n                icon = _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"];\n                status = \"warning\";\n                break;\n            case \"STATUS_CHANGE\":\n                icon = _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"];\n                status = \"success\";\n                break;\n            case \"PAYMENT_RECEIVED\":\n                icon = _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"];\n                status = \"success\";\n                break;\n            case \"CONTRACT_SIGNED\":\n                icon = _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"];\n                status = \"success\";\n                break;\n            default:\n                icon = _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"];\n                status = \"info\";\n        }\n        return {\n            id: activity.id,\n            type: activity.type,\n            message: activity.title,\n            description: activity.description,\n            time: new Date(activity.createdAt).toLocaleString(),\n            icon,\n            status,\n            user: activity.createdBy?.name || \"System\"\n        };\n    });\n    const quickActions = [\n        {\n            title: \"Add Lead\",\n            description: \"Track a new business lead\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            href: \"/dashboard/leads/new\",\n            color: \"text-green-600\"\n        },\n        {\n            title: \"Add Customer\",\n            description: \"Create a new customer profile\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            href: \"/dashboard/customers/new\",\n            color: \"text-blue-600\"\n        },\n        {\n            title: \"Create Quotation\",\n            description: \"Generate a new quotation\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            href: \"/dashboard/quotations/new\",\n            color: \"text-yellow-600\"\n        },\n        {\n            title: \"Create Invoice\",\n            description: \"Generate a new invoice\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            href: \"/dashboard/invoices/new\",\n            color: \"text-purple-600\"\n        },\n        {\n            title: \"New Contract\",\n            description: \"Create a new contract\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            href: \"/dashboard/contracts/new\",\n            color: \"text-indigo-600\"\n        },\n        {\n            title: \"View Reports\",\n            description: \"Analyze your business data\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n            href: \"/dashboard/reports\",\n            color: \"text-orange-600\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 text-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold mb-2\",\n                        children: [\n                            \"Welcome back, \",\n                            session?.user?.name || session?.user?.email || \"User\",\n                            \"!\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-blue-100\",\n                        children: \"Here's what's happening with your business today.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 flex items-center space-x-4 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        variant: \"secondary\",\n                                        className: \"mr-2\",\n                                        children: session?.user?.role || \"USER\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Role\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        variant: \"outline\",\n                                        className: \"mr-2 text-white border-white\",\n                                        children: [\n                                            dashboardData.stats.customers.total,\n                                            \" Customers\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        variant: \"outline\",\n                                        className: \"mr-2 text-white border-white\",\n                                        children: [\n                                            dashboardData.stats.leads.current,\n                                            \" Active Leads\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 275,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4\",\n                children: stats.map((stat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"hover:shadow-md transition-shadow\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"text-sm font-medium text-gray-600\",\n                                        children: stat.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                        className: `h-5 w-5 ${stat.color}`\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: stat.value\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 15\n                                    }, this),\n                                    stat.change && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mt-1\",\n                                        children: [\n                                            stat.changeType === \"increase\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-4 w-4 text-green-600 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 21\n                                            }, this) : stat.changeType === \"decrease\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-4 w-4 text-red-600 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 21\n                                            }, this) : null,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: `text-sm font-medium ${stat.changeType === \"increase\" ? \"text-green-600\" : stat.changeType === \"decrease\" ? \"text-red-600\" : \"text-gray-500\"}`,\n                                                children: stat.change\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 19\n                                            }, this),\n                                            stat.changeType !== \"neutral\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-500 ml-1\",\n                                                children: \"from last month\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 17\n                                    }, this),\n                                    stat.subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: stat.subtitle\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, stat.title, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 301,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"h-5 w-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Quick Actions\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-3\",\n                                    children: quickActions.map((action)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"outline\",\n                                            className: \"h-auto p-3 flex flex-col items-center space-y-2 hover:bg-gray-50\",\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: action.href,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(action.icon, {\n                                                        className: `h-5 w-5 ${action.color}`\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium text-xs\",\n                                                                children: action.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 360,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: action.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 361,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, action.title, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"lg:col-span-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-5 w-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Recent Activity\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4 max-h-80 overflow-y-auto\",\n                                    children: processedActivities.length > 0 ? processedActivities.map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `p-2 rounded-full ${activity.status === \"success\" ? \"bg-green-100\" : activity.status === \"warning\" ? \"bg-yellow-100\" : \"bg-blue-100\"}`,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(activity.icon, {\n                                                        className: `h-4 w-4 ${activity.status === \"success\" ? \"text-green-600\" : activity.status === \"warning\" ? \"text-yellow-600\" : \"text-blue-600\"}`\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-900\",\n                                                            children: activity.message\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 395,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: activity.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 396,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center mt-1 text-xs text-gray-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: activity.user\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 398,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"mx-1\",\n                                                                    children: \"•\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 399,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: activity.time\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 400,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 397,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 394,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, activity.id, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 19\n                                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 407,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"No recent activity\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 371,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 339,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"h-5 w-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Upcoming Tasks\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 421,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: dashboardData.upcomingTasks.length > 0 ? dashboardData.upcomingTasks.map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `p-2 rounded-full ${task.priority === \"HIGH\" ? \"bg-red-100\" : task.priority === \"MEDIUM\" ? \"bg-yellow-100\" : \"bg-green-100\"}`,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: `h-4 w-4 ${task.priority === \"HIGH\" ? \"text-red-600\" : task.priority === \"MEDIUM\" ? \"text-yellow-600\" : \"text-green-600\"}`\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: task.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: [\n                                                                \"Due: \",\n                                                                new Date(task.dueDate).toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        task.assignedTo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-400\",\n                                                            children: [\n                                                                \"Assigned to: \",\n                                                                task.assignedTo.name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 448,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, task.id, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 19\n                                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"No upcoming tasks\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 458,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 427,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 426,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 419,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-5 w-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Upcoming Renewals\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 467,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: dashboardData.upcomingRenewals.length > 0 ? dashboardData.upcomingRenewals.map((contract)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 rounded-full bg-orange-100\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4 text-orange-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 478,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: contract.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: [\n                                                                \"Customer: \",\n                                                                contract.customer.name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 483,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: [\n                                                                \"Renewal: \",\n                                                                new Date(contract.renewalDate).toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 486,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 481,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, contract.id, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 19\n                                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 494,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"No upcoming renewals\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 493,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 474,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 473,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 466,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 417,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 273,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvZGFzaGJvYXJkL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFNEM7QUFDRDtBQUNvQztBQUNsQztBQUNFO0FBRS9DLHNEQUFzRDtBQW1CakM7QUFvQk4sU0FBUzBCO0lBQ3RCLE1BQU0sRUFBRUMsTUFBTUMsT0FBTyxFQUFFQyxNQUFNLEVBQUUsR0FBRzdCLDJEQUFVQTtJQUM1QyxNQUFNLENBQUM4QixlQUFlQyxpQkFBaUIsR0FBRzdCLCtDQUFRQSxDQUF1QjtJQUN6RSxNQUFNLENBQUM4QixTQUFTQyxXQUFXLEdBQUcvQiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNnQyxPQUFPQyxTQUFTLEdBQUdqQywrQ0FBUUEsQ0FBZ0I7SUFFbERELGdEQUFTQSxDQUFDO1FBQ1IsTUFBTW1DLHFCQUFxQjtZQUN6QixJQUFJO2dCQUNGSCxXQUFXO2dCQUNYRSxTQUFTO2dCQUNULE1BQU1FLFdBQVcsTUFBTUMsTUFBTTtnQkFDN0IsSUFBSSxDQUFDRCxTQUFTRSxFQUFFLEVBQUU7b0JBQ2hCLE1BQU0sSUFBSUMsTUFBTTtnQkFDbEI7Z0JBQ0EsTUFBTWIsT0FBTyxNQUFNVSxTQUFTSSxJQUFJO2dCQUNoQ1YsaUJBQWlCSjtZQUNuQixFQUFFLE9BQU9lLEtBQUs7Z0JBQ1pQLFNBQVNPLGVBQWVGLFFBQVFFLElBQUlDLE9BQU8sR0FBRztZQUNoRCxTQUFVO2dCQUNSVixXQUFXO1lBQ2I7UUFDRjtRQUVBLElBQUlKLFdBQVcsV0FBVztZQUN4QixRQUFPLDZDQUE2QztRQUN0RDtRQUVBLElBQUlELFNBQVNnQixNQUFNQyxXQUFXO1lBQzVCVDtRQUNGLE9BQU8sSUFBSVIsV0FBVyxDQUFDQSxRQUFRZ0IsSUFBSSxFQUFFQyxXQUFXO1lBQzlDWixXQUFXO1lBQ1hFLFNBQVM7UUFDWCxPQUFPLElBQUlOLFdBQVcsbUJBQW1CO1lBQ3ZDSSxXQUFXO1lBQ1hFLFNBQVM7UUFDWDtJQUNGLEdBQUc7UUFBQ1AsU0FBU2dCLE1BQU1DO1FBQVdoQjtLQUFPO0lBRXJDLElBQUlBLFdBQVcsYUFBYUcsU0FBUztRQUNuQyxxQkFDRSw4REFBQ2M7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTs7Ozs7O2tDQUNmLDhEQUFDQzt3QkFBR0QsV0FBVTtrQ0FBeUM7Ozs7OztrQ0FDdkQsOERBQUNFO3dCQUFFRixXQUFVO2tDQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFJckM7SUFFQSxJQUFJYixTQUFTLENBQUNKLGVBQWU7UUFDM0IscUJBQ0UsOERBQUNnQjtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUMxQiw2T0FBV0E7d0JBQUMwQixXQUFVOzs7Ozs7a0NBQ3ZCLDhEQUFDQzt3QkFBR0QsV0FBVTtrQ0FBeUM7Ozs7OztrQ0FDdkQsOERBQUNFO3dCQUFFRixXQUFVO2tDQUFpQmIsU0FBUzs7Ozs7O2tDQUN2Qyw4REFBQzFCLHlEQUFNQTt3QkFDTDBDLFNBQVMsSUFBTUMsT0FBT0MsUUFBUSxDQUFDQyxNQUFNO3dCQUNyQ04sV0FBVTtrQ0FDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFNVDtJQUVBLE1BQU1PLFFBQVE7UUFDWjtZQUNFQyxPQUFPO1lBQ1BDLE9BQU8xQixjQUFjd0IsS0FBSyxDQUFDRyxTQUFTLENBQUNDLEtBQUssQ0FBQ0MsY0FBYztZQUN6REMsUUFBUSxDQUFDLEVBQUU5QixjQUFjd0IsS0FBSyxDQUFDRyxTQUFTLENBQUNHLE1BQU0sSUFBSSxJQUFJLE1BQU0sR0FBRyxFQUFFOUIsY0FBY3dCLEtBQUssQ0FBQ0csU0FBUyxDQUFDRyxNQUFNLENBQUNDLE9BQU8sQ0FBQyxHQUFHLENBQUMsQ0FBQztZQUNwSEMsWUFBWWhDLGNBQWN3QixLQUFLLENBQUNHLFNBQVMsQ0FBQ0csTUFBTSxJQUFJLElBQUksYUFBc0I7WUFDOUVHLE1BQU10RCw2T0FBS0E7WUFDWHVELE9BQU87WUFDUEMsVUFBVSxDQUFDLEVBQUVuQyxjQUFjd0IsS0FBSyxDQUFDRyxTQUFTLENBQUNTLE9BQU8sQ0FBQyxXQUFXLENBQUM7UUFDakU7UUFDQTtZQUNFWCxPQUFPO1lBQ1BDLE9BQU8xQixjQUFjd0IsS0FBSyxDQUFDYSxLQUFLLENBQUNELE9BQU8sQ0FBQ1AsY0FBYztZQUN2REMsUUFBUSxDQUFDLEVBQUU5QixjQUFjd0IsS0FBSyxDQUFDYSxLQUFLLENBQUNQLE1BQU0sSUFBSSxJQUFJLE1BQU0sR0FBRyxFQUFFOUIsY0FBY3dCLEtBQUssQ0FBQ2EsS0FBSyxDQUFDUCxNQUFNLENBQUNDLE9BQU8sQ0FBQyxHQUFHLENBQUMsQ0FBQztZQUM1R0MsWUFBWWhDLGNBQWN3QixLQUFLLENBQUNhLEtBQUssQ0FBQ1AsTUFBTSxJQUFJLElBQUksYUFBc0I7WUFDMUVHLE1BQU1sRCw2T0FBUUE7WUFDZG1ELE9BQU87WUFDUEMsVUFBVSxDQUFDLEVBQUVuQyxjQUFjd0IsS0FBSyxDQUFDYSxLQUFLLENBQUNULEtBQUssQ0FBQyxZQUFZLENBQUM7UUFDNUQ7UUFDQTtZQUNFSCxPQUFPO1lBQ1BDLE9BQU8xQixjQUFjd0IsS0FBSyxDQUFDYyxVQUFVLENBQUNGLE9BQU8sQ0FBQ1AsY0FBYztZQUM1REMsUUFBUSxDQUFDLEVBQUU5QixjQUFjd0IsS0FBSyxDQUFDYyxVQUFVLENBQUNSLE1BQU0sSUFBSSxJQUFJLE1BQU0sR0FBRyxFQUFFOUIsY0FBY3dCLEtBQUssQ0FBQ2MsVUFBVSxDQUFDUixNQUFNLENBQUNDLE9BQU8sQ0FBQyxHQUFHLENBQUMsQ0FBQztZQUN0SEMsWUFBWWhDLGNBQWN3QixLQUFLLENBQUNjLFVBQVUsQ0FBQ1IsTUFBTSxJQUFJLElBQUksYUFBc0I7WUFDL0VHLE1BQU1yRCw2T0FBUUE7WUFDZHNELE9BQU87WUFDUEMsVUFBVSxDQUFDLEVBQUVuQyxjQUFjd0IsS0FBSyxDQUFDYyxVQUFVLENBQUNWLEtBQUssQ0FBQyxNQUFNLENBQUM7UUFDM0Q7UUFDQTtZQUNFSCxPQUFPO1lBQ1BDLE9BQU8sQ0FBQyxDQUFDLEVBQUUxQixjQUFjd0IsS0FBSyxDQUFDZSxPQUFPLENBQUNYLEtBQUssQ0FBQ0MsY0FBYyxHQUFHLENBQUM7WUFDL0RDLFFBQVEsQ0FBQyxFQUFFOUIsY0FBY3dCLEtBQUssQ0FBQ2UsT0FBTyxDQUFDVCxNQUFNLElBQUksSUFBSSxNQUFNLEdBQUcsRUFBRTlCLGNBQWN3QixLQUFLLENBQUNlLE9BQU8sQ0FBQ1QsTUFBTSxDQUFDQyxPQUFPLENBQUMsR0FBRyxDQUFDLENBQUM7WUFDaEhDLFlBQVloQyxjQUFjd0IsS0FBSyxDQUFDZSxPQUFPLENBQUNULE1BQU0sSUFBSSxJQUFJLGFBQXNCO1lBQzVFRyxNQUFNbkQsOE9BQVVBO1lBQ2hCb0QsT0FBTztZQUNQQyxVQUFVLENBQUMsQ0FBQyxFQUFFbkMsY0FBY3dCLEtBQUssQ0FBQ2UsT0FBTyxDQUFDQyxPQUFPLENBQUNYLGNBQWMsR0FBRyxRQUFRLENBQUM7UUFDOUU7UUFDQTtZQUNFSixPQUFPO1lBQ1BDLE9BQU8xQixjQUFjd0IsS0FBSyxDQUFDaUIsU0FBUyxDQUFDTCxPQUFPLENBQUNQLGNBQWM7WUFDM0RDLFFBQVE7WUFDUkUsWUFBWTtZQUNaQyxNQUFNeEMsOE9BQVNBO1lBQ2Z5QyxPQUFPO1lBQ1BDLFVBQVUsQ0FBQyxFQUFFbkMsY0FBY3dCLEtBQUssQ0FBQ2lCLFNBQVMsQ0FBQ2IsS0FBSyxDQUFDLE1BQU0sQ0FBQztRQUMxRDtRQUNBO1lBQ0VILE9BQU87WUFDUEMsT0FBTzFCLGNBQWN3QixLQUFLLENBQUNrQixLQUFLLENBQUNOLE9BQU8sQ0FBQ1AsY0FBYztZQUN2REMsUUFBUTtZQUNSRSxZQUFZO1lBQ1pDLE1BQU12Qyw4T0FBTUE7WUFDWndDLE9BQU87WUFDUEMsVUFBVSxDQUFDLEVBQUVuQyxjQUFjd0IsS0FBSyxDQUFDa0IsS0FBSyxDQUFDZCxLQUFLLENBQUMsWUFBWSxDQUFDO1FBQzVEO0tBQ0Q7SUFFRCw0QkFBNEI7SUFDNUIsTUFBTWUsc0JBQXNCM0MsY0FBYzRDLGdCQUFnQixDQUFDQyxHQUFHLENBQUMsQ0FBQ0M7UUFDOUQsSUFBSWpDLFVBQVVpQyxTQUFTQyxXQUFXO1FBQ2xDLElBQUlkLE9BQU9qRCw4T0FBUUE7UUFDbkIsSUFBSWUsU0FBUztRQUViLG1DQUFtQztRQUNuQyxPQUFRK0MsU0FBU0UsSUFBSTtZQUNuQixLQUFLO2dCQUNIZixPQUFPckQsNk9BQVFBO2dCQUNmbUIsU0FBUztnQkFDVDtZQUNGLEtBQUs7Z0JBQ0hrQyxPQUFPdEQsNk9BQUtBO2dCQUNab0IsU0FBUztnQkFDVDtZQUNGLEtBQUs7Z0JBQ0hrQyxPQUFPcEQsOE9BQU9BO2dCQUNka0IsU0FBUztnQkFDVDtZQUNGLEtBQUs7Z0JBQ0hrQyxPQUFPNUMsOE9BQVFBO2dCQUNmVSxTQUFTO2dCQUNUO1lBQ0YsS0FBSztnQkFDSGtDLE9BQU9oRCw4T0FBVUE7Z0JBQ2pCYyxTQUFTO2dCQUNUO1lBQ0YsS0FBSztnQkFDSGtDLE9BQU9uRCw4T0FBVUE7Z0JBQ2pCaUIsU0FBUztnQkFDVDtZQUNGLEtBQUs7Z0JBQ0hrQyxPQUFPeEMsOE9BQVNBO2dCQUNoQk0sU0FBUztnQkFDVDtZQUNGO2dCQUNFa0MsT0FBT2pELDhPQUFRQTtnQkFDZmUsU0FBUztRQUNiO1FBRUEsT0FBTztZQUNMa0QsSUFBSUgsU0FBU0csRUFBRTtZQUNmRCxNQUFNRixTQUFTRSxJQUFJO1lBQ25CbkMsU0FBU2lDLFNBQVNyQixLQUFLO1lBQ3ZCc0IsYUFBYUQsU0FBU0MsV0FBVztZQUNqQ0csTUFBTSxJQUFJQyxLQUFLTCxTQUFTTSxTQUFTLEVBQUV2QixjQUFjO1lBQ2pESTtZQUNBbEM7WUFDQWUsTUFBTWdDLFNBQVNPLFNBQVMsRUFBRUMsUUFBUTtRQUNwQztJQUNGO0lBRUEsTUFBTUMsZUFBZTtRQUNuQjtZQUNFOUIsT0FBTztZQUNQc0IsYUFBYTtZQUNiZCxNQUFNbEQsNk9BQVFBO1lBQ2R5RSxNQUFNO1lBQ050QixPQUFPO1FBQ1Q7UUFDQTtZQUNFVCxPQUFPO1lBQ1BzQixhQUFhO1lBQ2JkLE1BQU10RCw2T0FBS0E7WUFDWDZFLE1BQU07WUFDTnRCLE9BQU87UUFDVDtRQUNBO1lBQ0VULE9BQU87WUFDUHNCLGFBQWE7WUFDYmQsTUFBTXJELDZPQUFRQTtZQUNkNEUsTUFBTTtZQUNOdEIsT0FBTztRQUNUO1FBQ0E7WUFDRVQsT0FBTztZQUNQc0IsYUFBYTtZQUNiZCxNQUFNcEQsOE9BQU9BO1lBQ2IyRSxNQUFNO1lBQ050QixPQUFPO1FBQ1Q7UUFDQTtZQUNFVCxPQUFPO1lBQ1BzQixhQUFhO1lBQ2JkLE1BQU14Qyw4T0FBU0E7WUFDZitELE1BQU07WUFDTnRCLE9BQU87UUFDVDtRQUNBO1lBQ0VULE9BQU87WUFDUHNCLGFBQWE7WUFDYmQsTUFBTXRDLDhPQUFTQTtZQUNmNkQsTUFBTTtZQUNOdEIsT0FBTztRQUNUO0tBQ0Q7SUFFRCxxQkFDRSw4REFBQ2xCO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUN3Qzt3QkFBR3hDLFdBQVU7OzRCQUEwQjs0QkFDdkJuQixTQUFTZ0IsTUFBTXdDLFFBQVF4RCxTQUFTZ0IsTUFBTTRDLFNBQVM7NEJBQU87Ozs7Ozs7a0NBRXZFLDhEQUFDdkM7d0JBQUVGLFdBQVU7a0NBQWdCOzs7Ozs7a0NBRzdCLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUMwQztnQ0FBSzFDLFdBQVU7O2tEQUNkLDhEQUFDeEMsdURBQUtBO3dDQUFDbUYsU0FBUTt3Q0FBWTNDLFdBQVU7a0RBQ2xDbkIsU0FBU2dCLE1BQU0rQyxRQUFROzs7Ozs7b0NBQ2xCOzs7Ozs7OzBDQUdWLDhEQUFDRjtnQ0FBSzFDLFdBQVU7O2tEQUNkLDhEQUFDeEMsdURBQUtBO3dDQUFDbUYsU0FBUTt3Q0FBVTNDLFdBQVU7OzRDQUNoQ2pCLGNBQWN3QixLQUFLLENBQUNHLFNBQVMsQ0FBQ0MsS0FBSzs0Q0FBQzs7Ozs7OztrREFFdkMsOERBQUNuRCx1REFBS0E7d0NBQUNtRixTQUFRO3dDQUFVM0MsV0FBVTs7NENBQ2hDakIsY0FBY3dCLEtBQUssQ0FBQ2EsS0FBSyxDQUFDRCxPQUFPOzRDQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU8zQyw4REFBQ3BCO2dCQUFJQyxXQUFVOzBCQUNaTyxNQUFNcUIsR0FBRyxDQUFDLENBQUNpQixxQkFDViw4REFBQ3pGLHFEQUFJQTt3QkFBa0I0QyxXQUFVOzswQ0FDL0IsOERBQUMxQywyREFBVUE7Z0NBQUMwQyxXQUFVOztrREFDcEIsOERBQUN6QywwREFBU0E7d0NBQUN5QyxXQUFVO2tEQUNsQjZDLEtBQUtyQyxLQUFLOzs7Ozs7a0RBRWIsOERBQUNxQyxLQUFLN0IsSUFBSTt3Q0FBQ2hCLFdBQVcsQ0FBQyxRQUFRLEVBQUU2QyxLQUFLNUIsS0FBSyxDQUFDLENBQUM7Ozs7Ozs7Ozs7OzswQ0FFL0MsOERBQUM1RCw0REFBV0E7O2tEQUNWLDhEQUFDMEM7d0NBQUlDLFdBQVU7a0RBQW9DNkMsS0FBS3BDLEtBQUs7Ozs7OztvQ0FDNURvQyxLQUFLaEMsTUFBTSxrQkFDViw4REFBQ2Q7d0NBQUlDLFdBQVU7OzRDQUNaNkMsS0FBSzlCLFVBQVUsS0FBSywyQkFDbkIsOERBQUM5Qyw4T0FBWUE7Z0RBQUMrQixXQUFVOzs7Ozt1REFDdEI2QyxLQUFLOUIsVUFBVSxLQUFLLDJCQUN0Qiw4REFBQzdDLDhPQUFjQTtnREFBQzhCLFdBQVU7Ozs7O3VEQUN4QjswREFDSiw4REFBQzBDO2dEQUFLMUMsV0FBVyxDQUFDLG9CQUFvQixFQUNwQzZDLEtBQUs5QixVQUFVLEtBQUssYUFBYSxtQkFDakM4QixLQUFLOUIsVUFBVSxLQUFLLGFBQWEsaUJBQWlCLGdCQUNuRCxDQUFDOzBEQUNDOEIsS0FBS2hDLE1BQU07Ozs7Ozs0Q0FFYmdDLEtBQUs5QixVQUFVLEtBQUssMkJBQ25CLDhEQUFDMkI7Z0RBQUsxQyxXQUFVOzBEQUE2Qjs7Ozs7Ozs7Ozs7O29DQUlsRDZDLEtBQUszQixRQUFRLGtCQUNaLDhEQUFDaEI7d0NBQUVGLFdBQVU7a0RBQThCNkMsS0FBSzNCLFFBQVE7Ozs7Ozs7Ozs7Ozs7dUJBNUJuRDJCLEtBQUtyQyxLQUFLOzs7Ozs7Ozs7OzBCQW9DekIsOERBQUNUO2dCQUFJQyxXQUFVOztrQ0FFYiw4REFBQzVDLHFEQUFJQTs7MENBQ0gsOERBQUNFLDJEQUFVQTswQ0FDVCw0RUFBQ0MsMERBQVNBO29DQUFDeUMsV0FBVTs7c0RBQ25CLDhEQUFDN0IsOE9BQUlBOzRDQUFDNkIsV0FBVTs7Ozs7O3dDQUFpQjs7Ozs7Ozs7Ozs7OzBDQUlyQyw4REFBQzNDLDREQUFXQTswQ0FDViw0RUFBQzBDO29DQUFJQyxXQUFVOzhDQUNac0MsYUFBYVYsR0FBRyxDQUFDLENBQUNrQix1QkFDakIsOERBQUNyRix5REFBTUE7NENBRUxrRixTQUFROzRDQUNSM0MsV0FBVTs0Q0FDVitDLE9BQU87c0RBRVAsNEVBQUNDO2dEQUFFVCxNQUFNTyxPQUFPUCxJQUFJOztrRUFDbEIsOERBQUNPLE9BQU85QixJQUFJO3dEQUFDaEIsV0FBVyxDQUFDLFFBQVEsRUFBRThDLE9BQU83QixLQUFLLENBQUMsQ0FBQzs7Ozs7O2tFQUNqRCw4REFBQ2xCO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVU7MEVBQXVCOEMsT0FBT3RDLEtBQUs7Ozs7OzswRUFDbEQsOERBQUNUO2dFQUFJQyxXQUFVOzBFQUF5QjhDLE9BQU9oQixXQUFXOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MkNBVHpEZ0IsT0FBT3RDLEtBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FtQjNCLDhEQUFDcEQscURBQUlBO3dCQUFDNEMsV0FBVTs7MENBQ2QsOERBQUMxQywyREFBVUE7MENBQ1QsNEVBQUNDLDBEQUFTQTtvQ0FBQ3lDLFdBQVU7O3NEQUNuQiw4REFBQ2pDLDhPQUFRQTs0Q0FBQ2lDLFdBQVU7Ozs7Ozt3Q0FBaUI7Ozs7Ozs7Ozs7OzswQ0FJekMsOERBQUMzQyw0REFBV0E7MENBQ1YsNEVBQUMwQztvQ0FBSUMsV0FBVTs4Q0FDWjBCLG9CQUFvQnVCLE1BQU0sR0FBRyxJQUM1QnZCLG9CQUFvQkUsR0FBRyxDQUFDLENBQUNDLHlCQUN2Qiw4REFBQzlCOzRDQUFzQkMsV0FBVTs7OERBQy9CLDhEQUFDRDtvREFBSUMsV0FBVyxDQUFDLGlCQUFpQixFQUNoQzZCLFNBQVMvQyxNQUFNLEtBQUssWUFBWSxpQkFDaEMrQyxTQUFTL0MsTUFBTSxLQUFLLFlBQVksa0JBQ2hDLGNBQ0QsQ0FBQzs4REFDQSw0RUFBQytDLFNBQVNiLElBQUk7d0RBQUNoQixXQUFXLENBQUMsUUFBUSxFQUNqQzZCLFNBQVMvQyxNQUFNLEtBQUssWUFBWSxtQkFDaEMrQyxTQUFTL0MsTUFBTSxLQUFLLFlBQVksb0JBQ2hDLGdCQUNELENBQUM7Ozs7Ozs7Ozs7OzhEQUVKLDhEQUFDaUI7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRTs0REFBRUYsV0FBVTtzRUFBeUI2QixTQUFTakMsT0FBTzs7Ozs7O3NFQUN0RCw4REFBQ007NERBQUVGLFdBQVU7c0VBQXlCNkIsU0FBU0MsV0FBVzs7Ozs7O3NFQUMxRCw4REFBQy9COzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQzBDOzhFQUFNYixTQUFTaEMsSUFBSTs7Ozs7OzhFQUNwQiw4REFBQzZDO29FQUFLMUMsV0FBVTs4RUFBTzs7Ozs7OzhFQUN2Qiw4REFBQzBDOzhFQUFNYixTQUFTSSxJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzJDQWxCaEJKLFNBQVNHLEVBQUU7Ozs7a0VBd0J2Qiw4REFBQ2pDO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ2pDLDhPQUFRQTtnREFBQ2lDLFdBQVU7Ozs7OzswREFDcEIsOERBQUNFOzBEQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVNmLDhEQUFDSDtnQkFBSUMsV0FBVTs7a0NBRWIsOERBQUM1QyxxREFBSUE7OzBDQUNILDhEQUFDRSwyREFBVUE7MENBQ1QsNEVBQUNDLDBEQUFTQTtvQ0FBQ3lDLFdBQVU7O3NEQUNuQiw4REFBQzNCLDhPQUFLQTs0Q0FBQzJCLFdBQVU7Ozs7Ozt3Q0FBaUI7Ozs7Ozs7Ozs7OzswQ0FJdEMsOERBQUMzQyw0REFBV0E7MENBQ1YsNEVBQUMwQztvQ0FBSUMsV0FBVTs4Q0FDWmpCLGNBQWNtRSxhQUFhLENBQUNELE1BQU0sR0FBRyxJQUNwQ2xFLGNBQWNtRSxhQUFhLENBQUN0QixHQUFHLENBQUMsQ0FBQ3VCLHFCQUMvQiw4REFBQ3BEOzRDQUFrQkMsV0FBVTs7OERBQzNCLDhEQUFDRDtvREFBSUMsV0FBVyxDQUFDLGlCQUFpQixFQUNoQ21ELEtBQUtDLFFBQVEsS0FBSyxTQUFTLGVBQzNCRCxLQUFLQyxRQUFRLEtBQUssV0FBVyxrQkFDN0IsZUFDRCxDQUFDOzhEQUNBLDRFQUFDM0UsOE9BQU1BO3dEQUFDdUIsV0FBVyxDQUFDLFFBQVEsRUFDMUJtRCxLQUFLQyxRQUFRLEtBQUssU0FBUyxpQkFDM0JELEtBQUtDLFFBQVEsS0FBSyxXQUFXLG9CQUM3QixpQkFDRCxDQUFDOzs7Ozs7Ozs7Ozs4REFFSiw4REFBQ3JEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0U7NERBQUVGLFdBQVU7c0VBQXFDbUQsS0FBSzNDLEtBQUs7Ozs7OztzRUFDNUQsOERBQUNOOzREQUFFRixXQUFVOztnRUFBd0I7Z0VBQzdCLElBQUlrQyxLQUFLaUIsS0FBS0UsT0FBTyxFQUFFQyxrQkFBa0I7Ozs7Ozs7d0RBRWhESCxLQUFLSSxVQUFVLGtCQUNkLDhEQUFDckQ7NERBQUVGLFdBQVU7O2dFQUF3QjtnRUFDckJtRCxLQUFLSSxVQUFVLENBQUNsQixJQUFJOzs7Ozs7Ozs7Ozs7OzsyQ0FuQmhDYyxLQUFLbkIsRUFBRTs7OztrRUEwQm5CLDhEQUFDakM7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDekIsOE9BQVdBO2dEQUFDeUIsV0FBVTs7Ozs7OzBEQUN2Qiw4REFBQ0U7MERBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBUWIsOERBQUM5QyxxREFBSUE7OzBDQUNILDhEQUFDRSwyREFBVUE7MENBQ1QsNEVBQUNDLDBEQUFTQTtvQ0FBQ3lDLFdBQVU7O3NEQUNuQiw4REFBQ3JDLDZPQUFRQTs0Q0FBQ3FDLFdBQVU7Ozs7Ozt3Q0FBaUI7Ozs7Ozs7Ozs7OzswQ0FJekMsOERBQUMzQyw0REFBV0E7MENBQ1YsNEVBQUMwQztvQ0FBSUMsV0FBVTs4Q0FDWmpCLGNBQWN5RSxnQkFBZ0IsQ0FBQ1AsTUFBTSxHQUFHLElBQ3ZDbEUsY0FBY3lFLGdCQUFnQixDQUFDNUIsR0FBRyxDQUFDLENBQUM2Qix5QkFDbEMsOERBQUMxRDs0Q0FBc0JDLFdBQVU7OzhEQUMvQiw4REFBQ0Q7b0RBQUlDLFdBQVU7OERBQ2IsNEVBQUN4Qiw4T0FBU0E7d0RBQUN3QixXQUFVOzs7Ozs7Ozs7Ozs4REFFdkIsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0U7NERBQUVGLFdBQVU7c0VBQXFDeUQsU0FBU2pELEtBQUs7Ozs7OztzRUFDaEUsOERBQUNOOzREQUFFRixXQUFVOztnRUFBd0I7Z0VBQ3hCeUQsU0FBU0MsUUFBUSxDQUFDckIsSUFBSTs7Ozs7OztzRUFFbkMsOERBQUNuQzs0REFBRUYsV0FBVTs7Z0VBQXdCO2dFQUN6QixJQUFJa0MsS0FBS3VCLFNBQVNFLFdBQVcsRUFBRUwsa0JBQWtCOzs7Ozs7Ozs7Ozs7OzsyQ0FWdkRHLFNBQVN6QixFQUFFOzs7O2tFQWdCdkIsOERBQUNqQzt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUN4Qiw4T0FBU0E7Z0RBQUN3QixXQUFVOzs7Ozs7MERBQ3JCLDhEQUFDRTswREFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVNyQiIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zYWFzLWFwcC8uL2FwcC9kYXNoYm9hcmQvcGFnZS50c3g/ZDEyNSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlU2Vzc2lvbiB9IGZyb20gJ25leHQtYXV0aC9yZWFjdCdcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50LCBDYXJkSGVhZGVyLCBDYXJkVGl0bGUgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvY2FyZCdcbmltcG9ydCB7IEJhZGdlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2JhZGdlJ1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2J1dHRvbidcbmltcG9ydCB7IFByb2dyZXNzIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3Byb2dyZXNzJ1xuLy8gaW1wb3J0IHsgU2tlbGV0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvc2tlbGV0b24nXG5pbXBvcnQge1xuICBVc2VycyxcbiAgRmlsZVRleHQsXG4gIFJlY2VpcHQsXG4gIERvbGxhclNpZ24sXG4gIFVzZXJQbHVzLFxuICBBY3Rpdml0eSxcbiAgVHJlbmRpbmdVcCxcbiAgQXJyb3dVcFJpZ2h0LFxuICBBcnJvd0Rvd25SaWdodCxcbiAgUGx1cyxcbiAgQ2FsZW5kYXIsXG4gIENsb2NrLFxuICBBbGVydENpcmNsZSxcbiAgQ2hlY2tDaXJjbGUsXG4gIEZpbGVDaGVjayxcbiAgVGFyZ2V0LFxuICBCYXJDaGFydDNcbn0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuXG5pbnRlcmZhY2UgRGFzaGJvYXJkRGF0YSB7XG4gIHN0YXRzOiB7XG4gICAgY3VzdG9tZXJzOiB7IGN1cnJlbnQ6IG51bWJlcjsgY2hhbmdlOiBudW1iZXI7IHRvdGFsOiBudW1iZXIgfVxuICAgIGxlYWRzOiB7IGN1cnJlbnQ6IG51bWJlcjsgY2hhbmdlOiBudW1iZXI7IHRvdGFsOiBudW1iZXIgfVxuICAgIHF1b3RhdGlvbnM6IHsgY3VycmVudDogbnVtYmVyOyBjaGFuZ2U6IG51bWJlcjsgdG90YWw6IG51bWJlciB9XG4gICAgaW52b2ljZXM6IHsgY3VycmVudDogbnVtYmVyOyBjaGFuZ2U6IG51bWJlcjsgdG90YWw6IG51bWJlciB9XG4gICAgY29udHJhY3RzOiB7IGN1cnJlbnQ6IG51bWJlcjsgdG90YWw6IG51bWJlciB9XG4gICAgdGFza3M6IHsgY3VycmVudDogbnVtYmVyOyB0b3RhbDogbnVtYmVyIH1cbiAgICByZXZlbnVlOiB7IHRvdGFsOiBudW1iZXI7IGxhc3RNb250aDogbnVtYmVyOyBwZW5kaW5nOiBudW1iZXI7IGNoYW5nZTogbnVtYmVyIH1cbiAgfVxuICByZWNlbnRBY3Rpdml0aWVzOiBhbnlbXVxuICBsZWFkc0J5U3RhdHVzOiBhbnlbXVxuICBpbnZvaWNlc0J5U3RhdHVzOiBhbnlbXVxuICB0b3BDdXN0b21lcnM6IGFueVtdXG4gIHVwY29taW5nVGFza3M6IGFueVtdXG4gIHVwY29taW5nUmVuZXdhbHM6IGFueVtdXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIERhc2hib2FyZFBhZ2UoKSB7XG4gIGNvbnN0IHsgZGF0YTogc2Vzc2lvbiwgc3RhdHVzIH0gPSB1c2VTZXNzaW9uKClcbiAgY29uc3QgW2Rhc2hib2FyZERhdGEsIHNldERhc2hib2FyZERhdGFdID0gdXNlU3RhdGU8RGFzaGJvYXJkRGF0YSB8IG51bGw+KG51bGwpXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpXG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbClcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGZldGNoRGFzaGJvYXJkRGF0YSA9IGFzeW5jICgpID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIHNldExvYWRpbmcodHJ1ZSlcbiAgICAgICAgc2V0RXJyb3IobnVsbClcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9kYXNoYm9hcmQnKVxuICAgICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gZmV0Y2ggZGFzaGJvYXJkIGRhdGEnKVxuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKClcbiAgICAgICAgc2V0RGFzaGJvYXJkRGF0YShkYXRhKVxuICAgICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICAgIHNldEVycm9yKGVyciBpbnN0YW5jZW9mIEVycm9yID8gZXJyLm1lc3NhZ2UgOiAnQW4gZXJyb3Igb2NjdXJyZWQnKVxuICAgICAgfSBmaW5hbGx5IHtcbiAgICAgICAgc2V0TG9hZGluZyhmYWxzZSlcbiAgICAgIH1cbiAgICB9XG5cbiAgICBpZiAoc3RhdHVzID09PSAnbG9hZGluZycpIHtcbiAgICAgIHJldHVybiAvLyBEb24ndCBkbyBhbnl0aGluZyB3aGlsZSBzZXNzaW9uIGlzIGxvYWRpbmdcbiAgICB9XG5cbiAgICBpZiAoc2Vzc2lvbj8udXNlcj8uY29tcGFueUlkKSB7XG4gICAgICBmZXRjaERhc2hib2FyZERhdGEoKVxuICAgIH0gZWxzZSBpZiAoc2Vzc2lvbiAmJiAhc2Vzc2lvbi51c2VyPy5jb21wYW55SWQpIHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpXG4gICAgICBzZXRFcnJvcignTm8gY29tcGFueSBhc3NvY2lhdGVkIHdpdGggdXNlcicpXG4gICAgfSBlbHNlIGlmIChzdGF0dXMgPT09ICd1bmF1dGhlbnRpY2F0ZWQnKSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKVxuICAgICAgc2V0RXJyb3IoJ1BsZWFzZSBsb2cgaW4gdG8gdmlldyBkYXNoYm9hcmQnKVxuICAgIH1cbiAgfSwgW3Nlc3Npb24/LnVzZXI/LmNvbXBhbnlJZCwgc3RhdHVzXSlcblxuICBpZiAoc3RhdHVzID09PSAnbG9hZGluZycgfHwgbG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGgtNjRcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTEyIHctMTIgYm9yZGVyLWItMiBib3JkZXItYmx1ZS02MDAgbXgtYXV0byBtYi00XCI+PC9kaXY+XG4gICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi0yXCI+TG9hZGluZyBEYXNoYm9hcmQ8L2gzPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDBcIj5QbGVhc2Ugd2FpdCB3aGlsZSB3ZSBmZXRjaCB5b3VyIGRhdGEuLi48L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKVxuICB9XG5cbiAgaWYgKGVycm9yIHx8ICFkYXNoYm9hcmREYXRhKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgaC02NFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgPEFsZXJ0Q2lyY2xlIGNsYXNzTmFtZT1cImgtMTIgdy0xMiB0ZXh0LXJlZC01MDAgbXgtYXV0byBtYi00XCIgLz5cbiAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTJcIj5FcnJvciBMb2FkaW5nIERhc2hib2FyZDwvaDM+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMFwiPntlcnJvciB8fCAnRmFpbGVkIHRvIGxvYWQgZGFzaGJvYXJkIGRhdGEnfTwvcD5cbiAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB3aW5kb3cubG9jYXRpb24ucmVsb2FkKCl9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJtdC00XCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICBSZXRyeVxuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIClcbiAgfVxuXG4gIGNvbnN0IHN0YXRzID0gW1xuICAgIHtcbiAgICAgIHRpdGxlOiAnVG90YWwgQ3VzdG9tZXJzJyxcbiAgICAgIHZhbHVlOiBkYXNoYm9hcmREYXRhLnN0YXRzLmN1c3RvbWVycy50b3RhbC50b0xvY2FsZVN0cmluZygpLFxuICAgICAgY2hhbmdlOiBgJHtkYXNoYm9hcmREYXRhLnN0YXRzLmN1c3RvbWVycy5jaGFuZ2UgPj0gMCA/ICcrJyA6ICcnfSR7ZGFzaGJvYXJkRGF0YS5zdGF0cy5jdXN0b21lcnMuY2hhbmdlLnRvRml4ZWQoMSl9JWAsXG4gICAgICBjaGFuZ2VUeXBlOiBkYXNoYm9hcmREYXRhLnN0YXRzLmN1c3RvbWVycy5jaGFuZ2UgPj0gMCA/ICdpbmNyZWFzZScgYXMgY29uc3QgOiAnZGVjcmVhc2UnIGFzIGNvbnN0LFxuICAgICAgaWNvbjogVXNlcnMsXG4gICAgICBjb2xvcjogJ3RleHQtYmx1ZS02MDAnLFxuICAgICAgc3VidGl0bGU6IGAke2Rhc2hib2FyZERhdGEuc3RhdHMuY3VzdG9tZXJzLmN1cnJlbnR9IHRoaXMgbW9udGhgXG4gICAgfSxcbiAgICB7XG4gICAgICB0aXRsZTogJ0FjdGl2ZSBMZWFkcycsXG4gICAgICB2YWx1ZTogZGFzaGJvYXJkRGF0YS5zdGF0cy5sZWFkcy5jdXJyZW50LnRvTG9jYWxlU3RyaW5nKCksXG4gICAgICBjaGFuZ2U6IGAke2Rhc2hib2FyZERhdGEuc3RhdHMubGVhZHMuY2hhbmdlID49IDAgPyAnKycgOiAnJ30ke2Rhc2hib2FyZERhdGEuc3RhdHMubGVhZHMuY2hhbmdlLnRvRml4ZWQoMSl9JWAsXG4gICAgICBjaGFuZ2VUeXBlOiBkYXNoYm9hcmREYXRhLnN0YXRzLmxlYWRzLmNoYW5nZSA+PSAwID8gJ2luY3JlYXNlJyBhcyBjb25zdCA6ICdkZWNyZWFzZScgYXMgY29uc3QsXG4gICAgICBpY29uOiBVc2VyUGx1cyxcbiAgICAgIGNvbG9yOiAndGV4dC1ncmVlbi02MDAnLFxuICAgICAgc3VidGl0bGU6IGAke2Rhc2hib2FyZERhdGEuc3RhdHMubGVhZHMudG90YWx9IHRvdGFsIGxlYWRzYFxuICAgIH0sXG4gICAge1xuICAgICAgdGl0bGU6ICdRdW90YXRpb25zJyxcbiAgICAgIHZhbHVlOiBkYXNoYm9hcmREYXRhLnN0YXRzLnF1b3RhdGlvbnMuY3VycmVudC50b0xvY2FsZVN0cmluZygpLFxuICAgICAgY2hhbmdlOiBgJHtkYXNoYm9hcmREYXRhLnN0YXRzLnF1b3RhdGlvbnMuY2hhbmdlID49IDAgPyAnKycgOiAnJ30ke2Rhc2hib2FyZERhdGEuc3RhdHMucXVvdGF0aW9ucy5jaGFuZ2UudG9GaXhlZCgxKX0lYCxcbiAgICAgIGNoYW5nZVR5cGU6IGRhc2hib2FyZERhdGEuc3RhdHMucXVvdGF0aW9ucy5jaGFuZ2UgPj0gMCA/ICdpbmNyZWFzZScgYXMgY29uc3QgOiAnZGVjcmVhc2UnIGFzIGNvbnN0LFxuICAgICAgaWNvbjogRmlsZVRleHQsXG4gICAgICBjb2xvcjogJ3RleHQteWVsbG93LTYwMCcsXG4gICAgICBzdWJ0aXRsZTogYCR7ZGFzaGJvYXJkRGF0YS5zdGF0cy5xdW90YXRpb25zLnRvdGFsfSB0b3RhbGBcbiAgICB9LFxuICAgIHtcbiAgICAgIHRpdGxlOiAnUmV2ZW51ZScsXG4gICAgICB2YWx1ZTogYCQke2Rhc2hib2FyZERhdGEuc3RhdHMucmV2ZW51ZS50b3RhbC50b0xvY2FsZVN0cmluZygpfWAsXG4gICAgICBjaGFuZ2U6IGAke2Rhc2hib2FyZERhdGEuc3RhdHMucmV2ZW51ZS5jaGFuZ2UgPj0gMCA/ICcrJyA6ICcnfSR7ZGFzaGJvYXJkRGF0YS5zdGF0cy5yZXZlbnVlLmNoYW5nZS50b0ZpeGVkKDEpfSVgLFxuICAgICAgY2hhbmdlVHlwZTogZGFzaGJvYXJkRGF0YS5zdGF0cy5yZXZlbnVlLmNoYW5nZSA+PSAwID8gJ2luY3JlYXNlJyBhcyBjb25zdCA6ICdkZWNyZWFzZScgYXMgY29uc3QsXG4gICAgICBpY29uOiBEb2xsYXJTaWduLFxuICAgICAgY29sb3I6ICd0ZXh0LXB1cnBsZS02MDAnLFxuICAgICAgc3VidGl0bGU6IGAkJHtkYXNoYm9hcmREYXRhLnN0YXRzLnJldmVudWUucGVuZGluZy50b0xvY2FsZVN0cmluZygpfSBwZW5kaW5nYFxuICAgIH0sXG4gICAge1xuICAgICAgdGl0bGU6ICdBY3RpdmUgQ29udHJhY3RzJyxcbiAgICAgIHZhbHVlOiBkYXNoYm9hcmREYXRhLnN0YXRzLmNvbnRyYWN0cy5jdXJyZW50LnRvTG9jYWxlU3RyaW5nKCksXG4gICAgICBjaGFuZ2U6ICcnLFxuICAgICAgY2hhbmdlVHlwZTogJ25ldXRyYWwnIGFzIGNvbnN0LFxuICAgICAgaWNvbjogRmlsZUNoZWNrLFxuICAgICAgY29sb3I6ICd0ZXh0LWluZGlnby02MDAnLFxuICAgICAgc3VidGl0bGU6IGAke2Rhc2hib2FyZERhdGEuc3RhdHMuY29udHJhY3RzLnRvdGFsfSB0b3RhbGBcbiAgICB9LFxuICAgIHtcbiAgICAgIHRpdGxlOiAnT3BlbiBUYXNrcycsXG4gICAgICB2YWx1ZTogZGFzaGJvYXJkRGF0YS5zdGF0cy50YXNrcy5jdXJyZW50LnRvTG9jYWxlU3RyaW5nKCksXG4gICAgICBjaGFuZ2U6ICcnLFxuICAgICAgY2hhbmdlVHlwZTogJ25ldXRyYWwnIGFzIGNvbnN0LFxuICAgICAgaWNvbjogVGFyZ2V0LFxuICAgICAgY29sb3I6ICd0ZXh0LW9yYW5nZS02MDAnLFxuICAgICAgc3VidGl0bGU6IGAke2Rhc2hib2FyZERhdGEuc3RhdHMudGFza3MudG90YWx9IHRvdGFsIHRhc2tzYFxuICAgIH1cbiAgXVxuXG4gIC8vIFByb2Nlc3MgcmVjZW50IGFjdGl2aXRpZXNcbiAgY29uc3QgcHJvY2Vzc2VkQWN0aXZpdGllcyA9IGRhc2hib2FyZERhdGEucmVjZW50QWN0aXZpdGllcy5tYXAoKGFjdGl2aXR5OiBhbnkpID0+IHtcbiAgICBsZXQgbWVzc2FnZSA9IGFjdGl2aXR5LmRlc2NyaXB0aW9uXG4gICAgbGV0IGljb24gPSBBY3Rpdml0eVxuICAgIGxldCBzdGF0dXMgPSAnaW5mbydcblxuICAgIC8vIEN1c3RvbWl6ZSBiYXNlZCBvbiBhY3Rpdml0eSB0eXBlXG4gICAgc3dpdGNoIChhY3Rpdml0eS50eXBlKSB7XG4gICAgICBjYXNlICdOT1RFJzpcbiAgICAgICAgaWNvbiA9IEZpbGVUZXh0XG4gICAgICAgIHN0YXR1cyA9ICdpbmZvJ1xuICAgICAgICBicmVha1xuICAgICAgY2FzZSAnQ0FMTCc6XG4gICAgICAgIGljb24gPSBVc2Vyc1xuICAgICAgICBzdGF0dXMgPSAnc3VjY2VzcydcbiAgICAgICAgYnJlYWtcbiAgICAgIGNhc2UgJ0VNQUlMJzpcbiAgICAgICAgaWNvbiA9IFJlY2VpcHRcbiAgICAgICAgc3RhdHVzID0gJ2luZm8nXG4gICAgICAgIGJyZWFrXG4gICAgICBjYXNlICdNRUVUSU5HJzpcbiAgICAgICAgaWNvbiA9IENhbGVuZGFyXG4gICAgICAgIHN0YXR1cyA9ICd3YXJuaW5nJ1xuICAgICAgICBicmVha1xuICAgICAgY2FzZSAnU1RBVFVTX0NIQU5HRSc6XG4gICAgICAgIGljb24gPSBUcmVuZGluZ1VwXG4gICAgICAgIHN0YXR1cyA9ICdzdWNjZXNzJ1xuICAgICAgICBicmVha1xuICAgICAgY2FzZSAnUEFZTUVOVF9SRUNFSVZFRCc6XG4gICAgICAgIGljb24gPSBEb2xsYXJTaWduXG4gICAgICAgIHN0YXR1cyA9ICdzdWNjZXNzJ1xuICAgICAgICBicmVha1xuICAgICAgY2FzZSAnQ09OVFJBQ1RfU0lHTkVEJzpcbiAgICAgICAgaWNvbiA9IEZpbGVDaGVja1xuICAgICAgICBzdGF0dXMgPSAnc3VjY2VzcydcbiAgICAgICAgYnJlYWtcbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIGljb24gPSBBY3Rpdml0eVxuICAgICAgICBzdGF0dXMgPSAnaW5mbydcbiAgICB9XG5cbiAgICByZXR1cm4ge1xuICAgICAgaWQ6IGFjdGl2aXR5LmlkLFxuICAgICAgdHlwZTogYWN0aXZpdHkudHlwZSxcbiAgICAgIG1lc3NhZ2U6IGFjdGl2aXR5LnRpdGxlLFxuICAgICAgZGVzY3JpcHRpb246IGFjdGl2aXR5LmRlc2NyaXB0aW9uLFxuICAgICAgdGltZTogbmV3IERhdGUoYWN0aXZpdHkuY3JlYXRlZEF0KS50b0xvY2FsZVN0cmluZygpLFxuICAgICAgaWNvbixcbiAgICAgIHN0YXR1cyxcbiAgICAgIHVzZXI6IGFjdGl2aXR5LmNyZWF0ZWRCeT8ubmFtZSB8fCAnU3lzdGVtJ1xuICAgIH1cbiAgfSlcblxuICBjb25zdCBxdWlja0FjdGlvbnMgPSBbXG4gICAge1xuICAgICAgdGl0bGU6ICdBZGQgTGVhZCcsXG4gICAgICBkZXNjcmlwdGlvbjogJ1RyYWNrIGEgbmV3IGJ1c2luZXNzIGxlYWQnLFxuICAgICAgaWNvbjogVXNlclBsdXMsXG4gICAgICBocmVmOiAnL2Rhc2hib2FyZC9sZWFkcy9uZXcnLFxuICAgICAgY29sb3I6ICd0ZXh0LWdyZWVuLTYwMCdcbiAgICB9LFxuICAgIHtcbiAgICAgIHRpdGxlOiAnQWRkIEN1c3RvbWVyJyxcbiAgICAgIGRlc2NyaXB0aW9uOiAnQ3JlYXRlIGEgbmV3IGN1c3RvbWVyIHByb2ZpbGUnLFxuICAgICAgaWNvbjogVXNlcnMsXG4gICAgICBocmVmOiAnL2Rhc2hib2FyZC9jdXN0b21lcnMvbmV3JyxcbiAgICAgIGNvbG9yOiAndGV4dC1ibHVlLTYwMCdcbiAgICB9LFxuICAgIHtcbiAgICAgIHRpdGxlOiAnQ3JlYXRlIFF1b3RhdGlvbicsXG4gICAgICBkZXNjcmlwdGlvbjogJ0dlbmVyYXRlIGEgbmV3IHF1b3RhdGlvbicsXG4gICAgICBpY29uOiBGaWxlVGV4dCxcbiAgICAgIGhyZWY6ICcvZGFzaGJvYXJkL3F1b3RhdGlvbnMvbmV3JyxcbiAgICAgIGNvbG9yOiAndGV4dC15ZWxsb3ctNjAwJ1xuICAgIH0sXG4gICAge1xuICAgICAgdGl0bGU6ICdDcmVhdGUgSW52b2ljZScsXG4gICAgICBkZXNjcmlwdGlvbjogJ0dlbmVyYXRlIGEgbmV3IGludm9pY2UnLFxuICAgICAgaWNvbjogUmVjZWlwdCxcbiAgICAgIGhyZWY6ICcvZGFzaGJvYXJkL2ludm9pY2VzL25ldycsXG4gICAgICBjb2xvcjogJ3RleHQtcHVycGxlLTYwMCdcbiAgICB9LFxuICAgIHtcbiAgICAgIHRpdGxlOiAnTmV3IENvbnRyYWN0JyxcbiAgICAgIGRlc2NyaXB0aW9uOiAnQ3JlYXRlIGEgbmV3IGNvbnRyYWN0JyxcbiAgICAgIGljb246IEZpbGVDaGVjayxcbiAgICAgIGhyZWY6ICcvZGFzaGJvYXJkL2NvbnRyYWN0cy9uZXcnLFxuICAgICAgY29sb3I6ICd0ZXh0LWluZGlnby02MDAnXG4gICAgfSxcbiAgICB7XG4gICAgICB0aXRsZTogJ1ZpZXcgUmVwb3J0cycsXG4gICAgICBkZXNjcmlwdGlvbjogJ0FuYWx5emUgeW91ciBidXNpbmVzcyBkYXRhJyxcbiAgICAgIGljb246IEJhckNoYXJ0MyxcbiAgICAgIGhyZWY6ICcvZGFzaGJvYXJkL3JlcG9ydHMnLFxuICAgICAgY29sb3I6ICd0ZXh0LW9yYW5nZS02MDAnXG4gICAgfVxuICBdXG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgey8qIFdlbGNvbWUgU2VjdGlvbiAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNjAwIHRvLXB1cnBsZS02MDAgcm91bmRlZC1sZyBwLTYgdGV4dC13aGl0ZVwiPlxuICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIG1iLTJcIj5cbiAgICAgICAgICBXZWxjb21lIGJhY2ssIHtzZXNzaW9uPy51c2VyPy5uYW1lIHx8IHNlc3Npb24/LnVzZXI/LmVtYWlsIHx8ICdVc2VyJ30hXG4gICAgICAgIDwvaDE+XG4gICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtYmx1ZS0xMDBcIj5cbiAgICAgICAgICBIZXJlJ3Mgd2hhdCdzIGhhcHBlbmluZyB3aXRoIHlvdXIgYnVzaW5lc3MgdG9kYXkuXG4gICAgICAgIDwvcD5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC00IGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNCB0ZXh0LXNtXCI+XG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxCYWRnZSB2YXJpYW50PVwic2Vjb25kYXJ5XCIgY2xhc3NOYW1lPVwibXItMlwiPlxuICAgICAgICAgICAgICB7c2Vzc2lvbj8udXNlcj8ucm9sZSB8fCAnVVNFUid9XG4gICAgICAgICAgICA8L0JhZGdlPlxuICAgICAgICAgICAgUm9sZVxuICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgPEJhZGdlIHZhcmlhbnQ9XCJvdXRsaW5lXCIgY2xhc3NOYW1lPVwibXItMiB0ZXh0LXdoaXRlIGJvcmRlci13aGl0ZVwiPlxuICAgICAgICAgICAgICB7ZGFzaGJvYXJkRGF0YS5zdGF0cy5jdXN0b21lcnMudG90YWx9IEN1c3RvbWVyc1xuICAgICAgICAgICAgPC9CYWRnZT5cbiAgICAgICAgICAgIDxCYWRnZSB2YXJpYW50PVwib3V0bGluZVwiIGNsYXNzTmFtZT1cIm1yLTIgdGV4dC13aGl0ZSBib3JkZXItd2hpdGVcIj5cbiAgICAgICAgICAgICAge2Rhc2hib2FyZERhdGEuc3RhdHMubGVhZHMuY3VycmVudH0gQWN0aXZlIExlYWRzXG4gICAgICAgICAgICA8L0JhZGdlPlxuICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIFN0YXRzIEdyaWQgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTMgeGw6Z3JpZC1jb2xzLTYgZ2FwLTRcIj5cbiAgICAgICAge3N0YXRzLm1hcCgoc3RhdCkgPT4gKFxuICAgICAgICAgIDxDYXJkIGtleT17c3RhdC50aXRsZX0gY2xhc3NOYW1lPVwiaG92ZXI6c2hhZG93LW1kIHRyYW5zaXRpb24tc2hhZG93XCI+XG4gICAgICAgICAgICA8Q2FyZEhlYWRlciBjbGFzc05hbWU9XCJmbGV4IGZsZXgtcm93IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gc3BhY2UteS0wIHBiLTJcIj5cbiAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICB7c3RhdC50aXRsZX1cbiAgICAgICAgICAgICAgPC9DYXJkVGl0bGU+XG4gICAgICAgICAgICAgIDxzdGF0Lmljb24gY2xhc3NOYW1lPXtgaC01IHctNSAke3N0YXQuY29sb3J9YH0gLz5cbiAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPntzdGF0LnZhbHVlfTwvZGl2PlxuICAgICAgICAgICAgICB7c3RhdC5jaGFuZ2UgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAge3N0YXQuY2hhbmdlVHlwZSA9PT0gJ2luY3JlYXNlJyA/IChcbiAgICAgICAgICAgICAgICAgICAgPEFycm93VXBSaWdodCBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtZ3JlZW4tNjAwIG1yLTFcIiAvPlxuICAgICAgICAgICAgICAgICAgKSA6IHN0YXQuY2hhbmdlVHlwZSA9PT0gJ2RlY3JlYXNlJyA/IChcbiAgICAgICAgICAgICAgICAgICAgPEFycm93RG93blJpZ2h0IGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1yZWQtNjAwIG1yLTFcIiAvPlxuICAgICAgICAgICAgICAgICAgKSA6IG51bGx9XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2B0ZXh0LXNtIGZvbnQtbWVkaXVtICR7XG4gICAgICAgICAgICAgICAgICAgIHN0YXQuY2hhbmdlVHlwZSA9PT0gJ2luY3JlYXNlJyA/ICd0ZXh0LWdyZWVuLTYwMCcgOlxuICAgICAgICAgICAgICAgICAgICBzdGF0LmNoYW5nZVR5cGUgPT09ICdkZWNyZWFzZScgPyAndGV4dC1yZWQtNjAwJyA6ICd0ZXh0LWdyYXktNTAwJ1xuICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICB7c3RhdC5jaGFuZ2V9XG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICB7c3RhdC5jaGFuZ2VUeXBlICE9PSAnbmV1dHJhbCcgJiYgKFxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDAgbWwtMVwiPmZyb20gbGFzdCBtb250aDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIHtzdGF0LnN1YnRpdGxlICYmIChcbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDAgbXQtMVwiPntzdGF0LnN1YnRpdGxlfTwvcD5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgPC9DYXJkPlxuICAgICAgICApKX1cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogTWFpbiBDb250ZW50IEdyaWQgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbGc6Z3JpZC1jb2xzLTMgZ2FwLTZcIj5cbiAgICAgICAgey8qIFF1aWNrIEFjdGlvbnMgKi99XG4gICAgICAgIDxDYXJkPlxuICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICA8UGx1cyBjbGFzc05hbWU9XCJoLTUgdy01IG1yLTJcIiAvPlxuICAgICAgICAgICAgICBRdWljayBBY3Rpb25zXG4gICAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIGdhcC0zXCI+XG4gICAgICAgICAgICAgIHtxdWlja0FjdGlvbnMubWFwKChhY3Rpb24pID0+IChcbiAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICBrZXk9e2FjdGlvbi50aXRsZX1cbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtYXV0byBwLTMgZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIgc3BhY2UteS0yIGhvdmVyOmJnLWdyYXktNTBcIlxuICAgICAgICAgICAgICAgICAgYXNDaGlsZFxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxhIGhyZWY9e2FjdGlvbi5ocmVmfT5cbiAgICAgICAgICAgICAgICAgICAgPGFjdGlvbi5pY29uIGNsYXNzTmFtZT17YGgtNSB3LTUgJHthY3Rpb24uY29sb3J9YH0gLz5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC14c1wiPnthY3Rpb24udGl0bGV9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj57YWN0aW9uLmRlc2NyaXB0aW9ufTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICA8L0NhcmQ+XG5cbiAgICAgICAgey8qIFJlY2VudCBBY3Rpdml0eSAqL31cbiAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwibGc6Y29sLXNwYW4tMlwiPlxuICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICA8QWN0aXZpdHkgY2xhc3NOYW1lPVwiaC01IHctNSBtci0yXCIgLz5cbiAgICAgICAgICAgICAgUmVjZW50IEFjdGl2aXR5XG4gICAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTQgbWF4LWgtODAgb3ZlcmZsb3cteS1hdXRvXCI+XG4gICAgICAgICAgICAgIHtwcm9jZXNzZWRBY3Rpdml0aWVzLmxlbmd0aCA+IDAgPyAoXG4gICAgICAgICAgICAgICAgcHJvY2Vzc2VkQWN0aXZpdGllcy5tYXAoKGFjdGl2aXR5OiBhbnkpID0+IChcbiAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXthY3Rpdml0eS5pZH0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BwLTIgcm91bmRlZC1mdWxsICR7XG4gICAgICAgICAgICAgICAgICAgICAgYWN0aXZpdHkuc3RhdHVzID09PSAnc3VjY2VzcycgPyAnYmctZ3JlZW4tMTAwJyA6XG4gICAgICAgICAgICAgICAgICAgICAgYWN0aXZpdHkuc3RhdHVzID09PSAnd2FybmluZycgPyAnYmcteWVsbG93LTEwMCcgOlxuICAgICAgICAgICAgICAgICAgICAgICdiZy1ibHVlLTEwMCdcbiAgICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICAgIDxhY3Rpdml0eS5pY29uIGNsYXNzTmFtZT17YGgtNCB3LTQgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGFjdGl2aXR5LnN0YXR1cyA9PT0gJ3N1Y2Nlc3MnID8gJ3RleHQtZ3JlZW4tNjAwJyA6XG4gICAgICAgICAgICAgICAgICAgICAgICBhY3Rpdml0eS5zdGF0dXMgPT09ICd3YXJuaW5nJyA/ICd0ZXh0LXllbGxvdy02MDAnIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICd0ZXh0LWJsdWUtNjAwJ1xuICAgICAgICAgICAgICAgICAgICAgIH1gfSAvPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbWluLXctMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTkwMFwiPnthY3Rpdml0eS5tZXNzYWdlfTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj57YWN0aXZpdHkuZGVzY3JpcHRpb259PC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgbXQtMSB0ZXh0LXhzIHRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPnthY3Rpdml0eS51c2VyfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm14LTFcIj7igKI8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj57YWN0aXZpdHkudGltZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKSlcbiAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTggdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgICAgPEFjdGl2aXR5IGNsYXNzTmFtZT1cImgtMTIgdy0xMiBteC1hdXRvIG1iLTQgdGV4dC1ncmF5LTMwMFwiIC8+XG4gICAgICAgICAgICAgICAgICA8cD5ObyByZWNlbnQgYWN0aXZpdHk8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICA8L0NhcmQ+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIEFkZGl0aW9uYWwgU2VjdGlvbnMgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbGc6Z3JpZC1jb2xzLTIgZ2FwLTZcIj5cbiAgICAgICAgey8qIFVwY29taW5nIFRhc2tzICovfVxuICAgICAgICA8Q2FyZD5cbiAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPENsb2NrIGNsYXNzTmFtZT1cImgtNSB3LTUgbXItMlwiIC8+XG4gICAgICAgICAgICAgIFVwY29taW5nIFRhc2tzXG4gICAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICAgICAge2Rhc2hib2FyZERhdGEudXBjb21pbmdUYXNrcy5sZW5ndGggPiAwID8gKFxuICAgICAgICAgICAgICAgIGRhc2hib2FyZERhdGEudXBjb21pbmdUYXNrcy5tYXAoKHRhc2s6IGFueSkgPT4gKFxuICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e3Rhc2suaWR9IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMyBwLTMgYmctZ3JheS01MCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgcC0yIHJvdW5kZWQtZnVsbCAke1xuICAgICAgICAgICAgICAgICAgICAgIHRhc2sucHJpb3JpdHkgPT09ICdISUdIJyA/ICdiZy1yZWQtMTAwJyA6XG4gICAgICAgICAgICAgICAgICAgICAgdGFzay5wcmlvcml0eSA9PT0gJ01FRElVTScgPyAnYmcteWVsbG93LTEwMCcgOlxuICAgICAgICAgICAgICAgICAgICAgICdiZy1ncmVlbi0xMDAnXG4gICAgICAgICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICAgICAgICA8VGFyZ2V0IGNsYXNzTmFtZT17YGgtNCB3LTQgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRhc2sucHJpb3JpdHkgPT09ICdISUdIJyA/ICd0ZXh0LXJlZC02MDAnIDpcbiAgICAgICAgICAgICAgICAgICAgICAgIHRhc2sucHJpb3JpdHkgPT09ICdNRURJVU0nID8gJ3RleHQteWVsbG93LTYwMCcgOlxuICAgICAgICAgICAgICAgICAgICAgICAgJ3RleHQtZ3JlZW4tNjAwJ1xuICAgICAgICAgICAgICAgICAgICAgIH1gfSAvPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbWluLXctMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMFwiPnt0YXNrLnRpdGxlfTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIER1ZToge25ldyBEYXRlKHRhc2suZHVlRGF0ZSkudG9Mb2NhbGVEYXRlU3RyaW5nKCl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgIHt0YXNrLmFzc2lnbmVkVG8gJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIEFzc2lnbmVkIHRvOiB7dGFzay5hc3NpZ25lZFRvLm5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApKVxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktOCB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICA8Q2hlY2tDaXJjbGUgY2xhc3NOYW1lPVwiaC0xMiB3LTEyIG14LWF1dG8gbWItNCB0ZXh0LWdyYXktMzAwXCIgLz5cbiAgICAgICAgICAgICAgICAgIDxwPk5vIHVwY29taW5nIHRhc2tzPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgIHsvKiBDb250cmFjdCBSZW5ld2FscyAqL31cbiAgICAgICAgPENhcmQ+XG4gICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxGaWxlVGV4dCBjbGFzc05hbWU9XCJoLTUgdy01IG1yLTJcIiAvPlxuICAgICAgICAgICAgICBVcGNvbWluZyBSZW5ld2Fsc1xuICAgICAgICAgICAgPC9DYXJkVGl0bGU+XG4gICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgICAgIHtkYXNoYm9hcmREYXRhLnVwY29taW5nUmVuZXdhbHMubGVuZ3RoID4gMCA/IChcbiAgICAgICAgICAgICAgICBkYXNoYm9hcmREYXRhLnVwY29taW5nUmVuZXdhbHMubWFwKChjb250cmFjdDogYW55KSA9PiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2IGtleT17Y29udHJhY3QuaWR9IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMyBwLTMgYmctZ3JheS01MCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0yIHJvdW5kZWQtZnVsbCBiZy1vcmFuZ2UtMTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPEZpbGVDaGVjayBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtb3JhbmdlLTYwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBtaW4tdy0wXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwXCI+e2NvbnRyYWN0LnRpdGxlfTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIEN1c3RvbWVyOiB7Y29udHJhY3QuY3VzdG9tZXIubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICBSZW5ld2FsOiB7bmV3IERhdGUoY29udHJhY3QucmVuZXdhbERhdGUpLnRvTG9jYWxlRGF0ZVN0cmluZygpfVxuICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApKVxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktOCB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICA8RmlsZUNoZWNrIGNsYXNzTmFtZT1cImgtMTIgdy0xMiBteC1hdXRvIG1iLTQgdGV4dC1ncmF5LTMwMFwiIC8+XG4gICAgICAgICAgICAgICAgICA8cD5ObyB1cGNvbWluZyByZW5ld2FsczwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgIDwvQ2FyZD5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApXG59XG5cblxuIl0sIm5hbWVzIjpbInVzZVNlc3Npb24iLCJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsIkNhcmQiLCJDYXJkQ29udGVudCIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJCYWRnZSIsIkJ1dHRvbiIsIlVzZXJzIiwiRmlsZVRleHQiLCJSZWNlaXB0IiwiRG9sbGFyU2lnbiIsIlVzZXJQbHVzIiwiQWN0aXZpdHkiLCJUcmVuZGluZ1VwIiwiQXJyb3dVcFJpZ2h0IiwiQXJyb3dEb3duUmlnaHQiLCJQbHVzIiwiQ2FsZW5kYXIiLCJDbG9jayIsIkFsZXJ0Q2lyY2xlIiwiQ2hlY2tDaXJjbGUiLCJGaWxlQ2hlY2siLCJUYXJnZXQiLCJCYXJDaGFydDMiLCJEYXNoYm9hcmRQYWdlIiwiZGF0YSIsInNlc3Npb24iLCJzdGF0dXMiLCJkYXNoYm9hcmREYXRhIiwic2V0RGFzaGJvYXJkRGF0YSIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsImZldGNoRGFzaGJvYXJkRGF0YSIsInJlc3BvbnNlIiwiZmV0Y2giLCJvayIsIkVycm9yIiwianNvbiIsImVyciIsIm1lc3NhZ2UiLCJ1c2VyIiwiY29tcGFueUlkIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDMiLCJwIiwib25DbGljayIsIndpbmRvdyIsImxvY2F0aW9uIiwicmVsb2FkIiwic3RhdHMiLCJ0aXRsZSIsInZhbHVlIiwiY3VzdG9tZXJzIiwidG90YWwiLCJ0b0xvY2FsZVN0cmluZyIsImNoYW5nZSIsInRvRml4ZWQiLCJjaGFuZ2VUeXBlIiwiaWNvbiIsImNvbG9yIiwic3VidGl0bGUiLCJjdXJyZW50IiwibGVhZHMiLCJxdW90YXRpb25zIiwicmV2ZW51ZSIsInBlbmRpbmciLCJjb250cmFjdHMiLCJ0YXNrcyIsInByb2Nlc3NlZEFjdGl2aXRpZXMiLCJyZWNlbnRBY3Rpdml0aWVzIiwibWFwIiwiYWN0aXZpdHkiLCJkZXNjcmlwdGlvbiIsInR5cGUiLCJpZCIsInRpbWUiLCJEYXRlIiwiY3JlYXRlZEF0IiwiY3JlYXRlZEJ5IiwibmFtZSIsInF1aWNrQWN0aW9ucyIsImhyZWYiLCJoMSIsImVtYWlsIiwic3BhbiIsInZhcmlhbnQiLCJyb2xlIiwic3RhdCIsImFjdGlvbiIsImFzQ2hpbGQiLCJhIiwibGVuZ3RoIiwidXBjb21pbmdUYXNrcyIsInRhc2siLCJwcmlvcml0eSIsImR1ZURhdGUiLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJhc3NpZ25lZFRvIiwidXBjb21pbmdSZW5ld2FscyIsImNvbnRyYWN0IiwiY3VzdG9tZXIiLCJyZW5ld2FsRGF0ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/dashboard/dashboard-layout.tsx":
/*!***************************************************!*\
  !*** ./components/dashboard/dashboard-layout.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DashboardLayout: () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./sidebar */ \"(ssr)/./components/dashboard/sidebar.tsx\");\n/* harmony import */ var _header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./header */ \"(ssr)/./components/dashboard/header.tsx\");\n/* harmony import */ var _mobile_sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./mobile-sidebar */ \"(ssr)/./components/dashboard/mobile-sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ DashboardLayout auto */ \n\n\n\n\n\nfunction DashboardLayout({ children }) {\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [sidebarCollapsed, setSidebarCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const user = session?.user ? {\n        name: session.user.name,\n        email: session.user.email,\n        image: session.user.image,\n        role: session.user.role,\n        company: session.user.company\n    } : undefined;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen flex overflow-hidden bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden md:flex md:flex-shrink-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sidebar__WEBPACK_IMPORTED_MODULE_3__.Sidebar, {\n                    user: user,\n                    collapsed: sidebarCollapsed,\n                    onToggle: ()=>setSidebarCollapsed(!sidebarCollapsed)\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mobile_sidebar__WEBPACK_IMPORTED_MODULE_5__.MobileSidebar, {\n                isOpen: mobileMenuOpen,\n                onClose: ()=>setMobileMenuOpen(false),\n                user: user\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col w-0 flex-1 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_header__WEBPACK_IMPORTED_MODULE_4__.Header, {\n                        user: user,\n                        onMobileMenuToggle: ()=>setMobileMenuOpen(true)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 relative overflow-y-auto focus:outline-none\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-7xl mx-auto px-4 sm:px-6 md:px-8\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"bg-white border-t border-gray-200 px-4 py-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 md:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-center text-sm text-gray-500\",\n                                children: [\n                                    \"\\xa9 \",\n                                    new Date().getFullYear(),\n                                    \" Business SaaS. All rights reserved.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/dashboard/dashboard-layout.tsx\n");

/***/ }),

/***/ "(ssr)/./components/dashboard/header.tsx":
/*!*****************************************!*\
  !*** ./components/dashboard/header.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(ssr)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/avatar */ \"(ssr)/./components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building2_Crown_LogOut_Menu_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building2,Crown,LogOut,Menu,Search,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building2_Crown_LogOut_Menu_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building2,Crown,LogOut,Menu,Search,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building2_Crown_LogOut_Menu_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building2,Crown,LogOut,Menu,Search,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building2_Crown_LogOut_Menu_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building2,Crown,LogOut,Menu,Search,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building2_Crown_LogOut_Menu_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building2,Crown,LogOut,Menu,Search,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building2_Crown_LogOut_Menu_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building2,Crown,LogOut,Menu,Search,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building2_Crown_LogOut_Menu_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building2,Crown,LogOut,Menu,Search,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building2_Crown_LogOut_Menu_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building2,Crown,LogOut,Menu,Search,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n\n\n\n\n\n\n\nfunction Header({ user, onMobileMenuToggle, className }) {\n    const [notifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            title: \"New customer inquiry\",\n            time: \"2 min ago\",\n            unread: true\n        },\n        {\n            id: 2,\n            title: \"Invoice payment received\",\n            time: \"1 hour ago\",\n            unread: true\n        },\n        {\n            id: 3,\n            title: \"Quotation approved\",\n            time: \"3 hours ago\",\n            unread: false\n        }\n    ]);\n    const unreadCount = notifications.filter((n)=>n.unread).length;\n    const handleSignOut = ()=>{\n        (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.signOut)({\n            callbackUrl: \"/auth/signin\"\n        });\n    };\n    const getRoleBadge = (role)=>{\n        switch(role){\n            case \"SUPER_ADMIN\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    variant: \"destructive\",\n                    className: \"text-xs\",\n                    children: \"Super Admin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 16\n                }, this);\n            case \"ADMIN\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    variant: \"default\",\n                    className: \"text-xs\",\n                    children: \"Admin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 16\n                }, this);\n            case \"MANAGER\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    variant: \"secondary\",\n                    className: \"text-xs\",\n                    children: \"Manager\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 16\n                }, this);\n            default:\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: `bg-white border-b border-gray-200 px-4 py-3 ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            size: \"icon\",\n                            className: \"md:hidden\",\n                            onClick: onMobileMenuToggle,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building2_Crown_LogOut_Menu_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building2_Crown_LogOut_Menu_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search...\",\n                                        className: \"pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenu, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuTrigger, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building2_Crown_LogOut_Menu_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 17\n                                            }, this),\n                                            unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                variant: \"destructive\",\n                                                className: \"absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs\",\n                                                children: unreadCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuContent, {\n                                    align: \"end\",\n                                    className: \"w-80\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuLabel, {\n                                            children: \"Notifications\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuSeparator, {}, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, this),\n                                        notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                                                className: \"flex flex-col items-start p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between w-full\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: `text-sm ${notification.unread ? \"font-medium\" : \"\"}`,\n                                                                children: notification.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                                                lineNumber: 118,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            notification.unread && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-blue-600 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                                                lineNumber: 122,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                                        lineNumber: 117,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                        children: notification.time\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                                        lineNumber: 125,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, notification.id, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 17\n                                            }, this)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuSeparator, {}, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                                            className: \"text-center text-blue-600\",\n                                            children: \"View all notifications\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this),\n                        user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenu, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuTrigger, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        className: \"flex items-center space-x-2 px-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                                className: \"h-8 w-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                                        src: user.image || \"\",\n                                                        alt: user.name || \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                                        className: \"bg-blue-600 text-white\",\n                                                        children: user.name?.charAt(0)?.toUpperCase() || user.email?.charAt(0)?.toUpperCase() || \"U\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"hidden md:block text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: user.name || user.email\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                                                lineNumber: 148,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            getRoleBadge(user.role)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: user.company?.name || \"No Company\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuContent, {\n                                    align: \"end\",\n                                    className: \"w-56\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuLabel, {\n                                            children: \"My Account\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuSeparator, {}, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building2_Crown_LogOut_Menu_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Profile\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building2_Crown_LogOut_Menu_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Company Settings\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building2_Crown_LogOut_Menu_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Subscription\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building2_Crown_LogOut_Menu_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Settings\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuSeparator, {}, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                                            onClick: handleSignOut,\n                                            className: \"text-red-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building2_Crown_LogOut_Menu_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Sign out\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n            lineNumber: 69,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\header.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/dashboard/header.tsx\n");

/***/ }),

/***/ "(ssr)/./components/dashboard/mobile-sidebar.tsx":
/*!*************************************************!*\
  !*** ./components/dashboard/mobile-sidebar.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MobileSidebar: () => (/* binding */ MobileSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./sidebar */ \"(ssr)/./components/dashboard/sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ MobileSidebar auto */ \n\n\n\n\nfunction MobileSidebar({ isOpen, onClose, user }) {\n    // Close on escape key\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleEscape = (e)=>{\n            if (e.key === \"Escape\") {\n                onClose();\n            }\n        };\n        if (isOpen) {\n            document.addEventListener(\"keydown\", handleEscape);\n            document.body.style.overflow = \"hidden\";\n        }\n        return ()=>{\n            document.removeEventListener(\"keydown\", handleEscape);\n            document.body.style.overflow = \"unset\";\n        };\n    }, [\n        isOpen,\n        onClose\n    ]);\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 md:hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-gray-900/80 transition-opacity\",\n                onClick: onClose\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\mobile-sidebar.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-y-0 left-0 flex w-full max-w-xs\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative flex w-full flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute right-0 top-0 -mr-12 pt-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: onClose,\n                                className: \"text-white hover:text-white hover:bg-gray-600\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\mobile-sidebar.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\mobile-sidebar.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\mobile-sidebar.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sidebar__WEBPACK_IMPORTED_MODULE_3__.Sidebar, {\n                            user: user,\n                            collapsed: false\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\mobile-sidebar.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\mobile-sidebar.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\mobile-sidebar.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\mobile-sidebar.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/dashboard/mobile-sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/dashboard/sidebar.tsx":
/*!******************************************!*\
  !*** ./components/dashboard/sidebar.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/avatar */ \"(ssr)/./components/ui/avatar.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronLeft,ChevronRight,Crown,FileCheck,FileText,LayoutDashboard,Package,Receipt,Settings,Shield,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronLeft,ChevronRight,Crown,FileCheck,FileText,LayoutDashboard,Package,Receipt,Settings,Shield,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronLeft,ChevronRight,Crown,FileCheck,FileText,LayoutDashboard,Package,Receipt,Settings,Shield,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronLeft,ChevronRight,Crown,FileCheck,FileText,LayoutDashboard,Package,Receipt,Settings,Shield,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronLeft,ChevronRight,Crown,FileCheck,FileText,LayoutDashboard,Package,Receipt,Settings,Shield,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/receipt.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronLeft,ChevronRight,Crown,FileCheck,FileText,LayoutDashboard,Package,Receipt,Settings,Shield,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-check.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronLeft,ChevronRight,Crown,FileCheck,FileText,LayoutDashboard,Package,Receipt,Settings,Shield,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronLeft,ChevronRight,Crown,FileCheck,FileText,LayoutDashboard,Package,Receipt,Settings,Shield,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronLeft,ChevronRight,Crown,FileCheck,FileText,LayoutDashboard,Package,Receipt,Settings,Shield,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronLeft,ChevronRight,Crown,FileCheck,FileText,LayoutDashboard,Package,Receipt,Settings,Shield,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronLeft,ChevronRight,Crown,FileCheck,FileText,LayoutDashboard,Package,Receipt,Settings,Shield,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronLeft,ChevronRight,Crown,FileCheck,FileText,LayoutDashboard,Package,Receipt,Settings,Shield,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronLeft,ChevronRight,Crown,FileCheck,FileText,LayoutDashboard,Package,Receipt,Settings,Shield,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronLeft,ChevronRight,Crown,FileCheck,FileText,LayoutDashboard,Package,Receipt,Settings,Shield,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \n\n\n\n\n\n\n\nconst menuItems = [\n    {\n        title: \"Main\",\n        items: [\n            {\n                title: \"Dashboard\",\n                href: \"/dashboard\",\n                icon: _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n            },\n            {\n                title: \"Customers\",\n                href: \"/dashboard/customers\",\n                icon: _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n            },\n            {\n                title: \"Leads\",\n                href: \"/dashboard/leads\",\n                icon: _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n            }\n        ]\n    },\n    {\n        title: \"Documents\",\n        items: [\n            {\n                title: \"Quotations\",\n                href: \"/dashboard/quotations\",\n                icon: _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n            },\n            {\n                title: \"Invoices\",\n                href: \"/dashboard/invoices\",\n                icon: _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n            },\n            {\n                title: \"Contracts\",\n                href: \"/dashboard/contracts\",\n                icon: _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n            }\n        ]\n    },\n    {\n        title: \"Inventory\",\n        items: [\n            {\n                title: \"Items\",\n                href: \"/dashboard/items\",\n                icon: _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n            }\n        ]\n    },\n    {\n        title: \"Analytics\",\n        items: [\n            {\n                title: \"Reports\",\n                href: \"/dashboard/reports\",\n                icon: _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n            }\n        ]\n    },\n    {\n        title: \"Account\",\n        items: [\n            {\n                title: \"Subscription\",\n                href: \"/dashboard/subscription\",\n                icon: _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                badge: \"PRO\",\n                badgeVariant: \"secondary\"\n            },\n            {\n                title: \"Settings\",\n                href: \"/dashboard/settings\",\n                icon: _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n            }\n        ]\n    }\n];\nconst superAdminItems = [\n    {\n        title: \"Super Admin\",\n        items: [\n            {\n                title: \"Admin Panel\",\n                href: \"/super-admin\",\n                icon: _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                badge: \"ADMIN\",\n                badgeVariant: \"destructive\"\n            }\n        ]\n    }\n];\nfunction Sidebar({ user, collapsed = false, onToggle, className }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    // Combine menu items with super admin items if user is super admin\n    const allMenuItems = user?.role === \"SUPER_ADMIN\" ? [\n        ...menuItems,\n        ...superAdminItems\n    ] : menuItems;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex h-full flex-col bg-gray-900 text-white transition-all duration-300\", collapsed ? \"w-16\" : \"w-64\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-16 items-center justify-between px-4 border-b border-gray-800\",\n                children: [\n                    !collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"h-6 w-6 text-blue-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-semibold\",\n                                children: \"Business SaaS\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, this),\n                    onToggle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"ghost\",\n                        size: \"icon\",\n                        onClick: onToggle,\n                        className: \"text-gray-400 hover:text-white hover:bg-gray-800\",\n                        children: collapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 26\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 65\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, this),\n            user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.Avatar, {\n                            className: \"h-8 w-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarImage, {\n                                    src: user.image || \"\",\n                                    alt: user.name || \"\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarFallback, {\n                                    className: \"bg-blue-600 text-white\",\n                                    children: user.name?.charAt(0)?.toUpperCase() || user.email?.charAt(0)?.toUpperCase() || \"U\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 13\n                        }, this),\n                        !collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium text-white truncate\",\n                                    children: user.name || user.email\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400 truncate\",\n                                    children: user.company?.name || user.role || \"User\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                lineNumber: 177,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex-1 overflow-y-auto py-4\",\n                children: allMenuItems.map((group)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            !collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"px-4 mb-2 text-xs font-semibold text-gray-400 uppercase tracking-wider\",\n                                children: group.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-1 px-2\",\n                                children: group.items.map((item)=>{\n                                    const isActive = pathname === item.href;\n                                    const Icon = item.icon;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: item.href,\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex items-center px-2 py-2 text-sm rounded-md transition-colors\", isActive ? \"bg-blue-600 text-white\" : \"text-gray-300 hover:bg-gray-800 hover:text-white\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"h-5 w-5\", collapsed ? \"mx-auto\" : \"mr-3\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 23\n                                                }, this),\n                                                !collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex-1\",\n                                                            children: item.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                                            lineNumber: 227,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            variant: item.badgeVariant || \"default\",\n                                                            className: \"ml-2\",\n                                                            children: item.badge\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                                            lineNumber: 229,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, item.href, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, group.title, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n        lineNumber: 148,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/dashboard/sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/providers.tsx":
/*!**********************************!*\
  !*** ./components/providers.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.module.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _contexts_BrandingContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/BrandingContext */ \"(ssr)/./contexts/BrandingContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\n\n\nfunction Providers({ children }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(()=>new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.QueryClient({\n            defaultOptions: {\n                queries: {\n                    staleTime: 60 * 1000,\n                    retry: 1\n                }\n            }\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__.QueryClientProvider, {\n            client: queryClient,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_BrandingContext__WEBPACK_IMPORTED_MODULE_4__.BrandingProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                    attribute: \"class\",\n                    defaultTheme: \"light\",\n                    enableSystem: true,\n                    disableTransitionOnChange: true,\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\providers.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\providers.tsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\providers.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\providers.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/avatar.tsx":
/*!**********************************!*\
  !*** ./components/ui/avatar.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Avatar: () => (/* binding */ Avatar),\n/* harmony export */   AvatarFallback: () => (/* binding */ AvatarFallback),\n/* harmony export */   AvatarImage: () => (/* binding */ AvatarImage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-avatar */ \"(ssr)/./node_modules/@radix-ui/react-avatar/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Avatar,AvatarImage,AvatarFallback auto */ \n\n\n\nconst Avatar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\ui\\\\avatar.tsx\",\n        lineNumber: 12,\n        columnNumber: 3\n    }, undefined));\nAvatar.displayName = _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Root.displayName;\nconst AvatarImage = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Image, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"aspect-square h-full w-full\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\ui\\\\avatar.tsx\",\n        lineNumber: 27,\n        columnNumber: 3\n    }, undefined));\nAvatarImage.displayName = _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Image.displayName;\nconst AvatarFallback = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Fallback, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-full w-full items-center justify-center rounded-full bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\ui\\\\avatar.tsx\",\n        lineNumber: 39,\n        columnNumber: 3\n    }, undefined));\nAvatarFallback.displayName = _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Fallback.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/avatar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/badge.tsx":
/*!*********************************!*\
  !*** ./components/ui/badge.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\",\n            success: \"border-transparent bg-green-500 text-white hover:bg-green-600\",\n            warning: \"border-transparent bg-yellow-500 text-white hover:bg-yellow-600\",\n            info: \"border-transparent bg-blue-500 text-white hover:bg-blue-600\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 32,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 47,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 59,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 67,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/dropdown-menu.tsx":
/*!*****************************************!*\
  !*** ./components/ui/dropdown-menu.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DropdownMenu: () => (/* binding */ DropdownMenu),\n/* harmony export */   DropdownMenuCheckboxItem: () => (/* binding */ DropdownMenuCheckboxItem),\n/* harmony export */   DropdownMenuContent: () => (/* binding */ DropdownMenuContent),\n/* harmony export */   DropdownMenuGroup: () => (/* binding */ DropdownMenuGroup),\n/* harmony export */   DropdownMenuItem: () => (/* binding */ DropdownMenuItem),\n/* harmony export */   DropdownMenuLabel: () => (/* binding */ DropdownMenuLabel),\n/* harmony export */   DropdownMenuPortal: () => (/* binding */ DropdownMenuPortal),\n/* harmony export */   DropdownMenuRadioGroup: () => (/* binding */ DropdownMenuRadioGroup),\n/* harmony export */   DropdownMenuRadioItem: () => (/* binding */ DropdownMenuRadioItem),\n/* harmony export */   DropdownMenuSeparator: () => (/* binding */ DropdownMenuSeparator),\n/* harmony export */   DropdownMenuShortcut: () => (/* binding */ DropdownMenuShortcut),\n/* harmony export */   DropdownMenuSub: () => (/* binding */ DropdownMenuSub),\n/* harmony export */   DropdownMenuSubContent: () => (/* binding */ DropdownMenuSubContent),\n/* harmony export */   DropdownMenuSubTrigger: () => (/* binding */ DropdownMenuSubTrigger),\n/* harmony export */   DropdownMenuTrigger: () => (/* binding */ DropdownMenuTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-dropdown-menu */ \"(ssr)/./node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ DropdownMenu,DropdownMenuTrigger,DropdownMenuContent,DropdownMenuItem,DropdownMenuCheckboxItem,DropdownMenuRadioItem,DropdownMenuLabel,DropdownMenuSeparator,DropdownMenuShortcut,DropdownMenuGroup,DropdownMenuPortal,DropdownMenuSub,DropdownMenuSubContent,DropdownMenuSubTrigger,DropdownMenuRadioGroup auto */ \n\n\n\n\nconst DropdownMenu = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst DropdownMenuTrigger = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Trigger;\nconst DropdownMenuGroup = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Group;\nconst DropdownMenuPortal = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Portal;\nconst DropdownMenuSub = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Sub;\nconst DropdownMenuRadioGroup = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioGroup;\nconst DropdownMenuSubTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubTrigger, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent\", inset && \"pl-8\", className),\n        ...props,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"ml-auto h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 37,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 27,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSubTrigger.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubTrigger.displayName;\nconst DropdownMenuSubContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubContent, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 47,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSubContent.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubContent.displayName;\nconst DropdownMenuContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, sideOffset = 4, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Portal, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Content, {\n            ref: ref,\n            sideOffset: sideOffset,\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n            lineNumber: 64,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuContent.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\nconst DropdownMenuItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Item, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", inset && \"pl-8\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 83,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Item.displayName;\nconst DropdownMenuCheckboxItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, checked, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.CheckboxItem, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        checked: checked,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 108,\n                columnNumber: 5\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 99,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuCheckboxItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.CheckboxItem.displayName;\nconst DropdownMenuRadioItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioItem, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-2 w-2 fill-current\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 131,\n                columnNumber: 5\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 123,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuRadioItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioItem.displayName;\nconst DropdownMenuLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Label, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-2 py-1.5 text-sm font-semibold\", inset && \"pl-8\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 147,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuLabel.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Label.displayName;\nconst DropdownMenuSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Separator, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"-mx-1 my-1 h-px bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 163,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSeparator.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Separator.displayName;\nconst DropdownMenuShortcut = ({ className, ...props })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"ml-auto text-xs tracking-widest opacity-60\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 176,\n        columnNumber: 5\n    }, undefined);\n};\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/dropdown-menu.tsx\n");

/***/ }),

/***/ "(ssr)/./contexts/BrandingContext.tsx":
/*!**************************************!*\
  !*** ./contexts/BrandingContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BrandingProvider: () => (/* binding */ BrandingProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useBranding: () => (/* binding */ useBranding)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ BrandingProvider,useBranding,default auto */ \n\nconst defaultBranding = {\n    appName: \"SaaS Platform\",\n    logoUrl: \"\",\n    faviconUrl: \"\",\n    primaryColor: \"#3b82f6\",\n    secondaryColor: \"#64748b\",\n    accentColor: \"#10b981\",\n    backgroundColor: \"#ffffff\",\n    textColor: \"#1f2937\",\n    theme: \"light\",\n    fontFamily: \"Inter, sans-serif\",\n    customCss: \"\"\n};\nconst BrandingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction BrandingProvider({ children }) {\n    const [branding, setBranding] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultBranding);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchBranding();\n    }, []);\n    const fetchBranding = async ()=>{\n        try {\n            const response = await fetch(\"/api/global-config/branding\");\n            const data = await response.json();\n            if (data.success && data.branding) {\n                setBranding({\n                    ...defaultBranding,\n                    ...data.branding\n                });\n                applyBranding({\n                    ...defaultBranding,\n                    ...data.branding\n                });\n            } else {\n                applyBranding(defaultBranding);\n            }\n        } catch (error) {\n            console.error(\"Error fetching branding config:\", error);\n            applyBranding(defaultBranding);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const updateBranding = (config)=>{\n        const newBranding = {\n            ...branding,\n            ...config\n        };\n        setBranding(newBranding);\n        applyBranding(newBranding);\n    };\n    const applyBranding = (config)=>{\n        // Apply CSS custom properties\n        const root = document.documentElement;\n        root.style.setProperty(\"--primary-color\", config.primaryColor);\n        root.style.setProperty(\"--secondary-color\", config.secondaryColor);\n        root.style.setProperty(\"--accent-color\", config.accentColor);\n        root.style.setProperty(\"--background-color\", config.backgroundColor);\n        root.style.setProperty(\"--text-color\", config.textColor);\n        root.style.setProperty(\"--font-family\", config.fontFamily);\n        // Apply theme class\n        document.body.className = document.body.className.replace(/theme-\\w+/g, \"\");\n        document.body.classList.add(`theme-${config.theme}`);\n        // Update document title\n        document.title = config.appName;\n        // Update favicon\n        if (config.faviconUrl) {\n            let favicon = document.querySelector('link[rel=\"icon\"]');\n            if (!favicon) {\n                favicon = document.createElement(\"link\");\n                favicon.rel = \"icon\";\n                document.head.appendChild(favicon);\n            }\n            favicon.href = config.faviconUrl;\n        }\n        // Apply custom CSS\n        let customStyleElement = document.getElementById(\"custom-branding-css\");\n        if (config.customCss) {\n            if (!customStyleElement) {\n                customStyleElement = document.createElement(\"style\");\n                customStyleElement.id = \"custom-branding-css\";\n                document.head.appendChild(customStyleElement);\n            }\n            customStyleElement.textContent = config.customCss;\n        } else if (customStyleElement) {\n            customStyleElement.remove();\n        }\n        // Update meta theme-color for mobile browsers\n        let themeColorMeta = document.querySelector('meta[name=\"theme-color\"]');\n        if (!themeColorMeta) {\n            themeColorMeta = document.createElement(\"meta\");\n            themeColorMeta.name = \"theme-color\";\n            document.head.appendChild(themeColorMeta);\n        }\n        themeColorMeta.content = config.primaryColor;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BrandingContext.Provider, {\n        value: {\n            branding,\n            updateBranding,\n            loading\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\contexts\\\\BrandingContext.tsx\",\n        lineNumber: 126,\n        columnNumber: 5\n    }, this);\n}\nfunction useBranding() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(BrandingContext);\n    if (context === undefined) {\n        throw new Error(\"useBranding must be used within a BrandingProvider\");\n    }\n    return context;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BrandingContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/BrandingContext.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculatePercentage: () => (/* binding */ calculatePercentage),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   generateContractNumber: () => (/* binding */ generateContractNumber),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   generateInvoiceNumber: () => (/* binding */ generateInvoiceNumber),\n/* harmony export */   generateQuotationNumber: () => (/* binding */ generateQuotationNumber),\n/* harmony export */   getInitials: () => (/* binding */ getInitials),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   isValidPhone: () => (/* binding */ isValidPhone),\n/* harmony export */   slugify: () => (/* binding */ slugify),\n/* harmony export */   truncate: () => (/* binding */ truncate)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatCurrency(amount, currency = \"USD\", locale = \"en-US\") {\n    return new Intl.NumberFormat(locale, {\n        style: \"currency\",\n        currency\n    }).format(amount);\n}\nfunction formatDate(date, options = {}) {\n    return new Intl.DateTimeFormat(\"en-US\", {\n        year: \"numeric\",\n        month: \"short\",\n        day: \"numeric\",\n        ...options\n    }).format(new Date(date));\n}\nfunction generateId(prefix) {\n    const timestamp = Date.now().toString(36);\n    const randomStr = Math.random().toString(36).substring(2, 8);\n    return prefix ? `${prefix}_${timestamp}_${randomStr}` : `${timestamp}_${randomStr}`;\n}\nfunction slugify(text) {\n    return text.toLowerCase().replace(/[^\\w ]+/g, \"\").replace(/ +/g, \"-\");\n}\nfunction truncate(text, length = 100) {\n    if (text.length <= length) return text;\n    return text.substring(0, length) + \"...\";\n}\nfunction getInitials(name) {\n    return name.split(\" \").map((word)=>word.charAt(0)).join(\"\").toUpperCase().substring(0, 2);\n}\nfunction calculatePercentage(value, total) {\n    if (total === 0) return 0;\n    return Math.round(value / total * 100);\n}\nfunction debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\nfunction isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\nfunction isValidPhone(phone) {\n    const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/;\n    return phoneRegex.test(phone.replace(/\\s/g, \"\"));\n}\nfunction generateInvoiceNumber() {\n    const year = new Date().getFullYear();\n    const month = String(new Date().getMonth() + 1).padStart(2, \"0\");\n    const random = Math.floor(Math.random() * 9999).toString().padStart(4, \"0\");\n    return `INV-${year}${month}-${random}`;\n}\nfunction generateQuotationNumber() {\n    const year = new Date().getFullYear();\n    const month = String(new Date().getMonth() + 1).padStart(2, \"0\");\n    const random = Math.floor(Math.random() * 9999).toString().padStart(4, \"0\");\n    return `QUO-${year}${month}-${random}`;\n}\nfunction generateContractNumber() {\n    const year = new Date().getFullYear();\n    const month = String(new Date().getMonth() + 1).padStart(2, \"0\");\n    const random = Math.floor(Math.random() * 9999).toString().padStart(4, \"0\");\n    return `CON-${year}${month}-${random}`;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8f869fe146ad\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0anMtc2Fhcy1hcHAvLi9hcHAvZ2xvYmFscy5jc3M/YzYxMCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjhmODY5ZmUxNDZhZFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/dashboard/layout.tsx":
/*!**********************************!*\
  !*** ./app/dashboard/layout.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\proj\nextjs-saas\app\dashboard\layout.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\proj\nextjs-saas\app\dashboard\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/providers */ \"(rsc)/./components/providers.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\n\n\nconst metadata = {\n    title: {\n        default: \"Business SaaS - Complete Business Management Solution\",\n        template: \"%s | Business SaaS\"\n    },\n    description: \"Complete SaaS solution for business management including CRM, invoicing, quotations, and more.\",\n    keywords: [\n        \"SaaS\",\n        \"Business Management\",\n        \"CRM\",\n        \"Invoicing\",\n        \"Quotations\"\n    ],\n    authors: [\n        {\n            name: \"Business SaaS Team\"\n        }\n    ],\n    creator: \"Business SaaS\",\n    openGraph: {\n        type: \"website\",\n        locale: \"en_US\",\n        url: process.env.NEXT_PUBLIC_APP_URL,\n        title: \"Business SaaS - Complete Business Management Solution\",\n        description: \"Complete SaaS solution for business management including CRM, invoicing, quotations, and more.\",\n        siteName: \"Business SaaS\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Business SaaS - Complete Business Management Solution\",\n        description: \"Complete SaaS solution for business management including CRM, invoicing, quotations, and more.\",\n        creator: \"@businesssaas\"\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers__WEBPACK_IMPORTED_MODULE_1__.Providers, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n                        position: \"top-right\",\n                        toastOptions: {\n                            duration: 4000,\n                            style: {\n                                background: \"#363636\",\n                                color: \"#fff\"\n                            },\n                            success: {\n                                duration: 3000,\n                                iconTheme: {\n                                    primary: \"#4ade80\",\n                                    secondary: \"#fff\"\n                                }\n                            },\n                            error: {\n                                duration: 4000,\n                                iconTheme: {\n                                    primary: \"#ef4444\",\n                                    secondary: \"#fff\"\n                                }\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\layout.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\layout.tsx\",\n                lineNumber: 53,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\layout.tsx\",\n            lineNumber: 52,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\layout.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/providers.tsx":
/*!**********************************!*\
  !*** ./components/providers.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\proj\nextjs-saas\components\providers.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\proj\nextjs-saas\components\providers.tsx#Providers`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/@tanstack","vendor-chunks/react-hot-toast","vendor-chunks/next-themes","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/class-variance-authority","vendor-chunks/clsx","vendor-chunks/@floating-ui","vendor-chunks/tslib","vendor-chunks/react-remove-scroll","vendor-chunks/aria-hidden","vendor-chunks/react-remove-scroll-bar","vendor-chunks/use-sync-external-store","vendor-chunks/use-callback-ref","vendor-chunks/use-sidecar","vendor-chunks/react-style-singleton","vendor-chunks/get-nonce"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();