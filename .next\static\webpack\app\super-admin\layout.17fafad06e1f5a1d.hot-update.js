"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/super-admin/layout",{

/***/ "(app-pages-browser)/./app/super-admin/layout.tsx":
/*!************************************!*\
  !*** ./app/super-admin/layout.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SuperAdminLayoutWrapper; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_super_admin_super_admin_layout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/super-admin/super-admin-layout */ \"(app-pages-browser)/./components/super-admin/super-admin-layout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction SuperAdminLayoutWrapper(param) {\n    let { children } = param;\n    var _session_user;\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\layout.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\layout.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\layout.tsx\",\n                lineNumber: 17,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\layout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this);\n    }\n    if (status === \"unauthenticated\") {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.redirect)(\"/auth/signin\");\n    }\n    if ((session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.role) !== \"SUPER_ADMIN\") {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.redirect)(\"/dashboard\");\n    }\n    const user = (session === null || session === void 0 ? void 0 : session.user) ? {\n        name: session.user.name || \"\",\n        email: session.user.email || \"\",\n        image: session.user.image || \"\",\n        role: session.user.role || \"\",\n        company: (()=>{\n            try {\n                if (session.user.company && typeof session.user.company === \"object\") {\n                    return {\n                        name: String(session.user.company.name || \"\")\n                    };\n                }\n                return undefined;\n            } catch (error) {\n                console.error(\"Error processing company object:\", error);\n                return undefined;\n            }\n        })()\n    } : undefined;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_super_admin_super_admin_layout__WEBPACK_IMPORTED_MODULE_3__.SuperAdminLayout, {\n        user: user,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\layout.tsx\",\n        lineNumber: 51,\n        columnNumber: 10\n    }, this);\n}\n_s(SuperAdminLayoutWrapper, \"ujwIunAD3hlHFoJLG3BNiDLiMqM=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession\n    ];\n});\n_c = SuperAdminLayoutWrapper;\nvar _c;\n$RefreshReg$(_c, \"SuperAdminLayoutWrapper\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/super-admin/layout.tsx\n"));

/***/ })

});