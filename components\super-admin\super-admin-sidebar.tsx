'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  Shield,
  LayoutDashboard,
  Building2,
  Users,
  CreditCard,
  Settings,
  BarChart3,
  AlertTriangle,
  FileText,
  Globe,
  Database,
  ChevronLeft,
  ChevronRight,
  Crown,
  Activity,
  Lock,
  Zap,
  Palette,
  DollarSign,
  Mail,
  Bell,
  Key,
  Plug,
  FileImage,
  Code,
  Shield as ShieldCheck,
  Smartphone,
  QrCode,
  Banknote,
  Webhook,
  Monitor,
  Megaphone
} from 'lucide-react'

interface SuperAdminSidebarProps {
  user?: {
    name?: string | null
    email?: string | null
    image?: string | null
    role?: string
    company?: {
      name?: string
    }
  }
  collapsed?: boolean
  onToggle?: () => void
  className?: string
}

const menuItems = [
  {
    title: 'Overview',
    items: [
      {
        title: 'Dashboard',
        href: '/super-admin',
        icon: LayoutDashboard,
      },
      {
        title: 'System Health',
        href: '/super-admin/system-health',
        icon: Activity,
        badge: 'LIVE',
        badgeVariant: 'destructive' as const,
      },
    ],
  },
  {
    title: 'Business Management',
    items: [
      {
        title: 'Companies',
        href: '/super-admin/companies',
        icon: Building2,
      },
      {
        title: 'Users',
        href: '/super-admin/users',
        icon: Users,
      },
      {
        title: 'Subscriptions',
        href: '/super-admin/subscriptions',
        icon: CreditCard,
      },
      {
        title: 'Pricing Plans',
        href: '/super-admin/pricing-plans',
        icon: DollarSign,
      },
    ],
  },
  {
    title: 'SaaS Configuration',
    items: [
      {
        title: 'Global Config',
        href: '/super-admin/global-config',
        icon: Globe,
      },
      {
        title: 'Branding',
        href: '/super-admin/branding',
        icon: Palette,
      },
      {
        title: 'Payment Gateways',
        href: '/super-admin/payment-gateways',
        icon: Banknote,
      },
      {
        title: 'Payment Methods',
        href: '/super-admin/payment-methods',
        icon: Smartphone,
      },
    ],
  },
  {
    title: 'Content Management',
    items: [
      {
        title: 'Landing Page CMS',
        href: '/super-admin/cms',
        icon: Monitor,
      },
      {
        title: 'Email Templates',
        href: '/super-admin/email-templates',
        icon: Mail,
      },
      {
        title: 'Notifications',
        href: '/super-admin/notifications',
        icon: Bell,
      },
      {
        title: 'Media Library',
        href: '/super-admin/media',
        icon: FileImage,
      },
    ],
  },
  {
    title: 'Integrations',
    items: [
      {
        title: 'API Management',
        href: '/super-admin/api-management',
        icon: Key,
      },
      {
        title: 'Webhooks',
        href: '/super-admin/webhooks',
        icon: Webhook,
      },
      {
        title: 'Third-party Apps',
        href: '/super-admin/integrations',
        icon: Plug,
      },
    ],
  },
  {
    title: 'Analytics & Reports',
    items: [
      {
        title: 'Reports',
        href: '/super-admin/reports',
        icon: BarChart3,
      },
      {
        title: 'Performance',
        href: '/super-admin/performance',
        icon: Zap,
      },
      {
        title: 'Marketing Analytics',
        href: '/super-admin/marketing',
        icon: Megaphone,
      },
    ],
  },
  {
    title: 'Security & Compliance',
    items: [
      {
        title: 'Security Center',
        href: '/super-admin/security',
        icon: Lock,
      },
      {
        title: 'Audit Logs',
        href: '/super-admin/audit-logs',
        icon: FileText,
      },
      {
        title: 'Compliance',
        href: '/super-admin/compliance',
        icon: ShieldCheck,
      },
      {
        title: 'Alerts',
        href: '/super-admin/alerts',
        icon: AlertTriangle,
        badge: '3',
        badgeVariant: 'destructive' as const,
      },
    ],
  },
  {
    title: 'System',
    items: [
      {
        title: 'Settings',
        href: '/super-admin/settings',
        icon: Settings,
      },
      {
        title: 'Database',
        href: '/super-admin/database',
        icon: Database,
      },
      {
        title: 'Developer Tools',
        href: '/super-admin/developer',
        icon: Code,
      },
    ],
  },
]

export function SuperAdminSidebar({ user, collapsed = false, onToggle, className }: SuperAdminSidebarProps) {
  const pathname = usePathname()

  return (
    <div
      className={cn(
        'flex h-full flex-col bg-gray-900 text-white transition-all duration-300',
        collapsed ? 'w-16' : 'w-64',
        className
      )}
    >
      {/* Header */}
      <div className="flex h-16 items-center justify-between px-4 border-b border-gray-800">
        {!collapsed && (
          <div className="flex items-center space-x-2">
            <Shield className="h-6 w-6 text-red-400" />
            <span className="font-semibold">Super Admin</span>
          </div>
        )}
        {onToggle && (
          <Button
            variant="ghost"
            size="icon"
            onClick={onToggle}
            className="text-gray-400 hover:text-white hover:bg-gray-800"
          >
            {collapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
          </Button>
        )}
      </div>

      {/* User Profile */}
      {user && (
        <div className="p-4 border-b border-gray-800">
          <div className="flex items-center space-x-3">
            <Avatar className="h-8 w-8">
              <AvatarImage src={user.image || ''} alt={user.name || ''} />
              <AvatarFallback className="bg-red-600 text-white">
                {user.name?.charAt(0)?.toUpperCase() || user.email?.charAt(0)?.toUpperCase() || 'SA'}
              </AvatarFallback>
            </Avatar>
            {!collapsed && (
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-white truncate">
                  {user.name || user.email}
                </p>
                <div className="flex items-center space-x-1">
                  <Crown className="h-3 w-3 text-yellow-400" />
                  <p className="text-xs text-yellow-400">Super Admin</p>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Navigation */}
      <nav className="flex-1 overflow-y-auto py-4">
        {menuItems.map((group) => (
          <div key={group.title} className="mb-6">
            {!collapsed && (
              <h3 className="px-4 mb-2 text-xs font-semibold text-gray-400 uppercase tracking-wider">
                {group.title}
              </h3>
            )}
            <ul className="space-y-1 px-2">
              {group.items.map((item) => {
                const isActive = pathname === item.href
                const Icon = item.icon

                return (
                  <li key={item.href}>
                    <Link
                      href={item.href}
                      className={cn(
                        'flex items-center px-2 py-2 text-sm rounded-md transition-colors',
                        isActive
                          ? 'bg-red-600 text-white'
                          : 'text-gray-300 hover:bg-gray-800 hover:text-white'
                      )}
                    >
                      <Icon className={cn('h-5 w-5', collapsed ? 'mx-auto' : 'mr-3')} />
                      {!collapsed && (
                        <>
                          <span className="flex-1">{item.title}</span>
                          {item.badge && (
                            <Badge variant={item.badgeVariant || 'default'} className="ml-2">
                              {item.badge}
                            </Badge>
                          )}
                        </>
                      )}
                    </Link>
                  </li>
                )
              })}
            </ul>
          </div>
        ))}
      </nav>

      {/* Quick Actions */}
      {!collapsed && (
        <div className="p-4 border-t border-gray-800">
          <div className="space-y-2">
            <Link
              href="/dashboard"
              className="flex items-center px-3 py-2 text-sm text-gray-300 hover:text-white hover:bg-gray-800 rounded-md transition-colors"
            >
              <LayoutDashboard className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Link>
          </div>
        </div>
      )}
    </div>
  )
}
