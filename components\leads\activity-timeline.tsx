'use client'

import { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Activity,
  Phone,
  Mail,
  Calendar,
  FileText,
  CheckSquare,
  Plus,
  MoreHorizontal,
  Edit,
  Trash2,
  Clock,
  User,
  Tag,
  AlertCircle,
  CheckCircle,
  XCircle
} from 'lucide-react'
import { toast } from 'react-hot-toast'
import { ActivityForm } from './activity-form'

interface ActivityData {
  id: string
  type: 'NOTE' | 'CALL' | 'EMAIL' | 'MEETING' | 'TASK'
  title: string
  description: string | null
  status: 'PENDING' | 'COMPLETED' | 'CANCELLED'
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
  scheduledAt: string | null
  completedAt: string | null
  duration: number | null
  outcome: string | null
  followUpRequired: boolean
  followUpDate: string | null
  tags: string[]
  createdAt: string
  updatedAt: string
  createdBy: {
    id: string
    name: string | null
    email: string
  }
  assignedTo: {
    id: string
    name: string | null
    email: string
  } | null
}

interface ActivityTimelineProps {
  leadId: string
}

export function ActivityTimeline({ leadId }: ActivityTimelineProps) {
  const [activities, setActivities] = useState<ActivityData[]>([])
  const [loading, setLoading] = useState(true)
  const [showForm, setShowForm] = useState(false)
  const [editingActivity, setEditingActivity] = useState<ActivityData | null>(null)
  const [filter, setFilter] = useState<string>('all')

  const fetchActivities = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        limit: '50'
      })
      
      if (filter !== 'all') {
        params.append('type', filter)
      }

      const response = await fetch(`/api/leads/${leadId}/activities?${params}`)
      if (!response.ok) {
        throw new Error('Failed to fetch activities')
      }

      const data = await response.json()
      setActivities(data.activities)
    } catch (error) {
      toast.error('Failed to load activities')
      console.error('Error fetching activities:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (leadId) {
      fetchActivities()
    }
  }, [leadId, filter])

  const handleDelete = async (activityId: string) => {
    if (!confirm('Are you sure you want to delete this activity?')) return

    try {
      const response = await fetch(`/api/activities/${activityId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error('Failed to delete activity')
      }

      toast.success('Activity deleted successfully')
      fetchActivities()
    } catch (error) {
      toast.error('Failed to delete activity')
      console.error('Error deleting activity:', error)
    }
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'NOTE':
        return <FileText className="h-4 w-4" />
      case 'CALL':
        return <Phone className="h-4 w-4" />
      case 'EMAIL':
        return <Mail className="h-4 w-4" />
      case 'MEETING':
        return <Calendar className="h-4 w-4" />
      case 'TASK':
        return <CheckSquare className="h-4 w-4" />
      default:
        return <Activity className="h-4 w-4" />
    }
  }

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'NOTE':
        return 'text-blue-600 bg-blue-100'
      case 'CALL':
        return 'text-green-600 bg-green-100'
      case 'EMAIL':
        return 'text-purple-600 bg-purple-100'
      case 'MEETING':
        return 'text-orange-600 bg-orange-100'
      case 'TASK':
        return 'text-red-600 bg-red-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'PENDING':
        return <Clock className="h-4 w-4 text-yellow-600" />
      case 'CANCELLED':
        return <XCircle className="h-4 w-4 text-red-600" />
      default:
        return <AlertCircle className="h-4 w-4 text-gray-600" />
    }
  }

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'LOW':
        return <Badge variant="secondary" className="text-xs">Low</Badge>
      case 'MEDIUM':
        return <Badge className="bg-blue-100 text-blue-800 text-xs">Medium</Badge>
      case 'HIGH':
        return <Badge className="bg-orange-100 text-orange-800 text-xs">High</Badge>
      case 'URGENT':
        return <Badge variant="destructive" className="text-xs">Urgent</Badge>
      default:
        return <Badge variant="secondary" className="text-xs">{priority}</Badge>
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h3 className="text-lg font-medium">Activity Timeline</h3>
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className="text-sm border rounded px-2 py-1"
          >
            <option value="all">All Activities</option>
            <option value="NOTE">Notes</option>
            <option value="CALL">Calls</option>
            <option value="EMAIL">Emails</option>
            <option value="MEETING">Meetings</option>
            <option value="TASK">Tasks</option>
          </select>
        </div>
        <Button onClick={() => setShowForm(true)} size="sm">
          <Plus className="h-4 w-4 mr-2" />
          Add Activity
        </Button>
      </div>

      {/* Timeline */}
      <div className="space-y-4">
        {activities.length === 0 ? (
          <Card>
            <CardContent className="py-8 text-center text-gray-500">
              <Activity className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No activities found</p>
              <p className="text-sm">Start by adding a note, call, or meeting</p>
            </CardContent>
          </Card>
        ) : (
          activities.map((activity) => (
            <Card key={activity.id} className="relative">
              <CardContent className="p-4">
                <div className="flex items-start space-x-4">
                  {/* Activity Icon */}
                  <div className={`p-2 rounded-full ${getActivityColor(activity.type)}`}>
                    {getActivityIcon(activity.type)}
                  </div>

                  {/* Activity Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <h4 className="font-medium text-gray-900">{activity.title}</h4>
                          {getPriorityBadge(activity.priority)}
                          {getStatusIcon(activity.status)}
                        </div>
                        
                        {activity.description && (
                          <p className="text-sm text-gray-600 mb-2">{activity.description}</p>
                        )}

                        {/* Activity Details */}
                        <div className="flex flex-wrap items-center gap-4 text-xs text-gray-500 mb-2">
                          <div className="flex items-center space-x-1">
                            <User className="h-3 w-3" />
                            <span>{activity.createdBy.name || activity.createdBy.email}</span>
                          </div>
                          
                          <div className="flex items-center space-x-1">
                            <Clock className="h-3 w-3" />
                            <span>{new Date(activity.createdAt).toLocaleString()}</span>
                          </div>

                          {activity.scheduledAt && (
                            <div className="flex items-center space-x-1">
                              <Calendar className="h-3 w-3" />
                              <span>Scheduled: {new Date(activity.scheduledAt).toLocaleString()}</span>
                            </div>
                          )}

                          {activity.duration && (
                            <div className="flex items-center space-x-1">
                              <Clock className="h-3 w-3" />
                              <span>{activity.duration} minutes</span>
                            </div>
                          )}
                        </div>

                        {/* Outcome */}
                        {activity.outcome && (
                          <div className="bg-gray-50 rounded p-2 mb-2">
                            <p className="text-sm text-gray-700">
                              <strong>Outcome:</strong> {activity.outcome}
                            </p>
                          </div>
                        )}

                        {/* Follow-up */}
                        {activity.followUpRequired && activity.followUpDate && (
                          <div className="bg-yellow-50 border border-yellow-200 rounded p-2 mb-2">
                            <p className="text-sm text-yellow-800">
                              <strong>Follow-up required:</strong> {new Date(activity.followUpDate).toLocaleDateString()}
                            </p>
                          </div>
                        )}

                        {/* Tags */}
                        {activity.tags.length > 0 && (
                          <div className="flex items-center space-x-1 mb-2">
                            <Tag className="h-3 w-3 text-gray-400" />
                            <div className="flex flex-wrap gap-1">
                              {activity.tags.map((tag, index) => (
                                <Badge key={index} variant="outline" className="text-xs">
                                  {tag}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Actions */}
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => setEditingActivity(activity)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => handleDelete(activity.id)}
                            className="text-red-600"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Activity Form */}
      {(showForm || editingActivity) && (
        <ActivityForm
          isOpen={showForm || !!editingActivity}
          onClose={() => {
            setShowForm(false)
            setEditingActivity(null)
          }}
          onSuccess={() => {
            setShowForm(false)
            setEditingActivity(null)
            fetchActivities()
          }}
          leadId={leadId}
          activity={editingActivity}
          mode={editingActivity ? 'edit' : 'create'}
        />
      )}
    </div>
  )
}
