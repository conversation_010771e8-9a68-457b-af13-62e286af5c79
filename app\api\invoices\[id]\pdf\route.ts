import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET /api/invoices/[id]/pdf - Generate PDF for invoice
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Fetch invoice with all related data
    const invoice = await prisma.invoice.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId
      },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            company: true,
            address: true,
            city: true,
            state: true,
            country: true,
            postalCode: true
          }
        },
        items: {
          orderBy: { createdAt: 'asc' }
        },
        createdBy: {
          select: {
            name: true,
            email: true
          }
        },
        companyRef: {
          select: {
            name: true,
            email: true,
            phone: true,
            address: true,
            city: true,
            state: true,
            country: true,
            postalCode: true,
            website: true,
            logo: true
          }
        },
        transactions: {
          where: { type: 'PAYMENT' },
          orderBy: { createdAt: 'desc' }
        }
      }
    })

    if (!invoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 })
    }

    // Calculate totals
    const itemTotals = invoice.items.map(item => {
      const lineTotal = Number(item.quantity) * Number(item.unitPrice)
      const discountAmount = (lineTotal * Number(item.discount)) / 100
      const subtotal = lineTotal - discountAmount
      const taxAmount = (subtotal * Number(item.taxRate)) / 100
      const total = subtotal + taxAmount

      return {
        ...item,
        lineTotal: Math.round(lineTotal * 100) / 100,
        discountAmount: Math.round(discountAmount * 100) / 100,
        subtotal: Math.round(subtotal * 100) / 100,
        taxAmount: Math.round(taxAmount * 100) / 100,
        total: Math.round(total * 100) / 100
      }
    })

    const subtotal = itemTotals.reduce((sum, item) => sum + item.subtotal, 0)
    const totalTax = itemTotals.reduce((sum, item) => sum + item.taxAmount, 0)
    const grandTotal = Number(invoice.total)
    const paidAmount = Number(invoice.paidAmount)
    const balanceDue = grandTotal - paidAmount

    // Calculate payment history
    const payments = invoice.transactions.map(payment => ({
      id: payment.id,
      amount: Number(payment.amount),
      date: payment.date,
      method: payment.method,
      reference: payment.reference
    }))

    // Generate HTML for PDF
    const html = generateInvoiceHTML({
      invoice,
      customer: invoice.customer,
      company: invoice.companyRef,
      items: itemTotals,
      payments,
      totals: {
        subtotal: Math.round(subtotal * 100) / 100,
        totalTax: Math.round(totalTax * 100) / 100,
        grandTotal: Math.round(grandTotal * 100) / 100,
        paidAmount: Math.round(paidAmount * 100) / 100,
        balanceDue: Math.round(balanceDue * 100) / 100
      }
    })

    // For now, return HTML (in production, you'd use a PDF library like Puppeteer)
    return new Response(html, {
      headers: {
        'Content-Type': 'text/html',
        'Content-Disposition': `inline; filename="invoice-${invoice.invoiceNumber}.html"`
      }
    })

  } catch (error) {
    console.error('Error generating invoice PDF:', error)
    return NextResponse.json(
      { error: 'Failed to generate PDF' },
      { status: 500 }
    )
  }
}

function generateInvoiceHTML(data: any) {
  const { invoice, customer, company, items, payments, totals } = data

  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice ${invoice.invoiceNumber}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 40px;
            border-bottom: 3px solid #e5e7eb;
            padding-bottom: 20px;
        }
        .company-info {
            flex: 1;
        }
        .company-name {
            font-size: 28px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 10px;
        }
        .invoice-info {
            text-align: right;
            flex: 1;
        }
        .invoice-title {
            font-size: 32px;
            font-weight: bold;
            color: #dc2626;
            margin-bottom: 10px;
        }
        .invoice-number {
            font-size: 20px;
            color: #6b7280;
            margin-bottom: 5px;
        }
        .customer-section {
            margin: 30px 0;
            padding: 20px;
            background-color: #f9fafb;
            border-radius: 8px;
            border-left: 4px solid #3b82f6;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 15px;
            border-bottom: 1px solid #d1d5db;
            padding-bottom: 5px;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .items-table th,
        .items-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        .items-table th {
            background-color: #f3f4f6;
            font-weight: bold;
            color: #374151;
            border-bottom: 2px solid #d1d5db;
        }
        .items-table .number {
            text-align: right;
        }
        .totals-section {
            margin-top: 30px;
            padding: 20px;
            background-color: #f9fafb;
            border-radius: 8px;
        }
        .totals-table {
            width: 100%;
            max-width: 400px;
            margin-left: auto;
        }
        .totals-table td {
            padding: 8px 12px;
            border: none;
        }
        .totals-table .label {
            text-align: right;
            font-weight: 500;
        }
        .totals-table .amount {
            text-align: right;
            font-weight: bold;
        }
        .grand-total {
            border-top: 2px solid #374151;
            font-size: 18px;
            color: #1f2937;
        }
        .balance-due {
            background-color: #fee2e2;
            border: 2px solid #fca5a5;
            font-size: 20px;
            color: #dc2626;
        }
        .paid-in-full {
            background-color: #d1fae5;
            border: 2px solid #86efac;
            font-size: 20px;
            color: #065f46;
        }
        .payments-section {
            margin-top: 30px;
            padding: 20px;
            background-color: #f0f9ff;
            border-radius: 8px;
            border-left: 4px solid #0ea5e9;
        }
        .payment-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #e0f2fe;
        }
        .payment-item:last-child {
            border-bottom: none;
        }
        .terms-section {
            margin-top: 40px;
            padding: 20px;
            background-color: #fef3c7;
            border-radius: 8px;
            border-left: 4px solid #f59e0b;
        }
        .notes-section {
            margin-top: 20px;
            padding: 20px;
            background-color: #eff6ff;
            border-radius: 8px;
            border-left: 4px solid #3b82f6;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            color: #6b7280;
            font-size: 14px;
            border-top: 1px solid #e5e7eb;
            padding-top: 20px;
        }
        .status-badge {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-draft { background-color: #f3f4f6; color: #374151; }
        .status-sent { background-color: #dbeafe; color: #1d4ed8; }
        .status-paid { background-color: #d1fae5; color: #065f46; }
        .status-overdue { background-color: #fee2e2; color: #dc2626; }
        .status-cancelled { background-color: #fed7aa; color: #ea580c; }
        .overdue-notice {
            background-color: #fee2e2;
            border: 2px solid #fca5a5;
            color: #dc2626;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-info">
            <div class="company-name">${company.name}</div>
            <div>${company.address || ''}</div>
            <div>${company.city || ''}, ${company.state || ''} ${company.postalCode || ''}</div>
            <div>${company.country || ''}</div>
            <div>Email: ${company.email || ''}</div>
            <div>Phone: ${company.phone || ''}</div>
            ${company.website ? `<div>Website: ${company.website}</div>` : ''}
        </div>
        <div class="invoice-info">
            <div class="invoice-title">INVOICE</div>
            <div class="invoice-number">${invoice.invoiceNumber}</div>
            <div>Issue Date: ${new Date(invoice.issueDate || invoice.createdAt).toLocaleDateString()}</div>
            ${invoice.dueDate ? `<div>Due Date: ${new Date(invoice.dueDate).toLocaleDateString()}</div>` : ''}
            <div class="status-badge status-${invoice.status.toLowerCase()}">${invoice.status}</div>
        </div>
    </div>

    ${invoice.status === 'OVERDUE' ? `
    <div class="overdue-notice">
        ⚠️ THIS INVOICE IS OVERDUE ⚠️<br>
        Please remit payment immediately to avoid additional charges.
    </div>
    ` : ''}

    <div class="customer-section">
        <div class="section-title">Bill To:</div>
        <div><strong>${customer.name}</strong></div>
        ${customer.company ? `<div>${customer.company}</div>` : ''}
        ${customer.address ? `<div>${customer.address}</div>` : ''}
        ${customer.city || customer.state || customer.postalCode ? 
          `<div>${customer.city || ''}, ${customer.state || ''} ${customer.postalCode || ''}</div>` : ''}
        ${customer.country ? `<div>${customer.country}</div>` : ''}
        ${customer.email ? `<div>Email: ${customer.email}</div>` : ''}
        ${customer.phone ? `<div>Phone: ${customer.phone}</div>` : ''}
    </div>

    ${invoice.title ? `
    <div class="section-title">Project: ${invoice.title}</div>
    ` : ''}

    <table class="items-table">
        <thead>
            <tr>
                <th>Description</th>
                <th class="number">Qty</th>
                <th class="number">Unit Price</th>
                <th class="number">Discount</th>
                <th class="number">Tax</th>
                <th class="number">Total</th>
            </tr>
        </thead>
        <tbody>
            ${items.map(item => `
                <tr>
                    <td>
                        <strong>${item.name}</strong>
                        ${item.description ? `<br><small>${item.description}</small>` : ''}
                    </td>
                    <td class="number">${Number(item.quantity)}</td>
                    <td class="number">$${Number(item.unitPrice).toFixed(2)}</td>
                    <td class="number">${Number(item.discount)}%</td>
                    <td class="number">${Number(item.taxRate)}%</td>
                    <td class="number">$${item.total.toFixed(2)}</td>
                </tr>
            `).join('')}
        </tbody>
    </table>

    <div class="totals-section">
        <table class="totals-table">
            <tr>
                <td class="label">Subtotal:</td>
                <td class="amount">$${totals.subtotal.toFixed(2)}</td>
            </tr>
            <tr>
                <td class="label">Tax:</td>
                <td class="amount">$${totals.totalTax.toFixed(2)}</td>
            </tr>
            <tr class="grand-total">
                <td class="label">Total:</td>
                <td class="amount">$${totals.grandTotal.toFixed(2)}</td>
            </tr>
            ${totals.paidAmount > 0 ? `
            <tr>
                <td class="label">Paid:</td>
                <td class="amount">-$${totals.paidAmount.toFixed(2)}</td>
            </tr>
            ` : ''}
            <tr class="${totals.balanceDue <= 0 ? 'paid-in-full' : 'balance-due'}">
                <td class="label">${totals.balanceDue <= 0 ? 'PAID IN FULL' : 'Balance Due'}:</td>
                <td class="amount">$${Math.max(0, totals.balanceDue).toFixed(2)}</td>
            </tr>
        </table>
    </div>

    ${payments.length > 0 ? `
    <div class="payments-section">
        <div class="section-title">Payment History:</div>
        ${payments.map(payment => `
            <div class="payment-item">
                <div>
                    <strong>${new Date(payment.date).toLocaleDateString()}</strong>
                    <span> - ${payment.method}</span>
                    ${payment.reference ? `<span> (${payment.reference})</span>` : ''}
                </div>
                <div><strong>$${payment.amount.toFixed(2)}</strong></div>
            </div>
        `).join('')}
    </div>
    ` : ''}

    ${invoice.terms ? `
    <div class="terms-section">
        <div class="section-title">Terms & Conditions:</div>
        <p>${invoice.terms}</p>
    </div>
    ` : ''}

    ${invoice.notes ? `
    <div class="notes-section">
        <div class="section-title">Notes:</div>
        <p>${invoice.notes}</p>
    </div>
    ` : ''}

    <div class="footer">
        <p>Thank you for your business!</p>
        <p>Generated on ${new Date().toLocaleDateString()} by ${invoice.createdBy.name || invoice.createdBy.email}</p>
        ${totals.balanceDue > 0 ? `
        <p><strong>Please remit payment by the due date to avoid late fees.</strong></p>
        ` : ''}
    </div>
</body>
</html>
  `
}
