(()=>{var e={};e.id=8098,e.ids=[8098],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},94996:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var r=s(50482),a=s(69108),n=s(62563),o=s.n(n),i=s(68300),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(t,l);let d=["",{children:["auth",{children:["signin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,99204)),"C:\\proj\\nextjs-saas\\app\\auth\\signin\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,59504)),"C:\\proj\\nextjs-saas\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\proj\\nextjs-saas\\app\\auth\\signin\\page.tsx"],u="/auth/signin/page",m={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/auth/signin/page",pathname:"/auth/signin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},70952:(e,t,s)=>{Promise.resolve().then(s.bind(s,66847))},64588:(e,t,s)=>{Promise.resolve().then(s.bind(s,56189)),Promise.resolve().then(s.bind(s,44669))},19634:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2583,23)),Promise.resolve().then(s.t.bind(s,26840,23)),Promise.resolve().then(s.t.bind(s,38771,23)),Promise.resolve().then(s.t.bind(s,13225,23)),Promise.resolve().then(s.t.bind(s,9295,23)),Promise.resolve().then(s.t.bind(s,43982,23))},66847:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>g});var r=s(95344),a=s(3729),n=s(47674),o=s(22254),i=s(20783),l=s.n(i),d=s(16212),c=s(92549),u=s(1586),m=s(61351),p=s(91700),f=s(1222),x=s(53148);function g(){let[e,t]=(0,a.useState)(""),[s,i]=(0,a.useState)(""),[g,h]=(0,a.useState)(!1),[v,b]=(0,a.useState)(!1),[y,j]=(0,a.useState)("");(0,o.useRouter)();let N=async t=>{t.preventDefault(),b(!0),j("");try{let t=await (0,n.signIn)("credentials",{email:e,password:s,redirect:!1});console.log("Sign in result:",t),t?.error?(j("Invalid email or password"),console.error("Sign in error:",t.error)):t?.ok&&(console.log("Login successful, redirecting to dashboard"),window.location.href="/dashboard")}catch(e){console.error("Sign in exception:",e),j("An error occurred. Please try again.")}finally{b(!1)}};return r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 p-4",children:(0,r.jsxs)("div",{className:"w-full max-w-md",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center mb-8",children:[r.jsx(p.Z,{className:"h-8 w-8 text-blue-600 mr-2"}),r.jsx("span",{className:"text-2xl font-bold text-gray-900",children:"Business SaaS"})]}),(0,r.jsxs)(m.Zb,{children:[(0,r.jsxs)(m.Ol,{className:"space-y-1",children:[r.jsx(m.ll,{className:"text-2xl text-center",children:"Welcome back"}),r.jsx(m.SZ,{className:"text-center",children:"Enter your credentials to access your account"})]}),(0,r.jsxs)(m.aY,{children:[(0,r.jsxs)("form",{onSubmit:N,className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(u._,{htmlFor:"email",children:"Email"}),r.jsx(c.I,{id:"email",type:"email",placeholder:"Enter your email",value:e,onChange:e=>t(e.target.value),required:!0})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(u._,{htmlFor:"password",children:"Password"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx(c.I,{id:"password",type:g?"text":"password",placeholder:"Enter your password",value:s,onChange:e=>i(e.target.value),required:!0}),r.jsx(d.z,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>h(!g),children:g?r.jsx(f.Z,{className:"h-4 w-4"}):r.jsx(x.Z,{className:"h-4 w-4"})})]})]}),y&&r.jsx("div",{className:"text-sm text-red-600 bg-red-50 p-3 rounded-md",children:y}),r.jsx(d.z,{type:"submit",className:"w-full",disabled:v,children:v?"Signing in...":"Sign In"})]}),r.jsx("div",{className:"mt-4 text-center",children:r.jsx(l(),{href:"/auth/forgot-password",className:"text-sm text-blue-600 hover:underline",children:"Forgot your password?"})}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsxs)("div",{className:"relative",children:[r.jsx("div",{className:"absolute inset-0 flex items-center",children:r.jsx("span",{className:"w-full border-t"})}),r.jsx("div",{className:"relative flex justify-center text-xs uppercase",children:r.jsx("span",{className:"bg-background px-2 text-muted-foreground",children:"Demo Accounts"})})]}),(0,r.jsxs)("div",{className:"mt-4 space-y-2 text-sm",children:[(0,r.jsxs)("div",{className:"bg-blue-50 p-3 rounded-md",children:[r.jsx("p",{className:"font-medium text-blue-900",children:"Super Admin"}),r.jsx("p",{className:"text-blue-700",children:"<EMAIL> / admin123"})]}),(0,r.jsxs)("div",{className:"bg-green-50 p-3 rounded-md",children:[r.jsx("p",{className:"font-medium text-green-900",children:"Demo User"}),r.jsx("p",{className:"text-green-700",children:"<EMAIL> / demo123"})]})]})]}),(0,r.jsxs)("div",{className:"mt-6 text-center text-sm",children:[r.jsx("span",{className:"text-muted-foreground",children:"Don't have an account? "}),r.jsx(l(),{href:"/auth/signup",className:"text-blue-600 hover:underline",children:"Sign up"})]})]})]})]})})}},56189:(e,t,s)=>{"use strict";s.r(t),s.d(t,{Providers:()=>c});var r=s(95344),a=s(47674),n=s(6256),o=s(19115),i=s(26274),l=s(3729),d=s(66091);function c({children:e}){let[t]=(0,l.useState)(()=>new o.S({defaultOptions:{queries:{staleTime:6e4,retry:1}}}));return r.jsx(a.SessionProvider,{children:r.jsx(i.aH,{client:t,children:r.jsx(d.lY,{children:r.jsx(n.f,{attribute:"class",defaultTheme:"light",enableSystem:!0,disableTransitionOnChange:!0,children:e})})})})}},16212:(e,t,s)=>{"use strict";s.d(t,{z:()=>d});var r=s(95344),a=s(3729),n=s(32751),o=s(49247),i=s(91626);let l=(0,o.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef(({className:e,variant:t,size:s,asChild:a=!1,...o},d)=>{let c=a?n.g7:"button";return r.jsx(c,{className:(0,i.cn)(l({variant:t,size:s,className:e})),ref:d,...o})});d.displayName="Button"},61351:(e,t,s)=>{"use strict";s.d(t,{Ol:()=>i,SZ:()=>d,Zb:()=>o,aY:()=>c,eW:()=>u,ll:()=>l});var r=s(95344),a=s(3729),n=s(91626);let o=a.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));o.displayName="Card";let i=a.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...t}));i.displayName="CardHeader";let l=a.forwardRef(({className:e,...t},s)=>r.jsx("h3",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let d=a.forwardRef(({className:e,...t},s)=>r.jsx("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let c=a.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,n.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent";let u=a.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",e),...t}));u.displayName="CardFooter"},92549:(e,t,s)=>{"use strict";s.d(t,{I:()=>o});var r=s(95344),a=s(3729),n=s(91626);let o=a.forwardRef(({className:e,type:t,...s},a)=>r.jsx("input",{type:t,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...s}));o.displayName="Input"},1586:(e,t,s)=>{"use strict";s.d(t,{_:()=>d});var r=s(95344),a=s(3729),n=s(14217),o=s(49247),i=s(91626);let l=(0,o.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef(({className:e,...t},s)=>r.jsx(n.f,{ref:s,className:(0,i.cn)(l(),e),...t}));d.displayName=n.f.displayName},66091:(e,t,s)=>{"use strict";s.d(t,{TC:()=>l,lY:()=>i});var r=s(95344),a=s(3729);let n={appName:"SaaS Platform",logoUrl:"",faviconUrl:"",primaryColor:"#3b82f6",secondaryColor:"#64748b",accentColor:"#10b981",backgroundColor:"#ffffff",textColor:"#1f2937",theme:"light",fontFamily:"Inter, sans-serif",customCss:""},o=(0,a.createContext)(void 0);function i({children:e}){let[t,s]=(0,a.useState)(n),[i,l]=(0,a.useState)(!0);(0,a.useEffect)(()=>{d()},[]);let d=async()=>{try{let e=await fetch("/api/global-config/branding"),t=await e.json();t.success&&t.branding?(s({...n,...t.branding}),c({...n,...t.branding})):c(n)}catch(e){console.error("Error fetching branding config:",e),c(n)}finally{l(!1)}},c=e=>{let t=document.documentElement;if(t.style.setProperty("--primary-color",e.primaryColor),t.style.setProperty("--secondary-color",e.secondaryColor),t.style.setProperty("--accent-color",e.accentColor),t.style.setProperty("--background-color",e.backgroundColor),t.style.setProperty("--text-color",e.textColor),t.style.setProperty("--font-family",e.fontFamily),document.body.className=document.body.className.replace(/theme-\w+/g,""),document.body.classList.add(`theme-${e.theme}`),document.title=e.appName,e.faviconUrl){let t=document.querySelector('link[rel="icon"]');t||((t=document.createElement("link")).rel="icon",document.head.appendChild(t)),t.href=e.faviconUrl}let s=document.getElementById("custom-branding-css");e.customCss?(s||((s=document.createElement("style")).id="custom-branding-css",document.head.appendChild(s)),s.textContent=e.customCss):s&&s.remove();let r=document.querySelector('meta[name="theme-color"]');r||((r=document.createElement("meta")).name="theme-color",document.head.appendChild(r)),r.content=e.primaryColor};return r.jsx(o.Provider,{value:{branding:t,updateBranding:e=>{let r={...t,...e};s(r),c(r)},loading:i},children:e})}function l(){let e=(0,a.useContext)(o);if(void 0===e)throw Error("useBranding must be used within a BrandingProvider");return e}},91626:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n});var r=s(56815),a=s(79377);function n(...e){return(0,a.m6)((0,r.W)(e))}},1222:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(69224).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},53148:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(69224).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},22254:(e,t,s)=>{e.exports=s(14767)},99204:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>n,__esModule:()=>a,default:()=>o});let r=(0,s(86843).createProxy)(String.raw`C:\proj\nextjs-saas\app\auth\signin\page.tsx`),{__esModule:a,$$typeof:n}=r,o=r.default},59504:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p,metadata:()=>m});var r=s(25036),a=s(80265),n=s.n(a),o=s(86843);let i=(0,o.createProxy)(String.raw`C:\proj\nextjs-saas\components\providers.tsx`),{__esModule:l,$$typeof:d}=i;i.default;let c=(0,o.createProxy)(String.raw`C:\proj\nextjs-saas\components\providers.tsx#Providers`);var u=s(69636);s(67272);let m={title:{default:"Business SaaS - Complete Business Management Solution",template:"%s | Business SaaS"},description:"Complete SaaS solution for business management including CRM, invoicing, quotations, and more.",keywords:["SaaS","Business Management","CRM","Invoicing","Quotations"],authors:[{name:"Business SaaS Team"}],creator:"Business SaaS",openGraph:{type:"website",locale:"en_US",url:process.env.NEXT_PUBLIC_APP_URL,title:"Business SaaS - Complete Business Management Solution",description:"Complete SaaS solution for business management including CRM, invoicing, quotations, and more.",siteName:"Business SaaS"},twitter:{card:"summary_large_image",title:"Business SaaS - Complete Business Management Solution",description:"Complete SaaS solution for business management including CRM, invoicing, quotations, and more.",creator:"@businesssaas"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function p({children:e}){return r.jsx("html",{lang:"en",suppressHydrationWarning:!0,children:r.jsx("body",{className:n().className,children:(0,r.jsxs)(c,{children:[e,r.jsx(u.x7,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#4ade80",secondary:"#fff"}},error:{duration:4e3,iconTheme:{primary:"#ef4444",secondary:"#fff"}}}})]})})})}},67272:()=>{},14217:(e,t,s)=>{"use strict";s.d(t,{f:()=>i});var r=s(3729),a=s(62409),n=s(95344),o=r.forwardRef((e,t)=>(0,n.jsx)(a.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var i=o},62409:(e,t,s)=>{"use strict";s.d(t,{WV:()=>i,jH:()=>l});var r=s(3729),a=s(81202),n=s(32751),o=s(95344),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let s=(0,n.Z8)(`Primitive.${t}`),a=r.forwardRef((e,r)=>{let{asChild:a,...n}=e,i=a?s:t;return(0,o.jsx)(i,{...n,ref:r})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{});function l(e,t){e&&a.flushSync(()=>e.dispatchEvent(t))}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[1638,7948,6671,4626],()=>s(94996));module.exports=r})();