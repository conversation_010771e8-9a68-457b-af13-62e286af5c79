{"version": 3, "file": "ColumnVisibility.js", "sources": ["../../../src/features/ColumnVisibility.ts"], "sourcesContent": ["import { ColumnPinningPosition } from '..'\nimport {\n  Cell,\n  Column,\n  OnChangeFn,\n  Table,\n  Updater,\n  Row,\n  RowData,\n  TableFeature,\n} from '../types'\nimport { getMemoOptions, makeStateUpdater, memo } from '../utils'\n\nexport type VisibilityState = Record<string, boolean>\n\nexport interface VisibilityTableState {\n  columnVisibility: VisibilityState\n}\n\nexport interface VisibilityOptions {\n  /**\n   * Whether to enable column hiding. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#enablehiding)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  enableHiding?: boolean\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnVisibility` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#oncolumnvisibilitychange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  onColumnVisibilityChange?: OnChangeFn<VisibilityState>\n}\n\nexport type VisibilityDefaultOptions = Pick<\n  VisibilityOptions,\n  'onColumnVisibilityChange'\n>\n\nexport interface VisibilityInstance<TData extends RowData> {\n  /**\n   * If column pinning, returns a flat array of leaf-node columns that are visible in the unpinned/center portion of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getcentervisibleleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getCenterVisibleLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns whether all columns are visible\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getisallcolumnsvisible)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getIsAllColumnsVisible: () => boolean\n  /**\n   * Returns whether any columns are visible\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getissomecolumnsvisible)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getIsSomeColumnsVisible: () => boolean\n  /**\n   * If column pinning, returns a flat array of leaf-node columns that are visible in the left portion of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getleftvisibleleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getLeftVisibleLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * If column pinning, returns a flat array of leaf-node columns that are visible in the right portion of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getrightvisibleleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getRightVisibleLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns a handler for toggling the visibility of all columns, meant to be bound to a `input[type=checkbox]` element.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#gettoggleallcolumnsvisibilityhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getToggleAllColumnsVisibilityHandler: () => (event: unknown) => void\n  /**\n   * Returns a flat array of columns that are visible, including parent columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getvisibleflatcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getVisibleFlatColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns a flat array of leaf-node columns that are visible.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getvisibleleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getVisibleLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Resets the column visibility state to the initial state. If `defaultState` is provided, the state will be reset to `{}`\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#resetcolumnvisibility)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  resetColumnVisibility: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.columnVisibility` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#setcolumnvisibility)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  setColumnVisibility: (updater: Updater<VisibilityState>) => void\n  /**\n   * Toggles the visibility of all columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#toggleallcolumnsvisible)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  toggleAllColumnsVisible: (value?: boolean) => void\n}\n\nexport interface VisibilityColumnDef {\n  enableHiding?: boolean\n}\n\nexport interface VisibilityRow<TData extends RowData> {\n  _getAllVisibleCells: () => Cell<TData, unknown>[]\n  /**\n   * Returns an array of cells that account for column visibility for the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getvisiblecells)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getVisibleCells: () => Cell<TData, unknown>[]\n}\n\nexport interface VisibilityColumn {\n  /**\n   * Returns whether the column can be hidden\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getcanhide)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getCanHide: () => boolean\n  /**\n   * Returns whether the column is visible\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getisvisible)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getIsVisible: () => boolean\n  /**\n   * Returns a function that can be used to toggle the column visibility. This function can be used to bind to an event handler to a checkbox.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#gettogglevisibilityhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getToggleVisibilityHandler: () => (event: unknown) => void\n  /**\n   * Toggles the visibility of the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#togglevisibility)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  toggleVisibility: (value?: boolean) => void\n}\n\n//\n\nexport const ColumnVisibility: TableFeature = {\n  getInitialState: (state): VisibilityTableState => {\n    return {\n      columnVisibility: {},\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): VisibilityDefaultOptions => {\n    return {\n      onColumnVisibilityChange: makeStateUpdater('columnVisibility', table),\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    column.toggleVisibility = value => {\n      if (column.getCanHide()) {\n        table.setColumnVisibility(old => ({\n          ...old,\n          [column.id]: value ?? !column.getIsVisible(),\n        }))\n      }\n    }\n    column.getIsVisible = () => {\n      const childColumns = column.columns\n      return (\n        (childColumns.length\n          ? childColumns.some(c => c.getIsVisible())\n          : table.getState().columnVisibility?.[column.id]) ?? true\n      )\n    }\n\n    column.getCanHide = () => {\n      return (\n        (column.columnDef.enableHiding ?? true) &&\n        (table.options.enableHiding ?? true)\n      )\n    }\n    column.getToggleVisibilityHandler = () => {\n      return (e: unknown) => {\n        column.toggleVisibility?.(\n          ((e as MouseEvent).target as HTMLInputElement).checked\n        )\n      }\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row._getAllVisibleCells = memo(\n      () => [row.getAllCells(), table.getState().columnVisibility],\n      cells => {\n        return cells.filter(cell => cell.column.getIsVisible())\n      },\n      getMemoOptions(table.options, 'debugRows', '_getAllVisibleCells')\n    )\n    row.getVisibleCells = memo(\n      () => [\n        row.getLeftVisibleCells(),\n        row.getCenterVisibleCells(),\n        row.getRightVisibleCells(),\n      ],\n      (left, center, right) => [...left, ...center, ...right],\n      getMemoOptions(table.options, 'debugRows', 'getVisibleCells')\n    )\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    const makeVisibleColumnsMethod = (\n      key: string,\n      getColumns: () => Column<TData, unknown>[]\n    ): (() => Column<TData, unknown>[]) => {\n      return memo(\n        () => [\n          getColumns(),\n          getColumns()\n            .filter(d => d.getIsVisible())\n            .map(d => d.id)\n            .join('_'),\n        ],\n        columns => {\n          return columns.filter(d => d.getIsVisible?.())\n        },\n        getMemoOptions(table.options, 'debugColumns', key)\n      )\n    }\n\n    table.getVisibleFlatColumns = makeVisibleColumnsMethod(\n      'getVisibleFlatColumns',\n      () => table.getAllFlatColumns()\n    )\n    table.getVisibleLeafColumns = makeVisibleColumnsMethod(\n      'getVisibleLeafColumns',\n      () => table.getAllLeafColumns()\n    )\n    table.getLeftVisibleLeafColumns = makeVisibleColumnsMethod(\n      'getLeftVisibleLeafColumns',\n      () => table.getLeftLeafColumns()\n    )\n    table.getRightVisibleLeafColumns = makeVisibleColumnsMethod(\n      'getRightVisibleLeafColumns',\n      () => table.getRightLeafColumns()\n    )\n    table.getCenterVisibleLeafColumns = makeVisibleColumnsMethod(\n      'getCenterVisibleLeafColumns',\n      () => table.getCenterLeafColumns()\n    )\n\n    table.setColumnVisibility = updater =>\n      table.options.onColumnVisibilityChange?.(updater)\n\n    table.resetColumnVisibility = defaultState => {\n      table.setColumnVisibility(\n        defaultState ? {} : table.initialState.columnVisibility ?? {}\n      )\n    }\n\n    table.toggleAllColumnsVisible = value => {\n      value = value ?? !table.getIsAllColumnsVisible()\n\n      table.setColumnVisibility(\n        table.getAllLeafColumns().reduce(\n          (obj, column) => ({\n            ...obj,\n            [column.id]: !value ? !column.getCanHide?.() : value,\n          }),\n          {}\n        )\n      )\n    }\n\n    table.getIsAllColumnsVisible = () =>\n      !table.getAllLeafColumns().some(column => !column.getIsVisible?.())\n\n    table.getIsSomeColumnsVisible = () =>\n      table.getAllLeafColumns().some(column => column.getIsVisible?.())\n\n    table.getToggleAllColumnsVisibilityHandler = () => {\n      return (e: unknown) => {\n        table.toggleAllColumnsVisible(\n          ((e as MouseEvent).target as HTMLInputElement)?.checked\n        )\n      }\n    }\n  },\n}\n\nexport function _getVisibleLeafColumns<TData extends RowData>(\n  table: Table<TData>,\n  position?: ColumnPinningPosition | 'center'\n) {\n  return !position\n    ? table.getVisibleLeafColumns()\n    : position === 'center'\n      ? table.getCenterVisibleLeafColumns()\n      : position === 'left'\n        ? table.getLeftVisibleLeafColumns()\n        : table.getRightVisibleLeafColumns()\n}\n"], "names": ["ColumnVisibility", "getInitialState", "state", "columnVisibility", "getDefaultOptions", "table", "onColumnVisibilityChange", "makeStateUpdater", "createColumn", "column", "toggleVisibility", "value", "getCanHide", "setColumnVisibility", "old", "id", "getIsVisible", "_ref", "_table$getState$colum", "childColumns", "columns", "length", "some", "c", "getState", "_column$columnDef$ena", "_table$options$enable", "columnDef", "enableHiding", "options", "getToggleVisibilityHandler", "e", "target", "checked", "createRow", "row", "_getAllVisibleCells", "memo", "getAllCells", "cells", "filter", "cell", "getMemoOptions", "getVisibleCells", "getLeftVisibleCells", "getCenterVisibleCells", "getRightVisibleCells", "left", "center", "right", "createTable", "makeVisibleColumnsMethod", "key", "getColumns", "d", "map", "join", "getVisibleFlatColumns", "getAllFlatColumns", "getVisibleLeafColumns", "getAllLeafColumns", "getLeftVisibleLeafColumns", "getLeftLeafColumns", "getRightVisibleLeafColumns", "getRightLeafColumns", "getCenterVisibleLeafColumns", "getCenterLeafColumns", "updater", "resetColumnVisibility", "defaultState", "_table$initialState$c", "initialState", "toggleAllColumnsVisible", "_value", "getIsAllColumnsVisible", "reduce", "obj", "getIsSomeColumnsVisible", "getToggleAllColumnsVisibilityHandler", "_target", "_getVisibleLeafColumns", "position"], "mappings": ";;;;;;;;;;;;;;AAqJA;;AAEO,MAAMA,gBAA8B,GAAG;EAC5CC,eAAe,EAAGC,KAAK,IAA2B;IAChD,OAAO;MACLC,gBAAgB,EAAE,EAAE;MACpB,GAAGD,KAAAA;KACJ,CAAA;GACF;EAEDE,iBAAiB,EACfC,KAAmB,IACU;IAC7B,OAAO;AACLC,MAAAA,wBAAwB,EAAEC,sBAAgB,CAAC,kBAAkB,EAAEF,KAAK,CAAA;KACrE,CAAA;GACF;AAEDG,EAAAA,YAAY,EAAEA,CACZC,MAA6B,EAC7BJ,KAAmB,KACV;AACTI,IAAAA,MAAM,CAACC,gBAAgB,GAAGC,KAAK,IAAI;AACjC,MAAA,IAAIF,MAAM,CAACG,UAAU,EAAE,EAAE;AACvBP,QAAAA,KAAK,CAACQ,mBAAmB,CAACC,GAAG,KAAK;AAChC,UAAA,GAAGA,GAAG;AACN,UAAA,CAACL,MAAM,CAACM,EAAE,GAAGJ,KAAK,IAAA,IAAA,GAALA,KAAK,GAAI,CAACF,MAAM,CAACO,YAAY,EAAC;AAC7C,SAAC,CAAC,CAAC,CAAA;AACL,OAAA;KACD,CAAA;IACDP,MAAM,CAACO,YAAY,GAAG,MAAM;MAAA,IAAAC,IAAA,EAAAC,qBAAA,CAAA;AAC1B,MAAA,MAAMC,YAAY,GAAGV,MAAM,CAACW,OAAO,CAAA;AACnC,MAAA,OAAA,CAAAH,IAAA,GACGE,YAAY,CAACE,MAAM,GAChBF,YAAY,CAACG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACP,YAAY,EAAE,CAAC,GAAA,CAAAE,qBAAA,GACxCb,KAAK,CAACmB,QAAQ,EAAE,CAACrB,gBAAgB,KAAA,IAAA,GAAA,KAAA,CAAA,GAAjCe,qBAAA,CAAoCT,MAAM,CAACM,EAAE,CAAC,KAAAE,IAAAA,GAAAA,IAAA,GAAK,IAAI,CAAA;KAE9D,CAAA;IAEDR,MAAM,CAACG,UAAU,GAAG,MAAM;MAAA,IAAAa,qBAAA,EAAAC,qBAAA,CAAA;MACxB,OACE,CAAA,CAAAD,qBAAA,GAAChB,MAAM,CAACkB,SAAS,CAACC,YAAY,KAAAH,IAAAA,GAAAA,qBAAA,GAAI,IAAI,OAAAC,qBAAA,GACrCrB,KAAK,CAACwB,OAAO,CAACD,YAAY,KAAAF,IAAAA,GAAAA,qBAAA,GAAI,IAAI,CAAC,CAAA;KAEvC,CAAA;IACDjB,MAAM,CAACqB,0BAA0B,GAAG,MAAM;AACxC,MAAA,OAAQC,CAAU,IAAK;AACrBtB,QAAAA,MAAM,CAACC,gBAAgB,IAAvBD,IAAAA,IAAAA,MAAM,CAACC,gBAAgB,CACnBqB,CAAC,CAAgBC,MAAM,CAAsBC,OACjD,CAAC,CAAA;OACF,CAAA;KACF,CAAA;GACF;AAEDC,EAAAA,SAAS,EAAEA,CACTC,GAAe,EACf9B,KAAmB,KACV;IACT8B,GAAG,CAACC,mBAAmB,GAAGC,UAAI,CAC5B,MAAM,CAACF,GAAG,CAACG,WAAW,EAAE,EAAEjC,KAAK,CAACmB,QAAQ,EAAE,CAACrB,gBAAgB,CAAC,EAC5DoC,KAAK,IAAI;AACP,MAAA,OAAOA,KAAK,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAChC,MAAM,CAACO,YAAY,EAAE,CAAC,CAAA;KACxD,EACD0B,oBAAc,CAACrC,KAAK,CAACwB,OAAO,EAAE,WAAW,EAAE,qBAAqB,CAClE,CAAC,CAAA;IACDM,GAAG,CAACQ,eAAe,GAAGN,UAAI,CACxB,MAAM,CACJF,GAAG,CAACS,mBAAmB,EAAE,EACzBT,GAAG,CAACU,qBAAqB,EAAE,EAC3BV,GAAG,CAACW,oBAAoB,EAAE,CAC3B,EACD,CAACC,IAAI,EAAEC,MAAM,EAAEC,KAAK,KAAK,CAAC,GAAGF,IAAI,EAAE,GAAGC,MAAM,EAAE,GAAGC,KAAK,CAAC,EACvDP,oBAAc,CAACrC,KAAK,CAACwB,OAAO,EAAE,WAAW,EAAE,iBAAiB,CAC9D,CAAC,CAAA;GACF;EAEDqB,WAAW,EAA0B7C,KAAmB,IAAW;AACjE,IAAA,MAAM8C,wBAAwB,GAAGA,CAC/BC,GAAW,EACXC,UAA0C,KACL;AACrC,MAAA,OAAOhB,UAAI,CACT,MAAM,CACJgB,UAAU,EAAE,EACZA,UAAU,EAAE,CACTb,MAAM,CAACc,CAAC,IAAIA,CAAC,CAACtC,YAAY,EAAE,CAAC,CAC7BuC,GAAG,CAACD,CAAC,IAAIA,CAAC,CAACvC,EAAE,CAAC,CACdyC,IAAI,CAAC,GAAG,CAAC,CACb,EACDpC,OAAO,IAAI;AACT,QAAA,OAAOA,OAAO,CAACoB,MAAM,CAACc,CAAC,IAAIA,CAAC,CAACtC,YAAY,oBAAdsC,CAAC,CAACtC,YAAY,EAAI,CAAC,CAAA;OAC/C,EACD0B,oBAAc,CAACrC,KAAK,CAACwB,OAAO,EAAE,cAAc,EAAEuB,GAAG,CACnD,CAAC,CAAA;KACF,CAAA;AAED/C,IAAAA,KAAK,CAACoD,qBAAqB,GAAGN,wBAAwB,CACpD,uBAAuB,EACvB,MAAM9C,KAAK,CAACqD,iBAAiB,EAC/B,CAAC,CAAA;AACDrD,IAAAA,KAAK,CAACsD,qBAAqB,GAAGR,wBAAwB,CACpD,uBAAuB,EACvB,MAAM9C,KAAK,CAACuD,iBAAiB,EAC/B,CAAC,CAAA;AACDvD,IAAAA,KAAK,CAACwD,yBAAyB,GAAGV,wBAAwB,CACxD,2BAA2B,EAC3B,MAAM9C,KAAK,CAACyD,kBAAkB,EAChC,CAAC,CAAA;AACDzD,IAAAA,KAAK,CAAC0D,0BAA0B,GAAGZ,wBAAwB,CACzD,4BAA4B,EAC5B,MAAM9C,KAAK,CAAC2D,mBAAmB,EACjC,CAAC,CAAA;AACD3D,IAAAA,KAAK,CAAC4D,2BAA2B,GAAGd,wBAAwB,CAC1D,6BAA6B,EAC7B,MAAM9C,KAAK,CAAC6D,oBAAoB,EAClC,CAAC,CAAA;AAED7D,IAAAA,KAAK,CAACQ,mBAAmB,GAAGsD,OAAO,IACjC9D,KAAK,CAACwB,OAAO,CAACvB,wBAAwB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAtCD,KAAK,CAACwB,OAAO,CAACvB,wBAAwB,CAAG6D,OAAO,CAAC,CAAA;AAEnD9D,IAAAA,KAAK,CAAC+D,qBAAqB,GAAGC,YAAY,IAAI;AAAA,MAAA,IAAAC,qBAAA,CAAA;MAC5CjE,KAAK,CAACQ,mBAAmB,CACvBwD,YAAY,GAAG,EAAE,IAAAC,qBAAA,GAAGjE,KAAK,CAACkE,YAAY,CAACpE,gBAAgB,KAAA,IAAA,GAAAmE,qBAAA,GAAI,EAC7D,CAAC,CAAA;KACF,CAAA;AAEDjE,IAAAA,KAAK,CAACmE,uBAAuB,GAAG7D,KAAK,IAAI;AAAA,MAAA,IAAA8D,MAAA,CAAA;AACvC9D,MAAAA,KAAK,GAAA8D,CAAAA,MAAA,GAAG9D,KAAK,KAAA8D,IAAAA,GAAAA,MAAA,GAAI,CAACpE,KAAK,CAACqE,sBAAsB,EAAE,CAAA;AAEhDrE,MAAAA,KAAK,CAACQ,mBAAmB,CACvBR,KAAK,CAACuD,iBAAiB,EAAE,CAACe,MAAM,CAC9B,CAACC,GAAG,EAAEnE,MAAM,MAAM;AAChB,QAAA,GAAGmE,GAAG;AACN,QAAA,CAACnE,MAAM,CAACM,EAAE,GAAG,CAACJ,KAAK,GAAG,EAACF,MAAM,CAACG,UAAU,IAAjBH,IAAAA,IAAAA,MAAM,CAACG,UAAU,EAAI,CAAGD,GAAAA,KAAAA;AACjD,OAAC,CAAC,EACF,EACF,CACF,CAAC,CAAA;KACF,CAAA;IAEDN,KAAK,CAACqE,sBAAsB,GAAG,MAC7B,CAACrE,KAAK,CAACuD,iBAAiB,EAAE,CAACtC,IAAI,CAACb,MAAM,IAAI,EAACA,MAAM,CAACO,YAAY,IAAnBP,IAAAA,IAAAA,MAAM,CAACO,YAAY,EAAI,CAAC,CAAA,CAAA;IAErEX,KAAK,CAACwE,uBAAuB,GAAG,MAC9BxE,KAAK,CAACuD,iBAAiB,EAAE,CAACtC,IAAI,CAACb,MAAM,IAAIA,MAAM,CAACO,YAAY,IAAA,IAAA,GAAA,KAAA,CAAA,GAAnBP,MAAM,CAACO,YAAY,EAAI,CAAC,CAAA;IAEnEX,KAAK,CAACyE,oCAAoC,GAAG,MAAM;AACjD,MAAA,OAAQ/C,CAAU,IAAK;AAAA,QAAA,IAAAgD,OAAA,CAAA;AACrB1E,QAAAA,KAAK,CAACmE,uBAAuB,CAAAO,CAAAA,OAAA,GACzBhD,CAAC,CAAgBC,MAAM,KAAzB+C,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,OAAA,CAAgD9C,OAClD,CAAC,CAAA;OACF,CAAA;KACF,CAAA;AACH,GAAA;AACF,EAAC;AAEM,SAAS+C,sBAAsBA,CACpC3E,KAAmB,EACnB4E,QAA2C,EAC3C;AACA,EAAA,OAAO,CAACA,QAAQ,GACZ5E,KAAK,CAACsD,qBAAqB,EAAE,GAC7BsB,QAAQ,KAAK,QAAQ,GACnB5E,KAAK,CAAC4D,2BAA2B,EAAE,GACnCgB,QAAQ,KAAK,MAAM,GACjB5E,KAAK,CAACwD,yBAAyB,EAAE,GACjCxD,KAAK,CAAC0D,0BAA0B,EAAE,CAAA;AAC5C;;;;;"}