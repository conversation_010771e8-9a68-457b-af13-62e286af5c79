'use client'

import { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  FileText,
  DollarSign,
  TrendingUp,
  Clock,
  Target,
  Users,
  Calendar,
  RefreshCw,
  BarChart3,
  CheckCircle,
  Eye,
  AlertCircle
} from 'lucide-react'
import { toast } from 'react-hot-toast'

interface QuotationAnalytics {
  summary: {
    totalQuotations: number
    totalValue: number
    averageValue: number
    acceptanceRate: number
    viewRate: number
    avgTimeToAcceptance: number
    pendingRevenue: number
    confirmedRevenue: number
    expiredQuotations: number
  }
  quotationsByStatus: Array<{
    status: string
    count: number
    value: number
  }>
  quotationsByMonth: Array<{
    month: string
    quotation_count: number
    total_value: number
  }>
  quotationTrends: Array<{
    date: string
    quotations_created: number
    quotations_accepted: number
    total_value: number
  }>
  topQuotations: Array<{
    id: string
    quotationNumber: string
    title: string
    total: number
    status: string
    customer: {
      id: string
      name: string
      company: string | null
      email: string | null
    }
    createdBy: {
      name: string | null
      email: string | null
    }
    createdAt: string
  }>
  recentQuotations: Array<{
    id: string
    quotationNumber: string
    title: string
    total: number
    status: string
    customer: {
      id: string
      name: string
      company: string | null
    }
    createdBy: {
      name: string | null
      email: string | null
    }
    createdAt: string
  }>
  customerQuotations: Array<{
    customer: {
      id: string
      name: string
      company: string | null
      email: string | null
    }
    quotationCount: number
    totalValue: number
  }>
  revenueForecast: {
    pending: number
    confirmed: number
    potential: number
  }
  period: number
}

export function QuotationAnalytics() {
  const [analytics, setAnalytics] = useState<QuotationAnalytics | null>(null)
  const [loading, setLoading] = useState(true)
  const [period, setPeriod] = useState('30')

  const fetchAnalytics = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/quotations/analytics?period=${period}`)
      if (!response.ok) {
        throw new Error('Failed to fetch analytics')
      }

      const data = await response.json()
      setAnalytics(data)
    } catch (error) {
      toast.error('Failed to load quotation analytics')
      console.error('Error fetching analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchAnalytics()
  }, [period])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DRAFT':
        return 'bg-gray-100 text-gray-800'
      case 'SENT':
        return 'bg-blue-100 text-blue-800'
      case 'VIEWED':
        return 'bg-yellow-100 text-yellow-800'
      case 'ACCEPTED':
        return 'bg-green-100 text-green-800'
      case 'REJECTED':
        return 'bg-red-100 text-red-800'
      case 'EXPIRED':
        return 'bg-orange-100 text-orange-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!analytics) {
    return (
      <div className="text-center py-8 text-gray-500">
        <BarChart3 className="h-12 w-12 mx-auto mb-4 text-gray-300" />
        <p>Failed to load analytics data</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Quotation Analytics</h3>
        <div className="flex items-center space-x-2">
          <Select value={period} onValueChange={setPeriod}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">Last 7 days</SelectItem>
              <SelectItem value="30">Last 30 days</SelectItem>
              <SelectItem value="90">Last 90 days</SelectItem>
              <SelectItem value="365">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={fetchAnalytics} size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-blue-100 rounded-full">
                <FileText className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Total Quotations</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.summary.totalQuotations}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-green-100 rounded-full">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Total Value</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(analytics.summary.totalValue)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-purple-100 rounded-full">
                <Target className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Acceptance Rate</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analytics.summary.acceptanceRate.toFixed(1)}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-orange-100 rounded-full">
                <TrendingUp className="h-6 w-6 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Avg Value</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(analytics.summary.averageValue)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-indigo-100 rounded-full">
                <Eye className="h-6 w-6 text-indigo-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">View Rate</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analytics.summary.viewRate.toFixed(1)}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-pink-100 rounded-full">
                <Clock className="h-6 w-6 text-pink-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Avg Time to Accept</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analytics.summary.avgTimeToAcceptance.toFixed(1)}d
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-red-100 rounded-full">
                <AlertCircle className="h-6 w-6 text-red-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Expired</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analytics.summary.expiredQuotations}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Revenue Forecast */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <DollarSign className="h-5 w-5 mr-2" />
            Revenue Forecast
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">
                {formatCurrency(analytics.revenueForecast.confirmed)}
              </p>
              <p className="text-sm text-gray-500">Confirmed Revenue</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600">
                {formatCurrency(analytics.revenueForecast.pending)}
              </p>
              <p className="text-sm text-gray-500">Pending Revenue</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-purple-600">
                {formatCurrency(analytics.revenueForecast.potential)}
              </p>
              <p className="text-sm text-gray-500">Total Potential</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Charts and Breakdowns */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Quotations by Status */}
        <Card>
          <CardHeader>
            <CardTitle>Quotations by Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.quotationsByStatus.map((item) => (
                <div key={item.status} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Badge className={getStatusColor(item.status)}>
                      {item.status}
                    </Badge>
                  </div>
                  <div className="text-right">
                    <span className="font-semibold">{item.count}</span>
                    <p className="text-sm text-gray-500">
                      {formatCurrency(item.value)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Top Customers */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Top Customers by Quotation Value
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.customerQuotations.slice(0, 5).map((item, index) => (
                <div key={item.customer.id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-xs font-semibold text-blue-600">#{index + 1}</span>
                    </div>
                    <div>
                      <p className="font-medium">{item.customer.name}</p>
                      <p className="text-sm text-gray-500">{item.customer.company}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-green-600">
                      {formatCurrency(item.totalValue)}
                    </p>
                    <p className="text-sm text-gray-500">
                      {item.quotationCount} quotations
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Quotations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Target className="h-5 w-5 mr-2" />
            Top Quotations by Value
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {analytics.topQuotations.slice(0, 5).map((quotation, index) => (
              <div key={quotation.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-sm font-semibold text-blue-600">#{index + 1}</span>
                  </div>
                  <div>
                    <p className="font-medium">{quotation.quotationNumber}</p>
                    <p className="text-sm text-gray-600">{quotation.title}</p>
                    <p className="text-sm text-gray-500">
                      {quotation.customer.name} • {quotation.createdBy.name}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-green-600">
                    {formatCurrency(quotation.total)}
                  </p>
                  <Badge className={getStatusColor(quotation.status)} variant="outline">
                    {quotation.status}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Quotations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="h-5 w-5 mr-2" />
            Recent Quotations (Last 7 Days)
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {analytics.recentQuotations.length === 0 ? (
              <p className="text-gray-500 text-center py-4">No recent quotations</p>
            ) : (
              analytics.recentQuotations.map((quotation) => (
                <div key={quotation.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium">{quotation.quotationNumber}</p>
                    <p className="text-sm text-gray-600">{quotation.title}</p>
                    <p className="text-sm text-gray-500">
                      {quotation.customer.name} • {new Date(quotation.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-green-600">
                      {formatCurrency(quotation.total)}
                    </p>
                    <Badge className={getStatusColor(quotation.status)} variant="outline">
                      {quotation.status}
                    </Badge>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
