import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET /api/super-admin/subscriptions - Get all subscriptions with analytics
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || session?.user?.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Super admin access required' }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const status = searchParams.get('status') || ''
    const plan = searchParams.get('plan') || ''
    const search = searchParams.get('search') || ''

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {}
    
    if (status && status !== 'all') {
      where.status = status
    }

    if (plan && plan !== 'all') {
      where.plan = plan
    }

    if (search) {
      where.company = {
        OR: [
          { name: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } }
        ]
      }
    }

    // Get subscriptions with pagination
    const [subscriptions, total] = await Promise.all([
      prisma.subscription.findMany({
        where,
        skip,
        take: limit,
        include: {
          company: {
            select: {
              id: true,
              name: true,
              email: true,
              status: true,
              industry: true,
              size: true,
              owner: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  avatar: true
                }
              },
              _count: {
                select: {
                  members: true,
                  customers: true,
                  quotations: true,
                  invoices: true
                }
              }
            }
          },
          pricingPlan: {
            select: {
              id: true,
              name: true,
              monthlyPrice: true,
              yearlyPrice: true,
              features: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      }),
      prisma.subscription.count({ where })
    ])

    // Get subscription statistics
    const stats = await Promise.all([
      // Count by status
      prisma.subscription.groupBy({
        by: ['status'],
        _count: { id: true }
      }),
      // Count by plan
      prisma.subscription.groupBy({
        by: ['plan'],
        _count: { id: true }
      }),
      // Revenue metrics
      prisma.subscription.aggregate({
        where: { status: 'ACTIVE' },
        _sum: { amount: true },
        _avg: { amount: true },
        _count: { id: true }
      }),
      // Monthly recurring revenue
      prisma.subscription.findMany({
        where: { 
          status: 'ACTIVE',
          billingCycle: 'MONTHLY'
        },
        select: { amount: true }
      }),
      // Yearly recurring revenue
      prisma.subscription.findMany({
        where: { 
          status: 'ACTIVE',
          billingCycle: 'YEARLY'
        },
        select: { amount: true }
      }),
      // Churn rate (cancelled in last 30 days)
      prisma.subscription.count({
        where: {
          status: 'CANCELLED',
          updatedAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
          }
        }
      }),
      // New subscriptions (last 30 days)
      prisma.subscription.count({
        where: {
          status: 'ACTIVE',
          createdAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
          }
        }
      })
    ])

    const [byStatus, byPlan, revenueMetrics, monthlyRevenue, yearlyRevenue, churnCount, newSubscriptions] = stats

    // Calculate MRR and ARR
    const mrr = monthlyRevenue.reduce((sum, sub) => sum + Number(sub.amount), 0) +
                (yearlyRevenue.reduce((sum, sub) => sum + Number(sub.amount), 0) / 12)
    const arr = mrr * 12

    return NextResponse.json({
      subscriptions: subscriptions.map(sub => ({
        ...sub,
        company: {
          ...sub.company,
          metrics: {
            totalUsers: sub.company._count.members,
            totalCustomers: sub.company._count.customers,
            totalQuotations: sub.company._count.quotations,
            totalInvoices: sub.company._count.invoices
          }
        }
      })),
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      stats: {
        total,
        byStatus: byStatus.map(item => ({
          status: item.status,
          count: item._count.id
        })),
        byPlan: byPlan.map(item => ({
          plan: item.plan,
          count: item._count.id
        })),
        revenue: {
          total: Number(revenueMetrics._sum.amount || 0),
          average: Number(revenueMetrics._avg.amount || 0),
          activeSubscriptions: revenueMetrics._count.id,
          mrr: Math.round(mrr),
          arr: Math.round(arr)
        },
        metrics: {
          churnCount,
          newSubscriptions,
          churnRate: total > 0 ? (churnCount / total) * 100 : 0,
          growthRate: total > 0 ? (newSubscriptions / total) * 100 : 0
        }
      }
    })

  } catch (error) {
    console.error('Error fetching subscriptions:', error)
    return NextResponse.json(
      { error: 'Failed to fetch subscriptions' },
      { status: 500 }
    )
  }
}

// POST /api/super-admin/subscriptions - Create or update subscription
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || session?.user?.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Super admin access required' }, { status: 403 })
    }

    const body = await request.json()
    const {
      companyId,
      plan,
      status,
      amount,
      billingCycle,
      startDate,
      endDate,
      trialEndDate,
      stripeSubscriptionId,
      stripeCustomerId
    } = body

    // Validate required fields
    if (!companyId || !plan || !amount) {
      return NextResponse.json(
        { error: 'Company ID, plan, and amount are required' },
        { status: 400 }
      )
    }

    // Check if company exists
    const company = await prisma.company.findUnique({
      where: { id: companyId }
    })

    if (!company) {
      return NextResponse.json({ error: 'Company not found' }, { status: 404 })
    }

    // Check if subscription already exists
    const existingSubscription = await prisma.subscription.findUnique({
      where: { companyId }
    })

    let subscription
    if (existingSubscription) {
      // Update existing subscription
      subscription = await prisma.subscription.update({
        where: { companyId },
        data: {
          plan,
          status: status || 'ACTIVE',
          amount,
          billingCycle: billingCycle || 'MONTHLY',
          startDate: startDate ? new Date(startDate) : undefined,
          endDate: endDate ? new Date(endDate) : undefined,
          trialEndDate: trialEndDate ? new Date(trialEndDate) : undefined,
          stripeSubscriptionId,
          stripeCustomerId
        },
        include: {
          company: {
            select: {
              name: true,
              email: true
            }
          }
        }
      })

      // Log the action
      await prisma.auditLog.create({
        data: {
          action: 'SUBSCRIPTION_UPDATED',
          entityType: 'Subscription',
          entityId: subscription.id,
          userId: session.user.id,
          userEmail: session.user.email,
          userRole: session.user.role,
          companyId,
          newValues: {
            plan,
            status,
            amount,
            billingCycle
          },
          metadata: {
            updatedByAdmin: true,
            adminId: session.user.id
          }
        }
      })
    } else {
      // Create new subscription
      subscription = await prisma.subscription.create({
        data: {
          companyId,
          plan,
          status: status || 'ACTIVE',
          amount,
          billingCycle: billingCycle || 'MONTHLY',
          startDate: startDate ? new Date(startDate) : new Date(),
          endDate: endDate ? new Date(endDate) : undefined,
          trialEndDate: trialEndDate ? new Date(trialEndDate) : undefined,
          stripeSubscriptionId,
          stripeCustomerId
        },
        include: {
          company: {
            select: {
              name: true,
              email: true
            }
          }
        }
      })

      // Log the action
      await prisma.auditLog.create({
        data: {
          action: 'SUBSCRIPTION_CREATED',
          entityType: 'Subscription',
          entityId: subscription.id,
          userId: session.user.id,
          userEmail: session.user.email,
          userRole: session.user.role,
          companyId,
          newValues: {
            plan,
            status,
            amount,
            billingCycle
          },
          metadata: {
            createdByAdmin: true,
            adminId: session.user.id
          }
        }
      })
    }

    return NextResponse.json({
      subscription,
      message: existingSubscription ? 'Subscription updated successfully' : 'Subscription created successfully'
    }, { status: existingSubscription ? 200 : 201 })

  } catch (error) {
    console.error('Error managing subscription:', error)
    return NextResponse.json(
      { error: 'Failed to manage subscription' },
      { status: 500 }
    )
  }
}
