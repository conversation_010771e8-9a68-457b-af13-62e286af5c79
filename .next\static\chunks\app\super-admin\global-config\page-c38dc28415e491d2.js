(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4228],{93364:function(e,s,a){Promise.resolve().then(a.bind(a,88393))},88393:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return k}});var r=a(57437),l=a(2265),n=a(82749),i=a(24033),t=a(27815),c=a(85754),o=a(45179),d=a(49842),m=a(23444),u=a(86443),h=a(45509),x=a(40110),p=a(28956),f=a(64280),g=a(92295),j=a(22812),v=a(29409),y=a(49036),b=a(5589),N=a(1295),C=a(77216),w=a(99670),S=a(25750),P=a(5925);function k(){var e;let{data:s,status:a}=(0,n.useSession)(),[k,F]=(0,l.useState)({appName:"SaaS Platform",appDescription:"Modern SaaS application for business management",appUrl:"https://yourapp.com",supportEmail:"<EMAIL>",companyName:"Your Company",companyAddress:"123 Business St, City, Country",companyPhone:"+****************",logoUrl:"",faviconUrl:"",primaryColor:"#3b82f6",secondaryColor:"#64748b",accentColor:"#10b981",backgroundColor:"#ffffff",textColor:"#1f2937",theme:"light",fontFamily:"Inter, sans-serif",customCss:"",timezone:"UTC",dateFormat:"MM/DD/YYYY",currency:"USD",language:"en",enableRegistration:!0,enableTrials:!0,enableMultiTenant:!0,enableApiAccess:!0,enableWebhooks:!0,sessionTimeout:24,passwordMinLength:8,requireTwoFactor:!1,allowSocialLogin:!0,emailProvider:"smtp",smtpHost:"",smtpPort:587,smtpUsername:"",smtpPassword:"",smtpSecure:!0,maxUsersPerCompany:100,maxCompaniesPerUser:5,defaultStorageLimit:5,maintenanceMode:!1,maintenanceMessage:"System is under maintenance. Please check back later."}),[M,U]=(0,l.useState)(!0),[_,Y]=(0,l.useState)(!1),[A,T]=(0,l.useState)(!1);(0,l.useEffect)(()=>{Q()},[]);let Q=async()=>{try{let e=await fetch("/api/super-admin/global-config"),s=await e.json();s.success&&s.config&&F(e=>({...e,...s.config}))}catch(e){console.error("Error fetching config:",e),P.toast.error("Failed to load configuration")}finally{U(!1)}};if("loading"===a)return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})});"unauthenticated"===a&&(0,i.redirect)("/auth/signin"),(null==s?void 0:null===(e=s.user)||void 0===e?void 0:e.role)!=="SUPER_ADMIN"&&(0,i.redirect)("/dashboard"),(0,l.useEffect)(()=>{Q()},[]);let D=(e,s)=>{F(a=>({...a,[e]:s}))},I=async()=>{Y(!0);try{let e=await fetch("/api/super-admin/global-config",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(k)}),s=await e.json();s.success?(P.toast.success("Configuration saved successfully!"),window.location.reload()):P.toast.error(s.error||"Failed to save configuration")}catch(e){console.error("Error saving config:",e),P.toast.error("Failed to save configuration")}finally{Y(!1)}};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(p.Z,{className:"h-8 w-8 text-blue-600"}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Global Configuration"})]}),(0,r.jsx)("p",{className:"text-gray-500 mt-1",children:"Manage system-wide settings and configuration"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)(c.z,{variant:"outline",onClick:Q,disabled:M,children:[(0,r.jsx)(f.Z,{className:"h-4 w-4 mr-2 ".concat(M?"animate-spin":"")}),"Refresh"]}),(0,r.jsxs)(c.z,{onClick:I,disabled:_,children:[(0,r.jsx)(g.Z,{className:"h-4 w-4 mr-2 ".concat(_?"animate-spin":"")}),"Save Changes"]})]})]}),(0,r.jsxs)(x.mQ,{defaultValue:"application",className:"space-y-6",children:[(0,r.jsxs)(x.dr,{className:"grid w-full grid-cols-7",children:[(0,r.jsx)(x.SP,{value:"application",children:"Application"}),(0,r.jsx)(x.SP,{value:"branding",children:"Branding"}),(0,r.jsx)(x.SP,{value:"system",children:"System"}),(0,r.jsx)(x.SP,{value:"features",children:"Features"}),(0,r.jsx)(x.SP,{value:"security",children:"Security"}),(0,r.jsx)(x.SP,{value:"email",children:"Email"}),(0,r.jsx)(x.SP,{value:"limits",children:"Limits"})]}),(0,r.jsx)(x.nU,{value:"application",children:(0,r.jsxs)(t.Zb,{children:[(0,r.jsxs)(t.Ol,{children:[(0,r.jsxs)(t.ll,{className:"flex items-center",children:[(0,r.jsx)(j.Z,{className:"h-5 w-5 mr-2"}),"Application Settings"]}),(0,r.jsx)(t.SZ,{children:"Basic application information and company details"})]}),(0,r.jsxs)(t.aY,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d._,{htmlFor:"appName",children:"Application Name"}),(0,r.jsx)(o.I,{id:"appName",value:k.appName,onChange:e=>D("appName",e.target.value),placeholder:"Your SaaS App"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d._,{htmlFor:"appUrl",children:"Application URL"}),(0,r.jsx)(o.I,{id:"appUrl",value:k.appUrl,onChange:e=>D("appUrl",e.target.value),placeholder:"https://yourapp.com"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d._,{htmlFor:"appDescription",children:"Application Description"}),(0,r.jsx)(m.g,{id:"appDescription",value:k.appDescription,onChange:e=>D("appDescription",e.target.value),placeholder:"Brief description of your application",rows:3})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d._,{htmlFor:"companyName",children:"Company Name"}),(0,r.jsx)(o.I,{id:"companyName",value:k.companyName,onChange:e=>D("companyName",e.target.value),placeholder:"Your Company Inc."})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d._,{htmlFor:"supportEmail",children:"Support Email"}),(0,r.jsx)(o.I,{id:"supportEmail",type:"email",value:k.supportEmail,onChange:e=>D("supportEmail",e.target.value),placeholder:"<EMAIL>"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d._,{htmlFor:"companyPhone",children:"Company Phone"}),(0,r.jsx)(o.I,{id:"companyPhone",value:k.companyPhone,onChange:e=>D("companyPhone",e.target.value),placeholder:"+****************"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d._,{htmlFor:"companyAddress",children:"Company Address"}),(0,r.jsx)(o.I,{id:"companyAddress",value:k.companyAddress,onChange:e=>D("companyAddress",e.target.value),placeholder:"123 Business St, City, Country"})]})]})]})]})}),(0,r.jsx)(x.nU,{value:"branding",children:(0,r.jsxs)(t.Zb,{children:[(0,r.jsxs)(t.Ol,{children:[(0,r.jsxs)(t.ll,{className:"flex items-center",children:[(0,r.jsx)(j.Z,{className:"h-5 w-5 mr-2"}),"Branding & Appearance"]}),(0,r.jsx)(t.SZ,{children:"Customize your application's visual identity and branding"})]}),(0,r.jsxs)(t.aY,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d._,{htmlFor:"logoUrl",children:"Logo URL"}),(0,r.jsx)(o.I,{id:"logoUrl",value:k.logoUrl,onChange:e=>D("logoUrl",e.target.value),placeholder:"https://example.com/logo.png"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Recommended: 200x50px PNG or SVG"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d._,{htmlFor:"faviconUrl",children:"Favicon URL"}),(0,r.jsx)(o.I,{id:"faviconUrl",value:k.faviconUrl,onChange:e=>D("faviconUrl",e.target.value),placeholder:"https://example.com/favicon.ico"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Recommended: 32x32px ICO or PNG"})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h3",{className:"text-lg font-medium",children:"Color Scheme"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d._,{htmlFor:"primaryColor",children:"Primary Color"}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)(o.I,{id:"primaryColor",type:"color",value:k.primaryColor,onChange:e=>D("primaryColor",e.target.value),className:"w-16 h-10 p-1 border rounded"}),(0,r.jsx)(o.I,{value:k.primaryColor,onChange:e=>D("primaryColor",e.target.value),placeholder:"#3b82f6",className:"flex-1"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d._,{htmlFor:"secondaryColor",children:"Secondary Color"}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)(o.I,{id:"secondaryColor",type:"color",value:k.secondaryColor,onChange:e=>D("secondaryColor",e.target.value),className:"w-16 h-10 p-1 border rounded"}),(0,r.jsx)(o.I,{value:k.secondaryColor,onChange:e=>D("secondaryColor",e.target.value),placeholder:"#64748b",className:"flex-1"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d._,{htmlFor:"accentColor",children:"Accent Color"}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)(o.I,{id:"accentColor",type:"color",value:k.accentColor,onChange:e=>D("accentColor",e.target.value),className:"w-16 h-10 p-1 border rounded"}),(0,r.jsx)(o.I,{value:k.accentColor,onChange:e=>D("accentColor",e.target.value),placeholder:"#10b981",className:"flex-1"})]})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d._,{htmlFor:"theme",children:"Theme"}),(0,r.jsxs)(h.Ph,{value:k.theme,onValueChange:e=>D("theme",e),children:[(0,r.jsx)(h.i4,{children:(0,r.jsx)(h.ki,{placeholder:"Select theme"})}),(0,r.jsxs)(h.Bw,{children:[(0,r.jsx)(h.Ql,{value:"light",children:"Light"}),(0,r.jsx)(h.Ql,{value:"dark",children:"Dark"}),(0,r.jsx)(h.Ql,{value:"auto",children:"Auto (System)"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d._,{htmlFor:"fontFamily",children:"Font Family"}),(0,r.jsxs)(h.Ph,{value:k.fontFamily,onValueChange:e=>D("fontFamily",e),children:[(0,r.jsx)(h.i4,{children:(0,r.jsx)(h.ki,{placeholder:"Select font"})}),(0,r.jsxs)(h.Bw,{children:[(0,r.jsx)(h.Ql,{value:"Inter, sans-serif",children:"Inter"}),(0,r.jsx)(h.Ql,{value:"Roboto, sans-serif",children:"Roboto"}),(0,r.jsx)(h.Ql,{value:"Open Sans, sans-serif",children:"Open Sans"}),(0,r.jsx)(h.Ql,{value:"Lato, sans-serif",children:"Lato"}),(0,r.jsx)(h.Ql,{value:"Poppins, sans-serif",children:"Poppins"}),(0,r.jsx)(h.Ql,{value:"Montserrat, sans-serif",children:"Montserrat"})]})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d._,{htmlFor:"customCss",children:"Custom CSS"}),(0,r.jsx)(m.g,{id:"customCss",value:k.customCss,onChange:e=>D("customCss",e.target.value),placeholder:"/* Add your custom CSS here */ .custom-class { color: #333; }",rows:6,className:"font-mono text-sm"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Add custom CSS to override default styles. Use with caution."})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d._,{children:"Preview"}),(0,r.jsxs)("div",{className:"p-4 border rounded-lg",style:{backgroundColor:k.backgroundColor,color:k.textColor,fontFamily:k.fontFamily},children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[k.logoUrl?(0,r.jsx)("img",{src:k.logoUrl,alt:"Logo",className:"h-8"}):(0,r.jsx)("div",{className:"w-8 h-8 rounded",style:{backgroundColor:k.primaryColor}}),(0,r.jsx)("span",{className:"font-bold",children:k.appName})]}),(0,r.jsx)("button",{className:"px-4 py-2 rounded text-white text-sm",style:{backgroundColor:k.primaryColor},children:"Primary Button"}),(0,r.jsx)("button",{className:"px-4 py-2 rounded text-white text-sm ml-2",style:{backgroundColor:k.accentColor},children:"Accent Button"})]})]})]})]})}),(0,r.jsx)(x.nU,{value:"system",children:(0,r.jsxs)(t.Zb,{children:[(0,r.jsxs)(t.Ol,{children:[(0,r.jsxs)(t.ll,{className:"flex items-center",children:[(0,r.jsx)(v.Z,{className:"h-5 w-5 mr-2"}),"System Settings"]}),(0,r.jsx)(t.SZ,{children:"Configure system-wide preferences and defaults"})]}),(0,r.jsxs)(t.aY,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d._,{htmlFor:"timezone",children:"Default Timezone"}),(0,r.jsxs)(h.Ph,{value:k.timezone,onValueChange:e=>D("timezone",e),children:[(0,r.jsx)(h.i4,{children:(0,r.jsx)(h.ki,{})}),(0,r.jsxs)(h.Bw,{children:[(0,r.jsx)(h.Ql,{value:"UTC",children:"UTC"}),(0,r.jsx)(h.Ql,{value:"America/New_York",children:"Eastern Time"}),(0,r.jsx)(h.Ql,{value:"America/Chicago",children:"Central Time"}),(0,r.jsx)(h.Ql,{value:"America/Denver",children:"Mountain Time"}),(0,r.jsx)(h.Ql,{value:"America/Los_Angeles",children:"Pacific Time"}),(0,r.jsx)(h.Ql,{value:"Europe/London",children:"London"}),(0,r.jsx)(h.Ql,{value:"Europe/Paris",children:"Paris"}),(0,r.jsx)(h.Ql,{value:"Asia/Tokyo",children:"Tokyo"}),(0,r.jsx)(h.Ql,{value:"Asia/Shanghai",children:"Shanghai"}),(0,r.jsx)(h.Ql,{value:"Asia/Kolkata",children:"India"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d._,{htmlFor:"dateFormat",children:"Date Format"}),(0,r.jsxs)(h.Ph,{value:k.dateFormat,onValueChange:e=>D("dateFormat",e),children:[(0,r.jsx)(h.i4,{children:(0,r.jsx)(h.ki,{})}),(0,r.jsxs)(h.Bw,{children:[(0,r.jsx)(h.Ql,{value:"MM/DD/YYYY",children:"MM/DD/YYYY"}),(0,r.jsx)(h.Ql,{value:"DD/MM/YYYY",children:"DD/MM/YYYY"}),(0,r.jsx)(h.Ql,{value:"YYYY-MM-DD",children:"YYYY-MM-DD"}),(0,r.jsx)(h.Ql,{value:"DD-MM-YYYY",children:"DD-MM-YYYY"})]})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d._,{htmlFor:"currency",children:"Default Currency"}),(0,r.jsxs)(h.Ph,{value:k.currency,onValueChange:e=>D("currency",e),children:[(0,r.jsx)(h.i4,{children:(0,r.jsx)(h.ki,{})}),(0,r.jsxs)(h.Bw,{children:[(0,r.jsx)(h.Ql,{value:"USD",children:"USD - US Dollar"}),(0,r.jsx)(h.Ql,{value:"EUR",children:"EUR - Euro"}),(0,r.jsx)(h.Ql,{value:"GBP",children:"GBP - British Pound"}),(0,r.jsx)(h.Ql,{value:"JPY",children:"JPY - Japanese Yen"}),(0,r.jsx)(h.Ql,{value:"INR",children:"INR - Indian Rupee"}),(0,r.jsx)(h.Ql,{value:"CAD",children:"CAD - Canadian Dollar"}),(0,r.jsx)(h.Ql,{value:"AUD",children:"AUD - Australian Dollar"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d._,{htmlFor:"language",children:"Default Language"}),(0,r.jsxs)(h.Ph,{value:k.language,onValueChange:e=>D("language",e),children:[(0,r.jsx)(h.i4,{children:(0,r.jsx)(h.ki,{})}),(0,r.jsxs)(h.Bw,{children:[(0,r.jsx)(h.Ql,{value:"en",children:"English"}),(0,r.jsx)(h.Ql,{value:"es",children:"Spanish"}),(0,r.jsx)(h.Ql,{value:"fr",children:"French"}),(0,r.jsx)(h.Ql,{value:"de",children:"German"}),(0,r.jsx)(h.Ql,{value:"it",children:"Italian"}),(0,r.jsx)(h.Ql,{value:"pt",children:"Portuguese"}),(0,r.jsx)(h.Ql,{value:"ja",children:"Japanese"}),(0,r.jsx)(h.Ql,{value:"ko",children:"Korean"}),(0,r.jsx)(h.Ql,{value:"zh",children:"Chinese"}),(0,r.jsx)(h.Ql,{value:"hi",children:"Hindi"})]})]})]})]}),(0,r.jsxs)("div",{className:"space-y-4 p-4 border rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(d._,{htmlFor:"maintenanceMode",className:"text-base font-medium",children:"Maintenance Mode"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Enable to put the application in maintenance mode"})]}),(0,r.jsx)(u.r,{id:"maintenanceMode",checked:k.maintenanceMode,onCheckedChange:e=>D("maintenanceMode",e)})]}),k.maintenanceMode&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d._,{htmlFor:"maintenanceMessage",children:"Maintenance Message"}),(0,r.jsx)(m.g,{id:"maintenanceMessage",value:k.maintenanceMessage,onChange:e=>D("maintenanceMessage",e.target.value),placeholder:"System is under maintenance...",rows:2})]})]})]})]})}),(0,r.jsx)(x.nU,{value:"features",children:(0,r.jsxs)(t.Zb,{children:[(0,r.jsxs)(t.Ol,{children:[(0,r.jsxs)(t.ll,{className:"flex items-center",children:[(0,r.jsx)(y.Z,{className:"h-5 w-5 mr-2"}),"Feature Flags"]}),(0,r.jsx)(t.SZ,{children:"Enable or disable application features"})]}),(0,r.jsx)(t.aY,{className:"space-y-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(d._,{htmlFor:"enableRegistration",className:"text-base font-medium",children:"User Registration"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Allow new users to register accounts"})]}),(0,r.jsx)(u.r,{id:"enableRegistration",checked:k.enableRegistration,onCheckedChange:e=>D("enableRegistration",e)})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(d._,{htmlFor:"enableTrials",className:"text-base font-medium",children:"Free Trials"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Enable free trial periods for new users"})]}),(0,r.jsx)(u.r,{id:"enableTrials",checked:k.enableTrials,onCheckedChange:e=>D("enableTrials",e)})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(d._,{htmlFor:"enableMultiTenant",className:"text-base font-medium",children:"Multi-Tenant"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Allow users to manage multiple companies"})]}),(0,r.jsx)(u.r,{id:"enableMultiTenant",checked:k.enableMultiTenant,onCheckedChange:e=>D("enableMultiTenant",e)})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(d._,{htmlFor:"enableApiAccess",className:"text-base font-medium",children:"API Access"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Enable REST API access for integrations"})]}),(0,r.jsx)(u.r,{id:"enableApiAccess",checked:k.enableApiAccess,onCheckedChange:e=>D("enableApiAccess",e)})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(d._,{htmlFor:"enableWebhooks",className:"text-base font-medium",children:"Webhooks"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Enable webhook notifications for events"})]}),(0,r.jsx)(u.r,{id:"enableWebhooks",checked:k.enableWebhooks,onCheckedChange:e=>D("enableWebhooks",e)})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(d._,{htmlFor:"allowSocialLogin",className:"text-base font-medium",children:"Social Login"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Allow login with Google, GitHub, etc."})]}),(0,r.jsx)(u.r,{id:"allowSocialLogin",checked:k.allowSocialLogin,onCheckedChange:e=>D("allowSocialLogin",e)})]})]})]})})]})}),(0,r.jsx)(x.nU,{value:"security",children:(0,r.jsxs)(t.Zb,{children:[(0,r.jsxs)(t.Ol,{children:[(0,r.jsxs)(t.ll,{className:"flex items-center",children:[(0,r.jsx)(b.Z,{className:"h-5 w-5 mr-2"}),"Security Settings"]}),(0,r.jsx)(t.SZ,{children:"Configure security policies and authentication settings"})]}),(0,r.jsxs)(t.aY,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d._,{htmlFor:"sessionTimeout",children:"Session Timeout (hours)"}),(0,r.jsx)(o.I,{id:"sessionTimeout",type:"number",value:k.sessionTimeout,onChange:e=>D("sessionTimeout",parseInt(e.target.value)||24),min:"1",max:"168"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Maximum session duration before auto-logout"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d._,{htmlFor:"passwordMinLength",children:"Minimum Password Length"}),(0,r.jsx)(o.I,{id:"passwordMinLength",type:"number",value:k.passwordMinLength,onChange:e=>D("passwordMinLength",parseInt(e.target.value)||8),min:"6",max:"32"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Minimum characters required for passwords"})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(d._,{htmlFor:"requireTwoFactor",className:"text-base font-medium",children:"Require Two-Factor Authentication"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Force all users to enable 2FA for enhanced security"})]}),(0,r.jsx)(u.r,{id:"requireTwoFactor",checked:k.requireTwoFactor,onCheckedChange:e=>D("requireTwoFactor",e)})]})]})]})}),(0,r.jsx)(x.nU,{value:"email",children:(0,r.jsxs)(t.Zb,{children:[(0,r.jsxs)(t.Ol,{children:[(0,r.jsxs)(t.ll,{className:"flex items-center",children:[(0,r.jsx)(N.Z,{className:"h-5 w-5 mr-2"}),"Email Configuration"]}),(0,r.jsx)(t.SZ,{children:"Configure SMTP settings for transactional emails"})]}),(0,r.jsxs)(t.aY,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d._,{htmlFor:"emailProvider",children:"Email Provider"}),(0,r.jsxs)(h.Ph,{value:k.emailProvider,onValueChange:e=>D("emailProvider",e),children:[(0,r.jsx)(h.i4,{children:(0,r.jsx)(h.ki,{})}),(0,r.jsxs)(h.Bw,{children:[(0,r.jsx)(h.Ql,{value:"smtp",children:"Custom SMTP"}),(0,r.jsx)(h.Ql,{value:"sendgrid",children:"SendGrid"}),(0,r.jsx)(h.Ql,{value:"mailgun",children:"Mailgun"}),(0,r.jsx)(h.Ql,{value:"ses",children:"Amazon SES"}),(0,r.jsx)(h.Ql,{value:"postmark",children:"Postmark"})]})]})]}),"smtp"===k.emailProvider&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d._,{htmlFor:"smtpHost",children:"SMTP Host"}),(0,r.jsx)(o.I,{id:"smtpHost",value:k.smtpHost,onChange:e=>D("smtpHost",e.target.value),placeholder:"smtp.gmail.com"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d._,{htmlFor:"smtpPort",children:"SMTP Port"}),(0,r.jsx)(o.I,{id:"smtpPort",type:"number",value:k.smtpPort,onChange:e=>D("smtpPort",parseInt(e.target.value)||587),placeholder:"587"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d._,{htmlFor:"smtpUsername",children:"SMTP Username"}),(0,r.jsx)(o.I,{id:"smtpUsername",value:k.smtpUsername,onChange:e=>D("smtpUsername",e.target.value),placeholder:"<EMAIL>"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d._,{htmlFor:"smtpPassword",children:"SMTP Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(o.I,{id:"smtpPassword",type:A?"text":"password",value:k.smtpPassword,onChange:e=>D("smtpPassword",e.target.value),placeholder:"your-app-password"}),(0,r.jsx)(c.z,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>T(!A),children:A?(0,r.jsx)(C.Z,{className:"h-4 w-4"}):(0,r.jsx)(w.Z,{className:"h-4 w-4"})})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(d._,{htmlFor:"smtpSecure",className:"text-base font-medium",children:"Use TLS/SSL"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Enable secure connection for SMTP"})]}),(0,r.jsx)(u.r,{id:"smtpSecure",checked:k.smtpSecure,onCheckedChange:e=>D("smtpSecure",e)})]})]})]})]})}),(0,r.jsx)(x.nU,{value:"limits",children:(0,r.jsxs)(t.Zb,{children:[(0,r.jsxs)(t.Ol,{children:[(0,r.jsxs)(t.ll,{className:"flex items-center",children:[(0,r.jsx)(S.Z,{className:"h-5 w-5 mr-2"}),"System Limits"]}),(0,r.jsx)(t.SZ,{children:"Configure default limits and quotas"})]}),(0,r.jsx)(t.aY,{className:"space-y-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d._,{htmlFor:"maxUsersPerCompany",children:"Max Users per Company"}),(0,r.jsx)(o.I,{id:"maxUsersPerCompany",type:"number",value:k.maxUsersPerCompany,onChange:e=>D("maxUsersPerCompany",parseInt(e.target.value)||100),min:"1"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Default limit for new companies"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d._,{htmlFor:"maxCompaniesPerUser",children:"Max Companies per User"}),(0,r.jsx)(o.I,{id:"maxCompaniesPerUser",type:"number",value:k.maxCompaniesPerUser,onChange:e=>D("maxCompaniesPerUser",parseInt(e.target.value)||5),min:"1"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"How many companies a user can join"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d._,{htmlFor:"defaultStorageLimit",children:"Default Storage Limit (GB)"}),(0,r.jsx)(o.I,{id:"defaultStorageLimit",type:"number",value:k.defaultStorageLimit,onChange:e=>D("defaultStorageLimit",parseInt(e.target.value)||5),min:"1"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Default storage quota for new companies"})]})]})})]})})]})]})}},85754:function(e,s,a){"use strict";a.d(s,{z:function(){return o}});var r=a(57437),l=a(2265),n=a(67256),i=a(96061),t=a(1657);let c=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=l.forwardRef((e,s)=>{let{className:a,variant:l,size:i,asChild:o=!1,...d}=e,m=o?n.g7:"button";return(0,r.jsx)(m,{className:(0,t.cn)(c({variant:l,size:i,className:a})),ref:s,...d})});o.displayName="Button"},27815:function(e,s,a){"use strict";a.d(s,{Ol:function(){return t},SZ:function(){return o},Zb:function(){return i},aY:function(){return d},eW:function(){return m},ll:function(){return c}});var r=a(57437),l=a(2265),n=a(1657);let i=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)("div",{ref:s,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...l})});i.displayName="Card";let t=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",a),...l})});t.displayName="CardHeader";let c=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)("h3",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",a),...l})});c.displayName="CardTitle";let o=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",a),...l})});o.displayName="CardDescription";let d=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)("div",{ref:s,className:(0,n.cn)("p-6 pt-0",a),...l})});d.displayName="CardContent";let m=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",a),...l})});m.displayName="CardFooter"},45179:function(e,s,a){"use strict";a.d(s,{I:function(){return i}});var r=a(57437),l=a(2265),n=a(1657);let i=l.forwardRef((e,s)=>{let{className:a,type:l,...i}=e;return(0,r.jsx)("input",{type:l,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:s,...i})});i.displayName="Input"},49842:function(e,s,a){"use strict";a.d(s,{_:function(){return o}});var r=a(57437),l=a(2265),n=a(36743),i=a(96061),t=a(1657);let c=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)(n.f,{ref:s,className:(0,t.cn)(c(),a),...l})});o.displayName=n.f.displayName},45509:function(e,s,a){"use strict";a.d(s,{Bw:function(){return p},Ph:function(){return d},Ql:function(){return f},i4:function(){return u},ki:function(){return m}});var r=a(57437),l=a(2265),n=a(99530),i=a(83523),t=a(9224),c=a(62442),o=a(1657);let d=n.fC;n.ZA;let m=n.B4,u=l.forwardRef((e,s)=>{let{className:a,children:l,...t}=e;return(0,r.jsxs)(n.xz,{ref:s,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...t,children:[l,(0,r.jsx)(n.JO,{asChild:!0,children:(0,r.jsx)(i.Z,{className:"h-4 w-4 opacity-50"})})]})});u.displayName=n.xz.displayName;let h=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)(n.u_,{ref:s,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",a),...l,children:(0,r.jsx)(t.Z,{className:"h-4 w-4"})})});h.displayName=n.u_.displayName;let x=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)(n.$G,{ref:s,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",a),...l,children:(0,r.jsx)(i.Z,{className:"h-4 w-4"})})});x.displayName=n.$G.displayName;let p=l.forwardRef((e,s)=>{let{className:a,children:l,position:i="popper",...t}=e;return(0,r.jsx)(n.h_,{children:(0,r.jsxs)(n.VY,{ref:s,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:i,...t,children:[(0,r.jsx)(h,{}),(0,r.jsx)(n.l_,{className:(0,o.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:l}),(0,r.jsx)(x,{})]})})});p.displayName=n.VY.displayName,l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)(n.__,{ref:s,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...l})}).displayName=n.__.displayName;let f=l.forwardRef((e,s)=>{let{className:a,children:l,...i}=e;return(0,r.jsxs)(n.ck,{ref:s,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...i,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(n.wU,{children:(0,r.jsx)(c.Z,{className:"h-4 w-4"})})}),(0,r.jsx)(n.eT,{children:l})]})});f.displayName=n.ck.displayName,l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)(n.Z0,{ref:s,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",a),...l})}).displayName=n.Z0.displayName},86443:function(e,s,a){"use strict";a.d(s,{r:function(){return t}});var r=a(57437),l=a(2265),n=a(92376),i=a(1657);let t=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)(n.fC,{className:(0,i.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",a),...l,ref:s,children:(0,r.jsx)(n.bU,{className:(0,i.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});t.displayName=n.fC.displayName},40110:function(e,s,a){"use strict";a.d(s,{SP:function(){return o},dr:function(){return c},mQ:function(){return t},nU:function(){return d}});var r=a(57437),l=a(2265),n=a(34522),i=a(1657);let t=n.fC,c=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)(n.aV,{ref:s,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",a),...l})});c.displayName=n.aV.displayName;let o=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)(n.xz,{ref:s,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",a),...l})});o.displayName=n.xz.displayName;let d=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)(n.VY,{ref:s,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",a),...l})});d.displayName=n.VY.displayName},23444:function(e,s,a){"use strict";a.d(s,{g:function(){return i}});var r=a(57437),l=a(2265),n=a(1657);let i=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:s,...l})});i.displayName="Textarea"},1657:function(e,s,a){"use strict";a.d(s,{cn:function(){return n}});var r=a(57042),l=a(74769);function n(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,l.m6)((0,r.W)(s))}}},function(e){e.O(0,[6723,9502,2749,1706,4138,4997,4522,2747,2971,4938,1744],function(){return e(e.s=93364)}),_N_E=e.O()}]);