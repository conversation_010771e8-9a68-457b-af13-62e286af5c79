"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui";
exports.ids = ["vendor-chunks/@radix-ui"];
exports.modules = {

/***/ "(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/@radix-ui/primitive/dist/index.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeEventHandlers: () => (/* binding */ composeEventHandlers)\n/* harmony export */ });\n// packages/core/primitive/src/primitive.tsx\nfunction composeEventHandlers(originalEventHandler, ourEventHandler, { checkForDefaultPrevented = true } = {}) {\n    return function handleEvent(event) {\n        originalEventHandler?.(event);\n        if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n            return ourEventHandler?.(event);\n        }\n    };\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3ByaW1pdGl2ZS9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsNENBQTRDO0FBQzVDLFNBQVNBLHFCQUFxQkMsb0JBQW9CLEVBQUVDLGVBQWUsRUFBRSxFQUFFQywyQkFBMkIsSUFBSSxFQUFFLEdBQUcsQ0FBQyxDQUFDO0lBQzNHLE9BQU8sU0FBU0MsWUFBWUMsS0FBSztRQUMvQkosdUJBQXVCSTtRQUN2QixJQUFJRiw2QkFBNkIsU0FBUyxDQUFDRSxNQUFNQyxnQkFBZ0IsRUFBRTtZQUNqRSxPQUFPSixrQkFBa0JHO1FBQzNCO0lBQ0Y7QUFDRjtBQUdFLENBQ0Ysa0NBQWtDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dGpzLXNhYXMtYXBwLy4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9wcmltaXRpdmUvZGlzdC9pbmRleC5tanM/MTg2OCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9jb3JlL3ByaW1pdGl2ZS9zcmMvcHJpbWl0aXZlLnRzeFxuZnVuY3Rpb24gY29tcG9zZUV2ZW50SGFuZGxlcnMob3JpZ2luYWxFdmVudEhhbmRsZXIsIG91ckV2ZW50SGFuZGxlciwgeyBjaGVja0ZvckRlZmF1bHRQcmV2ZW50ZWQgPSB0cnVlIH0gPSB7fSkge1xuICByZXR1cm4gZnVuY3Rpb24gaGFuZGxlRXZlbnQoZXZlbnQpIHtcbiAgICBvcmlnaW5hbEV2ZW50SGFuZGxlcj8uKGV2ZW50KTtcbiAgICBpZiAoY2hlY2tGb3JEZWZhdWx0UHJldmVudGVkID09PSBmYWxzZSB8fCAhZXZlbnQuZGVmYXVsdFByZXZlbnRlZCkge1xuICAgICAgcmV0dXJuIG91ckV2ZW50SGFuZGxlcj8uKGV2ZW50KTtcbiAgICB9XG4gIH07XG59XG5leHBvcnQge1xuICBjb21wb3NlRXZlbnRIYW5kbGVyc1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6WyJjb21wb3NlRXZlbnRIYW5kbGVycyIsIm9yaWdpbmFsRXZlbnRIYW5kbGVyIiwib3VyRXZlbnRIYW5kbGVyIiwiY2hlY2tGb3JEZWZhdWx0UHJldmVudGVkIiwiaGFuZGxlRXZlbnQiLCJldmVudCIsImRlZmF1bHRQcmV2ZW50ZWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-compose-refs/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeRefs: () => (/* binding */ composeRefs),\n/* harmony export */   useComposedRefs: () => (/* binding */ useComposedRefs)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/compose-refs/src/compose-refs.tsx\n\nfunction setRef(ref, value) {\n    if (typeof ref === \"function\") {\n        return ref(value);\n    } else if (ref !== null && ref !== void 0) {\n        ref.current = value;\n    }\n}\nfunction composeRefs(...refs) {\n    return (node)=>{\n        let hasCleanup = false;\n        const cleanups = refs.map((ref)=>{\n            const cleanup = setRef(ref, node);\n            if (!hasCleanup && typeof cleanup == \"function\") {\n                hasCleanup = true;\n            }\n            return cleanup;\n        });\n        if (hasCleanup) {\n            return ()=>{\n                for(let i = 0; i < cleanups.length; i++){\n                    const cleanup = cleanups[i];\n                    if (typeof cleanup == \"function\") {\n                        cleanup();\n                    } else {\n                        setRef(refs[i], null);\n                    }\n                }\n            };\n        }\n    };\n}\nfunction useComposedRefs(...refs) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(composeRefs(...refs), refs);\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@radix-ui/react-context/dist/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createContext: () => (/* binding */ createContext2),\n/* harmony export */   createContextScope: () => (/* binding */ createContextScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/context/src/create-context.tsx\n\n\nfunction createContext2(rootComponentName, defaultContext) {\n    const Context = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    const Provider = (props)=>{\n        const { children, ...context } = props;\n        const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>context, Object.values(context));\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, {\n            value,\n            children\n        });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName) {\n        const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n        if (context) return context;\n        if (defaultContext !== void 0) return defaultContext;\n        throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [\n        Provider,\n        useContext2\n    ];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n    let defaultContexts = [];\n    function createContext3(rootComponentName, defaultContext) {\n        const BaseContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n        const index = defaultContexts.length;\n        defaultContexts = [\n            ...defaultContexts,\n            defaultContext\n        ];\n        const Provider = (props)=>{\n            const { scope, children, ...context } = props;\n            const Context = scope?.[scopeName]?.[index] || BaseContext;\n            const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>context, Object.values(context));\n            return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, {\n                value,\n                children\n            });\n        };\n        Provider.displayName = rootComponentName + \"Provider\";\n        function useContext2(consumerName, scope) {\n            const Context = scope?.[scopeName]?.[index] || BaseContext;\n            const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n            if (context) return context;\n            if (defaultContext !== void 0) return defaultContext;\n            throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n        }\n        return [\n            Provider,\n            useContext2\n        ];\n    }\n    const createScope = ()=>{\n        const scopeContexts = defaultContexts.map((defaultContext)=>{\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n        });\n        return function useScope(scope) {\n            const contexts = scope?.[scopeName] || scopeContexts;\n            return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n                    [`__scope${scopeName}`]: {\n                        ...scope,\n                        [scopeName]: contexts\n                    }\n                }), [\n                scope,\n                contexts\n            ]);\n        };\n    };\n    createScope.scopeName = scopeName;\n    return [\n        createContext3,\n        composeContextScopes(createScope, ...createContextScopeDeps)\n    ];\n}\nfunction composeContextScopes(...scopes) {\n    const baseScope = scopes[0];\n    if (scopes.length === 1) return baseScope;\n    const createScope = ()=>{\n        const scopeHooks = scopes.map((createScope2)=>({\n                useScope: createScope2(),\n                scopeName: createScope2.scopeName\n            }));\n        return function useComposedScopes(overrideScopes) {\n            const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName })=>{\n                const scopeProps = useScope(overrideScopes);\n                const currentScope = scopeProps[`__scope${scopeName}`];\n                return {\n                    ...nextScopes2,\n                    ...currentScope\n                };\n            }, {});\n            return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n                    [`__scope${baseScope.scopeName}`]: nextScopes\n                }), [\n                nextScopes\n            ]);\n        };\n    };\n    createScope.scopeName = baseScope.scopeName;\n    return createScope;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWNvbnRleHQvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBLGdEQUFnRDtBQUNqQjtBQUNTO0FBQ3hDLFNBQVNFLGVBQWVDLGlCQUFpQixFQUFFQyxjQUFjO0lBQ3ZELE1BQU1DLHdCQUFVTCxnREFBbUIsQ0FBQ0k7SUFDcEMsTUFBTUcsV0FBVyxDQUFDQztRQUNoQixNQUFNLEVBQUVDLFFBQVEsRUFBRSxHQUFHQyxTQUFTLEdBQUdGO1FBQ2pDLE1BQU1HLFFBQVFYLDBDQUFhLENBQUMsSUFBTVUsU0FBU0csT0FBT0MsTUFBTSxDQUFDSjtRQUN6RCxPQUFPLGFBQWEsR0FBR1Qsc0RBQUdBLENBQUNJLFFBQVFFLFFBQVEsRUFBRTtZQUFFSTtZQUFPRjtRQUFTO0lBQ2pFO0lBQ0FGLFNBQVNRLFdBQVcsR0FBR1osb0JBQW9CO0lBQzNDLFNBQVNhLFlBQVlDLFlBQVk7UUFDL0IsTUFBTVAsVUFBVVYsNkNBQWdCLENBQUNLO1FBQ2pDLElBQUlLLFNBQVMsT0FBT0E7UUFDcEIsSUFBSU4sbUJBQW1CLEtBQUssR0FBRyxPQUFPQTtRQUN0QyxNQUFNLElBQUllLE1BQU0sQ0FBQyxFQUFFLEVBQUVGLGFBQWEseUJBQXlCLEVBQUVkLGtCQUFrQixFQUFFLENBQUM7SUFDcEY7SUFDQSxPQUFPO1FBQUNJO1FBQVVTO0tBQVk7QUFDaEM7QUFDQSxTQUFTSSxtQkFBbUJDLFNBQVMsRUFBRUMseUJBQXlCLEVBQUU7SUFDaEUsSUFBSUMsa0JBQWtCLEVBQUU7SUFDeEIsU0FBU0MsZUFBZXJCLGlCQUFpQixFQUFFQyxjQUFjO1FBQ3ZELE1BQU1xQiw0QkFBY3pCLGdEQUFtQixDQUFDSTtRQUN4QyxNQUFNc0IsUUFBUUgsZ0JBQWdCSSxNQUFNO1FBQ3BDSixrQkFBa0I7ZUFBSUE7WUFBaUJuQjtTQUFlO1FBQ3RELE1BQU1HLFdBQVcsQ0FBQ0M7WUFDaEIsTUFBTSxFQUFFb0IsS0FBSyxFQUFFbkIsUUFBUSxFQUFFLEdBQUdDLFNBQVMsR0FBR0Y7WUFDeEMsTUFBTUgsVUFBVXVCLE9BQU8sQ0FBQ1AsVUFBVSxFQUFFLENBQUNLLE1BQU0sSUFBSUQ7WUFDL0MsTUFBTWQsUUFBUVgsMENBQWEsQ0FBQyxJQUFNVSxTQUFTRyxPQUFPQyxNQUFNLENBQUNKO1lBQ3pELE9BQU8sYUFBYSxHQUFHVCxzREFBR0EsQ0FBQ0ksUUFBUUUsUUFBUSxFQUFFO2dCQUFFSTtnQkFBT0Y7WUFBUztRQUNqRTtRQUNBRixTQUFTUSxXQUFXLEdBQUdaLG9CQUFvQjtRQUMzQyxTQUFTYSxZQUFZQyxZQUFZLEVBQUVXLEtBQUs7WUFDdEMsTUFBTXZCLFVBQVV1QixPQUFPLENBQUNQLFVBQVUsRUFBRSxDQUFDSyxNQUFNLElBQUlEO1lBQy9DLE1BQU1mLFVBQVVWLDZDQUFnQixDQUFDSztZQUNqQyxJQUFJSyxTQUFTLE9BQU9BO1lBQ3BCLElBQUlOLG1CQUFtQixLQUFLLEdBQUcsT0FBT0E7WUFDdEMsTUFBTSxJQUFJZSxNQUFNLENBQUMsRUFBRSxFQUFFRixhQUFhLHlCQUF5QixFQUFFZCxrQkFBa0IsRUFBRSxDQUFDO1FBQ3BGO1FBQ0EsT0FBTztZQUFDSTtZQUFVUztTQUFZO0lBQ2hDO0lBQ0EsTUFBTWEsY0FBYztRQUNsQixNQUFNQyxnQkFBZ0JQLGdCQUFnQlEsR0FBRyxDQUFDLENBQUMzQjtZQUN6QyxxQkFBT0osZ0RBQW1CLENBQUNJO1FBQzdCO1FBQ0EsT0FBTyxTQUFTNEIsU0FBU0osS0FBSztZQUM1QixNQUFNSyxXQUFXTCxPQUFPLENBQUNQLFVBQVUsSUFBSVM7WUFDdkMsT0FBTzlCLDBDQUFhLENBQ2xCLElBQU87b0JBQUUsQ0FBQyxDQUFDLE9BQU8sRUFBRXFCLFVBQVUsQ0FBQyxDQUFDLEVBQUU7d0JBQUUsR0FBR08sS0FBSzt3QkFBRSxDQUFDUCxVQUFVLEVBQUVZO29CQUFTO2dCQUFFLElBQ3RFO2dCQUFDTDtnQkFBT0s7YUFBUztRQUVyQjtJQUNGO0lBQ0FKLFlBQVlSLFNBQVMsR0FBR0E7SUFDeEIsT0FBTztRQUFDRztRQUFnQlUscUJBQXFCTCxnQkFBZ0JQO0tBQXdCO0FBQ3ZGO0FBQ0EsU0FBU1kscUJBQXFCLEdBQUdDLE1BQU07SUFDckMsTUFBTUMsWUFBWUQsTUFBTSxDQUFDLEVBQUU7SUFDM0IsSUFBSUEsT0FBT1IsTUFBTSxLQUFLLEdBQUcsT0FBT1M7SUFDaEMsTUFBTVAsY0FBYztRQUNsQixNQUFNUSxhQUFhRixPQUFPSixHQUFHLENBQUMsQ0FBQ08sZUFBa0I7Z0JBQy9DTixVQUFVTTtnQkFDVmpCLFdBQVdpQixhQUFhakIsU0FBUztZQUNuQztRQUNBLE9BQU8sU0FBU2tCLGtCQUFrQkMsY0FBYztZQUM5QyxNQUFNQyxhQUFhSixXQUFXSyxNQUFNLENBQUMsQ0FBQ0MsYUFBYSxFQUFFWCxRQUFRLEVBQUVYLFNBQVMsRUFBRTtnQkFDeEUsTUFBTXVCLGFBQWFaLFNBQVNRO2dCQUM1QixNQUFNSyxlQUFlRCxVQUFVLENBQUMsQ0FBQyxPQUFPLEVBQUV2QixVQUFVLENBQUMsQ0FBQztnQkFDdEQsT0FBTztvQkFBRSxHQUFHc0IsV0FBVztvQkFBRSxHQUFHRSxZQUFZO2dCQUFDO1lBQzNDLEdBQUcsQ0FBQztZQUNKLE9BQU83QywwQ0FBYSxDQUFDLElBQU87b0JBQUUsQ0FBQyxDQUFDLE9BQU8sRUFBRW9DLFVBQVVmLFNBQVMsQ0FBQyxDQUFDLENBQUMsRUFBRW9CO2dCQUFXLElBQUk7Z0JBQUNBO2FBQVc7UUFDOUY7SUFDRjtJQUNBWixZQUFZUixTQUFTLEdBQUdlLFVBQVVmLFNBQVM7SUFDM0MsT0FBT1E7QUFDVDtBQUlFLENBQ0Ysa0NBQWtDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dGpzLXNhYXMtYXBwLy4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC1jb250ZXh0L2Rpc3QvaW5kZXgubWpzPzEwODEiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvY29udGV4dC9zcmMvY3JlYXRlLWNvbnRleHQudHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IGpzeCB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xuZnVuY3Rpb24gY3JlYXRlQ29udGV4dDIocm9vdENvbXBvbmVudE5hbWUsIGRlZmF1bHRDb250ZXh0KSB7XG4gIGNvbnN0IENvbnRleHQgPSBSZWFjdC5jcmVhdGVDb250ZXh0KGRlZmF1bHRDb250ZXh0KTtcbiAgY29uc3QgUHJvdmlkZXIgPSAocHJvcHMpID0+IHtcbiAgICBjb25zdCB7IGNoaWxkcmVuLCAuLi5jb250ZXh0IH0gPSBwcm9wcztcbiAgICBjb25zdCB2YWx1ZSA9IFJlYWN0LnVzZU1lbW8oKCkgPT4gY29udGV4dCwgT2JqZWN0LnZhbHVlcyhjb250ZXh0KSk7XG4gICAgcmV0dXJuIC8qIEBfX1BVUkVfXyAqLyBqc3goQ29udGV4dC5Qcm92aWRlciwgeyB2YWx1ZSwgY2hpbGRyZW4gfSk7XG4gIH07XG4gIFByb3ZpZGVyLmRpc3BsYXlOYW1lID0gcm9vdENvbXBvbmVudE5hbWUgKyBcIlByb3ZpZGVyXCI7XG4gIGZ1bmN0aW9uIHVzZUNvbnRleHQyKGNvbnN1bWVyTmFtZSkge1xuICAgIGNvbnN0IGNvbnRleHQgPSBSZWFjdC51c2VDb250ZXh0KENvbnRleHQpO1xuICAgIGlmIChjb250ZXh0KSByZXR1cm4gY29udGV4dDtcbiAgICBpZiAoZGVmYXVsdENvbnRleHQgIT09IHZvaWQgMCkgcmV0dXJuIGRlZmF1bHRDb250ZXh0O1xuICAgIHRocm93IG5ldyBFcnJvcihgXFxgJHtjb25zdW1lck5hbWV9XFxgIG11c3QgYmUgdXNlZCB3aXRoaW4gXFxgJHtyb290Q29tcG9uZW50TmFtZX1cXGBgKTtcbiAgfVxuICByZXR1cm4gW1Byb3ZpZGVyLCB1c2VDb250ZXh0Ml07XG59XG5mdW5jdGlvbiBjcmVhdGVDb250ZXh0U2NvcGUoc2NvcGVOYW1lLCBjcmVhdGVDb250ZXh0U2NvcGVEZXBzID0gW10pIHtcbiAgbGV0IGRlZmF1bHRDb250ZXh0cyA9IFtdO1xuICBmdW5jdGlvbiBjcmVhdGVDb250ZXh0Myhyb290Q29tcG9uZW50TmFtZSwgZGVmYXVsdENvbnRleHQpIHtcbiAgICBjb25zdCBCYXNlQ29udGV4dCA9IFJlYWN0LmNyZWF0ZUNvbnRleHQoZGVmYXVsdENvbnRleHQpO1xuICAgIGNvbnN0IGluZGV4ID0gZGVmYXVsdENvbnRleHRzLmxlbmd0aDtcbiAgICBkZWZhdWx0Q29udGV4dHMgPSBbLi4uZGVmYXVsdENvbnRleHRzLCBkZWZhdWx0Q29udGV4dF07XG4gICAgY29uc3QgUHJvdmlkZXIgPSAocHJvcHMpID0+IHtcbiAgICAgIGNvbnN0IHsgc2NvcGUsIGNoaWxkcmVuLCAuLi5jb250ZXh0IH0gPSBwcm9wcztcbiAgICAgIGNvbnN0IENvbnRleHQgPSBzY29wZT8uW3Njb3BlTmFtZV0/LltpbmRleF0gfHwgQmFzZUNvbnRleHQ7XG4gICAgICBjb25zdCB2YWx1ZSA9IFJlYWN0LnVzZU1lbW8oKCkgPT4gY29udGV4dCwgT2JqZWN0LnZhbHVlcyhjb250ZXh0KSk7XG4gICAgICByZXR1cm4gLyogQF9fUFVSRV9fICovIGpzeChDb250ZXh0LlByb3ZpZGVyLCB7IHZhbHVlLCBjaGlsZHJlbiB9KTtcbiAgICB9O1xuICAgIFByb3ZpZGVyLmRpc3BsYXlOYW1lID0gcm9vdENvbXBvbmVudE5hbWUgKyBcIlByb3ZpZGVyXCI7XG4gICAgZnVuY3Rpb24gdXNlQ29udGV4dDIoY29uc3VtZXJOYW1lLCBzY29wZSkge1xuICAgICAgY29uc3QgQ29udGV4dCA9IHNjb3BlPy5bc2NvcGVOYW1lXT8uW2luZGV4XSB8fCBCYXNlQ29udGV4dDtcbiAgICAgIGNvbnN0IGNvbnRleHQgPSBSZWFjdC51c2VDb250ZXh0KENvbnRleHQpO1xuICAgICAgaWYgKGNvbnRleHQpIHJldHVybiBjb250ZXh0O1xuICAgICAgaWYgKGRlZmF1bHRDb250ZXh0ICE9PSB2b2lkIDApIHJldHVybiBkZWZhdWx0Q29udGV4dDtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgXFxgJHtjb25zdW1lck5hbWV9XFxgIG11c3QgYmUgdXNlZCB3aXRoaW4gXFxgJHtyb290Q29tcG9uZW50TmFtZX1cXGBgKTtcbiAgICB9XG4gICAgcmV0dXJuIFtQcm92aWRlciwgdXNlQ29udGV4dDJdO1xuICB9XG4gIGNvbnN0IGNyZWF0ZVNjb3BlID0gKCkgPT4ge1xuICAgIGNvbnN0IHNjb3BlQ29udGV4dHMgPSBkZWZhdWx0Q29udGV4dHMubWFwKChkZWZhdWx0Q29udGV4dCkgPT4ge1xuICAgICAgcmV0dXJuIFJlYWN0LmNyZWF0ZUNvbnRleHQoZGVmYXVsdENvbnRleHQpO1xuICAgIH0pO1xuICAgIHJldHVybiBmdW5jdGlvbiB1c2VTY29wZShzY29wZSkge1xuICAgICAgY29uc3QgY29udGV4dHMgPSBzY29wZT8uW3Njb3BlTmFtZV0gfHwgc2NvcGVDb250ZXh0cztcbiAgICAgIHJldHVybiBSZWFjdC51c2VNZW1vKFxuICAgICAgICAoKSA9PiAoeyBbYF9fc2NvcGUke3Njb3BlTmFtZX1gXTogeyAuLi5zY29wZSwgW3Njb3BlTmFtZV06IGNvbnRleHRzIH0gfSksXG4gICAgICAgIFtzY29wZSwgY29udGV4dHNdXG4gICAgICApO1xuICAgIH07XG4gIH07XG4gIGNyZWF0ZVNjb3BlLnNjb3BlTmFtZSA9IHNjb3BlTmFtZTtcbiAgcmV0dXJuIFtjcmVhdGVDb250ZXh0MywgY29tcG9zZUNvbnRleHRTY29wZXMoY3JlYXRlU2NvcGUsIC4uLmNyZWF0ZUNvbnRleHRTY29wZURlcHMpXTtcbn1cbmZ1bmN0aW9uIGNvbXBvc2VDb250ZXh0U2NvcGVzKC4uLnNjb3Blcykge1xuICBjb25zdCBiYXNlU2NvcGUgPSBzY29wZXNbMF07XG4gIGlmIChzY29wZXMubGVuZ3RoID09PSAxKSByZXR1cm4gYmFzZVNjb3BlO1xuICBjb25zdCBjcmVhdGVTY29wZSA9ICgpID0+IHtcbiAgICBjb25zdCBzY29wZUhvb2tzID0gc2NvcGVzLm1hcCgoY3JlYXRlU2NvcGUyKSA9PiAoe1xuICAgICAgdXNlU2NvcGU6IGNyZWF0ZVNjb3BlMigpLFxuICAgICAgc2NvcGVOYW1lOiBjcmVhdGVTY29wZTIuc2NvcGVOYW1lXG4gICAgfSkpO1xuICAgIHJldHVybiBmdW5jdGlvbiB1c2VDb21wb3NlZFNjb3BlcyhvdmVycmlkZVNjb3Blcykge1xuICAgICAgY29uc3QgbmV4dFNjb3BlcyA9IHNjb3BlSG9va3MucmVkdWNlKChuZXh0U2NvcGVzMiwgeyB1c2VTY29wZSwgc2NvcGVOYW1lIH0pID0+IHtcbiAgICAgICAgY29uc3Qgc2NvcGVQcm9wcyA9IHVzZVNjb3BlKG92ZXJyaWRlU2NvcGVzKTtcbiAgICAgICAgY29uc3QgY3VycmVudFNjb3BlID0gc2NvcGVQcm9wc1tgX19zY29wZSR7c2NvcGVOYW1lfWBdO1xuICAgICAgICByZXR1cm4geyAuLi5uZXh0U2NvcGVzMiwgLi4uY3VycmVudFNjb3BlIH07XG4gICAgICB9LCB7fSk7XG4gICAgICByZXR1cm4gUmVhY3QudXNlTWVtbygoKSA9PiAoeyBbYF9fc2NvcGUke2Jhc2VTY29wZS5zY29wZU5hbWV9YF06IG5leHRTY29wZXMgfSksIFtuZXh0U2NvcGVzXSk7XG4gICAgfTtcbiAgfTtcbiAgY3JlYXRlU2NvcGUuc2NvcGVOYW1lID0gYmFzZVNjb3BlLnNjb3BlTmFtZTtcbiAgcmV0dXJuIGNyZWF0ZVNjb3BlO1xufVxuZXhwb3J0IHtcbiAgY3JlYXRlQ29udGV4dDIgYXMgY3JlYXRlQ29udGV4dCxcbiAgY3JlYXRlQ29udGV4dFNjb3BlXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbIlJlYWN0IiwianN4IiwiY3JlYXRlQ29udGV4dDIiLCJyb290Q29tcG9uZW50TmFtZSIsImRlZmF1bHRDb250ZXh0IiwiQ29udGV4dCIsImNyZWF0ZUNvbnRleHQiLCJQcm92aWRlciIsInByb3BzIiwiY2hpbGRyZW4iLCJjb250ZXh0IiwidmFsdWUiLCJ1c2VNZW1vIiwiT2JqZWN0IiwidmFsdWVzIiwiZGlzcGxheU5hbWUiLCJ1c2VDb250ZXh0MiIsImNvbnN1bWVyTmFtZSIsInVzZUNvbnRleHQiLCJFcnJvciIsImNyZWF0ZUNvbnRleHRTY29wZSIsInNjb3BlTmFtZSIsImNyZWF0ZUNvbnRleHRTY29wZURlcHMiLCJkZWZhdWx0Q29udGV4dHMiLCJjcmVhdGVDb250ZXh0MyIsIkJhc2VDb250ZXh0IiwiaW5kZXgiLCJsZW5ndGgiLCJzY29wZSIsImNyZWF0ZVNjb3BlIiwic2NvcGVDb250ZXh0cyIsIm1hcCIsInVzZVNjb3BlIiwiY29udGV4dHMiLCJjb21wb3NlQ29udGV4dFNjb3BlcyIsInNjb3BlcyIsImJhc2VTY29wZSIsInNjb3BlSG9va3MiLCJjcmVhdGVTY29wZTIiLCJ1c2VDb21wb3NlZFNjb3BlcyIsIm92ZXJyaWRlU2NvcGVzIiwibmV4dFNjb3BlcyIsInJlZHVjZSIsIm5leHRTY29wZXMyIiwic2NvcGVQcm9wcyIsImN1cnJlbnRTY29wZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@radix-ui/react-primitive/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Primitive: () => (/* binding */ Primitive),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   dispatchDiscreteCustomEvent: () => (/* binding */ dispatchDiscreteCustomEvent)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/primitive.tsx\n\n\n\n\nvar NODES = [\n    \"a\",\n    \"button\",\n    \"div\",\n    \"form\",\n    \"h2\",\n    \"h3\",\n    \"img\",\n    \"input\",\n    \"label\",\n    \"li\",\n    \"nav\",\n    \"ol\",\n    \"p\",\n    \"select\",\n    \"span\",\n    \"svg\",\n    \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node)=>{\n    const Slot = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(`Primitive.${node}`);\n    const Node = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { asChild, ...primitiveProps } = props;\n        const Comp = asChild ? Slot : node;\n        if (false) {}\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Comp, {\n            ...primitiveProps,\n            ref: forwardedRef\n        });\n    });\n    Node.displayName = `Primitive.${node}`;\n    return {\n        ...primitive,\n        [node]: Node\n    };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n    if (target) react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(()=>target.dispatchEvent(event));\n}\nvar Root = Primitive;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@radix-ui/react-slot/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Slot),\n/* harmony export */   Slot: () => (/* binding */ Slot),\n/* harmony export */   Slottable: () => (/* binding */ Slottable),\n/* harmony export */   createSlot: () => (/* binding */ createSlot),\n/* harmony export */   createSlottable: () => (/* binding */ createSlottable)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/slot.tsx\n\n\n\n// @__NO_SIDE_EFFECTS__\nfunction createSlot(ownerName) {\n    const SlotClone = /* @__PURE__ */ createSlotClone(ownerName);\n    const Slot2 = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { children, ...slotProps } = props;\n        const childrenArray = react__WEBPACK_IMPORTED_MODULE_0__.Children.toArray(children);\n        const slottable = childrenArray.find(isSlottable);\n        if (slottable) {\n            const newElement = slottable.props.children;\n            const newChildren = childrenArray.map((child)=>{\n                if (child === slottable) {\n                    if (react__WEBPACK_IMPORTED_MODULE_0__.Children.count(newElement) > 1) return react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null);\n                    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? newElement.props.children : null;\n                } else {\n                    return child;\n                }\n            });\n            return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, {\n                ...slotProps,\n                ref: forwardedRef,\n                children: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(newElement, void 0, newChildren) : null\n            });\n        }\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, {\n            ...slotProps,\n            ref: forwardedRef,\n            children\n        });\n    });\n    Slot2.displayName = `${ownerName}.Slot`;\n    return Slot2;\n}\nvar Slot = /* @__PURE__ */ createSlot(\"Slot\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlotClone(ownerName) {\n    const SlotClone = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { children, ...slotProps } = props;\n        if (/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(children)) {\n            const childrenRef = getElementRef(children);\n            const props2 = mergeProps(slotProps, children.props);\n            if (children.type !== react__WEBPACK_IMPORTED_MODULE_0__.Fragment) {\n                props2.ref = forwardedRef ? (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.composeRefs)(forwardedRef, childrenRef) : childrenRef;\n            }\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(children, props2);\n        }\n        return react__WEBPACK_IMPORTED_MODULE_0__.Children.count(children) > 1 ? react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null) : null;\n    });\n    SlotClone.displayName = `${ownerName}.SlotClone`;\n    return SlotClone;\n}\nvar SLOTTABLE_IDENTIFIER = Symbol(\"radix.slottable\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlottable(ownerName) {\n    const Slottable2 = ({ children })=>{\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n            children\n        });\n    };\n    Slottable2.displayName = `${ownerName}.Slottable`;\n    Slottable2.__radixId = SLOTTABLE_IDENTIFIER;\n    return Slottable2;\n}\nvar Slottable = /* @__PURE__ */ createSlottable(\"Slottable\");\nfunction isSlottable(child) {\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(child) && typeof child.type === \"function\" && \"__radixId\" in child.type && child.type.__radixId === SLOTTABLE_IDENTIFIER;\n}\nfunction mergeProps(slotProps, childProps) {\n    const overrideProps = {\n        ...childProps\n    };\n    for(const propName in childProps){\n        const slotPropValue = slotProps[propName];\n        const childPropValue = childProps[propName];\n        const isHandler = /^on[A-Z]/.test(propName);\n        if (isHandler) {\n            if (slotPropValue && childPropValue) {\n                overrideProps[propName] = (...args)=>{\n                    const result = childPropValue(...args);\n                    slotPropValue(...args);\n                    return result;\n                };\n            } else if (slotPropValue) {\n                overrideProps[propName] = slotPropValue;\n            }\n        } else if (propName === \"style\") {\n            overrideProps[propName] = {\n                ...slotPropValue,\n                ...childPropValue\n            };\n        } else if (propName === \"className\") {\n            overrideProps[propName] = [\n                slotPropValue,\n                childPropValue\n            ].filter(Boolean).join(\" \");\n        }\n    }\n    return {\n        ...slotProps,\n        ...overrideProps\n    };\n}\nfunction getElementRef(element) {\n    let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n    let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.ref;\n    }\n    getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n    mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.props.ref;\n    }\n    return element.props.ref || element.ref;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-switch/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@radix-ui/react-switch/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   Switch: () => (/* binding */ Switch),\n/* harmony export */   SwitchThumb: () => (/* binding */ SwitchThumb),\n/* harmony export */   Thumb: () => (/* binding */ Thumb),\n/* harmony export */   createSwitchScope: () => (/* binding */ createSwitchScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-use-previous */ \"(ssr)/./node_modules/@radix-ui/react-use-previous/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-size */ \"(ssr)/./node_modules/@radix-ui/react-use-size/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Root,Switch,SwitchThumb,Thumb,createSwitchScope auto */ // src/switch.tsx\n\n\n\n\n\n\n\n\n\nvar SWITCH_NAME = \"Switch\";\nvar [createSwitchContext, createSwitchScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(SWITCH_NAME);\nvar [SwitchProvider, useSwitchContext] = createSwitchContext(SWITCH_NAME);\nvar Switch = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSwitch, name, checked: checkedProp, defaultChecked, required, disabled, value = \"on\", onCheckedChange, form, ...switchProps } = props;\n    const [button, setButton] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node)=>setButton(node));\n    const hasConsumerStoppedPropagationRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const isFormControl = button ? form || !!button.closest(\"form\") : true;\n    const [checked, setChecked] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__.useControllableState)({\n        prop: checkedProp,\n        defaultProp: defaultChecked ?? false,\n        onChange: onCheckedChange,\n        caller: SWITCH_NAME\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(SwitchProvider, {\n        scope: __scopeSwitch,\n        checked,\n        disabled,\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.button, {\n                type: \"button\",\n                role: \"switch\",\n                \"aria-checked\": checked,\n                \"aria-required\": required,\n                \"data-state\": getState(checked),\n                \"data-disabled\": disabled ? \"\" : void 0,\n                disabled,\n                value,\n                ...switchProps,\n                ref: composedRefs,\n                onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(props.onClick, (event)=>{\n                    setChecked((prevChecked)=>!prevChecked);\n                    if (isFormControl) {\n                        hasConsumerStoppedPropagationRef.current = event.isPropagationStopped();\n                        if (!hasConsumerStoppedPropagationRef.current) event.stopPropagation();\n                    }\n                })\n            }),\n            isFormControl && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SwitchBubbleInput, {\n                control: button,\n                bubbles: !hasConsumerStoppedPropagationRef.current,\n                name,\n                value,\n                checked,\n                required,\n                disabled,\n                form,\n                style: {\n                    transform: \"translateX(-100%)\"\n                }\n            })\n        ]\n    });\n});\nSwitch.displayName = SWITCH_NAME;\nvar THUMB_NAME = \"SwitchThumb\";\nvar SwitchThumb = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSwitch, ...thumbProps } = props;\n    const context = useSwitchContext(THUMB_NAME, __scopeSwitch);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.span, {\n        \"data-state\": getState(context.checked),\n        \"data-disabled\": context.disabled ? \"\" : void 0,\n        ...thumbProps,\n        ref: forwardedRef\n    });\n});\nSwitchThumb.displayName = THUMB_NAME;\nvar BUBBLE_INPUT_NAME = \"SwitchBubbleInput\";\nvar SwitchBubbleInput = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(({ __scopeSwitch, control, checked, bubbles = true, ...props }, forwardedRef)=>{\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(ref, forwardedRef);\n    const prevChecked = (0,_radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_7__.usePrevious)(checked);\n    const controlSize = (0,_radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_8__.useSize)(control);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const input = ref.current;\n        if (!input) return;\n        const inputProto = window.HTMLInputElement.prototype;\n        const descriptor = Object.getOwnPropertyDescriptor(inputProto, \"checked\");\n        const setChecked = descriptor.set;\n        if (prevChecked !== checked && setChecked) {\n            const event = new Event(\"click\", {\n                bubbles\n            });\n            setChecked.call(input, checked);\n            input.dispatchEvent(event);\n        }\n    }, [\n        prevChecked,\n        checked,\n        bubbles\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"input\", {\n        type: \"checkbox\",\n        \"aria-hidden\": true,\n        defaultChecked: checked,\n        ...props,\n        tabIndex: -1,\n        ref: composedRefs,\n        style: {\n            ...props.style,\n            ...controlSize,\n            position: \"absolute\",\n            pointerEvents: \"none\",\n            opacity: 0,\n            margin: 0\n        }\n    });\n});\nSwitchBubbleInput.displayName = BUBBLE_INPUT_NAME;\nfunction getState(checked) {\n    return checked ? \"checked\" : \"unchecked\";\n}\nvar Root = Switch;\nvar Thumb = SwitchThumb;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-switch/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useControllableState: () => (/* binding */ useControllableState),\n/* harmony export */   useControllableStateReducer: () => (/* binding */ useControllableStateReducer)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_effect_event__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-effect-event */ \"(ssr)/./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs\");\n// src/use-controllable-state.tsx\n\n\nvar useInsertionEffect = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useInsertionEffect \".trim().toString()] || _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect;\nfunction useControllableState({ prop, defaultProp, onChange = ()=>{}, caller }) {\n    const [uncontrolledProp, setUncontrolledProp, onChangeRef] = useUncontrolledState({\n        defaultProp,\n        onChange\n    });\n    const isControlled = prop !== void 0;\n    const value = isControlled ? prop : uncontrolledProp;\n    if (true) {\n        const isControlledRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(prop !== void 0);\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            const wasControlled = isControlledRef.current;\n            if (wasControlled !== isControlled) {\n                const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n                const to = isControlled ? \"controlled\" : \"uncontrolled\";\n                console.warn(`${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`);\n            }\n            isControlledRef.current = isControlled;\n        }, [\n            isControlled,\n            caller\n        ]);\n    }\n    const setValue = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((nextValue)=>{\n        if (isControlled) {\n            const value2 = isFunction(nextValue) ? nextValue(prop) : nextValue;\n            if (value2 !== prop) {\n                onChangeRef.current?.(value2);\n            }\n        } else {\n            setUncontrolledProp(nextValue);\n        }\n    }, [\n        isControlled,\n        prop,\n        setUncontrolledProp,\n        onChangeRef\n    ]);\n    return [\n        value,\n        setValue\n    ];\n}\nfunction useUncontrolledState({ defaultProp, onChange }) {\n    const [value, setValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(defaultProp);\n    const prevValueRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(value);\n    const onChangeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(onChange);\n    useInsertionEffect(()=>{\n        onChangeRef.current = onChange;\n    }, [\n        onChange\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (prevValueRef.current !== value) {\n            onChangeRef.current?.(value);\n            prevValueRef.current = value;\n        }\n    }, [\n        value,\n        prevValueRef\n    ]);\n    return [\n        value,\n        setValue,\n        onChangeRef\n    ];\n}\nfunction isFunction(value) {\n    return typeof value === \"function\";\n}\n// src/use-controllable-state-reducer.tsx\n\n\nvar SYNC_STATE = Symbol(\"RADIX:SYNC_STATE\");\nfunction useControllableStateReducer(reducer, userArgs, initialArg, init) {\n    const { prop: controlledState, defaultProp, onChange: onChangeProp, caller } = userArgs;\n    const isControlled = controlledState !== void 0;\n    const onChange = (0,_radix_ui_react_use_effect_event__WEBPACK_IMPORTED_MODULE_2__.useEffectEvent)(onChangeProp);\n    if (true) {\n        const isControlledRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(controlledState !== void 0);\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            const wasControlled = isControlledRef.current;\n            if (wasControlled !== isControlled) {\n                const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n                const to = isControlled ? \"controlled\" : \"uncontrolled\";\n                console.warn(`${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`);\n            }\n            isControlledRef.current = isControlled;\n        }, [\n            isControlled,\n            caller\n        ]);\n    }\n    const args = [\n        {\n            ...initialArg,\n            state: defaultProp\n        }\n    ];\n    if (init) {\n        args.push(init);\n    }\n    const [internalState, dispatch] = react__WEBPACK_IMPORTED_MODULE_0__.useReducer((state2, action)=>{\n        if (action.type === SYNC_STATE) {\n            return {\n                ...state2,\n                state: action.state\n            };\n        }\n        const next = reducer(state2, action);\n        if (isControlled && !Object.is(next.state, state2.state)) {\n            onChange(next.state);\n        }\n        return next;\n    }, ...args);\n    const uncontrolledState = internalState.state;\n    const prevValueRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(uncontrolledState);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (prevValueRef.current !== uncontrolledState) {\n            prevValueRef.current = uncontrolledState;\n            if (!isControlled) {\n                onChange(uncontrolledState);\n            }\n        }\n    }, [\n        onChange,\n        uncontrolledState,\n        prevValueRef,\n        isControlled\n    ]);\n    const state = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        const isControlled2 = controlledState !== void 0;\n        if (isControlled2) {\n            return {\n                ...internalState,\n                state: controlledState\n            };\n        }\n        return internalState;\n    }, [\n        internalState,\n        controlledState\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (isControlled && !Object.is(controlledState, internalState.state)) {\n            dispatch({\n                type: SYNC_STATE,\n                state: controlledState\n            });\n        }\n    }, [\n        controlledState,\n        internalState.state,\n        isControlled\n    ]);\n    return [\n        state,\n        dispatch\n    ];\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEffectEvent: () => (/* binding */ useEffectEvent)\n/* harmony export */ });\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// src/use-effect-event.tsx\n\n\nvar useReactEffectEvent = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useEffectEvent \".trim().toString()];\nvar useReactInsertionEffect = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useInsertionEffect \".trim().toString()];\nfunction useEffectEvent(callback) {\n    if (typeof useReactEffectEvent === \"function\") {\n        return useReactEffectEvent(callback);\n    }\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(()=>{\n        throw new Error(\"Cannot call an event handler while rendering.\");\n    });\n    if (typeof useReactInsertionEffect === \"function\") {\n        useReactInsertionEffect(()=>{\n            ref.current = callback;\n        });\n    } else {\n        (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(()=>{\n            ref.current = callback;\n        });\n    }\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>(...args)=>ref.current?.(...args), []);\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1lZmZlY3QtZXZlbnQvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBLDJCQUEyQjtBQUN5QztBQUNyQztBQUMvQixJQUFJRSxzQkFBc0JELHlMQUFLLENBQUMsbUJBQW1CRSxJQUFJLEdBQUdDLFFBQVEsR0FBRztBQUNyRSxJQUFJQywwQkFBMEJKLHlMQUFLLENBQUMsdUJBQXVCRSxJQUFJLEdBQUdDLFFBQVEsR0FBRztBQUM3RSxTQUFTRSxlQUFlQyxRQUFRO0lBQzlCLElBQUksT0FBT0wsd0JBQXdCLFlBQVk7UUFDN0MsT0FBT0Esb0JBQW9CSztJQUM3QjtJQUNBLE1BQU1DLE1BQU1QLHlDQUFZLENBQUM7UUFDdkIsTUFBTSxJQUFJUyxNQUFNO0lBQ2xCO0lBQ0EsSUFBSSxPQUFPTCw0QkFBNEIsWUFBWTtRQUNqREEsd0JBQXdCO1lBQ3RCRyxJQUFJRyxPQUFPLEdBQUdKO1FBQ2hCO0lBQ0YsT0FBTztRQUNMUCxrRkFBZUEsQ0FBQztZQUNkUSxJQUFJRyxPQUFPLEdBQUdKO1FBQ2hCO0lBQ0Y7SUFDQSxPQUFPTiwwQ0FBYSxDQUFDLElBQU0sQ0FBQyxHQUFHWSxPQUFTTCxJQUFJRyxPQUFPLE1BQU1FLE9BQU8sRUFBRTtBQUNwRTtBQUdFLENBQ0Ysa0NBQWtDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dGpzLXNhYXMtYXBwLy4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC11c2UtZWZmZWN0LWV2ZW50L2Rpc3QvaW5kZXgubWpzPzg4NTciXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL3VzZS1lZmZlY3QtZXZlbnQudHN4XG5pbXBvcnQgeyB1c2VMYXlvdXRFZmZlY3QgfSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LXVzZS1sYXlvdXQtZWZmZWN0XCI7XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbnZhciB1c2VSZWFjdEVmZmVjdEV2ZW50ID0gUmVhY3RbXCIgdXNlRWZmZWN0RXZlbnQgXCIudHJpbSgpLnRvU3RyaW5nKCldO1xudmFyIHVzZVJlYWN0SW5zZXJ0aW9uRWZmZWN0ID0gUmVhY3RbXCIgdXNlSW5zZXJ0aW9uRWZmZWN0IFwiLnRyaW0oKS50b1N0cmluZygpXTtcbmZ1bmN0aW9uIHVzZUVmZmVjdEV2ZW50KGNhbGxiYWNrKSB7XG4gIGlmICh0eXBlb2YgdXNlUmVhY3RFZmZlY3RFdmVudCA9PT0gXCJmdW5jdGlvblwiKSB7XG4gICAgcmV0dXJuIHVzZVJlYWN0RWZmZWN0RXZlbnQoY2FsbGJhY2spO1xuICB9XG4gIGNvbnN0IHJlZiA9IFJlYWN0LnVzZVJlZigoKSA9PiB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFwiQ2Fubm90IGNhbGwgYW4gZXZlbnQgaGFuZGxlciB3aGlsZSByZW5kZXJpbmcuXCIpO1xuICB9KTtcbiAgaWYgKHR5cGVvZiB1c2VSZWFjdEluc2VydGlvbkVmZmVjdCA9PT0gXCJmdW5jdGlvblwiKSB7XG4gICAgdXNlUmVhY3RJbnNlcnRpb25FZmZlY3QoKCkgPT4ge1xuICAgICAgcmVmLmN1cnJlbnQgPSBjYWxsYmFjaztcbiAgICB9KTtcbiAgfSBlbHNlIHtcbiAgICB1c2VMYXlvdXRFZmZlY3QoKCkgPT4ge1xuICAgICAgcmVmLmN1cnJlbnQgPSBjYWxsYmFjaztcbiAgICB9KTtcbiAgfVxuICByZXR1cm4gUmVhY3QudXNlTWVtbygoKSA9PiAoLi4uYXJncykgPT4gcmVmLmN1cnJlbnQ/LiguLi5hcmdzKSwgW10pO1xufVxuZXhwb3J0IHtcbiAgdXNlRWZmZWN0RXZlbnRcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOlsidXNlTGF5b3V0RWZmZWN0IiwiUmVhY3QiLCJ1c2VSZWFjdEVmZmVjdEV2ZW50IiwidHJpbSIsInRvU3RyaW5nIiwidXNlUmVhY3RJbnNlcnRpb25FZmZlY3QiLCJ1c2VFZmZlY3RFdmVudCIsImNhbGxiYWNrIiwicmVmIiwidXNlUmVmIiwiRXJyb3IiLCJjdXJyZW50IiwidXNlTWVtbyIsImFyZ3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLayoutEffect: () => (/* binding */ useLayoutEffect2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-layout-effect/src/use-layout-effect.tsx\n\nvar useLayoutEffect2 = globalThis?.document ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : ()=>{};\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1sYXlvdXQtZWZmZWN0L2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUEsNkRBQTZEO0FBQzlCO0FBQy9CLElBQUlDLG1CQUFtQkMsWUFBWUMsV0FBV0gsa0RBQXFCLEdBQUcsS0FDdEU7QUFHRSxDQUNGLGtDQUFrQyIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zYWFzLWFwcC8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtdXNlLWxheW91dC1lZmZlY3QvZGlzdC9pbmRleC5tanM/MmQ2ZiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC91c2UtbGF5b3V0LWVmZmVjdC9zcmMvdXNlLWxheW91dC1lZmZlY3QudHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbnZhciB1c2VMYXlvdXRFZmZlY3QyID0gZ2xvYmFsVGhpcz8uZG9jdW1lbnQgPyBSZWFjdC51c2VMYXlvdXRFZmZlY3QgOiAoKSA9PiB7XG59O1xuZXhwb3J0IHtcbiAgdXNlTGF5b3V0RWZmZWN0MiBhcyB1c2VMYXlvdXRFZmZlY3Rcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VMYXlvdXRFZmZlY3QyIiwiZ2xvYmFsVGhpcyIsImRvY3VtZW50IiwidXNlTGF5b3V0RWZmZWN0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-previous/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-previous/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   usePrevious: () => (/* binding */ usePrevious)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-previous/src/use-previous.tsx\n\nfunction usePrevious(value) {\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n        value,\n        previous: value\n    });\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        if (ref.current.value !== value) {\n            ref.current.previous = ref.current.value;\n            ref.current.value = value;\n        }\n        return ref.current.previous;\n    }, [\n        value\n    ]);\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1wcmV2aW91cy9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBLG1EQUFtRDtBQUNwQjtBQUMvQixTQUFTQyxZQUFZQyxLQUFLO0lBQ3hCLE1BQU1DLE1BQU1ILHlDQUFZLENBQUM7UUFBRUU7UUFBT0csVUFBVUg7SUFBTTtJQUNsRCxPQUFPRiwwQ0FBYSxDQUFDO1FBQ25CLElBQUlHLElBQUlJLE9BQU8sQ0FBQ0wsS0FBSyxLQUFLQSxPQUFPO1lBQy9CQyxJQUFJSSxPQUFPLENBQUNGLFFBQVEsR0FBR0YsSUFBSUksT0FBTyxDQUFDTCxLQUFLO1lBQ3hDQyxJQUFJSSxPQUFPLENBQUNMLEtBQUssR0FBR0E7UUFDdEI7UUFDQSxPQUFPQyxJQUFJSSxPQUFPLENBQUNGLFFBQVE7SUFDN0IsR0FBRztRQUFDSDtLQUFNO0FBQ1o7QUFHRSxDQUNGLGtDQUFrQyIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zYWFzLWFwcC8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtdXNlLXByZXZpb3VzL2Rpc3QvaW5kZXgubWpzP2EwYWYiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvdXNlLXByZXZpb3VzL3NyYy91c2UtcHJldmlvdXMudHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmZ1bmN0aW9uIHVzZVByZXZpb3VzKHZhbHVlKSB7XG4gIGNvbnN0IHJlZiA9IFJlYWN0LnVzZVJlZih7IHZhbHVlLCBwcmV2aW91czogdmFsdWUgfSk7XG4gIHJldHVybiBSZWFjdC51c2VNZW1vKCgpID0+IHtcbiAgICBpZiAocmVmLmN1cnJlbnQudmFsdWUgIT09IHZhbHVlKSB7XG4gICAgICByZWYuY3VycmVudC5wcmV2aW91cyA9IHJlZi5jdXJyZW50LnZhbHVlO1xuICAgICAgcmVmLmN1cnJlbnQudmFsdWUgPSB2YWx1ZTtcbiAgICB9XG4gICAgcmV0dXJuIHJlZi5jdXJyZW50LnByZXZpb3VzO1xuICB9LCBbdmFsdWVdKTtcbn1cbmV4cG9ydCB7XG4gIHVzZVByZXZpb3VzXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlUHJldmlvdXMiLCJ2YWx1ZSIsInJlZiIsInVzZVJlZiIsInByZXZpb3VzIiwidXNlTWVtbyIsImN1cnJlbnQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-previous/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-size/dist/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-size/dist/index.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSize: () => (/* binding */ useSize)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n// packages/react/use-size/src/use-size.tsx\n\n\nfunction useSize(element) {\n    const [size, setSize] = react__WEBPACK_IMPORTED_MODULE_0__.useState(void 0);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(()=>{\n        if (element) {\n            setSize({\n                width: element.offsetWidth,\n                height: element.offsetHeight\n            });\n            const resizeObserver = new ResizeObserver((entries)=>{\n                if (!Array.isArray(entries)) {\n                    return;\n                }\n                if (!entries.length) {\n                    return;\n                }\n                const entry = entries[0];\n                let width;\n                let height;\n                if (\"borderBoxSize\" in entry) {\n                    const borderSizeEntry = entry[\"borderBoxSize\"];\n                    const borderSize = Array.isArray(borderSizeEntry) ? borderSizeEntry[0] : borderSizeEntry;\n                    width = borderSize[\"inlineSize\"];\n                    height = borderSize[\"blockSize\"];\n                } else {\n                    width = element.offsetWidth;\n                    height = element.offsetHeight;\n                }\n                setSize({\n                    width,\n                    height\n                });\n            });\n            resizeObserver.observe(element, {\n                box: \"border-box\"\n            });\n            return ()=>resizeObserver.unobserve(element);\n        } else {\n            setSize(void 0);\n        }\n    }, [\n        element\n    ]);\n    return size;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-size/dist/index.mjs\n");

/***/ })

};
;