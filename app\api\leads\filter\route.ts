import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Validation schema for lead filtering
const filterSchema = z.object({
  // Basic filters
  search: z.string().optional(),
  status: z.array(z.string()).optional(),
  priority: z.array(z.string()).optional(),
  source: z.array(z.string()).optional(),
  industry: z.array(z.string()).optional(),
  companySize: z.array(z.string()).optional(),
  
  // Score filters
  scoreMin: z.number().min(0).max(100).optional(),
  scoreMax: z.number().min(0).max(100).optional(),
  temperature: z.array(z.enum(['HOT', 'WARM', 'COLD'])).optional(),
  
  // Date filters
  createdAfter: z.string().optional(),
  createdBefore: z.string().optional(),
  lastActivityAfter: z.string().optional(),
  lastActivityBefore: z.string().optional(),
  convertedAfter: z.string().optional(),
  convertedBefore: z.string().optional(),
  
  // Budget filters
  budgetMin: z.number().optional(),
  budgetMax: z.number().optional(),
  hasBudget: z.boolean().optional(),
  
  // Activity filters
  hasActivities: z.boolean().optional(),
  activityCountMin: z.number().optional(),
  activityCountMax: z.number().optional(),
  hasRecentActivity: z.boolean().optional(), // within last 7 days
  
  // Assignment filters
  assignedTo: z.array(z.string()).optional(),
  unassigned: z.boolean().optional(),
  
  // Qualification filters
  isQualified: z.boolean().optional(),
  hasPhone: z.boolean().optional(),
  hasEmail: z.boolean().optional(),
  hasWebsite: z.boolean().optional(),
  hasTimeline: z.boolean().optional(),
  
  // Conversion filters
  isConverted: z.boolean().optional(),
  conversionType: z.array(z.string()).optional(),
  
  // Pagination and sorting
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
  sortBy: z.enum(['createdAt', 'updatedAt', 'score', 'lastName', 'companyName', 'lastActivity']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  
  // Export options
  export: z.boolean().default(false),
  exportFormat: z.enum(['csv', 'xlsx']).default('csv')
})

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const filters = filterSchema.parse(body)

    // Build where clause
    const where: any = {
      companyId: session.user.companyId
    }

    // Text search across multiple fields
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase()
      where.OR = [
        { firstName: { contains: searchTerm, mode: 'insensitive' } },
        { lastName: { contains: searchTerm, mode: 'insensitive' } },
        { email: { contains: searchTerm, mode: 'insensitive' } },
        { phone: { contains: searchTerm, mode: 'insensitive' } },
        { companyName: { contains: searchTerm, mode: 'insensitive' } },
        { title: { contains: searchTerm, mode: 'insensitive' } },
        { website: { contains: searchTerm, mode: 'insensitive' } }
      ]
    }

    // Status filters
    if (filters.status && filters.status.length > 0) {
      where.status = { in: filters.status }
    }

    if (filters.priority && filters.priority.length > 0) {
      where.priority = { in: filters.priority }
    }

    if (filters.source && filters.source.length > 0) {
      where.source = { in: filters.source }
    }

    if (filters.industry && filters.industry.length > 0) {
      where.industry = { in: filters.industry }
    }

    if (filters.companySize && filters.companySize.length > 0) {
      where.companySize = { in: filters.companySize }
    }

    // Score filters
    if (filters.scoreMin !== undefined || filters.scoreMax !== undefined) {
      where.score = {}
      if (filters.scoreMin !== undefined) where.score.gte = filters.scoreMin
      if (filters.scoreMax !== undefined) where.score.lte = filters.scoreMax
    }

    // Temperature filters (based on score ranges)
    if (filters.temperature && filters.temperature.length > 0) {
      const tempConditions = []
      if (filters.temperature.includes('HOT')) {
        tempConditions.push({ score: { gte: 70 } })
      }
      if (filters.temperature.includes('WARM')) {
        tempConditions.push({ score: { gte: 40, lt: 70 } })
      }
      if (filters.temperature.includes('COLD')) {
        tempConditions.push({ score: { lt: 40 } })
      }
      if (tempConditions.length > 0) {
        where.OR = where.OR ? [...where.OR, ...tempConditions] : tempConditions
      }
    }

    // Date filters
    if (filters.createdAfter || filters.createdBefore) {
      where.createdAt = {}
      if (filters.createdAfter) where.createdAt.gte = new Date(filters.createdAfter)
      if (filters.createdBefore) where.createdAt.lte = new Date(filters.createdBefore)
    }

    if (filters.convertedAfter || filters.convertedBefore) {
      where.convertedAt = {}
      if (filters.convertedAfter) where.convertedAt.gte = new Date(filters.convertedAfter)
      if (filters.convertedBefore) where.convertedAt.lte = new Date(filters.convertedBefore)
    }

    // Budget filters
    if (filters.budgetMin !== undefined || filters.budgetMax !== undefined) {
      where.budget = {}
      if (filters.budgetMin !== undefined) where.budget.gte = filters.budgetMin
      if (filters.budgetMax !== undefined) where.budget.lte = filters.budgetMax
    }

    if (filters.hasBudget !== undefined) {
      if (filters.hasBudget) {
        where.budget = { gt: 0 }
      } else {
        where.OR = [
          { budget: null },
          { budget: 0 }
        ]
      }
    }

    // Assignment filters
    if (filters.assignedTo && filters.assignedTo.length > 0) {
      where.assignedToId = { in: filters.assignedTo }
    }

    if (filters.unassigned) {
      where.assignedToId = null
    }

    // Qualification filters
    if (filters.hasPhone !== undefined) {
      if (filters.hasPhone) {
        where.phone = { not: null }
      } else {
        where.phone = null
      }
    }

    if (filters.hasEmail !== undefined) {
      if (filters.hasEmail) {
        where.email = { not: null }
      } else {
        where.email = null
      }
    }

    if (filters.hasWebsite !== undefined) {
      if (filters.hasWebsite) {
        where.website = { not: null }
      } else {
        where.website = null
      }
    }

    if (filters.hasTimeline !== undefined) {
      if (filters.hasTimeline) {
        where.timeline = { not: null }
      } else {
        where.timeline = null
      }
    }

    // Conversion filters
    if (filters.isConverted !== undefined) {
      if (filters.isConverted) {
        where.status = 'CONVERTED'
      } else {
        where.status = { not: 'CONVERTED' }
      }
    }

    if (filters.conversionType && filters.conversionType.length > 0) {
      where.conversion = {
        conversionType: { in: filters.conversionType }
      }
    }

    // Activity-based filters (requires subqueries)
    if (filters.hasActivities !== undefined || 
        filters.activityCountMin !== undefined || 
        filters.activityCountMax !== undefined ||
        filters.hasRecentActivity !== undefined ||
        filters.lastActivityAfter || 
        filters.lastActivityBefore) {
      
      // We'll handle these with include and post-processing
    }

    // Build sort order
    const orderBy: any = {}
    if (filters.sortBy === 'lastActivity') {
      // Special handling for last activity sorting
      orderBy.activities = {
        _count: filters.sortOrder
      }
    } else {
      orderBy[filters.sortBy] = filters.sortOrder
    }

    // Execute query
    const [leads, total] = await Promise.all([
      prisma.lead.findMany({
        where,
        include: {
          assignedTo: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          activities: {
            select: {
              id: true,
              createdAt: true
            },
            orderBy: {
              createdAt: 'desc'
            },
            take: 1
          },
          conversion: {
            select: {
              id: true,
              conversionType: true,
              conversionValue: true,
              createdAt: true
            }
          },
          _count: {
            select: {
              activities: true,
              leadNotes: true
            }
          }
        },
        orderBy,
        skip: filters.export ? 0 : (filters.page - 1) * filters.limit,
        take: filters.export ? undefined : filters.limit
      }),
      prisma.lead.count({ where })
    ])

    // Post-process for activity-based filters
    let filteredLeads = leads

    if (filters.hasActivities !== undefined) {
      filteredLeads = filteredLeads.filter(lead => 
        filters.hasActivities ? lead._count.activities > 0 : lead._count.activities === 0
      )
    }

    if (filters.activityCountMin !== undefined) {
      filteredLeads = filteredLeads.filter(lead => lead._count.activities >= filters.activityCountMin!)
    }

    if (filters.activityCountMax !== undefined) {
      filteredLeads = filteredLeads.filter(lead => lead._count.activities <= filters.activityCountMax!)
    }

    if (filters.hasRecentActivity !== undefined) {
      const sevenDaysAgo = new Date()
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
      
      filteredLeads = filteredLeads.filter(lead => {
        const hasRecent = lead.activities.length > 0 && 
          new Date(lead.activities[0].createdAt) >= sevenDaysAgo
        return filters.hasRecentActivity ? hasRecent : !hasRecent
      })
    }

    if (filters.lastActivityAfter) {
      const afterDate = new Date(filters.lastActivityAfter)
      filteredLeads = filteredLeads.filter(lead => 
        lead.activities.length > 0 && new Date(lead.activities[0].createdAt) >= afterDate
      )
    }

    if (filters.lastActivityBefore) {
      const beforeDate = new Date(filters.lastActivityBefore)
      filteredLeads = filteredLeads.filter(lead => 
        lead.activities.length > 0 && new Date(lead.activities[0].createdAt) <= beforeDate
      )
    }

    // Handle export
    if (filters.export) {
      return handleExport(filteredLeads, filters.exportFormat)
    }

    // Calculate filter summary
    const filterSummary = {
      totalResults: filteredLeads.length,
      statusBreakdown: getStatusBreakdown(filteredLeads),
      temperatureBreakdown: getTemperatureBreakdown(filteredLeads),
      averageScore: getAverageScore(filteredLeads),
      conversionRate: getConversionRate(filteredLeads)
    }

    return NextResponse.json({
      leads: filteredLeads,
      pagination: {
        page: filters.page,
        limit: filters.limit,
        total: filteredLeads.length,
        pages: Math.ceil(filteredLeads.length / filters.limit)
      },
      filters: filters,
      summary: filterSummary
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid filter parameters', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error filtering leads:', error)
    return NextResponse.json(
      { error: 'Failed to filter leads' },
      { status: 500 }
    )
  }
}

// Helper functions
function getStatusBreakdown(leads: any[]) {
  const breakdown: Record<string, number> = {}
  leads.forEach(lead => {
    breakdown[lead.status] = (breakdown[lead.status] || 0) + 1
  })
  return breakdown
}

function getTemperatureBreakdown(leads: any[]) {
  const breakdown = { HOT: 0, WARM: 0, COLD: 0 }
  leads.forEach(lead => {
    if (lead.score >= 70) breakdown.HOT++
    else if (lead.score >= 40) breakdown.WARM++
    else breakdown.COLD++
  })
  return breakdown
}

function getAverageScore(leads: any[]) {
  if (leads.length === 0) return 0
  const total = leads.reduce((sum, lead) => sum + lead.score, 0)
  return Math.round(total / leads.length)
}

function getConversionRate(leads: any[]) {
  if (leads.length === 0) return 0
  const converted = leads.filter(lead => lead.status === 'CONVERTED').length
  return Math.round((converted / leads.length) * 100)
}

async function handleExport(leads: any[], format: 'csv' | 'xlsx') {
  // Export functionality would be implemented here
  // For now, return the data in JSON format
  return NextResponse.json({
    data: leads,
    format,
    message: 'Export functionality to be implemented'
  })
}
