'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  DollarSign,
  TrendingUp,
  TrendingDown,
  BarChart3,
  RefreshCw,
  PieChart,
  Target,
  Activity,
  CreditCard,
  Wallet,
  Building,
  Calculator
} from 'lucide-react'
import { toast } from 'react-hot-toast'

interface AccountAnalytics {
  summary: {
    totalAccounts: number
    totalAssets: number
    totalLiabilities: number
    totalEquity: number
    totalRevenue: number
    totalExpenses: number
    netIncome: number
    totalTransactions: number
    completedTransactions: number
    pendingTransactions: number
    totalTransactionAmount: number
    activeBudgets: number
    budgetedAmount: number
    actualAmount: number
    budgetVariance: number
    overBudgetCount: number
    reconciledCount: number
    unreconciledCount: number
  }
  accountsByType: Array<{
    accountType: string
    count: number
    totalBalance: number
  }>
  balanceSheet: {
    assets: {
      current: number
      fixed: number
      total: number
    }
    liabilities: {
      current: number
      longTerm: number
      total: number
    }
    equity: {
      total: number
    }
  }
  profitLoss: {
    revenue: number
    expenses: number
    netIncome: number
    profitMargin: number
  }
  cashFlow: Array<{
    month: string
    cash_inflow: number
    cash_outflow: number
    transaction_count: number
  }>
  topAccounts: Array<{
    id: string
    name: string
    accountType: string
    balance: number
    currency: string
    accountNumber: string | null
  }>
  recentTransactions: Array<{
    id: string
    transactionNumber: string
    description: string
    amount: number
    currency: string
    transactionDate: string
    transactionType: string
    status: string
    debitAccount: {
      name: string
      accountType: string
    }
    creditAccount: {
      name: string
      accountType: string
    } | null
    customer: {
      name: string
      companyName: string | null
    } | null
    createdBy: {
      name: string | null
      email: string | null
    }
    createdAt: string
  }>
  accountActivity: Array<{
    id: string
    name: string
    accountType: string
    transactionCount: number
    totalAmount: number
    lastTransactionDate: string | null
  }>
  period: number
}

export function AccountAnalytics() {
  const [analytics, setAnalytics] = useState<AccountAnalytics | null>(null)
  const [loading, setLoading] = useState(true)
  const [period, setPeriod] = useState('30')

  const fetchAnalytics = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/accounts/analytics?period=${period}`)
      if (!response.ok) {
        throw new Error('Failed to fetch analytics')
      }

      const data = await response.json()
      setAnalytics(data)
    } catch (error) {
      toast.error('Failed to load account analytics')
      console.error('Error fetching analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchAnalytics()
  }, [period])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num)
  }

  const getAccountTypeColor = (type: string) => {
    switch (type) {
      case 'ASSET':
        return 'bg-green-100 text-green-800'
      case 'LIABILITY':
        return 'bg-red-100 text-red-800'
      case 'EQUITY':
        return 'bg-blue-100 text-blue-800'
      case 'REVENUE':
        return 'bg-purple-100 text-purple-800'
      case 'EXPENSE':
        return 'bg-orange-100 text-orange-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getTransactionTypeColor = (type: string) => {
    switch (type) {
      case 'PAYMENT':
        return 'bg-red-100 text-red-800'
      case 'RECEIPT':
        return 'bg-green-100 text-green-800'
      case 'TRANSFER':
        return 'bg-blue-100 text-blue-800'
      case 'ADJUSTMENT':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!analytics) {
    return (
      <div className="text-center py-8 text-gray-500">
        <BarChart3 className="h-12 w-12 mx-auto mb-4 text-gray-300" />
        <p>Failed to load analytics data</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Financial Analytics</h3>
        <div className="flex items-center space-x-2">
          <Select value={period} onValueChange={setPeriod}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">Last 7 days</SelectItem>
              <SelectItem value="30">Last 30 days</SelectItem>
              <SelectItem value="90">Last 90 days</SelectItem>
              <SelectItem value="365">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={fetchAnalytics} size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-blue-100 rounded-full">
                <Building className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Total Accounts</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.summary.totalAccounts}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-green-100 rounded-full">
                <TrendingUp className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Net Income</p>
                <p className={`text-2xl font-bold ${analytics.summary.netIncome >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {formatCurrency(analytics.summary.netIncome)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-purple-100 rounded-full">
                <Activity className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Transactions</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.summary.totalTransactions}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-orange-100 rounded-full">
                <Target className="h-6 w-6 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Active Budgets</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.summary.activeBudgets}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Balance Sheet Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calculator className="h-5 w-5 mr-2" />
            Balance Sheet Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-6">
            <div className="text-center">
              <h4 className="font-semibold text-green-600 mb-2">Assets</h4>
              <p className="text-2xl font-bold text-green-600">
                {formatCurrency(analytics.balanceSheet.assets.total)}
              </p>
              <div className="mt-2 space-y-1">
                <div className="flex justify-between text-sm">
                  <span>Current:</span>
                  <span>{formatCurrency(analytics.balanceSheet.assets.current)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Fixed:</span>
                  <span>{formatCurrency(analytics.balanceSheet.assets.fixed)}</span>
                </div>
              </div>
            </div>
            
            <div className="text-center">
              <h4 className="font-semibold text-red-600 mb-2">Liabilities</h4>
              <p className="text-2xl font-bold text-red-600">
                {formatCurrency(analytics.balanceSheet.liabilities.total)}
              </p>
              <div className="mt-2 space-y-1">
                <div className="flex justify-between text-sm">
                  <span>Current:</span>
                  <span>{formatCurrency(analytics.balanceSheet.liabilities.current)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Long-term:</span>
                  <span>{formatCurrency(analytics.balanceSheet.liabilities.longTerm)}</span>
                </div>
              </div>
            </div>
            
            <div className="text-center">
              <h4 className="font-semibold text-blue-600 mb-2">Equity</h4>
              <p className="text-2xl font-bold text-blue-600">
                {formatCurrency(analytics.balanceSheet.equity.total)}
              </p>
              <div className="mt-2">
                <div className="text-sm text-gray-500">
                  Assets - Liabilities = Equity
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Profit & Loss */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <TrendingUp className="h-5 w-5 mr-2" />
            Profit & Loss Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">
                {formatCurrency(analytics.profitLoss.revenue)}
              </p>
              <p className="text-sm text-gray-500">Total Revenue</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-red-600">
                {formatCurrency(analytics.profitLoss.expenses)}
              </p>
              <p className="text-sm text-gray-500">Total Expenses</p>
            </div>
            <div className="text-center">
              <p className={`text-2xl font-bold ${analytics.profitLoss.netIncome >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {formatCurrency(analytics.profitLoss.netIncome)}
              </p>
              <p className="text-sm text-gray-500">Net Income</p>
            </div>
            <div className="text-center">
              <p className={`text-2xl font-bold ${analytics.profitLoss.profitMargin >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {analytics.profitLoss.profitMargin.toFixed(1)}%
              </p>
              <p className="text-sm text-gray-500">Profit Margin</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Budget Analysis */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Target className="h-5 w-5 mr-2" />
            Budget Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600">
                {formatCurrency(analytics.summary.budgetedAmount)}
              </p>
              <p className="text-sm text-gray-500">Budgeted Amount</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-purple-600">
                {formatCurrency(analytics.summary.actualAmount)}
              </p>
              <p className="text-sm text-gray-500">Actual Amount</p>
            </div>
            <div className="text-center">
              <p className={`text-2xl font-bold ${analytics.summary.budgetVariance >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {formatCurrency(analytics.summary.budgetVariance)}
              </p>
              <p className="text-sm text-gray-500">Variance</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-orange-600">
                {analytics.summary.overBudgetCount}
              </p>
              <p className="text-sm text-gray-500">Over Budget</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Charts and Breakdowns */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Accounts by Type */}
        <Card>
          <CardHeader>
            <CardTitle>Accounts by Type</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.accountsByType.map((item) => (
                <div key={item.accountType} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Badge className={getAccountTypeColor(item.accountType)}>
                      {item.accountType}
                    </Badge>
                  </div>
                  <div className="text-right">
                    <span className="font-semibold">{item.count}</span>
                    <p className="text-sm text-gray-500">
                      {formatCurrency(item.totalBalance)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Transaction Status */}
        <Card>
          <CardHeader>
            <CardTitle>Transaction Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Badge className="bg-green-100 text-green-800">
                    Completed
                  </Badge>
                </div>
                <div className="text-right">
                  <span className="font-semibold">{analytics.summary.completedTransactions}</span>
                  <p className="text-sm text-gray-500">
                    {formatCurrency(analytics.summary.totalTransactionAmount)}
                  </p>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Badge className="bg-yellow-100 text-yellow-800">
                    Pending
                  </Badge>
                </div>
                <div className="text-right">
                  <span className="font-semibold">{analytics.summary.pendingTransactions}</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Badge className="bg-blue-100 text-blue-800">
                    Reconciled
                  </Badge>
                </div>
                <div className="text-right">
                  <span className="font-semibold">{analytics.summary.reconciledCount}</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Badge className="bg-orange-100 text-orange-800">
                    Unreconciled
                  </Badge>
                </div>
                <div className="text-right">
                  <span className="font-semibold">{analytics.summary.unreconciledCount}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Accounts */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Wallet className="h-5 w-5 mr-2" />
            Top Accounts by Balance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {analytics.topAccounts.slice(0, 5).map((account, index) => (
              <div key={account.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-xs font-semibold text-blue-600">#{index + 1}</span>
                  </div>
                  <div>
                    <p className="font-medium">{account.name}</p>
                    <div className="flex items-center space-x-2">
                      <Badge className={getAccountTypeColor(account.accountType)} variant="outline">
                        {account.accountType}
                      </Badge>
                      {account.accountNumber && (
                        <span className="text-sm text-gray-500">{account.accountNumber}</span>
                      )}
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-green-600">
                    {formatCurrency(account.balance)}
                  </p>
                  <p className="text-sm text-gray-500">{account.currency}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Transactions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CreditCard className="h-5 w-5 mr-2" />
            Recent Transactions (Last 7 Days)
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {analytics.recentTransactions.length === 0 ? (
              <p className="text-gray-500 text-center py-4">No recent transactions</p>
            ) : (
              analytics.recentTransactions.map((transaction) => (
                <div key={transaction.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium">{transaction.transactionNumber}</p>
                    <p className="text-sm text-gray-600">{transaction.description}</p>
                    <div className="flex items-center space-x-2 mt-1">
                      <Badge className={getTransactionTypeColor(transaction.transactionType)} variant="outline">
                        {transaction.transactionType}
                      </Badge>
                      <span className="text-sm text-gray-500">
                        {new Date(transaction.transactionDate).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-green-600">
                      {formatCurrency(transaction.amount)}
                    </p>
                    <p className="text-sm text-gray-500">
                      {transaction.debitAccount.name} → {transaction.creditAccount?.name || 'N/A'}
                    </p>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>

      {/* Account Activity */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Activity className="h-5 w-5 mr-2" />
            Most Active Accounts
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {analytics.accountActivity.slice(0, 5).map((activity) => (
              <div key={activity.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <p className="font-medium">{activity.name}</p>
                  <div className="flex items-center space-x-2">
                    <Badge className={getAccountTypeColor(activity.accountType)} variant="outline">
                      {activity.accountType}
                    </Badge>
                    {activity.lastTransactionDate && (
                      <span className="text-sm text-gray-500">
                        Last: {new Date(activity.lastTransactionDate).toLocaleDateString()}
                      </span>
                    )}
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-blue-600">
                    {activity.transactionCount} transactions
                  </p>
                  <p className="text-sm text-gray-500">
                    {formatCurrency(activity.totalAmount)}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
