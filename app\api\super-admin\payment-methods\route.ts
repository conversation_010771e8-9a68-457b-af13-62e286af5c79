import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get payment method configurations from database
    let methods = []
    let config = {}
    
    try {
      methods = await prisma.paymentMethodConfig.findMany()
      
      // Convert to config object
      config = methods.reduce((acc, method) => {
        acc[method.method] = {
          enabled: method.isEnabled,
          ...method.config
        }
        return acc
      }, {} as Record<string, any>)
    } catch (error) {
      console.error('Error fetching payment methods:', error)
      // Return default config if table doesn't exist yet
      config = {
        creditCard: {
          enabled: true,
          supportedBrands: ['visa', 'mastercard', 'amex', 'discover'],
          requireCVV: true,
          require3DS: false
        },
        debitCard: {
          enabled: true,
          supportedBrands: ['visa', 'mastercard'],
          requireCVV: true,
          require3DS: false
        },
        applePay: {
          enabled: false,
          merchantId: ''
        },
        googlePay: {
          enabled: false,
          merchantId: ''
        },
        paypal: {
          enabled: false,
          clientId: ''
        },
        netBanking: {
          enabled: false,
          supportedBanks: []
        },
        ach: {
          enabled: false,
          verificationMethod: 'instant'
        },
        wire: {
          enabled: false,
          requireManualApproval: true
        },
        upi: {
          enabled: false,
          supportedApps: ['gpay', 'phonepe', 'paytm'],
          qrCodeEnabled: true
        },
        bitcoin: {
          enabled: false,
          walletAddress: ''
        },
        ethereum: {
          enabled: false,
          walletAddress: ''
        },
        klarna: {
          enabled: false,
          clientId: ''
        },
        afterpay: {
          enabled: false,
          merchantId: ''
        }
      }
    }

    return NextResponse.json({
      success: true,
      methods: methods,
      config: config
    })
  } catch (error) {
    console.error('Error fetching payment methods:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const config = await request.json()

    // Update or create payment method configurations
    const updates = []
    
    for (const [method, settings] of Object.entries(config)) {
      const { enabled, ...methodConfig } = settings as any

      try {
        updates.push(
          prisma.paymentMethodConfig.upsert({
            where: { method },
            update: {
              isEnabled: enabled,
              config: methodConfig,
              updatedAt: new Date()
            },
            create: {
              method,
              isEnabled: enabled,
              config: methodConfig
            }
          })
        )
      } catch (error) {
        console.error(`Error updating payment method config for ${method}:`, error)
      }
    }

    try {
      await Promise.all(updates)
    } catch (error) {
      console.error('Error saving payment method config:', error)
      // Continue even if some updates fail
    }

    // Log the configuration change
    try {
      await prisma.auditLog.create({
        data: {
          action: 'UPDATE_PAYMENT_METHODS',
          entityType: 'PAYMENT_METHOD_CONFIG',
          entityId: 'payment_methods',
          userId: session.user.id,
          details: {
            updatedMethods: Object.keys(config),
            timestamp: new Date().toISOString()
          }
        }
      })
    } catch (error) {
      // Ignore audit log errors
      console.error('Error creating audit log:', error)
    }

    return NextResponse.json({
      success: true,
      message: 'Payment method configuration updated successfully'
    })
  } catch (error) {
    console.error('Error updating payment method config:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
