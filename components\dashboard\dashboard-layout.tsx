'use client'

import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { Sidebar } from './sidebar'
import { Header } from './header'
import { MobileSidebar } from './mobile-sidebar'

interface DashboardLayoutProps {
  children: React.ReactNode
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const { data: session } = useSession()
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  const user = session?.user ? {
    name: session.user.name || '',
    email: session.user.email || '',
    image: session.user.image || '',
    role: session.user.role || '',
    company: (() => {
      try {
        if (session.user.company && typeof session.user.company === 'object') {
          return { name: String(session.user.company.name || '') }
        }
        return undefined
      } catch (error) {
        console.error('Error processing company object:', error)
        return undefined
      }
    })()
  } : undefined

  return (
    <div className="h-screen flex overflow-hidden bg-gray-100">
      {/* Desktop Sidebar */}
      <div className="hidden md:flex md:flex-shrink-0">
        <Sidebar
          user={user}
          collapsed={sidebarCollapsed}
          onToggle={() => setSidebarCollapsed(!sidebarCollapsed)}
        />
      </div>

      {/* Mobile Sidebar */}
      <MobileSidebar
        isOpen={mobileMenuOpen}
        onClose={() => setMobileMenuOpen(false)}
        user={user}
      />

      {/* Main Content */}
      <div className="flex flex-col w-0 flex-1 overflow-hidden">
        <Header
          user={user}
          onMobileMenuToggle={() => setMobileMenuOpen(true)}
        />
        
        <main className="flex-1 relative overflow-y-auto focus:outline-none">
          <div className="py-6">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
              {children}
            </div>
          </div>
        </main>

        {/* Footer */}
        <footer className="bg-white border-t border-gray-200 px-4 py-3">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
            <p className="text-center text-sm text-gray-500">
              © {new Date().getFullYear()} Business SaaS. All rights reserved.
            </p>
          </div>
        </footer>
      </div>
    </div>
  )
}
