import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '30' // days
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - parseInt(period))

    // Get conversion analytics
    const [
      totalLeads,
      convertedLeads,
      conversions,
      conversionsByType,
      conversionsByMonth,
      topPerformers,
      conversionFunnel
    ] = await Promise.all([
      // Total leads in period
      prisma.lead.count({
        where: {
          companyId: session.user.companyId,
          createdAt: { gte: startDate }
        }
      }),

      // Converted leads in period
      prisma.lead.count({
        where: {
          companyId: session.user.companyId,
          status: 'CONVERTED',
          convertedAt: { gte: startDate }
        }
      }),

      // All conversions with details
      prisma.leadConversion.findMany({
        where: {
          companyId: session.user.companyId,
          createdAt: { gte: startDate }
        },
        include: {
          lead: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              companyName: true,
              score: true,
              source: true
            }
          },
          customer: {
            select: {
              id: true,
              name: true,
              email: true,
              company: true
            }
          },
          salesRep: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),

      // Conversions by type
      prisma.leadConversion.groupBy({
        by: ['conversionType'],
        where: {
          companyId: session.user.companyId,
          createdAt: { gte: startDate }
        },
        _count: {
          id: true
        },
        _sum: {
          conversionValue: true
        }
      }),

      // Conversions by month (last 12 months)
      prisma.$queryRaw`
        SELECT 
          DATE_TRUNC('month', "createdAt") as month,
          COUNT(*)::int as conversions,
          SUM("conversionValue")::float as total_value
        FROM "LeadConversion" 
        WHERE "companyId" = ${session.user.companyId}
          AND "createdAt" >= ${new Date(Date.now() - 365 * 24 * 60 * 60 * 1000)}
        GROUP BY DATE_TRUNC('month', "createdAt")
        ORDER BY month DESC
        LIMIT 12
      `,

      // Top performing sales reps
      prisma.leadConversion.groupBy({
        by: ['salesRepId'],
        where: {
          companyId: session.user.companyId,
          createdAt: { gte: startDate }
        },
        _count: {
          id: true
        },
        _sum: {
          conversionValue: true
        },
        orderBy: {
          _count: {
            id: 'desc'
          }
        },
        take: 10
      }),

      // Conversion funnel data
      Promise.all([
        prisma.lead.count({
          where: {
            companyId: session.user.companyId,
            createdAt: { gte: startDate }
          }
        }),
        prisma.lead.count({
          where: {
            companyId: session.user.companyId,
            createdAt: { gte: startDate },
            status: { in: ['QUALIFIED', 'PROPOSAL', 'NEGOTIATION'] }
          }
        }),
        prisma.lead.count({
          where: {
            companyId: session.user.companyId,
            createdAt: { gte: startDate },
            status: { in: ['PROPOSAL', 'NEGOTIATION'] }
          }
        }),
        prisma.lead.count({
          where: {
            companyId: session.user.companyId,
            createdAt: { gte: startDate },
            status: 'CONVERTED'
          }
        })
      ])
    ])

    // Calculate conversion rate
    const conversionRate = totalLeads > 0 ? (convertedLeads / totalLeads) * 100 : 0

    // Calculate average conversion time
    const avgConversionTime = await calculateAverageConversionTime(session.user.companyId, startDate)

    // Calculate total conversion value
    const totalConversionValue = conversions.reduce((sum, conv) => sum + (conv.conversionValue || 0), 0)

    // Get sales rep details for top performers
    const salesRepIds = topPerformers.map(p => p.salesRepId).filter(Boolean)
    const salesReps = await prisma.user.findMany({
      where: {
        id: { in: salesRepIds },
        companyId: session.user.companyId
      },
      select: {
        id: true,
        name: true,
        email: true
      }
    })

    const topPerformersWithDetails = topPerformers.map(performer => {
      const salesRep = salesReps.find(rep => rep.id === performer.salesRepId)
      return {
        ...performer,
        salesRep,
        conversionRate: totalLeads > 0 ? (performer._count.id / totalLeads) * 100 : 0
      }
    })

    // Conversion funnel with percentages
    const [totalFunnelLeads, qualifiedLeads, proposalLeads, convertedFunnelLeads] = conversionFunnel
    const funnelData = [
      {
        stage: 'Total Leads',
        count: totalFunnelLeads,
        percentage: 100
      },
      {
        stage: 'Qualified',
        count: qualifiedLeads,
        percentage: totalFunnelLeads > 0 ? (qualifiedLeads / totalFunnelLeads) * 100 : 0
      },
      {
        stage: 'Proposal',
        count: proposalLeads,
        percentage: totalFunnelLeads > 0 ? (proposalLeads / totalFunnelLeads) * 100 : 0
      },
      {
        stage: 'Converted',
        count: convertedFunnelLeads,
        percentage: totalFunnelLeads > 0 ? (convertedFunnelLeads / totalFunnelLeads) * 100 : 0
      }
    ]

    // Lead source analysis
    const leadSourceAnalysis = await prisma.lead.groupBy({
      by: ['source'],
      where: {
        companyId: session.user.companyId,
        createdAt: { gte: startDate }
      },
      _count: {
        id: true
      }
    })

    const convertedBySource = await prisma.lead.groupBy({
      by: ['source'],
      where: {
        companyId: session.user.companyId,
        status: 'CONVERTED',
        convertedAt: { gte: startDate }
      },
      _count: {
        id: true
      }
    })

    const sourceAnalysis = leadSourceAnalysis.map(source => {
      const converted = convertedBySource.find(c => c.source === source.source)?._count.id || 0
      return {
        source: source.source,
        totalLeads: source._count.id,
        convertedLeads: converted,
        conversionRate: source._count.id > 0 ? (converted / source._count.id) * 100 : 0
      }
    })

    return NextResponse.json({
      summary: {
        totalLeads,
        convertedLeads,
        conversionRate: Math.round(conversionRate * 100) / 100,
        totalConversions: conversions.length,
        totalConversionValue,
        averageConversionValue: conversions.length > 0 ? totalConversionValue / conversions.length : 0,
        averageConversionTime: Math.round(avgConversionTime)
      },
      conversionsByType: conversionsByType.map(type => ({
        type: type.conversionType,
        count: type._count.id,
        totalValue: type._sum.conversionValue || 0,
        averageValue: type._count.id > 0 ? (type._sum.conversionValue || 0) / type._count.id : 0
      })),
      conversionsByMonth,
      topPerformers: topPerformersWithDetails,
      conversionFunnel: funnelData,
      sourceAnalysis,
      recentConversions: conversions.slice(0, 10),
      trends: {
        conversionRate: conversionRate,
        averageValue: conversions.length > 0 ? totalConversionValue / conversions.length : 0,
        timeToConvert: avgConversionTime
      }
    })

  } catch (error) {
    console.error('Error fetching conversion analytics:', error)
    return NextResponse.json(
      { error: 'Failed to fetch conversion analytics' },
      { status: 500 }
    )
  }
}

async function calculateAverageConversionTime(companyId: string, startDate: Date) {
  const conversions = await prisma.leadConversion.findMany({
    where: {
      companyId,
      createdAt: { gte: startDate }
    },
    include: {
      lead: {
        select: {
          createdAt: true
        }
      }
    }
  })

  if (conversions.length === 0) return 0

  const totalTime = conversions.reduce((sum, conversion) => {
    const leadCreated = new Date(conversion.lead.createdAt)
    const converted = new Date(conversion.createdAt)
    const timeDiff = converted.getTime() - leadCreated.getTime()
    return sum + timeDiff
  }, 0)

  // Return average time in days
  return totalTime / conversions.length / (1000 * 60 * 60 * 24)
}
