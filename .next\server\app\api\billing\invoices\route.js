"use strict";(()=>{var e={};e.id=4688,e.ids=[4688],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},32081:e=>{e.exports=require("child_process")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},84399:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>h,originalPathname:()=>v,patchFetch:()=>f,requestAsyncStorage:()=>_,routeModule:()=>y,serverHooks:()=>w,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>I});var s={};r.r(s),r.d(s,{GET:()=>p,POST:()=>m});var a=r(95419),i=r(69108),n=r(99678),o=r(78070),c=r(81355),l=r(3205),u=r(9108),d=r(15922);async function p(e){try{let t=await (0,c.getServerSession)(l.L);if(!t?.user?.companyId)return o.Z.json({success:!1,error:"Unauthorized"},{status:401});let{searchParams:r}=new URL(e.url),s=parseInt(r.get("limit")||"10"),a=(parseInt(r.get("offset")||"0"),await u._.subscription.findFirst({where:{companyId:t.user.companyId,status:{in:["ACTIVE","TRIALING","PAST_DUE","CANCELED"]}},include:{pricingPlan:!0}}));if(!a||!a.stripeCustomerId)return o.Z.json({success:!0,data:{invoices:[],hasMore:!1,total:0}});let i=await d.Ag.invoices.list({customer:a.stripeCustomerId,limit:s,starting_after:void 0,expand:["data.payment_intent"]}),n=i.data.map(e=>({id:e.id,number:e.number,status:e.status,amount:e.total,currency:e.currency,created:new Date(1e3*e.created),dueDate:e.due_date?new Date(1e3*e.due_date):null,paidAt:e.status_transitions.paid_at?new Date(1e3*e.status_transitions.paid_at):null,description:e.description,hostedInvoiceUrl:e.hosted_invoice_url,invoicePdf:e.invoice_pdf,periodStart:new Date(1e3*e.period_start),periodEnd:new Date(1e3*e.period_end),subtotal:e.subtotal,tax:e.tax||0,discount:e.discount?.amount||0,lines:e.lines.data.map(e=>({id:e.id,description:e.description,amount:e.amount,quantity:e.quantity,period:{start:new Date(1e3*e.period.start),end:new Date(1e3*e.period.end)}})),paymentMethod:e.payment_intent?.payment_method_types||[],attemptCount:e.attempt_count}));return o.Z.json({success:!0,data:{invoices:n,hasMore:i.has_more,total:n.length,subscription:{planName:a.pricingPlan.name,status:a.status,billingCycle:a.billingCycle}}})}catch(e){return console.error("Error fetching billing invoices:",e),o.Z.json({success:!1,error:"Failed to fetch billing invoices"},{status:500})}}async function m(e){try{let t=await (0,c.getServerSession)(l.L);if(!t?.user?.companyId)return o.Z.json({success:!1,error:"Unauthorized"},{status:401});let{action:r,invoiceId:s}=await e.json(),a=await u._.subscription.findFirst({where:{companyId:t.user.companyId,status:{in:["ACTIVE","TRIALING","PAST_DUE","CANCELED"]}}});if(!a||!a.stripeCustomerId)return o.Z.json({success:!1,error:"No subscription found"},{status:404});switch(r){case"retry_payment":let i=await d.Ag.invoices.retrieve(s);if("open"!==i.status)return o.Z.json({success:!1,error:"Invoice is not payable"},{status:400});{let e=await d.Ag.invoices.pay(s);return o.Z.json({success:!0,data:{paymentIntent:e}})}case"download_pdf":let n=await d.Ag.invoices.retrieve(s);return o.Z.json({success:!0,data:{pdfUrl:n.invoice_pdf,hostedUrl:n.hosted_invoice_url}});default:return o.Z.json({success:!1,error:"Invalid action"},{status:400})}}catch(e){return console.error("Error handling invoice action:",e),o.Z.json({success:!1,error:"Failed to handle invoice action"},{status:500})}}let y=new a.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/billing/invoices/route",pathname:"/api/billing/invoices",filename:"route",bundlePath:"app/api/billing/invoices/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\billing\\invoices\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:_,staticGenerationAsyncStorage:g,serverHooks:w,headerHooks:h,staticGenerationBailout:I}=y,v="/api/billing/invoices/route";function f(){return(0,n.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:g})}},3205:(e,t,r)=>{r.d(t,{L:()=>l});var s=r(86485),a=r(10375),i=r(50694),n=r(6521),o=r.n(n),c=r(9108);let l={providers:[(0,s.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await c._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),r=t?.companyId;if(!r&&t){let e=await c._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(r=e?.id)&&await c._.user.update({where:{id:t.id},data:{companyId:r}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await o().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await c._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:r}}catch(e){return console.error("Authentication error:",e),null}}}),(0,a.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,i.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,r)=>{r.d(t,{_:()=>a});let s=require("@prisma/client"),a=globalThis.prisma??new s.PrismaClient},15922:(e,t,r)=>{r.d(t,{$t:()=>l,Ag:()=>a,FL:()=>c,R:()=>o,Sh:()=>n,hT:()=>i});var s=r(91211);if(!process.env.STRIPE_SECRET_KEY)throw Error("STRIPE_SECRET_KEY is not set in environment variables");let a=new s.Z(process.env.STRIPE_SECRET_KEY,{apiVersion:"2024-06-20",typescript:!0}),i=async e=>(await a.customers.list({email:e,limit:1})).data[0]||null,n=async e=>await a.customers.create({email:e.email,name:e.name,metadata:{companyId:e.companyId}}),o=async e=>{let t={customer:e.customerId,payment_method_types:["card"],line_items:[{price:e.priceId,quantity:1}],mode:"subscription",success_url:e.successUrl,cancel_url:e.cancelUrl,allow_promotion_codes:!0};return e.trialPeriodDays&&e.trialPeriodDays>0&&(t.subscription_data={trial_period_days:e.trialPeriodDays}),await a.checkout.sessions.create(t)},c=async e=>await a.billingPortal.sessions.create({customer:e.customerId,return_url:e.returnUrl}),l=(e,t,r)=>a.webhooks.constructEvent(e,t,r)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1638,6206,6521,2455,4520,1211],()=>r(84399));module.exports=s})();