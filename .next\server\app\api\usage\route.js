"use strict";(()=>{var e={};e.id=1153,e.ids=[1153],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},95425:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>_,originalPathname:()=>v,patchFetch:()=>P,requestAsyncStorage:()=>w,routeModule:()=>I,serverHooks:()=>f,staticGenerationAsyncStorage:()=>x,staticGenerationBailout:()=>q});var a={};r.r(a),r.d(a,{GET:()=>d,formatBytes:()=>h,getUsageStatus:()=>y});var n=r(95419),s=r(69108),o=r(99678),i=r(78070),c=r(81355),u=r(3205),l=r(9108);async function d(e){try{let t=await (0,c.getServerSession)(u.L);if(!t?.user?.companyId)return i.Z.json({success:!1,error:"Unauthorized"},{status:401});let{searchParams:r}=new URL(e.url),a=r.get("period")||"30",n="true"===r.get("detailed"),s=await l._.subscription.findFirst({where:{companyId:t.user.companyId,status:{in:["ACTIVE","TRIALING","PAST_DUE"]}},include:{pricingPlan:!0}});if(!s)return i.Z.json({success:!1,error:"No active subscription found"},{status:404});let o=new Date;o.setDate(o.getDate()-parseInt(a));let[d,g,I,w,x,f,_,q,v]=await Promise.all([l._.user.count({where:{companyId:t.user.companyId}}),l._.customer.count({where:{companyId:t.user.companyId}}),l._.quotation.count({where:{companyId:t.user.companyId}}),l._.invoice.count({where:{companyId:t.user.companyId}}),l._.contract.count({where:{companyId:t.user.companyId}}),l._.quotation.count({where:{companyId:t.user.companyId,createdAt:{gte:o}}}),l._.invoice.count({where:{companyId:t.user.companyId,createdAt:{gte:o}}}),l._.contract.count({where:{companyId:t.user.companyId,createdAt:{gte:o}}}),p(t.user.companyId)]),P={users:s.pricingPlan.maxUsers,customers:s.pricingPlan.maxCustomers,quotations:s.pricingPlan.maxQuotations,invoices:s.pricingPlan.maxInvoices,contracts:s.pricingPlan.maxContracts,storage:Number(s.pricingPlan.maxStorage)},b={users:{current:d,limit:P.users,percentage:Math.round(d/P.users*100),status:y(d,P.users)},customers:{current:g,limit:P.customers,percentage:Math.round(g/P.customers*100),status:y(g,P.customers)},quotations:{current:I,limit:P.quotations,percentage:Math.round(I/P.quotations*100),status:y(I,P.quotations),recent:f},invoices:{current:w,limit:P.invoices,percentage:Math.round(w/P.invoices*100),status:y(w,P.invoices),recent:_},contracts:{current:x,limit:P.contracts,percentage:Math.round(x/P.contracts*100),status:y(x,P.contracts),recent:q},storage:{current:v,limit:P.storage,percentage:Math.round(v/P.storage*100),status:y(v,P.storage),currentFormatted:h(v),limitFormatted:h(P.storage)}},A=null;return n&&(A=await m(t.user.companyId,o)),i.Z.json({success:!0,data:{usage:b,subscription:{planName:s.pricingPlan.name,status:s.status,billingCycle:s.billingCycle,currentPeriodEnd:s.currentPeriodEnd},period:parseInt(a),detailed:A}})}catch(e){return console.error("Error fetching usage data:",e),i.Z.json({success:!1,error:"Failed to fetch usage data"},{status:500})}}async function p(e){let[t,r,a]=await Promise.all([l._.quotation.count({where:{companyId:e}}),l._.invoice.count({where:{companyId:e}}),l._.contract.count({where:{companyId:e}})]);return 5e4*t+75e3*r+1e5*a+Math.floor(1e7*Math.random())}async function m(e,t){let r=await l._.quotation.groupBy({by:["createdAt"],where:{companyId:e,createdAt:{gte:t}},_count:!0}),a=await l._.invoice.groupBy({by:["createdAt"],where:{companyId:e,createdAt:{gte:t}},_count:!0});return{dailyQuotations:g(r),dailyInvoices:g(a)}}function g(e){return Object.entries(e.reduce((e,t)=>{let r=t.createdAt.toISOString().split("T")[0];return e[r]=(e[r]||0)+t._count,e},{})).map(([e,t])=>({date:e,count:t}))}function y(e,t){let r=e/t*100;return e>=t?"exceeded":r>=90?"critical":r>=75?"warning":"normal"}function h(e){if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB","TB"][t]}let I=new n.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/usage/route",pathname:"/api/usage",filename:"route",bundlePath:"app/api/usage/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\usage\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:w,staticGenerationAsyncStorage:x,serverHooks:f,headerHooks:_,staticGenerationBailout:q}=I,v="/api/usage/route";function P(){return(0,o.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:x})}},3205:(e,t,r)=>{r.d(t,{L:()=>u});var a=r(86485),n=r(10375),s=r(50694),o=r(6521),i=r.n(o),c=r(9108);let u={providers:[(0,a.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await c._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),r=t?.companyId;if(!r&&t){let e=await c._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(r=e?.id)&&await c._.user.update({where:{id:t.id},data:{companyId:r}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await i().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await c._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:r}}catch(e){return console.error("Authentication error:",e),null}}}),(0,n.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,s.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,r)=>{r.d(t,{_:()=>n});let a=require("@prisma/client"),n=globalThis.prisma??new a.PrismaClient}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,6206,6521,2455,4520],()=>r(95425));module.exports=a})();