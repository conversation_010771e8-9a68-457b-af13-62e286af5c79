import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'

// GET /api/super-admin/users/[id] - Get user details
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || session?.user?.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Super admin access required' }, { status: 403 })
    }

    const userId = params.id

    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        company: {
          select: {
            id: true,
            name: true,
            status: true,
            industry: true,
            size: true,
            createdAt: true
          }
        },
        ownedCompany: {
          select: {
            id: true,
            name: true,
            status: true,
            industry: true,
            size: true,
            createdAt: true
          }
        },
        userSettings: true,
        _count: {
          select: {
            createdLeads: true,
            assignedLeads: true,
            createdCustomers: true,
            assignedCustomers: true,
            createdQuotations: true,
            assignedQuotations: true,
            createdInvoices: true,
            assignedInvoices: true,
            createdContracts: true,
            assignedContracts: true,
            createdTasks: true,
            assignedTasks: true,
            activities: true,
            notes: true,
            documents: true
          }
        }
      }
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Get recent activities
    const recentActivities = await prisma.activity.findMany({
      where: { createdById: userId },
      take: 10,
      orderBy: { createdAt: 'desc' },
      include: {
        company: {
          select: { name: true }
        }
      }
    })

    // Get audit logs for this user
    const auditLogs = await prisma.auditLog.findMany({
      where: {
        OR: [
          { userId: userId },
          { entityType: 'User', entityId: userId }
        ]
      },
      take: 20,
      orderBy: { createdAt: 'desc' }
    })

    // Get login history (from audit logs)
    const loginHistory = await prisma.auditLog.findMany({
      where: {
        userId: userId,
        action: { in: ['LOGIN', 'LOGOUT', 'LOGIN_FAILED'] }
      },
      take: 10,
      orderBy: { createdAt: 'desc' }
    })

    // Remove password from response
    const { password: _, ...userResponse } = user

    return NextResponse.json({
      user: userResponse,
      recentActivities: recentActivities.map(activity => ({
        id: activity.id,
        type: activity.type,
        title: activity.title,
        description: activity.description,
        company: activity.company?.name,
        createdAt: activity.createdAt
      })),
      auditLogs: auditLogs.map(log => ({
        id: log.id,
        action: log.action,
        entityType: log.entityType,
        userEmail: log.userEmail,
        ipAddress: log.ipAddress,
        metadata: log.metadata,
        createdAt: log.createdAt
      })),
      loginHistory: loginHistory.map(log => ({
        id: log.id,
        action: log.action,
        ipAddress: log.ipAddress,
        userAgent: log.userAgent,
        metadata: log.metadata,
        createdAt: log.createdAt
      }))
    })

  } catch (error) {
    console.error('Error fetching user details:', error)
    return NextResponse.json(
      { error: 'Failed to fetch user details' },
      { status: 500 }
    )
  }
}

// PATCH /api/super-admin/users/[id] - Update user
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || session?.user?.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Super admin access required' }, { status: 403 })
    }

    const userId = params.id
    const body = await request.json()

    // Get current user data for audit log
    const currentUser = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        email: true,
        name: true,
        role: true,
        status: true,
        companyId: true
      }
    })

    if (!currentUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Prepare update data
    const updateData: any = {}
    const allowedFields = [
      'name', 'firstName', 'lastName', 'phone', 'role', 'status',
      'title', 'department', 'timezone', 'language', 'companyId'
    ]

    // Only include allowed fields that are present in the request
    allowedFields.forEach(field => {
      if (body[field] !== undefined) {
        updateData[field] = body[field]
      }
    })

    // Handle password update separately
    if (body.password) {
      updateData.password = await bcrypt.hash(body.password, 12)
    }

    // Update user
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: updateData,
      include: {
        company: {
          select: {
            id: true,
            name: true,
            status: true
          }
        }
      }
    })

    // Log the action
    await prisma.auditLog.create({
      data: {
        action: 'USER_UPDATED',
        entityType: 'User',
        entityId: userId,
        userId: session.user.id,
        userEmail: session.user.email,
        userRole: session.user.role,
        oldValues: currentUser,
        newValues: updateData,
        metadata: {
          updatedByAdmin: true,
          adminId: session.user.id,
          fieldsUpdated: Object.keys(updateData)
        }
      }
    })

    // Remove password from response
    const { password: _, ...userResponse } = updatedUser

    return NextResponse.json({
      user: userResponse,
      message: 'User updated successfully'
    })

  } catch (error) {
    console.error('Error updating user:', error)
    return NextResponse.json(
      { error: 'Failed to update user' },
      { status: 500 }
    )
  }
}

// DELETE /api/super-admin/users/[id] - Delete user (soft delete)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || session?.user?.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Super admin access required' }, { status: 403 })
    }

    const userId = params.id

    // Prevent deleting self
    if (userId === session.user.id) {
      return NextResponse.json(
        { error: 'Cannot delete your own account' },
        { status: 400 }
      )
    }

    // Get user data for audit log
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        email: true,
        name: true,
        role: true,
        status: true
      }
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Soft delete by setting status to INACTIVE
    await prisma.user.update({
      where: { id: userId },
      data: {
        status: 'INACTIVE',
        email: `deleted_${Date.now()}_${user.email}` // Prevent email conflicts
      }
    })

    // Log the action
    await prisma.auditLog.create({
      data: {
        action: 'USER_DELETED',
        entityType: 'User',
        entityId: userId,
        userId: session.user.id,
        userEmail: session.user.email,
        userRole: session.user.role,
        oldValues: user,
        metadata: {
          deletedByAdmin: true,
          adminId: session.user.id,
          deletionType: 'soft'
        }
      }
    })

    return NextResponse.json({
      message: 'User deleted successfully'
    })

  } catch (error) {
    console.error('Error deleting user:', error)
    return NextResponse.json(
      { error: 'Failed to delete user' },
      { status: 500 }
    )
  }
}
