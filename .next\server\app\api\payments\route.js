"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/payments/route";
exports.ids = ["app/api/payments/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fpayments%2Froute&page=%2Fapi%2Fpayments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpayments%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fpayments%2Froute&page=%2Fapi%2Fpayments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpayments%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_proj_nextjs_saas_app_api_payments_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/payments/route.ts */ \"(rsc)/./app/api/payments/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/payments/route\",\n        pathname: \"/api/payments\",\n        filename: \"route\",\n        bundlePath: \"app/api/payments/route\"\n    },\n    resolvedPagePath: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\api\\\\payments\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_proj_nextjs_saas_app_api_payments_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/payments/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fpayments%2Froute&page=%2Fapi%2Fpayments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpayments%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/payments/route.ts":
/*!***********************************!*\
  !*** ./app/api/payments/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n// GET /api/payments - Get all payments for the user's company\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id || !session?.user?.companyId) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get(\"page\") || \"1\");\n        const limit = parseInt(searchParams.get(\"limit\") || \"50\");\n        const status = searchParams.get(\"status\");\n        const method = searchParams.get(\"method\");\n        const startDate = searchParams.get(\"startDate\");\n        const endDate = searchParams.get(\"endDate\");\n        // Build where clause\n        const where = {\n            companyId: session.user.companyId,\n            type: \"PAYMENT\"\n        };\n        if (status && status !== \"all\") {\n            where.paymentStatus = status;\n        }\n        if (method && method !== \"all\") {\n            where.paymentMethod = method;\n        }\n        if (startDate || endDate) {\n            where.transactionDate = {};\n            if (startDate) {\n                where.transactionDate.gte = new Date(startDate);\n            }\n            if (endDate) {\n                where.transactionDate.lte = new Date(endDate);\n            }\n        }\n        // Get payments with pagination\n        const [payments, total] = await Promise.all([\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.transaction.findMany({\n                where,\n                include: {\n                    invoice: {\n                        select: {\n                            id: true,\n                            invoiceNumber: true,\n                            customer: {\n                                select: {\n                                    id: true,\n                                    name: true,\n                                    companyName: true\n                                }\n                            }\n                        }\n                    }\n                },\n                orderBy: {\n                    transactionDate: \"desc\"\n                },\n                skip: (page - 1) * limit,\n                take: limit\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.transaction.count({\n                where\n            })\n        ]);\n        // Transform the data\n        const transformedPayments = payments.map((payment)=>({\n                id: payment.id,\n                amount: Number(payment.amount),\n                paymentDate: payment.transactionDate.toISOString(),\n                paymentMethod: payment.paymentMethod || \"OTHER\",\n                reference: payment.reference,\n                notes: payment.notes,\n                status: payment.paymentStatus,\n                invoice: payment.invoice ? {\n                    id: payment.invoice.id,\n                    invoiceNumber: payment.invoice.invoiceNumber,\n                    customer: {\n                        id: payment.invoice.customer.id,\n                        name: payment.invoice.customer.name,\n                        company: payment.invoice.customer.companyName\n                    }\n                } : null,\n                createdBy: null,\n                createdAt: payment.createdAt?.toISOString() || payment.transactionDate.toISOString()\n            }));\n        // Calculate summary statistics\n        const summary = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.transaction.aggregate({\n            where: {\n                companyId: session.user.companyId,\n                type: \"PAYMENT\"\n            },\n            _sum: {\n                amount: true\n            },\n            _count: {\n                id: true\n            }\n        });\n        const completedSummary = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.transaction.aggregate({\n            where: {\n                companyId: session.user.companyId,\n                type: \"PAYMENT\",\n                paymentStatus: \"COMPLETED\"\n            },\n            _sum: {\n                amount: true\n            }\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            payments: transformedPayments,\n            pagination: {\n                page,\n                limit,\n                total,\n                pages: Math.ceil(total / limit)\n            },\n            summary: {\n                totalAmount: Number(summary._sum.amount || 0),\n                totalCount: summary._count.id,\n                completedAmount: Number(completedSummary._sum.amount || 0)\n            }\n        });\n    } catch (error) {\n        console.error(\"Error fetching payments:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to fetch payments\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/payments - Create a new payment (for standalone payments)\nasync function POST(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id || !session?.user?.companyId) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { amount, paymentMethod, reference, notes, customerId, description } = body;\n        // Validate required fields\n        if (!amount || amount <= 0) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Valid amount is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Create the payment transaction\n        const payment = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.transaction.create({\n            data: {\n                type: \"PAYMENT\",\n                amount: amount,\n                paymentMethod: paymentMethod || \"OTHER\",\n                paymentStatus: \"COMPLETED\",\n                reference,\n                notes,\n                description: description || \"Standalone payment\",\n                companyId: session.user.companyId,\n                transactionDate: new Date()\n            }\n        });\n        // Log activity\n        await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.activity.create({\n            data: {\n                type: \"PAYMENT\",\n                title: \"Payment Recorded\",\n                description: `Standalone payment of $${amount} recorded`,\n                companyId: session.user.companyId,\n                createdById: session.user.id\n            }\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            payment: {\n                id: payment.id,\n                amount: Number(payment.amount),\n                paymentDate: payment.transactionDate.toISOString(),\n                paymentMethod: payment.paymentMethod,\n                reference: payment.reference,\n                notes: payment.notes,\n                status: payment.paymentStatus,\n                createdBy: null,\n                customer: null,\n                createdAt: payment.createdAt?.toISOString() || payment.transactionDate.toISOString()\n            },\n            message: \"Payment recorded successfully\"\n        });\n    } catch (error) {\n        console.error(\"Error creating payment:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to create payment\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/payments/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/github */ \"(rsc)/./node_modules/next-auth/providers/github.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n\nconst authOptions = {\n    // Using JWT strategy instead of database adapter for now\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                try {\n                    if (!credentials?.email || !credentials?.password) {\n                        console.log(\"Missing credentials\");\n                        return null;\n                    }\n                    console.log(\"Attempting to authenticate user:\", credentials.email);\n                    const user = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.findUnique({\n                        where: {\n                            email: credentials.email\n                        },\n                        select: {\n                            id: true,\n                            email: true,\n                            name: true,\n                            password: true,\n                            role: true,\n                            companyId: true\n                        }\n                    });\n                    // Get the company ID - either as member or owner\n                    let finalCompanyId = user?.companyId;\n                    if (!finalCompanyId && user) {\n                        const ownedCompany = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.company.findFirst({\n                            where: {\n                                ownerId: user.id\n                            },\n                            select: {\n                                id: true\n                            }\n                        });\n                        finalCompanyId = ownedCompany?.id;\n                        // If user is company owner, update their companyId for future queries\n                        if (finalCompanyId) {\n                            await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                                where: {\n                                    id: user.id\n                                },\n                                data: {\n                                    companyId: finalCompanyId\n                                }\n                            });\n                        }\n                    }\n                    if (!user) {\n                        console.log(\"User not found:\", credentials.email);\n                        return null;\n                    }\n                    if (!user.password) {\n                        console.log(\"User has no password set:\", credentials.email);\n                        return null;\n                    }\n                    const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_3___default().compare(credentials.password, user.password);\n                    if (!isPasswordValid) {\n                        console.log(\"Invalid password for user:\", credentials.email);\n                        return null;\n                    }\n                    // Update last login\n                    await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                        where: {\n                            id: user.id\n                        },\n                        data: {\n                            lastLoginAt: new Date(),\n                            loginCount: {\n                                increment: 1\n                            }\n                        }\n                    });\n                    console.log(\"User authenticated successfully:\", user.email);\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name,\n                        role: user.role,\n                        companyId: finalCompanyId\n                    };\n                } catch (error) {\n                    console.error(\"Authentication error:\", error);\n                    return null;\n                }\n            }\n        }),\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            clientId: process.env.GITHUB_CLIENT_ID,\n            clientSecret: process.env.GITHUB_CLIENT_SECRET\n        })\n    ],\n    session: {\n        strategy: \"jwt\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                console.log(\"JWT callback - user data:\", {\n                    id: user.id,\n                    email: user.email,\n                    role: user.role,\n                    companyId: user.companyId\n                });\n                token.role = user.role;\n                token.companyId = user.companyId;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n                session.user.companyId = token.companyId;\n                // Ensure no company object is ever set on the session\n                if (session.user.company) {\n                    delete session.user.company;\n                }\n                console.log(\"Session callback - final session:\", {\n                    id: session.user.id,\n                    email: session.user.email,\n                    role: session.user.role,\n                    companyId: session.user.companyId\n                });\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        signUp: \"/auth/signup\",\n        error: \"/auth/error\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zYWFzLWFwcC8uL2xpYi9wcmlzbWEudHM/OTgyMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/lru-cache","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fpayments%2Froute&page=%2Fapi%2Fpayments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpayments%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();