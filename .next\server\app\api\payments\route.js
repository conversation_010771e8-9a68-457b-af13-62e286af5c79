"use strict";(()=>{var e={};e.id=7649,e.ids=[7649],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},38379:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>h,originalPathname:()=>f,patchFetch:()=>S,requestAsyncStorage:()=>y,routeModule:()=>p,serverHooks:()=>I,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>w});var r={};a.r(r),a.d(r,{GET:()=>d,POST:()=>m});var n=a(95419),o=a(69108),s=a(99678),i=a(78070),c=a(81355),u=a(3205),l=a(9108);async function d(e){try{let t=await (0,c.getServerSession)(u.L);if(!t?.user?.id||!t?.user?.companyId)return i.Z.json({error:"Unauthorized"},{status:401});let{searchParams:a}=new URL(e.url),r=parseInt(a.get("page")||"1"),n=parseInt(a.get("limit")||"50"),o=a.get("status"),s=a.get("method"),d=a.get("startDate"),m=a.get("endDate"),p={companyId:t.user.companyId,type:"PAYMENT"};o&&"all"!==o&&(p.paymentStatus=o),s&&"all"!==s&&(p.paymentMethod=s),(d||m)&&(p.transactionDate={},d&&(p.transactionDate.gte=new Date(d)),m&&(p.transactionDate.lte=new Date(m)));let[y,g]=await Promise.all([l._.transaction.findMany({where:p,include:{invoice:{select:{id:!0,invoiceNumber:!0,customer:{select:{id:!0,name:!0,companyName:!0}}}}},orderBy:{transactionDate:"desc"},skip:(r-1)*n,take:n}),l._.transaction.count({where:p})]),I=y.map(e=>({id:e.id,amount:Number(e.amount),paymentDate:e.transactionDate.toISOString(),paymentMethod:e.paymentMethod||"OTHER",reference:e.reference,notes:e.notes,status:e.paymentStatus,invoice:e.invoice?{id:e.invoice.id,invoiceNumber:e.invoice.invoiceNumber,customer:{id:e.invoice.customer.id,name:e.invoice.customer.name,company:e.invoice.customer.companyName}}:null,createdBy:null,createdAt:e.createdAt?.toISOString()||e.transactionDate.toISOString()})),h=await l._.transaction.aggregate({where:{companyId:t.user.companyId,type:"PAYMENT"},_sum:{amount:!0},_count:{id:!0}}),w=await l._.transaction.aggregate({where:{companyId:t.user.companyId,type:"PAYMENT",paymentStatus:"COMPLETED"},_sum:{amount:!0}});return i.Z.json({payments:I,pagination:{page:r,limit:n,total:g,pages:Math.ceil(g/n)},summary:{totalAmount:Number(h._sum.amount||0),totalCount:h._count.id,completedAmount:Number(w._sum.amount||0)}})}catch(e){return console.error("Error fetching payments:",e),i.Z.json({error:"Failed to fetch payments"},{status:500})}}async function m(e){try{let t=await (0,c.getServerSession)(u.L);if(!t?.user?.id||!t?.user?.companyId)return i.Z.json({error:"Unauthorized"},{status:401});let{amount:a,paymentMethod:r,reference:n,notes:o,customerId:s,description:d}=await e.json();if(!a||a<=0)return i.Z.json({error:"Valid amount is required"},{status:400});let m=await l._.transaction.create({data:{type:"PAYMENT",amount:a,paymentMethod:r||"OTHER",paymentStatus:"COMPLETED",reference:n,notes:o,description:d||"Standalone payment",companyId:t.user.companyId,transactionDate:new Date}});return await l._.activity.create({data:{type:"PAYMENT",title:"Payment Recorded",description:`Standalone payment of $${a} recorded`,companyId:t.user.companyId,createdById:t.user.id}}),i.Z.json({payment:{id:m.id,amount:Number(m.amount),paymentDate:m.transactionDate.toISOString(),paymentMethod:m.paymentMethod,reference:m.reference,notes:m.notes,status:m.paymentStatus,createdBy:null,customer:null,createdAt:m.createdAt?.toISOString()||m.transactionDate.toISOString()},message:"Payment recorded successfully"})}catch(e){return console.error("Error creating payment:",e),i.Z.json({error:"Failed to create payment"},{status:500})}}let p=new n.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/payments/route",pathname:"/api/payments",filename:"route",bundlePath:"app/api/payments/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\payments\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:y,staticGenerationAsyncStorage:g,serverHooks:I,headerHooks:h,staticGenerationBailout:w}=p,f="/api/payments/route";function S(){return(0,s.patchFetch)({serverHooks:I,staticGenerationAsyncStorage:g})}},3205:(e,t,a)=>{a.d(t,{L:()=>u});var r=a(86485),n=a(10375),o=a(50694),s=a(6521),i=a.n(s),c=a(9108);let u={providers:[(0,r.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await c._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),a=t?.companyId;if(!a&&t){let e=await c._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(a=e?.id)&&await c._.user.update({where:{id:t.id},data:{companyId:a}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await i().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await c._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:a}}catch(e){return console.error("Authentication error:",e),null}}}),(0,n.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,o.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,a)=>{a.d(t,{_:()=>n});let r=require("@prisma/client"),n=globalThis.prisma??new r.PrismaClient}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[1638,6206,6521,2455,4520],()=>a(38379));module.exports=r})();