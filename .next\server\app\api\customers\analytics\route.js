"use strict";(()=>{var e={};e.id=8663,e.ids=[8663],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},37625:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>I,originalPathname:()=>g,patchFetch:()=>v,requestAsyncStorage:()=>p,routeModule:()=>m,serverHooks:()=>w,staticGenerationAsyncStorage:()=>y,staticGenerationBailout:()=>h});var a={};r.r(a),r.d(a,{GET:()=>d});var o=r(95419),s=r(69108),n=r(99678),i=r(78070),c=r(81355),u=r(3205),l=r(9108);async function d(e){try{let t=await (0,c.getServerSession)(u.L);if(!t?.user?.id||!t?.user?.companyId)return i.Z.json({error:"Unauthorized"},{status:401});let{searchParams:r}=new URL(e.url),a=r.get("period")||"30",o=new Date;o.setDate(o.getDate()-parseInt(a));let[s,n,d,m,p,y,w,I,h,g]=await Promise.all([l._.customer.count({where:{companyId:t.user.companyId,createdAt:{gte:o}}}),l._.customer.groupBy({by:["status"],where:{companyId:t.user.companyId,createdAt:{gte:o}},_count:{id:!0}}),l._.customer.groupBy({by:["industry"],where:{companyId:t.user.companyId,industry:{not:null},createdAt:{gte:o}},_count:{id:!0}}),Promise.resolve([{date:new Date().toISOString().split("T")[0],customer_count:await l._.customer.count({where:{companyId:t.user.companyId}})}]),l._.customer.findMany({where:{companyId:t.user.companyId},select:{id:!0,name:!0,company:!0,email:!0,status:!0,invoices:{where:{status:"PAID"},select:{total:!0}},quotations:{where:{status:"ACCEPTED"},select:{total:!0}}},take:10}),l._.customer.findMany({where:{companyId:t.user.companyId,invoices:{some:{status:"PAID"}}},select:{id:!0,name:!0,createdAt:!0,invoices:{where:{status:"PAID"},select:{total:!0,paidAt:!0}}}}),Promise.all([l._.customer.count({where:{companyId:t.user.companyId,activities:{some:{createdAt:{gte:new Date(Date.now()-2592e6)}}}}}),l._.customer.count({where:{companyId:t.user.companyId,quotations:{some:{createdAt:{gte:new Date(Date.now()-2592e6)}}}}}),l._.customer.count({where:{companyId:t.user.companyId,invoices:{some:{createdAt:{gte:new Date(Date.now()-2592e6)}}}}})]),l._.customer.findMany({where:{companyId:t.user.companyId,createdAt:{gte:new Date(Date.now()-6048e5)}},orderBy:{createdAt:"desc"},take:10,include:{createdBy:{select:{name:!0,email:!0}}}}),Promise.all([l._.customer.count({where:{companyId:t.user.companyId,createdAt:{gte:new Date(Date.now()-7776e6)}}}),l._.customer.count({where:{companyId:t.user.companyId,createdAt:{gte:new Date(Date.now()-7776e6)},OR:[{activities:{some:{createdAt:{gte:new Date(Date.now()-2592e6)}}}},{invoices:{some:{createdAt:{gte:new Date(Date.now()-2592e6)}}}}]}})]),Promise.all([l._.customer.count({where:{companyId:t.user.companyId,invoices:{some:{status:"PAID",total:{gte:1e4}}}}}),l._.customer.count({where:{companyId:t.user.companyId,status:"ACTIVE",activities:{some:{createdAt:{gte:new Date(Date.now()-2592e6)}}}}}),l._.customer.count({where:{companyId:t.user.companyId,status:"PROSPECT",invoices:{none:{}}}})])]),v=p.map(e=>{let t=e.invoices.reduce((e,t)=>e+t.total,0)+e.quotations.reduce((e,t)=>e+t.total,0);return{...e,totalRevenue:t,invoices:void 0,quotations:void 0}}).sort((e,t)=>t.totalRevenue-e.totalRevenue),_=y.map(e=>{let t=e.invoices.reduce((e,t)=>e+t.total,0),r=Math.max(1,Math.floor((Date.now()-new Date(e.createdAt).getTime())/864e5)),a=t/Math.max(1,r/30);return{id:e.id,name:e.name,totalRevenue:t,customerAge:r,avgMonthlyRevenue:a,clv:12*a}}),D=_.length>0?_.reduce((e,t)=>e+t.clv,0)/_.length:0,[A,x]=h,[f,q,P]=w,[C,E,T]=g,b=v.reduce((e,t)=>e+t.totalRevenue,0);return i.Z.json({summary:{totalCustomers:s,totalRevenue:b,avgCLV:Math.round(100*D)/100,retentionRate:Math.round(100*(A>0?x/A*100:0))/100,newCustomersThisWeek:I.length,engagementRate:s>0?Math.round(f/s*100):0},customersByStatus:n.map(e=>({status:e.status,count:e._count.id})),customersByIndustry:d.map(e=>({industry:e.industry,count:e._count.id})),customerGrowth:m,topCustomers:v.slice(0,10),engagement:{withActivities:f,withQuotations:q,withInvoices:P},recentCustomers:I,segmentation:{highValue:C,active:E,prospects:T},clvMetrics:_.slice(0,10),period:parseInt(a)})}catch(e){return console.error("Error fetching customer analytics:",e),i.Z.json({error:"Failed to fetch customer analytics"},{status:500})}}let m=new o.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/customers/analytics/route",pathname:"/api/customers/analytics",filename:"route",bundlePath:"app/api/customers/analytics/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\customers\\analytics\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:p,staticGenerationAsyncStorage:y,serverHooks:w,headerHooks:I,staticGenerationBailout:h}=m,g="/api/customers/analytics/route";function v(){return(0,n.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:y})}},3205:(e,t,r)=>{r.d(t,{L:()=>u});var a=r(86485),o=r(10375),s=r(50694),n=r(6521),i=r.n(n),c=r(9108);let u={providers:[(0,a.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await c._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),r=t?.companyId;if(!r&&t){let e=await c._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(r=e?.id)&&await c._.user.update({where:{id:t.id},data:{companyId:r}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await i().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await c._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:r}}catch(e){return console.error("Authentication error:",e),null}}}),(0,o.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,s.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,r)=>{r.d(t,{_:()=>o});let a=require("@prisma/client"),o=globalThis.prisma??new a.PrismaClient}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,6206,6521,2455,4520],()=>r(37625));module.exports=a})();