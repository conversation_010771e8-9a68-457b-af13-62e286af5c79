(()=>{var e={};e.id=4844,e.ids=[4844],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},50583:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>f,tree:()=>d});var s=a(50482),r=a(69108),n=a(62563),o=a.n(n),l=a(68300),i={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);a.d(t,i);let d=["",{children:["dashboard",{children:["leads",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,99709)),"C:\\proj\\nextjs-saas\\app\\dashboard\\leads\\new\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,96339)),"C:\\proj\\nextjs-saas\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,59504)),"C:\\proj\\nextjs-saas\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,69361,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\proj\\nextjs-saas\\app\\dashboard\\leads\\new\\page.tsx"],u="/dashboard/leads/new/page",p={require:a,loadChunk:()=>Promise.resolve()},f=new s.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/leads/new/page",pathname:"/dashboard/leads/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},52192:(e,t,a)=>{Promise.resolve().then(a.bind(a,44999))},44999:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>f});var s=a(95344),r=a(3729),n=a(22254),o=a(61351),l=a(16212),i=a(68008),d=a(63024),c=a(28240),u=a(20783),p=a.n(u);function f(){let e=(0,n.useRouter)(),[t,a]=(0,r.useState)(!1);return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[s.jsx(l.z,{variant:"ghost",size:"sm",asChild:!0,children:(0,s.jsxs)(p(),{href:"/dashboard/leads",children:[s.jsx(d.Z,{className:"h-4 w-4 mr-2"}),"Back to Leads"]})}),(0,s.jsxs)("div",{children:[s.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Add New Lead"}),s.jsx("p",{className:"text-gray-500",children:"Create a new lead in your sales pipeline"})]})]}),s.jsx("div",{className:"flex items-center space-x-2",children:s.jsx(l.z,{variant:"outline",asChild:!0,children:s.jsx(p(),{href:"/dashboard/leads/pipeline",children:"Pipeline View"})})})]}),(0,s.jsxs)(o.Zb,{children:[s.jsx(o.Ol,{children:(0,s.jsxs)(o.ll,{className:"flex items-center",children:[s.jsx(c.Z,{className:"h-5 w-5 mr-2"}),"Lead Information"]})}),s.jsx(o.aY,{children:s.jsx(i.p,{isOpen:!0,onClose:()=>e.push("/dashboard/leads"),onSuccess:()=>{e.push("/dashboard/leads")},mode:"create"})})]})]})}},16802:(e,t,a)=>{"use strict";a.d(t,{$N:()=>x,Be:()=>g,Vq:()=>i,cN:()=>m,cZ:()=>p,fK:()=>f,hg:()=>d,t9:()=>u});var s=a(95344),r=a(3729),n=a(88794),o=a(14513),l=a(91626);let i=n.fC,d=n.xz,c=n.h_;n.x8;let u=r.forwardRef(({className:e,...t},a)=>s.jsx(n.aV,{ref:a,className:(0,l.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));u.displayName=n.aV.displayName;let p=r.forwardRef(({className:e,children:t,...a},r)=>(0,s.jsxs)(c,{children:[s.jsx(u,{}),(0,s.jsxs)(n.VY,{ref:r,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...a,children:[t,(0,s.jsxs)(n.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[s.jsx(o.Z,{className:"h-4 w-4"}),s.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));p.displayName=n.VY.displayName;let f=({className:e,...t})=>s.jsx("div",{className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});f.displayName="DialogHeader";let m=({className:e,...t})=>s.jsx("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});m.displayName="DialogFooter";let x=r.forwardRef(({className:e,...t},a)=>s.jsx(n.Dx,{ref:a,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));x.displayName=n.Dx.displayName;let g=r.forwardRef(({className:e,...t},a)=>s.jsx(n.dk,{ref:a,className:(0,l.cn)("text-sm text-muted-foreground",e),...t}));g.displayName=n.dk.displayName},92549:(e,t,a)=>{"use strict";a.d(t,{I:()=>o});var s=a(95344),r=a(3729),n=a(91626);let o=r.forwardRef(({className:e,type:t,...a},r)=>s.jsx("input",{type:t,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...a}));o.displayName="Input"},1586:(e,t,a)=>{"use strict";a.d(t,{_:()=>d});var s=a(95344),r=a(3729),n=a(14217),o=a(49247),l=a(91626);let i=(0,o.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef(({className:e,...t},a)=>s.jsx(n.f,{ref:a,className:(0,l.cn)(i(),e),...t}));d.displayName=n.f.displayName},17470:(e,t,a)=>{"use strict";a.d(t,{Bw:()=>x,Ph:()=>c,Ql:()=>g,i4:()=>p,ki:()=>u});var s=a(95344),r=a(3729),n=a(1146),o=a(25390),l=a(12704),i=a(62312),d=a(91626);let c=n.fC;n.ZA;let u=n.B4,p=r.forwardRef(({className:e,children:t,...a},r)=>(0,s.jsxs)(n.xz,{ref:r,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...a,children:[t,s.jsx(n.JO,{asChild:!0,children:s.jsx(o.Z,{className:"h-4 w-4 opacity-50"})})]}));p.displayName=n.xz.displayName;let f=r.forwardRef(({className:e,...t},a)=>s.jsx(n.u_,{ref:a,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:s.jsx(l.Z,{className:"h-4 w-4"})}));f.displayName=n.u_.displayName;let m=r.forwardRef(({className:e,...t},a)=>s.jsx(n.$G,{ref:a,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:s.jsx(o.Z,{className:"h-4 w-4"})}));m.displayName=n.$G.displayName;let x=r.forwardRef(({className:e,children:t,position:a="popper",...r},o)=>s.jsx(n.h_,{children:(0,s.jsxs)(n.VY,{ref:o,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...r,children:[s.jsx(f,{}),s.jsx(n.l_,{className:(0,d.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),s.jsx(m,{})]})}));x.displayName=n.VY.displayName,r.forwardRef(({className:e,...t},a)=>s.jsx(n.__,{ref:a,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=n.__.displayName;let g=r.forwardRef(({className:e,children:t,...a},r)=>(0,s.jsxs)(n.ck,{ref:r,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...a,children:[s.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:s.jsx(n.wU,{children:s.jsx(i.Z,{className:"h-4 w-4"})})}),s.jsx(n.eT,{children:t})]}));g.displayName=n.ck.displayName,r.forwardRef(({className:e,...t},a)=>s.jsx(n.Z0,{ref:a,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=n.Z0.displayName},63024:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(69224).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},99709:(e,t,a)=>{"use strict";a.r(t),a.d(t,{$$typeof:()=>n,__esModule:()=>r,default:()=>o});let s=(0,a(86843).createProxy)(String.raw`C:\proj\nextjs-saas\app\dashboard\leads\new\page.tsx`),{__esModule:r,$$typeof:n}=s,o=s.default},88794:(e,t,a)=>{"use strict";a.d(t,{Dx:()=>es,VY:()=>ea,aV:()=>et,dk:()=>er,fC:()=>J,h_:()=>ee,x8:()=>en,xz:()=>Q});var s=a(3729),r=a(85222),n=a(31405),o=a(98462),l=a(99048),i=a(33183),d=a(44155),c=a(27386),u=a(31179),p=a(43234),f=a(62409),m=a(1106),x=a(71210),g=a(45904),h=a(32751),y=a(95344),j="Dialog",[b,N]=(0,o.b)(j),[w,v]=b(j),_=e=>{let{__scopeDialog:t,children:a,open:r,defaultOpen:n,onOpenChange:o,modal:d=!0}=e,c=s.useRef(null),u=s.useRef(null),[p,f]=(0,i.T)({prop:r,defaultProp:n??!1,onChange:o,caller:j});return(0,y.jsx)(w,{scope:t,triggerRef:c,contentRef:u,contentId:(0,l.M)(),titleId:(0,l.M)(),descriptionId:(0,l.M)(),open:p,onOpenChange:f,onOpenToggle:s.useCallback(()=>f(e=>!e),[f]),modal:d,children:a})};_.displayName=j;var R="DialogTrigger",C=s.forwardRef((e,t)=>{let{__scopeDialog:a,...s}=e,o=v(R,a),l=(0,n.e)(t,o.triggerRef);return(0,y.jsx)(f.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":Y(o.open),...s,ref:l,onClick:(0,r.M)(e.onClick,o.onOpenToggle)})});C.displayName=R;var D="DialogPortal",[k,P]=b(D,{forceMount:void 0}),I=e=>{let{__scopeDialog:t,forceMount:a,children:r,container:n}=e,o=v(D,t);return(0,y.jsx)(k,{scope:t,forceMount:a,children:s.Children.map(r,e=>(0,y.jsx)(p.z,{present:a||o.open,children:(0,y.jsx)(u.h,{asChild:!0,container:n,children:e})}))})};I.displayName=D;var M="DialogOverlay",O=s.forwardRef((e,t)=>{let a=P(M,e.__scopeDialog),{forceMount:s=a.forceMount,...r}=e,n=v(M,e.__scopeDialog);return n.modal?(0,y.jsx)(p.z,{present:s||n.open,children:(0,y.jsx)(Z,{...r,ref:t})}):null});O.displayName=M;var z=(0,h.Z8)("DialogOverlay.RemoveScroll"),Z=s.forwardRef((e,t)=>{let{__scopeDialog:a,...s}=e,r=v(M,a);return(0,y.jsx)(x.Z,{as:z,allowPinchZoom:!0,shards:[r.contentRef],children:(0,y.jsx)(f.WV.div,{"data-state":Y(r.open),...s,ref:t,style:{pointerEvents:"auto",...s.style}})})}),E="DialogContent",V=s.forwardRef((e,t)=>{let a=P(E,e.__scopeDialog),{forceMount:s=a.forceMount,...r}=e,n=v(E,e.__scopeDialog);return(0,y.jsx)(p.z,{present:s||n.open,children:n.modal?(0,y.jsx)(A,{...r,ref:t}):(0,y.jsx)(F,{...r,ref:t})})});V.displayName=E;var A=s.forwardRef((e,t)=>{let a=v(E,e.__scopeDialog),o=s.useRef(null),l=(0,n.e)(t,a.contentRef,o);return s.useEffect(()=>{let e=o.current;if(e)return(0,g.Ry)(e)},[]),(0,y.jsx)(q,{...e,ref:l,trapFocus:a.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,r.M)(e.onCloseAutoFocus,e=>{e.preventDefault(),a.triggerRef.current?.focus()}),onPointerDownOutside:(0,r.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,a=0===t.button&&!0===t.ctrlKey;(2===t.button||a)&&e.preventDefault()}),onFocusOutside:(0,r.M)(e.onFocusOutside,e=>e.preventDefault())})}),F=s.forwardRef((e,t)=>{let a=v(E,e.__scopeDialog),r=s.useRef(!1),n=s.useRef(!1);return(0,y.jsx)(q,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(r.current||a.triggerRef.current?.focus(),t.preventDefault()),r.current=!1,n.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(r.current=!0,"pointerdown"!==t.detail.originalEvent.type||(n.current=!0));let s=t.target;a.triggerRef.current?.contains(s)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&n.current&&t.preventDefault()}})}),q=s.forwardRef((e,t)=>{let{__scopeDialog:a,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:l,...i}=e,u=v(E,a),p=s.useRef(null),f=(0,n.e)(t,p);return(0,m.EW)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(c.M,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:l,children:(0,y.jsx)(d.XB,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":Y(u.open),...i,ref:f,onDismiss:()=>u.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(U,{titleId:u.titleId}),(0,y.jsx)(X,{contentRef:p,descriptionId:u.descriptionId})]})]})}),$="DialogTitle",W=s.forwardRef((e,t)=>{let{__scopeDialog:a,...s}=e,r=v($,a);return(0,y.jsx)(f.WV.h2,{id:r.titleId,...s,ref:t})});W.displayName=$;var T="DialogDescription",B=s.forwardRef((e,t)=>{let{__scopeDialog:a,...s}=e,r=v(T,a);return(0,y.jsx)(f.WV.p,{id:r.descriptionId,...s,ref:t})});B.displayName=T;var G="DialogClose",S=s.forwardRef((e,t)=>{let{__scopeDialog:a,...s}=e,n=v(G,a);return(0,y.jsx)(f.WV.button,{type:"button",...s,ref:t,onClick:(0,r.M)(e.onClick,()=>n.onOpenChange(!1))})});function Y(e){return e?"open":"closed"}S.displayName=G;var L="DialogTitleWarning",[H,K]=(0,o.k)(L,{contentName:E,titleName:$,docsSlug:"dialog"}),U=({titleId:e})=>{let t=K(L),a=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return s.useEffect(()=>{e&&!document.getElementById(e)&&console.error(a)},[a,e]),null},X=({contentRef:e,descriptionId:t})=>{let a=K("DialogDescriptionWarning"),r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${a.contentName}}.`;return s.useEffect(()=>{let a=e.current?.getAttribute("aria-describedby");t&&a&&!document.getElementById(t)&&console.warn(r)},[r,e,t]),null},J=_,Q=C,ee=I,et=O,ea=V,es=W,er=B,en=S}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[1638,7948,6671,4626,7792,2506,8830,7150,2125,5045,8008],()=>a(50583));module.exports=s})();