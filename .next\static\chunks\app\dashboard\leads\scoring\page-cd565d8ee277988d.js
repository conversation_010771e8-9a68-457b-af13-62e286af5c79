(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2533],{90998:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(62898).Z)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]])},72894:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(62898).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},92457:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(62898).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},13008:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(62898).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},81016:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(62898).Z)("Flame",[["path",{d:"M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z",key:"96xj49"}]])},64280:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(62898).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},3928:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(62898).Z)("Snowflake",[["line",{x1:"2",x2:"22",y1:"12",y2:"12",key:"1dnqot"}],["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"m20 16-4-4 4-4",key:"rquw4f"}],["path",{d:"m4 8 4 4-4 4",key:"12s3z9"}],["path",{d:"m16 4-4 4-4-4",key:"1tumq1"}],["path",{d:"m8 20 4-4 4 4",key:"9p200w"}]])},44135:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(62898).Z)("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},66654:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(62898).Z)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},89275:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(62898).Z)("Thermometer",[["path",{d:"M14 4v10.54a4 4 0 1 1-4 0V4a2 2 0 0 1 4 0Z",key:"17jzev"}]])},85790:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(62898).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},25750:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(62898).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},84691:function(e,t,r){Promise.resolve().then(r.bind(r,66255))},66255:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return v}});var s=r(57437),a=r(2265),n=r(27815),i=r(85754),o=r(31478),l=r(72894),c=r(64280),d=r(25750),u=r(66654),m=r(90998),x=r(85790),p=r(89275),f=r(92457),h=r(13008),g=r(5925),y=r(89673);function b(){let[e,t]=(0,a.useState)(null),[r,b]=(0,a.useState)(!0),[v,j]=(0,a.useState)(!1),N=async()=>{try{b(!0);let e=await fetch("/api/leads/scoring/bulk",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"analyze"})});if(!e.ok)throw Error("Failed to fetch analytics");let r=await e.json();t(r)}catch(e){g.toast.error("Failed to load scoring analytics"),console.error("Error fetching analytics:",e)}finally{b(!1)}},w=async()=>{try{j(!0);let e=await fetch("/api/leads/scoring/bulk",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"recalculate"})});if(!e.ok)throw Error("Failed to recalculate scores");let t=await e.json();g.toast.success("Recalculated ".concat(t.updatedCount," lead scores")),N()}catch(e){g.toast.error("Failed to recalculate scores"),console.error("Error recalculating scores:",e)}finally{j(!1)}};(0,a.useEffect)(()=>{N()},[]);let k=e=>{switch(e){case"URGENT":return"text-red-600 bg-red-100";case"HIGH":return"text-orange-600 bg-orange-100";case"MEDIUM":return"text-yellow-600 bg-yellow-100";case"LOW":return"text-green-600 bg-green-100";default:return"text-gray-600 bg-gray-100"}};return r?(0,s.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):e?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Lead Scoring Analytics"}),(0,s.jsxs)(i.z,{onClick:w,disabled:v,children:[(0,s.jsx)(c.Z,{className:"h-4 w-4 mr-2 ".concat(v?"animate-spin":"")}),v?"Recalculating...":"Recalculate All Scores"]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,s.jsx)(n.Zb,{children:(0,s.jsx)(n.aY,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"p-3 bg-blue-100 rounded-full",children:(0,s.jsx)(d.Z,{className:"h-6 w-6 text-blue-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Total Leads"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.totalLeads})]})]})})}),(0,s.jsx)(n.Zb,{children:(0,s.jsx)(n.aY,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"p-3 bg-green-100 rounded-full",children:(0,s.jsx)(u.Z,{className:"h-6 w-6 text-green-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Average Score"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.averageScore})]})]})})}),(0,s.jsx)(n.Zb,{children:(0,s.jsx)(n.aY,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"p-3 bg-purple-100 rounded-full",children:(0,s.jsx)(m.Z,{className:"h-6 w-6 text-purple-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Recent Leads (30d)"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.recentLeads})]})]})})}),(0,s.jsx)(n.Zb,{children:(0,s.jsx)(n.aY,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"p-3 bg-orange-100 rounded-full",children:(0,s.jsx)(x.Z,{className:"h-6 w-6 text-orange-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Recent Avg Score"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.recentAverageScore})]})]})})})]}),(0,s.jsxs)(n.Zb,{children:[(0,s.jsx)(n.Ol,{children:(0,s.jsxs)(n.ll,{className:"flex items-center",children:[(0,s.jsx)(p.Z,{className:"h-5 w-5 mr-2"}),"Lead Temperature Distribution"]})}),(0,s.jsx)(n.aY,{children:(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-3xl font-bold text-red-600",children:e.temperatureDistribution.HOT}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:"Hot Leads"}),(0,s.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 mt-2",children:(0,s.jsx)("div",{className:"bg-red-600 h-2 rounded-full",style:{width:"".concat(e.temperatureDistribution.HOT/e.summary.totalLeads*100,"%")}})})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-3xl font-bold text-orange-600",children:e.temperatureDistribution.WARM}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:"Warm Leads"}),(0,s.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 mt-2",children:(0,s.jsx)("div",{className:"bg-orange-600 h-2 rounded-full",style:{width:"".concat(e.temperatureDistribution.WARM/e.summary.totalLeads*100,"%")}})})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-3xl font-bold text-blue-600",children:e.temperatureDistribution.COLD}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:"Cold Leads"}),(0,s.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 mt-2",children:(0,s.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"".concat(e.temperatureDistribution.COLD/e.summary.totalLeads*100,"%")}})})]})]})})]}),(0,s.jsxs)(n.Zb,{children:[(0,s.jsx)(n.Ol,{children:(0,s.jsxs)(n.ll,{className:"flex items-center",children:[(0,s.jsx)(f.Z,{className:"h-5 w-5 mr-2"}),"Score Distribution"]})}),(0,s.jsx)(n.aY,{children:(0,s.jsx)("div",{className:"space-y-3",children:Object.entries(e.scoreRanges).map(t=>{let[r,a]=t;return(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("span",{className:"text-sm font-medium",children:[r," points"]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"w-32 bg-gray-200 rounded-full h-2",children:(0,s.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"".concat(a/e.summary.totalLeads*100,"%")}})}),(0,s.jsx)("span",{className:"text-sm text-gray-500 w-8",children:a})]})]},r)})})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)(n.Zb,{children:[(0,s.jsx)(n.Ol,{children:(0,s.jsxs)(n.ll,{className:"flex items-center",children:[(0,s.jsx)(h.Z,{className:"h-5 w-5 mr-2 text-green-600"}),"Top Performers"]})}),(0,s.jsx)(n.aY,{children:(0,s.jsx)("div",{className:"space-y-2",children:e.topPerformers.slice(0,5).map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-2 bg-gray-50 rounded",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsxs)("span",{className:"text-sm font-medium",children:["#",t+1]}),(0,s.jsx)(y.q9,{score:e.score})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(o.C,{variant:"outline",className:"text-xs",children:e.status}),(0,s.jsx)("span",{className:"text-sm font-bold",children:e.score})]})]},e.id))})})]}),(0,s.jsxs)(n.Zb,{children:[(0,s.jsx)(n.Ol,{children:(0,s.jsxs)(n.ll,{className:"flex items-center",children:[(0,s.jsx)(l.Z,{className:"h-5 w-5 mr-2 text-red-600"}),"Needs Attention"]})}),(0,s.jsx)(n.aY,{children:(0,s.jsx)("div",{className:"space-y-2",children:e.bottomPerformers.slice(0,5).map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-2 bg-gray-50 rounded",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsxs)("span",{className:"text-sm font-medium",children:["#",t+1]}),(0,s.jsx)(y.q9,{score:e.score})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(o.C,{variant:"outline",className:"text-xs",children:e.status}),(0,s.jsx)("span",{className:"text-sm font-bold",children:e.score})]})]},e.id))})})]})]}),e.recommendations.length>0&&(0,s.jsxs)(n.Zb,{children:[(0,s.jsx)(n.Ol,{children:(0,s.jsx)(n.ll,{children:"Recommendations"})}),(0,s.jsx)(n.aY,{children:(0,s.jsx)("div",{className:"space-y-3",children:e.recommendations.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-start space-x-3 p-3 bg-gray-50 rounded-lg",children:[(0,s.jsx)(o.C,{className:"".concat(k(e.priority)," text-xs"),children:e.priority}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.message}),(0,s.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["Recommended action: ",e.action]})]})]},t))})})]})]}):(0,s.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,s.jsx)(l.Z,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,s.jsx)("p",{children:"Failed to load analytics data"})]})}function v(){return(0,s.jsx)("div",{className:"container mx-auto py-6",children:(0,s.jsx)(b,{})})}},89673:function(e,t,r){"use strict";r.d(t,{OT:function(){return u},q9:function(){return d}});var s=r(57437),a=r(31478),n=r(81016),i=r(44135),o=r(3928),l=r(89275);function c(e){let{score:t,size:r="md",showIcon:c=!0,showLabel:d=!0}=e,u=t>=70?"HOT":t>=40?"WARM":"COLD",m=(e=>{switch(e){case"HOT":return{color:"text-red-600 bg-red-100 border-red-200",icon:n.Z,label:"Hot Lead",description:"High priority, ready to convert"};case"WARM":return{color:"text-orange-600 bg-orange-100 border-orange-200",icon:i.Z,label:"Warm Lead",description:"Good potential, needs nurturing"};case"COLD":return{color:"text-blue-600 bg-blue-100 border-blue-200",icon:o.Z,label:"Cold Lead",description:"Low engagement, requires attention"};default:return{color:"text-gray-600 bg-gray-100 border-gray-200",icon:l.Z,label:"Unknown",description:"Temperature not determined"}}})(u),x=(e=>{switch(e){case"sm":return{badge:"text-xs px-2 py-1",icon:"h-3 w-3",text:"text-xs"};case"lg":return{badge:"text-base px-4 py-2",icon:"h-5 w-5",text:"text-base"};default:return{badge:"text-sm px-3 py-1",icon:"h-4 w-4",text:"text-sm"}}})(r),p=m.icon;return(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(a.C,{className:"".concat(m.color," border ").concat(x.badge," font-medium"),variant:"outline",children:(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[c&&(0,s.jsx)(p,{className:x.icon}),(0,s.jsx)("span",{children:u})]})}),d&&(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsx)("span",{className:"".concat(x.text," font-medium text-gray-900"),children:m.label}),"lg"===r&&(0,s.jsx)("span",{className:"text-xs text-gray-500",children:m.description})]})]})}function d(e){let{score:t}=e;return(0,s.jsx)(c,{score:t,size:"sm",showLabel:!1})}function u(e){let{score:t}=e;return(0,s.jsx)(c,{score:t,size:"lg",showIcon:!0,showLabel:!0})}},31478:function(e,t,r){"use strict";r.d(t,{C:function(){return o}});var s=r(57437);r(2265);var a=r(96061),n=r(1657);let i=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600",info:"border-transparent bg-blue-500 text-white hover:bg-blue-600"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:r,...a}=e;return(0,s.jsx)("div",{className:(0,n.cn)(i({variant:r}),t),...a})}},85754:function(e,t,r){"use strict";r.d(t,{z:function(){return c}});var s=r(57437),a=r(2265),n=r(67256),i=r(96061),o=r(1657);let l=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,t)=>{let{className:r,variant:a,size:i,asChild:c=!1,...d}=e,u=c?n.g7:"button";return(0,s.jsx)(u,{className:(0,o.cn)(l({variant:a,size:i,className:r})),ref:t,...d})});c.displayName="Button"},27815:function(e,t,r){"use strict";r.d(t,{Ol:function(){return o},SZ:function(){return c},Zb:function(){return i},aY:function(){return d},eW:function(){return u},ll:function(){return l}});var s=r(57437),a=r(2265),n=r(1657);let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});i.displayName="Card";let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...a})});o.displayName="CardHeader";let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})});l.displayName="CardTitle";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",r),...a})});c.displayName="CardDescription";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",r),...a})});d.displayName="CardContent";let u=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",r),...a})});u.displayName="CardFooter"},1657:function(e,t,r){"use strict";r.d(t,{cn:function(){return n}});var s=r(57042),a=r(74769);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.m6)((0,s.W)(t))}},5925:function(e,t,r){"use strict";let s,a;r.r(t),r.d(t,{CheckmarkIcon:function(){return J},ErrorIcon:function(){return W},LoaderIcon:function(){return V},ToastBar:function(){return eo},ToastIcon:function(){return et},Toaster:function(){return eu},default:function(){return em},resolveValue:function(){return k},toast:function(){return R},useToaster:function(){return P},useToasterStore:function(){return D}});var n,i=r(2265);let o={data:""},l=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||o,c=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,d=/\/\*[^]*?\*\/|  +/g,u=/\n+/g,m=(e,t)=>{let r="",s="",a="";for(let n in e){let i=e[n];"@"==n[0]?"i"==n[1]?r=n+" "+i+";":s+="f"==n[1]?m(i,n):n+"{"+m(i,"k"==n[1]?"":t)+"}":"object"==typeof i?s+=m(i,t?t.replace(/([^,])+/g,e=>n.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)):n):null!=i&&(n=/^--/.test(n)?n:n.replace(/[A-Z]/g,"-$&").toLowerCase(),a+=m.p?m.p(n,i):n+":"+i+";")}return r+(t&&a?t+"{"+a+"}":a)+s},x={},p=e=>{if("object"==typeof e){let t="";for(let r in e)t+=r+p(e[r]);return t}return e},f=(e,t,r,s,a)=>{var n;let i=p(e),o=x[i]||(x[i]=(e=>{let t=0,r=11;for(;t<e.length;)r=101*r+e.charCodeAt(t++)>>>0;return"go"+r})(i));if(!x[o]){let t=i!==e?e:(e=>{let t,r,s=[{}];for(;t=c.exec(e.replace(d,""));)t[4]?s.shift():t[3]?(r=t[3].replace(u," ").trim(),s.unshift(s[0][r]=s[0][r]||{})):s[0][t[1]]=t[2].replace(u," ").trim();return s[0]})(e);x[o]=m(a?{["@keyframes "+o]:t}:t,r?"":"."+o)}let l=r&&x.g?x.g:null;return r&&(x.g=x[o]),n=x[o],l?t.data=t.data.replace(l,n):-1===t.data.indexOf(n)&&(t.data=s?n+t.data:t.data+n),o},h=(e,t,r)=>e.reduce((e,s,a)=>{let n=t[a];if(n&&n.call){let e=n(r),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;n=t?"."+t:e&&"object"==typeof e?e.props?"":m(e,""):!1===e?"":e}return e+s+(null==n?"":n)},"");function g(e){let t=this||{},r=e.call?e(t.p):e;return f(r.unshift?r.raw?h(r,[].slice.call(arguments,1),t.p):r.reduce((e,r)=>Object.assign(e,r&&r.call?r(t.p):r),{}):r,l(t.target),t.g,t.o,t.k)}g.bind({g:1});let y,b,v,j=g.bind({k:1});function N(e,t){let r=this||{};return function(){let s=arguments;function a(n,i){let o=Object.assign({},n),l=o.className||a.className;r.p=Object.assign({theme:b&&b()},o),r.o=/ *go\d+/.test(l),o.className=g.apply(r,s)+(l?" "+l:""),t&&(o.ref=i);let c=e;return e[0]&&(c=o.as||e,delete o.as),v&&c[0]&&v(o),y(c,o)}return t?t(a):a}}var w=e=>"function"==typeof e,k=(e,t)=>w(e)?e(t):e,Z=(s=0,()=>(++s).toString()),C=()=>{if(void 0===a&&"u">typeof window){let e=matchMedia("(prefers-reduced-motion: reduce)");a=!e||e.matches}return a},O=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case 2:let{toast:r}=t;return O(e,{type:e.toasts.find(e=>e.id===r.id)?1:0,toast:r});case 3:let{toastId:s}=t;return{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,dismissed:!0,visible:!1}:e)};case 4:return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let a=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(e=>({...e,pauseDuration:e.pauseDuration+a}))}}},E=[],L={toasts:[],pausedAt:void 0},M=e=>{L=O(L,e),E.forEach(e=>{e(L)})},T={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},D=(e={})=>{let[t,r]=(0,i.useState)(L),s=(0,i.useRef)(L);(0,i.useEffect)(()=>(s.current!==L&&r(L),E.push(r),()=>{let e=E.indexOf(r);e>-1&&E.splice(e,1)}),[]);let a=t.toasts.map(t=>{var r,s,a;return{...e,...e[t.type],...t,removeDelay:t.removeDelay||(null==(r=e[t.type])?void 0:r.removeDelay)||(null==e?void 0:e.removeDelay),duration:t.duration||(null==(s=e[t.type])?void 0:s.duration)||(null==e?void 0:e.duration)||T[t.type],style:{...e.style,...null==(a=e[t.type])?void 0:a.style,...t.style}}});return{...t,toasts:a}},z=(e,t="blank",r)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...r,id:(null==r?void 0:r.id)||Z()}),A=e=>(t,r)=>{let s=z(t,e,r);return M({type:2,toast:s}),s.id},R=(e,t)=>A("blank")(e,t);R.error=A("error"),R.success=A("success"),R.loading=A("loading"),R.custom=A("custom"),R.dismiss=e=>{M({type:3,toastId:e})},R.remove=e=>M({type:4,toastId:e}),R.promise=(e,t,r)=>{let s=R.loading(t.loading,{...r,...null==r?void 0:r.loading});return"function"==typeof e&&(e=e()),e.then(e=>{let a=t.success?k(t.success,e):void 0;return a?R.success(a,{id:s,...r,...null==r?void 0:r.success}):R.dismiss(s),e}).catch(e=>{let a=t.error?k(t.error,e):void 0;a?R.error(a,{id:s,...r,...null==r?void 0:r.error}):R.dismiss(s)}),e};var S=(e,t)=>{M({type:1,toast:{id:e,height:t}})},$=()=>{M({type:5,time:Date.now()})},H=new Map,I=1e3,q=(e,t=I)=>{if(H.has(e))return;let r=setTimeout(()=>{H.delete(e),M({type:4,toastId:e})},t);H.set(e,r)},P=e=>{let{toasts:t,pausedAt:r}=D(e);(0,i.useEffect)(()=>{if(r)return;let e=Date.now(),s=t.map(t=>{if(t.duration===1/0)return;let r=(t.duration||0)+t.pauseDuration-(e-t.createdAt);if(r<0){t.visible&&R.dismiss(t.id);return}return setTimeout(()=>R.dismiss(t.id),r)});return()=>{s.forEach(e=>e&&clearTimeout(e))}},[t,r]);let s=(0,i.useCallback)(()=>{r&&M({type:6,time:Date.now()})},[r]),a=(0,i.useCallback)((e,r)=>{let{reverseOrder:s=!1,gutter:a=8,defaultPosition:n}=r||{},i=t.filter(t=>(t.position||n)===(e.position||n)&&t.height),o=i.findIndex(t=>t.id===e.id),l=i.filter((e,t)=>t<o&&e.visible).length;return i.filter(e=>e.visible).slice(...s?[l+1]:[0,l]).reduce((e,t)=>e+(t.height||0)+a,0)},[t]);return(0,i.useEffect)(()=>{t.forEach(e=>{if(e.dismissed)q(e.id,e.removeDelay);else{let t=H.get(e.id);t&&(clearTimeout(t),H.delete(e.id))}})},[t]),{toasts:t,handlers:{updateHeight:S,startPause:$,endPause:s,calculateOffset:a}}},F=j`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,Y=j`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,_=j`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,W=N("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${F} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${Y} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${_} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,U=j`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,V=N("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${U} 1s linear infinite;
`,B=j`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,G=j`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,J=N("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${B} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${G} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,K=N("div")`
  position: absolute;
`,Q=N("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,X=j`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,ee=N("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${X} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,et=({toast:e})=>{let{icon:t,type:r,iconTheme:s}=e;return void 0!==t?"string"==typeof t?i.createElement(ee,null,t):t:"blank"===r?null:i.createElement(Q,null,i.createElement(V,{...s}),"loading"!==r&&i.createElement(K,null,"error"===r?i.createElement(W,{...s}):i.createElement(J,{...s})))},er=e=>`
0% {transform: translate3d(0,${-200*e}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,es=e=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${-150*e}%,-1px) scale(.6); opacity:0;}
`,ea=N("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,en=N("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,ei=(e,t)=>{let r=e.includes("top")?1:-1,[s,a]=C()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[er(r),es(r)];return{animation:t?`${j(s)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${j(a)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},eo=i.memo(({toast:e,position:t,style:r,children:s})=>{let a=e.height?ei(e.position||t||"top-center",e.visible):{opacity:0},n=i.createElement(et,{toast:e}),o=i.createElement(en,{...e.ariaProps},k(e.message,e));return i.createElement(ea,{className:e.className,style:{...a,...r,...e.style}},"function"==typeof s?s({icon:n,message:o}):i.createElement(i.Fragment,null,n,o))});n=i.createElement,m.p=void 0,y=n,b=void 0,v=void 0;var el=({id:e,className:t,style:r,onHeightUpdate:s,children:a})=>{let n=i.useCallback(t=>{if(t){let r=()=>{s(e,t.getBoundingClientRect().height)};r(),new MutationObserver(r).observe(t,{subtree:!0,childList:!0,characterData:!0})}},[e,s]);return i.createElement("div",{ref:n,className:t,style:r},a)},ec=(e,t)=>{let r=e.includes("top"),s=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:C()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(r?1:-1)}px)`,...r?{top:0}:{bottom:0},...s}},ed=g`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,eu=({reverseOrder:e,position:t="top-center",toastOptions:r,gutter:s,children:a,containerStyle:n,containerClassName:o})=>{let{toasts:l,handlers:c}=P(r);return i.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...n},className:o,onMouseEnter:c.startPause,onMouseLeave:c.endPause},l.map(r=>{let n=r.position||t,o=ec(n,c.calculateOffset(r,{reverseOrder:e,gutter:s,defaultPosition:t}));return i.createElement(el,{id:r.id,key:r.id,onHeightUpdate:c.updateHeight,className:r.visible?ed:"",style:o},"custom"===r.type?k(r.message,r):a?a(r):i.createElement(eo,{toast:r,position:n}))}))},em=R}},function(e){e.O(0,[6723,2971,4938,1744],function(){return e(e.s=84691)}),_N_E=e.O()}]);