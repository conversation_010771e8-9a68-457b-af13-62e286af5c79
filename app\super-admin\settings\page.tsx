'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { redirect } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  TabsTrigger,
} from '@/components/ui/tabs'
import {
  Settings,
  Search,
  Plus,
  Edit,
  Trash2,
  Save,
  RefreshCw,
  Eye,
  EyeOff,
  Lock,
  Unlock,
  Globe,
  Shield,
  Database,
  Mail,
  Bell,
  Palette,
  Code
} from 'lucide-react'

interface SystemSetting {
  id: string
  key: string
  value: any
  category: string
  description?: string
  isPublic: boolean
  isEditable: boolean
  createdAt: Date
  updatedAt: Date
}

interface Category {
  name: string
  count: number
}

export default function SystemSettingsPage() {
  const { data: session, status } = useSession()
  const [settings, setSettings] = useState<Record<string, SystemSetting[]>>({})
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [editingSettings, setEditingSettings] = useState<Record<string, any>>({})
  const [newSetting, setNewSetting] = useState({
    key: '',
    value: '',
    category: '',
    description: '',
    isPublic: false,
    isEditable: true
  })
  const [showNewSettingDialog, setShowNewSettingDialog] = useState(false)

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (status === 'unauthenticated') {
    redirect('/auth/signin')
  }

  if (session?.user?.role !== 'SUPER_ADMIN') {
    redirect('/dashboard')
  }

  const fetchSettings = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        ...(selectedCategory && selectedCategory !== 'all' && { category: selectedCategory }),
        ...(searchTerm && { search: searchTerm })
      })

      const response = await fetch(`/api/super-admin/settings?${params}`)
      if (!response.ok) throw new Error('Failed to fetch settings')

      const data = await response.json()
      setSettings(data.settings)
      setCategories(data.categories)
    } catch (error) {
      console.error('Error fetching settings:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchSettings()
  }, [selectedCategory, searchTerm])

  const handleSettingChange = (settingId: string, field: string, value: any) => {
    setEditingSettings(prev => ({
      ...prev,
      [settingId]: {
        ...prev[settingId],
        [field]: value
      }
    }))
  }

  const saveSettings = async () => {
    try {
      setSaving(true)
      const settingsToUpdate = Object.entries(editingSettings).map(([id, changes]) => ({
        id,
        ...changes
      }))

      const response = await fetch('/api/super-admin/settings', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ settings: settingsToUpdate })
      })

      if (!response.ok) throw new Error('Failed to save settings')

      setEditingSettings({})
      await fetchSettings()
    } catch (error) {
      console.error('Error saving settings:', error)
    } finally {
      setSaving(false)
    }
  }

  const createSetting = async () => {
    try {
      const response = await fetch('/api/super-admin/settings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newSetting)
      })

      if (!response.ok) throw new Error('Failed to create setting')

      setNewSetting({
        key: '',
        value: '',
        category: '',
        description: '',
        isPublic: false,
        isEditable: true
      })
      setShowNewSettingDialog(false)
      await fetchSettings()
    } catch (error) {
      console.error('Error creating setting:', error)
    }
  }

  const deleteSetting = async (settingId: string) => {
    if (!confirm('Are you sure you want to delete this setting?')) return

    try {
      const response = await fetch(`/api/super-admin/settings?id=${settingId}`, {
        method: 'DELETE'
      })

      if (!response.ok) throw new Error('Failed to delete setting')

      await fetchSettings()
    } catch (error) {
      console.error('Error deleting setting:', error)
    }
  }

  const getCategoryIcon = (category: string) => {
    const icons = {
      'security': Shield,
      'database': Database,
      'email': Mail,
      'notifications': Bell,
      'ui': Palette,
      'api': Code,
      'general': Settings
    }
    return icons[category.toLowerCase() as keyof typeof icons] || Settings
  }

  const formatValue = (value: any, isEditing: boolean = false) => {
    if (typeof value === 'boolean') {
      return value ? 'true' : 'false'
    }
    if (typeof value === 'object') {
      return isEditing ? JSON.stringify(value, null, 2) : JSON.stringify(value)
    }
    return String(value)
  }

  const parseValue = (value: string, originalValue: any) => {
    if (typeof originalValue === 'boolean') {
      return value === 'true'
    }
    if (typeof originalValue === 'number') {
      return Number(value)
    }
    if (typeof originalValue === 'object') {
      try {
        return JSON.parse(value)
      } catch {
        return value
      }
    }
    return value
  }

  const hasChanges = Object.keys(editingSettings).length > 0

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <div className="flex items-center space-x-3">
            <Settings className="h-8 w-8 text-blue-600" />
            <h1 className="text-3xl font-bold text-gray-900">System Settings</h1>
          </div>
          <p className="text-gray-500 mt-1">Configure global application settings</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={fetchSettings} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          {hasChanges && (
            <Button onClick={saveSettings} disabled={saving}>
              <Save className={`h-4 w-4 mr-2 ${saving ? 'animate-spin' : ''}`} />
              Save Changes
            </Button>
          )}
          <Dialog open={showNewSettingDialog} onOpenChange={setShowNewSettingDialog}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Setting
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Setting</DialogTitle>
                <DialogDescription>
                  Add a new system setting to configure application behavior.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="key">Key</Label>
                  <Input
                    id="key"
                    value={newSetting.key}
                    onChange={(e) => setNewSetting(prev => ({ ...prev, key: e.target.value }))}
                    placeholder="e.g., app.max_users_per_company"
                  />
                </div>
                <div>
                  <Label htmlFor="category">Category</Label>
                  <Select value={newSetting.category} onValueChange={(value) => setNewSetting(prev => ({ ...prev, category: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="general">General</SelectItem>
                      <SelectItem value="security">Security</SelectItem>
                      <SelectItem value="database">Database</SelectItem>
                      <SelectItem value="email">Email</SelectItem>
                      <SelectItem value="notifications">Notifications</SelectItem>
                      <SelectItem value="ui">UI/UX</SelectItem>
                      <SelectItem value="api">API</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="value">Value</Label>
                  <Textarea
                    id="value"
                    value={newSetting.value}
                    onChange={(e) => setNewSetting(prev => ({ ...prev, value: e.target.value }))}
                    placeholder="Setting value (JSON for objects)"
                  />
                </div>
                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={newSetting.description}
                    onChange={(e) => setNewSetting(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Describe what this setting does"
                  />
                </div>
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="isPublic"
                      checked={newSetting.isPublic}
                      onCheckedChange={(checked) => setNewSetting(prev => ({ ...prev, isPublic: checked }))}
                    />
                    <Label htmlFor="isPublic">Public</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="isEditable"
                      checked={newSetting.isEditable}
                      onCheckedChange={(checked) => setNewSetting(prev => ({ ...prev, isEditable: checked }))}
                    />
                    <Label htmlFor="isEditable">Editable</Label>
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setShowNewSettingDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={createSetting}>Create Setting</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Settings</p>
                <p className="text-2xl font-bold text-gray-900">
                  {Object.values(settings).flat().length}
                </p>
              </div>
              <Settings className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Categories</p>
                <p className="text-2xl font-bold text-gray-900">{categories.length}</p>
              </div>
              <Database className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Public Settings</p>
                <p className="text-2xl font-bold text-gray-900">
                  {Object.values(settings).flat().filter(s => s.isPublic).length}
                </p>
              </div>
              <Globe className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search settings..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category.name} value={category.name}>
                    {category.name} ({category.count})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Settings by Category */}
      <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
        <TabsList className="grid w-full grid-cols-4 lg:grid-cols-8">
          <TabsTrigger value="all">All</TabsTrigger>
          {categories.slice(0, 7).map((category) => {
            const Icon = getCategoryIcon(category.name)
            return (
              <TabsTrigger key={category.name} value={category.name} className="flex items-center space-x-1">
                <Icon className="h-4 w-4" />
                <span className="hidden sm:inline">{category.name}</span>
              </TabsTrigger>
            )
          })}
        </TabsList>

        <TabsContent value={selectedCategory} className="space-y-6">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            Object.entries(settings).map(([category, categorySettings]) => (
              <Card key={category}>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    {(() => {
                      const Icon = getCategoryIcon(category)
                      return <Icon className="h-5 w-5" />
                    })()}
                    <span>{category}</span>
                    <Badge variant="secondary">{categorySettings.length}</Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Key</TableHead>
                          <TableHead>Value</TableHead>
                          <TableHead>Description</TableHead>
                          <TableHead>Flags</TableHead>
                          <TableHead>Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {categorySettings.map((setting) => {
                          const isEditing = editingSettings[setting.id]
                          const currentValue = isEditing?.value !== undefined ? isEditing.value : setting.value

                          return (
                            <TableRow key={setting.id}>
                              <TableCell>
                                <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                                  {setting.key}
                                </code>
                              </TableCell>
                              <TableCell>
                                {setting.isEditable ? (
                                  <Textarea
                                    value={formatValue(currentValue, true)}
                                    onChange={(e) => handleSettingChange(setting.id, 'value', parseValue(e.target.value, setting.value))}
                                    className="min-h-[60px] font-mono text-sm"
                                  />
                                ) : (
                                  <code className="text-sm bg-gray-50 px-2 py-1 rounded block max-w-xs overflow-hidden">
                                    {formatValue(setting.value)}
                                  </code>
                                )}
                              </TableCell>
                              <TableCell>
                                {setting.isEditable ? (
                                  <Input
                                    value={isEditing?.description !== undefined ? isEditing.description : setting.description || ''}
                                    onChange={(e) => handleSettingChange(setting.id, 'description', e.target.value)}
                                    placeholder="Add description..."
                                  />
                                ) : (
                                  <span className="text-sm text-gray-600">
                                    {setting.description || 'No description'}
                                  </span>
                                )}
                              </TableCell>
                              <TableCell>
                                <div className="flex items-center space-x-2">
                                  <div className="flex items-center space-x-1">
                                    {setting.isPublic ? <Eye className="h-4 w-4 text-green-600" /> : <EyeOff className="h-4 w-4 text-gray-400" />}
                                    <span className="text-xs">{setting.isPublic ? 'Public' : 'Private'}</span>
                                  </div>
                                  <div className="flex items-center space-x-1">
                                    {setting.isEditable ? <Unlock className="h-4 w-4 text-blue-600" /> : <Lock className="h-4 w-4 text-gray-400" />}
                                    <span className="text-xs">{setting.isEditable ? 'Editable' : 'Locked'}</span>
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell>
                                {setting.isEditable && (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => deleteSetting(setting.id)}
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                )}
                              </TableCell>
                            </TableRow>
                          )
                        })}
                      </TableBody>
                    </Table>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
