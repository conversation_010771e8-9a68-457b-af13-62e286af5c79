'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Lock,
  Shield,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Eye,
  Key,
  UserCheck,
  Globe,
  RefreshCw,
  Settings,
  Activity,
  Clock,
  MapPin
} from 'lucide-react'

interface SecurityEvent {
  id: string
  type: 'login' | 'failed_login' | 'permission_change' | 'data_access' | 'suspicious_activity'
  user: string
  description: string
  timestamp: Date
  severity: 'low' | 'medium' | 'high' | 'critical'
  location: string
  ip: string
}

interface SecuritySetting {
  id: string
  name: string
  description: string
  enabled: boolean
  category: 'authentication' | 'access_control' | 'monitoring' | 'encryption'
}

export default function SuperAdminSecurityPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date())

  // Mock data - in production, this would come from APIs
  const [securityEvents] = useState<SecurityEvent[]>([
    {
      id: '1',
      type: 'failed_login',
      user: '<EMAIL>',
      description: 'Multiple failed login attempts',
      timestamp: new Date(Date.now() - 5 * 60 * 1000),
      severity: 'high',
      location: 'New York, US',
      ip: '*************'
    },
    {
      id: '2',
      type: 'login',
      user: '<EMAIL>',
      description: 'Successful admin login',
      timestamp: new Date(Date.now() - 15 * 60 * 1000),
      severity: 'low',
      location: 'London, UK',
      ip: '********'
    },
    {
      id: '3',
      type: 'permission_change',
      user: '<EMAIL>',
      description: 'User permissions modified',
      timestamp: new Date(Date.now() - 30 * 60 * 1000),
      severity: 'medium',
      location: 'London, UK',
      ip: '********'
    },
    {
      id: '4',
      type: 'suspicious_activity',
      user: '<EMAIL>',
      description: 'Unusual data access pattern detected',
      timestamp: new Date(Date.now() - 45 * 60 * 1000),
      severity: 'critical',
      location: 'Unknown',
      ip: '***********'
    }
  ])

  const [securitySettings, setSecuritySettings] = useState<SecuritySetting[]>([
    {
      id: '1',
      name: 'Two-Factor Authentication',
      description: 'Require 2FA for all admin accounts',
      enabled: true,
      category: 'authentication'
    },
    {
      id: '2',
      name: 'Session Timeout',
      description: 'Automatically log out inactive users',
      enabled: true,
      category: 'authentication'
    },
    {
      id: '3',
      name: 'IP Whitelist',
      description: 'Restrict access to approved IP addresses',
      enabled: false,
      category: 'access_control'
    },
    {
      id: '4',
      name: 'Audit Logging',
      description: 'Log all administrative actions',
      enabled: true,
      category: 'monitoring'
    },
    {
      id: '5',
      name: 'Data Encryption',
      description: 'Encrypt sensitive data at rest',
      enabled: true,
      category: 'encryption'
    },
    {
      id: '6',
      name: 'Intrusion Detection',
      description: 'Monitor for suspicious activities',
      enabled: true,
      category: 'monitoring'
    }
  ])

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  const refreshData = () => {
    setIsLoading(true)
    setLastUpdated(new Date())
    setTimeout(() => setIsLoading(false), 1000)
  }

  const toggleSetting = (settingId: string) => {
    setSecuritySettings(prev => 
      prev.map(setting => 
        setting.id === settingId 
          ? { ...setting, enabled: !setting.enabled }
          : setting
      )
    )
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'destructive'
      case 'high':
        return 'destructive'
      case 'medium':
        return 'secondary'
      case 'low':
        return 'outline'
      default:
        return 'outline'
    }
  }

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'login':
        return <UserCheck className="h-4 w-4 text-green-500" />
      case 'failed_login':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'permission_change':
        return <Key className="h-4 w-4 text-yellow-500" />
      case 'data_access':
        return <Eye className="h-4 w-4 text-blue-500" />
      case 'suspicious_activity':
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      default:
        return <Activity className="h-4 w-4 text-gray-500" />
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'authentication':
        return <UserCheck className="h-4 w-4" />
      case 'access_control':
        return <Lock className="h-4 w-4" />
      case 'monitoring':
        return <Eye className="h-4 w-4" />
      case 'encryption':
        return <Shield className="h-4 w-4" />
      default:
        return <Settings className="h-4 w-4" />
    }
  }

  // Security metrics
  const securityMetrics = {
    totalEvents: securityEvents.length,
    criticalEvents: securityEvents.filter(e => e.severity === 'critical').length,
    activeThreats: securityEvents.filter(e => e.severity === 'critical' || e.severity === 'high').length,
    securityScore: 85
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Security Center</h1>
          <p className="text-muted-foreground">
            Monitor security events and manage security settings
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="outline" className="text-xs">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </Badge>
          <Button onClick={refreshData} disabled={isLoading} size="sm">
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Security Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Security Score</CardTitle>
            <Shield className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{securityMetrics.securityScore}%</div>
            <p className="text-xs text-muted-foreground">Overall security rating</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Threats</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{securityMetrics.activeThreats}</div>
            <p className="text-xs text-muted-foreground">Requiring attention</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Security Events</CardTitle>
            <Activity className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{securityMetrics.totalEvents}</div>
            <p className="text-xs text-muted-foreground">Last 24 hours</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Critical Events</CardTitle>
            <XCircle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{securityMetrics.criticalEvents}</div>
            <p className="text-xs text-muted-foreground">Immediate action needed</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="events" className="space-y-4">
        <TabsList>
          <TabsTrigger value="events">Security Events</TabsTrigger>
          <TabsTrigger value="settings">Security Settings</TabsTrigger>
          <TabsTrigger value="policies">Access Policies</TabsTrigger>
          <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
        </TabsList>

        <TabsContent value="events" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Security Events</CardTitle>
              <CardDescription>
                Latest security events and activities across the system
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Event</TableHead>
                    <TableHead>User</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead>Time</TableHead>
                    <TableHead>Severity</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {securityEvents.map((event) => (
                    <TableRow key={event.id}>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          {getEventIcon(event.type)}
                          <div>
                            <div className="font-medium">{event.description}</div>
                            <div className="text-xs text-muted-foreground">IP: {event.ip}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="font-medium">{event.user}</TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <MapPin className="h-3 w-3" />
                          <span className="text-sm">{event.location}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <Clock className="h-3 w-3" />
                          <span className="text-sm">{event.timestamp.toLocaleTimeString()}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={getSeverityColor(event.severity)} className="capitalize">
                          {event.severity}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Security Configuration</CardTitle>
              <CardDescription>
                Manage security settings and policies
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {['authentication', 'access_control', 'monitoring', 'encryption'].map((category) => (
                  <div key={category}>
                    <h3 className="text-lg font-medium mb-3 capitalize flex items-center space-x-2">
                      {getCategoryIcon(category)}
                      <span>{category.replace('_', ' ')}</span>
                    </h3>
                    <div className="space-y-3">
                      {securitySettings
                        .filter(setting => setting.category === category)
                        .map((setting) => (
                          <div key={setting.id} className="flex items-center justify-between p-3 border rounded-lg">
                            <div className="space-y-1">
                              <Label htmlFor={setting.id} className="text-sm font-medium">
                                {setting.name}
                              </Label>
                              <p className="text-xs text-muted-foreground">
                                {setting.description}
                              </p>
                            </div>
                            <Switch
                              id={setting.id}
                              checked={setting.enabled}
                              onCheckedChange={() => toggleSetting(setting.id)}
                            />
                          </div>
                        ))}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="policies" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Access Control Policies</CardTitle>
              <CardDescription>
                Manage user access policies and permissions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <div className="text-sm font-medium">Password Policy</div>
                    <div className="text-xs text-muted-foreground">
                      Minimum 8 characters, uppercase, lowercase, numbers, and symbols required
                    </div>
                    <Badge variant="outline" className="text-xs">Active</Badge>
                  </div>
                  <div className="space-y-2">
                    <div className="text-sm font-medium">Session Management</div>
                    <div className="text-xs text-muted-foreground">
                      30-minute timeout for inactive sessions
                    </div>
                    <Badge variant="outline" className="text-xs">Active</Badge>
                  </div>
                  <div className="space-y-2">
                    <div className="text-sm font-medium">Role-Based Access</div>
                    <div className="text-xs text-muted-foreground">
                      Permissions based on user roles and responsibilities
                    </div>
                    <Badge variant="outline" className="text-xs">Active</Badge>
                  </div>
                  <div className="space-y-2">
                    <div className="text-sm font-medium">API Rate Limiting</div>
                    <div className="text-xs text-muted-foreground">
                      1000 requests per hour per user
                    </div>
                    <Badge variant="outline" className="text-xs">Active</Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="monitoring" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Security Monitoring</CardTitle>
              <CardDescription>
                Real-time security monitoring and threat detection
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <div className="text-sm font-medium">Intrusion Detection</div>
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-sm">Active</span>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Monitoring for suspicious activities
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="text-sm font-medium">Vulnerability Scanning</div>
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-sm">Active</span>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Daily automated security scans
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="text-sm font-medium">Threat Intelligence</div>
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-sm">Active</span>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Real-time threat feed integration
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
