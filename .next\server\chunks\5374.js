"use strict";exports.id=5374,exports.ids=[5374],exports.modules={32845:(e,t,a)=>{a.d(t,{R:()=>v});var l=a(95344),r=a(3729),i=a(60708),s=a(55697),n=a(12374),o=a(16212),d=a(92549),c=a(1586),u=a(16802),p=a(69436),h=a(14513),m=a(44669);let x=n.Ry({title:n.Z_().min(1,"Title is required"),description:n.Z_().optional(),customerId:n.Z_().min(1,"Customer is required"),quotationId:n.Z_().optional(),invoiceId:n.Z_().optional(),type:n.Km(["SERVICE","PRODUCT","SUBSCRIPTION","MAINTENANCE","CONSULTING","OTHER"]).default("SERVICE"),status:n.Km(["DRAFT","REVIEW","SENT","SIGNED","ACTIVE","COMPLETED","CANCELLED","EXPIRED"]).default("DRAFT"),value:n.Rx().min(0,"Contract value must be positive").optional(),currency:n.Z_().default("USD"),startDate:n.Z_().optional(),endDate:n.Z_().optional(),renewalDate:n.Z_().optional(),autoRenewal:n.O7().default(!1),renewalPeriod:n.Rx().optional(),terms:n.Z_().optional(),conditions:n.Z_().optional(),notes:n.Z_().optional(),templateId:n.Z_().optional(),signatureRequired:n.O7().default(!0),priority:n.Km(["LOW","MEDIUM","HIGH","URGENT"]).default("MEDIUM"),tags:n.IX(n.Z_()).optional().default([]),assignedToId:n.Z_().optional()});function v({isOpen:e,onClose:t,onSuccess:a,contract:n,mode:v,preselectedCustomerId:g,preselectedQuotationId:j,preselectedInvoiceId:y}){let[f,b]=(0,r.useState)(!1),[I,w]=(0,r.useState)([]),[N,D]=(0,r.useState)([]),[C,E]=(0,r.useState)([]),[S,R]=(0,r.useState)([]),[T,_]=(0,r.useState)([]),[k,Z]=(0,r.useState)(""),{register:A,handleSubmit:F,formState:{errors:q},reset:M,watch:U,setValue:P,getValues:O}=(0,i.cI)({resolver:(0,s.F)(x),defaultValues:n?{title:n.title,description:n.description||"",customerId:n.customerId||g||"",quotationId:n.quotationId||j||"",invoiceId:n.invoiceId||y||"",type:n.type||"SERVICE",status:n.status||"DRAFT",value:n.value||void 0,currency:n.currency||"USD",startDate:n.startDate?new Date(n.startDate).toISOString().split("T")[0]:"",endDate:n.endDate?new Date(n.endDate).toISOString().split("T")[0]:"",renewalDate:n.renewalDate?new Date(n.renewalDate).toISOString().split("T")[0]:"",autoRenewal:n.autoRenewal||!1,renewalPeriod:n.renewalPeriod||void 0,terms:n.terms||"",conditions:n.conditions||"",notes:n.notes||"",templateId:n.templateId||"",signatureRequired:!1!==n.signatureRequired,priority:n.priority||"MEDIUM",tags:n.tags||[],assignedToId:n.assignedToId||""}:{type:"SERVICE",status:"DRAFT",currency:"USD",autoRenewal:!1,signatureRequired:!0,priority:"MEDIUM",tags:[],customerId:g||"",quotationId:j||"",invoiceId:y||""}}),V=U("tags");(0,r.useEffect)(()=>{let t=async()=>{try{let[e,t,a]=await Promise.all([fetch("/api/customers?limit=100"),fetch("/api/quotations?limit=100"),fetch("/api/invoices?limit=100")]);if(e.ok){let t=await e.json();w(t.customers||[])}if(t.ok){let e=await t.json();D(e.quotations||[])}if(a.ok){let e=await a.json();E(e.invoices||[])}}catch(e){console.error("Error fetching data:",e)}};e&&t()},[e]);let L=()=>{k.trim()&&!V?.includes(k.trim())&&(P("tags",[...V||[],k.trim()]),Z(""))},G=e=>{P("tags",(V||[]).filter(t=>t!==e))},H=async e=>{b(!0);try{let l="create"===v?"/api/contracts":`/api/contracts/${n.id}`,r=await fetch(l,{method:"create"===v?"POST":"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!r.ok){let e=await r.json();throw Error(e.error||"Failed to save contract")}m.toast.success(`Contract ${"create"===v?"created":"updated"} successfully`),M(),a(),t()}catch(e){m.toast.error(e instanceof Error?e.message:"An error occurred")}finally{b(!1)}},z=()=>{M(),t()};return l.jsx(u.Vq,{open:e,onOpenChange:z,children:(0,l.jsxs)(u.cZ,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,l.jsxs)(u.fK,{children:[l.jsx(u.$N,{children:"create"===v?"Create New Contract":"Edit Contract"}),l.jsx(u.Be,{children:"create"===v?"Create a new contract with terms, conditions, and signature requirements.":"Update the contract information and settings."})]}),(0,l.jsxs)("form",{onSubmit:F(H),className:"space-y-6",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"md:col-span-2",children:[l.jsx(c._,{htmlFor:"title",children:"Contract Title *"}),l.jsx(d.I,{id:"title",...A("title"),placeholder:"Contract title"}),q.title&&l.jsx("p",{className:"text-sm text-red-600 mt-1",children:q.title.message})]}),(0,l.jsxs)("div",{children:[l.jsx(c._,{htmlFor:"customerId",children:"Customer *"}),(0,l.jsxs)("select",{id:"customerId",...A("customerId"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[l.jsx("option",{value:"",children:"Select a customer"}),I.map(e=>(0,l.jsxs)("option",{value:e.id,children:[e.name," ",e.company&&`(${e.company})`]},e.id))]}),q.customerId&&l.jsx("p",{className:"text-sm text-red-600 mt-1",children:q.customerId.message})]}),(0,l.jsxs)("div",{children:[l.jsx(c._,{htmlFor:"type",children:"Contract Type"}),l.jsx("select",{id:"type",...A("type"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[{value:"SERVICE",label:"Service Agreement"},{value:"PRODUCT",label:"Product Sale"},{value:"SUBSCRIPTION",label:"Subscription"},{value:"MAINTENANCE",label:"Maintenance"},{value:"CONSULTING",label:"Consulting"},{value:"OTHER",label:"Other"}].map(e=>l.jsx("option",{value:e.value,children:e.label},e.value))})]}),(0,l.jsxs)("div",{children:[l.jsx(c._,{htmlFor:"status",children:"Status"}),l.jsx("select",{id:"status",...A("status"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[{value:"DRAFT",label:"Draft"},{value:"REVIEW",label:"Under Review"},{value:"SENT",label:"Sent to Customer"},{value:"SIGNED",label:"Signed"},{value:"ACTIVE",label:"Active"},{value:"COMPLETED",label:"Completed"},{value:"CANCELLED",label:"Cancelled"},{value:"EXPIRED",label:"Expired"}].map(e=>l.jsx("option",{value:e.value,children:e.label},e.value))})]}),(0,l.jsxs)("div",{children:[l.jsx(c._,{htmlFor:"priority",children:"Priority"}),l.jsx("select",{id:"priority",...A("priority"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[{value:"LOW",label:"Low"},{value:"MEDIUM",label:"Medium"},{value:"HIGH",label:"High"},{value:"URGENT",label:"Urgent"}].map(e=>l.jsx("option",{value:e.value,children:e.label},e.value))})]}),(0,l.jsxs)("div",{children:[l.jsx(c._,{htmlFor:"quotationId",children:"Related Quotation"}),(0,l.jsxs)("select",{id:"quotationId",...A("quotationId"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[l.jsx("option",{value:"",children:"Select a quotation (optional)"}),N.map(e=>(0,l.jsxs)("option",{value:e.id,children:[e.quotationNumber," - ",e.title]},e.id))]})]}),(0,l.jsxs)("div",{children:[l.jsx(c._,{htmlFor:"invoiceId",children:"Related Invoice"}),(0,l.jsxs)("select",{id:"invoiceId",...A("invoiceId"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[l.jsx("option",{value:"",children:"Select an invoice (optional)"}),C.map(e=>l.jsx("option",{value:e.id,children:e.invoiceNumber},e.id))]})]}),(0,l.jsxs)("div",{children:[l.jsx(c._,{htmlFor:"assignedToId",children:"Assigned To"}),(0,l.jsxs)("select",{id:"assignedToId",...A("assignedToId"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[l.jsx("option",{value:"",children:"Select a user (optional)"}),S.map(e=>l.jsx("option",{value:e.id,children:e.name||e.email},e.id))]})]})]}),(0,l.jsxs)("div",{children:[l.jsx(c._,{htmlFor:"description",children:"Description"}),l.jsx("textarea",{id:"description",...A("description"),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Brief description of the contract..."})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,l.jsxs)("div",{children:[l.jsx(c._,{htmlFor:"value",children:"Contract Value"}),l.jsx(d.I,{id:"value",type:"number",min:"0",step:"0.01",...A("value",{valueAsNumber:!0}),placeholder:"0.00"})]}),(0,l.jsxs)("div",{children:[l.jsx(c._,{htmlFor:"currency",children:"Currency"}),l.jsx("select",{id:"currency",...A("currency"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[{value:"USD",label:"USD ($)"},{value:"EUR",label:"EUR (€)"},{value:"GBP",label:"GBP (\xa3)"},{value:"CAD",label:"CAD (C$)"},{value:"AUD",label:"AUD (A$)"}].map(e=>l.jsx("option",{value:e.value,children:e.label},e.value))})]}),(0,l.jsxs)("div",{children:[l.jsx(c._,{htmlFor:"renewalPeriod",children:"Renewal Period (months)"}),l.jsx(d.I,{id:"renewalPeriod",type:"number",min:"1",...A("renewalPeriod",{valueAsNumber:!0}),placeholder:"12"})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,l.jsxs)("div",{children:[l.jsx(c._,{htmlFor:"startDate",children:"Start Date"}),l.jsx(d.I,{id:"startDate",type:"date",...A("startDate")})]}),(0,l.jsxs)("div",{children:[l.jsx(c._,{htmlFor:"endDate",children:"End Date"}),l.jsx(d.I,{id:"endDate",type:"date",...A("endDate")})]}),(0,l.jsxs)("div",{children:[l.jsx(c._,{htmlFor:"renewalDate",children:"Renewal Date"}),l.jsx(d.I,{id:"renewalDate",type:"date",...A("renewalDate")})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[l.jsx("input",{type:"checkbox",id:"autoRenewal",...A("autoRenewal"),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),l.jsx(c._,{htmlFor:"autoRenewal",children:"Auto Renewal"})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[l.jsx("input",{type:"checkbox",id:"signatureRequired",...A("signatureRequired"),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),l.jsx(c._,{htmlFor:"signatureRequired",children:"Signature Required"})]})]}),(0,l.jsxs)("div",{children:[l.jsx(c._,{htmlFor:"tags",children:"Tags"}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("div",{className:"flex space-x-2",children:[l.jsx(d.I,{value:k,onChange:e=>Z(e.target.value),placeholder:"Add a tag...",onKeyPress:e=>{"Enter"===e.key&&(e.preventDefault(),L())}}),l.jsx(o.z,{type:"button",onClick:L,size:"sm",children:"Add"})]}),V&&V.length>0&&l.jsx("div",{className:"flex flex-wrap gap-2",children:V.map(e=>(0,l.jsxs)(p.C,{variant:"secondary",className:"flex items-center space-x-1",children:[l.jsx("span",{children:e}),l.jsx("button",{type:"button",onClick:()=>G(e),className:"ml-1 hover:text-red-600",children:l.jsx(h.Z,{className:"h-3 w-3"})})]},e))})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[l.jsx(c._,{htmlFor:"terms",children:"Terms & Conditions"}),l.jsx("textarea",{id:"terms",...A("terms"),rows:6,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Contract terms and conditions..."})]}),(0,l.jsxs)("div",{children:[l.jsx(c._,{htmlFor:"conditions",children:"Additional Conditions"}),l.jsx("textarea",{id:"conditions",...A("conditions"),rows:6,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Additional conditions and clauses..."})]})]}),(0,l.jsxs)("div",{children:[l.jsx(c._,{htmlFor:"notes",children:"Internal Notes"}),l.jsx("textarea",{id:"notes",...A("notes"),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Internal notes (not visible to customer)..."})]}),(0,l.jsxs)(u.cN,{children:[l.jsx(o.z,{type:"button",variant:"outline",onClick:z,children:"Cancel"}),l.jsx(o.z,{type:"submit",disabled:f,children:f?"Saving...":"create"===v?"Create Contract":"Update Contract"})]})]})]})})}},45961:(e,t,a)=>{a.d(t,{Z:()=>l});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,a(69224).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},7060:(e,t,a)=>{a.d(t,{Z:()=>l});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,a(69224).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1960:(e,t,a)=>{a.d(t,{Z:()=>l});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,a(69224).Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},96885:(e,t,a)=>{a.d(t,{Z:()=>l});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,a(69224).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},98989:(e,t,a)=>{a.d(t,{Z:()=>l});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,a(69224).Z)("FileSignature",[["path",{d:"M20 19.5v.5a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h8.5L18 5.5",key:"kd5d3"}],["path",{d:"M8 18h1",key:"13wk12"}],["path",{d:"M18.42 9.61a2.1 2.1 0 1 1 2.97 2.97L16.95 17 13 18l.99-3.95 4.43-4.44Z",key:"johvi5"}]])},66827:(e,t,a)=>{a.d(t,{Z:()=>l});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,a(69224).Z)("Repeat",[["path",{d:"m17 2 4 4-4 4",key:"nntrym"}],["path",{d:"M3 11v-1a4 4 0 0 1 4-4h14",key:"84bu3i"}],["path",{d:"m7 22-4-4 4-4",key:"1wqhfi"}],["path",{d:"M21 13v1a4 4 0 0 1-4 4H3",key:"1rx37r"}]])},36135:(e,t,a)=>{a.d(t,{Z:()=>l});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,a(69224).Z)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]])}};