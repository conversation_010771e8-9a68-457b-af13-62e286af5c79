import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const signatureSchema = z.object({
  signerName: z.string().min(1, 'Signer name is required'),
  signerEmail: z.string().email('Valid email is required'),
  signerRole: z.string().optional(),
  signatureType: z.enum(['ELECTRONIC', 'DIGITAL', 'WET_SIGNATURE']).default('ELECTRONIC'),
  signatureData: z.string().optional(), // Base64 encoded signature image
  ipAddress: z.string().optional(),
  userAgent: z.string().optional(),
  notes: z.string().optional()
})

// GET /api/contracts/[id]/signatures - Get signatures for contract
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify contract exists and belongs to user's company
    const contract = await prisma.contract.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId
      }
    })

    if (!contract) {
      return NextResponse.json({ error: 'Contract not found' }, { status: 404 })
    }

    // Get all signatures for this contract
    const signatures = await prisma.signature.findMany({
      where: {
        contractId: params.id
      },
      include: {
        signedBy: {
          select: {
            name: true,
            email: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    return NextResponse.json({
      signatures: signatures.map(signature => ({
        id: signature.id,
        signerName: signature.signerName,
        signerEmail: signature.signerEmail,
        signerRole: signature.signerRole,
        signatureType: signature.signatureType,
        signedAt: signature.signedAt,
        ipAddress: signature.ipAddress,
        userAgent: signature.userAgent,
        notes: signature.notes,
        signedBy: signature.signedBy,
        createdAt: signature.createdAt
      }))
    })

  } catch (error) {
    console.error('Error fetching signatures:', error)
    return NextResponse.json(
      { error: 'Failed to fetch signatures' },
      { status: 500 }
    )
  }
}

// POST /api/contracts/[id]/signatures - Record signature for contract
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = signatureSchema.parse(body)

    // Get contract with current signature status
    const contract = await prisma.contract.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId
      },
      include: {
        signatures: true
      }
    })

    if (!contract) {
      return NextResponse.json({ error: 'Contract not found' }, { status: 404 })
    }

    // Check if contract is in a signable state
    if (!['SENT', 'REVIEW'].includes(contract.status)) {
      return NextResponse.json(
        { error: 'Contract is not in a signable state' },
        { status: 400 }
      )
    }

    // Check if this email has already signed
    const existingSignature = contract.signatures.find(
      sig => sig.signerEmail.toLowerCase() === validatedData.signerEmail.toLowerCase()
    )

    if (existingSignature) {
      return NextResponse.json(
        { error: 'This email has already signed the contract' },
        { status: 400 }
      )
    }

    // Get client IP and user agent from headers
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown'
    const userAgent = request.headers.get('user-agent') || 'unknown'

    // Create signature record
    const signature = await prisma.$transaction(async (tx) => {
      // Create signature
      const newSignature = await tx.signature.create({
        data: {
          signerName: validatedData.signerName,
          signerEmail: validatedData.signerEmail,
          signerRole: validatedData.signerRole,
          signatureType: validatedData.signatureType,
          signatureData: validatedData.signatureData,
          signedAt: new Date(),
          ipAddress: validatedData.ipAddress || clientIP,
          userAgent: validatedData.userAgent || userAgent,
          notes: validatedData.notes,
          contractId: params.id,
          companyId: session.user.companyId!,
          signedById: session.user.id
        },
        include: {
          signedBy: {
            select: {
              name: true,
              email: true
            }
          }
        }
      })

      // Update contract status to SIGNED if signature is required and this completes it
      if (contract.signatureRequired) {
        await tx.contract.update({
          where: { id: params.id },
          data: { 
            status: 'SIGNED',
            signedAt: new Date()
          }
        })
      }

      // Log activity
      await tx.activity.create({
        data: {
          type: 'SIGNATURE',
          title: 'Contract Signed',
          description: `Contract ${contract.contractNumber} was signed by ${validatedData.signerName} (${validatedData.signerEmail})`,
          contractId: params.id,
          customerId: contract.customerId,
          companyId: session.user.companyId!,
          createdById: session.user.id
        }
      })

      return newSignature
    })

    return NextResponse.json({
      signature: {
        id: signature.id,
        signerName: signature.signerName,
        signerEmail: signature.signerEmail,
        signerRole: signature.signerRole,
        signatureType: signature.signatureType,
        signedAt: signature.signedAt,
        ipAddress: signature.ipAddress,
        userAgent: signature.userAgent,
        notes: signature.notes,
        signedBy: signature.signedBy,
        createdAt: signature.createdAt
      },
      message: 'Signature recorded successfully'
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error recording signature:', error)
    return NextResponse.json(
      { error: 'Failed to record signature' },
      { status: 500 }
    )
  }
}

// DELETE /api/contracts/[id]/signatures/[signatureId] - Delete signature
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const signatureId = searchParams.get('signatureId')

    if (!signatureId) {
      return NextResponse.json({ error: 'Signature ID is required' }, { status: 400 })
    }

    // Verify signature exists and belongs to the contract and company
    const signature = await prisma.signature.findFirst({
      where: {
        id: signatureId,
        contractId: params.id,
        companyId: session.user.companyId
      },
      include: {
        contract: true
      }
    })

    if (!signature) {
      return NextResponse.json({ error: 'Signature not found' }, { status: 404 })
    }

    // Check if contract allows signature deletion (only if not yet active)
    if (signature.contract.status === 'ACTIVE') {
      return NextResponse.json(
        { error: 'Cannot delete signatures from active contracts' },
        { status: 400 }
      )
    }

    await prisma.$transaction(async (tx) => {
      // Delete the signature
      await tx.signature.delete({
        where: { id: signatureId }
      })

      // Check if there are remaining signatures
      const remainingSignatures = await tx.signature.count({
        where: {
          contractId: params.id
        }
      })

      // Update contract status if no signatures remain
      if (remainingSignatures === 0 && signature.contract.signatureRequired) {
        await tx.contract.update({
          where: { id: params.id },
          data: {
            status: 'SENT', // Revert to sent status
            signedAt: null
          }
        })
      }

      // Log activity
      await tx.activity.create({
        data: {
          type: 'SIGNATURE',
          title: 'Signature Removed',
          description: `Signature by ${signature.signerName} (${signature.signerEmail}) was removed from contract ${signature.contract.contractNumber}`,
          contractId: params.id,
          customerId: signature.contract.customerId,
          companyId: session.user.companyId!,
          createdById: session.user.id
        }
      })
    })

    return NextResponse.json({ message: 'Signature deleted successfully' })

  } catch (error) {
    console.error('Error deleting signature:', error)
    return NextResponse.json(
      { error: 'Failed to delete signature' },
      { status: 500 }
    )
  }
}
