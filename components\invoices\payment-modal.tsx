'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { CreditCard, DollarSign, FileText, User, Trash2, Calendar } from 'lucide-react'
import { toast } from 'react-hot-toast'

interface Invoice {
  id: string
  invoiceNumber: string
  title: string | null
  total: number
  paidAmount: number
  status: string
  customer: {
    id: string
    name: string
    company: string | null
  }
}

interface Payment {
  id: string
  amount: number
  paymentDate: string
  paymentMethod: string
  reference: string | null
  notes: string | null
  createdBy: {
    name: string | null
    email: string | null
  }
  createdAt: string
}

interface PaymentModalProps {
  open: boolean
  invoice: Invoice | null
  onClose: () => void
  onSuccess: () => void
}

export function PaymentModal({ open, invoice, onClose, onSuccess }: PaymentModalProps) {
  const [payments, setPayments] = useState<Payment[]>([])
  const [loading, setLoading] = useState(false)
  const [submitting, setSubmitting] = useState(false)
  const [formData, setFormData] = useState({
    amount: '',
    paymentDate: new Date().toISOString().split('T')[0],
    paymentMethod: 'CASH',
    reference: '',
    notes: ''
  })

  const fetchPayments = async () => {
    if (!invoice) return

    try {
      setLoading(true)
      const response = await fetch(`/api/invoices/${invoice.id}/payments`)
      if (!response.ok) {
        throw new Error('Failed to fetch payments')
      }

      const data = await response.json()
      setPayments(data.payments)
    } catch (error) {
      toast.error('Failed to load payments')
      console.error('Error fetching payments:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (open && invoice) {
      fetchPayments()
      // Set default amount to remaining balance
      const remainingBalance = invoice.total - invoice.paidAmount
      setFormData(prev => ({
        ...prev,
        amount: remainingBalance > 0 ? remainingBalance.toString() : ''
      }))
    }
  }, [open, invoice])

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!invoice) return

    const amount = parseFloat(formData.amount)
    if (isNaN(amount) || amount <= 0) {
      toast.error('Please enter a valid payment amount')
      return
    }

    const remainingBalance = invoice.total - invoice.paidAmount
    if (amount > remainingBalance) {
      toast.error(`Payment amount cannot exceed remaining balance of $${remainingBalance.toFixed(2)}`)
      return
    }

    setSubmitting(true)
    try {
      const response = await fetch(`/api/invoices/${invoice.id}/payments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount,
          paymentDate: formData.paymentDate,
          paymentMethod: formData.paymentMethod,
          reference: formData.reference || undefined,
          notes: formData.notes || undefined
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to record payment')
      }

      toast.success('Payment recorded successfully!')
      
      // Reset form
      setFormData({
        amount: '',
        paymentDate: new Date().toISOString().split('T')[0],
        paymentMethod: 'CASH',
        reference: '',
        notes: ''
      })

      // Refresh payments and notify parent
      await fetchPayments()
      onSuccess()
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to record payment')
    } finally {
      setSubmitting(false)
    }
  }

  const handleDeletePayment = async (paymentId: string) => {
    if (!invoice) return

    if (!confirm('Are you sure you want to delete this payment?')) {
      return
    }

    try {
      const response = await fetch(`/api/invoices/${invoice.id}/payments?paymentId=${paymentId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to delete payment')
      }

      toast.success('Payment deleted successfully!')
      await fetchPayments()
      onSuccess()
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to delete payment')
    }
  }

  const handleClose = () => {
    setFormData({
      amount: '',
      paymentDate: new Date().toISOString().split('T')[0],
      paymentMethod: 'CASH',
      reference: '',
      notes: ''
    })
    setPayments([])
    onClose()
  }

  if (!invoice) return null

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DRAFT':
        return 'bg-gray-100 text-gray-800'
      case 'SENT':
        return 'bg-blue-100 text-blue-800'
      case 'PAID':
        return 'bg-green-100 text-green-800'
      case 'OVERDUE':
        return 'bg-red-100 text-red-800'
      case 'CANCELLED':
        return 'bg-orange-100 text-orange-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const remainingBalance = invoice.total - invoice.paidAmount

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <CreditCard className="h-5 w-5 mr-2" />
            Payment Management
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Invoice Summary */}
          <div className="p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-2">
                <FileText className="h-5 w-5 text-blue-600" />
                <span className="font-semibold">{invoice.invoiceNumber}</span>
                <Badge className={getStatusColor(invoice.status)}>
                  {invoice.status}
                </Badge>
              </div>
              <span className="text-lg font-bold text-green-600">
                {formatCurrency(invoice.total)}
              </span>
            </div>
            <div className="text-sm text-gray-600">
              <p className="font-medium">{invoice.title || 'No title'}</p>
              <div className="flex items-center mt-1">
                <User className="h-4 w-4 mr-1" />
                <span>{invoice.customer.name}</span>
                {invoice.customer.company && (
                  <span className="ml-1">({invoice.customer.company})</span>
                )}
              </div>
            </div>
            <div className="mt-3 grid grid-cols-3 gap-4 text-sm">
              <div>
                <span className="text-gray-500">Total:</span>
                <div className="font-semibold">{formatCurrency(invoice.total)}</div>
              </div>
              <div>
                <span className="text-gray-500">Paid:</span>
                <div className="font-semibold text-green-600">{formatCurrency(invoice.paidAmount)}</div>
              </div>
              <div>
                <span className="text-gray-500">Balance:</span>
                <div className={`font-semibold ${remainingBalance > 0 ? 'text-red-600' : 'text-green-600'}`}>
                  {formatCurrency(remainingBalance)}
                </div>
              </div>
            </div>
          </div>

          {/* Record New Payment */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Record New Payment</h3>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="amount">Payment Amount *</Label>
                  <Input
                    id="amount"
                    type="number"
                    step="0.01"
                    min="0.01"
                    max={remainingBalance}
                    value={formData.amount}
                    onChange={(e) => handleInputChange('amount', e.target.value)}
                    placeholder="0.00"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="paymentDate">Payment Date *</Label>
                  <Input
                    id="paymentDate"
                    type="date"
                    value={formData.paymentDate}
                    onChange={(e) => handleInputChange('paymentDate', e.target.value)}
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="paymentMethod">Payment Method *</Label>
                  <Select value={formData.paymentMethod} onValueChange={(value) => handleInputChange('paymentMethod', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="CASH">Cash</SelectItem>
                      <SelectItem value="CHECK">Check</SelectItem>
                      <SelectItem value="CREDIT_CARD">Credit Card</SelectItem>
                      <SelectItem value="BANK_TRANSFER">Bank Transfer</SelectItem>
                      <SelectItem value="PAYPAL">PayPal</SelectItem>
                      <SelectItem value="OTHER">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="reference">Reference (optional)</Label>
                  <Input
                    id="reference"
                    value={formData.reference}
                    onChange={(e) => handleInputChange('reference', e.target.value)}
                    placeholder="Check #, Transaction ID, etc."
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="notes">Notes (optional)</Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  placeholder="Additional notes about this payment..."
                  rows={3}
                />
              </div>

              <Button type="submit" disabled={submitting || remainingBalance <= 0}>
                {submitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Recording...
                  </>
                ) : (
                  <>
                    <DollarSign className="h-4 w-4 mr-2" />
                    Record Payment
                  </>
                )}
              </Button>
            </form>
          </div>

          <Separator />

          {/* Payment History */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Payment History</h3>
            {loading ? (
              <div className="flex items-center justify-center py-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              </div>
            ) : payments.length === 0 ? (
              <p className="text-gray-500 text-center py-4">No payments recorded</p>
            ) : (
              <div className="space-y-3">
                {payments.map((payment) => (
                  <div key={payment.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <div className="flex items-center space-x-2">
                        <span className="font-semibold">{formatCurrency(payment.amount)}</span>
                        <Badge variant="outline">{payment.paymentMethod}</Badge>
                      </div>
                      <div className="text-sm text-gray-600">
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-3 w-3" />
                          <span>{new Date(payment.paymentDate).toLocaleDateString()}</span>
                        </div>
                        {payment.reference && (
                          <div>Reference: {payment.reference}</div>
                        )}
                        {payment.notes && (
                          <div>Notes: {payment.notes}</div>
                        )}
                        <div className="text-xs text-gray-500 mt-1">
                          Recorded by {payment.createdBy.name || payment.createdBy.email} on{' '}
                          {new Date(payment.createdAt).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeletePayment(payment.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex justify-end pt-4 border-t">
            <Button variant="outline" onClick={handleClose}>
              Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
