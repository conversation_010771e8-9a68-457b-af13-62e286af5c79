"use strict";(()=>{var e={};e.id=9252,e.ids=[9252],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},51692:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>E,originalPathname:()=>L,patchFetch:()=>v,requestAsyncStorage:()=>g,routeModule:()=>w,serverHooks:()=>h,staticGenerationAsyncStorage:()=>f,staticGenerationBailout:()=>Z});var r={};a.r(r),a.d(r,{DELETE:()=>_,GET:()=>y,PUT:()=>I});var s=a(95419),o=a(69108),i=a(99678),n=a(78070),l=a(81355),d=a(3205),u=a(9108),c=a(25252),p=a(52178);let m=c.Ry({firstName:c.Z_().min(1,"First name is required").optional(),lastName:c.Z_().min(1,"Last name is required").optional(),email:c.Z_().email("Invalid email address").optional(),phone:c.Z_().optional().nullable(),companyName:c.Z_().optional().nullable(),title:c.Z_().optional().nullable(),website:c.Z_().url().optional().nullable(),source:c.Km(["WEBSITE","REFERRAL","SOCIAL_MEDIA","EMAIL_CAMPAIGN","COLD_CALL","TRADE_SHOW","PARTNER","OTHER"]).optional(),status:c.Km(["NEW","CONTACTED","QUALIFIED","PROPOSAL","NEGOTIATION","CLOSED_WON","CLOSED_LOST"]).optional(),priority:c.Km(["LOW","MEDIUM","HIGH","URGENT"]).optional(),address:c.Z_().optional().nullable(),city:c.Z_().optional().nullable(),state:c.Z_().optional().nullable(),country:c.Z_().optional().nullable(),postalCode:c.Z_().optional().nullable(),industry:c.Z_().optional().nullable(),companySize:c.Z_().optional().nullable(),budget:c.Rx().min(0).optional().nullable(),timeline:c.Z_().optional().nullable(),description:c.Z_().optional().nullable()});async function y(e,{params:t}){try{let e=await (0,l.getServerSession)(d.L);if(!e?.user?.id)return n.Z.json({error:"Unauthorized"},{status:401});let a=await u._.lead.findFirst({where:{id:t.id,companyId:e.user.companyId||void 0},include:{customer:{select:{id:!0,name:!0,companyName:!0}},assignedTo:{select:{id:!0,name:!0,email:!0}},activities:{orderBy:{createdAt:"desc"},take:10,include:{createdBy:{select:{name:!0}}}},_count:{select:{activities:!0,leadNotes:!0,tasks:!0,documents:!0}}}});if(!a)return n.Z.json({error:"Lead not found"},{status:404});return n.Z.json(a)}catch(e){return console.error("Error fetching lead:",e),n.Z.json({error:"Failed to fetch lead"},{status:500})}}async function I(e,{params:t}){try{let a=await (0,l.getServerSession)(d.L);if(!a?.user?.id)return n.Z.json({error:"Unauthorized"},{status:401});let r=await e.json(),s=m.parse(r),o=await u._.lead.findFirst({where:{id:t.id,companyId:a.user.companyId||void 0}});if(!o)return n.Z.json({error:"Lead not found"},{status:404});let i=await u._.lead.update({where:{id:t.id},data:{...s,updatedAt:new Date},include:{customer:{select:{id:!0,name:!0,companyName:!0}},assignedTo:{select:{id:!0,name:!0,email:!0}},_count:{select:{activities:!0,leadNotes:!0,tasks:!0,documents:!0}}}});return s.status&&s.status!==o.status&&await u._.activity.create({data:{type:"STATUS_CHANGE",title:"Lead Status Updated",description:`Lead status changed from ${o.status} to ${s.status}`,leadId:i.id,customerId:i.customerId,companyId:a.user.companyId,createdById:a.user.id}}),n.Z.json(i)}catch(e){if(e instanceof p.jm)return n.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("Error updating lead:",e),n.Z.json({error:"Failed to update lead"},{status:500})}}async function _(e,{params:t}){try{let e=await (0,l.getServerSession)(d.L);if(!e?.user?.id)return n.Z.json({error:"Unauthorized"},{status:401});let a=await u._.lead.findFirst({where:{id:t.id,companyId:e.user.companyId||void 0},include:{_count:{select:{activities:!0,leadNotes:!0,tasks:!0,documents:!0}}}});if(!a)return n.Z.json({error:"Lead not found"},{status:404});if(a._count.activities>0||a._count.tasks>0)return n.Z.json({error:"Cannot delete lead with existing activities or tasks",details:a._count},{status:400});return await u._.lead.delete({where:{id:t.id}}),n.Z.json({message:"Lead deleted successfully"})}catch(e){return console.error("Error deleting lead:",e),n.Z.json({error:"Failed to delete lead"},{status:500})}}let w=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/leads/[id]/route",pathname:"/api/leads/[id]",filename:"route",bundlePath:"app/api/leads/[id]/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\leads\\[id]\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:g,staticGenerationAsyncStorage:f,serverHooks:h,headerHooks:E,staticGenerationBailout:Z}=w,L="/api/leads/[id]/route";function v(){return(0,i.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:f})}},3205:(e,t,a)=>{a.d(t,{L:()=>d});var r=a(86485),s=a(10375),o=a(50694),i=a(6521),n=a.n(i),l=a(9108);let d={providers:[(0,r.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await l._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),a=t?.companyId;if(!a&&t){let e=await l._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(a=e?.id)&&await l._.user.update({where:{id:t.id},data:{companyId:a}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await n().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await l._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:a}}catch(e){return console.error("Authentication error:",e),null}}}),(0,s.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,o.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,a)=>{a.d(t,{_:()=>s});let r=require("@prisma/client"),s=globalThis.prisma??new r.PrismaClient}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[1638,6206,6521,2455,4520,5252],()=>a(51692));module.exports=r})();