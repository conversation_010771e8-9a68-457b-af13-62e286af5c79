import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '30' // days
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - parseInt(period))

    // Get comprehensive financial accounts analytics
    const [
      totalAccounts,
      accountsByType,
      accountBalances,
      transactionMetrics,
      budgetAnalysis,
      recentTransactions,
      topAccountsByBalance,
      cashFlowAnalysis,
      profitLossAnalysis,
      balanceSheetData,
      accountActivity,
      reconciliationStatus
    ] = await Promise.all([
      // Total accounts
      prisma.financialAccount.count({
        where: {
          companyId: session.user.companyId,
          isActive: true
        }
      }),

      // Accounts by type
      prisma.financialAccount.groupBy({
        by: ['accountType'],
        where: {
          companyId: session.user.companyId,
          isActive: true
        },
        _count: {
          id: true
        },
        _sum: {
          balance: true
        }
      }),

      // Account balances summary
      Promise.all([
        // Total assets
        prisma.financialAccount.aggregate({
          where: {
            companyId: session.user.companyId,
            accountType: 'ASSET',
            isActive: true
          },
          _sum: {
            balance: true
          }
        }),
        // Total liabilities
        prisma.financialAccount.aggregate({
          where: {
            companyId: session.user.companyId,
            accountType: 'LIABILITY',
            isActive: true
          },
          _sum: {
            balance: true
          }
        }),
        // Total equity
        prisma.financialAccount.aggregate({
          where: {
            companyId: session.user.companyId,
            accountType: 'EQUITY',
            isActive: true
          },
          _sum: {
            balance: true
          }
        }),
        // Total revenue
        prisma.financialAccount.aggregate({
          where: {
            companyId: session.user.companyId,
            accountType: 'REVENUE',
            isActive: true
          },
          _sum: {
            balance: true
          }
        }),
        // Total expenses
        prisma.financialAccount.aggregate({
          where: {
            companyId: session.user.companyId,
            accountType: 'EXPENSE',
            isActive: true
          },
          _sum: {
            balance: true
          }
        })
      ]),

      // Transaction metrics
      Promise.all([
        // Total transactions
        prisma.accountTransaction.count({
          where: {
            companyId: session.user.companyId,
            createdAt: { gte: startDate }
          }
        }),
        // Completed transactions
        prisma.accountTransaction.count({
          where: {
            companyId: session.user.companyId,
            status: 'COMPLETED',
            createdAt: { gte: startDate }
          }
        }),
        // Pending transactions
        prisma.accountTransaction.count({
          where: {
            companyId: session.user.companyId,
            status: 'PENDING'
          }
        }),
        // Total transaction amount
        prisma.accountTransaction.aggregate({
          where: {
            companyId: session.user.companyId,
            status: 'COMPLETED',
            createdAt: { gte: startDate }
          },
          _sum: {
            amount: true
          }
        })
      ]),

      // Budget analysis
      Promise.all([
        // Active budgets
        prisma.budget.count({
          where: {
            companyId: session.user.companyId,
            status: 'ACTIVE'
          }
        }),
        // Budget vs actual
        prisma.budget.aggregate({
          where: {
            companyId: session.user.companyId,
            status: 'ACTIVE'
          },
          _sum: {
            budgetedAmount: true,
            actualAmount: true,
            variance: true
          }
        }),
        // Over budget count
        prisma.budget.count({
          where: {
            companyId: session.user.companyId,
            status: 'ACTIVE',
            variance: { lt: 0 }
          }
        })
      ]),

      // Recent transactions
      prisma.accountTransaction.findMany({
        where: {
          companyId: session.user.companyId,
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
          }
        },
        include: {
          debitAccount: {
            select: {
              name: true,
              accountType: true
            }
          },
          creditAccount: {
            select: {
              name: true,
              accountType: true
            }
          },
          customer: {
            select: {
              name: true,
              companyName: true
            }
          },
          createdBy: {
            select: {
              name: true,
              email: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: 10
      }),

      // Top accounts by balance
      prisma.financialAccount.findMany({
        where: {
          companyId: session.user.companyId,
          isActive: true
        },
        select: {
          id: true,
          name: true,
          accountType: true,
          balance: true,
          currency: true,
          accountNumber: true
        },
        orderBy: { balance: 'desc' },
        take: 10
      }),

      // Cash flow analysis (last 12 months)
      prisma.$queryRaw`
        SELECT 
          DATE_TRUNC('month', "transactionDate") as month,
          SUM(CASE WHEN fa."accountType" = 'ASSET' THEN at.amount ELSE 0 END) as cash_inflow,
          SUM(CASE WHEN fa."accountType" = 'EXPENSE' THEN at.amount ELSE 0 END) as cash_outflow,
          COUNT(at.id) as transaction_count
        FROM "account_transactions" at
        JOIN "financial_accounts" fa ON at."debitAccountId" = fa.id
        WHERE at."companyId" = ${session.user.companyId}
          AND at.status = 'COMPLETED'
          AND at."transactionDate" >= ${new Date(Date.now() - 365 * 24 * 60 * 60 * 1000)}
        GROUP BY DATE_TRUNC('month', "transactionDate")
        ORDER BY month DESC
        LIMIT 12
      `,

      // Profit & Loss analysis
      Promise.all([
        // Revenue accounts total
        prisma.$queryRaw`
          SELECT SUM(at.amount) as total_revenue
          FROM "account_transactions" at
          JOIN "financial_accounts" fa ON at."creditAccountId" = fa.id
          WHERE at."companyId" = ${session.user.companyId}
            AND fa."accountType" = 'REVENUE'
            AND at.status = 'COMPLETED'
            AND at."transactionDate" >= ${startDate}
        `,
        // Expense accounts total
        prisma.$queryRaw`
          SELECT SUM(at.amount) as total_expenses
          FROM "account_transactions" at
          JOIN "financial_accounts" fa ON at."debitAccountId" = fa.id
          WHERE at."companyId" = ${session.user.companyId}
            AND fa."accountType" = 'EXPENSE'
            AND at.status = 'COMPLETED'
            AND at."transactionDate" >= ${startDate}
        `
      ]),

      // Balance sheet data
      Promise.all([
        // Current assets
        prisma.financialAccount.aggregate({
          where: {
            companyId: session.user.companyId,
            accountType: 'ASSET',
            accountSubType: { in: ['CURRENT_ASSET', 'CASH', 'ACCOUNTS_RECEIVABLE'] },
            isActive: true
          },
          _sum: {
            balance: true
          }
        }),
        // Fixed assets
        prisma.financialAccount.aggregate({
          where: {
            companyId: session.user.companyId,
            accountType: 'ASSET',
            accountSubType: { in: ['FIXED_ASSET', 'PROPERTY', 'EQUIPMENT'] },
            isActive: true
          },
          _sum: {
            balance: true
          }
        }),
        // Current liabilities
        prisma.financialAccount.aggregate({
          where: {
            companyId: session.user.companyId,
            accountType: 'LIABILITY',
            accountSubType: { in: ['CURRENT_LIABILITY', 'ACCOUNTS_PAYABLE'] },
            isActive: true
          },
          _sum: {
            balance: true
          }
        }),
        // Long-term liabilities
        prisma.financialAccount.aggregate({
          where: {
            companyId: session.user.companyId,
            accountType: 'LIABILITY',
            accountSubType: { in: ['LONG_TERM_LIABILITY', 'LOANS'] },
            isActive: true
          },
          _sum: {
            balance: true
          }
        })
      ]),

      // Account activity (transactions per account)
      prisma.$queryRaw`
        SELECT 
          fa.id,
          fa.name,
          fa."accountType",
          COUNT(at.id) as transaction_count,
          SUM(at.amount) as total_amount,
          MAX(at."transactionDate") as last_transaction_date
        FROM "financial_accounts" fa
        LEFT JOIN "account_transactions" at ON (fa.id = at."debitAccountId" OR fa.id = at."creditAccountId")
        WHERE fa."companyId" = ${session.user.companyId}
          AND fa."isActive" = true
          AND (at."transactionDate" >= ${startDate} OR at.id IS NULL)
        GROUP BY fa.id, fa.name, fa."accountType"
        ORDER BY transaction_count DESC
        LIMIT 10
      `,

      // Reconciliation status
      Promise.all([
        // Reconciled transactions
        prisma.accountTransaction.count({
          where: {
            companyId: session.user.companyId,
            isReconciled: true,
            createdAt: { gte: startDate }
          }
        }),
        // Unreconciled transactions
        prisma.accountTransaction.count({
          where: {
            companyId: session.user.companyId,
            isReconciled: false,
            status: 'COMPLETED'
          }
        })
      ])
    ])

    // Process metrics
    const [totalTransactions, completedTransactions, pendingTransactions, totalTransactionAmount] = transactionMetrics
    const [activeBudgets, budgetTotals, overBudgetCount] = budgetAnalysis
    const [totalAssets, totalLiabilities, totalEquity, totalRevenue, totalExpenses] = accountBalances
    const [revenueResult, expensesResult] = profitLossAnalysis
    const [currentAssets, fixedAssets, currentLiabilities, longTermLiabilities] = balanceSheetData
    const [reconciledCount, unreconciledCount] = reconciliationStatus

    const totalRevenueAmount = Number(revenueResult[0]?.total_revenue || 0)
    const totalExpensesAmount = Number(expensesResult[0]?.total_expenses || 0)
    const netIncome = totalRevenueAmount - totalExpensesAmount

    return NextResponse.json({
      summary: {
        totalAccounts,
        totalAssets: Number(totalAssets._sum.balance || 0),
        totalLiabilities: Number(totalLiabilities._sum.balance || 0),
        totalEquity: Number(totalEquity._sum.balance || 0),
        totalRevenue: Number(totalRevenue._sum.balance || 0),
        totalExpenses: Number(totalExpenses._sum.balance || 0),
        netIncome,
        totalTransactions,
        completedTransactions,
        pendingTransactions,
        totalTransactionAmount: Number(totalTransactionAmount._sum.amount || 0),
        activeBudgets,
        budgetedAmount: Number(budgetTotals._sum.budgetedAmount || 0),
        actualAmount: Number(budgetTotals._sum.actualAmount || 0),
        budgetVariance: Number(budgetTotals._sum.variance || 0),
        overBudgetCount,
        reconciledCount,
        unreconciledCount
      },
      accountsByType: accountsByType.map(item => ({
        accountType: item.accountType,
        count: item._count.id,
        totalBalance: Number(item._sum.balance || 0)
      })),
      balanceSheet: {
        assets: {
          current: Number(currentAssets._sum.balance || 0),
          fixed: Number(fixedAssets._sum.balance || 0),
          total: Number(totalAssets._sum.balance || 0)
        },
        liabilities: {
          current: Number(currentLiabilities._sum.balance || 0),
          longTerm: Number(longTermLiabilities._sum.balance || 0),
          total: Number(totalLiabilities._sum.balance || 0)
        },
        equity: {
          total: Number(totalEquity._sum.balance || 0)
        }
      },
      profitLoss: {
        revenue: totalRevenueAmount,
        expenses: totalExpensesAmount,
        netIncome,
        profitMargin: totalRevenueAmount > 0 ? (netIncome / totalRevenueAmount) * 100 : 0
      },
      cashFlow: cashFlowAnalysis,
      topAccounts: topAccountsByBalance.map(account => ({
        id: account.id,
        name: account.name,
        accountType: account.accountType,
        balance: Number(account.balance),
        currency: account.currency,
        accountNumber: account.accountNumber
      })),
      recentTransactions: recentTransactions.map(transaction => ({
        id: transaction.id,
        transactionNumber: transaction.transactionNumber,
        description: transaction.description,
        amount: Number(transaction.amount),
        currency: transaction.currency,
        transactionDate: transaction.transactionDate,
        transactionType: transaction.transactionType,
        status: transaction.status,
        debitAccount: transaction.debitAccount,
        creditAccount: transaction.creditAccount,
        customer: transaction.customer,
        createdBy: transaction.createdBy,
        createdAt: transaction.createdAt
      })),
      accountActivity: (accountActivity as any[]).map(activity => ({
        id: activity.id,
        name: activity.name,
        accountType: activity.accountType,
        transactionCount: Number(activity.transaction_count),
        totalAmount: Number(activity.total_amount || 0),
        lastTransactionDate: activity.last_transaction_date
      })),
      period: parseInt(period)
    })

  } catch (error) {
    console.error('Error fetching accounts analytics:', error)
    return NextResponse.json(
      { error: 'Failed to fetch accounts analytics' },
      { status: 500 }
    )
  }
}
