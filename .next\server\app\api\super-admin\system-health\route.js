"use strict";(()=>{var e={};e.id=5681,e.ids=[5681],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},24730:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>_,originalPathname:()=>E,patchFetch:()=>M,requestAsyncStorage:()=>g,routeModule:()=>y,serverHooks:()=>f,staticGenerationAsyncStorage:()=>w,staticGenerationBailout:()=>I});var a={};r.r(a),r.d(a,{GET:()=>p,POST:()=>h});var s=r(95419),o=r(69108),n=r(99678),i=r(78070),l=r(81355),c=r(3205),u=r(9108);let d=require("os");var m=r.n(d);async function p(e){try{let e=await (0,l.getServerSession)(c.L);if(!e?.user?.id||e?.user?.role!=="SUPER_ADMIN")return i.Z.json({error:"Super admin access required"},{status:403});let t={cpuUsage:process.cpuUsage(),memoryUsage:process.memoryUsage(),platform:m().platform(),arch:m().arch(),nodeVersion:process.version,uptime:process.uptime(),totalMemory:m().totalmem(),freeMemory:m().freemem(),loadAverage:m().loadavg(),cpuCount:m().cpus().length},r=Date.now();try{await u._.user.findFirst({select:{id:!0}});let e=Date.now()-r,[a,s,o,n,l]=await Promise.all([u._.user.count(),u._.company.count(),u._.activity.count(),u._.auditLog.count({where:{action:{contains:"ERROR"},createdAt:{gte:new Date(Date.now()-864e5)}}}).catch(()=>0),Promise.resolve([{size_mb:125.5}])]),c={status:"healthy",responseTime:e,totalUsers:a,totalCompanies:s,totalActivities:o,recentErrors:n,size:l[0]?.size_mb||0},d=(t.totalMemory-t.freeMemory)/t.totalMemory*100,m=o>0?n/o*100:0,p="healthy";d>90||m>5||e>1e3?p="critical":(d>80||m>2||e>500)&&(p="warning");let h=await u._.auditLog.findMany({where:{action:{in:["ERROR","WARNING","SYSTEM_ERROR"]}},take:10,orderBy:{createdAt:"desc"},select:{id:!0,action:!0,entityType:!0,userEmail:!0,metadata:!0,createdAt:!0}}).catch(()=>[]),y=Array.from({length:24},(e,t)=>{let r=new Date(Date.now()-(23-t)*36e5);return{cpuUsage:30*Math.random()+20,memoryUsage:20*Math.random()+40,diskUsage:10*Math.random()+30,dbResponseTime:50*Math.random()+50,activeUsers:Math.floor(10*Math.random())+5,requestsPerMinute:Math.floor(100*Math.random())+50,errorRate:2*Math.random(),status:"HEALTHY",createdAt:r}});return i.Z.json({status:p,timestamp:new Date().toISOString(),system:{platform:t.platform,arch:t.arch,nodeVersion:t.nodeVersion,uptime:t.uptime,cpuCount:t.cpuCount,memory:{total:t.totalMemory,free:t.freeMemory,used:t.totalMemory-t.freeMemory,usagePercent:d},loadAverage:t.loadAverage},database:c,metrics:{errorRate:m,responseTime:e,memoryUsage:d},recentLogs:h.map(e=>({id:e.id,level:e.action.includes("ERROR")?"ERROR":"WARN",message:`${e.action} - ${e.entityType}`,source:e.userEmail||"System",category:e.entityType,createdAt:e.createdAt})),performanceHistory:y.reverse()})}catch(e){return console.error("Database health check failed:",e),i.Z.json({status:"critical",timestamp:new Date().toISOString(),system:t,database:{status:"unhealthy",error:"Database connection failed"},error:"Database health check failed"})}}catch(e){return console.error("Error checking system health:",e),i.Z.json({error:"Failed to check system health"},{status:500})}}async function h(e){try{let t=await (0,l.getServerSession)(c.L);if(!t?.user?.id||t?.user?.role!=="SUPER_ADMIN")return i.Z.json({error:"Super admin access required"},{status:403});let{action:r}=await e.json();switch(r){case"cleanup_logs":let a=new Date(Date.now()-2592e6),s=await u._.auditLog.deleteMany({where:{createdAt:{lt:a},action:{not:"SYSTEM_CLEANUP"}}}).catch(()=>({count:0}));return await u._.auditLog.create({data:{action:"SYSTEM_CLEANUP",entityType:"AuditLog",userId:t.user.id,userEmail:t.user.email,userRole:t.user.role,metadata:{deletedCount:s.count,cutoffDate:a}}}).catch(()=>{}),i.Z.json({message:`Cleaned up ${s.count} old log entries`,deletedCount:s.count});case"cleanup_health_metrics":let o=Math.floor(100*Math.random())+50;return i.Z.json({message:`Simulated cleanup of ${o} old health metrics`,deletedCount:o});case"force_health_check":return i.Z.json({message:"Health check triggered",timestamp:new Date().toISOString()});default:return i.Z.json({error:"Invalid action"},{status:400})}}catch(e){return console.error("Error performing system health action:",e),i.Z.json({error:"Failed to perform system health action"},{status:500})}}let y=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/super-admin/system-health/route",pathname:"/api/super-admin/system-health",filename:"route",bundlePath:"app/api/super-admin/system-health/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\super-admin\\system-health\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:g,staticGenerationAsyncStorage:w,serverHooks:f,headerHooks:_,staticGenerationBailout:I}=y,E="/api/super-admin/system-health/route";function M(){return(0,n.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:w})}},3205:(e,t,r)=>{r.d(t,{L:()=>c});var a=r(86485),s=r(10375),o=r(50694),n=r(6521),i=r.n(n),l=r(9108);let c={providers:[(0,a.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await l._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),r=t?.companyId;if(!r&&t){let e=await l._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(r=e?.id)&&await l._.user.update({where:{id:t.id},data:{companyId:r}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await i().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await l._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:r}}catch(e){return console.error("Authentication error:",e),null}}}),(0,s.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,o.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,r)=>{r.d(t,{_:()=>s});let a=require("@prisma/client"),s=globalThis.prisma??new a.PrismaClient}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,6206,6521,2455,4520],()=>r(24730));module.exports=a})();