"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/super-admin/system-health/route";
exports.ids = ["app/api/super-admin/system-health/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("os");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Fsystem-health%2Froute&page=%2Fapi%2Fsuper-admin%2Fsystem-health%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Fsystem-health%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Fsystem-health%2Froute&page=%2Fapi%2Fsuper-admin%2Fsystem-health%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Fsystem-health%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_proj_nextjs_saas_app_api_super_admin_system_health_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/super-admin/system-health/route.ts */ \"(rsc)/./app/api/super-admin/system-health/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/super-admin/system-health/route\",\n        pathname: \"/api/super-admin/system-health\",\n        filename: \"route\",\n        bundlePath: \"app/api/super-admin/system-health/route\"\n    },\n    resolvedPagePath: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\api\\\\super-admin\\\\system-health\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_proj_nextjs_saas_app_api_super_admin_system_health_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/super-admin/system-health/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Fsystem-health%2Froute&page=%2Fapi%2Fsuper-admin%2Fsystem-health%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Fsystem-health%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/super-admin/system-health/route.ts":
/*!****************************************************!*\
  !*** ./app/api/super-admin/system-health/route.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var os__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! os */ \"os\");\n/* harmony import */ var os__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(os__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\n// GET /api/super-admin/system-health - Get system health metrics\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id || session?.user?.role !== \"SUPER_ADMIN\") {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Super admin access required\"\n            }, {\n                status: 403\n            });\n        }\n        // Get system metrics\n        const systemMetrics = {\n            // CPU and Memory\n            cpuUsage: process.cpuUsage(),\n            memoryUsage: process.memoryUsage(),\n            // System info\n            platform: os__WEBPACK_IMPORTED_MODULE_4___default().platform(),\n            arch: os__WEBPACK_IMPORTED_MODULE_4___default().arch(),\n            nodeVersion: process.version,\n            uptime: process.uptime(),\n            // OS metrics\n            totalMemory: os__WEBPACK_IMPORTED_MODULE_4___default().totalmem(),\n            freeMemory: os__WEBPACK_IMPORTED_MODULE_4___default().freemem(),\n            loadAverage: os__WEBPACK_IMPORTED_MODULE_4___default().loadavg(),\n            cpuCount: os__WEBPACK_IMPORTED_MODULE_4___default().cpus().length\n        };\n        // Database health check\n        const dbHealthStart = Date.now();\n        try {\n            await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.$queryRaw`SELECT 1`;\n            const dbResponseTime = Date.now() - dbHealthStart;\n            // Get database stats\n            const [totalUsers, totalCompanies, totalActivities, recentErrors, dbSize] = await Promise.all([\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.count(),\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.company.count(),\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.activity.count(),\n                // Use audit logs as alternative to system logs for error counting\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.auditLog.count({\n                    where: {\n                        action: {\n                            contains: \"ERROR\"\n                        },\n                        createdAt: {\n                            gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours\n                        }\n                    }\n                }).catch(()=>0),\n                // Approximate database size (MySQL specific)\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.$queryRaw`\n          SELECT \n            ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb\n          FROM information_schema.tables \n          WHERE table_schema = DATABASE()\n        `\n            ]);\n            const databaseHealth = {\n                status: \"healthy\",\n                responseTime: dbResponseTime,\n                totalUsers,\n                totalCompanies,\n                totalActivities,\n                recentErrors,\n                size: dbSize[0]?.size_mb || 0\n            };\n            // Calculate health scores\n            const memoryUsagePercent = (systemMetrics.totalMemory - systemMetrics.freeMemory) / systemMetrics.totalMemory * 100;\n            const errorRate = totalActivities > 0 ? recentErrors / totalActivities * 100 : 0;\n            let overallStatus = \"healthy\";\n            if (memoryUsagePercent > 90 || errorRate > 5 || dbResponseTime > 1000) {\n                overallStatus = \"critical\";\n            } else if (memoryUsagePercent > 80 || errorRate > 2 || dbResponseTime > 500) {\n                overallStatus = \"warning\";\n            }\n            // Get recent audit logs as alternative to system logs\n            const recentLogs = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.auditLog.findMany({\n                where: {\n                    action: {\n                        in: [\n                            \"ERROR\",\n                            \"WARNING\",\n                            \"SYSTEM_ERROR\"\n                        ]\n                    }\n                },\n                take: 10,\n                orderBy: {\n                    createdAt: \"desc\"\n                },\n                select: {\n                    id: true,\n                    action: true,\n                    entityType: true,\n                    userEmail: true,\n                    metadata: true,\n                    createdAt: true\n                }\n            }).catch(()=>[]) // Return empty array if audit logs fail\n            ;\n            // Generate mock performance metrics for demonstration\n            // In production, this would come from a SystemHealth table\n            const performanceMetrics = Array.from({\n                length: 24\n            }, (_, i)=>{\n                const timestamp = new Date(Date.now() - (23 - i) * 60 * 60 * 1000) // Last 24 hours\n                ;\n                return {\n                    cpuUsage: Math.random() * 30 + 20,\n                    memoryUsage: Math.random() * 20 + 40,\n                    diskUsage: Math.random() * 10 + 30,\n                    dbResponseTime: Math.random() * 50 + 50,\n                    activeUsers: Math.floor(Math.random() * 10) + 5,\n                    requestsPerMinute: Math.floor(Math.random() * 100) + 50,\n                    errorRate: Math.random() * 2,\n                    status: \"HEALTHY\",\n                    createdAt: timestamp\n                };\n            });\n            // Note: In production, you would store current health metrics in a SystemHealth table\n            // For now, we skip this to avoid database errors since the table doesn't exist\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                status: overallStatus,\n                timestamp: new Date().toISOString(),\n                system: {\n                    platform: systemMetrics.platform,\n                    arch: systemMetrics.arch,\n                    nodeVersion: systemMetrics.nodeVersion,\n                    uptime: systemMetrics.uptime,\n                    cpuCount: systemMetrics.cpuCount,\n                    memory: {\n                        total: systemMetrics.totalMemory,\n                        free: systemMetrics.freeMemory,\n                        used: systemMetrics.totalMemory - systemMetrics.freeMemory,\n                        usagePercent: memoryUsagePercent\n                    },\n                    loadAverage: systemMetrics.loadAverage\n                },\n                database: databaseHealth,\n                metrics: {\n                    errorRate,\n                    responseTime: dbResponseTime,\n                    memoryUsage: memoryUsagePercent\n                },\n                recentLogs: recentLogs.map((log)=>({\n                        id: log.id,\n                        level: log.action.includes(\"ERROR\") ? \"ERROR\" : \"WARN\",\n                        message: `${log.action} - ${log.entityType}`,\n                        source: log.userEmail || \"System\",\n                        category: log.entityType,\n                        createdAt: log.createdAt\n                    })),\n                performanceHistory: performanceMetrics.reverse() // Oldest first for charts\n            });\n        } catch (dbError) {\n            console.error(\"Database health check failed:\", dbError);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                status: \"critical\",\n                timestamp: new Date().toISOString(),\n                system: systemMetrics,\n                database: {\n                    status: \"unhealthy\",\n                    error: \"Database connection failed\"\n                },\n                error: \"Database health check failed\"\n            });\n        }\n    } catch (error) {\n        console.error(\"Error checking system health:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to check system health\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/super-admin/system-health - Trigger health check and cleanup\nasync function POST(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id || session?.user?.role !== \"SUPER_ADMIN\") {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Super admin access required\"\n            }, {\n                status: 403\n            });\n        }\n        const body = await request.json();\n        const { action } = body;\n        switch(action){\n            case \"cleanup_logs\":\n                // Clean up old audit logs (older than 30 days) as alternative to system logs\n                const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);\n                const deletedLogs = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.auditLog.deleteMany({\n                    where: {\n                        createdAt: {\n                            lt: thirtyDaysAgo\n                        },\n                        action: {\n                            not: \"SYSTEM_CLEANUP\"\n                        } // Don't delete cleanup logs themselves\n                    }\n                }).catch(()=>({\n                        count: 0\n                    })) // Fallback if operation fails\n                ;\n                // Create a new audit log for this cleanup action\n                await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.auditLog.create({\n                    data: {\n                        action: \"SYSTEM_CLEANUP\",\n                        entityType: \"AuditLog\",\n                        userId: session.user.id,\n                        userEmail: session.user.email,\n                        userRole: session.user.role,\n                        metadata: {\n                            deletedCount: deletedLogs.count,\n                            cutoffDate: thirtyDaysAgo\n                        }\n                    }\n                }).catch(()=>{}) // Ignore errors for audit log creation\n                ;\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    message: `Cleaned up ${deletedLogs.count} old log entries`,\n                    deletedCount: deletedLogs.count\n                });\n            case \"cleanup_health_metrics\":\n                // Note: In production, this would clean up old health metrics from SystemHealth table\n                // For now, we'll simulate the cleanup since the table doesn't exist\n                const simulatedDeletedCount = Math.floor(Math.random() * 100) + 50;\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    message: `Simulated cleanup of ${simulatedDeletedCount} old health metrics`,\n                    deletedCount: simulatedDeletedCount\n                });\n            case \"force_health_check\":\n                // Force a comprehensive health check\n                // This would trigger the GET endpoint logic\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    message: \"Health check triggered\",\n                    timestamp: new Date().toISOString()\n                });\n            default:\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"Invalid action\"\n                }, {\n                    status: 400\n                });\n        }\n    } catch (error) {\n        console.error(\"Error performing system health action:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to perform system health action\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/super-admin/system-health/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/github */ \"(rsc)/./node_modules/next-auth/providers/github.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n\nconst authOptions = {\n    // Using JWT strategy instead of database adapter for now\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                try {\n                    if (!credentials?.email || !credentials?.password) {\n                        console.log(\"Missing credentials\");\n                        return null;\n                    }\n                    console.log(\"Attempting to authenticate user:\", credentials.email);\n                    const user = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.findUnique({\n                        where: {\n                            email: credentials.email\n                        },\n                        select: {\n                            id: true,\n                            email: true,\n                            name: true,\n                            password: true,\n                            role: true,\n                            companyId: true\n                        }\n                    });\n                    // Get the company ID - either as member or owner\n                    let finalCompanyId = user?.companyId;\n                    if (!finalCompanyId && user) {\n                        const ownedCompany = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.company.findFirst({\n                            where: {\n                                ownerId: user.id\n                            },\n                            select: {\n                                id: true\n                            }\n                        });\n                        finalCompanyId = ownedCompany?.id;\n                        // If user is company owner, update their companyId for future queries\n                        if (finalCompanyId) {\n                            await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                                where: {\n                                    id: user.id\n                                },\n                                data: {\n                                    companyId: finalCompanyId\n                                }\n                            });\n                        }\n                    }\n                    if (!user) {\n                        console.log(\"User not found:\", credentials.email);\n                        return null;\n                    }\n                    if (!user.password) {\n                        console.log(\"User has no password set:\", credentials.email);\n                        return null;\n                    }\n                    const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_3___default().compare(credentials.password, user.password);\n                    if (!isPasswordValid) {\n                        console.log(\"Invalid password for user:\", credentials.email);\n                        return null;\n                    }\n                    // Update last login\n                    await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                        where: {\n                            id: user.id\n                        },\n                        data: {\n                            lastLoginAt: new Date(),\n                            loginCount: {\n                                increment: 1\n                            }\n                        }\n                    });\n                    console.log(\"User authenticated successfully:\", user.email);\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name,\n                        role: user.role,\n                        companyId: finalCompanyId\n                    };\n                } catch (error) {\n                    console.error(\"Authentication error:\", error);\n                    return null;\n                }\n            }\n        }),\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            clientId: process.env.GITHUB_CLIENT_ID,\n            clientSecret: process.env.GITHUB_CLIENT_SECRET\n        })\n    ],\n    session: {\n        strategy: \"jwt\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                console.log(\"JWT callback - user data:\", {\n                    id: user.id,\n                    email: user.email,\n                    role: user.role,\n                    companyId: user.companyId\n                });\n                token.role = user.role;\n                token.companyId = user.companyId;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n                session.user.companyId = token.companyId;\n                console.log(\"Session callback - final session:\", {\n                    id: session.user.id,\n                    email: session.user.email,\n                    role: session.user.role,\n                    companyId: session.user.companyId\n                });\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        signUp: \"/auth/signup\",\n        error: \"/auth/error\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zYWFzLWFwcC8uL2xpYi9wcmlzbWEudHM/OTgyMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/lru-cache","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Fsystem-health%2Froute&page=%2Fapi%2Fsuper-admin%2Fsystem-health%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Fsystem-health%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();