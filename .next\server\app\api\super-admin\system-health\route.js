"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/super-admin/system-health/route";
exports.ids = ["app/api/super-admin/system-health/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("os");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Fsystem-health%2Froute&page=%2Fapi%2Fsuper-admin%2Fsystem-health%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Fsystem-health%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Fsystem-health%2Froute&page=%2Fapi%2Fsuper-admin%2Fsystem-health%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Fsystem-health%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_proj_nextjs_saas_app_api_super_admin_system_health_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/super-admin/system-health/route.ts */ \"(rsc)/./app/api/super-admin/system-health/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/super-admin/system-health/route\",\n        pathname: \"/api/super-admin/system-health\",\n        filename: \"route\",\n        bundlePath: \"app/api/super-admin/system-health/route\"\n    },\n    resolvedPagePath: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\api\\\\super-admin\\\\system-health\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_proj_nextjs_saas_app_api_super_admin_system_health_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/super-admin/system-health/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Fsystem-health%2Froute&page=%2Fapi%2Fsuper-admin%2Fsystem-health%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Fsystem-health%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/super-admin/system-health/route.ts":
/*!****************************************************!*\
  !*** ./app/api/super-admin/system-health/route.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var os__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! os */ \"os\");\n/* harmony import */ var os__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(os__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\n// GET /api/super-admin/system-health - Get system health metrics\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id || session?.user?.role !== \"SUPER_ADMIN\") {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Super admin access required\"\n            }, {\n                status: 403\n            });\n        }\n        // Get system metrics\n        const systemMetrics = {\n            // CPU and Memory\n            cpuUsage: process.cpuUsage(),\n            memoryUsage: process.memoryUsage(),\n            // System info\n            platform: os__WEBPACK_IMPORTED_MODULE_4___default().platform(),\n            arch: os__WEBPACK_IMPORTED_MODULE_4___default().arch(),\n            nodeVersion: process.version,\n            uptime: process.uptime(),\n            // OS metrics\n            totalMemory: os__WEBPACK_IMPORTED_MODULE_4___default().totalmem(),\n            freeMemory: os__WEBPACK_IMPORTED_MODULE_4___default().freemem(),\n            loadAverage: os__WEBPACK_IMPORTED_MODULE_4___default().loadavg(),\n            cpuCount: os__WEBPACK_IMPORTED_MODULE_4___default().cpus().length\n        };\n        // Database health check\n        const dbHealthStart = Date.now();\n        try {\n            await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.$queryRaw`SELECT 1`;\n            const dbResponseTime = Date.now() - dbHealthStart;\n            // Get database stats\n            const [totalUsers, totalCompanies, totalActivities, recentErrors, dbSize] = await Promise.all([\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.count(),\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.company.count(),\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.activity.count(),\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.systemLog.count({\n                    where: {\n                        level: \"ERROR\",\n                        createdAt: {\n                            gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours\n                        }\n                    }\n                }),\n                // Approximate database size (MySQL specific)\n                _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.$queryRaw`\n          SELECT \n            ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb\n          FROM information_schema.tables \n          WHERE table_schema = DATABASE()\n        `\n            ]);\n            const databaseHealth = {\n                status: \"healthy\",\n                responseTime: dbResponseTime,\n                totalUsers,\n                totalCompanies,\n                totalActivities,\n                recentErrors,\n                size: dbSize[0]?.size_mb || 0\n            };\n            // Calculate health scores\n            const memoryUsagePercent = (systemMetrics.totalMemory - systemMetrics.freeMemory) / systemMetrics.totalMemory * 100;\n            const errorRate = totalActivities > 0 ? recentErrors / totalActivities * 100 : 0;\n            let overallStatus = \"healthy\";\n            if (memoryUsagePercent > 90 || errorRate > 5 || dbResponseTime > 1000) {\n                overallStatus = \"critical\";\n            } else if (memoryUsagePercent > 80 || errorRate > 2 || dbResponseTime > 500) {\n                overallStatus = \"warning\";\n            }\n            // Get recent system logs\n            const recentLogs = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.systemLog.findMany({\n                where: {\n                    level: {\n                        in: [\n                            \"ERROR\",\n                            \"WARN\"\n                        ]\n                    }\n                },\n                take: 10,\n                orderBy: {\n                    createdAt: \"desc\"\n                },\n                select: {\n                    id: true,\n                    level: true,\n                    message: true,\n                    source: true,\n                    category: true,\n                    createdAt: true\n                }\n            });\n            // Get performance metrics over time\n            const performanceMetrics = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.systemHealth.findMany({\n                take: 24,\n                orderBy: {\n                    createdAt: \"desc\"\n                },\n                select: {\n                    cpuUsage: true,\n                    memoryUsage: true,\n                    diskUsage: true,\n                    dbResponseTime: true,\n                    activeUsers: true,\n                    requestsPerMinute: true,\n                    errorRate: true,\n                    status: true,\n                    createdAt: true\n                }\n            });\n            // Store current health metrics\n            await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.systemHealth.create({\n                data: {\n                    cpuUsage: memoryUsagePercent,\n                    memoryUsage: memoryUsagePercent,\n                    diskUsage: 0,\n                    dbConnections: 1,\n                    dbResponseTime: dbResponseTime,\n                    activeUsers: await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.count({\n                        where: {\n                            status: \"ACTIVE\"\n                        }\n                    }),\n                    requestsPerMinute: 0,\n                    errorRate: errorRate,\n                    status: overallStatus.toUpperCase(),\n                    uptime: systemMetrics.uptime,\n                    metrics: {\n                        nodeVersion: systemMetrics.nodeVersion,\n                        platform: systemMetrics.platform,\n                        arch: systemMetrics.arch,\n                        cpuCount: systemMetrics.cpuCount,\n                        totalMemory: systemMetrics.totalMemory,\n                        freeMemory: systemMetrics.freeMemory,\n                        loadAverage: systemMetrics.loadAverage\n                    }\n                }\n            });\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                status: overallStatus,\n                timestamp: new Date().toISOString(),\n                system: {\n                    platform: systemMetrics.platform,\n                    arch: systemMetrics.arch,\n                    nodeVersion: systemMetrics.nodeVersion,\n                    uptime: systemMetrics.uptime,\n                    cpuCount: systemMetrics.cpuCount,\n                    memory: {\n                        total: systemMetrics.totalMemory,\n                        free: systemMetrics.freeMemory,\n                        used: systemMetrics.totalMemory - systemMetrics.freeMemory,\n                        usagePercent: memoryUsagePercent\n                    },\n                    loadAverage: systemMetrics.loadAverage\n                },\n                database: databaseHealth,\n                metrics: {\n                    errorRate,\n                    responseTime: dbResponseTime,\n                    memoryUsage: memoryUsagePercent\n                },\n                recentLogs,\n                performanceHistory: performanceMetrics.reverse() // Oldest first for charts\n            });\n        } catch (dbError) {\n            console.error(\"Database health check failed:\", dbError);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                status: \"critical\",\n                timestamp: new Date().toISOString(),\n                system: systemMetrics,\n                database: {\n                    status: \"unhealthy\",\n                    error: \"Database connection failed\"\n                },\n                error: \"Database health check failed\"\n            });\n        }\n    } catch (error) {\n        console.error(\"Error checking system health:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to check system health\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/super-admin/system-health - Trigger health check and cleanup\nasync function POST(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id || session?.user?.role !== \"SUPER_ADMIN\") {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Super admin access required\"\n            }, {\n                status: 403\n            });\n        }\n        const body = await request.json();\n        const { action } = body;\n        switch(action){\n            case \"cleanup_logs\":\n                // Clean up old system logs (older than 30 days)\n                const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);\n                const deletedLogs = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.systemLog.deleteMany({\n                    where: {\n                        createdAt: {\n                            lt: thirtyDaysAgo\n                        }\n                    }\n                });\n                await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.auditLog.create({\n                    data: {\n                        action: \"SYSTEM_CLEANUP\",\n                        entityType: \"SystemLog\",\n                        userId: session.user.id,\n                        userEmail: session.user.email,\n                        userRole: session.user.role,\n                        metadata: {\n                            deletedCount: deletedLogs.count,\n                            cutoffDate: thirtyDaysAgo\n                        }\n                    }\n                });\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    message: `Cleaned up ${deletedLogs.count} old log entries`,\n                    deletedCount: deletedLogs.count\n                });\n            case \"cleanup_health_metrics\":\n                // Clean up old health metrics (older than 7 days)\n                const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);\n                const deletedMetrics = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.systemHealth.deleteMany({\n                    where: {\n                        createdAt: {\n                            lt: sevenDaysAgo\n                        }\n                    }\n                });\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    message: `Cleaned up ${deletedMetrics.count} old health metrics`,\n                    deletedCount: deletedMetrics.count\n                });\n            case \"force_health_check\":\n                // Force a comprehensive health check\n                // This would trigger the GET endpoint logic\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    message: \"Health check triggered\",\n                    timestamp: new Date().toISOString()\n                });\n            default:\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"Invalid action\"\n                }, {\n                    status: 400\n                });\n        }\n    } catch (error) {\n        console.error(\"Error performing system health action:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to perform system health action\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/super-admin/system-health/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/github */ \"(rsc)/./node_modules/next-auth/providers/github.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n\nconst authOptions = {\n    // Using JWT strategy instead of database adapter for now\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                try {\n                    if (!credentials?.email || !credentials?.password) {\n                        console.log(\"Missing credentials\");\n                        return null;\n                    }\n                    console.log(\"Attempting to authenticate user:\", credentials.email);\n                    const user = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.findUnique({\n                        where: {\n                            email: credentials.email\n                        },\n                        select: {\n                            id: true,\n                            email: true,\n                            name: true,\n                            password: true,\n                            role: true,\n                            companyId: true\n                        }\n                    });\n                    // Get the company ID - either as member or owner\n                    let finalCompanyId = user?.companyId;\n                    if (!finalCompanyId && user) {\n                        const ownedCompany = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.company.findFirst({\n                            where: {\n                                ownerId: user.id\n                            },\n                            select: {\n                                id: true\n                            }\n                        });\n                        finalCompanyId = ownedCompany?.id;\n                        // If user is company owner, update their companyId for future queries\n                        if (finalCompanyId) {\n                            await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                                where: {\n                                    id: user.id\n                                },\n                                data: {\n                                    companyId: finalCompanyId\n                                }\n                            });\n                        }\n                    }\n                    if (!user) {\n                        console.log(\"User not found:\", credentials.email);\n                        return null;\n                    }\n                    if (!user.password) {\n                        console.log(\"User has no password set:\", credentials.email);\n                        return null;\n                    }\n                    const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_3___default().compare(credentials.password, user.password);\n                    if (!isPasswordValid) {\n                        console.log(\"Invalid password for user:\", credentials.email);\n                        return null;\n                    }\n                    // Update last login\n                    await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                        where: {\n                            id: user.id\n                        },\n                        data: {\n                            lastLoginAt: new Date(),\n                            loginCount: {\n                                increment: 1\n                            }\n                        }\n                    });\n                    console.log(\"User authenticated successfully:\", user.email);\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name,\n                        role: user.role,\n                        companyId: finalCompanyId\n                    };\n                } catch (error) {\n                    console.error(\"Authentication error:\", error);\n                    return null;\n                }\n            }\n        }),\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            clientId: process.env.GITHUB_CLIENT_ID,\n            clientSecret: process.env.GITHUB_CLIENT_SECRET\n        })\n    ],\n    session: {\n        strategy: \"jwt\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                console.log(\"JWT callback - user data:\", {\n                    id: user.id,\n                    email: user.email,\n                    role: user.role,\n                    companyId: user.companyId\n                });\n                token.role = user.role;\n                token.companyId = user.companyId;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n                session.user.companyId = token.companyId;\n                console.log(\"Session callback - final session:\", {\n                    id: session.user.id,\n                    email: session.user.email,\n                    role: session.user.role,\n                    companyId: session.user.companyId\n                });\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        signUp: \"/auth/signup\",\n        error: \"/auth/error\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zYWFzLWFwcC8uL2xpYi9wcmlzbWEudHM/OTgyMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/lru-cache","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Fsystem-health%2Froute&page=%2Fapi%2Fsuper-admin%2Fsystem-health%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Fsystem-health%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();