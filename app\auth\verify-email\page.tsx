'use client'

import { useState, useEffect } from 'react'
import { useR<PERSON>er, useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  Building2, 
  CheckCircle, 
  AlertCircle, 
  Mail, 
  RefreshCw,
  ArrowRight
} from 'lucide-react'
import { toast } from 'react-hot-toast'

export default function VerifyEmailPage() {
  const [status, setStatus] = useState<'loading' | 'success' | 'error' | 'expired' | 'resend'>('loading')
  const [isResending, setIsResending] = useState(false)
  const [message, setMessage] = useState('')
  const router = useRouter()
  const searchParams = useSearchParams()
  
  const token = searchParams.get('token')
  const email = searchParams.get('email')

  useEffect(() => {
    if (token) {
      verifyEmail(token)
    } else if (!email) {
      setStatus('error')
      setMessage('Invalid verification link')
    }
  }, [token])

  const verifyEmail = async (verificationToken: string) => {
    try {
      const response = await fetch('/api/auth/verify-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token: verificationToken }),
      })

      const data = await response.json()

      if (response.ok) {
        setStatus('success')
        setMessage('Your email has been verified successfully!')
        
        // Auto-redirect to signin after 3 seconds
        setTimeout(() => {
          router.push('/auth/signin?verified=true')
        }, 3000)
      } else {
        if (data.error === 'Token expired') {
          setStatus('expired')
          setMessage('Your verification link has expired. Please request a new one.')
        } else {
          setStatus('error')
          setMessage(data.error || 'Failed to verify email')
        }
      }
    } catch (error) {
      console.error('Verification error:', error)
      setStatus('error')
      setMessage('An error occurred during verification')
    }
  }

  const resendVerificationEmail = async () => {
    if (!email) {
      toast.error('Email address is required')
      return
    }

    setIsResending(true)

    try {
      const response = await fetch('/api/auth/resend-verification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      })

      const data = await response.json()

      if (response.ok) {
        setStatus('resend')
        setMessage('A new verification email has been sent to your inbox.')
        toast.success('Verification email sent!')
      } else {
        toast.error(data.error || 'Failed to resend verification email')
      }
    } catch (error) {
      console.error('Resend error:', error)
      toast.error('An error occurred while resending the email')
    } finally {
      setIsResending(false)
    }
  }

  const getStatusIcon = () => {
    switch (status) {
      case 'loading':
        return <RefreshCw className="h-12 w-12 text-blue-600 animate-spin" />
      case 'success':
        return <CheckCircle className="h-12 w-12 text-green-600" />
      case 'error':
      case 'expired':
        return <AlertCircle className="h-12 w-12 text-red-600" />
      case 'resend':
        return <Mail className="h-12 w-12 text-blue-600" />
      default:
        return <RefreshCw className="h-12 w-12 text-blue-600 animate-spin" />
    }
  }

  const getStatusTitle = () => {
    switch (status) {
      case 'loading':
        return 'Verifying your email...'
      case 'success':
        return 'Email verified successfully!'
      case 'error':
        return 'Verification failed'
      case 'expired':
        return 'Verification link expired'
      case 'resend':
        return 'New verification email sent'
      default:
        return 'Verifying your email...'
    }
  }

  const getStatusDescription = () => {
    switch (status) {
      case 'loading':
        return 'Please wait while we verify your email address...'
      case 'success':
        return 'Your account is now active. You will be redirected to the sign-in page shortly.'
      case 'error':
        return 'We encountered an issue verifying your email address.'
      case 'expired':
        return 'Your verification link has expired. Please request a new one below.'
      case 'resend':
        return 'Please check your inbox and click the verification link in the new email.'
      default:
        return 'Please wait while we verify your email address...'
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 p-4">
      <div className="w-full max-w-md">
        {/* Logo */}
        <div className="flex items-center justify-center mb-8">
          <Building2 className="h-8 w-8 text-blue-600 mr-2" />
          <span className="text-2xl font-bold text-gray-900">Business SaaS</span>
        </div>

        <Card>
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              {getStatusIcon()}
            </div>
            <CardTitle className="text-2xl">{getStatusTitle()}</CardTitle>
            <CardDescription>{getStatusDescription()}</CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-4">
            {message && (
              <div className={`p-4 rounded-lg text-center ${
                status === 'success' 
                  ? 'bg-green-50 text-green-800 border border-green-200'
                  : status === 'resend'
                  ? 'bg-blue-50 text-blue-800 border border-blue-200'
                  : 'bg-red-50 text-red-800 border border-red-200'
              }`}>
                {message}
              </div>
            )}

            {/* Action buttons based on status */}
            <div className="space-y-3">
              {status === 'success' && (
                <div className="space-y-3">
                  <Button asChild className="w-full">
                    <Link href="/auth/signin?verified=true">
                      Continue to Sign In
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Link>
                  </Button>
                  <p className="text-sm text-center text-gray-500">
                    Redirecting automatically in 3 seconds...
                  </p>
                </div>
              )}

              {(status === 'expired' || status === 'error') && email && (
                <Button 
                  onClick={resendVerificationEmail} 
                  disabled={isResending}
                  className="w-full"
                >
                  {isResending ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <Mail className="h-4 w-4 mr-2" />
                      Resend Verification Email
                    </>
                  )}
                </Button>
              )}

              {status === 'resend' && (
                <div className="text-center">
                  <p className="text-sm text-gray-600 mb-3">
                    Didn't receive the email? Check your spam folder or try again.
                  </p>
                  <Button 
                    variant="outline"
                    onClick={resendVerificationEmail} 
                    disabled={isResending}
                    size="sm"
                  >
                    {isResending ? (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                        Sending...
                      </>
                    ) : (
                      'Send Again'
                    )}
                  </Button>
                </div>
              )}

              {/* Always show back to signin link */}
              <div className="text-center pt-4 border-t">
                <Link 
                  href="/auth/signin" 
                  className="text-sm text-blue-600 hover:underline"
                >
                  Back to Sign In
                </Link>
              </div>
            </div>

            {/* Help text */}
            {(status === 'error' || status === 'expired') && (
              <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-2">Need help?</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Make sure you're using the latest verification email</li>
                  <li>• Check your spam or junk folder</li>
                  <li>• Verification links expire after 24 hours</li>
                  <li>• Contact support if you continue having issues</li>
                </ul>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
