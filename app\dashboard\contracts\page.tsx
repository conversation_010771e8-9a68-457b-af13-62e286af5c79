'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { ColumnDef } from '@tanstack/react-table'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { DataTable } from '@/components/ui/data-table'
import { ContractForm } from '@/components/contracts/contract-form'
import { ContractAnalytics } from '@/components/contracts/contract-analytics'
import { SignatureModal } from '@/components/contracts/signature-modal'
import { 
  FileText, 
  Plus, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Eye,
  Send,
  Download,
  Copy,
  DollarSign,
  Calendar,
  User,
  CheckCircle,
  Clock,
  XCircle,
  AlertTriangle,
  FileSignature,
  Building2,
  Target,
  BarChart3
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { toast } from 'react-hot-toast'
import Link from 'next/link'

interface Contract {
  id: string
  contractNumber: string
  title: string
  description: string | null
  type: 'SERVICE' | 'PRODUCT' | 'SUBSCRIPTION' | 'MAINTENANCE' | 'CONSULTING' | 'OTHER'
  status: 'DRAFT' | 'REVIEW' | 'SENT' | 'SIGNED' | 'ACTIVE' | 'COMPLETED' | 'CANCELLED' | 'EXPIRED'
  value: number | null
  currency: string
  startDate: string | null
  endDate: string | null
  renewalDate: string | null
  autoRenewal: boolean
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
  signatureRequired: boolean
  tags: string[]
  customer: {
    id: string
    name: string
    email: string | null
    company: string | null
  }
  quotation: {
    id: string
    quotationNumber: string
    title: string
  } | null
  invoice: {
    id: string
    invoiceNumber: string
  } | null
  template: {
    id: string
    name: string
    type: string
  } | null
  createdBy: {
    name: string | null
    email: string | null
  }
  assignedTo: {
    name: string | null
    email: string | null
  } | null
  _count: {
    activities: number
    signatures: number
    documents: number
  }
}

export default function ContractsPage() {
  const { data: session } = useSession()
  const [contracts, setContracts] = useState<Contract[]>([])
  const [loading, setLoading] = useState(true)
  const [showForm, setShowForm] = useState(false)
  const [editingContract, setEditingContract] = useState<Contract | null>(null)
  const [showAnalytics, setShowAnalytics] = useState(false)
  const [showSignatureModal, setShowSignatureModal] = useState(false)
  const [signatureContract, setSignatureContract] = useState<Contract | null>(null)
  const [stats, setStats] = useState({
    total: 0,
    draft: 0,
    active: 0,
    signed: 0,
    expired: 0,
    totalValue: 0
  })

  const fetchContracts = async () => {
    try {
      const response = await fetch('/api/contracts')
      if (!response.ok) throw new Error('Failed to fetch contracts')
      
      const data = await response.json()
      setContracts(data.contracts)
      
      // Calculate stats
      const total = data.contracts.length
      const draft = data.contracts.filter((c: Contract) => c.status === 'DRAFT').length
      const active = data.contracts.filter((c: Contract) => c.status === 'ACTIVE').length
      const signed = data.contracts.filter((c: Contract) => c.status === 'SIGNED').length
      const expired = data.contracts.filter((c: Contract) => c.status === 'EXPIRED').length
      const totalValue = data.contracts.reduce((sum: number, c: Contract) => sum + (c.value || 0), 0)
      
      setStats({ total, draft, active, signed, expired, totalValue })
    } catch (error) {
      toast.error('Failed to load contracts')
      console.error('Error fetching contracts:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchContracts()
  }, [])

  const handleDelete = async (contract: Contract) => {
    if (!confirm(`Are you sure you want to delete contract "${contract.contractNumber}"?`)) return

    try {
      const response = await fetch(`/api/contracts/${contract.id}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to delete contract')
      }

      toast.success('Contract deleted successfully')
      fetchContracts()
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to delete contract')
    }
  }

  const handleEdit = (contract: Contract) => {
    setEditingContract(contract)
    setShowForm(true)
  }

  const handleFormClose = () => {
    setShowForm(false)
    setEditingContract(null)
  }

  const handleDownloadPDF = async (contractId: string) => {
    try {
      const response = await fetch(`/api/contracts/${contractId}/pdf`)
      if (!response.ok) {
        throw new Error('Failed to generate PDF')
      }

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `contract-${contractId}.html`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)

      toast.success('PDF downloaded successfully')
    } catch (error) {
      toast.error('Failed to download PDF')
      console.error('Error downloading PDF:', error)
    }
  }

  const handleManageSignatures = (contract: Contract) => {
    setSignatureContract(contract)
    setShowSignatureModal(true)
  }

  const getStatusBadge = (status: string | null | undefined) => {
    if (!status) return <Badge variant="secondary">Unknown</Badge>
    
    switch (status) {
      case 'DRAFT':
        return <Badge variant="secondary">Draft</Badge>
      case 'REVIEW':
        return <Badge variant="warning">Review</Badge>
      case 'SENT':
        return <Badge variant="info">Sent</Badge>
      case 'SIGNED':
        return <Badge variant="success">Signed</Badge>
      case 'ACTIVE':
        return <Badge variant="success">Active</Badge>
      case 'COMPLETED':
        return <Badge variant="success">Completed</Badge>
      case 'CANCELLED':
        return <Badge variant="destructive">Cancelled</Badge>
      case 'EXPIRED':
        return <Badge variant="destructive">Expired</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const getTypeBadge = (type: string) => {
    const colors: Record<string, string> = {
      SERVICE: 'bg-blue-100 text-blue-800',
      PRODUCT: 'bg-green-100 text-green-800',
      SUBSCRIPTION: 'bg-purple-100 text-purple-800',
      MAINTENANCE: 'bg-orange-100 text-orange-800',
      CONSULTING: 'bg-indigo-100 text-indigo-800',
      OTHER: 'bg-gray-100 text-gray-800'
    }
    
    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${colors[type] || colors.OTHER}`}>
        {type}
      </span>
    )
  }

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'URGENT':
        return <Badge variant="destructive">Urgent</Badge>
      case 'HIGH':
        return <Badge variant="warning">High</Badge>
      case 'MEDIUM':
        return <Badge variant="info">Medium</Badge>
      case 'LOW':
        return <Badge variant="secondary">Low</Badge>
      default:
        return <Badge variant="secondary">{priority}</Badge>
    }
  }

  const columns: ColumnDef<Contract>[] = [
    {
      accessorKey: 'contractNumber',
      header: 'Contract',
      cell: ({ row }) => {
        const contract = row.original
        return (
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
              <FileSignature className="h-4 w-4 text-purple-600" />
            </div>
            <div>
              <div className="font-medium">{contract.contractNumber}</div>
              <div className="text-sm text-gray-500">{contract.title}</div>
            </div>
          </div>
        )
      }
    },
    {
      accessorKey: 'customer',
      header: 'Customer',
      cell: ({ row }) => {
        const customer = row.original.customer
        return (
          <div>
            <div className="font-medium">{customer.name}</div>
            {customer.company && (
              <div className="text-sm text-gray-500">{customer.company}</div>
            )}
          </div>
        )
      }
    },
    {
      accessorKey: 'type',
      header: 'Type',
      cell: ({ row }) => getTypeBadge(row.getValue('type'))
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => getStatusBadge(row.getValue('status'))
    },
    {
      accessorKey: 'priority',
      header: 'Priority',
      cell: ({ row }) => getPriorityBadge(row.getValue('priority'))
    },
    {
      accessorKey: 'value',
      header: 'Value',
      cell: ({ row }) => {
        const value = row.getValue('value') as number | null
        const currency = row.original.currency
        return value ? (
          <div className="flex items-center space-x-2">
            <DollarSign className="h-3 w-3 text-green-600" />
            <span className="font-medium">{currency} {value.toLocaleString()}</span>
          </div>
        ) : (
          <span className="text-gray-400 text-sm">-</span>
        )
      }
    },
    {
      accessorKey: 'endDate',
      header: 'End Date',
      cell: ({ row }) => {
        const date = row.getValue('endDate') as string | null
        if (!date) return <span className="text-gray-400 text-sm">-</span>
        
        const endDate = new Date(date)
        const today = new Date()
        const isExpiring = endDate < new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000) // 30 days
        
        return (
          <div className="flex items-center space-x-2">
            <Calendar className={`h-3 w-3 ${isExpiring ? 'text-red-400' : 'text-gray-400'}`} />
            <span className={`text-sm ${isExpiring ? 'text-red-600' : ''}`}>
              {endDate.toLocaleDateString()}
            </span>
          </div>
        )
      }
    },
    {
      accessorKey: 'assignedTo',
      header: 'Assigned To',
      cell: ({ row }) => {
        const assignedTo = row.original.assignedTo
        return assignedTo ? (
          <div className="flex items-center space-x-2">
            <User className="h-3 w-3 text-gray-400" />
            <span className="text-sm">{assignedTo.name || 'Unknown'}</span>
          </div>
        ) : (
          <span className="text-gray-400 text-sm">Unassigned</span>
        )
      }
    },
    {
      id: 'actions',
      cell: ({ row }) => {
        const contract = row.original
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem asChild>
                <Link href={`/dashboard/contracts/${contract.id}`}>
                  <Eye className="mr-2 h-4 w-4" />
                  View Details
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleEdit(contract)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Copy className="mr-2 h-4 w-4" />
                Duplicate
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Send className="mr-2 h-4 w-4" />
                Send for Signature
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleDownloadPDF(contract.id)}>
                <Download className="mr-2 h-4 w-4" />
                Download PDF
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleManageSignatures(contract)}>
                <FileSignature className="mr-2 h-4 w-4" />
                Manage Signatures
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => handleDelete(contract)}
                className="text-red-600"
                disabled={contract.status === 'SIGNED' || contract.status === 'ACTIVE' || contract._count.signatures > 0}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )
      }
    }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Contracts</h1>
          <p className="text-gray-600 mt-1">Create and manage your contracts</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => setShowAnalytics(!showAnalytics)}>
            <BarChart3 className="h-4 w-4 mr-2" />
            Analytics
          </Button>
          <Button onClick={() => setShowForm(true)}>
            <Plus className="h-4 w-4 mr-2" />
            New Contract
          </Button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Contracts</CardTitle>
            <FileSignature className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">All contracts</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Draft</CardTitle>
            <Clock className="h-4 w-4 text-gray-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.draft}</div>
            <p className="text-xs text-muted-foreground">Draft contracts</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.active}</div>
            <p className="text-xs text-muted-foreground">Active contracts</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Signed</CardTitle>
            <FileSignature className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.signed}</div>
            <p className="text-xs text-muted-foreground">Signed contracts</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Expired</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.expired}</div>
            <p className="text-xs text-muted-foreground">Expired contracts</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Value</CardTitle>
            <DollarSign className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${stats.totalValue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Total contract value</p>
          </CardContent>
        </Card>
      </div>

      {/* Analytics */}
      {showAnalytics && (
        <ContractAnalytics />
      )}

      {/* Contracts Table */}
      <Card>
        <CardHeader>
          <CardTitle>Contract Management</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <DataTable
              columns={columns}
              data={contracts}
              searchPlaceholder="Search contracts..."
            />
          )}
        </CardContent>
      </Card>

      {/* Contract Form Modal */}
      <ContractForm
        isOpen={showForm}
        onClose={handleFormClose}
        onSuccess={fetchContracts}
        contract={editingContract}
        mode={editingContract ? 'edit' : 'create'}
      />

      {/* Signature Modal */}
      <SignatureModal
        open={showSignatureModal}
        contract={signatureContract}
        onClose={() => {
          setShowSignatureModal(false)
          setSignatureContract(null)
        }}
        onSuccess={() => {
          setShowSignatureModal(false)
          setSignatureContract(null)
          fetchContracts()
        }}
      />
    </div>
  )
}
