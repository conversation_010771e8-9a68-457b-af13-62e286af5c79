"use strict";(()=>{var e={};e.id=6695,e.ids=[6695],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},91623:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>x,originalPathname:()=>S,patchFetch:()=>_,requestAsyncStorage:()=>h,routeModule:()=>v,serverHooks:()=>j,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>F});var a={};t.r(a),t.d(a,{DELETE:()=>I,GET:()=>y,POST:()=>f,PUT:()=>w});var s=t(95419),i=t(69108),n=t(99678),o=t(78070),d=t(81355),l=t(3205),u=t(9108),c=t(25252),p=t(52178);let m=c.Ry({name:c.Z_().min(1,"Filter name is required"),description:c.Z_().optional(),filters:c.IM(c.Yj()),isPublic:c.O7().default(!1),isDefault:c.O7().default(!1)});async function y(e){try{let r=await (0,d.getServerSession)(l.L);if(!r?.user?.id||!r?.user?.companyId)return o.Z.json({error:"Unauthorized"},{status:401});let{searchParams:t}=new URL(e.url),a="true"===t.get("includePublic"),s={companyId:r.user.companyId,OR:[{createdById:r.user.id},...a?[{isPublic:!0}]:[]]},i=await u._.leadSavedFilter.findMany({where:s,include:{createdBy:{select:{id:!0,name:!0,email:!0}}},orderBy:[{isDefault:"desc"},{createdAt:"desc"}]});return o.Z.json({savedFilters:i})}catch(e){return console.error("Error fetching saved filters:",e),o.Z.json({error:"Failed to fetch saved filters"},{status:500})}}async function f(e){try{let r=await (0,d.getServerSession)(l.L);if(!r?.user?.id||!r?.user?.companyId)return o.Z.json({error:"Unauthorized"},{status:401});let t=await e.json(),a=m.parse(t);a.isDefault&&await u._.leadSavedFilter.updateMany({where:{createdById:r.user.id,companyId:r.user.companyId,isDefault:!0},data:{isDefault:!1}});let s=await u._.leadSavedFilter.create({data:{...a,companyId:r.user.companyId,createdById:r.user.id},include:{createdBy:{select:{id:!0,name:!0,email:!0}}}});return o.Z.json({savedFilter:s},{status:201})}catch(e){if(e instanceof p.jm)return o.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("Error creating saved filter:",e),o.Z.json({error:"Failed to create saved filter"},{status:500})}}async function w(e){try{let r=await (0,d.getServerSession)(l.L);if(!r?.user?.id||!r?.user?.companyId)return o.Z.json({error:"Unauthorized"},{status:401});let{searchParams:t}=new URL(e.url),a=t.get("id");if(!a)return o.Z.json({error:"Filter ID is required"},{status:400});let s=await e.json(),i=m.parse(s),n=await u._.leadSavedFilter.findFirst({where:{id:a,companyId:r.user.companyId,createdById:r.user.id}});if(!n)return o.Z.json({error:"Filter not found or you do not have permission to edit it"},{status:404});i.isDefault&&!n.isDefault&&await u._.leadSavedFilter.updateMany({where:{createdById:r.user.id,companyId:r.user.companyId,isDefault:!0,id:{not:a}},data:{isDefault:!1}});let c=await u._.leadSavedFilter.update({where:{id:a},data:{...i,updatedAt:new Date},include:{createdBy:{select:{id:!0,name:!0,email:!0}}}});return o.Z.json({savedFilter:c})}catch(e){if(e instanceof p.jm)return o.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("Error updating saved filter:",e),o.Z.json({error:"Failed to update saved filter"},{status:500})}}async function I(e){try{let r=await (0,d.getServerSession)(l.L);if(!r?.user?.id||!r?.user?.companyId)return o.Z.json({error:"Unauthorized"},{status:401});let{searchParams:t}=new URL(e.url),a=t.get("id");if(!a)return o.Z.json({error:"Filter ID is required"},{status:400});if(!await u._.leadSavedFilter.findFirst({where:{id:a,companyId:r.user.companyId,createdById:r.user.id}}))return o.Z.json({error:"Filter not found or you do not have permission to delete it"},{status:404});return await u._.leadSavedFilter.delete({where:{id:a}}),o.Z.json({message:"Filter deleted successfully"})}catch(e){return console.error("Error deleting saved filter:",e),o.Z.json({error:"Failed to delete saved filter"},{status:500})}}let v=new s.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/leads/saved-filters/route",pathname:"/api/leads/saved-filters",filename:"route",bundlePath:"app/api/leads/saved-filters/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\leads\\saved-filters\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:h,staticGenerationAsyncStorage:g,serverHooks:j,headerHooks:x,staticGenerationBailout:F}=v,S="/api/leads/saved-filters/route";function _(){return(0,n.patchFetch)({serverHooks:j,staticGenerationAsyncStorage:g})}},3205:(e,r,t)=>{t.d(r,{L:()=>l});var a=t(86485),s=t(10375),i=t(50694),n=t(6521),o=t.n(n),d=t(9108);let l={providers:[(0,a.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let r=await d._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),t=r?.companyId;if(!t&&r){let e=await d._.company.findFirst({where:{ownerId:r.id},select:{id:!0}});(t=e?.id)&&await d._.user.update({where:{id:r.id},data:{companyId:t}})}if(!r)return console.log("User not found:",e.email),null;if(!r.password)return console.log("User has no password set:",e.email),null;if(!await o().compare(e.password,r.password))return console.log("Invalid password for user:",e.email),null;return await d._.user.update({where:{id:r.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",r.email),{id:r.id,email:r.email,name:r.name,role:r.role,companyId:t}}catch(e){return console.error("Authentication error:",e),null}}}),(0,s.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,i.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(console.log("JWT callback - user data:",{id:r.id,email:r.email,role:r.role,companyId:r.companyId}),e.role=r.role,e.companyId=r.companyId),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role,e.user.companyId=r.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,r,t)=>{t.d(r,{_:()=>s});let a=require("@prisma/client"),s=globalThis.prisma??new a.PrismaClient}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[1638,6206,6521,2455,4520,5252],()=>t(91623));module.exports=a})();