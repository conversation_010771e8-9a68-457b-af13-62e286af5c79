"use strict";(()=>{var e={};e.id=7429,e.ids=[7429],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},63127:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>v,originalPathname:()=>j,patchFetch:()=>q,requestAsyncStorage:()=>h,routeModule:()=>g,serverHooks:()=>_,staticGenerationAsyncStorage:()=>I,staticGenerationBailout:()=>Z});var o={};r.r(o),r.d(o,{DELETE:()=>f,GET:()=>y,PUT:()=>w});var a=r(95419),s=r(69108),i=r(99678),n=r(78070),l=r(81355),u=r(3205),c=r(9108),d=r(25252),m=r(52178);let p=d.Ry({name:d.Z_().min(1,"Name is required").optional(),email:d.Z_().email("Invalid email").optional().nullable().or(d.i0("")),phone:d.Z_().optional().nullable().or(d.i0("")),company:d.Z_().optional().nullable().or(d.i0("")),address:d.Z_().optional().nullable().or(d.i0("")),city:d.Z_().optional().nullable().or(d.i0("")),state:d.Z_().optional().nullable().or(d.i0("")),country:d.Z_().optional().nullable().or(d.i0("")),postalCode:d.Z_().optional().nullable().or(d.i0("")),industry:d.Z_().optional().nullable().or(d.i0("")),website:d.Z_().url("Invalid website URL").optional().nullable().or(d.i0("")),notes:d.Z_().optional().nullable().or(d.i0("")),tags:d.IX(d.Z_()).optional().default([]),status:d.Km(["ACTIVE","INACTIVE","PROSPECT"]).optional()});async function y(e,{params:t}){try{let e=await (0,l.getServerSession)(u.L);if(!e?.user?.id)return n.Z.json({error:"Unauthorized"},{status:401});let r=await c._.customer.findFirst({where:{id:t.id,companyId:e.user.companyId||void 0},include:{createdBy:{select:{name:!0,email:!0}},leads:{orderBy:{createdAt:"desc"},take:5,include:{createdBy:{select:{name:!0}}}},quotations:{orderBy:{createdAt:"desc"},take:5,include:{createdBy:{select:{name:!0}}}},invoices:{orderBy:{createdAt:"desc"},take:5,include:{createdBy:{select:{name:!0}}}},activities:{orderBy:{createdAt:"desc"},take:10,include:{createdBy:{select:{name:!0}}}},_count:{select:{leads:!0,quotations:!0,invoices:!0,contracts:!0,activities:!0}}}});if(!r)return n.Z.json({error:"Customer not found"},{status:404});return n.Z.json(r)}catch(e){return console.error("Error fetching customer:",e),n.Z.json({error:"Failed to fetch customer"},{status:500})}}async function w(e,{params:t}){try{let r=await (0,l.getServerSession)(u.L);if(!r?.user?.id)return n.Z.json({error:"Unauthorized"},{status:401});let o=await e.json();console.log("Customer update request body:",JSON.stringify(o,null,2));let a=p.parse(o);console.log("Validated customer data:",JSON.stringify(a,null,2));let s={};Object.entries(a).forEach(([e,t])=>{"company"===e?s.companyName=""===t?null:t:s[e]=""===t?null:t});let i=await c._.customer.findFirst({where:{id:t.id,companyId:r.user.companyId||void 0}});if(!i)return n.Z.json({error:"Customer not found"},{status:404});if(s.email&&s.email!==i.email&&await c._.customer.findFirst({where:{email:s.email,companyId:r.user.companyId||void 0,id:{not:t.id}}}))return n.Z.json({error:"Customer with this email already exists"},{status:400});let d=await c._.customer.update({where:{id:t.id},data:s,include:{createdBy:{select:{name:!0,email:!0}},_count:{select:{leads:!0,quotations:!0,invoices:!0,activities:!0}}}});return await c._.activity.create({data:{type:"NOTE",title:"Customer Updated",description:`Customer "${d.name}" was updated`,customerId:d.id,companyId:r.user.companyId,createdById:r.user.id}}),n.Z.json(d)}catch(e){if(e instanceof m.jm)return console.error("Customer validation error:",e.errors),n.Z.json({error:"Validation failed",details:e.errors,message:e.errors.map(e=>`${e.path.join(".")}: ${e.message}`).join(", ")},{status:400});return console.error("Error updating customer:",e),n.Z.json({error:"Failed to update customer"},{status:500})}}async function f(e,{params:t}){try{let e=await (0,l.getServerSession)(u.L);if(!e?.user?.id)return n.Z.json({error:"Unauthorized"},{status:401});let r=await c._.customer.findFirst({where:{id:t.id,companyId:e.user.companyId||void 0},include:{_count:{select:{leads:!0,quotations:!0,invoices:!0,contracts:!0}}}});if(!r)return n.Z.json({error:"Customer not found"},{status:404});if(r._count.leads>0||r._count.quotations>0||r._count.invoices>0||r._count.contracts>0)return n.Z.json({error:"Cannot delete customer with existing leads, quotations, invoices, or contracts",details:r._count},{status:400});return await c._.customer.delete({where:{id:t.id}}),n.Z.json({message:"Customer deleted successfully"})}catch(e){return console.error("Error deleting customer:",e),n.Z.json({error:"Failed to delete customer"},{status:500})}}let g=new a.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/customers/[id]/route",pathname:"/api/customers/[id]",filename:"route",bundlePath:"app/api/customers/[id]/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\customers\\[id]\\route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:h,staticGenerationAsyncStorage:I,serverHooks:_,headerHooks:v,staticGenerationBailout:Z}=g,j="/api/customers/[id]/route";function q(){return(0,i.patchFetch)({serverHooks:_,staticGenerationAsyncStorage:I})}},3205:(e,t,r)=>{r.d(t,{L:()=>u});var o=r(86485),a=r(10375),s=r(50694),i=r(6521),n=r.n(i),l=r(9108);let u={providers:[(0,o.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await l._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),r=t?.companyId;if(!r&&t){let e=await l._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(r=e?.id)&&await l._.user.update({where:{id:t.id},data:{companyId:r}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await n().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await l._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:r}}catch(e){return console.error("Authentication error:",e),null}}}),(0,a.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,s.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,r)=>{r.d(t,{_:()=>a});let o=require("@prisma/client"),a=globalThis.prisma??new o.PrismaClient}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[1638,6206,6521,2455,4520,5252],()=>r(63127));module.exports=o})();