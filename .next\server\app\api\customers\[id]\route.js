"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/customers/[id]/route";
exports.ids = ["app/api/customers/[id]/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcustomers%2F%5Bid%5D%2Froute&page=%2Fapi%2Fcustomers%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcustomers%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcustomers%2F%5Bid%5D%2Froute&page=%2Fapi%2Fcustomers%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcustomers%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_proj_nextjs_saas_app_api_customers_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/customers/[id]/route.ts */ \"(rsc)/./app/api/customers/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/customers/[id]/route\",\n        pathname: \"/api/customers/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/customers/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\api\\\\customers\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_proj_nextjs_saas_app_api_customers_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/customers/[id]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcustomers%2F%5Bid%5D%2Froute&page=%2Fapi%2Fcustomers%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcustomers%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/customers/[id]/route.ts":
/*!*****************************************!*\
  !*** ./app/api/customers/[id]/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/ZodError.js\");\n\n\n\n\n\n// Validation schema for customer update\nconst customerUpdateSchema = zod__WEBPACK_IMPORTED_MODULE_4__.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_4__.string().min(1, \"Name is required\").optional(),\n    email: zod__WEBPACK_IMPORTED_MODULE_4__.string().email(\"Invalid email\").optional().nullable().or(zod__WEBPACK_IMPORTED_MODULE_4__.literal(\"\")),\n    phone: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable().or(zod__WEBPACK_IMPORTED_MODULE_4__.literal(\"\")),\n    company: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable().or(zod__WEBPACK_IMPORTED_MODULE_4__.literal(\"\")),\n    address: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable().or(zod__WEBPACK_IMPORTED_MODULE_4__.literal(\"\")),\n    city: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable().or(zod__WEBPACK_IMPORTED_MODULE_4__.literal(\"\")),\n    state: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable().or(zod__WEBPACK_IMPORTED_MODULE_4__.literal(\"\")),\n    country: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable().or(zod__WEBPACK_IMPORTED_MODULE_4__.literal(\"\")),\n    postalCode: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable().or(zod__WEBPACK_IMPORTED_MODULE_4__.literal(\"\")),\n    industry: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable().or(zod__WEBPACK_IMPORTED_MODULE_4__.literal(\"\")),\n    website: zod__WEBPACK_IMPORTED_MODULE_4__.string().url(\"Invalid website URL\").optional().nullable().or(zod__WEBPACK_IMPORTED_MODULE_4__.literal(\"\")),\n    notes: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable().or(zod__WEBPACK_IMPORTED_MODULE_4__.literal(\"\")),\n    tags: zod__WEBPACK_IMPORTED_MODULE_4__.array(zod__WEBPACK_IMPORTED_MODULE_4__.string()).optional().default([]),\n    status: zod__WEBPACK_IMPORTED_MODULE_4__[\"enum\"]([\n        \"ACTIVE\",\n        \"INACTIVE\",\n        \"PROSPECT\"\n    ]).optional()\n});\n// GET /api/customers/[id] - Get single customer\nasync function GET(request, { params }) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const customer = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.customer.findFirst({\n            where: {\n                id: params.id,\n                companyId: session.user.companyId || undefined\n            },\n            include: {\n                createdBy: {\n                    select: {\n                        name: true,\n                        email: true\n                    }\n                },\n                leads: {\n                    orderBy: {\n                        createdAt: \"desc\"\n                    },\n                    take: 5,\n                    include: {\n                        createdBy: {\n                            select: {\n                                name: true\n                            }\n                        }\n                    }\n                },\n                quotations: {\n                    orderBy: {\n                        createdAt: \"desc\"\n                    },\n                    take: 5,\n                    include: {\n                        createdBy: {\n                            select: {\n                                name: true\n                            }\n                        }\n                    }\n                },\n                invoices: {\n                    orderBy: {\n                        createdAt: \"desc\"\n                    },\n                    take: 5,\n                    include: {\n                        createdBy: {\n                            select: {\n                                name: true\n                            }\n                        }\n                    }\n                },\n                activities: {\n                    orderBy: {\n                        createdAt: \"desc\"\n                    },\n                    take: 10,\n                    include: {\n                        createdBy: {\n                            select: {\n                                name: true\n                            }\n                        }\n                    }\n                },\n                _count: {\n                    select: {\n                        leads: true,\n                        quotations: true,\n                        invoices: true,\n                        contracts: true,\n                        activities: true\n                    }\n                }\n            }\n        });\n        if (!customer) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Customer not found\"\n            }, {\n                status: 404\n            });\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(customer);\n    } catch (error) {\n        console.error(\"Error fetching customer:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to fetch customer\"\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT /api/customers/[id] - Update customer\nasync function PUT(request, { params }) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        console.log(\"Customer update request body:\", JSON.stringify(body, null, 2));\n        const validatedData = customerUpdateSchema.parse(body);\n        console.log(\"Validated customer data:\", JSON.stringify(validatedData, null, 2));\n        // Transform empty strings to null for database\n        const transformedData = Object.fromEntries(Object.entries(validatedData).map(([key, value])=>[\n                key,\n                value === \"\" ? null : value\n            ]));\n        // Check if customer exists and belongs to user's company\n        const existingCustomer = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.customer.findFirst({\n            where: {\n                id: params.id,\n                companyId: session.user.companyId || undefined\n            }\n        });\n        if (!existingCustomer) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Customer not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Check if email is being changed and if it conflicts with another customer\n        if (transformedData.email && transformedData.email !== existingCustomer.email) {\n            const emailConflict = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.customer.findFirst({\n                where: {\n                    email: transformedData.email,\n                    companyId: session.user.companyId || undefined,\n                    id: {\n                        not: params.id\n                    }\n                }\n            });\n            if (emailConflict) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"Customer with this email already exists\"\n                }, {\n                    status: 400\n                });\n            }\n        }\n        const customer = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.customer.update({\n            where: {\n                id: params.id\n            },\n            data: transformedData,\n            include: {\n                createdBy: {\n                    select: {\n                        name: true,\n                        email: true\n                    }\n                },\n                _count: {\n                    select: {\n                        leads: true,\n                        quotations: true,\n                        invoices: true,\n                        activities: true\n                    }\n                }\n            }\n        });\n        // Log activity\n        await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.activity.create({\n            data: {\n                type: \"NOTE\",\n                title: \"Customer Updated\",\n                description: `Customer \"${customer.name}\" was updated`,\n                customerId: customer.id,\n                companyId: session.user.companyId,\n                createdById: session.user.id\n            }\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(customer);\n    } catch (error) {\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_5__.ZodError) {\n            console.error(\"Customer validation error:\", error.errors);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Validation failed\",\n                details: error.errors,\n                message: error.errors.map((e)=>`${e.path.join(\".\")}: ${e.message}`).join(\", \")\n            }, {\n                status: 400\n            });\n        }\n        console.error(\"Error updating customer:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to update customer\"\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE /api/customers/[id] - Delete customer\nasync function DELETE(request, { params }) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        // Check if customer exists and belongs to user's company\n        const customer = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.customer.findFirst({\n            where: {\n                id: params.id,\n                companyId: session.user.companyId || undefined\n            },\n            include: {\n                _count: {\n                    select: {\n                        leads: true,\n                        quotations: true,\n                        invoices: true,\n                        contracts: true\n                    }\n                }\n            }\n        });\n        if (!customer) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Customer not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Check if customer has related records\n        const hasRelatedRecords = customer._count.leads > 0 || customer._count.quotations > 0 || customer._count.invoices > 0 || customer._count.contracts > 0;\n        if (hasRelatedRecords) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Cannot delete customer with existing leads, quotations, invoices, or contracts\",\n                details: customer._count\n            }, {\n                status: 400\n            });\n        }\n        await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.customer.delete({\n            where: {\n                id: params.id\n            }\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            message: \"Customer deleted successfully\"\n        });\n    } catch (error) {\n        console.error(\"Error deleting customer:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to delete customer\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvYXBpL2N1c3RvbWVycy9baWRdL3JvdXRlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBdUQ7QUFDWDtBQUNKO0FBQ0g7QUFDZDtBQUV2Qix3Q0FBd0M7QUFDeEMsTUFBTUssdUJBQXVCRCx1Q0FBUSxDQUFDO0lBQ3BDRyxNQUFNSCx1Q0FBUSxHQUFHSyxHQUFHLENBQUMsR0FBRyxvQkFBb0JDLFFBQVE7SUFDcERDLE9BQU9QLHVDQUFRLEdBQUdPLEtBQUssQ0FBQyxpQkFBaUJELFFBQVEsR0FBR0UsUUFBUSxHQUFHQyxFQUFFLENBQUNULHdDQUFTLENBQUM7SUFDNUVXLE9BQU9YLHVDQUFRLEdBQUdNLFFBQVEsR0FBR0UsUUFBUSxHQUFHQyxFQUFFLENBQUNULHdDQUFTLENBQUM7SUFDckRZLFNBQVNaLHVDQUFRLEdBQUdNLFFBQVEsR0FBR0UsUUFBUSxHQUFHQyxFQUFFLENBQUNULHdDQUFTLENBQUM7SUFDdkRhLFNBQVNiLHVDQUFRLEdBQUdNLFFBQVEsR0FBR0UsUUFBUSxHQUFHQyxFQUFFLENBQUNULHdDQUFTLENBQUM7SUFDdkRjLE1BQU1kLHVDQUFRLEdBQUdNLFFBQVEsR0FBR0UsUUFBUSxHQUFHQyxFQUFFLENBQUNULHdDQUFTLENBQUM7SUFDcERlLE9BQU9mLHVDQUFRLEdBQUdNLFFBQVEsR0FBR0UsUUFBUSxHQUFHQyxFQUFFLENBQUNULHdDQUFTLENBQUM7SUFDckRnQixTQUFTaEIsdUNBQVEsR0FBR00sUUFBUSxHQUFHRSxRQUFRLEdBQUdDLEVBQUUsQ0FBQ1Qsd0NBQVMsQ0FBQztJQUN2RGlCLFlBQVlqQix1Q0FBUSxHQUFHTSxRQUFRLEdBQUdFLFFBQVEsR0FBR0MsRUFBRSxDQUFDVCx3Q0FBUyxDQUFDO0lBQzFEa0IsVUFBVWxCLHVDQUFRLEdBQUdNLFFBQVEsR0FBR0UsUUFBUSxHQUFHQyxFQUFFLENBQUNULHdDQUFTLENBQUM7SUFDeERtQixTQUFTbkIsdUNBQVEsR0FBR29CLEdBQUcsQ0FBQyx1QkFBdUJkLFFBQVEsR0FBR0UsUUFBUSxHQUFHQyxFQUFFLENBQUNULHdDQUFTLENBQUM7SUFDbEZxQixPQUFPckIsdUNBQVEsR0FBR00sUUFBUSxHQUFHRSxRQUFRLEdBQUdDLEVBQUUsQ0FBQ1Qsd0NBQVMsQ0FBQztJQUNyRHNCLE1BQU10QixzQ0FBTyxDQUFDQSx1Q0FBUSxJQUFJTSxRQUFRLEdBQUdrQixPQUFPLENBQUMsRUFBRTtJQUMvQ0MsUUFBUXpCLHdDQUFNLENBQUM7UUFBQztRQUFVO1FBQVk7S0FBVyxFQUFFTSxRQUFRO0FBQzdEO0FBRUEsZ0RBQWdEO0FBQ3pDLGVBQWVxQixJQUNwQkMsT0FBb0IsRUFDcEIsRUFBRUMsTUFBTSxFQUE4QjtJQUV0QyxJQUFJO1FBQ0YsTUFBTUMsVUFBVSxNQUFNakMsMkRBQWdCQSxDQUFDQyxrREFBV0E7UUFDbEQsSUFBSSxDQUFDZ0MsU0FBU0MsTUFBTUMsSUFBSTtZQUN0QixPQUFPcEMsa0ZBQVlBLENBQUNxQyxJQUFJLENBQUM7Z0JBQUVDLE9BQU87WUFBZSxHQUFHO2dCQUFFVCxRQUFRO1lBQUk7UUFDcEU7UUFFQSxNQUFNVSxXQUFXLE1BQU1wQywrQ0FBTUEsQ0FBQ29DLFFBQVEsQ0FBQ0MsU0FBUyxDQUFDO1lBQy9DQyxPQUFPO2dCQUNMTCxJQUFJSCxPQUFPRyxFQUFFO2dCQUNiTSxXQUFXUixRQUFRQyxJQUFJLENBQUNPLFNBQVMsSUFBSUM7WUFDdkM7WUFDQUMsU0FBUztnQkFDUEMsV0FBVztvQkFDVEMsUUFBUTt3QkFBRXZDLE1BQU07d0JBQU1JLE9BQU87b0JBQUs7Z0JBQ3BDO2dCQUNBb0MsT0FBTztvQkFDTEMsU0FBUzt3QkFBRUMsV0FBVztvQkFBTztvQkFDN0JDLE1BQU07b0JBQ05OLFNBQVM7d0JBQ1BDLFdBQVc7NEJBQUVDLFFBQVE7Z0NBQUV2QyxNQUFNOzRCQUFLO3dCQUFFO29CQUN0QztnQkFDRjtnQkFDQTRDLFlBQVk7b0JBQ1ZILFNBQVM7d0JBQUVDLFdBQVc7b0JBQU87b0JBQzdCQyxNQUFNO29CQUNOTixTQUFTO3dCQUNQQyxXQUFXOzRCQUFFQyxRQUFRO2dDQUFFdkMsTUFBTTs0QkFBSzt3QkFBRTtvQkFDdEM7Z0JBQ0Y7Z0JBQ0E2QyxVQUFVO29CQUNSSixTQUFTO3dCQUFFQyxXQUFXO29CQUFPO29CQUM3QkMsTUFBTTtvQkFDTk4sU0FBUzt3QkFDUEMsV0FBVzs0QkFBRUMsUUFBUTtnQ0FBRXZDLE1BQU07NEJBQUs7d0JBQUU7b0JBQ3RDO2dCQUNGO2dCQUNBOEMsWUFBWTtvQkFDVkwsU0FBUzt3QkFBRUMsV0FBVztvQkFBTztvQkFDN0JDLE1BQU07b0JBQ05OLFNBQVM7d0JBQ1BDLFdBQVc7NEJBQUVDLFFBQVE7Z0NBQUV2QyxNQUFNOzRCQUFLO3dCQUFFO29CQUN0QztnQkFDRjtnQkFDQStDLFFBQVE7b0JBQ05SLFFBQVE7d0JBQ05DLE9BQU87d0JBQ1BJLFlBQVk7d0JBQ1pDLFVBQVU7d0JBQ1ZHLFdBQVc7d0JBQ1hGLFlBQVk7b0JBQ2Q7Z0JBQ0Y7WUFDRjtRQUNGO1FBRUEsSUFBSSxDQUFDZCxVQUFVO1lBQ2IsT0FBT3ZDLGtGQUFZQSxDQUFDcUMsSUFBSSxDQUFDO2dCQUFFQyxPQUFPO1lBQXFCLEdBQUc7Z0JBQUVULFFBQVE7WUFBSTtRQUMxRTtRQUVBLE9BQU83QixrRkFBWUEsQ0FBQ3FDLElBQUksQ0FBQ0U7SUFDM0IsRUFBRSxPQUFPRCxPQUFPO1FBQ2RrQixRQUFRbEIsS0FBSyxDQUFDLDRCQUE0QkE7UUFDMUMsT0FBT3RDLGtGQUFZQSxDQUFDcUMsSUFBSSxDQUN0QjtZQUFFQyxPQUFPO1FBQTJCLEdBQ3BDO1lBQUVULFFBQVE7UUFBSTtJQUVsQjtBQUNGO0FBRUEsNENBQTRDO0FBQ3JDLGVBQWU0QixJQUNwQnpCLE9BQW9CLEVBQ3BCLEVBQUVDLE1BQU0sRUFBOEI7SUFFdEMsSUFBSTtRQUNGLE1BQU1DLFVBQVUsTUFBTWpDLDJEQUFnQkEsQ0FBQ0Msa0RBQVdBO1FBQ2xELElBQUksQ0FBQ2dDLFNBQVNDLE1BQU1DLElBQUk7WUFDdEIsT0FBT3BDLGtGQUFZQSxDQUFDcUMsSUFBSSxDQUFDO2dCQUFFQyxPQUFPO1lBQWUsR0FBRztnQkFBRVQsUUFBUTtZQUFJO1FBQ3BFO1FBRUEsTUFBTTZCLE9BQU8sTUFBTTFCLFFBQVFLLElBQUk7UUFDL0JtQixRQUFRRyxHQUFHLENBQUMsaUNBQWlDQyxLQUFLQyxTQUFTLENBQUNILE1BQU0sTUFBTTtRQUV4RSxNQUFNSSxnQkFBZ0J6RCxxQkFBcUIwRCxLQUFLLENBQUNMO1FBQ2pERixRQUFRRyxHQUFHLENBQUMsNEJBQTRCQyxLQUFLQyxTQUFTLENBQUNDLGVBQWUsTUFBTTtRQUU1RSwrQ0FBK0M7UUFDL0MsTUFBTUUsa0JBQWtCQyxPQUFPQyxXQUFXLENBQ3hDRCxPQUFPRSxPQUFPLENBQUNMLGVBQWVNLEdBQUcsQ0FBQyxDQUFDLENBQUNDLEtBQUtDLE1BQU0sR0FBSztnQkFDbEREO2dCQUNBQyxVQUFVLEtBQUssT0FBT0E7YUFDdkI7UUFHSCx5REFBeUQ7UUFDekQsTUFBTUMsbUJBQW1CLE1BQU1wRSwrQ0FBTUEsQ0FBQ29DLFFBQVEsQ0FBQ0MsU0FBUyxDQUFDO1lBQ3ZEQyxPQUFPO2dCQUNMTCxJQUFJSCxPQUFPRyxFQUFFO2dCQUNiTSxXQUFXUixRQUFRQyxJQUFJLENBQUNPLFNBQVMsSUFBSUM7WUFDdkM7UUFDRjtRQUVBLElBQUksQ0FBQzRCLGtCQUFrQjtZQUNyQixPQUFPdkUsa0ZBQVlBLENBQUNxQyxJQUFJLENBQUM7Z0JBQUVDLE9BQU87WUFBcUIsR0FBRztnQkFBRVQsUUFBUTtZQUFJO1FBQzFFO1FBRUEsNEVBQTRFO1FBQzVFLElBQUltQyxnQkFBZ0JyRCxLQUFLLElBQUlxRCxnQkFBZ0JyRCxLQUFLLEtBQUs0RCxpQkFBaUI1RCxLQUFLLEVBQUU7WUFDN0UsTUFBTTZELGdCQUFnQixNQUFNckUsK0NBQU1BLENBQUNvQyxRQUFRLENBQUNDLFNBQVMsQ0FBQztnQkFDcERDLE9BQU87b0JBQ0w5QixPQUFPcUQsZ0JBQWdCckQsS0FBSztvQkFDNUIrQixXQUFXUixRQUFRQyxJQUFJLENBQUNPLFNBQVMsSUFBSUM7b0JBQ3JDUCxJQUFJO3dCQUFFcUMsS0FBS3hDLE9BQU9HLEVBQUU7b0JBQUM7Z0JBQ3ZCO1lBQ0Y7WUFFQSxJQUFJb0MsZUFBZTtnQkFDakIsT0FBT3hFLGtGQUFZQSxDQUFDcUMsSUFBSSxDQUN0QjtvQkFBRUMsT0FBTztnQkFBMEMsR0FDbkQ7b0JBQUVULFFBQVE7Z0JBQUk7WUFFbEI7UUFDRjtRQUVBLE1BQU1VLFdBQVcsTUFBTXBDLCtDQUFNQSxDQUFDb0MsUUFBUSxDQUFDbUMsTUFBTSxDQUFDO1lBQzVDakMsT0FBTztnQkFBRUwsSUFBSUgsT0FBT0csRUFBRTtZQUFDO1lBQ3ZCdUMsTUFBTVg7WUFDTnBCLFNBQVM7Z0JBQ1BDLFdBQVc7b0JBQ1RDLFFBQVE7d0JBQUV2QyxNQUFNO3dCQUFNSSxPQUFPO29CQUFLO2dCQUNwQztnQkFDQTJDLFFBQVE7b0JBQ05SLFFBQVE7d0JBQ05DLE9BQU87d0JBQ1BJLFlBQVk7d0JBQ1pDLFVBQVU7d0JBQ1ZDLFlBQVk7b0JBQ2Q7Z0JBQ0Y7WUFDRjtRQUNGO1FBRUEsZUFBZTtRQUNmLE1BQU1sRCwrQ0FBTUEsQ0FBQ3lFLFFBQVEsQ0FBQ0MsTUFBTSxDQUFDO1lBQzNCRixNQUFNO2dCQUNKRyxNQUFNO2dCQUNOQyxPQUFPO2dCQUNQQyxhQUFhLENBQUMsVUFBVSxFQUFFekMsU0FBU2hDLElBQUksQ0FBQyxhQUFhLENBQUM7Z0JBQ3REMEUsWUFBWTFDLFNBQVNILEVBQUU7Z0JBQ3ZCTSxXQUFXUixRQUFRQyxJQUFJLENBQUNPLFNBQVM7Z0JBQ2pDd0MsYUFBYWhELFFBQVFDLElBQUksQ0FBQ0MsRUFBRTtZQUM5QjtRQUNGO1FBRUEsT0FBT3BDLGtGQUFZQSxDQUFDcUMsSUFBSSxDQUFDRTtJQUMzQixFQUFFLE9BQU9ELE9BQU87UUFDZCxJQUFJQSxpQkFBaUJsQyx5Q0FBVSxFQUFFO1lBQy9Cb0QsUUFBUWxCLEtBQUssQ0FBQyw4QkFBOEJBLE1BQU04QyxNQUFNO1lBQ3hELE9BQU9wRixrRkFBWUEsQ0FBQ3FDLElBQUksQ0FDdEI7Z0JBQ0VDLE9BQU87Z0JBQ1ArQyxTQUFTL0MsTUFBTThDLE1BQU07Z0JBQ3JCRSxTQUFTaEQsTUFBTThDLE1BQU0sQ0FBQ2hCLEdBQUcsQ0FBQ21CLENBQUFBLElBQUssQ0FBQyxFQUFFQSxFQUFFQyxJQUFJLENBQUNDLElBQUksQ0FBQyxLQUFLLEVBQUUsRUFBRUYsRUFBRUQsT0FBTyxDQUFDLENBQUMsRUFBRUcsSUFBSSxDQUFDO1lBQzNFLEdBQ0E7Z0JBQUU1RCxRQUFRO1lBQUk7UUFFbEI7UUFFQTJCLFFBQVFsQixLQUFLLENBQUMsNEJBQTRCQTtRQUMxQyxPQUFPdEMsa0ZBQVlBLENBQUNxQyxJQUFJLENBQ3RCO1lBQUVDLE9BQU87UUFBNEIsR0FDckM7WUFBRVQsUUFBUTtRQUFJO0lBRWxCO0FBQ0Y7QUFFQSwrQ0FBK0M7QUFDeEMsZUFBZTZELE9BQ3BCMUQsT0FBb0IsRUFDcEIsRUFBRUMsTUFBTSxFQUE4QjtJQUV0QyxJQUFJO1FBQ0YsTUFBTUMsVUFBVSxNQUFNakMsMkRBQWdCQSxDQUFDQyxrREFBV0E7UUFDbEQsSUFBSSxDQUFDZ0MsU0FBU0MsTUFBTUMsSUFBSTtZQUN0QixPQUFPcEMsa0ZBQVlBLENBQUNxQyxJQUFJLENBQUM7Z0JBQUVDLE9BQU87WUFBZSxHQUFHO2dCQUFFVCxRQUFRO1lBQUk7UUFDcEU7UUFFQSx5REFBeUQ7UUFDekQsTUFBTVUsV0FBVyxNQUFNcEMsK0NBQU1BLENBQUNvQyxRQUFRLENBQUNDLFNBQVMsQ0FBQztZQUMvQ0MsT0FBTztnQkFDTEwsSUFBSUgsT0FBT0csRUFBRTtnQkFDYk0sV0FBV1IsUUFBUUMsSUFBSSxDQUFDTyxTQUFTLElBQUlDO1lBQ3ZDO1lBQ0FDLFNBQVM7Z0JBQ1BVLFFBQVE7b0JBQ05SLFFBQVE7d0JBQ05DLE9BQU87d0JBQ1BJLFlBQVk7d0JBQ1pDLFVBQVU7d0JBQ1ZHLFdBQVc7b0JBQ2I7Z0JBQ0Y7WUFDRjtRQUNGO1FBRUEsSUFBSSxDQUFDaEIsVUFBVTtZQUNiLE9BQU92QyxrRkFBWUEsQ0FBQ3FDLElBQUksQ0FBQztnQkFBRUMsT0FBTztZQUFxQixHQUFHO2dCQUFFVCxRQUFRO1lBQUk7UUFDMUU7UUFFQSx3Q0FBd0M7UUFDeEMsTUFBTThELG9CQUFvQnBELFNBQVNlLE1BQU0sQ0FBQ1AsS0FBSyxHQUFHLEtBQ3pCUixTQUFTZSxNQUFNLENBQUNILFVBQVUsR0FBRyxLQUM3QlosU0FBU2UsTUFBTSxDQUFDRixRQUFRLEdBQUcsS0FDM0JiLFNBQVNlLE1BQU0sQ0FBQ0MsU0FBUyxHQUFHO1FBRXJELElBQUlvQyxtQkFBbUI7WUFDckIsT0FBTzNGLGtGQUFZQSxDQUFDcUMsSUFBSSxDQUN0QjtnQkFDRUMsT0FBTztnQkFDUCtDLFNBQVM5QyxTQUFTZSxNQUFNO1lBQzFCLEdBQ0E7Z0JBQUV6QixRQUFRO1lBQUk7UUFFbEI7UUFFQSxNQUFNMUIsK0NBQU1BLENBQUNvQyxRQUFRLENBQUNxRCxNQUFNLENBQUM7WUFDM0JuRCxPQUFPO2dCQUFFTCxJQUFJSCxPQUFPRyxFQUFFO1lBQUM7UUFDekI7UUFFQSxPQUFPcEMsa0ZBQVlBLENBQUNxQyxJQUFJLENBQUM7WUFBRWlELFNBQVM7UUFBZ0M7SUFDdEUsRUFBRSxPQUFPaEQsT0FBTztRQUNka0IsUUFBUWxCLEtBQUssQ0FBQyw0QkFBNEJBO1FBQzFDLE9BQU90QyxrRkFBWUEsQ0FBQ3FDLElBQUksQ0FDdEI7WUFBRUMsT0FBTztRQUE0QixHQUNyQztZQUFFVCxRQUFRO1FBQUk7SUFFbEI7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zYWFzLWFwcC8uL2FwcC9hcGkvY3VzdG9tZXJzL1tpZF0vcm91dGUudHM/ODAyMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVxdWVzdCwgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInXG5pbXBvcnQgeyBnZXRTZXJ2ZXJTZXNzaW9uIH0gZnJvbSAnbmV4dC1hdXRoJ1xuaW1wb3J0IHsgYXV0aE9wdGlvbnMgfSBmcm9tICdAL2xpYi9hdXRoJ1xuaW1wb3J0IHsgcHJpc21hIH0gZnJvbSAnQC9saWIvcHJpc21hJ1xuaW1wb3J0IHsgeiB9IGZyb20gJ3pvZCdcblxuLy8gVmFsaWRhdGlvbiBzY2hlbWEgZm9yIGN1c3RvbWVyIHVwZGF0ZVxuY29uc3QgY3VzdG9tZXJVcGRhdGVTY2hlbWEgPSB6Lm9iamVjdCh7XG4gIG5hbWU6IHouc3RyaW5nKCkubWluKDEsICdOYW1lIGlzIHJlcXVpcmVkJykub3B0aW9uYWwoKSxcbiAgZW1haWw6IHouc3RyaW5nKCkuZW1haWwoJ0ludmFsaWQgZW1haWwnKS5vcHRpb25hbCgpLm51bGxhYmxlKCkub3Ioei5saXRlcmFsKCcnKSksXG4gIHBob25lOiB6LnN0cmluZygpLm9wdGlvbmFsKCkubnVsbGFibGUoKS5vcih6LmxpdGVyYWwoJycpKSxcbiAgY29tcGFueTogei5zdHJpbmcoKS5vcHRpb25hbCgpLm51bGxhYmxlKCkub3Ioei5saXRlcmFsKCcnKSksXG4gIGFkZHJlc3M6IHouc3RyaW5nKCkub3B0aW9uYWwoKS5udWxsYWJsZSgpLm9yKHoubGl0ZXJhbCgnJykpLFxuICBjaXR5OiB6LnN0cmluZygpLm9wdGlvbmFsKCkubnVsbGFibGUoKS5vcih6LmxpdGVyYWwoJycpKSxcbiAgc3RhdGU6IHouc3RyaW5nKCkub3B0aW9uYWwoKS5udWxsYWJsZSgpLm9yKHoubGl0ZXJhbCgnJykpLFxuICBjb3VudHJ5OiB6LnN0cmluZygpLm9wdGlvbmFsKCkubnVsbGFibGUoKS5vcih6LmxpdGVyYWwoJycpKSxcbiAgcG9zdGFsQ29kZTogei5zdHJpbmcoKS5vcHRpb25hbCgpLm51bGxhYmxlKCkub3Ioei5saXRlcmFsKCcnKSksXG4gIGluZHVzdHJ5OiB6LnN0cmluZygpLm9wdGlvbmFsKCkubnVsbGFibGUoKS5vcih6LmxpdGVyYWwoJycpKSxcbiAgd2Vic2l0ZTogei5zdHJpbmcoKS51cmwoJ0ludmFsaWQgd2Vic2l0ZSBVUkwnKS5vcHRpb25hbCgpLm51bGxhYmxlKCkub3Ioei5saXRlcmFsKCcnKSksXG4gIG5vdGVzOiB6LnN0cmluZygpLm9wdGlvbmFsKCkubnVsbGFibGUoKS5vcih6LmxpdGVyYWwoJycpKSxcbiAgdGFnczogei5hcnJheSh6LnN0cmluZygpKS5vcHRpb25hbCgpLmRlZmF1bHQoW10pLFxuICBzdGF0dXM6IHouZW51bShbJ0FDVElWRScsICdJTkFDVElWRScsICdQUk9TUEVDVCddKS5vcHRpb25hbCgpXG59KVxuXG4vLyBHRVQgL2FwaS9jdXN0b21lcnMvW2lkXSAtIEdldCBzaW5nbGUgY3VzdG9tZXJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBHRVQoXG4gIHJlcXVlc3Q6IE5leHRSZXF1ZXN0LFxuICB7IHBhcmFtcyB9OiB7IHBhcmFtczogeyBpZDogc3RyaW5nIH0gfVxuKSB7XG4gIHRyeSB7XG4gICAgY29uc3Qgc2Vzc2lvbiA9IGF3YWl0IGdldFNlcnZlclNlc3Npb24oYXV0aE9wdGlvbnMpXG4gICAgaWYgKCFzZXNzaW9uPy51c2VyPy5pZCkge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgZXJyb3I6ICdVbmF1dGhvcml6ZWQnIH0sIHsgc3RhdHVzOiA0MDEgfSlcbiAgICB9XG5cbiAgICBjb25zdCBjdXN0b21lciA9IGF3YWl0IHByaXNtYS5jdXN0b21lci5maW5kRmlyc3Qoe1xuICAgICAgd2hlcmU6IHtcbiAgICAgICAgaWQ6IHBhcmFtcy5pZCxcbiAgICAgICAgY29tcGFueUlkOiBzZXNzaW9uLnVzZXIuY29tcGFueUlkIHx8IHVuZGVmaW5lZFxuICAgICAgfSxcbiAgICAgIGluY2x1ZGU6IHtcbiAgICAgICAgY3JlYXRlZEJ5OiB7XG4gICAgICAgICAgc2VsZWN0OiB7IG5hbWU6IHRydWUsIGVtYWlsOiB0cnVlIH1cbiAgICAgICAgfSxcbiAgICAgICAgbGVhZHM6IHtcbiAgICAgICAgICBvcmRlckJ5OiB7IGNyZWF0ZWRBdDogJ2Rlc2MnIH0sXG4gICAgICAgICAgdGFrZTogNSxcbiAgICAgICAgICBpbmNsdWRlOiB7XG4gICAgICAgICAgICBjcmVhdGVkQnk6IHsgc2VsZWN0OiB7IG5hbWU6IHRydWUgfSB9XG4gICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgICBxdW90YXRpb25zOiB7XG4gICAgICAgICAgb3JkZXJCeTogeyBjcmVhdGVkQXQ6ICdkZXNjJyB9LFxuICAgICAgICAgIHRha2U6IDUsXG4gICAgICAgICAgaW5jbHVkZToge1xuICAgICAgICAgICAgY3JlYXRlZEJ5OiB7IHNlbGVjdDogeyBuYW1lOiB0cnVlIH0gfVxuICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAgaW52b2ljZXM6IHtcbiAgICAgICAgICBvcmRlckJ5OiB7IGNyZWF0ZWRBdDogJ2Rlc2MnIH0sXG4gICAgICAgICAgdGFrZTogNSxcbiAgICAgICAgICBpbmNsdWRlOiB7XG4gICAgICAgICAgICBjcmVhdGVkQnk6IHsgc2VsZWN0OiB7IG5hbWU6IHRydWUgfSB9XG4gICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgICBhY3Rpdml0aWVzOiB7XG4gICAgICAgICAgb3JkZXJCeTogeyBjcmVhdGVkQXQ6ICdkZXNjJyB9LFxuICAgICAgICAgIHRha2U6IDEwLFxuICAgICAgICAgIGluY2x1ZGU6IHtcbiAgICAgICAgICAgIGNyZWF0ZWRCeTogeyBzZWxlY3Q6IHsgbmFtZTogdHJ1ZSB9IH1cbiAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICAgIF9jb3VudDoge1xuICAgICAgICAgIHNlbGVjdDoge1xuICAgICAgICAgICAgbGVhZHM6IHRydWUsXG4gICAgICAgICAgICBxdW90YXRpb25zOiB0cnVlLFxuICAgICAgICAgICAgaW52b2ljZXM6IHRydWUsXG4gICAgICAgICAgICBjb250cmFjdHM6IHRydWUsXG4gICAgICAgICAgICBhY3Rpdml0aWVzOiB0cnVlXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfSlcblxuICAgIGlmICghY3VzdG9tZXIpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiAnQ3VzdG9tZXIgbm90IGZvdW5kJyB9LCB7IHN0YXR1czogNDA0IH0pXG4gICAgfVxuXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKGN1c3RvbWVyKVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIGN1c3RvbWVyOicsIGVycm9yKVxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgZXJyb3I6ICdGYWlsZWQgdG8gZmV0Y2ggY3VzdG9tZXInIH0sXG4gICAgICB7IHN0YXR1czogNTAwIH1cbiAgICApXG4gIH1cbn1cblxuLy8gUFVUIC9hcGkvY3VzdG9tZXJzL1tpZF0gLSBVcGRhdGUgY3VzdG9tZXJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBQVVQoXG4gIHJlcXVlc3Q6IE5leHRSZXF1ZXN0LFxuICB7IHBhcmFtcyB9OiB7IHBhcmFtczogeyBpZDogc3RyaW5nIH0gfVxuKSB7XG4gIHRyeSB7XG4gICAgY29uc3Qgc2Vzc2lvbiA9IGF3YWl0IGdldFNlcnZlclNlc3Npb24oYXV0aE9wdGlvbnMpXG4gICAgaWYgKCFzZXNzaW9uPy51c2VyPy5pZCkge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgZXJyb3I6ICdVbmF1dGhvcml6ZWQnIH0sIHsgc3RhdHVzOiA0MDEgfSlcbiAgICB9XG5cbiAgICBjb25zdCBib2R5ID0gYXdhaXQgcmVxdWVzdC5qc29uKClcbiAgICBjb25zb2xlLmxvZygnQ3VzdG9tZXIgdXBkYXRlIHJlcXVlc3QgYm9keTonLCBKU09OLnN0cmluZ2lmeShib2R5LCBudWxsLCAyKSlcblxuICAgIGNvbnN0IHZhbGlkYXRlZERhdGEgPSBjdXN0b21lclVwZGF0ZVNjaGVtYS5wYXJzZShib2R5KVxuICAgIGNvbnNvbGUubG9nKCdWYWxpZGF0ZWQgY3VzdG9tZXIgZGF0YTonLCBKU09OLnN0cmluZ2lmeSh2YWxpZGF0ZWREYXRhLCBudWxsLCAyKSlcblxuICAgIC8vIFRyYW5zZm9ybSBlbXB0eSBzdHJpbmdzIHRvIG51bGwgZm9yIGRhdGFiYXNlXG4gICAgY29uc3QgdHJhbnNmb3JtZWREYXRhID0gT2JqZWN0LmZyb21FbnRyaWVzKFxuICAgICAgT2JqZWN0LmVudHJpZXModmFsaWRhdGVkRGF0YSkubWFwKChba2V5LCB2YWx1ZV0pID0+IFtcbiAgICAgICAga2V5LFxuICAgICAgICB2YWx1ZSA9PT0gJycgPyBudWxsIDogdmFsdWVcbiAgICAgIF0pXG4gICAgKVxuXG4gICAgLy8gQ2hlY2sgaWYgY3VzdG9tZXIgZXhpc3RzIGFuZCBiZWxvbmdzIHRvIHVzZXIncyBjb21wYW55XG4gICAgY29uc3QgZXhpc3RpbmdDdXN0b21lciA9IGF3YWl0IHByaXNtYS5jdXN0b21lci5maW5kRmlyc3Qoe1xuICAgICAgd2hlcmU6IHtcbiAgICAgICAgaWQ6IHBhcmFtcy5pZCxcbiAgICAgICAgY29tcGFueUlkOiBzZXNzaW9uLnVzZXIuY29tcGFueUlkIHx8IHVuZGVmaW5lZFxuICAgICAgfVxuICAgIH0pXG5cbiAgICBpZiAoIWV4aXN0aW5nQ3VzdG9tZXIpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiAnQ3VzdG9tZXIgbm90IGZvdW5kJyB9LCB7IHN0YXR1czogNDA0IH0pXG4gICAgfVxuXG4gICAgLy8gQ2hlY2sgaWYgZW1haWwgaXMgYmVpbmcgY2hhbmdlZCBhbmQgaWYgaXQgY29uZmxpY3RzIHdpdGggYW5vdGhlciBjdXN0b21lclxuICAgIGlmICh0cmFuc2Zvcm1lZERhdGEuZW1haWwgJiYgdHJhbnNmb3JtZWREYXRhLmVtYWlsICE9PSBleGlzdGluZ0N1c3RvbWVyLmVtYWlsKSB7XG4gICAgICBjb25zdCBlbWFpbENvbmZsaWN0ID0gYXdhaXQgcHJpc21hLmN1c3RvbWVyLmZpbmRGaXJzdCh7XG4gICAgICAgIHdoZXJlOiB7XG4gICAgICAgICAgZW1haWw6IHRyYW5zZm9ybWVkRGF0YS5lbWFpbCxcbiAgICAgICAgICBjb21wYW55SWQ6IHNlc3Npb24udXNlci5jb21wYW55SWQgfHwgdW5kZWZpbmVkLFxuICAgICAgICAgIGlkOiB7IG5vdDogcGFyYW1zLmlkIH1cbiAgICAgICAgfVxuICAgICAgfSlcblxuICAgICAgaWYgKGVtYWlsQ29uZmxpY3QpIHtcbiAgICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICAgIHsgZXJyb3I6ICdDdXN0b21lciB3aXRoIHRoaXMgZW1haWwgYWxyZWFkeSBleGlzdHMnIH0sXG4gICAgICAgICAgeyBzdGF0dXM6IDQwMCB9XG4gICAgICAgIClcbiAgICAgIH1cbiAgICB9XG5cbiAgICBjb25zdCBjdXN0b21lciA9IGF3YWl0IHByaXNtYS5jdXN0b21lci51cGRhdGUoe1xuICAgICAgd2hlcmU6IHsgaWQ6IHBhcmFtcy5pZCB9LFxuICAgICAgZGF0YTogdHJhbnNmb3JtZWREYXRhLFxuICAgICAgaW5jbHVkZToge1xuICAgICAgICBjcmVhdGVkQnk6IHtcbiAgICAgICAgICBzZWxlY3Q6IHsgbmFtZTogdHJ1ZSwgZW1haWw6IHRydWUgfVxuICAgICAgICB9LFxuICAgICAgICBfY291bnQ6IHtcbiAgICAgICAgICBzZWxlY3Q6IHtcbiAgICAgICAgICAgIGxlYWRzOiB0cnVlLFxuICAgICAgICAgICAgcXVvdGF0aW9uczogdHJ1ZSxcbiAgICAgICAgICAgIGludm9pY2VzOiB0cnVlLFxuICAgICAgICAgICAgYWN0aXZpdGllczogdHJ1ZVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0pXG5cbiAgICAvLyBMb2cgYWN0aXZpdHlcbiAgICBhd2FpdCBwcmlzbWEuYWN0aXZpdHkuY3JlYXRlKHtcbiAgICAgIGRhdGE6IHtcbiAgICAgICAgdHlwZTogJ05PVEUnLFxuICAgICAgICB0aXRsZTogJ0N1c3RvbWVyIFVwZGF0ZWQnLFxuICAgICAgICBkZXNjcmlwdGlvbjogYEN1c3RvbWVyIFwiJHtjdXN0b21lci5uYW1lfVwiIHdhcyB1cGRhdGVkYCxcbiAgICAgICAgY3VzdG9tZXJJZDogY3VzdG9tZXIuaWQsXG4gICAgICAgIGNvbXBhbnlJZDogc2Vzc2lvbi51c2VyLmNvbXBhbnlJZCEsXG4gICAgICAgIGNyZWF0ZWRCeUlkOiBzZXNzaW9uLnVzZXIuaWRcbiAgICAgIH1cbiAgICB9KVxuXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKGN1c3RvbWVyKVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGlmIChlcnJvciBpbnN0YW5jZW9mIHouWm9kRXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0N1c3RvbWVyIHZhbGlkYXRpb24gZXJyb3I6JywgZXJyb3IuZXJyb3JzKVxuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7XG4gICAgICAgICAgZXJyb3I6ICdWYWxpZGF0aW9uIGZhaWxlZCcsXG4gICAgICAgICAgZGV0YWlsczogZXJyb3IuZXJyb3JzLFxuICAgICAgICAgIG1lc3NhZ2U6IGVycm9yLmVycm9ycy5tYXAoZSA9PiBgJHtlLnBhdGguam9pbignLicpfTogJHtlLm1lc3NhZ2V9YCkuam9pbignLCAnKVxuICAgICAgICB9LFxuICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgIClcbiAgICB9XG5cbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciB1cGRhdGluZyBjdXN0b21lcjonLCBlcnJvcilcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICB7IGVycm9yOiAnRmFpbGVkIHRvIHVwZGF0ZSBjdXN0b21lcicgfSxcbiAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgIClcbiAgfVxufVxuXG4vLyBERUxFVEUgL2FwaS9jdXN0b21lcnMvW2lkXSAtIERlbGV0ZSBjdXN0b21lclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIERFTEVURShcbiAgcmVxdWVzdDogTmV4dFJlcXVlc3QsXG4gIHsgcGFyYW1zIH06IHsgcGFyYW1zOiB7IGlkOiBzdHJpbmcgfSB9XG4pIHtcbiAgdHJ5IHtcbiAgICBjb25zdCBzZXNzaW9uID0gYXdhaXQgZ2V0U2VydmVyU2Vzc2lvbihhdXRoT3B0aW9ucylcbiAgICBpZiAoIXNlc3Npb24/LnVzZXI/LmlkKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogJ1VuYXV0aG9yaXplZCcgfSwgeyBzdGF0dXM6IDQwMSB9KVxuICAgIH1cblxuICAgIC8vIENoZWNrIGlmIGN1c3RvbWVyIGV4aXN0cyBhbmQgYmVsb25ncyB0byB1c2VyJ3MgY29tcGFueVxuICAgIGNvbnN0IGN1c3RvbWVyID0gYXdhaXQgcHJpc21hLmN1c3RvbWVyLmZpbmRGaXJzdCh7XG4gICAgICB3aGVyZToge1xuICAgICAgICBpZDogcGFyYW1zLmlkLFxuICAgICAgICBjb21wYW55SWQ6IHNlc3Npb24udXNlci5jb21wYW55SWQgfHwgdW5kZWZpbmVkXG4gICAgICB9LFxuICAgICAgaW5jbHVkZToge1xuICAgICAgICBfY291bnQ6IHtcbiAgICAgICAgICBzZWxlY3Q6IHtcbiAgICAgICAgICAgIGxlYWRzOiB0cnVlLFxuICAgICAgICAgICAgcXVvdGF0aW9uczogdHJ1ZSxcbiAgICAgICAgICAgIGludm9pY2VzOiB0cnVlLFxuICAgICAgICAgICAgY29udHJhY3RzOiB0cnVlXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfSlcblxuICAgIGlmICghY3VzdG9tZXIpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiAnQ3VzdG9tZXIgbm90IGZvdW5kJyB9LCB7IHN0YXR1czogNDA0IH0pXG4gICAgfVxuXG4gICAgLy8gQ2hlY2sgaWYgY3VzdG9tZXIgaGFzIHJlbGF0ZWQgcmVjb3Jkc1xuICAgIGNvbnN0IGhhc1JlbGF0ZWRSZWNvcmRzID0gY3VzdG9tZXIuX2NvdW50LmxlYWRzID4gMCB8fCBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3VzdG9tZXIuX2NvdW50LnF1b3RhdGlvbnMgPiAwIHx8IFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjdXN0b21lci5fY291bnQuaW52b2ljZXMgPiAwIHx8IFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjdXN0b21lci5fY291bnQuY29udHJhY3RzID4gMFxuXG4gICAgaWYgKGhhc1JlbGF0ZWRSZWNvcmRzKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHsgXG4gICAgICAgICAgZXJyb3I6ICdDYW5ub3QgZGVsZXRlIGN1c3RvbWVyIHdpdGggZXhpc3RpbmcgbGVhZHMsIHF1b3RhdGlvbnMsIGludm9pY2VzLCBvciBjb250cmFjdHMnLFxuICAgICAgICAgIGRldGFpbHM6IGN1c3RvbWVyLl9jb3VudFxuICAgICAgICB9LFxuICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgIClcbiAgICB9XG5cbiAgICBhd2FpdCBwcmlzbWEuY3VzdG9tZXIuZGVsZXRlKHtcbiAgICAgIHdoZXJlOiB7IGlkOiBwYXJhbXMuaWQgfVxuICAgIH0pXG5cbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBtZXNzYWdlOiAnQ3VzdG9tZXIgZGVsZXRlZCBzdWNjZXNzZnVsbHknIH0pXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgZGVsZXRpbmcgY3VzdG9tZXI6JywgZXJyb3IpXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgeyBlcnJvcjogJ0ZhaWxlZCB0byBkZWxldGUgY3VzdG9tZXInIH0sXG4gICAgICB7IHN0YXR1czogNTAwIH1cbiAgICApXG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJOZXh0UmVzcG9uc2UiLCJnZXRTZXJ2ZXJTZXNzaW9uIiwiYXV0aE9wdGlvbnMiLCJwcmlzbWEiLCJ6IiwiY3VzdG9tZXJVcGRhdGVTY2hlbWEiLCJvYmplY3QiLCJuYW1lIiwic3RyaW5nIiwibWluIiwib3B0aW9uYWwiLCJlbWFpbCIsIm51bGxhYmxlIiwib3IiLCJsaXRlcmFsIiwicGhvbmUiLCJjb21wYW55IiwiYWRkcmVzcyIsImNpdHkiLCJzdGF0ZSIsImNvdW50cnkiLCJwb3N0YWxDb2RlIiwiaW5kdXN0cnkiLCJ3ZWJzaXRlIiwidXJsIiwibm90ZXMiLCJ0YWdzIiwiYXJyYXkiLCJkZWZhdWx0Iiwic3RhdHVzIiwiZW51bSIsIkdFVCIsInJlcXVlc3QiLCJwYXJhbXMiLCJzZXNzaW9uIiwidXNlciIsImlkIiwianNvbiIsImVycm9yIiwiY3VzdG9tZXIiLCJmaW5kRmlyc3QiLCJ3aGVyZSIsImNvbXBhbnlJZCIsInVuZGVmaW5lZCIsImluY2x1ZGUiLCJjcmVhdGVkQnkiLCJzZWxlY3QiLCJsZWFkcyIsIm9yZGVyQnkiLCJjcmVhdGVkQXQiLCJ0YWtlIiwicXVvdGF0aW9ucyIsImludm9pY2VzIiwiYWN0aXZpdGllcyIsIl9jb3VudCIsImNvbnRyYWN0cyIsImNvbnNvbGUiLCJQVVQiLCJib2R5IiwibG9nIiwiSlNPTiIsInN0cmluZ2lmeSIsInZhbGlkYXRlZERhdGEiLCJwYXJzZSIsInRyYW5zZm9ybWVkRGF0YSIsIk9iamVjdCIsImZyb21FbnRyaWVzIiwiZW50cmllcyIsIm1hcCIsImtleSIsInZhbHVlIiwiZXhpc3RpbmdDdXN0b21lciIsImVtYWlsQ29uZmxpY3QiLCJub3QiLCJ1cGRhdGUiLCJkYXRhIiwiYWN0aXZpdHkiLCJjcmVhdGUiLCJ0eXBlIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImN1c3RvbWVySWQiLCJjcmVhdGVkQnlJZCIsIlpvZEVycm9yIiwiZXJyb3JzIiwiZGV0YWlscyIsIm1lc3NhZ2UiLCJlIiwicGF0aCIsImpvaW4iLCJERUxFVEUiLCJoYXNSZWxhdGVkUmVjb3JkcyIsImRlbGV0ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/api/customers/[id]/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/github */ \"(rsc)/./node_modules/next-auth/providers/github.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n\nconst authOptions = {\n    // Using JWT strategy instead of database adapter for now\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                try {\n                    if (!credentials?.email || !credentials?.password) {\n                        console.log(\"Missing credentials\");\n                        return null;\n                    }\n                    console.log(\"Attempting to authenticate user:\", credentials.email);\n                    const user = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.findUnique({\n                        where: {\n                            email: credentials.email\n                        },\n                        select: {\n                            id: true,\n                            email: true,\n                            name: true,\n                            password: true,\n                            role: true,\n                            companyId: true\n                        }\n                    });\n                    // Get the company ID - either as member or owner\n                    let finalCompanyId = user?.companyId;\n                    if (!finalCompanyId && user) {\n                        const ownedCompany = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.company.findFirst({\n                            where: {\n                                ownerId: user.id\n                            },\n                            select: {\n                                id: true\n                            }\n                        });\n                        finalCompanyId = ownedCompany?.id;\n                        // If user is company owner, update their companyId for future queries\n                        if (finalCompanyId) {\n                            await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                                where: {\n                                    id: user.id\n                                },\n                                data: {\n                                    companyId: finalCompanyId\n                                }\n                            });\n                        }\n                    }\n                    if (!user) {\n                        console.log(\"User not found:\", credentials.email);\n                        return null;\n                    }\n                    if (!user.password) {\n                        console.log(\"User has no password set:\", credentials.email);\n                        return null;\n                    }\n                    const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_3___default().compare(credentials.password, user.password);\n                    if (!isPasswordValid) {\n                        console.log(\"Invalid password for user:\", credentials.email);\n                        return null;\n                    }\n                    // Update last login\n                    await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                        where: {\n                            id: user.id\n                        },\n                        data: {\n                            lastLoginAt: new Date(),\n                            loginCount: {\n                                increment: 1\n                            }\n                        }\n                    });\n                    console.log(\"User authenticated successfully:\", user.email);\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name,\n                        role: user.role,\n                        companyId: finalCompanyId\n                    };\n                } catch (error) {\n                    console.error(\"Authentication error:\", error);\n                    return null;\n                }\n            }\n        }),\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            clientId: process.env.GITHUB_CLIENT_ID,\n            clientSecret: process.env.GITHUB_CLIENT_SECRET\n        })\n    ],\n    session: {\n        strategy: \"jwt\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                console.log(\"JWT callback - user data:\", {\n                    id: user.id,\n                    email: user.email,\n                    role: user.role,\n                    companyId: user.companyId\n                });\n                token.role = user.role;\n                token.companyId = user.companyId;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n                session.user.companyId = token.companyId;\n                // Ensure no company object is ever set on the session\n                if (session.user.company) {\n                    delete session.user.company;\n                }\n                console.log(\"Session callback - final session:\", {\n                    id: session.user.id,\n                    email: session.user.email,\n                    role: session.user.role,\n                    companyId: session.user.companyId\n                });\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        signUp: \"/auth/signup\",\n        error: \"/auth/error\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zYWFzLWFwcC8uL2xpYi9wcmlzbWEudHM/OTgyMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/lru-cache","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcustomers%2F%5Bid%5D%2Froute&page=%2Fapi%2Fcustomers%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcustomers%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();