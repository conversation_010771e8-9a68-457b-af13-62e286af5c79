(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3105],{90998:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62898).Z)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]])},72894:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62898).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},13008:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62898).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6141:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62898).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},99670:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62898).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},50774:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62898).Z)("Key",[["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["path",{d:"m15.5 7.5 3 3L22 7l-3-3",key:"1rn1fs"}]])},5589:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62898).Z)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},96142:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62898).Z)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},64280:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62898).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},29409:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62898).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},49036:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62898).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},52431:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62898).Z)("UserCheck",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]])},82104:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62898).Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},27538:function(e,t,s){Promise.resolve().then(s.bind(s,42239))},42239:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return Z}});var r=s(57437),n=s(2265),i=s(27815),a=s(31478),c=s(85754),l=s(86443),d=s(49842),o=s(40110),u=s(19160),m=s(52431),x=s(82104),f=s(50774),h=s(99670),p=s(72894),v=s(90998),y=s(5589),j=s(49036),g=s(29409),b=s(64280),N=s(96142),w=s(6141),k=s(13008);function Z(){let[e,t]=(0,n.useState)(!0),[s,Z]=(0,n.useState)(new Date),[S]=(0,n.useState)([{id:"1",type:"failed_login",user:"<EMAIL>",description:"Multiple failed login attempts",timestamp:new Date(Date.now()-3e5),severity:"high",location:"New York, US",ip:"*************"},{id:"2",type:"login",user:"<EMAIL>",description:"Successful admin login",timestamp:new Date(Date.now()-9e5),severity:"low",location:"London, UK",ip:"********"},{id:"3",type:"permission_change",user:"<EMAIL>",description:"User permissions modified",timestamp:new Date(Date.now()-18e5),severity:"medium",location:"London, UK",ip:"********"},{id:"4",type:"suspicious_activity",user:"<EMAIL>",description:"Unusual data access pattern detected",timestamp:new Date(Date.now()-27e5),severity:"critical",location:"Unknown",ip:"***********"}]),[C,R]=(0,n.useState)([{id:"1",name:"Two-Factor Authentication",description:"Require 2FA for all admin accounts",enabled:!0,category:"authentication"},{id:"2",name:"Session Timeout",description:"Automatically log out inactive users",enabled:!0,category:"authentication"},{id:"3",name:"IP Whitelist",description:"Restrict access to approved IP addresses",enabled:!1,category:"access_control"},{id:"4",name:"Audit Logging",description:"Log all administrative actions",enabled:!0,category:"monitoring"},{id:"5",name:"Data Encryption",description:"Encrypt sensitive data at rest",enabled:!0,category:"encryption"},{id:"6",name:"Intrusion Detection",description:"Monitor for suspicious activities",enabled:!0,category:"monitoring"}]);(0,n.useEffect)(()=>{let e=setTimeout(()=>{t(!1)},1e3);return()=>clearTimeout(e)},[]);let M=e=>{R(t=>t.map(t=>t.id===e?{...t,enabled:!t.enabled}:t))},A=e=>{switch(e){case"critical":case"high":return"destructive";case"medium":return"secondary";default:return"outline"}},T=e=>{switch(e){case"login":return(0,r.jsx)(m.Z,{className:"h-4 w-4 text-green-500"});case"failed_login":return(0,r.jsx)(x.Z,{className:"h-4 w-4 text-red-500"});case"permission_change":return(0,r.jsx)(f.Z,{className:"h-4 w-4 text-yellow-500"});case"data_access":return(0,r.jsx)(h.Z,{className:"h-4 w-4 text-blue-500"});case"suspicious_activity":return(0,r.jsx)(p.Z,{className:"h-4 w-4 text-red-500"});default:return(0,r.jsx)(v.Z,{className:"h-4 w-4 text-gray-500"})}},z=e=>{switch(e){case"authentication":return(0,r.jsx)(m.Z,{className:"h-4 w-4"});case"access_control":return(0,r.jsx)(y.Z,{className:"h-4 w-4"});case"monitoring":return(0,r.jsx)(h.Z,{className:"h-4 w-4"});case"encryption":return(0,r.jsx)(j.Z,{className:"h-4 w-4"});default:return(0,r.jsx)(g.Z,{className:"h-4 w-4"})}},D={totalEvents:S.length,criticalEvents:S.filter(e=>"critical"===e.severity).length,activeThreats:S.filter(e=>"critical"===e.severity||"high"===e.severity).length,securityScore:85};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Security Center"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Monitor security events and manage security settings"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)(a.C,{variant:"outline",className:"text-xs",children:["Last updated: ",s.toLocaleTimeString()]}),(0,r.jsxs)(c.z,{onClick:()=>{t(!0),Z(new Date),setTimeout(()=>t(!1),1e3)},disabled:e,size:"sm",children:[(0,r.jsx)(b.Z,{className:"h-4 w-4 mr-2 ".concat(e?"animate-spin":"")}),"Refresh"]})]})]}),(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(i.ll,{className:"text-sm font-medium",children:"Security Score"}),(0,r.jsx)(j.Z,{className:"h-4 w-4 text-green-500"})]}),(0,r.jsxs)(i.aY,{children:[(0,r.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:[D.securityScore,"%"]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Overall security rating"})]})]}),(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(i.ll,{className:"text-sm font-medium",children:"Active Threats"}),(0,r.jsx)(p.Z,{className:"h-4 w-4 text-red-500"})]}),(0,r.jsxs)(i.aY,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-red-600",children:D.activeThreats}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Requiring attention"})]})]}),(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(i.ll,{className:"text-sm font-medium",children:"Security Events"}),(0,r.jsx)(v.Z,{className:"h-4 w-4 text-blue-500"})]}),(0,r.jsxs)(i.aY,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:D.totalEvents}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Last 24 hours"})]})]}),(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(i.ll,{className:"text-sm font-medium",children:"Critical Events"}),(0,r.jsx)(x.Z,{className:"h-4 w-4 text-red-500"})]}),(0,r.jsxs)(i.aY,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-red-600",children:D.criticalEvents}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Immediate action needed"})]})]})]}),(0,r.jsxs)(o.mQ,{defaultValue:"events",className:"space-y-4",children:[(0,r.jsxs)(o.dr,{children:[(0,r.jsx)(o.SP,{value:"events",children:"Security Events"}),(0,r.jsx)(o.SP,{value:"settings",children:"Security Settings"}),(0,r.jsx)(o.SP,{value:"policies",children:"Access Policies"}),(0,r.jsx)(o.SP,{value:"monitoring",children:"Monitoring"})]}),(0,r.jsx)(o.nU,{value:"events",className:"space-y-4",children:(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{children:[(0,r.jsx)(i.ll,{children:"Recent Security Events"}),(0,r.jsx)(i.SZ,{children:"Latest security events and activities across the system"})]}),(0,r.jsx)(i.aY,{children:(0,r.jsxs)(u.iA,{children:[(0,r.jsx)(u.xD,{children:(0,r.jsxs)(u.SC,{children:[(0,r.jsx)(u.ss,{children:"Event"}),(0,r.jsx)(u.ss,{children:"User"}),(0,r.jsx)(u.ss,{children:"Location"}),(0,r.jsx)(u.ss,{children:"Time"}),(0,r.jsx)(u.ss,{children:"Severity"})]})}),(0,r.jsx)(u.RM,{children:S.map(e=>(0,r.jsxs)(u.SC,{children:[(0,r.jsx)(u.pj,{children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[T(e.type),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium",children:e.description}),(0,r.jsxs)("div",{className:"text-xs text-muted-foreground",children:["IP: ",e.ip]})]})]})}),(0,r.jsx)(u.pj,{className:"font-medium",children:e.user}),(0,r.jsx)(u.pj,{children:(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(N.Z,{className:"h-3 w-3"}),(0,r.jsx)("span",{className:"text-sm",children:e.location})]})}),(0,r.jsx)(u.pj,{children:(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(w.Z,{className:"h-3 w-3"}),(0,r.jsx)("span",{className:"text-sm",children:e.timestamp.toLocaleTimeString()})]})}),(0,r.jsx)(u.pj,{children:(0,r.jsx)(a.C,{variant:A(e.severity),className:"capitalize",children:e.severity})})]},e.id))})]})})]})}),(0,r.jsx)(o.nU,{value:"settings",className:"space-y-4",children:(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{children:[(0,r.jsx)(i.ll,{children:"Security Configuration"}),(0,r.jsx)(i.SZ,{children:"Manage security settings and policies"})]}),(0,r.jsx)(i.aY,{children:(0,r.jsx)("div",{className:"space-y-6",children:["authentication","access_control","monitoring","encryption"].map(e=>(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"text-lg font-medium mb-3 capitalize flex items-center space-x-2",children:[z(e),(0,r.jsx)("span",{children:e.replace("_"," ")})]}),(0,r.jsx)("div",{className:"space-y-3",children:C.filter(t=>t.category===e).map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)(d._,{htmlFor:e.id,className:"text-sm font-medium",children:e.name}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:e.description})]}),(0,r.jsx)(l.r,{id:e.id,checked:e.enabled,onCheckedChange:()=>M(e.id)})]},e.id))})]},e))})})]})}),(0,r.jsx)(o.nU,{value:"policies",className:"space-y-4",children:(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{children:[(0,r.jsx)(i.ll,{children:"Access Control Policies"}),(0,r.jsx)(i.SZ,{children:"Manage user access policies and permissions"})]}),(0,r.jsx)(i.aY,{children:(0,r.jsx)("div",{className:"space-y-4",children:(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-sm font-medium",children:"Password Policy"}),(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:"Minimum 8 characters, uppercase, lowercase, numbers, and symbols required"}),(0,r.jsx)(a.C,{variant:"outline",className:"text-xs",children:"Active"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-sm font-medium",children:"Session Management"}),(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:"30-minute timeout for inactive sessions"}),(0,r.jsx)(a.C,{variant:"outline",className:"text-xs",children:"Active"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-sm font-medium",children:"Role-Based Access"}),(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:"Permissions based on user roles and responsibilities"}),(0,r.jsx)(a.C,{variant:"outline",className:"text-xs",children:"Active"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-sm font-medium",children:"API Rate Limiting"}),(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:"1000 requests per hour per user"}),(0,r.jsx)(a.C,{variant:"outline",className:"text-xs",children:"Active"})]})]})})})]})}),(0,r.jsx)(o.nU,{value:"monitoring",className:"space-y-4",children:(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{children:[(0,r.jsx)(i.ll,{children:"Security Monitoring"}),(0,r.jsx)(i.SZ,{children:"Real-time security monitoring and threat detection"})]}),(0,r.jsx)(i.aY,{children:(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-3",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-sm font-medium",children:"Intrusion Detection"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(k.Z,{className:"h-4 w-4 text-green-500"}),(0,r.jsx)("span",{className:"text-sm",children:"Active"})]}),(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:"Monitoring for suspicious activities"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-sm font-medium",children:"Vulnerability Scanning"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(k.Z,{className:"h-4 w-4 text-green-500"}),(0,r.jsx)("span",{className:"text-sm",children:"Active"})]}),(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:"Daily automated security scans"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-sm font-medium",children:"Threat Intelligence"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(k.Z,{className:"h-4 w-4 text-green-500"}),(0,r.jsx)("span",{className:"text-sm",children:"Active"})]}),(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:"Real-time threat feed integration"})]})]})})]})})]})]})}},31478:function(e,t,s){"use strict";s.d(t,{C:function(){return c}});var r=s(57437);s(2265);var n=s(96061),i=s(1657);let a=(0,n.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600",info:"border-transparent bg-blue-500 text-white hover:bg-blue-600"}},defaultVariants:{variant:"default"}});function c(e){let{className:t,variant:s,...n}=e;return(0,r.jsx)("div",{className:(0,i.cn)(a({variant:s}),t),...n})}},85754:function(e,t,s){"use strict";s.d(t,{z:function(){return d}});var r=s(57437),n=s(2265),i=s(67256),a=s(96061),c=s(1657);let l=(0,a.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=n.forwardRef((e,t)=>{let{className:s,variant:n,size:a,asChild:d=!1,...o}=e,u=d?i.g7:"button";return(0,r.jsx)(u,{className:(0,c.cn)(l({variant:n,size:a,className:s})),ref:t,...o})});d.displayName="Button"},27815:function(e,t,s){"use strict";s.d(t,{Ol:function(){return c},SZ:function(){return d},Zb:function(){return a},aY:function(){return o},eW:function(){return u},ll:function(){return l}});var r=s(57437),n=s(2265),i=s(1657);let a=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...n})});a.displayName="Card";let c=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",s),...n})});c.displayName="CardHeader";let l=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)("h3",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",s),...n})});l.displayName="CardTitle";let d=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",s),...n})});d.displayName="CardDescription";let o=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",s),...n})});o.displayName="CardContent";let u=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",s),...n})});u.displayName="CardFooter"},49842:function(e,t,s){"use strict";s.d(t,{_:function(){return d}});var r=s(57437),n=s(2265),i=s(36743),a=s(96061),c=s(1657);let l=(0,a.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)(i.f,{ref:t,className:(0,c.cn)(l(),s),...n})});d.displayName=i.f.displayName},86443:function(e,t,s){"use strict";s.d(t,{r:function(){return c}});var r=s(57437),n=s(2265),i=s(92376),a=s(1657);let c=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)(i.fC,{className:(0,a.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",s),...n,ref:t,children:(0,r.jsx)(i.bU,{className:(0,a.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});c.displayName=i.fC.displayName},19160:function(e,t,s){"use strict";s.d(t,{RM:function(){return l},SC:function(){return d},iA:function(){return a},pj:function(){return u},ss:function(){return o},xD:function(){return c}});var r=s(57437),n=s(2265),i=s(1657);let a=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)("div",{className:"relative w-full overflow-auto",children:(0,r.jsx)("table",{ref:t,className:(0,i.cn)("w-full caption-bottom text-sm",s),...n})})});a.displayName="Table";let c=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)("thead",{ref:t,className:(0,i.cn)("[&_tr]:border-b",s),...n})});c.displayName="TableHeader";let l=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)("tbody",{ref:t,className:(0,i.cn)("[&_tr:last-child]:border-0",s),...n})});l.displayName="TableBody",n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)("tfoot",{ref:t,className:(0,i.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",s),...n})}).displayName="TableFooter";let d=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)("tr",{ref:t,className:(0,i.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",s),...n})});d.displayName="TableRow";let o=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)("th",{ref:t,className:(0,i.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",s),...n})});o.displayName="TableHead";let u=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)("td",{ref:t,className:(0,i.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",s),...n})});u.displayName="TableCell",n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)("caption",{ref:t,className:(0,i.cn)("mt-4 text-sm text-muted-foreground",s),...n})}).displayName="TableCaption"},40110:function(e,t,s){"use strict";s.d(t,{SP:function(){return d},dr:function(){return l},mQ:function(){return c},nU:function(){return o}});var r=s(57437),n=s(2265),i=s(34522),a=s(1657);let c=i.fC,l=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)(i.aV,{ref:t,className:(0,a.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",s),...n})});l.displayName=i.aV.displayName;let d=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)(i.xz,{ref:t,className:(0,a.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",s),...n})});d.displayName=i.xz.displayName;let o=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)(i.VY,{ref:t,className:(0,a.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",s),...n})});o.displayName=i.VY.displayName},1657:function(e,t,s){"use strict";s.d(t,{cn:function(){return i}});var r=s(57042),n=s(74769);function i(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,n.m6)((0,r.W)(t))}},36743:function(e,t,s){"use strict";s.d(t,{f:function(){return c}});var r=s(2265),n=s(9381),i=s(57437),a=r.forwardRef((e,t)=>(0,i.jsx)(n.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));a.displayName="Label";var c=a},92376:function(e,t,s){"use strict";s.d(t,{bU:function(){return w},fC:function(){return N}});var r=s(2265),n=s(85744),i=s(42210),a=s(56989),c=s(73763),l=s(85184),d=s(94977),o=s(9381),u=s(57437),m="Switch",[x,f]=(0,a.b)(m),[h,p]=x(m),v=r.forwardRef((e,t)=>{let{__scopeSwitch:s,name:a,checked:l,defaultChecked:d,required:x,disabled:f,value:p="on",onCheckedChange:v,form:y,...j}=e,[N,w]=r.useState(null),k=(0,i.e)(t,e=>w(e)),Z=r.useRef(!1),S=!N||y||!!N.closest("form"),[C,R]=(0,c.T)({prop:l,defaultProp:d??!1,onChange:v,caller:m});return(0,u.jsxs)(h,{scope:s,checked:C,disabled:f,children:[(0,u.jsx)(o.WV.button,{type:"button",role:"switch","aria-checked":C,"aria-required":x,"data-state":b(C),"data-disabled":f?"":void 0,disabled:f,value:p,...j,ref:k,onClick:(0,n.M)(e.onClick,e=>{R(e=>!e),S&&(Z.current=e.isPropagationStopped(),Z.current||e.stopPropagation())})}),S&&(0,u.jsx)(g,{control:N,bubbles:!Z.current,name:a,value:p,checked:C,required:x,disabled:f,form:y,style:{transform:"translateX(-100%)"}})]})});v.displayName=m;var y="SwitchThumb",j=r.forwardRef((e,t)=>{let{__scopeSwitch:s,...r}=e,n=p(y,s);return(0,u.jsx)(o.WV.span,{"data-state":b(n.checked),"data-disabled":n.disabled?"":void 0,...r,ref:t})});j.displayName=y;var g=r.forwardRef(({__scopeSwitch:e,control:t,checked:s,bubbles:n=!0,...a},c)=>{let o=r.useRef(null),m=(0,i.e)(o,c),x=(0,l.D)(s),f=(0,d.t)(t);return r.useEffect(()=>{let e=o.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(x!==s&&t){let r=new Event("click",{bubbles:n});t.call(e,s),e.dispatchEvent(r)}},[x,s,n]),(0,u.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:s,...a,tabIndex:-1,ref:m,style:{...a.style,...f,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function b(e){return e?"checked":"unchecked"}g.displayName="SwitchBubbleInput";var N=v,w=j},85184:function(e,t,s){"use strict";s.d(t,{D:function(){return n}});var r=s(2265);function n(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},94977:function(e,t,s){"use strict";s.d(t,{t:function(){return i}});var r=s(2265),n=s(51030);function i(e){let[t,s]=r.useState(void 0);return(0,n.b)(()=>{if(e){s({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,n;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,n=t.blockSize}else r=e.offsetWidth,n=e.offsetHeight;s({width:r,height:n})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}s(void 0)},[e]),t}}},function(e){e.O(0,[6723,9502,4522,2971,4938,1744],function(){return e(e.s=27538)}),_N_E=e.O()}]);