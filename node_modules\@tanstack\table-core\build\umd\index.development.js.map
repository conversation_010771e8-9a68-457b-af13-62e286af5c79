{"version": 3, "file": "index.development.js", "sources": ["../../src/columnHelper.ts", "../../src/utils.ts", "../../src/core/cell.ts", "../../src/core/column.ts", "../../src/core/headers.ts", "../../src/core/row.ts", "../../src/features/ColumnFaceting.ts", "../../src/filterFns.ts", "../../src/features/ColumnFiltering.ts", "../../src/aggregationFns.ts", "../../src/features/ColumnGrouping.ts", "../../src/features/ColumnOrdering.ts", "../../src/features/ColumnPinning.ts", "../../src/utils/document.ts", "../../src/features/ColumnSizing.ts", "../../src/features/ColumnVisibility.ts", "../../src/features/GlobalFaceting.ts", "../../src/features/GlobalFiltering.ts", "../../src/features/RowExpanding.ts", "../../src/features/RowPagination.ts", "../../src/features/RowPinning.ts", "../../src/features/RowSelection.ts", "../../src/sortingFns.ts", "../../src/features/RowSorting.ts", "../../src/core/table.ts", "../../src/utils/getCoreRowModel.ts", "../../src/utils/getExpandedRowModel.ts", "../../src/utils/getFacetedMinMaxValues.ts", "../../src/utils/filterRowsUtils.ts", "../../src/utils/getFacetedRowModel.ts", "../../src/utils/getFacetedUniqueValues.ts", "../../src/utils/getFilteredRowModel.ts", "../../src/utils/getGroupedRowModel.ts", "../../src/utils/getPaginationRowModel.ts", "../../src/utils/getSortedRowModel.ts"], "sourcesContent": ["import {\n  AccessorFn,\n  AccessorFnColumnDef,\n  AccessorKeyColumnDef,\n  DisplayColumnDef,\n  GroupColumnDef,\n  IdentifiedColumnDef,\n  RowData,\n} from './types'\nimport { DeepKeys, DeepValue } from './utils'\n\n// type Person = {\n//   firstName: string\n//   lastName: string\n//   age: number\n//   visits: number\n//   status: string\n//   progress: number\n//   createdAt: Date\n//   nested: {\n//     foo: [\n//       {\n//         bar: 'bar'\n//       }\n//     ]\n//     bar: { subBar: boolean }[]\n//     baz: {\n//       foo: 'foo'\n//       bar: {\n//         baz: 'baz'\n//       }\n//     }\n//   }\n// }\n\n// const test: DeepKeys<Person> = 'nested.foo.0.bar'\n// const test2: DeepKeys<Person> = 'nested.bar'\n\n// const helper = createColumnHelper<Person>()\n\n// helper.accessor('nested.foo', {\n//   cell: info => info.getValue(),\n// })\n\n// helper.accessor('nested.foo.0.bar', {\n//   cell: info => info.getValue(),\n// })\n\n// helper.accessor('nested.bar', {\n//   cell: info => info.getValue(),\n// })\n\nexport type ColumnHelper<TData extends RowData> = {\n  accessor: <\n    TAccessor extends AccessorFn<TData> | DeepKeys<TData>,\n    TValue extends TAccessor extends AccessorFn<TData, infer TReturn>\n      ? TReturn\n      : TAccessor extends DeepKeys<TData>\n        ? DeepValue<TData, TAccessor>\n        : never,\n  >(\n    accessor: TAccessor,\n    column: TAccessor extends AccessorFn<TData>\n      ? DisplayColumnDef<TData, TValue>\n      : IdentifiedColumnDef<TData, TValue>\n  ) => TAccessor extends AccessorFn<TData>\n    ? AccessorFnColumnDef<TData, TValue>\n    : AccessorKeyColumnDef<TData, TValue>\n  display: (column: DisplayColumnDef<TData>) => DisplayColumnDef<TData, unknown>\n  group: (column: GroupColumnDef<TData>) => GroupColumnDef<TData, unknown>\n}\n\nexport function createColumnHelper<\n  TData extends RowData,\n>(): ColumnHelper<TData> {\n  return {\n    accessor: (accessor, column) => {\n      return typeof accessor === 'function'\n        ? ({\n            ...column,\n            accessorFn: accessor,\n          } as any)\n        : {\n            ...column,\n            accessorKey: accessor,\n          }\n    },\n    display: column => column,\n    group: column => column,\n  }\n}\n", "import { TableOptionsResolved, TableState, Updater } from './types'\n\nexport type PartialKeys<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>\nexport type RequiredKeys<T, K extends keyof T> = Omit<T, K> &\n  Required<Pick<T, K>>\nexport type Overwrite<T, U extends { [TKey in keyof T]?: any }> = Omit<\n  T,\n  keyof U\n> &\n  U\n\nexport type UnionToIntersection<T> = (\n  T extends any ? (x: T) => any : never\n) extends (x: infer R) => any\n  ? R\n  : never\n\nexport type IsAny<T, Y, N> = 1 extends 0 & T ? Y : N\nexport type IsKnown<T, Y, N> = unknown extends T ? N : Y\n\ntype ComputeRange<\n  N extends number,\n  Result extends Array<unknown> = [],\n> = Result['length'] extends N\n  ? Result\n  : ComputeRange<N, [...Result, Result['length']]>\ntype Index40 = ComputeRange<40>[number]\n\n// Is this type a tuple?\ntype IsTuple<T> = T extends readonly any[] & { length: infer Length }\n  ? Length extends Index40\n    ? T\n    : never\n  : never\n\n// If this type is a tuple, what indices are allowed?\ntype AllowedIndexes<\n  Tuple extends ReadonlyArray<any>,\n  Keys extends number = never,\n> = Tuple extends readonly []\n  ? Keys\n  : Tuple extends readonly [infer _, ...infer Tail]\n    ? AllowedIndexes<Tail, Keys | Tail['length']>\n    : Keys\n\nexport type DeepKeys<T, TDepth extends any[] = []> = TDepth['length'] extends 5\n  ? never\n  : unknown extends T\n    ? string\n    : T extends readonly any[] & IsTuple<T>\n      ? AllowedIndexes<T> | DeepKeysPrefix<T, AllowedIndexes<T>, TDepth>\n      : T extends any[]\n        ? DeepKeys<T[number], [...TDepth, any]>\n        : T extends Date\n          ? never\n          : T extends object\n            ? (keyof T & string) | DeepKeysPrefix<T, keyof T, TDepth>\n            : never\n\ntype DeepKeysPrefix<\n  T,\n  TPrefix,\n  TDepth extends any[],\n> = TPrefix extends keyof T & (number | string)\n  ? `${TPrefix}.${DeepKeys<T[TPrefix], [...TDepth, any]> & string}`\n  : never\n\nexport type DeepValue<T, TProp> =\n  T extends Record<string | number, any>\n    ? TProp extends `${infer TBranch}.${infer TDeepProp}`\n      ? DeepValue<T[TBranch], TDeepProp>\n      : T[TProp & string]\n    : never\n\nexport type NoInfer<T> = [T][T extends any ? 0 : never]\n\nexport type Getter<TValue> = <TTValue = TValue>() => NoInfer<TTValue>\n\n///\n\nexport function functionalUpdate<T>(updater: Updater<T>, input: T): T {\n  return typeof updater === 'function'\n    ? (updater as (input: T) => T)(input)\n    : updater\n}\n\nexport function noop() {\n  //\n}\n\nexport function makeStateUpdater<K extends keyof TableState>(\n  key: K,\n  instance: unknown\n) {\n  return (updater: Updater<TableState[K]>) => {\n    ;(instance as any).setState(<TTableState>(old: TTableState) => {\n      return {\n        ...old,\n        [key]: functionalUpdate(updater, (old as any)[key]),\n      }\n    })\n  }\n}\n\ntype AnyFunction = (...args: any) => any\n\nexport function isFunction<T extends AnyFunction>(d: any): d is T {\n  return d instanceof Function\n}\n\nexport function isNumberArray(d: any): d is number[] {\n  return Array.isArray(d) && d.every(val => typeof val === 'number')\n}\n\nexport function flattenBy<TNode>(\n  arr: TNode[],\n  getChildren: (item: TNode) => TNode[]\n) {\n  const flat: TNode[] = []\n\n  const recurse = (subArr: TNode[]) => {\n    subArr.forEach(item => {\n      flat.push(item)\n      const children = getChildren(item)\n      if (children?.length) {\n        recurse(children)\n      }\n    })\n  }\n\n  recurse(arr)\n\n  return flat\n}\n\nexport function memo<TDeps extends readonly any[], TDepArgs, TResult>(\n  getDeps: (depArgs?: TDepArgs) => [...TDeps],\n  fn: (...args: NoInfer<[...TDeps]>) => TResult,\n  opts: {\n    key: any\n    debug?: () => any\n    onChange?: (result: TResult) => void\n  }\n): (depArgs?: TDepArgs) => TResult {\n  let deps: any[] = []\n  let result: TResult | undefined\n\n  return depArgs => {\n    let depTime: number\n    if (opts.key && opts.debug) depTime = Date.now()\n\n    const newDeps = getDeps(depArgs)\n\n    const depsChanged =\n      newDeps.length !== deps.length ||\n      newDeps.some((dep: any, index: number) => deps[index] !== dep)\n\n    if (!depsChanged) {\n      return result!\n    }\n\n    deps = newDeps\n\n    let resultTime: number\n    if (opts.key && opts.debug) resultTime = Date.now()\n\n    result = fn(...newDeps)\n    opts?.onChange?.(result)\n\n    if (opts.key && opts.debug) {\n      if (opts?.debug()) {\n        const depEndTime = Math.round((Date.now() - depTime!) * 100) / 100\n        const resultEndTime = Math.round((Date.now() - resultTime!) * 100) / 100\n        const resultFpsPercentage = resultEndTime / 16\n\n        const pad = (str: number | string, num: number) => {\n          str = String(str)\n          while (str.length < num) {\n            str = ' ' + str\n          }\n          return str\n        }\n\n        console.info(\n          `%c⏱ ${pad(resultEndTime, 5)} /${pad(depEndTime, 5)} ms`,\n          `\n            font-size: .6rem;\n            font-weight: bold;\n            color: hsl(${Math.max(\n              0,\n              Math.min(120 - 120 * resultFpsPercentage, 120)\n            )}deg 100% 31%);`,\n          opts?.key\n        )\n      }\n    }\n\n    return result!\n  }\n}\n\nexport function getMemoOptions(\n  tableOptions: Partial<TableOptionsResolved<any>>,\n  debugLevel:\n    | 'debugAll'\n    | 'debugCells'\n    | 'debugTable'\n    | 'debugColumns'\n    | 'debugRows'\n    | 'debugHeaders',\n  key: string,\n  onChange?: (result: any) => void\n) {\n  return {\n    debug: () => tableOptions?.debugAll ?? tableOptions[debugLevel],\n    key: process.env.NODE_ENV === 'development' && key,\n    onChange,\n  }\n}\n", "import { RowData, Cell, Column, Row, Table } from '../types'\nimport { Getter, getMemoOptions, memo } from '../utils'\n\nexport interface CellContext<TData extends RowData, TValue> {\n  cell: Cell<TData, TValue>\n  column: Column<TData, TValue>\n  getValue: Getter<TValue>\n  renderValue: Getter<TValue | null>\n  row: Row<TData>\n  table: Table<TData>\n}\n\nexport interface CoreCell<TData extends RowData, TValue> {\n  /**\n   * The associated Column object for the cell.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/cell#column)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/cells)\n   */\n  column: Column<TData, TValue>\n  /**\n   * Returns the rendering context (or props) for cell-based components like cells and aggregated cells. Use these props with your framework's `flexRender` utility to render these using the template of your choice:\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/cell#getcontext)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/cells)\n   */\n  getContext: () => CellContext<TData, TValue>\n  /**\n   * Returns the value for the cell, accessed via the associated column's accessor key or accessor function.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/cell#getvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/cells)\n   */\n  getValue: CellContext<TData, TValue>['getValue']\n  /**\n   * The unique ID for the cell across the entire table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/cell#id)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/cells)\n   */\n  id: string\n  /**\n   * Renders the value for a cell the same as `getValue`, but will return the `renderFallbackValue` if no value is found.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/cell#rendervalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/cells)\n   */\n  renderValue: CellContext<TData, TValue>['renderValue']\n  /**\n   * The associated Row object for the cell.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/cell#row)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/cells)\n   */\n  row: Row<TData>\n}\n\nexport function createCell<TData extends RowData, TValue>(\n  table: Table<TData>,\n  row: Row<TData>,\n  column: Column<TData, TValue>,\n  columnId: string\n): Cell<TData, TValue> {\n  const getRenderValue = () =>\n    cell.getValue() ?? table.options.renderFallbackValue\n\n  const cell: CoreCell<TData, TValue> = {\n    id: `${row.id}_${column.id}`,\n    row,\n    column,\n    getValue: () => row.getValue(columnId),\n    renderValue: getRenderValue,\n    getContext: memo(\n      () => [table, column, row, cell],\n      (table, column, row, cell) => ({\n        table,\n        column,\n        row,\n        cell: cell as Cell<TData, TValue>,\n        getValue: cell.getValue,\n        renderValue: cell.renderValue,\n      }),\n      getMemoOptions(table.options, 'debugCells', 'cell.getContext')\n    ),\n  }\n\n  table._features.forEach(feature => {\n    feature.createCell?.(\n      cell as Cell<TData, TValue>,\n      column,\n      row as Row<TData>,\n      table\n    )\n  }, {})\n\n  return cell as Cell<TData, TValue>\n}\n", "import {\n  Column,\n  Table,\n  AccessorFn,\n  ColumnDef,\n  RowData,\n  ColumnDefResolved,\n} from '../types'\nimport { getMemoOptions, memo } from '../utils'\n\nexport interface CoreColumn<TData extends RowData, TValue> {\n  /**\n   * The resolved accessor function to use when extracting the value for the column from each row. Will only be defined if the column def has a valid accessor key or function defined.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#accessorfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  accessorFn?: AccessorFn<TData, TValue>\n  /**\n   * The original column def used to create the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#columndef)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  columnDef: ColumnDef<TData, TValue>\n  /**\n   * The child column (if the column is a group column). Will be an empty array if the column is not a group column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#columns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  columns: Column<TData, TValue>[]\n  /**\n   * The depth of the column (if grouped) relative to the root column def array.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#depth)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  depth: number\n  /**\n   * Returns the flattened array of this column and all child/grand-child columns for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#getflatcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  getFlatColumns: () => Column<TData, TValue>[]\n  /**\n   * Returns an array of all leaf-node columns for this column. If a column has no children, it is considered the only leaf-node column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#getleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  getLeafColumns: () => Column<TData, TValue>[]\n  /**\n   * The resolved unique identifier for the column resolved in this priority:\n      - A manual `id` property from the column def\n      - The accessor key from the column def\n      - The header string from the column def\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#id)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  id: string\n  /**\n   * The parent column for this column. Will be undefined if this is a root column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#parent)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  parent?: Column<TData, TValue>\n}\n\nexport function createColumn<TData extends RowData, TValue>(\n  table: Table<TData>,\n  columnDef: ColumnDef<TData, TValue>,\n  depth: number,\n  parent?: Column<TData, TValue>\n): Column<TData, TValue> {\n  const defaultColumn = table._getDefaultColumnDef()\n\n  const resolvedColumnDef = {\n    ...defaultColumn,\n    ...columnDef,\n  } as ColumnDefResolved<TData>\n\n  const accessorKey = resolvedColumnDef.accessorKey\n\n  let id =\n    resolvedColumnDef.id ??\n    (accessorKey\n      ? typeof String.prototype.replaceAll === 'function'\n        ? accessorKey.replaceAll('.', '_')\n        : accessorKey.replace(/\\./g, '_')\n      : undefined) ??\n    (typeof resolvedColumnDef.header === 'string'\n      ? resolvedColumnDef.header\n      : undefined)\n\n  let accessorFn: AccessorFn<TData> | undefined\n\n  if (resolvedColumnDef.accessorFn) {\n    accessorFn = resolvedColumnDef.accessorFn\n  } else if (accessorKey) {\n    // Support deep accessor keys\n    if (accessorKey.includes('.')) {\n      accessorFn = (originalRow: TData) => {\n        let result = originalRow as Record<string, any>\n\n        for (const key of accessorKey.split('.')) {\n          result = result?.[key]\n          if (process.env.NODE_ENV !== 'production' && result === undefined) {\n            console.warn(\n              `\"${key}\" in deeply nested key \"${accessorKey}\" returned undefined.`\n            )\n          }\n        }\n\n        return result\n      }\n    } else {\n      accessorFn = (originalRow: TData) =>\n        (originalRow as any)[resolvedColumnDef.accessorKey]\n    }\n  }\n\n  if (!id) {\n    if (process.env.NODE_ENV !== 'production') {\n      throw new Error(\n        resolvedColumnDef.accessorFn\n          ? `Columns require an id when using an accessorFn`\n          : `Columns require an id when using a non-string header`\n      )\n    }\n    throw new Error()\n  }\n\n  let column: CoreColumn<TData, any> = {\n    id: `${String(id)}`,\n    accessorFn,\n    parent: parent as any,\n    depth,\n    columnDef: resolvedColumnDef as ColumnDef<TData, any>,\n    columns: [],\n    getFlatColumns: memo(\n      () => [true],\n      () => {\n        return [\n          column as Column<TData, TValue>,\n          ...column.columns?.flatMap(d => d.getFlatColumns()),\n        ]\n      },\n      getMemoOptions(table.options, 'debugColumns', 'column.getFlatColumns')\n    ),\n    getLeafColumns: memo(\n      () => [table._getOrderColumnsFn()],\n      orderColumns => {\n        if (column.columns?.length) {\n          let leafColumns = column.columns.flatMap(column =>\n            column.getLeafColumns()\n          )\n\n          return orderColumns(leafColumns)\n        }\n\n        return [column as Column<TData, TValue>]\n      },\n      getMemoOptions(table.options, 'debugColumns', 'column.getLeafColumns')\n    ),\n  }\n\n  for (const feature of table._features) {\n    feature.createColumn?.(column as Column<TData, TValue>, table)\n  }\n\n  // Yes, we have to convert table to unknown, because we know more than the compiler here.\n  return column as Column<TData, TValue>\n}\n", "import {\n  <PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n  <PERSON><PERSON>,\n  HeaderGroup,\n  Table,\n  TableFeature,\n} from '../types'\nimport { getMemoOptions, memo } from '../utils'\n\nconst debug = 'debugHeaders'\n\nexport interface CoreHeaderGroup<TData extends RowData> {\n  depth: number\n  headers: Header<TData, unknown>[]\n  id: string\n}\n\nexport interface HeaderContext<TData, TValue> {\n  /**\n   * An instance of a column.\n   */\n  column: Column<TData, TValue>\n  /**\n   * An instance of a header.\n   */\n  header: Header<TData, TValue>\n  /**\n   * The table instance.\n   */\n  table: Table<TData>\n}\n\nexport interface CoreHeader<TData extends RowData, TValue> {\n  /**\n   * The col-span for the header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#colspan)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  colSpan: number\n  /**\n   * The header's associated column object.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#column)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  column: Column<TData, TValue>\n  /**\n   * The depth of the header, zero-indexed based.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#depth)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  depth: number\n  /**\n   * Returns the rendering context (or props) for column-based components like headers, footers and filters.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#getcontext)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getContext: () => HeaderContext<TData, TValue>\n  /**\n   * Returns the leaf headers hierarchically nested under this header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#getleafheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeafHeaders: () => Header<TData, unknown>[]\n  /**\n   * The header's associated header group object.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#headergroup)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  headerGroup: HeaderGroup<TData>\n  /**\n   * The unique identifier for the header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#id)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  id: string\n  /**\n   * The index for the header within the header group.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#index)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  index: number\n  /**\n   * A boolean denoting if the header is a placeholder header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#isplaceholder)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  isPlaceholder: boolean\n  /**\n   * If the header is a placeholder header, this will be a unique header ID that does not conflict with any other headers across the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#placeholderid)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  placeholderId?: string\n  /**\n   * The row-span for the header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#rowspan)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  rowSpan: number\n  /**\n   * The header's hierarchical sub/child headers. Will be empty if the header's associated column is a leaf-column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#subheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  subHeaders: Header<TData, TValue>[]\n}\n\nexport interface HeadersInstance<TData extends RowData> {\n  /**\n   * Returns all header groups for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getheadergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getHeaderGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the header groups for the left pinned columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getleftheadergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeftHeaderGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the header groups for columns that are not pinned.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getcenterheadergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getCenterHeaderGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the header groups for the right pinned columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getrightheadergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getRightHeaderGroups: () => HeaderGroup<TData>[]\n\n  /**\n   * Returns the footer groups for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getfootergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getFooterGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the footer groups for the left pinned columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getleftfootergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeftFooterGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the footer groups for columns that are not pinned.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getcenterfootergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getCenterFooterGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the footer groups for the right pinned columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getrightfootergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getRightFooterGroups: () => HeaderGroup<TData>[]\n\n  /**\n   * Returns headers for all columns in the table, including parent headers.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getflatheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getFlatHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all left pinned columns in the table, including parent headers.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getleftflatheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeftFlatHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all columns that are not pinned, including parent headers.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getcenterflatheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getCenterFlatHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all right pinned columns in the table, including parent headers.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getrightflatheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getRightFlatHeaders: () => Header<TData, unknown>[]\n\n  /**\n   * Returns headers for all leaf columns in the table, (not including parent headers).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getleafheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeafHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all left pinned leaf columns in the table, (not including parent headers).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getleftleafheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeftLeafHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all columns that are not pinned, (not including parent headers).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getcenterleafheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getCenterLeafHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all right pinned leaf columns in the table, (not including parent headers).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getrightleafheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getRightLeafHeaders: () => Header<TData, unknown>[]\n}\n\n//\n\nfunction createHeader<TData extends RowData, TValue>(\n  table: Table<TData>,\n  column: Column<TData, TValue>,\n  options: {\n    id?: string\n    isPlaceholder?: boolean\n    placeholderId?: string\n    index: number\n    depth: number\n  }\n): Header<TData, TValue> {\n  const id = options.id ?? column.id\n\n  let header: CoreHeader<TData, TValue> = {\n    id,\n    column,\n    index: options.index,\n    isPlaceholder: !!options.isPlaceholder,\n    placeholderId: options.placeholderId,\n    depth: options.depth,\n    subHeaders: [],\n    colSpan: 0,\n    rowSpan: 0,\n    headerGroup: null!,\n    getLeafHeaders: (): Header<TData, unknown>[] => {\n      const leafHeaders: Header<TData, unknown>[] = []\n\n      const recurseHeader = (h: CoreHeader<TData, any>) => {\n        if (h.subHeaders && h.subHeaders.length) {\n          h.subHeaders.map(recurseHeader)\n        }\n        leafHeaders.push(h as Header<TData, unknown>)\n      }\n\n      recurseHeader(header)\n\n      return leafHeaders\n    },\n    getContext: () => ({\n      table,\n      header: header as Header<TData, TValue>,\n      column,\n    }),\n  }\n\n  table._features.forEach(feature => {\n    feature.createHeader?.(header as Header<TData, TValue>, table)\n  })\n\n  return header as Header<TData, TValue>\n}\n\nexport const Headers: TableFeature = {\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    // Header Groups\n\n    table.getHeaderGroups = memo(\n      () => [\n        table.getAllColumns(),\n        table.getVisibleLeafColumns(),\n        table.getState().columnPinning.left,\n        table.getState().columnPinning.right,\n      ],\n      (allColumns, leafColumns, left, right) => {\n        const leftColumns =\n          left\n            ?.map(columnId => leafColumns.find(d => d.id === columnId)!)\n            .filter(Boolean) ?? []\n\n        const rightColumns =\n          right\n            ?.map(columnId => leafColumns.find(d => d.id === columnId)!)\n            .filter(Boolean) ?? []\n\n        const centerColumns = leafColumns.filter(\n          column => !left?.includes(column.id) && !right?.includes(column.id)\n        )\n\n        const headerGroups = buildHeaderGroups(\n          allColumns,\n          [...leftColumns, ...centerColumns, ...rightColumns],\n          table\n        )\n\n        return headerGroups\n      },\n      getMemoOptions(table.options, debug, 'getHeaderGroups')\n    )\n\n    table.getCenterHeaderGroups = memo(\n      () => [\n        table.getAllColumns(),\n        table.getVisibleLeafColumns(),\n        table.getState().columnPinning.left,\n        table.getState().columnPinning.right,\n      ],\n      (allColumns, leafColumns, left, right) => {\n        leafColumns = leafColumns.filter(\n          column => !left?.includes(column.id) && !right?.includes(column.id)\n        )\n        return buildHeaderGroups(allColumns, leafColumns, table, 'center')\n      },\n      getMemoOptions(table.options, debug, 'getCenterHeaderGroups')\n    )\n\n    table.getLeftHeaderGroups = memo(\n      () => [\n        table.getAllColumns(),\n        table.getVisibleLeafColumns(),\n        table.getState().columnPinning.left,\n      ],\n      (allColumns, leafColumns, left) => {\n        const orderedLeafColumns =\n          left\n            ?.map(columnId => leafColumns.find(d => d.id === columnId)!)\n            .filter(Boolean) ?? []\n\n        return buildHeaderGroups(allColumns, orderedLeafColumns, table, 'left')\n      },\n      getMemoOptions(table.options, debug, 'getLeftHeaderGroups')\n    )\n\n    table.getRightHeaderGroups = memo(\n      () => [\n        table.getAllColumns(),\n        table.getVisibleLeafColumns(),\n        table.getState().columnPinning.right,\n      ],\n      (allColumns, leafColumns, right) => {\n        const orderedLeafColumns =\n          right\n            ?.map(columnId => leafColumns.find(d => d.id === columnId)!)\n            .filter(Boolean) ?? []\n\n        return buildHeaderGroups(allColumns, orderedLeafColumns, table, 'right')\n      },\n      getMemoOptions(table.options, debug, 'getRightHeaderGroups')\n    )\n\n    // Footer Groups\n\n    table.getFooterGroups = memo(\n      () => [table.getHeaderGroups()],\n      headerGroups => {\n        return [...headerGroups].reverse()\n      },\n      getMemoOptions(table.options, debug, 'getFooterGroups')\n    )\n\n    table.getLeftFooterGroups = memo(\n      () => [table.getLeftHeaderGroups()],\n      headerGroups => {\n        return [...headerGroups].reverse()\n      },\n      getMemoOptions(table.options, debug, 'getLeftFooterGroups')\n    )\n\n    table.getCenterFooterGroups = memo(\n      () => [table.getCenterHeaderGroups()],\n      headerGroups => {\n        return [...headerGroups].reverse()\n      },\n      getMemoOptions(table.options, debug, 'getCenterFooterGroups')\n    )\n\n    table.getRightFooterGroups = memo(\n      () => [table.getRightHeaderGroups()],\n      headerGroups => {\n        return [...headerGroups].reverse()\n      },\n      getMemoOptions(table.options, debug, 'getRightFooterGroups')\n    )\n\n    // Flat Headers\n\n    table.getFlatHeaders = memo(\n      () => [table.getHeaderGroups()],\n      headerGroups => {\n        return headerGroups\n          .map(headerGroup => {\n            return headerGroup.headers\n          })\n          .flat()\n      },\n      getMemoOptions(table.options, debug, 'getFlatHeaders')\n    )\n\n    table.getLeftFlatHeaders = memo(\n      () => [table.getLeftHeaderGroups()],\n      left => {\n        return left\n          .map(headerGroup => {\n            return headerGroup.headers\n          })\n          .flat()\n      },\n      getMemoOptions(table.options, debug, 'getLeftFlatHeaders')\n    )\n\n    table.getCenterFlatHeaders = memo(\n      () => [table.getCenterHeaderGroups()],\n      left => {\n        return left\n          .map(headerGroup => {\n            return headerGroup.headers\n          })\n          .flat()\n      },\n      getMemoOptions(table.options, debug, 'getCenterFlatHeaders')\n    )\n\n    table.getRightFlatHeaders = memo(\n      () => [table.getRightHeaderGroups()],\n      left => {\n        return left\n          .map(headerGroup => {\n            return headerGroup.headers\n          })\n          .flat()\n      },\n      getMemoOptions(table.options, debug, 'getRightFlatHeaders')\n    )\n\n    // Leaf Headers\n\n    table.getCenterLeafHeaders = memo(\n      () => [table.getCenterFlatHeaders()],\n      flatHeaders => {\n        return flatHeaders.filter(header => !header.subHeaders?.length)\n      },\n      getMemoOptions(table.options, debug, 'getCenterLeafHeaders')\n    )\n\n    table.getLeftLeafHeaders = memo(\n      () => [table.getLeftFlatHeaders()],\n      flatHeaders => {\n        return flatHeaders.filter(header => !header.subHeaders?.length)\n      },\n      getMemoOptions(table.options, debug, 'getLeftLeafHeaders')\n    )\n\n    table.getRightLeafHeaders = memo(\n      () => [table.getRightFlatHeaders()],\n      flatHeaders => {\n        return flatHeaders.filter(header => !header.subHeaders?.length)\n      },\n      getMemoOptions(table.options, debug, 'getRightLeafHeaders')\n    )\n\n    table.getLeafHeaders = memo(\n      () => [\n        table.getLeftHeaderGroups(),\n        table.getCenterHeaderGroups(),\n        table.getRightHeaderGroups(),\n      ],\n      (left, center, right) => {\n        return [\n          ...(left[0]?.headers ?? []),\n          ...(center[0]?.headers ?? []),\n          ...(right[0]?.headers ?? []),\n        ]\n          .map(header => {\n            return header.getLeafHeaders()\n          })\n          .flat()\n      },\n      getMemoOptions(table.options, debug, 'getLeafHeaders')\n    )\n  },\n}\n\nexport function buildHeaderGroups<TData extends RowData>(\n  allColumns: Column<TData, unknown>[],\n  columnsToGroup: Column<TData, unknown>[],\n  table: Table<TData>,\n  headerFamily?: 'center' | 'left' | 'right'\n) {\n  // Find the max depth of the columns:\n  // build the leaf column row\n  // build each buffer row going up\n  //    placeholder for non-existent level\n  //    real column for existing level\n\n  let maxDepth = 0\n\n  const findMaxDepth = (columns: Column<TData, unknown>[], depth = 1) => {\n    maxDepth = Math.max(maxDepth, depth)\n\n    columns\n      .filter(column => column.getIsVisible())\n      .forEach(column => {\n        if (column.columns?.length) {\n          findMaxDepth(column.columns, depth + 1)\n        }\n      }, 0)\n  }\n\n  findMaxDepth(allColumns)\n\n  let headerGroups: HeaderGroup<TData>[] = []\n\n  const createHeaderGroup = (\n    headersToGroup: Header<TData, unknown>[],\n    depth: number\n  ) => {\n    // The header group we are creating\n    const headerGroup: HeaderGroup<TData> = {\n      depth,\n      id: [headerFamily, `${depth}`].filter(Boolean).join('_'),\n      headers: [],\n    }\n\n    // The parent columns we're going to scan next\n    const pendingParentHeaders: Header<TData, unknown>[] = []\n\n    // Scan each column for parents\n    headersToGroup.forEach(headerToGroup => {\n      // What is the latest (last) parent column?\n\n      const latestPendingParentHeader = [...pendingParentHeaders].reverse()[0]\n\n      const isLeafHeader = headerToGroup.column.depth === headerGroup.depth\n\n      let column: Column<TData, unknown>\n      let isPlaceholder = false\n\n      if (isLeafHeader && headerToGroup.column.parent) {\n        // The parent header is new\n        column = headerToGroup.column.parent\n      } else {\n        // The parent header is repeated\n        column = headerToGroup.column\n        isPlaceholder = true\n      }\n\n      if (\n        latestPendingParentHeader &&\n        latestPendingParentHeader?.column === column\n      ) {\n        // This column is repeated. Add it as a sub header to the next batch\n        latestPendingParentHeader.subHeaders.push(headerToGroup)\n      } else {\n        // This is a new header. Let's create it\n        const header = createHeader(table, column, {\n          id: [headerFamily, depth, column.id, headerToGroup?.id]\n            .filter(Boolean)\n            .join('_'),\n          isPlaceholder,\n          placeholderId: isPlaceholder\n            ? `${pendingParentHeaders.filter(d => d.column === column).length}`\n            : undefined,\n          depth,\n          index: pendingParentHeaders.length,\n        })\n\n        // Add the headerToGroup as a subHeader of the new header\n        header.subHeaders.push(headerToGroup)\n        // Add the new header to the pendingParentHeaders to get grouped\n        // in the next batch\n        pendingParentHeaders.push(header)\n      }\n\n      headerGroup.headers.push(headerToGroup)\n      headerToGroup.headerGroup = headerGroup\n    })\n\n    headerGroups.push(headerGroup)\n\n    if (depth > 0) {\n      createHeaderGroup(pendingParentHeaders, depth - 1)\n    }\n  }\n\n  const bottomHeaders = columnsToGroup.map((column, index) =>\n    createHeader(table, column, {\n      depth: maxDepth,\n      index,\n    })\n  )\n\n  createHeaderGroup(bottomHeaders, maxDepth - 1)\n\n  headerGroups.reverse()\n\n  // headerGroups = headerGroups.filter(headerGroup => {\n  //   return !headerGroup.headers.every(header => header.isPlaceholder)\n  // })\n\n  const recurseHeadersForSpans = (\n    headers: Header<TData, unknown>[]\n  ): { colSpan: number; rowSpan: number }[] => {\n    const filteredHeaders = headers.filter(header =>\n      header.column.getIsVisible()\n    )\n\n    return filteredHeaders.map(header => {\n      let colSpan = 0\n      let rowSpan = 0\n      let childRowSpans = [0]\n\n      if (header.subHeaders && header.subHeaders.length) {\n        childRowSpans = []\n\n        recurseHeadersForSpans(header.subHeaders).forEach(\n          ({ colSpan: childColSpan, rowSpan: childRowSpan }) => {\n            colSpan += childColSpan\n            childRowSpans.push(childRowSpan)\n          }\n        )\n      } else {\n        colSpan = 1\n      }\n\n      const minChildRowSpan = Math.min(...childRowSpans)\n      rowSpan = rowSpan + minChildRowSpan\n\n      header.colSpan = colSpan\n      header.rowSpan = rowSpan\n\n      return { colSpan, rowSpan }\n    })\n  }\n\n  recurseHeadersForSpans(headerGroups[0]?.headers ?? [])\n\n  return headerGroups\n}\n", "import { RowData, Cell, Row, Table } from '../types'\nimport { flattenBy, getMemoOptions, memo } from '../utils'\nimport { createCell } from './cell'\n\nexport interface CoreRow<TData extends RowData> {\n  _getAllCellsByColumnId: () => Record<string, Cell<TData, unknown>>\n  _uniqueValuesCache: Record<string, unknown>\n  _valuesCache: Record<string, unknown>\n  /**\n   * The depth of the row (if nested or grouped) relative to the root row array.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#depth)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  depth: number\n  /**\n   * Returns all of the cells for the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getallcells)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getAllCells: () => Cell<TData, unknown>[]\n  /**\n   * Returns the leaf rows for the row, not including any parent rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getleafrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getLeafRows: () => Row<TData>[]\n  /**\n   * Returns the parent row for the row, if it exists.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getparentrow)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getParentRow: () => Row<TData> | undefined\n  /**\n   * Returns the parent rows for the row, all the way up to a root row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getparentrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getParentRows: () => Row<TData>[]\n  /**\n   * Returns a unique array of values from the row for a given columnId.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getuniquevalues)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getUniqueValues: <TValue>(columnId: string) => TValue[]\n  /**\n   * Returns the value from the row for a given columnId.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getValue: <TValue>(columnId: string) => TValue\n  /**\n   * The resolved unique identifier for the row resolved via the `options.getRowId` option. Defaults to the row's index (or relative index if it is a subRow).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#id)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  id: string\n  /**\n   * The index of the row within its parent array (or the root data array).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#index)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  index: number\n  /**\n   * The original row object provided to the table. If the row is a grouped row, the original row object will be the first original in the group.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#original)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  original: TData\n  /**\n   * An array of the original subRows as returned by the `options.getSubRows` option.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#originalsubrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  originalSubRows?: TData[]\n  /**\n   * If nested, this row's parent row id.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#parentid)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  parentId?: string\n  /**\n   * Renders the value for the row in a given columnId the same as `getValue`, but will return the `renderFallbackValue` if no value is found.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#rendervalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  renderValue: <TValue>(columnId: string) => TValue\n  /**\n   * An array of subRows for the row as returned and created by the `options.getSubRows` option.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#subrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  subRows: Row<TData>[]\n}\n\nexport const createRow = <TData extends RowData>(\n  table: Table<TData>,\n  id: string,\n  original: TData,\n  rowIndex: number,\n  depth: number,\n  subRows?: Row<TData>[],\n  parentId?: string\n): Row<TData> => {\n  let row: CoreRow<TData> = {\n    id,\n    index: rowIndex,\n    original,\n    depth,\n    parentId,\n    _valuesCache: {},\n    _uniqueValuesCache: {},\n    getValue: columnId => {\n      if (row._valuesCache.hasOwnProperty(columnId)) {\n        return row._valuesCache[columnId]\n      }\n\n      const column = table.getColumn(columnId)\n\n      if (!column?.accessorFn) {\n        return undefined\n      }\n\n      row._valuesCache[columnId] = column.accessorFn(\n        row.original as TData,\n        rowIndex\n      )\n\n      return row._valuesCache[columnId] as any\n    },\n    getUniqueValues: columnId => {\n      if (row._uniqueValuesCache.hasOwnProperty(columnId)) {\n        return row._uniqueValuesCache[columnId]\n      }\n\n      const column = table.getColumn(columnId)\n\n      if (!column?.accessorFn) {\n        return undefined\n      }\n\n      if (!column.columnDef.getUniqueValues) {\n        row._uniqueValuesCache[columnId] = [row.getValue(columnId)]\n        return row._uniqueValuesCache[columnId]\n      }\n\n      row._uniqueValuesCache[columnId] = column.columnDef.getUniqueValues(\n        row.original as TData,\n        rowIndex\n      )\n\n      return row._uniqueValuesCache[columnId] as any\n    },\n    renderValue: columnId =>\n      row.getValue(columnId) ?? table.options.renderFallbackValue,\n    subRows: subRows ?? [],\n    getLeafRows: () => flattenBy(row.subRows, d => d.subRows),\n    getParentRow: () =>\n      row.parentId ? table.getRow(row.parentId, true) : undefined,\n    getParentRows: () => {\n      let parentRows: Row<TData>[] = []\n      let currentRow = row\n      while (true) {\n        const parentRow = currentRow.getParentRow()\n        if (!parentRow) break\n        parentRows.push(parentRow)\n        currentRow = parentRow\n      }\n      return parentRows.reverse()\n    },\n    getAllCells: memo(\n      () => [table.getAllLeafColumns()],\n      leafColumns => {\n        return leafColumns.map(column => {\n          return createCell(table, row as Row<TData>, column, column.id)\n        })\n      },\n      getMemoOptions(table.options, 'debugRows', 'getAllCells')\n    ),\n\n    _getAllCellsByColumnId: memo(\n      () => [row.getAllCells()],\n      allCells => {\n        return allCells.reduce(\n          (acc, cell) => {\n            acc[cell.column.id] = cell\n            return acc\n          },\n          {} as Record<string, Cell<TData, unknown>>\n        )\n      },\n      getMemoOptions(table.options, 'debugRows', 'getAllCellsByColumnId')\n    ),\n  }\n\n  for (let i = 0; i < table._features.length; i++) {\n    const feature = table._features[i]\n    feature?.createRow?.(row as Row<TData>, table)\n  }\n\n  return row as Row<TData>\n}\n", "import { RowModel } from '..'\nimport { Column, RowData, Table, TableFeature } from '../types'\n\nexport interface FacetedColumn<TData extends RowData> {\n  _getFacetedMinMaxValues?: () => undefined | [number, number]\n  _getFacetedRowModel?: () => RowModel<TData>\n  _getFacetedUniqueValues?: () => Map<any, number>\n  /**\n   * A function that **computes and returns** a min/max tuple derived from `column.getFacetedRowModel`. Useful for displaying faceted result values.\n   * > ⚠️ Requires that you pass a valid `getFacetedMinMaxValues` function to `options.getFacetedMinMaxValues`. A default implementation is provided via the exported `getFacetedMinMaxValues` function.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-faceting#getfacetedminmaxvalues)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-faceting)\n   */\n  getFacetedMinMaxValues: () => undefined | [number, number]\n  /**\n   * Returns the row model with all other column filters applied, excluding its own filter. Useful for displaying faceted result counts.\n   * > ⚠️ Requires that you pass a valid `getFacetedRowModel` function to `options.facetedRowModel`. A default implementation is provided via the exported `getFacetedRowModel` function.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-faceting#getfacetedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-faceting)\n   */\n  getFacetedRowModel: () => RowModel<TData>\n  /**\n   * A function that **computes and returns** a `Map` of unique values and their occurrences derived from `column.getFacetedRowModel`. Useful for displaying faceted result values.\n   * > ⚠️ Requires that you pass a valid `getFacetedUniqueValues` function to `options.getFacetedUniqueValues`. A default implementation is provided via the exported `getFacetedUniqueValues` function.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-faceting#getfaceteduniquevalues)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-faceting)\n   */\n  getFacetedUniqueValues: () => Map<any, number>\n}\n\nexport interface FacetedOptions<TData extends RowData> {\n  getFacetedMinMaxValues?: (\n    table: Table<TData>,\n    columnId: string\n  ) => () => undefined | [number, number]\n  getFacetedRowModel?: (\n    table: Table<TData>,\n    columnId: string\n  ) => () => RowModel<TData>\n  getFacetedUniqueValues?: (\n    table: Table<TData>,\n    columnId: string\n  ) => () => Map<any, number>\n}\n\n//\n\nexport const ColumnFaceting: TableFeature = {\n  createColumn: <TData extends RowData>(\n    column: Column<TData, unknown>,\n    table: Table<TData>\n  ): void => {\n    column._getFacetedRowModel =\n      table.options.getFacetedRowModel &&\n      table.options.getFacetedRowModel(table, column.id)\n    column.getFacetedRowModel = () => {\n      if (!column._getFacetedRowModel) {\n        return table.getPreFilteredRowModel()\n      }\n\n      return column._getFacetedRowModel()\n    }\n    column._getFacetedUniqueValues =\n      table.options.getFacetedUniqueValues &&\n      table.options.getFacetedUniqueValues(table, column.id)\n    column.getFacetedUniqueValues = () => {\n      if (!column._getFacetedUniqueValues) {\n        return new Map()\n      }\n\n      return column._getFacetedUniqueValues()\n    }\n    column._getFacetedMinMaxValues =\n      table.options.getFacetedMinMaxValues &&\n      table.options.getFacetedMinMaxValues(table, column.id)\n    column.getFacetedMinMaxValues = () => {\n      if (!column._getFacetedMinMaxValues) {\n        return undefined\n      }\n\n      return column._getFacetedMinMaxValues()\n    }\n  },\n}\n", "import { FilterFn } from './features/ColumnFiltering'\n\nconst includesString: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: string\n) => {\n  const search = filterValue?.toString()?.toLowerCase()\n  return Boolean(\n    row\n      .getValue<string | null>(columnId)\n      ?.toString()\n      ?.toLowerCase()\n      ?.includes(search)\n  )\n}\n\nincludesString.autoRemove = (val: any) => testFalsey(val)\n\nconst includesStringSensitive: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: string\n) => {\n  return Boolean(\n    row.getValue<string | null>(columnId)?.toString()?.includes(filterValue)\n  )\n}\n\nincludesStringSensitive.autoRemove = (val: any) => testFalsey(val)\n\nconst equalsString: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: string\n) => {\n  return (\n    row.getValue<string | null>(columnId)?.toString()?.toLowerCase() ===\n    filterValue?.toLowerCase()\n  )\n}\n\nequalsString.autoRemove = (val: any) => testFalsey(val)\n\nconst arrIncludes: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: unknown\n) => {\n  return row.getValue<unknown[]>(columnId)?.includes(filterValue)\n}\n\narrIncludes.autoRemove = (val: any) => testFalsey(val)\n\nconst arrIncludesAll: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: unknown[]\n) => {\n  return !filterValue.some(\n    val => !row.getValue<unknown[]>(columnId)?.includes(val)\n  )\n}\n\narrIncludesAll.autoRemove = (val: any) => testFalsey(val) || !val?.length\n\nconst arrIncludesSome: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: unknown[]\n) => {\n  return filterValue.some(val =>\n    row.getValue<unknown[]>(columnId)?.includes(val)\n  )\n}\n\narrIncludesSome.autoRemove = (val: any) => testFalsey(val) || !val?.length\n\nconst equals: FilterFn<any> = (row, columnId: string, filterValue: unknown) => {\n  return row.getValue(columnId) === filterValue\n}\n\nequals.autoRemove = (val: any) => testFalsey(val)\n\nconst weakEquals: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: unknown\n) => {\n  return row.getValue(columnId) == filterValue\n}\n\nweakEquals.autoRemove = (val: any) => testFalsey(val)\n\nconst inNumberRange: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: [number, number]\n) => {\n  let [min, max] = filterValue\n\n  const rowValue = row.getValue<number>(columnId)\n  return rowValue >= min && rowValue <= max\n}\n\ninNumberRange.resolveFilterValue = (val: [any, any]) => {\n  let [unsafeMin, unsafeMax] = val\n\n  let parsedMin =\n    typeof unsafeMin !== 'number' ? parseFloat(unsafeMin as string) : unsafeMin\n  let parsedMax =\n    typeof unsafeMax !== 'number' ? parseFloat(unsafeMax as string) : unsafeMax\n\n  let min =\n    unsafeMin === null || Number.isNaN(parsedMin) ? -Infinity : parsedMin\n  let max = unsafeMax === null || Number.isNaN(parsedMax) ? Infinity : parsedMax\n\n  if (min > max) {\n    const temp = min\n    min = max\n    max = temp\n  }\n\n  return [min, max] as const\n}\n\ninNumberRange.autoRemove = (val: any) =>\n  testFalsey(val) || (testFalsey(val[0]) && testFalsey(val[1]))\n\n// Export\n\nexport const filterFns = {\n  includesString,\n  includesStringSensitive,\n  equalsString,\n  arrIncludes,\n  arrIncludesAll,\n  arrIncludesSome,\n  equals,\n  weakEquals,\n  inNumberRange,\n}\n\nexport type BuiltInFilterFn = keyof typeof filterFns\n\n// Utils\n\nfunction testFalsey(val: any) {\n  return val === undefined || val === null || val === ''\n}\n", "import { RowModel } from '..'\nimport { BuiltInFilterFn, filterFns } from '../filterFns'\nimport {\n  Column,\n  FilterFns,\n  FilterMeta,\n  OnChangeFn,\n  Row,\n  RowData,\n  Table,\n  TableFeature,\n  Updater,\n} from '../types'\nimport { functionalUpdate, isFunction, makeStateUpdater } from '../utils'\n\nexport interface ColumnFiltersTableState {\n  columnFilters: ColumnFiltersState\n}\n\nexport type ColumnFiltersState = ColumnFilter[]\n\nexport interface ColumnFilter {\n  id: string\n  value: unknown\n}\n\nexport interface ResolvedColumnFilter<TData extends RowData> {\n  filterFn: FilterFn<TData>\n  id: string\n  resolvedValue: unknown\n}\n\nexport interface FilterFn<TData extends RowData> {\n  (\n    row: Row<TData>,\n    columnId: string,\n    filterValue: any,\n    addMeta: (meta: FilterMeta) => void\n  ): boolean\n  autoRemove?: ColumnFilterAutoRemoveTestFn<TData>\n  resolveFilterValue?: TransformFilterValueFn<TData>\n}\n\nexport type TransformFilterValueFn<TData extends RowData> = (\n  value: any,\n  column?: Column<TData, unknown>\n) => unknown\n\nexport type ColumnFilterAutoRemoveTestFn<TData extends RowData> = (\n  value: any,\n  column?: Column<TData, unknown>\n) => boolean\n\nexport type CustomFilterFns<TData extends RowData> = Record<\n  string,\n  FilterFn<TData>\n>\n\nexport type FilterFnOption<TData extends RowData> =\n  | 'auto'\n  | BuiltInFilterFn\n  | keyof FilterFns\n  | FilterFn<TData>\n\nexport interface ColumnFiltersColumnDef<TData extends RowData> {\n  /**\n   * Enables/disables the **column** filter for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#enablecolumnfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  enableColumnFilter?: boolean\n  /**\n   * The filter function to use with this column. Can be the name of a built-in filter function or a custom filter function.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#filterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  filterFn?: FilterFnOption<TData>\n}\n\nexport interface ColumnFiltersColumn<TData extends RowData> {\n  /**\n   * Returns an automatically calculated filter function for the column based off of the columns first known value.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getautofilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getAutoFilterFn: () => FilterFn<TData> | undefined\n  /**\n   * Returns whether or not the column can be **column** filtered.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getcanfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getCanFilter: () => boolean\n  /**\n   * Returns the filter function (either user-defined or automatic, depending on configuration) for the columnId specified.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getfilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getFilterFn: () => FilterFn<TData> | undefined\n  /**\n   * Returns the index (including `-1`) of the column filter in the table's `state.columnFilters` array.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getfilterindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getFilterIndex: () => number\n  /**\n   * Returns the current filter value for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getfiltervalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getFilterValue: () => unknown\n  /**\n   * Returns whether or not the column is currently filtered.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getisfiltered)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getIsFiltered: () => boolean\n  /**\n   * A function that sets the current filter value for the column. You can pass it a value or an updater function for immutability-safe operations on existing values.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#setfiltervalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  setFilterValue: (updater: Updater<any>) => void\n}\n\nexport interface ColumnFiltersRow<TData extends RowData> {\n  /**\n   * The column filters map for the row. This object tracks whether a row is passing/failing specific filters by their column ID.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#columnfilters)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  columnFilters: Record<string, boolean>\n  /**\n   * The column filters meta map for the row. This object tracks any filter meta for a row as optionally provided during the filtering process.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#columnfiltersmeta)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  columnFiltersMeta: Record<string, FilterMeta>\n}\n\ninterface ColumnFiltersOptionsBase<TData extends RowData> {\n  /**\n   * Enables/disables **column** filtering for all columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#enablecolumnfilters)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  enableColumnFilters?: boolean\n  /**\n   * Enables/disables all filtering for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#enablefilters)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  enableFilters?: boolean\n  /**\n   * By default, filtering is done from parent rows down (so if a parent row is filtered out, all of its children will be filtered out as well). Setting this option to `true` will cause filtering to be done from leaf rows up (which means parent rows will be included so long as one of their child or grand-child rows is also included).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#filterfromleafrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  filterFromLeafRows?: boolean\n  /**\n   * If provided, this function is called **once** per table and should return a **new function** which will calculate and return the row model for the table when it's filtered.\n   * - For server-side filtering, this function is unnecessary and can be ignored since the server should already return the filtered row model.\n   * - For client-side filtering, this function is required. A default implementation is provided via any table adapter's `{ getFilteredRowModel }` export.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getfilteredrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getFilteredRowModel?: (table: Table<any>) => () => RowModel<any>\n  /**\n   * Disables the `getFilteredRowModel` from being used to filter data. This may be useful if your table needs to dynamically support both client-side and server-side filtering.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#manualfiltering)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  manualFiltering?: boolean\n  /**\n   * By default, filtering is done for all rows (max depth of 100), no matter if they are root level parent rows or the child leaf rows of a parent row. Setting this option to `0` will cause filtering to only be applied to the root level parent rows, with all sub-rows remaining unfiltered. Similarly, setting this option to `1` will cause filtering to only be applied to child leaf rows 1 level deep, and so on.\n\n   * This is useful for situations where you want a row's entire child hierarchy to be visible regardless of the applied filter.\n    * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#maxleafrowfilterdepth)\n    * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  maxLeafRowFilterDepth?: number\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnFilters` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#oncolumnfilterschange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  onColumnFiltersChange?: OnChangeFn<ColumnFiltersState>\n}\n\ntype ResolvedFilterFns = keyof FilterFns extends never\n  ? {\n      filterFns?: Record<string, FilterFn<any>>\n    }\n  : {\n      filterFns: Record<keyof FilterFns, FilterFn<any>>\n    }\n\nexport interface ColumnFiltersOptions<TData extends RowData>\n  extends ColumnFiltersOptionsBase<TData>,\n    ResolvedFilterFns {}\n\nexport interface ColumnFiltersInstance<TData extends RowData> {\n  _getFilteredRowModel?: () => RowModel<TData>\n  /**\n   * Returns the row model for the table after **column** filtering has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getfilteredrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getFilteredRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model for the table before any **column** filtering has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getprefilteredrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getPreFilteredRowModel: () => RowModel<TData>\n  /**\n   * Resets the **columnFilters** state to `initialState.columnFilters`, or `true` can be passed to force a default blank state reset to `[]`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#resetcolumnfilters)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  resetColumnFilters: (defaultState?: boolean) => void\n  /**\n   * Resets the **globalFilter** state to `initialState.globalFilter`, or `true` can be passed to force a default blank state reset to `undefined`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#resetglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  resetGlobalFilter: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.columnFilters` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#setcolumnfilters)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  setColumnFilters: (updater: Updater<ColumnFiltersState>) => void\n  /**\n   * Sets or updates the `state.globalFilter` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#setglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  setGlobalFilter: (updater: Updater<any>) => void\n}\n\n//\n\nexport const ColumnFiltering: TableFeature = {\n  getDefaultColumnDef: <\n    TData extends RowData,\n  >(): ColumnFiltersColumnDef<TData> => {\n    return {\n      filterFn: 'auto',\n    }\n  },\n\n  getInitialState: (state): ColumnFiltersTableState => {\n    return {\n      columnFilters: [],\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ColumnFiltersOptions<TData> => {\n    return {\n      onColumnFiltersChange: makeStateUpdater('columnFilters', table),\n      filterFromLeafRows: false,\n      maxLeafRowFilterDepth: 100,\n    } as ColumnFiltersOptions<TData>\n  },\n\n  createColumn: <TData extends RowData>(\n    column: Column<TData, unknown>,\n    table: Table<TData>\n  ): void => {\n    column.getAutoFilterFn = () => {\n      const firstRow = table.getCoreRowModel().flatRows[0]\n\n      const value = firstRow?.getValue(column.id)\n\n      if (typeof value === 'string') {\n        return filterFns.includesString\n      }\n\n      if (typeof value === 'number') {\n        return filterFns.inNumberRange\n      }\n\n      if (typeof value === 'boolean') {\n        return filterFns.equals\n      }\n\n      if (value !== null && typeof value === 'object') {\n        return filterFns.equals\n      }\n\n      if (Array.isArray(value)) {\n        return filterFns.arrIncludes\n      }\n\n      return filterFns.weakEquals\n    }\n    column.getFilterFn = () => {\n      return isFunction(column.columnDef.filterFn)\n        ? column.columnDef.filterFn\n        : column.columnDef.filterFn === 'auto'\n          ? column.getAutoFilterFn()\n          : // @ts-ignore\n            table.options.filterFns?.[column.columnDef.filterFn as string] ??\n            filterFns[column.columnDef.filterFn as BuiltInFilterFn]\n    }\n    column.getCanFilter = () => {\n      return (\n        (column.columnDef.enableColumnFilter ?? true) &&\n        (table.options.enableColumnFilters ?? true) &&\n        (table.options.enableFilters ?? true) &&\n        !!column.accessorFn\n      )\n    }\n\n    column.getIsFiltered = () => column.getFilterIndex() > -1\n\n    column.getFilterValue = () =>\n      table.getState().columnFilters?.find(d => d.id === column.id)?.value\n\n    column.getFilterIndex = () =>\n      table.getState().columnFilters?.findIndex(d => d.id === column.id) ?? -1\n\n    column.setFilterValue = value => {\n      table.setColumnFilters(old => {\n        const filterFn = column.getFilterFn()\n        const previousFilter = old?.find(d => d.id === column.id)\n\n        const newFilter = functionalUpdate(\n          value,\n          previousFilter ? previousFilter.value : undefined\n        )\n\n        //\n        if (\n          shouldAutoRemoveFilter(filterFn as FilterFn<TData>, newFilter, column)\n        ) {\n          return old?.filter(d => d.id !== column.id) ?? []\n        }\n\n        const newFilterObj = { id: column.id, value: newFilter }\n\n        if (previousFilter) {\n          return (\n            old?.map(d => {\n              if (d.id === column.id) {\n                return newFilterObj\n              }\n              return d\n            }) ?? []\n          )\n        }\n\n        if (old?.length) {\n          return [...old, newFilterObj]\n        }\n\n        return [newFilterObj]\n      })\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    _table: Table<TData>\n  ): void => {\n    row.columnFilters = {}\n    row.columnFiltersMeta = {}\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setColumnFilters = (updater: Updater<ColumnFiltersState>) => {\n      const leafColumns = table.getAllLeafColumns()\n\n      const updateFn = (old: ColumnFiltersState) => {\n        return functionalUpdate(updater, old)?.filter(filter => {\n          const column = leafColumns.find(d => d.id === filter.id)\n\n          if (column) {\n            const filterFn = column.getFilterFn()\n\n            if (shouldAutoRemoveFilter(filterFn, filter.value, column)) {\n              return false\n            }\n          }\n\n          return true\n        })\n      }\n\n      table.options.onColumnFiltersChange?.(updateFn)\n    }\n\n    table.resetColumnFilters = defaultState => {\n      table.setColumnFilters(\n        defaultState ? [] : table.initialState?.columnFilters ?? []\n      )\n    }\n\n    table.getPreFilteredRowModel = () => table.getCoreRowModel()\n    table.getFilteredRowModel = () => {\n      if (!table._getFilteredRowModel && table.options.getFilteredRowModel) {\n        table._getFilteredRowModel = table.options.getFilteredRowModel(table)\n      }\n\n      if (table.options.manualFiltering || !table._getFilteredRowModel) {\n        return table.getPreFilteredRowModel()\n      }\n\n      return table._getFilteredRowModel()\n    }\n  },\n}\n\nexport function shouldAutoRemoveFilter<TData extends RowData>(\n  filterFn?: FilterFn<TData>,\n  value?: any,\n  column?: Column<TData, unknown>\n) {\n  return (\n    (filterFn && filterFn.autoRemove\n      ? filterFn.autoRemove(value, column)\n      : false) ||\n    typeof value === 'undefined' ||\n    (typeof value === 'string' && !value)\n  )\n}\n", "import { AggregationFn } from './features/ColumnGrouping'\nimport { isNumberArray } from './utils'\n\nconst sum: AggregationFn<any> = (columnId, _leafRows, childRows) => {\n  // It's faster to just add the aggregations together instead of\n  // process leaf nodes individually\n  return childRows.reduce((sum, next) => {\n    const nextValue = next.getValue(columnId)\n    return sum + (typeof nextValue === 'number' ? nextValue : 0)\n  }, 0)\n}\n\nconst min: AggregationFn<any> = (columnId, _leafRows, childRows) => {\n  let min: number | undefined\n\n  childRows.forEach(row => {\n    const value = row.getValue<number>(columnId)\n\n    if (\n      value != null &&\n      (min! > value || (min === undefined && value >= value))\n    ) {\n      min = value\n    }\n  })\n\n  return min\n}\n\nconst max: AggregationFn<any> = (columnId, _leafRows, childRows) => {\n  let max: number | undefined\n\n  childRows.forEach(row => {\n    const value = row.getValue<number>(columnId)\n    if (\n      value != null &&\n      (max! < value || (max === undefined && value >= value))\n    ) {\n      max = value\n    }\n  })\n\n  return max\n}\n\nconst extent: AggregationFn<any> = (columnId, _leafRows, childRows) => {\n  let min: number | undefined\n  let max: number | undefined\n\n  childRows.forEach(row => {\n    const value = row.getValue<number>(columnId)\n    if (value != null) {\n      if (min === undefined) {\n        if (value >= value) min = max = value\n      } else {\n        if (min > value) min = value\n        if (max! < value) max = value\n      }\n    }\n  })\n\n  return [min, max]\n}\n\nconst mean: AggregationFn<any> = (columnId, leafRows) => {\n  let count = 0\n  let sum = 0\n\n  leafRows.forEach(row => {\n    let value = row.getValue<number>(columnId)\n    if (value != null && (value = +value) >= value) {\n      ++count, (sum += value)\n    }\n  })\n\n  if (count) return sum / count\n\n  return\n}\n\nconst median: AggregationFn<any> = (columnId, leafRows) => {\n  if (!leafRows.length) {\n    return\n  }\n\n  const values = leafRows.map(row => row.getValue(columnId))\n  if (!isNumberArray(values)) {\n    return\n  }\n  if (values.length === 1) {\n    return values[0]\n  }\n\n  const mid = Math.floor(values.length / 2)\n  const nums = values.sort((a, b) => a - b)\n  return values.length % 2 !== 0 ? nums[mid] : (nums[mid - 1]! + nums[mid]!) / 2\n}\n\nconst unique: AggregationFn<any> = (columnId, leafRows) => {\n  return Array.from(new Set(leafRows.map(d => d.getValue(columnId))).values())\n}\n\nconst uniqueCount: AggregationFn<any> = (columnId, leafRows) => {\n  return new Set(leafRows.map(d => d.getValue(columnId))).size\n}\n\nconst count: AggregationFn<any> = (_columnId, leafRows) => {\n  return leafRows.length\n}\n\nexport const aggregationFns = {\n  sum,\n  min,\n  max,\n  extent,\n  mean,\n  median,\n  unique,\n  uniqueCount,\n  count,\n}\n\nexport type BuiltInAggregationFn = keyof typeof aggregationFns\n", "import { RowModel } from '..'\nimport { BuiltInAggregationFn, aggregationFns } from '../aggregationFns'\nimport {\n  AggregationFns,\n  Cell,\n  Column,\n  ColumnDefTemplate,\n  OnChangeFn,\n  Row,\n  RowData,\n  Table,\n  TableFeature,\n  Updater,\n} from '../types'\nimport { isFunction, makeStateUpdater } from '../utils'\n\nexport type GroupingState = string[]\n\nexport interface GroupingTableState {\n  grouping: GroupingState\n}\n\nexport type AggregationFn<TData extends RowData> = (\n  columnId: string,\n  leafRows: Row<TData>[],\n  childRows: Row<TData>[]\n) => any\n\nexport type CustomAggregationFns = Record<string, AggregationFn<any>>\n\nexport type AggregationFnOption<TData extends RowData> =\n  | 'auto'\n  | keyof AggregationFns\n  | BuiltInAggregationFn\n  | AggregationFn<TData>\n\nexport interface GroupingColumnDef<TData extends RowData, TValue> {\n  /**\n   * The cell to display each row for the column if the cell is an aggregate. If a function is passed, it will be passed a props object with the context of the cell and should return the property type for your adapter (the exact type depends on the adapter being used).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#aggregatedcell)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  aggregatedCell?: ColumnDefTemplate<\n    ReturnType<Cell<TData, TValue>['getContext']>\n  >\n  /**\n   * The resolved aggregation function for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#aggregationfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  aggregationFn?: AggregationFnOption<TData>\n  /**\n   * Enables/disables grouping for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#enablegrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  enableGrouping?: boolean\n  /**\n   * Specify a value to be used for grouping rows on this column. If this option is not specified, the value derived from `accessorKey` / `accessorFn` will be used instead.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getgroupingvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getGroupingValue?: (row: TData) => any\n}\n\nexport interface GroupingColumn<TData extends RowData> {\n  /**\n   * Returns the aggregation function for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getaggregationfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getAggregationFn: () => AggregationFn<TData> | undefined\n  /**\n   * Returns the automatically inferred aggregation function for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getautoaggregationfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getAutoAggregationFn: () => AggregationFn<TData> | undefined\n  /**\n   * Returns whether or not the column can be grouped.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getcangroup)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getCanGroup: () => boolean\n  /**\n   * Returns the index of the column in the grouping state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getgroupedindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getGroupedIndex: () => number\n  /**\n   * Returns whether or not the column is currently grouped.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getisgrouped)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getIsGrouped: () => boolean\n  /**\n   * Returns a function that toggles the grouping state of the column. This is useful for passing to the `onClick` prop of a button.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#gettogglegroupinghandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getToggleGroupingHandler: () => () => void\n  /**\n   * Toggles the grouping state of the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#togglegrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  toggleGrouping: () => void\n}\n\nexport interface GroupingRow {\n  _groupingValuesCache: Record<string, any>\n  /**\n   * Returns the grouping value for any row and column (including leaf rows).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getgroupingvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getGroupingValue: (columnId: string) => unknown\n  /**\n   * Returns whether or not the row is currently grouped.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getisgrouped)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getIsGrouped: () => boolean\n  /**\n   * If this row is grouped, this is the id of the column that this row is grouped by.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#groupingcolumnid)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  groupingColumnId?: string\n  /**\n   * If this row is grouped, this is the unique/shared value for the `groupingColumnId` for all of the rows in this group.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#groupingvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  groupingValue?: unknown\n}\n\nexport interface GroupingCell {\n  /**\n   * Returns whether or not the cell is currently aggregated.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getisaggregated)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getIsAggregated: () => boolean\n  /**\n   * Returns whether or not the cell is currently grouped.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getisgrouped)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getIsGrouped: () => boolean\n  /**\n   * Returns whether or not the cell is currently a placeholder cell.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getisplaceholder)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getIsPlaceholder: () => boolean\n}\n\nexport interface ColumnDefaultOptions {\n  enableGrouping: boolean\n  onGroupingChange: OnChangeFn<GroupingState>\n}\n\ninterface GroupingOptionsBase {\n  /**\n   * Enables/disables grouping for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#enablegrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  enableGrouping?: boolean\n  /**\n   * Returns the row model after grouping has taken place, but no further.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getgroupedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getGroupedRowModel?: (table: Table<any>) => () => RowModel<any>\n  /**\n   * Grouping columns are automatically reordered by default to the start of the columns list. If you would rather remove them or leave them as-is, set the appropriate mode here.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#groupedcolumnmode)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  groupedColumnMode?: false | 'reorder' | 'remove'\n  /**\n   * Enables manual grouping. If this option is set to `true`, the table will not automatically group rows using `getGroupedRowModel()` and instead will expect you to manually group the rows before passing them to the table. This is useful if you are doing server-side grouping and aggregation.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#manualgrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  manualGrouping?: boolean\n  /**\n   * If this function is provided, it will be called when the grouping state changes and you will be expected to manage the state yourself. You can pass the managed state back to the table via the `tableOptions.state.grouping` option.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#ongroupingchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  onGroupingChange?: OnChangeFn<GroupingState>\n}\n\ntype ResolvedAggregationFns = keyof AggregationFns extends never\n  ? {\n      aggregationFns?: Record<string, AggregationFn<any>>\n    }\n  : {\n      aggregationFns: Record<keyof AggregationFns, AggregationFn<any>>\n    }\n\nexport interface GroupingOptions\n  extends GroupingOptionsBase,\n    ResolvedAggregationFns {}\n\nexport type GroupingColumnMode = false | 'reorder' | 'remove'\n\nexport interface GroupingInstance<TData extends RowData> {\n  _getGroupedRowModel?: () => RowModel<TData>\n  /**\n   * Returns the row model for the table after grouping has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getgroupedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getGroupedRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model for the table before any grouping has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getpregroupedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getPreGroupedRowModel: () => RowModel<TData>\n  /**\n   * Resets the **grouping** state to `initialState.grouping`, or `true` can be passed to force a default blank state reset to `[]`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#resetgrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  resetGrouping: (defaultState?: boolean) => void\n  /**\n   * Updates the grouping state of the table via an update function or value.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#setgrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  setGrouping: (updater: Updater<GroupingState>) => void\n}\n\n//\n\nexport const ColumnGrouping: TableFeature = {\n  getDefaultColumnDef: <TData extends RowData>(): GroupingColumnDef<\n    TData,\n    unknown\n  > => {\n    return {\n      aggregatedCell: props => (props.getValue() as any)?.toString?.() ?? null,\n      aggregationFn: 'auto',\n    }\n  },\n\n  getInitialState: (state): GroupingTableState => {\n    return {\n      grouping: [],\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): GroupingOptions => {\n    return {\n      onGroupingChange: makeStateUpdater('grouping', table),\n      groupedColumnMode: 'reorder',\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    column.toggleGrouping = () => {\n      table.setGrouping(old => {\n        // Find any existing grouping for this column\n        if (old?.includes(column.id)) {\n          return old.filter(d => d !== column.id)\n        }\n\n        return [...(old ?? []), column.id]\n      })\n    }\n\n    column.getCanGroup = () => {\n      return (\n        (column.columnDef.enableGrouping ?? true) &&\n        (table.options.enableGrouping ?? true) &&\n        (!!column.accessorFn || !!column.columnDef.getGroupingValue)\n      )\n    }\n\n    column.getIsGrouped = () => {\n      return table.getState().grouping?.includes(column.id)\n    }\n\n    column.getGroupedIndex = () => table.getState().grouping?.indexOf(column.id)\n\n    column.getToggleGroupingHandler = () => {\n      const canGroup = column.getCanGroup()\n\n      return () => {\n        if (!canGroup) return\n        column.toggleGrouping()\n      }\n    }\n    column.getAutoAggregationFn = () => {\n      const firstRow = table.getCoreRowModel().flatRows[0]\n\n      const value = firstRow?.getValue(column.id)\n\n      if (typeof value === 'number') {\n        return aggregationFns.sum\n      }\n\n      if (Object.prototype.toString.call(value) === '[object Date]') {\n        return aggregationFns.extent\n      }\n    }\n    column.getAggregationFn = () => {\n      if (!column) {\n        throw new Error()\n      }\n\n      return isFunction(column.columnDef.aggregationFn)\n        ? column.columnDef.aggregationFn\n        : column.columnDef.aggregationFn === 'auto'\n          ? column.getAutoAggregationFn()\n          : table.options.aggregationFns?.[\n              column.columnDef.aggregationFn as string\n            ] ??\n            aggregationFns[\n              column.columnDef.aggregationFn as BuiltInAggregationFn\n            ]\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setGrouping = updater => table.options.onGroupingChange?.(updater)\n\n    table.resetGrouping = defaultState => {\n      table.setGrouping(defaultState ? [] : table.initialState?.grouping ?? [])\n    }\n\n    table.getPreGroupedRowModel = () => table.getFilteredRowModel()\n    table.getGroupedRowModel = () => {\n      if (!table._getGroupedRowModel && table.options.getGroupedRowModel) {\n        table._getGroupedRowModel = table.options.getGroupedRowModel(table)\n      }\n\n      if (table.options.manualGrouping || !table._getGroupedRowModel) {\n        return table.getPreGroupedRowModel()\n      }\n\n      return table._getGroupedRowModel()\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row.getIsGrouped = () => !!row.groupingColumnId\n    row.getGroupingValue = columnId => {\n      if (row._groupingValuesCache.hasOwnProperty(columnId)) {\n        return row._groupingValuesCache[columnId]\n      }\n\n      const column = table.getColumn(columnId)\n\n      if (!column?.columnDef.getGroupingValue) {\n        return row.getValue(columnId)\n      }\n\n      row._groupingValuesCache[columnId] = column.columnDef.getGroupingValue(\n        row.original\n      )\n\n      return row._groupingValuesCache[columnId]\n    }\n    row._groupingValuesCache = {}\n  },\n\n  createCell: <TData extends RowData, TValue>(\n    cell: Cell<TData, TValue>,\n    column: Column<TData, TValue>,\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    const getRenderValue = () =>\n      cell.getValue() ?? table.options.renderFallbackValue\n\n    cell.getIsGrouped = () =>\n      column.getIsGrouped() && column.id === row.groupingColumnId\n    cell.getIsPlaceholder = () => !cell.getIsGrouped() && column.getIsGrouped()\n    cell.getIsAggregated = () =>\n      !cell.getIsGrouped() && !cell.getIsPlaceholder() && !!row.subRows?.length\n  },\n}\n\nexport function orderColumns<TData extends RowData>(\n  leafColumns: Column<TData, unknown>[],\n  grouping: string[],\n  groupedColumnMode?: GroupingColumnMode\n) {\n  if (!grouping?.length || !groupedColumnMode) {\n    return leafColumns\n  }\n\n  const nonGroupingColumns = leafColumns.filter(\n    col => !grouping.includes(col.id)\n  )\n\n  if (groupedColumnMode === 'remove') {\n    return nonGroupingColumns\n  }\n\n  const groupingColumns = grouping\n    .map(g => leafColumns.find(col => col.id === g)!)\n    .filter(Boolean)\n\n  return [...groupingColumns, ...nonGroupingColumns]\n}\n", "import { getMemoOptions, makeStateUpdater, memo } from '../utils'\n\nimport {\n  Column,\n  OnChangeFn,\n  RowData,\n  Table,\n  TableFeature,\n  Updater,\n} from '../types'\n\nimport { orderColumns } from './ColumnGrouping'\nimport { ColumnPinningPosition, _getVisibleLeafColumns } from '..'\n\nexport interface ColumnOrderTableState {\n  columnOrder: ColumnOrderState\n}\n\nexport type ColumnOrderState = string[]\n\nexport interface ColumnOrderOptions {\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnOrder` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-ordering#oncolumnorderchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-ordering)\n   */\n  onColumnOrderChange?: OnChangeFn<ColumnOrderState>\n}\n\nexport interface ColumnOrderColumn {\n  /**\n   * Returns the index of the column in the order of the visible columns. Optionally pass a `position` parameter to get the index of the column in a sub-section of the table\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-ordering#getindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-ordering)\n   */\n  getIndex: (position?: ColumnPinningPosition | 'center') => number\n  /**\n   * Returns `true` if the column is the first column in the order of the visible columns. Optionally pass a `position` parameter to check if the column is the first in a sub-section of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-ordering#getisfirstcolumn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-ordering)\n   */\n  getIsFirstColumn: (position?: ColumnPinningPosition | 'center') => boolean\n  /**\n   * Returns `true` if the column is the last column in the order of the visible columns. Optionally pass a `position` parameter to check if the column is the last in a sub-section of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-ordering#getislastcolumn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-ordering)\n   */\n  getIsLastColumn: (position?: ColumnPinningPosition | 'center') => boolean\n}\n\nexport interface ColumnOrderDefaultOptions {\n  onColumnOrderChange: OnChangeFn<ColumnOrderState>\n}\n\nexport interface ColumnOrderInstance<TData extends RowData> {\n  _getOrderColumnsFn: () => (\n    columns: Column<TData, unknown>[]\n  ) => Column<TData, unknown>[]\n  /**\n   * Resets the **columnOrder** state to `initialState.columnOrder`, or `true` can be passed to force a default blank state reset to `[]`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-ordering#resetcolumnorder)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-ordering)\n   */\n  resetColumnOrder: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.columnOrder` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-ordering#setcolumnorder)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-ordering)\n   */\n  setColumnOrder: (updater: Updater<ColumnOrderState>) => void\n}\n\n//\n\nexport const ColumnOrdering: TableFeature = {\n  getInitialState: (state): ColumnOrderTableState => {\n    return {\n      columnOrder: [],\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ColumnOrderDefaultOptions => {\n    return {\n      onColumnOrderChange: makeStateUpdater('columnOrder', table),\n    }\n  },\n\n  createColumn: <TData extends RowData>(\n    column: Column<TData, unknown>,\n    table: Table<TData>\n  ): void => {\n    column.getIndex = memo(\n      position => [_getVisibleLeafColumns(table, position)],\n      columns => columns.findIndex(d => d.id === column.id),\n      getMemoOptions(table.options, 'debugColumns', 'getIndex')\n    )\n    column.getIsFirstColumn = position => {\n      const columns = _getVisibleLeafColumns(table, position)\n      return columns[0]?.id === column.id\n    }\n    column.getIsLastColumn = position => {\n      const columns = _getVisibleLeafColumns(table, position)\n      return columns[columns.length - 1]?.id === column.id\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setColumnOrder = updater =>\n      table.options.onColumnOrderChange?.(updater)\n    table.resetColumnOrder = defaultState => {\n      table.setColumnOrder(\n        defaultState ? [] : table.initialState.columnOrder ?? []\n      )\n    }\n    table._getOrderColumnsFn = memo(\n      () => [\n        table.getState().columnOrder,\n        table.getState().grouping,\n        table.options.groupedColumnMode,\n      ],\n      (columnOrder, grouping, groupedColumnMode) =>\n        (columns: Column<TData, unknown>[]) => {\n          // Sort grouped columns to the start of the column list\n          // before the headers are built\n          let orderedColumns: Column<TData, unknown>[] = []\n\n          // If there is no order, return the normal columns\n          if (!columnOrder?.length) {\n            orderedColumns = columns\n          } else {\n            const columnOrderCopy = [...columnOrder]\n\n            // If there is an order, make a copy of the columns\n            const columnsCopy = [...columns]\n\n            // And make a new ordered array of the columns\n\n            // Loop over the columns and place them in order into the new array\n            while (columnsCopy.length && columnOrderCopy.length) {\n              const targetColumnId = columnOrderCopy.shift()\n              const foundIndex = columnsCopy.findIndex(\n                d => d.id === targetColumnId\n              )\n              if (foundIndex > -1) {\n                orderedColumns.push(columnsCopy.splice(foundIndex, 1)[0]!)\n              }\n            }\n\n            // If there are any columns left, add them to the end\n            orderedColumns = [...orderedColumns, ...columnsCopy]\n          }\n\n          return orderColumns(orderedColumns, grouping, groupedColumnMode)\n        },\n      getMemoOptions(table.options, 'debugTable', '_getOrderColumnsFn')\n    )\n  },\n}\n", "import {\n  OnChangeFn,\n  Updater,\n  Table,\n  Column,\n  Row,\n  Cell,\n  RowData,\n  TableFeature,\n} from '../types'\nimport { getMemoOptions, makeStateUpdater, memo } from '../utils'\n\nexport type ColumnPinningPosition = false | 'left' | 'right'\n\nexport interface ColumnPinningState {\n  left?: string[]\n  right?: string[]\n}\n\nexport interface ColumnPinningTableState {\n  columnPinning: ColumnPinningState\n}\n\nexport interface ColumnPinningOptions {\n  /**\n   * Enables/disables column pinning for the table. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#enablecolumnpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  enableColumnPinning?: boolean\n  /**\n   * @deprecated Use `enableColumnPinning` or `enableRowPinning` instead.\n   * Enables/disables all pinning for the table. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#enablepinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  enablePinning?: boolean\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnPinning` changes. This overrides the default internal state management, so you will also need to supply `state.columnPinning` from your own managed state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#oncolumnpinningchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/oncolumnpinningchange)\n   */\n  onColumnPinningChange?: OnChangeFn<ColumnPinningState>\n}\n\nexport interface ColumnPinningDefaultOptions {\n  onColumnPinningChange: OnChangeFn<ColumnPinningState>\n}\n\nexport interface ColumnPinningColumnDef {\n  /**\n   * Enables/disables column pinning for this column. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#enablepinning-1)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  enablePinning?: boolean\n}\n\nexport interface ColumnPinningColumn {\n  /**\n   * Returns whether or not the column can be pinned.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getcanpin)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getCanPin: () => boolean\n  /**\n   * Returns the pinned position of the column. (`'left'`, `'right'` or `false`)\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getispinned)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getIsPinned: () => ColumnPinningPosition\n  /**\n   * Returns the numeric pinned index of the column within a pinned column group.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getpinnedindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getPinnedIndex: () => number\n  /**\n   * Pins a column to the `'left'` or `'right'`, or unpins the column to the center if `false` is passed.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#pin)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  pin: (position: ColumnPinningPosition) => void\n}\n\nexport interface ColumnPinningRow<TData extends RowData> {\n  /**\n   * Returns all center pinned (unpinned) leaf cells in the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getcentervisiblecells)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getCenterVisibleCells: () => Cell<TData, unknown>[]\n  /**\n   * Returns all left pinned leaf cells in the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getleftvisiblecells)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getLeftVisibleCells: () => Cell<TData, unknown>[]\n  /**\n   * Returns all right pinned leaf cells in the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getrightvisiblecells)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getRightVisibleCells: () => Cell<TData, unknown>[]\n}\n\nexport interface ColumnPinningInstance<TData extends RowData> {\n  /**\n   * Returns all center pinned (unpinned) leaf columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getcenterleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getCenterLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns whether or not any columns are pinned. Optionally specify to only check for pinned columns in either the `left` or `right` position.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getissomecolumnspinned)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getIsSomeColumnsPinned: (position?: ColumnPinningPosition) => boolean\n  /**\n   * Returns all left pinned leaf columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getleftleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getLeftLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns all right pinned leaf columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getrightleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getRightLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Resets the **columnPinning** state to `initialState.columnPinning`, or `true` can be passed to force a default blank state reset to `{ left: [], right: [], }`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#resetcolumnpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  resetColumnPinning: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.columnPinning` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#setcolumnpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  setColumnPinning: (updater: Updater<ColumnPinningState>) => void\n}\n\n//\n\nconst getDefaultColumnPinningState = (): ColumnPinningState => ({\n  left: [],\n  right: [],\n})\n\nexport const ColumnPinning: TableFeature = {\n  getInitialState: (state): ColumnPinningTableState => {\n    return {\n      columnPinning: getDefaultColumnPinningState(),\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ColumnPinningDefaultOptions => {\n    return {\n      onColumnPinningChange: makeStateUpdater('columnPinning', table),\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    column.pin = position => {\n      const columnIds = column\n        .getLeafColumns()\n        .map(d => d.id)\n        .filter(Boolean) as string[]\n\n      table.setColumnPinning(old => {\n        if (position === 'right') {\n          return {\n            left: (old?.left ?? []).filter(d => !columnIds?.includes(d)),\n            right: [\n              ...(old?.right ?? []).filter(d => !columnIds?.includes(d)),\n              ...columnIds,\n            ],\n          }\n        }\n\n        if (position === 'left') {\n          return {\n            left: [\n              ...(old?.left ?? []).filter(d => !columnIds?.includes(d)),\n              ...columnIds,\n            ],\n            right: (old?.right ?? []).filter(d => !columnIds?.includes(d)),\n          }\n        }\n\n        return {\n          left: (old?.left ?? []).filter(d => !columnIds?.includes(d)),\n          right: (old?.right ?? []).filter(d => !columnIds?.includes(d)),\n        }\n      })\n    }\n\n    column.getCanPin = () => {\n      const leafColumns = column.getLeafColumns()\n\n      return leafColumns.some(\n        d =>\n          (d.columnDef.enablePinning ?? true) &&\n          (table.options.enableColumnPinning ??\n            table.options.enablePinning ??\n            true)\n      )\n    }\n\n    column.getIsPinned = () => {\n      const leafColumnIds = column.getLeafColumns().map(d => d.id)\n\n      const { left, right } = table.getState().columnPinning\n\n      const isLeft = leafColumnIds.some(d => left?.includes(d))\n      const isRight = leafColumnIds.some(d => right?.includes(d))\n\n      return isLeft ? 'left' : isRight ? 'right' : false\n    }\n\n    column.getPinnedIndex = () => {\n      const position = column.getIsPinned()\n\n      return position\n        ? table.getState().columnPinning?.[position]?.indexOf(column.id) ?? -1\n        : 0\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row.getCenterVisibleCells = memo(\n      () => [\n        row._getAllVisibleCells(),\n        table.getState().columnPinning.left,\n        table.getState().columnPinning.right,\n      ],\n      (allCells, left, right) => {\n        const leftAndRight: string[] = [...(left ?? []), ...(right ?? [])]\n\n        return allCells.filter(d => !leftAndRight.includes(d.column.id))\n      },\n      getMemoOptions(table.options, 'debugRows', 'getCenterVisibleCells')\n    )\n    row.getLeftVisibleCells = memo(\n      () => [row._getAllVisibleCells(), table.getState().columnPinning.left],\n      (allCells, left) => {\n        const cells = (left ?? [])\n          .map(columnId => allCells.find(cell => cell.column.id === columnId)!)\n          .filter(Boolean)\n          .map(d => ({ ...d, position: 'left' }) as Cell<TData, unknown>)\n\n        return cells\n      },\n      getMemoOptions(table.options, 'debugRows', 'getLeftVisibleCells')\n    )\n    row.getRightVisibleCells = memo(\n      () => [row._getAllVisibleCells(), table.getState().columnPinning.right],\n      (allCells, right) => {\n        const cells = (right ?? [])\n          .map(columnId => allCells.find(cell => cell.column.id === columnId)!)\n          .filter(Boolean)\n          .map(d => ({ ...d, position: 'right' }) as Cell<TData, unknown>)\n\n        return cells\n      },\n      getMemoOptions(table.options, 'debugRows', 'getRightVisibleCells')\n    )\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setColumnPinning = updater =>\n      table.options.onColumnPinningChange?.(updater)\n\n    table.resetColumnPinning = defaultState =>\n      table.setColumnPinning(\n        defaultState\n          ? getDefaultColumnPinningState()\n          : table.initialState?.columnPinning ?? getDefaultColumnPinningState()\n      )\n\n    table.getIsSomeColumnsPinned = position => {\n      const pinningState = table.getState().columnPinning\n\n      if (!position) {\n        return Boolean(pinningState.left?.length || pinningState.right?.length)\n      }\n      return Boolean(pinningState[position]?.length)\n    }\n\n    table.getLeftLeafColumns = memo(\n      () => [table.getAllLeafColumns(), table.getState().columnPinning.left],\n      (allColumns, left) => {\n        return (left ?? [])\n          .map(columnId => allColumns.find(column => column.id === columnId)!)\n          .filter(Boolean)\n      },\n      getMemoOptions(table.options, 'debugColumns', 'getLeftLeafColumns')\n    )\n\n    table.getRightLeafColumns = memo(\n      () => [table.getAllLeafColumns(), table.getState().columnPinning.right],\n      (allColumns, right) => {\n        return (right ?? [])\n          .map(columnId => allColumns.find(column => column.id === columnId)!)\n          .filter(Boolean)\n      },\n      getMemoOptions(table.options, 'debugColumns', 'getRightLeafColumns')\n    )\n\n    table.getCenterLeafColumns = memo(\n      () => [\n        table.getAllLeafColumns(),\n        table.getState().columnPinning.left,\n        table.getState().columnPinning.right,\n      ],\n      (allColumns, left, right) => {\n        const leftAndRight: string[] = [...(left ?? []), ...(right ?? [])]\n\n        return allColumns.filter(d => !leftAndRight.includes(d.id))\n      },\n      getMemoOptions(table.options, 'debugColumns', 'getCenterLeafColumns')\n    )\n  },\n}\n", "export function safelyAccessDocument(_document?: Document): Document | null {\n  return _document || (typeof document !== 'undefined' ? document : null)\n}\n\nexport function safelyAccessDocumentEvent(event: Event): Document | null {\n  return !!event &&\n    !!event.target &&\n    typeof event.target === 'object' &&\n    'ownerDocument' in event.target\n    ? (event.target.ownerDocument as Document | null)\n    : null\n}\n", "import { _getVisibleLeafColumns } from '..'\nimport {\n  <PERSON><PERSON><PERSON>,\n  <PERSON>umn,\n  <PERSON>er,\n  OnChangeFn,\n  Table,\n  Updater,\n  TableFeature,\n} from '../types'\nimport { getMemoOptions, makeStateUpdater, memo } from '../utils'\nimport { ColumnPinningPosition } from './ColumnPinning'\nimport { safelyAccessDocument } from '../utils/document'\n\n//\n\nexport interface ColumnSizingTableState {\n  columnSizing: ColumnSizingState\n  columnSizingInfo: ColumnSizingInfoState\n}\n\nexport type ColumnSizingState = Record<string, number>\n\nexport interface ColumnSizingInfoState {\n  columnSizingStart: [string, number][]\n  deltaOffset: null | number\n  deltaPercentage: null | number\n  isResizingColumn: false | string\n  startOffset: null | number\n  startSize: null | number\n}\n\nexport type ColumnResizeMode = 'onChange' | 'onEnd'\n\nexport type ColumnResizeDirection = 'ltr' | 'rtl'\n\nexport interface ColumnSizingOptions {\n  /**\n   * Determines when the columnSizing state is updated. `onChange` updates the state when the user is dragging the resize handle. `onEnd` updates the state when the user releases the resize handle.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#columnresizemode)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  columnResizeMode?: ColumnResizeMode\n  /**\n   * Enables or disables column resizing for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#enablecolumnresizing)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  enableColumnResizing?: boolean\n  /**\n   * Enables or disables right-to-left support for resizing the column. defaults to 'ltr'.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#columnResizeDirection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  columnResizeDirection?: ColumnResizeDirection\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnSizing` changes. This overrides the default internal state management, so you will also need to supply `state.columnSizing` from your own managed state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#oncolumnsizingchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  onColumnSizingChange?: OnChangeFn<ColumnSizingState>\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnSizingInfo` changes. This overrides the default internal state management, so you will also need to supply `state.columnSizingInfo` from your own managed state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#oncolumnsizinginfochange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  onColumnSizingInfoChange?: OnChangeFn<ColumnSizingInfoState>\n}\n\nexport type ColumnSizingDefaultOptions = Pick<\n  ColumnSizingOptions,\n  | 'columnResizeMode'\n  | 'onColumnSizingChange'\n  | 'onColumnSizingInfoChange'\n  | 'columnResizeDirection'\n>\n\nexport interface ColumnSizingInstance {\n  /**\n   * If pinning, returns the total size of the center portion of the table by calculating the sum of the sizes of all unpinned/center leaf-columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getcentertotalsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getCenterTotalSize: () => number\n  /**\n   * Returns the total size of the left portion of the table by calculating the sum of the sizes of all left leaf-columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getlefttotalsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getLeftTotalSize: () => number\n  /**\n   * Returns the total size of the right portion of the table by calculating the sum of the sizes of all right leaf-columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getrighttotalsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getRightTotalSize: () => number\n  /**\n   * Returns the total size of the table by calculating the sum of the sizes of all leaf-columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#gettotalsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getTotalSize: () => number\n  /**\n   * Resets column sizing to its initial state. If `defaultState` is `true`, the default state for the table will be used instead of the initialValue provided to the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#resetcolumnsizing)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  resetColumnSizing: (defaultState?: boolean) => void\n  /**\n   * Resets column sizing info to its initial state. If `defaultState` is `true`, the default state for the table will be used instead of the initialValue provided to the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#resetheadersizeinfo)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  resetHeaderSizeInfo: (defaultState?: boolean) => void\n  /**\n   * Sets the column sizing state using an updater function or a value. This will trigger the underlying `onColumnSizingChange` function if one is passed to the table options, otherwise the state will be managed automatically by the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#setcolumnsizing)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  setColumnSizing: (updater: Updater<ColumnSizingState>) => void\n  /**\n   * Sets the column sizing info state using an updater function or a value. This will trigger the underlying `onColumnSizingInfoChange` function if one is passed to the table options, otherwise the state will be managed automatically by the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#setcolumnsizinginfo)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  setColumnSizingInfo: (updater: Updater<ColumnSizingInfoState>) => void\n}\n\nexport interface ColumnSizingColumnDef {\n  /**\n   * Enables or disables column resizing for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#enableresizing)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  enableResizing?: boolean\n  /**\n   * The maximum allowed size for the column\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#maxsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  maxSize?: number\n  /**\n   * The minimum allowed size for the column\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#minsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  minSize?: number\n  /**\n   * The desired size for the column\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#size)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  size?: number\n}\n\nexport interface ColumnSizingColumn {\n  /**\n   * Returns `true` if the column can be resized.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getcanresize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getCanResize: () => boolean\n  /**\n   * Returns `true` if the column is currently being resized.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getisresizing)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getIsResizing: () => boolean\n  /**\n   * Returns the current size of the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getSize: () => number\n  /**\n   * Returns the offset measurement along the row-axis (usually the x-axis for standard tables) for the header. This is effectively a sum of the offset measurements of all preceding (left) headers in relation to the current column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getstart)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getStart: (position?: ColumnPinningPosition | 'center') => number\n  /**\n   * Returns the offset measurement along the row-axis (usually the x-axis for standard tables) for the header. This is effectively a sum of the offset measurements of all succeeding (right) headers in relation to the current column.\n   */\n  getAfter: (position?: ColumnPinningPosition | 'center') => number\n  /**\n   * Resets the column to its initial size.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#resetsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  resetSize: () => void\n}\n\nexport interface ColumnSizingHeader {\n  /**\n   * Returns an event handler function that can be used to resize the header. It can be used as an:\n   * - `onMouseDown` handler\n   * - `onTouchStart` handler\n   *\n   * The dragging and release events are automatically handled for you.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getresizehandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getResizeHandler: (context?: Document) => (event: unknown) => void\n  /**\n   * Returns the current size of the header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getSize: () => number\n  /**\n   * Returns the offset measurement along the row-axis (usually the x-axis for standard tables) for the header. This is effectively a sum of the offset measurements of all preceding headers.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getstart)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getStart: (position?: ColumnPinningPosition) => number\n}\n\n//\n\nexport const defaultColumnSizing = {\n  size: 150,\n  minSize: 20,\n  maxSize: Number.MAX_SAFE_INTEGER,\n}\n\nconst getDefaultColumnSizingInfoState = (): ColumnSizingInfoState => ({\n  startOffset: null,\n  startSize: null,\n  deltaOffset: null,\n  deltaPercentage: null,\n  isResizingColumn: false,\n  columnSizingStart: [],\n})\n\nexport const ColumnSizing: TableFeature = {\n  getDefaultColumnDef: (): ColumnSizingColumnDef => {\n    return defaultColumnSizing\n  },\n  getInitialState: (state): ColumnSizingTableState => {\n    return {\n      columnSizing: {},\n      columnSizingInfo: getDefaultColumnSizingInfoState(),\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ColumnSizingDefaultOptions => {\n    return {\n      columnResizeMode: 'onEnd',\n      columnResizeDirection: 'ltr',\n      onColumnSizingChange: makeStateUpdater('columnSizing', table),\n      onColumnSizingInfoChange: makeStateUpdater('columnSizingInfo', table),\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    column.getSize = () => {\n      const columnSize = table.getState().columnSizing[column.id]\n\n      return Math.min(\n        Math.max(\n          column.columnDef.minSize ?? defaultColumnSizing.minSize,\n          columnSize ?? column.columnDef.size ?? defaultColumnSizing.size\n        ),\n        column.columnDef.maxSize ?? defaultColumnSizing.maxSize\n      )\n    }\n\n    column.getStart = memo(\n      position => [\n        position,\n        _getVisibleLeafColumns(table, position),\n        table.getState().columnSizing,\n      ],\n      (position, columns) =>\n        columns\n          .slice(0, column.getIndex(position))\n          .reduce((sum, column) => sum + column.getSize(), 0),\n      getMemoOptions(table.options, 'debugColumns', 'getStart')\n    )\n\n    column.getAfter = memo(\n      position => [\n        position,\n        _getVisibleLeafColumns(table, position),\n        table.getState().columnSizing,\n      ],\n      (position, columns) =>\n        columns\n          .slice(column.getIndex(position) + 1)\n          .reduce((sum, column) => sum + column.getSize(), 0),\n      getMemoOptions(table.options, 'debugColumns', 'getAfter')\n    )\n\n    column.resetSize = () => {\n      table.setColumnSizing(({ [column.id]: _, ...rest }) => {\n        return rest\n      })\n    }\n    column.getCanResize = () => {\n      return (\n        (column.columnDef.enableResizing ?? true) &&\n        (table.options.enableColumnResizing ?? true)\n      )\n    }\n    column.getIsResizing = () => {\n      return table.getState().columnSizingInfo.isResizingColumn === column.id\n    }\n  },\n\n  createHeader: <TData extends RowData, TValue>(\n    header: Header<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    header.getSize = () => {\n      let sum = 0\n\n      const recurse = (header: Header<TData, TValue>) => {\n        if (header.subHeaders.length) {\n          header.subHeaders.forEach(recurse)\n        } else {\n          sum += header.column.getSize() ?? 0\n        }\n      }\n\n      recurse(header)\n\n      return sum\n    }\n    header.getStart = () => {\n      if (header.index > 0) {\n        const prevSiblingHeader = header.headerGroup.headers[header.index - 1]!\n        return prevSiblingHeader.getStart() + prevSiblingHeader.getSize()\n      }\n\n      return 0\n    }\n    header.getResizeHandler = _contextDocument => {\n      const column = table.getColumn(header.column.id)\n      const canResize = column?.getCanResize()\n\n      return (e: unknown) => {\n        if (!column || !canResize) {\n          return\n        }\n\n        ;(e as any).persist?.()\n\n        if (isTouchStartEvent(e)) {\n          // lets not respond to multiple touches (e.g. 2 or 3 fingers)\n          if (e.touches && e.touches.length > 1) {\n            return\n          }\n        }\n\n        const startSize = header.getSize()\n\n        const columnSizingStart: [string, number][] = header\n          ? header.getLeafHeaders().map(d => [d.column.id, d.column.getSize()])\n          : [[column.id, column.getSize()]]\n\n        const clientX = isTouchStartEvent(e)\n          ? Math.round(e.touches[0]!.clientX)\n          : (e as MouseEvent).clientX\n\n        const newColumnSizing: ColumnSizingState = {}\n\n        const updateOffset = (\n          eventType: 'move' | 'end',\n          clientXPos?: number\n        ) => {\n          if (typeof clientXPos !== 'number') {\n            return\n          }\n\n          table.setColumnSizingInfo(old => {\n            const deltaDirection =\n              table.options.columnResizeDirection === 'rtl' ? -1 : 1\n            const deltaOffset =\n              (clientXPos - (old?.startOffset ?? 0)) * deltaDirection\n            const deltaPercentage = Math.max(\n              deltaOffset / (old?.startSize ?? 0),\n              -0.999999\n            )\n\n            old.columnSizingStart.forEach(([columnId, headerSize]) => {\n              newColumnSizing[columnId] =\n                Math.round(\n                  Math.max(headerSize + headerSize * deltaPercentage, 0) * 100\n                ) / 100\n            })\n\n            return {\n              ...old,\n              deltaOffset,\n              deltaPercentage,\n            }\n          })\n\n          if (\n            table.options.columnResizeMode === 'onChange' ||\n            eventType === 'end'\n          ) {\n            table.setColumnSizing(old => ({\n              ...old,\n              ...newColumnSizing,\n            }))\n          }\n        }\n\n        const onMove = (clientXPos?: number) => updateOffset('move', clientXPos)\n\n        const onEnd = (clientXPos?: number) => {\n          updateOffset('end', clientXPos)\n\n          table.setColumnSizingInfo(old => ({\n            ...old,\n            isResizingColumn: false,\n            startOffset: null,\n            startSize: null,\n            deltaOffset: null,\n            deltaPercentage: null,\n            columnSizingStart: [],\n          }))\n        }\n\n        const contextDocument = safelyAccessDocument(_contextDocument)\n\n        const mouseEvents = {\n          moveHandler: (e: MouseEvent) => onMove(e.clientX),\n          upHandler: (e: MouseEvent) => {\n            contextDocument?.removeEventListener(\n              'mousemove',\n              mouseEvents.moveHandler\n            )\n            contextDocument?.removeEventListener(\n              'mouseup',\n              mouseEvents.upHandler\n            )\n            onEnd(e.clientX)\n          },\n        }\n\n        const touchEvents = {\n          moveHandler: (e: TouchEvent) => {\n            if (e.cancelable) {\n              e.preventDefault()\n              e.stopPropagation()\n            }\n            onMove(e.touches[0]!.clientX)\n            return false\n          },\n          upHandler: (e: TouchEvent) => {\n            contextDocument?.removeEventListener(\n              'touchmove',\n              touchEvents.moveHandler\n            )\n            contextDocument?.removeEventListener(\n              'touchend',\n              touchEvents.upHandler\n            )\n            if (e.cancelable) {\n              e.preventDefault()\n              e.stopPropagation()\n            }\n            onEnd(e.touches[0]?.clientX)\n          },\n        }\n\n        const passiveIfSupported = passiveEventSupported()\n          ? { passive: false }\n          : false\n\n        if (isTouchStartEvent(e)) {\n          contextDocument?.addEventListener(\n            'touchmove',\n            touchEvents.moveHandler,\n            passiveIfSupported\n          )\n          contextDocument?.addEventListener(\n            'touchend',\n            touchEvents.upHandler,\n            passiveIfSupported\n          )\n        } else {\n          contextDocument?.addEventListener(\n            'mousemove',\n            mouseEvents.moveHandler,\n            passiveIfSupported\n          )\n          contextDocument?.addEventListener(\n            'mouseup',\n            mouseEvents.upHandler,\n            passiveIfSupported\n          )\n        }\n\n        table.setColumnSizingInfo(old => ({\n          ...old,\n          startOffset: clientX,\n          startSize,\n          deltaOffset: 0,\n          deltaPercentage: 0,\n          columnSizingStart,\n          isResizingColumn: column.id,\n        }))\n      }\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setColumnSizing = updater =>\n      table.options.onColumnSizingChange?.(updater)\n    table.setColumnSizingInfo = updater =>\n      table.options.onColumnSizingInfoChange?.(updater)\n    table.resetColumnSizing = defaultState => {\n      table.setColumnSizing(\n        defaultState ? {} : table.initialState.columnSizing ?? {}\n      )\n    }\n    table.resetHeaderSizeInfo = defaultState => {\n      table.setColumnSizingInfo(\n        defaultState\n          ? getDefaultColumnSizingInfoState()\n          : table.initialState.columnSizingInfo ??\n              getDefaultColumnSizingInfoState()\n      )\n    }\n    table.getTotalSize = () =>\n      table.getHeaderGroups()[0]?.headers.reduce((sum, header) => {\n        return sum + header.getSize()\n      }, 0) ?? 0\n    table.getLeftTotalSize = () =>\n      table.getLeftHeaderGroups()[0]?.headers.reduce((sum, header) => {\n        return sum + header.getSize()\n      }, 0) ?? 0\n    table.getCenterTotalSize = () =>\n      table.getCenterHeaderGroups()[0]?.headers.reduce((sum, header) => {\n        return sum + header.getSize()\n      }, 0) ?? 0\n    table.getRightTotalSize = () =>\n      table.getRightHeaderGroups()[0]?.headers.reduce((sum, header) => {\n        return sum + header.getSize()\n      }, 0) ?? 0\n  },\n}\n\nlet passiveSupported: boolean | null = null\nexport function passiveEventSupported() {\n  if (typeof passiveSupported === 'boolean') return passiveSupported\n\n  let supported = false\n  try {\n    const options = {\n      get passive() {\n        supported = true\n        return false\n      },\n    }\n\n    const noop = () => {}\n\n    window.addEventListener('test', noop, options)\n    window.removeEventListener('test', noop)\n  } catch (err) {\n    supported = false\n  }\n  passiveSupported = supported\n  return passiveSupported\n}\n\nfunction isTouchStartEvent(e: unknown): e is TouchEvent {\n  return (e as TouchEvent).type === 'touchstart'\n}\n", "import { ColumnPinningPosition } from '..'\nimport {\n  Cell,\n  Column,\n  OnChangeFn,\n  Table,\n  Updater,\n  Row,\n  RowData,\n  TableFeature,\n} from '../types'\nimport { getMemoOptions, makeStateUpdater, memo } from '../utils'\n\nexport type VisibilityState = Record<string, boolean>\n\nexport interface VisibilityTableState {\n  columnVisibility: VisibilityState\n}\n\nexport interface VisibilityOptions {\n  /**\n   * Whether to enable column hiding. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#enablehiding)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  enableHiding?: boolean\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnVisibility` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#oncolumnvisibilitychange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  onColumnVisibilityChange?: OnChangeFn<VisibilityState>\n}\n\nexport type VisibilityDefaultOptions = Pick<\n  VisibilityOptions,\n  'onColumnVisibilityChange'\n>\n\nexport interface VisibilityInstance<TData extends RowData> {\n  /**\n   * If column pinning, returns a flat array of leaf-node columns that are visible in the unpinned/center portion of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getcentervisibleleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getCenterVisibleLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns whether all columns are visible\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getisallcolumnsvisible)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getIsAllColumnsVisible: () => boolean\n  /**\n   * Returns whether any columns are visible\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getissomecolumnsvisible)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getIsSomeColumnsVisible: () => boolean\n  /**\n   * If column pinning, returns a flat array of leaf-node columns that are visible in the left portion of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getleftvisibleleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getLeftVisibleLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * If column pinning, returns a flat array of leaf-node columns that are visible in the right portion of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getrightvisibleleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getRightVisibleLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns a handler for toggling the visibility of all columns, meant to be bound to a `input[type=checkbox]` element.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#gettoggleallcolumnsvisibilityhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getToggleAllColumnsVisibilityHandler: () => (event: unknown) => void\n  /**\n   * Returns a flat array of columns that are visible, including parent columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getvisibleflatcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getVisibleFlatColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns a flat array of leaf-node columns that are visible.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getvisibleleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getVisibleLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Resets the column visibility state to the initial state. If `defaultState` is provided, the state will be reset to `{}`\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#resetcolumnvisibility)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  resetColumnVisibility: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.columnVisibility` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#setcolumnvisibility)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  setColumnVisibility: (updater: Updater<VisibilityState>) => void\n  /**\n   * Toggles the visibility of all columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#toggleallcolumnsvisible)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  toggleAllColumnsVisible: (value?: boolean) => void\n}\n\nexport interface VisibilityColumnDef {\n  enableHiding?: boolean\n}\n\nexport interface VisibilityRow<TData extends RowData> {\n  _getAllVisibleCells: () => Cell<TData, unknown>[]\n  /**\n   * Returns an array of cells that account for column visibility for the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getvisiblecells)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getVisibleCells: () => Cell<TData, unknown>[]\n}\n\nexport interface VisibilityColumn {\n  /**\n   * Returns whether the column can be hidden\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getcanhide)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getCanHide: () => boolean\n  /**\n   * Returns whether the column is visible\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getisvisible)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getIsVisible: () => boolean\n  /**\n   * Returns a function that can be used to toggle the column visibility. This function can be used to bind to an event handler to a checkbox.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#gettogglevisibilityhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getToggleVisibilityHandler: () => (event: unknown) => void\n  /**\n   * Toggles the visibility of the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#togglevisibility)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  toggleVisibility: (value?: boolean) => void\n}\n\n//\n\nexport const ColumnVisibility: TableFeature = {\n  getInitialState: (state): VisibilityTableState => {\n    return {\n      columnVisibility: {},\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): VisibilityDefaultOptions => {\n    return {\n      onColumnVisibilityChange: makeStateUpdater('columnVisibility', table),\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    column.toggleVisibility = value => {\n      if (column.getCanHide()) {\n        table.setColumnVisibility(old => ({\n          ...old,\n          [column.id]: value ?? !column.getIsVisible(),\n        }))\n      }\n    }\n    column.getIsVisible = () => {\n      const childColumns = column.columns\n      return (\n        (childColumns.length\n          ? childColumns.some(c => c.getIsVisible())\n          : table.getState().columnVisibility?.[column.id]) ?? true\n      )\n    }\n\n    column.getCanHide = () => {\n      return (\n        (column.columnDef.enableHiding ?? true) &&\n        (table.options.enableHiding ?? true)\n      )\n    }\n    column.getToggleVisibilityHandler = () => {\n      return (e: unknown) => {\n        column.toggleVisibility?.(\n          ((e as MouseEvent).target as HTMLInputElement).checked\n        )\n      }\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row._getAllVisibleCells = memo(\n      () => [row.getAllCells(), table.getState().columnVisibility],\n      cells => {\n        return cells.filter(cell => cell.column.getIsVisible())\n      },\n      getMemoOptions(table.options, 'debugRows', '_getAllVisibleCells')\n    )\n    row.getVisibleCells = memo(\n      () => [\n        row.getLeftVisibleCells(),\n        row.getCenterVisibleCells(),\n        row.getRightVisibleCells(),\n      ],\n      (left, center, right) => [...left, ...center, ...right],\n      getMemoOptions(table.options, 'debugRows', 'getVisibleCells')\n    )\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    const makeVisibleColumnsMethod = (\n      key: string,\n      getColumns: () => Column<TData, unknown>[]\n    ): (() => Column<TData, unknown>[]) => {\n      return memo(\n        () => [\n          getColumns(),\n          getColumns()\n            .filter(d => d.getIsVisible())\n            .map(d => d.id)\n            .join('_'),\n        ],\n        columns => {\n          return columns.filter(d => d.getIsVisible?.())\n        },\n        getMemoOptions(table.options, 'debugColumns', key)\n      )\n    }\n\n    table.getVisibleFlatColumns = makeVisibleColumnsMethod(\n      'getVisibleFlatColumns',\n      () => table.getAllFlatColumns()\n    )\n    table.getVisibleLeafColumns = makeVisibleColumnsMethod(\n      'getVisibleLeafColumns',\n      () => table.getAllLeafColumns()\n    )\n    table.getLeftVisibleLeafColumns = makeVisibleColumnsMethod(\n      'getLeftVisibleLeafColumns',\n      () => table.getLeftLeafColumns()\n    )\n    table.getRightVisibleLeafColumns = makeVisibleColumnsMethod(\n      'getRightVisibleLeafColumns',\n      () => table.getRightLeafColumns()\n    )\n    table.getCenterVisibleLeafColumns = makeVisibleColumnsMethod(\n      'getCenterVisibleLeafColumns',\n      () => table.getCenterLeafColumns()\n    )\n\n    table.setColumnVisibility = updater =>\n      table.options.onColumnVisibilityChange?.(updater)\n\n    table.resetColumnVisibility = defaultState => {\n      table.setColumnVisibility(\n        defaultState ? {} : table.initialState.columnVisibility ?? {}\n      )\n    }\n\n    table.toggleAllColumnsVisible = value => {\n      value = value ?? !table.getIsAllColumnsVisible()\n\n      table.setColumnVisibility(\n        table.getAllLeafColumns().reduce(\n          (obj, column) => ({\n            ...obj,\n            [column.id]: !value ? !column.getCanHide?.() : value,\n          }),\n          {}\n        )\n      )\n    }\n\n    table.getIsAllColumnsVisible = () =>\n      !table.getAllLeafColumns().some(column => !column.getIsVisible?.())\n\n    table.getIsSomeColumnsVisible = () =>\n      table.getAllLeafColumns().some(column => column.getIsVisible?.())\n\n    table.getToggleAllColumnsVisibilityHandler = () => {\n      return (e: unknown) => {\n        table.toggleAllColumnsVisible(\n          ((e as MouseEvent).target as HTMLInputElement)?.checked\n        )\n      }\n    }\n  },\n}\n\nexport function _getVisibleLeafColumns<TData extends RowData>(\n  table: Table<TData>,\n  position?: ColumnPinningPosition | 'center'\n) {\n  return !position\n    ? table.getVisibleLeafColumns()\n    : position === 'center'\n      ? table.getCenterVisibleLeafColumns()\n      : position === 'left'\n        ? table.getLeftVisibleLeafColumns()\n        : table.getRightVisibleLeafColumns()\n}\n", "import { RowModel } from '..'\nimport { Table, RowData, TableFeature } from '../types'\n\nexport interface GlobalFacetingInstance<TData extends RowData> {\n  _getGlobalFacetedMinMaxValues?: () => undefined | [number, number]\n  _getGlobalFacetedRowModel?: () => RowModel<TData>\n  _getGlobalFacetedUniqueValues?: () => Map<any, number>\n  /**\n   * Currently, this function returns the built-in `includesString` filter function. In future releases, it may return more dynamic filter functions based on the nature of the data provided.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-faceting#getglobalautofilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-faceting)\n   */\n  getGlobalFacetedMinMaxValues: () => undefined | [number, number]\n  /**\n   * Returns the row model for the table after **global** filtering has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-faceting#getglobalfacetedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-faceting)\n   */\n  getGlobalFacetedRowModel: () => RowModel<TData>\n  /**\n   * Returns the faceted unique values for the global filter.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-faceting#getglobalfaceteduniquevalues)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-faceting)\n   */\n  getGlobalFacetedUniqueValues: () => Map<any, number>\n}\n\n//\n\nexport const GlobalFaceting: TableFeature = {\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table._getGlobalFacetedRowModel =\n      table.options.getFacetedRowModel &&\n      table.options.getFacetedRowModel(table, '__global__')\n\n    table.getGlobalFacetedRowModel = () => {\n      if (table.options.manualFiltering || !table._getGlobalFacetedRowModel) {\n        return table.getPreFilteredRowModel()\n      }\n\n      return table._getGlobalFacetedRowModel()\n    }\n\n    table._getGlobalFacetedUniqueValues =\n      table.options.getFacetedUniqueValues &&\n      table.options.getFacetedUniqueValues(table, '__global__')\n    table.getGlobalFacetedUniqueValues = () => {\n      if (!table._getGlobalFacetedUniqueValues) {\n        return new Map()\n      }\n\n      return table._getGlobalFacetedUniqueValues()\n    }\n\n    table._getGlobalFacetedMinMaxValues =\n      table.options.getFacetedMinMaxValues &&\n      table.options.getFacetedMinMaxValues(table, '__global__')\n    table.getGlobalFacetedMinMaxValues = () => {\n      if (!table._getGlobalFacetedMinMaxValues) {\n        return\n      }\n\n      return table._getGlobalFacetedMinMaxValues()\n    }\n  },\n}\n", "import { FilterFn, FilterFnOption } from '..'\nimport { BuiltInFilterFn, filterFns } from '../filterFns'\nimport {\n  Column,\n  OnChangeFn,\n  Table,\n  Updater,\n  RowData,\n  TableFeature,\n} from '../types'\nimport { isFunction, makeStateUpdater } from '../utils'\n\nexport interface GlobalFilterTableState {\n  globalFilter: any\n}\n\nexport interface GlobalFilterColumnDef {\n  /**\n   * Enables/disables the **global** filter for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#enableglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  enableGlobalFilter?: boolean\n}\n\nexport interface GlobalFilterColumn {\n  /**\n   * Returns whether or not the column can be **globally** filtered. Set to `false` to disable a column from being scanned during global filtering.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#getcanglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  getCanGlobalFilter: () => boolean\n}\n\nexport interface GlobalFilterOptions<TData extends RowData> {\n  /**\n   * Enables/disables **global** filtering for all columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#enableglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  enableGlobalFilter?: boolean\n  /**\n   * If provided, this function will be called with the column and should return `true` or `false` to indicate whether this column should be used for global filtering.\n   *\n   * This is useful if the column can contain data that is not `string` or `number` (i.e. `undefined`).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#getcolumncanglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  getColumnCanGlobalFilter?: (column: Column<TData, unknown>) => boolean\n  /**\n   * The filter function to use for global filtering.\n   * - A `string` referencing a built-in filter function\n   * - A `string` that references a custom filter functions provided via the `tableOptions.filterFns` option\n   * - A custom filter function\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#globalfilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  globalFilterFn?: FilterFnOption<TData>\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.globalFilter` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#onglobalfilterchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  onGlobalFilterChange?: OnChangeFn<any>\n}\n\nexport interface GlobalFilterInstance<TData extends RowData> {\n  /**\n   * Currently, this function returns the built-in `includesString` filter function. In future releases, it may return more dynamic filter functions based on the nature of the data provided.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#getglobalautofilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  getGlobalAutoFilterFn: () => FilterFn<TData> | undefined\n  /**\n   * Returns the filter function (either user-defined or automatic, depending on configuration) for the global filter.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#getglobalfilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  getGlobalFilterFn: () => FilterFn<TData> | undefined\n  /**\n   * Resets the **globalFilter** state to `initialState.globalFilter`, or `true` can be passed to force a default blank state reset to `undefined`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#resetglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  resetGlobalFilter: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.globalFilter` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#setglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  setGlobalFilter: (updater: Updater<any>) => void\n}\n\n//\n\nexport const GlobalFiltering: TableFeature = {\n  getInitialState: (state): GlobalFilterTableState => {\n    return {\n      globalFilter: undefined,\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): GlobalFilterOptions<TData> => {\n    return {\n      onGlobalFilterChange: makeStateUpdater('globalFilter', table),\n      globalFilterFn: 'auto',\n      getColumnCanGlobalFilter: column => {\n        const value = table\n          .getCoreRowModel()\n          .flatRows[0]?._getAllCellsByColumnId()\n          [column.id]?.getValue()\n\n        return typeof value === 'string' || typeof value === 'number'\n      },\n    } as GlobalFilterOptions<TData>\n  },\n\n  createColumn: <TData extends RowData>(\n    column: Column<TData, unknown>,\n    table: Table<TData>\n  ): void => {\n    column.getCanGlobalFilter = () => {\n      return (\n        (column.columnDef.enableGlobalFilter ?? true) &&\n        (table.options.enableGlobalFilter ?? true) &&\n        (table.options.enableFilters ?? true) &&\n        (table.options.getColumnCanGlobalFilter?.(column) ?? true) &&\n        !!column.accessorFn\n      )\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.getGlobalAutoFilterFn = () => {\n      return filterFns.includesString\n    }\n\n    table.getGlobalFilterFn = () => {\n      const { globalFilterFn: globalFilterFn } = table.options\n\n      return isFunction(globalFilterFn)\n        ? globalFilterFn\n        : globalFilterFn === 'auto'\n          ? table.getGlobalAutoFilterFn()\n          : table.options.filterFns?.[globalFilterFn as string] ??\n            filterFns[globalFilterFn as BuiltInFilterFn]\n    }\n\n    table.setGlobalFilter = updater => {\n      table.options.onGlobalFilterChange?.(updater)\n    }\n\n    table.resetGlobalFilter = defaultState => {\n      table.setGlobalFilter(\n        defaultState ? undefined : table.initialState.globalFilter\n      )\n    }\n  },\n}\n", "import { RowModel } from '..'\nimport {\n  OnChangeFn,\n  Table,\n  Row,\n  Updater,\n  RowData,\n  TableFeature,\n} from '../types'\nimport { makeStateUpdater } from '../utils'\n\nexport type ExpandedStateList = Record<string, boolean>\nexport type ExpandedState = true | Record<string, boolean>\nexport interface ExpandedTableState {\n  expanded: ExpandedState\n}\n\nexport interface ExpandedRow {\n  /**\n   * Returns whether the row can be expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getcanexpand)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getCanExpand: () => boolean\n  /**\n   * Returns whether all parent rows of the row are expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getisallparentsexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getIsAllParentsExpanded: () => boolean\n  /**\n   * Returns whether the row is expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getisexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getIsExpanded: () => boolean\n  /**\n   * Returns a function that can be used to toggle the expanded state of the row. This function can be used to bind to an event handler to a button.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#gettoggleexpandedhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getToggleExpandedHandler: () => () => void\n  /**\n   * Toggles the expanded state (or sets it if `expanded` is provided) for the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#toggleexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  toggleExpanded: (expanded?: boolean) => void\n}\n\nexport interface ExpandedOptions<TData extends RowData> {\n  /**\n   * Enable this setting to automatically reset the expanded state of the table when expanding state changes.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#autoresetexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  autoResetExpanded?: boolean\n  /**\n   * Enable/disable expanding for all rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#enableexpanding)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  enableExpanding?: boolean\n  /**\n   * This function is responsible for returning the expanded row model. If this function is not provided, the table will not expand rows. You can use the default exported `getExpandedRowModel` function to get the expanded row model or implement your own.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getexpandedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getExpandedRowModel?: (table: Table<any>) => () => RowModel<any>\n  /**\n   * If provided, allows you to override the default behavior of determining whether a row is currently expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getisrowexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getIsRowExpanded?: (row: Row<TData>) => boolean\n  /**\n   * If provided, allows you to override the default behavior of determining whether a row can be expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getrowcanexpand)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getRowCanExpand?: (row: Row<TData>) => boolean\n  /**\n   * Enables manual row expansion. If this is set to `true`, `getExpandedRowModel` will not be used to expand rows and you would be expected to perform the expansion in your own data model. This is useful if you are doing server-side expansion.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#manualexpanding)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  manualExpanding?: boolean\n  /**\n   * This function is called when the `expanded` table state changes. If a function is provided, you will be responsible for managing this state on your own. To pass the managed state back to the table, use the `tableOptions.state.expanded` option.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#onexpandedchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  onExpandedChange?: OnChangeFn<ExpandedState>\n  /**\n   * If `true` expanded rows will be paginated along with the rest of the table (which means expanded rows may span multiple pages). If `false` expanded rows will not be considered for pagination (which means expanded rows will always render on their parents page. This also means more rows will be rendered than the set page size)\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#paginateexpandedrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  paginateExpandedRows?: boolean\n}\n\nexport interface ExpandedInstance<TData extends RowData> {\n  _autoResetExpanded: () => void\n  _getExpandedRowModel?: () => RowModel<TData>\n  /**\n   * Returns whether there are any rows that can be expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getcansomerowsexpand)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getCanSomeRowsExpand: () => boolean\n  /**\n   * Returns the maximum depth of the expanded rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getexpandeddepth)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getExpandedDepth: () => number\n  /**\n   * Returns the row model after expansion has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getexpandedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getExpandedRowModel: () => RowModel<TData>\n  /**\n   * Returns whether all rows are currently expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getisallrowsexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getIsAllRowsExpanded: () => boolean\n  /**\n   * Returns whether there are any rows that are currently expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getissomerowsexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getIsSomeRowsExpanded: () => boolean\n  /**\n   * Returns the row model before expansion has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getpreexpandedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getPreExpandedRowModel: () => RowModel<TData>\n  /**\n   * Returns a handler that can be used to toggle the expanded state of all rows. This handler is meant to be used with an `input[type=checkbox]` element.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#gettoggleallrowsexpandedhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getToggleAllRowsExpandedHandler: () => (event: unknown) => void\n  /**\n   * Resets the expanded state of the table to the initial state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#resetexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  resetExpanded: (defaultState?: boolean) => void\n  /**\n   * Updates the expanded state of the table via an update function or value.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#setexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  setExpanded: (updater: Updater<ExpandedState>) => void\n  /**\n   * Toggles the expanded state for all rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#toggleallrowsexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  toggleAllRowsExpanded: (expanded?: boolean) => void\n}\n\n//\n\nexport const RowExpanding: TableFeature = {\n  getInitialState: (state): ExpandedTableState => {\n    return {\n      expanded: {},\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ExpandedOptions<TData> => {\n    return {\n      onExpandedChange: makeStateUpdater('expanded', table),\n      paginateExpandedRows: true,\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    let registered = false\n    let queued = false\n\n    table._autoResetExpanded = () => {\n      if (!registered) {\n        table._queue(() => {\n          registered = true\n        })\n        return\n      }\n\n      if (\n        table.options.autoResetAll ??\n        table.options.autoResetExpanded ??\n        !table.options.manualExpanding\n      ) {\n        if (queued) return\n        queued = true\n        table._queue(() => {\n          table.resetExpanded()\n          queued = false\n        })\n      }\n    }\n    table.setExpanded = updater => table.options.onExpandedChange?.(updater)\n    table.toggleAllRowsExpanded = expanded => {\n      if (expanded ?? !table.getIsAllRowsExpanded()) {\n        table.setExpanded(true)\n      } else {\n        table.setExpanded({})\n      }\n    }\n    table.resetExpanded = defaultState => {\n      table.setExpanded(defaultState ? {} : table.initialState?.expanded ?? {})\n    }\n    table.getCanSomeRowsExpand = () => {\n      return table\n        .getPrePaginationRowModel()\n        .flatRows.some(row => row.getCanExpand())\n    }\n    table.getToggleAllRowsExpandedHandler = () => {\n      return (e: unknown) => {\n        ;(e as any).persist?.()\n        table.toggleAllRowsExpanded()\n      }\n    }\n    table.getIsSomeRowsExpanded = () => {\n      const expanded = table.getState().expanded\n      return expanded === true || Object.values(expanded).some(Boolean)\n    }\n    table.getIsAllRowsExpanded = () => {\n      const expanded = table.getState().expanded\n\n      // If expanded is true, save some cycles and return true\n      if (typeof expanded === 'boolean') {\n        return expanded === true\n      }\n\n      if (!Object.keys(expanded).length) {\n        return false\n      }\n\n      // If any row is not expanded, return false\n      if (table.getRowModel().flatRows.some(row => !row.getIsExpanded())) {\n        return false\n      }\n\n      // They must all be expanded :shrug:\n      return true\n    }\n    table.getExpandedDepth = () => {\n      let maxDepth = 0\n\n      const rowIds =\n        table.getState().expanded === true\n          ? Object.keys(table.getRowModel().rowsById)\n          : Object.keys(table.getState().expanded)\n\n      rowIds.forEach(id => {\n        const splitId = id.split('.')\n        maxDepth = Math.max(maxDepth, splitId.length)\n      })\n\n      return maxDepth\n    }\n    table.getPreExpandedRowModel = () => table.getSortedRowModel()\n    table.getExpandedRowModel = () => {\n      if (!table._getExpandedRowModel && table.options.getExpandedRowModel) {\n        table._getExpandedRowModel = table.options.getExpandedRowModel(table)\n      }\n\n      if (table.options.manualExpanding || !table._getExpandedRowModel) {\n        return table.getPreExpandedRowModel()\n      }\n\n      return table._getExpandedRowModel()\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row.toggleExpanded = expanded => {\n      table.setExpanded(old => {\n        const exists = old === true ? true : !!old?.[row.id]\n\n        let oldExpanded: ExpandedStateList = {}\n\n        if (old === true) {\n          Object.keys(table.getRowModel().rowsById).forEach(rowId => {\n            oldExpanded[rowId] = true\n          })\n        } else {\n          oldExpanded = old\n        }\n\n        expanded = expanded ?? !exists\n\n        if (!exists && expanded) {\n          return {\n            ...oldExpanded,\n            [row.id]: true,\n          }\n        }\n\n        if (exists && !expanded) {\n          const { [row.id]: _, ...rest } = oldExpanded\n          return rest\n        }\n\n        return old\n      })\n    }\n    row.getIsExpanded = () => {\n      const expanded = table.getState().expanded\n\n      return !!(\n        table.options.getIsRowExpanded?.(row) ??\n        (expanded === true || expanded?.[row.id])\n      )\n    }\n    row.getCanExpand = () => {\n      return (\n        table.options.getRowCanExpand?.(row) ??\n        ((table.options.enableExpanding ?? true) && !!row.subRows?.length)\n      )\n    }\n    row.getIsAllParentsExpanded = () => {\n      let isFullyExpanded = true\n      let currentRow = row\n\n      while (isFullyExpanded && currentRow.parentId) {\n        currentRow = table.getRow(currentRow.parentId, true)\n        isFullyExpanded = currentRow.getIsExpanded()\n      }\n\n      return isFullyExpanded\n    }\n    row.getToggleExpandedHandler = () => {\n      const canExpand = row.getCanExpand()\n\n      return () => {\n        if (!canExpand) return\n        row.toggleExpanded()\n      }\n    }\n  },\n}\n", "import {\n  OnChangeFn,\n  Table,\n  RowModel,\n  Updater,\n  <PERSON>Data,\n  TableFeature,\n} from '../types'\nimport {\n  functionalUpdate,\n  getMemoOptions,\n  makeStateUpdater,\n  memo,\n} from '../utils'\n\nexport interface PaginationState {\n  pageIndex: number\n  pageSize: number\n}\n\nexport interface PaginationTableState {\n  pagination: PaginationState\n}\n\nexport interface PaginationInitialTableState {\n  pagination?: Partial<PaginationState>\n}\n\nexport interface PaginationOptions {\n  /**\n   * If set to `true`, pagination will be reset to the first page when page-altering state changes eg. `data` is updated, filters change, grouping changes, etc.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#autoresetpageindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  autoResetPageIndex?: boolean\n  /**\n   * Returns the row model after pagination has taken place, but no further.\n   *\n   * Pagination columns are automatically reordered by default to the start of the columns list. If you would rather remove them or leave them as-is, set the appropriate mode here.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getpaginationrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getPaginationRowModel?: (table: Table<any>) => () => RowModel<any>\n  /**\n   * Enables manual pagination. If this option is set to `true`, the table will not automatically paginate rows using `getPaginationRowModel()` and instead will expect you to manually paginate the rows before passing them to the table. This is useful if you are doing server-side pagination and aggregation.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#manualpagination)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  manualPagination?: boolean\n  /**\n   * If this function is provided, it will be called when the pagination state changes and you will be expected to manage the state yourself. You can pass the managed state back to the table via the `tableOptions.state.pagination` option.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#onpaginationchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  onPaginationChange?: OnChangeFn<PaginationState>\n  /**\n   * When manually controlling pagination, you can supply a total `pageCount` value to the table if you know it (Or supply a `rowCount` and `pageCount` will be calculated). If you do not know how many pages there are, you can set this to `-1`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#pagecount)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  pageCount?: number\n  /**\n   * When manually controlling pagination, you can supply a total `rowCount` value to the table if you know it. The `pageCount` can be calculated from this value and the `pageSize`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#rowcount)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  rowCount?: number\n}\n\nexport interface PaginationDefaultOptions {\n  onPaginationChange: OnChangeFn<PaginationState>\n}\n\nexport interface PaginationInstance<TData extends RowData> {\n  _autoResetPageIndex: () => void\n  _getPaginationRowModel?: () => RowModel<TData>\n  /**\n   * Returns whether the table can go to the next page.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getcannextpage)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getCanNextPage: () => boolean\n  /**\n   * Returns whether the table can go to the previous page.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getcanpreviouspage)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getCanPreviousPage: () => boolean\n  /**\n   * Returns the page count. If manually paginating or controlling the pagination state, this will come directly from the `options.pageCount` table option, otherwise it will be calculated from the table data using the total row count and current page size.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getpagecount)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getPageCount: () => number\n  /**\n   * Returns the row count. If manually paginating or controlling the pagination state, this will come directly from the `options.rowCount` table option, otherwise it will be calculated from the table data.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getrowcount)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getRowCount: () => number\n  /**\n   * Returns an array of page options (zero-index-based) for the current page size.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getpageoptions)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getPageOptions: () => number[]\n  /**\n   * Returns the row model for the table after pagination has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getpaginationrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getPaginationRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model for the table before any pagination has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getprepaginationrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getPrePaginationRowModel: () => RowModel<TData>\n  /**\n   * Increments the page index by one, if possible.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#nextpage)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  nextPage: () => void\n  /**\n   * Decrements the page index by one, if possible.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#previouspage)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  previousPage: () => void\n  /**\n   * Sets the page index to `0`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#firstpage)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  firstPage: () => void\n  /**\n   * Sets the page index to the last page.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#lastpage)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  lastPage: () => void\n  /**\n   * Resets the page index to its initial state. If `defaultState` is `true`, the page index will be reset to `0` regardless of initial state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#resetpageindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  resetPageIndex: (defaultState?: boolean) => void\n  /**\n   * Resets the page size to its initial state. If `defaultState` is `true`, the page size will be reset to `10` regardless of initial state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#resetpagesize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  resetPageSize: (defaultState?: boolean) => void\n  /**\n   * Resets the **pagination** state to `initialState.pagination`, or `true` can be passed to force a default blank state reset to `[]`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#resetpagination)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  resetPagination: (defaultState?: boolean) => void\n  /**\n   * @deprecated The page count no longer exists in the pagination state. Just pass as a table option instead.\n   */\n  setPageCount: (updater: Updater<number>) => void\n  /**\n   * Updates the page index using the provided function or value in the `state.pagination.pageIndex` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#setpageindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  setPageIndex: (updater: Updater<number>) => void\n  /**\n   * Updates the page size using the provided function or value in the `state.pagination.pageSize` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#setpagesize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  setPageSize: (updater: Updater<number>) => void\n  /**\n   * Sets or updates the `state.pagination` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#setpagination)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  setPagination: (updater: Updater<PaginationState>) => void\n}\n\n//\n\nconst defaultPageIndex = 0\nconst defaultPageSize = 10\n\nconst getDefaultPaginationState = (): PaginationState => ({\n  pageIndex: defaultPageIndex,\n  pageSize: defaultPageSize,\n})\n\nexport const RowPagination: TableFeature = {\n  getInitialState: (state): PaginationTableState => {\n    return {\n      ...state,\n      pagination: {\n        ...getDefaultPaginationState(),\n        ...state?.pagination,\n      },\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): PaginationDefaultOptions => {\n    return {\n      onPaginationChange: makeStateUpdater('pagination', table),\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    let registered = false\n    let queued = false\n\n    table._autoResetPageIndex = () => {\n      if (!registered) {\n        table._queue(() => {\n          registered = true\n        })\n        return\n      }\n\n      if (\n        table.options.autoResetAll ??\n        table.options.autoResetPageIndex ??\n        !table.options.manualPagination\n      ) {\n        if (queued) return\n        queued = true\n        table._queue(() => {\n          table.resetPageIndex()\n          queued = false\n        })\n      }\n    }\n    table.setPagination = updater => {\n      const safeUpdater: Updater<PaginationState> = old => {\n        let newState = functionalUpdate(updater, old)\n\n        return newState\n      }\n\n      return table.options.onPaginationChange?.(safeUpdater)\n    }\n    table.resetPagination = defaultState => {\n      table.setPagination(\n        defaultState\n          ? getDefaultPaginationState()\n          : table.initialState.pagination ?? getDefaultPaginationState()\n      )\n    }\n    table.setPageIndex = updater => {\n      table.setPagination(old => {\n        let pageIndex = functionalUpdate(updater, old.pageIndex)\n\n        const maxPageIndex =\n          typeof table.options.pageCount === 'undefined' ||\n          table.options.pageCount === -1\n            ? Number.MAX_SAFE_INTEGER\n            : table.options.pageCount - 1\n\n        pageIndex = Math.max(0, Math.min(pageIndex, maxPageIndex))\n\n        return {\n          ...old,\n          pageIndex,\n        }\n      })\n    }\n    table.resetPageIndex = defaultState => {\n      table.setPageIndex(\n        defaultState\n          ? defaultPageIndex\n          : table.initialState?.pagination?.pageIndex ?? defaultPageIndex\n      )\n    }\n    table.resetPageSize = defaultState => {\n      table.setPageSize(\n        defaultState\n          ? defaultPageSize\n          : table.initialState?.pagination?.pageSize ?? defaultPageSize\n      )\n    }\n    table.setPageSize = updater => {\n      table.setPagination(old => {\n        const pageSize = Math.max(1, functionalUpdate(updater, old.pageSize))\n        const topRowIndex = old.pageSize * old.pageIndex!\n        const pageIndex = Math.floor(topRowIndex / pageSize)\n\n        return {\n          ...old,\n          pageIndex,\n          pageSize,\n        }\n      })\n    }\n    //deprecated\n    table.setPageCount = updater =>\n      table.setPagination(old => {\n        let newPageCount = functionalUpdate(\n          updater,\n          table.options.pageCount ?? -1\n        )\n\n        if (typeof newPageCount === 'number') {\n          newPageCount = Math.max(-1, newPageCount)\n        }\n\n        return {\n          ...old,\n          pageCount: newPageCount,\n        }\n      })\n\n    table.getPageOptions = memo(\n      () => [table.getPageCount()],\n      pageCount => {\n        let pageOptions: number[] = []\n        if (pageCount && pageCount > 0) {\n          pageOptions = [...new Array(pageCount)].fill(null).map((_, i) => i)\n        }\n        return pageOptions\n      },\n      getMemoOptions(table.options, 'debugTable', 'getPageOptions')\n    )\n\n    table.getCanPreviousPage = () => table.getState().pagination.pageIndex > 0\n\n    table.getCanNextPage = () => {\n      const { pageIndex } = table.getState().pagination\n\n      const pageCount = table.getPageCount()\n\n      if (pageCount === -1) {\n        return true\n      }\n\n      if (pageCount === 0) {\n        return false\n      }\n\n      return pageIndex < pageCount - 1\n    }\n\n    table.previousPage = () => {\n      return table.setPageIndex(old => old - 1)\n    }\n\n    table.nextPage = () => {\n      return table.setPageIndex(old => {\n        return old + 1\n      })\n    }\n\n    table.firstPage = () => {\n      return table.setPageIndex(0)\n    }\n\n    table.lastPage = () => {\n      return table.setPageIndex(table.getPageCount() - 1)\n    }\n\n    table.getPrePaginationRowModel = () => table.getExpandedRowModel()\n    table.getPaginationRowModel = () => {\n      if (\n        !table._getPaginationRowModel &&\n        table.options.getPaginationRowModel\n      ) {\n        table._getPaginationRowModel =\n          table.options.getPaginationRowModel(table)\n      }\n\n      if (table.options.manualPagination || !table._getPaginationRowModel) {\n        return table.getPrePaginationRowModel()\n      }\n\n      return table._getPaginationRowModel()\n    }\n\n    table.getPageCount = () => {\n      return (\n        table.options.pageCount ??\n        Math.ceil(table.getRowCount() / table.getState().pagination.pageSize)\n      )\n    }\n\n    table.getRowCount = () => {\n      return (\n        table.options.rowCount ?? table.getPrePaginationRowModel().rows.length\n      )\n    }\n  },\n}\n", "import {\n  OnChangeFn,\n  Updater,\n  Table,\n  Row,\n  RowData,\n  TableFeature,\n} from '../types'\nimport { getMemoOptions, makeStateUpdater, memo } from '../utils'\n\nexport type RowPinningPosition = false | 'top' | 'bottom'\n\nexport interface RowPinningState {\n  bottom?: string[]\n  top?: string[]\n}\n\nexport interface RowPinningTableState {\n  rowPinning: RowPinningState\n}\n\nexport interface RowPinningOptions<TData extends RowData> {\n  /**\n   * Enables/disables row pinning for the table. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#enablerowpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  enableRowPinning?: boolean | ((row: Row<TData>) => boolean)\n  /**\n   * When `false`, pinned rows will not be visible if they are filtered or paginated out of the table. When `true`, pinned rows will always be visible regardless of filtering or pagination. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#keeppinnedrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  keepPinnedRows?: boolean\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.rowPinning` changes. This overrides the default internal state management, so you will also need to supply `state.rowPinning` from your own managed state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#onrowpinningchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/onrowpinningchange)\n   */\n  onRowPinningChange?: OnChangeFn<RowPinningState>\n}\n\nexport interface RowPinningDefaultOptions {\n  onRowPinningChange: OnChangeFn<RowPinningState>\n}\n\nexport interface RowPinningRow {\n  /**\n   * Returns whether or not the row can be pinned.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#getcanpin-1)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getCanPin: () => boolean\n  /**\n   * Returns the pinned position of the row. (`'top'`, `'bottom'` or `false`)\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#getispinned-1)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getIsPinned: () => RowPinningPosition\n  /**\n   * Returns the numeric pinned index of the row within a pinned row group.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#getpinnedindex-1)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getPinnedIndex: () => number\n  /**\n   * Pins a row to the `'top'` or `'bottom'`, or unpins the row to the center if `false` is passed.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#pin-1)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  pin: (\n    position: RowPinningPosition,\n    includeLeafRows?: boolean,\n    includeParentRows?: boolean\n  ) => void\n}\n\nexport interface RowPinningInstance<TData extends RowData> {\n  _getPinnedRows: (\n    visiblePinnedRows: Array<Row<TData>>,\n    pinnedRowIds: Array<string> | undefined,\n    position: 'top' | 'bottom'\n  ) => Row<TData>[]\n  /**\n   * Returns all bottom pinned rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#getbottomrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getBottomRows: () => Row<TData>[]\n  /**\n   * Returns all rows that are not pinned to the top or bottom.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#getcenterrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getCenterRows: () => Row<TData>[]\n  /**\n   * Returns whether or not any rows are pinned. Optionally specify to only check for pinned rows in either the `top` or `bottom` position.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#getissomerowspinned)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getIsSomeRowsPinned: (position?: RowPinningPosition) => boolean\n  /**\n   * Returns all top pinned rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#gettoprows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getTopRows: () => Row<TData>[]\n  /**\n   * Resets the **rowPinning** state to `initialState.rowPinning`, or `true` can be passed to force a default blank state reset to `{ top: [], bottom: [], }`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#resetrowpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  resetRowPinning: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.rowPinning` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#setrowpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  setRowPinning: (updater: Updater<RowPinningState>) => void\n}\n\n//\n\nconst getDefaultRowPinningState = (): RowPinningState => ({\n  top: [],\n  bottom: [],\n})\n\nexport const RowPinning: TableFeature = {\n  getInitialState: (state): RowPinningTableState => {\n    return {\n      rowPinning: getDefaultRowPinningState(),\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): RowPinningDefaultOptions => {\n    return {\n      onRowPinningChange: makeStateUpdater('rowPinning', table),\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row.pin = (position, includeLeafRows, includeParentRows) => {\n      const leafRowIds = includeLeafRows\n        ? row.getLeafRows().map(({ id }) => id)\n        : []\n      const parentRowIds = includeParentRows\n        ? row.getParentRows().map(({ id }) => id)\n        : []\n      const rowIds = new Set([...parentRowIds, row.id, ...leafRowIds])\n\n      table.setRowPinning(old => {\n        if (position === 'bottom') {\n          return {\n            top: (old?.top ?? []).filter(d => !rowIds?.has(d)),\n            bottom: [\n              ...(old?.bottom ?? []).filter(d => !rowIds?.has(d)),\n              ...Array.from(rowIds),\n            ],\n          }\n        }\n\n        if (position === 'top') {\n          return {\n            top: [\n              ...(old?.top ?? []).filter(d => !rowIds?.has(d)),\n              ...Array.from(rowIds),\n            ],\n            bottom: (old?.bottom ?? []).filter(d => !rowIds?.has(d)),\n          }\n        }\n\n        return {\n          top: (old?.top ?? []).filter(d => !rowIds?.has(d)),\n          bottom: (old?.bottom ?? []).filter(d => !rowIds?.has(d)),\n        }\n      })\n    }\n    row.getCanPin = () => {\n      const { enableRowPinning, enablePinning } = table.options\n      if (typeof enableRowPinning === 'function') {\n        return enableRowPinning(row)\n      }\n      return enableRowPinning ?? enablePinning ?? true\n    }\n    row.getIsPinned = () => {\n      const rowIds = [row.id]\n\n      const { top, bottom } = table.getState().rowPinning\n\n      const isTop = rowIds.some(d => top?.includes(d))\n      const isBottom = rowIds.some(d => bottom?.includes(d))\n\n      return isTop ? 'top' : isBottom ? 'bottom' : false\n    }\n    row.getPinnedIndex = () => {\n      const position = row.getIsPinned()\n      if (!position) return -1\n\n      const visiblePinnedRowIds = (\n        position === 'top' ? table.getTopRows() : table.getBottomRows()\n      )?.map(({ id }) => id)\n\n      return visiblePinnedRowIds?.indexOf(row.id) ?? -1\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setRowPinning = updater => table.options.onRowPinningChange?.(updater)\n\n    table.resetRowPinning = defaultState =>\n      table.setRowPinning(\n        defaultState\n          ? getDefaultRowPinningState()\n          : table.initialState?.rowPinning ?? getDefaultRowPinningState()\n      )\n\n    table.getIsSomeRowsPinned = position => {\n      const pinningState = table.getState().rowPinning\n\n      if (!position) {\n        return Boolean(pinningState.top?.length || pinningState.bottom?.length)\n      }\n      return Boolean(pinningState[position]?.length)\n    }\n\n    table._getPinnedRows = (visibleRows, pinnedRowIds, position) => {\n      const rows =\n        table.options.keepPinnedRows ?? true\n          ? //get all rows that are pinned even if they would not be otherwise visible\n            //account for expanded parent rows, but not pagination or filtering\n            (pinnedRowIds ?? []).map(rowId => {\n              const row = table.getRow(rowId, true)\n              return row.getIsAllParentsExpanded() ? row : null\n            })\n          : //else get only visible rows that are pinned\n            (pinnedRowIds ?? []).map(\n              rowId => visibleRows.find(row => row.id === rowId)!\n            )\n\n      return rows.filter(Boolean).map(d => ({ ...d, position })) as Row<TData>[]\n    }\n\n    table.getTopRows = memo(\n      () => [table.getRowModel().rows, table.getState().rowPinning.top],\n      (allRows, topPinnedRowIds) =>\n        table._getPinnedRows(allRows, topPinnedRowIds, 'top'),\n      getMemoOptions(table.options, 'debugRows', 'getTopRows')\n    )\n\n    table.getBottomRows = memo(\n      () => [table.getRowModel().rows, table.getState().rowPinning.bottom],\n      (allRows, bottomPinnedRowIds) =>\n        table._getPinnedRows(allRows, bottomPinnedRowIds, 'bottom'),\n      getMemoOptions(table.options, 'debugRows', 'getBottomRows')\n    )\n\n    table.getCenterRows = memo(\n      () => [\n        table.getRowModel().rows,\n        table.getState().rowPinning.top,\n        table.getState().rowPinning.bottom,\n      ],\n      (allRows, top, bottom) => {\n        const topAndBottom = new Set([...(top ?? []), ...(bottom ?? [])])\n        return allRows.filter(d => !topAndBottom.has(d.id))\n      },\n      getMemoOptions(table.options, 'debugRows', 'getCenterRows')\n    )\n  },\n}\n", "import {\n  OnChangeFn,\n  Table,\n  Row,\n  <PERSON>Model,\n  Updater,\n  RowData,\n  TableFeature,\n} from '../types'\nimport { getMemoOptions, makeStateUpdater, memo } from '../utils'\n\nexport type RowSelectionState = Record<string, boolean>\n\nexport interface RowSelectionTableState {\n  rowSelection: RowSelectionState\n}\n\nexport interface RowSelectionOptions<TData extends RowData> {\n  /**\n   * - Enables/disables multiple row selection for all rows in the table OR\n   * - A function that given a row, returns whether to enable/disable multiple row selection for that row's children/grandchildren\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#enablemultirowselection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  enableMultiRowSelection?: boolean | ((row: Row<TData>) => boolean)\n  /**\n   * - Enables/disables row selection for all rows in the table OR\n   * - A function that given a row, returns whether to enable/disable row selection for that row\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#enablerowselection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  enableRowSelection?: boolean | ((row: Row<TData>) => boolean)\n  /**\n   * Enables/disables automatic sub-row selection when a parent row is selected, or a function that enables/disables automatic sub-row selection for each row.\n   * (Use in combination with expanding or grouping features)\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#enablesubrowselection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  enableSubRowSelection?: boolean | ((row: Row<TData>) => boolean)\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.rowSelection` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#onrowselectionchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  onRowSelectionChange?: OnChangeFn<RowSelectionState>\n  // enableGroupingRowSelection?:\n  //   | boolean\n  //   | ((\n  //       row: Row<TData>\n  //     ) => boolean)\n  // isAdditiveSelectEvent?: (e: unknown) => boolean\n  // isInclusiveSelectEvent?: (e: unknown) => boolean\n  // selectRowsFn?: (\n  //   table: Table<TData>,\n  //   rowModel: RowModel<TData>\n  // ) => RowModel<TData>\n}\n\nexport interface RowSelectionRow {\n  /**\n   * Returns whether or not the row can multi-select.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getcanmultiselect)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getCanMultiSelect: () => boolean\n  /**\n   * Returns whether or not the row can be selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getcanselect)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getCanSelect: () => boolean\n  /**\n   * Returns whether or not the row can select sub rows automatically when the parent row is selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getcanselectsubrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getCanSelectSubRows: () => boolean\n  /**\n   * Returns whether or not all of the row's sub rows are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getisallsubrowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsAllSubRowsSelected: () => boolean\n  /**\n   * Returns whether or not the row is selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getisselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsSelected: () => boolean\n  /**\n   * Returns whether or not some of the row's sub rows are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getissomeselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsSomeSelected: () => boolean\n  /**\n   * Returns a handler that can be used to toggle the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#gettoggleselectedhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getToggleSelectedHandler: () => (event: unknown) => void\n  /**\n   * Selects/deselects the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#toggleselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  toggleSelected: (value?: boolean, opts?: { selectChildren?: boolean }) => void\n}\n\nexport interface RowSelectionInstance<TData extends RowData> {\n  /**\n   * Returns the row model of all rows that are selected after filtering has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getfilteredselectedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getFilteredSelectedRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model of all rows that are selected after grouping has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getgroupedselectedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getGroupedSelectedRowModel: () => RowModel<TData>\n  /**\n   * Returns whether or not all rows on the current page are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getisallpagerowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsAllPageRowsSelected: () => boolean\n  /**\n   * Returns whether or not all rows in the table are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getisallrowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsAllRowsSelected: () => boolean\n  /**\n   * Returns whether or not any rows on the current page are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getissomepagerowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsSomePageRowsSelected: () => boolean\n  /**\n   * Returns whether or not any rows in the table are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getissomerowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsSomeRowsSelected: () => boolean\n  /**\n   * Returns the core row model of all rows before row selection has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getpreselectedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getPreSelectedRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model of all rows that are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getselectedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getSelectedRowModel: () => RowModel<TData>\n  /**\n   * Returns a handler that can be used to toggle all rows on the current page.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#gettoggleallpagerowsselectedhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getToggleAllPageRowsSelectedHandler: () => (event: unknown) => void\n  /**\n   * Returns a handler that can be used to toggle all rows in the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#gettoggleallrowsselectedhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getToggleAllRowsSelectedHandler: () => (event: unknown) => void\n  /**\n   * Resets the **rowSelection** state to the `initialState.rowSelection`, or `true` can be passed to force a default blank state reset to `{}`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#resetrowselection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  resetRowSelection: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.rowSelection` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#setrowselection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  setRowSelection: (updater: Updater<RowSelectionState>) => void\n  /**\n   * Selects/deselects all rows on the current page.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#toggleallpagerowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  toggleAllPageRowsSelected: (value?: boolean) => void\n  /**\n   * Selects/deselects all rows in the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#toggleallrowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  toggleAllRowsSelected: (value?: boolean) => void\n}\n\n//\n\nexport const RowSelection: TableFeature = {\n  getInitialState: (state): RowSelectionTableState => {\n    return {\n      rowSelection: {},\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): RowSelectionOptions<TData> => {\n    return {\n      onRowSelectionChange: makeStateUpdater('rowSelection', table),\n      enableRowSelection: true,\n      enableMultiRowSelection: true,\n      enableSubRowSelection: true,\n      // enableGroupingRowSelection: false,\n      // isAdditiveSelectEvent: (e: unknown) => !!e.metaKey,\n      // isInclusiveSelectEvent: (e: unknown) => !!e.shiftKey,\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setRowSelection = updater =>\n      table.options.onRowSelectionChange?.(updater)\n    table.resetRowSelection = defaultState =>\n      table.setRowSelection(\n        defaultState ? {} : table.initialState.rowSelection ?? {}\n      )\n    table.toggleAllRowsSelected = value => {\n      table.setRowSelection(old => {\n        value =\n          typeof value !== 'undefined' ? value : !table.getIsAllRowsSelected()\n\n        const rowSelection = { ...old }\n\n        const preGroupedFlatRows = table.getPreGroupedRowModel().flatRows\n\n        // We don't use `mutateRowIsSelected` here for performance reasons.\n        // All of the rows are flat already, so it wouldn't be worth it\n        if (value) {\n          preGroupedFlatRows.forEach(row => {\n            if (!row.getCanSelect()) {\n              return\n            }\n            rowSelection[row.id] = true\n          })\n        } else {\n          preGroupedFlatRows.forEach(row => {\n            delete rowSelection[row.id]\n          })\n        }\n\n        return rowSelection\n      })\n    }\n    table.toggleAllPageRowsSelected = value =>\n      table.setRowSelection(old => {\n        const resolvedValue =\n          typeof value !== 'undefined'\n            ? value\n            : !table.getIsAllPageRowsSelected()\n\n        const rowSelection: RowSelectionState = { ...old }\n\n        table.getRowModel().rows.forEach(row => {\n          mutateRowIsSelected(rowSelection, row.id, resolvedValue, true, table)\n        })\n\n        return rowSelection\n      })\n\n    // addRowSelectionRange: rowId => {\n    //   const {\n    //     rows,\n    //     rowsById,\n    //     options: { selectGroupingRows, selectSubRows },\n    //   } = table\n\n    //   const findSelectedRow = (rows: Row[]) => {\n    //     let found\n    //     rows.find(d => {\n    //       if (d.getIsSelected()) {\n    //         found = d\n    //         return true\n    //       }\n    //       const subFound = findSelectedRow(d.subRows || [])\n    //       if (subFound) {\n    //         found = subFound\n    //         return true\n    //       }\n    //       return false\n    //     })\n    //     return found\n    //   }\n\n    //   const firstRow = findSelectedRow(rows) || rows[0]\n    //   const lastRow = rowsById[rowId]\n\n    //   let include = false\n    //   const selectedRowIds = {}\n\n    //   const addRow = (row: Row) => {\n    //     mutateRowIsSelected(selectedRowIds, row.id, true, {\n    //       rowsById,\n    //       selectGroupingRows: selectGroupingRows!,\n    //       selectSubRows: selectSubRows!,\n    //     })\n    //   }\n\n    //   table.rows.forEach(row => {\n    //     const isFirstRow = row.id === firstRow.id\n    //     const isLastRow = row.id === lastRow.id\n\n    //     if (isFirstRow || isLastRow) {\n    //       if (!include) {\n    //         include = true\n    //       } else if (include) {\n    //         addRow(row)\n    //         include = false\n    //       }\n    //     }\n\n    //     if (include) {\n    //       addRow(row)\n    //     }\n    //   })\n\n    //   table.setRowSelection(selectedRowIds)\n    // },\n    table.getPreSelectedRowModel = () => table.getCoreRowModel()\n    table.getSelectedRowModel = memo(\n      () => [table.getState().rowSelection, table.getCoreRowModel()],\n      (rowSelection, rowModel) => {\n        if (!Object.keys(rowSelection).length) {\n          return {\n            rows: [],\n            flatRows: [],\n            rowsById: {},\n          }\n        }\n\n        return selectRowsFn(table, rowModel)\n      },\n      getMemoOptions(table.options, 'debugTable', 'getSelectedRowModel')\n    )\n\n    table.getFilteredSelectedRowModel = memo(\n      () => [table.getState().rowSelection, table.getFilteredRowModel()],\n      (rowSelection, rowModel) => {\n        if (!Object.keys(rowSelection).length) {\n          return {\n            rows: [],\n            flatRows: [],\n            rowsById: {},\n          }\n        }\n\n        return selectRowsFn(table, rowModel)\n      },\n      getMemoOptions(table.options, 'debugTable', 'getFilteredSelectedRowModel')\n    )\n\n    table.getGroupedSelectedRowModel = memo(\n      () => [table.getState().rowSelection, table.getSortedRowModel()],\n      (rowSelection, rowModel) => {\n        if (!Object.keys(rowSelection).length) {\n          return {\n            rows: [],\n            flatRows: [],\n            rowsById: {},\n          }\n        }\n\n        return selectRowsFn(table, rowModel)\n      },\n      getMemoOptions(table.options, 'debugTable', 'getGroupedSelectedRowModel')\n    )\n\n    ///\n\n    // getGroupingRowCanSelect: rowId => {\n    //   const row = table.getRow(rowId)\n\n    //   if (!row) {\n    //     throw new Error()\n    //   }\n\n    //   if (typeof table.options.enableGroupingRowSelection === 'function') {\n    //     return table.options.enableGroupingRowSelection(row)\n    //   }\n\n    //   return table.options.enableGroupingRowSelection ?? false\n    // },\n\n    table.getIsAllRowsSelected = () => {\n      const preGroupedFlatRows = table.getFilteredRowModel().flatRows\n      const { rowSelection } = table.getState()\n\n      let isAllRowsSelected = Boolean(\n        preGroupedFlatRows.length && Object.keys(rowSelection).length\n      )\n\n      if (isAllRowsSelected) {\n        if (\n          preGroupedFlatRows.some(\n            row => row.getCanSelect() && !rowSelection[row.id]\n          )\n        ) {\n          isAllRowsSelected = false\n        }\n      }\n\n      return isAllRowsSelected\n    }\n\n    table.getIsAllPageRowsSelected = () => {\n      const paginationFlatRows = table\n        .getPaginationRowModel()\n        .flatRows.filter(row => row.getCanSelect())\n      const { rowSelection } = table.getState()\n\n      let isAllPageRowsSelected = !!paginationFlatRows.length\n\n      if (\n        isAllPageRowsSelected &&\n        paginationFlatRows.some(row => !rowSelection[row.id])\n      ) {\n        isAllPageRowsSelected = false\n      }\n\n      return isAllPageRowsSelected\n    }\n\n    table.getIsSomeRowsSelected = () => {\n      const totalSelected = Object.keys(\n        table.getState().rowSelection ?? {}\n      ).length\n      return (\n        totalSelected > 0 &&\n        totalSelected < table.getFilteredRowModel().flatRows.length\n      )\n    }\n\n    table.getIsSomePageRowsSelected = () => {\n      const paginationFlatRows = table.getPaginationRowModel().flatRows\n      return table.getIsAllPageRowsSelected()\n        ? false\n        : paginationFlatRows\n            .filter(row => row.getCanSelect())\n            .some(d => d.getIsSelected() || d.getIsSomeSelected())\n    }\n\n    table.getToggleAllRowsSelectedHandler = () => {\n      return (e: unknown) => {\n        table.toggleAllRowsSelected(\n          ((e as MouseEvent).target as HTMLInputElement).checked\n        )\n      }\n    }\n\n    table.getToggleAllPageRowsSelectedHandler = () => {\n      return (e: unknown) => {\n        table.toggleAllPageRowsSelected(\n          ((e as MouseEvent).target as HTMLInputElement).checked\n        )\n      }\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row.toggleSelected = (value, opts) => {\n      const isSelected = row.getIsSelected()\n\n      table.setRowSelection(old => {\n        value = typeof value !== 'undefined' ? value : !isSelected\n\n        if (row.getCanSelect() && isSelected === value) {\n          return old\n        }\n\n        const selectedRowIds = { ...old }\n\n        mutateRowIsSelected(\n          selectedRowIds,\n          row.id,\n          value,\n          opts?.selectChildren ?? true,\n          table\n        )\n\n        return selectedRowIds\n      })\n    }\n    row.getIsSelected = () => {\n      const { rowSelection } = table.getState()\n      return isRowSelected(row, rowSelection)\n    }\n\n    row.getIsSomeSelected = () => {\n      const { rowSelection } = table.getState()\n      return isSubRowSelected(row, rowSelection, table) === 'some'\n    }\n\n    row.getIsAllSubRowsSelected = () => {\n      const { rowSelection } = table.getState()\n      return isSubRowSelected(row, rowSelection, table) === 'all'\n    }\n\n    row.getCanSelect = () => {\n      if (typeof table.options.enableRowSelection === 'function') {\n        return table.options.enableRowSelection(row)\n      }\n\n      return table.options.enableRowSelection ?? true\n    }\n\n    row.getCanSelectSubRows = () => {\n      if (typeof table.options.enableSubRowSelection === 'function') {\n        return table.options.enableSubRowSelection(row)\n      }\n\n      return table.options.enableSubRowSelection ?? true\n    }\n\n    row.getCanMultiSelect = () => {\n      if (typeof table.options.enableMultiRowSelection === 'function') {\n        return table.options.enableMultiRowSelection(row)\n      }\n\n      return table.options.enableMultiRowSelection ?? true\n    }\n    row.getToggleSelectedHandler = () => {\n      const canSelect = row.getCanSelect()\n\n      return (e: unknown) => {\n        if (!canSelect) return\n        row.toggleSelected(\n          ((e as MouseEvent).target as HTMLInputElement)?.checked\n        )\n      }\n    }\n  },\n}\n\nconst mutateRowIsSelected = <TData extends RowData>(\n  selectedRowIds: Record<string, boolean>,\n  id: string,\n  value: boolean,\n  includeChildren: boolean,\n  table: Table<TData>\n) => {\n  const row = table.getRow(id, true)\n\n  // const isGrouped = row.getIsGrouped()\n\n  // if ( // TODO: enforce grouping row selection rules\n  //   !isGrouped ||\n  //   (isGrouped && table.options.enableGroupingRowSelection)\n  // ) {\n  if (value) {\n    if (!row.getCanMultiSelect()) {\n      Object.keys(selectedRowIds).forEach(key => delete selectedRowIds[key])\n    }\n    if (row.getCanSelect()) {\n      selectedRowIds[id] = true\n    }\n  } else {\n    delete selectedRowIds[id]\n  }\n  // }\n\n  if (includeChildren && row.subRows?.length && row.getCanSelectSubRows()) {\n    row.subRows.forEach(row =>\n      mutateRowIsSelected(selectedRowIds, row.id, value, includeChildren, table)\n    )\n  }\n}\n\nexport function selectRowsFn<TData extends RowData>(\n  table: Table<TData>,\n  rowModel: RowModel<TData>\n): RowModel<TData> {\n  const rowSelection = table.getState().rowSelection\n\n  const newSelectedFlatRows: Row<TData>[] = []\n  const newSelectedRowsById: Record<string, Row<TData>> = {}\n\n  // Filters top level and nested rows\n  const recurseRows = (rows: Row<TData>[], depth = 0): Row<TData>[] => {\n    return rows\n      .map(row => {\n        const isSelected = isRowSelected(row, rowSelection)\n\n        if (isSelected) {\n          newSelectedFlatRows.push(row)\n          newSelectedRowsById[row.id] = row\n        }\n\n        if (row.subRows?.length) {\n          row = {\n            ...row,\n            subRows: recurseRows(row.subRows, depth + 1),\n          }\n        }\n\n        if (isSelected) {\n          return row\n        }\n      })\n      .filter(Boolean) as Row<TData>[]\n  }\n\n  return {\n    rows: recurseRows(rowModel.rows),\n    flatRows: newSelectedFlatRows,\n    rowsById: newSelectedRowsById,\n  }\n}\n\nexport function isRowSelected<TData extends RowData>(\n  row: Row<TData>,\n  selection: Record<string, boolean>\n): boolean {\n  return selection[row.id] ?? false\n}\n\nexport function isSubRowSelected<TData extends RowData>(\n  row: Row<TData>,\n  selection: Record<string, boolean>,\n  table: Table<TData>\n): boolean | 'some' | 'all' {\n  if (!row.subRows?.length) return false\n\n  let allChildrenSelected = true\n  let someSelected = false\n\n  row.subRows.forEach(subRow => {\n    // Bail out early if we know both of these\n    if (someSelected && !allChildrenSelected) {\n      return\n    }\n\n    if (subRow.getCanSelect()) {\n      if (isRowSelected(subRow, selection)) {\n        someSelected = true\n      } else {\n        allChildrenSelected = false\n      }\n    }\n\n    // Check row selection of nested subrows\n    if (subRow.subRows && subRow.subRows.length) {\n      const subRowChildrenSelected = isSubRowSelected(subRow, selection, table)\n      if (subRowChildrenSelected === 'all') {\n        someSelected = true\n      } else if (subRowChildrenSelected === 'some') {\n        someSelected = true\n        allChildrenSelected = false\n      } else {\n        allChildrenSelected = false\n      }\n    }\n  })\n\n  return allChildrenSelected ? 'all' : someSelected ? 'some' : false\n}\n", "import { SortingFn } from './features/RowSorting'\n\nexport const reSplitAlphaNumeric = /([0-9]+)/gm\n\nconst alphanumeric: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareAlphanumeric(\n    toString(rowA.getValue(columnId)).toLowerCase(),\n    toString(rowB.getValue(columnId)).toLowerCase()\n  )\n}\n\nconst alphanumericCaseSensitive: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareAlphanumeric(\n    toString(rowA.getValue(columnId)),\n    toString(rowB.getValue(columnId))\n  )\n}\n\n// The text filter is more basic (less numeric support)\n// but is much faster\nconst text: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareBasic(\n    toString(rowA.getValue(columnId)).toLowerCase(),\n    toString(rowB.getValue(columnId)).toLowerCase()\n  )\n}\n\n// The text filter is more basic (less numeric support)\n// but is much faster\nconst textCaseSensitive: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareBasic(\n    toString(rowA.getValue(columnId)),\n    toString(rowB.getValue(columnId))\n  )\n}\n\nconst datetime: SortingFn<any> = (rowA, rowB, columnId) => {\n  const a = rowA.getValue<Date>(columnId)\n  const b = rowB.getValue<Date>(columnId)\n\n  // Can handle nullish values\n  // Use > and < because == (and ===) doesn't work with\n  // Date objects (would require calling getTime()).\n  return a > b ? 1 : a < b ? -1 : 0\n}\n\nconst basic: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareBasic(rowA.getValue(columnId), rowB.getValue(columnId))\n}\n\n// Utils\n\nfunction compareBasic(a: any, b: any) {\n  return a === b ? 0 : a > b ? 1 : -1\n}\n\nfunction toString(a: any) {\n  if (typeof a === 'number') {\n    if (isNaN(a) || a === Infinity || a === -Infinity) {\n      return ''\n    }\n    return String(a)\n  }\n  if (typeof a === 'string') {\n    return a\n  }\n  return ''\n}\n\n// Mixed sorting is slow, but very inclusive of many edge cases.\n// It handles numbers, mixed alphanumeric combinations, and even\n// null, undefined, and Infinity\nfunction compareAlphanumeric(aStr: string, bStr: string) {\n  // Split on number groups, but keep the delimiter\n  // Then remove falsey split values\n  const a = aStr.split(reSplitAlphaNumeric).filter(Boolean)\n  const b = bStr.split(reSplitAlphaNumeric).filter(Boolean)\n\n  // While\n  while (a.length && b.length) {\n    const aa = a.shift()!\n    const bb = b.shift()!\n\n    const an = parseInt(aa, 10)\n    const bn = parseInt(bb, 10)\n\n    const combo = [an, bn].sort()\n\n    // Both are string\n    if (isNaN(combo[0]!)) {\n      if (aa > bb) {\n        return 1\n      }\n      if (bb > aa) {\n        return -1\n      }\n      continue\n    }\n\n    // One is a string, one is a number\n    if (isNaN(combo[1]!)) {\n      return isNaN(an) ? -1 : 1\n    }\n\n    // Both are numbers\n    if (an > bn) {\n      return 1\n    }\n    if (bn > an) {\n      return -1\n    }\n  }\n\n  return a.length - b.length\n}\n\n// Exports\n\nexport const sortingFns = {\n  alphanumeric,\n  alphanumericCaseSensitive,\n  text,\n  textCaseSensitive,\n  datetime,\n  basic,\n}\n\nexport type BuiltInSortingFn = keyof typeof sortingFns\n", "import { RowModel } from '..'\nimport {\n  BuiltInSortingFn,\n  reSplitAlphaNumeric,\n  sortingFns,\n} from '../sortingFns'\n\nimport {\n  Column,\n  OnChangeFn,\n  Table,\n  Row,\n  Updater,\n  RowData,\n  SortingFns,\n  TableFeature,\n} from '../types'\n\nimport { isFunction, makeStateUpdater } from '../utils'\n\nexport type SortDirection = 'asc' | 'desc'\n\nexport interface ColumnSort {\n  desc: boolean\n  id: string\n}\n\nexport type SortingState = ColumnSort[]\n\nexport interface SortingTableState {\n  sorting: SortingState\n}\n\nexport interface SortingFn<TData extends RowData> {\n  (rowA: Row<TData>, rowB: Row<TData>, columnId: string): number\n}\n\nexport type CustomSortingFns<TData extends RowData> = Record<\n  string,\n  SortingFn<TData>\n>\n\nexport type SortingFnOption<TData extends RowData> =\n  | 'auto'\n  | keyof SortingFns\n  | BuiltInSortingFn\n  | SortingFn<TData>\n\nexport interface SortingColumnDef<TData extends RowData> {\n  /**\n   * Enables/Disables multi-sorting for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablemultisort)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableMultiSort?: boolean\n  /**\n   * Enables/Disables sorting for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablesorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableSorting?: boolean\n  /**\n   * Inverts the order of the sorting for this column. This is useful for values that have an inverted best/worst scale where lower numbers are better, eg. a ranking (1st, 2nd, 3rd) or golf-like scoring\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#invertsorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  invertSorting?: boolean\n  /**\n   * Set to `true` for sorting toggles on this column to start in the descending direction.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#sortdescfirst)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  sortDescFirst?: boolean\n  /**\n   * The sorting function to use with this column.\n   * - A `string` referencing a built-in sorting function\n   * - A custom sorting function\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#sortingfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  sortingFn?: SortingFnOption<TData>\n  /**\n   * The priority of undefined values when sorting this column.\n   * - `false`\n   *   - Undefined values will be considered tied and need to be sorted by the next column filter or original index (whichever applies)\n   * - `-1`\n   *   - Undefined values will be sorted with higher priority (ascending) (if ascending, undefined will appear on the beginning of the list)\n   * - `1`\n   *   - Undefined values will be sorted with lower priority (descending) (if ascending, undefined will appear on the end of the list)\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#sortundefined)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  sortUndefined?: false | -1 | 1 | 'first' | 'last'\n}\n\nexport interface SortingColumn<TData extends RowData> {\n  /**\n   * Removes this column from the table's sorting state\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#clearsorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  clearSorting: () => void\n  /**\n   * Returns a sort direction automatically inferred based on the columns values.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getautosortdir)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getAutoSortDir: () => SortDirection\n  /**\n   * Returns a sorting function automatically inferred based on the columns values.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getautosortingfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getAutoSortingFn: () => SortingFn<TData>\n  /**\n   * Returns whether this column can be multi-sorted.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getcanmultisort)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getCanMultiSort: () => boolean\n  /**\n   * Returns whether this column can be sorted.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getcansort)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getCanSort: () => boolean\n  /**\n   * Returns the first direction that should be used when sorting this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getfirstsortdir)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getFirstSortDir: () => SortDirection\n  /**\n   * Returns the current sort direction of this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getissorted)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getIsSorted: () => false | SortDirection\n  /**\n   * Returns the next sorting order.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getnextsortingorder)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getNextSortingOrder: () => SortDirection | false\n  /**\n   * Returns the index position of this column's sorting within the sorting state\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getsortindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getSortIndex: () => number\n  /**\n   * Returns the resolved sorting function to be used for this column\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getsortingfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getSortingFn: () => SortingFn<TData>\n  /**\n   * Returns a function that can be used to toggle this column's sorting state. This is useful for attaching a click handler to the column header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#gettogglesortinghandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getToggleSortingHandler: () => undefined | ((event: unknown) => void)\n  /**\n   * Toggles this columns sorting state. If `desc` is provided, it will force the sort direction to that value. If `isMulti` is provided, it will additivity multi-sort the column (or toggle it if it is already sorted).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#togglesorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  toggleSorting: (desc?: boolean, isMulti?: boolean) => void\n}\n\ninterface SortingOptionsBase {\n  /**\n   * Enables/disables the ability to remove multi-sorts\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablemultiremove)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableMultiRemove?: boolean\n  /**\n   * Enables/Disables multi-sorting for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablemultisort)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableMultiSort?: boolean\n  /**\n   * Enables/Disables sorting for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablesorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableSorting?: boolean\n  /**\n   * Enables/Disables the ability to remove sorting for the table.\n   * - If `true` then changing sort order will circle like: 'none' -> 'desc' -> 'asc' -> 'none' -> ...\n   * - If `false` then changing sort order will circle like: 'none' -> 'desc' -> 'asc' -> 'desc' -> 'asc' -> ...\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablesortingremoval)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableSortingRemoval?: boolean\n  /**\n   * This function is used to retrieve the sorted row model. If using server-side sorting, this function is not required. To use client-side sorting, pass the exported `getSortedRowModel()` from your adapter to your table or implement your own.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getsortedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getSortedRowModel?: (table: Table<any>) => () => RowModel<any>\n  /**\n   * Pass a custom function that will be used to determine if a multi-sort event should be triggered. It is passed the event from the sort toggle handler and should return `true` if the event should trigger a multi-sort.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#ismultisortevent)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  isMultiSortEvent?: (e: unknown) => boolean\n  /**\n   * Enables manual sorting for the table. If this is `true`, you will be expected to sort your data before it is passed to the table. This is useful if you are doing server-side sorting.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#manualsorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  manualSorting?: boolean\n  /**\n   * Set a maximum number of columns that can be multi-sorted.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#maxmultisortcolcount)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  maxMultiSortColCount?: number\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.sorting` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#onsortingchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  onSortingChange?: OnChangeFn<SortingState>\n  /**\n   * If `true`, all sorts will default to descending as their first toggle state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#sortdescfirst)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  sortDescFirst?: boolean\n}\n\ntype ResolvedSortingFns = keyof SortingFns extends never\n  ? {\n      sortingFns?: Record<string, SortingFn<any>>\n    }\n  : {\n      sortingFns: Record<keyof SortingFns, SortingFn<any>>\n    }\n\nexport interface SortingOptions<TData extends RowData>\n  extends SortingOptionsBase,\n    ResolvedSortingFns {}\n\nexport interface SortingInstance<TData extends RowData> {\n  _getSortedRowModel?: () => RowModel<TData>\n  /**\n   * Returns the row model for the table before any sorting has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getpresortedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getPreSortedRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model for the table after sorting has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getsortedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getSortedRowModel: () => RowModel<TData>\n  /**\n   * Resets the **sorting** state to `initialState.sorting`, or `true` can be passed to force a default blank state reset to `[]`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#resetsorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  resetSorting: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.sorting` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#setsorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  setSorting: (updater: Updater<SortingState>) => void\n}\n\n//\n\nexport const RowSorting: TableFeature = {\n  getInitialState: (state): SortingTableState => {\n    return {\n      sorting: [],\n      ...state,\n    }\n  },\n\n  getDefaultColumnDef: <TData extends RowData>(): SortingColumnDef<TData> => {\n    return {\n      sortingFn: 'auto',\n      sortUndefined: 1,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): SortingOptions<TData> => {\n    return {\n      onSortingChange: makeStateUpdater('sorting', table),\n      isMultiSortEvent: (e: unknown) => {\n        return (e as MouseEvent).shiftKey\n      },\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    column.getAutoSortingFn = () => {\n      const firstRows = table.getFilteredRowModel().flatRows.slice(10)\n\n      let isString = false\n\n      for (const row of firstRows) {\n        const value = row?.getValue(column.id)\n\n        if (Object.prototype.toString.call(value) === '[object Date]') {\n          return sortingFns.datetime\n        }\n\n        if (typeof value === 'string') {\n          isString = true\n\n          if (value.split(reSplitAlphaNumeric).length > 1) {\n            return sortingFns.alphanumeric\n          }\n        }\n      }\n\n      if (isString) {\n        return sortingFns.text\n      }\n\n      return sortingFns.basic\n    }\n    column.getAutoSortDir = () => {\n      const firstRow = table.getFilteredRowModel().flatRows[0]\n\n      const value = firstRow?.getValue(column.id)\n\n      if (typeof value === 'string') {\n        return 'asc'\n      }\n\n      return 'desc'\n    }\n    column.getSortingFn = () => {\n      if (!column) {\n        throw new Error()\n      }\n\n      return isFunction(column.columnDef.sortingFn)\n        ? column.columnDef.sortingFn\n        : column.columnDef.sortingFn === 'auto'\n          ? column.getAutoSortingFn()\n          : table.options.sortingFns?.[column.columnDef.sortingFn as string] ??\n            sortingFns[column.columnDef.sortingFn as BuiltInSortingFn]\n    }\n    column.toggleSorting = (desc, multi) => {\n      // if (column.columns.length) {\n      //   column.columns.forEach((c, i) => {\n      //     if (c.id) {\n      //       table.toggleColumnSorting(c.id, undefined, multi || !!i)\n      //     }\n      //   })\n      //   return\n      // }\n\n      // this needs to be outside of table.setSorting to be in sync with rerender\n      const nextSortingOrder = column.getNextSortingOrder()\n      const hasManualValue = typeof desc !== 'undefined' && desc !== null\n\n      table.setSorting(old => {\n        // Find any existing sorting for this column\n        const existingSorting = old?.find(d => d.id === column.id)\n        const existingIndex = old?.findIndex(d => d.id === column.id)\n\n        let newSorting: SortingState = []\n\n        // What should we do with this sort action?\n        let sortAction: 'add' | 'remove' | 'toggle' | 'replace'\n        let nextDesc = hasManualValue ? desc : nextSortingOrder === 'desc'\n\n        // Multi-mode\n        if (old?.length && column.getCanMultiSort() && multi) {\n          if (existingSorting) {\n            sortAction = 'toggle'\n          } else {\n            sortAction = 'add'\n          }\n        } else {\n          // Normal mode\n          if (old?.length && existingIndex !== old.length - 1) {\n            sortAction = 'replace'\n          } else if (existingSorting) {\n            sortAction = 'toggle'\n          } else {\n            sortAction = 'replace'\n          }\n        }\n\n        // Handle toggle states that will remove the sorting\n        if (sortAction === 'toggle') {\n          // If we are \"actually\" toggling (not a manual set value), should we remove the sorting?\n          if (!hasManualValue) {\n            // Is our intention to remove?\n            if (!nextSortingOrder) {\n              sortAction = 'remove'\n            }\n          }\n        }\n\n        if (sortAction === 'add') {\n          newSorting = [\n            ...old,\n            {\n              id: column.id,\n              desc: nextDesc,\n            },\n          ]\n          // Take latest n columns\n          newSorting.splice(\n            0,\n            newSorting.length -\n              (table.options.maxMultiSortColCount ?? Number.MAX_SAFE_INTEGER)\n          )\n        } else if (sortAction === 'toggle') {\n          // This flips (or sets) the\n          newSorting = old.map(d => {\n            if (d.id === column.id) {\n              return {\n                ...d,\n                desc: nextDesc,\n              }\n            }\n            return d\n          })\n        } else if (sortAction === 'remove') {\n          newSorting = old.filter(d => d.id !== column.id)\n        } else {\n          newSorting = [\n            {\n              id: column.id,\n              desc: nextDesc,\n            },\n          ]\n        }\n\n        return newSorting\n      })\n    }\n\n    column.getFirstSortDir = () => {\n      const sortDescFirst =\n        column.columnDef.sortDescFirst ??\n        table.options.sortDescFirst ??\n        column.getAutoSortDir() === 'desc'\n      return sortDescFirst ? 'desc' : 'asc'\n    }\n\n    column.getNextSortingOrder = (multi?: boolean) => {\n      const firstSortDirection = column.getFirstSortDir()\n      const isSorted = column.getIsSorted()\n\n      if (!isSorted) {\n        return firstSortDirection\n      }\n\n      if (\n        isSorted !== firstSortDirection &&\n        (table.options.enableSortingRemoval ?? true) && // If enableSortRemove, enable in general\n        (multi ? table.options.enableMultiRemove ?? true : true) // If multi, don't allow if enableMultiRemove))\n      ) {\n        return false\n      }\n      return isSorted === 'desc' ? 'asc' : 'desc'\n    }\n\n    column.getCanSort = () => {\n      return (\n        (column.columnDef.enableSorting ?? true) &&\n        (table.options.enableSorting ?? true) &&\n        !!column.accessorFn\n      )\n    }\n\n    column.getCanMultiSort = () => {\n      return (\n        column.columnDef.enableMultiSort ??\n        table.options.enableMultiSort ??\n        !!column.accessorFn\n      )\n    }\n\n    column.getIsSorted = () => {\n      const columnSort = table.getState().sorting?.find(d => d.id === column.id)\n\n      return !columnSort ? false : columnSort.desc ? 'desc' : 'asc'\n    }\n\n    column.getSortIndex = () =>\n      table.getState().sorting?.findIndex(d => d.id === column.id) ?? -1\n\n    column.clearSorting = () => {\n      //clear sorting for just 1 column\n      table.setSorting(old =>\n        old?.length ? old.filter(d => d.id !== column.id) : []\n      )\n    }\n\n    column.getToggleSortingHandler = () => {\n      const canSort = column.getCanSort()\n\n      return (e: unknown) => {\n        if (!canSort) return\n        ;(e as any).persist?.()\n        column.toggleSorting?.(\n          undefined,\n          column.getCanMultiSort() ? table.options.isMultiSortEvent?.(e) : false\n        )\n      }\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setSorting = updater => table.options.onSortingChange?.(updater)\n    table.resetSorting = defaultState => {\n      table.setSorting(defaultState ? [] : table.initialState?.sorting ?? [])\n    }\n    table.getPreSortedRowModel = () => table.getGroupedRowModel()\n    table.getSortedRowModel = () => {\n      if (!table._getSortedRowModel && table.options.getSortedRowModel) {\n        table._getSortedRowModel = table.options.getSortedRowModel(table)\n      }\n\n      if (table.options.manualSorting || !table._getSortedRowModel) {\n        return table.getPreSortedRowModel()\n      }\n\n      return table._getSortedRowModel()\n    }\n  },\n}\n", "import { functionalUpdate, getMemoOptions, memo, RequiredKeys } from '../utils'\n\nimport {\n  Updater,\n  TableOptionsResolved,\n  TableState,\n  Table,\n  InitialTableState,\n  Row,\n  Column,\n  RowModel,\n  ColumnDef,\n  TableOptions,\n  RowData,\n  TableMeta,\n  ColumnDefResolved,\n  GroupColumnDef,\n  TableFeature,\n} from '../types'\n\n//\nimport { createColumn } from './column'\nimport { Headers } from './headers'\n//\n\nimport { ColumnFaceting } from '../features/ColumnFaceting'\nimport { ColumnFiltering } from '../features/ColumnFiltering'\nimport { ColumnGrouping } from '../features/ColumnGrouping'\nimport { ColumnOrdering } from '../features/ColumnOrdering'\nimport { ColumnPinning } from '../features/ColumnPinning'\nimport { ColumnSizing } from '../features/ColumnSizing'\nimport { ColumnVisibility } from '../features/ColumnVisibility'\nimport { GlobalFaceting } from '../features/GlobalFaceting'\nimport { GlobalFiltering } from '../features/GlobalFiltering'\nimport { RowExpanding } from '../features/RowExpanding'\nimport { RowPagination } from '../features/RowPagination'\nimport { RowPinning } from '../features/RowPinning'\nimport { RowSelection } from '../features/RowSelection'\nimport { RowSorting } from '../features/RowSorting'\n\nconst builtInFeatures = [\n  Headers,\n  ColumnVisibility,\n  ColumnOrdering,\n  ColumnPinning,\n  ColumnFaceting,\n  ColumnFiltering,\n  GlobalFaceting, //depends on ColumnFaceting\n  GlobalFiltering, //depends on ColumnFiltering\n  RowSorting,\n  ColumnGrouping, //depends on RowSorting\n  RowExpanding,\n  RowPagination,\n  RowPinning,\n  RowSelection,\n  ColumnSizing,\n] as const\n\n//\n\nexport interface CoreTableState {}\n\nexport interface CoreOptions<TData extends RowData> {\n  /**\n   * An array of extra features that you can add to the table instance.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#_features)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  _features?: TableFeature[]\n  /**\n   * Set this option to override any of the `autoReset...` feature options.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#autoresetall)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  autoResetAll?: boolean\n  /**\n   * The array of column defs to use for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#columns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  columns: ColumnDef<TData, any>[]\n  /**\n   * The data for the table to display. This array should match the type you provided to `table.setRowType<...>`. Columns can access this data via string/index or a functional accessor. When the `data` option changes reference, the table will reprocess the data.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#data)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  data: TData[]\n  /**\n   * Set this option to `true` to output all debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugall)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugAll?: boolean\n  /**\n   * Set this option to `true` to output cell debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugcells]\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugCells?: boolean\n  /**\n   * Set this option to `true` to output column debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugColumns?: boolean\n  /**\n   * Set this option to `true` to output header debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugHeaders?: boolean\n  /**\n   * Set this option to `true` to output row debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugRows?: boolean\n  /**\n   * Set this option to `true` to output table debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugtable)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugTable?: boolean\n  /**\n   * Default column options to use for all column defs supplied to the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#defaultcolumn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  defaultColumn?: Partial<ColumnDef<TData, unknown>>\n  /**\n   * This required option is a factory for a function that computes and returns the core row model for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getcorerowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getCoreRowModel: (table: Table<any>) => () => RowModel<any>\n  /**\n   * This optional function is used to derive a unique ID for any given row. If not provided the rows index is used (nested rows join together with `.` using their grandparents' index eg. `index.index.index`). If you need to identify individual rows that are originating from any server-side operations, it's suggested you use this function to return an ID that makes sense regardless of network IO/ambiguity eg. a userId, taskId, database ID field, etc.\n   * @example getRowId: row => row.userId\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getrowid)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getRowId?: (originalRow: TData, index: number, parent?: Row<TData>) => string\n  /**\n   * This optional function is used to access the sub rows for any given row. If you are using nested rows, you will need to use this function to return the sub rows object (or undefined) from the row.\n   * @example getSubRows: row => row.subRows\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getsubrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getSubRows?: (originalRow: TData, index: number) => undefined | TData[]\n  /**\n   * Use this option to optionally pass initial state to the table. This state will be used when resetting various table states either automatically by the table (eg. `options.autoResetPageIndex`) or via functions like `table.resetRowSelection()`. Most reset function allow you optionally pass a flag to reset to a blank/default state instead of the initial state.\n   *\n   * Table state will not be reset when this object changes, which also means that the initial state object does not need to be stable.\n   *\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#initialstate)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  initialState?: InitialTableState\n  /**\n   * This option is used to optionally implement the merging of table options.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#mergeoptions)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  mergeOptions?: (\n    defaultOptions: TableOptions<TData>,\n    options: Partial<TableOptions<TData>>\n  ) => TableOptions<TData>\n  /**\n   * You can pass any object to `options.meta` and access it anywhere the `table` is available via `table.options.meta`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#meta)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  meta?: TableMeta<TData>\n  /**\n   * The `onStateChange` option can be used to optionally listen to state changes within the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#onstatechange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  onStateChange: (updater: Updater<TableState>) => void\n  /**\n   * Value used when the desired value is not found in the data.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#renderfallbackvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  renderFallbackValue: any\n  /**\n   * The `state` option can be used to optionally _control_ part or all of the table state. The state you pass here will merge with and overwrite the internal automatically-managed state to produce the final state for the table. You can also listen to state changes via the `onStateChange` option.\n   * > Note: Any state passed in here will override both the internal state and any other `initialState` you provide.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#state)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  state: Partial<TableState>\n}\n\nexport interface CoreInstance<TData extends RowData> {\n  _features: readonly TableFeature[]\n  _getAllFlatColumnsById: () => Record<string, Column<TData, unknown>>\n  _getColumnDefs: () => ColumnDef<TData, unknown>[]\n  _getCoreRowModel?: () => RowModel<TData>\n  _getDefaultColumnDef: () => Partial<ColumnDef<TData, unknown>>\n  _getRowId: (_: TData, index: number, parent?: Row<TData>) => string\n  _queue: (cb: () => void) => void\n  /**\n   * Returns all columns in the table in their normalized and nested hierarchy.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getallcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getAllColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns all columns in the table flattened to a single level.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getallflatcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getAllFlatColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns all leaf-node columns in the table flattened to a single level. This does not include parent columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getallleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getAllLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns a single column by its ID.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getcolumn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getColumn: (columnId: string) => Column<TData, unknown> | undefined\n  /**\n   * Returns the core row model before any processing has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getcorerowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getCoreRowModel: () => RowModel<TData>\n  /**\n   * Returns the row with the given ID.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getrow)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getRow: (id: string, searchAll?: boolean) => Row<TData>\n  /**\n   * Returns the final model after all processing from other used features has been applied. This is the row model that is most commonly used for rendering.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getRowModel: () => RowModel<TData>\n  /**\n   * Call this function to get the table's current state. It's recommended to use this function and its state, especially when managing the table state manually. It is the exact same state used internally by the table for every feature and function it provides.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getstate)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getState: () => TableState\n  /**\n   * This is the resolved initial state of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#initialstate)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  initialState: TableState\n  /**\n   * A read-only reference to the table's current options.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#options)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  options: RequiredKeys<TableOptionsResolved<TData>, 'state'>\n  /**\n   * Call this function to reset the table state to the initial state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#reset)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  reset: () => void\n  /**\n   * This function can be used to update the table options.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#setoptions)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  setOptions: (newOptions: Updater<TableOptionsResolved<TData>>) => void\n  /**\n   * Call this function to update the table state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#setstate)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  setState: (updater: Updater<TableState>) => void\n}\n\nexport function createTable<TData extends RowData>(\n  options: TableOptionsResolved<TData>\n): Table<TData> {\n  if (\n    process.env.NODE_ENV !== 'production' &&\n    (options.debugAll || options.debugTable)\n  ) {\n    console.info('Creating Table Instance...')\n  }\n\n  const _features = [...builtInFeatures, ...(options._features ?? [])]\n\n  let table = { _features } as unknown as Table<TData>\n\n  const defaultOptions = table._features.reduce((obj, feature) => {\n    return Object.assign(obj, feature.getDefaultOptions?.(table))\n  }, {}) as TableOptionsResolved<TData>\n\n  const mergeOptions = (options: TableOptionsResolved<TData>) => {\n    if (table.options.mergeOptions) {\n      return table.options.mergeOptions(defaultOptions, options)\n    }\n\n    return {\n      ...defaultOptions,\n      ...options,\n    }\n  }\n\n  const coreInitialState: CoreTableState = {}\n\n  let initialState = {\n    ...coreInitialState,\n    ...(options.initialState ?? {}),\n  } as TableState\n\n  table._features.forEach(feature => {\n    initialState = (feature.getInitialState?.(initialState) ??\n      initialState) as TableState\n  })\n\n  const queued: (() => void)[] = []\n  let queuedTimeout = false\n\n  const coreInstance: CoreInstance<TData> = {\n    _features,\n    options: {\n      ...defaultOptions,\n      ...options,\n    },\n    initialState,\n    _queue: cb => {\n      queued.push(cb)\n\n      if (!queuedTimeout) {\n        queuedTimeout = true\n\n        // Schedule a microtask to run the queued callbacks after\n        // the current call stack (render, etc) has finished.\n        Promise.resolve()\n          .then(() => {\n            while (queued.length) {\n              queued.shift()!()\n            }\n            queuedTimeout = false\n          })\n          .catch(error =>\n            setTimeout(() => {\n              throw error\n            })\n          )\n      }\n    },\n    reset: () => {\n      table.setState(table.initialState)\n    },\n    setOptions: updater => {\n      const newOptions = functionalUpdate(updater, table.options)\n      table.options = mergeOptions(newOptions) as RequiredKeys<\n        TableOptionsResolved<TData>,\n        'state'\n      >\n    },\n\n    getState: () => {\n      return table.options.state as TableState\n    },\n\n    setState: (updater: Updater<TableState>) => {\n      table.options.onStateChange?.(updater)\n    },\n\n    _getRowId: (row: TData, index: number, parent?: Row<TData>) =>\n      table.options.getRowId?.(row, index, parent) ??\n      `${parent ? [parent.id, index].join('.') : index}`,\n\n    getCoreRowModel: () => {\n      if (!table._getCoreRowModel) {\n        table._getCoreRowModel = table.options.getCoreRowModel(table)\n      }\n\n      return table._getCoreRowModel!()\n    },\n\n    // The final calls start at the bottom of the model,\n    // expanded rows, which then work their way up\n\n    getRowModel: () => {\n      return table.getPaginationRowModel()\n    },\n    //in next version, we should just pass in the row model as the optional 2nd arg\n    getRow: (id: string, searchAll?: boolean) => {\n      let row = (\n        searchAll ? table.getPrePaginationRowModel() : table.getRowModel()\n      ).rowsById[id]\n\n      if (!row) {\n        row = table.getCoreRowModel().rowsById[id]\n        if (!row) {\n          if (process.env.NODE_ENV !== 'production') {\n            throw new Error(`getRow could not find row with ID: ${id}`)\n          }\n          throw new Error()\n        }\n      }\n\n      return row\n    },\n    _getDefaultColumnDef: memo(\n      () => [table.options.defaultColumn],\n      defaultColumn => {\n        defaultColumn = (defaultColumn ?? {}) as Partial<\n          ColumnDef<TData, unknown>\n        >\n\n        return {\n          header: props => {\n            const resolvedColumnDef = props.header.column\n              .columnDef as ColumnDefResolved<TData>\n\n            if (resolvedColumnDef.accessorKey) {\n              return resolvedColumnDef.accessorKey\n            }\n\n            if (resolvedColumnDef.accessorFn) {\n              return resolvedColumnDef.id\n            }\n\n            return null\n          },\n          // footer: props => props.header.column.id,\n          cell: props => props.renderValue<any>()?.toString?.() ?? null,\n          ...table._features.reduce((obj, feature) => {\n            return Object.assign(obj, feature.getDefaultColumnDef?.())\n          }, {}),\n          ...defaultColumn,\n        } as Partial<ColumnDef<TData, unknown>>\n      },\n      getMemoOptions(options, 'debugColumns', '_getDefaultColumnDef')\n    ),\n\n    _getColumnDefs: () => table.options.columns,\n\n    getAllColumns: memo(\n      () => [table._getColumnDefs()],\n      columnDefs => {\n        const recurseColumns = (\n          columnDefs: ColumnDef<TData, unknown>[],\n          parent?: Column<TData, unknown>,\n          depth = 0\n        ): Column<TData, unknown>[] => {\n          return columnDefs.map(columnDef => {\n            const column = createColumn(table, columnDef, depth, parent)\n\n            const groupingColumnDef = columnDef as GroupColumnDef<\n              TData,\n              unknown\n            >\n\n            column.columns = groupingColumnDef.columns\n              ? recurseColumns(groupingColumnDef.columns, column, depth + 1)\n              : []\n\n            return column\n          })\n        }\n\n        return recurseColumns(columnDefs)\n      },\n      getMemoOptions(options, 'debugColumns', 'getAllColumns')\n    ),\n\n    getAllFlatColumns: memo(\n      () => [table.getAllColumns()],\n      allColumns => {\n        return allColumns.flatMap(column => {\n          return column.getFlatColumns()\n        })\n      },\n      getMemoOptions(options, 'debugColumns', 'getAllFlatColumns')\n    ),\n\n    _getAllFlatColumnsById: memo(\n      () => [table.getAllFlatColumns()],\n      flatColumns => {\n        return flatColumns.reduce(\n          (acc, column) => {\n            acc[column.id] = column\n            return acc\n          },\n          {} as Record<string, Column<TData, unknown>>\n        )\n      },\n      getMemoOptions(options, 'debugColumns', 'getAllFlatColumnsById')\n    ),\n\n    getAllLeafColumns: memo(\n      () => [table.getAllColumns(), table._getOrderColumnsFn()],\n      (allColumns, orderColumns) => {\n        let leafColumns = allColumns.flatMap(column => column.getLeafColumns())\n        return orderColumns(leafColumns)\n      },\n      getMemoOptions(options, 'debugColumns', 'getAllLeafColumns')\n    ),\n\n    getColumn: columnId => {\n      const column = table._getAllFlatColumnsById()[columnId]\n\n      if (process.env.NODE_ENV !== 'production' && !column) {\n        console.error(`[Table] Column with id '${columnId}' does not exist.`)\n      }\n\n      return column\n    },\n  }\n\n  Object.assign(table, coreInstance)\n\n  for (let index = 0; index < table._features.length; index++) {\n    const feature = table._features[index]\n    feature?.createTable?.(table)\n  }\n\n  return table\n}\n", "import { createRow } from '../core/row'\nimport { Table, Row, RowModel, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\n\nexport function getCoreRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [table.options.data],\n      (\n        data\n      ): {\n        rows: Row<TData>[]\n        flatRows: Row<TData>[]\n        rowsById: Record<string, Row<TData>>\n      } => {\n        const rowModel: RowModel<TData> = {\n          rows: [],\n          flatRows: [],\n          rowsById: {},\n        }\n\n        const accessRows = (\n          originalRows: TData[],\n          depth = 0,\n          parentRow?: Row<TData>\n        ): Row<TData>[] => {\n          const rows = [] as Row<TData>[]\n\n          for (let i = 0; i < originalRows.length; i++) {\n            // This could be an expensive check at scale, so we should move it somewhere else, but where?\n            // if (!id) {\n            //   if (process.env.NODE_ENV !== 'production') {\n            //     throw new Error(`getRowId expected an ID, but got ${id}`)\n            //   }\n            // }\n\n            // Make the row\n            const row = createRow(\n              table,\n              table._getRowId(originalRows[i]!, i, parentRow),\n              originalRows[i]!,\n              i,\n              depth,\n              undefined,\n              parentRow?.id\n            )\n\n            // Keep track of every row in a flat array\n            rowModel.flatRows.push(row)\n            // Also keep track of every row by its ID\n            rowModel.rowsById[row.id] = row\n            // Push table row into parent\n            rows.push(row)\n\n            // Get the original subrows\n            if (table.options.getSubRows) {\n              row.originalSubRows = table.options.getSubRows(\n                originalRows[i]!,\n                i\n              )\n\n              // Then recursively access them\n              if (row.originalSubRows?.length) {\n                row.subRows = accessRows(row.originalSubRows, depth + 1, row)\n              }\n            }\n          }\n\n          return rows\n        }\n\n        rowModel.rows = accessRows(data)\n\n        return rowModel\n      },\n      getMemoOptions(table.options, 'debugTable', 'getRowModel', () =>\n        table._autoResetPageIndex()\n      )\n    )\n}\n", "import { Table, Row, RowModel, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\n\nexport function getExpandedRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [\n        table.getState().expanded,\n        table.getPreExpandedRowModel(),\n        table.options.paginateExpandedRows,\n      ],\n      (expanded, rowModel, paginateExpandedRows) => {\n        if (\n          !rowModel.rows.length ||\n          (expanded !== true && !Object.keys(expanded ?? {}).length)\n        ) {\n          return rowModel\n        }\n\n        if (!paginateExpandedRows) {\n          // Only expand rows at this point if they are being paginated\n          return rowModel\n        }\n\n        return expandRows(rowModel)\n      },\n      getMemoOptions(table.options, 'debugTable', 'getExpandedRowModel')\n    )\n}\n\nexport function expandRows<TData extends RowData>(rowModel: RowModel<TData>) {\n  const expandedRows: Row<TData>[] = []\n\n  const handleRow = (row: Row<TData>) => {\n    expandedRows.push(row)\n\n    if (row.subRows?.length && row.getIsExpanded()) {\n      row.subRows.forEach(handleRow)\n    }\n  }\n\n  rowModel.rows.forEach(handleRow)\n\n  return {\n    rows: expandedRows,\n    flatRows: rowModel.flatRows,\n    rowsById: rowModel.rowsById,\n  }\n}\n", "import { Table, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\n\nexport function getFacetedMinMaxValues<TData extends RowData>(): (\n  table: Table<TData>,\n  columnId: string\n) => () => undefined | [number, number] {\n  return (table, columnId) =>\n    memo(\n      () => [table.getColumn(columnId)?.getFacetedRowModel()],\n      facetedRowModel => {\n        if (!facetedRowModel) return undefined\n\n        const uniqueValues = facetedRowModel.flatRows\n          .flatMap(flatRow => flatRow.getUniqueValues(columnId) ?? [])\n          .map(Number)\n          .filter(value => !Number.isNaN(value))\n\n        if (!uniqueValues.length) return\n\n        let facetedMinValue = uniqueValues[0]!\n        let facetedMaxValue = uniqueValues[uniqueValues.length - 1]!\n\n        for (const value of uniqueValues) {\n          if (value < facetedMinValue) facetedMinValue = value\n          else if (value > facetedMaxValue) facetedMaxValue = value\n        }\n\n        return [facetedMinValue, facetedMaxValue]\n      },\n      getMemoOptions(table.options, 'debugTable', 'getFacetedMinMaxValues')\n    )\n}\n", "import { createRow } from '../core/row'\nimport { Row, RowModel, Table, RowData } from '../types'\n\nexport function filterRows<TData extends RowData>(\n  rows: Row<TData>[],\n  filterRowImpl: (row: Row<TData>) => any,\n  table: Table<TData>\n) {\n  if (table.options.filterFromLeafRows) {\n    return filterRowModelFromLeafs(rows, filterRowImpl, table)\n  }\n\n  return filterRowModelFromRoot(rows, filterRowImpl, table)\n}\n\nfunction filterRowModelFromLeafs<TData extends RowData>(\n  rowsToFilter: Row<TData>[],\n  filterRow: (row: Row<TData>) => Row<TData>[],\n  table: Table<TData>\n): RowModel<TData> {\n  const newFilteredFlatRows: Row<TData>[] = []\n  const newFilteredRowsById: Record<string, Row<TData>> = {}\n  const maxDepth = table.options.maxLeafRowFilterDepth ?? 100\n\n  const recurseFilterRows = (rowsToFilter: Row<TData>[], depth = 0) => {\n    const rows: Row<TData>[] = []\n\n    // Filter from children up first\n    for (let i = 0; i < rowsToFilter.length; i++) {\n      let row = rowsToFilter[i]!\n\n      const newRow = createRow(\n        table,\n        row.id,\n        row.original,\n        row.index,\n        row.depth,\n        undefined,\n        row.parentId\n      )\n      newRow.columnFilters = row.columnFilters\n\n      if (row.subRows?.length && depth < maxDepth) {\n        newRow.subRows = recurseFilterRows(row.subRows, depth + 1)\n        row = newRow\n\n        if (filterRow(row) && !newRow.subRows.length) {\n          rows.push(row)\n          newFilteredRowsById[row.id] = row\n          newFilteredFlatRows.push(row)\n          continue\n        }\n\n        if (filterRow(row) || newRow.subRows.length) {\n          rows.push(row)\n          newFilteredRowsById[row.id] = row\n          newFilteredFlatRows.push(row)\n          continue\n        }\n      } else {\n        row = newRow\n        if (filterRow(row)) {\n          rows.push(row)\n          newFilteredRowsById[row.id] = row\n          newFilteredFlatRows.push(row)\n        }\n      }\n    }\n\n    return rows\n  }\n\n  return {\n    rows: recurseFilterRows(rowsToFilter),\n    flatRows: newFilteredFlatRows,\n    rowsById: newFilteredRowsById,\n  }\n}\n\nfunction filterRowModelFromRoot<TData extends RowData>(\n  rowsToFilter: Row<TData>[],\n  filterRow: (row: Row<TData>) => any,\n  table: Table<TData>\n): RowModel<TData> {\n  const newFilteredFlatRows: Row<TData>[] = []\n  const newFilteredRowsById: Record<string, Row<TData>> = {}\n  const maxDepth = table.options.maxLeafRowFilterDepth ?? 100\n\n  // Filters top level and nested rows\n  const recurseFilterRows = (rowsToFilter: Row<TData>[], depth = 0) => {\n    // Filter from parents downward first\n\n    const rows: Row<TData>[] = []\n\n    // Apply the filter to any subRows\n    for (let i = 0; i < rowsToFilter.length; i++) {\n      let row = rowsToFilter[i]!\n\n      const pass = filterRow(row)\n\n      if (pass) {\n        if (row.subRows?.length && depth < maxDepth) {\n          const newRow = createRow(\n            table,\n            row.id,\n            row.original,\n            row.index,\n            row.depth,\n            undefined,\n            row.parentId\n          )\n          newRow.subRows = recurseFilterRows(row.subRows, depth + 1)\n          row = newRow\n        }\n\n        rows.push(row)\n        newFilteredFlatRows.push(row)\n        newFilteredRowsById[row.id] = row\n      }\n    }\n\n    return rows\n  }\n\n  return {\n    rows: recurseFilterRows(rowsToFilter),\n    flatRows: newFilteredFlatRows,\n    rowsById: newFilteredRowsById,\n  }\n}\n", "import { Table, RowModel, Row, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\nimport { filterRows } from './filterRowsUtils'\n\nexport function getFacetedRowModel<TData extends RowData>(): (\n  table: Table<TData>,\n  columnId: string\n) => () => RowModel<TData> {\n  return (table, columnId) =>\n    memo(\n      () => [\n        table.getPreFilteredRowModel(),\n        table.getState().columnFilters,\n        table.getState().globalFilter,\n        table.getFilteredRowModel(),\n      ],\n      (preRowModel, columnFilters, globalFilter) => {\n        if (\n          !preRowModel.rows.length ||\n          (!columnFilters?.length && !globalFilter)\n        ) {\n          return preRowModel\n        }\n\n        const filterableIds = [\n          ...columnFilters.map(d => d.id).filter(d => d !== columnId),\n          globalFilter ? '__global__' : undefined,\n        ].filter(Boolean) as string[]\n\n        const filterRowsImpl = (row: Row<TData>) => {\n          // Horizontally filter rows through each column\n          for (let i = 0; i < filterableIds.length; i++) {\n            if (row.columnFilters[filterableIds[i]!] === false) {\n              return false\n            }\n          }\n          return true\n        }\n\n        return filterRows(preRowModel.rows, filterRowsImpl, table)\n      },\n      getMemoOptions(table.options, 'debugTable', 'getFacetedRowModel')\n    )\n}\n", "import { Table, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\n\nexport function getFacetedUniqueValues<TData extends RowData>(): (\n  table: Table<TData>,\n  columnId: string\n) => () => Map<any, number> {\n  return (table, columnId) =>\n    memo(\n      () => [table.getColumn(columnId)?.getFacetedRowModel()],\n      facetedRowModel => {\n        if (!facetedRowModel) return new Map()\n\n        let facetedUniqueValues = new Map<any, number>()\n\n        for (let i = 0; i < facetedRowModel.flatRows.length; i++) {\n          const values =\n            facetedRowModel.flatRows[i]!.getUniqueValues<number>(columnId)\n\n          for (let j = 0; j < values.length; j++) {\n            const value = values[j]!\n\n            if (facetedUniqueValues.has(value)) {\n              facetedUniqueValues.set(\n                value,\n                (facetedUniqueValues.get(value) ?? 0) + 1\n              )\n            } else {\n              facetedUniqueValues.set(value, 1)\n            }\n          }\n        }\n\n        return facetedUniqueValues\n      },\n      getMemoOptions(\n        table.options,\n        'debugTable',\n        `getFacetedUniqueValues_${columnId}`\n      )\n    )\n}\n", "import { ResolvedColumnFilter } from '../features/ColumnFiltering'\nimport { Table, RowModel, Row, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\nimport { filterRows } from './filterRowsUtils'\n\nexport function getFilteredRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [\n        table.getPreFilteredRowModel(),\n        table.getState().columnFilters,\n        table.getState().globalFilter,\n      ],\n      (rowModel, columnFilters, globalFilter) => {\n        if (\n          !rowModel.rows.length ||\n          (!columnFilters?.length && !globalFilter)\n        ) {\n          for (let i = 0; i < rowModel.flatRows.length; i++) {\n            rowModel.flatRows[i]!.columnFilters = {}\n            rowModel.flatRows[i]!.columnFiltersMeta = {}\n          }\n          return rowModel\n        }\n\n        const resolvedColumnFilters: ResolvedColumnFilter<TData>[] = []\n        const resolvedGlobalFilters: ResolvedColumnFilter<TData>[] = []\n\n        ;(columnFilters ?? []).forEach(d => {\n          const column = table.getColumn(d.id)\n\n          if (!column) {\n            return\n          }\n\n          const filterFn = column.getFilterFn()\n\n          if (!filterFn) {\n            if (process.env.NODE_ENV !== 'production') {\n              console.warn(\n                `Could not find a valid 'column.filterFn' for column with the ID: ${column.id}.`\n              )\n            }\n            return\n          }\n\n          resolvedColumnFilters.push({\n            id: d.id,\n            filterFn,\n            resolvedValue: filterFn.resolveFilterValue?.(d.value) ?? d.value,\n          })\n        })\n\n        const filterableIds = (columnFilters ?? []).map(d => d.id)\n\n        const globalFilterFn = table.getGlobalFilterFn()\n\n        const globallyFilterableColumns = table\n          .getAllLeafColumns()\n          .filter(column => column.getCanGlobalFilter())\n\n        if (\n          globalFilter &&\n          globalFilterFn &&\n          globallyFilterableColumns.length\n        ) {\n          filterableIds.push('__global__')\n\n          globallyFilterableColumns.forEach(column => {\n            resolvedGlobalFilters.push({\n              id: column.id,\n              filterFn: globalFilterFn,\n              resolvedValue:\n                globalFilterFn.resolveFilterValue?.(globalFilter) ??\n                globalFilter,\n            })\n          })\n        }\n\n        let currentColumnFilter\n        let currentGlobalFilter\n\n        // Flag the prefiltered row model with each filter state\n        for (let j = 0; j < rowModel.flatRows.length; j++) {\n          const row = rowModel.flatRows[j]!\n\n          row.columnFilters = {}\n\n          if (resolvedColumnFilters.length) {\n            for (let i = 0; i < resolvedColumnFilters.length; i++) {\n              currentColumnFilter = resolvedColumnFilters[i]!\n              const id = currentColumnFilter.id\n\n              // Tag the row with the column filter state\n              row.columnFilters[id] = currentColumnFilter.filterFn(\n                row,\n                id,\n                currentColumnFilter.resolvedValue,\n                filterMeta => {\n                  row.columnFiltersMeta[id] = filterMeta\n                }\n              )\n            }\n          }\n\n          if (resolvedGlobalFilters.length) {\n            for (let i = 0; i < resolvedGlobalFilters.length; i++) {\n              currentGlobalFilter = resolvedGlobalFilters[i]!\n              const id = currentGlobalFilter.id\n              // Tag the row with the first truthy global filter state\n              if (\n                currentGlobalFilter.filterFn(\n                  row,\n                  id,\n                  currentGlobalFilter.resolvedValue,\n                  filterMeta => {\n                    row.columnFiltersMeta[id] = filterMeta\n                  }\n                )\n              ) {\n                row.columnFilters.__global__ = true\n                break\n              }\n            }\n\n            if (row.columnFilters.__global__ !== true) {\n              row.columnFilters.__global__ = false\n            }\n          }\n        }\n\n        const filterRowsImpl = (row: Row<TData>) => {\n          // Horizontally filter rows through each column\n          for (let i = 0; i < filterableIds.length; i++) {\n            if (row.columnFilters[filterableIds[i]!] === false) {\n              return false\n            }\n          }\n          return true\n        }\n\n        // Filter final rows using all of the active filters\n        return filterRows(rowModel.rows, filterRowsImpl, table)\n      },\n      getMemoOptions(table.options, 'debugTable', 'getFilteredRowModel', () =>\n        table._autoResetPageIndex()\n      )\n    )\n}\n", "import { createRow } from '../core/row'\nimport { Row, RowData, RowModel, Table } from '../types'\nimport { flattenBy, getMemoOptions, memo } from '../utils'\nimport { GroupingState } from '../features/ColumnGrouping'\n\nexport function getGroupedRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [table.getState().grouping, table.getPreGroupedRowModel()],\n      (grouping, rowModel) => {\n        if (!rowModel.rows.length || !grouping.length) {\n          rowModel.rows.forEach(row => {\n            row.depth = 0\n            row.parentId = undefined\n          })\n          return rowModel\n        }\n\n        // Filter the grouping list down to columns that exist\n        const existingGrouping = grouping.filter(columnId =>\n          table.getColumn(columnId)\n        )\n\n        const groupedFlatRows: Row<TData>[] = []\n        const groupedRowsById: Record<string, Row<TData>> = {}\n        // const onlyGroupedFlatRows: Row[] = [];\n        // const onlyGroupedRowsById: Record<RowId, Row> = {};\n        // const nonGroupedFlatRows: Row[] = [];\n        // const nonGroupedRowsById: Record<RowId, Row> = {};\n\n        // Recursively group the data\n        const groupUpRecursively = (\n          rows: Row<TData>[],\n          depth = 0,\n          parentId?: string\n        ) => {\n          // Grouping depth has been been met\n          // Stop grouping and simply rewrite thd depth and row relationships\n          if (depth >= existingGrouping.length) {\n            return rows.map(row => {\n              row.depth = depth\n\n              groupedFlatRows.push(row)\n              groupedRowsById[row.id] = row\n\n              if (row.subRows) {\n                row.subRows = groupUpRecursively(row.subRows, depth + 1, row.id)\n              }\n\n              return row\n            })\n          }\n\n          const columnId: string = existingGrouping[depth]!\n\n          // Group the rows together for this level\n          const rowGroupsMap = groupBy(rows, columnId)\n\n          // Perform aggregations for each group\n          const aggregatedGroupedRows = Array.from(rowGroupsMap.entries()).map(\n            ([groupingValue, groupedRows], index) => {\n              let id = `${columnId}:${groupingValue}`\n              id = parentId ? `${parentId}>${id}` : id\n\n              // First, Recurse to group sub rows before aggregation\n              const subRows = groupUpRecursively(groupedRows, depth + 1, id)\n\n              subRows.forEach(subRow => {\n                subRow.parentId = id\n              })\n\n              // Flatten the leaf rows of the rows in this group\n              const leafRows = depth\n                ? flattenBy(groupedRows, row => row.subRows)\n                : groupedRows\n\n              const row = createRow(\n                table,\n                id,\n                leafRows[0]!.original,\n                index,\n                depth,\n                undefined,\n                parentId\n              )\n\n              Object.assign(row, {\n                groupingColumnId: columnId,\n                groupingValue,\n                subRows,\n                leafRows,\n                getValue: (columnId: string) => {\n                  // Don't aggregate columns that are in the grouping\n                  if (existingGrouping.includes(columnId)) {\n                    if (row._valuesCache.hasOwnProperty(columnId)) {\n                      return row._valuesCache[columnId]\n                    }\n\n                    if (groupedRows[0]) {\n                      row._valuesCache[columnId] =\n                        groupedRows[0].getValue(columnId) ?? undefined\n                    }\n\n                    return row._valuesCache[columnId]\n                  }\n\n                  if (row._groupingValuesCache.hasOwnProperty(columnId)) {\n                    return row._groupingValuesCache[columnId]\n                  }\n\n                  // Aggregate the values\n                  const column = table.getColumn(columnId)\n                  const aggregateFn = column?.getAggregationFn()\n\n                  if (aggregateFn) {\n                    row._groupingValuesCache[columnId] = aggregateFn(\n                      columnId,\n                      leafRows,\n                      groupedRows\n                    )\n\n                    return row._groupingValuesCache[columnId]\n                  }\n                },\n              })\n\n              subRows.forEach(subRow => {\n                groupedFlatRows.push(subRow)\n                groupedRowsById[subRow.id] = subRow\n                // if (subRow.getIsGrouped?.()) {\n                //   onlyGroupedFlatRows.push(subRow);\n                //   onlyGroupedRowsById[subRow.id] = subRow;\n                // } else {\n                //   nonGroupedFlatRows.push(subRow);\n                //   nonGroupedRowsById[subRow.id] = subRow;\n                // }\n              })\n\n              return row\n            }\n          )\n\n          return aggregatedGroupedRows\n        }\n\n        const groupedRows = groupUpRecursively(rowModel.rows, 0)\n\n        groupedRows.forEach(subRow => {\n          groupedFlatRows.push(subRow)\n          groupedRowsById[subRow.id] = subRow\n          // if (subRow.getIsGrouped?.()) {\n          //   onlyGroupedFlatRows.push(subRow);\n          //   onlyGroupedRowsById[subRow.id] = subRow;\n          // } else {\n          //   nonGroupedFlatRows.push(subRow);\n          //   nonGroupedRowsById[subRow.id] = subRow;\n          // }\n        })\n\n        return {\n          rows: groupedRows,\n          flatRows: groupedFlatRows,\n          rowsById: groupedRowsById,\n        }\n      },\n      getMemoOptions(table.options, 'debugTable', 'getGroupedRowModel', () => {\n        table._queue(() => {\n          table._autoResetExpanded()\n          table._autoResetPageIndex()\n        })\n      })\n    )\n}\n\nfunction groupBy<TData extends RowData>(rows: Row<TData>[], columnId: string) {\n  const groupMap = new Map<any, Row<TData>[]>()\n\n  return rows.reduce((map, row) => {\n    const resKey = `${row.getGroupingValue(columnId)}`\n    const previous = map.get(resKey)\n    if (!previous) {\n      map.set(resKey, [row])\n    } else {\n      previous.push(row)\n    }\n    return map\n  }, groupMap)\n}\n", "import { Table, RowModel, Row, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\nimport { expandRows } from './getExpandedRowModel'\n\nexport function getPaginationRowModel<TData extends RowData>(opts?: {\n  initialSync: boolean\n}): (table: Table<TData>) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [\n        table.getState().pagination,\n        table.getPrePaginationRowModel(),\n        table.options.paginateExpandedRows\n          ? undefined\n          : table.getState().expanded,\n      ],\n      (pagination, rowModel) => {\n        if (!rowModel.rows.length) {\n          return rowModel\n        }\n\n        const { pageSize, pageIndex } = pagination\n        let { rows, flatRows, rowsById } = rowModel\n        const pageStart = pageSize * pageIndex\n        const pageEnd = pageStart + pageSize\n\n        rows = rows.slice(pageStart, pageEnd)\n\n        let paginatedRowModel: RowModel<TData>\n\n        if (!table.options.paginateExpandedRows) {\n          paginatedRowModel = expandRows({\n            rows,\n            flatRows,\n            rowsById,\n          })\n        } else {\n          paginatedRowModel = {\n            rows,\n            flatRows,\n            rowsById,\n          }\n        }\n\n        paginatedRowModel.flatRows = []\n\n        const handleRow = (row: Row<TData>) => {\n          paginatedRowModel.flatRows.push(row)\n          if (row.subRows.length) {\n            row.subRows.forEach(handleRow)\n          }\n        }\n\n        paginatedRowModel.rows.forEach(handleRow)\n\n        return paginatedRowModel\n      },\n      getMemoOptions(table.options, 'debugTable', 'getPaginationRowModel')\n    )\n}\n", "import { Table, Row, RowModel, RowData } from '../types'\nimport { SortingFn } from '../features/RowSorting'\nimport { getMemoOptions, memo } from '../utils'\n\nexport function getSortedRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [table.getState().sorting, table.getPreSortedRowModel()],\n      (sorting, rowModel) => {\n        if (!rowModel.rows.length || !sorting?.length) {\n          return rowModel\n        }\n\n        const sortingState = table.getState().sorting\n\n        const sortedFlatRows: Row<TData>[] = []\n\n        // Filter out sortings that correspond to non existing columns\n        const availableSorting = sortingState.filter(sort =>\n          table.getColumn(sort.id)?.getCanSort()\n        )\n\n        const columnInfoById: Record<\n          string,\n          {\n            sortUndefined?: false | -1 | 1 | 'first' | 'last'\n            invertSorting?: boolean\n            sortingFn: SortingFn<TData>\n          }\n        > = {}\n\n        availableSorting.forEach(sortEntry => {\n          const column = table.getColumn(sortEntry.id)\n          if (!column) return\n\n          columnInfoById[sortEntry.id] = {\n            sortUndefined: column.columnDef.sortUndefined,\n            invertSorting: column.columnDef.invertSorting,\n            sortingFn: column.getSortingFn(),\n          }\n        })\n\n        const sortData = (rows: Row<TData>[]) => {\n          // This will also perform a stable sorting using the row index\n          // if needed.\n          const sortedData = rows.map(row => ({ ...row }))\n\n          sortedData.sort((rowA, rowB) => {\n            for (let i = 0; i < availableSorting.length; i += 1) {\n              const sortEntry = availableSorting[i]!\n              const columnInfo = columnInfoById[sortEntry.id]!\n              const sortUndefined = columnInfo.sortUndefined\n              const isDesc = sortEntry?.desc ?? false\n\n              let sortInt = 0\n\n              // All sorting ints should always return in ascending order\n              if (sortUndefined) {\n                const aValue = rowA.getValue(sortEntry.id)\n                const bValue = rowB.getValue(sortEntry.id)\n\n                const aUndefined = aValue === undefined\n                const bUndefined = bValue === undefined\n\n                if (aUndefined || bUndefined) {\n                  if (sortUndefined === 'first') return aUndefined ? -1 : 1\n                  if (sortUndefined === 'last') return aUndefined ? 1 : -1\n                  sortInt =\n                    aUndefined && bUndefined\n                      ? 0\n                      : aUndefined\n                        ? sortUndefined\n                        : -sortUndefined\n                }\n              }\n\n              if (sortInt === 0) {\n                sortInt = columnInfo.sortingFn(rowA, rowB, sortEntry.id)\n              }\n\n              // If sorting is non-zero, take care of desc and inversion\n              if (sortInt !== 0) {\n                if (isDesc) {\n                  sortInt *= -1\n                }\n\n                if (columnInfo.invertSorting) {\n                  sortInt *= -1\n                }\n\n                return sortInt\n              }\n            }\n\n            return rowA.index - rowB.index\n          })\n\n          // If there are sub-rows, sort them\n          sortedData.forEach(row => {\n            sortedFlatRows.push(row)\n            if (row.subRows?.length) {\n              row.subRows = sortData(row.subRows)\n            }\n          })\n\n          return sortedData\n        }\n\n        return {\n          rows: sortData(rowModel.rows),\n          flatRows: sortedFlatRows,\n          rowsById: rowModel.rowsById,\n        }\n      },\n      getMemoOptions(table.options, 'debugTable', 'getSortedRowModel', () =>\n        table._autoResetPageIndex()\n      )\n    )\n}\n"], "names": ["createColumnHelper", "accessor", "column", "accessorFn", "accessorKey", "display", "group", "functionalUpdate", "updater", "input", "noop", "makeStateUpdater", "key", "instance", "setState", "old", "isFunction", "d", "Function", "isNumberArray", "Array", "isArray", "every", "val", "flattenBy", "arr", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flat", "recurse", "subArr", "for<PERSON>ach", "item", "push", "children", "length", "memo", "getDeps", "fn", "opts", "deps", "result", "depArgs", "depTime", "debug", "Date", "now", "newDeps", "depsChanged", "some", "dep", "index", "resultTime", "onChange", "depEndTime", "Math", "round", "resultEndTime", "resultFpsPercentage", "pad", "str", "num", "String", "console", "info", "max", "min", "getMemoOptions", "tableOptions", "debugLevel", "_tableOptions$debugAl", "debugAll", "createCell", "table", "row", "columnId", "getRenderValue", "_cell$getValue", "cell", "getValue", "options", "renderFallbackValue", "id", "renderValue", "getContext", "_features", "feature", "createColumn", "columnDef", "depth", "parent", "_ref", "_resolvedColumnDef$id", "defaultColumn", "_getDefaultColumnDef", "resolvedColumnDef", "prototype", "replaceAll", "replace", "undefined", "header", "includes", "originalRow", "split", "_result", "warn", "Error", "columns", "getFlatColumns", "_column$columns", "flatMap", "getLeafColumns", "_getOrderColumnsFn", "orderColumns", "_column$columns2", "leafColumns", "createHeader", "_options$id", "isPlaceholder", "placeholderId", "subHeaders", "colSpan", "rowSpan", "headerGroup", "getLeafHeaders", "leafHeaders", "recurse<PERSON><PERSON><PERSON>", "h", "map", "Headers", "createTable", "getHeaderGroups", "getAllColumns", "getVisibleLeafColumns", "getState", "columnPinning", "left", "right", "allColumns", "_left$map$filter", "_right$map$filter", "leftColumns", "find", "filter", "Boolean", "rightColumns", "centerColumns", "headerGroups", "buildHeaderGroups", "getCenterHeaderGroups", "getLeftHeaderGroups", "_left$map$filter2", "orderedLeafColumns", "getRightHeaderGroups", "_right$map$filter2", "getFooterGroups", "reverse", "getLeftFooterGroups", "getCenterFooterGroups", "getRightFooterGroups", "getFlatHeaders", "headers", "getLeftFlatHeaders", "getCenterFlatHeaders", "getRightFlatHeaders", "getCenterLeafHeaders", "flatHeaders", "_header$subHeaders", "getLeftLeafHeaders", "_header$subHeaders2", "getRightLeafHeaders", "_header$subHeaders3", "center", "_left$0$headers", "_left$", "_center$0$headers", "_center$", "_right$0$headers", "_right$", "columnsToGroup", "headerFamily", "_headerGroups$0$heade", "_headerGroups$", "max<PERSON><PERSON><PERSON>", "findMaxDepth", "getIsVisible", "createHeaderGroup", "headersToGroup", "join", "pendingParentHeaders", "headerToGroup", "latestPendingParentHeader", "isLeafHeader", "bottomHeaders", "recurseHeadersForSpans", "filteredHeaders", "childRowSpans", "childColSpan", "childRowSpan", "minChildRowSpan", "createRow", "original", "rowIndex", "subRows", "parentId", "_valuesCache", "_uniqueValuesCache", "hasOwnProperty", "getColumn", "getUniqueValues", "_row$getValue", "getLeafRows", "getParentRow", "getRow", "getParentRows", "parentRows", "currentRow", "parentRow", "getAllCells", "getAllLeafColumns", "_getAllCellsByColumnId", "allCells", "reduce", "acc", "i", "ColumnFaceting", "_getFacetedRowModel", "getFacetedRowModel", "getPreFilteredRowModel", "_getFacetedUniqueValues", "getFacetedUniqueValues", "Map", "_getFacetedMinMaxValues", "getFacetedMinMaxValues", "includesString", "filterValue", "_filterValue$toString", "search", "toString", "toLowerCase", "autoRemove", "<PERSON><PERSON><PERSON><PERSON>", "includesStringSensitive", "_row$getValue2", "equalsString", "_row$getValue3", "arrIncludes", "_row$getValue4", "arrIncludesAll", "_row$getValue5", "arrIncludesSome", "_row$getValue6", "equals", "weakEquals", "inNumberRange", "rowValue", "resolveFilterValue", "unsafeMin", "unsafeMax", "parsedMin", "parseFloat", "parsedMax", "Number", "isNaN", "Infinity", "temp", "filterFns", "ColumnFiltering", "getDefaultColumnDef", "filterFn", "getInitialState", "state", "columnFilters", "getDefaultOptions", "onColumnFiltersChange", "filterFromLeafRows", "maxLeafRowFilterDepth", "getAutoFilterFn", "firstRow", "getCoreRowModel", "flatRows", "value", "getFilterFn", "_table$options$filter", "_table$options$filter2", "getCanFilter", "_column$columnDef$ena", "_table$options$enable", "_table$options$enable2", "enableColumnFilter", "enableColumnFilters", "enableFilters", "getIsFiltered", "getFilterIndex", "getFilterValue", "_table$getState$colum", "_table$getState$colum2", "_table$getState$colum3", "findIndex", "setFilterValue", "setColumnFilters", "previousFilter", "newFilter", "shouldAutoRemoveFilter", "_old$filter", "newFilterObj", "_old$map", "_table", "columnFiltersMeta", "updateFn", "_functionalUpdate", "resetColumnFilters", "defaultState", "_table$initialState$c", "_table$initialState", "initialState", "getFilteredRowModel", "_getFilteredRowModel", "manualFiltering", "sum", "_leafRows", "childRows", "next", "nextValue", "extent", "mean", "leafRows", "count", "median", "values", "mid", "floor", "nums", "sort", "a", "b", "unique", "from", "Set", "uniqueCount", "size", "_columnId", "aggregationFns", "ColumnGrouping", "aggregatedCell", "props", "_toString", "_props$getValue", "aggregationFn", "grouping", "onGroupingChange", "groupedColumnMode", "toggleGrouping", "setGrouping", "getCanGroup", "enableGrouping", "getGroupingValue", "getIsGrouped", "_table$getState$group", "getGroupedIndex", "_table$getState$group2", "indexOf", "getToggleGroupingHandler", "canGroup", "getAutoAggregationFn", "Object", "call", "getAggregationFn", "_table$options$aggreg", "_table$options$aggreg2", "resetGrouping", "_table$initialState$g", "getPreGroupedRowModel", "getGroupedRowModel", "_getGroupedRowModel", "manualGrouping", "groupingColumnId", "_groupingValuesCache", "getIsPlaceholder", "getIsAggregated", "_row$subRows", "nonGroupingColumns", "col", "groupingColumns", "g", "ColumnOrdering", "columnOrder", "onColumnOrderChange", "getIndex", "position", "_getVisibleLeafColumns", "getIsFirstColumn", "_columns$", "getIsLastColumn", "_columns", "setColumnOrder", "resetColumnOrder", "orderedColumns", "columnOrderCopy", "columnsCopy", "targetColumnId", "shift", "foundIndex", "splice", "getDefaultColumnPinningState", "ColumnPinning", "onColumnPinningChange", "pin", "columnIds", "setColumnPinning", "_old$left3", "_old$right3", "_old$left", "_old$right", "_old$left2", "_old$right2", "getCanPin", "_d$columnDef$enablePi", "enablePinning", "enableColumnPinning", "getIsPinned", "leafColumnIds", "isLeft", "isRight", "getPinnedIndex", "getCenterVisibleCells", "_getAllVisibleCells", "leftAndRight", "getLeftVisibleCells", "cells", "getRightVisibleCells", "resetColumnPinning", "getIsSomeColumnsPinned", "_pinningState$positio", "pinningState", "_pinningState$left", "_pinningState$right", "getLeftLeafColumns", "getRightLeafColumns", "getCenterLeafColumns", "safelyAccessDocument", "_document", "document", "defaultColumnSizing", "minSize", "maxSize", "MAX_SAFE_INTEGER", "getDefaultColumnSizingInfoState", "startOffset", "startSize", "deltaOffset", "deltaPercentage", "isResizingColumn", "columnSizingStart", "ColumnSizing", "columnSizing", "columnSizingInfo", "columnResizeMode", "columnResizeDirection", "onColumnSizingChange", "onColumnSizingInfoChange", "getSize", "_column$columnDef$min", "_column$columnDef$max", "columnSize", "getStart", "slice", "getAfter", "resetSize", "setColumnSizing", "_ref2", "_", "rest", "getCanResize", "enableResizing", "enableColumnResizing", "getIsResizing", "_header$column$getSiz", "prevSiblingHeader", "getResizeHandler", "_contextDocument", "canResize", "e", "persist", "isTouchStartEvent", "touches", "clientX", "newColumnSizing", "updateOffset", "eventType", "clientXPos", "setColumnSizingInfo", "_old$startOffset", "_old$startSize", "deltaDirection", "_ref3", "headerSize", "onMove", "onEnd", "contextDocument", "mouseEvents", "<PERSON><PERSON><PERSON><PERSON>", "up<PERSON><PERSON><PERSON>", "removeEventListener", "touchEvents", "cancelable", "preventDefault", "stopPropagation", "_e$touches$", "passiveIfSupported", "passiveEventSupported", "passive", "addEventListener", "resetColumnSizing", "resetHeaderSizeInfo", "_table$initialState$c2", "getTotalSize", "_table$getHeaderGroup", "_table$getHeaderGroup2", "getLeftTotalSize", "_table$getLeftHeaderG", "_table$getLeftHeaderG2", "getCenterTotalSize", "_table$getCenterHeade", "_table$getCenterHeade2", "getRightTotalSize", "_table$getRightHeader", "_table$getRightHeader2", "passiveSupported", "supported", "window", "err", "type", "ColumnVisibility", "columnVisibility", "onColumnVisibilityChange", "toggleVisibility", "getCanHide", "setColumnVisibility", "childColumns", "c", "enableHiding", "getToggleVisibilityHandler", "target", "checked", "getVisibleCells", "makeVisibleColumnsMethod", "getColumns", "getVisibleFlatColumns", "getAllFlatColumns", "getLeftVisibleLeafColumns", "getRightVisibleLeafColumns", "getCenterVisibleLeafColumns", "resetColumnVisibility", "toggleAllColumnsVisible", "_value", "getIsAllColumnsVisible", "obj", "getIsSomeColumnsVisible", "getToggleAllColumnsVisibilityHandler", "_target", "GlobalFaceting", "_getGlobalFacetedRowModel", "getGlobalFacetedRowModel", "_getGlobalFacetedUniqueValues", "getGlobalFacetedUniqueValues", "_getGlobalFacetedMinMaxValues", "getGlobalFacetedMinMaxValues", "GlobalFiltering", "globalFilter", "onGlobalFilterChange", "globalFilterFn", "getColumnCanGlobalFilter", "_table$getCoreRowMode", "getCanGlobalFilter", "_table$options$getCol", "enableGlobalFilter", "getGlobalAutoFilterFn", "getGlobalFilterFn", "setGlobalFilter", "resetGlobalFilter", "RowExpanding", "expanded", "onExpandedChange", "paginateExpandedRows", "registered", "queued", "_autoResetExpanded", "_table$options$autoRe", "_queue", "autoResetAll", "autoResetExpanded", "manualExpanding", "resetExpanded", "setExpanded", "toggleAllRowsExpanded", "getIsAllRowsExpanded", "_table$initialState$e", "getCanSomeRowsExpand", "getPrePaginationRowModel", "getCanExpand", "getToggleAllRowsExpandedHandler", "getIsSomeRowsExpanded", "keys", "getRowModel", "getIsExpanded", "getExpandedDepth", "rowIds", "rowsById", "splitId", "getPreExpandedRowModel", "getSortedRowModel", "getExpandedRowModel", "_getExpandedRowModel", "toggleExpanded", "_expanded", "exists", "oldExpanded", "rowId", "_table$options$getIsR", "getIsRowExpanded", "_table$options$getRow", "getRowCanExpand", "enableExpanding", "getIsAllParentsExpanded", "isFullyExpanded", "getToggleExpandedHandler", "canExpand", "defaultPageIndex", "defaultPageSize", "getDefaultPaginationState", "pageIndex", "pageSize", "RowPagination", "pagination", "onPaginationChange", "_autoResetPageIndex", "autoResetPageIndex", "manualPagination", "resetPageIndex", "setPagination", "safeUpdater", "newState", "resetPagination", "_table$initialState$p", "setPageIndex", "maxPageIndex", "pageCount", "_table$initialState$p2", "resetPageSize", "_table$initialState$p3", "_table$initialState2", "setPageSize", "topRowIndex", "setPageCount", "_table$options$pageCo", "newPageCount", "getPageOptions", "getPageCount", "pageOptions", "fill", "getCanPreviousPage", "getCanNextPage", "previousPage", "nextPage", "firstPage", "lastPage", "getPaginationRowModel", "_getPaginationRowModel", "_table$options$pageCo2", "ceil", "getRowCount", "_table$options$rowCou", "rowCount", "rows", "getDefaultRowPinningState", "top", "bottom", "RowPinning", "rowPinning", "onRowPinningChange", "includeLeafRows", "includeParentRows", "leafRowIds", "parentRowIds", "setRowPinning", "_old$top3", "_old$bottom3", "_old$top", "_old$bottom", "has", "_old$top2", "_old$bottom2", "enableRowPinning", "isTop", "isBottom", "_ref4", "_visiblePinnedRowIds$", "visiblePinnedRowIds", "getTopRows", "getBottomRows", "_ref5", "resetRowPinning", "_table$initialState$r", "getIsSomeRowsPinned", "_pinningState$top", "_pinningState$bottom", "_getPinnedRows", "visibleRows", "pinnedRowIds", "_table$options$keepPi", "keepPinnedRows", "allRows", "topPinnedRowIds", "bottomPinnedRowIds", "getCenterRows", "topAndBottom", "RowSelection", "rowSelection", "onRowSelectionChange", "enableRowSelection", "enableMultiRowSelection", "enableSubRowSelection", "setRowSelection", "resetRowSelection", "toggleAllRowsSelected", "getIsAllRowsSelected", "preGroupedFlatRows", "getCanSelect", "toggleAllPageRowsSelected", "resolvedValue", "getIsAllPageRowsSelected", "mutateRowIsSelected", "getPreSelectedRowModel", "getSelectedRowModel", "rowModel", "selectRowsFn", "getFilteredSelectedRowModel", "getGroupedSelectedRowModel", "isAllRowsSelected", "paginationFlatRows", "isAllPageRowsSelected", "getIsSomeRowsSelected", "_table$getState$rowSe", "totalSelected", "getIsSomePageRowsSelected", "getIsSelected", "getIsSomeSelected", "getToggleAllRowsSelectedHandler", "getToggleAllPageRowsSelectedHandler", "toggleSelected", "isSelected", "_opts$selectChildren", "selectedRowIds", "select<PERSON><PERSON><PERSON><PERSON>", "isRowSelected", "isSubRowSelected", "getIsAllSubRowsSelected", "getCanSelectSubRows", "getCanMultiSelect", "_table$options$enable3", "getToggleSelectedHandler", "canSelect", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "newSelectedFlatRows", "newSelectedRowsById", "recurseRows", "_row$subRows2", "selection", "_selection$row$id", "_row$subRows3", "allChildrenSelected", "someSelected", "subRow", "subRowChildrenSelected", "reSplitAlphaNumeric", "alphanumeric", "rowA", "rowB", "compareAlphanumeric", "alphanumericCaseSensitive", "text", "compareBasic", "textCaseSensitive", "datetime", "basic", "aStr", "bStr", "aa", "bb", "an", "parseInt", "bn", "combo", "sortingFns", "RowSorting", "sorting", "sortingFn", "sortUndefined", "onSortingChange", "isMultiSortEvent", "shift<PERSON>ey", "getAutoSortingFn", "firstRows", "isString", "getAutoSortDir", "getSortingFn", "_table$options$sortin", "_table$options$sortin2", "toggleSorting", "desc", "multi", "nextSortingOrder", "getNextSortingOrder", "hasManual<PERSON><PERSON>ue", "setSorting", "existingSorting", "existingIndex", "newSorting", "sortAction", "nextDesc", "getCanMultiSort", "_table$options$maxMul", "maxMultiSortColCount", "getFirstSortDir", "_column$columnDef$sor", "sortDescFirst", "firstSortDirection", "isSorted", "getIsSorted", "enableSortingRemoval", "enableMultiRemove", "getCanSort", "enableSorting", "_column$columnDef$ena2", "enableMultiSort", "_table$getState$sorti", "columnSort", "getSortIndex", "_table$getState$sorti2", "_table$getState$sorti3", "clearSorting", "getToggleSortingHandler", "canSort", "resetSorting", "_table$initialState$s", "getPreSortedRowModel", "_getSortedRowModel", "manualSorting", "builtInFeatures", "_options$_features", "_options$initialState", "debugTable", "defaultOptions", "assign", "mergeOptions", "coreInitialState", "_feature$getInitialSt", "queuedTimeout", "coreInstance", "cb", "Promise", "resolve", "then", "catch", "error", "setTimeout", "reset", "setOptions", "newOptions", "onStateChange", "_getRowId", "getRowId", "_getCoreRowModel", "searchAll", "_defaultColumn", "_props$renderValue$to", "_props$renderValue", "_getColumnDefs", "columnDefs", "recurseColumns", "groupingColumnDef", "_getAllFlatColumnsById", "flatColumns", "data", "accessRows", "originalRows", "getSubRows", "_row$originalSubRows", "originalSubRows", "expandRows", "expandedRows", "handleRow", "_table$getColumn", "facetedRowModel", "uniqueValues", "flatRow", "_flatRow$getUniqueVal", "facetedMinValue", "facetedMaxValue", "filterRows", "filterRowImpl", "filterRowModelFromLeafs", "filterRowModelFromRoot", "rowsToFilter", "filterRow", "_table$options$maxLea", "newFilteredFlatRows", "newFilteredRowsById", "recurseFilterRows", "newRow", "_table$options$maxLea2", "pass", "preRowModel", "filterableIds", "filterRowsImpl", "facetedUniqueValues", "j", "_facetedUniqueValues$", "set", "get", "resolvedColumnFilters", "resolvedGlobalFilters", "_filterFn$resolveFilt", "globallyFilterableColumns", "_globalFilterFn$resol", "currentColumnFilter", "currentGlobalFilter", "filterMeta", "__global__", "existingGrouping", "groupedFlatRows", "groupedRowsById", "groupUpRecursively", "rowGroupsMap", "groupBy", "aggregatedGroupedRows", "entries", "groupingValue", "groupedRows", "_groupedRows$0$getVal", "aggregateFn", "groupMap", "res<PERSON>ey", "previous", "pageStart", "pageEnd", "paginatedRowModel", "sortingState", "sortedFlatRows", "availableSorting", "columnInfoById", "sortEntry", "invertSorting", "sortData", "sortedData", "_sortEntry$desc", "columnInfo", "isDesc", "sortInt", "aValue", "bValue", "aUndefined", "bUndefined"], "mappings": ";;;;;;;;;;;;;;;;EAWA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;;EAEA;;EAEA;EACA;EACA;;EAEA;EACA;EACA;;EAEA;EACA;EACA;;EAsBO,SAASA,kBAAkBA,GAET;IACvB,OAAO;EACLC,IAAAA,QAAQ,EAAEA,CAACA,QAAQ,EAAEC,MAAM,KAAK;EAC9B,MAAA,OAAO,OAAOD,QAAQ,KAAK,UAAU,GAChC;EACC,QAAA,GAAGC,MAAM;EACTC,QAAAA,UAAU,EAAEF,QAAAA;EACd,OAAC,GACD;EACE,QAAA,GAAGC,MAAM;EACTE,QAAAA,WAAW,EAAEH,QAAAA;SACd,CAAA;OACN;MACDI,OAAO,EAAEH,MAAM,IAAIA,MAAM;MACzBI,KAAK,EAAEJ,MAAM,IAAIA,MAAAA;KAClB,CAAA;EACH;;EC9DA;;EAOA;;EA2CA;;EAEO,SAASK,gBAAgBA,CAAIC,OAAmB,EAAEC,KAAQ,EAAK;IACpE,OAAO,OAAOD,OAAO,KAAK,UAAU,GAC/BA,OAAO,CAAqBC,KAAK,CAAC,GACnCD,OAAO,CAAA;EACb,CAAA;EAEO,SAASE,IAAIA,GAAG;EACrB;EAAA,CAAA;EAGK,SAASC,gBAAgBA,CAC9BC,GAAM,EACNC,QAAiB,EACjB;EACA,EAAA,OAAQL,OAA+B,IAAK;EACxCK,IAAAA,QAAQ,CAASC,QAAQ,CAAeC,GAAgB,IAAK;QAC7D,OAAO;EACL,QAAA,GAAGA,GAAG;UACN,CAACH,GAAG,GAAGL,gBAAgB,CAACC,OAAO,EAAGO,GAAG,CAASH,GAAG,CAAC,CAAA;SACnD,CAAA;EACH,KAAC,CAAC,CAAA;KACH,CAAA;EACH,CAAA;EAIO,SAASI,UAAUA,CAAwBC,CAAM,EAAU;IAChE,OAAOA,CAAC,YAAYC,QAAQ,CAAA;EAC9B,CAAA;EAEO,SAASC,aAAaA,CAACF,CAAM,EAAiB;EACnD,EAAA,OAAOG,KAAK,CAACC,OAAO,CAACJ,CAAC,CAAC,IAAIA,CAAC,CAACK,KAAK,CAACC,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,CAAC,CAAA;EACpE,CAAA;EAEO,SAASC,SAASA,CACvBC,GAAY,EACZC,WAAqC,EACrC;IACA,MAAMC,IAAa,GAAG,EAAE,CAAA;IAExB,MAAMC,OAAO,GAAIC,MAAe,IAAK;EACnCA,IAAAA,MAAM,CAACC,OAAO,CAACC,IAAI,IAAI;EACrBJ,MAAAA,IAAI,CAACK,IAAI,CAACD,IAAI,CAAC,CAAA;EACf,MAAA,MAAME,QAAQ,GAAGP,WAAW,CAACK,IAAI,CAAC,CAAA;EAClC,MAAA,IAAIE,QAAQ,IAAA,IAAA,IAARA,QAAQ,CAAEC,MAAM,EAAE;UACpBN,OAAO,CAACK,QAAQ,CAAC,CAAA;EACnB,OAAA;EACF,KAAC,CAAC,CAAA;KACH,CAAA;IAEDL,OAAO,CAACH,GAAG,CAAC,CAAA;EAEZ,EAAA,OAAOE,IAAI,CAAA;EACb,CAAA;EAEO,SAASQ,IAAIA,CAClBC,OAA2C,EAC3CC,EAA6C,EAC7CC,IAIC,EACgC;IACjC,IAAIC,IAAW,GAAG,EAAE,CAAA;EACpB,EAAA,IAAIC,MAA2B,CAAA;EAE/B,EAAA,OAAOC,OAAO,IAAI;EAChB,IAAA,IAAIC,OAAe,CAAA;EACnB,IAAA,IAAIJ,IAAI,CAAC1B,GAAG,IAAI0B,IAAI,CAACK,KAAK,EAAED,OAAO,GAAGE,IAAI,CAACC,GAAG,EAAE,CAAA;EAEhD,IAAA,MAAMC,OAAO,GAAGV,OAAO,CAACK,OAAO,CAAC,CAAA;MAEhC,MAAMM,WAAW,GACfD,OAAO,CAACZ,MAAM,KAAKK,IAAI,CAACL,MAAM,IAC9BY,OAAO,CAACE,IAAI,CAAC,CAACC,GAAQ,EAAEC,KAAa,KAAKX,IAAI,CAACW,KAAK,CAAC,KAAKD,GAAG,CAAC,CAAA;MAEhE,IAAI,CAACF,WAAW,EAAE;EAChB,MAAA,OAAOP,MAAM,CAAA;EACf,KAAA;EAEAD,IAAAA,IAAI,GAAGO,OAAO,CAAA;EAEd,IAAA,IAAIK,UAAkB,CAAA;EACtB,IAAA,IAAIb,IAAI,CAAC1B,GAAG,IAAI0B,IAAI,CAACK,KAAK,EAAEQ,UAAU,GAAGP,IAAI,CAACC,GAAG,EAAE,CAAA;EAEnDL,IAAAA,MAAM,GAAGH,EAAE,CAAC,GAAGS,OAAO,CAAC,CAAA;MACvBR,IAAI,IAAA,IAAA,IAAJA,IAAI,CAAEc,QAAQ,IAAA,IAAA,IAAdd,IAAI,CAAEc,QAAQ,CAAGZ,MAAM,CAAC,CAAA;EAExB,IAAA,IAAIF,IAAI,CAAC1B,GAAG,IAAI0B,IAAI,CAACK,KAAK,EAAE;EAC1B,MAAA,IAAIL,IAAI,IAAJA,IAAAA,IAAAA,IAAI,CAAEK,KAAK,EAAE,EAAE;EACjB,QAAA,MAAMU,UAAU,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACX,IAAI,CAACC,GAAG,EAAE,GAAGH,OAAQ,IAAI,GAAG,CAAC,GAAG,GAAG,CAAA;EAClE,QAAA,MAAMc,aAAa,GAAGF,IAAI,CAACC,KAAK,CAAC,CAACX,IAAI,CAACC,GAAG,EAAE,GAAGM,UAAW,IAAI,GAAG,CAAC,GAAG,GAAG,CAAA;EACxE,QAAA,MAAMM,mBAAmB,GAAGD,aAAa,GAAG,EAAE,CAAA;EAE9C,QAAA,MAAME,GAAG,GAAGA,CAACC,GAAoB,EAAEC,GAAW,KAAK;EACjDD,UAAAA,GAAG,GAAGE,MAAM,CAACF,GAAG,CAAC,CAAA;EACjB,UAAA,OAAOA,GAAG,CAACzB,MAAM,GAAG0B,GAAG,EAAE;cACvBD,GAAG,GAAG,GAAG,GAAGA,GAAG,CAAA;EACjB,WAAA;EACA,UAAA,OAAOA,GAAG,CAAA;WACX,CAAA;EAEDG,QAAAA,OAAO,CAACC,IAAI,CACV,OAAOL,GAAG,CAACF,aAAa,EAAE,CAAC,CAAC,CAAA,EAAA,EAAKE,GAAG,CAACL,UAAU,EAAE,CAAC,CAAC,KAAK,EACxD,CAAA;AACV;AACA;AACA,uBAAyBC,EAAAA,IAAI,CAACU,GAAG,CACnB,CAAC,EACDV,IAAI,CAACW,GAAG,CAAC,GAAG,GAAG,GAAG,GAAGR,mBAAmB,EAAE,GAAG,CAC/C,CAAC,CAAA,cAAA,CAAgB,EACnBnB,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAE1B,GACR,CAAC,CAAA;EACH,OAAA;EACF,KAAA;EAEA,IAAA,OAAO4B,MAAM,CAAA;KACd,CAAA;EACH,CAAA;EAEO,SAAS0B,cAAcA,CAC5BC,YAAgD,EAChDC,UAMkB,EAClBxD,GAAW,EACXwC,QAAgC,EAChC;IACA,OAAO;EACLT,IAAAA,KAAK,EAAEA,MAAA;EAAA,MAAA,IAAA0B,qBAAA,CAAA;EAAA,MAAA,OAAA,CAAAA,qBAAA,GAAMF,YAAY,IAAA,IAAA,GAAA,KAAA,CAAA,GAAZA,YAAY,CAAEG,QAAQ,KAAA,IAAA,GAAAD,qBAAA,GAAIF,YAAY,CAACC,UAAU,CAAC,CAAA;EAAA,KAAA;MAC/DxD,GAAG,EAA4CA,GAAG;EAClDwC,IAAAA,QAAAA;KACD,CAAA;EACH;;ECvKO,SAASmB,UAAUA,CACxBC,KAAmB,EACnBC,GAAe,EACfvE,MAA6B,EAC7BwE,QAAgB,EACK;IACrB,MAAMC,cAAc,GAAGA,MAAA;EAAA,IAAA,IAAAC,cAAA,CAAA;EAAA,IAAA,OAAA,CAAAA,cAAA,GACrBC,IAAI,CAACC,QAAQ,EAAE,KAAAF,IAAAA,GAAAA,cAAA,GAAIJ,KAAK,CAACO,OAAO,CAACC,mBAAmB,CAAA;EAAA,GAAA,CAAA;EAEtD,EAAA,MAAMH,IAA6B,GAAG;MACpCI,EAAE,EAAE,GAAGR,GAAG,CAACQ,EAAE,CAAI/E,CAAAA,EAAAA,MAAM,CAAC+E,EAAE,CAAE,CAAA;MAC5BR,GAAG;MACHvE,MAAM;MACN4E,QAAQ,EAAEA,MAAML,GAAG,CAACK,QAAQ,CAACJ,QAAQ,CAAC;EACtCQ,IAAAA,WAAW,EAAEP,cAAc;MAC3BQ,UAAU,EAAEhD,IAAI,CACd,MAAM,CAACqC,KAAK,EAAEtE,MAAM,EAAEuE,GAAG,EAAEI,IAAI,CAAC,EAChC,CAACL,KAAK,EAAEtE,MAAM,EAAEuE,GAAG,EAAEI,IAAI,MAAM;QAC7BL,KAAK;QACLtE,MAAM;QACNuE,GAAG;EACHI,MAAAA,IAAI,EAAEA,IAA2B;QACjCC,QAAQ,EAAED,IAAI,CAACC,QAAQ;QACvBI,WAAW,EAAEL,IAAI,CAACK,WAAAA;OACnB,CAAC,EACFhB,cAAc,CAACM,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,iBAAiB,CAC/D,CAAA;KACD,CAAA;EAEDP,EAAAA,KAAK,CAACY,SAAS,CAACtD,OAAO,CAACuD,OAAO,IAAI;EACjCA,IAAAA,OAAO,CAACd,UAAU,IAAlBc,IAAAA,IAAAA,OAAO,CAACd,UAAU,CAChBM,IAAI,EACJ3E,MAAM,EACNuE,GAAG,EACHD,KACF,CAAC,CAAA;KACF,EAAE,EAAE,CAAC,CAAA;EAEN,EAAA,OAAOK,IAAI,CAAA;EACb;;EC1BO,SAASS,YAAYA,CAC1Bd,KAAmB,EACnBe,SAAmC,EACnCC,KAAa,EACbC,MAA8B,EACP;IAAA,IAAAC,IAAA,EAAAC,qBAAA,CAAA;EACvB,EAAA,MAAMC,aAAa,GAAGpB,KAAK,CAACqB,oBAAoB,EAAE,CAAA;EAElD,EAAA,MAAMC,iBAAiB,GAAG;EACxB,IAAA,GAAGF,aAAa;MAChB,GAAGL,SAAAA;KACwB,CAAA;EAE7B,EAAA,MAAMnF,WAAW,GAAG0F,iBAAiB,CAAC1F,WAAW,CAAA;IAEjD,IAAI6E,EAAE,GAAAS,CAAAA,IAAA,GAAAC,CAAAA,qBAAA,GACJG,iBAAiB,CAACb,EAAE,KAAAU,IAAAA,GAAAA,qBAAA,GACnBvF,WAAW,GACR,OAAOyD,MAAM,CAACkC,SAAS,CAACC,UAAU,KAAK,UAAU,GAC/C5F,WAAW,CAAC4F,UAAU,CAAC,GAAG,EAAE,GAAG,CAAC,GAChC5F,WAAW,CAAC6F,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,GACjCC,SAAS,KAAA,IAAA,GAAAR,IAAA,GACZ,OAAOI,iBAAiB,CAACK,MAAM,KAAK,QAAQ,GACzCL,iBAAiB,CAACK,MAAM,GACxBD,SAAU,CAAA;EAEhB,EAAA,IAAI/F,UAAyC,CAAA;IAE7C,IAAI2F,iBAAiB,CAAC3F,UAAU,EAAE;MAChCA,UAAU,GAAG2F,iBAAiB,CAAC3F,UAAU,CAAA;KAC1C,MAAM,IAAIC,WAAW,EAAE;EACtB;EACA,IAAA,IAAIA,WAAW,CAACgG,QAAQ,CAAC,GAAG,CAAC,EAAE;QAC7BjG,UAAU,GAAIkG,WAAkB,IAAK;UACnC,IAAI7D,MAAM,GAAG6D,WAAkC,CAAA;UAE/C,KAAK,MAAMzF,GAAG,IAAIR,WAAW,CAACkG,KAAK,CAAC,GAAG,CAAC,EAAE;EAAA,UAAA,IAAAC,OAAA,CAAA;YACxC/D,MAAM,GAAA,CAAA+D,OAAA,GAAG/D,MAAM,qBAAN+D,OAAA,CAAS3F,GAAG,CAAC,CAAA;YACtB,IAA6C4B,MAAM,KAAK0D,SAAS,EAAE;cACjEpC,OAAO,CAAC0C,IAAI,CACV,CAAA,CAAA,EAAI5F,GAAG,CAA2BR,wBAAAA,EAAAA,WAAW,uBAC/C,CAAC,CAAA;EACH,WAAA;EACF,SAAA;EAEA,QAAA,OAAOoC,MAAM,CAAA;SACd,CAAA;EACH,KAAC,MAAM;QACLrC,UAAU,GAAIkG,WAAkB,IAC7BA,WAAW,CAASP,iBAAiB,CAAC1F,WAAW,CAAC,CAAA;EACvD,KAAA;EACF,GAAA;IAEA,IAAI,CAAC6E,EAAE,EAAE;EACP,IAA2C;QACzC,MAAM,IAAIwB,KAAK,CACbX,iBAAiB,CAAC3F,UAAU,GACxB,CAAA,8CAAA,CAAgD,GAChD,CAAA,oDAAA,CACN,CAAC,CAAA;EACH,KAAA;EAEF,GAAA;EAEA,EAAA,IAAID,MAA8B,GAAG;EACnC+E,IAAAA,EAAE,EAAE,CAAGpB,EAAAA,MAAM,CAACoB,EAAE,CAAC,CAAE,CAAA;MACnB9E,UAAU;EACVsF,IAAAA,MAAM,EAAEA,MAAa;MACrBD,KAAK;EACLD,IAAAA,SAAS,EAAEO,iBAA0C;EACrDY,IAAAA,OAAO,EAAE,EAAE;MACXC,cAAc,EAAExE,IAAI,CAClB,MAAM,CAAC,IAAI,CAAC,EACZ,MAAM;EAAA,MAAA,IAAAyE,eAAA,CAAA;QACJ,OAAO,CACL1G,MAAM,EACN,IAAA,CAAA0G,eAAA,GAAG1G,MAAM,CAACwG,OAAO,KAAdE,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,eAAA,CAAgBC,OAAO,CAAC5F,CAAC,IAAIA,CAAC,CAAC0F,cAAc,EAAE,CAAC,EACpD,CAAA;OACF,EACDzC,cAAc,CAACM,KAAK,CAACO,OAAO,EAAE,cAAc,EAAE,uBAAuB,CACvE,CAAC;EACD+B,IAAAA,cAAc,EAAE3E,IAAI,CAClB,MAAM,CAACqC,KAAK,CAACuC,kBAAkB,EAAE,CAAC,EAClCC,YAAY,IAAI;EAAA,MAAA,IAAAC,gBAAA,CAAA;QACd,IAAAA,CAAAA,gBAAA,GAAI/G,MAAM,CAACwG,OAAO,KAAdO,IAAAA,IAAAA,gBAAA,CAAgB/E,MAAM,EAAE;EAC1B,QAAA,IAAIgF,WAAW,GAAGhH,MAAM,CAACwG,OAAO,CAACG,OAAO,CAAC3G,MAAM,IAC7CA,MAAM,CAAC4G,cAAc,EACvB,CAAC,CAAA;UAED,OAAOE,YAAY,CAACE,WAAW,CAAC,CAAA;EAClC,OAAA;QAEA,OAAO,CAAChH,MAAM,CAA0B,CAAA;OACzC,EACDgE,cAAc,CAACM,KAAK,CAACO,OAAO,EAAE,cAAc,EAAE,uBAAuB,CACvE,CAAA;KACD,CAAA;EAED,EAAA,KAAK,MAAMM,OAAO,IAAIb,KAAK,CAACY,SAAS,EAAE;MACrCC,OAAO,CAACC,YAAY,IAAA,IAAA,IAApBD,OAAO,CAACC,YAAY,CAAGpF,MAAM,EAA2BsE,KAAK,CAAC,CAAA;EAChE,GAAA;;EAEA;EACA,EAAA,OAAOtE,MAAM,CAAA;EACf;;EC9JA,MAAMyC,KAAK,GAAG,cAAc,CAAA;EAwM5B;;EAEA,SAASwE,YAAYA,CACnB3C,KAAmB,EACnBtE,MAA6B,EAC7B6E,OAMC,EACsB;EAAA,EAAA,IAAAqC,WAAA,CAAA;EACvB,EAAA,MAAMnC,EAAE,GAAA,CAAAmC,WAAA,GAAGrC,OAAO,CAACE,EAAE,KAAA,IAAA,GAAAmC,WAAA,GAAIlH,MAAM,CAAC+E,EAAE,CAAA;EAElC,EAAA,IAAIkB,MAAiC,GAAG;MACtClB,EAAE;MACF/E,MAAM;MACNgD,KAAK,EAAE6B,OAAO,CAAC7B,KAAK;EACpBmE,IAAAA,aAAa,EAAE,CAAC,CAACtC,OAAO,CAACsC,aAAa;MACtCC,aAAa,EAAEvC,OAAO,CAACuC,aAAa;MACpC9B,KAAK,EAAET,OAAO,CAACS,KAAK;EACpB+B,IAAAA,UAAU,EAAE,EAAE;EACdC,IAAAA,OAAO,EAAE,CAAC;EACVC,IAAAA,OAAO,EAAE,CAAC;EACVC,IAAAA,WAAW,EAAE,IAAK;MAClBC,cAAc,EAAEA,MAAgC;QAC9C,MAAMC,WAAqC,GAAG,EAAE,CAAA;QAEhD,MAAMC,aAAa,GAAIC,CAAyB,IAAK;UACnD,IAAIA,CAAC,CAACP,UAAU,IAAIO,CAAC,CAACP,UAAU,CAACrF,MAAM,EAAE;EACvC4F,UAAAA,CAAC,CAACP,UAAU,CAACQ,GAAG,CAACF,aAAa,CAAC,CAAA;EACjC,SAAA;EACAD,QAAAA,WAAW,CAAC5F,IAAI,CAAC8F,CAA2B,CAAC,CAAA;SAC9C,CAAA;QAEDD,aAAa,CAAC1B,MAAM,CAAC,CAAA;EAErB,MAAA,OAAOyB,WAAW,CAAA;OACnB;MACDzC,UAAU,EAAEA,OAAO;QACjBX,KAAK;EACL2B,MAAAA,MAAM,EAAEA,MAA+B;EACvCjG,MAAAA,MAAAA;OACD,CAAA;KACF,CAAA;EAEDsE,EAAAA,KAAK,CAACY,SAAS,CAACtD,OAAO,CAACuD,OAAO,IAAI;MACjCA,OAAO,CAAC8B,YAAY,IAAA,IAAA,IAApB9B,OAAO,CAAC8B,YAAY,CAAGhB,MAAM,EAA2B3B,KAAK,CAAC,CAAA;EAChE,GAAC,CAAC,CAAA;EAEF,EAAA,OAAO2B,MAAM,CAAA;EACf,CAAA;AAEO,QAAM6B,OAAqB,GAAG;IACnCC,WAAW,EAA0BzD,KAAmB,IAAW;EACjE;;MAEAA,KAAK,CAAC0D,eAAe,GAAG/F,IAAI,CAC1B,MAAM,CACJqC,KAAK,CAAC2D,aAAa,EAAE,EACrB3D,KAAK,CAAC4D,qBAAqB,EAAE,EAC7B5D,KAAK,CAAC6D,QAAQ,EAAE,CAACC,aAAa,CAACC,IAAI,EACnC/D,KAAK,CAAC6D,QAAQ,EAAE,CAACC,aAAa,CAACE,KAAK,CACrC,EACD,CAACC,UAAU,EAAEvB,WAAW,EAAEqB,IAAI,EAAEC,KAAK,KAAK;QAAA,IAAAE,gBAAA,EAAAC,iBAAA,CAAA;EACxC,MAAA,MAAMC,WAAW,GAAA,CAAAF,gBAAA,GACfH,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CACAR,GAAG,CAACrD,QAAQ,IAAIwC,WAAW,CAAC2B,IAAI,CAAC5H,CAAC,IAAIA,CAAC,CAACgE,EAAE,KAAKP,QAAQ,CAAE,CAAC,CAC3DoE,MAAM,CAACC,OAAO,CAAC,KAAAL,IAAAA,GAAAA,gBAAA,GAAI,EAAE,CAAA;EAE1B,MAAA,MAAMM,YAAY,GAAA,CAAAL,iBAAA,GAChBH,KAAK,IAALA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,KAAK,CACDT,GAAG,CAACrD,QAAQ,IAAIwC,WAAW,CAAC2B,IAAI,CAAC5H,CAAC,IAAIA,CAAC,CAACgE,EAAE,KAAKP,QAAQ,CAAE,CAAC,CAC3DoE,MAAM,CAACC,OAAO,CAAC,KAAAJ,IAAAA,GAAAA,iBAAA,GAAI,EAAE,CAAA;EAE1B,MAAA,MAAMM,aAAa,GAAG/B,WAAW,CAAC4B,MAAM,CACtC5I,MAAM,IAAI,EAACqI,IAAI,IAAA,IAAA,IAAJA,IAAI,CAAEnC,QAAQ,CAAClG,MAAM,CAAC+E,EAAE,CAAC,CAAA,IAAI,EAACuD,KAAK,YAALA,KAAK,CAAEpC,QAAQ,CAAClG,MAAM,CAAC+E,EAAE,CAAC,CACrE,CAAC,CAAA;EAED,MAAA,MAAMiE,YAAY,GAAGC,iBAAiB,CACpCV,UAAU,EACV,CAAC,GAAGG,WAAW,EAAE,GAAGK,aAAa,EAAE,GAAGD,YAAY,CAAC,EACnDxE,KACF,CAAC,CAAA;EAED,MAAA,OAAO0E,YAAY,CAAA;OACpB,EACDhF,cAAc,CAACM,KAAK,CAACO,OAAO,EAAEpC,KAAK,EAAE,iBAAiB,CACxD,CAAC,CAAA;MAED6B,KAAK,CAAC4E,qBAAqB,GAAGjH,IAAI,CAChC,MAAM,CACJqC,KAAK,CAAC2D,aAAa,EAAE,EACrB3D,KAAK,CAAC4D,qBAAqB,EAAE,EAC7B5D,KAAK,CAAC6D,QAAQ,EAAE,CAACC,aAAa,CAACC,IAAI,EACnC/D,KAAK,CAAC6D,QAAQ,EAAE,CAACC,aAAa,CAACE,KAAK,CACrC,EACD,CAACC,UAAU,EAAEvB,WAAW,EAAEqB,IAAI,EAAEC,KAAK,KAAK;EACxCtB,MAAAA,WAAW,GAAGA,WAAW,CAAC4B,MAAM,CAC9B5I,MAAM,IAAI,EAACqI,IAAI,IAAA,IAAA,IAAJA,IAAI,CAAEnC,QAAQ,CAAClG,MAAM,CAAC+E,EAAE,CAAC,CAAA,IAAI,EAACuD,KAAK,YAALA,KAAK,CAAEpC,QAAQ,CAAClG,MAAM,CAAC+E,EAAE,CAAC,CACrE,CAAC,CAAA;QACD,OAAOkE,iBAAiB,CAACV,UAAU,EAAEvB,WAAW,EAAE1C,KAAK,EAAE,QAAQ,CAAC,CAAA;OACnE,EACDN,cAAc,CAACM,KAAK,CAACO,OAAO,EAAEpC,KAAK,EAAE,uBAAuB,CAC9D,CAAC,CAAA;EAED6B,IAAAA,KAAK,CAAC6E,mBAAmB,GAAGlH,IAAI,CAC9B,MAAM,CACJqC,KAAK,CAAC2D,aAAa,EAAE,EACrB3D,KAAK,CAAC4D,qBAAqB,EAAE,EAC7B5D,KAAK,CAAC6D,QAAQ,EAAE,CAACC,aAAa,CAACC,IAAI,CACpC,EACD,CAACE,UAAU,EAAEvB,WAAW,EAAEqB,IAAI,KAAK;EAAA,MAAA,IAAAe,iBAAA,CAAA;EACjC,MAAA,MAAMC,kBAAkB,GAAA,CAAAD,iBAAA,GACtBf,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CACAR,GAAG,CAACrD,QAAQ,IAAIwC,WAAW,CAAC2B,IAAI,CAAC5H,CAAC,IAAIA,CAAC,CAACgE,EAAE,KAAKP,QAAQ,CAAE,CAAC,CAC3DoE,MAAM,CAACC,OAAO,CAAC,KAAAO,IAAAA,GAAAA,iBAAA,GAAI,EAAE,CAAA;QAE1B,OAAOH,iBAAiB,CAACV,UAAU,EAAEc,kBAAkB,EAAE/E,KAAK,EAAE,MAAM,CAAC,CAAA;OACxE,EACDN,cAAc,CAACM,KAAK,CAACO,OAAO,EAAEpC,KAAK,EAAE,qBAAqB,CAC5D,CAAC,CAAA;EAED6B,IAAAA,KAAK,CAACgF,oBAAoB,GAAGrH,IAAI,CAC/B,MAAM,CACJqC,KAAK,CAAC2D,aAAa,EAAE,EACrB3D,KAAK,CAAC4D,qBAAqB,EAAE,EAC7B5D,KAAK,CAAC6D,QAAQ,EAAE,CAACC,aAAa,CAACE,KAAK,CACrC,EACD,CAACC,UAAU,EAAEvB,WAAW,EAAEsB,KAAK,KAAK;EAAA,MAAA,IAAAiB,kBAAA,CAAA;EAClC,MAAA,MAAMF,kBAAkB,GAAA,CAAAE,kBAAA,GACtBjB,KAAK,IAALA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,KAAK,CACDT,GAAG,CAACrD,QAAQ,IAAIwC,WAAW,CAAC2B,IAAI,CAAC5H,CAAC,IAAIA,CAAC,CAACgE,EAAE,KAAKP,QAAQ,CAAE,CAAC,CAC3DoE,MAAM,CAACC,OAAO,CAAC,KAAAU,IAAAA,GAAAA,kBAAA,GAAI,EAAE,CAAA;QAE1B,OAAON,iBAAiB,CAACV,UAAU,EAAEc,kBAAkB,EAAE/E,KAAK,EAAE,OAAO,CAAC,CAAA;OACzE,EACDN,cAAc,CAACM,KAAK,CAACO,OAAO,EAAEpC,KAAK,EAAE,sBAAsB,CAC7D,CAAC,CAAA;;EAED;;EAEA6B,IAAAA,KAAK,CAACkF,eAAe,GAAGvH,IAAI,CAC1B,MAAM,CAACqC,KAAK,CAAC0D,eAAe,EAAE,CAAC,EAC/BgB,YAAY,IAAI;EACd,MAAA,OAAO,CAAC,GAAGA,YAAY,CAAC,CAACS,OAAO,EAAE,CAAA;OACnC,EACDzF,cAAc,CAACM,KAAK,CAACO,OAAO,EAAEpC,KAAK,EAAE,iBAAiB,CACxD,CAAC,CAAA;EAED6B,IAAAA,KAAK,CAACoF,mBAAmB,GAAGzH,IAAI,CAC9B,MAAM,CAACqC,KAAK,CAAC6E,mBAAmB,EAAE,CAAC,EACnCH,YAAY,IAAI;EACd,MAAA,OAAO,CAAC,GAAGA,YAAY,CAAC,CAACS,OAAO,EAAE,CAAA;OACnC,EACDzF,cAAc,CAACM,KAAK,CAACO,OAAO,EAAEpC,KAAK,EAAE,qBAAqB,CAC5D,CAAC,CAAA;EAED6B,IAAAA,KAAK,CAACqF,qBAAqB,GAAG1H,IAAI,CAChC,MAAM,CAACqC,KAAK,CAAC4E,qBAAqB,EAAE,CAAC,EACrCF,YAAY,IAAI;EACd,MAAA,OAAO,CAAC,GAAGA,YAAY,CAAC,CAACS,OAAO,EAAE,CAAA;OACnC,EACDzF,cAAc,CAACM,KAAK,CAACO,OAAO,EAAEpC,KAAK,EAAE,uBAAuB,CAC9D,CAAC,CAAA;EAED6B,IAAAA,KAAK,CAACsF,oBAAoB,GAAG3H,IAAI,CAC/B,MAAM,CAACqC,KAAK,CAACgF,oBAAoB,EAAE,CAAC,EACpCN,YAAY,IAAI;EACd,MAAA,OAAO,CAAC,GAAGA,YAAY,CAAC,CAACS,OAAO,EAAE,CAAA;OACnC,EACDzF,cAAc,CAACM,KAAK,CAACO,OAAO,EAAEpC,KAAK,EAAE,sBAAsB,CAC7D,CAAC,CAAA;;EAED;;EAEA6B,IAAAA,KAAK,CAACuF,cAAc,GAAG5H,IAAI,CACzB,MAAM,CAACqC,KAAK,CAAC0D,eAAe,EAAE,CAAC,EAC/BgB,YAAY,IAAI;EACd,MAAA,OAAOA,YAAY,CAChBnB,GAAG,CAACL,WAAW,IAAI;UAClB,OAAOA,WAAW,CAACsC,OAAO,CAAA;EAC5B,OAAC,CAAC,CACDrI,IAAI,EAAE,CAAA;OACV,EACDuC,cAAc,CAACM,KAAK,CAACO,OAAO,EAAEpC,KAAK,EAAE,gBAAgB,CACvD,CAAC,CAAA;EAED6B,IAAAA,KAAK,CAACyF,kBAAkB,GAAG9H,IAAI,CAC7B,MAAM,CAACqC,KAAK,CAAC6E,mBAAmB,EAAE,CAAC,EACnCd,IAAI,IAAI;EACN,MAAA,OAAOA,IAAI,CACRR,GAAG,CAACL,WAAW,IAAI;UAClB,OAAOA,WAAW,CAACsC,OAAO,CAAA;EAC5B,OAAC,CAAC,CACDrI,IAAI,EAAE,CAAA;OACV,EACDuC,cAAc,CAACM,KAAK,CAACO,OAAO,EAAEpC,KAAK,EAAE,oBAAoB,CAC3D,CAAC,CAAA;EAED6B,IAAAA,KAAK,CAAC0F,oBAAoB,GAAG/H,IAAI,CAC/B,MAAM,CAACqC,KAAK,CAAC4E,qBAAqB,EAAE,CAAC,EACrCb,IAAI,IAAI;EACN,MAAA,OAAOA,IAAI,CACRR,GAAG,CAACL,WAAW,IAAI;UAClB,OAAOA,WAAW,CAACsC,OAAO,CAAA;EAC5B,OAAC,CAAC,CACDrI,IAAI,EAAE,CAAA;OACV,EACDuC,cAAc,CAACM,KAAK,CAACO,OAAO,EAAEpC,KAAK,EAAE,sBAAsB,CAC7D,CAAC,CAAA;EAED6B,IAAAA,KAAK,CAAC2F,mBAAmB,GAAGhI,IAAI,CAC9B,MAAM,CAACqC,KAAK,CAACgF,oBAAoB,EAAE,CAAC,EACpCjB,IAAI,IAAI;EACN,MAAA,OAAOA,IAAI,CACRR,GAAG,CAACL,WAAW,IAAI;UAClB,OAAOA,WAAW,CAACsC,OAAO,CAAA;EAC5B,OAAC,CAAC,CACDrI,IAAI,EAAE,CAAA;OACV,EACDuC,cAAc,CAACM,KAAK,CAACO,OAAO,EAAEpC,KAAK,EAAE,qBAAqB,CAC5D,CAAC,CAAA;;EAED;;EAEA6B,IAAAA,KAAK,CAAC4F,oBAAoB,GAAGjI,IAAI,CAC/B,MAAM,CAACqC,KAAK,CAAC0F,oBAAoB,EAAE,CAAC,EACpCG,WAAW,IAAI;EACb,MAAA,OAAOA,WAAW,CAACvB,MAAM,CAAC3C,MAAM,IAAA;EAAA,QAAA,IAAAmE,kBAAA,CAAA;UAAA,OAAI,EAAA,CAAAA,kBAAA,GAACnE,MAAM,CAACoB,UAAU,KAAA,IAAA,IAAjB+C,kBAAA,CAAmBpI,MAAM,CAAA,CAAA;SAAC,CAAA,CAAA;OAChE,EACDgC,cAAc,CAACM,KAAK,CAACO,OAAO,EAAEpC,KAAK,EAAE,sBAAsB,CAC7D,CAAC,CAAA;EAED6B,IAAAA,KAAK,CAAC+F,kBAAkB,GAAGpI,IAAI,CAC7B,MAAM,CAACqC,KAAK,CAACyF,kBAAkB,EAAE,CAAC,EAClCI,WAAW,IAAI;EACb,MAAA,OAAOA,WAAW,CAACvB,MAAM,CAAC3C,MAAM,IAAA;EAAA,QAAA,IAAAqE,mBAAA,CAAA;UAAA,OAAI,EAAA,CAAAA,mBAAA,GAACrE,MAAM,CAACoB,UAAU,KAAA,IAAA,IAAjBiD,mBAAA,CAAmBtI,MAAM,CAAA,CAAA;SAAC,CAAA,CAAA;OAChE,EACDgC,cAAc,CAACM,KAAK,CAACO,OAAO,EAAEpC,KAAK,EAAE,oBAAoB,CAC3D,CAAC,CAAA;EAED6B,IAAAA,KAAK,CAACiG,mBAAmB,GAAGtI,IAAI,CAC9B,MAAM,CAACqC,KAAK,CAAC2F,mBAAmB,EAAE,CAAC,EACnCE,WAAW,IAAI;EACb,MAAA,OAAOA,WAAW,CAACvB,MAAM,CAAC3C,MAAM,IAAA;EAAA,QAAA,IAAAuE,mBAAA,CAAA;UAAA,OAAI,EAAA,CAAAA,mBAAA,GAACvE,MAAM,CAACoB,UAAU,KAAA,IAAA,IAAjBmD,mBAAA,CAAmBxI,MAAM,CAAA,CAAA;SAAC,CAAA,CAAA;OAChE,EACDgC,cAAc,CAACM,KAAK,CAACO,OAAO,EAAEpC,KAAK,EAAE,qBAAqB,CAC5D,CAAC,CAAA;EAED6B,IAAAA,KAAK,CAACmD,cAAc,GAAGxF,IAAI,CACzB,MAAM,CACJqC,KAAK,CAAC6E,mBAAmB,EAAE,EAC3B7E,KAAK,CAAC4E,qBAAqB,EAAE,EAC7B5E,KAAK,CAACgF,oBAAoB,EAAE,CAC7B,EACD,CAACjB,IAAI,EAAEoC,MAAM,EAAEnC,KAAK,KAAK;QAAA,IAAAoC,eAAA,EAAAC,MAAA,EAAAC,iBAAA,EAAAC,QAAA,EAAAC,gBAAA,EAAAC,OAAA,CAAA;EACvB,MAAA,OAAO,CACL,IAAA,CAAAL,eAAA,GAAA,CAAAC,MAAA,GAAItC,IAAI,CAAC,CAAC,CAAC,KAAPsC,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,MAAA,CAASb,OAAO,KAAAY,IAAAA,GAAAA,eAAA,GAAI,EAAE,GAC1B,IAAAE,CAAAA,iBAAA,GAAAC,CAAAA,QAAA,GAAIJ,MAAM,CAAC,CAAC,CAAC,KAATI,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,QAAA,CAAWf,OAAO,KAAAc,IAAAA,GAAAA,iBAAA,GAAI,EAAE,GAC5B,IAAAE,CAAAA,gBAAA,GAAAC,CAAAA,OAAA,GAAIzC,KAAK,CAAC,CAAC,CAAC,KAAA,IAAA,GAAA,KAAA,CAAA,GAARyC,OAAA,CAAUjB,OAAO,KAAA,IAAA,GAAAgB,gBAAA,GAAI,EAAE,EAC5B,CACEjD,GAAG,CAAC5B,MAAM,IAAI;EACb,QAAA,OAAOA,MAAM,CAACwB,cAAc,EAAE,CAAA;EAChC,OAAC,CAAC,CACDhG,IAAI,EAAE,CAAA;OACV,EACDuC,cAAc,CAACM,KAAK,CAACO,OAAO,EAAEpC,KAAK,EAAE,gBAAgB,CACvD,CAAC,CAAA;EACH,GAAA;EACF,EAAC;EAEM,SAASwG,iBAAiBA,CAC/BV,UAAoC,EACpCyC,cAAwC,EACxC1G,KAAmB,EACnB2G,YAA0C,EAC1C;IAAA,IAAAC,qBAAA,EAAAC,cAAA,CAAA;EACA;EACA;EACA;EACA;EACA;;IAEA,IAAIC,QAAQ,GAAG,CAAC,CAAA;EAEhB,EAAA,MAAMC,YAAY,GAAG,UAAC7E,OAAiC,EAAElB,KAAK,EAAS;EAAA,IAAA,IAAdA,KAAK,KAAA,KAAA,CAAA,EAAA;EAALA,MAAAA,KAAK,GAAG,CAAC,CAAA;EAAA,KAAA;MAChE8F,QAAQ,GAAGhI,IAAI,CAACU,GAAG,CAACsH,QAAQ,EAAE9F,KAAK,CAAC,CAAA;EAEpCkB,IAAAA,OAAO,CACJoC,MAAM,CAAC5I,MAAM,IAAIA,MAAM,CAACsL,YAAY,EAAE,CAAC,CACvC1J,OAAO,CAAC5B,MAAM,IAAI;EAAA,MAAA,IAAA0G,eAAA,CAAA;QACjB,IAAAA,CAAAA,eAAA,GAAI1G,MAAM,CAACwG,OAAO,KAAdE,IAAAA,IAAAA,eAAA,CAAgB1E,MAAM,EAAE;UAC1BqJ,YAAY,CAACrL,MAAM,CAACwG,OAAO,EAAElB,KAAK,GAAG,CAAC,CAAC,CAAA;EACzC,OAAA;OACD,EAAE,CAAC,CAAC,CAAA;KACR,CAAA;IAED+F,YAAY,CAAC9C,UAAU,CAAC,CAAA;IAExB,IAAIS,YAAkC,GAAG,EAAE,CAAA;EAE3C,EAAA,MAAMuC,iBAAiB,GAAGA,CACxBC,cAAwC,EACxClG,KAAa,KACV;EACH;EACA,IAAA,MAAMkC,WAA+B,GAAG;QACtClC,KAAK;EACLP,MAAAA,EAAE,EAAE,CAACkG,YAAY,EAAE,CAAA,EAAG3F,KAAK,CAAE,CAAA,CAAC,CAACsD,MAAM,CAACC,OAAO,CAAC,CAAC4C,IAAI,CAAC,GAAG,CAAC;EACxD3B,MAAAA,OAAO,EAAE,EAAA;OACV,CAAA;;EAED;MACA,MAAM4B,oBAA8C,GAAG,EAAE,CAAA;;EAEzD;EACAF,IAAAA,cAAc,CAAC5J,OAAO,CAAC+J,aAAa,IAAI;EACtC;;EAEA,MAAA,MAAMC,yBAAyB,GAAG,CAAC,GAAGF,oBAAoB,CAAC,CAACjC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAA;QAExE,MAAMoC,YAAY,GAAGF,aAAa,CAAC3L,MAAM,CAACsF,KAAK,KAAKkC,WAAW,CAAClC,KAAK,CAAA;EAErE,MAAA,IAAItF,MAA8B,CAAA;QAClC,IAAImH,aAAa,GAAG,KAAK,CAAA;EAEzB,MAAA,IAAI0E,YAAY,IAAIF,aAAa,CAAC3L,MAAM,CAACuF,MAAM,EAAE;EAC/C;EACAvF,QAAAA,MAAM,GAAG2L,aAAa,CAAC3L,MAAM,CAACuF,MAAM,CAAA;EACtC,OAAC,MAAM;EACL;UACAvF,MAAM,GAAG2L,aAAa,CAAC3L,MAAM,CAAA;EAC7BmH,QAAAA,aAAa,GAAG,IAAI,CAAA;EACtB,OAAA;QAEA,IACEyE,yBAAyB,IACzB,CAAAA,yBAAyB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAzBA,yBAAyB,CAAE5L,MAAM,MAAKA,MAAM,EAC5C;EACA;EACA4L,QAAAA,yBAAyB,CAACvE,UAAU,CAACvF,IAAI,CAAC6J,aAAa,CAAC,CAAA;EAC1D,OAAC,MAAM;EACL;EACA,QAAA,MAAM1F,MAAM,GAAGgB,YAAY,CAAC3C,KAAK,EAAEtE,MAAM,EAAE;YACzC+E,EAAE,EAAE,CAACkG,YAAY,EAAE3F,KAAK,EAAEtF,MAAM,CAAC+E,EAAE,EAAE4G,aAAa,IAAA,IAAA,GAAA,KAAA,CAAA,GAAbA,aAAa,CAAE5G,EAAE,CAAC,CACpD6D,MAAM,CAACC,OAAO,CAAC,CACf4C,IAAI,CAAC,GAAG,CAAC;YACZtE,aAAa;YACbC,aAAa,EAAED,aAAa,GACxB,CAAA,EAAGuE,oBAAoB,CAAC9C,MAAM,CAAC7H,CAAC,IAAIA,CAAC,CAACf,MAAM,KAAKA,MAAM,CAAC,CAACgC,MAAM,CAAA,CAAE,GACjEgE,SAAS;YACbV,KAAK;YACLtC,KAAK,EAAE0I,oBAAoB,CAAC1J,MAAAA;EAC9B,SAAC,CAAC,CAAA;;EAEF;EACAiE,QAAAA,MAAM,CAACoB,UAAU,CAACvF,IAAI,CAAC6J,aAAa,CAAC,CAAA;EACrC;EACA;EACAD,QAAAA,oBAAoB,CAAC5J,IAAI,CAACmE,MAAM,CAAC,CAAA;EACnC,OAAA;EAEAuB,MAAAA,WAAW,CAACsC,OAAO,CAAChI,IAAI,CAAC6J,aAAa,CAAC,CAAA;QACvCA,aAAa,CAACnE,WAAW,GAAGA,WAAW,CAAA;EACzC,KAAC,CAAC,CAAA;EAEFwB,IAAAA,YAAY,CAAClH,IAAI,CAAC0F,WAAW,CAAC,CAAA;MAE9B,IAAIlC,KAAK,GAAG,CAAC,EAAE;EACbiG,MAAAA,iBAAiB,CAACG,oBAAoB,EAAEpG,KAAK,GAAG,CAAC,CAAC,CAAA;EACpD,KAAA;KACD,CAAA;EAED,EAAA,MAAMwG,aAAa,GAAGd,cAAc,CAACnD,GAAG,CAAC,CAAC7H,MAAM,EAAEgD,KAAK,KACrDiE,YAAY,CAAC3C,KAAK,EAAEtE,MAAM,EAAE;EAC1BsF,IAAAA,KAAK,EAAE8F,QAAQ;EACfpI,IAAAA,KAAAA;EACF,GAAC,CACH,CAAC,CAAA;EAEDuI,EAAAA,iBAAiB,CAACO,aAAa,EAAEV,QAAQ,GAAG,CAAC,CAAC,CAAA;IAE9CpC,YAAY,CAACS,OAAO,EAAE,CAAA;;EAEtB;EACA;EACA;;IAEA,MAAMsC,sBAAsB,GAC1BjC,OAAiC,IACU;EAC3C,IAAA,MAAMkC,eAAe,GAAGlC,OAAO,CAAClB,MAAM,CAAC3C,MAAM,IAC3CA,MAAM,CAACjG,MAAM,CAACsL,YAAY,EAC5B,CAAC,CAAA;EAED,IAAA,OAAOU,eAAe,CAACnE,GAAG,CAAC5B,MAAM,IAAI;QACnC,IAAIqB,OAAO,GAAG,CAAC,CAAA;QACf,IAAIC,OAAO,GAAG,CAAC,CAAA;EACf,MAAA,IAAI0E,aAAa,GAAG,CAAC,CAAC,CAAC,CAAA;QAEvB,IAAIhG,MAAM,CAACoB,UAAU,IAAIpB,MAAM,CAACoB,UAAU,CAACrF,MAAM,EAAE;EACjDiK,QAAAA,aAAa,GAAG,EAAE,CAAA;UAElBF,sBAAsB,CAAC9F,MAAM,CAACoB,UAAU,CAAC,CAACzF,OAAO,CAC/C4D,IAAA,IAAsD;YAAA,IAArD;EAAE8B,YAAAA,OAAO,EAAE4E,YAAY;EAAE3E,YAAAA,OAAO,EAAE4E,YAAAA;EAAa,WAAC,GAAA3G,IAAA,CAAA;EAC/C8B,UAAAA,OAAO,IAAI4E,YAAY,CAAA;EACvBD,UAAAA,aAAa,CAACnK,IAAI,CAACqK,YAAY,CAAC,CAAA;EAClC,SACF,CAAC,CAAA;EACH,OAAC,MAAM;EACL7E,QAAAA,OAAO,GAAG,CAAC,CAAA;EACb,OAAA;QAEA,MAAM8E,eAAe,GAAGhJ,IAAI,CAACW,GAAG,CAAC,GAAGkI,aAAa,CAAC,CAAA;QAClD1E,OAAO,GAAGA,OAAO,GAAG6E,eAAe,CAAA;QAEnCnG,MAAM,CAACqB,OAAO,GAAGA,OAAO,CAAA;QACxBrB,MAAM,CAACsB,OAAO,GAAGA,OAAO,CAAA;QAExB,OAAO;UAAED,OAAO;EAAEC,QAAAA,OAAAA;SAAS,CAAA;EAC7B,KAAC,CAAC,CAAA;KACH,CAAA;EAEDwE,EAAAA,sBAAsB,EAAAb,qBAAA,GAAA,CAAAC,cAAA,GAACnC,YAAY,CAAC,CAAC,CAAC,KAAA,IAAA,GAAA,KAAA,CAAA,GAAfmC,cAAA,CAAiBrB,OAAO,YAAAoB,qBAAA,GAAI,EAAE,CAAC,CAAA;EAEtD,EAAA,OAAOlC,YAAY,CAAA;EACrB;;QChiBaqD,SAAS,GAAGA,CACvB/H,KAAmB,EACnBS,EAAU,EACVuH,QAAe,EACfC,QAAgB,EAChBjH,KAAa,EACbkH,OAAsB,EACtBC,QAAiB,KACF;EACf,EAAA,IAAIlI,GAAmB,GAAG;MACxBQ,EAAE;EACF/B,IAAAA,KAAK,EAAEuJ,QAAQ;MACfD,QAAQ;MACRhH,KAAK;MACLmH,QAAQ;MACRC,YAAY,EAAE,EAAE;MAChBC,kBAAkB,EAAE,EAAE;MACtB/H,QAAQ,EAAEJ,QAAQ,IAAI;QACpB,IAAID,GAAG,CAACmI,YAAY,CAACE,cAAc,CAACpI,QAAQ,CAAC,EAAE;EAC7C,QAAA,OAAOD,GAAG,CAACmI,YAAY,CAAClI,QAAQ,CAAC,CAAA;EACnC,OAAA;EAEA,MAAA,MAAMxE,MAAM,GAAGsE,KAAK,CAACuI,SAAS,CAACrI,QAAQ,CAAC,CAAA;EAExC,MAAA,IAAI,EAACxE,MAAM,IAAA,IAAA,IAANA,MAAM,CAAEC,UAAU,CAAE,EAAA;EACvB,QAAA,OAAO+F,SAAS,CAAA;EAClB,OAAA;EAEAzB,MAAAA,GAAG,CAACmI,YAAY,CAAClI,QAAQ,CAAC,GAAGxE,MAAM,CAACC,UAAU,CAC5CsE,GAAG,CAAC+H,QAAQ,EACZC,QACF,CAAC,CAAA;EAED,MAAA,OAAOhI,GAAG,CAACmI,YAAY,CAAClI,QAAQ,CAAC,CAAA;OAClC;MACDsI,eAAe,EAAEtI,QAAQ,IAAI;QAC3B,IAAID,GAAG,CAACoI,kBAAkB,CAACC,cAAc,CAACpI,QAAQ,CAAC,EAAE;EACnD,QAAA,OAAOD,GAAG,CAACoI,kBAAkB,CAACnI,QAAQ,CAAC,CAAA;EACzC,OAAA;EAEA,MAAA,MAAMxE,MAAM,GAAGsE,KAAK,CAACuI,SAAS,CAACrI,QAAQ,CAAC,CAAA;EAExC,MAAA,IAAI,EAACxE,MAAM,IAAA,IAAA,IAANA,MAAM,CAAEC,UAAU,CAAE,EAAA;EACvB,QAAA,OAAO+F,SAAS,CAAA;EAClB,OAAA;EAEA,MAAA,IAAI,CAAChG,MAAM,CAACqF,SAAS,CAACyH,eAAe,EAAE;EACrCvI,QAAAA,GAAG,CAACoI,kBAAkB,CAACnI,QAAQ,CAAC,GAAG,CAACD,GAAG,CAACK,QAAQ,CAACJ,QAAQ,CAAC,CAAC,CAAA;EAC3D,QAAA,OAAOD,GAAG,CAACoI,kBAAkB,CAACnI,QAAQ,CAAC,CAAA;EACzC,OAAA;EAEAD,MAAAA,GAAG,CAACoI,kBAAkB,CAACnI,QAAQ,CAAC,GAAGxE,MAAM,CAACqF,SAAS,CAACyH,eAAe,CACjEvI,GAAG,CAAC+H,QAAQ,EACZC,QACF,CAAC,CAAA;EAED,MAAA,OAAOhI,GAAG,CAACoI,kBAAkB,CAACnI,QAAQ,CAAC,CAAA;OACxC;EACDQ,IAAAA,WAAW,EAAER,QAAQ,IAAA;EAAA,MAAA,IAAAuI,aAAA,CAAA;EAAA,MAAA,OAAA,CAAAA,aAAA,GACnBxI,GAAG,CAACK,QAAQ,CAACJ,QAAQ,CAAC,KAAA,IAAA,GAAAuI,aAAA,GAAIzI,KAAK,CAACO,OAAO,CAACC,mBAAmB,CAAA;EAAA,KAAA;EAC7D0H,IAAAA,OAAO,EAAEA,OAAO,IAAPA,IAAAA,GAAAA,OAAO,GAAI,EAAE;EACtBQ,IAAAA,WAAW,EAAEA,MAAM1L,SAAS,CAACiD,GAAG,CAACiI,OAAO,EAAEzL,CAAC,IAAIA,CAAC,CAACyL,OAAO,CAAC;EACzDS,IAAAA,YAAY,EAAEA,MACZ1I,GAAG,CAACkI,QAAQ,GAAGnI,KAAK,CAAC4I,MAAM,CAAC3I,GAAG,CAACkI,QAAQ,EAAE,IAAI,CAAC,GAAGzG,SAAS;MAC7DmH,aAAa,EAAEA,MAAM;QACnB,IAAIC,UAAwB,GAAG,EAAE,CAAA;QACjC,IAAIC,UAAU,GAAG9I,GAAG,CAAA;EACpB,MAAA,OAAO,IAAI,EAAE;EACX,QAAA,MAAM+I,SAAS,GAAGD,UAAU,CAACJ,YAAY,EAAE,CAAA;UAC3C,IAAI,CAACK,SAAS,EAAE,MAAA;EAChBF,QAAAA,UAAU,CAACtL,IAAI,CAACwL,SAAS,CAAC,CAAA;EAC1BD,QAAAA,UAAU,GAAGC,SAAS,CAAA;EACxB,OAAA;EACA,MAAA,OAAOF,UAAU,CAAC3D,OAAO,EAAE,CAAA;OAC5B;EACD8D,IAAAA,WAAW,EAAEtL,IAAI,CACf,MAAM,CAACqC,KAAK,CAACkJ,iBAAiB,EAAE,CAAC,EACjCxG,WAAW,IAAI;EACb,MAAA,OAAOA,WAAW,CAACa,GAAG,CAAC7H,MAAM,IAAI;UAC/B,OAAOqE,UAAU,CAACC,KAAK,EAAEC,GAAG,EAAgBvE,MAAM,EAAEA,MAAM,CAAC+E,EAAE,CAAC,CAAA;EAChE,OAAC,CAAC,CAAA;OACH,EACDf,cAAc,CAACM,KAAK,CAACO,OAAO,EAAE,WAAW,EAAE,aAAa,CAC1D,CAAC;EAED4I,IAAAA,sBAAsB,EAAExL,IAAI,CAC1B,MAAM,CAACsC,GAAG,CAACgJ,WAAW,EAAE,CAAC,EACzBG,QAAQ,IAAI;QACV,OAAOA,QAAQ,CAACC,MAAM,CACpB,CAACC,GAAG,EAAEjJ,IAAI,KAAK;UACbiJ,GAAG,CAACjJ,IAAI,CAAC3E,MAAM,CAAC+E,EAAE,CAAC,GAAGJ,IAAI,CAAA;EAC1B,QAAA,OAAOiJ,GAAG,CAAA;SACX,EACD,EACF,CAAC,CAAA;OACF,EACD5J,cAAc,CAACM,KAAK,CAACO,OAAO,EAAE,WAAW,EAAE,uBAAuB,CACpE,CAAA;KACD,CAAA;EAED,EAAA,KAAK,IAAIgJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvJ,KAAK,CAACY,SAAS,CAAClD,MAAM,EAAE6L,CAAC,EAAE,EAAE;EAC/C,IAAA,MAAM1I,OAAO,GAAGb,KAAK,CAACY,SAAS,CAAC2I,CAAC,CAAC,CAAA;EAClC1I,IAAAA,OAAO,IAAPA,IAAAA,IAAAA,OAAO,CAAEkH,SAAS,IAAlBlH,IAAAA,IAAAA,OAAO,CAAEkH,SAAS,CAAG9H,GAAG,EAAgBD,KAAK,CAAC,CAAA;EAChD,GAAA;EAEA,EAAA,OAAOC,GAAG,CAAA;EACZ;;EC3JA;;AAEO,QAAMuJ,cAA4B,GAAG;EAC1C1I,EAAAA,YAAY,EAAEA,CACZpF,MAA8B,EAC9BsE,KAAmB,KACV;MACTtE,MAAM,CAAC+N,mBAAmB,GACxBzJ,KAAK,CAACO,OAAO,CAACmJ,kBAAkB,IAChC1J,KAAK,CAACO,OAAO,CAACmJ,kBAAkB,CAAC1J,KAAK,EAAEtE,MAAM,CAAC+E,EAAE,CAAC,CAAA;MACpD/E,MAAM,CAACgO,kBAAkB,GAAG,MAAM;EAChC,MAAA,IAAI,CAAChO,MAAM,CAAC+N,mBAAmB,EAAE;EAC/B,QAAA,OAAOzJ,KAAK,CAAC2J,sBAAsB,EAAE,CAAA;EACvC,OAAA;EAEA,MAAA,OAAOjO,MAAM,CAAC+N,mBAAmB,EAAE,CAAA;OACpC,CAAA;MACD/N,MAAM,CAACkO,uBAAuB,GAC5B5J,KAAK,CAACO,OAAO,CAACsJ,sBAAsB,IACpC7J,KAAK,CAACO,OAAO,CAACsJ,sBAAsB,CAAC7J,KAAK,EAAEtE,MAAM,CAAC+E,EAAE,CAAC,CAAA;MACxD/E,MAAM,CAACmO,sBAAsB,GAAG,MAAM;EACpC,MAAA,IAAI,CAACnO,MAAM,CAACkO,uBAAuB,EAAE;UACnC,OAAO,IAAIE,GAAG,EAAE,CAAA;EAClB,OAAA;EAEA,MAAA,OAAOpO,MAAM,CAACkO,uBAAuB,EAAE,CAAA;OACxC,CAAA;MACDlO,MAAM,CAACqO,uBAAuB,GAC5B/J,KAAK,CAACO,OAAO,CAACyJ,sBAAsB,IACpChK,KAAK,CAACO,OAAO,CAACyJ,sBAAsB,CAAChK,KAAK,EAAEtE,MAAM,CAAC+E,EAAE,CAAC,CAAA;MACxD/E,MAAM,CAACsO,sBAAsB,GAAG,MAAM;EACpC,MAAA,IAAI,CAACtO,MAAM,CAACqO,uBAAuB,EAAE;EACnC,QAAA,OAAOrI,SAAS,CAAA;EAClB,OAAA;EAEA,MAAA,OAAOhG,MAAM,CAACqO,uBAAuB,EAAE,CAAA;OACxC,CAAA;EACH,GAAA;EACF;;ECjFA,MAAME,cAA6B,GAAGA,CACpChK,GAAG,EACHC,QAAgB,EAChBgK,WAAmB,KAChB;IAAA,IAAAC,qBAAA,EAAA1B,aAAA,CAAA;EACH,EAAA,MAAM2B,MAAM,GAAGF,WAAW,IAAAC,IAAAA,IAAAA,CAAAA,qBAAA,GAAXD,WAAW,CAAEG,QAAQ,EAAE,KAAvBF,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,qBAAA,CAAyBG,WAAW,EAAE,CAAA;EACrD,EAAA,OAAO/F,OAAO,CAAA,CAAAkE,aAAA,GACZxI,GAAG,CACAK,QAAQ,CAAgBJ,QAAQ,CAAC,KAAA,IAAA,IAAA,CAAAuI,aAAA,GADpCA,aAAA,CAEI4B,QAAQ,EAAE,KAAA5B,IAAAA,IAAAA,CAAAA,aAAA,GAFdA,aAAA,CAGI6B,WAAW,EAAE,KAAA,IAAA,GAAA,KAAA,CAAA,GAHjB7B,aAAA,CAII7G,QAAQ,CAACwI,MAAM,CACrB,CAAC,CAAA;EACH,CAAC,CAAA;EAEDH,cAAc,CAACM,UAAU,GAAIxN,GAAQ,IAAKyN,UAAU,CAACzN,GAAG,CAAC,CAAA;EAEzD,MAAM0N,uBAAsC,GAAGA,CAC7CxK,GAAG,EACHC,QAAgB,EAChBgK,WAAmB,KAChB;EAAA,EAAA,IAAAQ,cAAA,CAAA;IACH,OAAOnG,OAAO,CAAAmG,CAAAA,cAAA,GACZzK,GAAG,CAACK,QAAQ,CAAgBJ,QAAQ,CAAC,KAAAwK,IAAAA,IAAAA,CAAAA,cAAA,GAArCA,cAAA,CAAuCL,QAAQ,EAAE,KAAA,IAAA,GAAA,KAAA,CAAA,GAAjDK,cAAA,CAAmD9I,QAAQ,CAACsI,WAAW,CACzE,CAAC,CAAA;EACH,CAAC,CAAA;EAEDO,uBAAuB,CAACF,UAAU,GAAIxN,GAAQ,IAAKyN,UAAU,CAACzN,GAAG,CAAC,CAAA;EAElE,MAAM4N,YAA2B,GAAGA,CAClC1K,GAAG,EACHC,QAAgB,EAChBgK,WAAmB,KAChB;EAAA,EAAA,IAAAU,cAAA,CAAA;EACH,EAAA,OACE,CAAAA,CAAAA,cAAA,GAAA3K,GAAG,CAACK,QAAQ,CAAgBJ,QAAQ,CAAC,KAAA,IAAA,IAAA,CAAA0K,cAAA,GAArCA,cAAA,CAAuCP,QAAQ,EAAE,KAAjDO,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,cAAA,CAAmDN,WAAW,EAAE,OAChEJ,WAAW,IAAXA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,WAAW,CAAEI,WAAW,EAAE,CAAA,CAAA;EAE9B,CAAC,CAAA;EAEDK,YAAY,CAACJ,UAAU,GAAIxN,GAAQ,IAAKyN,UAAU,CAACzN,GAAG,CAAC,CAAA;EAEvD,MAAM8N,WAA0B,GAAGA,CACjC5K,GAAG,EACHC,QAAgB,EAChBgK,WAAoB,KACjB;EAAA,EAAA,IAAAY,cAAA,CAAA;EACH,EAAA,OAAA,CAAAA,cAAA,GAAO7K,GAAG,CAACK,QAAQ,CAAYJ,QAAQ,CAAC,KAAA,IAAA,GAAA,KAAA,CAAA,GAAjC4K,cAAA,CAAmClJ,QAAQ,CAACsI,WAAW,CAAC,CAAA;EACjE,CAAC,CAAA;EAEDW,WAAW,CAACN,UAAU,GAAIxN,GAAQ,IAAKyN,UAAU,CAACzN,GAAG,CAAC,CAAA;EAEtD,MAAMgO,cAA6B,GAAGA,CACpC9K,GAAG,EACHC,QAAgB,EAChBgK,WAAsB,KACnB;EACH,EAAA,OAAO,CAACA,WAAW,CAAC1L,IAAI,CACtBzB,GAAG,IAAA;EAAA,IAAA,IAAAiO,cAAA,CAAA;EAAA,IAAA,OAAI,EAAAA,CAAAA,cAAA,GAAC/K,GAAG,CAACK,QAAQ,CAAYJ,QAAQ,CAAC,aAAjC8K,cAAA,CAAmCpJ,QAAQ,CAAC7E,GAAG,CAAC,CAAA,CAAA;EAAA,GAC1D,CAAC,CAAA;EACH,CAAC,CAAA;EAEDgO,cAAc,CAACR,UAAU,GAAIxN,GAAQ,IAAKyN,UAAU,CAACzN,GAAG,CAAC,IAAI,EAACA,GAAG,IAAHA,IAAAA,IAAAA,GAAG,CAAEW,MAAM,CAAA,CAAA;EAEzE,MAAMuN,eAA8B,GAAGA,CACrChL,GAAG,EACHC,QAAgB,EAChBgK,WAAsB,KACnB;EACH,EAAA,OAAOA,WAAW,CAAC1L,IAAI,CAACzB,GAAG,IAAA;EAAA,IAAA,IAAAmO,cAAA,CAAA;EAAA,IAAA,OAAA,CAAAA,cAAA,GACzBjL,GAAG,CAACK,QAAQ,CAAYJ,QAAQ,CAAC,KAAA,IAAA,GAAA,KAAA,CAAA,GAAjCgL,cAAA,CAAmCtJ,QAAQ,CAAC7E,GAAG,CAAC,CAAA;EAAA,GAClD,CAAC,CAAA;EACH,CAAC,CAAA;EAEDkO,eAAe,CAACV,UAAU,GAAIxN,GAAQ,IAAKyN,UAAU,CAACzN,GAAG,CAAC,IAAI,EAACA,GAAG,IAAHA,IAAAA,IAAAA,GAAG,CAAEW,MAAM,CAAA,CAAA;EAE1E,MAAMyN,MAAqB,GAAGA,CAAClL,GAAG,EAAEC,QAAgB,EAAEgK,WAAoB,KAAK;EAC7E,EAAA,OAAOjK,GAAG,CAACK,QAAQ,CAACJ,QAAQ,CAAC,KAAKgK,WAAW,CAAA;EAC/C,CAAC,CAAA;EAEDiB,MAAM,CAACZ,UAAU,GAAIxN,GAAQ,IAAKyN,UAAU,CAACzN,GAAG,CAAC,CAAA;EAEjD,MAAMqO,UAAyB,GAAGA,CAChCnL,GAAG,EACHC,QAAgB,EAChBgK,WAAoB,KACjB;EACH,EAAA,OAAOjK,GAAG,CAACK,QAAQ,CAACJ,QAAQ,CAAC,IAAIgK,WAAW,CAAA;EAC9C,CAAC,CAAA;EAEDkB,UAAU,CAACb,UAAU,GAAIxN,GAAQ,IAAKyN,UAAU,CAACzN,GAAG,CAAC,CAAA;EAErD,MAAMsO,aAA4B,GAAGA,CACnCpL,GAAG,EACHC,QAAgB,EAChBgK,WAA6B,KAC1B;EACH,EAAA,IAAI,CAACzK,GAAG,EAAED,GAAG,CAAC,GAAG0K,WAAW,CAAA;EAE5B,EAAA,MAAMoB,QAAQ,GAAGrL,GAAG,CAACK,QAAQ,CAASJ,QAAQ,CAAC,CAAA;EAC/C,EAAA,OAAOoL,QAAQ,IAAI7L,GAAG,IAAI6L,QAAQ,IAAI9L,GAAG,CAAA;EAC3C,CAAC,CAAA;EAED6L,aAAa,CAACE,kBAAkB,GAAIxO,GAAe,IAAK;EACtD,EAAA,IAAI,CAACyO,SAAS,EAAEC,SAAS,CAAC,GAAG1O,GAAG,CAAA;EAEhC,EAAA,IAAI2O,SAAS,GACX,OAAOF,SAAS,KAAK,QAAQ,GAAGG,UAAU,CAACH,SAAmB,CAAC,GAAGA,SAAS,CAAA;EAC7E,EAAA,IAAII,SAAS,GACX,OAAOH,SAAS,KAAK,QAAQ,GAAGE,UAAU,CAACF,SAAmB,CAAC,GAAGA,SAAS,CAAA;EAE7E,EAAA,IAAIhM,GAAG,GACL+L,SAAS,KAAK,IAAI,IAAIK,MAAM,CAACC,KAAK,CAACJ,SAAS,CAAC,GAAG,CAACK,QAAQ,GAAGL,SAAS,CAAA;EACvE,EAAA,IAAIlM,GAAG,GAAGiM,SAAS,KAAK,IAAI,IAAII,MAAM,CAACC,KAAK,CAACF,SAAS,CAAC,GAAGG,QAAQ,GAAGH,SAAS,CAAA;IAE9E,IAAInM,GAAG,GAAGD,GAAG,EAAE;MACb,MAAMwM,IAAI,GAAGvM,GAAG,CAAA;EAChBA,IAAAA,GAAG,GAAGD,GAAG,CAAA;EACTA,IAAAA,GAAG,GAAGwM,IAAI,CAAA;EACZ,GAAA;EAEA,EAAA,OAAO,CAACvM,GAAG,EAAED,GAAG,CAAC,CAAA;EACnB,CAAC,CAAA;EAED6L,aAAa,CAACd,UAAU,GAAIxN,GAAQ,IAClCyN,UAAU,CAACzN,GAAG,CAAC,IAAKyN,UAAU,CAACzN,GAAG,CAAC,CAAC,CAAC,CAAC,IAAIyN,UAAU,CAACzN,GAAG,CAAC,CAAC,CAAC,CAAE,CAAA;;EAE/D;;AAEO,QAAMkP,SAAS,GAAG;IACvBhC,cAAc;IACdQ,uBAAuB;IACvBE,YAAY;IACZE,WAAW;IACXE,cAAc;IACdE,eAAe;IACfE,MAAM;IACNC,UAAU;EACVC,EAAAA,aAAAA;EACF,EAAC;EAID;;EAEA,SAASb,UAAUA,CAACzN,GAAQ,EAAE;IAC5B,OAAOA,GAAG,KAAK2E,SAAS,IAAI3E,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,EAAE,CAAA;EACxD;;EC2FA;;AAEO,QAAMmP,eAA6B,GAAG;IAC3CC,mBAAmB,EAAEA,MAEiB;MACpC,OAAO;EACLC,MAAAA,QAAQ,EAAE,MAAA;OACX,CAAA;KACF;IAEDC,eAAe,EAAGC,KAAK,IAA8B;MACnD,OAAO;EACLC,MAAAA,aAAa,EAAE,EAAE;QACjB,GAAGD,KAAAA;OACJ,CAAA;KACF;IAEDE,iBAAiB,EACfxM,KAAmB,IACa;MAChC,OAAO;EACLyM,MAAAA,qBAAqB,EAAEtQ,gBAAgB,CAAC,eAAe,EAAE6D,KAAK,CAAC;EAC/D0M,MAAAA,kBAAkB,EAAE,KAAK;EACzBC,MAAAA,qBAAqB,EAAE,GAAA;OACxB,CAAA;KACF;EAED7L,EAAAA,YAAY,EAAEA,CACZpF,MAA8B,EAC9BsE,KAAmB,KACV;MACTtE,MAAM,CAACkR,eAAe,GAAG,MAAM;QAC7B,MAAMC,QAAQ,GAAG7M,KAAK,CAAC8M,eAAe,EAAE,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAA;QAEpD,MAAMC,KAAK,GAAGH,QAAQ,IAARA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,QAAQ,CAAEvM,QAAQ,CAAC5E,MAAM,CAAC+E,EAAE,CAAC,CAAA;EAE3C,MAAA,IAAI,OAAOuM,KAAK,KAAK,QAAQ,EAAE;UAC7B,OAAOf,SAAS,CAAChC,cAAc,CAAA;EACjC,OAAA;EAEA,MAAA,IAAI,OAAO+C,KAAK,KAAK,QAAQ,EAAE;UAC7B,OAAOf,SAAS,CAACZ,aAAa,CAAA;EAChC,OAAA;EAEA,MAAA,IAAI,OAAO2B,KAAK,KAAK,SAAS,EAAE;UAC9B,OAAOf,SAAS,CAACd,MAAM,CAAA;EACzB,OAAA;QAEA,IAAI6B,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;UAC/C,OAAOf,SAAS,CAACd,MAAM,CAAA;EACzB,OAAA;EAEA,MAAA,IAAIvO,KAAK,CAACC,OAAO,CAACmQ,KAAK,CAAC,EAAE;UACxB,OAAOf,SAAS,CAACpB,WAAW,CAAA;EAC9B,OAAA;QAEA,OAAOoB,SAAS,CAACb,UAAU,CAAA;OAC5B,CAAA;MACD1P,MAAM,CAACuR,WAAW,GAAG,MAAM;QAAA,IAAAC,qBAAA,EAAAC,sBAAA,CAAA;EACzB,MAAA,OAAO3Q,UAAU,CAACd,MAAM,CAACqF,SAAS,CAACqL,QAAQ,CAAC,GACxC1Q,MAAM,CAACqF,SAAS,CAACqL,QAAQ,GACzB1Q,MAAM,CAACqF,SAAS,CAACqL,QAAQ,KAAK,MAAM,GAClC1Q,MAAM,CAACkR,eAAe,EAAE;EACxB,MAAA,CAAAM,qBAAA,GAAA,CAAAC,sBAAA,GACAnN,KAAK,CAACO,OAAO,CAAC0L,SAAS,KAAA,IAAA,GAAA,KAAA,CAAA,GAAvBkB,sBAAA,CAA0BzR,MAAM,CAACqF,SAAS,CAACqL,QAAQ,CAAW,KAAAc,IAAAA,GAAAA,qBAAA,GAC9DjB,SAAS,CAACvQ,MAAM,CAACqF,SAAS,CAACqL,QAAQ,CAAoB,CAAA;OAC9D,CAAA;MACD1Q,MAAM,CAAC0R,YAAY,GAAG,MAAM;EAAA,MAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,CAAA;EAC1B,MAAA,OACE,EAAAF,qBAAA,GAAC3R,MAAM,CAACqF,SAAS,CAACyM,kBAAkB,KAAA,IAAA,GAAAH,qBAAA,GAAI,IAAI,OAAAC,qBAAA,GAC3CtN,KAAK,CAACO,OAAO,CAACkN,mBAAmB,KAAA,IAAA,GAAAH,qBAAA,GAAI,IAAI,CAAC,KAAAC,CAAAA,sBAAA,GAC1CvN,KAAK,CAACO,OAAO,CAACmN,aAAa,YAAAH,sBAAA,GAAI,IAAI,CAAC,IACrC,CAAC,CAAC7R,MAAM,CAACC,UAAU,CAAA;OAEtB,CAAA;MAEDD,MAAM,CAACiS,aAAa,GAAG,MAAMjS,MAAM,CAACkS,cAAc,EAAE,GAAG,CAAC,CAAC,CAAA;MAEzDlS,MAAM,CAACmS,cAAc,GAAG,MAAA;EAAA,MAAA,IAAAC,qBAAA,CAAA;EAAA,MAAA,OAAA,CAAAA,qBAAA,GACtB9N,KAAK,CAAC6D,QAAQ,EAAE,CAAC0I,aAAa,KAAA,IAAA,IAAA,CAAAuB,qBAAA,GAA9BA,qBAAA,CAAgCzJ,IAAI,CAAC5H,CAAC,IAAIA,CAAC,CAACgE,EAAE,KAAK/E,MAAM,CAAC+E,EAAE,CAAC,KAA7DqN,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,qBAAA,CAA+Dd,KAAK,CAAA;EAAA,KAAA,CAAA;MAEtEtR,MAAM,CAACkS,cAAc,GAAG,MAAA;QAAA,IAAAG,sBAAA,EAAAC,sBAAA,CAAA;EAAA,MAAA,OAAA,CAAAD,sBAAA,GAAA,CAAAC,sBAAA,GACtBhO,KAAK,CAAC6D,QAAQ,EAAE,CAAC0I,aAAa,KAAA,IAAA,GAAA,KAAA,CAAA,GAA9ByB,sBAAA,CAAgCC,SAAS,CAACxR,CAAC,IAAIA,CAAC,CAACgE,EAAE,KAAK/E,MAAM,CAAC+E,EAAE,CAAC,KAAA,IAAA,GAAAsN,sBAAA,GAAI,CAAC,CAAC,CAAA;EAAA,KAAA,CAAA;EAE1ErS,IAAAA,MAAM,CAACwS,cAAc,GAAGlB,KAAK,IAAI;EAC/BhN,MAAAA,KAAK,CAACmO,gBAAgB,CAAC5R,GAAG,IAAI;EAC5B,QAAA,MAAM6P,QAAQ,GAAG1Q,MAAM,CAACuR,WAAW,EAAE,CAAA;EACrC,QAAA,MAAMmB,cAAc,GAAG7R,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAE8H,IAAI,CAAC5H,CAAC,IAAIA,CAAC,CAACgE,EAAE,KAAK/E,MAAM,CAAC+E,EAAE,CAAC,CAAA;EAEzD,QAAA,MAAM4N,SAAS,GAAGtS,gBAAgB,CAChCiR,KAAK,EACLoB,cAAc,GAAGA,cAAc,CAACpB,KAAK,GAAGtL,SAC1C,CAAC,CAAA;;EAED;UACA,IACE4M,sBAAsB,CAAClC,QAAQ,EAAqBiC,SAAS,EAAE3S,MAAM,CAAC,EACtE;EAAA,UAAA,IAAA6S,WAAA,CAAA;YACA,OAAAA,CAAAA,WAAA,GAAOhS,GAAG,IAAA,IAAA,GAAA,KAAA,CAAA,GAAHA,GAAG,CAAE+H,MAAM,CAAC7H,CAAC,IAAIA,CAAC,CAACgE,EAAE,KAAK/E,MAAM,CAAC+E,EAAE,CAAC,KAAA,IAAA,GAAA8N,WAAA,GAAI,EAAE,CAAA;EACnD,SAAA;EAEA,QAAA,MAAMC,YAAY,GAAG;YAAE/N,EAAE,EAAE/E,MAAM,CAAC+E,EAAE;EAAEuM,UAAAA,KAAK,EAAEqB,SAAAA;WAAW,CAAA;EAExD,QAAA,IAAID,cAAc,EAAE;EAAA,UAAA,IAAAK,QAAA,CAAA;YAClB,OAAAA,CAAAA,QAAA,GACElS,GAAG,IAAA,IAAA,GAAA,KAAA,CAAA,GAAHA,GAAG,CAAEgH,GAAG,CAAC9G,CAAC,IAAI;EACZ,YAAA,IAAIA,CAAC,CAACgE,EAAE,KAAK/E,MAAM,CAAC+E,EAAE,EAAE;EACtB,cAAA,OAAO+N,YAAY,CAAA;EACrB,aAAA;EACA,YAAA,OAAO/R,CAAC,CAAA;EACV,WAAC,CAAC,KAAA,IAAA,GAAAgS,QAAA,GAAI,EAAE,CAAA;EAEZ,SAAA;EAEA,QAAA,IAAIlS,GAAG,IAAA,IAAA,IAAHA,GAAG,CAAEmB,MAAM,EAAE;EACf,UAAA,OAAO,CAAC,GAAGnB,GAAG,EAAEiS,YAAY,CAAC,CAAA;EAC/B,SAAA;UAEA,OAAO,CAACA,YAAY,CAAC,CAAA;EACvB,OAAC,CAAC,CAAA;OACH,CAAA;KACF;EAEDzG,EAAAA,SAAS,EAAEA,CACT9H,GAAe,EACfyO,MAAoB,KACX;EACTzO,IAAAA,GAAG,CAACsM,aAAa,GAAG,EAAE,CAAA;EACtBtM,IAAAA,GAAG,CAAC0O,iBAAiB,GAAG,EAAE,CAAA;KAC3B;IAEDlL,WAAW,EAA0BzD,KAAmB,IAAW;EACjEA,IAAAA,KAAK,CAACmO,gBAAgB,GAAInS,OAAoC,IAAK;EACjE,MAAA,MAAM0G,WAAW,GAAG1C,KAAK,CAACkJ,iBAAiB,EAAE,CAAA;QAE7C,MAAM0F,QAAQ,GAAIrS,GAAuB,IAAK;EAAA,QAAA,IAAAsS,iBAAA,CAAA;EAC5C,QAAA,OAAA,CAAAA,iBAAA,GAAO9S,gBAAgB,CAACC,OAAO,EAAEO,GAAG,CAAC,KAAA,IAAA,GAAA,KAAA,CAAA,GAA9BsS,iBAAA,CAAgCvK,MAAM,CAACA,MAAM,IAAI;EACtD,UAAA,MAAM5I,MAAM,GAAGgH,WAAW,CAAC2B,IAAI,CAAC5H,CAAC,IAAIA,CAAC,CAACgE,EAAE,KAAK6D,MAAM,CAAC7D,EAAE,CAAC,CAAA;EAExD,UAAA,IAAI/E,MAAM,EAAE;EACV,YAAA,MAAM0Q,QAAQ,GAAG1Q,MAAM,CAACuR,WAAW,EAAE,CAAA;cAErC,IAAIqB,sBAAsB,CAAClC,QAAQ,EAAE9H,MAAM,CAAC0I,KAAK,EAAEtR,MAAM,CAAC,EAAE;EAC1D,cAAA,OAAO,KAAK,CAAA;EACd,aAAA;EACF,WAAA;EAEA,UAAA,OAAO,IAAI,CAAA;EACb,SAAC,CAAC,CAAA;SACH,CAAA;EAEDsE,MAAAA,KAAK,CAACO,OAAO,CAACkM,qBAAqB,IAAnCzM,IAAAA,IAAAA,KAAK,CAACO,OAAO,CAACkM,qBAAqB,CAAGmC,QAAQ,CAAC,CAAA;OAChD,CAAA;EAED5O,IAAAA,KAAK,CAAC8O,kBAAkB,GAAGC,YAAY,IAAI;QAAA,IAAAC,qBAAA,EAAAC,mBAAA,CAAA;QACzCjP,KAAK,CAACmO,gBAAgB,CACpBY,YAAY,GAAG,EAAE,GAAA,CAAAC,qBAAA,GAAA,CAAAC,mBAAA,GAAGjP,KAAK,CAACkP,YAAY,qBAAlBD,mBAAA,CAAoB1C,aAAa,KAAAyC,IAAAA,GAAAA,qBAAA,GAAI,EAC3D,CAAC,CAAA;OACF,CAAA;MAEDhP,KAAK,CAAC2J,sBAAsB,GAAG,MAAM3J,KAAK,CAAC8M,eAAe,EAAE,CAAA;MAC5D9M,KAAK,CAACmP,mBAAmB,GAAG,MAAM;QAChC,IAAI,CAACnP,KAAK,CAACoP,oBAAoB,IAAIpP,KAAK,CAACO,OAAO,CAAC4O,mBAAmB,EAAE;UACpEnP,KAAK,CAACoP,oBAAoB,GAAGpP,KAAK,CAACO,OAAO,CAAC4O,mBAAmB,CAACnP,KAAK,CAAC,CAAA;EACvE,OAAA;QAEA,IAAIA,KAAK,CAACO,OAAO,CAAC8O,eAAe,IAAI,CAACrP,KAAK,CAACoP,oBAAoB,EAAE;EAChE,QAAA,OAAOpP,KAAK,CAAC2J,sBAAsB,EAAE,CAAA;EACvC,OAAA;EAEA,MAAA,OAAO3J,KAAK,CAACoP,oBAAoB,EAAE,CAAA;OACpC,CAAA;EACH,GAAA;EACF,EAAC;EAEM,SAASd,sBAAsBA,CACpClC,QAA0B,EAC1BY,KAAW,EACXtR,MAA+B,EAC/B;EACA,EAAA,OACE,CAAC0Q,QAAQ,IAAIA,QAAQ,CAAC7B,UAAU,GAC5B6B,QAAQ,CAAC7B,UAAU,CAACyC,KAAK,EAAEtR,MAAM,CAAC,GAClC,KAAK,KACT,OAAOsR,KAAK,KAAK,WAAW,IAC3B,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAACA,KAAM,CAAA;EAEzC;;ECzaA,MAAMsC,GAAuB,GAAGA,CAACpP,QAAQ,EAAEqP,SAAS,EAAEC,SAAS,KAAK;EAClE;EACA;IACA,OAAOA,SAAS,CAACnG,MAAM,CAAC,CAACiG,GAAG,EAAEG,IAAI,KAAK;EACrC,IAAA,MAAMC,SAAS,GAAGD,IAAI,CAACnP,QAAQ,CAACJ,QAAQ,CAAC,CAAA;MACzC,OAAOoP,GAAG,IAAI,OAAOI,SAAS,KAAK,QAAQ,GAAGA,SAAS,GAAG,CAAC,CAAC,CAAA;KAC7D,EAAE,CAAC,CAAC,CAAA;EACP,CAAC,CAAA;EAED,MAAMjQ,GAAuB,GAAGA,CAACS,QAAQ,EAAEqP,SAAS,EAAEC,SAAS,KAAK;EAClE,EAAA,IAAI/P,GAAuB,CAAA;EAE3B+P,EAAAA,SAAS,CAAClS,OAAO,CAAC2C,GAAG,IAAI;EACvB,IAAA,MAAM+M,KAAK,GAAG/M,GAAG,CAACK,QAAQ,CAASJ,QAAQ,CAAC,CAAA;EAE5C,IAAA,IACE8M,KAAK,IAAI,IAAI,KACZvN,GAAG,GAAIuN,KAAK,IAAKvN,GAAG,KAAKiC,SAAS,IAAIsL,KAAK,IAAIA,KAAM,CAAC,EACvD;EACAvN,MAAAA,GAAG,GAAGuN,KAAK,CAAA;EACb,KAAA;EACF,GAAC,CAAC,CAAA;EAEF,EAAA,OAAOvN,GAAG,CAAA;EACZ,CAAC,CAAA;EAED,MAAMD,GAAuB,GAAGA,CAACU,QAAQ,EAAEqP,SAAS,EAAEC,SAAS,KAAK;EAClE,EAAA,IAAIhQ,GAAuB,CAAA;EAE3BgQ,EAAAA,SAAS,CAAClS,OAAO,CAAC2C,GAAG,IAAI;EACvB,IAAA,MAAM+M,KAAK,GAAG/M,GAAG,CAACK,QAAQ,CAASJ,QAAQ,CAAC,CAAA;EAC5C,IAAA,IACE8M,KAAK,IAAI,IAAI,KACZxN,GAAG,GAAIwN,KAAK,IAAKxN,GAAG,KAAKkC,SAAS,IAAIsL,KAAK,IAAIA,KAAM,CAAC,EACvD;EACAxN,MAAAA,GAAG,GAAGwN,KAAK,CAAA;EACb,KAAA;EACF,GAAC,CAAC,CAAA;EAEF,EAAA,OAAOxN,GAAG,CAAA;EACZ,CAAC,CAAA;EAED,MAAMmQ,MAA0B,GAAGA,CAACzP,QAAQ,EAAEqP,SAAS,EAAEC,SAAS,KAAK;EACrE,EAAA,IAAI/P,GAAuB,CAAA;EAC3B,EAAA,IAAID,GAAuB,CAAA;EAE3BgQ,EAAAA,SAAS,CAAClS,OAAO,CAAC2C,GAAG,IAAI;EACvB,IAAA,MAAM+M,KAAK,GAAG/M,GAAG,CAACK,QAAQ,CAASJ,QAAQ,CAAC,CAAA;MAC5C,IAAI8M,KAAK,IAAI,IAAI,EAAE;QACjB,IAAIvN,GAAG,KAAKiC,SAAS,EAAE;UACrB,IAAIsL,KAAK,IAAIA,KAAK,EAAEvN,GAAG,GAAGD,GAAG,GAAGwN,KAAK,CAAA;EACvC,OAAC,MAAM;EACL,QAAA,IAAIvN,GAAG,GAAGuN,KAAK,EAAEvN,GAAG,GAAGuN,KAAK,CAAA;EAC5B,QAAA,IAAIxN,GAAG,GAAIwN,KAAK,EAAExN,GAAG,GAAGwN,KAAK,CAAA;EAC/B,OAAA;EACF,KAAA;EACF,GAAC,CAAC,CAAA;EAEF,EAAA,OAAO,CAACvN,GAAG,EAAED,GAAG,CAAC,CAAA;EACnB,CAAC,CAAA;EAED,MAAMoQ,IAAwB,GAAGA,CAAC1P,QAAQ,EAAE2P,QAAQ,KAAK;IACvD,IAAIC,KAAK,GAAG,CAAC,CAAA;IACb,IAAIR,GAAG,GAAG,CAAC,CAAA;EAEXO,EAAAA,QAAQ,CAACvS,OAAO,CAAC2C,GAAG,IAAI;EACtB,IAAA,IAAI+M,KAAK,GAAG/M,GAAG,CAACK,QAAQ,CAASJ,QAAQ,CAAC,CAAA;MAC1C,IAAI8M,KAAK,IAAI,IAAI,IAAI,CAACA,KAAK,GAAG,CAACA,KAAK,KAAKA,KAAK,EAAE;EAC9C,MAAA,EAAE8C,KAAK,EAAGR,GAAG,IAAItC,KAAM,CAAA;EACzB,KAAA;EACF,GAAC,CAAC,CAAA;EAEF,EAAA,IAAI8C,KAAK,EAAE,OAAOR,GAAG,GAAGQ,KAAK,CAAA;EAE7B,EAAA,OAAA;EACF,CAAC,CAAA;EAED,MAAMC,MAA0B,GAAGA,CAAC7P,QAAQ,EAAE2P,QAAQ,KAAK;EACzD,EAAA,IAAI,CAACA,QAAQ,CAACnS,MAAM,EAAE;EACpB,IAAA,OAAA;EACF,GAAA;EAEA,EAAA,MAAMsS,MAAM,GAAGH,QAAQ,CAACtM,GAAG,CAACtD,GAAG,IAAIA,GAAG,CAACK,QAAQ,CAACJ,QAAQ,CAAC,CAAC,CAAA;EAC1D,EAAA,IAAI,CAACvD,aAAa,CAACqT,MAAM,CAAC,EAAE;EAC1B,IAAA,OAAA;EACF,GAAA;EACA,EAAA,IAAIA,MAAM,CAACtS,MAAM,KAAK,CAAC,EAAE;MACvB,OAAOsS,MAAM,CAAC,CAAC,CAAC,CAAA;EAClB,GAAA;IAEA,MAAMC,GAAG,GAAGnR,IAAI,CAACoR,KAAK,CAACF,MAAM,CAACtS,MAAM,GAAG,CAAC,CAAC,CAAA;EACzC,EAAA,MAAMyS,IAAI,GAAGH,MAAM,CAACI,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC,CAAA;IACzC,OAAON,MAAM,CAACtS,MAAM,GAAG,CAAC,KAAK,CAAC,GAAGyS,IAAI,CAACF,GAAG,CAAC,GAAG,CAACE,IAAI,CAACF,GAAG,GAAG,CAAC,CAAC,GAAIE,IAAI,CAACF,GAAG,CAAE,IAAI,CAAC,CAAA;EAChF,CAAC,CAAA;EAED,MAAMM,MAA0B,GAAGA,CAACrQ,QAAQ,EAAE2P,QAAQ,KAAK;IACzD,OAAOjT,KAAK,CAAC4T,IAAI,CAAC,IAAIC,GAAG,CAACZ,QAAQ,CAACtM,GAAG,CAAC9G,CAAC,IAAIA,CAAC,CAAC6D,QAAQ,CAACJ,QAAQ,CAAC,CAAC,CAAC,CAAC8P,MAAM,EAAE,CAAC,CAAA;EAC9E,CAAC,CAAA;EAED,MAAMU,WAA+B,GAAGA,CAACxQ,QAAQ,EAAE2P,QAAQ,KAAK;EAC9D,EAAA,OAAO,IAAIY,GAAG,CAACZ,QAAQ,CAACtM,GAAG,CAAC9G,CAAC,IAAIA,CAAC,CAAC6D,QAAQ,CAACJ,QAAQ,CAAC,CAAC,CAAC,CAACyQ,IAAI,CAAA;EAC9D,CAAC,CAAA;EAED,MAAMb,KAAyB,GAAGA,CAACc,SAAS,EAAEf,QAAQ,KAAK;IACzD,OAAOA,QAAQ,CAACnS,MAAM,CAAA;EACxB,CAAC,CAAA;AAEM,QAAMmT,cAAc,GAAG;IAC5BvB,GAAG;IACH7P,GAAG;IACHD,GAAG;IACHmQ,MAAM;IACNC,IAAI;IACJG,MAAM;IACNQ,MAAM;IACNG,WAAW;EACXZ,EAAAA,KAAAA;EACF;;ECuHA;;AAEO,QAAMgB,cAA4B,GAAG;IAC1C3E,mBAAmB,EAAEA,MAGhB;MACH,OAAO;EACL4E,MAAAA,cAAc,EAAEC,KAAK,IAAA;UAAA,IAAAC,SAAA,EAAAC,eAAA,CAAA;UAAA,OAAAD,CAAAA,SAAA,IAAAC,eAAA,GAAKF,KAAK,CAAC1Q,QAAQ,EAAE,KAAjB4Q,IAAAA,IAAAA,eAAA,CAA2B7G,QAAQ,IAAA,IAAA,GAAA,KAAA,CAAA,GAAnC6G,eAAA,CAA2B7G,QAAQ,EAAI,KAAA,IAAA,GAAA4G,SAAA,GAAI,IAAI,CAAA;EAAA,OAAA;EACxEE,MAAAA,aAAa,EAAE,MAAA;OAChB,CAAA;KACF;IAED9E,eAAe,EAAGC,KAAK,IAAyB;MAC9C,OAAO;EACL8E,MAAAA,QAAQ,EAAE,EAAE;QACZ,GAAG9E,KAAAA;OACJ,CAAA;KACF;IAEDE,iBAAiB,EACfxM,KAAmB,IACC;MACpB,OAAO;EACLqR,MAAAA,gBAAgB,EAAElV,gBAAgB,CAAC,UAAU,EAAE6D,KAAK,CAAC;EACrDsR,MAAAA,iBAAiB,EAAE,SAAA;OACpB,CAAA;KACF;EAEDxQ,EAAAA,YAAY,EAAEA,CACZpF,MAA6B,EAC7BsE,KAAmB,KACV;MACTtE,MAAM,CAAC6V,cAAc,GAAG,MAAM;EAC5BvR,MAAAA,KAAK,CAACwR,WAAW,CAACjV,GAAG,IAAI;EACvB;UACA,IAAIA,GAAG,IAAHA,IAAAA,IAAAA,GAAG,CAAEqF,QAAQ,CAAClG,MAAM,CAAC+E,EAAE,CAAC,EAAE;YAC5B,OAAOlE,GAAG,CAAC+H,MAAM,CAAC7H,CAAC,IAAIA,CAAC,KAAKf,MAAM,CAAC+E,EAAE,CAAC,CAAA;EACzC,SAAA;EAEA,QAAA,OAAO,CAAC,IAAIlE,GAAG,IAAA,IAAA,GAAHA,GAAG,GAAI,EAAE,GAAGb,MAAM,CAAC+E,EAAE,CAAC,CAAA;EACpC,OAAC,CAAC,CAAA;OACH,CAAA;MAED/E,MAAM,CAAC+V,WAAW,GAAG,MAAM;QAAA,IAAApE,qBAAA,EAAAC,qBAAA,CAAA;EACzB,MAAA,OACE,EAAAD,qBAAA,GAAC3R,MAAM,CAACqF,SAAS,CAAC2Q,cAAc,KAAArE,IAAAA,GAAAA,qBAAA,GAAI,IAAI,MAAA,CAAAC,qBAAA,GACvCtN,KAAK,CAACO,OAAO,CAACmR,cAAc,KAAA,IAAA,GAAApE,qBAAA,GAAI,IAAI,CAAC,KACrC,CAAC,CAAC5R,MAAM,CAACC,UAAU,IAAI,CAAC,CAACD,MAAM,CAACqF,SAAS,CAAC4Q,gBAAgB,CAAC,CAAA;OAE/D,CAAA;MAEDjW,MAAM,CAACkW,YAAY,GAAG,MAAM;EAAA,MAAA,IAAAC,qBAAA,CAAA;EAC1B,MAAA,OAAA,CAAAA,qBAAA,GAAO7R,KAAK,CAAC6D,QAAQ,EAAE,CAACuN,QAAQ,KAAA,IAAA,GAAA,KAAA,CAAA,GAAzBS,qBAAA,CAA2BjQ,QAAQ,CAAClG,MAAM,CAAC+E,EAAE,CAAC,CAAA;OACtD,CAAA;MAED/E,MAAM,CAACoW,eAAe,GAAG,MAAA;EAAA,MAAA,IAAAC,sBAAA,CAAA;EAAA,MAAA,OAAA,CAAAA,sBAAA,GAAM/R,KAAK,CAAC6D,QAAQ,EAAE,CAACuN,QAAQ,KAAA,IAAA,GAAA,KAAA,CAAA,GAAzBW,sBAAA,CAA2BC,OAAO,CAACtW,MAAM,CAAC+E,EAAE,CAAC,CAAA;EAAA,KAAA,CAAA;MAE5E/E,MAAM,CAACuW,wBAAwB,GAAG,MAAM;EACtC,MAAA,MAAMC,QAAQ,GAAGxW,MAAM,CAAC+V,WAAW,EAAE,CAAA;EAErC,MAAA,OAAO,MAAM;UACX,IAAI,CAACS,QAAQ,EAAE,OAAA;UACfxW,MAAM,CAAC6V,cAAc,EAAE,CAAA;SACxB,CAAA;OACF,CAAA;MACD7V,MAAM,CAACyW,oBAAoB,GAAG,MAAM;QAClC,MAAMtF,QAAQ,GAAG7M,KAAK,CAAC8M,eAAe,EAAE,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAA;QAEpD,MAAMC,KAAK,GAAGH,QAAQ,IAARA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,QAAQ,CAAEvM,QAAQ,CAAC5E,MAAM,CAAC+E,EAAE,CAAC,CAAA;EAE3C,MAAA,IAAI,OAAOuM,KAAK,KAAK,QAAQ,EAAE;UAC7B,OAAO6D,cAAc,CAACvB,GAAG,CAAA;EAC3B,OAAA;EAEA,MAAA,IAAI8C,MAAM,CAAC7Q,SAAS,CAAC8I,QAAQ,CAACgI,IAAI,CAACrF,KAAK,CAAC,KAAK,eAAe,EAAE;UAC7D,OAAO6D,cAAc,CAAClB,MAAM,CAAA;EAC9B,OAAA;OACD,CAAA;MACDjU,MAAM,CAAC4W,gBAAgB,GAAG,MAAM;QAAA,IAAAC,qBAAA,EAAAC,sBAAA,CAAA;QAC9B,IAAI,CAAC9W,MAAM,EAAE;UACX,MAAM,IAAIuG,KAAK,EAAE,CAAA;EACnB,OAAA;QAEA,OAAOzF,UAAU,CAACd,MAAM,CAACqF,SAAS,CAACoQ,aAAa,CAAC,GAC7CzV,MAAM,CAACqF,SAAS,CAACoQ,aAAa,GAC9BzV,MAAM,CAACqF,SAAS,CAACoQ,aAAa,KAAK,MAAM,GACvCzV,MAAM,CAACyW,oBAAoB,EAAE,IAAAI,qBAAA,GAAA,CAAAC,sBAAA,GAC7BxS,KAAK,CAACO,OAAO,CAACsQ,cAAc,KAAA,IAAA,GAAA,KAAA,CAAA,GAA5B2B,sBAAA,CACE9W,MAAM,CAACqF,SAAS,CAACoQ,aAAa,CAC/B,KAAAoB,IAAAA,GAAAA,qBAAA,GACD1B,cAAc,CACZnV,MAAM,CAACqF,SAAS,CAACoQ,aAAa,CAC/B,CAAA;OACR,CAAA;KACF;IAED1N,WAAW,EAA0BzD,KAAmB,IAAW;EACjEA,IAAAA,KAAK,CAACwR,WAAW,GAAGxV,OAAO,IAAIgE,KAAK,CAACO,OAAO,CAAC8Q,gBAAgB,IAAA,IAAA,GAAA,KAAA,CAAA,GAA9BrR,KAAK,CAACO,OAAO,CAAC8Q,gBAAgB,CAAGrV,OAAO,CAAC,CAAA;EAExEgE,IAAAA,KAAK,CAACyS,aAAa,GAAG1D,YAAY,IAAI;QAAA,IAAA2D,qBAAA,EAAAzD,mBAAA,CAAA;QACpCjP,KAAK,CAACwR,WAAW,CAACzC,YAAY,GAAG,EAAE,GAAA,CAAA2D,qBAAA,GAAA,CAAAzD,mBAAA,GAAGjP,KAAK,CAACkP,YAAY,qBAAlBD,mBAAA,CAAoBmC,QAAQ,KAAAsB,IAAAA,GAAAA,qBAAA,GAAI,EAAE,CAAC,CAAA;OAC1E,CAAA;MAED1S,KAAK,CAAC2S,qBAAqB,GAAG,MAAM3S,KAAK,CAACmP,mBAAmB,EAAE,CAAA;MAC/DnP,KAAK,CAAC4S,kBAAkB,GAAG,MAAM;QAC/B,IAAI,CAAC5S,KAAK,CAAC6S,mBAAmB,IAAI7S,KAAK,CAACO,OAAO,CAACqS,kBAAkB,EAAE;UAClE5S,KAAK,CAAC6S,mBAAmB,GAAG7S,KAAK,CAACO,OAAO,CAACqS,kBAAkB,CAAC5S,KAAK,CAAC,CAAA;EACrE,OAAA;QAEA,IAAIA,KAAK,CAACO,OAAO,CAACuS,cAAc,IAAI,CAAC9S,KAAK,CAAC6S,mBAAmB,EAAE;EAC9D,QAAA,OAAO7S,KAAK,CAAC2S,qBAAqB,EAAE,CAAA;EACtC,OAAA;EAEA,MAAA,OAAO3S,KAAK,CAAC6S,mBAAmB,EAAE,CAAA;OACnC,CAAA;KACF;EAED9K,EAAAA,SAAS,EAAEA,CACT9H,GAAe,EACfD,KAAmB,KACV;MACTC,GAAG,CAAC2R,YAAY,GAAG,MAAM,CAAC,CAAC3R,GAAG,CAAC8S,gBAAgB,CAAA;EAC/C9S,IAAAA,GAAG,CAAC0R,gBAAgB,GAAGzR,QAAQ,IAAI;QACjC,IAAID,GAAG,CAAC+S,oBAAoB,CAAC1K,cAAc,CAACpI,QAAQ,CAAC,EAAE;EACrD,QAAA,OAAOD,GAAG,CAAC+S,oBAAoB,CAAC9S,QAAQ,CAAC,CAAA;EAC3C,OAAA;EAEA,MAAA,MAAMxE,MAAM,GAAGsE,KAAK,CAACuI,SAAS,CAACrI,QAAQ,CAAC,CAAA;QAExC,IAAI,EAACxE,MAAM,IAANA,IAAAA,IAAAA,MAAM,CAAEqF,SAAS,CAAC4Q,gBAAgB,CAAE,EAAA;EACvC,QAAA,OAAO1R,GAAG,CAACK,QAAQ,CAACJ,QAAQ,CAAC,CAAA;EAC/B,OAAA;EAEAD,MAAAA,GAAG,CAAC+S,oBAAoB,CAAC9S,QAAQ,CAAC,GAAGxE,MAAM,CAACqF,SAAS,CAAC4Q,gBAAgB,CACpE1R,GAAG,CAAC+H,QACN,CAAC,CAAA;EAED,MAAA,OAAO/H,GAAG,CAAC+S,oBAAoB,CAAC9S,QAAQ,CAAC,CAAA;OAC1C,CAAA;EACDD,IAAAA,GAAG,CAAC+S,oBAAoB,GAAG,EAAE,CAAA;KAC9B;IAEDjT,UAAU,EAAEA,CACVM,IAAyB,EACzB3E,MAA6B,EAC7BuE,GAAe,EACfD,KAAmB,KACV;EAITK,IAAAA,IAAI,CAACuR,YAAY,GAAG,MAClBlW,MAAM,CAACkW,YAAY,EAAE,IAAIlW,MAAM,CAAC+E,EAAE,KAAKR,GAAG,CAAC8S,gBAAgB,CAAA;EAC7D1S,IAAAA,IAAI,CAAC4S,gBAAgB,GAAG,MAAM,CAAC5S,IAAI,CAACuR,YAAY,EAAE,IAAIlW,MAAM,CAACkW,YAAY,EAAE,CAAA;MAC3EvR,IAAI,CAAC6S,eAAe,GAAG,MAAA;EAAA,MAAA,IAAAC,YAAA,CAAA;QAAA,OACrB,CAAC9S,IAAI,CAACuR,YAAY,EAAE,IAAI,CAACvR,IAAI,CAAC4S,gBAAgB,EAAE,IAAI,CAAC,EAAAE,CAAAA,YAAA,GAAClT,GAAG,CAACiI,OAAO,KAAA,IAAA,IAAXiL,YAAA,CAAazV,MAAM,CAAA,CAAA;EAAA,KAAA,CAAA;EAC7E,GAAA;EACF,EAAC;EAEM,SAAS8E,YAAYA,CAC1BE,WAAqC,EACrC0O,QAAkB,EAClBE,iBAAsC,EACtC;IACA,IAAI,EAACF,QAAQ,IAARA,IAAAA,IAAAA,QAAQ,CAAE1T,MAAM,CAAA,IAAI,CAAC4T,iBAAiB,EAAE;EAC3C,IAAA,OAAO5O,WAAW,CAAA;EACpB,GAAA;EAEA,EAAA,MAAM0Q,kBAAkB,GAAG1Q,WAAW,CAAC4B,MAAM,CAC3C+O,GAAG,IAAI,CAACjC,QAAQ,CAACxP,QAAQ,CAACyR,GAAG,CAAC5S,EAAE,CAClC,CAAC,CAAA;IAED,IAAI6Q,iBAAiB,KAAK,QAAQ,EAAE;EAClC,IAAA,OAAO8B,kBAAkB,CAAA;EAC3B,GAAA;IAEA,MAAME,eAAe,GAAGlC,QAAQ,CAC7B7N,GAAG,CAACgQ,CAAC,IAAI7Q,WAAW,CAAC2B,IAAI,CAACgP,GAAG,IAAIA,GAAG,CAAC5S,EAAE,KAAK8S,CAAC,CAAE,CAAC,CAChDjP,MAAM,CAACC,OAAO,CAAC,CAAA;EAElB,EAAA,OAAO,CAAC,GAAG+O,eAAe,EAAE,GAAGF,kBAAkB,CAAC,CAAA;EACpD;;EC7VA;;AAEO,QAAMI,cAA4B,GAAG;IAC1CnH,eAAe,EAAGC,KAAK,IAA4B;MACjD,OAAO;EACLmH,MAAAA,WAAW,EAAE,EAAE;QACf,GAAGnH,KAAAA;OACJ,CAAA;KACF;IAEDE,iBAAiB,EACfxM,KAAmB,IACW;MAC9B,OAAO;EACL0T,MAAAA,mBAAmB,EAAEvX,gBAAgB,CAAC,aAAa,EAAE6D,KAAK,CAAA;OAC3D,CAAA;KACF;EAEDc,EAAAA,YAAY,EAAEA,CACZpF,MAA8B,EAC9BsE,KAAmB,KACV;EACTtE,IAAAA,MAAM,CAACiY,QAAQ,GAAGhW,IAAI,CACpBiW,QAAQ,IAAI,CAACC,sBAAsB,CAAC7T,KAAK,EAAE4T,QAAQ,CAAC,CAAC,EACrD1R,OAAO,IAAIA,OAAO,CAAC+L,SAAS,CAACxR,CAAC,IAAIA,CAAC,CAACgE,EAAE,KAAK/E,MAAM,CAAC+E,EAAE,CAAC,EACrDf,cAAc,CAACM,KAAK,CAACO,OAAO,EAAE,cAAc,EAAE,UAAU,CAC1D,CAAC,CAAA;EACD7E,IAAAA,MAAM,CAACoY,gBAAgB,GAAGF,QAAQ,IAAI;EAAA,MAAA,IAAAG,SAAA,CAAA;EACpC,MAAA,MAAM7R,OAAO,GAAG2R,sBAAsB,CAAC7T,KAAK,EAAE4T,QAAQ,CAAC,CAAA;EACvD,MAAA,OAAO,CAAAG,CAAAA,SAAA,GAAA7R,OAAO,CAAC,CAAC,CAAC,KAAV6R,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,SAAA,CAAYtT,EAAE,MAAK/E,MAAM,CAAC+E,EAAE,CAAA;OACpC,CAAA;EACD/E,IAAAA,MAAM,CAACsY,eAAe,GAAGJ,QAAQ,IAAI;EAAA,MAAA,IAAAK,QAAA,CAAA;EACnC,MAAA,MAAM/R,OAAO,GAAG2R,sBAAsB,CAAC7T,KAAK,EAAE4T,QAAQ,CAAC,CAAA;EACvD,MAAA,OAAO,EAAAK,QAAA,GAAA/R,OAAO,CAACA,OAAO,CAACxE,MAAM,GAAG,CAAC,CAAC,qBAA3BuW,QAAA,CAA6BxT,EAAE,MAAK/E,MAAM,CAAC+E,EAAE,CAAA;OACrD,CAAA;KACF;IAEDgD,WAAW,EAA0BzD,KAAmB,IAAW;EACjEA,IAAAA,KAAK,CAACkU,cAAc,GAAGlY,OAAO,IAC5BgE,KAAK,CAACO,OAAO,CAACmT,mBAAmB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAjC1T,KAAK,CAACO,OAAO,CAACmT,mBAAmB,CAAG1X,OAAO,CAAC,CAAA;EAC9CgE,IAAAA,KAAK,CAACmU,gBAAgB,GAAGpF,YAAY,IAAI;EAAA,MAAA,IAAAC,qBAAA,CAAA;EACvChP,MAAAA,KAAK,CAACkU,cAAc,CAClBnF,YAAY,GAAG,EAAE,IAAAC,qBAAA,GAAGhP,KAAK,CAACkP,YAAY,CAACuE,WAAW,YAAAzE,qBAAA,GAAI,EACxD,CAAC,CAAA;OACF,CAAA;EACDhP,IAAAA,KAAK,CAACuC,kBAAkB,GAAG5E,IAAI,CAC7B,MAAM,CACJqC,KAAK,CAAC6D,QAAQ,EAAE,CAAC4P,WAAW,EAC5BzT,KAAK,CAAC6D,QAAQ,EAAE,CAACuN,QAAQ,EACzBpR,KAAK,CAACO,OAAO,CAAC+Q,iBAAiB,CAChC,EACD,CAACmC,WAAW,EAAErC,QAAQ,EAAEE,iBAAiB,KACtCpP,OAAiC,IAAK;EACrC;EACA;QACA,IAAIkS,cAAwC,GAAG,EAAE,CAAA;;EAEjD;EACA,MAAA,IAAI,EAACX,WAAW,IAAA,IAAA,IAAXA,WAAW,CAAE/V,MAAM,CAAE,EAAA;EACxB0W,QAAAA,cAAc,GAAGlS,OAAO,CAAA;EAC1B,OAAC,MAAM;EACL,QAAA,MAAMmS,eAAe,GAAG,CAAC,GAAGZ,WAAW,CAAC,CAAA;;EAExC;EACA,QAAA,MAAMa,WAAW,GAAG,CAAC,GAAGpS,OAAO,CAAC,CAAA;;EAEhC;;EAEA;EACA,QAAA,OAAOoS,WAAW,CAAC5W,MAAM,IAAI2W,eAAe,CAAC3W,MAAM,EAAE;EACnD,UAAA,MAAM6W,cAAc,GAAGF,eAAe,CAACG,KAAK,EAAE,CAAA;EAC9C,UAAA,MAAMC,UAAU,GAAGH,WAAW,CAACrG,SAAS,CACtCxR,CAAC,IAAIA,CAAC,CAACgE,EAAE,KAAK8T,cAChB,CAAC,CAAA;EACD,UAAA,IAAIE,UAAU,GAAG,CAAC,CAAC,EAAE;EACnBL,YAAAA,cAAc,CAAC5W,IAAI,CAAC8W,WAAW,CAACI,MAAM,CAACD,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC,CAAE,CAAC,CAAA;EAC5D,WAAA;EACF,SAAA;;EAEA;EACAL,QAAAA,cAAc,GAAG,CAAC,GAAGA,cAAc,EAAE,GAAGE,WAAW,CAAC,CAAA;EACtD,OAAA;EAEA,MAAA,OAAO9R,YAAY,CAAC4R,cAAc,EAAEhD,QAAQ,EAAEE,iBAAiB,CAAC,CAAA;OACjE,EACH5R,cAAc,CAACM,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,oBAAoB,CAClE,CAAC,CAAA;EACH,GAAA;EACF;;ECfA;;EAEA,MAAMoU,4BAA4B,GAAGA,OAA2B;EAC9D5Q,EAAAA,IAAI,EAAE,EAAE;EACRC,EAAAA,KAAK,EAAE,EAAA;EACT,CAAC,CAAC,CAAA;AAEK,QAAM4Q,aAA2B,GAAG;IACzCvI,eAAe,EAAGC,KAAK,IAA8B;MACnD,OAAO;QACLxI,aAAa,EAAE6Q,4BAA4B,EAAE;QAC7C,GAAGrI,KAAAA;OACJ,CAAA;KACF;IAEDE,iBAAiB,EACfxM,KAAmB,IACa;MAChC,OAAO;EACL6U,MAAAA,qBAAqB,EAAE1Y,gBAAgB,CAAC,eAAe,EAAE6D,KAAK,CAAA;OAC/D,CAAA;KACF;EAEDc,EAAAA,YAAY,EAAEA,CACZpF,MAA6B,EAC7BsE,KAAmB,KACV;EACTtE,IAAAA,MAAM,CAACoZ,GAAG,GAAGlB,QAAQ,IAAI;QACvB,MAAMmB,SAAS,GAAGrZ,MAAM,CACrB4G,cAAc,EAAE,CAChBiB,GAAG,CAAC9G,CAAC,IAAIA,CAAC,CAACgE,EAAE,CAAC,CACd6D,MAAM,CAACC,OAAO,CAAa,CAAA;EAE9BvE,MAAAA,KAAK,CAACgV,gBAAgB,CAACzY,GAAG,IAAI;UAAA,IAAA0Y,UAAA,EAAAC,WAAA,CAAA;UAC5B,IAAItB,QAAQ,KAAK,OAAO,EAAE;YAAA,IAAAuB,SAAA,EAAAC,UAAA,CAAA;YACxB,OAAO;cACLrR,IAAI,EAAE,CAAAoR,CAAAA,SAAA,GAAC5Y,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAEwH,IAAI,KAAAoR,IAAAA,GAAAA,SAAA,GAAI,EAAE,EAAE7Q,MAAM,CAAC7H,CAAC,IAAI,EAACsY,SAAS,IAAA,IAAA,IAATA,SAAS,CAAEnT,QAAQ,CAACnF,CAAC,CAAC,CAAC,CAAA;EAC5DuH,YAAAA,KAAK,EAAE,CACL,GAAG,CAAA,CAAAoR,UAAA,GAAC7Y,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAEyH,KAAK,KAAAoR,IAAAA,GAAAA,UAAA,GAAI,EAAE,EAAE9Q,MAAM,CAAC7H,CAAC,IAAI,EAACsY,SAAS,YAATA,SAAS,CAAEnT,QAAQ,CAACnF,CAAC,CAAC,CAAC,CAAA,EAC1D,GAAGsY,SAAS,CAAA;aAEf,CAAA;EACH,SAAA;UAEA,IAAInB,QAAQ,KAAK,MAAM,EAAE;YAAA,IAAAyB,UAAA,EAAAC,WAAA,CAAA;YACvB,OAAO;EACLvR,YAAAA,IAAI,EAAE,CACJ,GAAG,CAAA,CAAAsR,UAAA,GAAC9Y,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAEwH,IAAI,KAAAsR,IAAAA,GAAAA,UAAA,GAAI,EAAE,EAAE/Q,MAAM,CAAC7H,CAAC,IAAI,EAACsY,SAAS,YAATA,SAAS,CAAEnT,QAAQ,CAACnF,CAAC,CAAC,CAAA,CAAC,EACzD,GAAGsY,SAAS,CACb;cACD/Q,KAAK,EAAE,CAAAsR,CAAAA,WAAA,GAAC/Y,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAEyH,KAAK,KAAAsR,IAAAA,GAAAA,WAAA,GAAI,EAAE,EAAEhR,MAAM,CAAC7H,CAAC,IAAI,EAACsY,SAAS,IAATA,IAAAA,IAAAA,SAAS,CAAEnT,QAAQ,CAACnF,CAAC,CAAC,CAAA,CAAA;aAC9D,CAAA;EACH,SAAA;UAEA,OAAO;YACLsH,IAAI,EAAE,CAAAkR,CAAAA,UAAA,GAAC1Y,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAEwH,IAAI,KAAAkR,IAAAA,GAAAA,UAAA,GAAI,EAAE,EAAE3Q,MAAM,CAAC7H,CAAC,IAAI,EAACsY,SAAS,IAAA,IAAA,IAATA,SAAS,CAAEnT,QAAQ,CAACnF,CAAC,CAAC,CAAC,CAAA;YAC5DuH,KAAK,EAAE,CAAAkR,CAAAA,WAAA,GAAC3Y,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAEyH,KAAK,KAAAkR,IAAAA,GAAAA,WAAA,GAAI,EAAE,EAAE5Q,MAAM,CAAC7H,CAAC,IAAI,EAACsY,SAAS,IAATA,IAAAA,IAAAA,SAAS,CAAEnT,QAAQ,CAACnF,CAAC,CAAC,CAAA,CAAA;WAC9D,CAAA;EACH,OAAC,CAAC,CAAA;OACH,CAAA;MAEDf,MAAM,CAAC6Z,SAAS,GAAG,MAAM;EACvB,MAAA,MAAM7S,WAAW,GAAGhH,MAAM,CAAC4G,cAAc,EAAE,CAAA;EAE3C,MAAA,OAAOI,WAAW,CAAClE,IAAI,CACrB/B,CAAC,IAAA;EAAA,QAAA,IAAA+Y,qBAAA,EAAAtU,IAAA,EAAAoM,qBAAA,CAAA;EAAA,QAAA,OACC,CAAAkI,CAAAA,qBAAA,GAAC/Y,CAAC,CAACsE,SAAS,CAAC0U,aAAa,KAAA,IAAA,GAAAD,qBAAA,GAAI,IAAI,MAAAtU,CAAAA,IAAA,IAAAoM,qBAAA,GACjCtN,KAAK,CAACO,OAAO,CAACmV,mBAAmB,KAAA,IAAA,GAAApI,qBAAA,GAChCtN,KAAK,CAACO,OAAO,CAACkV,aAAa,KAAA,IAAA,GAAAvU,IAAA,GAC3B,IAAI,CAAC,CAAA;EAAA,OACX,CAAC,CAAA;OACF,CAAA;MAEDxF,MAAM,CAACia,WAAW,GAAG,MAAM;EACzB,MAAA,MAAMC,aAAa,GAAGla,MAAM,CAAC4G,cAAc,EAAE,CAACiB,GAAG,CAAC9G,CAAC,IAAIA,CAAC,CAACgE,EAAE,CAAC,CAAA;QAE5D,MAAM;UAAEsD,IAAI;EAAEC,QAAAA,KAAAA;EAAM,OAAC,GAAGhE,KAAK,CAAC6D,QAAQ,EAAE,CAACC,aAAa,CAAA;EAEtD,MAAA,MAAM+R,MAAM,GAAGD,aAAa,CAACpX,IAAI,CAAC/B,CAAC,IAAIsH,IAAI,IAAA,IAAA,GAAA,KAAA,CAAA,GAAJA,IAAI,CAAEnC,QAAQ,CAACnF,CAAC,CAAC,CAAC,CAAA;EACzD,MAAA,MAAMqZ,OAAO,GAAGF,aAAa,CAACpX,IAAI,CAAC/B,CAAC,IAAIuH,KAAK,IAAA,IAAA,GAAA,KAAA,CAAA,GAALA,KAAK,CAAEpC,QAAQ,CAACnF,CAAC,CAAC,CAAC,CAAA;QAE3D,OAAOoZ,MAAM,GAAG,MAAM,GAAGC,OAAO,GAAG,OAAO,GAAG,KAAK,CAAA;OACnD,CAAA;MAEDpa,MAAM,CAACqa,cAAc,GAAG,MAAM;QAAA,IAAAjI,qBAAA,EAAAC,sBAAA,CAAA;EAC5B,MAAA,MAAM6F,QAAQ,GAAGlY,MAAM,CAACia,WAAW,EAAE,CAAA;EAErC,MAAA,OAAO/B,QAAQ,GAAA,CAAA9F,qBAAA,GAAA,CAAAC,sBAAA,GACX/N,KAAK,CAAC6D,QAAQ,EAAE,CAACC,aAAa,KAAA,IAAA,IAAA,CAAAiK,sBAAA,GAA9BA,sBAAA,CAAiC6F,QAAQ,CAAC,KAA1C7F,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,sBAAA,CAA4CiE,OAAO,CAACtW,MAAM,CAAC+E,EAAE,CAAC,YAAAqN,qBAAA,GAAI,CAAC,CAAC,GACpE,CAAC,CAAA;OACN,CAAA;KACF;EAED/F,EAAAA,SAAS,EAAEA,CACT9H,GAAe,EACfD,KAAmB,KACV;EACTC,IAAAA,GAAG,CAAC+V,qBAAqB,GAAGrY,IAAI,CAC9B,MAAM,CACJsC,GAAG,CAACgW,mBAAmB,EAAE,EACzBjW,KAAK,CAAC6D,QAAQ,EAAE,CAACC,aAAa,CAACC,IAAI,EACnC/D,KAAK,CAAC6D,QAAQ,EAAE,CAACC,aAAa,CAACE,KAAK,CACrC,EACD,CAACoF,QAAQ,EAAErF,IAAI,EAAEC,KAAK,KAAK;EACzB,MAAA,MAAMkS,YAAsB,GAAG,CAAC,IAAInS,IAAI,IAAA,IAAA,GAAJA,IAAI,GAAI,EAAE,GAAG,IAAIC,KAAK,IAAA,IAAA,GAALA,KAAK,GAAI,EAAE,EAAE,CAAA;EAElE,MAAA,OAAOoF,QAAQ,CAAC9E,MAAM,CAAC7H,CAAC,IAAI,CAACyZ,YAAY,CAACtU,QAAQ,CAACnF,CAAC,CAACf,MAAM,CAAC+E,EAAE,CAAC,CAAC,CAAA;OACjE,EACDf,cAAc,CAACM,KAAK,CAACO,OAAO,EAAE,WAAW,EAAE,uBAAuB,CACpE,CAAC,CAAA;EACDN,IAAAA,GAAG,CAACkW,mBAAmB,GAAGxY,IAAI,CAC5B,MAAM,CAACsC,GAAG,CAACgW,mBAAmB,EAAE,EAAEjW,KAAK,CAAC6D,QAAQ,EAAE,CAACC,aAAa,CAACC,IAAI,CAAC,EACtE,CAACqF,QAAQ,EAAErF,IAAI,KAAK;EAClB,MAAA,MAAMqS,KAAK,GAAG,CAACrS,IAAI,IAAA,IAAA,GAAJA,IAAI,GAAI,EAAE,EACtBR,GAAG,CAACrD,QAAQ,IAAIkJ,QAAQ,CAAC/E,IAAI,CAAChE,IAAI,IAAIA,IAAI,CAAC3E,MAAM,CAAC+E,EAAE,KAAKP,QAAQ,CAAE,CAAC,CACpEoE,MAAM,CAACC,OAAO,CAAC,CACfhB,GAAG,CAAC9G,CAAC,KAAK;EAAE,QAAA,GAAGA,CAAC;EAAEmX,QAAAA,QAAQ,EAAE,MAAA;EAAO,OAAC,CAAyB,CAAC,CAAA;EAEjE,MAAA,OAAOwC,KAAK,CAAA;OACb,EACD1W,cAAc,CAACM,KAAK,CAACO,OAAO,EAAE,WAAW,EAAE,qBAAqB,CAClE,CAAC,CAAA;EACDN,IAAAA,GAAG,CAACoW,oBAAoB,GAAG1Y,IAAI,CAC7B,MAAM,CAACsC,GAAG,CAACgW,mBAAmB,EAAE,EAAEjW,KAAK,CAAC6D,QAAQ,EAAE,CAACC,aAAa,CAACE,KAAK,CAAC,EACvE,CAACoF,QAAQ,EAAEpF,KAAK,KAAK;EACnB,MAAA,MAAMoS,KAAK,GAAG,CAACpS,KAAK,IAAA,IAAA,GAALA,KAAK,GAAI,EAAE,EACvBT,GAAG,CAACrD,QAAQ,IAAIkJ,QAAQ,CAAC/E,IAAI,CAAChE,IAAI,IAAIA,IAAI,CAAC3E,MAAM,CAAC+E,EAAE,KAAKP,QAAQ,CAAE,CAAC,CACpEoE,MAAM,CAACC,OAAO,CAAC,CACfhB,GAAG,CAAC9G,CAAC,KAAK;EAAE,QAAA,GAAGA,CAAC;EAAEmX,QAAAA,QAAQ,EAAE,OAAA;EAAQ,OAAC,CAAyB,CAAC,CAAA;EAElE,MAAA,OAAOwC,KAAK,CAAA;OACb,EACD1W,cAAc,CAACM,KAAK,CAACO,OAAO,EAAE,WAAW,EAAE,sBAAsB,CACnE,CAAC,CAAA;KACF;IAEDkD,WAAW,EAA0BzD,KAAmB,IAAW;EACjEA,IAAAA,KAAK,CAACgV,gBAAgB,GAAGhZ,OAAO,IAC9BgE,KAAK,CAACO,OAAO,CAACsU,qBAAqB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAnC7U,KAAK,CAACO,OAAO,CAACsU,qBAAqB,CAAG7Y,OAAO,CAAC,CAAA;MAEhDgE,KAAK,CAACsW,kBAAkB,GAAGvH,YAAY,IAAA;QAAA,IAAAC,qBAAA,EAAAC,mBAAA,CAAA;QAAA,OACrCjP,KAAK,CAACgV,gBAAgB,CACpBjG,YAAY,GACR4F,4BAA4B,EAAE,GAAA3F,CAAAA,qBAAA,GAAAC,CAAAA,mBAAA,GAC9BjP,KAAK,CAACkP,YAAY,KAAlBD,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,mBAAA,CAAoBnL,aAAa,KAAAkL,IAAAA,GAAAA,qBAAA,GAAI2F,4BAA4B,EACvE,CAAC,CAAA;EAAA,KAAA,CAAA;EAEH3U,IAAAA,KAAK,CAACuW,sBAAsB,GAAG3C,QAAQ,IAAI;EAAA,MAAA,IAAA4C,qBAAA,CAAA;QACzC,MAAMC,YAAY,GAAGzW,KAAK,CAAC6D,QAAQ,EAAE,CAACC,aAAa,CAAA;QAEnD,IAAI,CAAC8P,QAAQ,EAAE;UAAA,IAAA8C,kBAAA,EAAAC,mBAAA,CAAA;UACb,OAAOpS,OAAO,CAAC,CAAAmS,CAAAA,kBAAA,GAAAD,YAAY,CAAC1S,IAAI,KAAA,IAAA,GAAA,KAAA,CAAA,GAAjB2S,kBAAA,CAAmBhZ,MAAM,MAAAiZ,CAAAA,mBAAA,GAAIF,YAAY,CAACzS,KAAK,KAAlB2S,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,mBAAA,CAAoBjZ,MAAM,CAAC,CAAA,CAAA;EACzE,OAAA;EACA,MAAA,OAAO6G,OAAO,CAAA,CAAAiS,qBAAA,GAACC,YAAY,CAAC7C,QAAQ,CAAC,KAAtB4C,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,qBAAA,CAAwB9Y,MAAM,CAAC,CAAA;OAC/C,CAAA;EAEDsC,IAAAA,KAAK,CAAC4W,kBAAkB,GAAGjZ,IAAI,CAC7B,MAAM,CAACqC,KAAK,CAACkJ,iBAAiB,EAAE,EAAElJ,KAAK,CAAC6D,QAAQ,EAAE,CAACC,aAAa,CAACC,IAAI,CAAC,EACtE,CAACE,UAAU,EAAEF,IAAI,KAAK;EACpB,MAAA,OAAO,CAACA,IAAI,IAAJA,IAAAA,GAAAA,IAAI,GAAI,EAAE,EACfR,GAAG,CAACrD,QAAQ,IAAI+D,UAAU,CAACI,IAAI,CAAC3I,MAAM,IAAIA,MAAM,CAAC+E,EAAE,KAAKP,QAAQ,CAAE,CAAC,CACnEoE,MAAM,CAACC,OAAO,CAAC,CAAA;OACnB,EACD7E,cAAc,CAACM,KAAK,CAACO,OAAO,EAAE,cAAc,EAAE,oBAAoB,CACpE,CAAC,CAAA;EAEDP,IAAAA,KAAK,CAAC6W,mBAAmB,GAAGlZ,IAAI,CAC9B,MAAM,CAACqC,KAAK,CAACkJ,iBAAiB,EAAE,EAAElJ,KAAK,CAAC6D,QAAQ,EAAE,CAACC,aAAa,CAACE,KAAK,CAAC,EACvE,CAACC,UAAU,EAAED,KAAK,KAAK;EACrB,MAAA,OAAO,CAACA,KAAK,IAALA,IAAAA,GAAAA,KAAK,GAAI,EAAE,EAChBT,GAAG,CAACrD,QAAQ,IAAI+D,UAAU,CAACI,IAAI,CAAC3I,MAAM,IAAIA,MAAM,CAAC+E,EAAE,KAAKP,QAAQ,CAAE,CAAC,CACnEoE,MAAM,CAACC,OAAO,CAAC,CAAA;OACnB,EACD7E,cAAc,CAACM,KAAK,CAACO,OAAO,EAAE,cAAc,EAAE,qBAAqB,CACrE,CAAC,CAAA;EAEDP,IAAAA,KAAK,CAAC8W,oBAAoB,GAAGnZ,IAAI,CAC/B,MAAM,CACJqC,KAAK,CAACkJ,iBAAiB,EAAE,EACzBlJ,KAAK,CAAC6D,QAAQ,EAAE,CAACC,aAAa,CAACC,IAAI,EACnC/D,KAAK,CAAC6D,QAAQ,EAAE,CAACC,aAAa,CAACE,KAAK,CACrC,EACD,CAACC,UAAU,EAAEF,IAAI,EAAEC,KAAK,KAAK;EAC3B,MAAA,MAAMkS,YAAsB,GAAG,CAAC,IAAInS,IAAI,IAAA,IAAA,GAAJA,IAAI,GAAI,EAAE,GAAG,IAAIC,KAAK,IAAA,IAAA,GAALA,KAAK,GAAI,EAAE,EAAE,CAAA;EAElE,MAAA,OAAOC,UAAU,CAACK,MAAM,CAAC7H,CAAC,IAAI,CAACyZ,YAAY,CAACtU,QAAQ,CAACnF,CAAC,CAACgE,EAAE,CAAC,CAAC,CAAA;OAC5D,EACDf,cAAc,CAACM,KAAK,CAACO,OAAO,EAAE,cAAc,EAAE,sBAAsB,CACtE,CAAC,CAAA;EACH,GAAA;EACF;;EC/UO,SAASwW,oBAAoBA,CAACC,SAAoB,EAAmB;IAC1E,OAAOA,SAAS,KAAK,OAAOC,QAAQ,KAAK,WAAW,GAAGA,QAAQ,GAAG,IAAI,CAAC,CAAA;EACzE;;ECYA;;EA2MA;;AAEO,QAAMC,mBAAmB,GAAG;EACjCvG,EAAAA,IAAI,EAAE,GAAG;EACTwG,EAAAA,OAAO,EAAE,EAAE;IACXC,OAAO,EAAEvL,MAAM,CAACwL,gBAAAA;EAClB,EAAC;EAED,MAAMC,+BAA+B,GAAGA,OAA8B;EACpEC,EAAAA,WAAW,EAAE,IAAI;EACjBC,EAAAA,SAAS,EAAE,IAAI;EACfC,EAAAA,WAAW,EAAE,IAAI;EACjBC,EAAAA,eAAe,EAAE,IAAI;EACrBC,EAAAA,gBAAgB,EAAE,KAAK;EACvBC,EAAAA,iBAAiB,EAAE,EAAA;EACrB,CAAC,CAAC,CAAA;AAEK,QAAMC,YAA0B,GAAG;IACxC1L,mBAAmB,EAAEA,MAA6B;EAChD,IAAA,OAAO+K,mBAAmB,CAAA;KAC3B;IACD7K,eAAe,EAAGC,KAAK,IAA6B;MAClD,OAAO;QACLwL,YAAY,EAAE,EAAE;QAChBC,gBAAgB,EAAET,+BAA+B,EAAE;QACnD,GAAGhL,KAAAA;OACJ,CAAA;KACF;IAEDE,iBAAiB,EACfxM,KAAmB,IACY;MAC/B,OAAO;EACLgY,MAAAA,gBAAgB,EAAE,OAAO;EACzBC,MAAAA,qBAAqB,EAAE,KAAK;EAC5BC,MAAAA,oBAAoB,EAAE/b,gBAAgB,CAAC,cAAc,EAAE6D,KAAK,CAAC;EAC7DmY,MAAAA,wBAAwB,EAAEhc,gBAAgB,CAAC,kBAAkB,EAAE6D,KAAK,CAAA;OACrE,CAAA;KACF;EAEDc,EAAAA,YAAY,EAAEA,CACZpF,MAA6B,EAC7BsE,KAAmB,KACV;MACTtE,MAAM,CAAC0c,OAAO,GAAG,MAAM;EAAA,MAAA,IAAAC,qBAAA,EAAAnX,IAAA,EAAAoX,qBAAA,CAAA;EACrB,MAAA,MAAMC,UAAU,GAAGvY,KAAK,CAAC6D,QAAQ,EAAE,CAACiU,YAAY,CAACpc,MAAM,CAAC+E,EAAE,CAAC,CAAA;QAE3D,OAAO3B,IAAI,CAACW,GAAG,CACbX,IAAI,CAACU,GAAG,CAAA,CAAA6Y,qBAAA,GACN3c,MAAM,CAACqF,SAAS,CAACoW,OAAO,KAAAkB,IAAAA,GAAAA,qBAAA,GAAInB,mBAAmB,CAACC,OAAO,EAAAjW,CAAAA,IAAA,GACvDqX,UAAU,IAAVA,IAAAA,GAAAA,UAAU,GAAI7c,MAAM,CAACqF,SAAS,CAAC4P,IAAI,KAAA,IAAA,GAAAzP,IAAA,GAAIgW,mBAAmB,CAACvG,IAC7D,CAAC,EAAA,CAAA2H,qBAAA,GACD5c,MAAM,CAACqF,SAAS,CAACqW,OAAO,KAAAkB,IAAAA,GAAAA,qBAAA,GAAIpB,mBAAmB,CAACE,OAClD,CAAC,CAAA;OACF,CAAA;EAED1b,IAAAA,MAAM,CAAC8c,QAAQ,GAAG7a,IAAI,CACpBiW,QAAQ,IAAI,CACVA,QAAQ,EACRC,sBAAsB,CAAC7T,KAAK,EAAE4T,QAAQ,CAAC,EACvC5T,KAAK,CAAC6D,QAAQ,EAAE,CAACiU,YAAY,CAC9B,EACD,CAAClE,QAAQ,EAAE1R,OAAO,KAChBA,OAAO,CACJuW,KAAK,CAAC,CAAC,EAAE/c,MAAM,CAACiY,QAAQ,CAACC,QAAQ,CAAC,CAAC,CACnCvK,MAAM,CAAC,CAACiG,GAAG,EAAE5T,MAAM,KAAK4T,GAAG,GAAG5T,MAAM,CAAC0c,OAAO,EAAE,EAAE,CAAC,CAAC,EACvD1Y,cAAc,CAACM,KAAK,CAACO,OAAO,EAAE,cAAc,EAAE,UAAU,CAC1D,CAAC,CAAA;EAED7E,IAAAA,MAAM,CAACgd,QAAQ,GAAG/a,IAAI,CACpBiW,QAAQ,IAAI,CACVA,QAAQ,EACRC,sBAAsB,CAAC7T,KAAK,EAAE4T,QAAQ,CAAC,EACvC5T,KAAK,CAAC6D,QAAQ,EAAE,CAACiU,YAAY,CAC9B,EACD,CAAClE,QAAQ,EAAE1R,OAAO,KAChBA,OAAO,CACJuW,KAAK,CAAC/c,MAAM,CAACiY,QAAQ,CAACC,QAAQ,CAAC,GAAG,CAAC,CAAC,CACpCvK,MAAM,CAAC,CAACiG,GAAG,EAAE5T,MAAM,KAAK4T,GAAG,GAAG5T,MAAM,CAAC0c,OAAO,EAAE,EAAE,CAAC,CAAC,EACvD1Y,cAAc,CAACM,KAAK,CAACO,OAAO,EAAE,cAAc,EAAE,UAAU,CAC1D,CAAC,CAAA;MAED7E,MAAM,CAACid,SAAS,GAAG,MAAM;EACvB3Y,MAAAA,KAAK,CAAC4Y,eAAe,CAACC,KAAA,IAAiC;UAAA,IAAhC;EAAE,UAAA,CAACnd,MAAM,CAAC+E,EAAE,GAAGqY,CAAC;YAAE,GAAGC,IAAAA;EAAK,SAAC,GAAAF,KAAA,CAAA;EAChD,QAAA,OAAOE,IAAI,CAAA;EACb,OAAC,CAAC,CAAA;OACH,CAAA;MACDrd,MAAM,CAACsd,YAAY,GAAG,MAAM;QAAA,IAAA3L,qBAAA,EAAAC,qBAAA,CAAA;QAC1B,OACE,CAAA,CAAAD,qBAAA,GAAC3R,MAAM,CAACqF,SAAS,CAACkY,cAAc,KAAA5L,IAAAA,GAAAA,qBAAA,GAAI,IAAI,OAAAC,qBAAA,GACvCtN,KAAK,CAACO,OAAO,CAAC2Y,oBAAoB,KAAA5L,IAAAA,GAAAA,qBAAA,GAAI,IAAI,CAAC,CAAA;OAE/C,CAAA;MACD5R,MAAM,CAACyd,aAAa,GAAG,MAAM;EAC3B,MAAA,OAAOnZ,KAAK,CAAC6D,QAAQ,EAAE,CAACkU,gBAAgB,CAACJ,gBAAgB,KAAKjc,MAAM,CAAC+E,EAAE,CAAA;OACxE,CAAA;KACF;EAEDkC,EAAAA,YAAY,EAAEA,CACZhB,MAA6B,EAC7B3B,KAAmB,KACV;MACT2B,MAAM,CAACyW,OAAO,GAAG,MAAM;QACrB,IAAI9I,GAAG,GAAG,CAAC,CAAA;QAEX,MAAMlS,OAAO,GAAIuE,MAA6B,IAAK;EACjD,QAAA,IAAIA,MAAM,CAACoB,UAAU,CAACrF,MAAM,EAAE;EAC5BiE,UAAAA,MAAM,CAACoB,UAAU,CAACzF,OAAO,CAACF,OAAO,CAAC,CAAA;EACpC,SAAC,MAAM;EAAA,UAAA,IAAAgc,qBAAA,CAAA;EACL9J,UAAAA,GAAG,IAAA8J,CAAAA,qBAAA,GAAIzX,MAAM,CAACjG,MAAM,CAAC0c,OAAO,EAAE,KAAAgB,IAAAA,GAAAA,qBAAA,GAAI,CAAC,CAAA;EACrC,SAAA;SACD,CAAA;QAEDhc,OAAO,CAACuE,MAAM,CAAC,CAAA;EAEf,MAAA,OAAO2N,GAAG,CAAA;OACX,CAAA;MACD3N,MAAM,CAAC6W,QAAQ,GAAG,MAAM;EACtB,MAAA,IAAI7W,MAAM,CAACjD,KAAK,GAAG,CAAC,EAAE;EACpB,QAAA,MAAM2a,iBAAiB,GAAG1X,MAAM,CAACuB,WAAW,CAACsC,OAAO,CAAC7D,MAAM,CAACjD,KAAK,GAAG,CAAC,CAAE,CAAA;UACvE,OAAO2a,iBAAiB,CAACb,QAAQ,EAAE,GAAGa,iBAAiB,CAACjB,OAAO,EAAE,CAAA;EACnE,OAAA;EAEA,MAAA,OAAO,CAAC,CAAA;OACT,CAAA;EACDzW,IAAAA,MAAM,CAAC2X,gBAAgB,GAAGC,gBAAgB,IAAI;QAC5C,MAAM7d,MAAM,GAAGsE,KAAK,CAACuI,SAAS,CAAC5G,MAAM,CAACjG,MAAM,CAAC+E,EAAE,CAAC,CAAA;QAChD,MAAM+Y,SAAS,GAAG9d,MAAM,IAAA,IAAA,GAAA,KAAA,CAAA,GAANA,MAAM,CAAEsd,YAAY,EAAE,CAAA;EAExC,MAAA,OAAQS,CAAU,IAAK;EACrB,QAAA,IAAI,CAAC/d,MAAM,IAAI,CAAC8d,SAAS,EAAE;EACzB,UAAA,OAAA;EACF,SAAA;EAEEC,QAAAA,CAAC,CAASC,OAAO,IAAA,IAAA,IAAjBD,CAAC,CAASC,OAAO,EAAI,CAAA;EAEvB,QAAA,IAAIC,iBAAiB,CAACF,CAAC,CAAC,EAAE;EACxB;YACA,IAAIA,CAAC,CAACG,OAAO,IAAIH,CAAC,CAACG,OAAO,CAAClc,MAAM,GAAG,CAAC,EAAE;EACrC,YAAA,OAAA;EACF,WAAA;EACF,SAAA;EAEA,QAAA,MAAM8Z,SAAS,GAAG7V,MAAM,CAACyW,OAAO,EAAE,CAAA;EAElC,QAAA,MAAMR,iBAAqC,GAAGjW,MAAM,GAChDA,MAAM,CAACwB,cAAc,EAAE,CAACI,GAAG,CAAC9G,CAAC,IAAI,CAACA,CAAC,CAACf,MAAM,CAAC+E,EAAE,EAAEhE,CAAC,CAACf,MAAM,CAAC0c,OAAO,EAAE,CAAC,CAAC,GACnE,CAAC,CAAC1c,MAAM,CAAC+E,EAAE,EAAE/E,MAAM,CAAC0c,OAAO,EAAE,CAAC,CAAC,CAAA;UAEnC,MAAMyB,OAAO,GAAGF,iBAAiB,CAACF,CAAC,CAAC,GAChC3a,IAAI,CAACC,KAAK,CAAC0a,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC,CAAEC,OAAO,CAAC,GAChCJ,CAAC,CAAgBI,OAAO,CAAA;UAE7B,MAAMC,eAAkC,GAAG,EAAE,CAAA;EAE7C,QAAA,MAAMC,YAAY,GAAGA,CACnBC,SAAyB,EACzBC,UAAmB,KAChB;EACH,UAAA,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;EAClC,YAAA,OAAA;EACF,WAAA;EAEAja,UAAAA,KAAK,CAACka,mBAAmB,CAAC3d,GAAG,IAAI;cAAA,IAAA4d,gBAAA,EAAAC,cAAA,CAAA;EAC/B,YAAA,MAAMC,cAAc,GAClBra,KAAK,CAACO,OAAO,CAAC0X,qBAAqB,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;EACxD,YAAA,MAAMR,WAAW,GACf,CAACwC,UAAU,IAAAE,CAAAA,gBAAA,GAAI5d,GAAG,IAAA,IAAA,GAAA,KAAA,CAAA,GAAHA,GAAG,CAAEgb,WAAW,KAAA4C,IAAAA,GAAAA,gBAAA,GAAI,CAAC,CAAC,IAAIE,cAAc,CAAA;cACzD,MAAM3C,eAAe,GAAG5Y,IAAI,CAACU,GAAG,CAC9BiY,WAAW,IAAA2C,CAAAA,cAAA,GAAI7d,GAAG,oBAAHA,GAAG,CAAEib,SAAS,KAAA,IAAA,GAAA4C,cAAA,GAAI,CAAC,CAAC,EACnC,CAAC,QACH,CAAC,CAAA;EAED7d,YAAAA,GAAG,CAACqb,iBAAiB,CAACta,OAAO,CAACgd,KAAA,IAA4B;EAAA,cAAA,IAA3B,CAACpa,QAAQ,EAAEqa,UAAU,CAAC,GAAAD,KAAA,CAAA;gBACnDR,eAAe,CAAC5Z,QAAQ,CAAC,GACvBpB,IAAI,CAACC,KAAK,CACRD,IAAI,CAACU,GAAG,CAAC+a,UAAU,GAAGA,UAAU,GAAG7C,eAAe,EAAE,CAAC,CAAC,GAAG,GAC3D,CAAC,GAAG,GAAG,CAAA;EACX,aAAC,CAAC,CAAA;cAEF,OAAO;EACL,cAAA,GAAGnb,GAAG;gBACNkb,WAAW;EACXC,cAAAA,eAAAA;eACD,CAAA;EACH,WAAC,CAAC,CAAA;YAEF,IACE1X,KAAK,CAACO,OAAO,CAACyX,gBAAgB,KAAK,UAAU,IAC7CgC,SAAS,KAAK,KAAK,EACnB;EACAha,YAAAA,KAAK,CAAC4Y,eAAe,CAACrc,GAAG,KAAK;EAC5B,cAAA,GAAGA,GAAG;gBACN,GAAGud,eAAAA;EACL,aAAC,CAAC,CAAC,CAAA;EACL,WAAA;WACD,CAAA;UAED,MAAMU,MAAM,GAAIP,UAAmB,IAAKF,YAAY,CAAC,MAAM,EAAEE,UAAU,CAAC,CAAA;UAExE,MAAMQ,KAAK,GAAIR,UAAmB,IAAK;EACrCF,UAAAA,YAAY,CAAC,KAAK,EAAEE,UAAU,CAAC,CAAA;EAE/Bja,UAAAA,KAAK,CAACka,mBAAmB,CAAC3d,GAAG,KAAK;EAChC,YAAA,GAAGA,GAAG;EACNob,YAAAA,gBAAgB,EAAE,KAAK;EACvBJ,YAAAA,WAAW,EAAE,IAAI;EACjBC,YAAAA,SAAS,EAAE,IAAI;EACfC,YAAAA,WAAW,EAAE,IAAI;EACjBC,YAAAA,eAAe,EAAE,IAAI;EACrBE,YAAAA,iBAAiB,EAAE,EAAA;EACrB,WAAC,CAAC,CAAC,CAAA;WACJ,CAAA;EAED,QAAA,MAAM8C,eAAe,GAAG3D,oBAAoB,CAACwC,gBAAgB,CAAC,CAAA;EAE9D,QAAA,MAAMoB,WAAW,GAAG;YAClBC,WAAW,EAAGnB,CAAa,IAAKe,MAAM,CAACf,CAAC,CAACI,OAAO,CAAC;YACjDgB,SAAS,EAAGpB,CAAa,IAAK;cAC5BiB,eAAe,IAAA,IAAA,IAAfA,eAAe,CAAEI,mBAAmB,CAClC,WAAW,EACXH,WAAW,CAACC,WACd,CAAC,CAAA;cACDF,eAAe,IAAA,IAAA,IAAfA,eAAe,CAAEI,mBAAmB,CAClC,SAAS,EACTH,WAAW,CAACE,SACd,CAAC,CAAA;EACDJ,YAAAA,KAAK,CAAChB,CAAC,CAACI,OAAO,CAAC,CAAA;EAClB,WAAA;WACD,CAAA;EAED,QAAA,MAAMkB,WAAW,GAAG;YAClBH,WAAW,EAAGnB,CAAa,IAAK;cAC9B,IAAIA,CAAC,CAACuB,UAAU,EAAE;gBAChBvB,CAAC,CAACwB,cAAc,EAAE,CAAA;gBAClBxB,CAAC,CAACyB,eAAe,EAAE,CAAA;EACrB,aAAA;cACAV,MAAM,CAACf,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC,CAAEC,OAAO,CAAC,CAAA;EAC7B,YAAA,OAAO,KAAK,CAAA;aACb;YACDgB,SAAS,EAAGpB,CAAa,IAAK;EAAA,YAAA,IAAA0B,WAAA,CAAA;cAC5BT,eAAe,IAAA,IAAA,IAAfA,eAAe,CAAEI,mBAAmB,CAClC,WAAW,EACXC,WAAW,CAACH,WACd,CAAC,CAAA;cACDF,eAAe,IAAA,IAAA,IAAfA,eAAe,CAAEI,mBAAmB,CAClC,UAAU,EACVC,WAAW,CAACF,SACd,CAAC,CAAA;cACD,IAAIpB,CAAC,CAACuB,UAAU,EAAE;gBAChBvB,CAAC,CAACwB,cAAc,EAAE,CAAA;gBAClBxB,CAAC,CAACyB,eAAe,EAAE,CAAA;EACrB,aAAA;EACAT,YAAAA,KAAK,CAAAU,CAAAA,WAAA,GAAC1B,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC,KAAA,IAAA,GAAA,KAAA,CAAA,GAAZuB,WAAA,CAActB,OAAO,CAAC,CAAA;EAC9B,WAAA;WACD,CAAA;EAED,QAAA,MAAMuB,kBAAkB,GAAGC,qBAAqB,EAAE,GAC9C;EAAEC,UAAAA,OAAO,EAAE,KAAA;EAAM,SAAC,GAClB,KAAK,CAAA;EAET,QAAA,IAAI3B,iBAAiB,CAACF,CAAC,CAAC,EAAE;EACxBiB,UAAAA,eAAe,IAAfA,IAAAA,IAAAA,eAAe,CAAEa,gBAAgB,CAC/B,WAAW,EACXR,WAAW,CAACH,WAAW,EACvBQ,kBACF,CAAC,CAAA;EACDV,UAAAA,eAAe,IAAfA,IAAAA,IAAAA,eAAe,CAAEa,gBAAgB,CAC/B,UAAU,EACVR,WAAW,CAACF,SAAS,EACrBO,kBACF,CAAC,CAAA;EACH,SAAC,MAAM;EACLV,UAAAA,eAAe,IAAfA,IAAAA,IAAAA,eAAe,CAAEa,gBAAgB,CAC/B,WAAW,EACXZ,WAAW,CAACC,WAAW,EACvBQ,kBACF,CAAC,CAAA;EACDV,UAAAA,eAAe,IAAfA,IAAAA,IAAAA,eAAe,CAAEa,gBAAgB,CAC/B,SAAS,EACTZ,WAAW,CAACE,SAAS,EACrBO,kBACF,CAAC,CAAA;EACH,SAAA;EAEApb,QAAAA,KAAK,CAACka,mBAAmB,CAAC3d,GAAG,KAAK;EAChC,UAAA,GAAGA,GAAG;EACNgb,UAAAA,WAAW,EAAEsC,OAAO;YACpBrC,SAAS;EACTC,UAAAA,WAAW,EAAE,CAAC;EACdC,UAAAA,eAAe,EAAE,CAAC;YAClBE,iBAAiB;YACjBD,gBAAgB,EAAEjc,MAAM,CAAC+E,EAAAA;EAC3B,SAAC,CAAC,CAAC,CAAA;SACJ,CAAA;OACF,CAAA;KACF;IAEDgD,WAAW,EAA0BzD,KAAmB,IAAW;EACjEA,IAAAA,KAAK,CAAC4Y,eAAe,GAAG5c,OAAO,IAC7BgE,KAAK,CAACO,OAAO,CAAC2X,oBAAoB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAlClY,KAAK,CAACO,OAAO,CAAC2X,oBAAoB,CAAGlc,OAAO,CAAC,CAAA;EAC/CgE,IAAAA,KAAK,CAACka,mBAAmB,GAAGle,OAAO,IACjCgE,KAAK,CAACO,OAAO,CAAC4X,wBAAwB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAtCnY,KAAK,CAACO,OAAO,CAAC4X,wBAAwB,CAAGnc,OAAO,CAAC,CAAA;EACnDgE,IAAAA,KAAK,CAACwb,iBAAiB,GAAGzM,YAAY,IAAI;EAAA,MAAA,IAAAC,qBAAA,CAAA;QACxChP,KAAK,CAAC4Y,eAAe,CACnB7J,YAAY,GAAG,EAAE,IAAAC,qBAAA,GAAGhP,KAAK,CAACkP,YAAY,CAAC4I,YAAY,KAAA,IAAA,GAAA9I,qBAAA,GAAI,EACzD,CAAC,CAAA;OACF,CAAA;EACDhP,IAAAA,KAAK,CAACyb,mBAAmB,GAAG1M,YAAY,IAAI;EAAA,MAAA,IAAA2M,sBAAA,CAAA;QAC1C1b,KAAK,CAACka,mBAAmB,CACvBnL,YAAY,GACRuI,+BAA+B,EAAE,GAAA,CAAAoE,sBAAA,GACjC1b,KAAK,CAACkP,YAAY,CAAC6I,gBAAgB,KAAA,IAAA,GAAA2D,sBAAA,GACjCpE,+BAA+B,EACvC,CAAC,CAAA;OACF,CAAA;MACDtX,KAAK,CAAC2b,YAAY,GAAG,MAAA;QAAA,IAAAC,qBAAA,EAAAC,sBAAA,CAAA;QAAA,OAAAD,CAAAA,qBAAA,IAAAC,sBAAA,GACnB7b,KAAK,CAAC0D,eAAe,EAAE,CAAC,CAAC,CAAC,KAA1BmY,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,sBAAA,CAA4BrW,OAAO,CAAC6D,MAAM,CAAC,CAACiG,GAAG,EAAE3N,MAAM,KAAK;EAC1D,QAAA,OAAO2N,GAAG,GAAG3N,MAAM,CAACyW,OAAO,EAAE,CAAA;EAC/B,OAAC,EAAE,CAAC,CAAC,KAAAwD,IAAAA,GAAAA,qBAAA,GAAI,CAAC,CAAA;EAAA,KAAA,CAAA;MACZ5b,KAAK,CAAC8b,gBAAgB,GAAG,MAAA;QAAA,IAAAC,qBAAA,EAAAC,sBAAA,CAAA;QAAA,OAAAD,CAAAA,qBAAA,IAAAC,sBAAA,GACvBhc,KAAK,CAAC6E,mBAAmB,EAAE,CAAC,CAAC,CAAC,KAA9BmX,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,sBAAA,CAAgCxW,OAAO,CAAC6D,MAAM,CAAC,CAACiG,GAAG,EAAE3N,MAAM,KAAK;EAC9D,QAAA,OAAO2N,GAAG,GAAG3N,MAAM,CAACyW,OAAO,EAAE,CAAA;EAC/B,OAAC,EAAE,CAAC,CAAC,KAAA2D,IAAAA,GAAAA,qBAAA,GAAI,CAAC,CAAA;EAAA,KAAA,CAAA;MACZ/b,KAAK,CAACic,kBAAkB,GAAG,MAAA;QAAA,IAAAC,qBAAA,EAAAC,sBAAA,CAAA;QAAA,OAAAD,CAAAA,qBAAA,IAAAC,sBAAA,GACzBnc,KAAK,CAAC4E,qBAAqB,EAAE,CAAC,CAAC,CAAC,KAAhCuX,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,sBAAA,CAAkC3W,OAAO,CAAC6D,MAAM,CAAC,CAACiG,GAAG,EAAE3N,MAAM,KAAK;EAChE,QAAA,OAAO2N,GAAG,GAAG3N,MAAM,CAACyW,OAAO,EAAE,CAAA;EAC/B,OAAC,EAAE,CAAC,CAAC,KAAA8D,IAAAA,GAAAA,qBAAA,GAAI,CAAC,CAAA;EAAA,KAAA,CAAA;MACZlc,KAAK,CAACoc,iBAAiB,GAAG,MAAA;QAAA,IAAAC,qBAAA,EAAAC,sBAAA,CAAA;QAAA,OAAAD,CAAAA,qBAAA,IAAAC,sBAAA,GACxBtc,KAAK,CAACgF,oBAAoB,EAAE,CAAC,CAAC,CAAC,KAA/BsX,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,sBAAA,CAAiC9W,OAAO,CAAC6D,MAAM,CAAC,CAACiG,GAAG,EAAE3N,MAAM,KAAK;EAC/D,QAAA,OAAO2N,GAAG,GAAG3N,MAAM,CAACyW,OAAO,EAAE,CAAA;EAC/B,OAAC,EAAE,CAAC,CAAC,KAAAiE,IAAAA,GAAAA,qBAAA,GAAI,CAAC,CAAA;EAAA,KAAA,CAAA;EACd,GAAA;EACF,EAAC;EAED,IAAIE,gBAAgC,GAAG,IAAI,CAAA;EACpC,SAASlB,qBAAqBA,GAAG;EACtC,EAAA,IAAI,OAAOkB,gBAAgB,KAAK,SAAS,EAAE,OAAOA,gBAAgB,CAAA;IAElE,IAAIC,SAAS,GAAG,KAAK,CAAA;IACrB,IAAI;EACF,IAAA,MAAMjc,OAAO,GAAG;QACd,IAAI+a,OAAOA,GAAG;EACZkB,QAAAA,SAAS,GAAG,IAAI,CAAA;EAChB,QAAA,OAAO,KAAK,CAAA;EACd,OAAA;OACD,CAAA;EAED,IAAA,MAAMtgB,IAAI,GAAGA,MAAM,EAAE,CAAA;MAErBugB,MAAM,CAAClB,gBAAgB,CAAC,MAAM,EAAErf,IAAI,EAAEqE,OAAO,CAAC,CAAA;EAC9Ckc,IAAAA,MAAM,CAAC3B,mBAAmB,CAAC,MAAM,EAAE5e,IAAI,CAAC,CAAA;KACzC,CAAC,OAAOwgB,GAAG,EAAE;EACZF,IAAAA,SAAS,GAAG,KAAK,CAAA;EACnB,GAAA;EACAD,EAAAA,gBAAgB,GAAGC,SAAS,CAAA;EAC5B,EAAA,OAAOD,gBAAgB,CAAA;EACzB,CAAA;EAEA,SAAS5C,iBAAiBA,CAACF,CAAU,EAAmB;EACtD,EAAA,OAAQA,CAAC,CAAgBkD,IAAI,KAAK,YAAY,CAAA;EAChD;;EC7aA;;AAEO,QAAMC,gBAA8B,GAAG;IAC5CvQ,eAAe,EAAGC,KAAK,IAA2B;MAChD,OAAO;QACLuQ,gBAAgB,EAAE,EAAE;QACpB,GAAGvQ,KAAAA;OACJ,CAAA;KACF;IAEDE,iBAAiB,EACfxM,KAAmB,IACU;MAC7B,OAAO;EACL8c,MAAAA,wBAAwB,EAAE3gB,gBAAgB,CAAC,kBAAkB,EAAE6D,KAAK,CAAA;OACrE,CAAA;KACF;EAEDc,EAAAA,YAAY,EAAEA,CACZpF,MAA6B,EAC7BsE,KAAmB,KACV;EACTtE,IAAAA,MAAM,CAACqhB,gBAAgB,GAAG/P,KAAK,IAAI;EACjC,MAAA,IAAItR,MAAM,CAACshB,UAAU,EAAE,EAAE;EACvBhd,QAAAA,KAAK,CAACid,mBAAmB,CAAC1gB,GAAG,KAAK;EAChC,UAAA,GAAGA,GAAG;EACN,UAAA,CAACb,MAAM,CAAC+E,EAAE,GAAGuM,KAAK,IAAA,IAAA,GAALA,KAAK,GAAI,CAACtR,MAAM,CAACsL,YAAY,EAAC;EAC7C,SAAC,CAAC,CAAC,CAAA;EACL,OAAA;OACD,CAAA;MACDtL,MAAM,CAACsL,YAAY,GAAG,MAAM;QAAA,IAAA9F,IAAA,EAAA4M,qBAAA,CAAA;EAC1B,MAAA,MAAMoP,YAAY,GAAGxhB,MAAM,CAACwG,OAAO,CAAA;EACnC,MAAA,OAAA,CAAAhB,IAAA,GACGgc,YAAY,CAACxf,MAAM,GAChBwf,YAAY,CAAC1e,IAAI,CAAC2e,CAAC,IAAIA,CAAC,CAACnW,YAAY,EAAE,CAAC,GAAA,CAAA8G,qBAAA,GACxC9N,KAAK,CAAC6D,QAAQ,EAAE,CAACgZ,gBAAgB,KAAA,IAAA,GAAA,KAAA,CAAA,GAAjC/O,qBAAA,CAAoCpS,MAAM,CAAC+E,EAAE,CAAC,KAAAS,IAAAA,GAAAA,IAAA,GAAK,IAAI,CAAA;OAE9D,CAAA;MAEDxF,MAAM,CAACshB,UAAU,GAAG,MAAM;QAAA,IAAA3P,qBAAA,EAAAC,qBAAA,CAAA;QACxB,OACE,CAAA,CAAAD,qBAAA,GAAC3R,MAAM,CAACqF,SAAS,CAACqc,YAAY,KAAA/P,IAAAA,GAAAA,qBAAA,GAAI,IAAI,OAAAC,qBAAA,GACrCtN,KAAK,CAACO,OAAO,CAAC6c,YAAY,KAAA9P,IAAAA,GAAAA,qBAAA,GAAI,IAAI,CAAC,CAAA;OAEvC,CAAA;MACD5R,MAAM,CAAC2hB,0BAA0B,GAAG,MAAM;EACxC,MAAA,OAAQ5D,CAAU,IAAK;EACrB/d,QAAAA,MAAM,CAACqhB,gBAAgB,IAAvBrhB,IAAAA,IAAAA,MAAM,CAACqhB,gBAAgB,CACnBtD,CAAC,CAAgB6D,MAAM,CAAsBC,OACjD,CAAC,CAAA;SACF,CAAA;OACF,CAAA;KACF;EAEDxV,EAAAA,SAAS,EAAEA,CACT9H,GAAe,EACfD,KAAmB,KACV;MACTC,GAAG,CAACgW,mBAAmB,GAAGtY,IAAI,CAC5B,MAAM,CAACsC,GAAG,CAACgJ,WAAW,EAAE,EAAEjJ,KAAK,CAAC6D,QAAQ,EAAE,CAACgZ,gBAAgB,CAAC,EAC5DzG,KAAK,IAAI;EACP,MAAA,OAAOA,KAAK,CAAC9R,MAAM,CAACjE,IAAI,IAAIA,IAAI,CAAC3E,MAAM,CAACsL,YAAY,EAAE,CAAC,CAAA;OACxD,EACDtH,cAAc,CAACM,KAAK,CAACO,OAAO,EAAE,WAAW,EAAE,qBAAqB,CAClE,CAAC,CAAA;MACDN,GAAG,CAACud,eAAe,GAAG7f,IAAI,CACxB,MAAM,CACJsC,GAAG,CAACkW,mBAAmB,EAAE,EACzBlW,GAAG,CAAC+V,qBAAqB,EAAE,EAC3B/V,GAAG,CAACoW,oBAAoB,EAAE,CAC3B,EACD,CAACtS,IAAI,EAAEoC,MAAM,EAAEnC,KAAK,KAAK,CAAC,GAAGD,IAAI,EAAE,GAAGoC,MAAM,EAAE,GAAGnC,KAAK,CAAC,EACvDtE,cAAc,CAACM,KAAK,CAACO,OAAO,EAAE,WAAW,EAAE,iBAAiB,CAC9D,CAAC,CAAA;KACF;IAEDkD,WAAW,EAA0BzD,KAAmB,IAAW;EACjE,IAAA,MAAMyd,wBAAwB,GAAGA,CAC/BrhB,GAAW,EACXshB,UAA0C,KACL;EACrC,MAAA,OAAO/f,IAAI,CACT,MAAM,CACJ+f,UAAU,EAAE,EACZA,UAAU,EAAE,CACTpZ,MAAM,CAAC7H,CAAC,IAAIA,CAAC,CAACuK,YAAY,EAAE,CAAC,CAC7BzD,GAAG,CAAC9G,CAAC,IAAIA,CAAC,CAACgE,EAAE,CAAC,CACd0G,IAAI,CAAC,GAAG,CAAC,CACb,EACDjF,OAAO,IAAI;EACT,QAAA,OAAOA,OAAO,CAACoC,MAAM,CAAC7H,CAAC,IAAIA,CAAC,CAACuK,YAAY,oBAAdvK,CAAC,CAACuK,YAAY,EAAI,CAAC,CAAA;SAC/C,EACDtH,cAAc,CAACM,KAAK,CAACO,OAAO,EAAE,cAAc,EAAEnE,GAAG,CACnD,CAAC,CAAA;OACF,CAAA;EAED4D,IAAAA,KAAK,CAAC2d,qBAAqB,GAAGF,wBAAwB,CACpD,uBAAuB,EACvB,MAAMzd,KAAK,CAAC4d,iBAAiB,EAC/B,CAAC,CAAA;EACD5d,IAAAA,KAAK,CAAC4D,qBAAqB,GAAG6Z,wBAAwB,CACpD,uBAAuB,EACvB,MAAMzd,KAAK,CAACkJ,iBAAiB,EAC/B,CAAC,CAAA;EACDlJ,IAAAA,KAAK,CAAC6d,yBAAyB,GAAGJ,wBAAwB,CACxD,2BAA2B,EAC3B,MAAMzd,KAAK,CAAC4W,kBAAkB,EAChC,CAAC,CAAA;EACD5W,IAAAA,KAAK,CAAC8d,0BAA0B,GAAGL,wBAAwB,CACzD,4BAA4B,EAC5B,MAAMzd,KAAK,CAAC6W,mBAAmB,EACjC,CAAC,CAAA;EACD7W,IAAAA,KAAK,CAAC+d,2BAA2B,GAAGN,wBAAwB,CAC1D,6BAA6B,EAC7B,MAAMzd,KAAK,CAAC8W,oBAAoB,EAClC,CAAC,CAAA;EAED9W,IAAAA,KAAK,CAACid,mBAAmB,GAAGjhB,OAAO,IACjCgE,KAAK,CAACO,OAAO,CAACuc,wBAAwB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAtC9c,KAAK,CAACO,OAAO,CAACuc,wBAAwB,CAAG9gB,OAAO,CAAC,CAAA;EAEnDgE,IAAAA,KAAK,CAACge,qBAAqB,GAAGjP,YAAY,IAAI;EAAA,MAAA,IAAAC,qBAAA,CAAA;QAC5ChP,KAAK,CAACid,mBAAmB,CACvBlO,YAAY,GAAG,EAAE,IAAAC,qBAAA,GAAGhP,KAAK,CAACkP,YAAY,CAAC2N,gBAAgB,KAAA,IAAA,GAAA7N,qBAAA,GAAI,EAC7D,CAAC,CAAA;OACF,CAAA;EAEDhP,IAAAA,KAAK,CAACie,uBAAuB,GAAGjR,KAAK,IAAI;EAAA,MAAA,IAAAkR,MAAA,CAAA;EACvClR,MAAAA,KAAK,GAAAkR,CAAAA,MAAA,GAAGlR,KAAK,KAAAkR,IAAAA,GAAAA,MAAA,GAAI,CAACle,KAAK,CAACme,sBAAsB,EAAE,CAAA;EAEhDne,MAAAA,KAAK,CAACid,mBAAmB,CACvBjd,KAAK,CAACkJ,iBAAiB,EAAE,CAACG,MAAM,CAC9B,CAAC+U,GAAG,EAAE1iB,MAAM,MAAM;EAChB,QAAA,GAAG0iB,GAAG;EACN,QAAA,CAAC1iB,MAAM,CAAC+E,EAAE,GAAG,CAACuM,KAAK,GAAG,EAACtR,MAAM,CAACshB,UAAU,IAAjBthB,IAAAA,IAAAA,MAAM,CAACshB,UAAU,EAAI,CAAGhQ,GAAAA,KAAAA;EACjD,OAAC,CAAC,EACF,EACF,CACF,CAAC,CAAA;OACF,CAAA;MAEDhN,KAAK,CAACme,sBAAsB,GAAG,MAC7B,CAACne,KAAK,CAACkJ,iBAAiB,EAAE,CAAC1K,IAAI,CAAC9C,MAAM,IAAI,EAACA,MAAM,CAACsL,YAAY,IAAnBtL,IAAAA,IAAAA,MAAM,CAACsL,YAAY,EAAI,CAAC,CAAA,CAAA;MAErEhH,KAAK,CAACqe,uBAAuB,GAAG,MAC9Bre,KAAK,CAACkJ,iBAAiB,EAAE,CAAC1K,IAAI,CAAC9C,MAAM,IAAIA,MAAM,CAACsL,YAAY,IAAA,IAAA,GAAA,KAAA,CAAA,GAAnBtL,MAAM,CAACsL,YAAY,EAAI,CAAC,CAAA;MAEnEhH,KAAK,CAACse,oCAAoC,GAAG,MAAM;EACjD,MAAA,OAAQ7E,CAAU,IAAK;EAAA,QAAA,IAAA8E,OAAA,CAAA;EACrBve,QAAAA,KAAK,CAACie,uBAAuB,CAAAM,CAAAA,OAAA,GACzB9E,CAAC,CAAgB6D,MAAM,KAAzBiB,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,OAAA,CAAgDhB,OAClD,CAAC,CAAA;SACF,CAAA;OACF,CAAA;EACH,GAAA;EACF,EAAC;EAEM,SAAS1J,sBAAsBA,CACpC7T,KAAmB,EACnB4T,QAA2C,EAC3C;EACA,EAAA,OAAO,CAACA,QAAQ,GACZ5T,KAAK,CAAC4D,qBAAqB,EAAE,GAC7BgQ,QAAQ,KAAK,QAAQ,GACnB5T,KAAK,CAAC+d,2BAA2B,EAAE,GACnCnK,QAAQ,KAAK,MAAM,GACjB5T,KAAK,CAAC6d,yBAAyB,EAAE,GACjC7d,KAAK,CAAC8d,0BAA0B,EAAE,CAAA;EAC5C;;ECjSA;;AAEO,QAAMU,cAA4B,GAAG;IAC1C/a,WAAW,EAA0BzD,KAAmB,IAAW;EACjEA,IAAAA,KAAK,CAACye,yBAAyB,GAC7Bze,KAAK,CAACO,OAAO,CAACmJ,kBAAkB,IAChC1J,KAAK,CAACO,OAAO,CAACmJ,kBAAkB,CAAC1J,KAAK,EAAE,YAAY,CAAC,CAAA;MAEvDA,KAAK,CAAC0e,wBAAwB,GAAG,MAAM;QACrC,IAAI1e,KAAK,CAACO,OAAO,CAAC8O,eAAe,IAAI,CAACrP,KAAK,CAACye,yBAAyB,EAAE;EACrE,QAAA,OAAOze,KAAK,CAAC2J,sBAAsB,EAAE,CAAA;EACvC,OAAA;EAEA,MAAA,OAAO3J,KAAK,CAACye,yBAAyB,EAAE,CAAA;OACzC,CAAA;EAEDze,IAAAA,KAAK,CAAC2e,6BAA6B,GACjC3e,KAAK,CAACO,OAAO,CAACsJ,sBAAsB,IACpC7J,KAAK,CAACO,OAAO,CAACsJ,sBAAsB,CAAC7J,KAAK,EAAE,YAAY,CAAC,CAAA;MAC3DA,KAAK,CAAC4e,4BAA4B,GAAG,MAAM;EACzC,MAAA,IAAI,CAAC5e,KAAK,CAAC2e,6BAA6B,EAAE;UACxC,OAAO,IAAI7U,GAAG,EAAE,CAAA;EAClB,OAAA;EAEA,MAAA,OAAO9J,KAAK,CAAC2e,6BAA6B,EAAE,CAAA;OAC7C,CAAA;EAED3e,IAAAA,KAAK,CAAC6e,6BAA6B,GACjC7e,KAAK,CAACO,OAAO,CAACyJ,sBAAsB,IACpChK,KAAK,CAACO,OAAO,CAACyJ,sBAAsB,CAAChK,KAAK,EAAE,YAAY,CAAC,CAAA;MAC3DA,KAAK,CAAC8e,4BAA4B,GAAG,MAAM;EACzC,MAAA,IAAI,CAAC9e,KAAK,CAAC6e,6BAA6B,EAAE;EACxC,QAAA,OAAA;EACF,OAAA;EAEA,MAAA,OAAO7e,KAAK,CAAC6e,6BAA6B,EAAE,CAAA;OAC7C,CAAA;EACH,GAAA;EACF;;EC4BA;;AAEO,QAAME,eAA6B,GAAG;IAC3C1S,eAAe,EAAGC,KAAK,IAA6B;MAClD,OAAO;EACL0S,MAAAA,YAAY,EAAEtd,SAAS;QACvB,GAAG4K,KAAAA;OACJ,CAAA;KACF;IAEDE,iBAAiB,EACfxM,KAAmB,IACY;MAC/B,OAAO;EACLif,MAAAA,oBAAoB,EAAE9iB,gBAAgB,CAAC,cAAc,EAAE6D,KAAK,CAAC;EAC7Dkf,MAAAA,cAAc,EAAE,MAAM;QACtBC,wBAAwB,EAAEzjB,MAAM,IAAI;EAAA,QAAA,IAAA0jB,qBAAA,CAAA;EAClC,QAAA,MAAMpS,KAAK,GAAA,CAAAoS,qBAAA,GAAGpf,KAAK,CAChB8M,eAAe,EAAE,CACjBC,QAAQ,CAAC,CAAC,CAAC,KAAAqS,IAAAA,IAAAA,CAAAA,qBAAA,GAFAA,qBAAA,CAEEjW,sBAAsB,EAAE,CACrCzN,MAAM,CAAC+E,EAAE,CAAC,KAHC2e,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,qBAAA,CAGC9e,QAAQ,EAAE,CAAA;UAEzB,OAAO,OAAO0M,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,CAAA;EAC/D,OAAA;OACD,CAAA;KACF;EAEDlM,EAAAA,YAAY,EAAEA,CACZpF,MAA8B,EAC9BsE,KAAmB,KACV;MACTtE,MAAM,CAAC2jB,kBAAkB,GAAG,MAAM;EAAA,MAAA,IAAAhS,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAA+R,qBAAA,CAAA;EAChC,MAAA,OACE,CAAAjS,CAAAA,qBAAA,GAAC3R,MAAM,CAACqF,SAAS,CAACwe,kBAAkB,KAAAlS,IAAAA,GAAAA,qBAAA,GAAI,IAAI,OAAAC,qBAAA,GAC3CtN,KAAK,CAACO,OAAO,CAACgf,kBAAkB,KAAA,IAAA,GAAAjS,qBAAA,GAAI,IAAI,CAAC,KAAAC,CAAAA,sBAAA,GACzCvN,KAAK,CAACO,OAAO,CAACmN,aAAa,KAAA,IAAA,GAAAH,sBAAA,GAAI,IAAI,CAAC,KAAA+R,CAAAA,qBAAA,GACpCtf,KAAK,CAACO,OAAO,CAAC4e,wBAAwB,oBAAtCnf,KAAK,CAACO,OAAO,CAAC4e,wBAAwB,CAAGzjB,MAAM,CAAC,YAAA4jB,qBAAA,GAAI,IAAI,CAAC,IAC1D,CAAC,CAAC5jB,MAAM,CAACC,UAAU,CAAA;OAEtB,CAAA;KACF;IAED8H,WAAW,EAA0BzD,KAAmB,IAAW;MACjEA,KAAK,CAACwf,qBAAqB,GAAG,MAAM;QAClC,OAAOvT,SAAS,CAAChC,cAAc,CAAA;OAChC,CAAA;MAEDjK,KAAK,CAACyf,iBAAiB,GAAG,MAAM;QAAA,IAAAvS,qBAAA,EAAAC,sBAAA,CAAA;QAC9B,MAAM;EAAE+R,QAAAA,cAAc,EAAEA,cAAAA;SAAgB,GAAGlf,KAAK,CAACO,OAAO,CAAA;EAExD,MAAA,OAAO/D,UAAU,CAAC0iB,cAAc,CAAC,GAC7BA,cAAc,GACdA,cAAc,KAAK,MAAM,GACvBlf,KAAK,CAACwf,qBAAqB,EAAE,GAAAtS,CAAAA,qBAAA,GAAAC,CAAAA,sBAAA,GAC7BnN,KAAK,CAACO,OAAO,CAAC0L,SAAS,KAAvBkB,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,sBAAA,CAA0B+R,cAAc,CAAW,KAAAhS,IAAAA,GAAAA,qBAAA,GACnDjB,SAAS,CAACiT,cAAc,CAAoB,CAAA;OACnD,CAAA;EAEDlf,IAAAA,KAAK,CAAC0f,eAAe,GAAG1jB,OAAO,IAAI;EACjCgE,MAAAA,KAAK,CAACO,OAAO,CAAC0e,oBAAoB,IAAlCjf,IAAAA,IAAAA,KAAK,CAACO,OAAO,CAAC0e,oBAAoB,CAAGjjB,OAAO,CAAC,CAAA;OAC9C,CAAA;EAEDgE,IAAAA,KAAK,CAAC2f,iBAAiB,GAAG5Q,YAAY,IAAI;EACxC/O,MAAAA,KAAK,CAAC0f,eAAe,CACnB3Q,YAAY,GAAGrN,SAAS,GAAG1B,KAAK,CAACkP,YAAY,CAAC8P,YAChD,CAAC,CAAA;OACF,CAAA;EACH,GAAA;EACF;;ECKA;;AAEO,QAAMY,YAA0B,GAAG;IACxCvT,eAAe,EAAGC,KAAK,IAAyB;MAC9C,OAAO;QACLuT,QAAQ,EAAE,EAAE;QACZ,GAAGvT,KAAAA;OACJ,CAAA;KACF;IAEDE,iBAAiB,EACfxM,KAAmB,IACQ;MAC3B,OAAO;EACL8f,MAAAA,gBAAgB,EAAE3jB,gBAAgB,CAAC,UAAU,EAAE6D,KAAK,CAAC;EACrD+f,MAAAA,oBAAoB,EAAE,IAAA;OACvB,CAAA;KACF;IAEDtc,WAAW,EAA0BzD,KAAmB,IAAW;MACjE,IAAIggB,UAAU,GAAG,KAAK,CAAA;MACtB,IAAIC,MAAM,GAAG,KAAK,CAAA;MAElBjgB,KAAK,CAACkgB,kBAAkB,GAAG,MAAM;QAAA,IAAAhf,IAAA,EAAAif,qBAAA,CAAA;QAC/B,IAAI,CAACH,UAAU,EAAE;UACfhgB,KAAK,CAACogB,MAAM,CAAC,MAAM;EACjBJ,UAAAA,UAAU,GAAG,IAAI,CAAA;EACnB,SAAC,CAAC,CAAA;EACF,QAAA,OAAA;EACF,OAAA;QAEA,IAAA9e,CAAAA,IAAA,GAAAif,CAAAA,qBAAA,GACEngB,KAAK,CAACO,OAAO,CAAC8f,YAAY,KAAAF,IAAAA,GAAAA,qBAAA,GAC1BngB,KAAK,CAACO,OAAO,CAAC+f,iBAAiB,KAAA,IAAA,GAAApf,IAAA,GAC/B,CAAClB,KAAK,CAACO,OAAO,CAACggB,eAAe,EAC9B;EACA,QAAA,IAAIN,MAAM,EAAE,OAAA;EACZA,QAAAA,MAAM,GAAG,IAAI,CAAA;UACbjgB,KAAK,CAACogB,MAAM,CAAC,MAAM;YACjBpgB,KAAK,CAACwgB,aAAa,EAAE,CAAA;EACrBP,UAAAA,MAAM,GAAG,KAAK,CAAA;EAChB,SAAC,CAAC,CAAA;EACJ,OAAA;OACD,CAAA;EACDjgB,IAAAA,KAAK,CAACygB,WAAW,GAAGzkB,OAAO,IAAIgE,KAAK,CAACO,OAAO,CAACuf,gBAAgB,IAAA,IAAA,GAAA,KAAA,CAAA,GAA9B9f,KAAK,CAACO,OAAO,CAACuf,gBAAgB,CAAG9jB,OAAO,CAAC,CAAA;EACxEgE,IAAAA,KAAK,CAAC0gB,qBAAqB,GAAGb,QAAQ,IAAI;QACxC,IAAIA,QAAQ,IAARA,IAAAA,GAAAA,QAAQ,GAAI,CAAC7f,KAAK,CAAC2gB,oBAAoB,EAAE,EAAE;EAC7C3gB,QAAAA,KAAK,CAACygB,WAAW,CAAC,IAAI,CAAC,CAAA;EACzB,OAAC,MAAM;EACLzgB,QAAAA,KAAK,CAACygB,WAAW,CAAC,EAAE,CAAC,CAAA;EACvB,OAAA;OACD,CAAA;EACDzgB,IAAAA,KAAK,CAACwgB,aAAa,GAAGzR,YAAY,IAAI;QAAA,IAAA6R,qBAAA,EAAA3R,mBAAA,CAAA;QACpCjP,KAAK,CAACygB,WAAW,CAAC1R,YAAY,GAAG,EAAE,GAAA,CAAA6R,qBAAA,GAAA,CAAA3R,mBAAA,GAAGjP,KAAK,CAACkP,YAAY,KAAlBD,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,mBAAA,CAAoB4Q,QAAQ,YAAAe,qBAAA,GAAI,EAAE,CAAC,CAAA;OAC1E,CAAA;MACD5gB,KAAK,CAAC6gB,oBAAoB,GAAG,MAAM;EACjC,MAAA,OAAO7gB,KAAK,CACT8gB,wBAAwB,EAAE,CAC1B/T,QAAQ,CAACvO,IAAI,CAACyB,GAAG,IAAIA,GAAG,CAAC8gB,YAAY,EAAE,CAAC,CAAA;OAC5C,CAAA;MACD/gB,KAAK,CAACghB,+BAA+B,GAAG,MAAM;EAC5C,MAAA,OAAQvH,CAAU,IAAK;EACnBA,QAAAA,CAAC,CAASC,OAAO,IAAA,IAAA,IAAjBD,CAAC,CAASC,OAAO,EAAI,CAAA;UACvB1Z,KAAK,CAAC0gB,qBAAqB,EAAE,CAAA;SAC9B,CAAA;OACF,CAAA;MACD1gB,KAAK,CAACihB,qBAAqB,GAAG,MAAM;QAClC,MAAMpB,QAAQ,GAAG7f,KAAK,CAAC6D,QAAQ,EAAE,CAACgc,QAAQ,CAAA;EAC1C,MAAA,OAAOA,QAAQ,KAAK,IAAI,IAAIzN,MAAM,CAACpC,MAAM,CAAC6P,QAAQ,CAAC,CAACrhB,IAAI,CAAC+F,OAAO,CAAC,CAAA;OAClE,CAAA;MACDvE,KAAK,CAAC2gB,oBAAoB,GAAG,MAAM;QACjC,MAAMd,QAAQ,GAAG7f,KAAK,CAAC6D,QAAQ,EAAE,CAACgc,QAAQ,CAAA;;EAE1C;EACA,MAAA,IAAI,OAAOA,QAAQ,KAAK,SAAS,EAAE;UACjC,OAAOA,QAAQ,KAAK,IAAI,CAAA;EAC1B,OAAA;QAEA,IAAI,CAACzN,MAAM,CAAC8O,IAAI,CAACrB,QAAQ,CAAC,CAACniB,MAAM,EAAE;EACjC,QAAA,OAAO,KAAK,CAAA;EACd,OAAA;;EAEA;EACA,MAAA,IAAIsC,KAAK,CAACmhB,WAAW,EAAE,CAACpU,QAAQ,CAACvO,IAAI,CAACyB,GAAG,IAAI,CAACA,GAAG,CAACmhB,aAAa,EAAE,CAAC,EAAE;EAClE,QAAA,OAAO,KAAK,CAAA;EACd,OAAA;;EAEA;EACA,MAAA,OAAO,IAAI,CAAA;OACZ,CAAA;MACDphB,KAAK,CAACqhB,gBAAgB,GAAG,MAAM;QAC7B,IAAIva,QAAQ,GAAG,CAAC,CAAA;EAEhB,MAAA,MAAMwa,MAAM,GACVthB,KAAK,CAAC6D,QAAQ,EAAE,CAACgc,QAAQ,KAAK,IAAI,GAC9BzN,MAAM,CAAC8O,IAAI,CAAClhB,KAAK,CAACmhB,WAAW,EAAE,CAACI,QAAQ,CAAC,GACzCnP,MAAM,CAAC8O,IAAI,CAAClhB,KAAK,CAAC6D,QAAQ,EAAE,CAACgc,QAAQ,CAAC,CAAA;EAE5CyB,MAAAA,MAAM,CAAChkB,OAAO,CAACmD,EAAE,IAAI;EACnB,QAAA,MAAM+gB,OAAO,GAAG/gB,EAAE,CAACqB,KAAK,CAAC,GAAG,CAAC,CAAA;UAC7BgF,QAAQ,GAAGhI,IAAI,CAACU,GAAG,CAACsH,QAAQ,EAAE0a,OAAO,CAAC9jB,MAAM,CAAC,CAAA;EAC/C,OAAC,CAAC,CAAA;EAEF,MAAA,OAAOoJ,QAAQ,CAAA;OAChB,CAAA;MACD9G,KAAK,CAACyhB,sBAAsB,GAAG,MAAMzhB,KAAK,CAAC0hB,iBAAiB,EAAE,CAAA;MAC9D1hB,KAAK,CAAC2hB,mBAAmB,GAAG,MAAM;QAChC,IAAI,CAAC3hB,KAAK,CAAC4hB,oBAAoB,IAAI5hB,KAAK,CAACO,OAAO,CAACohB,mBAAmB,EAAE;UACpE3hB,KAAK,CAAC4hB,oBAAoB,GAAG5hB,KAAK,CAACO,OAAO,CAACohB,mBAAmB,CAAC3hB,KAAK,CAAC,CAAA;EACvE,OAAA;QAEA,IAAIA,KAAK,CAACO,OAAO,CAACggB,eAAe,IAAI,CAACvgB,KAAK,CAAC4hB,oBAAoB,EAAE;EAChE,QAAA,OAAO5hB,KAAK,CAACyhB,sBAAsB,EAAE,CAAA;EACvC,OAAA;EAEA,MAAA,OAAOzhB,KAAK,CAAC4hB,oBAAoB,EAAE,CAAA;OACpC,CAAA;KACF;EAED7Z,EAAAA,SAAS,EAAEA,CACT9H,GAAe,EACfD,KAAmB,KACV;EACTC,IAAAA,GAAG,CAAC4hB,cAAc,GAAGhC,QAAQ,IAAI;EAC/B7f,MAAAA,KAAK,CAACygB,WAAW,CAAClkB,GAAG,IAAI;EAAA,QAAA,IAAAulB,SAAA,CAAA;EACvB,QAAA,MAAMC,MAAM,GAAGxlB,GAAG,KAAK,IAAI,GAAG,IAAI,GAAG,CAAC,EAACA,GAAG,IAAHA,IAAAA,IAAAA,GAAG,CAAG0D,GAAG,CAACQ,EAAE,CAAC,CAAA,CAAA;UAEpD,IAAIuhB,WAA8B,GAAG,EAAE,CAAA;UAEvC,IAAIzlB,GAAG,KAAK,IAAI,EAAE;EAChB6V,UAAAA,MAAM,CAAC8O,IAAI,CAAClhB,KAAK,CAACmhB,WAAW,EAAE,CAACI,QAAQ,CAAC,CAACjkB,OAAO,CAAC2kB,KAAK,IAAI;EACzDD,YAAAA,WAAW,CAACC,KAAK,CAAC,GAAG,IAAI,CAAA;EAC3B,WAAC,CAAC,CAAA;EACJ,SAAC,MAAM;EACLD,UAAAA,WAAW,GAAGzlB,GAAG,CAAA;EACnB,SAAA;UAEAsjB,QAAQ,GAAA,CAAAiC,SAAA,GAAGjC,QAAQ,YAAAiC,SAAA,GAAI,CAACC,MAAM,CAAA;EAE9B,QAAA,IAAI,CAACA,MAAM,IAAIlC,QAAQ,EAAE;YACvB,OAAO;EACL,YAAA,GAAGmC,WAAW;cACd,CAAC/hB,GAAG,CAACQ,EAAE,GAAG,IAAA;aACX,CAAA;EACH,SAAA;EAEA,QAAA,IAAIshB,MAAM,IAAI,CAAClC,QAAQ,EAAE;YACvB,MAAM;EAAE,YAAA,CAAC5f,GAAG,CAACQ,EAAE,GAAGqY,CAAC;cAAE,GAAGC,IAAAA;EAAK,WAAC,GAAGiJ,WAAW,CAAA;EAC5C,UAAA,OAAOjJ,IAAI,CAAA;EACb,SAAA;EAEA,QAAA,OAAOxc,GAAG,CAAA;EACZ,OAAC,CAAC,CAAA;OACH,CAAA;MACD0D,GAAG,CAACmhB,aAAa,GAAG,MAAM;EAAA,MAAA,IAAAc,qBAAA,CAAA;QACxB,MAAMrC,QAAQ,GAAG7f,KAAK,CAAC6D,QAAQ,EAAE,CAACgc,QAAQ,CAAA;EAE1C,MAAA,OAAO,CAAC,EAAA,CAAAqC,qBAAA,GACNliB,KAAK,CAACO,OAAO,CAAC4hB,gBAAgB,IAA9BniB,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,KAAK,CAACO,OAAO,CAAC4hB,gBAAgB,CAAGliB,GAAG,CAAC,KAAAiiB,IAAAA,GAAAA,qBAAA,GACpCrC,QAAQ,KAAK,IAAI,KAAIA,QAAQ,IAAA,IAAA,GAAA,KAAA,CAAA,GAARA,QAAQ,CAAG5f,GAAG,CAACQ,EAAE,CAAC,CACzC,CAAA,CAAA;OACF,CAAA;MACDR,GAAG,CAAC8gB,YAAY,GAAG,MAAM;EAAA,MAAA,IAAAqB,qBAAA,EAAA9U,qBAAA,EAAA6F,YAAA,CAAA;EACvB,MAAA,OAAA,CAAAiP,qBAAA,GACEpiB,KAAK,CAACO,OAAO,CAAC8hB,eAAe,IAA7BriB,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,KAAK,CAACO,OAAO,CAAC8hB,eAAe,CAAGpiB,GAAG,CAAC,KAAAmiB,IAAAA,GAAAA,qBAAA,GACnC,CAAA9U,CAAAA,qBAAA,GAACtN,KAAK,CAACO,OAAO,CAAC+hB,eAAe,KAAAhV,IAAAA,GAAAA,qBAAA,GAAI,IAAI,KAAK,CAAC,EAAA6F,CAAAA,YAAA,GAAClT,GAAG,CAACiI,OAAO,KAAXiL,IAAAA,IAAAA,YAAA,CAAazV,MAAM,CAAA,CAAA;OAEpE,CAAA;MACDuC,GAAG,CAACsiB,uBAAuB,GAAG,MAAM;QAClC,IAAIC,eAAe,GAAG,IAAI,CAAA;QAC1B,IAAIzZ,UAAU,GAAG9I,GAAG,CAAA;EAEpB,MAAA,OAAOuiB,eAAe,IAAIzZ,UAAU,CAACZ,QAAQ,EAAE;UAC7CY,UAAU,GAAG/I,KAAK,CAAC4I,MAAM,CAACG,UAAU,CAACZ,QAAQ,EAAE,IAAI,CAAC,CAAA;EACpDqa,QAAAA,eAAe,GAAGzZ,UAAU,CAACqY,aAAa,EAAE,CAAA;EAC9C,OAAA;EAEA,MAAA,OAAOoB,eAAe,CAAA;OACvB,CAAA;MACDviB,GAAG,CAACwiB,wBAAwB,GAAG,MAAM;EACnC,MAAA,MAAMC,SAAS,GAAGziB,GAAG,CAAC8gB,YAAY,EAAE,CAAA;EAEpC,MAAA,OAAO,MAAM;UACX,IAAI,CAAC2B,SAAS,EAAE,OAAA;UAChBziB,GAAG,CAAC4hB,cAAc,EAAE,CAAA;SACrB,CAAA;OACF,CAAA;EACH,GAAA;EACF;;EC1KA;;EAEA,MAAMc,gBAAgB,GAAG,CAAC,CAAA;EAC1B,MAAMC,eAAe,GAAG,EAAE,CAAA;EAE1B,MAAMC,yBAAyB,GAAGA,OAAwB;EACxDC,EAAAA,SAAS,EAAEH,gBAAgB;EAC3BI,EAAAA,QAAQ,EAAEH,eAAAA;EACZ,CAAC,CAAC,CAAA;AAEK,QAAMI,aAA2B,GAAG;IACzC3W,eAAe,EAAGC,KAAK,IAA2B;MAChD,OAAO;EACL,MAAA,GAAGA,KAAK;EACR2W,MAAAA,UAAU,EAAE;UACV,GAAGJ,yBAAyB,EAAE;EAC9B,QAAA,IAAGvW,KAAK,IAAA,IAAA,GAAA,KAAA,CAAA,GAALA,KAAK,CAAE2W,UAAU;EACtB,OAAA;OACD,CAAA;KACF;IAEDzW,iBAAiB,EACfxM,KAAmB,IACU;MAC7B,OAAO;EACLkjB,MAAAA,kBAAkB,EAAE/mB,gBAAgB,CAAC,YAAY,EAAE6D,KAAK,CAAA;OACzD,CAAA;KACF;IAEDyD,WAAW,EAA0BzD,KAAmB,IAAW;MACjE,IAAIggB,UAAU,GAAG,KAAK,CAAA;MACtB,IAAIC,MAAM,GAAG,KAAK,CAAA;MAElBjgB,KAAK,CAACmjB,mBAAmB,GAAG,MAAM;QAAA,IAAAjiB,IAAA,EAAAif,qBAAA,CAAA;QAChC,IAAI,CAACH,UAAU,EAAE;UACfhgB,KAAK,CAACogB,MAAM,CAAC,MAAM;EACjBJ,UAAAA,UAAU,GAAG,IAAI,CAAA;EACnB,SAAC,CAAC,CAAA;EACF,QAAA,OAAA;EACF,OAAA;QAEA,IAAA9e,CAAAA,IAAA,GAAAif,CAAAA,qBAAA,GACEngB,KAAK,CAACO,OAAO,CAAC8f,YAAY,KAAAF,IAAAA,GAAAA,qBAAA,GAC1BngB,KAAK,CAACO,OAAO,CAAC6iB,kBAAkB,KAAA,IAAA,GAAAliB,IAAA,GAChC,CAAClB,KAAK,CAACO,OAAO,CAAC8iB,gBAAgB,EAC/B;EACA,QAAA,IAAIpD,MAAM,EAAE,OAAA;EACZA,QAAAA,MAAM,GAAG,IAAI,CAAA;UACbjgB,KAAK,CAACogB,MAAM,CAAC,MAAM;YACjBpgB,KAAK,CAACsjB,cAAc,EAAE,CAAA;EACtBrD,UAAAA,MAAM,GAAG,KAAK,CAAA;EAChB,SAAC,CAAC,CAAA;EACJ,OAAA;OACD,CAAA;EACDjgB,IAAAA,KAAK,CAACujB,aAAa,GAAGvnB,OAAO,IAAI;QAC/B,MAAMwnB,WAAqC,GAAGjnB,GAAG,IAAI;EACnD,QAAA,IAAIknB,QAAQ,GAAG1nB,gBAAgB,CAACC,OAAO,EAAEO,GAAG,CAAC,CAAA;EAE7C,QAAA,OAAOknB,QAAQ,CAAA;SAChB,CAAA;EAED,MAAA,OAAOzjB,KAAK,CAACO,OAAO,CAAC2iB,kBAAkB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAhCljB,KAAK,CAACO,OAAO,CAAC2iB,kBAAkB,CAAGM,WAAW,CAAC,CAAA;OACvD,CAAA;EACDxjB,IAAAA,KAAK,CAAC0jB,eAAe,GAAG3U,YAAY,IAAI;EAAA,MAAA,IAAA4U,qBAAA,CAAA;QACtC3jB,KAAK,CAACujB,aAAa,CACjBxU,YAAY,GACR8T,yBAAyB,EAAE,GAAA,CAAAc,qBAAA,GAC3B3jB,KAAK,CAACkP,YAAY,CAAC+T,UAAU,KAAA,IAAA,GAAAU,qBAAA,GAAId,yBAAyB,EAChE,CAAC,CAAA;OACF,CAAA;EACD7iB,IAAAA,KAAK,CAAC4jB,YAAY,GAAG5nB,OAAO,IAAI;EAC9BgE,MAAAA,KAAK,CAACujB,aAAa,CAAChnB,GAAG,IAAI;UACzB,IAAIumB,SAAS,GAAG/mB,gBAAgB,CAACC,OAAO,EAAEO,GAAG,CAACumB,SAAS,CAAC,CAAA;EAExD,QAAA,MAAMe,YAAY,GAChB,OAAO7jB,KAAK,CAACO,OAAO,CAACujB,SAAS,KAAK,WAAW,IAC9C9jB,KAAK,CAACO,OAAO,CAACujB,SAAS,KAAK,CAAC,CAAC,GAC1BjY,MAAM,CAACwL,gBAAgB,GACvBrX,KAAK,CAACO,OAAO,CAACujB,SAAS,GAAG,CAAC,CAAA;EAEjChB,QAAAA,SAAS,GAAGhkB,IAAI,CAACU,GAAG,CAAC,CAAC,EAAEV,IAAI,CAACW,GAAG,CAACqjB,SAAS,EAAEe,YAAY,CAAC,CAAC,CAAA;UAE1D,OAAO;EACL,UAAA,GAAGtnB,GAAG;EACNumB,UAAAA,SAAAA;WACD,CAAA;EACH,OAAC,CAAC,CAAA;OACH,CAAA;EACD9iB,IAAAA,KAAK,CAACsjB,cAAc,GAAGvU,YAAY,IAAI;QAAA,IAAAgV,sBAAA,EAAA9U,mBAAA,CAAA;EACrCjP,MAAAA,KAAK,CAAC4jB,YAAY,CAChB7U,YAAY,GACR4T,gBAAgB,GAAAoB,CAAAA,sBAAA,GAAA9U,CAAAA,mBAAA,GAChBjP,KAAK,CAACkP,YAAY,KAAAD,IAAAA,IAAAA,CAAAA,mBAAA,GAAlBA,mBAAA,CAAoBgU,UAAU,KAA9BhU,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,mBAAA,CAAgC6T,SAAS,KAAAiB,IAAAA,GAAAA,sBAAA,GAAIpB,gBACnD,CAAC,CAAA;OACF,CAAA;EACD3iB,IAAAA,KAAK,CAACgkB,aAAa,GAAGjV,YAAY,IAAI;QAAA,IAAAkV,sBAAA,EAAAC,oBAAA,CAAA;EACpClkB,MAAAA,KAAK,CAACmkB,WAAW,CACfpV,YAAY,GACR6T,eAAe,GAAAqB,CAAAA,sBAAA,GAAAC,CAAAA,oBAAA,GACflkB,KAAK,CAACkP,YAAY,KAAAgV,IAAAA,IAAAA,CAAAA,oBAAA,GAAlBA,oBAAA,CAAoBjB,UAAU,KAA9BiB,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,oBAAA,CAAgCnB,QAAQ,KAAAkB,IAAAA,GAAAA,sBAAA,GAAIrB,eAClD,CAAC,CAAA;OACF,CAAA;EACD5iB,IAAAA,KAAK,CAACmkB,WAAW,GAAGnoB,OAAO,IAAI;EAC7BgE,MAAAA,KAAK,CAACujB,aAAa,CAAChnB,GAAG,IAAI;EACzB,QAAA,MAAMwmB,QAAQ,GAAGjkB,IAAI,CAACU,GAAG,CAAC,CAAC,EAAEzD,gBAAgB,CAACC,OAAO,EAAEO,GAAG,CAACwmB,QAAQ,CAAC,CAAC,CAAA;UACrE,MAAMqB,WAAW,GAAG7nB,GAAG,CAACwmB,QAAQ,GAAGxmB,GAAG,CAACumB,SAAU,CAAA;UACjD,MAAMA,SAAS,GAAGhkB,IAAI,CAACoR,KAAK,CAACkU,WAAW,GAAGrB,QAAQ,CAAC,CAAA;UAEpD,OAAO;EACL,UAAA,GAAGxmB,GAAG;YACNumB,SAAS;EACTC,UAAAA,QAAAA;WACD,CAAA;EACH,OAAC,CAAC,CAAA;OACH,CAAA;EACD;MACA/iB,KAAK,CAACqkB,YAAY,GAAGroB,OAAO,IAC1BgE,KAAK,CAACujB,aAAa,CAAChnB,GAAG,IAAI;EAAA,MAAA,IAAA+nB,qBAAA,CAAA;EACzB,MAAA,IAAIC,YAAY,GAAGxoB,gBAAgB,CACjCC,OAAO,EAAA,CAAAsoB,qBAAA,GACPtkB,KAAK,CAACO,OAAO,CAACujB,SAAS,KAAA,IAAA,GAAAQ,qBAAA,GAAI,CAAC,CAC9B,CAAC,CAAA;EAED,MAAA,IAAI,OAAOC,YAAY,KAAK,QAAQ,EAAE;UACpCA,YAAY,GAAGzlB,IAAI,CAACU,GAAG,CAAC,CAAC,CAAC,EAAE+kB,YAAY,CAAC,CAAA;EAC3C,OAAA;QAEA,OAAO;EACL,QAAA,GAAGhoB,GAAG;EACNunB,QAAAA,SAAS,EAAES,YAAAA;SACZ,CAAA;EACH,KAAC,CAAC,CAAA;EAEJvkB,IAAAA,KAAK,CAACwkB,cAAc,GAAG7mB,IAAI,CACzB,MAAM,CAACqC,KAAK,CAACykB,YAAY,EAAE,CAAC,EAC5BX,SAAS,IAAI;QACX,IAAIY,WAAqB,GAAG,EAAE,CAAA;EAC9B,MAAA,IAAIZ,SAAS,IAAIA,SAAS,GAAG,CAAC,EAAE;UAC9BY,WAAW,GAAG,CAAC,GAAG,IAAI9nB,KAAK,CAACknB,SAAS,CAAC,CAAC,CAACa,IAAI,CAAC,IAAI,CAAC,CAACphB,GAAG,CAAC,CAACuV,CAAC,EAAEvP,CAAC,KAAKA,CAAC,CAAC,CAAA;EACrE,OAAA;EACA,MAAA,OAAOmb,WAAW,CAAA;OACnB,EACDhlB,cAAc,CAACM,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,gBAAgB,CAC9D,CAAC,CAAA;EAEDP,IAAAA,KAAK,CAAC4kB,kBAAkB,GAAG,MAAM5kB,KAAK,CAAC6D,QAAQ,EAAE,CAACof,UAAU,CAACH,SAAS,GAAG,CAAC,CAAA;MAE1E9iB,KAAK,CAAC6kB,cAAc,GAAG,MAAM;QAC3B,MAAM;EAAE/B,QAAAA,SAAAA;EAAU,OAAC,GAAG9iB,KAAK,CAAC6D,QAAQ,EAAE,CAACof,UAAU,CAAA;EAEjD,MAAA,MAAMa,SAAS,GAAG9jB,KAAK,CAACykB,YAAY,EAAE,CAAA;EAEtC,MAAA,IAAIX,SAAS,KAAK,CAAC,CAAC,EAAE;EACpB,QAAA,OAAO,IAAI,CAAA;EACb,OAAA;QAEA,IAAIA,SAAS,KAAK,CAAC,EAAE;EACnB,QAAA,OAAO,KAAK,CAAA;EACd,OAAA;EAEA,MAAA,OAAOhB,SAAS,GAAGgB,SAAS,GAAG,CAAC,CAAA;OACjC,CAAA;MAED9jB,KAAK,CAAC8kB,YAAY,GAAG,MAAM;QACzB,OAAO9kB,KAAK,CAAC4jB,YAAY,CAACrnB,GAAG,IAAIA,GAAG,GAAG,CAAC,CAAC,CAAA;OAC1C,CAAA;MAEDyD,KAAK,CAAC+kB,QAAQ,GAAG,MAAM;EACrB,MAAA,OAAO/kB,KAAK,CAAC4jB,YAAY,CAACrnB,GAAG,IAAI;UAC/B,OAAOA,GAAG,GAAG,CAAC,CAAA;EAChB,OAAC,CAAC,CAAA;OACH,CAAA;MAEDyD,KAAK,CAACglB,SAAS,GAAG,MAAM;EACtB,MAAA,OAAOhlB,KAAK,CAAC4jB,YAAY,CAAC,CAAC,CAAC,CAAA;OAC7B,CAAA;MAED5jB,KAAK,CAACilB,QAAQ,GAAG,MAAM;QACrB,OAAOjlB,KAAK,CAAC4jB,YAAY,CAAC5jB,KAAK,CAACykB,YAAY,EAAE,GAAG,CAAC,CAAC,CAAA;OACpD,CAAA;MAEDzkB,KAAK,CAAC8gB,wBAAwB,GAAG,MAAM9gB,KAAK,CAAC2hB,mBAAmB,EAAE,CAAA;MAClE3hB,KAAK,CAACklB,qBAAqB,GAAG,MAAM;QAClC,IACE,CAACllB,KAAK,CAACmlB,sBAAsB,IAC7BnlB,KAAK,CAACO,OAAO,CAAC2kB,qBAAqB,EACnC;UACAllB,KAAK,CAACmlB,sBAAsB,GAC1BnlB,KAAK,CAACO,OAAO,CAAC2kB,qBAAqB,CAACllB,KAAK,CAAC,CAAA;EAC9C,OAAA;QAEA,IAAIA,KAAK,CAACO,OAAO,CAAC8iB,gBAAgB,IAAI,CAACrjB,KAAK,CAACmlB,sBAAsB,EAAE;EACnE,QAAA,OAAOnlB,KAAK,CAAC8gB,wBAAwB,EAAE,CAAA;EACzC,OAAA;EAEA,MAAA,OAAO9gB,KAAK,CAACmlB,sBAAsB,EAAE,CAAA;OACtC,CAAA;MAEDnlB,KAAK,CAACykB,YAAY,GAAG,MAAM;EAAA,MAAA,IAAAW,sBAAA,CAAA;EACzB,MAAA,OAAA,CAAAA,sBAAA,GACEplB,KAAK,CAACO,OAAO,CAACujB,SAAS,KAAA,IAAA,GAAAsB,sBAAA,GACvBtmB,IAAI,CAACumB,IAAI,CAACrlB,KAAK,CAACslB,WAAW,EAAE,GAAGtlB,KAAK,CAAC6D,QAAQ,EAAE,CAACof,UAAU,CAACF,QAAQ,CAAC,CAAA;OAExE,CAAA;MAED/iB,KAAK,CAACslB,WAAW,GAAG,MAAM;EAAA,MAAA,IAAAC,qBAAA,CAAA;EACxB,MAAA,OAAA,CAAAA,qBAAA,GACEvlB,KAAK,CAACO,OAAO,CAACilB,QAAQ,KAAAD,IAAAA,GAAAA,qBAAA,GAAIvlB,KAAK,CAAC8gB,wBAAwB,EAAE,CAAC2E,IAAI,CAAC/nB,MAAM,CAAA;OAEzE,CAAA;EACH,GAAA;EACF;;EClRA;;EAEA,MAAMgoB,yBAAyB,GAAGA,OAAwB;EACxDC,EAAAA,GAAG,EAAE,EAAE;EACPC,EAAAA,MAAM,EAAE,EAAA;EACV,CAAC,CAAC,CAAA;AAEK,QAAMC,UAAwB,GAAG;IACtCxZ,eAAe,EAAGC,KAAK,IAA2B;MAChD,OAAO;QACLwZ,UAAU,EAAEJ,yBAAyB,EAAE;QACvC,GAAGpZ,KAAAA;OACJ,CAAA;KACF;IAEDE,iBAAiB,EACfxM,KAAmB,IACU;MAC7B,OAAO;EACL+lB,MAAAA,kBAAkB,EAAE5pB,gBAAgB,CAAC,YAAY,EAAE6D,KAAK,CAAA;OACzD,CAAA;KACF;EAED+H,EAAAA,SAAS,EAAEA,CACT9H,GAAe,EACfD,KAAmB,KACV;MACTC,GAAG,CAAC6U,GAAG,GAAG,CAAClB,QAAQ,EAAEoS,eAAe,EAAEC,iBAAiB,KAAK;EAC1D,MAAA,MAAMC,UAAU,GAAGF,eAAe,GAC9B/lB,GAAG,CAACyI,WAAW,EAAE,CAACnF,GAAG,CAACrC,IAAA,IAAA;UAAA,IAAC;EAAET,UAAAA,EAAAA;EAAG,SAAC,GAAAS,IAAA,CAAA;EAAA,QAAA,OAAKT,EAAE,CAAA;EAAA,OAAA,CAAC,GACrC,EAAE,CAAA;EACN,MAAA,MAAM0lB,YAAY,GAAGF,iBAAiB,GAClChmB,GAAG,CAAC4I,aAAa,EAAE,CAACtF,GAAG,CAACsV,KAAA,IAAA;UAAA,IAAC;EAAEpY,UAAAA,EAAAA;EAAG,SAAC,GAAAoY,KAAA,CAAA;EAAA,QAAA,OAAKpY,EAAE,CAAA;EAAA,OAAA,CAAC,GACvC,EAAE,CAAA;EACN,MAAA,MAAM6gB,MAAM,GAAG,IAAI7Q,GAAG,CAAC,CAAC,GAAG0V,YAAY,EAAElmB,GAAG,CAACQ,EAAE,EAAE,GAAGylB,UAAU,CAAC,CAAC,CAAA;EAEhElmB,MAAAA,KAAK,CAAComB,aAAa,CAAC7pB,GAAG,IAAI;UAAA,IAAA8pB,SAAA,EAAAC,YAAA,CAAA;UACzB,IAAI1S,QAAQ,KAAK,QAAQ,EAAE;YAAA,IAAA2S,QAAA,EAAAC,WAAA,CAAA;YACzB,OAAO;cACLb,GAAG,EAAE,CAAAY,CAAAA,QAAA,GAAChqB,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAEopB,GAAG,KAAAY,IAAAA,GAAAA,QAAA,GAAI,EAAE,EAAEjiB,MAAM,CAAC7H,CAAC,IAAI,EAAC6kB,MAAM,IAAA,IAAA,IAANA,MAAM,CAAEmF,GAAG,CAAChqB,CAAC,CAAC,CAAC,CAAA;EAClDmpB,YAAAA,MAAM,EAAE,CACN,GAAG,CAAAY,CAAAA,WAAA,GAACjqB,GAAG,IAAA,IAAA,GAAA,KAAA,CAAA,GAAHA,GAAG,CAAEqpB,MAAM,KAAAY,IAAAA,GAAAA,WAAA,GAAI,EAAE,EAAEliB,MAAM,CAAC7H,CAAC,IAAI,EAAC6kB,MAAM,IAAA,IAAA,IAANA,MAAM,CAAEmF,GAAG,CAAChqB,CAAC,CAAC,CAAA,CAAC,EACnD,GAAGG,KAAK,CAAC4T,IAAI,CAAC8Q,MAAM,CAAC,CAAA;aAExB,CAAA;EACH,SAAA;UAEA,IAAI1N,QAAQ,KAAK,KAAK,EAAE;YAAA,IAAA8S,SAAA,EAAAC,YAAA,CAAA;YACtB,OAAO;EACLhB,YAAAA,GAAG,EAAE,CACH,GAAG,CAAAe,CAAAA,SAAA,GAACnqB,GAAG,IAAA,IAAA,GAAA,KAAA,CAAA,GAAHA,GAAG,CAAEopB,GAAG,KAAAe,IAAAA,GAAAA,SAAA,GAAI,EAAE,EAAEpiB,MAAM,CAAC7H,CAAC,IAAI,EAAC6kB,MAAM,IAANA,IAAAA,IAAAA,MAAM,CAAEmF,GAAG,CAAChqB,CAAC,CAAC,CAAC,CAAA,EAChD,GAAGG,KAAK,CAAC4T,IAAI,CAAC8Q,MAAM,CAAC,CACtB;cACDsE,MAAM,EAAE,CAAAe,CAAAA,YAAA,GAACpqB,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAEqpB,MAAM,KAAAe,IAAAA,GAAAA,YAAA,GAAI,EAAE,EAAEriB,MAAM,CAAC7H,CAAC,IAAI,EAAC6kB,MAAM,IAANA,IAAAA,IAAAA,MAAM,CAAEmF,GAAG,CAAChqB,CAAC,CAAC,CAAA,CAAA;aACxD,CAAA;EACH,SAAA;UAEA,OAAO;YACLkpB,GAAG,EAAE,CAAAU,CAAAA,SAAA,GAAC9pB,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAEopB,GAAG,KAAAU,IAAAA,GAAAA,SAAA,GAAI,EAAE,EAAE/hB,MAAM,CAAC7H,CAAC,IAAI,EAAC6kB,MAAM,IAAA,IAAA,IAANA,MAAM,CAAEmF,GAAG,CAAChqB,CAAC,CAAC,CAAC,CAAA;YAClDmpB,MAAM,EAAE,CAAAU,CAAAA,YAAA,GAAC/pB,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAEqpB,MAAM,KAAAU,IAAAA,GAAAA,YAAA,GAAI,EAAE,EAAEhiB,MAAM,CAAC7H,CAAC,IAAI,EAAC6kB,MAAM,IAANA,IAAAA,IAAAA,MAAM,CAAEmF,GAAG,CAAChqB,CAAC,CAAC,CAAA,CAAA;WACxD,CAAA;EACH,OAAC,CAAC,CAAA;OACH,CAAA;MACDwD,GAAG,CAACsV,SAAS,GAAG,MAAM;EAAA,MAAA,IAAA+E,KAAA,CAAA;QACpB,MAAM;UAAEsM,gBAAgB;EAAEnR,QAAAA,aAAAA;SAAe,GAAGzV,KAAK,CAACO,OAAO,CAAA;EACzD,MAAA,IAAI,OAAOqmB,gBAAgB,KAAK,UAAU,EAAE;UAC1C,OAAOA,gBAAgB,CAAC3mB,GAAG,CAAC,CAAA;EAC9B,OAAA;QACA,OAAAqa,CAAAA,KAAA,GAAOsM,gBAAgB,IAAhBA,IAAAA,GAAAA,gBAAgB,GAAInR,aAAa,KAAA,IAAA,GAAA6E,KAAA,GAAI,IAAI,CAAA;OACjD,CAAA;MACDra,GAAG,CAAC0V,WAAW,GAAG,MAAM;EACtB,MAAA,MAAM2L,MAAM,GAAG,CAACrhB,GAAG,CAACQ,EAAE,CAAC,CAAA;QAEvB,MAAM;UAAEklB,GAAG;EAAEC,QAAAA,MAAAA;EAAO,OAAC,GAAG5lB,KAAK,CAAC6D,QAAQ,EAAE,CAACiiB,UAAU,CAAA;EAEnD,MAAA,MAAMe,KAAK,GAAGvF,MAAM,CAAC9iB,IAAI,CAAC/B,CAAC,IAAIkpB,GAAG,IAAA,IAAA,GAAA,KAAA,CAAA,GAAHA,GAAG,CAAE/jB,QAAQ,CAACnF,CAAC,CAAC,CAAC,CAAA;EAChD,MAAA,MAAMqqB,QAAQ,GAAGxF,MAAM,CAAC9iB,IAAI,CAAC/B,CAAC,IAAImpB,MAAM,IAAA,IAAA,GAAA,KAAA,CAAA,GAANA,MAAM,CAAEhkB,QAAQ,CAACnF,CAAC,CAAC,CAAC,CAAA;QAEtD,OAAOoqB,KAAK,GAAG,KAAK,GAAGC,QAAQ,GAAG,QAAQ,GAAG,KAAK,CAAA;OACnD,CAAA;MACD7mB,GAAG,CAAC8V,cAAc,GAAG,MAAM;QAAA,IAAAgR,KAAA,EAAAC,qBAAA,CAAA;EACzB,MAAA,MAAMpT,QAAQ,GAAG3T,GAAG,CAAC0V,WAAW,EAAE,CAAA;EAClC,MAAA,IAAI,CAAC/B,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAA;QAExB,MAAMqT,mBAAmB,GAAAF,CAAAA,KAAA,GACvBnT,QAAQ,KAAK,KAAK,GAAG5T,KAAK,CAACknB,UAAU,EAAE,GAAGlnB,KAAK,CAACmnB,aAAa,EAAE,qBADrCJ,KAAA,CAEzBxjB,GAAG,CAAC6jB,KAAA,IAAA;UAAA,IAAC;EAAE3mB,UAAAA,EAAAA;EAAG,SAAC,GAAA2mB,KAAA,CAAA;EAAA,QAAA,OAAK3mB,EAAE,CAAA;SAAC,CAAA,CAAA;EAEtB,MAAA,OAAA,CAAAumB,qBAAA,GAAOC,mBAAmB,IAAnBA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,mBAAmB,CAAEjV,OAAO,CAAC/R,GAAG,CAACQ,EAAE,CAAC,KAAA,IAAA,GAAAumB,qBAAA,GAAI,CAAC,CAAC,CAAA;OAClD,CAAA;KACF;IAEDvjB,WAAW,EAA0BzD,KAAmB,IAAW;EACjEA,IAAAA,KAAK,CAAComB,aAAa,GAAGpqB,OAAO,IAAIgE,KAAK,CAACO,OAAO,CAACwlB,kBAAkB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAhC/lB,KAAK,CAACO,OAAO,CAACwlB,kBAAkB,CAAG/pB,OAAO,CAAC,CAAA;MAE5EgE,KAAK,CAACqnB,eAAe,GAAGtY,YAAY,IAAA;QAAA,IAAAuY,qBAAA,EAAArY,mBAAA,CAAA;QAAA,OAClCjP,KAAK,CAAComB,aAAa,CACjBrX,YAAY,GACR2W,yBAAyB,EAAE,GAAA4B,CAAAA,qBAAA,GAAArY,CAAAA,mBAAA,GAC3BjP,KAAK,CAACkP,YAAY,KAAlBD,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,mBAAA,CAAoB6W,UAAU,KAAAwB,IAAAA,GAAAA,qBAAA,GAAI5B,yBAAyB,EACjE,CAAC,CAAA;EAAA,KAAA,CAAA;EAEH1lB,IAAAA,KAAK,CAACunB,mBAAmB,GAAG3T,QAAQ,IAAI;EAAA,MAAA,IAAA4C,qBAAA,CAAA;QACtC,MAAMC,YAAY,GAAGzW,KAAK,CAAC6D,QAAQ,EAAE,CAACiiB,UAAU,CAAA;QAEhD,IAAI,CAAClS,QAAQ,EAAE;UAAA,IAAA4T,iBAAA,EAAAC,oBAAA,CAAA;UACb,OAAOljB,OAAO,CAAC,CAAAijB,CAAAA,iBAAA,GAAA/Q,YAAY,CAACkP,GAAG,KAAA,IAAA,GAAA,KAAA,CAAA,GAAhB6B,iBAAA,CAAkB9pB,MAAM,MAAA+pB,CAAAA,oBAAA,GAAIhR,YAAY,CAACmP,MAAM,KAAnB6B,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,oBAAA,CAAqB/pB,MAAM,CAAC,CAAA,CAAA;EACzE,OAAA;EACA,MAAA,OAAO6G,OAAO,CAAA,CAAAiS,qBAAA,GAACC,YAAY,CAAC7C,QAAQ,CAAC,KAAtB4C,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,qBAAA,CAAwB9Y,MAAM,CAAC,CAAA;OAC/C,CAAA;MAEDsC,KAAK,CAAC0nB,cAAc,GAAG,CAACC,WAAW,EAAEC,YAAY,EAAEhU,QAAQ,KAAK;EAAA,MAAA,IAAAiU,qBAAA,CAAA;EAC9D,MAAA,MAAMpC,IAAI,GACR,CAAAoC,CAAAA,qBAAA,GAAA7nB,KAAK,CAACO,OAAO,CAACunB,cAAc,KAAAD,IAAAA,GAAAA,qBAAA,GAAI,IAAI;EAChC;EACA;QACA,CAACD,YAAY,WAAZA,YAAY,GAAI,EAAE,EAAErkB,GAAG,CAAC0e,KAAK,IAAI;UAChC,MAAMhiB,GAAG,GAAGD,KAAK,CAAC4I,MAAM,CAACqZ,KAAK,EAAE,IAAI,CAAC,CAAA;UACrC,OAAOhiB,GAAG,CAACsiB,uBAAuB,EAAE,GAAGtiB,GAAG,GAAG,IAAI,CAAA;EACnD,OAAC,CAAC;EACF;QACA,CAAC2nB,YAAY,WAAZA,YAAY,GAAI,EAAE,EAAErkB,GAAG,CACtB0e,KAAK,IAAI0F,WAAW,CAACtjB,IAAI,CAACpE,GAAG,IAAIA,GAAG,CAACQ,EAAE,KAAKwhB,KAAK,CACnD,CAAC,CAAA;QAEP,OAAOwD,IAAI,CAACnhB,MAAM,CAACC,OAAO,CAAC,CAAChB,GAAG,CAAC9G,CAAC,KAAK;EAAE,QAAA,GAAGA,CAAC;EAAEmX,QAAAA,QAAAA;EAAS,OAAC,CAAC,CAAC,CAAA;OAC3D,CAAA;MAED5T,KAAK,CAACknB,UAAU,GAAGvpB,IAAI,CACrB,MAAM,CAACqC,KAAK,CAACmhB,WAAW,EAAE,CAACsE,IAAI,EAAEzlB,KAAK,CAAC6D,QAAQ,EAAE,CAACiiB,UAAU,CAACH,GAAG,CAAC,EACjE,CAACoC,OAAO,EAAEC,eAAe,KACvBhoB,KAAK,CAAC0nB,cAAc,CAACK,OAAO,EAAEC,eAAe,EAAE,KAAK,CAAC,EACvDtoB,cAAc,CAACM,KAAK,CAACO,OAAO,EAAE,WAAW,EAAE,YAAY,CACzD,CAAC,CAAA;MAEDP,KAAK,CAACmnB,aAAa,GAAGxpB,IAAI,CACxB,MAAM,CAACqC,KAAK,CAACmhB,WAAW,EAAE,CAACsE,IAAI,EAAEzlB,KAAK,CAAC6D,QAAQ,EAAE,CAACiiB,UAAU,CAACF,MAAM,CAAC,EACpE,CAACmC,OAAO,EAAEE,kBAAkB,KAC1BjoB,KAAK,CAAC0nB,cAAc,CAACK,OAAO,EAAEE,kBAAkB,EAAE,QAAQ,CAAC,EAC7DvoB,cAAc,CAACM,KAAK,CAACO,OAAO,EAAE,WAAW,EAAE,eAAe,CAC5D,CAAC,CAAA;EAEDP,IAAAA,KAAK,CAACkoB,aAAa,GAAGvqB,IAAI,CACxB,MAAM,CACJqC,KAAK,CAACmhB,WAAW,EAAE,CAACsE,IAAI,EACxBzlB,KAAK,CAAC6D,QAAQ,EAAE,CAACiiB,UAAU,CAACH,GAAG,EAC/B3lB,KAAK,CAAC6D,QAAQ,EAAE,CAACiiB,UAAU,CAACF,MAAM,CACnC,EACD,CAACmC,OAAO,EAAEpC,GAAG,EAAEC,MAAM,KAAK;QACxB,MAAMuC,YAAY,GAAG,IAAI1X,GAAG,CAAC,CAAC,IAAIkV,GAAG,IAAA,IAAA,GAAHA,GAAG,GAAI,EAAE,GAAG,IAAIC,MAAM,IAAA,IAAA,GAANA,MAAM,GAAI,EAAE,EAAE,CAAC,CAAA;EACjE,MAAA,OAAOmC,OAAO,CAACzjB,MAAM,CAAC7H,CAAC,IAAI,CAAC0rB,YAAY,CAAC1B,GAAG,CAAChqB,CAAC,CAACgE,EAAE,CAAC,CAAC,CAAA;OACpD,EACDf,cAAc,CAACM,KAAK,CAACO,OAAO,EAAE,WAAW,EAAE,eAAe,CAC5D,CAAC,CAAA;EACH,GAAA;EACF;;EChFA;;AAEO,QAAM6nB,YAA0B,GAAG;IACxC/b,eAAe,EAAGC,KAAK,IAA6B;MAClD,OAAO;QACL+b,YAAY,EAAE,EAAE;QAChB,GAAG/b,KAAAA;OACJ,CAAA;KACF;IAEDE,iBAAiB,EACfxM,KAAmB,IACY;MAC/B,OAAO;EACLsoB,MAAAA,oBAAoB,EAAEnsB,gBAAgB,CAAC,cAAc,EAAE6D,KAAK,CAAC;EAC7DuoB,MAAAA,kBAAkB,EAAE,IAAI;EACxBC,MAAAA,uBAAuB,EAAE,IAAI;EAC7BC,MAAAA,qBAAqB,EAAE,IAAA;EACvB;EACA;EACA;OACD,CAAA;KACF;IAEDhlB,WAAW,EAA0BzD,KAAmB,IAAW;EACjEA,IAAAA,KAAK,CAAC0oB,eAAe,GAAG1sB,OAAO,IAC7BgE,KAAK,CAACO,OAAO,CAAC+nB,oBAAoB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAlCtoB,KAAK,CAACO,OAAO,CAAC+nB,oBAAoB,CAAGtsB,OAAO,CAAC,CAAA;MAC/CgE,KAAK,CAAC2oB,iBAAiB,GAAG5Z,YAAY,IAAA;EAAA,MAAA,IAAAuY,qBAAA,CAAA;QAAA,OACpCtnB,KAAK,CAAC0oB,eAAe,CACnB3Z,YAAY,GAAG,EAAE,GAAAuY,CAAAA,qBAAA,GAAGtnB,KAAK,CAACkP,YAAY,CAACmZ,YAAY,YAAAf,qBAAA,GAAI,EACzD,CAAC,CAAA;EAAA,KAAA,CAAA;EACHtnB,IAAAA,KAAK,CAAC4oB,qBAAqB,GAAG5b,KAAK,IAAI;EACrChN,MAAAA,KAAK,CAAC0oB,eAAe,CAACnsB,GAAG,IAAI;EAC3ByQ,QAAAA,KAAK,GACH,OAAOA,KAAK,KAAK,WAAW,GAAGA,KAAK,GAAG,CAAChN,KAAK,CAAC6oB,oBAAoB,EAAE,CAAA;EAEtE,QAAA,MAAMR,YAAY,GAAG;YAAE,GAAG9rB,GAAAA;WAAK,CAAA;UAE/B,MAAMusB,kBAAkB,GAAG9oB,KAAK,CAAC2S,qBAAqB,EAAE,CAAC5F,QAAQ,CAAA;;EAEjE;EACA;EACA,QAAA,IAAIC,KAAK,EAAE;EACT8b,UAAAA,kBAAkB,CAACxrB,OAAO,CAAC2C,GAAG,IAAI;EAChC,YAAA,IAAI,CAACA,GAAG,CAAC8oB,YAAY,EAAE,EAAE;EACvB,cAAA,OAAA;EACF,aAAA;EACAV,YAAAA,YAAY,CAACpoB,GAAG,CAACQ,EAAE,CAAC,GAAG,IAAI,CAAA;EAC7B,WAAC,CAAC,CAAA;EACJ,SAAC,MAAM;EACLqoB,UAAAA,kBAAkB,CAACxrB,OAAO,CAAC2C,GAAG,IAAI;EAChC,YAAA,OAAOooB,YAAY,CAACpoB,GAAG,CAACQ,EAAE,CAAC,CAAA;EAC7B,WAAC,CAAC,CAAA;EACJ,SAAA;EAEA,QAAA,OAAO4nB,YAAY,CAAA;EACrB,OAAC,CAAC,CAAA;OACH,CAAA;MACDroB,KAAK,CAACgpB,yBAAyB,GAAGhc,KAAK,IACrChN,KAAK,CAAC0oB,eAAe,CAACnsB,GAAG,IAAI;EAC3B,MAAA,MAAM0sB,aAAa,GACjB,OAAOjc,KAAK,KAAK,WAAW,GACxBA,KAAK,GACL,CAAChN,KAAK,CAACkpB,wBAAwB,EAAE,CAAA;EAEvC,MAAA,MAAMb,YAA+B,GAAG;UAAE,GAAG9rB,GAAAA;SAAK,CAAA;QAElDyD,KAAK,CAACmhB,WAAW,EAAE,CAACsE,IAAI,CAACnoB,OAAO,CAAC2C,GAAG,IAAI;EACtCkpB,QAAAA,mBAAmB,CAACd,YAAY,EAAEpoB,GAAG,CAACQ,EAAE,EAAEwoB,aAAa,EAAE,IAAI,EAAEjpB,KAAK,CAAC,CAAA;EACvE,OAAC,CAAC,CAAA;EAEF,MAAA,OAAOqoB,YAAY,CAAA;EACrB,KAAC,CAAC,CAAA;;EAEJ;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;;EAEA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;EAEA;EACA;MACAroB,KAAK,CAACopB,sBAAsB,GAAG,MAAMppB,KAAK,CAAC8M,eAAe,EAAE,CAAA;MAC5D9M,KAAK,CAACqpB,mBAAmB,GAAG1rB,IAAI,CAC9B,MAAM,CAACqC,KAAK,CAAC6D,QAAQ,EAAE,CAACwkB,YAAY,EAAEroB,KAAK,CAAC8M,eAAe,EAAE,CAAC,EAC9D,CAACub,YAAY,EAAEiB,QAAQ,KAAK;QAC1B,IAAI,CAAClX,MAAM,CAAC8O,IAAI,CAACmH,YAAY,CAAC,CAAC3qB,MAAM,EAAE;UACrC,OAAO;EACL+nB,UAAAA,IAAI,EAAE,EAAE;EACR1Y,UAAAA,QAAQ,EAAE,EAAE;EACZwU,UAAAA,QAAQ,EAAE,EAAC;WACZ,CAAA;EACH,OAAA;EAEA,MAAA,OAAOgI,YAAY,CAACvpB,KAAK,EAAEspB,QAAQ,CAAC,CAAA;OACrC,EACD5pB,cAAc,CAACM,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,qBAAqB,CACnE,CAAC,CAAA;MAEDP,KAAK,CAACwpB,2BAA2B,GAAG7rB,IAAI,CACtC,MAAM,CAACqC,KAAK,CAAC6D,QAAQ,EAAE,CAACwkB,YAAY,EAAEroB,KAAK,CAACmP,mBAAmB,EAAE,CAAC,EAClE,CAACkZ,YAAY,EAAEiB,QAAQ,KAAK;QAC1B,IAAI,CAAClX,MAAM,CAAC8O,IAAI,CAACmH,YAAY,CAAC,CAAC3qB,MAAM,EAAE;UACrC,OAAO;EACL+nB,UAAAA,IAAI,EAAE,EAAE;EACR1Y,UAAAA,QAAQ,EAAE,EAAE;EACZwU,UAAAA,QAAQ,EAAE,EAAC;WACZ,CAAA;EACH,OAAA;EAEA,MAAA,OAAOgI,YAAY,CAACvpB,KAAK,EAAEspB,QAAQ,CAAC,CAAA;OACrC,EACD5pB,cAAc,CAACM,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,6BAA6B,CAC3E,CAAC,CAAA;MAEDP,KAAK,CAACypB,0BAA0B,GAAG9rB,IAAI,CACrC,MAAM,CAACqC,KAAK,CAAC6D,QAAQ,EAAE,CAACwkB,YAAY,EAAEroB,KAAK,CAAC0hB,iBAAiB,EAAE,CAAC,EAChE,CAAC2G,YAAY,EAAEiB,QAAQ,KAAK;QAC1B,IAAI,CAAClX,MAAM,CAAC8O,IAAI,CAACmH,YAAY,CAAC,CAAC3qB,MAAM,EAAE;UACrC,OAAO;EACL+nB,UAAAA,IAAI,EAAE,EAAE;EACR1Y,UAAAA,QAAQ,EAAE,EAAE;EACZwU,UAAAA,QAAQ,EAAE,EAAC;WACZ,CAAA;EACH,OAAA;EAEA,MAAA,OAAOgI,YAAY,CAACvpB,KAAK,EAAEspB,QAAQ,CAAC,CAAA;OACrC,EACD5pB,cAAc,CAACM,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,4BAA4B,CAC1E,CAAC,CAAA;;EAED;;EAEA;EACA;;EAEA;EACA;EACA;;EAEA;EACA;EACA;;EAEA;EACA;;MAEAP,KAAK,CAAC6oB,oBAAoB,GAAG,MAAM;QACjC,MAAMC,kBAAkB,GAAG9oB,KAAK,CAACmP,mBAAmB,EAAE,CAACpC,QAAQ,CAAA;QAC/D,MAAM;EAAEsb,QAAAA,YAAAA;EAAa,OAAC,GAAGroB,KAAK,CAAC6D,QAAQ,EAAE,CAAA;EAEzC,MAAA,IAAI6lB,iBAAiB,GAAGnlB,OAAO,CAC7BukB,kBAAkB,CAACprB,MAAM,IAAI0U,MAAM,CAAC8O,IAAI,CAACmH,YAAY,CAAC,CAAC3qB,MACzD,CAAC,CAAA;EAED,MAAA,IAAIgsB,iBAAiB,EAAE;UACrB,IACEZ,kBAAkB,CAACtqB,IAAI,CACrByB,GAAG,IAAIA,GAAG,CAAC8oB,YAAY,EAAE,IAAI,CAACV,YAAY,CAACpoB,GAAG,CAACQ,EAAE,CACnD,CAAC,EACD;EACAipB,UAAAA,iBAAiB,GAAG,KAAK,CAAA;EAC3B,SAAA;EACF,OAAA;EAEA,MAAA,OAAOA,iBAAiB,CAAA;OACzB,CAAA;MAED1pB,KAAK,CAACkpB,wBAAwB,GAAG,MAAM;EACrC,MAAA,MAAMS,kBAAkB,GAAG3pB,KAAK,CAC7BklB,qBAAqB,EAAE,CACvBnY,QAAQ,CAACzI,MAAM,CAACrE,GAAG,IAAIA,GAAG,CAAC8oB,YAAY,EAAE,CAAC,CAAA;QAC7C,MAAM;EAAEV,QAAAA,YAAAA;EAAa,OAAC,GAAGroB,KAAK,CAAC6D,QAAQ,EAAE,CAAA;EAEzC,MAAA,IAAI+lB,qBAAqB,GAAG,CAAC,CAACD,kBAAkB,CAACjsB,MAAM,CAAA;EAEvD,MAAA,IACEksB,qBAAqB,IACrBD,kBAAkB,CAACnrB,IAAI,CAACyB,GAAG,IAAI,CAACooB,YAAY,CAACpoB,GAAG,CAACQ,EAAE,CAAC,CAAC,EACrD;EACAmpB,QAAAA,qBAAqB,GAAG,KAAK,CAAA;EAC/B,OAAA;EAEA,MAAA,OAAOA,qBAAqB,CAAA;OAC7B,CAAA;MAED5pB,KAAK,CAAC6pB,qBAAqB,GAAG,MAAM;EAAA,MAAA,IAAAC,qBAAA,CAAA;QAClC,MAAMC,aAAa,GAAG3X,MAAM,CAAC8O,IAAI,CAAA4I,CAAAA,qBAAA,GAC/B9pB,KAAK,CAAC6D,QAAQ,EAAE,CAACwkB,YAAY,KAAAyB,IAAAA,GAAAA,qBAAA,GAAI,EACnC,CAAC,CAACpsB,MAAM,CAAA;EACR,MAAA,OACEqsB,aAAa,GAAG,CAAC,IACjBA,aAAa,GAAG/pB,KAAK,CAACmP,mBAAmB,EAAE,CAACpC,QAAQ,CAACrP,MAAM,CAAA;OAE9D,CAAA;MAEDsC,KAAK,CAACgqB,yBAAyB,GAAG,MAAM;QACtC,MAAML,kBAAkB,GAAG3pB,KAAK,CAACklB,qBAAqB,EAAE,CAACnY,QAAQ,CAAA;EACjE,MAAA,OAAO/M,KAAK,CAACkpB,wBAAwB,EAAE,GACnC,KAAK,GACLS,kBAAkB,CACfrlB,MAAM,CAACrE,GAAG,IAAIA,GAAG,CAAC8oB,YAAY,EAAE,CAAC,CACjCvqB,IAAI,CAAC/B,CAAC,IAAIA,CAAC,CAACwtB,aAAa,EAAE,IAAIxtB,CAAC,CAACytB,iBAAiB,EAAE,CAAC,CAAA;OAC7D,CAAA;MAEDlqB,KAAK,CAACmqB,+BAA+B,GAAG,MAAM;EAC5C,MAAA,OAAQ1Q,CAAU,IAAK;UACrBzZ,KAAK,CAAC4oB,qBAAqB,CACvBnP,CAAC,CAAgB6D,MAAM,CAAsBC,OACjD,CAAC,CAAA;SACF,CAAA;OACF,CAAA;MAEDvd,KAAK,CAACoqB,mCAAmC,GAAG,MAAM;EAChD,MAAA,OAAQ3Q,CAAU,IAAK;UACrBzZ,KAAK,CAACgpB,yBAAyB,CAC3BvP,CAAC,CAAgB6D,MAAM,CAAsBC,OACjD,CAAC,CAAA;SACF,CAAA;OACF,CAAA;KACF;EAEDxV,EAAAA,SAAS,EAAEA,CACT9H,GAAe,EACfD,KAAmB,KACV;EACTC,IAAAA,GAAG,CAACoqB,cAAc,GAAG,CAACrd,KAAK,EAAElP,IAAI,KAAK;EACpC,MAAA,MAAMwsB,UAAU,GAAGrqB,GAAG,CAACgqB,aAAa,EAAE,CAAA;EAEtCjqB,MAAAA,KAAK,CAAC0oB,eAAe,CAACnsB,GAAG,IAAI;EAAA,QAAA,IAAAguB,oBAAA,CAAA;UAC3Bvd,KAAK,GAAG,OAAOA,KAAK,KAAK,WAAW,GAAGA,KAAK,GAAG,CAACsd,UAAU,CAAA;UAE1D,IAAIrqB,GAAG,CAAC8oB,YAAY,EAAE,IAAIuB,UAAU,KAAKtd,KAAK,EAAE;EAC9C,UAAA,OAAOzQ,GAAG,CAAA;EACZ,SAAA;EAEA,QAAA,MAAMiuB,cAAc,GAAG;YAAE,GAAGjuB,GAAAA;WAAK,CAAA;UAEjC4sB,mBAAmB,CACjBqB,cAAc,EACdvqB,GAAG,CAACQ,EAAE,EACNuM,KAAK,EAAA,CAAAud,oBAAA,GACLzsB,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAE2sB,cAAc,KAAA,IAAA,GAAAF,oBAAA,GAAI,IAAI,EAC5BvqB,KACF,CAAC,CAAA;EAED,QAAA,OAAOwqB,cAAc,CAAA;EACvB,OAAC,CAAC,CAAA;OACH,CAAA;MACDvqB,GAAG,CAACgqB,aAAa,GAAG,MAAM;QACxB,MAAM;EAAE5B,QAAAA,YAAAA;EAAa,OAAC,GAAGroB,KAAK,CAAC6D,QAAQ,EAAE,CAAA;EACzC,MAAA,OAAO6mB,aAAa,CAACzqB,GAAG,EAAEooB,YAAY,CAAC,CAAA;OACxC,CAAA;MAEDpoB,GAAG,CAACiqB,iBAAiB,GAAG,MAAM;QAC5B,MAAM;EAAE7B,QAAAA,YAAAA;EAAa,OAAC,GAAGroB,KAAK,CAAC6D,QAAQ,EAAE,CAAA;QACzC,OAAO8mB,gBAAgB,CAAC1qB,GAAG,EAAEooB,YAAmB,CAAC,KAAK,MAAM,CAAA;OAC7D,CAAA;MAEDpoB,GAAG,CAAC2qB,uBAAuB,GAAG,MAAM;QAClC,MAAM;EAAEvC,QAAAA,YAAAA;EAAa,OAAC,GAAGroB,KAAK,CAAC6D,QAAQ,EAAE,CAAA;QACzC,OAAO8mB,gBAAgB,CAAC1qB,GAAG,EAAEooB,YAAmB,CAAC,KAAK,KAAK,CAAA;OAC5D,CAAA;MAEDpoB,GAAG,CAAC8oB,YAAY,GAAG,MAAM;EAAA,MAAA,IAAAzb,qBAAA,CAAA;QACvB,IAAI,OAAOtN,KAAK,CAACO,OAAO,CAACgoB,kBAAkB,KAAK,UAAU,EAAE;EAC1D,QAAA,OAAOvoB,KAAK,CAACO,OAAO,CAACgoB,kBAAkB,CAACtoB,GAAG,CAAC,CAAA;EAC9C,OAAA;QAEA,OAAAqN,CAAAA,qBAAA,GAAOtN,KAAK,CAACO,OAAO,CAACgoB,kBAAkB,KAAA,IAAA,GAAAjb,qBAAA,GAAI,IAAI,CAAA;OAChD,CAAA;MAEDrN,GAAG,CAAC4qB,mBAAmB,GAAG,MAAM;EAAA,MAAA,IAAAtd,sBAAA,CAAA;QAC9B,IAAI,OAAOvN,KAAK,CAACO,OAAO,CAACkoB,qBAAqB,KAAK,UAAU,EAAE;EAC7D,QAAA,OAAOzoB,KAAK,CAACO,OAAO,CAACkoB,qBAAqB,CAACxoB,GAAG,CAAC,CAAA;EACjD,OAAA;QAEA,OAAAsN,CAAAA,sBAAA,GAAOvN,KAAK,CAACO,OAAO,CAACkoB,qBAAqB,KAAA,IAAA,GAAAlb,sBAAA,GAAI,IAAI,CAAA;OACnD,CAAA;MAEDtN,GAAG,CAAC6qB,iBAAiB,GAAG,MAAM;EAAA,MAAA,IAAAC,sBAAA,CAAA;QAC5B,IAAI,OAAO/qB,KAAK,CAACO,OAAO,CAACioB,uBAAuB,KAAK,UAAU,EAAE;EAC/D,QAAA,OAAOxoB,KAAK,CAACO,OAAO,CAACioB,uBAAuB,CAACvoB,GAAG,CAAC,CAAA;EACnD,OAAA;QAEA,OAAA8qB,CAAAA,sBAAA,GAAO/qB,KAAK,CAACO,OAAO,CAACioB,uBAAuB,KAAA,IAAA,GAAAuC,sBAAA,GAAI,IAAI,CAAA;OACrD,CAAA;MACD9qB,GAAG,CAAC+qB,wBAAwB,GAAG,MAAM;EACnC,MAAA,MAAMC,SAAS,GAAGhrB,GAAG,CAAC8oB,YAAY,EAAE,CAAA;EAEpC,MAAA,OAAQtP,CAAU,IAAK;EAAA,QAAA,IAAA8E,OAAA,CAAA;UACrB,IAAI,CAAC0M,SAAS,EAAE,OAAA;EAChBhrB,QAAAA,GAAG,CAACoqB,cAAc,CAAA9L,CAAAA,OAAA,GACd9E,CAAC,CAAgB6D,MAAM,KAAzBiB,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,OAAA,CAAgDhB,OAClD,CAAC,CAAA;SACF,CAAA;OACF,CAAA;EACH,GAAA;EACF,EAAC;EAED,MAAM4L,mBAAmB,GAAGA,CAC1BqB,cAAuC,EACvC/pB,EAAU,EACVuM,KAAc,EACdke,eAAwB,EACxBlrB,KAAmB,KAChB;EAAA,EAAA,IAAAmT,YAAA,CAAA;IACH,MAAMlT,GAAG,GAAGD,KAAK,CAAC4I,MAAM,CAACnI,EAAE,EAAE,IAAI,CAAC,CAAA;;EAElC;;EAEA;EACA;EACA;EACA;EACA,EAAA,IAAIuM,KAAK,EAAE;EACT,IAAA,IAAI,CAAC/M,GAAG,CAAC6qB,iBAAiB,EAAE,EAAE;EAC5B1Y,MAAAA,MAAM,CAAC8O,IAAI,CAACsJ,cAAc,CAAC,CAACltB,OAAO,CAAClB,GAAG,IAAI,OAAOouB,cAAc,CAACpuB,GAAG,CAAC,CAAC,CAAA;EACxE,KAAA;EACA,IAAA,IAAI6D,GAAG,CAAC8oB,YAAY,EAAE,EAAE;EACtByB,MAAAA,cAAc,CAAC/pB,EAAE,CAAC,GAAG,IAAI,CAAA;EAC3B,KAAA;EACF,GAAC,MAAM;MACL,OAAO+pB,cAAc,CAAC/pB,EAAE,CAAC,CAAA;EAC3B,GAAA;EACA;;EAEA,EAAA,IAAIyqB,eAAe,IAAA/X,CAAAA,YAAA,GAAIlT,GAAG,CAACiI,OAAO,KAAA,IAAA,IAAXiL,YAAA,CAAazV,MAAM,IAAIuC,GAAG,CAAC4qB,mBAAmB,EAAE,EAAE;MACvE5qB,GAAG,CAACiI,OAAO,CAAC5K,OAAO,CAAC2C,GAAG,IACrBkpB,mBAAmB,CAACqB,cAAc,EAAEvqB,GAAG,CAACQ,EAAE,EAAEuM,KAAK,EAAEke,eAAe,EAAElrB,KAAK,CAC3E,CAAC,CAAA;EACH,GAAA;EACF,CAAC,CAAA;EAEM,SAASupB,YAAYA,CAC1BvpB,KAAmB,EACnBspB,QAAyB,EACR;IACjB,MAAMjB,YAAY,GAAGroB,KAAK,CAAC6D,QAAQ,EAAE,CAACwkB,YAAY,CAAA;IAElD,MAAM8C,mBAAiC,GAAG,EAAE,CAAA;IAC5C,MAAMC,mBAA+C,GAAG,EAAE,CAAA;;EAE1D;EACA,EAAA,MAAMC,WAAW,GAAG,UAAC5F,IAAkB,EAAEzkB,KAAK,EAAuB;EACnE,IAAA,OAAOykB,IAAI,CACRliB,GAAG,CAACtD,GAAG,IAAI;EAAA,MAAA,IAAAqrB,aAAA,CAAA;EACV,MAAA,MAAMhB,UAAU,GAAGI,aAAa,CAACzqB,GAAG,EAAEooB,YAAY,CAAC,CAAA;EAEnD,MAAA,IAAIiC,UAAU,EAAE;EACda,QAAAA,mBAAmB,CAAC3tB,IAAI,CAACyC,GAAG,CAAC,CAAA;EAC7BmrB,QAAAA,mBAAmB,CAACnrB,GAAG,CAACQ,EAAE,CAAC,GAAGR,GAAG,CAAA;EACnC,OAAA;QAEA,IAAAqrB,CAAAA,aAAA,GAAIrrB,GAAG,CAACiI,OAAO,KAAXojB,IAAAA,IAAAA,aAAA,CAAa5tB,MAAM,EAAE;EACvBuC,QAAAA,GAAG,GAAG;EACJ,UAAA,GAAGA,GAAG;YACNiI,OAAO,EAAEmjB,WAAW,CAACprB,GAAG,CAACiI,OAAkB,CAAA;WAC5C,CAAA;EACH,OAAA;EAEA,MAAA,IAAIoiB,UAAU,EAAE;EACd,QAAA,OAAOrqB,GAAG,CAAA;EACZ,OAAA;EACF,KAAC,CAAC,CACDqE,MAAM,CAACC,OAAO,CAAC,CAAA;KACnB,CAAA;IAED,OAAO;EACLkhB,IAAAA,IAAI,EAAE4F,WAAW,CAAC/B,QAAQ,CAAC7D,IAAI,CAAC;EAChC1Y,IAAAA,QAAQ,EAAEoe,mBAAmB;EAC7B5J,IAAAA,QAAQ,EAAE6J,mBAAAA;KACX,CAAA;EACH,CAAA;EAEO,SAASV,aAAaA,CAC3BzqB,GAAe,EACfsrB,SAAkC,EACzB;EAAA,EAAA,IAAAC,iBAAA,CAAA;IACT,OAAAA,CAAAA,iBAAA,GAAOD,SAAS,CAACtrB,GAAG,CAACQ,EAAE,CAAC,KAAA,IAAA,GAAA+qB,iBAAA,GAAI,KAAK,CAAA;EACnC,CAAA;EAEO,SAASb,gBAAgBA,CAC9B1qB,GAAe,EACfsrB,SAAkC,EAClCvrB,KAAmB,EACO;EAAA,EAAA,IAAAyrB,aAAA,CAAA;EAC1B,EAAA,IAAI,EAAAA,CAAAA,aAAA,GAACxrB,GAAG,CAACiI,OAAO,KAAXujB,IAAAA,IAAAA,aAAA,CAAa/tB,MAAM,CAAE,EAAA,OAAO,KAAK,CAAA;IAEtC,IAAIguB,mBAAmB,GAAG,IAAI,CAAA;IAC9B,IAAIC,YAAY,GAAG,KAAK,CAAA;EAExB1rB,EAAAA,GAAG,CAACiI,OAAO,CAAC5K,OAAO,CAACsuB,MAAM,IAAI;EAC5B;EACA,IAAA,IAAID,YAAY,IAAI,CAACD,mBAAmB,EAAE;EACxC,MAAA,OAAA;EACF,KAAA;EAEA,IAAA,IAAIE,MAAM,CAAC7C,YAAY,EAAE,EAAE;EACzB,MAAA,IAAI2B,aAAa,CAACkB,MAAM,EAAEL,SAAS,CAAC,EAAE;EACpCI,QAAAA,YAAY,GAAG,IAAI,CAAA;EACrB,OAAC,MAAM;EACLD,QAAAA,mBAAmB,GAAG,KAAK,CAAA;EAC7B,OAAA;EACF,KAAA;;EAEA;MACA,IAAIE,MAAM,CAAC1jB,OAAO,IAAI0jB,MAAM,CAAC1jB,OAAO,CAACxK,MAAM,EAAE;QAC3C,MAAMmuB,sBAAsB,GAAGlB,gBAAgB,CAACiB,MAAM,EAAEL,SAAgB,CAAC,CAAA;QACzE,IAAIM,sBAAsB,KAAK,KAAK,EAAE;EACpCF,QAAAA,YAAY,GAAG,IAAI,CAAA;EACrB,OAAC,MAAM,IAAIE,sBAAsB,KAAK,MAAM,EAAE;EAC5CF,QAAAA,YAAY,GAAG,IAAI,CAAA;EACnBD,QAAAA,mBAAmB,GAAG,KAAK,CAAA;EAC7B,OAAC,MAAM;EACLA,QAAAA,mBAAmB,GAAG,KAAK,CAAA;EAC7B,OAAA;EACF,KAAA;EACF,GAAC,CAAC,CAAA;IAEF,OAAOA,mBAAmB,GAAG,KAAK,GAAGC,YAAY,GAAG,MAAM,GAAG,KAAK,CAAA;EACpE;;ACzpBO,QAAMG,mBAAmB,GAAG,aAAY;EAE/C,MAAMC,YAA4B,GAAGA,CAACC,IAAI,EAAEC,IAAI,EAAE/rB,QAAQ,KAAK;EAC7D,EAAA,OAAOgsB,mBAAmB,CACxB7hB,QAAQ,CAAC2hB,IAAI,CAAC1rB,QAAQ,CAACJ,QAAQ,CAAC,CAAC,CAACoK,WAAW,EAAE,EAC/CD,QAAQ,CAAC4hB,IAAI,CAAC3rB,QAAQ,CAACJ,QAAQ,CAAC,CAAC,CAACoK,WAAW,EAC/C,CAAC,CAAA;EACH,CAAC,CAAA;EAED,MAAM6hB,yBAAyC,GAAGA,CAACH,IAAI,EAAEC,IAAI,EAAE/rB,QAAQ,KAAK;IAC1E,OAAOgsB,mBAAmB,CACxB7hB,QAAQ,CAAC2hB,IAAI,CAAC1rB,QAAQ,CAACJ,QAAQ,CAAC,CAAC,EACjCmK,QAAQ,CAAC4hB,IAAI,CAAC3rB,QAAQ,CAACJ,QAAQ,CAAC,CAClC,CAAC,CAAA;EACH,CAAC,CAAA;;EAED;EACA;EACA,MAAMksB,IAAoB,GAAGA,CAACJ,IAAI,EAAEC,IAAI,EAAE/rB,QAAQ,KAAK;EACrD,EAAA,OAAOmsB,YAAY,CACjBhiB,QAAQ,CAAC2hB,IAAI,CAAC1rB,QAAQ,CAACJ,QAAQ,CAAC,CAAC,CAACoK,WAAW,EAAE,EAC/CD,QAAQ,CAAC4hB,IAAI,CAAC3rB,QAAQ,CAACJ,QAAQ,CAAC,CAAC,CAACoK,WAAW,EAC/C,CAAC,CAAA;EACH,CAAC,CAAA;;EAED;EACA;EACA,MAAMgiB,iBAAiC,GAAGA,CAACN,IAAI,EAAEC,IAAI,EAAE/rB,QAAQ,KAAK;IAClE,OAAOmsB,YAAY,CACjBhiB,QAAQ,CAAC2hB,IAAI,CAAC1rB,QAAQ,CAACJ,QAAQ,CAAC,CAAC,EACjCmK,QAAQ,CAAC4hB,IAAI,CAAC3rB,QAAQ,CAACJ,QAAQ,CAAC,CAClC,CAAC,CAAA;EACH,CAAC,CAAA;EAED,MAAMqsB,QAAwB,GAAGA,CAACP,IAAI,EAAEC,IAAI,EAAE/rB,QAAQ,KAAK;EACzD,EAAA,MAAMmQ,CAAC,GAAG2b,IAAI,CAAC1rB,QAAQ,CAAOJ,QAAQ,CAAC,CAAA;EACvC,EAAA,MAAMoQ,CAAC,GAAG2b,IAAI,CAAC3rB,QAAQ,CAAOJ,QAAQ,CAAC,CAAA;;EAEvC;EACA;EACA;EACA,EAAA,OAAOmQ,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;EACnC,CAAC,CAAA;EAED,MAAMkc,KAAqB,GAAGA,CAACR,IAAI,EAAEC,IAAI,EAAE/rB,QAAQ,KAAK;EACtD,EAAA,OAAOmsB,YAAY,CAACL,IAAI,CAAC1rB,QAAQ,CAACJ,QAAQ,CAAC,EAAE+rB,IAAI,CAAC3rB,QAAQ,CAACJ,QAAQ,CAAC,CAAC,CAAA;EACvE,CAAC,CAAA;;EAED;;EAEA,SAASmsB,YAAYA,CAAChc,CAAM,EAAEC,CAAM,EAAE;EACpC,EAAA,OAAOD,CAAC,KAAKC,CAAC,GAAG,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;EACrC,CAAA;EAEA,SAASjG,QAAQA,CAACgG,CAAM,EAAE;EACxB,EAAA,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;EACzB,IAAA,IAAIvE,KAAK,CAACuE,CAAC,CAAC,IAAIA,CAAC,KAAKtE,QAAQ,IAAIsE,CAAC,KAAK,CAACtE,QAAQ,EAAE;EACjD,MAAA,OAAO,EAAE,CAAA;EACX,KAAA;MACA,OAAO1M,MAAM,CAACgR,CAAC,CAAC,CAAA;EAClB,GAAA;EACA,EAAA,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;EACzB,IAAA,OAAOA,CAAC,CAAA;EACV,GAAA;EACA,EAAA,OAAO,EAAE,CAAA;EACX,CAAA;;EAEA;EACA;EACA;EACA,SAAS6b,mBAAmBA,CAACO,IAAY,EAAEC,IAAY,EAAE;EACvD;EACA;EACA,EAAA,MAAMrc,CAAC,GAAGoc,IAAI,CAAC3qB,KAAK,CAACgqB,mBAAmB,CAAC,CAACxnB,MAAM,CAACC,OAAO,CAAC,CAAA;EACzD,EAAA,MAAM+L,CAAC,GAAGoc,IAAI,CAAC5qB,KAAK,CAACgqB,mBAAmB,CAAC,CAACxnB,MAAM,CAACC,OAAO,CAAC,CAAA;;EAEzD;EACA,EAAA,OAAO8L,CAAC,CAAC3S,MAAM,IAAI4S,CAAC,CAAC5S,MAAM,EAAE;EAC3B,IAAA,MAAMivB,EAAE,GAAGtc,CAAC,CAACmE,KAAK,EAAG,CAAA;EACrB,IAAA,MAAMoY,EAAE,GAAGtc,CAAC,CAACkE,KAAK,EAAG,CAAA;EAErB,IAAA,MAAMqY,EAAE,GAAGC,QAAQ,CAACH,EAAE,EAAE,EAAE,CAAC,CAAA;EAC3B,IAAA,MAAMI,EAAE,GAAGD,QAAQ,CAACF,EAAE,EAAE,EAAE,CAAC,CAAA;MAE3B,MAAMI,KAAK,GAAG,CAACH,EAAE,EAAEE,EAAE,CAAC,CAAC3c,IAAI,EAAE,CAAA;;EAE7B;EACA,IAAA,IAAItE,KAAK,CAACkhB,KAAK,CAAC,CAAC,CAAE,CAAC,EAAE;QACpB,IAAIL,EAAE,GAAGC,EAAE,EAAE;EACX,QAAA,OAAO,CAAC,CAAA;EACV,OAAA;QACA,IAAIA,EAAE,GAAGD,EAAE,EAAE;EACX,QAAA,OAAO,CAAC,CAAC,CAAA;EACX,OAAA;EACA,MAAA,SAAA;EACF,KAAA;;EAEA;EACA,IAAA,IAAI7gB,KAAK,CAACkhB,KAAK,CAAC,CAAC,CAAE,CAAC,EAAE;QACpB,OAAOlhB,KAAK,CAAC+gB,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;EAC3B,KAAA;;EAEA;MACA,IAAIA,EAAE,GAAGE,EAAE,EAAE;EACX,MAAA,OAAO,CAAC,CAAA;EACV,KAAA;MACA,IAAIA,EAAE,GAAGF,EAAE,EAAE;EACX,MAAA,OAAO,CAAC,CAAC,CAAA;EACX,KAAA;EACF,GAAA;EAEA,EAAA,OAAOxc,CAAC,CAAC3S,MAAM,GAAG4S,CAAC,CAAC5S,MAAM,CAAA;EAC5B,CAAA;;EAEA;;AAEO,QAAMuvB,UAAU,GAAG;IACxBlB,YAAY;IACZI,yBAAyB;IACzBC,IAAI;IACJE,iBAAiB;IACjBC,QAAQ;EACRC,EAAAA,KAAAA;EACF;;ECsJA;;AAEO,QAAMU,UAAwB,GAAG;IACtC7gB,eAAe,EAAGC,KAAK,IAAwB;MAC7C,OAAO;EACL6gB,MAAAA,OAAO,EAAE,EAAE;QACX,GAAG7gB,KAAAA;OACJ,CAAA;KACF;IAEDH,mBAAmB,EAAEA,MAAsD;MACzE,OAAO;EACLihB,MAAAA,SAAS,EAAE,MAAM;EACjBC,MAAAA,aAAa,EAAE,CAAA;OAChB,CAAA;KACF;IAED7gB,iBAAiB,EACfxM,KAAmB,IACO;MAC1B,OAAO;EACLstB,MAAAA,eAAe,EAAEnxB,gBAAgB,CAAC,SAAS,EAAE6D,KAAK,CAAC;QACnDutB,gBAAgB,EAAG9T,CAAU,IAAK;UAChC,OAAQA,CAAC,CAAgB+T,QAAQ,CAAA;EACnC,OAAA;OACD,CAAA;KACF;EAED1sB,EAAAA,YAAY,EAAEA,CACZpF,MAA6B,EAC7BsE,KAAmB,KACV;MACTtE,MAAM,CAAC+xB,gBAAgB,GAAG,MAAM;EAC9B,MAAA,MAAMC,SAAS,GAAG1tB,KAAK,CAACmP,mBAAmB,EAAE,CAACpC,QAAQ,CAAC0L,KAAK,CAAC,EAAE,CAAC,CAAA;QAEhE,IAAIkV,QAAQ,GAAG,KAAK,CAAA;EAEpB,MAAA,KAAK,MAAM1tB,GAAG,IAAIytB,SAAS,EAAE;UAC3B,MAAM1gB,KAAK,GAAG/M,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAEK,QAAQ,CAAC5E,MAAM,CAAC+E,EAAE,CAAC,CAAA;EAEtC,QAAA,IAAI2R,MAAM,CAAC7Q,SAAS,CAAC8I,QAAQ,CAACgI,IAAI,CAACrF,KAAK,CAAC,KAAK,eAAe,EAAE;YAC7D,OAAOigB,UAAU,CAACV,QAAQ,CAAA;EAC5B,SAAA;EAEA,QAAA,IAAI,OAAOvf,KAAK,KAAK,QAAQ,EAAE;EAC7B2gB,UAAAA,QAAQ,GAAG,IAAI,CAAA;YAEf,IAAI3gB,KAAK,CAAClL,KAAK,CAACgqB,mBAAmB,CAAC,CAACpuB,MAAM,GAAG,CAAC,EAAE;cAC/C,OAAOuvB,UAAU,CAAClB,YAAY,CAAA;EAChC,WAAA;EACF,SAAA;EACF,OAAA;EAEA,MAAA,IAAI4B,QAAQ,EAAE;UACZ,OAAOV,UAAU,CAACb,IAAI,CAAA;EACxB,OAAA;QAEA,OAAOa,UAAU,CAACT,KAAK,CAAA;OACxB,CAAA;MACD9wB,MAAM,CAACkyB,cAAc,GAAG,MAAM;QAC5B,MAAM/gB,QAAQ,GAAG7M,KAAK,CAACmP,mBAAmB,EAAE,CAACpC,QAAQ,CAAC,CAAC,CAAC,CAAA;QAExD,MAAMC,KAAK,GAAGH,QAAQ,IAARA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,QAAQ,CAAEvM,QAAQ,CAAC5E,MAAM,CAAC+E,EAAE,CAAC,CAAA;EAE3C,MAAA,IAAI,OAAOuM,KAAK,KAAK,QAAQ,EAAE;EAC7B,QAAA,OAAO,KAAK,CAAA;EACd,OAAA;EAEA,MAAA,OAAO,MAAM,CAAA;OACd,CAAA;MACDtR,MAAM,CAACmyB,YAAY,GAAG,MAAM;QAAA,IAAAC,qBAAA,EAAAC,sBAAA,CAAA;QAC1B,IAAI,CAACryB,MAAM,EAAE;UACX,MAAM,IAAIuG,KAAK,EAAE,CAAA;EACnB,OAAA;QAEA,OAAOzF,UAAU,CAACd,MAAM,CAACqF,SAAS,CAACqsB,SAAS,CAAC,GACzC1xB,MAAM,CAACqF,SAAS,CAACqsB,SAAS,GAC1B1xB,MAAM,CAACqF,SAAS,CAACqsB,SAAS,KAAK,MAAM,GACnC1xB,MAAM,CAAC+xB,gBAAgB,EAAE,IAAAK,qBAAA,GAAA,CAAAC,sBAAA,GACzB/tB,KAAK,CAACO,OAAO,CAAC0sB,UAAU,KAAA,IAAA,GAAA,KAAA,CAAA,GAAxBc,sBAAA,CAA2BryB,MAAM,CAACqF,SAAS,CAACqsB,SAAS,CAAW,KAAAU,IAAAA,GAAAA,qBAAA,GAChEb,UAAU,CAACvxB,MAAM,CAACqF,SAAS,CAACqsB,SAAS,CAAqB,CAAA;OACjE,CAAA;EACD1xB,IAAAA,MAAM,CAACsyB,aAAa,GAAG,CAACC,IAAI,EAAEC,KAAK,KAAK;EACtC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA,MAAA,MAAMC,gBAAgB,GAAGzyB,MAAM,CAAC0yB,mBAAmB,EAAE,CAAA;QACrD,MAAMC,cAAc,GAAG,OAAOJ,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,IAAI,CAAA;EAEnEjuB,MAAAA,KAAK,CAACsuB,UAAU,CAAC/xB,GAAG,IAAI;EACtB;EACA,QAAA,MAAMgyB,eAAe,GAAGhyB,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAE8H,IAAI,CAAC5H,CAAC,IAAIA,CAAC,CAACgE,EAAE,KAAK/E,MAAM,CAAC+E,EAAE,CAAC,CAAA;EAC1D,QAAA,MAAM+tB,aAAa,GAAGjyB,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAE0R,SAAS,CAACxR,CAAC,IAAIA,CAAC,CAACgE,EAAE,KAAK/E,MAAM,CAAC+E,EAAE,CAAC,CAAA;UAE7D,IAAIguB,UAAwB,GAAG,EAAE,CAAA;;EAEjC;EACA,QAAA,IAAIC,UAAmD,CAAA;UACvD,IAAIC,QAAQ,GAAGN,cAAc,GAAGJ,IAAI,GAAGE,gBAAgB,KAAK,MAAM,CAAA;;EAElE;EACA,QAAA,IAAI5xB,GAAG,IAAA,IAAA,IAAHA,GAAG,CAAEmB,MAAM,IAAIhC,MAAM,CAACkzB,eAAe,EAAE,IAAIV,KAAK,EAAE;EACpD,UAAA,IAAIK,eAAe,EAAE;EACnBG,YAAAA,UAAU,GAAG,QAAQ,CAAA;EACvB,WAAC,MAAM;EACLA,YAAAA,UAAU,GAAG,KAAK,CAAA;EACpB,WAAA;EACF,SAAC,MAAM;EACL;EACA,UAAA,IAAInyB,GAAG,IAAA,IAAA,IAAHA,GAAG,CAAEmB,MAAM,IAAI8wB,aAAa,KAAKjyB,GAAG,CAACmB,MAAM,GAAG,CAAC,EAAE;EACnDgxB,YAAAA,UAAU,GAAG,SAAS,CAAA;aACvB,MAAM,IAAIH,eAAe,EAAE;EAC1BG,YAAAA,UAAU,GAAG,QAAQ,CAAA;EACvB,WAAC,MAAM;EACLA,YAAAA,UAAU,GAAG,SAAS,CAAA;EACxB,WAAA;EACF,SAAA;;EAEA;UACA,IAAIA,UAAU,KAAK,QAAQ,EAAE;EAC3B;YACA,IAAI,CAACL,cAAc,EAAE;EACnB;cACA,IAAI,CAACF,gBAAgB,EAAE;EACrBO,cAAAA,UAAU,GAAG,QAAQ,CAAA;EACvB,aAAA;EACF,WAAA;EACF,SAAA;UAEA,IAAIA,UAAU,KAAK,KAAK,EAAE;EAAA,UAAA,IAAAG,qBAAA,CAAA;EACxBJ,UAAAA,UAAU,GAAG,CACX,GAAGlyB,GAAG,EACN;cACEkE,EAAE,EAAE/E,MAAM,CAAC+E,EAAE;EACbwtB,YAAAA,IAAI,EAAEU,QAAAA;EACR,WAAC,CACF,CAAA;EACD;YACAF,UAAU,CAAC/Z,MAAM,CACf,CAAC,EACD+Z,UAAU,CAAC/wB,MAAM,IAAA,CAAAmxB,qBAAA,GACd7uB,KAAK,CAACO,OAAO,CAACuuB,oBAAoB,KAAAD,IAAAA,GAAAA,qBAAA,GAAIhjB,MAAM,CAACwL,gBAAgB,CAClE,CAAC,CAAA;EACH,SAAC,MAAM,IAAIqX,UAAU,KAAK,QAAQ,EAAE;EAClC;EACAD,UAAAA,UAAU,GAAGlyB,GAAG,CAACgH,GAAG,CAAC9G,CAAC,IAAI;EACxB,YAAA,IAAIA,CAAC,CAACgE,EAAE,KAAK/E,MAAM,CAAC+E,EAAE,EAAE;gBACtB,OAAO;EACL,gBAAA,GAAGhE,CAAC;EACJwxB,gBAAAA,IAAI,EAAEU,QAAAA;iBACP,CAAA;EACH,aAAA;EACA,YAAA,OAAOlyB,CAAC,CAAA;EACV,WAAC,CAAC,CAAA;EACJ,SAAC,MAAM,IAAIiyB,UAAU,KAAK,QAAQ,EAAE;EAClCD,UAAAA,UAAU,GAAGlyB,GAAG,CAAC+H,MAAM,CAAC7H,CAAC,IAAIA,CAAC,CAACgE,EAAE,KAAK/E,MAAM,CAAC+E,EAAE,CAAC,CAAA;EAClD,SAAC,MAAM;EACLguB,UAAAA,UAAU,GAAG,CACX;cACEhuB,EAAE,EAAE/E,MAAM,CAAC+E,EAAE;EACbwtB,YAAAA,IAAI,EAAEU,QAAAA;EACR,WAAC,CACF,CAAA;EACH,SAAA;EAEA,QAAA,OAAOF,UAAU,CAAA;EACnB,OAAC,CAAC,CAAA;OACH,CAAA;MAED/yB,MAAM,CAACqzB,eAAe,GAAG,MAAM;QAAA,IAAA7tB,IAAA,EAAA8tB,qBAAA,CAAA;EAC7B,MAAA,MAAMC,aAAa,GAAA,CAAA/tB,IAAA,GAAA,CAAA8tB,qBAAA,GACjBtzB,MAAM,CAACqF,SAAS,CAACkuB,aAAa,KAAA,IAAA,GAAAD,qBAAA,GAC9BhvB,KAAK,CAACO,OAAO,CAAC0uB,aAAa,KAAA,IAAA,GAAA/tB,IAAA,GAC3BxF,MAAM,CAACkyB,cAAc,EAAE,KAAK,MAAM,CAAA;EACpC,MAAA,OAAOqB,aAAa,GAAG,MAAM,GAAG,KAAK,CAAA;OACtC,CAAA;EAEDvzB,IAAAA,MAAM,CAAC0yB,mBAAmB,GAAIF,KAAe,IAAK;QAAA,IAAA5gB,qBAAA,EAAAC,sBAAA,CAAA;EAChD,MAAA,MAAM2hB,kBAAkB,GAAGxzB,MAAM,CAACqzB,eAAe,EAAE,CAAA;EACnD,MAAA,MAAMI,QAAQ,GAAGzzB,MAAM,CAAC0zB,WAAW,EAAE,CAAA;QAErC,IAAI,CAACD,QAAQ,EAAE;EACb,QAAA,OAAOD,kBAAkB,CAAA;EAC3B,OAAA;EAEA,MAAA,IACEC,QAAQ,KAAKD,kBAAkB,KAAA,CAAA5hB,qBAAA,GAC9BtN,KAAK,CAACO,OAAO,CAAC8uB,oBAAoB,KAAA,IAAA,GAAA/hB,qBAAA,GAAI,IAAI,CAAC;EAAI;EAC/C4gB,MAAAA,KAAK,GAAA3gB,CAAAA,sBAAA,GAAGvN,KAAK,CAACO,OAAO,CAAC+uB,iBAAiB,KAAA,IAAA,GAAA/hB,sBAAA,GAAI,IAAI,GAAG,IAAI,CAAC;UACxD;EACA,QAAA,OAAO,KAAK,CAAA;EACd,OAAA;EACA,MAAA,OAAO4hB,QAAQ,KAAK,MAAM,GAAG,KAAK,GAAG,MAAM,CAAA;OAC5C,CAAA;MAEDzzB,MAAM,CAAC6zB,UAAU,GAAG,MAAM;QAAA,IAAAliB,qBAAA,EAAA0d,sBAAA,CAAA;EACxB,MAAA,OACE,CAAA1d,CAAAA,qBAAA,GAAC3R,MAAM,CAACqF,SAAS,CAACyuB,aAAa,KAAAniB,IAAAA,GAAAA,qBAAA,GAAI,IAAI,OAAA0d,sBAAA,GACtC/qB,KAAK,CAACO,OAAO,CAACivB,aAAa,KAAA,IAAA,GAAAzE,sBAAA,GAAI,IAAI,CAAC,IACrC,CAAC,CAACrvB,MAAM,CAACC,UAAU,CAAA;OAEtB,CAAA;MAEDD,MAAM,CAACkzB,eAAe,GAAG,MAAM;QAAA,IAAA/V,KAAA,EAAA4W,sBAAA,CAAA;QAC7B,OAAA5W,CAAAA,KAAA,GAAA4W,CAAAA,sBAAA,GACE/zB,MAAM,CAACqF,SAAS,CAAC2uB,eAAe,KAAA,IAAA,GAAAD,sBAAA,GAChCzvB,KAAK,CAACO,OAAO,CAACmvB,eAAe,KAAA7W,IAAAA,GAAAA,KAAA,GAC7B,CAAC,CAACnd,MAAM,CAACC,UAAU,CAAA;OAEtB,CAAA;MAEDD,MAAM,CAAC0zB,WAAW,GAAG,MAAM;EAAA,MAAA,IAAAO,qBAAA,CAAA;QACzB,MAAMC,UAAU,GAAAD,CAAAA,qBAAA,GAAG3vB,KAAK,CAAC6D,QAAQ,EAAE,CAACspB,OAAO,KAAA,IAAA,GAAA,KAAA,CAAA,GAAxBwC,qBAAA,CAA0BtrB,IAAI,CAAC5H,CAAC,IAAIA,CAAC,CAACgE,EAAE,KAAK/E,MAAM,CAAC+E,EAAE,CAAC,CAAA;QAE1E,OAAO,CAACmvB,UAAU,GAAG,KAAK,GAAGA,UAAU,CAAC3B,IAAI,GAAG,MAAM,GAAG,KAAK,CAAA;OAC9D,CAAA;MAEDvyB,MAAM,CAACm0B,YAAY,GAAG,MAAA;QAAA,IAAAC,sBAAA,EAAAC,sBAAA,CAAA;EAAA,MAAA,OAAA,CAAAD,sBAAA,GAAA,CAAAC,sBAAA,GACpB/vB,KAAK,CAAC6D,QAAQ,EAAE,CAACspB,OAAO,KAAA,IAAA,GAAA,KAAA,CAAA,GAAxB4C,sBAAA,CAA0B9hB,SAAS,CAACxR,CAAC,IAAIA,CAAC,CAACgE,EAAE,KAAK/E,MAAM,CAAC+E,EAAE,CAAC,KAAA,IAAA,GAAAqvB,sBAAA,GAAI,CAAC,CAAC,CAAA;EAAA,KAAA,CAAA;MAEpEp0B,MAAM,CAACs0B,YAAY,GAAG,MAAM;EAC1B;QACAhwB,KAAK,CAACsuB,UAAU,CAAC/xB,GAAG,IAClBA,GAAG,IAAA,IAAA,IAAHA,GAAG,CAAEmB,MAAM,GAAGnB,GAAG,CAAC+H,MAAM,CAAC7H,CAAC,IAAIA,CAAC,CAACgE,EAAE,KAAK/E,MAAM,CAAC+E,EAAE,CAAC,GAAG,EACtD,CAAC,CAAA;OACF,CAAA;MAED/E,MAAM,CAACu0B,uBAAuB,GAAG,MAAM;EACrC,MAAA,MAAMC,OAAO,GAAGx0B,MAAM,CAAC6zB,UAAU,EAAE,CAAA;EAEnC,MAAA,OAAQ9V,CAAU,IAAK;UACrB,IAAI,CAACyW,OAAO,EAAE,OAAA;EACZzW,QAAAA,CAAC,CAASC,OAAO,IAAA,IAAA,IAAjBD,CAAC,CAASC,OAAO,EAAI,CAAA;EACvBhe,QAAAA,MAAM,CAACsyB,aAAa,IAApBtyB,IAAAA,IAAAA,MAAM,CAACsyB,aAAa,CAClBtsB,SAAS,EACThG,MAAM,CAACkzB,eAAe,EAAE,GAAG5uB,KAAK,CAACO,OAAO,CAACgtB,gBAAgB,IAAA,IAAA,GAAA,KAAA,CAAA,GAA9BvtB,KAAK,CAACO,OAAO,CAACgtB,gBAAgB,CAAG9T,CAAC,CAAC,GAAG,KACnE,CAAC,CAAA;SACF,CAAA;OACF,CAAA;KACF;IAEDhW,WAAW,EAA0BzD,KAAmB,IAAW;EACjEA,IAAAA,KAAK,CAACsuB,UAAU,GAAGtyB,OAAO,IAAIgE,KAAK,CAACO,OAAO,CAAC+sB,eAAe,IAAA,IAAA,GAAA,KAAA,CAAA,GAA7BttB,KAAK,CAACO,OAAO,CAAC+sB,eAAe,CAAGtxB,OAAO,CAAC,CAAA;EACtEgE,IAAAA,KAAK,CAACmwB,YAAY,GAAGphB,YAAY,IAAI;QAAA,IAAAqhB,qBAAA,EAAAnhB,mBAAA,CAAA;QACnCjP,KAAK,CAACsuB,UAAU,CAACvf,YAAY,GAAG,EAAE,GAAA,CAAAqhB,qBAAA,GAAA,CAAAnhB,mBAAA,GAAGjP,KAAK,CAACkP,YAAY,qBAAlBD,mBAAA,CAAoBke,OAAO,KAAAiD,IAAAA,GAAAA,qBAAA,GAAI,EAAE,CAAC,CAAA;OACxE,CAAA;MACDpwB,KAAK,CAACqwB,oBAAoB,GAAG,MAAMrwB,KAAK,CAAC4S,kBAAkB,EAAE,CAAA;MAC7D5S,KAAK,CAAC0hB,iBAAiB,GAAG,MAAM;QAC9B,IAAI,CAAC1hB,KAAK,CAACswB,kBAAkB,IAAItwB,KAAK,CAACO,OAAO,CAACmhB,iBAAiB,EAAE;UAChE1hB,KAAK,CAACswB,kBAAkB,GAAGtwB,KAAK,CAACO,OAAO,CAACmhB,iBAAiB,CAAC1hB,KAAK,CAAC,CAAA;EACnE,OAAA;QAEA,IAAIA,KAAK,CAACO,OAAO,CAACgwB,aAAa,IAAI,CAACvwB,KAAK,CAACswB,kBAAkB,EAAE;EAC5D,QAAA,OAAOtwB,KAAK,CAACqwB,oBAAoB,EAAE,CAAA;EACrC,OAAA;EAEA,MAAA,OAAOrwB,KAAK,CAACswB,kBAAkB,EAAE,CAAA;OAClC,CAAA;EACH,GAAA;EACF;;ECrfA,MAAME,eAAe,GAAG,CACtBhtB,OAAO,EACPoZ,gBAAgB,EAChBpJ,cAAc,EACdoB,aAAa,EACbpL,cAAc,EACd0C,eAAe,EACfsS,cAAc;EAAE;EAChBO,eAAe;EAAE;EACjBmO,UAAU,EACVpc,cAAc;EAAE;EAChB8O,YAAY,EACZoD,aAAa,EACb6C,UAAU,EACVuC,YAAY,EACZvQ,YAAY,CACJ,CAAA;;EAEV;;EAgOO,SAASpU,WAAWA,CACzBlD,OAAoC,EACtB;IAAA,IAAAkwB,kBAAA,EAAAC,qBAAA,CAAA;EACd,EAAA,IACuC,CACpCnwB,OAAO,CAACT,QAAQ,IAAIS,OAAO,CAACowB,UAAU,CAAC,EACxC;EACArxB,IAAAA,OAAO,CAACC,IAAI,CAAC,4BAA4B,CAAC,CAAA;EAC5C,GAAA;EAEA,EAAA,MAAMqB,SAAS,GAAG,CAAC,GAAG4vB,eAAe,EAAE,IAAAC,CAAAA,kBAAA,GAAIlwB,OAAO,CAACK,SAAS,KAAA,IAAA,GAAA6vB,kBAAA,GAAI,EAAE,EAAE,CAAA;EAEpE,EAAA,IAAIzwB,KAAK,GAAG;EAAEY,IAAAA,SAAAA;KAAsC,CAAA;EAEpD,EAAA,MAAMgwB,cAAc,GAAG5wB,KAAK,CAACY,SAAS,CAACyI,MAAM,CAAC,CAAC+U,GAAG,EAAEvd,OAAO,KAAK;EAC9D,IAAA,OAAOuR,MAAM,CAACye,MAAM,CAACzS,GAAG,EAAEvd,OAAO,CAAC2L,iBAAiB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAzB3L,OAAO,CAAC2L,iBAAiB,CAAGxM,KAAK,CAAC,CAAC,CAAA;KAC9D,EAAE,EAAE,CAAgC,CAAA;IAErC,MAAM8wB,YAAY,GAAIvwB,OAAoC,IAAK;EAC7D,IAAA,IAAIP,KAAK,CAACO,OAAO,CAACuwB,YAAY,EAAE;QAC9B,OAAO9wB,KAAK,CAACO,OAAO,CAACuwB,YAAY,CAACF,cAAc,EAAErwB,OAAO,CAAC,CAAA;EAC5D,KAAA;MAEA,OAAO;EACL,MAAA,GAAGqwB,cAAc;QACjB,GAAGrwB,OAAAA;OACJ,CAAA;KACF,CAAA;IAED,MAAMwwB,gBAAgC,GAAG,EAAE,CAAA;EAE3C,EAAA,IAAI7hB,YAAY,GAAG;EACjB,IAAA,GAAG6hB,gBAAgB;MACnB,IAAAL,CAAAA,qBAAA,GAAInwB,OAAO,CAAC2O,YAAY,KAAAwhB,IAAAA,GAAAA,qBAAA,GAAI,EAAE;KACjB,CAAA;EAEf1wB,EAAAA,KAAK,CAACY,SAAS,CAACtD,OAAO,CAACuD,OAAO,IAAI;EAAA,IAAA,IAAAmwB,qBAAA,CAAA;EACjC9hB,IAAAA,YAAY,IAAA8hB,qBAAA,GAAInwB,OAAO,CAACwL,eAAe,IAAvBxL,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,OAAO,CAACwL,eAAe,CAAG6C,YAAY,CAAC,KAAA8hB,IAAAA,GAAAA,qBAAA,GACrD9hB,YAA2B,CAAA;EAC/B,GAAC,CAAC,CAAA;IAEF,MAAM+Q,MAAsB,GAAG,EAAE,CAAA;IACjC,IAAIgR,aAAa,GAAG,KAAK,CAAA;EAEzB,EAAA,MAAMC,YAAiC,GAAG;MACxCtwB,SAAS;EACTL,IAAAA,OAAO,EAAE;EACP,MAAA,GAAGqwB,cAAc;QACjB,GAAGrwB,OAAAA;OACJ;MACD2O,YAAY;MACZkR,MAAM,EAAE+Q,EAAE,IAAI;EACZlR,MAAAA,MAAM,CAACziB,IAAI,CAAC2zB,EAAE,CAAC,CAAA;QAEf,IAAI,CAACF,aAAa,EAAE;EAClBA,QAAAA,aAAa,GAAG,IAAI,CAAA;;EAEpB;EACA;EACAG,QAAAA,OAAO,CAACC,OAAO,EAAE,CACdC,IAAI,CAAC,MAAM;YACV,OAAOrR,MAAM,CAACviB,MAAM,EAAE;EACpBuiB,YAAAA,MAAM,CAACzL,KAAK,EAAE,EAAG,CAAA;EACnB,WAAA;EACAyc,UAAAA,aAAa,GAAG,KAAK,CAAA;WACtB,CAAC,CACDM,KAAK,CAACC,KAAK,IACVC,UAAU,CAAC,MAAM;EACf,UAAA,MAAMD,KAAK,CAAA;EACb,SAAC,CACH,CAAC,CAAA;EACL,OAAA;OACD;MACDE,KAAK,EAAEA,MAAM;EACX1xB,MAAAA,KAAK,CAAC1D,QAAQ,CAAC0D,KAAK,CAACkP,YAAY,CAAC,CAAA;OACnC;MACDyiB,UAAU,EAAE31B,OAAO,IAAI;QACrB,MAAM41B,UAAU,GAAG71B,gBAAgB,CAACC,OAAO,EAAEgE,KAAK,CAACO,OAAO,CAAC,CAAA;EAC3DP,MAAAA,KAAK,CAACO,OAAO,GAAGuwB,YAAY,CAACc,UAAU,CAGtC,CAAA;OACF;MAED/tB,QAAQ,EAAEA,MAAM;EACd,MAAA,OAAO7D,KAAK,CAACO,OAAO,CAAC+L,KAAK,CAAA;OAC3B;MAEDhQ,QAAQ,EAAGN,OAA4B,IAAK;EAC1CgE,MAAAA,KAAK,CAACO,OAAO,CAACsxB,aAAa,IAA3B7xB,IAAAA,IAAAA,KAAK,CAACO,OAAO,CAACsxB,aAAa,CAAG71B,OAAO,CAAC,CAAA;OACvC;EAED81B,IAAAA,SAAS,EAAEA,CAAC7xB,GAAU,EAAEvB,KAAa,EAAEuC,MAAmB,KAAA;EAAA,MAAA,IAAAmhB,qBAAA,CAAA;EAAA,MAAA,OAAA,CAAAA,qBAAA,GACxDpiB,KAAK,CAACO,OAAO,CAACwxB,QAAQ,IAAtB/xB,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,KAAK,CAACO,OAAO,CAACwxB,QAAQ,CAAG9xB,GAAG,EAAEvB,KAAK,EAAEuC,MAAM,CAAC,KAAAmhB,IAAAA,GAAAA,qBAAA,GAC5C,CAAGnhB,EAAAA,MAAM,GAAG,CAACA,MAAM,CAACR,EAAE,EAAE/B,KAAK,CAAC,CAACyI,IAAI,CAAC,GAAG,CAAC,GAAGzI,KAAK,CAAE,CAAA,CAAA;EAAA,KAAA;MAEpDoO,eAAe,EAAEA,MAAM;EACrB,MAAA,IAAI,CAAC9M,KAAK,CAACgyB,gBAAgB,EAAE;UAC3BhyB,KAAK,CAACgyB,gBAAgB,GAAGhyB,KAAK,CAACO,OAAO,CAACuM,eAAe,CAAC9M,KAAK,CAAC,CAAA;EAC/D,OAAA;EAEA,MAAA,OAAOA,KAAK,CAACgyB,gBAAgB,EAAG,CAAA;OACjC;EAED;EACA;;MAEA7Q,WAAW,EAAEA,MAAM;EACjB,MAAA,OAAOnhB,KAAK,CAACklB,qBAAqB,EAAE,CAAA;OACrC;EACD;EACAtc,IAAAA,MAAM,EAAEA,CAACnI,EAAU,EAAEwxB,SAAmB,KAAK;QAC3C,IAAIhyB,GAAG,GAAG,CACRgyB,SAAS,GAAGjyB,KAAK,CAAC8gB,wBAAwB,EAAE,GAAG9gB,KAAK,CAACmhB,WAAW,EAAE,EAClEI,QAAQ,CAAC9gB,EAAE,CAAC,CAAA;QAEd,IAAI,CAACR,GAAG,EAAE;UACRA,GAAG,GAAGD,KAAK,CAAC8M,eAAe,EAAE,CAACyU,QAAQ,CAAC9gB,EAAE,CAAC,CAAA;UAC1C,IAAI,CAACR,GAAG,EAAE;EACR,UAA2C;EACzC,YAAA,MAAM,IAAIgC,KAAK,CAAC,CAAsCxB,mCAAAA,EAAAA,EAAE,EAAE,CAAC,CAAA;EAC7D,WAAA;EAEF,SAAA;EACF,OAAA;EAEA,MAAA,OAAOR,GAAG,CAAA;OACX;EACDoB,IAAAA,oBAAoB,EAAE1D,IAAI,CACxB,MAAM,CAACqC,KAAK,CAACO,OAAO,CAACa,aAAa,CAAC,EACnCA,aAAa,IAAI;EAAA,MAAA,IAAA8wB,cAAA,CAAA;QACf9wB,aAAa,GAAA,CAAA8wB,cAAA,GAAI9wB,aAAa,YAAA8wB,cAAA,GAAI,EAEjC,CAAA;QAED,OAAO;UACLvwB,MAAM,EAAEqP,KAAK,IAAI;YACf,MAAM1P,iBAAiB,GAAG0P,KAAK,CAACrP,MAAM,CAACjG,MAAM,CAC1CqF,SAAqC,CAAA;YAExC,IAAIO,iBAAiB,CAAC1F,WAAW,EAAE;cACjC,OAAO0F,iBAAiB,CAAC1F,WAAW,CAAA;EACtC,WAAA;YAEA,IAAI0F,iBAAiB,CAAC3F,UAAU,EAAE;cAChC,OAAO2F,iBAAiB,CAACb,EAAE,CAAA;EAC7B,WAAA;EAEA,UAAA,OAAO,IAAI,CAAA;WACZ;EACD;EACAJ,QAAAA,IAAI,EAAE2Q,KAAK,IAAA;YAAA,IAAAmhB,qBAAA,EAAAC,kBAAA,CAAA;YAAA,OAAAD,CAAAA,qBAAA,IAAAC,kBAAA,GAAIphB,KAAK,CAACtQ,WAAW,EAAO,KAAxB0xB,IAAAA,IAAAA,kBAAA,CAA0B/nB,QAAQ,IAAA,IAAA,GAAA,KAAA,CAAA,GAAlC+nB,kBAAA,CAA0B/nB,QAAQ,EAAI,KAAA,IAAA,GAAA8nB,qBAAA,GAAI,IAAI,CAAA;EAAA,SAAA;UAC7D,GAAGnyB,KAAK,CAACY,SAAS,CAACyI,MAAM,CAAC,CAAC+U,GAAG,EAAEvd,OAAO,KAAK;EAC1C,UAAA,OAAOuR,MAAM,CAACye,MAAM,CAACzS,GAAG,EAAEvd,OAAO,CAACsL,mBAAmB,oBAA3BtL,OAAO,CAACsL,mBAAmB,EAAI,CAAC,CAAA;WAC3D,EAAE,EAAE,CAAC;UACN,GAAG/K,aAAAA;SACJ,CAAA;OACF,EACD1B,cAAc,CAACa,OAAO,EAAE,cAAc,EAAE,sBAAsB,CAChE,CAAC;EAED8xB,IAAAA,cAAc,EAAEA,MAAMryB,KAAK,CAACO,OAAO,CAAC2B,OAAO;EAE3CyB,IAAAA,aAAa,EAAEhG,IAAI,CACjB,MAAM,CAACqC,KAAK,CAACqyB,cAAc,EAAE,CAAC,EAC9BC,UAAU,IAAI;QACZ,MAAMC,cAAc,GAAG,UACrBD,UAAuC,EACvCrxB,MAA+B,EAC/BD,KAAK,EACwB;EAAA,QAAA,IAD7BA,KAAK,KAAA,KAAA,CAAA,EAAA;EAALA,UAAAA,KAAK,GAAG,CAAC,CAAA;EAAA,SAAA;EAET,QAAA,OAAOsxB,UAAU,CAAC/uB,GAAG,CAACxC,SAAS,IAAI;YACjC,MAAMrF,MAAM,GAAGoF,YAAY,CAACd,KAAK,EAAEe,SAAS,EAAEC,KAAK,EAAEC,MAAM,CAAC,CAAA;YAE5D,MAAMuxB,iBAAiB,GAAGzxB,SAGzB,CAAA;YAEDrF,MAAM,CAACwG,OAAO,GAAGswB,iBAAiB,CAACtwB,OAAO,GACtCqwB,cAAc,CAACC,iBAAiB,CAACtwB,OAAO,EAAExG,MAAM,EAAEsF,KAAK,GAAG,CAAC,CAAC,GAC5D,EAAE,CAAA;EAEN,UAAA,OAAOtF,MAAM,CAAA;EACf,SAAC,CAAC,CAAA;SACH,CAAA;QAED,OAAO62B,cAAc,CAACD,UAAU,CAAC,CAAA;OAClC,EACD5yB,cAAc,CAACa,OAAO,EAAE,cAAc,EAAE,eAAe,CACzD,CAAC;EAEDqd,IAAAA,iBAAiB,EAAEjgB,IAAI,CACrB,MAAM,CAACqC,KAAK,CAAC2D,aAAa,EAAE,CAAC,EAC7BM,UAAU,IAAI;EACZ,MAAA,OAAOA,UAAU,CAAC5B,OAAO,CAAC3G,MAAM,IAAI;EAClC,QAAA,OAAOA,MAAM,CAACyG,cAAc,EAAE,CAAA;EAChC,OAAC,CAAC,CAAA;OACH,EACDzC,cAAc,CAACa,OAAO,EAAE,cAAc,EAAE,mBAAmB,CAC7D,CAAC;EAEDkyB,IAAAA,sBAAsB,EAAE90B,IAAI,CAC1B,MAAM,CAACqC,KAAK,CAAC4d,iBAAiB,EAAE,CAAC,EACjC8U,WAAW,IAAI;QACb,OAAOA,WAAW,CAACrpB,MAAM,CACvB,CAACC,GAAG,EAAE5N,MAAM,KAAK;EACf4N,QAAAA,GAAG,CAAC5N,MAAM,CAAC+E,EAAE,CAAC,GAAG/E,MAAM,CAAA;EACvB,QAAA,OAAO4N,GAAG,CAAA;SACX,EACD,EACF,CAAC,CAAA;OACF,EACD5J,cAAc,CAACa,OAAO,EAAE,cAAc,EAAE,uBAAuB,CACjE,CAAC;MAED2I,iBAAiB,EAAEvL,IAAI,CACrB,MAAM,CAACqC,KAAK,CAAC2D,aAAa,EAAE,EAAE3D,KAAK,CAACuC,kBAAkB,EAAE,CAAC,EACzD,CAAC0B,UAAU,EAAEzB,YAAY,KAAK;EAC5B,MAAA,IAAIE,WAAW,GAAGuB,UAAU,CAAC5B,OAAO,CAAC3G,MAAM,IAAIA,MAAM,CAAC4G,cAAc,EAAE,CAAC,CAAA;QACvE,OAAOE,YAAY,CAACE,WAAW,CAAC,CAAA;OACjC,EACDhD,cAAc,CAACa,OAAO,EAAE,cAAc,EAAE,mBAAmB,CAC7D,CAAC;MAEDgI,SAAS,EAAErI,QAAQ,IAAI;QACrB,MAAMxE,MAAM,GAAGsE,KAAK,CAACyyB,sBAAsB,EAAE,CAACvyB,QAAQ,CAAC,CAAA;QAEvD,IAA6C,CAACxE,MAAM,EAAE;EACpD4D,QAAAA,OAAO,CAACkyB,KAAK,CAAC,CAA2BtxB,wBAAAA,EAAAA,QAAQ,mBAAmB,CAAC,CAAA;EACvE,OAAA;EAEA,MAAA,OAAOxE,MAAM,CAAA;EACf,KAAA;KACD,CAAA;EAED0W,EAAAA,MAAM,CAACye,MAAM,CAAC7wB,KAAK,EAAEkxB,YAAY,CAAC,CAAA;EAElC,EAAA,KAAK,IAAIxyB,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGsB,KAAK,CAACY,SAAS,CAAClD,MAAM,EAAEgB,KAAK,EAAE,EAAE;EAC3D,IAAA,MAAMmC,OAAO,GAAGb,KAAK,CAACY,SAAS,CAAClC,KAAK,CAAC,CAAA;MACtCmC,OAAO,IAAA,IAAA,IAAPA,OAAO,CAAE4C,WAAW,IAAA,IAAA,IAApB5C,OAAO,CAAE4C,WAAW,CAAGzD,KAAK,CAAC,CAAA;EAC/B,GAAA;EAEA,EAAA,OAAOA,KAAK,CAAA;EACd;;EC1gBO,SAAS8M,eAAeA,GAEJ;EACzB,EAAA,OAAO9M,KAAK,IACVrC,IAAI,CACF,MAAM,CAACqC,KAAK,CAACO,OAAO,CAACoyB,IAAI,CAAC,EAExBA,IAAI,IAKD;EACH,IAAA,MAAMrJ,QAAyB,GAAG;EAChC7D,MAAAA,IAAI,EAAE,EAAE;EACR1Y,MAAAA,QAAQ,EAAE,EAAE;EACZwU,MAAAA,QAAQ,EAAE,EAAC;OACZ,CAAA;MAED,MAAMqR,UAAU,GAAG,UACjBC,YAAqB,EACrB7xB,KAAK,EACLgI,SAAsB,EACL;EAAA,MAAA,IAFjBhI,KAAK,KAAA,KAAA,CAAA,EAAA;EAALA,QAAAA,KAAK,GAAG,CAAC,CAAA;EAAA,OAAA;QAGT,MAAMykB,IAAI,GAAG,EAAkB,CAAA;EAE/B,MAAA,KAAK,IAAIlc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGspB,YAAY,CAACn1B,MAAM,EAAE6L,CAAC,EAAE,EAAE;EAC5C;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA,QAAA,MAAMtJ,GAAG,GAAG8H,SAAS,CACnB/H,KAAK,EACLA,KAAK,CAAC8xB,SAAS,CAACe,YAAY,CAACtpB,CAAC,CAAC,EAAGA,CAAC,EAAEP,SAAS,CAAC,EAC/C6pB,YAAY,CAACtpB,CAAC,CAAC,EACfA,CAAC,EACDvI,KAAK,EACLU,SAAS,EACTsH,SAAS,IAAA,IAAA,GAAA,KAAA,CAAA,GAATA,SAAS,CAAEvI,EACb,CAAC,CAAA;;EAED;EACA6oB,QAAAA,QAAQ,CAACvc,QAAQ,CAACvP,IAAI,CAACyC,GAAG,CAAC,CAAA;EAC3B;UACAqpB,QAAQ,CAAC/H,QAAQ,CAACthB,GAAG,CAACQ,EAAE,CAAC,GAAGR,GAAG,CAAA;EAC/B;EACAwlB,QAAAA,IAAI,CAACjoB,IAAI,CAACyC,GAAG,CAAC,CAAA;;EAEd;EACA,QAAA,IAAID,KAAK,CAACO,OAAO,CAACuyB,UAAU,EAAE;EAAA,UAAA,IAAAC,oBAAA,CAAA;EAC5B9yB,UAAAA,GAAG,CAAC+yB,eAAe,GAAGhzB,KAAK,CAACO,OAAO,CAACuyB,UAAU,CAC5CD,YAAY,CAACtpB,CAAC,CAAC,EACfA,CACF,CAAC,CAAA;;EAED;YACA,IAAAwpB,CAAAA,oBAAA,GAAI9yB,GAAG,CAAC+yB,eAAe,KAAnBD,IAAAA,IAAAA,oBAAA,CAAqBr1B,MAAM,EAAE;EAC/BuC,YAAAA,GAAG,CAACiI,OAAO,GAAG0qB,UAAU,CAAC3yB,GAAG,CAAC+yB,eAAe,EAAEhyB,KAAK,GAAG,CAAC,EAAEf,GAAG,CAAC,CAAA;EAC/D,WAAA;EACF,SAAA;EACF,OAAA;EAEA,MAAA,OAAOwlB,IAAI,CAAA;OACZ,CAAA;EAED6D,IAAAA,QAAQ,CAAC7D,IAAI,GAAGmN,UAAU,CAACD,IAAI,CAAC,CAAA;EAEhC,IAAA,OAAOrJ,QAAQ,CAAA;EACjB,GAAC,EACD5pB,cAAc,CAACM,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE,MACzDP,KAAK,CAACmjB,mBAAmB,EAC3B,CACF,CAAC,CAAA;EACL;;EC9EO,SAASxB,mBAAmBA,GAER;EACzB,EAAA,OAAO3hB,KAAK,IACVrC,IAAI,CACF,MAAM,CACJqC,KAAK,CAAC6D,QAAQ,EAAE,CAACgc,QAAQ,EACzB7f,KAAK,CAACyhB,sBAAsB,EAAE,EAC9BzhB,KAAK,CAACO,OAAO,CAACwf,oBAAoB,CACnC,EACD,CAACF,QAAQ,EAAEyJ,QAAQ,EAAEvJ,oBAAoB,KAAK;MAC5C,IACE,CAACuJ,QAAQ,CAAC7D,IAAI,CAAC/nB,MAAM,IACpBmiB,QAAQ,KAAK,IAAI,IAAI,CAACzN,MAAM,CAAC8O,IAAI,CAACrB,QAAQ,IAARA,IAAAA,GAAAA,QAAQ,GAAI,EAAE,CAAC,CAACniB,MAAO,EAC1D;EACA,MAAA,OAAO4rB,QAAQ,CAAA;EACjB,KAAA;MAEA,IAAI,CAACvJ,oBAAoB,EAAE;EACzB;EACA,MAAA,OAAOuJ,QAAQ,CAAA;EACjB,KAAA;MAEA,OAAO2J,UAAU,CAAC3J,QAAQ,CAAC,CAAA;KAC5B,EACD5pB,cAAc,CAACM,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,qBAAqB,CACnE,CAAC,CAAA;EACL,CAAA;EAEO,SAAS0yB,UAAUA,CAAwB3J,QAAyB,EAAE;IAC3E,MAAM4J,YAA0B,GAAG,EAAE,CAAA;IAErC,MAAMC,SAAS,GAAIlzB,GAAe,IAAK;EAAA,IAAA,IAAAkT,YAAA,CAAA;EACrC+f,IAAAA,YAAY,CAAC11B,IAAI,CAACyC,GAAG,CAAC,CAAA;EAEtB,IAAA,IAAI,CAAAkT,YAAA,GAAAlT,GAAG,CAACiI,OAAO,KAAXiL,IAAAA,IAAAA,YAAA,CAAazV,MAAM,IAAIuC,GAAG,CAACmhB,aAAa,EAAE,EAAE;EAC9CnhB,MAAAA,GAAG,CAACiI,OAAO,CAAC5K,OAAO,CAAC61B,SAAS,CAAC,CAAA;EAChC,KAAA;KACD,CAAA;EAED7J,EAAAA,QAAQ,CAAC7D,IAAI,CAACnoB,OAAO,CAAC61B,SAAS,CAAC,CAAA;IAEhC,OAAO;EACL1N,IAAAA,IAAI,EAAEyN,YAAY;MAClBnmB,QAAQ,EAAEuc,QAAQ,CAACvc,QAAQ;MAC3BwU,QAAQ,EAAE+H,QAAQ,CAAC/H,QAAAA;KACpB,CAAA;EACH;;EC/CO,SAASvX,sBAAsBA,GAGE;EACtC,EAAA,OAAO,CAAChK,KAAK,EAAEE,QAAQ,KACrBvC,IAAI,CACF,MAAA;EAAA,IAAA,IAAAy1B,gBAAA,CAAA;EAAA,IAAA,OAAM,CAAAA,CAAAA,gBAAA,GAACpzB,KAAK,CAACuI,SAAS,CAACrI,QAAQ,CAAC,qBAAzBkzB,gBAAA,CAA2B1pB,kBAAkB,EAAE,CAAC,CAAA;EAAA,GAAA,EACvD2pB,eAAe,IAAI;EACjB,IAAA,IAAI,CAACA,eAAe,EAAE,OAAO3xB,SAAS,CAAA;MAEtC,MAAM4xB,YAAY,GAAGD,eAAe,CAACtmB,QAAQ,CAC1C1K,OAAO,CAACkxB,OAAO,IAAA;EAAA,MAAA,IAAAC,qBAAA,CAAA;QAAA,OAAAA,CAAAA,qBAAA,GAAID,OAAO,CAAC/qB,eAAe,CAACtI,QAAQ,CAAC,KAAA,IAAA,GAAAszB,qBAAA,GAAI,EAAE,CAAA;EAAA,KAAA,CAAC,CAC3DjwB,GAAG,CAACsI,MAAM,CAAC,CACXvH,MAAM,CAAC0I,KAAK,IAAI,CAACnB,MAAM,CAACC,KAAK,CAACkB,KAAK,CAAC,CAAC,CAAA;EAExC,IAAA,IAAI,CAACsmB,YAAY,CAAC51B,MAAM,EAAE,OAAA;EAE1B,IAAA,IAAI+1B,eAAe,GAAGH,YAAY,CAAC,CAAC,CAAE,CAAA;MACtC,IAAII,eAAe,GAAGJ,YAAY,CAACA,YAAY,CAAC51B,MAAM,GAAG,CAAC,CAAE,CAAA;EAE5D,IAAA,KAAK,MAAMsP,KAAK,IAAIsmB,YAAY,EAAE;EAChC,MAAA,IAAItmB,KAAK,GAAGymB,eAAe,EAAEA,eAAe,GAAGzmB,KAAK,CAC/C,KAAA,IAAIA,KAAK,GAAG0mB,eAAe,EAAEA,eAAe,GAAG1mB,KAAK,CAAA;EAC3D,KAAA;EAEA,IAAA,OAAO,CAACymB,eAAe,EAAEC,eAAe,CAAC,CAAA;KAC1C,EACDh0B,cAAc,CAACM,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,wBAAwB,CACtE,CAAC,CAAA;EACL;;EC7BO,SAASozB,UAAUA,CACxBlO,IAAkB,EAClBmO,aAAuC,EACvC5zB,KAAmB,EACnB;EACA,EAAA,IAAIA,KAAK,CAACO,OAAO,CAACmM,kBAAkB,EAAE;EACpC,IAAA,OAAOmnB,uBAAuB,CAACpO,IAAI,EAAEmO,aAAa,EAAE5zB,KAAK,CAAC,CAAA;EAC5D,GAAA;EAEA,EAAA,OAAO8zB,sBAAsB,CAACrO,IAAI,EAAEmO,aAAa,EAAE5zB,KAAK,CAAC,CAAA;EAC3D,CAAA;EAEA,SAAS6zB,uBAAuBA,CAC9BE,YAA0B,EAC1BC,SAA4C,EAC5Ch0B,KAAmB,EACF;EAAA,EAAA,IAAAi0B,qBAAA,CAAA;IACjB,MAAMC,mBAAiC,GAAG,EAAE,CAAA;IAC5C,MAAMC,mBAA+C,GAAG,EAAE,CAAA;EAC1D,EAAA,MAAMrtB,QAAQ,GAAA,CAAAmtB,qBAAA,GAAGj0B,KAAK,CAACO,OAAO,CAACoM,qBAAqB,KAAA,IAAA,GAAAsnB,qBAAA,GAAI,GAAG,CAAA;EAE3D,EAAA,MAAMG,iBAAiB,GAAG,UAACL,YAA0B,EAAE/yB,KAAK,EAAS;EAAA,IAAA,IAAdA,KAAK,KAAA,KAAA,CAAA,EAAA;EAALA,MAAAA,KAAK,GAAG,CAAC,CAAA;EAAA,KAAA;MAC9D,MAAMykB,IAAkB,GAAG,EAAE,CAAA;;EAE7B;EACA,IAAA,KAAK,IAAIlc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwqB,YAAY,CAACr2B,MAAM,EAAE6L,CAAC,EAAE,EAAE;EAAA,MAAA,IAAA4J,YAAA,CAAA;EAC5C,MAAA,IAAIlT,GAAG,GAAG8zB,YAAY,CAACxqB,CAAC,CAAE,CAAA;QAE1B,MAAM8qB,MAAM,GAAGtsB,SAAS,CACtB/H,KAAK,EACLC,GAAG,CAACQ,EAAE,EACNR,GAAG,CAAC+H,QAAQ,EACZ/H,GAAG,CAACvB,KAAK,EACTuB,GAAG,CAACe,KAAK,EACTU,SAAS,EACTzB,GAAG,CAACkI,QACN,CAAC,CAAA;EACDksB,MAAAA,MAAM,CAAC9nB,aAAa,GAAGtM,GAAG,CAACsM,aAAa,CAAA;EAExC,MAAA,IAAI,CAAA4G,YAAA,GAAAlT,GAAG,CAACiI,OAAO,KAAA,IAAA,IAAXiL,YAAA,CAAazV,MAAM,IAAIsD,KAAK,GAAG8F,QAAQ,EAAE;EAC3CutB,QAAAA,MAAM,CAACnsB,OAAO,GAAGksB,iBAAiB,CAACn0B,GAAG,CAACiI,OAAO,EAAElH,KAAK,GAAG,CAAC,CAAC,CAAA;EAC1Df,QAAAA,GAAG,GAAGo0B,MAAM,CAAA;UAEZ,IAAIL,SAAS,CAAC/zB,GAAG,CAAC,IAAI,CAACo0B,MAAM,CAACnsB,OAAO,CAACxK,MAAM,EAAE;EAC5C+nB,UAAAA,IAAI,CAACjoB,IAAI,CAACyC,GAAG,CAAC,CAAA;EACdk0B,UAAAA,mBAAmB,CAACl0B,GAAG,CAACQ,EAAE,CAAC,GAAGR,GAAG,CAAA;EACjCi0B,UAAAA,mBAAmB,CAAC12B,IAAI,CAACyC,GAAG,CAAC,CAAA;EAC7B,UAAA,SAAA;EACF,SAAA;UAEA,IAAI+zB,SAAS,CAAC/zB,GAAG,CAAC,IAAIo0B,MAAM,CAACnsB,OAAO,CAACxK,MAAM,EAAE;EAC3C+nB,UAAAA,IAAI,CAACjoB,IAAI,CAACyC,GAAG,CAAC,CAAA;EACdk0B,UAAAA,mBAAmB,CAACl0B,GAAG,CAACQ,EAAE,CAAC,GAAGR,GAAG,CAAA;EACjCi0B,UAAAA,mBAAmB,CAAC12B,IAAI,CAACyC,GAAG,CAAC,CAAA;EAC7B,UAAA,SAAA;EACF,SAAA;EACF,OAAC,MAAM;EACLA,QAAAA,GAAG,GAAGo0B,MAAM,CAAA;EACZ,QAAA,IAAIL,SAAS,CAAC/zB,GAAG,CAAC,EAAE;EAClBwlB,UAAAA,IAAI,CAACjoB,IAAI,CAACyC,GAAG,CAAC,CAAA;EACdk0B,UAAAA,mBAAmB,CAACl0B,GAAG,CAACQ,EAAE,CAAC,GAAGR,GAAG,CAAA;EACjCi0B,UAAAA,mBAAmB,CAAC12B,IAAI,CAACyC,GAAG,CAAC,CAAA;EAC/B,SAAA;EACF,OAAA;EACF,KAAA;EAEA,IAAA,OAAOwlB,IAAI,CAAA;KACZ,CAAA;IAED,OAAO;EACLA,IAAAA,IAAI,EAAE2O,iBAAiB,CAACL,YAAY,CAAC;EACrChnB,IAAAA,QAAQ,EAAEmnB,mBAAmB;EAC7B3S,IAAAA,QAAQ,EAAE4S,mBAAAA;KACX,CAAA;EACH,CAAA;EAEA,SAASL,sBAAsBA,CAC7BC,YAA0B,EAC1BC,SAAmC,EACnCh0B,KAAmB,EACF;EAAA,EAAA,IAAAs0B,sBAAA,CAAA;IACjB,MAAMJ,mBAAiC,GAAG,EAAE,CAAA;IAC5C,MAAMC,mBAA+C,GAAG,EAAE,CAAA;EAC1D,EAAA,MAAMrtB,QAAQ,GAAA,CAAAwtB,sBAAA,GAAGt0B,KAAK,CAACO,OAAO,CAACoM,qBAAqB,KAAA,IAAA,GAAA2nB,sBAAA,GAAI,GAAG,CAAA;;EAE3D;EACA,EAAA,MAAMF,iBAAiB,GAAG,UAACL,YAA0B,EAAE/yB,KAAK,EAAS;EAAA,IAAA,IAAdA,KAAK,KAAA,KAAA,CAAA,EAAA;EAALA,MAAAA,KAAK,GAAG,CAAC,CAAA;EAAA,KAAA;EAC9D;;MAEA,MAAMykB,IAAkB,GAAG,EAAE,CAAA;;EAE7B;EACA,IAAA,KAAK,IAAIlc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwqB,YAAY,CAACr2B,MAAM,EAAE6L,CAAC,EAAE,EAAE;EAC5C,MAAA,IAAItJ,GAAG,GAAG8zB,YAAY,CAACxqB,CAAC,CAAE,CAAA;EAE1B,MAAA,MAAMgrB,IAAI,GAAGP,SAAS,CAAC/zB,GAAG,CAAC,CAAA;EAE3B,MAAA,IAAIs0B,IAAI,EAAE;EAAA,QAAA,IAAAjJ,aAAA,CAAA;EACR,QAAA,IAAI,CAAAA,aAAA,GAAArrB,GAAG,CAACiI,OAAO,KAAA,IAAA,IAAXojB,aAAA,CAAa5tB,MAAM,IAAIsD,KAAK,GAAG8F,QAAQ,EAAE;YAC3C,MAAMutB,MAAM,GAAGtsB,SAAS,CACtB/H,KAAK,EACLC,GAAG,CAACQ,EAAE,EACNR,GAAG,CAAC+H,QAAQ,EACZ/H,GAAG,CAACvB,KAAK,EACTuB,GAAG,CAACe,KAAK,EACTU,SAAS,EACTzB,GAAG,CAACkI,QACN,CAAC,CAAA;EACDksB,UAAAA,MAAM,CAACnsB,OAAO,GAAGksB,iBAAiB,CAACn0B,GAAG,CAACiI,OAAO,EAAElH,KAAK,GAAG,CAAC,CAAC,CAAA;EAC1Df,UAAAA,GAAG,GAAGo0B,MAAM,CAAA;EACd,SAAA;EAEA5O,QAAAA,IAAI,CAACjoB,IAAI,CAACyC,GAAG,CAAC,CAAA;EACdi0B,QAAAA,mBAAmB,CAAC12B,IAAI,CAACyC,GAAG,CAAC,CAAA;EAC7Bk0B,QAAAA,mBAAmB,CAACl0B,GAAG,CAACQ,EAAE,CAAC,GAAGR,GAAG,CAAA;EACnC,OAAA;EACF,KAAA;EAEA,IAAA,OAAOwlB,IAAI,CAAA;KACZ,CAAA;IAED,OAAO;EACLA,IAAAA,IAAI,EAAE2O,iBAAiB,CAACL,YAAY,CAAC;EACrChnB,IAAAA,QAAQ,EAAEmnB,mBAAmB;EAC7B3S,IAAAA,QAAQ,EAAE4S,mBAAAA;KACX,CAAA;EACH;;EC7HO,SAASzqB,kBAAkBA,GAGP;IACzB,OAAO,CAAC1J,KAAK,EAAEE,QAAQ,KACrBvC,IAAI,CACF,MAAM,CACJqC,KAAK,CAAC2J,sBAAsB,EAAE,EAC9B3J,KAAK,CAAC6D,QAAQ,EAAE,CAAC0I,aAAa,EAC9BvM,KAAK,CAAC6D,QAAQ,EAAE,CAACmb,YAAY,EAC7Bhf,KAAK,CAACmP,mBAAmB,EAAE,CAC5B,EACD,CAACqlB,WAAW,EAAEjoB,aAAa,EAAEyS,YAAY,KAAK;EAC5C,IAAA,IACE,CAACwV,WAAW,CAAC/O,IAAI,CAAC/nB,MAAM,IACvB,EAAC6O,aAAa,IAAA,IAAA,IAAbA,aAAa,CAAE7O,MAAM,CAAI,IAAA,CAACshB,YAAa,EACzC;EACA,MAAA,OAAOwV,WAAW,CAAA;EACpB,KAAA;EAEA,IAAA,MAAMC,aAAa,GAAG,CACpB,GAAGloB,aAAa,CAAChJ,GAAG,CAAC9G,CAAC,IAAIA,CAAC,CAACgE,EAAE,CAAC,CAAC6D,MAAM,CAAC7H,CAAC,IAAIA,CAAC,KAAKyD,QAAQ,CAAC,EAC3D8e,YAAY,GAAG,YAAY,GAAGtd,SAAS,CACxC,CAAC4C,MAAM,CAACC,OAAO,CAAa,CAAA;MAE7B,MAAMmwB,cAAc,GAAIz0B,GAAe,IAAK;EAC1C;EACA,MAAA,KAAK,IAAIsJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkrB,aAAa,CAAC/2B,MAAM,EAAE6L,CAAC,EAAE,EAAE;UAC7C,IAAItJ,GAAG,CAACsM,aAAa,CAACkoB,aAAa,CAAClrB,CAAC,CAAC,CAAE,KAAK,KAAK,EAAE;EAClD,UAAA,OAAO,KAAK,CAAA;EACd,SAAA;EACF,OAAA;EACA,MAAA,OAAO,IAAI,CAAA;OACZ,CAAA;MAED,OAAOoqB,UAAU,CAACa,WAAW,CAAC/O,IAAI,EAAEiP,cAAc,EAAE10B,KAAK,CAAC,CAAA;KAC3D,EACDN,cAAc,CAACM,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,oBAAoB,CAClE,CAAC,CAAA;EACL;;ECxCO,SAASsJ,sBAAsBA,GAGV;EAC1B,EAAA,OAAO,CAAC7J,KAAK,EAAEE,QAAQ,KACrBvC,IAAI,CACF,MAAA;EAAA,IAAA,IAAAy1B,gBAAA,CAAA;EAAA,IAAA,OAAM,CAAAA,CAAAA,gBAAA,GAACpzB,KAAK,CAACuI,SAAS,CAACrI,QAAQ,CAAC,qBAAzBkzB,gBAAA,CAA2B1pB,kBAAkB,EAAE,CAAC,CAAA;EAAA,GAAA,EACvD2pB,eAAe,IAAI;EACjB,IAAA,IAAI,CAACA,eAAe,EAAE,OAAO,IAAIvpB,GAAG,EAAE,CAAA;EAEtC,IAAA,IAAI6qB,mBAAmB,GAAG,IAAI7qB,GAAG,EAAe,CAAA;EAEhD,IAAA,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8pB,eAAe,CAACtmB,QAAQ,CAACrP,MAAM,EAAE6L,CAAC,EAAE,EAAE;EACxD,MAAA,MAAMyG,MAAM,GACVqjB,eAAe,CAACtmB,QAAQ,CAACxD,CAAC,CAAC,CAAEf,eAAe,CAAStI,QAAQ,CAAC,CAAA;EAEhE,MAAA,KAAK,IAAI00B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5kB,MAAM,CAACtS,MAAM,EAAEk3B,CAAC,EAAE,EAAE;EACtC,QAAA,MAAM5nB,KAAK,GAAGgD,MAAM,CAAC4kB,CAAC,CAAE,CAAA;EAExB,QAAA,IAAID,mBAAmB,CAAClO,GAAG,CAACzZ,KAAK,CAAC,EAAE;EAAA,UAAA,IAAA6nB,qBAAA,CAAA;YAClCF,mBAAmB,CAACG,GAAG,CACrB9nB,KAAK,EACL,CAAA6nB,CAAAA,qBAAA,GAACF,mBAAmB,CAACI,GAAG,CAAC/nB,KAAK,CAAC,KAAA6nB,IAAAA,GAAAA,qBAAA,GAAI,CAAC,IAAI,CAC1C,CAAC,CAAA;EACH,SAAC,MAAM;EACLF,UAAAA,mBAAmB,CAACG,GAAG,CAAC9nB,KAAK,EAAE,CAAC,CAAC,CAAA;EACnC,SAAA;EACF,OAAA;EACF,KAAA;EAEA,IAAA,OAAO2nB,mBAAmB,CAAA;EAC5B,GAAC,EACDj1B,cAAc,CACZM,KAAK,CAACO,OAAO,EACb,YAAY,EACZ,CAAA,uBAAA,EAA0BL,QAAQ,CAAA,CACpC,CACF,CAAC,CAAA;EACL;;ECpCO,SAASiP,mBAAmBA,GAER;EACzB,EAAA,OAAOnP,KAAK,IACVrC,IAAI,CACF,MAAM,CACJqC,KAAK,CAAC2J,sBAAsB,EAAE,EAC9B3J,KAAK,CAAC6D,QAAQ,EAAE,CAAC0I,aAAa,EAC9BvM,KAAK,CAAC6D,QAAQ,EAAE,CAACmb,YAAY,CAC9B,EACD,CAACsK,QAAQ,EAAE/c,aAAa,EAAEyS,YAAY,KAAK;EACzC,IAAA,IACE,CAACsK,QAAQ,CAAC7D,IAAI,CAAC/nB,MAAM,IACpB,EAAC6O,aAAa,IAAA,IAAA,IAAbA,aAAa,CAAE7O,MAAM,CAAI,IAAA,CAACshB,YAAa,EACzC;EACA,MAAA,KAAK,IAAIzV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+f,QAAQ,CAACvc,QAAQ,CAACrP,MAAM,EAAE6L,CAAC,EAAE,EAAE;UACjD+f,QAAQ,CAACvc,QAAQ,CAACxD,CAAC,CAAC,CAAEgD,aAAa,GAAG,EAAE,CAAA;UACxC+c,QAAQ,CAACvc,QAAQ,CAACxD,CAAC,CAAC,CAAEoF,iBAAiB,GAAG,EAAE,CAAA;EAC9C,OAAA;EACA,MAAA,OAAO2a,QAAQ,CAAA;EACjB,KAAA;MAEA,MAAM0L,qBAAoD,GAAG,EAAE,CAAA;MAC/D,MAAMC,qBAAoD,GAAG,EAAE,CAAA;MAE9D,CAAC1oB,aAAa,WAAbA,aAAa,GAAI,EAAE,EAAEjP,OAAO,CAACb,CAAC,IAAI;EAAA,MAAA,IAAAy4B,qBAAA,CAAA;QAClC,MAAMx5B,MAAM,GAAGsE,KAAK,CAACuI,SAAS,CAAC9L,CAAC,CAACgE,EAAE,CAAC,CAAA;QAEpC,IAAI,CAAC/E,MAAM,EAAE;EACX,QAAA,OAAA;EACF,OAAA;EAEA,MAAA,MAAM0Q,QAAQ,GAAG1Q,MAAM,CAACuR,WAAW,EAAE,CAAA;QAErC,IAAI,CAACb,QAAQ,EAAE;EACb,QAA2C;YACzC9M,OAAO,CAAC0C,IAAI,CACV,CAAA,iEAAA,EAAoEtG,MAAM,CAAC+E,EAAE,GAC/E,CAAC,CAAA;EACH,SAAA;EACA,QAAA,OAAA;EACF,OAAA;QAEAu0B,qBAAqB,CAACx3B,IAAI,CAAC;UACzBiD,EAAE,EAAEhE,CAAC,CAACgE,EAAE;UACR2L,QAAQ;EACR6c,QAAAA,aAAa,GAAAiM,qBAAA,GAAE9oB,QAAQ,CAACb,kBAAkB,oBAA3Ba,QAAQ,CAACb,kBAAkB,CAAG9O,CAAC,CAACuQ,KAAK,CAAC,YAAAkoB,qBAAA,GAAIz4B,CAAC,CAACuQ,KAAAA;EAC7D,OAAC,CAAC,CAAA;EACJ,KAAC,CAAC,CAAA;EAEF,IAAA,MAAMynB,aAAa,GAAG,CAACloB,aAAa,IAAA,IAAA,GAAbA,aAAa,GAAI,EAAE,EAAEhJ,GAAG,CAAC9G,CAAC,IAAIA,CAAC,CAACgE,EAAE,CAAC,CAAA;EAE1D,IAAA,MAAMye,cAAc,GAAGlf,KAAK,CAACyf,iBAAiB,EAAE,CAAA;EAEhD,IAAA,MAAM0V,yBAAyB,GAAGn1B,KAAK,CACpCkJ,iBAAiB,EAAE,CACnB5E,MAAM,CAAC5I,MAAM,IAAIA,MAAM,CAAC2jB,kBAAkB,EAAE,CAAC,CAAA;EAEhD,IAAA,IACEL,YAAY,IACZE,cAAc,IACdiW,yBAAyB,CAACz3B,MAAM,EAChC;EACA+2B,MAAAA,aAAa,CAACj3B,IAAI,CAAC,YAAY,CAAC,CAAA;EAEhC23B,MAAAA,yBAAyB,CAAC73B,OAAO,CAAC5B,MAAM,IAAI;EAAA,QAAA,IAAA05B,qBAAA,CAAA;UAC1CH,qBAAqB,CAACz3B,IAAI,CAAC;YACzBiD,EAAE,EAAE/E,MAAM,CAAC+E,EAAE;EACb2L,UAAAA,QAAQ,EAAE8S,cAAc;EACxB+J,UAAAA,aAAa,EAAAmM,CAAAA,qBAAA,GACXlW,cAAc,CAAC3T,kBAAkB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAjC2T,cAAc,CAAC3T,kBAAkB,CAAGyT,YAAY,CAAC,KAAA,IAAA,GAAAoW,qBAAA,GACjDpW,YAAAA;EACJ,SAAC,CAAC,CAAA;EACJ,OAAC,CAAC,CAAA;EACJ,KAAA;EAEA,IAAA,IAAIqW,mBAAmB,CAAA;EACvB,IAAA,IAAIC,mBAAmB,CAAA;;EAEvB;EACA,IAAA,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtL,QAAQ,CAACvc,QAAQ,CAACrP,MAAM,EAAEk3B,CAAC,EAAE,EAAE;EACjD,MAAA,MAAM30B,GAAG,GAAGqpB,QAAQ,CAACvc,QAAQ,CAAC6nB,CAAC,CAAE,CAAA;EAEjC30B,MAAAA,GAAG,CAACsM,aAAa,GAAG,EAAE,CAAA;QAEtB,IAAIyoB,qBAAqB,CAACt3B,MAAM,EAAE;EAChC,QAAA,KAAK,IAAI6L,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyrB,qBAAqB,CAACt3B,MAAM,EAAE6L,CAAC,EAAE,EAAE;EACrD8rB,UAAAA,mBAAmB,GAAGL,qBAAqB,CAACzrB,CAAC,CAAE,CAAA;EAC/C,UAAA,MAAM9I,EAAE,GAAG40B,mBAAmB,CAAC50B,EAAE,CAAA;;EAEjC;EACAR,UAAAA,GAAG,CAACsM,aAAa,CAAC9L,EAAE,CAAC,GAAG40B,mBAAmB,CAACjpB,QAAQ,CAClDnM,GAAG,EACHQ,EAAE,EACF40B,mBAAmB,CAACpM,aAAa,EACjCsM,UAAU,IAAI;EACZt1B,YAAAA,GAAG,CAAC0O,iBAAiB,CAAClO,EAAE,CAAC,GAAG80B,UAAU,CAAA;EACxC,WACF,CAAC,CAAA;EACH,SAAA;EACF,OAAA;QAEA,IAAIN,qBAAqB,CAACv3B,MAAM,EAAE;EAChC,QAAA,KAAK,IAAI6L,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0rB,qBAAqB,CAACv3B,MAAM,EAAE6L,CAAC,EAAE,EAAE;EACrD+rB,UAAAA,mBAAmB,GAAGL,qBAAqB,CAAC1rB,CAAC,CAAE,CAAA;EAC/C,UAAA,MAAM9I,EAAE,GAAG60B,mBAAmB,CAAC70B,EAAE,CAAA;EACjC;EACA,UAAA,IACE60B,mBAAmB,CAAClpB,QAAQ,CAC1BnM,GAAG,EACHQ,EAAE,EACF60B,mBAAmB,CAACrM,aAAa,EACjCsM,UAAU,IAAI;EACZt1B,YAAAA,GAAG,CAAC0O,iBAAiB,CAAClO,EAAE,CAAC,GAAG80B,UAAU,CAAA;EACxC,WACF,CAAC,EACD;EACAt1B,YAAAA,GAAG,CAACsM,aAAa,CAACipB,UAAU,GAAG,IAAI,CAAA;EACnC,YAAA,MAAA;EACF,WAAA;EACF,SAAA;EAEA,QAAA,IAAIv1B,GAAG,CAACsM,aAAa,CAACipB,UAAU,KAAK,IAAI,EAAE;EACzCv1B,UAAAA,GAAG,CAACsM,aAAa,CAACipB,UAAU,GAAG,KAAK,CAAA;EACtC,SAAA;EACF,OAAA;EACF,KAAA;MAEA,MAAMd,cAAc,GAAIz0B,GAAe,IAAK;EAC1C;EACA,MAAA,KAAK,IAAIsJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkrB,aAAa,CAAC/2B,MAAM,EAAE6L,CAAC,EAAE,EAAE;UAC7C,IAAItJ,GAAG,CAACsM,aAAa,CAACkoB,aAAa,CAAClrB,CAAC,CAAC,CAAE,KAAK,KAAK,EAAE;EAClD,UAAA,OAAO,KAAK,CAAA;EACd,SAAA;EACF,OAAA;EACA,MAAA,OAAO,IAAI,CAAA;OACZ,CAAA;;EAED;MACA,OAAOoqB,UAAU,CAACrK,QAAQ,CAAC7D,IAAI,EAAEiP,cAAc,EAAE10B,KAAK,CAAC,CAAA;EACzD,GAAC,EACDN,cAAc,CAACM,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,qBAAqB,EAAE,MACjEP,KAAK,CAACmjB,mBAAmB,EAC3B,CACF,CAAC,CAAA;EACL;;ECjJO,SAASvQ,kBAAkBA,GAEP;IACzB,OAAO5S,KAAK,IACVrC,IAAI,CACF,MAAM,CAACqC,KAAK,CAAC6D,QAAQ,EAAE,CAACuN,QAAQ,EAAEpR,KAAK,CAAC2S,qBAAqB,EAAE,CAAC,EAChE,CAACvB,QAAQ,EAAEkY,QAAQ,KAAK;MACtB,IAAI,CAACA,QAAQ,CAAC7D,IAAI,CAAC/nB,MAAM,IAAI,CAAC0T,QAAQ,CAAC1T,MAAM,EAAE;EAC7C4rB,MAAAA,QAAQ,CAAC7D,IAAI,CAACnoB,OAAO,CAAC2C,GAAG,IAAI;UAC3BA,GAAG,CAACe,KAAK,GAAG,CAAC,CAAA;UACbf,GAAG,CAACkI,QAAQ,GAAGzG,SAAS,CAAA;EAC1B,OAAC,CAAC,CAAA;EACF,MAAA,OAAO4nB,QAAQ,CAAA;EACjB,KAAA;;EAEA;EACA,IAAA,MAAMmM,gBAAgB,GAAGrkB,QAAQ,CAAC9M,MAAM,CAACpE,QAAQ,IAC/CF,KAAK,CAACuI,SAAS,CAACrI,QAAQ,CAC1B,CAAC,CAAA;MAED,MAAMw1B,eAA6B,GAAG,EAAE,CAAA;MACxC,MAAMC,eAA2C,GAAG,EAAE,CAAA;EACtD;EACA;EACA;EACA;;EAEA;MACA,MAAMC,kBAAkB,GAAG,UACzBnQ,IAAkB,EAClBzkB,KAAK,EACLmH,QAAiB,EACd;EAAA,MAAA,IAFHnH,KAAK,KAAA,KAAA,CAAA,EAAA;EAALA,QAAAA,KAAK,GAAG,CAAC,CAAA;EAAA,OAAA;EAGT;EACA;EACA,MAAA,IAAIA,KAAK,IAAIy0B,gBAAgB,CAAC/3B,MAAM,EAAE;EACpC,QAAA,OAAO+nB,IAAI,CAACliB,GAAG,CAACtD,GAAG,IAAI;YACrBA,GAAG,CAACe,KAAK,GAAGA,KAAK,CAAA;EAEjB00B,UAAAA,eAAe,CAACl4B,IAAI,CAACyC,GAAG,CAAC,CAAA;EACzB01B,UAAAA,eAAe,CAAC11B,GAAG,CAACQ,EAAE,CAAC,GAAGR,GAAG,CAAA;YAE7B,IAAIA,GAAG,CAACiI,OAAO,EAAE;EACfjI,YAAAA,GAAG,CAACiI,OAAO,GAAG0tB,kBAAkB,CAAC31B,GAAG,CAACiI,OAAO,EAAElH,KAAK,GAAG,CAAC,EAAEf,GAAG,CAACQ,EAAE,CAAC,CAAA;EAClE,WAAA;EAEA,UAAA,OAAOR,GAAG,CAAA;EACZ,SAAC,CAAC,CAAA;EACJ,OAAA;EAEA,MAAA,MAAMC,QAAgB,GAAGu1B,gBAAgB,CAACz0B,KAAK,CAAE,CAAA;;EAEjD;EACA,MAAA,MAAM60B,YAAY,GAAGC,OAAO,CAACrQ,IAAI,EAAEvlB,QAAQ,CAAC,CAAA;;EAE5C;EACA,MAAA,MAAM61B,qBAAqB,GAAGn5B,KAAK,CAAC4T,IAAI,CAACqlB,YAAY,CAACG,OAAO,EAAE,CAAC,CAACzyB,GAAG,CAClE,CAAArC,IAAA,EAA+BxC,KAAK,KAAK;EAAA,QAAA,IAAxC,CAACu3B,aAAa,EAAEC,WAAW,CAAC,GAAAh1B,IAAA,CAAA;EAC3B,QAAA,IAAIT,EAAE,GAAG,CAAA,EAAGP,QAAQ,CAAA,CAAA,EAAI+1B,aAAa,CAAE,CAAA,CAAA;UACvCx1B,EAAE,GAAG0H,QAAQ,GAAG,CAAA,EAAGA,QAAQ,CAAI1H,CAAAA,EAAAA,EAAE,CAAE,CAAA,GAAGA,EAAE,CAAA;;EAExC;UACA,MAAMyH,OAAO,GAAG0tB,kBAAkB,CAACM,WAAW,EAAEl1B,KAAK,GAAG,CAAC,EAAEP,EAAE,CAAC,CAAA;EAE9DyH,QAAAA,OAAO,CAAC5K,OAAO,CAACsuB,MAAM,IAAI;YACxBA,MAAM,CAACzjB,QAAQ,GAAG1H,EAAE,CAAA;EACtB,SAAC,CAAC,CAAA;;EAEF;EACA,QAAA,MAAMoP,QAAQ,GAAG7O,KAAK,GAClBhE,SAAS,CAACk5B,WAAW,EAAEj2B,GAAG,IAAIA,GAAG,CAACiI,OAAO,CAAC,GAC1CguB,WAAW,CAAA;UAEf,MAAMj2B,GAAG,GAAG8H,SAAS,CACnB/H,KAAK,EACLS,EAAE,EACFoP,QAAQ,CAAC,CAAC,CAAC,CAAE7H,QAAQ,EACrBtJ,KAAK,EACLsC,KAAK,EACLU,SAAS,EACTyG,QACF,CAAC,CAAA;EAEDiK,QAAAA,MAAM,CAACye,MAAM,CAAC5wB,GAAG,EAAE;EACjB8S,UAAAA,gBAAgB,EAAE7S,QAAQ;YAC1B+1B,aAAa;YACb/tB,OAAO;YACP2H,QAAQ;YACRvP,QAAQ,EAAGJ,QAAgB,IAAK;EAC9B;EACA,YAAA,IAAIu1B,gBAAgB,CAAC7zB,QAAQ,CAAC1B,QAAQ,CAAC,EAAE;gBACvC,IAAID,GAAG,CAACmI,YAAY,CAACE,cAAc,CAACpI,QAAQ,CAAC,EAAE;EAC7C,gBAAA,OAAOD,GAAG,CAACmI,YAAY,CAAClI,QAAQ,CAAC,CAAA;EACnC,eAAA;EAEA,cAAA,IAAIg2B,WAAW,CAAC,CAAC,CAAC,EAAE;EAAA,gBAAA,IAAAC,qBAAA,CAAA;kBAClBl2B,GAAG,CAACmI,YAAY,CAAClI,QAAQ,CAAC,GAAAi2B,CAAAA,qBAAA,GACxBD,WAAW,CAAC,CAAC,CAAC,CAAC51B,QAAQ,CAACJ,QAAQ,CAAC,KAAAi2B,IAAAA,GAAAA,qBAAA,GAAIz0B,SAAS,CAAA;EAClD,eAAA;EAEA,cAAA,OAAOzB,GAAG,CAACmI,YAAY,CAAClI,QAAQ,CAAC,CAAA;EACnC,aAAA;cAEA,IAAID,GAAG,CAAC+S,oBAAoB,CAAC1K,cAAc,CAACpI,QAAQ,CAAC,EAAE;EACrD,cAAA,OAAOD,GAAG,CAAC+S,oBAAoB,CAAC9S,QAAQ,CAAC,CAAA;EAC3C,aAAA;;EAEA;EACA,YAAA,MAAMxE,MAAM,GAAGsE,KAAK,CAACuI,SAAS,CAACrI,QAAQ,CAAC,CAAA;cACxC,MAAMk2B,WAAW,GAAG16B,MAAM,IAAA,IAAA,GAAA,KAAA,CAAA,GAANA,MAAM,CAAE4W,gBAAgB,EAAE,CAAA;EAE9C,YAAA,IAAI8jB,WAAW,EAAE;EACfn2B,cAAAA,GAAG,CAAC+S,oBAAoB,CAAC9S,QAAQ,CAAC,GAAGk2B,WAAW,CAC9Cl2B,QAAQ,EACR2P,QAAQ,EACRqmB,WACF,CAAC,CAAA;EAED,cAAA,OAAOj2B,GAAG,CAAC+S,oBAAoB,CAAC9S,QAAQ,CAAC,CAAA;EAC3C,aAAA;EACF,WAAA;EACF,SAAC,CAAC,CAAA;EAEFgI,QAAAA,OAAO,CAAC5K,OAAO,CAACsuB,MAAM,IAAI;EACxB8J,UAAAA,eAAe,CAACl4B,IAAI,CAACouB,MAAM,CAAC,CAAA;EAC5B+J,UAAAA,eAAe,CAAC/J,MAAM,CAACnrB,EAAE,CAAC,GAAGmrB,MAAM,CAAA;EACnC;EACA;EACA;EACA;EACA;EACA;EACA;EACF,SAAC,CAAC,CAAA;EAEF,QAAA,OAAO3rB,GAAG,CAAA;EACZ,OACF,CAAC,CAAA;EAED,MAAA,OAAO81B,qBAAqB,CAAA;OAC7B,CAAA;MAED,MAAMG,WAAW,GAAGN,kBAAkB,CAACtM,QAAQ,CAAC7D,IAAI,EAAE,CAAC,CAAC,CAAA;EAExDyQ,IAAAA,WAAW,CAAC54B,OAAO,CAACsuB,MAAM,IAAI;EAC5B8J,MAAAA,eAAe,CAACl4B,IAAI,CAACouB,MAAM,CAAC,CAAA;EAC5B+J,MAAAA,eAAe,CAAC/J,MAAM,CAACnrB,EAAE,CAAC,GAAGmrB,MAAM,CAAA;EACnC;EACA;EACA;EACA;EACA;EACA;EACA;EACF,KAAC,CAAC,CAAA;MAEF,OAAO;EACLnG,MAAAA,IAAI,EAAEyQ,WAAW;EACjBnpB,MAAAA,QAAQ,EAAE2oB,eAAe;EACzBnU,MAAAA,QAAQ,EAAEoU,eAAAA;OACX,CAAA;KACF,EACDj2B,cAAc,CAACM,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,oBAAoB,EAAE,MAAM;MACtEP,KAAK,CAACogB,MAAM,CAAC,MAAM;QACjBpgB,KAAK,CAACkgB,kBAAkB,EAAE,CAAA;QAC1BlgB,KAAK,CAACmjB,mBAAmB,EAAE,CAAA;EAC7B,KAAC,CAAC,CAAA;EACJ,GAAC,CACH,CAAC,CAAA;EACL,CAAA;EAEA,SAAS2S,OAAOA,CAAwBrQ,IAAkB,EAAEvlB,QAAgB,EAAE;EAC5E,EAAA,MAAMm2B,QAAQ,GAAG,IAAIvsB,GAAG,EAAqB,CAAA;IAE7C,OAAO2b,IAAI,CAACpc,MAAM,CAAC,CAAC9F,GAAG,EAAEtD,GAAG,KAAK;MAC/B,MAAMq2B,MAAM,GAAG,CAAGr2B,EAAAA,GAAG,CAAC0R,gBAAgB,CAACzR,QAAQ,CAAC,CAAE,CAAA,CAAA;EAClD,IAAA,MAAMq2B,QAAQ,GAAGhzB,GAAG,CAACwxB,GAAG,CAACuB,MAAM,CAAC,CAAA;MAChC,IAAI,CAACC,QAAQ,EAAE;QACbhzB,GAAG,CAACuxB,GAAG,CAACwB,MAAM,EAAE,CAACr2B,GAAG,CAAC,CAAC,CAAA;EACxB,KAAC,MAAM;EACLs2B,MAAAA,QAAQ,CAAC/4B,IAAI,CAACyC,GAAG,CAAC,CAAA;EACpB,KAAA;EACA,IAAA,OAAOsD,GAAG,CAAA;KACX,EAAE8yB,QAAQ,CAAC,CAAA;EACd;;ECzLO,SAASnR,qBAAqBA,CAAwBpnB,IAE5D,EAAkD;EACjD,EAAA,OAAOkC,KAAK,IACVrC,IAAI,CACF,MAAM,CACJqC,KAAK,CAAC6D,QAAQ,EAAE,CAACof,UAAU,EAC3BjjB,KAAK,CAAC8gB,wBAAwB,EAAE,EAChC9gB,KAAK,CAACO,OAAO,CAACwf,oBAAoB,GAC9Bre,SAAS,GACT1B,KAAK,CAAC6D,QAAQ,EAAE,CAACgc,QAAQ,CAC9B,EACD,CAACoD,UAAU,EAAEqG,QAAQ,KAAK;EACxB,IAAA,IAAI,CAACA,QAAQ,CAAC7D,IAAI,CAAC/nB,MAAM,EAAE;EACzB,MAAA,OAAO4rB,QAAQ,CAAA;EACjB,KAAA;MAEA,MAAM;QAAEvG,QAAQ;EAAED,MAAAA,SAAAA;EAAU,KAAC,GAAGG,UAAU,CAAA;MAC1C,IAAI;QAAEwC,IAAI;QAAE1Y,QAAQ;EAAEwU,MAAAA,QAAAA;EAAS,KAAC,GAAG+H,QAAQ,CAAA;EAC3C,IAAA,MAAMkN,SAAS,GAAGzT,QAAQ,GAAGD,SAAS,CAAA;EACtC,IAAA,MAAM2T,OAAO,GAAGD,SAAS,GAAGzT,QAAQ,CAAA;MAEpC0C,IAAI,GAAGA,IAAI,CAAChN,KAAK,CAAC+d,SAAS,EAAEC,OAAO,CAAC,CAAA;EAErC,IAAA,IAAIC,iBAAkC,CAAA;EAEtC,IAAA,IAAI,CAAC12B,KAAK,CAACO,OAAO,CAACwf,oBAAoB,EAAE;QACvC2W,iBAAiB,GAAGzD,UAAU,CAAC;UAC7BxN,IAAI;UACJ1Y,QAAQ;EACRwU,QAAAA,QAAAA;EACF,OAAC,CAAC,CAAA;EACJ,KAAC,MAAM;EACLmV,MAAAA,iBAAiB,GAAG;UAClBjR,IAAI;UACJ1Y,QAAQ;EACRwU,QAAAA,QAAAA;SACD,CAAA;EACH,KAAA;MAEAmV,iBAAiB,CAAC3pB,QAAQ,GAAG,EAAE,CAAA;MAE/B,MAAMomB,SAAS,GAAIlzB,GAAe,IAAK;EACrCy2B,MAAAA,iBAAiB,CAAC3pB,QAAQ,CAACvP,IAAI,CAACyC,GAAG,CAAC,CAAA;EACpC,MAAA,IAAIA,GAAG,CAACiI,OAAO,CAACxK,MAAM,EAAE;EACtBuC,QAAAA,GAAG,CAACiI,OAAO,CAAC5K,OAAO,CAAC61B,SAAS,CAAC,CAAA;EAChC,OAAA;OACD,CAAA;EAEDuD,IAAAA,iBAAiB,CAACjR,IAAI,CAACnoB,OAAO,CAAC61B,SAAS,CAAC,CAAA;EAEzC,IAAA,OAAOuD,iBAAiB,CAAA;KACzB,EACDh3B,cAAc,CAACM,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,uBAAuB,CACrE,CAAC,CAAA;EACL;;ECvDO,SAASmhB,iBAAiBA,GAEN;IACzB,OAAO1hB,KAAK,IACVrC,IAAI,CACF,MAAM,CAACqC,KAAK,CAAC6D,QAAQ,EAAE,CAACspB,OAAO,EAAEntB,KAAK,CAACqwB,oBAAoB,EAAE,CAAC,EAC9D,CAAClD,OAAO,EAAE7D,QAAQ,KAAK;EACrB,IAAA,IAAI,CAACA,QAAQ,CAAC7D,IAAI,CAAC/nB,MAAM,IAAI,EAACyvB,OAAO,IAAA,IAAA,IAAPA,OAAO,CAAEzvB,MAAM,CAAE,EAAA;EAC7C,MAAA,OAAO4rB,QAAQ,CAAA;EACjB,KAAA;MAEA,MAAMqN,YAAY,GAAG32B,KAAK,CAAC6D,QAAQ,EAAE,CAACspB,OAAO,CAAA;MAE7C,MAAMyJ,cAA4B,GAAG,EAAE,CAAA;;EAEvC;EACA,IAAA,MAAMC,gBAAgB,GAAGF,YAAY,CAACryB,MAAM,CAAC8L,IAAI,IAAA;EAAA,MAAA,IAAAgjB,gBAAA,CAAA;EAAA,MAAA,OAAA,CAAAA,gBAAA,GAC/CpzB,KAAK,CAACuI,SAAS,CAAC6H,IAAI,CAAC3P,EAAE,CAAC,KAAxB2yB,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,gBAAA,CAA0B7D,UAAU,EAAE,CAAA;EAAA,KACxC,CAAC,CAAA;MAED,MAAMuH,cAOL,GAAG,EAAE,CAAA;EAEND,IAAAA,gBAAgB,CAACv5B,OAAO,CAACy5B,SAAS,IAAI;QACpC,MAAMr7B,MAAM,GAAGsE,KAAK,CAACuI,SAAS,CAACwuB,SAAS,CAACt2B,EAAE,CAAC,CAAA;QAC5C,IAAI,CAAC/E,MAAM,EAAE,OAAA;EAEbo7B,MAAAA,cAAc,CAACC,SAAS,CAACt2B,EAAE,CAAC,GAAG;EAC7B4sB,QAAAA,aAAa,EAAE3xB,MAAM,CAACqF,SAAS,CAACssB,aAAa;EAC7C2J,QAAAA,aAAa,EAAEt7B,MAAM,CAACqF,SAAS,CAACi2B,aAAa;EAC7C5J,QAAAA,SAAS,EAAE1xB,MAAM,CAACmyB,YAAY,EAAC;SAChC,CAAA;EACH,KAAC,CAAC,CAAA;MAEF,MAAMoJ,QAAQ,GAAIxR,IAAkB,IAAK;EACvC;EACA;EACA,MAAA,MAAMyR,UAAU,GAAGzR,IAAI,CAACliB,GAAG,CAACtD,GAAG,KAAK;UAAE,GAAGA,GAAAA;EAAI,OAAC,CAAC,CAAC,CAAA;EAEhDi3B,MAAAA,UAAU,CAAC9mB,IAAI,CAAC,CAAC4b,IAAI,EAAEC,IAAI,KAAK;EAC9B,QAAA,KAAK,IAAI1iB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGstB,gBAAgB,CAACn5B,MAAM,EAAE6L,CAAC,IAAI,CAAC,EAAE;EAAA,UAAA,IAAA4tB,eAAA,CAAA;EACnD,UAAA,MAAMJ,SAAS,GAAGF,gBAAgB,CAACttB,CAAC,CAAE,CAAA;EACtC,UAAA,MAAM6tB,UAAU,GAAGN,cAAc,CAACC,SAAS,CAACt2B,EAAE,CAAE,CAAA;EAChD,UAAA,MAAM4sB,aAAa,GAAG+J,UAAU,CAAC/J,aAAa,CAAA;EAC9C,UAAA,MAAMgK,MAAM,GAAA,CAAAF,eAAA,GAAGJ,SAAS,IAAA,IAAA,GAAA,KAAA,CAAA,GAATA,SAAS,CAAE9I,IAAI,KAAA,IAAA,GAAAkJ,eAAA,GAAI,KAAK,CAAA;YAEvC,IAAIG,OAAO,GAAG,CAAC,CAAA;;EAEf;EACA,UAAA,IAAIjK,aAAa,EAAE;cACjB,MAAMkK,MAAM,GAAGvL,IAAI,CAAC1rB,QAAQ,CAACy2B,SAAS,CAACt2B,EAAE,CAAC,CAAA;cAC1C,MAAM+2B,MAAM,GAAGvL,IAAI,CAAC3rB,QAAQ,CAACy2B,SAAS,CAACt2B,EAAE,CAAC,CAAA;EAE1C,YAAA,MAAMg3B,UAAU,GAAGF,MAAM,KAAK71B,SAAS,CAAA;EACvC,YAAA,MAAMg2B,UAAU,GAAGF,MAAM,KAAK91B,SAAS,CAAA;cAEvC,IAAI+1B,UAAU,IAAIC,UAAU,EAAE;gBAC5B,IAAIrK,aAAa,KAAK,OAAO,EAAE,OAAOoK,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;gBACzD,IAAIpK,aAAa,KAAK,MAAM,EAAE,OAAOoK,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;EACxDH,cAAAA,OAAO,GACLG,UAAU,IAAIC,UAAU,GACpB,CAAC,GACDD,UAAU,GACRpK,aAAa,GACb,CAACA,aAAa,CAAA;EACxB,aAAA;EACF,WAAA;YAEA,IAAIiK,OAAO,KAAK,CAAC,EAAE;EACjBA,YAAAA,OAAO,GAAGF,UAAU,CAAChK,SAAS,CAACpB,IAAI,EAAEC,IAAI,EAAE8K,SAAS,CAACt2B,EAAE,CAAC,CAAA;EAC1D,WAAA;;EAEA;YACA,IAAI62B,OAAO,KAAK,CAAC,EAAE;EACjB,YAAA,IAAID,MAAM,EAAE;gBACVC,OAAO,IAAI,CAAC,CAAC,CAAA;EACf,aAAA;cAEA,IAAIF,UAAU,CAACJ,aAAa,EAAE;gBAC5BM,OAAO,IAAI,CAAC,CAAC,CAAA;EACf,aAAA;EAEA,YAAA,OAAOA,OAAO,CAAA;EAChB,WAAA;EACF,SAAA;EAEA,QAAA,OAAOtL,IAAI,CAACttB,KAAK,GAAGutB,IAAI,CAACvtB,KAAK,CAAA;EAChC,OAAC,CAAC,CAAA;;EAEF;EACAw4B,MAAAA,UAAU,CAAC55B,OAAO,CAAC2C,GAAG,IAAI;EAAA,QAAA,IAAAkT,YAAA,CAAA;EACxByjB,QAAAA,cAAc,CAACp5B,IAAI,CAACyC,GAAG,CAAC,CAAA;UACxB,IAAAkT,CAAAA,YAAA,GAAIlT,GAAG,CAACiI,OAAO,KAAXiL,IAAAA,IAAAA,YAAA,CAAazV,MAAM,EAAE;YACvBuC,GAAG,CAACiI,OAAO,GAAG+uB,QAAQ,CAACh3B,GAAG,CAACiI,OAAO,CAAC,CAAA;EACrC,SAAA;EACF,OAAC,CAAC,CAAA;EAEF,MAAA,OAAOgvB,UAAU,CAAA;OAClB,CAAA;MAED,OAAO;EACLzR,MAAAA,IAAI,EAAEwR,QAAQ,CAAC3N,QAAQ,CAAC7D,IAAI,CAAC;EAC7B1Y,MAAAA,QAAQ,EAAE6pB,cAAc;QACxBrV,QAAQ,EAAE+H,QAAQ,CAAC/H,QAAAA;OACpB,CAAA;EACH,GAAC,EACD7hB,cAAc,CAACM,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,mBAAmB,EAAE,MAC/DP,KAAK,CAACmjB,mBAAmB,EAC3B,CACF,CAAC,CAAA;EACL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}