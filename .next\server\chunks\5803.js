"use strict";exports.id=5803,exports.ids=[5803],exports.modules={16802:(e,t,a)=>{a.d(t,{$N:()=>m,Be:()=>x,Vq:()=>i,cN:()=>g,cZ:()=>f,fK:()=>p,hg:()=>d,t9:()=>u});var o=a(95344),r=a(3729),n=a(88794),s=a(14513),l=a(91626);let i=n.fC,d=n.xz,c=n.h_;n.x8;let u=r.forwardRef(({className:e,...t},a)=>o.jsx(n.aV,{ref:a,className:(0,l.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));u.displayName=n.aV.displayName;let f=r.forwardRef(({className:e,children:t,...a},r)=>(0,o.jsxs)(c,{children:[o.jsx(u,{}),(0,o.jsxs)(n.VY,{ref:r,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...a,children:[t,(0,o.jsxs)(n.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[o.jsx(s.Z,{className:"h-4 w-4"}),o.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));f.displayName=n.VY.displayName;let p=({className:e,...t})=>o.jsx("div",{className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});p.displayName="DialogHeader";let g=({className:e,...t})=>o.jsx("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});g.displayName="DialogFooter";let m=r.forwardRef(({className:e,...t},a)=>o.jsx(n.Dx,{ref:a,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));m.displayName=n.Dx.displayName;let x=r.forwardRef(({className:e,...t},a)=>o.jsx(n.dk,{ref:a,className:(0,l.cn)("text-sm text-muted-foreground",e),...t}));x.displayName=n.dk.displayName},92549:(e,t,a)=>{a.d(t,{I:()=>s});var o=a(95344),r=a(3729),n=a(91626);let s=r.forwardRef(({className:e,type:t,...a},r)=>o.jsx("input",{type:t,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...a}));s.displayName="Input"},1586:(e,t,a)=>{a.d(t,{_:()=>d});var o=a(95344),r=a(3729),n=a(14217),s=a(49247),l=a(91626);let i=(0,s.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef(({className:e,...t},a)=>o.jsx(n.f,{ref:a,className:(0,l.cn)(i(),e),...t}));d.displayName=n.f.displayName},63024:(e,t,a)=>{a.d(t,{Z:()=>o});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,a(69224).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},55794:(e,t,a)=>{a.d(t,{Z:()=>o});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,a(69224).Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},25545:(e,t,a)=>{a.d(t,{Z:()=>o});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,a(69224).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},75695:(e,t,a)=>{a.d(t,{Z:()=>o});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,a(69224).Z)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},36341:(e,t,a)=>{a.d(t,{Z:()=>o});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,a(69224).Z)("Tag",[["path",{d:"M12 2H2v10l9.29 9.29c.94.94 2.48.94 3.42 0l6.58-6.58c.94-.94.94-2.48 0-3.42L12 2Z",key:"14b2ls"}],["path",{d:"M7 7h.01",key:"7u93v4"}]])},38271:(e,t,a)=>{a.d(t,{Z:()=>o});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,a(69224).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},88794:(e,t,a)=>{a.d(t,{Dx:()=>eo,VY:()=>ea,aV:()=>et,dk:()=>er,fC:()=>J,h_:()=>ee,x8:()=>en,xz:()=>Q});var o=a(3729),r=a(85222),n=a(31405),s=a(98462),l=a(99048),i=a(33183),d=a(44155),c=a(27386),u=a(31179),f=a(43234),p=a(62409),g=a(1106),m=a(71210),x=a(45904),y=a(32751),h=a(95344),b="Dialog",[v,j]=(0,s.b)(b),[N,k]=v(b),w=e=>{let{__scopeDialog:t,children:a,open:r,defaultOpen:n,onOpenChange:s,modal:d=!0}=e,c=o.useRef(null),u=o.useRef(null),[f,p]=(0,i.T)({prop:r,defaultProp:n??!1,onChange:s,caller:b});return(0,h.jsx)(N,{scope:t,triggerRef:c,contentRef:u,contentId:(0,l.M)(),titleId:(0,l.M)(),descriptionId:(0,l.M)(),open:f,onOpenChange:p,onOpenToggle:o.useCallback(()=>p(e=>!e),[p]),modal:d,children:a})};w.displayName=b;var D="DialogTrigger",R=o.forwardRef((e,t)=>{let{__scopeDialog:a,...o}=e,s=k(D,a),l=(0,n.e)(t,s.triggerRef);return(0,h.jsx)(p.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":s.open,"aria-controls":s.contentId,"data-state":L(s.open),...o,ref:l,onClick:(0,r.M)(e.onClick,s.onOpenToggle)})});R.displayName=D;var C="DialogPortal",[M,I]=v(C,{forceMount:void 0}),Z=e=>{let{__scopeDialog:t,forceMount:a,children:r,container:n}=e,s=k(C,t);return(0,h.jsx)(M,{scope:t,forceMount:a,children:o.Children.map(r,e=>(0,h.jsx)(f.z,{present:a||s.open,children:(0,h.jsx)(u.h,{asChild:!0,container:n,children:e})}))})};Z.displayName=C;var O="DialogOverlay",V=o.forwardRef((e,t)=>{let a=I(O,e.__scopeDialog),{forceMount:o=a.forceMount,...r}=e,n=k(O,e.__scopeDialog);return n.modal?(0,h.jsx)(f.z,{present:o||n.open,children:(0,h.jsx)(F,{...r,ref:t})}):null});V.displayName=O;var _=(0,y.Z8)("DialogOverlay.RemoveScroll"),F=o.forwardRef((e,t)=>{let{__scopeDialog:a,...o}=e,r=k(O,a);return(0,h.jsx)(m.Z,{as:_,allowPinchZoom:!0,shards:[r.contentRef],children:(0,h.jsx)(p.WV.div,{"data-state":L(r.open),...o,ref:t,style:{pointerEvents:"auto",...o.style}})})}),E="DialogContent",z=o.forwardRef((e,t)=>{let a=I(E,e.__scopeDialog),{forceMount:o=a.forceMount,...r}=e,n=k(E,e.__scopeDialog);return(0,h.jsx)(f.z,{present:o||n.open,children:n.modal?(0,h.jsx)(P,{...r,ref:t}):(0,h.jsx)(W,{...r,ref:t})})});z.displayName=E;var P=o.forwardRef((e,t)=>{let a=k(E,e.__scopeDialog),s=o.useRef(null),l=(0,n.e)(t,a.contentRef,s);return o.useEffect(()=>{let e=s.current;if(e)return(0,x.Ry)(e)},[]),(0,h.jsx)(A,{...e,ref:l,trapFocus:a.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,r.M)(e.onCloseAutoFocus,e=>{e.preventDefault(),a.triggerRef.current?.focus()}),onPointerDownOutside:(0,r.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,a=0===t.button&&!0===t.ctrlKey;(2===t.button||a)&&e.preventDefault()}),onFocusOutside:(0,r.M)(e.onFocusOutside,e=>e.preventDefault())})}),W=o.forwardRef((e,t)=>{let a=k(E,e.__scopeDialog),r=o.useRef(!1),n=o.useRef(!1);return(0,h.jsx)(A,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(r.current||a.triggerRef.current?.focus(),t.preventDefault()),r.current=!1,n.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(r.current=!0,"pointerdown"!==t.detail.originalEvent.type||(n.current=!0));let o=t.target;a.triggerRef.current?.contains(o)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&n.current&&t.preventDefault()}})}),A=o.forwardRef((e,t)=>{let{__scopeDialog:a,trapFocus:r,onOpenAutoFocus:s,onCloseAutoFocus:l,...i}=e,u=k(E,a),f=o.useRef(null),p=(0,n.e)(t,f);return(0,g.EW)(),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(c.M,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:s,onUnmountAutoFocus:l,children:(0,h.jsx)(d.XB,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":L(u.open),...i,ref:p,onDismiss:()=>u.onOpenChange(!1)})}),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(X,{titleId:u.titleId}),(0,h.jsx)(G,{contentRef:f,descriptionId:u.descriptionId})]})]})}),T="DialogTitle",H=o.forwardRef((e,t)=>{let{__scopeDialog:a,...o}=e,r=k(T,a);return(0,h.jsx)(p.WV.h2,{id:r.titleId,...o,ref:t})});H.displayName=T;var $="DialogDescription",q=o.forwardRef((e,t)=>{let{__scopeDialog:a,...o}=e,r=k($,a);return(0,h.jsx)(p.WV.p,{id:r.descriptionId,...o,ref:t})});q.displayName=$;var B="DialogClose",S=o.forwardRef((e,t)=>{let{__scopeDialog:a,...o}=e,n=k(B,a);return(0,h.jsx)(p.WV.button,{type:"button",...o,ref:t,onClick:(0,r.M)(e.onClick,()=>n.onOpenChange(!1))})});function L(e){return e?"open":"closed"}S.displayName=B;var Y="DialogTitleWarning",[K,U]=(0,s.k)(Y,{contentName:E,titleName:T,docsSlug:"dialog"}),X=({titleId:e})=>{let t=U(Y),a=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return o.useEffect(()=>{e&&!document.getElementById(e)&&console.error(a)},[a,e]),null},G=({contentRef:e,descriptionId:t})=>{let a=U("DialogDescriptionWarning"),r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${a.contentName}}.`;return o.useEffect(()=>{let a=e.current?.getAttribute("aria-describedby");t&&a&&!document.getElementById(t)&&console.warn(r)},[r,e,t]),null},J=w,Q=R,ee=Z,et=V,ea=z,eo=H,er=q,en=S}};