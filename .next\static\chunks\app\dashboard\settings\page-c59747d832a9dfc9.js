(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3455],{3021:function(e,s,r){"use strict";r.d(s,{Z:function(){return n}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(62898).Z)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},98253:function(e,s,r){"use strict";r.d(s,{Z:function(){return n}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(62898).Z)("Building2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},71738:function(e,s,r){"use strict";r.d(s,{Z:function(){return n}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(62898).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},49036:function(e,s,r){"use strict";r.d(s,{Z:function(){return n}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(62898).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},67972:function(e,s,r){"use strict";r.d(s,{Z:function(){return n}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(62898).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},62889:function(e,s,r){Promise.resolve().then(r.bind(r,38372))},38372:function(e,s,r){"use strict";r.r(s),r.d(s,{default:function(){return f}});var n=r(57437),t=r(27815),i=r(85754),a=r(45179),l=r(49842),c=r(31478),d=r(67972),o=r(98253),u=r(3021),x=r(49036),h=r(71738);function f(){return(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Settings"}),(0,n.jsx)("p",{className:"text-gray-600 mt-1",children:"Manage your account and application preferences"})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,n.jsx)("div",{className:"lg:col-span-1",children:(0,n.jsxs)(t.Zb,{children:[(0,n.jsx)(t.Ol,{children:(0,n.jsx)(t.ll,{children:"Settings"})}),(0,n.jsx)(t.aY,{children:(0,n.jsxs)("nav",{className:"space-y-2",children:[(0,n.jsxs)("a",{href:"#profile",className:"flex items-center space-x-3 px-3 py-2 rounded-md bg-blue-50 text-blue-700",children:[(0,n.jsx)(d.Z,{className:"h-4 w-4"}),(0,n.jsx)("span",{children:"Profile"})]}),(0,n.jsxs)("a",{href:"#company",className:"flex items-center space-x-3 px-3 py-2 rounded-md hover:bg-gray-50",children:[(0,n.jsx)(o.Z,{className:"h-4 w-4"}),(0,n.jsx)("span",{children:"Company"})]}),(0,n.jsxs)("a",{href:"#notifications",className:"flex items-center space-x-3 px-3 py-2 rounded-md hover:bg-gray-50",children:[(0,n.jsx)(u.Z,{className:"h-4 w-4"}),(0,n.jsx)("span",{children:"Notifications"})]}),(0,n.jsxs)("a",{href:"#security",className:"flex items-center space-x-3 px-3 py-2 rounded-md hover:bg-gray-50",children:[(0,n.jsx)(x.Z,{className:"h-4 w-4"}),(0,n.jsx)("span",{children:"Security"})]}),(0,n.jsxs)("a",{href:"#billing",className:"flex items-center space-x-3 px-3 py-2 rounded-md hover:bg-gray-50",children:[(0,n.jsx)(h.Z,{className:"h-4 w-4"}),(0,n.jsx)("span",{children:"Billing"})]})]})})]})}),(0,n.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,n.jsxs)(t.Zb,{id:"profile",children:[(0,n.jsx)(t.Ol,{children:(0,n.jsxs)(t.ll,{className:"flex items-center space-x-2",children:[(0,n.jsx)(d.Z,{className:"h-5 w-5"}),(0,n.jsx)("span",{children:"Profile Settings"})]})}),(0,n.jsxs)(t.aY,{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)(l._,{htmlFor:"firstName",children:"First Name"}),(0,n.jsx)(a.I,{id:"firstName",placeholder:"John"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(l._,{htmlFor:"lastName",children:"Last Name"}),(0,n.jsx)(a.I,{id:"lastName",placeholder:"Doe"})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(l._,{htmlFor:"email",children:"Email"}),(0,n.jsx)(a.I,{id:"email",type:"email",placeholder:"<EMAIL>"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(l._,{htmlFor:"phone",children:"Phone"}),(0,n.jsx)(a.I,{id:"phone",placeholder:"+****************"})]}),(0,n.jsx)(i.z,{children:"Save Changes"})]})]}),(0,n.jsxs)(t.Zb,{id:"company",children:[(0,n.jsx)(t.Ol,{children:(0,n.jsxs)(t.ll,{className:"flex items-center space-x-2",children:[(0,n.jsx)(o.Z,{className:"h-5 w-5"}),(0,n.jsx)("span",{children:"Company Settings"})]})}),(0,n.jsxs)(t.aY,{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)(l._,{htmlFor:"companyName",children:"Company Name"}),(0,n.jsx)(a.I,{id:"companyName",placeholder:"Acme Corporation"})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)(l._,{htmlFor:"industry",children:"Industry"}),(0,n.jsx)(a.I,{id:"industry",placeholder:"Technology"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(l._,{htmlFor:"companySize",children:"Company Size"}),(0,n.jsxs)("select",{className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,n.jsx)("option",{children:"1-10 employees"}),(0,n.jsx)("option",{children:"11-50 employees"}),(0,n.jsx)("option",{children:"51-200 employees"}),(0,n.jsx)("option",{children:"200+ employees"})]})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(l._,{htmlFor:"website",children:"Website"}),(0,n.jsx)(a.I,{id:"website",placeholder:"https://example.com"})]}),(0,n.jsx)(i.z,{children:"Update Company"})]})]}),(0,n.jsxs)(t.Zb,{id:"notifications",children:[(0,n.jsx)(t.Ol,{children:(0,n.jsxs)(t.ll,{className:"flex items-center space-x-2",children:[(0,n.jsx)(u.Z,{className:"h-5 w-5"}),(0,n.jsx)("span",{children:"Notification Preferences"})]})}),(0,n.jsxs)(t.aY,{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-medium",children:"Email Notifications"}),(0,n.jsx)("p",{className:"text-sm text-gray-500",children:"Receive notifications via email"})]}),(0,n.jsx)("input",{type:"checkbox",className:"toggle",defaultChecked:!0})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-medium",children:"New Customer Alerts"}),(0,n.jsx)("p",{className:"text-sm text-gray-500",children:"Get notified when new customers sign up"})]}),(0,n.jsx)("input",{type:"checkbox",className:"toggle",defaultChecked:!0})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-medium",children:"Payment Notifications"}),(0,n.jsx)("p",{className:"text-sm text-gray-500",children:"Receive alerts for payments and invoices"})]}),(0,n.jsx)("input",{type:"checkbox",className:"toggle",defaultChecked:!0})]}),(0,n.jsx)(i.z,{children:"Save Preferences"})]})]}),(0,n.jsxs)(t.Zb,{id:"security",children:[(0,n.jsx)(t.Ol,{children:(0,n.jsxs)(t.ll,{className:"flex items-center space-x-2",children:[(0,n.jsx)(x.Z,{className:"h-5 w-5"}),(0,n.jsx)("span",{children:"Security Settings"})]})}),(0,n.jsxs)(t.aY,{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)(l._,{htmlFor:"currentPassword",children:"Current Password"}),(0,n.jsx)(a.I,{id:"currentPassword",type:"password"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(l._,{htmlFor:"newPassword",children:"New Password"}),(0,n.jsx)(a.I,{id:"newPassword",type:"password"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(l._,{htmlFor:"confirmPassword",children:"Confirm New Password"}),(0,n.jsx)(a.I,{id:"confirmPassword",type:"password"})]}),(0,n.jsx)(i.z,{children:"Change Password"})]})]}),(0,n.jsxs)(t.Zb,{id:"billing",children:[(0,n.jsx)(t.Ol,{children:(0,n.jsxs)(t.ll,{className:"flex items-center space-x-2",children:[(0,n.jsx)(h.Z,{className:"h-5 w-5"}),(0,n.jsx)("span",{children:"Billing & Subscription"})]})}),(0,n.jsxs)(t.aY,{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-medium",children:"Current Plan"}),(0,n.jsx)("p",{className:"text-sm text-gray-500",children:"Professional Plan"})]}),(0,n.jsx)(c.C,{variant:"success",children:"Active"})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-medium",children:"Next Billing Date"}),(0,n.jsx)("p",{className:"text-sm text-gray-500",children:"February 15, 2024"})]}),(0,n.jsx)("span",{className:"font-medium",children:"$49/month"})]}),(0,n.jsxs)("div",{className:"flex space-x-2",children:[(0,n.jsx)(i.z,{variant:"outline",children:"Change Plan"}),(0,n.jsx)(i.z,{variant:"outline",children:"Update Payment Method"})]})]})]})]})]})]})}},31478:function(e,s,r){"use strict";r.d(s,{C:function(){return l}});var n=r(57437);r(2265);var t=r(96061),i=r(1657);let a=(0,t.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600",info:"border-transparent bg-blue-500 text-white hover:bg-blue-600"}},defaultVariants:{variant:"default"}});function l(e){let{className:s,variant:r,...t}=e;return(0,n.jsx)("div",{className:(0,i.cn)(a({variant:r}),s),...t})}},85754:function(e,s,r){"use strict";r.d(s,{z:function(){return d}});var n=r(57437),t=r(2265),i=r(67256),a=r(96061),l=r(1657);let c=(0,a.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=t.forwardRef((e,s)=>{let{className:r,variant:t,size:a,asChild:d=!1,...o}=e,u=d?i.g7:"button";return(0,n.jsx)(u,{className:(0,l.cn)(c({variant:t,size:a,className:r})),ref:s,...o})});d.displayName="Button"},27815:function(e,s,r){"use strict";r.d(s,{Ol:function(){return l},SZ:function(){return d},Zb:function(){return a},aY:function(){return o},eW:function(){return u},ll:function(){return c}});var n=r(57437),t=r(2265),i=r(1657);let a=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,n.jsx)("div",{ref:s,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...t})});a.displayName="Card";let l=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,n.jsx)("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",r),...t})});l.displayName="CardHeader";let c=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,n.jsx)("h3",{ref:s,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",r),...t})});c.displayName="CardTitle";let d=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,n.jsx)("p",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",r),...t})});d.displayName="CardDescription";let o=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,n.jsx)("div",{ref:s,className:(0,i.cn)("p-6 pt-0",r),...t})});o.displayName="CardContent";let u=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,n.jsx)("div",{ref:s,className:(0,i.cn)("flex items-center p-6 pt-0",r),...t})});u.displayName="CardFooter"},45179:function(e,s,r){"use strict";r.d(s,{I:function(){return a}});var n=r(57437),t=r(2265),i=r(1657);let a=t.forwardRef((e,s)=>{let{className:r,type:t,...a}=e;return(0,n.jsx)("input",{type:t,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:s,...a})});a.displayName="Input"},49842:function(e,s,r){"use strict";r.d(s,{_:function(){return d}});var n=r(57437),t=r(2265),i=r(36743),a=r(96061),l=r(1657);let c=(0,a.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,n.jsx)(i.f,{ref:s,className:(0,l.cn)(c(),r),...t})});d.displayName=i.f.displayName},1657:function(e,s,r){"use strict";r.d(s,{cn:function(){return i}});var n=r(57042),t=r(74769);function i(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return(0,t.m6)((0,n.W)(s))}},36743:function(e,s,r){"use strict";r.d(s,{f:function(){return l}});var n=r(2265),t=r(9381),i=r(57437),a=n.forwardRef((e,s)=>(0,i.jsx)(t.WV.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));a.displayName="Label";var l=a},9381:function(e,s,r){"use strict";r.d(s,{WV:function(){return l},jH:function(){return c}});var n=r(2265),t=r(54887),i=r(67256),a=r(57437),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,s)=>{let r=(0,i.Z8)(`Primitive.${s}`),t=n.forwardRef((e,n)=>{let{asChild:t,...i}=e,l=t?r:s;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(l,{...i,ref:n})});return t.displayName=`Primitive.${s}`,{...e,[s]:t}},{});function c(e,s){e&&t.flushSync(()=>e.dispatchEvent(s))}}},function(e){e.O(0,[6723,2971,4938,1744],function(){return e(e.s=62889)}),_N_E=e.O()}]);