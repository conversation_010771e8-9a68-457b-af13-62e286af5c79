(()=>{var e={};e.id=923,e.ids=[923],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},17033:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>c.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>d});var a=t(50482),r=t(69108),l=t(62563),c=t.n(l),i=t(68300),n={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);t.d(s,n);let d=["",{children:["dashboard",{children:["customers",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,5343)),"C:\\proj\\nextjs-saas\\app\\dashboard\\customers\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,96339)),"C:\\proj\\nextjs-saas\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,59504)),"C:\\proj\\nextjs-saas\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\proj\\nextjs-saas\\app\\dashboard\\customers\\page.tsx"],x="/dashboard/customers/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/customers/page",pathname:"/dashboard/customers",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},4713:(e,s,t)=>{Promise.resolve().then(t.bind(t,60113))},60113:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>M});var a=t(95344),r=t(3729),l=t(47674),c=t(61351),i=t(16212),n=t(69436),d=t(10763),o=t(20298),x=t(17470),m=t(50340),h=t(33733),u=t(89895),p=t(48411),j=t(46064),f=t(15366),y=t(88534),g=t(76755),N=t(17910),v=t(44669);function b(){let[e,s]=(0,r.useState)(null),[t,l]=(0,r.useState)(!0),[d,o]=(0,r.useState)("30"),b=async()=>{try{l(!0);let e=await fetch(`/api/customers/analytics?period=${d}`);if(!e.ok)throw Error("Failed to fetch analytics");let t=await e.json();s(t)}catch(e){v.toast.error("Failed to load customer analytics"),console.error("Error fetching analytics:",e)}finally{l(!1)}};(0,r.useEffect)(()=>{b()},[d]);let w=e=>{switch(e){case"ACTIVE":return"bg-green-100 text-green-800";case"INACTIVE":default:return"bg-gray-100 text-gray-800";case"PROSPECT":return"bg-blue-100 text-blue-800"}},C=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e);return t?a.jsx("div",{className:"flex items-center justify-center py-8",children:a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):e?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("h3",{className:"text-lg font-semibold",children:"Customer Analytics"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(x.Ph,{value:d,onValueChange:o,children:[a.jsx(x.i4,{className:"w-32",children:a.jsx(x.ki,{})}),(0,a.jsxs)(x.Bw,{children:[a.jsx(x.Ql,{value:"7",children:"Last 7 days"}),a.jsx(x.Ql,{value:"30",children:"Last 30 days"}),a.jsx(x.Ql,{value:"90",children:"Last 90 days"}),a.jsx(x.Ql,{value:"365",children:"Last year"})]})]}),(0,a.jsxs)(i.z,{variant:"outline",onClick:b,size:"sm",children:[a.jsx(h.Z,{className:"h-4 w-4 mr-2"}),"Refresh"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4",children:[a.jsx(c.Zb,{children:a.jsx(c.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-blue-100 rounded-full",children:a.jsx(u.Z,{className:"h-6 w-6 text-blue-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Total Customers"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.totalCustomers})]})]})})}),a.jsx(c.Zb,{children:a.jsx(c.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-green-100 rounded-full",children:a.jsx(p.Z,{className:"h-6 w-6 text-green-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Total Revenue"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:C(e.summary.totalRevenue)})]})]})})}),a.jsx(c.Zb,{children:a.jsx(c.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-purple-100 rounded-full",children:a.jsx(j.Z,{className:"h-6 w-6 text-purple-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Avg CLV"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:C(e.summary.avgCLV)})]})]})})}),a.jsx(c.Zb,{children:a.jsx(c.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-orange-100 rounded-full",children:a.jsx(f.Z,{className:"h-6 w-6 text-orange-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Retention Rate"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[e.summary.retentionRate.toFixed(1),"%"]})]})]})})}),a.jsx(c.Zb,{children:a.jsx(c.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-indigo-100 rounded-full",children:a.jsx(y.Z,{className:"h-6 w-6 text-indigo-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Engagement Rate"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[e.summary.engagementRate,"%"]})]})]})})}),a.jsx(c.Zb,{children:a.jsx(c.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-pink-100 rounded-full",children:a.jsx(g.Z,{className:"h-6 w-6 text-pink-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"New This Week"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.newCustomersThisWeek})]})]})})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)(c.Zb,{children:[a.jsx(c.Ol,{children:a.jsx(c.ll,{children:"Customers by Status"})}),a.jsx(c.aY,{children:a.jsx("div",{className:"space-y-3",children:e.customersByStatus.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("div",{className:"flex items-center space-x-2",children:a.jsx(n.C,{className:w(e.status),children:e.status})}),a.jsx("span",{className:"font-semibold",children:e.count})]},e.status))})})]}),(0,a.jsxs)(c.Zb,{children:[a.jsx(c.Ol,{children:a.jsx(c.ll,{children:"Customer Segmentation"})}),a.jsx(c.aY,{children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-sm",children:"High Value Customers"}),a.jsx("span",{className:"font-semibold",children:e.segmentation.highValue})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-sm",children:"Active Customers"}),a.jsx("span",{className:"font-semibold",children:e.segmentation.active})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-sm",children:"Prospects"}),a.jsx("span",{className:"font-semibold",children:e.segmentation.prospects})]})]})})]}),(0,a.jsxs)(c.Zb,{children:[a.jsx(c.Ol,{children:a.jsx(c.ll,{children:"Customers by Industry"})}),a.jsx(c.aY,{children:a.jsx("div",{className:"space-y-3",children:e.customersByIndustry.slice(0,5).map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-sm",children:e.industry}),a.jsx("span",{className:"font-semibold",children:e.count})]},e.industry))})})]}),(0,a.jsxs)(c.Zb,{children:[a.jsx(c.Ol,{children:a.jsx(c.ll,{children:"Customer Engagement"})}),a.jsx(c.aY,{children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-sm",children:"With Recent Activities"}),a.jsx("span",{className:"font-semibold",children:e.engagement.withActivities})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-sm",children:"With Recent Quotations"}),a.jsx("span",{className:"font-semibold",children:e.engagement.withQuotations})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-sm",children:"With Recent Invoices"}),a.jsx("span",{className:"font-semibold",children:e.engagement.withInvoices})]})]})})]})]}),(0,a.jsxs)(c.Zb,{children:[a.jsx(c.Ol,{children:(0,a.jsxs)(c.ll,{className:"flex items-center",children:[a.jsx(N.Z,{className:"h-5 w-5 mr-2"}),"Top Customers by Revenue"]})}),a.jsx(c.aY,{children:a.jsx("div",{className:"space-y-3",children:0===e.topCustomers.length?a.jsx("p",{className:"text-gray-500 text-center py-4",children:"No customer data available"}):e.topCustomers.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,a.jsxs)("span",{className:"text-sm font-semibold text-blue-600",children:["#",s+1]})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[e.company&&`${e.company} • `,e.email]})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[a.jsx("p",{className:"font-semibold text-green-600",children:C(e.totalRevenue)}),a.jsx(n.C,{className:w(e.status),variant:"outline",children:e.status})]})]},e.id))})})]}),(0,a.jsxs)(c.Zb,{children:[a.jsx(c.Ol,{children:a.jsx(c.ll,{children:"Recent Customers (Last 7 Days)"})}),a.jsx(c.aY,{children:a.jsx("div",{className:"space-y-3",children:0===e.recentCustomers.length?a.jsx("p",{className:"text-gray-500 text-center py-4",children:"No new customers this week"}):e.recentCustomers.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[e.company&&`${e.company} • `,"Added by ",e.createdBy.name||e.createdBy.email]})]}),(0,a.jsxs)("div",{className:"text-right",children:[a.jsx("p",{className:"text-sm text-gray-500",children:new Date(e.createdAt).toLocaleDateString()}),a.jsx(n.C,{className:w(e.status),variant:"outline",children:e.status})]})]},e.id))})})]}),(0,a.jsxs)(c.Zb,{children:[a.jsx(c.Ol,{children:a.jsx(c.ll,{children:"Customer Lifetime Value Analysis"})}),a.jsx(c.aY,{children:a.jsx("div",{className:"space-y-3",children:0===e.clvMetrics.length?a.jsx("p",{className:"text-gray-500 text-center py-4",children:"No CLV data available"}):e.clvMetrics.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Customer for ",e.customerAge," days"]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("p",{className:"font-semibold text-purple-600",children:["CLV: ",C(e.clv)]}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Total: ",C(e.totalRevenue)]})]})]},e.id))})})]})]}):(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[a.jsx(m.Z,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),a.jsx("p",{children:"Failed to load analytics data"})]})}var w=t(92549),C=t(1586),k=t(84332),Z=t(16802),E=t(57341),S=t(3380),R=t(37121),O=t(7060),P=t(73229);function I({open:e,onClose:s,onSuccess:t}){let[l,d]=(0,r.useState)(null),[o,x]=(0,r.useState)(!1),[m,h]=(0,r.useState)(null),[u,p]=(0,r.useState)({skipDuplicates:!0,updateExisting:!1}),j=(0,r.useRef)(null),f=e=>{let s=e.split("\n").filter(e=>e.trim());if(s.length<2)throw Error("CSV file must have at least a header row and one data row");let t=s[0].split(",").map(e=>e.trim().replace(/"/g,"")),a=[];for(let e=1;e<s.length;e++){let r=s[e].split(",").map(e=>e.trim().replace(/"/g,"")),l={};t.forEach((e,s)=>{let t=r[s]||"";switch(e.toLowerCase()){case"name":l.name=t;break;case"email":l.email=t||null;break;case"phone":l.phone=t||null;break;case"company":l.company=t||null;break;case"address":l.address=t||null;break;case"city":l.city=t||null;break;case"state":l.state=t||null;break;case"country":l.country=t||null;break;case"postalcode":case"postal_code":case"zip":l.postalCode=t||null;break;case"industry":l.industry=t||null;break;case"website":l.website=t||null;break;case"notes":l.notes=t||null;break;case"status":l.status=["ACTIVE","INACTIVE","PROSPECT"].includes(t.toUpperCase())?t.toUpperCase():"PROSPECT";break;case"tags":l.tags=t?t.split(";").map(e=>e.trim()).filter(Boolean):[]}}),l.name&&a.push(l)}return a},y=async()=>{if(!l){v.toast.error("Please select a file to import");return}x(!0);try{let e=await l.text(),s=f(e);if(0===s.length)throw Error("No valid customer data found in the file");let a=await fetch("/api/customers/import",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({customers:s,options:u})});if(!a.ok){let e=await a.json();throw Error(e.error||"Failed to import customers")}let r=await a.json();h(r.results),(r.results.imported>0||r.results.updated>0)&&(v.toast.success(`Successfully processed ${r.results.imported+r.results.updated} customers`),t())}catch(e){v.toast.error(e instanceof Error?e.message:"Failed to import customers"),console.error("Import error:",e)}finally{x(!1)}},g=()=>{d(null),h(null),p({skipDuplicates:!0,updateExisting:!1}),s()},N=async()=>{try{let e=await fetch("/api/customers/import?format=csv");if(!e.ok)throw Error("Failed to download template");let s=await e.blob(),t=window.URL.createObjectURL(s),a=document.createElement("a");a.href=t,a.download="customer-import-template.csv",document.body.appendChild(a),a.click(),window.URL.revokeObjectURL(t),document.body.removeChild(a),v.toast.success("Template downloaded successfully")}catch(e){v.toast.error("Failed to download template")}};return a.jsx(Z.Vq,{open:e,onOpenChange:g,children:(0,a.jsxs)(Z.cZ,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[a.jsx(Z.fK,{children:a.jsx(Z.$N,{children:"Import Customers"})}),(0,a.jsxs)("div",{className:"space-y-6",children:[m?(0,a.jsxs)(c.Zb,{children:[a.jsx(c.Ol,{children:(0,a.jsxs)(c.ll,{className:"flex items-center",children:[a.jsx(O.Z,{className:"h-5 w-5 mr-2 text-green-600"}),"Import Results"]})}),(0,a.jsxs)(c.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-2xl font-bold text-blue-600",children:m.total}),a.jsx("div",{className:"text-sm text-gray-500",children:"Total"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-2xl font-bold text-green-600",children:m.imported}),a.jsx("div",{className:"text-sm text-gray-500",children:"Imported"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-2xl font-bold text-orange-600",children:m.updated}),a.jsx("div",{className:"text-sm text-gray-500",children:"Updated"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-2xl font-bold text-gray-600",children:m.skipped}),a.jsx("div",{className:"text-sm text-gray-500",children:"Skipped"})]})]}),m.errors.length>0&&(0,a.jsxs)(a.Fragment,{children:[a.jsx(E.Z,{}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h4",{className:"font-medium text-red-600 mb-2 flex items-center",children:[a.jsx(P.Z,{className:"h-4 w-4 mr-2"}),"Errors (",m.errors.length,")"]}),a.jsx("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:m.errors.map((e,s)=>(0,a.jsxs)("div",{className:"p-2 bg-red-50 rounded text-sm",children:[(0,a.jsxs)("div",{className:"font-medium text-red-800",children:["Row ",e.row,": ",e.error]}),(0,a.jsxs)("div",{className:"text-red-600 text-xs mt-1",children:["Data: ",JSON.stringify(e.data)]})]},s))})]})]})]})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(c.Zb,{children:[a.jsx(c.Ol,{children:(0,a.jsxs)(c.ll,{className:"flex items-center",children:[a.jsx(S.Z,{className:"h-5 w-5 mr-2"}),"Upload CSV File"]})}),(0,a.jsxs)(c.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[a.jsx(C._,{htmlFor:"file",children:"Select CSV File"}),a.jsx(w.I,{id:"file",type:"file",accept:".csv",onChange:e=>{let s=e.target.files?.[0];s&&("text/csv"===s.type||s.name.endsWith(".csv")?(d(s),h(null)):v.toast.error("Please select a CSV file"))},ref:j})]}),l&&a.jsx("div",{className:"p-3 bg-green-50 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(R.Z,{className:"h-4 w-4 text-green-600"}),a.jsx("span",{className:"text-sm font-medium text-green-800",children:l.name}),(0,a.jsxs)(n.C,{variant:"outline",children:[(l.size/1024).toFixed(1)," KB"]})]})}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[a.jsx("p",{children:"Upload a CSV file with customer data. The file should include columns like:"}),a.jsx("p",{className:"mt-1 font-mono text-xs bg-gray-100 p-2 rounded",children:"name, email, phone, company, address, city, state, country, postalCode, industry, website, notes, status, tags"})]}),(0,a.jsxs)(i.z,{variant:"outline",onClick:N,className:"w-full",children:[a.jsx(R.Z,{className:"h-4 w-4 mr-2"}),"Download Template"]})]})]}),(0,a.jsxs)(c.Zb,{children:[a.jsx(c.Ol,{children:a.jsx(c.ll,{children:"Import Options"})}),(0,a.jsxs)(c.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(k.X,{id:"skipDuplicates",checked:u.skipDuplicates,onCheckedChange:e=>p(s=>({...s,skipDuplicates:e}))}),a.jsx(C._,{htmlFor:"skipDuplicates",children:"Skip duplicate customers (by email)"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(k.X,{id:"updateExisting",checked:u.updateExisting,onCheckedChange:e=>p(s=>({...s,updateExisting:e}))}),a.jsx(C._,{htmlFor:"updateExisting",children:"Update existing customers"})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[a.jsx("p",{children:"• Skip duplicates: Existing customers with the same email will be ignored"}),a.jsx("p",{children:"• Update existing: Existing customers will be updated with new data"})]})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-2 pt-4 border-t",children:[a.jsx(i.z,{variant:"outline",onClick:g,children:m?"Close":"Cancel"}),!m&&a.jsx(i.z,{onClick:y,disabled:!l||o,children:o?"Importing...":"Import Customers"})]})]})]})})}var A=t(71206),T=t(40626),V=t(91700),L=t(62093),_=t(53148),F=t(75695),U=t(38271),D=t(96885),Y=t(51838),q=t(20886),z=t(20783),$=t.n(z);function M(){let{data:e}=(0,l.useSession)(),[s,t]=(0,r.useState)([]),[x,h]=(0,r.useState)(!0),[f,y]=(0,r.useState)(!1),[g,N]=(0,r.useState)(null),[w,C]=(0,r.useState)(!1),[k,Z]=(0,r.useState)(!1),[E,O]=(0,r.useState)({total:0,active:0,prospects:0,inactive:0}),P=async()=>{try{let e=await fetch("/api/customers");if(!e.ok)throw Error("Failed to fetch customers");let s=await e.json();t(s.customers);let a=s.customers.length,r=s.customers.filter(e=>"ACTIVE"===e.status).length,l=s.customers.filter(e=>"PROSPECT"===e.status).length,c=s.customers.filter(e=>"INACTIVE"===e.status).length;O({total:a,active:r,prospects:l,inactive:c})}catch(e){v.toast.error("Failed to load customers"),console.error("Error fetching customers:",e)}finally{h(!1)}};(0,r.useEffect)(()=>{P()},[]);let z=async e=>{if(confirm(`Are you sure you want to delete ${e.name}?`))try{let s=await fetch(`/api/customers/${e.id}`,{method:"DELETE"});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to delete customer")}v.toast.success("Customer deleted successfully"),P()}catch(e){v.toast.error(e instanceof Error?e.message:"Failed to delete customer")}},M=e=>{N(e),y(!0)},W=async(e="csv")=>{try{let s=await fetch(`/api/customers/export?format=${e}&includeStats=true`);if(!s.ok)throw Error("Failed to export customers");if("csv"===e){let e=await s.blob(),t=window.URL.createObjectURL(e),a=document.createElement("a");a.href=t,a.download=`customers-export-${new Date().toISOString().split("T")[0]}.csv`,document.body.appendChild(a),a.click(),window.URL.revokeObjectURL(t),document.body.removeChild(a)}else{let e=await s.json(),t=new Blob([JSON.stringify(e,null,2)],{type:"application/json"}),a=window.URL.createObjectURL(t),r=document.createElement("a");r.href=a,r.download=`customers-export-${new Date().toISOString().split("T")[0]}.json`,document.body.appendChild(r),r.click(),window.URL.revokeObjectURL(a),document.body.removeChild(r)}v.toast.success("Customers exported successfully")}catch(e){v.toast.error("Failed to export customers"),console.error("Error exporting customers:",e)}},X=async()=>{try{let e=await fetch("/api/customers/import?format=csv");if(!e.ok)throw Error("Failed to get import template");let s=await e.blob(),t=window.URL.createObjectURL(s),a=document.createElement("a");a.href=t,a.download="customer-import-template.csv",document.body.appendChild(a),a.click(),window.URL.revokeObjectURL(t),document.body.removeChild(a),v.toast.success("Import template downloaded")}catch(e){v.toast.error("Failed to download import template"),console.error("Error downloading template:",e)}},B=e=>{if(!e)return a.jsx(n.C,{variant:"secondary",children:"Unknown"});switch(e){case"ACTIVE":return a.jsx(n.C,{variant:"success",children:"Active"});case"INACTIVE":return a.jsx(n.C,{variant:"secondary",children:"Inactive"});case"PROSPECT":return a.jsx(n.C,{variant:"warning",children:"Prospect"});default:return a.jsx(n.C,{variant:"secondary",children:e})}},K=[{accessorKey:"name",header:"Name",cell:({row:e})=>{let s=e.original;return(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:a.jsx("span",{className:"text-blue-600 font-medium text-sm",children:s.name.charAt(0).toUpperCase()})}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium",children:s.name}),s.company&&a.jsx("div",{className:"text-sm text-gray-500",children:s.company})]})]})}},{accessorKey:"email",header:"Contact",cell:({row:e})=>{let s=e.original;return(0,a.jsxs)("div",{className:"space-y-1",children:[s.email&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[a.jsx(A.Z,{className:"h-3 w-3 text-gray-400"}),a.jsx("span",{children:s.email})]}),s.phone&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[a.jsx(T.Z,{className:"h-3 w-3 text-gray-400"}),a.jsx("span",{children:s.phone})]})]})}},{accessorKey:"status",header:"Status",cell:({row:e})=>B(e.getValue("status"))},{accessorKey:"industry",header:"Industry",cell:({row:e})=>{let s=e.getValue("industry");return s?(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(V.Z,{className:"h-3 w-3 text-gray-400"}),a.jsx("span",{className:"text-sm",children:s})]}):a.jsx("span",{className:"text-gray-400 text-sm",children:"-"})}},{accessorKey:"_count",header:"Activity",cell:({row:e})=>{let s=e.getValue("_count");return(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[a.jsx(j.Z,{className:"h-3 w-3 text-green-600"}),a.jsx("span",{children:s.leads})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[a.jsx(p.Z,{className:"h-3 w-3 text-blue-600"}),a.jsx("span",{children:s.quotations})]})]})}},{accessorKey:"tags",header:"Tags",cell:({row:e})=>{let s=e.getValue("tags");return s&&Array.isArray(s)&&0!==s.length?(0,a.jsxs)("div",{className:"flex flex-wrap gap-1",children:[s.slice(0,2).map(e=>a.jsx(n.C,{variant:"outline",className:"text-xs",children:e},e)),s.length>2&&(0,a.jsxs)(n.C,{variant:"outline",className:"text-xs",children:["+",s.length-2]})]}):a.jsx("span",{className:"text-gray-400 text-sm",children:"-"})}},{id:"actions",cell:({row:e})=>{let s=e.original;return(0,a.jsxs)(q.h_,{children:[a.jsx(q.$F,{asChild:!0,children:a.jsx(i.z,{variant:"ghost",className:"h-8 w-8 p-0",children:a.jsx(L.Z,{className:"h-4 w-4"})})}),(0,a.jsxs)(q.AW,{align:"end",children:[a.jsx(q.Ju,{children:"Actions"}),a.jsx(q.Xi,{asChild:!0,children:(0,a.jsxs)($(),{href:`/dashboard/customers/${s.id}`,children:[a.jsx(_.Z,{className:"mr-2 h-4 w-4"}),"View Details"]})}),(0,a.jsxs)(q.Xi,{onClick:()=>M(s),children:[a.jsx(F.Z,{className:"mr-2 h-4 w-4"}),"Edit"]}),a.jsx(q.VD,{}),(0,a.jsxs)(q.Xi,{onClick:()=>z(s),className:"text-red-600",children:[a.jsx(U.Z,{className:"mr-2 h-4 w-4"}),"Delete"]})]})]})}}];return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Customers"}),a.jsx("p",{className:"text-gray-600 mt-1",children:"Manage your customer relationships"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(i.z,{variant:"outline",onClick:()=>C(!w),children:[a.jsx(m.Z,{className:"h-4 w-4 mr-2"}),"Analytics"]}),(0,a.jsxs)(q.h_,{children:[a.jsx(q.$F,{asChild:!0,children:(0,a.jsxs)(i.z,{variant:"outline",children:[a.jsx(D.Z,{className:"h-4 w-4 mr-2"}),"Export"]})}),(0,a.jsxs)(q.AW,{children:[(0,a.jsxs)(q.Xi,{onClick:()=>W("csv"),children:[a.jsx(R.Z,{className:"h-4 w-4 mr-2"}),"Export as CSV"]}),(0,a.jsxs)(q.Xi,{onClick:()=>W("json"),children:[a.jsx(R.Z,{className:"h-4 w-4 mr-2"}),"Export as JSON"]})]})]}),(0,a.jsxs)(q.h_,{children:[a.jsx(q.$F,{asChild:!0,children:(0,a.jsxs)(i.z,{variant:"outline",children:[a.jsx(S.Z,{className:"h-4 w-4 mr-2"}),"Import"]})}),(0,a.jsxs)(q.AW,{children:[(0,a.jsxs)(q.Xi,{onClick:X,children:[a.jsx(D.Z,{className:"h-4 w-4 mr-2"}),"Download Template"]}),(0,a.jsxs)(q.Xi,{onClick:()=>Z(!0),children:[a.jsx(S.Z,{className:"h-4 w-4 mr-2"}),"Import Customers"]})]})]}),(0,a.jsxs)(i.z,{onClick:()=>y(!0),children:[a.jsx(Y.Z,{className:"h-4 w-4 mr-2"}),"Add Customer"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,a.jsxs)(c.Zb,{children:[(0,a.jsxs)(c.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(c.ll,{className:"text-sm font-medium",children:"Total Customers"}),a.jsx(u.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(c.aY,{children:[a.jsx("div",{className:"text-2xl font-bold",children:E.total}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"All customers"})]})]}),(0,a.jsxs)(c.Zb,{children:[(0,a.jsxs)(c.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(c.ll,{className:"text-sm font-medium",children:"Active"}),a.jsx(u.Z,{className:"h-4 w-4 text-green-600"})]}),(0,a.jsxs)(c.aY,{children:[a.jsx("div",{className:"text-2xl font-bold",children:E.active}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"Active customers"})]})]}),(0,a.jsxs)(c.Zb,{children:[(0,a.jsxs)(c.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(c.ll,{className:"text-sm font-medium",children:"Prospects"}),a.jsx(u.Z,{className:"h-4 w-4 text-yellow-600"})]}),(0,a.jsxs)(c.aY,{children:[a.jsx("div",{className:"text-2xl font-bold",children:E.prospects}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"Potential customers"})]})]}),(0,a.jsxs)(c.Zb,{children:[(0,a.jsxs)(c.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(c.ll,{className:"text-sm font-medium",children:"Inactive"}),a.jsx(u.Z,{className:"h-4 w-4 text-gray-400"})]}),(0,a.jsxs)(c.aY,{children:[a.jsx("div",{className:"text-2xl font-bold",children:E.inactive}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"Inactive customers"})]})]})]}),w&&a.jsx(b,{}),(0,a.jsxs)(c.Zb,{children:[a.jsx(c.Ol,{children:a.jsx(c.ll,{children:"Customer Directory"})}),a.jsx(c.aY,{children:x?a.jsx("div",{className:"flex items-center justify-center py-8",children:a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):a.jsx(d.w,{columns:K,data:s,searchPlaceholder:"Search customers..."})})]}),a.jsx(o.A,{isOpen:f,onClose:()=>{y(!1),N(null)},onSuccess:P,customer:g,mode:g?"edit":"create"}),a.jsx(I,{open:k,onClose:()=>Z(!1),onSuccess:()=>{Z(!1),P()}})]})}},84332:(e,s,t)=>{"use strict";t.d(s,{X:()=>O});var a=t(95344),r=t(3729),l=t(31405),c=t(98462),i=t(85222),n=t(33183),d=t(92062),o=t(63085),x=t(43234),m=t(62409),h="Checkbox",[u,p]=(0,c.b)(h),[j,f]=u(h);function y(e){let{__scopeCheckbox:s,checked:t,children:l,defaultChecked:c,disabled:i,form:d,name:o,onCheckedChange:x,required:m,value:u="on",internal_do_not_use_render:p}=e,[f,y]=(0,n.T)({prop:t,defaultProp:c??!1,onChange:x,caller:h}),[g,N]=r.useState(null),[v,b]=r.useState(null),w=r.useRef(!1),C=!g||!!d||!!g.closest("form"),k={checked:f,disabled:i,setChecked:y,control:g,setControl:N,name:o,form:d,value:u,hasConsumerStoppedPropagationRef:w,required:m,defaultChecked:!Z(c)&&c,isFormControl:C,bubbleInput:v,setBubbleInput:b};return(0,a.jsx)(j,{scope:s,...k,children:"function"==typeof p?p(k):l})}var g="CheckboxTrigger",N=r.forwardRef(({__scopeCheckbox:e,onKeyDown:s,onClick:t,...c},n)=>{let{control:d,value:o,disabled:x,checked:h,required:u,setControl:p,setChecked:j,hasConsumerStoppedPropagationRef:y,isFormControl:N,bubbleInput:v}=f(g,e),b=(0,l.e)(n,p),w=r.useRef(h);return r.useEffect(()=>{let e=d?.form;if(e){let s=()=>j(w.current);return e.addEventListener("reset",s),()=>e.removeEventListener("reset",s)}},[d,j]),(0,a.jsx)(m.WV.button,{type:"button",role:"checkbox","aria-checked":Z(h)?"mixed":h,"aria-required":u,"data-state":E(h),"data-disabled":x?"":void 0,disabled:x,value:o,...c,ref:b,onKeyDown:(0,i.M)(s,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,i.M)(t,e=>{j(e=>!!Z(e)||!e),v&&N&&(y.current=e.isPropagationStopped(),y.current||e.stopPropagation())})})});N.displayName=g;var v=r.forwardRef((e,s)=>{let{__scopeCheckbox:t,name:r,checked:l,defaultChecked:c,required:i,disabled:n,value:d,onCheckedChange:o,form:x,...m}=e;return(0,a.jsx)(y,{__scopeCheckbox:t,checked:l,defaultChecked:c,disabled:n,required:i,onCheckedChange:o,name:r,form:x,value:d,internal_do_not_use_render:({isFormControl:e})=>(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(N,{...m,ref:s,__scopeCheckbox:t}),e&&(0,a.jsx)(k,{__scopeCheckbox:t})]})})});v.displayName=h;var b="CheckboxIndicator",w=r.forwardRef((e,s)=>{let{__scopeCheckbox:t,forceMount:r,...l}=e,c=f(b,t);return(0,a.jsx)(x.z,{present:r||Z(c.checked)||!0===c.checked,children:(0,a.jsx)(m.WV.span,{"data-state":E(c.checked),"data-disabled":c.disabled?"":void 0,...l,ref:s,style:{pointerEvents:"none",...e.style}})})});w.displayName=b;var C="CheckboxBubbleInput",k=r.forwardRef(({__scopeCheckbox:e,...s},t)=>{let{control:c,hasConsumerStoppedPropagationRef:i,checked:n,defaultChecked:x,required:h,disabled:u,name:p,value:j,form:y,bubbleInput:g,setBubbleInput:N}=f(C,e),v=(0,l.e)(t,N),b=(0,d.D)(n),w=(0,o.t)(c);r.useEffect(()=>{if(!g)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,s=!i.current;if(b!==n&&e){let t=new Event("click",{bubbles:s});g.indeterminate=Z(n),e.call(g,!Z(n)&&n),g.dispatchEvent(t)}},[g,b,n,i]);let k=r.useRef(!Z(n)&&n);return(0,a.jsx)(m.WV.input,{type:"checkbox","aria-hidden":!0,defaultChecked:x??k.current,required:h,disabled:u,name:p,value:j,form:y,...s,tabIndex:-1,ref:v,style:{...s.style,...w,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function Z(e){return"indeterminate"===e}function E(e){return Z(e)?"indeterminate":e?"checked":"unchecked"}k.displayName=C;var S=t(62312),R=t(91626);let O=r.forwardRef(({className:e,...s},t)=>a.jsx(v,{ref:t,className:(0,R.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...s,children:a.jsx(w,{className:(0,R.cn)("flex items-center justify-center text-current"),children:a.jsx(S.Z,{className:"h-4 w-4"})})}));O.displayName=v.displayName},57341:(e,s,t)=>{"use strict";t.d(s,{Z:()=>o});var a=t(95344),r=t(3729),l=t(62409),c="horizontal",i=["horizontal","vertical"],n=r.forwardRef((e,s)=>{let{decorative:t,orientation:r=c,...n}=e,d=i.includes(r)?r:c;return(0,a.jsx)(l.WV.div,{"data-orientation":d,...t?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...n,ref:s})});n.displayName="Separator";var d=t(91626);let o=r.forwardRef(({className:e,orientation:s="horizontal",decorative:t=!0,...r},l)=>a.jsx(n,{ref:l,decorative:t,orientation:s,className:(0,d.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",e),...r}));o.displayName=n.displayName},7060:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},48411:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},96885:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},76755:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},3380:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},15366:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("UserCheck",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]])},73229:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},5343:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>l,__esModule:()=>r,default:()=>c});let a=(0,t(86843).createProxy)(String.raw`C:\proj\nextjs-saas\app\dashboard\customers\page.tsx`),{__esModule:r,$$typeof:l}=a,c=a.default}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[1638,7948,6671,4626,7792,2506,8830,7150,3117,2125,5045,5232,3609],()=>t(17033));module.exports=a})();