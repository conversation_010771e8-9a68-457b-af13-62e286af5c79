import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// Lead scoring criteria and weights (same as individual scoring)
const SCORING_CRITERIA = {
  companySize: {
    'STARTUP': 5,
    'SMALL': 10,
    'MEDIUM': 20,
    'LARGE': 25,
    'ENTERPRISE': 30
  },
  industry: {
    'TECHNOLOGY': 25,
    'FINANCE': 20,
    'HEALTHCARE': 20,
    'MANUFACTURING': 15,
    'RETAIL': 10,
    'OTHER': 5
  },
  hasPhone: 10,
  hasWebsite: 5,
  hasCompanyEmail: 5,
  activityCount: {
    0: 0,
    1: 5,
    2: 10,
    3: 15,
    4: 20,
    5: 25
  },
  recentActivity: 10,
  hasBudget: 15,
  hasTimeline: 10
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { action, leadIds } = await request.json()

    if (action === 'recalculate') {
      return await recalculateScores(session.user.companyId, session.user.id, leadIds)
    } else if (action === 'analyze') {
      return await analyzeLeadScores(session.user.companyId)
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 })

  } catch (error) {
    console.error('Error in bulk scoring operation:', error)
    return NextResponse.json(
      { error: 'Failed to perform bulk scoring operation' },
      { status: 500 }
    )
  }
}

async function recalculateScores(companyId: string, userId: string, leadIds?: string[]) {
  try {
    // Get leads to recalculate
    const whereClause: any = { companyId }
    if (leadIds && leadIds.length > 0) {
      whereClause.id = { in: leadIds }
    }

    const leads = await prisma.lead.findMany({
      where: whereClause,
      include: {
        activities: {
          orderBy: { createdAt: 'desc' },
          take: 10
        },
        _count: {
          select: {
            activities: true
          }
        }
      }
    })

    const updates = []
    const scoreHistoryEntries = []

    for (const lead of leads) {
      const newScore = calculateLeadScore(lead)
      
      if (newScore !== lead.score) {
        updates.push({
          id: lead.id,
          score: newScore
        })

        scoreHistoryEntries.push({
          leadId: lead.id,
          previousScore: lead.score,
          newScore: newScore,
          changeReason: 'Bulk score recalculation',
          isManual: false,
          createdById: userId,
          companyId: companyId
        })
      }
    }

    // Perform bulk updates
    if (updates.length > 0) {
      await Promise.all([
        // Update lead scores
        ...updates.map(update => 
          prisma.lead.update({
            where: { id: update.id },
            data: { 
              score: update.score,
              updatedAt: new Date()
            }
          })
        ),
        // Create score history entries
        prisma.leadScoreHistory.createMany({
          data: scoreHistoryEntries
        })
      ])
    }

    return NextResponse.json({
      message: 'Scores recalculated successfully',
      updatedCount: updates.length,
      totalProcessed: leads.length,
      updates: updates.map(u => ({
        leadId: u.id,
        newScore: u.score
      }))
    })

  } catch (error) {
    console.error('Error recalculating scores:', error)
    throw error
  }
}

async function analyzeLeadScores(companyId: string) {
  try {
    const leads = await prisma.lead.findMany({
      where: { companyId },
      select: {
        id: true,
        score: true,
        status: true,
        createdAt: true
      }
    })

    // Calculate analytics
    const totalLeads = leads.length
    const averageScore = totalLeads > 0 
      ? Math.round(leads.reduce((sum, lead) => sum + lead.score, 0) / totalLeads)
      : 0

    // Temperature distribution
    const temperatureDistribution = {
      HOT: leads.filter(l => l.score >= 70).length,
      WARM: leads.filter(l => l.score >= 40 && l.score < 70).length,
      COLD: leads.filter(l => l.score < 40).length
    }

    // Score distribution by ranges
    const scoreRanges = {
      '0-20': leads.filter(l => l.score >= 0 && l.score < 20).length,
      '20-40': leads.filter(l => l.score >= 20 && l.score < 40).length,
      '40-60': leads.filter(l => l.score >= 40 && l.score < 60).length,
      '60-80': leads.filter(l => l.score >= 60 && l.score < 80).length,
      '80-100': leads.filter(l => l.score >= 80 && l.score <= 100).length
    }

    // Status vs Score analysis
    const statusAnalysis = {}
    const statuses = [...new Set(leads.map(l => l.status))]
    
    for (const status of statuses) {
      const statusLeads = leads.filter(l => l.status === status)
      statusAnalysis[status] = {
        count: statusLeads.length,
        averageScore: statusLeads.length > 0 
          ? Math.round(statusLeads.reduce((sum, lead) => sum + lead.score, 0) / statusLeads.length)
          : 0
      }
    }

    // Top and bottom performers
    const sortedLeads = [...leads].sort((a, b) => b.score - a.score)
    const topPerformers = sortedLeads.slice(0, 10)
    const bottomPerformers = sortedLeads.slice(-10).reverse()

    // Recent trends (last 30 days)
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
    
    const recentLeads = leads.filter(l => new Date(l.createdAt) >= thirtyDaysAgo)
    const recentAverageScore = recentLeads.length > 0
      ? Math.round(recentLeads.reduce((sum, lead) => sum + lead.score, 0) / recentLeads.length)
      : 0

    return NextResponse.json({
      summary: {
        totalLeads,
        averageScore,
        recentLeads: recentLeads.length,
        recentAverageScore
      },
      temperatureDistribution,
      scoreRanges,
      statusAnalysis,
      topPerformers: topPerformers.map(l => ({
        id: l.id,
        score: l.score,
        status: l.status
      })),
      bottomPerformers: bottomPerformers.map(l => ({
        id: l.id,
        score: l.score,
        status: l.status
      })),
      recommendations: generateBulkRecommendations(leads, temperatureDistribution)
    })

  } catch (error) {
    console.error('Error analyzing lead scores:', error)
    throw error
  }
}

function calculateLeadScore(lead: any): number {
  let score = 0

  // Company size scoring
  if (lead.companySize && SCORING_CRITERIA.companySize[lead.companySize]) {
    score += SCORING_CRITERIA.companySize[lead.companySize]
  }

  // Industry scoring
  if (lead.industry && SCORING_CRITERIA.industry[lead.industry]) {
    score += SCORING_CRITERIA.industry[lead.industry]
  }

  // Contact information scoring
  if (lead.phone) score += SCORING_CRITERIA.hasPhone
  if (lead.website) score += SCORING_CRITERIA.hasWebsite
  if (lead.email && lead.companyName && lead.email.includes(lead.companyName.toLowerCase())) {
    score += SCORING_CRITERIA.hasCompanyEmail
  }

  // Activity scoring
  const activityCount = Math.min(lead._count.activities, 5)
  score += SCORING_CRITERIA.activityCount[activityCount] || 0

  // Recent activity bonus
  if (lead.activities.length > 0) {
    const lastActivity = new Date(lead.activities[0].createdAt)
    const daysSinceLastActivity = (Date.now() - lastActivity.getTime()) / (1000 * 60 * 60 * 24)
    if (daysSinceLastActivity <= 7) {
      score += SCORING_CRITERIA.recentActivity
    }
  }

  // Budget and timeline scoring
  if (lead.budget && lead.budget > 0) score += SCORING_CRITERIA.hasBudget
  if (lead.timeline) score += SCORING_CRITERIA.hasTimeline

  return Math.min(score, 100) // Cap at 100
}

function generateBulkRecommendations(leads: any[], temperatureDistribution: any) {
  const recommendations = []

  // Temperature-based recommendations
  if (temperatureDistribution.COLD > temperatureDistribution.HOT) {
    recommendations.push({
      type: 'ENGAGEMENT',
      priority: 'HIGH',
      message: `You have ${temperatureDistribution.COLD} cold leads. Focus on re-engagement campaigns.`,
      action: 'Launch re-engagement campaign'
    })
  }

  if (temperatureDistribution.HOT > 0) {
    recommendations.push({
      type: 'CONVERSION',
      priority: 'URGENT',
      message: `You have ${temperatureDistribution.HOT} hot leads ready for conversion.`,
      action: 'Prioritize hot leads for immediate follow-up'
    })
  }

  // Activity-based recommendations
  const leadsWithoutActivity = leads.filter(l => !l.activities || l.activities.length === 0).length
  if (leadsWithoutActivity > 0) {
    recommendations.push({
      type: 'ACTIVITY',
      priority: 'HIGH',
      message: `${leadsWithoutActivity} leads have no recorded activities.`,
      action: 'Schedule initial contact activities'
    })
  }

  return recommendations
}
