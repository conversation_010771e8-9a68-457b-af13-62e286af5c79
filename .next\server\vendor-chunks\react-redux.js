"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-redux";
exports.ids = ["vendor-chunks/react-redux"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-redux/dist/react-redux.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/react-redux/dist/react-redux.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Provider: () => (/* binding */ Provider_default),\n/* harmony export */   ReactReduxContext: () => (/* binding */ ReactReduxContext),\n/* harmony export */   batch: () => (/* binding */ batch),\n/* harmony export */   connect: () => (/* binding */ connect_default),\n/* harmony export */   createDispatchHook: () => (/* binding */ createDispatchHook),\n/* harmony export */   createSelectorHook: () => (/* binding */ createSelectorHook),\n/* harmony export */   createStoreHook: () => (/* binding */ createStoreHook),\n/* harmony export */   shallowEqual: () => (/* binding */ shallowEqual),\n/* harmony export */   useDispatch: () => (/* binding */ useDispatch),\n/* harmony export */   useSelector: () => (/* binding */ useSelector),\n/* harmony export */   useStore: () => (/* binding */ useStore)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var use_sync_external_store_with_selector_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-sync-external-store/with-selector.js */ \"(ssr)/./node_modules/use-sync-external-store/with-selector.js\");\n// src/utils/react.ts\n\n// src/utils/react-is.ts\nvar IS_REACT_19 = /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.version.startsWith(\"19\");\nvar REACT_ELEMENT_TYPE = /* @__PURE__ */ Symbol.for(IS_REACT_19 ? \"react.transitional.element\" : \"react.element\");\nvar REACT_PORTAL_TYPE = /* @__PURE__ */ Symbol.for(\"react.portal\");\nvar REACT_FRAGMENT_TYPE = /* @__PURE__ */ Symbol.for(\"react.fragment\");\nvar REACT_STRICT_MODE_TYPE = /* @__PURE__ */ Symbol.for(\"react.strict_mode\");\nvar REACT_PROFILER_TYPE = /* @__PURE__ */ Symbol.for(\"react.profiler\");\nvar REACT_CONSUMER_TYPE = /* @__PURE__ */ Symbol.for(\"react.consumer\");\nvar REACT_CONTEXT_TYPE = /* @__PURE__ */ Symbol.for(\"react.context\");\nvar REACT_FORWARD_REF_TYPE = /* @__PURE__ */ Symbol.for(\"react.forward_ref\");\nvar REACT_SUSPENSE_TYPE = /* @__PURE__ */ Symbol.for(\"react.suspense\");\nvar REACT_SUSPENSE_LIST_TYPE = /* @__PURE__ */ Symbol.for(\"react.suspense_list\");\nvar REACT_MEMO_TYPE = /* @__PURE__ */ Symbol.for(\"react.memo\");\nvar REACT_LAZY_TYPE = /* @__PURE__ */ Symbol.for(\"react.lazy\");\nvar REACT_OFFSCREEN_TYPE = /* @__PURE__ */ Symbol.for(\"react.offscreen\");\nvar REACT_CLIENT_REFERENCE = /* @__PURE__ */ Symbol.for(\"react.client.reference\");\nvar ForwardRef = REACT_FORWARD_REF_TYPE;\nvar Memo = REACT_MEMO_TYPE;\nfunction isValidElementType(type) {\n    return typeof type === \"string\" || typeof type === \"function\" || type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || type === REACT_OFFSCREEN_TYPE || typeof type === \"object\" && type !== null && (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_CONSUMER_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_CLIENT_REFERENCE || type.getModuleId !== void 0) ? true : false;\n}\nfunction typeOf(object) {\n    if (typeof object === \"object\" && object !== null) {\n        const { $$typeof } = object;\n        switch($$typeof){\n            case REACT_ELEMENT_TYPE:\n                switch(object = object.type, object){\n                    case REACT_FRAGMENT_TYPE:\n                    case REACT_PROFILER_TYPE:\n                    case REACT_STRICT_MODE_TYPE:\n                    case REACT_SUSPENSE_TYPE:\n                    case REACT_SUSPENSE_LIST_TYPE:\n                        return object;\n                    default:\n                        switch(object = object && object.$$typeof, object){\n                            case REACT_CONTEXT_TYPE:\n                            case REACT_FORWARD_REF_TYPE:\n                            case REACT_LAZY_TYPE:\n                            case REACT_MEMO_TYPE:\n                                return object;\n                            case REACT_CONSUMER_TYPE:\n                                return object;\n                            default:\n                                return $$typeof;\n                        }\n                }\n            case REACT_PORTAL_TYPE:\n                return $$typeof;\n        }\n    }\n}\nfunction isContextConsumer(object) {\n    return IS_REACT_19 ? typeOf(object) === REACT_CONSUMER_TYPE : typeOf(object) === REACT_CONTEXT_TYPE;\n}\nfunction isMemo(object) {\n    return typeOf(object) === REACT_MEMO_TYPE;\n}\n// src/utils/warning.ts\nfunction warning(message) {\n    if (typeof console !== \"undefined\" && typeof console.error === \"function\") {\n        console.error(message);\n    }\n    try {\n        throw new Error(message);\n    } catch (e) {}\n}\n// src/connect/verifySubselectors.ts\nfunction verify(selector, methodName) {\n    if (!selector) {\n        throw new Error(`Unexpected value for ${methodName} in connect.`);\n    } else if (methodName === \"mapStateToProps\" || methodName === \"mapDispatchToProps\") {\n        if (!Object.prototype.hasOwnProperty.call(selector, \"dependsOnOwnProps\")) {\n            warning(`The selector for ${methodName} of connect did not specify a value for dependsOnOwnProps.`);\n        }\n    }\n}\nfunction verifySubselectors(mapStateToProps, mapDispatchToProps, mergeProps) {\n    verify(mapStateToProps, \"mapStateToProps\");\n    verify(mapDispatchToProps, \"mapDispatchToProps\");\n    verify(mergeProps, \"mergeProps\");\n}\n// src/connect/selectorFactory.ts\nfunction pureFinalPropsSelectorFactory(mapStateToProps, mapDispatchToProps, mergeProps, dispatch, { areStatesEqual, areOwnPropsEqual, areStatePropsEqual }) {\n    let hasRunAtLeastOnce = false;\n    let state;\n    let ownProps;\n    let stateProps;\n    let dispatchProps;\n    let mergedProps;\n    function handleFirstCall(firstState, firstOwnProps) {\n        state = firstState;\n        ownProps = firstOwnProps;\n        stateProps = mapStateToProps(state, ownProps);\n        dispatchProps = mapDispatchToProps(dispatch, ownProps);\n        mergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n        hasRunAtLeastOnce = true;\n        return mergedProps;\n    }\n    function handleNewPropsAndNewState() {\n        stateProps = mapStateToProps(state, ownProps);\n        if (mapDispatchToProps.dependsOnOwnProps) dispatchProps = mapDispatchToProps(dispatch, ownProps);\n        mergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n        return mergedProps;\n    }\n    function handleNewProps() {\n        if (mapStateToProps.dependsOnOwnProps) stateProps = mapStateToProps(state, ownProps);\n        if (mapDispatchToProps.dependsOnOwnProps) dispatchProps = mapDispatchToProps(dispatch, ownProps);\n        mergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n        return mergedProps;\n    }\n    function handleNewState() {\n        const nextStateProps = mapStateToProps(state, ownProps);\n        const statePropsChanged = !areStatePropsEqual(nextStateProps, stateProps);\n        stateProps = nextStateProps;\n        if (statePropsChanged) mergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n        return mergedProps;\n    }\n    function handleSubsequentCalls(nextState, nextOwnProps) {\n        const propsChanged = !areOwnPropsEqual(nextOwnProps, ownProps);\n        const stateChanged = !areStatesEqual(nextState, state, nextOwnProps, ownProps);\n        state = nextState;\n        ownProps = nextOwnProps;\n        if (propsChanged && stateChanged) return handleNewPropsAndNewState();\n        if (propsChanged) return handleNewProps();\n        if (stateChanged) return handleNewState();\n        return mergedProps;\n    }\n    return function pureFinalPropsSelector(nextState, nextOwnProps) {\n        return hasRunAtLeastOnce ? handleSubsequentCalls(nextState, nextOwnProps) : handleFirstCall(nextState, nextOwnProps);\n    };\n}\nfunction finalPropsSelectorFactory(dispatch, { initMapStateToProps, initMapDispatchToProps, initMergeProps, ...options }) {\n    const mapStateToProps = initMapStateToProps(dispatch, options);\n    const mapDispatchToProps = initMapDispatchToProps(dispatch, options);\n    const mergeProps = initMergeProps(dispatch, options);\n    if (true) {\n        verifySubselectors(mapStateToProps, mapDispatchToProps, mergeProps);\n    }\n    return pureFinalPropsSelectorFactory(mapStateToProps, mapDispatchToProps, mergeProps, dispatch, options);\n}\n// src/utils/bindActionCreators.ts\nfunction bindActionCreators(actionCreators, dispatch) {\n    const boundActionCreators = {};\n    for(const key in actionCreators){\n        const actionCreator = actionCreators[key];\n        if (typeof actionCreator === \"function\") {\n            boundActionCreators[key] = (...args)=>dispatch(actionCreator(...args));\n        }\n    }\n    return boundActionCreators;\n}\n// src/utils/isPlainObject.ts\nfunction isPlainObject(obj) {\n    if (typeof obj !== \"object\" || obj === null) return false;\n    const proto = Object.getPrototypeOf(obj);\n    if (proto === null) return true;\n    let baseProto = proto;\n    while(Object.getPrototypeOf(baseProto) !== null){\n        baseProto = Object.getPrototypeOf(baseProto);\n    }\n    return proto === baseProto;\n}\n// src/utils/verifyPlainObject.ts\nfunction verifyPlainObject(value, displayName, methodName) {\n    if (!isPlainObject(value)) {\n        warning(`${methodName}() in ${displayName} must return a plain object. Instead received ${value}.`);\n    }\n}\n// src/connect/wrapMapToProps.ts\nfunction wrapMapToPropsConstant(getConstant) {\n    return function initConstantSelector(dispatch) {\n        const constant = getConstant(dispatch);\n        function constantSelector() {\n            return constant;\n        }\n        constantSelector.dependsOnOwnProps = false;\n        return constantSelector;\n    };\n}\nfunction getDependsOnOwnProps(mapToProps) {\n    return mapToProps.dependsOnOwnProps ? Boolean(mapToProps.dependsOnOwnProps) : mapToProps.length !== 1;\n}\nfunction wrapMapToPropsFunc(mapToProps, methodName) {\n    return function initProxySelector(dispatch, { displayName }) {\n        const proxy = function mapToPropsProxy(stateOrDispatch, ownProps) {\n            return proxy.dependsOnOwnProps ? proxy.mapToProps(stateOrDispatch, ownProps) : proxy.mapToProps(stateOrDispatch, void 0);\n        };\n        proxy.dependsOnOwnProps = true;\n        proxy.mapToProps = function detectFactoryAndVerify(stateOrDispatch, ownProps) {\n            proxy.mapToProps = mapToProps;\n            proxy.dependsOnOwnProps = getDependsOnOwnProps(mapToProps);\n            let props = proxy(stateOrDispatch, ownProps);\n            if (typeof props === \"function\") {\n                proxy.mapToProps = props;\n                proxy.dependsOnOwnProps = getDependsOnOwnProps(props);\n                props = proxy(stateOrDispatch, ownProps);\n            }\n            if (true) verifyPlainObject(props, displayName, methodName);\n            return props;\n        };\n        return proxy;\n    };\n}\n// src/connect/invalidArgFactory.ts\nfunction createInvalidArgFactory(arg, name) {\n    return (dispatch, options)=>{\n        throw new Error(`Invalid value of type ${typeof arg} for ${name} argument when connecting component ${options.wrappedComponentName}.`);\n    };\n}\n// src/connect/mapDispatchToProps.ts\nfunction mapDispatchToPropsFactory(mapDispatchToProps) {\n    return mapDispatchToProps && typeof mapDispatchToProps === \"object\" ? wrapMapToPropsConstant((dispatch)=>// @ts-ignore\n        bindActionCreators(mapDispatchToProps, dispatch)) : !mapDispatchToProps ? wrapMapToPropsConstant((dispatch)=>({\n            dispatch\n        })) : typeof mapDispatchToProps === \"function\" ? // @ts-ignore\n    wrapMapToPropsFunc(mapDispatchToProps, \"mapDispatchToProps\") : createInvalidArgFactory(mapDispatchToProps, \"mapDispatchToProps\");\n}\n// src/connect/mapStateToProps.ts\nfunction mapStateToPropsFactory(mapStateToProps) {\n    return !mapStateToProps ? wrapMapToPropsConstant(()=>({})) : typeof mapStateToProps === \"function\" ? // @ts-ignore\n    wrapMapToPropsFunc(mapStateToProps, \"mapStateToProps\") : createInvalidArgFactory(mapStateToProps, \"mapStateToProps\");\n}\n// src/connect/mergeProps.ts\nfunction defaultMergeProps(stateProps, dispatchProps, ownProps) {\n    return {\n        ...ownProps,\n        ...stateProps,\n        ...dispatchProps\n    };\n}\nfunction wrapMergePropsFunc(mergeProps) {\n    return function initMergePropsProxy(dispatch, { displayName, areMergedPropsEqual }) {\n        let hasRunOnce = false;\n        let mergedProps;\n        return function mergePropsProxy(stateProps, dispatchProps, ownProps) {\n            const nextMergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n            if (hasRunOnce) {\n                if (!areMergedPropsEqual(nextMergedProps, mergedProps)) mergedProps = nextMergedProps;\n            } else {\n                hasRunOnce = true;\n                mergedProps = nextMergedProps;\n                if (true) verifyPlainObject(mergedProps, displayName, \"mergeProps\");\n            }\n            return mergedProps;\n        };\n    };\n}\nfunction mergePropsFactory(mergeProps) {\n    return !mergeProps ? ()=>defaultMergeProps : typeof mergeProps === \"function\" ? wrapMergePropsFunc(mergeProps) : createInvalidArgFactory(mergeProps, \"mergeProps\");\n}\n// src/utils/batch.ts\nfunction defaultNoopBatch(callback) {\n    callback();\n}\n// src/utils/Subscription.ts\nfunction createListenerCollection() {\n    let first = null;\n    let last = null;\n    return {\n        clear () {\n            first = null;\n            last = null;\n        },\n        notify () {\n            defaultNoopBatch(()=>{\n                let listener = first;\n                while(listener){\n                    listener.callback();\n                    listener = listener.next;\n                }\n            });\n        },\n        get () {\n            const listeners = [];\n            let listener = first;\n            while(listener){\n                listeners.push(listener);\n                listener = listener.next;\n            }\n            return listeners;\n        },\n        subscribe (callback) {\n            let isSubscribed = true;\n            const listener = last = {\n                callback,\n                next: null,\n                prev: last\n            };\n            if (listener.prev) {\n                listener.prev.next = listener;\n            } else {\n                first = listener;\n            }\n            return function unsubscribe() {\n                if (!isSubscribed || first === null) return;\n                isSubscribed = false;\n                if (listener.next) {\n                    listener.next.prev = listener.prev;\n                } else {\n                    last = listener.prev;\n                }\n                if (listener.prev) {\n                    listener.prev.next = listener.next;\n                } else {\n                    first = listener.next;\n                }\n            };\n        }\n    };\n}\nvar nullListeners = {\n    notify () {},\n    get: ()=>[]\n};\nfunction createSubscription(store, parentSub) {\n    let unsubscribe;\n    let listeners = nullListeners;\n    let subscriptionsAmount = 0;\n    let selfSubscribed = false;\n    function addNestedSub(listener) {\n        trySubscribe();\n        const cleanupListener = listeners.subscribe(listener);\n        let removed = false;\n        return ()=>{\n            if (!removed) {\n                removed = true;\n                cleanupListener();\n                tryUnsubscribe();\n            }\n        };\n    }\n    function notifyNestedSubs() {\n        listeners.notify();\n    }\n    function handleChangeWrapper() {\n        if (subscription.onStateChange) {\n            subscription.onStateChange();\n        }\n    }\n    function isSubscribed() {\n        return selfSubscribed;\n    }\n    function trySubscribe() {\n        subscriptionsAmount++;\n        if (!unsubscribe) {\n            unsubscribe = parentSub ? parentSub.addNestedSub(handleChangeWrapper) : store.subscribe(handleChangeWrapper);\n            listeners = createListenerCollection();\n        }\n    }\n    function tryUnsubscribe() {\n        subscriptionsAmount--;\n        if (unsubscribe && subscriptionsAmount === 0) {\n            unsubscribe();\n            unsubscribe = void 0;\n            listeners.clear();\n            listeners = nullListeners;\n        }\n    }\n    function trySubscribeSelf() {\n        if (!selfSubscribed) {\n            selfSubscribed = true;\n            trySubscribe();\n        }\n    }\n    function tryUnsubscribeSelf() {\n        if (selfSubscribed) {\n            selfSubscribed = false;\n            tryUnsubscribe();\n        }\n    }\n    const subscription = {\n        addNestedSub,\n        notifyNestedSubs,\n        handleChangeWrapper,\n        isSubscribed,\n        trySubscribe: trySubscribeSelf,\n        tryUnsubscribe: tryUnsubscribeSelf,\n        getListeners: ()=>listeners\n    };\n    return subscription;\n}\n// src/utils/useIsomorphicLayoutEffect.ts\nvar canUseDOM = ()=>!!( false && 0);\nvar isDOM = /* @__PURE__ */ canUseDOM();\nvar isRunningInReactNative = ()=>typeof navigator !== \"undefined\" && navigator.product === \"ReactNative\";\nvar isReactNative = /* @__PURE__ */ isRunningInReactNative();\nvar getUseIsomorphicLayoutEffect = ()=>isDOM || isReactNative ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\nvar useIsomorphicLayoutEffect = /* @__PURE__ */ getUseIsomorphicLayoutEffect();\n// src/utils/shallowEqual.ts\nfunction is(x, y) {\n    if (x === y) {\n        return x !== 0 || y !== 0 || 1 / x === 1 / y;\n    } else {\n        return x !== x && y !== y;\n    }\n}\nfunction shallowEqual(objA, objB) {\n    if (is(objA, objB)) return true;\n    if (typeof objA !== \"object\" || objA === null || typeof objB !== \"object\" || objB === null) {\n        return false;\n    }\n    const keysA = Object.keys(objA);\n    const keysB = Object.keys(objB);\n    if (keysA.length !== keysB.length) return false;\n    for(let i = 0; i < keysA.length; i++){\n        if (!Object.prototype.hasOwnProperty.call(objB, keysA[i]) || !is(objA[keysA[i]], objB[keysA[i]])) {\n            return false;\n        }\n    }\n    return true;\n}\n// src/utils/hoistStatics.ts\nvar REACT_STATICS = {\n    childContextTypes: true,\n    contextType: true,\n    contextTypes: true,\n    defaultProps: true,\n    displayName: true,\n    getDefaultProps: true,\n    getDerivedStateFromError: true,\n    getDerivedStateFromProps: true,\n    mixins: true,\n    propTypes: true,\n    type: true\n};\nvar KNOWN_STATICS = {\n    name: true,\n    length: true,\n    prototype: true,\n    caller: true,\n    callee: true,\n    arguments: true,\n    arity: true\n};\nvar FORWARD_REF_STATICS = {\n    $$typeof: true,\n    render: true,\n    defaultProps: true,\n    displayName: true,\n    propTypes: true\n};\nvar MEMO_STATICS = {\n    $$typeof: true,\n    compare: true,\n    defaultProps: true,\n    displayName: true,\n    propTypes: true,\n    type: true\n};\nvar TYPE_STATICS = {\n    [ForwardRef]: FORWARD_REF_STATICS,\n    [Memo]: MEMO_STATICS\n};\nfunction getStatics(component) {\n    if (isMemo(component)) {\n        return MEMO_STATICS;\n    }\n    return TYPE_STATICS[component[\"$$typeof\"]] || REACT_STATICS;\n}\nvar defineProperty = Object.defineProperty;\nvar getOwnPropertyNames = Object.getOwnPropertyNames;\nvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\nvar getPrototypeOf = Object.getPrototypeOf;\nvar objectPrototype = Object.prototype;\nfunction hoistNonReactStatics(targetComponent, sourceComponent) {\n    if (typeof sourceComponent !== \"string\") {\n        if (objectPrototype) {\n            const inheritedComponent = getPrototypeOf(sourceComponent);\n            if (inheritedComponent && inheritedComponent !== objectPrototype) {\n                hoistNonReactStatics(targetComponent, inheritedComponent);\n            }\n        }\n        let keys = getOwnPropertyNames(sourceComponent);\n        if (getOwnPropertySymbols) {\n            keys = keys.concat(getOwnPropertySymbols(sourceComponent));\n        }\n        const targetStatics = getStatics(targetComponent);\n        const sourceStatics = getStatics(sourceComponent);\n        for(let i = 0; i < keys.length; ++i){\n            const key = keys[i];\n            if (!KNOWN_STATICS[key] && !(sourceStatics && sourceStatics[key]) && !(targetStatics && targetStatics[key])) {\n                const descriptor = getOwnPropertyDescriptor(sourceComponent, key);\n                try {\n                    defineProperty(targetComponent, key, descriptor);\n                } catch (e) {}\n            }\n        }\n    }\n    return targetComponent;\n}\n// src/components/Context.ts\nvar ContextKey = /* @__PURE__ */ Symbol.for(`react-redux-context`);\nvar gT = typeof globalThis !== \"undefined\" ? globalThis : /* fall back to a per-module scope (pre-8.1 behaviour) if `globalThis` is not available */ {};\nfunction getContext() {\n    if (!react__WEBPACK_IMPORTED_MODULE_0__.createContext) return {};\n    const contextMap = gT[ContextKey] ??= /* @__PURE__ */ new Map();\n    let realContext = contextMap.get(react__WEBPACK_IMPORTED_MODULE_0__.createContext);\n    if (!realContext) {\n        realContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\n        if (true) {\n            realContext.displayName = \"ReactRedux\";\n        }\n        contextMap.set(react__WEBPACK_IMPORTED_MODULE_0__.createContext, realContext);\n    }\n    return realContext;\n}\nvar ReactReduxContext = /* @__PURE__ */ getContext();\n// src/components/connect.tsx\nvar NO_SUBSCRIPTION_ARRAY = [\n    null,\n    null\n];\nvar stringifyComponent = (Comp)=>{\n    try {\n        return JSON.stringify(Comp);\n    } catch (err) {\n        return String(Comp);\n    }\n};\nfunction useIsomorphicLayoutEffectWithArgs(effectFunc, effectArgs, dependencies) {\n    useIsomorphicLayoutEffect(()=>effectFunc(...effectArgs), dependencies);\n}\nfunction captureWrapperProps(lastWrapperProps, lastChildProps, renderIsScheduled, wrapperProps, childPropsFromStoreUpdate, notifyNestedSubs) {\n    lastWrapperProps.current = wrapperProps;\n    renderIsScheduled.current = false;\n    if (childPropsFromStoreUpdate.current) {\n        childPropsFromStoreUpdate.current = null;\n        notifyNestedSubs();\n    }\n}\nfunction subscribeUpdates(shouldHandleStateChanges, store, subscription, childPropsSelector, lastWrapperProps, lastChildProps, renderIsScheduled, isMounted, childPropsFromStoreUpdate, notifyNestedSubs, additionalSubscribeListener) {\n    if (!shouldHandleStateChanges) return ()=>{};\n    let didUnsubscribe = false;\n    let lastThrownError = null;\n    const checkForUpdates = ()=>{\n        if (didUnsubscribe || !isMounted.current) {\n            return;\n        }\n        const latestStoreState = store.getState();\n        let newChildProps, error;\n        try {\n            newChildProps = childPropsSelector(latestStoreState, lastWrapperProps.current);\n        } catch (e) {\n            error = e;\n            lastThrownError = e;\n        }\n        if (!error) {\n            lastThrownError = null;\n        }\n        if (newChildProps === lastChildProps.current) {\n            if (!renderIsScheduled.current) {\n                notifyNestedSubs();\n            }\n        } else {\n            lastChildProps.current = newChildProps;\n            childPropsFromStoreUpdate.current = newChildProps;\n            renderIsScheduled.current = true;\n            additionalSubscribeListener();\n        }\n    };\n    subscription.onStateChange = checkForUpdates;\n    subscription.trySubscribe();\n    checkForUpdates();\n    const unsubscribeWrapper = ()=>{\n        didUnsubscribe = true;\n        subscription.tryUnsubscribe();\n        subscription.onStateChange = null;\n        if (lastThrownError) {\n            throw lastThrownError;\n        }\n    };\n    return unsubscribeWrapper;\n}\nfunction strictEqual(a, b) {\n    return a === b;\n}\nvar hasWarnedAboutDeprecatedPureOption = false;\nfunction connect(mapStateToProps, mapDispatchToProps, mergeProps, { // The `pure` option has been removed, so TS doesn't like us destructuring this to check its existence.\n// @ts-ignore\npure, areStatesEqual = strictEqual, areOwnPropsEqual = shallowEqual, areStatePropsEqual = shallowEqual, areMergedPropsEqual = shallowEqual, // use React's forwardRef to expose a ref of the wrapped component\nforwardRef = false, // the context consumer to use\ncontext = ReactReduxContext } = {}) {\n    if (true) {\n        if (pure !== void 0 && !hasWarnedAboutDeprecatedPureOption) {\n            hasWarnedAboutDeprecatedPureOption = true;\n            warning('The `pure` option has been removed. `connect` is now always a \"pure/memoized\" component');\n        }\n    }\n    const Context = context;\n    const initMapStateToProps = mapStateToPropsFactory(mapStateToProps);\n    const initMapDispatchToProps = mapDispatchToPropsFactory(mapDispatchToProps);\n    const initMergeProps = mergePropsFactory(mergeProps);\n    const shouldHandleStateChanges = Boolean(mapStateToProps);\n    const wrapWithConnect = (WrappedComponent)=>{\n        if (true) {\n            const isValid = /* @__PURE__ */ isValidElementType(WrappedComponent);\n            if (!isValid) throw new Error(`You must pass a component to the function returned by connect. Instead received ${stringifyComponent(WrappedComponent)}`);\n        }\n        const wrappedComponentName = WrappedComponent.displayName || WrappedComponent.name || \"Component\";\n        const displayName = `Connect(${wrappedComponentName})`;\n        const selectorFactoryOptions = {\n            shouldHandleStateChanges,\n            displayName,\n            wrappedComponentName,\n            WrappedComponent,\n            // @ts-ignore\n            initMapStateToProps,\n            initMapDispatchToProps,\n            initMergeProps,\n            areStatesEqual,\n            areStatePropsEqual,\n            areOwnPropsEqual,\n            areMergedPropsEqual\n        };\n        function ConnectFunction(props) {\n            const [propsContext, reactReduxForwardedRef, wrapperProps] = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n                const { reactReduxForwardedRef: reactReduxForwardedRef2, ...wrapperProps2 } = props;\n                return [\n                    props.context,\n                    reactReduxForwardedRef2,\n                    wrapperProps2\n                ];\n            }, [\n                props\n            ]);\n            const ContextToUse = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n                let ResultContext = Context;\n                if (propsContext?.Consumer) {\n                    if (true) {\n                        const isValid = /* @__PURE__ */ isContextConsumer(// @ts-ignore\n                        /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(propsContext.Consumer, null));\n                        if (!isValid) {\n                            throw new Error(\"You must pass a valid React context consumer as `props.context`\");\n                        }\n                        ResultContext = propsContext;\n                    }\n                }\n                return ResultContext;\n            }, [\n                propsContext,\n                Context\n            ]);\n            const contextValue = react__WEBPACK_IMPORTED_MODULE_0__.useContext(ContextToUse);\n            const didStoreComeFromProps = Boolean(props.store) && Boolean(props.store.getState) && Boolean(props.store.dispatch);\n            const didStoreComeFromContext = Boolean(contextValue) && Boolean(contextValue.store);\n            if ( true && !didStoreComeFromProps && !didStoreComeFromContext) {\n                throw new Error(`Could not find \"store\" in the context of \"${displayName}\". Either wrap the root component in a <Provider>, or pass a custom React context provider to <Provider> and the corresponding React context consumer to ${displayName} in connect options.`);\n            }\n            const store = didStoreComeFromProps ? props.store : contextValue.store;\n            const getServerState = didStoreComeFromContext ? contextValue.getServerState : store.getState;\n            const childPropsSelector = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n                return finalPropsSelectorFactory(store.dispatch, selectorFactoryOptions);\n            }, [\n                store\n            ]);\n            const [subscription, notifyNestedSubs] = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n                if (!shouldHandleStateChanges) return NO_SUBSCRIPTION_ARRAY;\n                const subscription2 = createSubscription(store, didStoreComeFromProps ? void 0 : contextValue.subscription);\n                const notifyNestedSubs2 = subscription2.notifyNestedSubs.bind(subscription2);\n                return [\n                    subscription2,\n                    notifyNestedSubs2\n                ];\n            }, [\n                store,\n                didStoreComeFromProps,\n                contextValue\n            ]);\n            const overriddenContextValue = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n                if (didStoreComeFromProps) {\n                    return contextValue;\n                }\n                return {\n                    ...contextValue,\n                    subscription\n                };\n            }, [\n                didStoreComeFromProps,\n                contextValue,\n                subscription\n            ]);\n            const lastChildProps = react__WEBPACK_IMPORTED_MODULE_0__.useRef(void 0);\n            const lastWrapperProps = react__WEBPACK_IMPORTED_MODULE_0__.useRef(wrapperProps);\n            const childPropsFromStoreUpdate = react__WEBPACK_IMPORTED_MODULE_0__.useRef(void 0);\n            const renderIsScheduled = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n            const isMounted = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n            const latestSubscriptionCallbackError = react__WEBPACK_IMPORTED_MODULE_0__.useRef(void 0);\n            useIsomorphicLayoutEffect(()=>{\n                isMounted.current = true;\n                return ()=>{\n                    isMounted.current = false;\n                };\n            }, []);\n            const actualChildPropsSelector = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n                const selector = ()=>{\n                    if (childPropsFromStoreUpdate.current && wrapperProps === lastWrapperProps.current) {\n                        return childPropsFromStoreUpdate.current;\n                    }\n                    return childPropsSelector(store.getState(), wrapperProps);\n                };\n                return selector;\n            }, [\n                store,\n                wrapperProps\n            ]);\n            const subscribeForReact = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n                const subscribe = (reactListener)=>{\n                    if (!subscription) {\n                        return ()=>{};\n                    }\n                    return subscribeUpdates(shouldHandleStateChanges, store, subscription, // @ts-ignore\n                    childPropsSelector, lastWrapperProps, lastChildProps, renderIsScheduled, isMounted, childPropsFromStoreUpdate, notifyNestedSubs, reactListener);\n                };\n                return subscribe;\n            }, [\n                subscription\n            ]);\n            useIsomorphicLayoutEffectWithArgs(captureWrapperProps, [\n                lastWrapperProps,\n                lastChildProps,\n                renderIsScheduled,\n                wrapperProps,\n                childPropsFromStoreUpdate,\n                notifyNestedSubs\n            ]);\n            let actualChildProps;\n            try {\n                actualChildProps = react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore(// TODO We're passing through a big wrapper that does a bunch of extra side effects besides subscribing\n                subscribeForReact, // TODO This is incredibly hacky. We've already processed the store update and calculated new child props,\n                // TODO and we're just passing that through so it triggers a re-render for us rather than relying on `uSES`.\n                actualChildPropsSelector, getServerState ? ()=>childPropsSelector(getServerState(), wrapperProps) : actualChildPropsSelector);\n            } catch (err) {\n                if (latestSubscriptionCallbackError.current) {\n                    ;\n                    err.message += `\nThe error may be correlated with this previous error:\n${latestSubscriptionCallbackError.current.stack}\n\n`;\n                }\n                throw err;\n            }\n            useIsomorphicLayoutEffect(()=>{\n                latestSubscriptionCallbackError.current = void 0;\n                childPropsFromStoreUpdate.current = void 0;\n                lastChildProps.current = actualChildProps;\n            });\n            const renderedWrappedComponent = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n                return(// @ts-ignore\n                /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(WrappedComponent, {\n                    ...actualChildProps,\n                    ref: reactReduxForwardedRef\n                }));\n            }, [\n                reactReduxForwardedRef,\n                WrappedComponent,\n                actualChildProps\n            ]);\n            const renderedChild = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n                if (shouldHandleStateChanges) {\n                    return /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(ContextToUse.Provider, {\n                        value: overriddenContextValue\n                    }, renderedWrappedComponent);\n                }\n                return renderedWrappedComponent;\n            }, [\n                ContextToUse,\n                renderedWrappedComponent,\n                overriddenContextValue\n            ]);\n            return renderedChild;\n        }\n        const _Connect = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.memo(ConnectFunction);\n        const Connect = _Connect;\n        Connect.WrappedComponent = WrappedComponent;\n        Connect.displayName = ConnectFunction.displayName = displayName;\n        if (forwardRef) {\n            const _forwarded = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function forwardConnectRef(props, ref) {\n                return /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Connect, {\n                    ...props,\n                    reactReduxForwardedRef: ref\n                });\n            });\n            const forwarded = _forwarded;\n            forwarded.displayName = displayName;\n            forwarded.WrappedComponent = WrappedComponent;\n            return /* @__PURE__ */ hoistNonReactStatics(forwarded, WrappedComponent);\n        }\n        return /* @__PURE__ */ hoistNonReactStatics(Connect, WrappedComponent);\n    };\n    return wrapWithConnect;\n}\nvar connect_default = connect;\n// src/components/Provider.tsx\nfunction Provider(providerProps) {\n    const { children, context, serverState, store } = providerProps;\n    const contextValue = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        const subscription = createSubscription(store);\n        const baseContextValue = {\n            store,\n            subscription,\n            getServerState: serverState ? ()=>serverState : void 0\n        };\n        if (false) {} else {\n            const { identityFunctionCheck = \"once\", stabilityCheck = \"once\" } = providerProps;\n            return /* @__PURE__ */ Object.assign(baseContextValue, {\n                stabilityCheck,\n                identityFunctionCheck\n            });\n        }\n    }, [\n        store,\n        serverState\n    ]);\n    const previousState = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>store.getState(), [\n        store\n    ]);\n    useIsomorphicLayoutEffect(()=>{\n        const { subscription } = contextValue;\n        subscription.onStateChange = subscription.notifyNestedSubs;\n        subscription.trySubscribe();\n        if (previousState !== store.getState()) {\n            subscription.notifyNestedSubs();\n        }\n        return ()=>{\n            subscription.tryUnsubscribe();\n            subscription.onStateChange = void 0;\n        };\n    }, [\n        contextValue,\n        previousState\n    ]);\n    const Context = context || ReactReduxContext;\n    return /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Context.Provider, {\n        value: contextValue\n    }, children);\n}\nvar Provider_default = Provider;\n// src/hooks/useReduxContext.ts\nfunction createReduxContextHook(context = ReactReduxContext) {\n    return function useReduxContext2() {\n        const contextValue = react__WEBPACK_IMPORTED_MODULE_0__.useContext(context);\n        if ( true && !contextValue) {\n            throw new Error(\"could not find react-redux context value; please ensure the component is wrapped in a <Provider>\");\n        }\n        return contextValue;\n    };\n}\nvar useReduxContext = /* @__PURE__ */ createReduxContextHook();\n// src/hooks/useStore.ts\nfunction createStoreHook(context = ReactReduxContext) {\n    const useReduxContext2 = context === ReactReduxContext ? useReduxContext : // @ts-ignore\n    createReduxContextHook(context);\n    const useStore2 = ()=>{\n        const { store } = useReduxContext2();\n        return store;\n    };\n    Object.assign(useStore2, {\n        withTypes: ()=>useStore2\n    });\n    return useStore2;\n}\nvar useStore = /* @__PURE__ */ createStoreHook();\n// src/hooks/useDispatch.ts\nfunction createDispatchHook(context = ReactReduxContext) {\n    const useStore2 = context === ReactReduxContext ? useStore : createStoreHook(context);\n    const useDispatch2 = ()=>{\n        const store = useStore2();\n        return store.dispatch;\n    };\n    Object.assign(useDispatch2, {\n        withTypes: ()=>useDispatch2\n    });\n    return useDispatch2;\n}\nvar useDispatch = /* @__PURE__ */ createDispatchHook();\n// src/hooks/useSelector.ts\n\nvar refEquality = (a, b)=>a === b;\nfunction createSelectorHook(context = ReactReduxContext) {\n    const useReduxContext2 = context === ReactReduxContext ? useReduxContext : createReduxContextHook(context);\n    const useSelector2 = (selector, equalityFnOrOptions = {})=>{\n        const { equalityFn = refEquality } = typeof equalityFnOrOptions === \"function\" ? {\n            equalityFn: equalityFnOrOptions\n        } : equalityFnOrOptions;\n        if (true) {\n            if (!selector) {\n                throw new Error(`You must pass a selector to useSelector`);\n            }\n            if (typeof selector !== \"function\") {\n                throw new Error(`You must pass a function as a selector to useSelector`);\n            }\n            if (typeof equalityFn !== \"function\") {\n                throw new Error(`You must pass a function as an equality function to useSelector`);\n            }\n        }\n        const reduxContext = useReduxContext2();\n        const { store, subscription, getServerState } = reduxContext;\n        const firstRun = react__WEBPACK_IMPORTED_MODULE_0__.useRef(true);\n        const wrappedSelector = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            [selector.name] (state) {\n                const selected = selector(state);\n                if (true) {\n                    const { devModeChecks = {} } = typeof equalityFnOrOptions === \"function\" ? {} : equalityFnOrOptions;\n                    const { identityFunctionCheck, stabilityCheck } = reduxContext;\n                    const { identityFunctionCheck: finalIdentityFunctionCheck, stabilityCheck: finalStabilityCheck } = {\n                        stabilityCheck,\n                        identityFunctionCheck,\n                        ...devModeChecks\n                    };\n                    if (finalStabilityCheck === \"always\" || finalStabilityCheck === \"once\" && firstRun.current) {\n                        const toCompare = selector(state);\n                        if (!equalityFn(selected, toCompare)) {\n                            let stack = void 0;\n                            try {\n                                throw new Error();\n                            } catch (e) {\n                                ;\n                                ({ stack } = e);\n                            }\n                            console.warn(\"Selector \" + (selector.name || \"unknown\") + \" returned a different result when called with the same parameters. This can lead to unnecessary rerenders.\\nSelectors that return a new reference (such as an object or an array) should be memoized: https://redux.js.org/usage/deriving-data-selectors#optimizing-selectors-with-memoization\", {\n                                state,\n                                selected,\n                                selected2: toCompare,\n                                stack\n                            });\n                        }\n                    }\n                    if (finalIdentityFunctionCheck === \"always\" || finalIdentityFunctionCheck === \"once\" && firstRun.current) {\n                        if (selected === state) {\n                            let stack = void 0;\n                            try {\n                                throw new Error();\n                            } catch (e) {\n                                ;\n                                ({ stack } = e);\n                            }\n                            console.warn(\"Selector \" + (selector.name || \"unknown\") + \" returned the root state when called. This can lead to unnecessary rerenders.\\nSelectors that return the entire state are almost certainly a mistake, as they will cause a rerender whenever *anything* in state changes.\", {\n                                stack\n                            });\n                        }\n                    }\n                    if (firstRun.current) firstRun.current = false;\n                }\n                return selected;\n            }\n        }[selector.name], [\n            selector\n        ]);\n        const selectedState = (0,use_sync_external_store_with_selector_js__WEBPACK_IMPORTED_MODULE_1__.useSyncExternalStoreWithSelector)(subscription.addNestedSub, store.getState, getServerState || store.getState, wrappedSelector, equalityFn);\n        react__WEBPACK_IMPORTED_MODULE_0__.useDebugValue(selectedState);\n        return selectedState;\n    };\n    Object.assign(useSelector2, {\n        withTypes: ()=>useSelector2\n    });\n    return useSelector2;\n}\nvar useSelector = /* @__PURE__ */ createSelectorHook();\n// src/exports.ts\nvar batch = defaultNoopBatch;\n //# sourceMappingURL=react-redux.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-redux/dist/react-redux.mjs\n");

/***/ })

};
;