import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { generatePasswordResetToken, sendPasswordResetEmail } from '@/lib/email'
import { z } from 'zod'

const forgotPasswordSchema = z.object({
  email: z.string().email('Invalid email address')
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email } = forgotPasswordSchema.parse(body)

    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email }
    })

    // Always return success to prevent email enumeration attacks
    // But only send email if user exists
    if (user) {
      // Check rate limiting - don't allow too frequent requests
      const recentReset = await prisma.passwordReset.findFirst({
        where: {
          userId: user.id,
          createdAt: {
            gt: new Date(Date.now() - 5 * 60 * 1000) // 5 minutes ago
          }
        }
      })

      if (recentReset) {
        // Still return success but don't send email
        return NextResponse.json({
          message: 'If an account with that email exists, we have sent a password reset link.'
        })
      }

      // Generate password reset token
      const resetToken = generatePasswordResetToken()
      const resetExpires = new Date(Date.now() + 60 * 60 * 1000) // 1 hour

      // Store password reset token
      await prisma.passwordReset.create({
        data: {
          userId: user.id,
          token: resetToken,
          expiresAt: resetExpires
        }
      })

      // Send password reset email
      try {
        await sendPasswordResetEmail(
          user.email,
          user.name || `${user.firstName} ${user.lastName}`,
          resetToken
        )
      } catch (emailError) {
        console.error('Failed to send password reset email:', emailError)
        // Don't fail the request if email fails, but log it
      }

      // Log the password reset request
      console.log(`Password reset requested for: ${user.email}`)
    }

    // Always return the same response regardless of whether user exists
    return NextResponse.json({
      message: 'If an account with that email exists, we have sent a password reset link.'
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid email address', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Forgot password error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
