"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/contracts/route";
exports.ids = ["app/api/contracts/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcontracts%2Froute&page=%2Fapi%2Fcontracts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontracts%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcontracts%2Froute&page=%2Fapi%2Fcontracts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontracts%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_proj_nextjs_saas_app_api_contracts_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/contracts/route.ts */ \"(rsc)/./app/api/contracts/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/contracts/route\",\n        pathname: \"/api/contracts\",\n        filename: \"route\",\n        bundlePath: \"app/api/contracts/route\"\n    },\n    resolvedPagePath: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\api\\\\contracts\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_proj_nextjs_saas_app_api_contracts_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/contracts/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcontracts%2Froute&page=%2Fapi%2Fcontracts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontracts%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/contracts/route.ts":
/*!************************************!*\
  !*** ./app/api/contracts/route.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/ZodError.js\");\n\n\n\n\n\n// Validation schema for contract creation/update\nconst contractSchema = zod__WEBPACK_IMPORTED_MODULE_4__.object({\n    title: zod__WEBPACK_IMPORTED_MODULE_4__.string().min(1, \"Title is required\"),\n    description: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    customerId: zod__WEBPACK_IMPORTED_MODULE_4__.string().min(1, \"Customer is required\"),\n    quotationId: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    invoiceId: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    type: zod__WEBPACK_IMPORTED_MODULE_4__[\"enum\"]([\n        \"SERVICE\",\n        \"PRODUCT\",\n        \"SUBSCRIPTION\",\n        \"MAINTENANCE\",\n        \"CONSULTING\",\n        \"OTHER\"\n    ]).default(\"SERVICE\"),\n    status: zod__WEBPACK_IMPORTED_MODULE_4__[\"enum\"]([\n        \"DRAFT\",\n        \"REVIEW\",\n        \"SENT\",\n        \"SIGNED\",\n        \"ACTIVE\",\n        \"COMPLETED\",\n        \"CANCELLED\",\n        \"EXPIRED\"\n    ]).default(\"DRAFT\"),\n    value: zod__WEBPACK_IMPORTED_MODULE_4__.number().min(0, \"Contract value must be positive\").optional().nullable(),\n    currency: zod__WEBPACK_IMPORTED_MODULE_4__.string().default(\"USD\"),\n    startDate: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    endDate: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    renewalDate: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    autoRenewal: zod__WEBPACK_IMPORTED_MODULE_4__.boolean().default(false),\n    renewalPeriod: zod__WEBPACK_IMPORTED_MODULE_4__.number().optional().nullable(),\n    terms: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    conditions: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    notes: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    templateId: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    signatureRequired: zod__WEBPACK_IMPORTED_MODULE_4__.boolean().default(true),\n    priority: zod__WEBPACK_IMPORTED_MODULE_4__[\"enum\"]([\n        \"LOW\",\n        \"MEDIUM\",\n        \"HIGH\",\n        \"URGENT\"\n    ]).default(\"MEDIUM\"),\n    tags: zod__WEBPACK_IMPORTED_MODULE_4__.array(zod__WEBPACK_IMPORTED_MODULE_4__.string()).optional().default([])\n});\n// GET /api/contracts - List contracts with filtering and pagination\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get(\"page\") || \"1\");\n        const limit = parseInt(searchParams.get(\"limit\") || \"10\");\n        const search = searchParams.get(\"search\") || \"\";\n        const status = searchParams.get(\"status\") || \"\";\n        const type = searchParams.get(\"type\") || \"\";\n        const customerId = searchParams.get(\"customerId\") || \"\";\n        const sortBy = searchParams.get(\"sortBy\") || \"createdAt\";\n        const sortOrder = searchParams.get(\"sortOrder\") || \"desc\";\n        const skip = (page - 1) * limit;\n        // Build where clause\n        const where = {\n            companyId: session.user.companyId || undefined\n        };\n        if (search) {\n            where.OR = [\n                {\n                    title: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    description: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    contractNumber: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    customer: {\n                        name: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    }\n                },\n                {\n                    customer: {\n                        company: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    }\n                }\n            ];\n        }\n        if (status) {\n            where.status = status;\n        }\n        if (type) {\n            where.type = type;\n        }\n        if (customerId) {\n            where.customerId = customerId;\n        }\n        // Get contracts with pagination\n        const [contracts, total] = await Promise.all([\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.contract.findMany({\n                where,\n                skip,\n                take: limit,\n                orderBy: {\n                    [sortBy]: sortOrder\n                },\n                include: {\n                    customer: {\n                        select: {\n                            id: true,\n                            name: true,\n                            email: true,\n                            company: true\n                        }\n                    },\n                    quotation: {\n                        select: {\n                            id: true,\n                            quotationNumber: true,\n                            title: true\n                        }\n                    },\n                    invoice: {\n                        select: {\n                            id: true,\n                            invoiceNumber: true\n                        }\n                    },\n                    createdBy: {\n                        select: {\n                            name: true,\n                            email: true\n                        }\n                    },\n                    assignedTo: {\n                        select: {\n                            name: true,\n                            email: true\n                        }\n                    },\n                    _count: {\n                        select: {\n                            activities: true,\n                            signatures: true,\n                            documents: true\n                        }\n                    }\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.contract.count({\n                where\n            })\n        ]);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            contracts,\n            pagination: {\n                page,\n                limit,\n                total,\n                pages: Math.ceil(total / limit)\n            }\n        });\n    } catch (error) {\n        console.error(\"Error fetching contracts:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to fetch contracts\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/contracts - Create new contract\nasync function POST(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const validatedData = contractSchema.parse(body);\n        // Generate contract number\n        const currentYear = new Date().getFullYear();\n        const lastContract = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.contract.findFirst({\n            where: {\n                companyId: session.user.companyId || undefined,\n                contractNumber: {\n                    startsWith: `CON-${currentYear}-`\n                }\n            },\n            orderBy: {\n                contractNumber: \"desc\"\n            }\n        });\n        let nextNumber = 1;\n        if (lastContract) {\n            const lastNumber = parseInt(lastContract.contractNumber.split(\"-\")[2]);\n            nextNumber = lastNumber + 1;\n        }\n        const contractNumber = `CON-${currentYear}-${nextNumber.toString().padStart(4, \"0\")}`;\n        // Prepare contract data\n        const contractData = {\n            ...validatedData,\n            contractNumber,\n            companyId: session.user.companyId,\n            createdById: session.user.id,\n            assignedToId: session.user.id // Default to creator\n        };\n        if (validatedData.startDate) {\n            contractData.startDate = new Date(validatedData.startDate);\n        }\n        if (validatedData.endDate) {\n            contractData.endDate = new Date(validatedData.endDate);\n        }\n        if (validatedData.renewalDate) {\n            contractData.renewalDate = new Date(validatedData.renewalDate);\n        }\n        // Create contract in a transaction\n        const contract = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.$transaction(async (tx)=>{\n            const newContract = await tx.contract.create({\n                data: contractData,\n                include: {\n                    customer: {\n                        select: {\n                            id: true,\n                            name: true,\n                            email: true,\n                            companyName: true\n                        }\n                    },\n                    quotation: {\n                        select: {\n                            id: true,\n                            quotationNumber: true,\n                            title: true\n                        }\n                    },\n                    invoice: {\n                        select: {\n                            id: true,\n                            invoiceNumber: true\n                        }\n                    },\n                    template: {\n                        select: {\n                            id: true,\n                            name: true,\n                            type: true\n                        }\n                    },\n                    createdBy: {\n                        select: {\n                            name: true,\n                            email: true\n                        }\n                    },\n                    assignedTo: {\n                        select: {\n                            name: true,\n                            email: true\n                        }\n                    }\n                }\n            });\n            // Log activity\n            await tx.activity.create({\n                data: {\n                    type: \"NOTE\",\n                    title: \"Contract Created\",\n                    description: `Contract \"${newContract.title}\" (${contractNumber}) was created`,\n                    contractId: newContract.id,\n                    customerId: newContract.customerId,\n                    quotationId: newContract.quotationId,\n                    invoiceId: newContract.invoiceId,\n                    companyId: session.user.companyId,\n                    createdById: session.user.id\n                }\n            });\n            return newContract;\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(contract, {\n            status: 201\n        });\n    } catch (error) {\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_5__.ZodError) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Validation failed\",\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        console.error(\"Error creating contract:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to create contract\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/contracts/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/github */ \"(rsc)/./node_modules/next-auth/providers/github.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n\nconst authOptions = {\n    // Using JWT strategy instead of database adapter for now\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                try {\n                    if (!credentials?.email || !credentials?.password) {\n                        console.log(\"Missing credentials\");\n                        return null;\n                    }\n                    console.log(\"Attempting to authenticate user:\", credentials.email);\n                    const user = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.findUnique({\n                        where: {\n                            email: credentials.email\n                        },\n                        select: {\n                            id: true,\n                            email: true,\n                            name: true,\n                            password: true,\n                            role: true,\n                            companyId: true\n                        }\n                    });\n                    // Get the company ID - either as member or owner\n                    let finalCompanyId = user?.companyId;\n                    if (!finalCompanyId && user) {\n                        const ownedCompany = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.company.findFirst({\n                            where: {\n                                ownerId: user.id\n                            },\n                            select: {\n                                id: true\n                            }\n                        });\n                        finalCompanyId = ownedCompany?.id;\n                        // If user is company owner, update their companyId for future queries\n                        if (finalCompanyId) {\n                            await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                                where: {\n                                    id: user.id\n                                },\n                                data: {\n                                    companyId: finalCompanyId\n                                }\n                            });\n                        }\n                    }\n                    if (!user) {\n                        console.log(\"User not found:\", credentials.email);\n                        return null;\n                    }\n                    if (!user.password) {\n                        console.log(\"User has no password set:\", credentials.email);\n                        return null;\n                    }\n                    const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_3___default().compare(credentials.password, user.password);\n                    if (!isPasswordValid) {\n                        console.log(\"Invalid password for user:\", credentials.email);\n                        return null;\n                    }\n                    // Update last login\n                    await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                        where: {\n                            id: user.id\n                        },\n                        data: {\n                            lastLoginAt: new Date(),\n                            loginCount: {\n                                increment: 1\n                            }\n                        }\n                    });\n                    console.log(\"User authenticated successfully:\", user.email);\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name,\n                        role: user.role,\n                        companyId: finalCompanyId\n                    };\n                } catch (error) {\n                    console.error(\"Authentication error:\", error);\n                    return null;\n                }\n            }\n        }),\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            clientId: process.env.GITHUB_CLIENT_ID,\n            clientSecret: process.env.GITHUB_CLIENT_SECRET\n        })\n    ],\n    session: {\n        strategy: \"jwt\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                console.log(\"JWT callback - user data:\", {\n                    id: user.id,\n                    email: user.email,\n                    role: user.role,\n                    companyId: user.companyId\n                });\n                token.role = user.role;\n                token.companyId = user.companyId;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n                session.user.companyId = token.companyId;\n                console.log(\"Session callback - final session:\", {\n                    id: session.user.id,\n                    email: session.user.email,\n                    role: session.user.role,\n                    companyId: session.user.companyId\n                });\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        signUp: \"/auth/signup\",\n        error: \"/auth/error\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zYWFzLWFwcC8uL2xpYi9wcmlzbWEudHM/OTgyMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/zod","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/lru-cache","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcontracts%2Froute&page=%2Fapi%2Fcontracts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontracts%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();