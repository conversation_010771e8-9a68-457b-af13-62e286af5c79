"use strict";(()=>{var e={};e.id=8326,e.ids=[8326],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},73746:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>f,originalPathname:()=>_,patchFetch:()=>b,requestAsyncStorage:()=>w,routeModule:()=>g,serverHooks:()=>E,staticGenerationAsyncStorage:()=>v,staticGenerationBailout:()=>h});var r={};a.r(r),a.d(r,{GET:()=>I,POST:()=>y});var n=a(95419),o=a(69108),i=a(99678),s=a(78070),l=a(81355),c=a(3205),u=a(9108),d=a(25252),p=a(52178);let m=d.Ry({title:d.Z_().min(1,"Title is required"),description:d.Z_().optional().nullable(),customerId:d.Z_().min(1,"Customer is required"),quotationId:d.Z_().optional().nullable(),invoiceId:d.Z_().optional().nullable(),type:d.Km(["SERVICE","PRODUCT","SUBSCRIPTION","MAINTENANCE","CONSULTING","OTHER"]).default("SERVICE"),status:d.Km(["DRAFT","REVIEW","SENT","SIGNED","ACTIVE","COMPLETED","CANCELLED","EXPIRED"]).default("DRAFT"),value:d.Rx().min(0,"Contract value must be positive").optional().nullable(),currency:d.Z_().default("USD"),startDate:d.Z_().optional().nullable(),endDate:d.Z_().optional().nullable(),renewalDate:d.Z_().optional().nullable(),autoRenewal:d.O7().default(!1),renewalPeriod:d.Rx().optional().nullable(),terms:d.Z_().optional().nullable(),conditions:d.Z_().optional().nullable(),notes:d.Z_().optional().nullable(),templateId:d.Z_().optional().nullable(),signatureRequired:d.O7().default(!0),priority:d.Km(["LOW","MEDIUM","HIGH","URGENT"]).default("MEDIUM"),tags:d.IX(d.Z_()).optional().default([])});async function I(e){try{let t=await (0,l.getServerSession)(c.L);if(!t?.user?.id)return s.Z.json({error:"Unauthorized"},{status:401});let{searchParams:a}=new URL(e.url),r=parseInt(a.get("page")||"1"),n=parseInt(a.get("limit")||"10"),o=a.get("search")||"",i=a.get("status")||"",d=a.get("type")||"",p=a.get("customerId")||"",m=a.get("sortBy")||"createdAt",I=a.get("sortOrder")||"desc",y=(r-1)*n,g={companyId:t.user.companyId||void 0};o&&(g.OR=[{title:{contains:o,mode:"insensitive"}},{description:{contains:o,mode:"insensitive"}},{contractNumber:{contains:o,mode:"insensitive"}},{customer:{name:{contains:o,mode:"insensitive"}}},{customer:{company:{contains:o,mode:"insensitive"}}}]),i&&(g.status=i),d&&(g.type=d),p&&(g.customerId=p);let[w,v]=await Promise.all([u._.contract.findMany({where:g,skip:y,take:n,orderBy:{[m]:I},include:{customer:{select:{id:!0,name:!0,email:!0,company:!0}},quotation:{select:{id:!0,quotationNumber:!0,title:!0}},invoice:{select:{id:!0,invoiceNumber:!0}},createdBy:{select:{name:!0,email:!0}},assignedTo:{select:{name:!0,email:!0}},_count:{select:{activities:!0,signatures:!0,documents:!0}}}}),u._.contract.count({where:g})]);return s.Z.json({contracts:w,pagination:{page:r,limit:n,total:v,pages:Math.ceil(v/n)}})}catch(e){return console.error("Error fetching contracts:",e),s.Z.json({error:"Failed to fetch contracts"},{status:500})}}async function y(e){try{let t=await (0,l.getServerSession)(c.L);if(!t?.user?.id)return s.Z.json({error:"Unauthorized"},{status:401});let a=await e.json(),r=m.parse(a),n=new Date().getFullYear(),o=await u._.contract.findFirst({where:{companyId:t.user.companyId||void 0,contractNumber:{startsWith:`CON-${n}-`}},orderBy:{contractNumber:"desc"}}),i=1;o&&(i=parseInt(o.contractNumber.split("-")[2])+1);let d=`CON-${n}-${i.toString().padStart(4,"0")}`,p={...r,contractNumber:d,companyId:t.user.companyId,createdById:t.user.id,assignedToId:t.user.id};r.startDate&&(p.startDate=new Date(r.startDate)),r.endDate&&(p.endDate=new Date(r.endDate)),r.renewalDate&&(p.renewalDate=new Date(r.renewalDate));let I=await u._.$transaction(async e=>{let a=await e.contract.create({data:p,include:{customer:{select:{id:!0,name:!0,email:!0,companyName:!0}},quotation:{select:{id:!0,quotationNumber:!0,title:!0}},invoice:{select:{id:!0,invoiceNumber:!0}},template:{select:{id:!0,name:!0,type:!0}},createdBy:{select:{name:!0,email:!0}},assignedTo:{select:{name:!0,email:!0}}}});return await e.activity.create({data:{type:"NOTE",title:"Contract Created",description:`Contract "${a.title}" (${d}) was created`,contractId:a.id,customerId:a.customerId,quotationId:a.quotationId,invoiceId:a.invoiceId,companyId:t.user.companyId,createdById:t.user.id}}),a});return s.Z.json(I,{status:201})}catch(e){if(e instanceof p.jm)return s.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("Error creating contract:",e),s.Z.json({error:"Failed to create contract"},{status:500})}}let g=new n.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/contracts/route",pathname:"/api/contracts",filename:"route",bundlePath:"app/api/contracts/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\contracts\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:w,staticGenerationAsyncStorage:v,serverHooks:E,headerHooks:f,staticGenerationBailout:h}=g,_="/api/contracts/route";function b(){return(0,i.patchFetch)({serverHooks:E,staticGenerationAsyncStorage:v})}},3205:(e,t,a)=>{a.d(t,{L:()=>c});var r=a(86485),n=a(10375),o=a(50694),i=a(6521),s=a.n(i),l=a(9108);let c={providers:[(0,r.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await l._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),a=t?.companyId;if(!a&&t){let e=await l._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(a=e?.id)&&await l._.user.update({where:{id:t.id},data:{companyId:a}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await s().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await l._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:a}}catch(e){return console.error("Authentication error:",e),null}}}),(0,n.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,o.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,a)=>{a.d(t,{_:()=>n});let r=require("@prisma/client"),n=globalThis.prisma??new r.PrismaClient}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[1638,6206,6521,2455,4520,5252],()=>a(73746));module.exports=r})();