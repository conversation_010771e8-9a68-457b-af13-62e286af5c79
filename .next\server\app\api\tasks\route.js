"use strict";(()=>{var e={};e.id=3495,e.ids=[3495],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},76707:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>_,originalPathname:()=>T,patchFetch:()=>E,requestAsyncStorage:()=>f,routeModule:()=>g,serverHooks:()=>h,staticGenerationAsyncStorage:()=>w,staticGenerationBailout:()=>x});var r={};a.r(r),a.d(r,{GET:()=>y,POST:()=>I});var s=a(95419),o=a(69108),n=a(99678),i=a(78070),d=a(81355),l=a(3205),u=a(9108),c=a(25252),p=a(52178);let m=c.Ry({title:c.Z_().min(1,"Title is required"),description:c.Z_().optional(),status:c.Km(["TODO","IN_PROGRESS","REVIEW","DONE","CANCELLED"]).default("TODO"),priority:c.Km(["LOW","MEDIUM","HIGH","URGENT","CRITICAL"]).default("MEDIUM"),dueDate:c.Z_().optional(),startDate:c.Z_().optional(),assignedToId:c.Z_().optional(),leadId:c.Z_().optional(),customerId:c.Z_().optional(),quotationId:c.Z_().optional(),invoiceId:c.Z_().optional(),contractId:c.Z_().optional(),tags:c.IX(c.Z_()).default([]),customFields:c.IM(c.Yj()).default({}),type:c.Z_().default("GENERAL"),category:c.Z_().optional(),estimatedHours:c.Rx().optional(),actualHours:c.Rx().optional()});async function y(e){try{let t=await (0,d.getServerSession)(l.L);if(!t?.user?.id||!t?.user?.companyId)return i.Z.json({error:"Unauthorized"},{status:401});let{searchParams:a}=new URL(e.url),r=a.get("status"),s=a.get("priority"),o=a.get("assignedToId"),n=a.get("leadId"),c=a.get("customerId"),p=a.get("dueAfter"),m=a.get("dueBefore"),y=a.get("search"),I=a.get("type"),g=a.get("category"),f=parseInt(a.get("page")||"1"),w=parseInt(a.get("limit")||"20"),h=a.get("sortBy")||"createdAt",_=a.get("sortOrder")||"desc",x={companyId:t.user.companyId};r&&(x.status=r),s&&(x.priority=s),o&&(x.assignedToId=o),n&&(x.leadId=n),c&&(x.customerId=c),I&&(x.type=I),g&&(x.category=g),(p||m)&&(x.dueDate={},p&&(x.dueDate.gte=new Date(p)),m&&(x.dueDate.lte=new Date(m))),y&&(x.OR=[{title:{contains:y,mode:"insensitive"}},{description:{contains:y,mode:"insensitive"}}]);let[T,E]=await Promise.all([u._.task.findMany({where:x,include:{assignedTo:{select:{id:!0,name:!0,email:!0}},createdBy:{select:{id:!0,name:!0,email:!0}},lead:{select:{id:!0,firstName:!0,lastName:!0,companyName:!0}},customer:{select:{id:!0,name:!0,email:!0,company:!0}},quotation:{select:{id:!0,title:!0,total:!0}},invoice:{select:{id:!0,invoiceNumber:!0,total:!0}},contract:{select:{id:!0,title:!0,status:!0}}},orderBy:{[h]:_},skip:(f-1)*w,take:w}),u._.task.count({where:x})]),Z=await u._.task.groupBy({by:["status"],where:{companyId:t.user.companyId},_count:{id:!0}}),D={total:E,byStatus:Z.reduce((e,t)=>(e[t.status]=t._count.id,e),{})};return i.Z.json({tasks:T,pagination:{page:f,limit:w,total:E,pages:Math.ceil(E/w)},stats:D})}catch(e){return console.error("Error fetching tasks:",e),i.Z.json({error:"Failed to fetch tasks"},{status:500})}}async function I(e){try{let t=await (0,d.getServerSession)(l.L);if(!t?.user?.id||!t?.user?.companyId)return i.Z.json({error:"Unauthorized"},{status:401});let a=await e.json(),r=m.parse(a);if(r.assignedToId&&!await u._.user.findFirst({where:{id:r.assignedToId,companyId:t.user.companyId}}))return i.Z.json({error:"Assigned user not found or not in your company"},{status:400});if(r.leadId&&!await u._.lead.findFirst({where:{id:r.leadId,companyId:t.user.companyId}}))return i.Z.json({error:"Lead not found or not in your company"},{status:400});if(r.customerId&&!await u._.customer.findFirst({where:{id:r.customerId,companyId:t.user.companyId}}))return i.Z.json({error:"Customer not found or not in your company"},{status:400});let s=await u._.task.create({data:{...r,dueDate:r.dueDate?new Date(r.dueDate):null,startDate:r.startDate?new Date(r.startDate):null,companyId:t.user.companyId,createdById:t.user.id,assignedToId:r.assignedToId||t.user.id},include:{assignedTo:{select:{id:!0,name:!0,email:!0}},createdBy:{select:{id:!0,name:!0,email:!0}},lead:{select:{id:!0,firstName:!0,lastName:!0,companyName:!0}},customer:{select:{id:!0,name:!0,email:!0,company:!0}}}});return i.Z.json({task:s},{status:201})}catch(e){if(e instanceof p.jm)return i.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("Error creating task:",e),i.Z.json({error:"Failed to create task"},{status:500})}}let g=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/tasks/route",pathname:"/api/tasks",filename:"route",bundlePath:"app/api/tasks/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\tasks\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:f,staticGenerationAsyncStorage:w,serverHooks:h,headerHooks:_,staticGenerationBailout:x}=g,T="/api/tasks/route";function E(){return(0,n.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:w})}},3205:(e,t,a)=>{a.d(t,{L:()=>l});var r=a(86485),s=a(10375),o=a(50694),n=a(6521),i=a.n(n),d=a(9108);let l={providers:[(0,r.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await d._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),a=t?.companyId;if(!a&&t){let e=await d._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(a=e?.id)&&await d._.user.update({where:{id:t.id},data:{companyId:a}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await i().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await d._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:a}}catch(e){return console.error("Authentication error:",e),null}}}),(0,s.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,o.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,a)=>{a.d(t,{_:()=>s});let r=require("@prisma/client"),s=globalThis.prisma??new r.PrismaClient}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[1638,6206,6521,2455,4520,5252],()=>a(76707));module.exports=r})();