(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8741],{90998:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]])},72894:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},35817:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},99670:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},76637:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]])},74056:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},64280:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},41827:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},49036:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},67972:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},82549:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},24033:function(e,t,n){e.exports=n(15313)},99808:function(e,t,n){"use strict";/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=n(2265),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=r.useState,u=r.useEffect,a=r.useLayoutEffect,s=r.useDebugValue;function l(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!o(e,n)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=i({inst:{value:n,getSnapshot:t}}),o=r[0].inst,c=r[1];return a(function(){o.value=n,o.getSnapshot=t,l(o)&&c({inst:o})},[e,n,t]),u(function(){return l(o)&&c({inst:o}),e(function(){l(o)&&c({inst:o})})},[e]),s(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:c},26272:function(e,t,n){"use strict";e.exports=n(99808)},61465:function(e,t,n){"use strict";n.d(t,{NY:function(){return b},Ee:function(){return M},fC:function(){return N}});var r=n(2265),o=n(56989),i=n(16459),u=n(51030),a=n(9381),s=n(26272);function l(){return()=>{}}var c=n(57437),d="Avatar",[f,p]=(0,o.b)(d),[y,m]=f(d),g=r.forwardRef((e,t)=>{let{__scopeAvatar:n,...o}=e,[i,u]=r.useState("idle");return(0,c.jsx)(y,{scope:n,imageLoadingStatus:i,onImageLoadingStatusChange:u,children:(0,c.jsx)(a.WV.span,{...o,ref:t})})});g.displayName=d;var v="AvatarImage",h=r.forwardRef((e,t)=>{let{__scopeAvatar:n,src:o,onLoadingStatusChange:d=()=>{},...f}=e,p=m(v,n),y=function(e,{referrerPolicy:t,crossOrigin:n}){let o=(0,s.useSyncExternalStore)(l,()=>!0,()=>!1),i=r.useRef(null),a=o?(i.current||(i.current=new window.Image),i.current):null,[c,d]=r.useState(()=>k(a,e));return(0,u.b)(()=>{d(k(a,e))},[a,e]),(0,u.b)(()=>{let e=e=>()=>{d(e)};if(!a)return;let r=e("loaded"),o=e("error");return a.addEventListener("load",r),a.addEventListener("error",o),t&&(a.referrerPolicy=t),"string"==typeof n&&(a.crossOrigin=n),()=>{a.removeEventListener("load",r),a.removeEventListener("error",o)}},[a,n,t]),c}(o,f),g=(0,i.W)(e=>{d(e),p.onImageLoadingStatusChange(e)});return(0,u.b)(()=>{"idle"!==y&&g(y)},[y,g]),"loaded"===y?(0,c.jsx)(a.WV.img,{...f,ref:t,src:o}):null});h.displayName=v;var x="AvatarFallback",w=r.forwardRef((e,t)=>{let{__scopeAvatar:n,delayMs:o,...i}=e,u=m(x,n),[s,l]=r.useState(void 0===o);return r.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>l(!0),o);return()=>window.clearTimeout(e)}},[o]),s&&"loaded"!==u.imageLoadingStatus?(0,c.jsx)(a.WV.span,{...i,ref:t}):null});function k(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}w.displayName=x;var N=g,M=h,b=w},28712:function(e,t,n){"use strict";n.d(t,{Dx:function(){return er},VY:function(){return en},aV:function(){return et},dk:function(){return eo},fC:function(){return J},h_:function(){return ee},x8:function(){return ei},xz:function(){return Q}});var r=n(2265),o=n(85744),i=n(42210),u=n(56989),a=n(20966),s=n(73763),l=n(79249),c=n(52759),d=n(52730),f=n(85606),p=n(9381),y=n(31244),m=n(73386),g=n(85859),v=n(67256),h=n(57437),x="Dialog",[w,k]=(0,u.b)(x),[N,M]=w(x),b=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:i,onOpenChange:u,modal:l=!0}=e,c=r.useRef(null),d=r.useRef(null),[f,p]=(0,s.T)({prop:o,defaultProp:i??!1,onChange:u,caller:x});return(0,h.jsx)(N,{scope:t,triggerRef:c,contentRef:d,contentId:(0,a.M)(),titleId:(0,a.M)(),descriptionId:(0,a.M)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:l,children:n})};b.displayName=x;var E="DialogTrigger",D=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,u=M(E,n),a=(0,i.e)(t,u.triggerRef);return(0,h.jsx)(p.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":u.open,"aria-controls":u.contentId,"data-state":$(u.open),...r,ref:a,onClick:(0,o.M)(e.onClick,u.onOpenToggle)})});D.displayName=E;var R="DialogPortal",[j,O]=w(R,{forceMount:void 0}),I=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:i}=e,u=M(R,t);return(0,h.jsx)(j,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,h.jsx)(f.z,{present:n||u.open,children:(0,h.jsx)(d.h,{asChild:!0,container:i,children:e})}))})};I.displayName=R;var C="DialogOverlay",S=r.forwardRef((e,t)=>{let n=O(C,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=M(C,e.__scopeDialog);return i.modal?(0,h.jsx)(f.z,{present:r||i.open,children:(0,h.jsx)(T,{...o,ref:t})}):null});S.displayName=C;var Z=(0,v.Z8)("DialogOverlay.RemoveScroll"),T=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=M(C,n);return(0,h.jsx)(m.Z,{as:Z,allowPinchZoom:!0,shards:[o.contentRef],children:(0,h.jsx)(p.WV.div,{"data-state":$(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),A="DialogContent",_=r.forwardRef((e,t)=>{let n=O(A,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=M(A,e.__scopeDialog);return(0,h.jsx)(f.z,{present:r||i.open,children:i.modal?(0,h.jsx)(F,{...o,ref:t}):(0,h.jsx)(L,{...o,ref:t})})});_.displayName=A;var F=r.forwardRef((e,t)=>{let n=M(A,e.__scopeDialog),u=r.useRef(null),a=(0,i.e)(t,n.contentRef,u);return r.useEffect(()=>{let e=u.current;if(e)return(0,g.Ry)(e)},[]),(0,h.jsx)(W,{...e,ref:a,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{e.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:(0,o.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault())})}),L=r.forwardRef((e,t)=>{let n=M(A,e.__scopeDialog),o=r.useRef(!1),i=r.useRef(!1);return(0,h.jsx)(W,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(o.current||n.triggerRef.current?.focus(),t.preventDefault()),o.current=!1,i.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(i.current=!0));let r=t.target;n.triggerRef.current?.contains(r)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),W=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:u,onCloseAutoFocus:a,...s}=e,d=M(A,n),f=r.useRef(null),p=(0,i.e)(t,f);return(0,y.EW)(),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(c.M,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:u,onUnmountAutoFocus:a,children:(0,h.jsx)(l.XB,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":$(d.open),...s,ref:p,onDismiss:()=>d.onOpenChange(!1)})}),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(K,{titleId:d.titleId}),(0,h.jsx)(G,{contentRef:f,descriptionId:d.descriptionId})]})]})}),P="DialogTitle",V=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=M(P,n);return(0,h.jsx)(p.WV.h2,{id:o.titleId,...r,ref:t})});V.displayName=P;var U="DialogDescription",z=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=M(U,n);return(0,h.jsx)(p.WV.p,{id:o.descriptionId,...r,ref:t})});z.displayName=U;var H="DialogClose",q=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=M(H,n);return(0,h.jsx)(p.WV.button,{type:"button",...r,ref:t,onClick:(0,o.M)(e.onClick,()=>i.onOpenChange(!1))})});function $(e){return e?"open":"closed"}q.displayName=H;var B="DialogTitleWarning",[X,Y]=(0,u.k)(B,{contentName:A,titleName:P,docsSlug:"dialog"}),K=({titleId:e})=>{let t=Y(B),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return r.useEffect(()=>{e&&!document.getElementById(e)&&console.error(n)},[n,e]),null},G=({contentRef:e,descriptionId:t})=>{let n=Y("DialogDescriptionWarning"),o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${n.contentName}}.`;return r.useEffect(()=>{let n=e.current?.getAttribute("aria-describedby");t&&n&&!document.getElementById(t)&&console.warn(o)},[o,e,t]),null},J=b,Q=D,ee=I,et=S,en=_,er=V,eo=z,ei=q},85606:function(e,t,n){"use strict";n.d(t,{z:function(){return u}});var r=n(2265),o=n(42210),i=n(51030),u=e=>{let t,n;let{present:u,children:s}=e,l=function(e){var t,n;let[o,u]=r.useState(),s=r.useRef(null),l=r.useRef(e),c=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>n[e][t]??e,t));return r.useEffect(()=>{let e=a(s.current);c.current="mounted"===d?e:"none"},[d]),(0,i.b)(()=>{let t=s.current,n=l.current;if(n!==e){let r=c.current,o=a(t);e?f("MOUNT"):"none"===o||t?.display==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),l.current=e}},[e,f]),(0,i.b)(()=>{if(o){let e;let t=o.ownerDocument.defaultView??window,n=n=>{let r=a(s.current).includes(n.animationName);if(n.target===o&&r&&(f("ANIMATION_END"),!l.current)){let n=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=n)})}},r=e=>{e.target===o&&(c.current=a(s.current))};return o.addEventListener("animationstart",r),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",r),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{s.current=e?getComputedStyle(e):null,u(e)},[])}}(u),c="function"==typeof s?s({present:l.isPresent}):r.Children.only(s),d=(0,o.e)(l.ref,(t=Object.getOwnPropertyDescriptor(c.props,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning?c.ref:(t=Object.getOwnPropertyDescriptor(c,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning?c.props.ref:c.props.ref||c.ref);return"function"==typeof s||l.isPresent?r.cloneElement(c,{ref:d}):null};function a(e){return e?.animationName||"none"}u.displayName="Presence"}}]);