'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { InvoiceForm } from '@/components/invoices/invoice-form'
import { 
  ArrowLeft, 
  Edit, 
  Trash2, 
  Send, 
  Download, 
  Copy,
  Mail,
  Phone,
  Building2,
  Calendar,
  User,
  FileText,
  DollarSign,
  Calculator,
  Activity,
  Plus,
  CreditCard,
  CheckCircle,
  Clock,
  AlertTriangle
} from 'lucide-react'
import { toast } from 'react-hot-toast'
import Link from 'next/link'

interface InvoiceItem {
  id: string
  description: string
  quantity: number
  unitPrice: number
  discount: number
  taxRate: number
}

interface Payment {
  id: string
  amount: number
  paymentDate: string
  paymentMethod: string | null
  reference: string | null
  notes: string | null
  createdAt: string
  createdBy: {
    name: string | null
  }
}

interface Invoice {
  id: string
  invoiceNumber: string
  status: 'DRAFT' | 'SENT' | 'VIEWED' | 'PAID' | 'OVERDUE' | 'CANCELLED'
  issueDate: string
  dueDate: string
  terms: string | null
  notes: string | null
  paymentTerms: string | null
  paymentMethod: string | null
  taxRate: number
  discountType: 'PERCENTAGE' | 'FIXED'
  discountValue: number
  subtotal: number
  total: number
  taxAmount: number
  discountAmount: number
  totalPaid: number
  amountDue: number
  customer: {
    id: string
    name: string
    email: string | null
    company: string | null
    phone: string | null
    address: string | null
    city: string | null
    state: string | null
    country: string | null
    postalCode: string | null
  }
  quotation: {
    id: string
    quotationNumber: string
    title: string
  } | null
  createdBy: {
    name: string | null
    email: string | null
  }
  items: InvoiceItem[]
  payments: Payment[]
  activities: any[]
  _count: {
    activities: number
    payments: number
  }
}

export default function InvoiceDetailPage() {
  const params = useParams()
  const router = useRouter()
  const [invoice, setInvoice] = useState<Invoice | null>(null)
  const [loading, setLoading] = useState(true)
  const [showEditForm, setShowEditForm] = useState(false)

  const fetchInvoice = async () => {
    try {
      const response = await fetch(`/api/invoices/${params.id}`)
      if (!response.ok) {
        if (response.status === 404) {
          toast.error('Invoice not found')
          router.push('/dashboard/invoices')
          return
        }
        throw new Error('Failed to fetch invoice')
      }
      
      const data = await response.json()
      setInvoice(data)
    } catch (error) {
      toast.error('Failed to load invoice details')
      console.error('Error fetching invoice:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (params.id) {
      fetchInvoice()
    }
  }, [params.id])

  const handleDelete = async () => {
    if (!invoice || !confirm(`Are you sure you want to delete invoice "${invoice.invoiceNumber}"?`)) return

    try {
      const response = await fetch(`/api/invoices/${invoice.id}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to delete invoice')
      }

      toast.success('Invoice deleted successfully')
      router.push('/dashboard/invoices')
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to delete invoice')
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'DRAFT':
        return <Badge variant="secondary">Draft</Badge>
      case 'SENT':
        return <Badge variant="info">Sent</Badge>
      case 'VIEWED':
        return <Badge variant="warning">Viewed</Badge>
      case 'PAID':
        return <Badge variant="success">Paid</Badge>
      case 'OVERDUE':
        return <Badge variant="destructive">Overdue</Badge>
      case 'CANCELLED':
        return <Badge variant="secondary">Cancelled</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PAID':
        return <CheckCircle className="h-5 w-5 text-green-600" />
      case 'OVERDUE':
        return <AlertTriangle className="h-5 w-5 text-red-600" />
      case 'DRAFT':
        return <Clock className="h-5 w-5 text-gray-600" />
      default:
        return <FileText className="h-5 w-5 text-blue-600" />
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!invoice) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Invoice not found</p>
        <Button asChild className="mt-4">
          <Link href="/dashboard/invoices">Back to Invoices</Link>
        </Button>
      </div>
    )
  }

  const isOverdue = new Date(invoice.dueDate) < new Date() && invoice.status !== 'PAID'

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/dashboard/invoices">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Invoices
            </Link>
          </Button>
          <div>
            <div className="flex items-center space-x-3">
              {getStatusIcon(invoice.status)}
              <h1 className="text-3xl font-bold text-gray-900">{invoice.invoiceNumber}</h1>
            </div>
            <div className="flex items-center space-x-2 mt-1">
              {getStatusBadge(invoice.status)}
              {isOverdue && <Badge variant="destructive">Overdue</Badge>}
              {invoice.quotation && (
                <span className="text-gray-500">• From {invoice.quotation.quotationNumber}</span>
              )}
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline">
            <Copy className="h-4 w-4 mr-2" />
            Duplicate
          </Button>
          <Button variant="outline">
            <Send className="h-4 w-4 mr-2" />
            Send
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            PDF
          </Button>
          <Button variant="outline">
            <CreditCard className="h-4 w-4 mr-2" />
            Record Payment
          </Button>
          <Button variant="outline" onClick={() => setShowEditForm(true)}>
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
          <Button 
            variant="destructive" 
            onClick={handleDelete}
            disabled={invoice.status === 'PAID' || invoice._count.payments > 0}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Customer Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="h-5 w-5 mr-2" />
                Customer Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-500">Customer Name</p>
                  <p className="font-medium">{invoice.customer.name}</p>
                </div>
                
                {invoice.customer.company && (
                  <div>
                    <p className="text-sm text-gray-500">Company</p>
                    <p className="font-medium">{invoice.customer.company}</p>
                  </div>
                )}
                
                {invoice.customer.email && (
                  <div>
                    <p className="text-sm text-gray-500">Email</p>
                    <p className="font-medium">{invoice.customer.email}</p>
                  </div>
                )}
                
                {invoice.customer.phone && (
                  <div>
                    <p className="text-sm text-gray-500">Phone</p>
                    <p className="font-medium">{invoice.customer.phone}</p>
                  </div>
                )}
              </div>

              {/* Address */}
              {(invoice.customer.address || invoice.customer.city) && (
                <div className="pt-4 border-t">
                  <p className="text-sm text-gray-500 mb-2">Billing Address</p>
                  <div className="text-gray-900">
                    {invoice.customer.address && <p>{invoice.customer.address}</p>}
                    <p>
                      {[invoice.customer.city, invoice.customer.state, invoice.customer.postalCode].filter(Boolean).join(', ')}
                    </p>
                    {invoice.customer.country && <p>{invoice.customer.country}</p>}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Items */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="h-5 w-5 mr-2" />
                Items
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-2">Description</th>
                      <th className="text-right py-2">Qty</th>
                      <th className="text-right py-2">Unit Price</th>
                      <th className="text-right py-2">Discount</th>
                      <th className="text-right py-2">Tax</th>
                      <th className="text-right py-2">Total</th>
                    </tr>
                  </thead>
                  <tbody>
                    {invoice.items && Array.isArray(invoice.items) ? invoice.items.map((item, index) => {
                      const itemTotal = item.quantity * item.unitPrice
                      const discountAmount = (itemTotal * item.discount) / 100
                      const afterDiscount = itemTotal - discountAmount
                      const taxAmount = (afterDiscount * item.taxRate) / 100
                      const finalTotal = afterDiscount + taxAmount

                      return (
                        <tr key={index} className="border-b">
                          <td className="py-3">{item.description}</td>
                          <td className="text-right py-3">{item.quantity}</td>
                          <td className="text-right py-3">${item.unitPrice.toFixed(2)}</td>
                          <td className="text-right py-3">{item.discount}%</td>
                          <td className="text-right py-3">{item.taxRate}%</td>
                          <td className="text-right py-3 font-medium">${finalTotal.toFixed(2)}</td>
                        </tr>
                      )
                    }) : (
                      <tr>
                        <td colSpan={6} className="text-center py-4 text-gray-500">
                          No items found
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>

          {/* Payment History */}
          {invoice.payments && invoice.payments.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <CreditCard className="h-5 w-5 mr-2" />
                  Payment History
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {invoice.payments.map((payment) => (
                    <div key={payment.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <p className="font-medium">${payment.amount.toFixed(2)}</p>
                        <p className="text-sm text-gray-500">
                          {new Date(payment.paymentDate).toLocaleDateString()}
                          {payment.paymentMethod && ` • ${payment.paymentMethod}`}
                        </p>
                        {payment.reference && (
                          <p className="text-sm text-gray-500">Ref: {payment.reference}</p>
                        )}
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-gray-500">
                          by {payment.createdBy.name || 'Unknown'}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Terms and Notes */}
          {(invoice.terms || invoice.notes) && (
            <Card>
              <CardHeader>
                <CardTitle>Additional Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {invoice.terms && (
                  <div>
                    <p className="text-sm text-gray-500 mb-2">Terms & Conditions</p>
                    <p className="text-gray-900 whitespace-pre-wrap">{invoice.terms}</p>
                  </div>
                )}
                
                {invoice.notes && (
                  <div className="pt-4 border-t">
                    <p className="text-sm text-gray-500 mb-2">Internal Notes</p>
                    <p className="text-gray-900 whitespace-pre-wrap">{invoice.notes}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Activity Timeline */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center">
                  <Activity className="h-5 w-5 mr-2" />
                  Activity Timeline
                </CardTitle>
                <Button variant="outline" size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Note
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {invoice.activities && Array.isArray(invoice.activities) && invoice.activities.length > 0 ? (
                <div className="space-y-4">
                  {invoice.activities.map((activity) => (
                    <div key={activity.id} className="flex items-start space-x-3 pb-4 border-b last:border-b-0">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <Activity className="h-4 w-4 text-blue-600" />
                      </div>
                      <div className="flex-1">
                        <p className="font-medium">{activity.title}</p>
                        <p className="text-sm text-gray-600">{activity.description}</p>
                        <p className="text-xs text-gray-500 mt-1">
                          {new Date(activity.createdAt).toLocaleDateString()} by {activity.createdBy.name}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-4">No activity recorded yet</p>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Financial Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calculator className="h-5 w-5 mr-2" />
                Financial Summary
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Subtotal:</span>
                  <span>${invoice.subtotal.toFixed(2)}</span>
                </div>
                
                {invoice.discountAmount > 0 && (
                  <div className="flex justify-between text-red-600">
                    <span>Discount:</span>
                    <span>-${invoice.discountAmount.toFixed(2)}</span>
                  </div>
                )}
                
                {invoice.taxAmount > 0 && (
                  <div className="flex justify-between">
                    <span>Tax:</span>
                    <span>${invoice.taxAmount.toFixed(2)}</span>
                  </div>
                )}
                
                <div className="flex justify-between font-bold text-lg border-t pt-2">
                  <span>Total:</span>
                  <span>${invoice.total.toFixed(2)}</span>
                </div>

                {invoice.totalPaid > 0 && (
                  <>
                    <div className="flex justify-between text-green-600">
                      <span>Paid:</span>
                      <span>-${invoice.totalPaid.toFixed(2)}</span>
                    </div>
                    
                    <div className="flex justify-between font-bold text-lg border-t pt-2">
                      <span>Amount Due:</span>
                      <span className={invoice.amountDue > 0 ? 'text-red-600' : 'text-green-600'}>
                        ${invoice.amountDue.toFixed(2)}
                      </span>
                    </div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Invoice Details */}
          <Card>
            <CardHeader>
              <CardTitle>Invoice Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4 text-gray-400" />
                  <span className="text-sm">Issue Date</span>
                </div>
                <span className="font-medium text-sm">
                  {new Date(invoice.issueDate).toLocaleDateString()}
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4 text-gray-400" />
                  <span className="text-sm">Due Date</span>
                </div>
                <span className={`font-medium text-sm ${isOverdue ? 'text-red-600' : ''}`}>
                  {new Date(invoice.dueDate).toLocaleDateString()}
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <User className="h-4 w-4 text-gray-400" />
                  <span className="text-sm">Created By</span>
                </div>
                <span className="font-medium text-sm">
                  {invoice.createdBy.name || 'Unknown'}
                </span>
              </div>

              {invoice.paymentTerms && (
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <CreditCard className="h-4 w-4 text-gray-400" />
                    <span className="text-sm">Payment Terms</span>
                  </div>
                  <span className="font-medium text-sm">
                    {invoice.paymentTerms}
                  </span>
                </div>
              )}

              {invoice.quotation && (
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <FileText className="h-4 w-4 text-gray-400" />
                    <span className="text-sm">Related Quotation</span>
                  </div>
                  <Link 
                    href={`/dashboard/quotations/${invoice.quotation.id}`}
                    className="font-medium text-sm text-blue-600 hover:underline"
                  >
                    {invoice.quotation.quotationNumber}
                  </Link>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button variant="outline" className="w-full justify-start">
                <Send className="h-4 w-4 mr-2" />
                Send to Customer
              </Button>
              
              <Button variant="outline" className="w-full justify-start">
                <Download className="h-4 w-4 mr-2" />
                Download PDF
              </Button>
              
              <Button variant="outline" className="w-full justify-start">
                <CreditCard className="h-4 w-4 mr-2" />
                Record Payment
              </Button>
              
              <Button variant="outline" className="w-full justify-start">
                <Copy className="h-4 w-4 mr-2" />
                Duplicate Invoice
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Edit Form Modal */}
      <InvoiceForm
        isOpen={showEditForm}
        onClose={() => setShowEditForm(false)}
        onSuccess={fetchInvoice}
        invoice={invoice}
        mode="edit"
      />
    </div>
  )
}
