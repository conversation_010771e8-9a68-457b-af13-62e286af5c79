"use strict";(()=>{var e={};e.id=9787,e.ids=[9787],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},97976:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>A,originalPathname:()=>j,patchFetch:()=>x,requestAsyncStorage:()=>f,routeModule:()=>w,serverHooks:()=>E,staticGenerationAsyncStorage:()=>h,staticGenerationBailout:()=>T});var s={};t.r(s),t.d(s,{DELETE:()=>I,GET:()=>m,POST:()=>y});var a=t(95419),n=t(69108),i=t(99678),o=t(78070),d=t(81355),u=t(3205),c=t(9108),l=t(25252),g=t(52178);let p=l.Ry({signerName:l.Z_().min(1,"Signer name is required"),signerEmail:l.Z_().email("Valid email is required"),signerRole:l.Z_().optional(),signatureType:l.Km(["ELECTRONIC","DIGITAL","WET_SIGNATURE"]).default("ELECTRONIC"),signatureData:l.Z_().optional(),ipAddress:l.Z_().optional(),userAgent:l.Z_().optional(),notes:l.Z_().optional()});async function m(e,{params:r}){try{let e=await (0,d.getServerSession)(u.L);if(!e?.user?.id||!e?.user?.companyId)return o.Z.json({error:"Unauthorized"},{status:401});if(!await c._.contract.findFirst({where:{id:r.id,companyId:e.user.companyId}}))return o.Z.json({error:"Contract not found"},{status:404});let t=await c._.signature.findMany({where:{contractId:r.id},include:{signedBy:{select:{name:!0,email:!0}}},orderBy:{createdAt:"desc"}});return o.Z.json({signatures:t.map(e=>({id:e.id,signerName:e.signerName,signerEmail:e.signerEmail,signerRole:e.signerRole,signatureType:e.signatureType,signedAt:e.signedAt,ipAddress:e.ipAddress,userAgent:e.userAgent,notes:e.notes,signedBy:e.signedBy,createdAt:e.createdAt}))})}catch(e){return console.error("Error fetching signatures:",e),o.Z.json({error:"Failed to fetch signatures"},{status:500})}}async function y(e,{params:r}){try{let t=await (0,d.getServerSession)(u.L);if(!t?.user?.id||!t?.user?.companyId)return o.Z.json({error:"Unauthorized"},{status:401});let s=await e.json(),a=p.parse(s),n=await c._.contract.findFirst({where:{id:r.id,companyId:t.user.companyId},include:{signatures:!0}});if(!n)return o.Z.json({error:"Contract not found"},{status:404});if(!["SENT","REVIEW"].includes(n.status))return o.Z.json({error:"Contract is not in a signable state"},{status:400});if(n.signatures.find(e=>e.signerEmail.toLowerCase()===a.signerEmail.toLowerCase()))return o.Z.json({error:"This email has already signed the contract"},{status:400});let i=e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown",l=e.headers.get("user-agent")||"unknown",g=await c._.$transaction(async e=>{let s=await e.signature.create({data:{signerName:a.signerName,signerEmail:a.signerEmail,signerRole:a.signerRole,signatureType:a.signatureType,signatureData:a.signatureData,signedAt:new Date,ipAddress:a.ipAddress||i,userAgent:a.userAgent||l,notes:a.notes,contractId:r.id,companyId:t.user.companyId,signedById:t.user.id},include:{signedBy:{select:{name:!0,email:!0}}}});return n.signatureRequired&&await e.contract.update({where:{id:r.id},data:{status:"SIGNED",signedAt:new Date}}),await e.activity.create({data:{type:"SIGNATURE",title:"Contract Signed",description:`Contract ${n.contractNumber} was signed by ${a.signerName} (${a.signerEmail})`,contractId:r.id,customerId:n.customerId,companyId:t.user.companyId,createdById:t.user.id}}),s});return o.Z.json({signature:{id:g.id,signerName:g.signerName,signerEmail:g.signerEmail,signerRole:g.signerRole,signatureType:g.signatureType,signedAt:g.signedAt,ipAddress:g.ipAddress,userAgent:g.userAgent,notes:g.notes,signedBy:g.signedBy,createdAt:g.createdAt},message:"Signature recorded successfully"})}catch(e){if(e instanceof g.jm)return o.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("Error recording signature:",e),o.Z.json({error:"Failed to record signature"},{status:500})}}async function I(e,{params:r}){try{let t=await (0,d.getServerSession)(u.L);if(!t?.user?.id||!t?.user?.companyId)return o.Z.json({error:"Unauthorized"},{status:401});let{searchParams:s}=new URL(e.url),a=s.get("signatureId");if(!a)return o.Z.json({error:"Signature ID is required"},{status:400});let n=await c._.signature.findFirst({where:{id:a,contractId:r.id,companyId:t.user.companyId},include:{contract:!0}});if(!n)return o.Z.json({error:"Signature not found"},{status:404});if("ACTIVE"===n.contract.status)return o.Z.json({error:"Cannot delete signatures from active contracts"},{status:400});return await c._.$transaction(async e=>{await e.signature.delete({where:{id:a}});let s=await e.signature.count({where:{contractId:r.id}});0===s&&n.contract.signatureRequired&&await e.contract.update({where:{id:r.id},data:{status:"SENT",signedAt:null}}),await e.activity.create({data:{type:"SIGNATURE",title:"Signature Removed",description:`Signature by ${n.signerName} (${n.signerEmail}) was removed from contract ${n.contract.contractNumber}`,contractId:r.id,customerId:n.contract.customerId,companyId:t.user.companyId,createdById:t.user.id}})}),o.Z.json({message:"Signature deleted successfully"})}catch(e){return console.error("Error deleting signature:",e),o.Z.json({error:"Failed to delete signature"},{status:500})}}let w=new a.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/contracts/[id]/signatures/route",pathname:"/api/contracts/[id]/signatures",filename:"route",bundlePath:"app/api/contracts/[id]/signatures/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\contracts\\[id]\\signatures\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:f,staticGenerationAsyncStorage:h,serverHooks:E,headerHooks:A,staticGenerationBailout:T}=w,j="/api/contracts/[id]/signatures/route";function x(){return(0,i.patchFetch)({serverHooks:E,staticGenerationAsyncStorage:h})}},3205:(e,r,t)=>{t.d(r,{L:()=>u});var s=t(86485),a=t(10375),n=t(50694),i=t(6521),o=t.n(i),d=t(9108);let u={providers:[(0,s.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let r=await d._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),t=r?.companyId;if(!t&&r){let e=await d._.company.findFirst({where:{ownerId:r.id},select:{id:!0}});(t=e?.id)&&await d._.user.update({where:{id:r.id},data:{companyId:t}})}if(!r)return console.log("User not found:",e.email),null;if(!r.password)return console.log("User has no password set:",e.email),null;if(!await o().compare(e.password,r.password))return console.log("Invalid password for user:",e.email),null;return await d._.user.update({where:{id:r.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",r.email),{id:r.id,email:r.email,name:r.name,role:r.role,companyId:t}}catch(e){return console.error("Authentication error:",e),null}}}),(0,a.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,n.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(console.log("JWT callback - user data:",{id:r.id,email:r.email,role:r.role,companyId:r.companyId}),e.role=r.role,e.companyId=r.companyId),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role,e.user.companyId=r.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,r,t)=>{t.d(r,{_:()=>a});let s=require("@prisma/client"),a=globalThis.prisma??new s.PrismaClient}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[1638,6206,6521,2455,4520,5252],()=>t(97976));module.exports=s})();