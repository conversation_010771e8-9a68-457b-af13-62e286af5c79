import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get payment gateway configurations from database
    let gateways = []
    let config = {}
    
    try {
      gateways = await prisma.paymentGatewayConfig.findMany()
      
      // Convert to config object
      config = gateways.reduce((acc, gateway) => {
        acc[gateway.provider] = {
          enabled: gateway.isEnabled,
          live: gateway.isLive,
          ...gateway.config
        }
        return acc
      }, {} as Record<string, any>)
    } catch (error) {
      console.error('Error fetching payment gateways:', error)
      // Return default config if table doesn't exist yet
      config = {
        stripe: {
          enabled: false,
          live: false,
          publishableKey: '',
          secretKey: '',
          webhookSecret: '',
          supportedMethods: ['card', 'apple_pay', 'google_pay']
        },
        paypal: {
          enabled: false,
          live: false,
          clientId: '',
          clientSecret: '',
          webhookId: '',
          supportedMethods: ['paypal', 'venmo']
        },
        razorpay: {
          enabled: false,
          live: false,
          keyId: '',
          keySecret: '',
          webhookSecret: '',
          supportedMethods: ['card', 'netbanking', 'upi', 'wallet']
        },
        square: {
          enabled: false,
          live: false,
          applicationId: '',
          accessToken: '',
          webhookSignatureKey: '',
          supportedMethods: ['card', 'apple_pay', 'google_pay']
        }
      }
    }

    return NextResponse.json({
      success: true,
      gateways: gateways,
      config: config
    })
  } catch (error) {
    console.error('Error fetching payment gateways:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const config = await request.json()

    // Update or create payment gateway configurations
    const updates = []
    
    for (const [provider, settings] of Object.entries(config)) {
      const { enabled, live, ...gatewayConfig } = settings as any

      try {
        updates.push(
          prisma.paymentGatewayConfig.upsert({
            where: { provider },
            update: {
              isEnabled: enabled,
              isLive: live,
              config: gatewayConfig,
              updatedAt: new Date()
            },
            create: {
              provider,
              isEnabled: enabled,
              isLive: live,
              config: gatewayConfig
            }
          })
        )
      } catch (error) {
        console.error(`Error updating payment gateway config for ${provider}:`, error)
      }
    }

    try {
      await Promise.all(updates)
    } catch (error) {
      console.error('Error saving payment gateway config:', error)
      // Continue even if some updates fail
    }

    // Log the configuration change
    try {
      await prisma.auditLog.create({
        data: {
          action: 'UPDATE_PAYMENT_GATEWAYS',
          entityType: 'PAYMENT_GATEWAY_CONFIG',
          entityId: 'payment_gateways',
          userId: session.user.id,
          details: {
            updatedProviders: Object.keys(config),
            timestamp: new Date().toISOString()
          }
        }
      })
    } catch (error) {
      // Ignore audit log errors
      console.error('Error creating audit log:', error)
    }

    return NextResponse.json({
      success: true,
      message: 'Payment gateway configuration updated successfully'
    })
  } catch (error) {
    console.error('Error updating payment gateway config:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
