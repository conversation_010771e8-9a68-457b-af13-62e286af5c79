(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8996],{71738:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(62898).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},41298:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(62898).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},99670:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(62898).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},76637:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(62898).Z)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]])},9883:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(62898).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},41827:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(62898).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},24236:function(e,t,r){Promise.resolve().then(r.bind(r,97854))},97854:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return g}});var s=r(57437),a=r(2265),n=r(27815),i=r(85754),o=r(31478),l=r(45179),c=r(71738),d=r(76637),u=r(41298),m=r(9883),f=r(41827),p=r(99670),x=r(5925),h=r(61396),y=r.n(h);function g(){let[e,t]=(0,a.useState)([]),[r,h]=(0,a.useState)(!0),[g,v]=(0,a.useState)(""),[b,j]=(0,a.useState)("all"),[N,w]=(0,a.useState)("all");(0,a.useEffect)(()=>{C()},[]);let C=async()=>{try{h(!0);let e=await fetch("/api/payments");if(!e.ok)throw Error("Failed to fetch payments");let r=await e.json();t(r.payments)}catch(e){x.toast.error("Failed to load payments"),console.error("Error fetching payments:",e)}finally{h(!1)}},E=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e),k=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),Z=e=>{switch(e){case"COMPLETED":return"bg-green-100 text-green-800";case"PENDING":return"bg-yellow-100 text-yellow-800";case"FAILED":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},D=e=>{switch(e){case"CREDIT_CARD":return(0,s.jsx)(c.Z,{className:"h-4 w-4"});case"BANK_TRANSFER":return(0,s.jsx)(d.Z,{className:"h-4 w-4"});default:return(0,s.jsx)(u.Z,{className:"h-4 w-4"})}},A=e.filter(e=>{var t,r,s;let a=(null===(t=e.invoice)||void 0===t?void 0:t.invoiceNumber.toLowerCase().includes(g.toLowerCase()))||(null===(r=e.invoice)||void 0===r?void 0:r.customer.name.toLowerCase().includes(g.toLowerCase()))||(null===(s=e.reference)||void 0===s?void 0:s.toLowerCase().includes(g.toLowerCase())),n="all"===b||e.status===b,i="all"===N||e.paymentMethod===N;return a&&n&&i}),T=A.reduce((e,t)=>e+t.amount,0),P=A.filter(e=>"COMPLETED"===e.status).reduce((e,t)=>e+t.amount,0);return r?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsx)("h1",{className:"text-3xl font-bold",children:"Payments"})}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[1,2,3].map(e=>(0,s.jsx)(n.Zb,{children:(0,s.jsx)(n.aY,{className:"p-6",children:(0,s.jsxs)("div",{className:"animate-pulse",children:[(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2 mb-2"}),(0,s.jsx)("div",{className:"h-8 bg-gray-200 rounded w-3/4"})]})})},e))})]}):(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold",children:"Payments"}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)(i.z,{variant:"outline",asChild:!0,children:(0,s.jsxs)(y(),{href:"/dashboard/invoices",children:[(0,s.jsx)(d.Z,{className:"h-4 w-4 mr-2"}),"Manage Invoices"]})}),(0,s.jsx)(i.z,{asChild:!0,children:(0,s.jsxs)(y(),{href:"/dashboard/payments/new",children:[(0,s.jsx)(m.Z,{className:"h-4 w-4 mr-2"}),"Record Payment"]})})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,s.jsx)(n.Zb,{children:(0,s.jsx)(n.aY,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(u.Z,{className:"h-8 w-8 text-blue-600"}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Payments"}),(0,s.jsx)("p",{className:"text-2xl font-bold",children:E(T)})]})]})})}),(0,s.jsx)(n.Zb,{children:(0,s.jsx)(n.aY,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(c.Z,{className:"h-8 w-8 text-green-600"}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Completed"}),(0,s.jsx)("p",{className:"text-2xl font-bold",children:E(P)})]})]})})}),(0,s.jsx)(n.Zb,{children:(0,s.jsx)(n.aY,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(d.Z,{className:"h-8 w-8 text-purple-600"}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Transactions"}),(0,s.jsx)("p",{className:"text-2xl font-bold",children:A.length})]})]})})})]}),(0,s.jsx)(n.Zb,{children:(0,s.jsx)(n.aY,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(f.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,s.jsx)(l.I,{placeholder:"Search by invoice, customer, or reference...",value:g,onChange:e=>v(e.target.value),className:"pl-10"})]})}),(0,s.jsxs)("select",{value:b,onChange:e=>j(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"all",children:"All Status"}),(0,s.jsx)("option",{value:"COMPLETED",children:"Completed"}),(0,s.jsx)("option",{value:"PENDING",children:"Pending"}),(0,s.jsx)("option",{value:"FAILED",children:"Failed"}),(0,s.jsx)("option",{value:"CANCELLED",children:"Cancelled"})]}),(0,s.jsxs)("select",{value:N,onChange:e=>w(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"all",children:"All Methods"}),(0,s.jsx)("option",{value:"CASH",children:"Cash"}),(0,s.jsx)("option",{value:"CHECK",children:"Check"}),(0,s.jsx)("option",{value:"CREDIT_CARD",children:"Credit Card"}),(0,s.jsx)("option",{value:"BANK_TRANSFER",children:"Bank Transfer"}),(0,s.jsx)("option",{value:"PAYPAL",children:"PayPal"}),(0,s.jsx)("option",{value:"OTHER",children:"Other"})]})]})})}),(0,s.jsxs)(n.Zb,{children:[(0,s.jsx)(n.Ol,{children:(0,s.jsx)(n.ll,{children:"Payment History"})}),(0,s.jsx)(n.aY,{children:0===A.length?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)(c.Z,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No payments found"}),(0,s.jsx)("p",{className:"text-gray-500",children:g||"all"!==b||"all"!==N?"Try adjusting your filters":"Start by recording your first payment"})]}):(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{className:"border-b",children:[(0,s.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Invoice"}),(0,s.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Customer"}),(0,s.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Amount"}),(0,s.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Method"}),(0,s.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Date"}),(0,s.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Status"}),(0,s.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Actions"})]})}),(0,s.jsx)("tbody",{children:A.map(e=>{var t,r,a;return(0,s.jsxs)("tr",{className:"border-b hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"py-3 px-4",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(d.Z,{className:"h-4 w-4 text-gray-400 mr-2"}),(0,s.jsx)("span",{className:"font-medium",children:(null===(t=e.invoice)||void 0===t?void 0:t.invoiceNumber)||"N/A"})]})}),(0,s.jsx)("td",{className:"py-3 px-4",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium",children:null===(r=e.invoice)||void 0===r?void 0:r.customer.name}),(null===(a=e.invoice)||void 0===a?void 0:a.customer.company)&&(0,s.jsx)("div",{className:"text-sm text-gray-500",children:e.invoice.customer.company})]})}),(0,s.jsx)("td",{className:"py-3 px-4 font-medium",children:E(e.amount)}),(0,s.jsx)("td",{className:"py-3 px-4",children:(0,s.jsxs)("div",{className:"flex items-center",children:[D(e.paymentMethod),(0,s.jsx)("span",{className:"ml-2",children:e.paymentMethod.replace("_"," ")})]})}),(0,s.jsx)("td",{className:"py-3 px-4 text-gray-600",children:k(e.paymentDate)}),(0,s.jsx)("td",{className:"py-3 px-4",children:(0,s.jsx)(o.C,{className:Z(e.status),children:e.status})}),(0,s.jsx)("td",{className:"py-3 px-4",children:(0,s.jsx)("div",{className:"flex items-center space-x-2",children:e.invoice&&(0,s.jsx)(i.z,{variant:"ghost",size:"sm",asChild:!0,children:(0,s.jsx)(y(),{href:"/dashboard/invoices/".concat(e.invoice.id),children:(0,s.jsx)(p.Z,{className:"h-4 w-4"})})})})})]},e.id)})})]})})})]})]})}},31478:function(e,t,r){"use strict";r.d(t,{C:function(){return o}});var s=r(57437);r(2265);var a=r(96061),n=r(1657);let i=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600",info:"border-transparent bg-blue-500 text-white hover:bg-blue-600"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:r,...a}=e;return(0,s.jsx)("div",{className:(0,n.cn)(i({variant:r}),t),...a})}},85754:function(e,t,r){"use strict";r.d(t,{z:function(){return c}});var s=r(57437),a=r(2265),n=r(67256),i=r(96061),o=r(1657);let l=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,t)=>{let{className:r,variant:a,size:i,asChild:c=!1,...d}=e,u=c?n.g7:"button";return(0,s.jsx)(u,{className:(0,o.cn)(l({variant:a,size:i,className:r})),ref:t,...d})});c.displayName="Button"},27815:function(e,t,r){"use strict";r.d(t,{Ol:function(){return o},SZ:function(){return c},Zb:function(){return i},aY:function(){return d},eW:function(){return u},ll:function(){return l}});var s=r(57437),a=r(2265),n=r(1657);let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});i.displayName="Card";let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...a})});o.displayName="CardHeader";let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})});l.displayName="CardTitle";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",r),...a})});c.displayName="CardDescription";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",r),...a})});d.displayName="CardContent";let u=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",r),...a})});u.displayName="CardFooter"},45179:function(e,t,r){"use strict";r.d(t,{I:function(){return i}});var s=r(57437),a=r(2265),n=r(1657);let i=a.forwardRef((e,t)=>{let{className:r,type:a,...i}=e;return(0,s.jsx)("input",{type:a,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...i})});i.displayName="Input"},1657:function(e,t,r){"use strict";r.d(t,{cn:function(){return n}});var s=r(57042),a=r(74769);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.m6)((0,s.W)(t))}},5925:function(e,t,r){"use strict";let s,a;r.r(t),r.d(t,{CheckmarkIcon:function(){return W},ErrorIcon:function(){return q},LoaderIcon:function(){return V},ToastBar:function(){return eo},ToastIcon:function(){return et},Toaster:function(){return eu},default:function(){return em},resolveValue:function(){return C},toast:function(){return O},useToaster:function(){return F},useToasterStore:function(){return S}});var n,i=r(2265);let o={data:""},l=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||o,c=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,d=/\/\*[^]*?\*\/|  +/g,u=/\n+/g,m=(e,t)=>{let r="",s="",a="";for(let n in e){let i=e[n];"@"==n[0]?"i"==n[1]?r=n+" "+i+";":s+="f"==n[1]?m(i,n):n+"{"+m(i,"k"==n[1]?"":t)+"}":"object"==typeof i?s+=m(i,t?t.replace(/([^,])+/g,e=>n.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)):n):null!=i&&(n=/^--/.test(n)?n:n.replace(/[A-Z]/g,"-$&").toLowerCase(),a+=m.p?m.p(n,i):n+":"+i+";")}return r+(t&&a?t+"{"+a+"}":a)+s},f={},p=e=>{if("object"==typeof e){let t="";for(let r in e)t+=r+p(e[r]);return t}return e},x=(e,t,r,s,a)=>{var n;let i=p(e),o=f[i]||(f[i]=(e=>{let t=0,r=11;for(;t<e.length;)r=101*r+e.charCodeAt(t++)>>>0;return"go"+r})(i));if(!f[o]){let t=i!==e?e:(e=>{let t,r,s=[{}];for(;t=c.exec(e.replace(d,""));)t[4]?s.shift():t[3]?(r=t[3].replace(u," ").trim(),s.unshift(s[0][r]=s[0][r]||{})):s[0][t[1]]=t[2].replace(u," ").trim();return s[0]})(e);f[o]=m(a?{["@keyframes "+o]:t}:t,r?"":"."+o)}let l=r&&f.g?f.g:null;return r&&(f.g=f[o]),n=f[o],l?t.data=t.data.replace(l,n):-1===t.data.indexOf(n)&&(t.data=s?n+t.data:t.data+n),o},h=(e,t,r)=>e.reduce((e,s,a)=>{let n=t[a];if(n&&n.call){let e=n(r),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;n=t?"."+t:e&&"object"==typeof e?e.props?"":m(e,""):!1===e?"":e}return e+s+(null==n?"":n)},"");function y(e){let t=this||{},r=e.call?e(t.p):e;return x(r.unshift?r.raw?h(r,[].slice.call(arguments,1),t.p):r.reduce((e,r)=>Object.assign(e,r&&r.call?r(t.p):r),{}):r,l(t.target),t.g,t.o,t.k)}y.bind({g:1});let g,v,b,j=y.bind({k:1});function N(e,t){let r=this||{};return function(){let s=arguments;function a(n,i){let o=Object.assign({},n),l=o.className||a.className;r.p=Object.assign({theme:v&&v()},o),r.o=/ *go\d+/.test(l),o.className=y.apply(r,s)+(l?" "+l:""),t&&(o.ref=i);let c=e;return e[0]&&(c=o.as||e,delete o.as),b&&c[0]&&b(o),g(c,o)}return t?t(a):a}}var w=e=>"function"==typeof e,C=(e,t)=>w(e)?e(t):e,E=(s=0,()=>(++s).toString()),k=()=>{if(void 0===a&&"u">typeof window){let e=matchMedia("(prefers-reduced-motion: reduce)");a=!e||e.matches}return a},Z=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case 2:let{toast:r}=t;return Z(e,{type:e.toasts.find(e=>e.id===r.id)?1:0,toast:r});case 3:let{toastId:s}=t;return{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,dismissed:!0,visible:!1}:e)};case 4:return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let a=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(e=>({...e,pauseDuration:e.pauseDuration+a}))}}},D=[],A={toasts:[],pausedAt:void 0},T=e=>{A=Z(A,e),D.forEach(e=>{e(A)})},P={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},S=(e={})=>{let[t,r]=(0,i.useState)(A),s=(0,i.useRef)(A);(0,i.useEffect)(()=>(s.current!==A&&r(A),D.push(r),()=>{let e=D.indexOf(r);e>-1&&D.splice(e,1)}),[]);let a=t.toasts.map(t=>{var r,s,a;return{...e,...e[t.type],...t,removeDelay:t.removeDelay||(null==(r=e[t.type])?void 0:r.removeDelay)||(null==e?void 0:e.removeDelay),duration:t.duration||(null==(s=e[t.type])?void 0:s.duration)||(null==e?void 0:e.duration)||P[t.type],style:{...e.style,...null==(a=e[t.type])?void 0:a.style,...t.style}}});return{...t,toasts:a}},I=(e,t="blank",r)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...r,id:(null==r?void 0:r.id)||E()}),z=e=>(t,r)=>{let s=I(t,e,r);return T({type:2,toast:s}),s.id},O=(e,t)=>z("blank")(e,t);O.error=z("error"),O.success=z("success"),O.loading=z("loading"),O.custom=z("custom"),O.dismiss=e=>{T({type:3,toastId:e})},O.remove=e=>T({type:4,toastId:e}),O.promise=(e,t,r)=>{let s=O.loading(t.loading,{...r,...null==r?void 0:r.loading});return"function"==typeof e&&(e=e()),e.then(e=>{let a=t.success?C(t.success,e):void 0;return a?O.success(a,{id:s,...r,...null==r?void 0:r.success}):O.dismiss(s),e}).catch(e=>{let a=t.error?C(t.error,e):void 0;a?O.error(a,{id:s,...r,...null==r?void 0:r.error}):O.dismiss(s)}),e};var L=(e,t)=>{T({type:1,toast:{id:e,height:t}})},M=()=>{T({type:5,time:Date.now()})},R=new Map,$=1e3,_=(e,t=$)=>{if(R.has(e))return;let r=setTimeout(()=>{R.delete(e),T({type:4,toastId:e})},t);R.set(e,r)},F=e=>{let{toasts:t,pausedAt:r}=S(e);(0,i.useEffect)(()=>{if(r)return;let e=Date.now(),s=t.map(t=>{if(t.duration===1/0)return;let r=(t.duration||0)+t.pauseDuration-(e-t.createdAt);if(r<0){t.visible&&O.dismiss(t.id);return}return setTimeout(()=>O.dismiss(t.id),r)});return()=>{s.forEach(e=>e&&clearTimeout(e))}},[t,r]);let s=(0,i.useCallback)(()=>{r&&T({type:6,time:Date.now()})},[r]),a=(0,i.useCallback)((e,r)=>{let{reverseOrder:s=!1,gutter:a=8,defaultPosition:n}=r||{},i=t.filter(t=>(t.position||n)===(e.position||n)&&t.height),o=i.findIndex(t=>t.id===e.id),l=i.filter((e,t)=>t<o&&e.visible).length;return i.filter(e=>e.visible).slice(...s?[l+1]:[0,l]).reduce((e,t)=>e+(t.height||0)+a,0)},[t]);return(0,i.useEffect)(()=>{t.forEach(e=>{if(e.dismissed)_(e.id,e.removeDelay);else{let t=R.get(e.id);t&&(clearTimeout(t),R.delete(e.id))}})},[t]),{toasts:t,handlers:{updateHeight:L,startPause:M,endPause:s,calculateOffset:a}}},H=j`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,Y=j`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,B=j`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,q=N("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${H} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${Y} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${B} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,U=j`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,V=N("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${U} 1s linear infinite;
`,K=j`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,G=j`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,W=N("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${K} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${G} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,J=N("div")`
  position: absolute;
`,Q=N("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,X=j`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,ee=N("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${X} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,et=({toast:e})=>{let{icon:t,type:r,iconTheme:s}=e;return void 0!==t?"string"==typeof t?i.createElement(ee,null,t):t:"blank"===r?null:i.createElement(Q,null,i.createElement(V,{...s}),"loading"!==r&&i.createElement(J,null,"error"===r?i.createElement(q,{...s}):i.createElement(W,{...s})))},er=e=>`
0% {transform: translate3d(0,${-200*e}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,es=e=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${-150*e}%,-1px) scale(.6); opacity:0;}
`,ea=N("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,en=N("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,ei=(e,t)=>{let r=e.includes("top")?1:-1,[s,a]=k()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[er(r),es(r)];return{animation:t?`${j(s)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${j(a)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},eo=i.memo(({toast:e,position:t,style:r,children:s})=>{let a=e.height?ei(e.position||t||"top-center",e.visible):{opacity:0},n=i.createElement(et,{toast:e}),o=i.createElement(en,{...e.ariaProps},C(e.message,e));return i.createElement(ea,{className:e.className,style:{...a,...r,...e.style}},"function"==typeof s?s({icon:n,message:o}):i.createElement(i.Fragment,null,n,o))});n=i.createElement,m.p=void 0,g=n,v=void 0,b=void 0;var el=({id:e,className:t,style:r,onHeightUpdate:s,children:a})=>{let n=i.useCallback(t=>{if(t){let r=()=>{s(e,t.getBoundingClientRect().height)};r(),new MutationObserver(r).observe(t,{subtree:!0,childList:!0,characterData:!0})}},[e,s]);return i.createElement("div",{ref:n,className:t,style:r},a)},ec=(e,t)=>{let r=e.includes("top"),s=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:k()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(r?1:-1)}px)`,...r?{top:0}:{bottom:0},...s}},ed=y`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,eu=({reverseOrder:e,position:t="top-center",toastOptions:r,gutter:s,children:a,containerStyle:n,containerClassName:o})=>{let{toasts:l,handlers:c}=F(r);return i.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...n},className:o,onMouseEnter:c.startPause,onMouseLeave:c.endPause},l.map(r=>{let n=r.position||t,o=ec(n,c.calculateOffset(r,{reverseOrder:e,gutter:s,defaultPosition:t}));return i.createElement(el,{id:r.id,key:r.id,onHeightUpdate:c.updateHeight,className:r.visible?ed:"",style:o},"custom"===r.type?C(r.message,r):a?a(r):i.createElement(eo,{toast:r,position:n}))}))},em=O}},function(e){e.O(0,[6723,1396,2971,4938,1744],function(){return e(e.s=24236)}),_N_E=e.O()}]);