import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET /api/payments - Get all payments for the user's company
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const status = searchParams.get('status')
    const method = searchParams.get('method')
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')

    // Build where clause
    const where: any = {
      companyId: session.user.companyId,
      type: 'PAYMENT'
    }

    if (status && status !== 'all') {
      where.paymentStatus = status
    }

    if (method && method !== 'all') {
      where.paymentMethod = method
    }

    if (startDate || endDate) {
      where.transactionDate = {}
      if (startDate) {
        where.transactionDate.gte = new Date(startDate)
      }
      if (endDate) {
        where.transactionDate.lte = new Date(endDate)
      }
    }

    // Get payments with pagination
    const [payments, total] = await Promise.all([
      prisma.transaction.findMany({
        where,
        include: {
          invoice: {
            select: {
              id: true,
              invoiceNumber: true,
              customer: {
                select: {
                  id: true,
                  name: true,
                  company: true
                }
              }
            }
          },
          createdBy: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        },
        orderBy: { transactionDate: 'desc' },
        skip: (page - 1) * limit,
        take: limit
      }),
      prisma.transaction.count({ where })
    ])

    // Transform the data
    const transformedPayments = payments.map(payment => ({
      id: payment.id,
      amount: Number(payment.amount),
      paymentDate: payment.transactionDate.toISOString(),
      paymentMethod: payment.paymentMethod || 'OTHER',
      reference: payment.reference,
      notes: payment.notes,
      status: payment.paymentStatus,
      invoice: payment.invoice ? {
        id: payment.invoice.id,
        invoiceNumber: payment.invoice.invoiceNumber,
        customer: {
          id: payment.invoice.customer.id,
          name: payment.invoice.customer.name,
          company: payment.invoice.customer.company
        }
      } : null,
      createdBy: payment.createdBy ? {
        name: payment.createdBy.name,
        email: payment.createdBy.email
      } : null,
      createdAt: payment.createdAt?.toISOString() || payment.transactionDate.toISOString()
    }))

    // Calculate summary statistics
    const summary = await prisma.transaction.aggregate({
      where: {
        companyId: session.user.companyId,
        type: 'PAYMENT'
      },
      _sum: {
        amount: true
      },
      _count: {
        id: true
      }
    })

    const completedSummary = await prisma.transaction.aggregate({
      where: {
        companyId: session.user.companyId,
        type: 'PAYMENT',
        paymentStatus: 'COMPLETED'
      },
      _sum: {
        amount: true
      }
    })

    return NextResponse.json({
      payments: transformedPayments,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      summary: {
        totalAmount: Number(summary._sum.amount || 0),
        totalCount: summary._count.id,
        completedAmount: Number(completedSummary._sum.amount || 0)
      }
    })

  } catch (error) {
    console.error('Error fetching payments:', error)
    return NextResponse.json(
      { error: 'Failed to fetch payments' },
      { status: 500 }
    )
  }
}

// POST /api/payments - Create a new payment (for standalone payments)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const {
      amount,
      paymentMethod,
      reference,
      notes,
      customerId,
      description
    } = body

    // Validate required fields
    if (!amount || amount <= 0) {
      return NextResponse.json(
        { error: 'Valid amount is required' },
        { status: 400 }
      )
    }

    // Create the payment transaction
    const payment = await prisma.transaction.create({
      data: {
        type: 'PAYMENT',
        amount: amount,
        paymentMethod: paymentMethod || 'OTHER',
        paymentStatus: 'COMPLETED',
        reference,
        notes,
        description: description || 'Standalone payment',
        companyId: session.user.companyId,
        createdById: session.user.id,
        customerId: customerId || null,
        transactionDate: new Date()
      },
      include: {
        createdBy: {
          select: {
            name: true,
            email: true
          }
        },
        customer: {
          select: {
            id: true,
            name: true,
            company: true
          }
        }
      }
    })

    // Log activity
    await prisma.activity.create({
      data: {
        type: 'PAYMENT',
        title: 'Payment Recorded',
        description: `Standalone payment of $${amount} recorded`,
        companyId: session.user.companyId,
        createdById: session.user.id,
        customerId: customerId || null
      }
    })

    return NextResponse.json({
      payment: {
        id: payment.id,
        amount: Number(payment.amount),
        paymentDate: payment.transactionDate.toISOString(),
        paymentMethod: payment.paymentMethod,
        reference: payment.reference,
        notes: payment.notes,
        status: payment.paymentStatus,
        createdBy: payment.createdBy,
        customer: payment.customer,
        createdAt: payment.createdAt?.toISOString() || payment.transactionDate.toISOString()
      },
      message: 'Payment recorded successfully'
    })

  } catch (error) {
    console.error('Error creating payment:', error)
    return NextResponse.json(
      { error: 'Failed to create payment' },
      { status: 500 }
    )
  }
}
