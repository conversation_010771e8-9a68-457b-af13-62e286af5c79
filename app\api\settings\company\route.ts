import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const companySettingsSchema = z.object({
  // Company Information
  companyName: z.string().optional(),
  companyEmail: z.string().email().optional().or(z.literal('')),
  companyPhone: z.string().optional(),
  companyAddress: z.string().optional(),
  companyCity: z.string().optional(),
  companyState: z.string().optional(),
  companyCountry: z.string().optional(),
  companyPostalCode: z.string().optional(),
  companyWebsite: z.string().url().optional().or(z.literal('')),
  companyLogo: z.string().optional(),
  
  // Business Details
  industry: z.string().optional(),
  businessType: z.string().optional(),
  taxId: z.string().optional(),
  registrationNumber: z.string().optional(),
  
  // Financial Settings
  defaultCurrency: z.string().default('USD'),
  taxRate: z.number().min(0).max(100).default(0),
  
  // Localization
  timezone: z.string().default('UTC'),
  dateFormat: z.string().default('MM/dd/yyyy'),
  timeFormat: z.string().default('12'),
  language: z.string().default('en'),
  
  // Branding
  primaryColor: z.string().default('#3b82f6'),
  secondaryColor: z.string().default('#1e3a8a'),
  accentColor: z.string().default('#f59e0b'),
  fontFamily: z.string().default('Inter'),
  
  // Document Settings
  invoicePrefix: z.string().default('INV'),
  quotationPrefix: z.string().default('QUO'),
  contractPrefix: z.string().default('CON'),
  invoiceNumbering: z.string().default('sequential'),
  
  // Payment Settings
  defaultPaymentTerms: z.string().optional(),
  bankDetails: z.any().optional(),
  paymentMethods: z.any().optional(),
  
  // Settings Objects
  emailSettings: z.any().optional(),
  notificationSettings: z.any().optional(),
  securitySettings: z.any().optional(),
  integrationSettings: z.any().optional(),
  featureSettings: z.any().optional(),
  customSettings: z.any().optional()
})

// GET /api/settings/company - Get company settings
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const settings = await prisma.companySettings.findUnique({
      where: {
        companyId: session.user.companyId
      },
      include: {
        company: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            address: true,
            city: true,
            state: true,
            country: true,
            postalCode: true,
            website: true,
            logo: true,
            industry: true,
            size: true,
            businessType: true,
            taxId: true,
            registrationNumber: true
          }
        }
      }
    })

    // If no settings exist, create default settings
    if (!settings) {
      const defaultSettings = await prisma.companySettings.create({
        data: {
          companyId: session.user.companyId,
          companyName: session.user.company?.name || '',
          defaultCurrency: 'USD',
          timezone: 'UTC',
          dateFormat: 'MM/dd/yyyy',
          timeFormat: '12',
          language: 'en',
          primaryColor: '#3b82f6',
          secondaryColor: '#1e3a8a',
          accentColor: '#f59e0b',
          fontFamily: 'Inter',
          invoicePrefix: 'INV',
          quotationPrefix: 'QUO',
          contractPrefix: 'CON',
          invoiceNumbering: 'sequential',
          emailSettings: {
            enabled: false,
            smtpHost: '',
            smtpPort: 587,
            smtpUsername: '',
            smtpPassword: '',
            smtpEncryption: 'tls',
            fromEmail: '',
            fromName: '',
            replyTo: ''
          },
          notificationSettings: {
            emailNotifications: true,
            pushNotifications: true,
            smsNotifications: false,
            invoiceNotifications: true,
            quotationNotifications: true,
            contractNotifications: true,
            paymentNotifications: true,
            reminderNotifications: true
          },
          securitySettings: {
            twoFactorRequired: false,
            sessionTimeout: 30,
            passwordPolicy: {
              minLength: 8,
              requireUppercase: true,
              requireLowercase: true,
              requireNumbers: true,
              requireSymbols: false
            },
            ipWhitelist: [],
            allowedDomains: []
          },
          integrationSettings: {
            webhooks: [],
            apiKeys: {},
            connectedServices: {}
          },
          featureSettings: {
            enableQuotations: true,
            enableInvoices: true,
            enableContracts: true,
            enableTasks: true,
            enableReports: true,
            enableAnalytics: true,
            enableFileStorage: true,
            enableTeamCollaboration: true
          }
        },
        include: {
          company: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
              address: true,
              city: true,
              state: true,
              country: true,
              postalCode: true,
              website: true,
              logo: true,
              industry: true,
              size: true,
              businessType: true,
              taxId: true,
              registrationNumber: true
            }
          }
        }
      })

      return NextResponse.json({
        settings: {
          ...defaultSettings,
          taxRate: Number(defaultSettings.taxRate)
        }
      })
    }

    return NextResponse.json({
      settings: {
        ...settings,
        taxRate: Number(settings.taxRate)
      }
    })

  } catch (error) {
    console.error('Error fetching company settings:', error)
    return NextResponse.json(
      { error: 'Failed to fetch company settings' },
      { status: 500 }
    )
  }
}

// PUT /api/settings/company - Update company settings
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = companySettingsSchema.parse(body)

    const settings = await prisma.$transaction(async (tx) => {
      // Update or create company settings
      const updatedSettings = await tx.companySettings.upsert({
        where: {
          companyId: session.user.companyId!
        },
        update: {
          ...validatedData,
          updatedAt: new Date()
        },
        create: {
          ...validatedData,
          companyId: session.user.companyId!
        },
        include: {
          company: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
              address: true,
              city: true,
              state: true,
              country: true,
              postalCode: true,
              website: true,
              logo: true,
              industry: true,
              size: true,
              businessType: true,
              taxId: true,
              registrationNumber: true
            }
          }
        }
      })

      // Also update company basic info if provided
      if (validatedData.companyName || validatedData.companyEmail || validatedData.companyPhone) {
        await tx.company.update({
          where: {
            id: session.user.companyId!
          },
          data: {
            ...(validatedData.companyName && { name: validatedData.companyName }),
            ...(validatedData.companyEmail && { email: validatedData.companyEmail }),
            ...(validatedData.companyPhone && { phone: validatedData.companyPhone }),
            ...(validatedData.companyAddress && { address: validatedData.companyAddress }),
            ...(validatedData.companyCity && { city: validatedData.companyCity }),
            ...(validatedData.companyState && { state: validatedData.companyState }),
            ...(validatedData.companyCountry && { country: validatedData.companyCountry }),
            ...(validatedData.companyPostalCode && { postalCode: validatedData.companyPostalCode }),
            ...(validatedData.companyWebsite && { website: validatedData.companyWebsite }),
            ...(validatedData.companyLogo && { logo: validatedData.companyLogo }),
            ...(validatedData.industry && { industry: validatedData.industry }),
            ...(validatedData.businessType && { businessType: validatedData.businessType }),
            ...(validatedData.taxId && { taxId: validatedData.taxId }),
            ...(validatedData.registrationNumber && { registrationNumber: validatedData.registrationNumber })
          }
        })
      }

      // Log activity
      await tx.activity.create({
        data: {
          type: 'SETTINGS',
          title: 'Company Settings Updated',
          description: 'Company settings were updated',
          companyId: session.user.companyId!,
          createdById: session.user.id
        }
      })

      return updatedSettings
    })

    return NextResponse.json({
      settings: {
        ...settings,
        taxRate: Number(settings.taxRate)
      },
      message: 'Company settings updated successfully'
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating company settings:', error)
    return NextResponse.json(
      { error: 'Failed to update company settings' },
      { status: 500 }
    )
  }
}
