"use strict";(()=>{var e={};e.id=4596,e.ids=[4596],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6113:e=>{e.exports=require("crypto")},64068:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>w,originalPathname:()=>j,patchFetch:()=>x,requestAsyncStorage:()=>h,routeModule:()=>p,serverHooks:()=>g,staticGenerationAsyncStorage:()=>v,staticGenerationBailout:()=>y});var a={};t.r(a),t.d(a,{POST:()=>f});var i=t(95419),n=t(69108),o=t(99678),s=t(78070),u=t(9108),l=t(11896),c=t(25252),d=t(52178);let m=c.Ry({email:c.Z_().email("Invalid email address")});async function f(e){try{let r=await e.json(),{email:t}=m.parse(r),a=await u._.user.findUnique({where:{email:t}});if(!a)return s.Z.json({error:"User not found"},{status:404});if(a.emailVerified)return s.Z.json({error:"Email is already verified"},{status:400});let i=a.emailVerificationExpires;if(i&&new Date().getTime()-i.getTime()<6e4)return s.Z.json({error:"Please wait before requesting another verification email"},{status:429});let n=(0,l.cl)(),o=new Date(Date.now()+864e5);await u._.user.update({where:{id:a.id},data:{emailVerificationToken:n,emailVerificationExpires:o}});try{await (0,l.zk)(a.email,a.name||`${a.firstName} ${a.lastName}`,n)}catch(e){return console.error("Failed to send verification email:",e),s.Z.json({error:"Failed to send verification email"},{status:500})}return console.log(`Verification email resent to: ${a.email}`),s.Z.json({message:"Verification email sent successfully"})}catch(e){if(e instanceof d.jm)return s.Z.json({error:"Invalid request data",details:e.errors},{status:400});return console.error("Resend verification error:",e),s.Z.json({error:"Internal server error"},{status:500})}}let p=new i.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/auth/resend-verification/route",pathname:"/api/auth/resend-verification",filename:"route",bundlePath:"app/api/auth/resend-verification/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\auth\\resend-verification\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:h,staticGenerationAsyncStorage:v,serverHooks:g,headerHooks:w,staticGenerationBailout:y}=p,j="/api/auth/resend-verification/route";function x(){return(0,o.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:v})}},11896:(e,r,t)=>{t.d(r,{LS:()=>l,cl:()=>o,uE:()=>s,zk:()=>u});var a=t(6113),i=t.n(a);let n={from:process.env.EMAIL_FROM||"<EMAIL>",baseUrl:"http://localhost:3000"};function o(){return i().randomBytes(32).toString("hex")}function s(){return i().randomBytes(32).toString("hex")}async function u(e,r,t){n.baseUrl,n.from,console.log("\uD83D\uDCE7 Email would be sent to:",e)}async function l(e,r,t){n.baseUrl,n.from,console.log("\uD83D\uDCE7 Password reset email would be sent to:",e)}},9108:(e,r,t)=>{t.d(r,{_:()=>i});let a=require("@prisma/client"),i=globalThis.prisma??new a.PrismaClient}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[1638,6206,5252],()=>t(64068));module.exports=a})();