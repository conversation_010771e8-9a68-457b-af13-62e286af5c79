"use strict";exports.id=9989,exports.ids=[9989],exports.modules={7004:(e,t,s)=>{s.d(t,{d:()=>v});var i=s(95344),a=s(3729),l=s(60708),n=s(55697),r=s(12374),d=s(16212),o=s(92549),c=s(1586),u=s(16802),m=s(51838),x=s(38271),h=s(55329),p=s(44669);let y=r.Ry({description:r.Z_().min(1,"Description is required"),quantity:r.Rx().min(1,"Quantity must be at least 1"),unitPrice:r.Rx().min(0,"Unit price must be positive"),discount:r.Rx().min(0).max(100).default(0),taxRate:r.Rx().min(0).max(100).default(0)}),j=r.Ry({title:r.Z_().min(1,"Title is required"),description:r.Z_().optional(),customerId:r.Z_().min(1,"Customer is required"),leadId:r.Z_().optional(),status:r.Km(["DRAFT","SENT","VIEWED","ACCEPTED","REJECTED","EXPIRED"]).default("DRAFT"),validUntil:r.Z_().optional(),terms:r.Z_().optional(),notes:r.Z_().optional(),paymentTerms:r.Z_().optional(),paymentDueDays:r.Rx().min(0).default(30),items:r.IX(y).min(1,"At least one item is required"),taxRate:r.Rx().min(0).max(100).default(0),discountType:r.Km(["PERCENTAGE","FIXED"]).default("PERCENTAGE"),discountValue:r.Rx().min(0).default(0)});function v({isOpen:e,onClose:t,onSuccess:s,quotation:r,mode:y,preselectedCustomerId:v,preselectedLeadId:f}){let[g,b]=(0,a.useState)(!1),[N,E]=(0,a.useState)([]),[T,R]=(0,a.useState)([]),{register:w,handleSubmit:I,formState:{errors:D},reset:A,watch:F,control:k,setValue:C}=(0,l.cI)({resolver:(0,n.F)(j),defaultValues:r?{title:r.title,description:r.description||"",customerId:r.customerId||v||"",leadId:r.leadId||f||"",status:r.status||"DRAFT",validUntil:r.validUntil?new Date(r.validUntil).toISOString().split("T")[0]:"",terms:r.terms||"",notes:r.notes||"",items:r.items||[{description:"",quantity:1,unitPrice:0,discount:0,taxRate:0}],taxRate:r.taxRate||0,discountType:r.discountType||"PERCENTAGE",discountValue:r.discountValue||0}:{status:"DRAFT",customerId:v||"",leadId:f||"",items:[{description:"",quantity:1,unitPrice:0,discount:0,taxRate:0}],taxRate:0,discountType:"PERCENTAGE",discountValue:0}}),{fields:P,append:_,remove:Z}=(0,l.Dq)({control:k,name:"items"}),q=F("items"),S=F("taxRate"),$=F("discountType"),M=F("discountValue");(0,a.useEffect)(()=>{let t=async()=>{try{let[e,t]=await Promise.all([fetch("/api/customers?limit=100"),fetch("/api/leads?limit=100")]);if(e.ok){let t=await e.json();E(t.customers)}if(t.ok){let e=await t.json();R(e.leads)}}catch(e){console.error("Error fetching data:",e)}};e&&t()},[e]);let V=(()=>{let e=q.reduce((e,t)=>{let s=(t.quantity||0)*(t.unitPrice||0),i=s*(t.discount||0)/100,a=s-i,l=a*(t.taxRate||0)/100;return e+a+l},0),t=e,s=(t="PERCENTAGE"===$?e-e*(M||0)/100:e-(M||0))*(S||0)/100;return{subtotal:Math.round(100*e)/100,total:Math.round(100*(t+s))/100,taxAmount:Math.round(100*s)/100,discountAmount:"PERCENTAGE"===$?Math.round(e*(M||0)/100*100)/100:M||0}})(),U=e=>{P.length>1&&Z(e)},z=async e=>{b(!0);try{let i="create"===y?"/api/quotations":`/api/quotations/${r.id}`,a=await fetch(i,{method:"create"===y?"POST":"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!a.ok){let e=await a.json();throw Error(e.error||"Failed to save quotation")}p.toast.success(`Quotation ${"create"===y?"created":"updated"} successfully`),A(),s(),t()}catch(e){p.toast.error(e instanceof Error?e.message:"An error occurred")}finally{b(!1)}},G=()=>{A(),t()};return i.jsx(u.Vq,{open:e,onOpenChange:G,children:(0,i.jsxs)(u.cZ,{className:"max-w-6xl max-h-[90vh] overflow-y-auto",children:[(0,i.jsxs)(u.fK,{children:[i.jsx(u.$N,{children:"create"===y?"Create New Quotation":"Edit Quotation"}),i.jsx(u.Be,{children:"create"===y?"Create a new quotation with items, pricing, and terms.":"Update the quotation information and items."})]}),(0,i.jsxs)("form",{onSubmit:I(z),className:"space-y-6",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[i.jsx(c._,{htmlFor:"title",children:"Title *"}),i.jsx(o.I,{id:"title",...w("title"),placeholder:"Quotation title"}),D.title&&i.jsx("p",{className:"text-sm text-red-600 mt-1",children:D.title.message})]}),(0,i.jsxs)("div",{children:[i.jsx(c._,{htmlFor:"status",children:"Status"}),i.jsx("select",{id:"status",...w("status"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[{value:"DRAFT",label:"Draft"},{value:"SENT",label:"Sent"},{value:"VIEWED",label:"Viewed"},{value:"ACCEPTED",label:"Accepted"},{value:"REJECTED",label:"Rejected"},{value:"EXPIRED",label:"Expired"}].map(e=>i.jsx("option",{value:e.value,children:e.label},e.value))})]}),(0,i.jsxs)("div",{children:[i.jsx(c._,{htmlFor:"customerId",children:"Customer *"}),(0,i.jsxs)("select",{id:"customerId",...w("customerId"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[i.jsx("option",{value:"",children:"Select a customer"}),N.map(e=>(0,i.jsxs)("option",{value:e.id,children:[e.name," ",e.company&&`(${e.company})`]},e.id))]}),D.customerId&&i.jsx("p",{className:"text-sm text-red-600 mt-1",children:D.customerId.message})]}),(0,i.jsxs)("div",{children:[i.jsx(c._,{htmlFor:"leadId",children:"Related Lead"}),(0,i.jsxs)("select",{id:"leadId",...w("leadId"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[i.jsx("option",{value:"",children:"Select a lead (optional)"}),T.map(e=>i.jsx("option",{value:e.id,children:e.name},e.id))]})]}),(0,i.jsxs)("div",{children:[i.jsx(c._,{htmlFor:"validUntil",children:"Valid Until"}),i.jsx(o.I,{id:"validUntil",type:"date",...w("validUntil")})]})]}),(0,i.jsxs)("div",{children:[i.jsx(c._,{htmlFor:"description",children:"Description"}),i.jsx("textarea",{id:"description",...w("description"),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Brief description of the quotation..."})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[i.jsx("h3",{className:"text-lg font-medium",children:"Items"}),(0,i.jsxs)(d.z,{type:"button",onClick:()=>{_({description:"",quantity:1,unitPrice:0,discount:0,taxRate:0})},size:"sm",children:[i.jsx(m.Z,{className:"h-4 w-4 mr-2"}),"Add Item"]})]}),i.jsx("div",{className:"space-y-4",children:P.map((e,t)=>(0,i.jsxs)("div",{className:"grid grid-cols-12 gap-2 items-end p-4 border rounded-lg",children:[(0,i.jsxs)("div",{className:"col-span-4",children:[i.jsx(c._,{htmlFor:`items.${t}.description`,children:"Description *"}),i.jsx(o.I,{...w(`items.${t}.description`),placeholder:"Item description"}),D.items?.[t]?.description&&i.jsx("p",{className:"text-sm text-red-600 mt-1",children:D.items[t]?.description?.message})]}),(0,i.jsxs)("div",{className:"col-span-2",children:[i.jsx(c._,{htmlFor:`items.${t}.quantity`,children:"Qty *"}),i.jsx(o.I,{type:"number",min:"1",...w(`items.${t}.quantity`,{valueAsNumber:!0}),placeholder:"1"})]}),(0,i.jsxs)("div",{className:"col-span-2",children:[i.jsx(c._,{htmlFor:`items.${t}.unitPrice`,children:"Unit Price *"}),i.jsx(o.I,{type:"number",min:"0",step:"0.01",...w(`items.${t}.unitPrice`,{valueAsNumber:!0}),placeholder:"0.00"})]}),(0,i.jsxs)("div",{className:"col-span-1",children:[i.jsx(c._,{htmlFor:`items.${t}.discount`,children:"Disc %"}),i.jsx(o.I,{type:"number",min:"0",max:"100",...w(`items.${t}.discount`,{valueAsNumber:!0}),placeholder:"0"})]}),(0,i.jsxs)("div",{className:"col-span-1",children:[i.jsx(c._,{htmlFor:`items.${t}.taxRate`,children:"Tax %"}),i.jsx(o.I,{type:"number",min:"0",max:"100",...w(`items.${t}.taxRate`,{valueAsNumber:!0}),placeholder:"0"})]}),(0,i.jsxs)("div",{className:"col-span-1",children:[i.jsx(c._,{children:"Total"}),(0,i.jsxs)("div",{className:"px-3 py-2 bg-gray-50 rounded-md text-sm",children:["$",(()=>{let e=q[t];if(!e)return"0.00";let s=(e.quantity||0)*(e.unitPrice||0),i=s*(e.discount||0)/100,a=s-i,l=a*(e.taxRate||0)/100;return(a+l).toFixed(2)})()]})]}),i.jsx("div",{className:"col-span-1",children:i.jsx(d.z,{type:"button",variant:"destructive",size:"sm",onClick:()=>U(t),disabled:1===P.length,children:i.jsx(x.Z,{className:"h-4 w-4"})})})]},e.id))})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{className:"space-y-4",children:[i.jsx("h3",{className:"text-lg font-medium",children:"Settings"}),(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[i.jsx(c._,{htmlFor:"discountType",children:"Discount Type"}),(0,i.jsxs)("select",{id:"discountType",...w("discountType"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[i.jsx("option",{value:"PERCENTAGE",children:"Percentage"}),i.jsx("option",{value:"FIXED",children:"Fixed Amount"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsxs)(c._,{htmlFor:"discountValue",children:["Discount ","PERCENTAGE"===$?"%":"$"]}),i.jsx(o.I,{type:"number",min:"0",step:"0.01",...w("discountValue",{valueAsNumber:!0}),placeholder:"0"})]})]}),(0,i.jsxs)("div",{children:[i.jsx(c._,{htmlFor:"taxRate",children:"Overall Tax Rate (%)"}),i.jsx(o.I,{type:"number",min:"0",max:"100",step:"0.01",...w("taxRate",{valueAsNumber:!0}),placeholder:"0"})]})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("h3",{className:"text-lg font-medium flex items-center",children:[i.jsx(h.Z,{className:"h-5 w-5 mr-2"}),"Totals"]}),(0,i.jsxs)("div",{className:"space-y-2 p-4 bg-gray-50 rounded-lg",children:[(0,i.jsxs)("div",{className:"flex justify-between",children:[i.jsx("span",{children:"Subtotal:"}),(0,i.jsxs)("span",{children:["$",V.subtotal.toFixed(2)]})]}),V.discountAmount>0&&(0,i.jsxs)("div",{className:"flex justify-between text-red-600",children:[i.jsx("span",{children:"Discount:"}),(0,i.jsxs)("span",{children:["-$",V.discountAmount.toFixed(2)]})]}),V.taxAmount>0&&(0,i.jsxs)("div",{className:"flex justify-between",children:[i.jsx("span",{children:"Tax:"}),(0,i.jsxs)("span",{children:["$",V.taxAmount.toFixed(2)]})]}),(0,i.jsxs)("div",{className:"flex justify-between font-bold text-lg border-t pt-2",children:[i.jsx("span",{children:"Total:"}),(0,i.jsxs)("span",{children:["$",V.total.toFixed(2)]})]})]})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[i.jsx(c._,{htmlFor:"paymentTerms",children:"Payment Terms"}),i.jsx("textarea",{id:"paymentTerms",...w("paymentTerms"),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Net 30, 2/10 Net 30, Payment due upon receipt, etc..."})]}),(0,i.jsxs)("div",{children:[i.jsx(c._,{htmlFor:"paymentDueDays",children:"Payment Due (Days)"}),i.jsx(o.I,{id:"paymentDueDays",type:"number",min:"0",...w("paymentDueDays",{valueAsNumber:!0}),placeholder:"30"}),i.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Number of days after invoice date"})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[i.jsx(c._,{htmlFor:"terms",children:"Terms & Conditions"}),i.jsx("textarea",{id:"terms",...w("terms"),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"General terms, delivery conditions, warranties, etc..."})]}),(0,i.jsxs)("div",{children:[i.jsx(c._,{htmlFor:"notes",children:"Internal Notes"}),i.jsx("textarea",{id:"notes",...w("notes"),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Internal notes (not visible to customer)..."})]})]}),(0,i.jsxs)(u.cN,{children:[i.jsx(d.z,{type:"button",variant:"outline",onClick:G,children:"Cancel"}),i.jsx(d.z,{type:"submit",disabled:g,children:g?"Saving...":"create"===y?"Create Quotation":"Update Quotation"})]})]})]})})}},55329:(e,t,s)=>{s.d(t,{Z:()=>i});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,s(69224).Z)("Calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]])},55794:(e,t,s)=>{s.d(t,{Z:()=>i});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,s(69224).Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},1960:(e,t,s)=>{s.d(t,{Z:()=>i});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,s(69224).Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},48411:(e,t,s)=>{s.d(t,{Z:()=>i});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,s(69224).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},96885:(e,t,s)=>{s.d(t,{Z:()=>i});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,s(69224).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},36135:(e,t,s)=>{s.d(t,{Z:()=>i});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,s(69224).Z)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]])}};