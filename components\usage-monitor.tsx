'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  AlertTriangle, 
  AlertCircle, 
  Info, 
  X, 
  TrendingUp,
  Users,
  Database,
  FileText,
  Receipt,
  FileContract
} from 'lucide-react'
import { useRouter } from 'next/navigation'

interface UsageAlert {
  id: string
  type: 'error' | 'warning' | 'info'
  title: string
  message: string
  percentage: number | null
  action: {
    label: string
    url: string
  }
  priority: 'high' | 'medium' | 'low'
}

interface UsageData {
  alerts: UsageAlert[]
  subscription: {
    planName: string
    status: string
    trialEnd?: string
  }
}

interface UsageMonitorProps {
  showAlerts?: boolean
  showQuickStats?: boolean
  compact?: boolean
  className?: string
}

export default function UsageMonitor({ 
  showAlerts = true, 
  showQuickStats = false, 
  compact = false,
  className = '' 
}: UsageMonitorProps) {
  const { data: session } = useSession()
  const router = useRouter()
  const [usageData, setUsageData] = useState<UsageData | null>(null)
  const [dismissedAlerts, setDismissedAlerts] = useState<Set<string>>(new Set())
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (session?.user) {
      fetchUsageAlerts()
    }
  }, [session])

  const fetchUsageAlerts = async () => {
    try {
      const response = await fetch('/api/usage/alerts')
      const data = await response.json()
      
      if (data.success) {
        setUsageData(data.data)
      }
    } catch (error) {
      console.error('Error fetching usage alerts:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleDismissAlert = async (alertId: string) => {
    setDismissedAlerts(prev => new Set([...prev, alertId]))
    
    try {
      await fetch('/api/usage/alerts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          alertId,
          action: 'dismiss'
        })
      })
    } catch (error) {
      console.error('Error dismissing alert:', error)
    }
  }

  const handleAlertAction = (alert: UsageAlert) => {
    router.push(alert.action.url)
  }

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'error':
        return <AlertTriangle className="h-4 w-4" />
      case 'warning':
        return <AlertCircle className="h-4 w-4" />
      case 'info':
        return <Info className="h-4 w-4" />
      default:
        return <Info className="h-4 w-4" />
    }
  }

  const getAlertVariant = (type: string) => {
    switch (type) {
      case 'error':
        return 'destructive'
      case 'warning':
        return 'default'
      case 'info':
        return 'default'
      default:
        return 'default'
    }
  }

  if (loading || !usageData || !session?.user) {
    return null
  }

  const visibleAlerts = usageData.alerts.filter(alert => !dismissedAlerts.has(alert.id))

  if (!showAlerts && !showQuickStats) {
    return null
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Usage Alerts */}
      {showAlerts && visibleAlerts.length > 0 && (
        <div className="space-y-3">
          {visibleAlerts.slice(0, compact ? 2 : 5).map((alert) => (
            <Alert 
              key={alert.id} 
              variant={getAlertVariant(alert.type)}
              className="relative"
            >
              <div className="flex items-start gap-3">
                {getAlertIcon(alert.type)}
                <div className="flex-1 min-w-0">
                  <AlertTitle className="text-sm font-medium">
                    {alert.title}
                  </AlertTitle>
                  <AlertDescription className="text-sm mt-1">
                    {alert.message}
                  </AlertDescription>
                  
                  {alert.percentage !== null && (
                    <div className="mt-2">
                      <Progress 
                        value={alert.percentage} 
                        className="h-2"
                        style={{ 
                          '--progress-background': alert.percentage >= 90 ? '#ef4444' : '#f59e0b' 
                        } as any}
                      />
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>{alert.percentage}% used</span>
                      </div>
                    </div>
                  )}
                  
                  <div className="flex items-center gap-2 mt-3">
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => handleAlertAction(alert)}
                    >
                      {alert.action.label}
                    </Button>
                    <Badge variant="secondary" className="text-xs">
                      {alert.priority}
                    </Badge>
                  </div>
                </div>
                
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 absolute top-2 right-2"
                  onClick={() => handleDismissAlert(alert.id)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            </Alert>
          ))}
          
          {visibleAlerts.length > (compact ? 2 : 5) && (
            <div className="text-center">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => router.push('/subscription?tab=usage')}
              >
                View All Alerts ({visibleAlerts.length - (compact ? 2 : 5)} more)
              </Button>
            </div>
          )}
        </div>
      )}

      {/* Quick Stats */}
      {showQuickStats && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Usage Overview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center">
              <div className="text-lg font-semibold">
                {usageData.subscription.planName}
              </div>
              <Badge 
                variant={usageData.subscription.status === 'ACTIVE' ? 'default' : 'secondary'}
                className="text-xs"
              >
                {usageData.subscription.status}
              </Badge>
              
              <div className="mt-3">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => router.push('/subscription')}
                >
                  View Details
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
