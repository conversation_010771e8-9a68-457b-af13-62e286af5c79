(()=>{var e={};e.id=6715,e.ids=[6715],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},91822:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>p,pages:()=>d,routeModule:()=>m,tree:()=>c});var a=s(50482),r=s(69108),i=s(62563),n=s.n(i),l=s(68300),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c=["",{children:["super-admin",{children:["payment-gateways",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,229)),"C:\\proj\\nextjs-saas\\app\\super-admin\\payment-gateways\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,11285)),"C:\\proj\\nextjs-saas\\app\\super-admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,59504)),"C:\\proj\\nextjs-saas\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\proj\\nextjs-saas\\app\\super-admin\\payment-gateways\\page.tsx"],p="/super-admin/payment-gateways/page",u={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/super-admin/payment-gateways/page",pathname:"/super-admin/payment-gateways",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},95803:(e,t,s)=>{Promise.resolve().then(s.bind(s,96748))},96748:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>S});var a=s(95344),r=s(3729),i=s(47674),n=s(22254),l=s(61351),o=s(16212),c=s(92549),d=s(1586),p=s(71809),u=s(25757),m=s(69436),h=s(7060),x=s(73229),f=s(45961),y=s(16469),b=s(33733),g=s(31498),v=s(85674),j=s(1222),w=s(53148),N=s(23485),k=s(44669);function S(){let{data:e,status:t}=(0,i.useSession)(),[s,S]=(0,r.useState)([]),[C,P]=(0,r.useState)({stripe:{enabled:!1,live:!1,publishableKey:"",secretKey:"",webhookSecret:"",supportedMethods:["card","apple_pay","google_pay"]},paypal:{enabled:!1,live:!1,clientId:"",clientSecret:"",webhookId:"",supportedMethods:["paypal","venmo"]},razorpay:{enabled:!1,live:!1,keyId:"",keySecret:"",webhookSecret:"",supportedMethods:["card","netbanking","upi","wallet"]},square:{enabled:!1,live:!1,applicationId:"",accessToken:"",webhookSignatureKey:"",supportedMethods:["card","apple_pay","google_pay"]}}),[_,M]=(0,r.useState)(!0),[Z,E]=(0,r.useState)(!1),[R,I]=(0,r.useState)({});if("loading"===t)return a.jsx("div",{className:"min-h-screen flex items-center justify-center",children:a.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})});"unauthenticated"===t&&(0,n.redirect)("/auth/signin"),e?.user?.role!=="SUPER_ADMIN"&&(0,n.redirect)("/dashboard");let q=async()=>{try{M(!0);let e=await fetch("/api/super-admin/payment-gateways"),t=await e.json();t.success&&(S(t.gateways),P({...C,...t.config}))}catch(e){console.error("Error fetching payment gateways:",e),k.toast.error("Failed to load payment gateways")}finally{M(!1)}},z=async()=>{try{E(!0);let e=await fetch("/api/super-admin/payment-gateways",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(C)}),t=await e.json();t.success?(k.toast.success("Payment gateway configuration saved successfully"),q()):k.toast.error(t.error||"Failed to save configuration")}catch(e){console.error("Error saving config:",e),k.toast.error("Failed to save configuration")}finally{E(!1)}},K=async e=>{try{let t=await fetch("/api/super-admin/payment-gateways/test",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({provider:e})}),s=await t.json();s.success?k.toast.success(`${e} connection test successful`):k.toast.error(s.error||`${e} connection test failed`)}catch(e){console.error("Error testing connection:",e),k.toast.error("Connection test failed")}};(0,r.useEffect)(()=>{q()},[]);let T=(e,t,s)=>{P(a=>({...a,[e]:{...a[e],[t]:s}}))},D=e=>{I(t=>({...t,[e]:!t[e]}))},W=e=>{switch(e){case"active":return a.jsx(h.Z,{className:"h-5 w-5 text-green-500"});case"error":return a.jsx(x.Z,{className:"h-5 w-5 text-red-500"});default:return a.jsx(f.Z,{className:"h-5 w-5 text-yellow-500"})}};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(y.Z,{className:"h-8 w-8 text-green-600"}),a.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Payment Gateways"})]}),a.jsx("p",{className:"text-gray-500 mt-1",children:"Configure payment processors and methods"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(o.z,{variant:"outline",onClick:q,disabled:_,children:[a.jsx(b.Z,{className:`h-4 w-4 mr-2 ${_?"animate-spin":""}`}),"Refresh"]}),(0,a.jsxs)(o.z,{onClick:z,disabled:Z,children:[a.jsx(g.Z,{className:`h-4 w-4 mr-2 ${Z?"animate-spin":""}`}),"Save Configuration"]})]})]}),a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:Object.entries(C).map(([e,t])=>a.jsx(l.Zb,{children:a.jsx(l.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500 capitalize",children:e}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[W(t.enabled?"active":"inactive"),a.jsx("span",{className:"text-sm font-medium",children:t.enabled?"Enabled":"Disabled"})]}),t.enabled&&a.jsx(m.C,{variant:t.live?"default":"secondary",className:"mt-2",children:t.live?"Live":"Test"})]}),a.jsx("div",{className:"text-right",children:a.jsx(v.Z,{className:"h-8 w-8 text-gray-400"})})]})})},e))}),(0,a.jsxs)(u.mQ,{defaultValue:"stripe",className:"space-y-6",children:[(0,a.jsxs)(u.dr,{className:"grid w-full grid-cols-4",children:[a.jsx(u.SP,{value:"stripe",children:"Stripe"}),a.jsx(u.SP,{value:"paypal",children:"PayPal"}),a.jsx(u.SP,{value:"razorpay",children:"Razorpay"}),a.jsx(u.SP,{value:"square",children:"Square"})]}),a.jsx(u.nU,{value:"stripe",children:(0,a.jsxs)(l.Zb,{children:[(0,a.jsxs)(l.Ol,{children:[(0,a.jsxs)(l.ll,{className:"flex items-center",children:[a.jsx(v.Z,{className:"h-5 w-5 mr-2"}),"Stripe Configuration"]}),a.jsx(l.SZ,{children:"Configure Stripe payment processing"})]}),(0,a.jsxs)(l.aY,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,a.jsxs)("div",{children:[a.jsx(d._,{htmlFor:"stripeEnabled",className:"text-base font-medium",children:"Enable Stripe"}),a.jsx("p",{className:"text-sm text-gray-500",children:"Accept credit cards, Apple Pay, Google Pay, and more"})]}),a.jsx(p.r,{id:"stripeEnabled",checked:C.stripe.enabled,onCheckedChange:e=>T("stripe","enabled",e)})]}),C.stripe.enabled&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,a.jsxs)("div",{children:[a.jsx(d._,{htmlFor:"stripeLive",className:"text-base font-medium",children:"Live Mode"}),a.jsx("p",{className:"text-sm text-gray-500",children:"Use live API keys for production payments"})]}),a.jsx(p.r,{id:"stripeLive",checked:C.stripe.live,onCheckedChange:e=>T("stripe","live",e)})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(d._,{htmlFor:"stripePublishableKey",children:"Publishable Key"}),a.jsx(c.I,{id:"stripePublishableKey",value:C.stripe.publishableKey,onChange:e=>T("stripe","publishableKey",e.target.value),placeholder:C.stripe.live?"pk_live_...":"pk_test_..."})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(d._,{htmlFor:"stripeSecretKey",children:"Secret Key"}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx(c.I,{id:"stripeSecretKey",type:R.stripeSecret?"text":"password",value:C.stripe.secretKey,onChange:e=>T("stripe","secretKey",e.target.value),placeholder:C.stripe.live?"sk_live_...":"sk_test_..."}),a.jsx(o.z,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>D("stripeSecret"),children:R.stripeSecret?a.jsx(j.Z,{className:"h-4 w-4"}):a.jsx(w.Z,{className:"h-4 w-4"})})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(d._,{htmlFor:"stripeWebhookSecret",children:"Webhook Endpoint Secret"}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx(c.I,{id:"stripeWebhookSecret",type:R.stripeWebhook?"text":"password",value:C.stripe.webhookSecret,onChange:e=>T("stripe","webhookSecret",e.target.value),placeholder:"whsec_..."}),a.jsx(o.z,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>D("stripeWebhook"),children:R.stripeWebhook?a.jsx(j.Z,{className:"h-4 w-4"}):a.jsx(w.Z,{className:"h-4 w-4"})})]}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:["Webhook URL: ",window.location.origin,"/api/webhooks/stripe"]})]}),a.jsx("div",{className:"flex items-center space-x-2",children:(0,a.jsxs)(o.z,{variant:"outline",onClick:()=>K("stripe"),disabled:!C.stripe.secretKey,children:[a.jsx(N.Z,{className:"h-4 w-4 mr-2"}),"Test Connection"]})})]})]})]})})]})]})}},92549:(e,t,s)=>{"use strict";s.d(t,{I:()=>n});var a=s(95344),r=s(3729),i=s(91626);let n=r.forwardRef(({className:e,type:t,...s},r)=>a.jsx("input",{type:t,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...s}));n.displayName="Input"},1586:(e,t,s)=>{"use strict";s.d(t,{_:()=>c});var a=s(95344),r=s(3729),i=s(14217),n=s(49247),l=s(91626);let o=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=r.forwardRef(({className:e,...t},s)=>a.jsx(i.f,{ref:s,className:(0,l.cn)(o(),e),...t}));c.displayName=i.f.displayName},71809:(e,t,s)=>{"use strict";s.d(t,{r:()=>N});var a=s(95344),r=s(3729),i=s(85222),n=s(31405),l=s(98462),o=s(33183),c=s(92062),d=s(63085),p=s(62409),u="Switch",[m,h]=(0,l.b)(u),[x,f]=m(u),y=r.forwardRef((e,t)=>{let{__scopeSwitch:s,name:l,checked:c,defaultChecked:d,required:m,disabled:h,value:f="on",onCheckedChange:y,form:b,...g}=e,[w,N]=r.useState(null),k=(0,n.e)(t,e=>N(e)),S=r.useRef(!1),C=!w||b||!!w.closest("form"),[P,_]=(0,o.T)({prop:c,defaultProp:d??!1,onChange:y,caller:u});return(0,a.jsxs)(x,{scope:s,checked:P,disabled:h,children:[(0,a.jsx)(p.WV.button,{type:"button",role:"switch","aria-checked":P,"aria-required":m,"data-state":j(P),"data-disabled":h?"":void 0,disabled:h,value:f,...g,ref:k,onClick:(0,i.M)(e.onClick,e=>{_(e=>!e),C&&(S.current=e.isPropagationStopped(),S.current||e.stopPropagation())})}),C&&(0,a.jsx)(v,{control:w,bubbles:!S.current,name:l,value:f,checked:P,required:m,disabled:h,form:b,style:{transform:"translateX(-100%)"}})]})});y.displayName=u;var b="SwitchThumb",g=r.forwardRef((e,t)=>{let{__scopeSwitch:s,...r}=e,i=f(b,s);return(0,a.jsx)(p.WV.span,{"data-state":j(i.checked),"data-disabled":i.disabled?"":void 0,...r,ref:t})});g.displayName=b;var v=r.forwardRef(({__scopeSwitch:e,control:t,checked:s,bubbles:i=!0,...l},o)=>{let p=r.useRef(null),u=(0,n.e)(p,o),m=(0,c.D)(s),h=(0,d.t)(t);return r.useEffect(()=>{let e=p.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(m!==s&&t){let a=new Event("click",{bubbles:i});t.call(e,s),e.dispatchEvent(a)}},[m,s,i]),(0,a.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:s,...l,tabIndex:-1,ref:u,style:{...l.style,...h,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function j(e){return e?"checked":"unchecked"}v.displayName="SwitchBubbleInput";var w=s(91626);let N=r.forwardRef(({className:e,...t},s)=>a.jsx(y,{className:(0,w.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...t,ref:s,children:a.jsx(g,{className:(0,w.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));N.displayName=y.displayName},25757:(e,t,s)=>{"use strict";s.d(t,{mQ:()=>M,nU:()=>R,dr:()=>Z,SP:()=>E});var a=s(95344),r=s(3729),i=s(85222),n=s(98462),l=s(34504),o=s(43234),c=s(62409),d=s(3975),p=s(33183),u=s(99048),m="Tabs",[h,x]=(0,n.b)(m,[l.Pc]),f=(0,l.Pc)(),[y,b]=h(m),g=r.forwardRef((e,t)=>{let{__scopeTabs:s,value:r,onValueChange:i,defaultValue:n,orientation:l="horizontal",dir:o,activationMode:h="automatic",...x}=e,f=(0,d.gm)(o),[b,g]=(0,p.T)({prop:r,onChange:i,defaultProp:n??"",caller:m});return(0,a.jsx)(y,{scope:s,baseId:(0,u.M)(),value:b,onValueChange:g,orientation:l,dir:f,activationMode:h,children:(0,a.jsx)(c.WV.div,{dir:f,"data-orientation":l,...x,ref:t})})});g.displayName=m;var v="TabsList",j=r.forwardRef((e,t)=>{let{__scopeTabs:s,loop:r=!0,...i}=e,n=b(v,s),o=f(s);return(0,a.jsx)(l.fC,{asChild:!0,...o,orientation:n.orientation,dir:n.dir,loop:r,children:(0,a.jsx)(c.WV.div,{role:"tablist","aria-orientation":n.orientation,...i,ref:t})})});j.displayName=v;var w="TabsTrigger",N=r.forwardRef((e,t)=>{let{__scopeTabs:s,value:r,disabled:n=!1,...o}=e,d=b(w,s),p=f(s),u=C(d.baseId,r),m=P(d.baseId,r),h=r===d.value;return(0,a.jsx)(l.ck,{asChild:!0,...p,focusable:!n,active:h,children:(0,a.jsx)(c.WV.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":m,"data-state":h?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:u,...o,ref:t,onMouseDown:(0,i.M)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(r)}),onKeyDown:(0,i.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(r)}),onFocus:(0,i.M)(e.onFocus,()=>{let e="manual"!==d.activationMode;h||n||!e||d.onValueChange(r)})})})});N.displayName=w;var k="TabsContent",S=r.forwardRef((e,t)=>{let{__scopeTabs:s,value:i,forceMount:n,children:l,...d}=e,p=b(k,s),u=C(p.baseId,i),m=P(p.baseId,i),h=i===p.value,x=r.useRef(h);return r.useEffect(()=>{let e=requestAnimationFrame(()=>x.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,a.jsx)(o.z,{present:n||h,children:({present:s})=>(0,a.jsx)(c.WV.div,{"data-state":h?"active":"inactive","data-orientation":p.orientation,role:"tabpanel","aria-labelledby":u,hidden:!s,id:m,tabIndex:0,...d,ref:t,style:{...e.style,animationDuration:x.current?"0s":void 0},children:s&&l})})});function C(e,t){return`${e}-trigger-${t}`}function P(e,t){return`${e}-content-${t}`}S.displayName=k;var _=s(91626);let M=g,Z=r.forwardRef(({className:e,...t},s)=>a.jsx(j,{ref:s,className:(0,_.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));Z.displayName=j.displayName;let E=r.forwardRef(({className:e,...t},s)=>a.jsx(N,{ref:s,className:(0,_.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));E.displayName=N.displayName;let R=r.forwardRef(({className:e,...t},s)=>a.jsx(S,{ref:s,className:(0,_.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));R.displayName=S.displayName},1222:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(69224).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},53148:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(69224).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},31498:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(69224).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},73229:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(69224).Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},229:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>r,default:()=>n});let a=(0,s(86843).createProxy)(String.raw`C:\proj\nextjs-saas\app\super-admin\payment-gateways\page.tsx`),{__esModule:r,$$typeof:i}=a,n=a.default},14217:(e,t,s)=>{"use strict";s.d(t,{f:()=>l});var a=s(3729),r=s(62409),i=s(95344),n=a.forwardRef((e,t)=>(0,i.jsx)(r.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var l=n},92062:(e,t,s)=>{"use strict";s.d(t,{D:()=>r});var a=s(3729);function r(e){let t=a.useRef({value:e,previous:e});return a.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[1638,7948,6671,4626,7792,2506,1729,2125,3965],()=>s(91822));module.exports=a})();