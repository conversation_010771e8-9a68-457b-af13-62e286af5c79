"use strict";(()=>{var e={};e.id=7079,e.ids=[7079],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},97246:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>h,originalPathname:()=>I,patchFetch:()=>_,requestAsyncStorage:()=>w,routeModule:()=>g,serverHooks:()=>E,staticGenerationAsyncStorage:()=>S,staticGenerationBailout:()=>f});var s={};r.r(s),r.d(s,{DELETE:()=>y,GET:()=>c,POST:()=>p,PUT:()=>m});var i=r(95419),a=r(69108),n=r(99678),o=r(78070),u=r(81355),l=r(3205),d=r(9108);async function c(e){try{let t=await (0,u.getServerSession)(l.L);if(!t?.user?.id||t?.user?.role!=="SUPER_ADMIN")return o.Z.json({error:"Super admin access required"},{status:403});let{searchParams:r}=new URL(e.url),s=r.get("category")||"",i=r.get("search")||"",a="true"===r.get("publicOnly"),n={};s&&"all"!==s&&(n.category=s),i&&(n.OR=[{key:{contains:i,mode:"insensitive"}},{description:{contains:i,mode:"insensitive"}}]),a&&(n.isPublic=!0);let c=await d._.systemSettings.findMany({where:n,orderBy:[{category:"asc"},{key:"asc"}]}),p=c.reduce((e,t)=>(e[t.category]||(e[t.category]=[]),e[t.category].push(t),e),{}),m=await d._.systemSettings.groupBy({by:["category"],_count:{id:!0},orderBy:{category:"asc"}});return o.Z.json({settings:p,categories:m.map(e=>({name:e.category,count:e._count.id})),total:c.length})}catch(e){return console.error("Error fetching system settings:",e),o.Z.json({error:"Failed to fetch system settings"},{status:500})}}async function p(e){try{let t=await (0,u.getServerSession)(l.L);if(!t?.user?.id||t?.user?.role!=="SUPER_ADMIN")return o.Z.json({error:"Super admin access required"},{status:403});let{key:r,value:s,category:i,description:a,isPublic:n=!1,isEditable:c=!0}=await e.json();if(!r||!i||void 0===s)return o.Z.json({error:"Key, category, and value are required"},{status:400});if(await d._.systemSettings.findUnique({where:{key:r}}))return o.Z.json({error:"Setting with this key already exists"},{status:400});let p=await d._.systemSettings.create({data:{key:r,value:s,category:i,description:a,isPublic:n,isEditable:c}});return await d._.auditLog.create({data:{action:"SYSTEM_SETTING_CREATED",entityType:"SystemSettings",entityId:p.id,userId:t.user.id,userEmail:t.user.email,userRole:t.user.role,newValues:{key:p.key,value:p.value,category:p.category,isPublic:p.isPublic,isEditable:p.isEditable},metadata:{createdByAdmin:!0,adminId:t.user.id}}}),o.Z.json({setting:p,message:"System setting created successfully"},{status:201})}catch(e){return console.error("Error creating system setting:",e),o.Z.json({error:"Failed to create system setting"},{status:500})}}async function m(e){try{let t=await (0,u.getServerSession)(l.L);if(!t?.user?.id||t?.user?.role!=="SUPER_ADMIN")return o.Z.json({error:"Super admin access required"},{status:403});let{settings:r}=await e.json();if(!Array.isArray(r))return o.Z.json({error:"Settings must be an array"},{status:400});let s=[],i=[];for(let e of r){let{id:r,value:a,description:n,isPublic:o,isEditable:u}=e;if(!r)continue;let l=await d._.systemSettings.findUnique({where:{id:r}});if(!l||!l.isEditable)continue;let c=await d._.systemSettings.update({where:{id:r},data:{...void 0!==a&&{value:a},...void 0!==n&&{description:n},...void 0!==o&&{isPublic:o},...void 0!==u&&{isEditable:u}}});s.push(c),i.push({action:"SYSTEM_SETTING_UPDATED",entityType:"SystemSettings",entityId:r,userId:t.user.id,userEmail:t.user.email,userRole:t.user.role,oldValues:{value:l.value,description:l.description,isPublic:l.isPublic,isEditable:l.isEditable},newValues:{value:c.value,description:c.description,isPublic:c.isPublic,isEditable:c.isEditable},metadata:{updatedByAdmin:!0,adminId:t.user.id,bulkUpdate:!0}})}return i.length>0&&await d._.auditLog.createMany({data:i}),o.Z.json({updatedSettings:s,message:`Updated ${s.length} settings successfully`})}catch(e){return console.error("Error updating system settings:",e),o.Z.json({error:"Failed to update system settings"},{status:500})}}async function y(e){try{let t=await (0,u.getServerSession)(l.L);if(!t?.user?.id||t?.user?.role!=="SUPER_ADMIN")return o.Z.json({error:"Super admin access required"},{status:403});let{searchParams:r}=new URL(e.url),s=r.get("id");if(!s)return o.Z.json({error:"Setting ID is required"},{status:400});let i=await d._.systemSettings.findUnique({where:{id:s}});if(!i)return o.Z.json({error:"Setting not found"},{status:404});if(!i.isEditable)return o.Z.json({error:"This setting cannot be deleted"},{status:400});return await d._.systemSettings.delete({where:{id:s}}),await d._.auditLog.create({data:{action:"SYSTEM_SETTING_DELETED",entityType:"SystemSettings",entityId:s,userId:t.user.id,userEmail:t.user.email,userRole:t.user.role,oldValues:{key:i.key,value:i.value,category:i.category,description:i.description},metadata:{deletedByAdmin:!0,adminId:t.user.id}}}),o.Z.json({message:"System setting deleted successfully"})}catch(e){return console.error("Error deleting system setting:",e),o.Z.json({error:"Failed to delete system setting"},{status:500})}}let g=new i.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/super-admin/settings/route",pathname:"/api/super-admin/settings",filename:"route",bundlePath:"app/api/super-admin/settings/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\super-admin\\settings\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:w,staticGenerationAsyncStorage:S,serverHooks:E,headerHooks:h,staticGenerationBailout:f}=g,I="/api/super-admin/settings/route";function _(){return(0,n.patchFetch)({serverHooks:E,staticGenerationAsyncStorage:S})}},3205:(e,t,r)=>{r.d(t,{L:()=>l});var s=r(86485),i=r(10375),a=r(50694),n=r(6521),o=r.n(n),u=r(9108);let l={providers:[(0,s.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await u._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),r=t?.companyId;if(!r&&t){let e=await u._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(r=e?.id)&&await u._.user.update({where:{id:t.id},data:{companyId:r}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await o().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await u._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:r}}catch(e){return console.error("Authentication error:",e),null}}}),(0,i.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,a.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,r)=>{r.d(t,{_:()=>i});let s=require("@prisma/client"),i=globalThis.prisma??new s.PrismaClient}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1638,6206,6521,2455,4520],()=>r(97246));module.exports=s})();