"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/super-admin/settings/route";
exports.ids = ["app/api/super-admin/settings/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Fsettings%2Froute&page=%2Fapi%2Fsuper-admin%2Fsettings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Fsettings%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Fsettings%2Froute&page=%2Fapi%2Fsuper-admin%2Fsettings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Fsettings%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_proj_nextjs_saas_app_api_super_admin_settings_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/super-admin/settings/route.ts */ \"(rsc)/./app/api/super-admin/settings/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/super-admin/settings/route\",\n        pathname: \"/api/super-admin/settings\",\n        filename: \"route\",\n        bundlePath: \"app/api/super-admin/settings/route\"\n    },\n    resolvedPagePath: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\api\\\\super-admin\\\\settings\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_proj_nextjs_saas_app_api_super_admin_settings_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/super-admin/settings/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Fsettings%2Froute&page=%2Fapi%2Fsuper-admin%2Fsettings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Fsettings%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/super-admin/settings/route.ts":
/*!***********************************************!*\
  !*** ./app/api/super-admin/settings/route.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n// GET /api/super-admin/settings - Get all system settings\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id || session?.user?.role !== \"SUPER_ADMIN\") {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Super admin access required\"\n            }, {\n                status: 403\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const category = searchParams.get(\"category\") || \"\";\n        const search = searchParams.get(\"search\") || \"\";\n        const publicOnly = searchParams.get(\"publicOnly\") === \"true\";\n        // Build where clause\n        const where = {};\n        if (category && category !== \"all\") {\n            where.category = category;\n        }\n        if (search) {\n            where.OR = [\n                {\n                    key: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    description: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                }\n            ];\n        }\n        if (publicOnly) {\n            where.isPublic = true;\n        }\n        const settings = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.systemSettings.findMany({\n            where,\n            orderBy: [\n                {\n                    category: \"asc\"\n                },\n                {\n                    key: \"asc\"\n                }\n            ]\n        });\n        // Group settings by category\n        const groupedSettings = settings.reduce((acc, setting)=>{\n            if (!acc[setting.category]) {\n                acc[setting.category] = [];\n            }\n            acc[setting.category].push(setting);\n            return acc;\n        }, {});\n        // Get categories with counts\n        const categories = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.systemSettings.groupBy({\n            by: [\n                \"category\"\n            ],\n            _count: {\n                id: true\n            },\n            orderBy: {\n                category: \"asc\"\n            }\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            settings: groupedSettings,\n            categories: categories.map((cat)=>({\n                    name: cat.category,\n                    count: cat._count.id\n                })),\n            total: settings.length\n        });\n    } catch (error) {\n        console.error(\"Error fetching system settings:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to fetch system settings\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/super-admin/settings - Create new system setting\nasync function POST(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id || session?.user?.role !== \"SUPER_ADMIN\") {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Super admin access required\"\n            }, {\n                status: 403\n            });\n        }\n        const body = await request.json();\n        const { key, value, category, description, isPublic = false, isEditable = true } = body;\n        // Validate required fields\n        if (!key || !category || value === undefined) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Key, category, and value are required\"\n            }, {\n                status: 400\n            });\n        }\n        // Check if setting already exists\n        const existingSetting = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.systemSettings.findUnique({\n            where: {\n                key\n            }\n        });\n        if (existingSetting) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Setting with this key already exists\"\n            }, {\n                status: 400\n            });\n        }\n        // Create setting\n        const setting = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.systemSettings.create({\n            data: {\n                key,\n                value,\n                category,\n                description,\n                isPublic,\n                isEditable\n            }\n        });\n        // Log the action\n        await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.auditLog.create({\n            data: {\n                action: \"SYSTEM_SETTING_CREATED\",\n                entityType: \"SystemSettings\",\n                entityId: setting.id,\n                userId: session.user.id,\n                userEmail: session.user.email,\n                userRole: session.user.role,\n                newValues: {\n                    key: setting.key,\n                    value: setting.value,\n                    category: setting.category,\n                    isPublic: setting.isPublic,\n                    isEditable: setting.isEditable\n                },\n                metadata: {\n                    createdByAdmin: true,\n                    adminId: session.user.id\n                }\n            }\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            setting,\n            message: \"System setting created successfully\"\n        }, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"Error creating system setting:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to create system setting\"\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT /api/super-admin/settings - Bulk update settings\nasync function PUT(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id || session?.user?.role !== \"SUPER_ADMIN\") {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Super admin access required\"\n            }, {\n                status: 403\n            });\n        }\n        const body = await request.json();\n        const { settings } = body;\n        if (!Array.isArray(settings)) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Settings must be an array\"\n            }, {\n                status: 400\n            });\n        }\n        const updatedSettings = [];\n        const auditLogs = [];\n        for (const settingUpdate of settings){\n            const { id, value, description, isPublic, isEditable } = settingUpdate;\n            if (!id) continue;\n            // Get current setting for audit log\n            const currentSetting = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.systemSettings.findUnique({\n                where: {\n                    id\n                }\n            });\n            if (!currentSetting) continue;\n            if (!currentSetting.isEditable) continue; // Skip non-editable settings\n            // Update setting\n            const updatedSetting = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.systemSettings.update({\n                where: {\n                    id\n                },\n                data: {\n                    ...value !== undefined && {\n                        value\n                    },\n                    ...description !== undefined && {\n                        description\n                    },\n                    ...isPublic !== undefined && {\n                        isPublic\n                    },\n                    ...isEditable !== undefined && {\n                        isEditable\n                    }\n                }\n            });\n            updatedSettings.push(updatedSetting);\n            // Prepare audit log\n            auditLogs.push({\n                action: \"SYSTEM_SETTING_UPDATED\",\n                entityType: \"SystemSettings\",\n                entityId: id,\n                userId: session.user.id,\n                userEmail: session.user.email,\n                userRole: session.user.role,\n                oldValues: {\n                    value: currentSetting.value,\n                    description: currentSetting.description,\n                    isPublic: currentSetting.isPublic,\n                    isEditable: currentSetting.isEditable\n                },\n                newValues: {\n                    value: updatedSetting.value,\n                    description: updatedSetting.description,\n                    isPublic: updatedSetting.isPublic,\n                    isEditable: updatedSetting.isEditable\n                },\n                metadata: {\n                    updatedByAdmin: true,\n                    adminId: session.user.id,\n                    bulkUpdate: true\n                }\n            });\n        }\n        // Create audit logs\n        if (auditLogs.length > 0) {\n            await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.auditLog.createMany({\n                data: auditLogs\n            });\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            updatedSettings,\n            message: `Updated ${updatedSettings.length} settings successfully`\n        });\n    } catch (error) {\n        console.error(\"Error updating system settings:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to update system settings\"\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE /api/super-admin/settings - Delete system setting\nasync function DELETE(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id || session?.user?.role !== \"SUPER_ADMIN\") {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Super admin access required\"\n            }, {\n                status: 403\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const settingId = searchParams.get(\"id\");\n        if (!settingId) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Setting ID is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Get setting for audit log\n        const setting = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.systemSettings.findUnique({\n            where: {\n                id: settingId\n            }\n        });\n        if (!setting) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Setting not found\"\n            }, {\n                status: 404\n            });\n        }\n        if (!setting.isEditable) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"This setting cannot be deleted\"\n            }, {\n                status: 400\n            });\n        }\n        // Delete setting\n        await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.systemSettings.delete({\n            where: {\n                id: settingId\n            }\n        });\n        // Log the action\n        await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.auditLog.create({\n            data: {\n                action: \"SYSTEM_SETTING_DELETED\",\n                entityType: \"SystemSettings\",\n                entityId: settingId,\n                userId: session.user.id,\n                userEmail: session.user.email,\n                userRole: session.user.role,\n                oldValues: {\n                    key: setting.key,\n                    value: setting.value,\n                    category: setting.category,\n                    description: setting.description\n                },\n                metadata: {\n                    deletedByAdmin: true,\n                    adminId: session.user.id\n                }\n            }\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            message: \"System setting deleted successfully\"\n        });\n    } catch (error) {\n        console.error(\"Error deleting system setting:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to delete system setting\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/super-admin/settings/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/github */ \"(rsc)/./node_modules/next-auth/providers/github.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n\nconst authOptions = {\n    // Using JWT strategy instead of database adapter for now\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                try {\n                    if (!credentials?.email || !credentials?.password) {\n                        console.log(\"Missing credentials\");\n                        return null;\n                    }\n                    console.log(\"Attempting to authenticate user:\", credentials.email);\n                    const user = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.findUnique({\n                        where: {\n                            email: credentials.email\n                        },\n                        select: {\n                            id: true,\n                            email: true,\n                            name: true,\n                            password: true,\n                            role: true,\n                            companyId: true\n                        }\n                    });\n                    // Get the company ID - either as member or owner\n                    let finalCompanyId = user?.companyId;\n                    if (!finalCompanyId && user) {\n                        const ownedCompany = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.company.findFirst({\n                            where: {\n                                ownerId: user.id\n                            },\n                            select: {\n                                id: true\n                            }\n                        });\n                        finalCompanyId = ownedCompany?.id;\n                        // If user is company owner, update their companyId for future queries\n                        if (finalCompanyId) {\n                            await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                                where: {\n                                    id: user.id\n                                },\n                                data: {\n                                    companyId: finalCompanyId\n                                }\n                            });\n                        }\n                    }\n                    if (!user) {\n                        console.log(\"User not found:\", credentials.email);\n                        return null;\n                    }\n                    if (!user.password) {\n                        console.log(\"User has no password set:\", credentials.email);\n                        return null;\n                    }\n                    const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_3___default().compare(credentials.password, user.password);\n                    if (!isPasswordValid) {\n                        console.log(\"Invalid password for user:\", credentials.email);\n                        return null;\n                    }\n                    // Update last login\n                    await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                        where: {\n                            id: user.id\n                        },\n                        data: {\n                            lastLoginAt: new Date(),\n                            loginCount: {\n                                increment: 1\n                            }\n                        }\n                    });\n                    console.log(\"User authenticated successfully:\", user.email);\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name,\n                        role: user.role,\n                        companyId: finalCompanyId\n                    };\n                } catch (error) {\n                    console.error(\"Authentication error:\", error);\n                    return null;\n                }\n            }\n        }),\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            clientId: process.env.GITHUB_CLIENT_ID,\n            clientSecret: process.env.GITHUB_CLIENT_SECRET\n        })\n    ],\n    session: {\n        strategy: \"jwt\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                console.log(\"JWT callback - user data:\", {\n                    id: user.id,\n                    email: user.email,\n                    role: user.role,\n                    companyId: user.companyId\n                });\n                token.role = user.role;\n                token.companyId = user.companyId;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n                session.user.companyId = token.companyId;\n                console.log(\"Session callback - final session:\", {\n                    id: session.user.id,\n                    email: session.user.email,\n                    role: session.user.role,\n                    companyId: session.user.companyId\n                });\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        signUp: \"/auth/signup\",\n        error: \"/auth/error\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zYWFzLWFwcC8uL2xpYi9wcmlzbWEudHM/OTgyMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/lru-cache","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Fsettings%2Froute&page=%2Fapi%2Fsuper-admin%2Fsettings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Fsettings%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();