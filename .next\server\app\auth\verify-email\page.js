(()=>{var e={};e.id=2616,e.ids=[2616],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},86771:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=t(50482),a=t(69108),n=t(62563),i=t.n(n),o=t(68300),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let c=["",{children:["auth",{children:["verify-email",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,9409)),"C:\\proj\\nextjs-saas\\app\\auth\\verify-email\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,59504)),"C:\\proj\\nextjs-saas\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\proj\\nextjs-saas\\app\\auth\\verify-email\\page.tsx"],u="/auth/verify-email/page",m={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/auth/verify-email/page",pathname:"/auth/verify-email",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},28368:(e,r,t)=>{Promise.resolve().then(t.bind(t,24540))},64588:(e,r,t)=>{Promise.resolve().then(t.bind(t,56189)),Promise.resolve().then(t.bind(t,44669))},19634:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,2583,23)),Promise.resolve().then(t.t.bind(t,26840,23)),Promise.resolve().then(t.t.bind(t,38771,23)),Promise.resolve().then(t.t.bind(t,13225,23)),Promise.resolve().then(t.t.bind(t,9295,23)),Promise.resolve().then(t.t.bind(t,43982,23))},24540:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>g});var s=t(95344),a=t(3729),n=t(22254),i=t(20783),o=t.n(i),l=t(16212),c=t(61351),d=t(33733),u=t(7060),m=t(66138),p=t(71206),h=t(91700),f=t(35299),x=t(44669);function g(){let[e,r]=(0,a.useState)("loading"),[t,i]=(0,a.useState)(!1),[g,y]=(0,a.useState)(""),v=(0,n.useRouter)(),b=(0,n.useSearchParams)(),j=b.get("token"),w=b.get("email");(0,a.useEffect)(()=>{j?C(j):w||(r("error"),y("Invalid verification link"))},[j]);let C=async e=>{try{let t=await fetch("/api/auth/verify-email",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({token:e})}),s=await t.json();t.ok?(r("success"),y("Your email has been verified successfully!"),setTimeout(()=>{v.push("/auth/signin?verified=true")},3e3)):"Token expired"===s.error?(r("expired"),y("Your verification link has expired. Please request a new one.")):(r("error"),y(s.error||"Failed to verify email"))}catch(e){console.error("Verification error:",e),r("error"),y("An error occurred during verification")}},N=async()=>{if(!w){x.toast.error("Email address is required");return}i(!0);try{let e=await fetch("/api/auth/resend-verification",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:w})}),t=await e.json();e.ok?(r("resend"),y("A new verification email has been sent to your inbox."),x.toast.success("Verification email sent!")):x.toast.error(t.error||"Failed to resend verification email")}catch(e){console.error("Resend error:",e),x.toast.error("An error occurred while resending the email")}finally{i(!1)}};return s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 p-4",children:(0,s.jsxs)("div",{className:"w-full max-w-md",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center mb-8",children:[s.jsx(h.Z,{className:"h-8 w-8 text-blue-600 mr-2"}),s.jsx("span",{className:"text-2xl font-bold text-gray-900",children:"Business SaaS"})]}),(0,s.jsxs)(c.Zb,{children:[(0,s.jsxs)(c.Ol,{className:"text-center",children:[s.jsx("div",{className:"flex justify-center mb-4",children:(()=>{switch(e){case"loading":default:return s.jsx(d.Z,{className:"h-12 w-12 text-blue-600 animate-spin"});case"success":return s.jsx(u.Z,{className:"h-12 w-12 text-green-600"});case"error":case"expired":return s.jsx(m.Z,{className:"h-12 w-12 text-red-600"});case"resend":return s.jsx(p.Z,{className:"h-12 w-12 text-blue-600"})}})()}),s.jsx(c.ll,{className:"text-2xl",children:(()=>{switch(e){case"loading":default:return"Verifying your email...";case"success":return"Email verified successfully!";case"error":return"Verification failed";case"expired":return"Verification link expired";case"resend":return"New verification email sent"}})()}),s.jsx(c.SZ,{children:(()=>{switch(e){case"loading":default:return"Please wait while we verify your email address...";case"success":return"Your account is now active. You will be redirected to the sign-in page shortly.";case"error":return"We encountered an issue verifying your email address.";case"expired":return"Your verification link has expired. Please request a new one below.";case"resend":return"Please check your inbox and click the verification link in the new email."}})()})]}),(0,s.jsxs)(c.aY,{className:"space-y-4",children:[g&&s.jsx("div",{className:`p-4 rounded-lg text-center ${"success"===e?"bg-green-50 text-green-800 border border-green-200":"resend"===e?"bg-blue-50 text-blue-800 border border-blue-200":"bg-red-50 text-red-800 border border-red-200"}`,children:g}),(0,s.jsxs)("div",{className:"space-y-3",children:["success"===e&&(0,s.jsxs)("div",{className:"space-y-3",children:[s.jsx(l.z,{asChild:!0,className:"w-full",children:(0,s.jsxs)(o(),{href:"/auth/signin?verified=true",children:["Continue to Sign In",s.jsx(f.Z,{className:"h-4 w-4 ml-2"})]})}),s.jsx("p",{className:"text-sm text-center text-gray-500",children:"Redirecting automatically in 3 seconds..."})]}),("expired"===e||"error"===e)&&w&&s.jsx(l.z,{onClick:N,disabled:t,className:"w-full",children:t?(0,s.jsxs)(s.Fragment,{children:[s.jsx(d.Z,{className:"h-4 w-4 mr-2 animate-spin"}),"Sending..."]}):(0,s.jsxs)(s.Fragment,{children:[s.jsx(p.Z,{className:"h-4 w-4 mr-2"}),"Resend Verification Email"]})}),"resend"===e&&(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("p",{className:"text-sm text-gray-600 mb-3",children:"Didn't receive the email? Check your spam folder or try again."}),s.jsx(l.z,{variant:"outline",onClick:N,disabled:t,size:"sm",children:t?(0,s.jsxs)(s.Fragment,{children:[s.jsx(d.Z,{className:"h-4 w-4 mr-2 animate-spin"}),"Sending..."]}):"Send Again"})]}),s.jsx("div",{className:"text-center pt-4 border-t",children:s.jsx(o(),{href:"/auth/signin",className:"text-sm text-blue-600 hover:underline",children:"Back to Sign In"})})]}),("error"===e||"expired"===e)&&(0,s.jsxs)("div",{className:"mt-6 p-4 bg-gray-50 rounded-lg",children:[s.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Need help?"}),(0,s.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[s.jsx("li",{children:"• Make sure you're using the latest verification email"}),s.jsx("li",{children:"• Check your spam or junk folder"}),s.jsx("li",{children:"• Verification links expire after 24 hours"}),s.jsx("li",{children:"• Contact support if you continue having issues"})]})]})]})]})]})})}},56189:(e,r,t)=>{"use strict";t.r(r),t.d(r,{Providers:()=>d});var s=t(95344),a=t(47674),n=t(6256),i=t(19115),o=t(26274),l=t(3729),c=t(66091);function d({children:e}){let[r]=(0,l.useState)(()=>new i.S({defaultOptions:{queries:{staleTime:6e4,retry:1}}}));return s.jsx(a.SessionProvider,{children:s.jsx(o.aH,{client:r,children:s.jsx(c.lY,{children:s.jsx(n.f,{attribute:"class",defaultTheme:"light",enableSystem:!0,disableTransitionOnChange:!0,children:e})})})})}},16212:(e,r,t)=>{"use strict";t.d(r,{z:()=>c});var s=t(95344),a=t(3729),n=t(32751),i=t(49247),o=t(91626);let l=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef(({className:e,variant:r,size:t,asChild:a=!1,...i},c)=>{let d=a?n.g7:"button";return s.jsx(d,{className:(0,o.cn)(l({variant:r,size:t,className:e})),ref:c,...i})});c.displayName="Button"},61351:(e,r,t)=>{"use strict";t.d(r,{Ol:()=>o,SZ:()=>c,Zb:()=>i,aY:()=>d,eW:()=>u,ll:()=>l});var s=t(95344),a=t(3729),n=t(91626);let i=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));i.displayName="Card";let o=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...r}));o.displayName="CardHeader";let l=a.forwardRef(({className:e,...r},t)=>s.jsx("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));l.displayName="CardTitle";let c=a.forwardRef(({className:e,...r},t)=>s.jsx("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",e),...r}));c.displayName="CardDescription";let d=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,n.cn)("p-6 pt-0",e),...r}));d.displayName="CardContent";let u=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",e),...r}));u.displayName="CardFooter"},66091:(e,r,t)=>{"use strict";t.d(r,{TC:()=>l,lY:()=>o});var s=t(95344),a=t(3729);let n={appName:"SaaS Platform",logoUrl:"",faviconUrl:"",primaryColor:"#3b82f6",secondaryColor:"#64748b",accentColor:"#10b981",backgroundColor:"#ffffff",textColor:"#1f2937",theme:"light",fontFamily:"Inter, sans-serif",customCss:""},i=(0,a.createContext)(void 0);function o({children:e}){let[r,t]=(0,a.useState)(n),[o,l]=(0,a.useState)(!0);(0,a.useEffect)(()=>{c()},[]);let c=async()=>{try{let e=await fetch("/api/global-config/branding"),r=await e.json();r.success&&r.branding?(t({...n,...r.branding}),d({...n,...r.branding})):d(n)}catch(e){console.error("Error fetching branding config:",e),d(n)}finally{l(!1)}},d=e=>{let r=document.documentElement;if(r.style.setProperty("--primary-color",e.primaryColor),r.style.setProperty("--secondary-color",e.secondaryColor),r.style.setProperty("--accent-color",e.accentColor),r.style.setProperty("--background-color",e.backgroundColor),r.style.setProperty("--text-color",e.textColor),r.style.setProperty("--font-family",e.fontFamily),document.body.className=document.body.className.replace(/theme-\w+/g,""),document.body.classList.add(`theme-${e.theme}`),document.title=e.appName,e.faviconUrl){let r=document.querySelector('link[rel="icon"]');r||((r=document.createElement("link")).rel="icon",document.head.appendChild(r)),r.href=e.faviconUrl}let t=document.getElementById("custom-branding-css");e.customCss?(t||((t=document.createElement("style")).id="custom-branding-css",document.head.appendChild(t)),t.textContent=e.customCss):t&&t.remove();let s=document.querySelector('meta[name="theme-color"]');s||((s=document.createElement("meta")).name="theme-color",document.head.appendChild(s)),s.content=e.primaryColor};return s.jsx(i.Provider,{value:{branding:r,updateBranding:e=>{let s={...r,...e};t(s),d(s)},loading:o},children:e})}function l(){let e=(0,a.useContext)(i);if(void 0===e)throw Error("useBranding must be used within a BrandingProvider");return e}},91626:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n});var s=t(56815),a=t(79377);function n(...e){return(0,a.m6)((0,s.W)(e))}},66138:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(69224).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},35299:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(69224).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},7060:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(69224).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},71206:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(69224).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},33733:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(69224).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},22254:(e,r,t)=>{e.exports=t(14767)},9409:(e,r,t)=>{"use strict";t.r(r),t.d(r,{$$typeof:()=>n,__esModule:()=>a,default:()=>i});let s=(0,t(86843).createProxy)(String.raw`C:\proj\nextjs-saas\app\auth\verify-email\page.tsx`),{__esModule:a,$$typeof:n}=s,i=s.default},59504:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p,metadata:()=>m});var s=t(25036),a=t(80265),n=t.n(a),i=t(86843);let o=(0,i.createProxy)(String.raw`C:\proj\nextjs-saas\components\providers.tsx`),{__esModule:l,$$typeof:c}=o;o.default;let d=(0,i.createProxy)(String.raw`C:\proj\nextjs-saas\components\providers.tsx#Providers`);var u=t(69636);t(67272);let m={title:{default:"Business SaaS - Complete Business Management Solution",template:"%s | Business SaaS"},description:"Complete SaaS solution for business management including CRM, invoicing, quotations, and more.",keywords:["SaaS","Business Management","CRM","Invoicing","Quotations"],authors:[{name:"Business SaaS Team"}],creator:"Business SaaS",openGraph:{type:"website",locale:"en_US",url:process.env.NEXT_PUBLIC_APP_URL,title:"Business SaaS - Complete Business Management Solution",description:"Complete SaaS solution for business management including CRM, invoicing, quotations, and more.",siteName:"Business SaaS"},twitter:{card:"summary_large_image",title:"Business SaaS - Complete Business Management Solution",description:"Complete SaaS solution for business management including CRM, invoicing, quotations, and more.",creator:"@businesssaas"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function p({children:e}){return s.jsx("html",{lang:"en",suppressHydrationWarning:!0,children:s.jsx("body",{className:n().className,children:(0,s.jsxs)(d,{children:[e,s.jsx(u.x7,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#4ade80",secondary:"#fff"}},error:{duration:4e3,iconTheme:{primary:"#ef4444",secondary:"#fff"}}}})]})})})}},67272:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[1638,7948,6671,4626],()=>t(86771));module.exports=s})();