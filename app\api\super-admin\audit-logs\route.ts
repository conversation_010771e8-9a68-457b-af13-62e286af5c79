import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET /api/super-admin/audit-logs - Get audit logs with filtering
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || session?.user?.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Super admin access required' }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const action = searchParams.get('action') || ''
    const entityType = searchParams.get('entityType') || ''
    const userId = searchParams.get('userId') || ''
    const companyId = searchParams.get('companyId') || ''
    const severity = searchParams.get('severity') || ''
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')
    const search = searchParams.get('search') || ''

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {}

    if (action && action !== 'all') {
      where.action = { contains: action, mode: 'insensitive' }
    }

    if (entityType && entityType !== 'all') {
      where.entityType = entityType
    }

    if (userId) {
      where.userId = userId
    }

    if (companyId) {
      where.companyId = companyId
    }

    if (severity && severity !== 'all') {
      where.severity = severity
    }

    if (startDate || endDate) {
      where.createdAt = {}
      if (startDate) {
        where.createdAt.gte = new Date(startDate)
      }
      if (endDate) {
        where.createdAt.lte = new Date(endDate)
      }
    }

    if (search) {
      where.OR = [
        { action: { contains: search, mode: 'insensitive' } },
        { entityType: { contains: search, mode: 'insensitive' } },
        { userEmail: { contains: search, mode: 'insensitive' } },
        { ipAddress: { contains: search, mode: 'insensitive' } }
      ]
    }

    // Get audit logs with pagination
    const [auditLogs, total] = await Promise.all([
      prisma.auditLog.findMany({
        where,
        skip,
        take: limit,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true
            }
          },
          company: {
            select: {
              id: true,
              name: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      }),
      prisma.auditLog.count({ where })
    ])

    // Get statistics
    const stats = await Promise.all([
      // Actions in the last 24 hours
      prisma.auditLog.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000)
          }
        }
      }),
      // Group by action
      prisma.auditLog.groupBy({
        by: ['action'],
        _count: { id: true },
        orderBy: { _count: { id: 'desc' } },
        take: 10
      }),
      // Group by severity
      prisma.auditLog.groupBy({
        by: ['severity'],
        _count: { id: true }
      }),
      // Group by entity type
      prisma.auditLog.groupBy({
        by: ['entityType'],
        _count: { id: true },
        orderBy: { _count: { id: 'desc' } },
        take: 10
      }),
      // Most active users
      prisma.auditLog.groupBy({
        by: ['userId'],
        _count: { id: true },
        where: { userId: { not: null } },
        orderBy: { _count: { id: 'desc' } },
        take: 10
      })
    ])

    const [last24Hours, byAction, bySeverity, byEntityType, byUser] = stats

    // Get user details for most active users
    const userIds = byUser.map(item => item.userId).filter(Boolean) as string[]
    const users = await prisma.user.findMany({
      where: { id: { in: userIds } },
      select: { id: true, name: true, email: true }
    })

    const userMap = users.reduce((acc, user) => {
      acc[user.id] = user
      return acc
    }, {} as Record<string, any>)

    return NextResponse.json({
      auditLogs: auditLogs.map(log => ({
        id: log.id,
        action: log.action,
        entityType: log.entityType,
        entityId: log.entityId,
        user: log.user,
        userEmail: log.userEmail,
        userRole: log.userRole,
        company: log.company,
        ipAddress: log.ipAddress,
        userAgent: log.userAgent,
        requestUrl: log.requestUrl,
        requestMethod: log.requestMethod,
        oldValues: log.oldValues,
        newValues: log.newValues,
        metadata: log.metadata,
        severity: log.severity,
        createdAt: log.createdAt
      })),
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      stats: {
        total,
        last24Hours,
        byAction: byAction.map(item => ({
          action: item.action,
          count: item._count.id
        })),
        bySeverity: bySeverity.map(item => ({
          severity: item.severity,
          count: item._count.id
        })),
        byEntityType: byEntityType.map(item => ({
          entityType: item.entityType,
          count: item._count.id
        })),
        byUser: byUser.map(item => ({
          userId: item.userId,
          user: userMap[item.userId!],
          count: item._count.id
        }))
      }
    })

  } catch (error) {
    console.error('Error fetching audit logs:', error)
    return NextResponse.json(
      { error: 'Failed to fetch audit logs' },
      { status: 500 }
    )
  }
}

// POST /api/super-admin/audit-logs - Create audit log entry (for testing or manual entries)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || session?.user?.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Super admin access required' }, { status: 403 })
    }

    const body = await request.json()
    const {
      action,
      entityType,
      entityId,
      oldValues,
      newValues,
      metadata,
      severity = 'INFO'
    } = body

    if (!action || !entityType) {
      return NextResponse.json(
        { error: 'Action and entityType are required' },
        { status: 400 }
      )
    }

    const auditLog = await prisma.auditLog.create({
      data: {
        action,
        entityType,
        entityId,
        userId: session.user.id,
        userEmail: session.user.email,
        userRole: session.user.role,
        oldValues,
        newValues,
        metadata: {
          ...metadata,
          manualEntry: true,
          createdByAdmin: true
        },
        severity
      }
    })

    return NextResponse.json({
      auditLog,
      message: 'Audit log entry created successfully'
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating audit log:', error)
    return NextResponse.json(
      { error: 'Failed to create audit log' },
      { status: 500 }
    )
  }
}

// DELETE /api/super-admin/audit-logs - Bulk delete old audit logs
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || session?.user?.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Super admin access required' }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const days = parseInt(searchParams.get('days') || '90')
    const severity = searchParams.get('severity')

    if (days < 30) {
      return NextResponse.json(
        { error: 'Cannot delete logs newer than 30 days' },
        { status: 400 }
      )
    }

    const cutoffDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000)
    const where: any = {
      createdAt: { lt: cutoffDate }
    }

    if (severity) {
      where.severity = severity
    }

    const deletedLogs = await prisma.auditLog.deleteMany({ where })

    // Log the cleanup action
    await prisma.auditLog.create({
      data: {
        action: 'AUDIT_LOG_CLEANUP',
        entityType: 'AuditLog',
        userId: session.user.id,
        userEmail: session.user.email,
        userRole: session.user.role,
        metadata: {
          deletedCount: deletedLogs.count,
          cutoffDate,
          days,
          severity
        },
        severity: 'INFO'
      }
    })

    return NextResponse.json({
      message: `Deleted ${deletedLogs.count} audit log entries older than ${days} days`,
      deletedCount: deletedLogs.count
    })

  } catch (error) {
    console.error('Error deleting audit logs:', error)
    return NextResponse.json(
      { error: 'Failed to delete audit logs' },
      { status: 500 }
    )
  }
}
