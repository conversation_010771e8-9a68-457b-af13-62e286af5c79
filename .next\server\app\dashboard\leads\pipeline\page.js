(()=>{var e={};e.id=8131,e.ids=[8131],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},87455:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>f,tree:()=>d});var n=r(50482),l=r(69108),a=r(62563),i=r.n(a),o=r(68300),s={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(s[e]=()=>o[e]);r.d(t,s);let d=["",{children:["dashboard",{children:["leads",{children:["pipeline",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,93398)),"C:\\proj\\nextjs-saas\\app\\dashboard\\leads\\pipeline\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,96339)),"C:\\proj\\nextjs-saas\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,59504)),"C:\\proj\\nextjs-saas\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\proj\\nextjs-saas\\app\\dashboard\\leads\\pipeline\\page.tsx"],u="/dashboard/leads/pipeline/page",p={require:r,loadChunk:()=>Promise.resolve()},f=new n.AppPageRouteModule({definition:{kind:l.x.APP_PAGE,page:"/dashboard/leads/pipeline/page",pathname:"/dashboard/leads/pipeline",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},62868:(e,t,r)=>{Promise.resolve().then(r.bind(r,84200))},84200:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>ak});var n=r(95344),l=r(3729),a=r.n(l),i=r(47674),o=r(81202),s=r.n(o);function d(e){return`Minified Redux error #${e}; visit https://redux.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}var c="function"==typeof Symbol&&Symbol.observable||"@@observable",u=()=>Math.random().toString(36).substring(7).split("").join("."),p={INIT:`@@redux/INIT${u()}`,REPLACE:`@@redux/REPLACE${u()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${u()}`};function f(e,t){return function(...r){return t(e.apply(this,r))}}function g(e,t){if("function"==typeof e)return f(e,t);if("object"!=typeof e||null===e)throw Error(d(16));let r={};for(let n in e){let l=e[n];"function"==typeof l&&(r[n]=f(l,t))}return r}function m(...e){return 0===e.length?e=>e:1===e.length?e[0]:e.reduce((e,t)=>(...r)=>e(t(...r)))}r(40687);var h=Symbol.for(l.version.startsWith("19")?"react.transitional.element":"react.element"),b=Symbol.for("react.portal"),x=Symbol.for("react.fragment"),y=Symbol.for("react.strict_mode"),v=Symbol.for("react.profiler"),I=Symbol.for("react.consumer"),N=Symbol.for("react.context"),D=Symbol.for("react.forward_ref"),E=Symbol.for("react.suspense"),w=Symbol.for("react.suspense_list"),A=Symbol.for("react.memo"),P=Symbol.for("react.lazy");function C(e){return function(t){let r=e(t);function n(){return r}return n.dependsOnOwnProps=!1,n}}function O(e){return e.dependsOnOwnProps?!!e.dependsOnOwnProps:1!==e.length}function R(e,t){return function(t,{displayName:r}){let n=function(e,t){return n.dependsOnOwnProps?n.mapToProps(e,t):n.mapToProps(e,void 0)};return n.dependsOnOwnProps=!0,n.mapToProps=function(t,r){n.mapToProps=e,n.dependsOnOwnProps=O(e);let l=n(t,r);return"function"==typeof l&&(n.mapToProps=l,n.dependsOnOwnProps=O(l),l=n(t,r)),l},n}}function S(e,t){return(r,n)=>{throw Error(`Invalid value of type ${typeof e} for ${t} argument when connecting component ${n.wrappedComponentName}.`)}}function j(e,t,r){return{...r,...e,...t}}var L={notify(){},get:()=>[]};function B(e,t){let r;let n=L,l=0,a=!1;function i(){d.onStateChange&&d.onStateChange()}function o(){if(l++,!r){let l,a;r=t?t.addNestedSub(i):e.subscribe(i),l=null,a=null,n={clear(){l=null,a=null},notify(){(()=>{let e=l;for(;e;)e.callback(),e=e.next})()},get(){let e=[],t=l;for(;t;)e.push(t),t=t.next;return e},subscribe(e){let t=!0,r=a={callback:e,next:null,prev:a};return r.prev?r.prev.next=r:l=r,function(){t&&null!==l&&(t=!1,r.next?r.next.prev=r.prev:a=r.prev,r.prev?r.prev.next=r.next:l=r.next)}}}}}function s(){l--,r&&0===l&&(r(),r=void 0,n.clear(),n=L)}let d={addNestedSub:function(e){o();let t=n.subscribe(e),r=!1;return()=>{r||(r=!0,t(),s())}},notifyNestedSubs:function(){n.notify()},handleChangeWrapper:i,isSubscribed:function(){return a},trySubscribe:function(){a||(a=!0,o())},tryUnsubscribe:function(){a&&(a=!1,s())},getListeners:()=>n};return d}var T="undefined"!=typeof navigator&&"ReactNative"===navigator.product?l.useLayoutEffect:l.useEffect;function G(e,t){return e===t?0!==e||0!==t||1/e==1/t:e!=e&&t!=t}function _(e,t){if(G(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;let r=Object.keys(e),n=Object.keys(t);if(r.length!==n.length)return!1;for(let n=0;n<r.length;n++)if(!Object.prototype.hasOwnProperty.call(t,r[n])||!G(e[r[n]],t[r[n]]))return!1;return!0}var M={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},k={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},$={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},F={[D]:{$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},[A]:$};function W(e){return function(e){if("object"==typeof e&&null!==e){let{$$typeof:t}=e;switch(t){case h:switch(e=e.type){case x:case v:case y:case E:case w:return e;default:switch(e=e&&e.$$typeof){case N:case D:case P:case A:case I:return e;default:return t}}case b:return t}}}(e)===A?$:F[e.$$typeof]||M}var U=Object.defineProperty,H=Object.getOwnPropertyNames,V=Object.getOwnPropertySymbols,Z=Object.getOwnPropertyDescriptor,q=Object.getPrototypeOf,z=Object.prototype;function Y(e,t){if("string"!=typeof t){if(z){let r=q(t);r&&r!==z&&Y(e,r)}let r=H(t);V&&(r=r.concat(V(t)));let n=W(e),l=W(t);for(let a=0;a<r.length;++a){let i=r[a];if(!k[i]&&!(l&&l[i])&&!(n&&n[i])){let r=Z(t,i);try{U(e,i,r)}catch(e){}}}}return e}var Q=Symbol.for("react-redux-context"),J="undefined"!=typeof globalThis?globalThis:{},X=function(){if(!l.createContext)return{};let e=J[Q]??=new Map,t=e.get(l.createContext);return t||(t=l.createContext(null),e.set(l.createContext,t)),t}(),K=[null,null];function ee(e,t){return e===t}var et=function(e,t,r,{pure:n,areStatesEqual:a=ee,areOwnPropsEqual:i=_,areStatePropsEqual:o=_,areMergedPropsEqual:s=_,forwardRef:d=!1,context:c=X}={}){let u=e?"function"==typeof e?R(e,"mapStateToProps"):S(e,"mapStateToProps"):C(()=>({})),p=t&&"object"==typeof t?C(e=>(function(e,t){let r={};for(let n in e){let l=e[n];"function"==typeof l&&(r[n]=(...e)=>t(l(...e)))}return r})(t,e)):t?"function"==typeof t?R(t,"mapDispatchToProps"):S(t,"mapDispatchToProps"):C(e=>({dispatch:e})),f=r?"function"==typeof r?function(e,{displayName:t,areMergedPropsEqual:n}){let l,a=!1;return function(e,t,i){let o=r(e,t,i);return a?n(o,l)||(l=o):(a=!0,l=o),l}}:S(r,"mergeProps"):()=>j,g=!!e;return e=>{let t=e.displayName||e.name||"Component",r=`Connect(${t})`,n={shouldHandleStateChanges:g,displayName:r,wrappedComponentName:t,WrappedComponent:e,initMapStateToProps:u,initMapDispatchToProps:p,initMergeProps:f,areStatesEqual:a,areStatePropsEqual:o,areOwnPropsEqual:i,areMergedPropsEqual:s};function m(t){var r;let a;let[i,o,s]=l.useMemo(()=>{let{reactReduxForwardedRef:e,...r}=t;return[t.context,e,r]},[t]),d=l.useMemo(()=>(i?.Consumer,c),[i,c]),u=l.useContext(d),p=!!t.store&&!!t.store.getState&&!!t.store.dispatch,f=!!u&&!!u.store,m=p?t.store:u.store,h=f?u.getServerState:m.getState,b=l.useMemo(()=>(function(e,{initMapStateToProps:t,initMapDispatchToProps:r,initMergeProps:n,...l}){let a=t(e,l);return function(e,t,r,n,{areStatesEqual:l,areOwnPropsEqual:a,areStatePropsEqual:i}){let o,s,d,c,u,p=!1;return function(f,g){return p?function(p,f){let g=!a(f,s),m=!l(p,o,f,s);return(o=p,s=f,g&&m)?(d=e(o,s),t.dependsOnOwnProps&&(c=t(n,s)),u=r(d,c,s)):g?(e.dependsOnOwnProps&&(d=e(o,s)),t.dependsOnOwnProps&&(c=t(n,s)),u=r(d,c,s)):m?function(){let t=e(o,s),n=!i(t,d);return d=t,n&&(u=r(d,c,s)),u}():u}(f,g):(d=e(o=f,s=g),c=t(n,s),u=r(d,c,s),p=!0,u)}}(a,r(e,l),n(e,l),e,l)})(m.dispatch,n),[m]),[x,y]=l.useMemo(()=>{if(!g)return K;let e=B(m,p?void 0:u.subscription),t=e.notifyNestedSubs.bind(e);return[e,t]},[m,p,u]),v=l.useMemo(()=>p?u:{...u,subscription:x},[p,u,x]),I=l.useRef(void 0),N=l.useRef(s),D=l.useRef(void 0),E=l.useRef(!1),w=l.useRef(!1),A=l.useRef(void 0);T(()=>(w.current=!0,()=>{w.current=!1}),[]);let P=l.useMemo(()=>()=>D.current&&s===N.current?D.current:b(m.getState(),s),[m,s]),C=l.useMemo(()=>e=>x?function(e,t,r,n,l,a,i,o,s,d,c){if(!e)return()=>{};let u=!1,p=null,f=()=>{let e,r;if(u||!o.current)return;let f=t.getState();try{e=n(f,l.current)}catch(e){r=e,p=e}r||(p=null),e===a.current?i.current||d():(a.current=e,s.current=e,i.current=!0,c())};return r.onStateChange=f,r.trySubscribe(),f(),()=>{if(u=!0,r.tryUnsubscribe(),r.onStateChange=null,p)throw p}}(g,m,x,b,N,I,E,w,D,y,e):()=>{},[x]);r=[N,I,E,s,D,y],T(()=>(function(e,t,r,n,l,a){e.current=n,r.current=!1,l.current&&(l.current=null,a())})(...r),void 0);try{a=l.useSyncExternalStore(C,P,h?()=>b(h(),s):P)}catch(e){throw A.current&&(e.message+=`
The error may be correlated with this previous error:
${A.current.stack}

`),e}T(()=>{A.current=void 0,D.current=void 0,I.current=a});let O=l.useMemo(()=>l.createElement(e,{...a,ref:o}),[o,e,a]);return l.useMemo(()=>g?l.createElement(d.Provider,{value:v},O):O,[d,O,v])}let h=l.memo(m);if(h.WrappedComponent=e,h.displayName=m.displayName=r,d){let t=l.forwardRef(function(e,t){return l.createElement(h,{...e,reactReduxForwardedRef:t})});return t.displayName=r,t.WrappedComponent=e,Y(t,e)}return Y(h,e)}},er=function(e){let{children:t,context:r,serverState:n,store:a}=e,i=l.useMemo(()=>{let e=B(a);return{store:a,subscription:e,getServerState:n?()=>n:void 0}},[a,n]),o=l.useMemo(()=>a.getState(),[a]);return T(()=>{let{subscription:e}=i;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),o!==a.getState()&&e.notifyNestedSubs(),()=>{e.tryUnsubscribe(),e.onStateChange=void 0}},[i,o]),l.createElement((r||X).Provider,{value:i},t)},en=function(e){var t=e.top,r=e.right,n=e.bottom,l=e.left;return{top:t,right:r,bottom:n,left:l,width:r-l,height:n-t,x:l,y:t,center:{x:(r+l)/2,y:(n+t)/2}}},el=function(e,t){return{top:e.top-t.top,left:e.left-t.left,bottom:e.bottom+t.bottom,right:e.right+t.right}},ea=function(e,t){return{top:e.top+t.top,left:e.left+t.left,bottom:e.bottom-t.bottom,right:e.right-t.right}},ei={top:0,right:0,bottom:0,left:0},eo=function(e){var t=e.borderBox,r=e.margin,n=void 0===r?ei:r,l=e.border,a=void 0===l?ei:l,i=e.padding,o=void 0===i?ei:i,s=en(el(t,n)),d=en(ea(t,a)),c=en(ea(d,o));return{marginBox:s,borderBox:en(t),paddingBox:d,contentBox:c,margin:n,border:a,padding:o}},es=function(e){var t=e.slice(0,-2);if("px"!==e.slice(-2))return 0;var r=Number(t);return isNaN(r)&&function(e,t){if(!e)throw Error("Invariant failed")}(!1),r},ed=function(e,t){var r=e.borderBox,n=e.border,l=e.margin,a=e.padding;return eo({borderBox:{top:r.top+t.y,left:r.left+t.x,bottom:r.bottom+t.y,right:r.right+t.x},border:n,margin:l,padding:a})},ec=function(e,t){return void 0===t&&(t={x:window.pageXOffset,y:window.pageYOffset}),ed(e,t)},eu=function(e,t){return eo({borderBox:e,margin:{top:es(t.marginTop),right:es(t.marginRight),bottom:es(t.marginBottom),left:es(t.marginLeft)},padding:{top:es(t.paddingTop),right:es(t.paddingRight),bottom:es(t.paddingBottom),left:es(t.paddingLeft)},border:{top:es(t.borderTopWidth),right:es(t.borderRightWidth),bottom:es(t.borderBottomWidth),left:es(t.borderLeftWidth)}})},ep=function(e){return eu(e.getBoundingClientRect(),window.getComputedStyle(e))};let ef=function(e){var t=[],r=null,n=function(){for(var n=arguments.length,l=Array(n),a=0;a<n;a++)l[a]=arguments[a];t=l,r||(r=requestAnimationFrame(function(){r=null,e.apply(void 0,t)}))};return n.cancel=function(){r&&(cancelAnimationFrame(r),r=null)},n};function eg(){return(eg=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let em=/[ \t]{2,}/g,eh=/^[ \t]*/gm,eb=e=>e.replace(em," ").replace(eh,"").trim();function ex(e,t){}function ey(){}function ev(e,t,r){let n=t.map(t=>{var n;let l=(n=t.options,{...r,...n});return e.addEventListener(t.eventName,t.fn,l),function(){e.removeEventListener(t.eventName,t.fn,l)}});return function(){n.forEach(e=>{e()})}}ex.bind(null,"warn"),ex.bind(null,"error");class eI extends Error{}function eN(e,t){throw new eI("Invariant failed")}eI.prototype.toString=function(){return this.message};class eD extends a().Component{constructor(...e){super(...e),this.callbacks=null,this.unbind=ey,this.onWindowError=e=>{let t=this.getCallbacks();t.isDragging()&&t.tryAbort(),e.error instanceof eI&&e.preventDefault()},this.getCallbacks=()=>{if(!this.callbacks)throw Error("Unable to find AppCallbacks in <ErrorBoundary/>");return this.callbacks},this.setCallbacks=e=>{this.callbacks=e}}componentDidMount(){this.unbind=ev(window,[{eventName:"error",fn:this.onWindowError}])}componentDidCatch(e){if(e instanceof eI){this.setState({});return}throw e}componentWillUnmount(){this.unbind()}render(){return this.props.children(this.setCallbacks)}}let eE=e=>e+1,ew=(e,t)=>{let r=e.droppableId===t.droppableId,n=eE(e.index),l=eE(t.index);return r?`
      You have moved the item from position ${n}
      to position ${l}
    `:`
    You have moved the item from position ${n}
    in list ${e.droppableId}
    to list ${t.droppableId}
    in position ${l}
  `},eA=(e,t,r)=>t.droppableId===r.droppableId?`
      The item ${e}
      has been combined with ${r.draggableId}`:`
      The item ${e}
      in list ${t.droppableId}
      has been combined with ${r.draggableId}
      in list ${r.droppableId}
    `,eP=e=>`
  The item has returned to its starting position
  of ${eE(e.index)}
`,eC={dragHandleUsageInstructions:`
  Press space bar to start a drag.
  When dragging you can use the arrow keys to move the item around and escape to cancel.
  Some screen readers may require you to be in focus mode or to use your pass through key
`,onDragStart:e=>`
  You have lifted an item in position ${eE(e.source.index)}
`,onDragUpdate:e=>{let t=e.destination;if(t)return ew(e.source,t);let r=e.combine;return r?eA(e.draggableId,e.source,r):"You are over an area that cannot be dropped on"},onDragEnd:e=>{if("CANCEL"===e.reason)return`
      Movement cancelled.
      ${eP(e.source)}
    `;let t=e.destination,r=e.combine;return t?`
      You have dropped the item.
      ${ew(e.source,t)}
    `:r?`
      You have dropped the item.
      ${eA(e.draggableId,e.source,r)}
    `:`
    The item has been dropped while not over a drop area.
    ${eP(e.source)}
  `}};function eO(e,t){if(e.length!==t.length)return!1;for(let l=0;l<e.length;l++){var r,n;if(!((r=e[l])===(n=t[l])||Number.isNaN(r)&&Number.isNaN(n)))return!1}return!0}function eR(e,t){let r=(0,l.useState)(()=>({inputs:t,result:e()}))[0],n=(0,l.useRef)(!0),a=(0,l.useRef)(r),i=n.current||t&&a.current.inputs&&eO(t,a.current.inputs)?a.current:{inputs:t,result:e()};return(0,l.useEffect)(()=>{n.current=!1,a.current=i},[i]),i.result}function eS(e,t){return eR(()=>e,t)}let ej={x:0,y:0},eL=(e,t)=>({x:e.x+t.x,y:e.y+t.y}),eB=(e,t)=>({x:e.x-t.x,y:e.y-t.y}),eT=(e,t)=>e.x===t.x&&e.y===t.y,eG=e=>({x:0!==e.x?-e.x:0,y:0!==e.y?-e.y:0}),e_=(e,t,r=0)=>"x"===e?{x:t,y:r}:{x:r,y:t},eM=(e,t)=>Math.sqrt((t.x-e.x)**2+(t.y-e.y)**2),ek=(e,t)=>Math.min(...t.map(t=>eM(e,t))),e$=e=>t=>({x:e(t.x),y:e(t.y)});var eF=(e,t)=>{let r=en({top:Math.max(t.top,e.top),right:Math.min(t.right,e.right),bottom:Math.min(t.bottom,e.bottom),left:Math.max(t.left,e.left)});return r.width<=0||r.height<=0?null:r};let eW=(e,t)=>({top:e.top+t.y,left:e.left+t.x,bottom:e.bottom+t.y,right:e.right+t.x}),eU=e=>[{x:e.left,y:e.top},{x:e.right,y:e.top},{x:e.left,y:e.bottom},{x:e.right,y:e.bottom}],eH=(e,t)=>t?eW(e,t.scroll.diff.displacement):e,eV=(e,t,r)=>r&&r.increasedBy?{...e,[t.end]:e[t.end]+r.increasedBy[t.line]}:e,eZ=(e,t)=>t&&t.shouldClipSubject?eF(t.pageMarginBox,e):en(e);var eq=({page:e,withPlaceholder:t,axis:r,frame:n})=>{let l=eZ(eV(eH(e.marginBox,n),r,t),n);return{page:e,withPlaceholder:t,active:l}},ez=(e,t)=>{e.frame||eN();let r=e.frame,n=eB(t,r.scroll.initial),l=eG(n),a={...r,scroll:{initial:r.scroll.initial,current:t,diff:{value:n,displacement:l},max:r.scroll.max}},i=eq({page:e.subject.page,withPlaceholder:e.subject.withPlaceholder,axis:e.axis,frame:a});return{...e,frame:a,subject:i}};function eY(e,t=eO){let r=null;function n(...l){if(r&&r.lastThis===this&&t(l,r.lastArgs))return r.lastResult;let a=e.apply(this,l);return r={lastResult:a,lastArgs:l,lastThis:this},a}return n.clear=function(){r=null},n}let eQ=eY(e=>e.reduce((e,t)=>(e[t.descriptor.id]=t,e),{})),eJ=eY(e=>e.reduce((e,t)=>(e[t.descriptor.id]=t,e),{})),eX=eY(e=>Object.values(e)),eK=eY(e=>Object.values(e));var e0=eY((e,t)=>eK(t).filter(t=>e===t.descriptor.droppableId).sort((e,t)=>e.descriptor.index-t.descriptor.index));function e1(e){return e.at&&"REORDER"===e.at.type?e.at.destination:null}function e2(e){return e.at&&"COMBINE"===e.at.type?e.at.combine:null}var e4=eY((e,t)=>t.filter(t=>t.descriptor.id!==e.descriptor.id)),e6=({isMovingForward:e,draggable:t,destination:r,insideDestination:n,previousImpact:l})=>{if(!r.isCombineEnabled||!e1(l))return null;function a(e){let t={type:"COMBINE",combine:{draggableId:e,droppableId:r.descriptor.id}};return{...l,at:t}}let i=l.displaced.all,o=i.length?i[0]:null;if(e)return o?a(o):null;let s=e4(t,n);if(!o)return s.length?a(s[s.length-1].descriptor.id):null;let d=s.findIndex(e=>e.descriptor.id===o);-1!==d||eN();let c=d-1;return c<0?null:a(s[c].descriptor.id)},e3=(e,t)=>e.descriptor.droppableId===t.descriptor.id;let e7={point:ej,value:0},e5={invisible:{},visible:{},all:[]},e9={displaced:e5,displacedBy:e7,at:null};var e8=(e,t)=>r=>e<=r&&r<=t,te=e=>{let t=e8(e.top,e.bottom),r=e8(e.left,e.right);return n=>{if(t(n.top)&&t(n.bottom)&&r(n.left)&&r(n.right))return!0;let l=t(n.top)||t(n.bottom),a=r(n.left)||r(n.right);if(l&&a)return!0;let i=n.top<e.top&&n.bottom>e.bottom,o=n.left<e.left&&n.right>e.right;return!!i&&!!o||i&&a||o&&l}},tt=e=>{let t=e8(e.top,e.bottom),r=e8(e.left,e.right);return e=>t(e.top)&&t(e.bottom)&&r(e.left)&&r(e.right)};let tr={direction:"vertical",line:"y",crossAxisLine:"x",start:"top",end:"bottom",size:"height",crossAxisStart:"left",crossAxisEnd:"right",crossAxisSize:"width"},tn={direction:"horizontal",line:"x",crossAxisLine:"y",start:"left",end:"right",size:"width",crossAxisStart:"top",crossAxisEnd:"bottom",crossAxisSize:"height"};var tl=e=>t=>{let r=e8(t.top,t.bottom),n=e8(t.left,t.right);return t=>e===tr?r(t.top)&&r(t.bottom):n(t.left)&&n(t.right)};let ta=(e,t)=>eW(e,t.frame?t.frame.scroll.diff.displacement:ej),ti=(e,t,r)=>!!t.subject.active&&r(t.subject.active)(e),to=(e,t,r)=>r(t)(e),ts=({target:e,destination:t,viewport:r,withDroppableDisplacement:n,isVisibleThroughFrameFn:l})=>{let a=n?ta(e,t):e;return ti(a,t,l)&&to(a,r,l)},td=e=>ts({...e,isVisibleThroughFrameFn:te}),tc=e=>ts({...e,isVisibleThroughFrameFn:tt}),tu=e=>ts({...e,isVisibleThroughFrameFn:tl(e.destination.axis)}),tp=(e,t,r)=>{if("boolean"==typeof r)return r;if(!t)return!0;let{invisible:n,visible:l}=t;if(n[e])return!1;let a=l[e];return!a||a.shouldAnimate};function tf({afterDragging:e,destination:t,displacedBy:r,viewport:n,forceShouldAnimate:l,last:a}){return e.reduce(function(e,i){let o=en(el(i.page.marginBox,{top:r.point.y,right:0,bottom:0,left:r.point.x})),s=i.descriptor.id;if(e.all.push(s),!td({target:o,destination:t,viewport:n,withDroppableDisplacement:!0}))return e.invisible[i.descriptor.id]=!0,e;let d=tp(s,a,l);return e.visible[s]={draggableId:s,shouldAnimate:d},e},{all:[],visible:{},invisible:{}})}function tg({insideDestination:e,inHomeList:t,displacedBy:r,destination:n}){let l=function(e,t){if(!e.length)return 0;let r=e[e.length-1].descriptor.index;return t.inHomeList?r:r+1}(e,{inHomeList:t});return{displaced:e5,displacedBy:r,at:{type:"REORDER",destination:{droppableId:n.descriptor.id,index:l}}}}function tm({draggable:e,insideDestination:t,destination:r,viewport:n,displacedBy:l,last:a,index:i,forceShouldAnimate:o}){let s=e3(e,r);if(null==i)return tg({insideDestination:t,inHomeList:s,displacedBy:l,destination:r});let d=t.find(e=>e.descriptor.index===i);if(!d)return tg({insideDestination:t,inHomeList:s,displacedBy:l,destination:r});let c=e4(e,t),u=t.indexOf(d);return{displaced:tf({afterDragging:c.slice(u),destination:r,displacedBy:l,last:a,viewport:n.frame,forceShouldAnimate:o}),displacedBy:l,at:{type:"REORDER",destination:{droppableId:r.descriptor.id,index:i}}}}function th(e,t){return!!t.effected[e]}var tb=({isMovingForward:e,destination:t,draggables:r,combine:n,afterCritical:l})=>{if(!t.isCombineEnabled)return null;let a=n.draggableId,i=r[a].descriptor.index;return th(a,l)?e?i:i-1:e?i+1:i},tx=({isMovingForward:e,isInHomeList:t,insideDestination:r,location:n})=>{if(!r.length)return null;let l=n.index,a=e?l+1:l-1,i=r[0].descriptor.index,o=r[r.length-1].descriptor.index;return a<i||a>(t?o:o+1)?null:a},ty=({isMovingForward:e,isInHomeList:t,draggable:r,draggables:n,destination:l,insideDestination:a,previousImpact:i,viewport:o,afterCritical:s})=>{let d=i.at;if(d||eN(),"REORDER"===d.type){let n=tx({isMovingForward:e,isInHomeList:t,location:d.destination,insideDestination:a});return null==n?null:tm({draggable:r,insideDestination:a,destination:l,viewport:o,last:i.displaced,displacedBy:i.displacedBy,index:n})}let c=tb({isMovingForward:e,destination:l,displaced:i.displaced,draggables:n,combine:d.combine,afterCritical:s});return null==c?null:tm({draggable:r,insideDestination:a,destination:l,viewport:o,last:i.displaced,displacedBy:i.displacedBy,index:c})},tv=({displaced:e,afterCritical:t,combineWith:r,displacedBy:n})=>{let l=!!(e.visible[r]||e.invisible[r]);return th(r,t)?l?ej:eG(n.point):l?n.point:ej},tI=({afterCritical:e,impact:t,draggables:r})=>{let n=e2(t);n||eN();let l=n.draggableId;return eL(r[l].page.borderBox.center,tv({displaced:t.displaced,afterCritical:e,combineWith:l,displacedBy:t.displacedBy}))};let tN=(e,t)=>t.margin[e.start]+t.borderBox[e.size]/2,tD=(e,t)=>t.margin[e.end]+t.borderBox[e.size]/2,tE=(e,t,r)=>t[e.crossAxisStart]+r.margin[e.crossAxisStart]+r.borderBox[e.crossAxisSize]/2,tw=({axis:e,moveRelativeTo:t,isMoving:r})=>e_(e.line,t.marginBox[e.end]+tN(e,r),tE(e,t.marginBox,r)),tA=({axis:e,moveRelativeTo:t,isMoving:r})=>e_(e.line,t.marginBox[e.start]-tD(e,r),tE(e,t.marginBox,r)),tP=({axis:e,moveInto:t,isMoving:r})=>e_(e.line,t.contentBox[e.start]+tN(e,r),tE(e,t.contentBox,r));var tC=({impact:e,draggable:t,draggables:r,droppable:n,afterCritical:l})=>{let a=e0(n.descriptor.id,r),i=t.page,o=n.axis;if(!a.length)return tP({axis:o,moveInto:n.page,isMoving:i});let{displaced:s,displacedBy:d}=e,c=s.all[0];if(c){let e=r[c];return th(c,l)?tA({axis:o,moveRelativeTo:e.page,isMoving:i}):tA({axis:o,moveRelativeTo:ed(e.page,d.point),isMoving:i})}let u=a[a.length-1];return u.descriptor.id===t.descriptor.id?i.borderBox.center:th(u.descriptor.id,l)?tw({axis:o,moveRelativeTo:ed(u.page,eG(l.displacedBy.point)),isMoving:i}):tw({axis:o,moveRelativeTo:u.page,isMoving:i})},tO=(e,t)=>{let r=e.frame;return r?eL(t,r.scroll.diff.displacement):t};let tR=({impact:e,draggable:t,droppable:r,draggables:n,afterCritical:l})=>{let a=t.page.borderBox.center,i=e.at;return r&&i?"REORDER"===i.type?tC({impact:e,draggable:t,draggables:n,droppable:r,afterCritical:l}):tI({impact:e,draggables:n,afterCritical:l}):a};var tS=e=>{let t=tR(e),r=e.droppable;return r?tO(r,t):t},tj=(e,t)=>{let r=eB(t,e.scroll.initial),n=eG(r);return{frame:en({top:t.y,bottom:t.y+e.frame.height,left:t.x,right:t.x+e.frame.width}),scroll:{initial:e.scroll.initial,max:e.scroll.max,current:t,diff:{value:r,displacement:n}}}};function tL(e,t){return e.map(e=>t[e])}var tB=({impact:e,viewport:t,destination:r,draggables:n,maxScrollChange:l})=>{let a=tj(t,eL(t.scroll.current,l)),i=r.frame?ez(r,eL(r.frame.scroll.current,l)):r,o=e.displaced,s=tf({afterDragging:tL(o.all,n),destination:r,displacedBy:e.displacedBy,viewport:a.frame,last:o,forceShouldAnimate:!1}),d=tf({afterDragging:tL(o.all,n),destination:i,displacedBy:e.displacedBy,viewport:t.frame,last:o,forceShouldAnimate:!1}),c={},u={},p=[o,s,d];return o.all.forEach(e=>{let t=function(e,t){for(let r=0;r<t.length;r++){let n=t[r].visible[e];if(n)return n}return null}(e,p);if(t){u[e]=t;return}c[e]=!0}),{...e,displaced:{all:o.all,invisible:c,visible:u}}},tT=(e,t)=>eL(e.scroll.diff.displacement,t),tG=({pageBorderBoxCenter:e,draggable:t,viewport:r})=>{let n=eB(tT(r,e),t.page.borderBox.center);return eL(t.client.borderBox.center,n)},t_=({draggable:e,destination:t,newPageBorderBoxCenter:r,viewport:n,withDroppableDisplacement:l,onlyOnMainAxis:a=!1})=>{let i=eB(r,e.page.borderBox.center),o={target:eW(e.page.borderBox,i),destination:t,withDroppableDisplacement:l,viewport:n};return a?tu(o):tc(o)},tM=({isMovingForward:e,draggable:t,destination:r,draggables:n,previousImpact:l,viewport:a,previousPageBorderBoxCenter:i,previousClientSelection:o,afterCritical:s})=>{if(!r.isEnabled)return null;let d=e0(r.descriptor.id,n),c=e3(t,r),u=e6({isMovingForward:e,draggable:t,destination:r,insideDestination:d,previousImpact:l})||ty({isMovingForward:e,isInHomeList:c,draggable:t,draggables:n,destination:r,insideDestination:d,previousImpact:l,viewport:a,afterCritical:s});if(!u)return null;let p=tS({impact:u,draggable:t,droppable:r,draggables:n,afterCritical:s});if(t_({draggable:t,destination:r,newPageBorderBoxCenter:p,viewport:a.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0}))return{clientSelection:tG({pageBorderBoxCenter:p,draggable:t,viewport:a}),impact:u,scrollJumpRequest:null};let f=eB(p,i);return{clientSelection:o,impact:tB({impact:u,viewport:a,destination:r,draggables:n,maxScrollChange:f}),scrollJumpRequest:f}};let tk=e=>{let t=e.subject.active;return t||eN(),t};var t$=({isMovingForward:e,pageBorderBoxCenter:t,source:r,droppables:n,viewport:l})=>{let a=r.subject.active;if(!a)return null;let i=r.axis,o=e8(a[i.start],a[i.end]),s=eX(n).filter(e=>e!==r).filter(e=>e.isEnabled).filter(e=>!!e.subject.active).filter(e=>te(l.frame)(tk(e))).filter(t=>{let r=tk(t);return e?a[i.crossAxisEnd]<r[i.crossAxisEnd]:r[i.crossAxisStart]<a[i.crossAxisStart]}).filter(e=>{let t=tk(e),r=e8(t[i.start],t[i.end]);return o(t[i.start])||o(t[i.end])||r(a[i.start])||r(a[i.end])}).sort((t,r)=>{let n=tk(t)[i.crossAxisStart],l=tk(r)[i.crossAxisStart];return e?n-l:l-n}).filter((e,t,r)=>tk(e)[i.crossAxisStart]===tk(r[0])[i.crossAxisStart]);if(!s.length)return null;if(1===s.length)return s[0];let d=s.filter(e=>e8(tk(e)[i.start],tk(e)[i.end])(t[i.line]));return 1===d.length?d[0]:d.length>1?d.sort((e,t)=>tk(e)[i.start]-tk(t)[i.start])[0]:s.sort((e,r)=>{let n=ek(t,eU(tk(e))),l=ek(t,eU(tk(r)));return n!==l?n-l:tk(e)[i.start]-tk(r)[i.start]})[0]};let tF=(e,t)=>{let r=e.page.borderBox.center;return th(e.descriptor.id,t)?eB(r,t.displacedBy.point):r},tW=(e,t)=>{let r=e.page.borderBox;return th(e.descriptor.id,t)?eW(r,eG(t.displacedBy.point)):r};var tU=({pageBorderBoxCenter:e,viewport:t,destination:r,insideDestination:n,afterCritical:l})=>n.filter(e=>tc({target:tW(e,l),destination:r,viewport:t.frame,withDroppableDisplacement:!0})).sort((t,n)=>{let a=eM(e,tO(r,tF(t,l))),i=eM(e,tO(r,tF(n,l)));return a<i?-1:i<a?1:t.descriptor.index-n.descriptor.index})[0]||null,tH=eY(function(e,t){let r=t[e.line];return{value:r,point:e_(e.line,r)}});let tV=(e,t,r)=>{let n=e.axis;if("virtual"===e.descriptor.mode)return e_(n.line,t[n.line]);let l=e.subject.page.contentBox[n.size],a=e0(e.descriptor.id,r).reduce((e,t)=>e+t.client.marginBox[n.size],0)+t[n.line]-l;return a<=0?null:e_(n.line,a)},tZ=(e,t)=>({...e,scroll:{...e.scroll,max:t}}),tq=(e,t,r)=>{let n=e.frame;e3(t,e)&&eN(),e.subject.withPlaceholder&&eN();let l=tH(e.axis,t.displaceBy).point,a=tV(e,l,r),i={placeholderSize:l,increasedBy:a,oldFrameMaxScroll:e.frame?e.frame.scroll.max:null};if(!n){let t=eq({page:e.subject.page,withPlaceholder:i,axis:e.axis,frame:e.frame});return{...e,subject:t}}let o=a?eL(n.scroll.max,a):n.scroll.max,s=tZ(n,o),d=eq({page:e.subject.page,withPlaceholder:i,axis:e.axis,frame:s});return{...e,subject:d,frame:s}},tz=e=>{let t=e.subject.withPlaceholder;t||eN();let r=e.frame;if(!r){let t=eq({page:e.subject.page,axis:e.axis,frame:null,withPlaceholder:null});return{...e,subject:t}}let n=t.oldFrameMaxScroll;n||eN();let l=tZ(r,n),a=eq({page:e.subject.page,axis:e.axis,frame:l,withPlaceholder:null});return{...e,subject:a,frame:l}};var tY=({previousPageBorderBoxCenter:e,moveRelativeTo:t,insideDestination:r,draggable:n,draggables:l,destination:a,viewport:i,afterCritical:o})=>{if(!t){if(r.length)return null;let e={displaced:e5,displacedBy:e7,at:{type:"REORDER",destination:{droppableId:a.descriptor.id,index:0}}},t=tS({impact:e,draggable:n,droppable:a,draggables:l,afterCritical:o}),s=e3(n,a)?a:tq(a,n,l);return t_({draggable:n,destination:s,newPageBorderBoxCenter:t,viewport:i.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0})?e:null}let s=e[a.axis.line]<=t.page.borderBox.center[a.axis.line],d=(()=>{let e=t.descriptor.index;return t.descriptor.id===n.descriptor.id||s?e:e+1})(),c=tH(a.axis,n.displaceBy);return tm({draggable:n,insideDestination:r,destination:a,viewport:i,displacedBy:c,last:e5,index:d})},tQ=({isMovingForward:e,previousPageBorderBoxCenter:t,draggable:r,isOver:n,draggables:l,droppables:a,viewport:i,afterCritical:o})=>{let s=t$({isMovingForward:e,pageBorderBoxCenter:t,source:n,droppables:a,viewport:i});if(!s)return null;let d=e0(s.descriptor.id,l),c=tU({pageBorderBoxCenter:t,viewport:i,destination:s,insideDestination:d,afterCritical:o}),u=tY({previousPageBorderBoxCenter:t,destination:s,draggable:r,draggables:l,moveRelativeTo:c,insideDestination:d,viewport:i,afterCritical:o});return u?{clientSelection:tG({pageBorderBoxCenter:tS({impact:u,draggable:r,droppable:s,draggables:l,afterCritical:o}),draggable:r,viewport:i}),impact:u,scrollJumpRequest:null}:null},tJ=e=>{let t=e.at;return t?"REORDER"===t.type?t.destination.droppableId:t.combine.droppableId:null};let tX=(e,t)=>{let r=tJ(e);return r?t[r]:null};var tK=({state:e,type:t})=>{let r=tX(e.impact,e.dimensions.droppables),n=!!r,l=e.dimensions.droppables[e.critical.droppable.id],a=r||l,i=a.axis.direction,o="vertical"===i&&("MOVE_UP"===t||"MOVE_DOWN"===t)||"horizontal"===i&&("MOVE_LEFT"===t||"MOVE_RIGHT"===t);if(o&&!n)return null;let s="MOVE_DOWN"===t||"MOVE_RIGHT"===t,d=e.dimensions.draggables[e.critical.draggable.id],c=e.current.page.borderBoxCenter,{draggables:u,droppables:p}=e.dimensions;return o?tM({isMovingForward:s,previousPageBorderBoxCenter:c,draggable:d,destination:a,draggables:u,viewport:e.viewport,previousClientSelection:e.current.client.selection,previousImpact:e.impact,afterCritical:e.afterCritical}):tQ({isMovingForward:s,previousPageBorderBoxCenter:c,draggable:d,isOver:a,draggables:u,droppables:p,viewport:e.viewport,afterCritical:e.afterCritical})};function t0(e){return"DRAGGING"===e.phase||"COLLECTING"===e.phase}function t1(e){let t=e8(e.top,e.bottom),r=e8(e.left,e.right);return function(e){return t(e.y)&&r(e.x)}}let t2=(e,t)=>en(eW(e,t));var t4=(e,t)=>{let r=e.frame;return r?t2(t,r.scroll.diff.value):t};function t6({displaced:e,id:t}){return!!(e.visible[t]||e.invisible[t])}var t3=({pageBorderBoxWithDroppableScroll:e,draggable:t,destination:r,insideDestination:n,last:l,viewport:a,afterCritical:i})=>{let o=r.axis,s=tH(r.axis,t.displaceBy),d=s.value,c=e[o.start],u=e[o.end],p=e4(t,n).find(e=>{let t=e.descriptor.id,r=e.page.borderBox.center[o.line],n=th(t,i),a=t6({displaced:l,id:t});return n?a?u<=r:c<r-d:a?u<=r+d:c<r})||null,f=function({draggable:e,closest:t,inHomeList:r}){return t?r&&t.descriptor.index>e.descriptor.index?t.descriptor.index-1:t.descriptor.index:null}({draggable:t,closest:p,inHomeList:e3(t,r)});return tm({draggable:t,insideDestination:n,destination:r,viewport:a,last:l,displacedBy:s,index:f})},t7=({draggable:e,pageBorderBoxWithDroppableScroll:t,previousImpact:r,destination:n,insideDestination:l,afterCritical:a})=>{if(!n.isCombineEnabled)return null;let i=n.axis,o=tH(n.axis,e.displaceBy),s=o.value,d=t[i.start],c=t[i.end],u=e4(e,l).find(e=>{let t=e.descriptor.id,n=e.page.borderBox,l=n[i.size]/4,o=th(t,a),u=t6({displaced:r.displaced,id:t});return o?u?c>n[i.start]+l&&c<n[i.end]-l:d>n[i.start]-s+l&&d<n[i.end]-s-l:u?c>n[i.start]+s+l&&c<n[i.end]+s-l:d>n[i.start]+l&&d<n[i.end]-l});return u?{displacedBy:o,displaced:r.displaced,at:{type:"COMBINE",combine:{draggableId:u.descriptor.id,droppableId:n.descriptor.id}}}:null},t5=({pageOffset:e,draggable:t,draggables:r,droppables:n,previousImpact:l,viewport:a,afterCritical:i})=>{let o=t2(t.page.borderBox,e),s=function({pageBorderBox:e,draggable:t,droppables:r}){let n=eX(r).filter(t=>{if(!t.isEnabled)return!1;let r=t.subject.active;if(!r||!(e.left<r.right)||!(e.right>r.left)||!(e.top<r.bottom)||!(e.bottom>r.top))return!1;if(t1(r)(e.center))return!0;let n=t.axis,l=r.center[n.crossAxisLine],a=e[n.crossAxisStart],i=e[n.crossAxisEnd],o=e8(r[n.crossAxisStart],r[n.crossAxisEnd]),s=o(a),d=o(i);return!s&&!d||(s?a<l:i>l)});return n.length?1===n.length?n[0].descriptor.id:function({pageBorderBox:e,draggable:t,candidates:r}){let n=t.page.borderBox.center,l=r.map(t=>{let r=t.axis,l=e_(t.axis.line,e.center[r.line],t.page.borderBox.center[r.crossAxisLine]);return{id:t.descriptor.id,distance:eM(n,l)}}).sort((e,t)=>t.distance-e.distance);return l[0]?l[0].id:null}({pageBorderBox:e,draggable:t,candidates:n}):null}({pageBorderBox:o,draggable:t,droppables:n});if(!s)return e9;let d=n[s],c=e0(d.descriptor.id,r),u=t4(d,o);return t7({pageBorderBoxWithDroppableScroll:u,draggable:t,previousImpact:l,destination:d,insideDestination:c,afterCritical:i})||t3({pageBorderBoxWithDroppableScroll:u,draggable:t,destination:d,insideDestination:c,last:l.displaced,viewport:a,afterCritical:i})},t9=(e,t)=>({...e,[t.descriptor.id]:t});let t8=({previousImpact:e,impact:t,droppables:r})=>{let n=tJ(e),l=tJ(t);if(!n||n===l)return r;let a=r[n];return a.subject.withPlaceholder?t9(r,tz(a)):r};var re=({draggable:e,draggables:t,droppables:r,previousImpact:n,impact:l})=>{let a=t8({previousImpact:n,impact:l,droppables:r}),i=tJ(l);if(!i)return a;let o=r[i];return e3(e,o)||o.subject.withPlaceholder?a:t9(a,tq(o,e,t))},rt=({state:e,clientSelection:t,dimensions:r,viewport:n,impact:l,scrollJumpRequest:a})=>{let i=n||e.viewport,o=r||e.dimensions,s=t||e.current.client.selection,d=eB(s,e.initial.client.selection),c={offset:d,selection:s,borderBoxCenter:eL(e.initial.client.borderBoxCenter,d)},u={selection:eL(c.selection,i.scroll.current),borderBoxCenter:eL(c.borderBoxCenter,i.scroll.current),offset:eL(c.offset,i.scroll.diff.value)},p={client:c,page:u};if("COLLECTING"===e.phase)return{...e,dimensions:o,viewport:i,current:p};let f=o.draggables[e.critical.draggable.id],g=l||t5({pageOffset:u.offset,draggable:f,draggables:o.draggables,droppables:o.droppables,previousImpact:e.impact,viewport:i,afterCritical:e.afterCritical}),m=re({draggable:f,impact:g,previousImpact:e.impact,draggables:o.draggables,droppables:o.droppables});return{...e,current:p,dimensions:{draggables:o.draggables,droppables:m},impact:g,viewport:i,scrollJumpRequest:a||null,forceShouldAnimate:!a&&null}},rr=({impact:e,viewport:t,draggables:r,destination:n,forceShouldAnimate:l})=>{let a=e.displaced,i=tf({afterDragging:a.all.map(e=>r[e]),destination:n,displacedBy:e.displacedBy,viewport:t.frame,forceShouldAnimate:l,last:a});return{...e,displaced:i}},rn=({impact:e,draggable:t,droppable:r,draggables:n,viewport:l,afterCritical:a})=>tG({pageBorderBoxCenter:tS({impact:e,draggable:t,draggables:n,droppable:r,afterCritical:a}),draggable:t,viewport:l}),rl=({state:e,dimensions:t,viewport:r})=>{"SNAP"!==e.movementMode&&eN();let n=e.impact,l=r||e.viewport,a=t||e.dimensions,{draggables:i,droppables:o}=a,s=i[e.critical.draggable.id],d=tJ(n);d||eN();let c=o[d],u=rr({impact:n,viewport:l,destination:c,draggables:i}),p=rn({impact:u,draggable:s,droppable:c,draggables:i,viewport:l,afterCritical:e.afterCritical});return rt({impact:u,clientSelection:p,state:e,dimensions:a,viewport:l})},ra=e=>({index:e.index,droppableId:e.droppableId}),ri=({draggable:e,home:t,draggables:r,viewport:n})=>{let l=tH(t.axis,e.displaceBy),a=e0(t.descriptor.id,r),i=a.indexOf(e);-1!==i||eN();let o=a.slice(i+1),s=o.reduce((e,t)=>(e[t.descriptor.id]=!0,e),{}),d={inVirtualList:"virtual"===t.descriptor.mode,displacedBy:l,effected:s};return{impact:{displaced:tf({afterDragging:o,destination:t,displacedBy:l,last:null,viewport:n.frame,forceShouldAnimate:!1}),displacedBy:l,at:{type:"REORDER",destination:ra(e.descriptor)}},afterCritical:d}},ro=(e,t)=>({draggables:e.draggables,droppables:t9(e.droppables,t)});let rs=e=>{},rd=e=>{};var rc=({draggable:e,offset:t,initialWindowScroll:r})=>{let n=ed(e.client,t),l=ec(n,r);return{...e,placeholder:{...e.placeholder,client:n},client:n,page:l}},ru=e=>{let t=e.frame;return t||eN(),t},rp=({additions:e,updatedDroppables:t,viewport:r})=>{let n=r.scroll.diff.value;return e.map(e=>{let l=eL(n,ru(t[e.descriptor.droppableId]).scroll.diff.value);return rc({draggable:e,offset:l,initialWindowScroll:r.scroll.initial})})},rf=({state:e,published:t})=>{rs();let r=t.modified.map(t=>ez(e.dimensions.droppables[t.droppableId],t.scroll)),n={...e.dimensions.droppables,...eQ(r)},l=eJ(rp({additions:t.additions,updatedDroppables:n,viewport:e.viewport})),a={...e.dimensions.draggables,...l};t.removals.forEach(e=>{delete a[e]});let i={droppables:n,draggables:a},o=tJ(e.impact),s=o?i.droppables[o]:null,{impact:d,afterCritical:c}=ri({draggable:i.draggables[e.critical.draggable.id],home:i.droppables[e.critical.droppable.id],draggables:a,viewport:e.viewport}),u=s&&s.isCombineEnabled?e.impact:d,p=t5({pageOffset:e.current.page.offset,draggable:i.draggables[e.critical.draggable.id],draggables:i.draggables,droppables:i.droppables,previousImpact:u,viewport:e.viewport,afterCritical:c});rd();let f={...e,phase:"DRAGGING",impact:p,onLiftImpact:d,dimensions:i,afterCritical:c,forceShouldAnimate:!1};return"COLLECTING"===e.phase?f:{...f,phase:"DROP_PENDING",reason:e.reason,isWaiting:!1}};let rg=e=>"SNAP"===e.movementMode,rm=(e,t,r)=>{let n=ro(e.dimensions,t);return!rg(e)||r?rt({state:e,dimensions:n}):rl({state:e,dimensions:n})};function rh(e){return e.isDragging&&"SNAP"===e.movementMode?{...e,scrollJumpRequest:null}:e}let rb={phase:"IDLE",completed:null,shouldFlush:!1};var rx=(e=rb,t)=>{if("FLUSH"===t.type)return{...rb,shouldFlush:!0};if("INITIAL_PUBLISH"===t.type){"IDLE"!==e.phase&&eN();let{critical:r,clientSelection:n,viewport:l,dimensions:a,movementMode:i}=t.payload,o=a.draggables[r.draggable.id],s=a.droppables[r.droppable.id],d={selection:n,borderBoxCenter:o.client.borderBox.center,offset:ej},c={client:d,page:{selection:eL(d.selection,l.scroll.initial),borderBoxCenter:eL(d.selection,l.scroll.initial),offset:eL(d.selection,l.scroll.diff.value)}},u=eX(a.droppables).every(e=>!e.isFixedOnPage),{impact:p,afterCritical:f}=ri({draggable:o,home:s,draggables:a.draggables,viewport:l});return{phase:"DRAGGING",isDragging:!0,critical:r,movementMode:i,dimensions:a,initial:c,current:c,isWindowScrollAllowed:u,impact:p,afterCritical:f,onLiftImpact:p,viewport:l,scrollJumpRequest:null,forceShouldAnimate:null}}if("COLLECTION_STARTING"===t.type)return"COLLECTING"===e.phase||"DROP_PENDING"===e.phase?e:("DRAGGING"!==e.phase&&eN(),{...e,phase:"COLLECTING"});if("PUBLISH_WHILE_DRAGGING"===t.type)return"COLLECTING"===e.phase||"DROP_PENDING"===e.phase||eN(),rf({state:e,published:t.payload});if("MOVE"===t.type){if("DROP_PENDING"===e.phase)return e;t0(e)||eN();let{client:r}=t.payload;return eT(r,e.current.client.selection)?e:rt({state:e,clientSelection:r,impact:rg(e)?e.impact:null})}if("UPDATE_DROPPABLE_SCROLL"===t.type){if("DROP_PENDING"===e.phase||"COLLECTING"===e.phase)return rh(e);t0(e)||eN();let{id:r,newScroll:n}=t.payload,l=e.dimensions.droppables[r];return l?rm(e,ez(l,n),!1):e}if("UPDATE_DROPPABLE_IS_ENABLED"===t.type){if("DROP_PENDING"===e.phase)return e;t0(e)||eN();let{id:r,isEnabled:n}=t.payload,l=e.dimensions.droppables[r];return l||eN(),l.isEnabled!==n||eN(),rm(e,{...l,isEnabled:n},!0)}if("UPDATE_DROPPABLE_IS_COMBINE_ENABLED"===t.type){if("DROP_PENDING"===e.phase)return e;t0(e)||eN();let{id:r,isCombineEnabled:n}=t.payload,l=e.dimensions.droppables[r];return l||eN(),l.isCombineEnabled!==n||eN(),rm(e,{...l,isCombineEnabled:n},!0)}if("MOVE_BY_WINDOW_SCROLL"===t.type){if("DROP_PENDING"===e.phase||"DROP_ANIMATING"===e.phase)return e;t0(e)||eN(),e.isWindowScrollAllowed||eN();let r=t.payload.newScroll;if(eT(e.viewport.scroll.current,r))return rh(e);let n=tj(e.viewport,r);return rg(e)?rl({state:e,viewport:n}):rt({state:e,viewport:n})}if("UPDATE_VIEWPORT_MAX_SCROLL"===t.type){if(!t0(e))return e;let r=t.payload.maxScroll;if(eT(r,e.viewport.scroll.max))return e;let n={...e.viewport,scroll:{...e.viewport.scroll,max:r}};return{...e,viewport:n}}if("MOVE_UP"===t.type||"MOVE_DOWN"===t.type||"MOVE_LEFT"===t.type||"MOVE_RIGHT"===t.type){if("COLLECTING"===e.phase||"DROP_PENDING"===e.phase)return e;"DRAGGING"!==e.phase&&eN();let r=tK({state:e,type:t.type});return r?rt({state:e,impact:r.impact,clientSelection:r.clientSelection,scrollJumpRequest:r.scrollJumpRequest}):e}if("DROP_PENDING"===t.type){let r=t.payload.reason;return"COLLECTING"!==e.phase&&eN(),{...e,phase:"DROP_PENDING",isWaiting:!0,reason:r}}if("DROP_ANIMATE"===t.type){let{completed:r,dropDuration:n,newHomeClientOffset:l}=t.payload;return"DRAGGING"===e.phase||"DROP_PENDING"===e.phase||eN(),{phase:"DROP_ANIMATING",completed:r,dropDuration:n,newHomeClientOffset:l,dimensions:e.dimensions}}if("DROP_COMPLETE"===t.type){let{completed:e}=t.payload;return{phase:"IDLE",completed:e,shouldFlush:!1}}return e};function ry(e,t){return e instanceof Object&&"type"in e&&e.type===t}let rv=e=>({type:"BEFORE_INITIAL_CAPTURE",payload:e}),rI=e=>({type:"LIFT",payload:e}),rN=e=>({type:"INITIAL_PUBLISH",payload:e}),rD=e=>({type:"PUBLISH_WHILE_DRAGGING",payload:e}),rE=()=>({type:"COLLECTION_STARTING",payload:null}),rw=e=>({type:"UPDATE_DROPPABLE_SCROLL",payload:e}),rA=e=>({type:"UPDATE_DROPPABLE_IS_ENABLED",payload:e}),rP=e=>({type:"UPDATE_DROPPABLE_IS_COMBINE_ENABLED",payload:e}),rC=e=>({type:"MOVE",payload:e}),rO=e=>({type:"MOVE_BY_WINDOW_SCROLL",payload:e}),rR=()=>({type:"MOVE_UP",payload:null}),rS=()=>({type:"MOVE_DOWN",payload:null}),rj=()=>({type:"MOVE_RIGHT",payload:null}),rL=()=>({type:"MOVE_LEFT",payload:null}),rB=()=>({type:"FLUSH",payload:null}),rT=e=>({type:"DROP_ANIMATE",payload:e}),rG=e=>({type:"DROP_COMPLETE",payload:e}),r_=e=>({type:"DROP",payload:e}),rM=e=>({type:"DROP_PENDING",payload:e}),rk=()=>({type:"DROP_ANIMATION_FINISHED",payload:null});var r$=e=>({getState:t,dispatch:r})=>n=>l=>{if(!ry(l,"LIFT")){n(l);return}let{id:a,clientSelection:i,movementMode:o}=l.payload,s=t();"DROP_ANIMATING"===s.phase&&r(rG({completed:s.completed})),"IDLE"!==t().phase&&eN(),r(rB()),r(rv({draggableId:a,movementMode:o}));let{critical:d,dimensions:c,viewport:u}=e.startPublishing({draggableId:a,scrollOptions:{shouldPublishImmediately:"SNAP"===o}});r(rN({critical:d,dimensions:c,clientSelection:i,movementMode:o,viewport:u}))},rF=e=>()=>t=>r=>{ry(r,"INITIAL_PUBLISH")&&e.dragging(),ry(r,"DROP_ANIMATE")&&e.dropping(r.payload.completed.result.reason),(ry(r,"FLUSH")||ry(r,"DROP_COMPLETE"))&&e.resting(),t(r)};let rW={outOfTheWay:"cubic-bezier(0.2, 0, 0, 1)",drop:"cubic-bezier(.2,1,.1,1)"},rU={opacity:{drop:0,combining:.7},scale:{drop:.75}},rH={outOfTheWay:.2,minDropTime:.33,maxDropTime:.55},rV=`${rH.outOfTheWay}s ${rW.outOfTheWay}`,rZ={fluid:`opacity ${rV}`,snap:`transform ${rV}, opacity ${rV}`,drop:e=>{let t=`${e}s ${rW.drop}`;return`transform ${t}, opacity ${t}`},outOfTheWay:`transform ${rV}`,placeholder:`height ${rV}, width ${rV}, margin ${rV}`},rq=e=>eT(e,ej)?void 0:`translate(${e.x}px, ${e.y}px)`,rz={moveTo:rq,drop:(e,t)=>{let r=rq(e);return r?t?`${r} scale(${rU.scale.drop})`:r:void 0}},{minDropTime:rY,maxDropTime:rQ}=rH,rJ=rQ-rY;var rX=({current:e,destination:t,reason:r})=>{let n=eM(e,t);if(n<=0)return rY;if(n>=1500)return rQ;let l=rY+n/1500*rJ;return Number(("CANCEL"===r?.6*l:l).toFixed(2))},rK=({impact:e,draggable:t,dimensions:r,viewport:n,afterCritical:l})=>{let{draggables:a,droppables:i}=r,o=tJ(e),s=o?i[o]:null,d=i[t.descriptor.droppableId];return eB(rn({impact:e,draggable:t,draggables:a,afterCritical:l,droppable:s||d,viewport:n}),t.client.borderBox.center)},r0=({draggables:e,reason:t,lastImpact:r,home:n,viewport:l,onLiftImpact:a})=>r.at&&"DROP"===t?"REORDER"===r.at.type?{impact:r,didDropInsideDroppable:!0}:{impact:{...r,displaced:e5},didDropInsideDroppable:!0}:{impact:rr({draggables:e,impact:a,destination:n,viewport:l,forceShouldAnimate:!0}),didDropInsideDroppable:!1};let r1=({getState:e,dispatch:t})=>r=>n=>{if(!ry(n,"DROP")){r(n);return}let l=e(),a=n.payload.reason;if("COLLECTING"===l.phase){t(rM({reason:a}));return}if("IDLE"===l.phase)return;"DROP_PENDING"===l.phase&&l.isWaiting&&eN(),"DRAGGING"===l.phase||"DROP_PENDING"===l.phase||eN();let i=l.critical,o=l.dimensions,s=o.draggables[l.critical.draggable.id],{impact:d,didDropInsideDroppable:c}=r0({reason:a,lastImpact:l.impact,afterCritical:l.afterCritical,onLiftImpact:l.onLiftImpact,home:l.dimensions.droppables[l.critical.droppable.id],viewport:l.viewport,draggables:l.dimensions.draggables}),u=c?e1(d):null,p=c?e2(d):null,f={index:i.draggable.index,droppableId:i.droppable.id},g={draggableId:s.descriptor.id,type:s.descriptor.type,source:f,reason:a,mode:l.movementMode,destination:u,combine:p},m=rK({impact:d,draggable:s,dimensions:o,viewport:l.viewport,afterCritical:l.afterCritical}),h={critical:l.critical,afterCritical:l.afterCritical,result:g,impact:d};if(!(!eT(l.current.client.offset,m)||g.combine)){t(rG({completed:h}));return}let b=rX({current:l.current.client.offset,destination:m,reason:a});t(rT({newHomeClientOffset:m,dropDuration:b,completed:h}))};var r2=()=>({x:window.pageXOffset,y:window.pageYOffset});let r4=e=>ry(e,"DROP_COMPLETE")||ry(e,"DROP_ANIMATE")||ry(e,"FLUSH"),r6=e=>{let t=function({onWindowScroll:e}){let t=ef(function(){e(r2())}),r={eventName:"scroll",options:{passive:!0,capture:!1},fn:e=>{(e.target===window||e.target===window.document)&&t()}},n=ey;function l(){return n!==ey}return{start:function(){l()&&eN(),n=ev(window,[r])},stop:function(){l()||eN(),t.cancel(),n(),n=ey},isActive:l}}({onWindowScroll:t=>{e.dispatch(rO({newScroll:t}))}});return e=>r=>{!t.isActive()&&ry(r,"INITIAL_PUBLISH")&&t.start(),t.isActive()&&r4(r)&&t.stop(),e(r)}};var r3=e=>{let t=!1,r=!1,n=setTimeout(()=>{r=!0}),l=l=>{t||r||(t=!0,e(l),clearTimeout(n))};return l.wasCalled=()=>t,l},r7=()=>{let e=[],t=t=>{let r=e.findIndex(e=>e.timerId===t);-1!==r||eN();let[n]=e.splice(r,1);n.callback()};return{add:r=>{let n=setTimeout(()=>t(n));e.push({timerId:n,callback:r})},flush:()=>{if(!e.length)return;let t=[...e];e.length=0,t.forEach(e=>{clearTimeout(e.timerId),e.callback()})}}};let r5=(e,t)=>null==e&&null==t||null!=e&&null!=t&&e.droppableId===t.droppableId&&e.index===t.index,r9=(e,t)=>null==e&&null==t||null!=e&&null!=t&&e.draggableId===t.draggableId&&e.droppableId===t.droppableId,r8=(e,t)=>{if(e===t)return!0;let r=e.draggable.id===t.draggable.id&&e.draggable.droppableId===t.draggable.droppableId&&e.draggable.type===t.draggable.type&&e.draggable.index===t.draggable.index,n=e.droppable.id===t.droppable.id&&e.droppable.type===t.droppable.type;return r&&n},ne=(e,t)=>{rs(),t(),rd()},nt=(e,t)=>({draggableId:e.draggable.id,type:e.droppable.type,source:{droppableId:e.droppable.id,index:e.draggable.index},mode:t});function nr(e,t,r,n){if(!e){r(n(t));return}let l=r3(r);e(t,{announce:l}),l.wasCalled()||r(n(t))}var nn=(e,t)=>{let r=r7(),n=null,l=r=>{n||eN(),n=null,ne("onDragEnd",()=>nr(e().onDragEnd,r,t,eC.onDragEnd))};return{beforeCapture:(t,r)=>{n&&eN(),ne("onBeforeCapture",()=>{let n=e().onBeforeCapture;n&&n({draggableId:t,mode:r})})},beforeStart:(t,r)=>{n&&eN(),ne("onBeforeDragStart",()=>{let n=e().onBeforeDragStart;n&&n(nt(t,r))})},start:(l,a)=>{n&&eN();let i=nt(l,a);n={mode:a,lastCritical:l,lastLocation:i.source,lastCombine:null},r.add(()=>{ne("onDragStart",()=>nr(e().onDragStart,i,t,eC.onDragStart))})},update:(l,a)=>{let i=e1(a),o=e2(a);n||eN();let s=!r8(l,n.lastCritical);s&&(n.lastCritical=l);let d=!r5(n.lastLocation,i);d&&(n.lastLocation=i);let c=!r9(n.lastCombine,o);if(c&&(n.lastCombine=o),!s&&!d&&!c)return;let u={...nt(l,n.mode),combine:o,destination:i};r.add(()=>{ne("onDragUpdate",()=>nr(e().onDragUpdate,u,t,eC.onDragUpdate))})},flush:()=>{n||eN(),r.flush()},drop:l,abort:()=>{n&&l({...nt(n.lastCritical,n.mode),combine:null,destination:null,reason:"CANCEL"})}}},nl=(e,t)=>{let r=nn(e,t);return e=>t=>n=>{if(ry(n,"BEFORE_INITIAL_CAPTURE")){r.beforeCapture(n.payload.draggableId,n.payload.movementMode);return}if(ry(n,"INITIAL_PUBLISH")){let e=n.payload.critical;r.beforeStart(e,n.payload.movementMode),t(n),r.start(e,n.payload.movementMode);return}if(ry(n,"DROP_COMPLETE")){let e=n.payload.completed.result;r.flush(),t(n),r.drop(e);return}if(t(n),ry(n,"FLUSH")){r.abort();return}let l=e.getState();"DRAGGING"===l.phase&&r.update(l.critical,l.impact)}};let na=e=>t=>r=>{if(!ry(r,"DROP_ANIMATION_FINISHED")){t(r);return}let n=e.getState();"DROP_ANIMATING"!==n.phase&&eN(),e.dispatch(rG({completed:n.completed}))},ni=e=>{let t=null,r=null;return n=>l=>{if((ry(l,"FLUSH")||ry(l,"DROP_COMPLETE")||ry(l,"DROP_ANIMATION_FINISHED"))&&(r&&(cancelAnimationFrame(r),r=null),t&&(t(),t=null)),n(l),!ry(l,"DROP_ANIMATE"))return;let a={eventName:"scroll",options:{capture:!0,passive:!1,once:!0},fn:function(){"DROP_ANIMATING"===e.getState().phase&&e.dispatch(rk())}};r=requestAnimationFrame(()=>{r=null,t=ev(window,[a])})}};var no=e=>()=>t=>r=>{(ry(r,"DROP_COMPLETE")||ry(r,"FLUSH")||ry(r,"DROP_ANIMATE"))&&e.stopPublishing(),t(r)},ns=e=>{let t=!1;return()=>r=>n=>{if(ry(n,"INITIAL_PUBLISH")){t=!0,e.tryRecordFocus(n.payload.critical.draggable.id),r(n),e.tryRestoreFocusRecorded();return}if(r(n),t){if(ry(n,"FLUSH")){t=!1,e.tryRestoreFocusRecorded();return}if(ry(n,"DROP_COMPLETE")){t=!1;let r=n.payload.completed.result;r.combine&&e.tryShiftRecord(r.draggableId,r.combine.draggableId),e.tryRestoreFocusRecorded()}}}};let nd=e=>ry(e,"DROP_COMPLETE")||ry(e,"DROP_ANIMATE")||ry(e,"FLUSH");var nc=e=>t=>r=>n=>{if(nd(n)){e.stop(),r(n);return}if(ry(n,"INITIAL_PUBLISH")){r(n);let l=t.getState();"DRAGGING"!==l.phase&&eN(),e.start(l);return}r(n),e.scroll(t.getState())};let nu=e=>t=>r=>{if(t(r),!ry(r,"PUBLISH_WHILE_DRAGGING"))return;let n=e.getState();"DROP_PENDING"!==n.phase||n.isWaiting||e.dispatch(r_({reason:n.reason}))};var np=({dimensionMarshal:e,focusMarshal:t,styleMarshal:r,getResponders:n,announce:l,autoScroller:a})=>(function e(t,r,n){if("function"!=typeof t)throw Error(d(2));if("function"==typeof r&&"function"==typeof n||"function"==typeof n&&"function"==typeof arguments[3])throw Error(d(0));if("function"==typeof r&&void 0===n&&(n=r,r=void 0),void 0!==n){if("function"!=typeof n)throw Error(d(1));return n(e)(t,r)}let l=t,a=r,i=new Map,o=i,s=0,u=!1;function f(){o===i&&(o=new Map,i.forEach((e,t)=>{o.set(t,e)}))}function g(){if(u)throw Error(d(3));return a}function m(e){if("function"!=typeof e)throw Error(d(4));if(u)throw Error(d(5));let t=!0;f();let r=s++;return o.set(r,e),function(){if(t){if(u)throw Error(d(6));t=!1,f(),o.delete(r),i=null}}}function h(e){if(!function(e){if("object"!=typeof e||null===e)return!1;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t||null===Object.getPrototypeOf(e)}(e))throw Error(d(7));if(void 0===e.type)throw Error(d(8));if("string"!=typeof e.type)throw Error(d(17));if(u)throw Error(d(9));try{u=!0,a=l(a,e)}finally{u=!1}return(i=o).forEach(e=>{e()}),e}return h({type:p.INIT}),{dispatch:h,subscribe:m,getState:g,replaceReducer:function(e){if("function"!=typeof e)throw Error(d(10));l=e,h({type:p.REPLACE})},[c]:function(){return{subscribe(e){if("object"!=typeof e||null===e)throw Error(d(11));function t(){e.next&&e.next(g())}return t(),{unsubscribe:m(t)}},[c](){return this}}}}})(rx,m(function(...e){return t=>(r,n)=>{let l=t(r,n),a=()=>{throw Error(d(15))},i={getState:l.getState,dispatch:(e,...t)=>a(e,...t)};return a=m(...e.map(e=>e(i)))(l.dispatch),{...l,dispatch:a}}}(rF(r),no(e),r$(e),r1,na,ni,nu,nc(a),r6,ns(t),nl(n,l))));let nf=()=>({additions:{},removals:{},modified:{}});var ng=({scrollHeight:e,scrollWidth:t,height:r,width:n})=>{let l=eB({x:t,y:e},{x:n,y:r});return{x:Math.max(0,l.x),y:Math.max(0,l.y)}},nm=()=>{let e=document.documentElement;return e||eN(),e},nh=()=>{let e=nm();return ng({scrollHeight:e.scrollHeight,scrollWidth:e.scrollWidth,width:e.clientWidth,height:e.clientHeight})},nb=()=>{let e=r2(),t=nh(),r=e.y,n=e.x,l=nm();return{frame:en({top:r,left:n,right:n+l.clientWidth,bottom:r+l.clientHeight}),scroll:{initial:e,current:e,max:t,diff:{value:ej,displacement:ej}}}},nx=({critical:e,scrollOptions:t,registry:r})=>{rs();let n=nb(),l=n.scroll.current,a=e.droppable,i=r.droppable.getAllByType(a.type).map(e=>e.callbacks.getDimensionAndWatchScroll(l,t)),o={draggables:eJ(r.draggable.getAllByType(e.draggable.type).map(e=>e.getDimension(l))),droppables:eQ(i)};return rd(),{dimensions:o,critical:e,viewport:n}};function ny(e,t,r){return r.descriptor.id!==t.id&&r.descriptor.type===t.type&&"virtual"===e.droppable.getById(r.descriptor.droppableId).descriptor.mode}var nv=(e,t)=>{let r=null,n=function({registry:e,callbacks:t}){let r=nf(),n=null,l=()=>{n||(t.collectionStarting(),n=requestAnimationFrame(()=>{n=null,rs();let{additions:l,removals:a,modified:i}=r,o=Object.keys(l).map(t=>e.draggable.getById(t).getDimension(ej)).sort((e,t)=>e.descriptor.index-t.descriptor.index),s=Object.keys(i).map(t=>{let r=e.droppable.getById(t).callbacks.getScrollWhileDragging();return{droppableId:t,scroll:r}}),d={additions:o,removals:Object.keys(a),modified:s};r=nf(),rd(),t.publish(d)}))};return{add:e=>{let t=e.descriptor.id;r.additions[t]=e,r.modified[e.descriptor.droppableId]=!0,r.removals[t]&&delete r.removals[t],l()},remove:e=>{let t=e.descriptor;r.removals[t.id]=!0,r.modified[t.droppableId]=!0,r.additions[t.id]&&delete r.additions[t.id],l()},stop:()=>{n&&(cancelAnimationFrame(n),n=null,r=nf())}}}({callbacks:{publish:t.publishWhileDragging,collectionStarting:t.collectionStarting},registry:e}),l=t=>{r||eN();let l=r.critical.draggable;"ADDITION"===t.type&&ny(e,l,t.value)&&n.add(t.value),"REMOVAL"===t.type&&ny(e,l,t.value)&&n.remove(t.value)};return{updateDroppableIsEnabled:(n,l)=>{e.droppable.exists(n)||eN(),r&&t.updateDroppableIsEnabled({id:n,isEnabled:l})},updateDroppableIsCombineEnabled:(n,l)=>{r&&(e.droppable.exists(n)||eN(),t.updateDroppableIsCombineEnabled({id:n,isCombineEnabled:l}))},scrollDroppable:(t,n)=>{r&&e.droppable.getById(t).callbacks.scroll(n)},updateDroppableScroll:(n,l)=>{r&&(e.droppable.exists(n)||eN(),t.updateDroppableScroll({id:n,newScroll:l}))},startPublishing:t=>{r&&eN();let n=e.draggable.getById(t.draggableId),a=e.droppable.getById(n.descriptor.droppableId),i={draggable:n.descriptor,droppable:a.descriptor};return r={critical:i,unsubscribe:e.subscribe(l)},nx({critical:i,registry:e,scrollOptions:t.scrollOptions})},stopPublishing:()=>{if(!r)return;n.stop();let t=r.critical.droppable;e.droppable.getAllByType(t.type).forEach(e=>e.callbacks.dragStopped()),r.unsubscribe(),r=null}}},nI=(e,t)=>"IDLE"===e.phase||"DROP_ANIMATING"===e.phase&&e.completed.result.draggableId!==t&&"DROP"===e.completed.result.reason,nN=e=>{window.scrollBy(e.x,e.y)};let nD=eY(e=>eX(e).filter(e=>!!e.isEnabled&&!!e.frame)),nE=(e,t)=>nD(t).find(t=>(t.frame||eN(),t1(t.frame.pageMarginBox)(e)))||null;var nw=({center:e,destination:t,droppables:r})=>{if(t){let e=r[t];return e.frame?e:null}return nE(e,r)};let nA={startFromPercentage:.25,maxScrollAtPercentage:.05,maxPixelScroll:28,ease:e=>e**2,durationDampening:{stopDampeningAt:1200,accelerateAt:360},disabled:!1};var nP=(e,t,r=()=>nA)=>{let n=r();return{startScrollingFrom:e[t.size]*n.startFromPercentage,maxScrollValueAt:e[t.size]*n.maxScrollAtPercentage}},nC=({startOfRange:e,endOfRange:t,current:r})=>{let n=t-e;return 0===n?0:(r-e)/n},nO=(e,t,r=()=>nA)=>{let n=r();if(e>t.startScrollingFrom)return 0;if(e<=t.maxScrollValueAt)return n.maxPixelScroll;if(e===t.startScrollingFrom)return 1;let l=nC({startOfRange:t.maxScrollValueAt,endOfRange:t.startScrollingFrom,current:e});return Math.ceil(n.maxPixelScroll*n.ease(1-l))},nR=(e,t,r)=>{let n=r(),l=n.durationDampening.accelerateAt,a=n.durationDampening.stopDampeningAt,i=Date.now()-t;if(i>=a)return e;if(i<l)return 1;let o=nC({startOfRange:l,endOfRange:a,current:i});return Math.ceil(e*n.ease(o))},nS=({distanceToEdge:e,thresholds:t,dragStartTime:r,shouldUseTimeDampening:n,getAutoScrollerOptions:l})=>{let a=nO(e,t,l);return 0===a?0:n?Math.max(nR(a,r,l),1):a},nj=({container:e,distanceToEdges:t,dragStartTime:r,axis:n,shouldUseTimeDampening:l,getAutoScrollerOptions:a})=>{let i=nP(e,n,a);return t[n.end]<t[n.start]?nS({distanceToEdge:t[n.end],thresholds:i,dragStartTime:r,shouldUseTimeDampening:l,getAutoScrollerOptions:a}):-1*nS({distanceToEdge:t[n.start],thresholds:i,dragStartTime:r,shouldUseTimeDampening:l,getAutoScrollerOptions:a})},nL=({container:e,subject:t,proposedScroll:r})=>{let n=t.height>e.height,l=t.width>e.width;return l||n?l&&n?null:{x:l?0:r.x,y:n?0:r.y}:r};let nB=e$(e=>0===e?0:e);var nT=({dragStartTime:e,container:t,subject:r,center:n,shouldUseTimeDampening:l,getAutoScrollerOptions:a})=>{let i={top:n.y-t.top,right:t.right-n.x,bottom:t.bottom-n.y,left:n.x-t.left},o=nj({container:t,distanceToEdges:i,dragStartTime:e,axis:tr,shouldUseTimeDampening:l,getAutoScrollerOptions:a}),s=nB({x:nj({container:t,distanceToEdges:i,dragStartTime:e,axis:tn,shouldUseTimeDampening:l,getAutoScrollerOptions:a}),y:o});if(eT(s,ej))return null;let d=nL({container:t,subject:r,proposedScroll:s});return d?eT(d,ej)?null:d:null};let nG=e$(e=>0===e?0:e>0?1:-1),n_=(()=>{let e=(e,t)=>e<0?e:e>t?e-t:0;return({current:t,max:r,change:n})=>{let l=eL(t,n),a={x:e(l.x,r.x),y:e(l.y,r.y)};return eT(a,ej)?null:a}})(),nM=({max:e,current:t,change:r})=>{let n={x:Math.max(t.x,e.x),y:Math.max(t.y,e.y)},l=nG(r),a=n_({max:n,current:t,change:l});return!a||0!==l.x&&0===a.x||0!==l.y&&0===a.y},nk=(e,t)=>nM({current:e.scroll.current,max:e.scroll.max,change:t}),n$=(e,t)=>{if(!nk(e,t))return null;let r=e.scroll.max;return n_({current:e.scroll.current,max:r,change:t})},nF=(e,t)=>{let r=e.frame;return!!r&&nM({current:r.scroll.current,max:r.scroll.max,change:t})},nW=(e,t)=>{let r=e.frame;return r&&nF(e,t)?n_({current:r.scroll.current,max:r.scroll.max,change:t}):null};var nU=({viewport:e,subject:t,center:r,dragStartTime:n,shouldUseTimeDampening:l,getAutoScrollerOptions:a})=>{let i=nT({dragStartTime:n,container:e.frame,subject:t,center:r,shouldUseTimeDampening:l,getAutoScrollerOptions:a});return i&&nk(e,i)?i:null},nH=({droppable:e,subject:t,center:r,dragStartTime:n,shouldUseTimeDampening:l,getAutoScrollerOptions:a})=>{let i=e.frame;if(!i)return null;let o=nT({dragStartTime:n,container:i.pageMarginBox,subject:t,center:r,shouldUseTimeDampening:l,getAutoScrollerOptions:a});return o&&nF(e,o)?o:null},nV=({state:e,dragStartTime:t,shouldUseTimeDampening:r,scrollWindow:n,scrollDroppable:l,getAutoScrollerOptions:a})=>{let i=e.current.page.borderBoxCenter,o=e.dimensions.draggables[e.critical.draggable.id].page.marginBox;if(e.isWindowScrollAllowed){let l=nU({dragStartTime:t,viewport:e.viewport,subject:o,center:i,shouldUseTimeDampening:r,getAutoScrollerOptions:a});if(l){n(l);return}}let s=nw({center:i,destination:tJ(e.impact),droppables:e.dimensions.droppables});if(!s)return;let d=nH({dragStartTime:t,droppable:s,subject:o,center:i,shouldUseTimeDampening:r,getAutoScrollerOptions:a});d&&l(s.descriptor.id,d)},nZ=({scrollWindow:e,scrollDroppable:t,getAutoScrollerOptions:r=()=>nA})=>{let n=ef(e),l=ef(t),a=null,i=e=>{a||eN();let{shouldUseTimeDampening:t,dragStartTime:i}=a;nV({state:e,scrollWindow:n,scrollDroppable:l,dragStartTime:i,shouldUseTimeDampening:t,getAutoScrollerOptions:r})};return{start:e=>{rs(),a&&eN();let t=Date.now(),n=!1,l=()=>{n=!0};nV({state:e,dragStartTime:0,shouldUseTimeDampening:!1,scrollWindow:l,scrollDroppable:l,getAutoScrollerOptions:r}),a={dragStartTime:t,shouldUseTimeDampening:n},rd(),n&&i(e)},stop:()=>{a&&(n.cancel(),l.cancel(),a=null)},scroll:i}},nq=({move:e,scrollDroppable:t,scrollWindow:r})=>{let n=(t,r)=>{e({client:eL(t.current.client.selection,r)})},l=(e,r)=>{if(!nF(e,r))return r;let n=nW(e,r);if(!n)return t(e.descriptor.id,r),null;let l=eB(r,n);return t(e.descriptor.id,l),eB(r,l)},a=(e,t,n)=>{if(!e||!nk(t,n))return n;let l=n$(t,n);if(!l)return r(n),null;let a=eB(n,l);return r(a),eB(n,a)};return e=>{let t=e.scrollJumpRequest;if(!t)return;let r=tJ(e.impact);r||eN();let i=l(e.dimensions.droppables[r],t);if(!i)return;let o=e.viewport,s=a(e.isWindowScrollAllowed,o,i);s&&n(e,s)}},nz=({scrollDroppable:e,scrollWindow:t,move:r,getAutoScrollerOptions:n})=>{let l=nZ({scrollWindow:t,scrollDroppable:e,getAutoScrollerOptions:n}),a=nq({move:r,scrollWindow:t,scrollDroppable:e});return{scroll:e=>{if(!n().disabled&&"DRAGGING"===e.phase){if("FLUID"===e.movementMode){l.scroll(e);return}e.scrollJumpRequest&&a(e)}},start:l.start,stop:l.stop}};let nY="data-rfd",nQ=(()=>{let e=`${nY}-drag-handle`;return{base:e,draggableId:`${e}-draggable-id`,contextId:`${e}-context-id`}})(),nJ=(()=>{let e=`${nY}-draggable`;return{base:e,contextId:`${e}-context-id`,id:`${e}-id`}})(),nX=(()=>{let e=`${nY}-droppable`;return{base:e,contextId:`${e}-context-id`,id:`${e}-id`}})(),nK={contextId:`${nY}-scroll-container-context-id`},n0=e=>t=>`[${t}="${e}"]`,n1=(e,t)=>e.map(e=>{let r=e.styles[t];return r?`${e.selector} { ${r} }`:""}).join(" ");var n2=e=>{let t=n0(e),r=(()=>{let e=`
      cursor: -webkit-grab;
      cursor: grab;
    `;return{selector:t(nQ.contextId),styles:{always:`
          -webkit-touch-callout: none;
          -webkit-tap-highlight-color: rgba(0,0,0,0);
          touch-action: manipulation;
        `,resting:e,dragging:"pointer-events: none;",dropAnimating:e}}})(),n=[(()=>{let e=`
      transition: ${rZ.outOfTheWay};
    `;return{selector:t(nJ.contextId),styles:{dragging:e,dropAnimating:e,userCancel:e}}})(),r,{selector:t(nX.contextId),styles:{always:"overflow-anchor: none;"}},{selector:"body",styles:{dragging:`
        cursor: grabbing;
        cursor: -webkit-grabbing;
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        overflow-anchor: none;
      `}}];return{always:n1(n,"always"),resting:n1(n,"resting"),dragging:n1(n,"dragging"),dropAnimating:n1(n,"dropAnimating"),userCancel:n1(n,"userCancel")}};let n4=l.useEffect,n6=()=>{let e=document.querySelector("head");return e||eN(),e},n3=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.type="text/css",t};function n7(e,t){return Array.from(e.querySelectorAll(t))}var n5=e=>e&&e.ownerDocument&&e.ownerDocument.defaultView?e.ownerDocument.defaultView:window;function n9(e){return e instanceof n5(e).HTMLElement}function n8(e,t){let r=`[${nQ.contextId}="${e}"]`,n=n7(document,r);if(!n.length)return null;let l=n.find(e=>e.getAttribute(nQ.draggableId)===t);return l&&n9(l)?l:null}function le(){let e={draggables:{},droppables:{}},t=[];function r(e){t.length&&t.forEach(t=>t(e))}function n(t){return e.draggables[t]||null}function l(t){return e.droppables[t]||null}return{draggable:{register:t=>{e.draggables[t.descriptor.id]=t,r({type:"ADDITION",value:t})},update:(t,r)=>{let n=e.draggables[r.descriptor.id];n&&n.uniqueId===t.uniqueId&&(delete e.draggables[r.descriptor.id],e.draggables[t.descriptor.id]=t)},unregister:t=>{let l=t.descriptor.id,a=n(l);a&&t.uniqueId===a.uniqueId&&(delete e.draggables[l],e.droppables[t.descriptor.droppableId]&&r({type:"REMOVAL",value:t}))},getById:function(e){let t=n(e);return t||eN(),t},findById:n,exists:e=>!!n(e),getAllByType:t=>Object.values(e.draggables).filter(e=>e.descriptor.type===t)},droppable:{register:t=>{e.droppables[t.descriptor.id]=t},unregister:t=>{let r=l(t.descriptor.id);r&&t.uniqueId===r.uniqueId&&delete e.droppables[t.descriptor.id]},getById:function(e){let t=l(e);return t||eN(),t},findById:l,exists:e=>!!l(e),getAllByType:t=>Object.values(e.droppables).filter(e=>e.descriptor.type===t)},subscribe:function(e){return t.push(e),function(){let r=t.indexOf(e);-1!==r&&t.splice(r,1)}},clean:function(){e.draggables={},e.droppables={},t.length=0}}}var lt=a().createContext(null),lr=()=>{let e=document.body;return e||eN(),e};let ln={position:"absolute",width:"1px",height:"1px",margin:"-1px",border:"0",padding:"0",overflow:"hidden",clip:"rect(0 0 0 0)","clip-path":"inset(100%)"},ll=e=>`rfd-announcement-${e}`,la={separator:"::"};function li(e,t=la){let r=a().useId();return eR(()=>`${e}${t.separator}${r}`,[t.separator,e,r])}var lo=a().createContext(null);let ls=/(\d+)\.(\d+)\.(\d+)/,ld=e=>{let t=ls.exec(e);return null!=t||eN(),{major:Number(t[1]),minor:Number(t[2]),patch:Number(t[3]),raw:e}},lc=(e,t)=>t.major>e.major||!(t.major<e.major)&&(t.minor>e.minor||!(t.minor<e.minor)&&t.patch>=e.patch);var lu=(e,t)=>{if(lc(ld(e),ld(t)))return},lp=e=>{let t=e.doctype;t&&(t.name.toLowerCase(),t.publicId)};function lf(e,t){}function lg(e){let t=(0,l.useRef)(e);return(0,l.useEffect)(()=>{t.current=e}),t}function lm(e){return"IDLE"!==e.phase&&"DROP_ANIMATING"!==e.phase&&e.isDragging}let lh={13:!0,9:!0};var lb=e=>{lh[e.keyCode]&&e.preventDefault()};let lx=(()=>{let e="visibilitychange";return"undefined"==typeof document?e:[e,`ms${e}`,`webkit${e}`,`moz${e}`,`o${e}`].find(e=>`on${e}` in document)||e})(),ly={type:"IDLE"};function lv(){}let lI={34:!0,33:!0,36:!0,35:!0},lN={type:"IDLE"},lD=["input","button","textarea","select","option","optgroup","video","audio"];var lE=e=>en(e.getBoundingClientRect()).center;let lw=(()=>{let e="matches";return"undefined"==typeof document?e:[e,"msMatchesSelector","webkitMatchesSelector"].find(e=>e in Element.prototype)||e})();function lA(e){e.preventDefault()}function lP({expected:e,phase:t,isLockActive:r,shouldWarn:n}){return!!r()&&e===t}function lC({lockAPI:e,store:t,registry:r,draggableId:n}){if(e.isClaimed())return!1;let l=r.draggable.findById(n);return!!(l&&l.options.isEnabled&&nI(t.getState(),n))}let lO=[function(e){let t=(0,l.useRef)(ly),r=(0,l.useRef)(ey),n=eR(()=>({eventName:"mousedown",fn:function(t){if(t.defaultPrevented||0!==t.button||t.ctrlKey||t.metaKey||t.shiftKey||t.altKey)return;let n=e.findClosestDraggableId(t);if(!n)return;let l=e.tryGetLock(n,o,{sourceEvent:t});if(!l)return;t.preventDefault();let a={x:t.clientX,y:t.clientY};r.current(),c(l,a)}}),[e]),a=eR(()=>({eventName:"webkitmouseforcewillbegin",fn:t=>{if(t.defaultPrevented)return;let r=e.findClosestDraggableId(t);if(!r)return;let n=e.findOptionsForDraggable(r);n&&!n.shouldRespectForcePress&&e.canGetLock(r)&&t.preventDefault()}}),[e]),i=eS(function(){r.current=ev(window,[a,n],{passive:!1,capture:!0})},[a,n]),o=eS(()=>{"IDLE"!==t.current.type&&(t.current=ly,r.current(),i())},[i]),s=eS(()=>{let e=t.current;o(),"DRAGGING"===e.type&&e.actions.cancel({shouldBlockNextClick:!0}),"PENDING"===e.type&&e.actions.abort()},[o]),d=eS(function(){let e=function({cancel:e,completed:t,getPhase:r,setPhase:n}){return[{eventName:"mousemove",fn:e=>{var t;let{button:l,clientX:a,clientY:i}=e;if(0!==l)return;let o={x:a,y:i},s=r();if("DRAGGING"===s.type){e.preventDefault(),s.actions.move(o);return}"PENDING"!==s.type&&eN(),t=s.point,(Math.abs(o.x-t.x)>=5||Math.abs(o.y-t.y)>=5)&&(e.preventDefault(),n({type:"DRAGGING",actions:s.actions.fluidLift(o)}))}},{eventName:"mouseup",fn:n=>{let l=r();if("DRAGGING"!==l.type){e();return}n.preventDefault(),l.actions.drop({shouldBlockNextClick:!0}),t()}},{eventName:"mousedown",fn:t=>{"DRAGGING"===r().type&&t.preventDefault(),e()}},{eventName:"keydown",fn:t=>{if("PENDING"===r().type){e();return}if(27===t.keyCode){t.preventDefault(),e();return}lb(t)}},{eventName:"resize",fn:e},{eventName:"scroll",options:{passive:!0,capture:!1},fn:()=>{"PENDING"===r().type&&e()}},{eventName:"webkitmouseforcedown",fn:t=>{let n=r();if("IDLE"!==n.type||eN(),n.actions.shouldRespectForcePress()){e();return}t.preventDefault()}},{eventName:lx,fn:e}]}({cancel:s,completed:o,getPhase:()=>t.current,setPhase:e=>{t.current=e}});r.current=ev(window,e,{capture:!0,passive:!1})},[s,o]),c=eS(function(e,r){"IDLE"!==t.current.type&&eN(),t.current={type:"PENDING",point:r,actions:e},d()},[d]);n4(function(){return i(),function(){r.current()}},[i])},function(e){let t=(0,l.useRef)(lv),r=eR(()=>({eventName:"keydown",fn:function(r){if(r.defaultPrevented||32!==r.keyCode)return;let l=e.findClosestDraggableId(r);if(!l)return;let a=e.tryGetLock(l,s,{sourceEvent:r});if(!a)return;r.preventDefault();let i=!0,o=a.snapLift();function s(){i||eN(),i=!1,t.current(),n()}t.current(),t.current=ev(window,function(e,t){function r(){t(),e.cancel()}return[{eventName:"keydown",fn:n=>{if(27===n.keyCode){n.preventDefault(),r();return}if(32===n.keyCode){n.preventDefault(),t(),e.drop();return}if(40===n.keyCode){n.preventDefault(),e.moveDown();return}if(38===n.keyCode){n.preventDefault(),e.moveUp();return}if(39===n.keyCode){n.preventDefault(),e.moveRight();return}if(37===n.keyCode){n.preventDefault(),e.moveLeft();return}if(lI[n.keyCode]){n.preventDefault();return}lb(n)}},{eventName:"mousedown",fn:r},{eventName:"mouseup",fn:r},{eventName:"click",fn:r},{eventName:"touchstart",fn:r},{eventName:"resize",fn:r},{eventName:"wheel",fn:r,options:{passive:!0}},{eventName:lx,fn:r}]}(o,s),{capture:!0,passive:!1})}}),[e]),n=eS(function(){t.current=ev(window,[r],{passive:!1,capture:!0})},[r]);n4(function(){return n(),function(){t.current()}},[n])},function(e){let t=(0,l.useRef)(lN),r=(0,l.useRef)(ey),n=eS(function(){return t.current},[]),a=eS(function(e){t.current=e},[]),i=eR(()=>({eventName:"touchstart",fn:function(t){if(t.defaultPrevented)return;let n=e.findClosestDraggableId(t);if(!n)return;let l=e.tryGetLock(n,s,{sourceEvent:t});if(!l)return;let{clientX:a,clientY:i}=t.touches[0];r.current(),p(l,{x:a,y:i})}}),[e]),o=eS(function(){r.current=ev(window,[i],{capture:!0,passive:!1})},[i]),s=eS(()=>{let e=t.current;"IDLE"!==e.type&&("PENDING"===e.type&&clearTimeout(e.longPressTimerId),a(lN),r.current(),o())},[o,a]),d=eS(()=>{let e=t.current;s(),"DRAGGING"===e.type&&e.actions.cancel({shouldBlockNextClick:!0}),"PENDING"===e.type&&e.actions.abort()},[s]),c=eS(function(){let e={capture:!0,passive:!1},t={cancel:d,completed:s,getPhase:n},l=ev(window,function({cancel:e,completed:t,getPhase:r}){return[{eventName:"touchmove",options:{capture:!1},fn:t=>{let n=r();if("DRAGGING"!==n.type){e();return}n.hasMoved=!0;let{clientX:l,clientY:a}=t.touches[0];t.preventDefault(),n.actions.move({x:l,y:a})}},{eventName:"touchend",fn:n=>{let l=r();if("DRAGGING"!==l.type){e();return}n.preventDefault(),l.actions.drop({shouldBlockNextClick:!0}),t()}},{eventName:"touchcancel",fn:t=>{if("DRAGGING"!==r().type){e();return}t.preventDefault(),e()}},{eventName:"touchforcechange",fn:t=>{let n=r();"IDLE"!==n.type||eN();let l=t.touches[0];if(!l||!(l.force>=.15))return;let a=n.actions.shouldRespectForcePress();if("PENDING"===n.type){a&&e();return}if(a){if(n.hasMoved){t.preventDefault();return}e();return}t.preventDefault()}},{eventName:lx,fn:e}]}(t),e),a=ev(window,function({cancel:e,getPhase:t}){return[{eventName:"orientationchange",fn:e},{eventName:"resize",fn:e},{eventName:"contextmenu",fn:e=>{e.preventDefault()}},{eventName:"keydown",fn:r=>{if("DRAGGING"!==t().type){e();return}27===r.keyCode&&r.preventDefault(),e()}},{eventName:lx,fn:e}]}(t),e);r.current=function(){l(),a()}},[d,n,s]),u=eS(function(){let e=n();"PENDING"!==e.type&&eN(),a({type:"DRAGGING",actions:e.actions.fluidLift(e.point),hasMoved:!1})},[n,a]),p=eS(function(e,t){"IDLE"!==n().type&&eN(),a({type:"PENDING",point:t,actions:e,longPressTimerId:setTimeout(u,120)}),c()},[c,n,a,u]);n4(function(){return o(),function(){r.current();let e=n();"PENDING"===e.type&&(clearTimeout(e.longPressTimerId),a(lN))}},[n,o,a]),n4(function(){return ev(window,[{eventName:"touchmove",fn:()=>{},options:{capture:!1,passive:!1}}])},[])}],lR=e=>({onBeforeCapture:t=>{(0,o.flushSync)(()=>{e.onBeforeCapture&&e.onBeforeCapture(t)})},onBeforeDragStart:e.onBeforeDragStart,onDragStart:e.onDragStart,onDragEnd:e.onDragEnd,onDragUpdate:e.onDragUpdate}),lS=e=>({...nA,...e.autoScrollerOptions,durationDampening:{...nA.durationDampening,...e.autoScrollerOptions}});function lj(e){return e.current||eN(),e.current}function lL(e){let{contextId:t,setCallbacks:r,sensors:n,nonce:i,dragHandleUsageInstructions:o}=e,s=(0,l.useRef)(null),d=lg(e),c=eS(()=>lR(d.current),[d]),u=eS(()=>lS(d.current),[d]),p=function(e){let t=eR(()=>ll(e),[e]),r=(0,l.useRef)(null);return(0,l.useEffect)(function(){let e=document.createElement("div");return r.current=e,e.id=t,e.setAttribute("aria-live","assertive"),e.setAttribute("aria-atomic","true"),eg(e.style,ln),lr().appendChild(e),function(){setTimeout(function(){let t=lr();t.contains(e)&&t.removeChild(e),e===r.current&&(r.current=null)})}},[t]),eS(e=>{let t=r.current;if(t){t.textContent=e;return}},[])}(t),f=function({contextId:e,text:t}){let r=li("hidden-text",{separator:"-"}),n=eR(()=>(function({contextId:e,uniqueId:t}){return`rfd-hidden-text-${e}-${t}`})({contextId:e,uniqueId:r}),[r,e]);return(0,l.useEffect)(function(){let e=document.createElement("div");return e.id=n,e.textContent=t,e.style.display="none",lr().appendChild(e),function(){let t=lr();t.contains(e)&&t.removeChild(e)}},[n,t]),n}({contextId:t,text:o}),m=function(e,t){let r=eR(()=>n2(e),[e]),n=(0,l.useRef)(null),a=(0,l.useRef)(null),i=eS(eY(e=>{let t=a.current;t||eN(),t.textContent=e}),[]),o=eS(e=>{let t=n.current;t||eN(),t.textContent=e},[]);n4(()=>{(n.current||a.current)&&eN();let l=n3(t),s=n3(t);return n.current=l,a.current=s,l.setAttribute(`${nY}-always`,e),s.setAttribute(`${nY}-dynamic`,e),n6().appendChild(l),n6().appendChild(s),o(r.always),i(r.resting),()=>{let e=e=>{let t=e.current;t||eN(),n6().removeChild(t),e.current=null};e(n),e(a)}},[t,o,i,r.always,r.resting,e]);let s=eS(()=>i(r.dragging),[i,r.dragging]),d=eS(e=>{if("DROP"===e){i(r.dropAnimating);return}i(r.userCancel)},[i,r.dropAnimating,r.userCancel]),c=eS(()=>{a.current&&i(r.resting)},[i,r.resting]);return eR(()=>({dragging:s,dropping:d,resting:c}),[s,d,c])}(t,i),h=eS(e=>{lj(s).dispatch(e)},[]),b=eR(()=>g({publishWhileDragging:rD,updateDroppableScroll:rw,updateDroppableIsEnabled:rA,updateDroppableIsCombineEnabled:rP,collectionStarting:rE},h),[h]),x=function(){let e=eR(le,[]);return(0,l.useEffect)(()=>function(){e.clean()},[e]),e}(),y=eR(()=>nv(x,b),[x,b]),v=eR(()=>nz({scrollWindow:nN,scrollDroppable:y.scrollDroppable,getAutoScrollerOptions:u,...g({move:rC},h)}),[y.scrollDroppable,h,u]),I=function(e){let t=(0,l.useRef)({}),r=(0,l.useRef)(null),n=(0,l.useRef)(null),a=(0,l.useRef)(!1),i=eS(function(e,r){let n={id:e,focus:r};return t.current[e]=n,function(){let r=t.current;r[e]!==n&&delete r[e]}},[]),o=eS(function(t){let r=n8(e,t);r&&r!==document.activeElement&&r.focus()},[e]),s=eS(function(e,t){r.current===e&&(r.current=t)},[]),d=eS(function(){!n.current&&a.current&&(n.current=requestAnimationFrame(()=>{n.current=null;let e=r.current;e&&o(e)}))},[o]),c=eS(function(e){r.current=null;let t=document.activeElement;t&&t.getAttribute(nQ.draggableId)===e&&(r.current=e)},[]);return n4(()=>(a.current=!0,function(){a.current=!1;let e=n.current;e&&cancelAnimationFrame(e)}),[]),eR(()=>({register:i,tryRecordFocus:c,tryRestoreFocusRecorded:d,tryShiftRecord:s}),[i,c,d,s])}(t),N=eR(()=>np({announce:p,autoScroller:v,dimensionMarshal:y,focusMarshal:I,getResponders:c,styleMarshal:m}),[p,v,y,I,c,m]);s.current=N;let D=eS(()=>{let e=lj(s);"IDLE"!==e.getState().phase&&e.dispatch(rB())},[]),E=eS(()=>{let e=lj(s).getState();return"DROP_ANIMATING"===e.phase||"IDLE"!==e.phase&&e.isDragging},[]);r(eR(()=>({isDragging:E,tryAbort:D}),[E,D]));let w=eS(e=>nI(lj(s).getState(),e),[]),A=eS(()=>t0(lj(s).getState()),[]),P=eR(()=>({marshal:y,focus:I,contextId:t,canLift:w,isMovementAllowed:A,dragHandleUsageInstructionsId:f,registry:x}),[t,y,f,I,w,A,x]);return function({contextId:e,store:t,registry:r,customSensors:n,enableDefaultSensors:a}){let i=[...a?lO:[],...n||[]],o=(0,l.useState)(()=>(function(){let e=null;function t(){e||eN(),e=null}return{isClaimed:function(){return!!e},isActive:function(t){return t===e},claim:function(t){e&&eN();let r={abandon:t};return e=r,r},release:t,tryAbandon:function(){e&&(e.abandon(),t())}}})())[0],s=eS(function(e,t){lm(e)&&!lm(t)&&o.tryAbandon()},[o]);n4(function(){let e=t.getState();return t.subscribe(()=>{let r=t.getState();s(e,r),e=r})},[o,t,s]),n4(()=>o.tryAbandon,[o.tryAbandon]);let d=eS(e=>lC({lockAPI:o,registry:r,store:t,draggableId:e}),[o,r,t]),c=eS((n,l,a)=>(function({lockAPI:e,contextId:t,store:r,registry:n,draggableId:l,forceSensorStop:a,sourceEvent:i}){if(!lC({lockAPI:e,store:r,registry:n,draggableId:l}))return null;let o=n.draggable.getById(l),s=function(e,t){let r=`[${nJ.contextId}="${e}"]`,n=n7(document,r).find(e=>e.getAttribute(nJ.id)===t);return n&&n9(n)?n:null}(t,o.descriptor.id);if(!s||i&&!o.options.canDragInteractiveElements&&function(e,t){let r=t.target;return!!n9(r)&&function e(t,r){if(null==r)return!1;if(lD.includes(r.tagName.toLowerCase()))return!0;let n=r.getAttribute("contenteditable");return"true"===n||""===n||r!==t&&e(t,r.parentElement)}(e,r)}(s,i))return null;let d=e.claim(a||ey),c="PRE_DRAG";function u(){return o.options.shouldRespectForcePress}function p(){return e.isActive(d)}let f=(function(e,t){lP({expected:e,phase:c,isLockActive:p,shouldWarn:!0})&&r.dispatch(t())}).bind(null,"DRAGGING");function g(t){function n(){e.release(),c="COMPLETED"}function l(e,l={shouldBlockNextClick:!1}){t.cleanup(),l.shouldBlockNextClick&&setTimeout(ev(window,[{eventName:"click",fn:lA,options:{once:!0,passive:!1,capture:!0}}])),n(),r.dispatch(r_({reason:e}))}return"PRE_DRAG"!==c&&(n(),eN()),r.dispatch(rI(t.liftActionArgs)),c="DRAGGING",{isActive:()=>lP({expected:"DRAGGING",phase:c,isLockActive:p,shouldWarn:!1}),shouldRespectForcePress:u,drop:e=>l("DROP",e),cancel:e=>l("CANCEL",e),...t.actions}}return{isActive:()=>lP({expected:"PRE_DRAG",phase:c,isLockActive:p,shouldWarn:!1}),shouldRespectForcePress:u,fluidLift:function(e){let t=ef(e=>{f(()=>rC({client:e}))});return{...g({liftActionArgs:{id:l,clientSelection:e,movementMode:"FLUID"},cleanup:()=>t.cancel(),actions:{move:t}}),move:t}},snapLift:function(){return g({liftActionArgs:{id:l,clientSelection:lE(s),movementMode:"SNAP"},cleanup:ey,actions:{moveUp:()=>f(rR),moveRight:()=>f(rj),moveDown:()=>f(rS),moveLeft:()=>f(rL)}})},abort:function(){lP({expected:"PRE_DRAG",phase:c,isLockActive:p,shouldWarn:!0})&&e.release()}}})({lockAPI:o,registry:r,contextId:e,store:t,draggableId:n,forceSensorStop:l||null,sourceEvent:a&&a.sourceEvent?a.sourceEvent:null}),[e,o,r,t]),u=eS(t=>(function(e,t){let r=function(e,t){let r=t.target;if(!(r instanceof n5(r).Element))return null;let n=`[${nQ.contextId}="${e}"]`,l=r.closest?r.closest(n):function e(t,r){return null==t?null:t[lw](r)?t:e(t.parentElement,r)}(r,n);return l&&n9(l)?l:null}(e,t);return r?r.getAttribute(nQ.draggableId):null})(e,t),[e]),p=eS(e=>{let t=r.draggable.findById(e);return t?t.options:null},[r.draggable]),f=eS(function(){o.isClaimed()&&(o.tryAbandon(),"IDLE"!==t.getState().phase&&t.dispatch(rB()))},[o,t]),g=eS(()=>o.isClaimed(),[o]),m=eR(()=>({canGetLock:d,tryGetLock:c,findClosestDraggableId:u,findOptionsForDraggable:p,tryReleaseLock:f,isLockClaimed:g}),[d,c,u,p,f,g]);for(let e=0;e<i.length;e++)i[e](m)}({contextId:t,store:N,registry:x,customSensors:n||null,enableDefaultSensors:!1!==e.enableDefaultSensors}),(0,l.useEffect)(()=>D,[D]),a().createElement(lo.Provider,{value:P},a().createElement(er,{context:lt,store:N},e.children))}function lB(e){let t=a().useId(),r=e.dragHandleUsageInstructions||eC.dragHandleUsageInstructions;return a().createElement(eD,null,n=>a().createElement(lL,{nonce:e.nonce,contextId:t,setCallbacks:n,dragHandleUsageInstructions:r,enableDefaultSensors:e.enableDefaultSensors,sensors:e.sensors,onBeforeCapture:e.onBeforeCapture,onBeforeDragStart:e.onBeforeDragStart,onDragStart:e.onDragStart,onDragUpdate:e.onDragUpdate,onDragEnd:e.onDragEnd,autoScrollerOptions:e.autoScrollerOptions},e.children))}let lT={dragging:5e3,dropAnimating:4500},lG=(e,t)=>t?rZ.drop(t.duration):e?rZ.snap:rZ.fluid,l_=(e,t)=>{if(e)return t?rU.opacity.drop:rU.opacity.combining},lM=e=>null!=e.forceShouldAnimate?e.forceShouldAnimate:"SNAP"===e.mode;var lk=a().createContext(null);function l$(e){e&&n9(e)||eN()}function lF(e){let t=(0,l.useContext)(e);return t||eN(),t}function lW(e){e.preventDefault()}var lU=(e,t)=>e===t,lH=e=>{let{combine:t,destination:r}=e;return r?r.droppableId:t?t.droppableId:null};let lV=e=>e.combine?e.combine.draggableId:null,lZ=e=>e.at&&"COMBINE"===e.at.type?e.at.combine.draggableId:null;function lq(e=null){return{isDragging:!1,isDropAnimating:!1,isClone:!1,dropAnimation:null,mode:null,draggingOver:null,combineTargetFor:e,combineWith:null}}let lz={mapped:{type:"SECONDARY",offset:ej,combineTargetFor:null,shouldAnimateDisplacement:!0,snapshot:lq(null)}},lY=et(()=>{let e=function(){let e=eY((e,t)=>({x:e,y:t})),t=eY((e,t,r=null,n=null,l=null)=>({isDragging:!0,isClone:t,isDropAnimating:!!l,dropAnimation:l,mode:e,draggingOver:r,combineWith:n,combineTargetFor:null})),r=eY((e,r,n,l,a=null,i=null,o=null)=>({mapped:{type:"DRAGGING",dropping:null,draggingOver:a,combineWith:i,mode:r,offset:e,dimension:n,forceShouldAnimate:o,snapshot:t(r,l,a,i,null)}}));return(n,l)=>{if(lm(n)){if(n.critical.draggable.id!==l.draggableId)return null;let t=n.current.client.offset,a=n.dimensions.draggables[l.draggableId],i=tJ(n.impact),o=lZ(n.impact),s=n.forceShouldAnimate;return r(e(t.x,t.y),n.movementMode,a,l.isClone,i,o,s)}if("DROP_ANIMATING"===n.phase){let e=n.completed;if(e.result.draggableId!==l.draggableId)return null;let r=l.isClone,a=n.dimensions.draggables[l.draggableId],i=e.result,o=i.mode,s=lH(i),d=lV(i),c={duration:n.dropDuration,curve:rW.drop,moveTo:n.newHomeClientOffset,opacity:d?rU.opacity.drop:null,scale:d?rU.scale.drop:null};return{mapped:{type:"DRAGGING",offset:n.newHomeClientOffset,dimension:a,dropping:c,draggingOver:s,combineWith:d,mode:o,forceShouldAnimate:null,snapshot:t(o,r,s,d,c)}}}return null}}(),t=function(){let e=eY((e,t)=>({x:e,y:t})),t=eY(lq),r=eY((e,r=null,n)=>({mapped:{type:"SECONDARY",offset:e,combineTargetFor:r,shouldAnimateDisplacement:n,snapshot:t(r)}})),n=e=>e?r(ej,e,!0):null,l=(t,l,a,i)=>{let o=a.displaced.visible[t],s=!!(i.inVirtualList&&i.effected[t]),d=e2(a),c=d&&d.draggableId===t?l:null;if(!o){if(!s)return n(c);if(a.displaced.invisible[t])return null;let l=eG(i.displacedBy.point);return r(e(l.x,l.y),c,!0)}if(s)return n(c);let u=a.displacedBy.point;return r(e(u.x,u.y),c,o.shouldAnimate)};return(e,t)=>{if(lm(e))return e.critical.draggable.id===t.draggableId?null:l(t.draggableId,e.critical.draggable.id,e.impact,e.afterCritical);if("DROP_ANIMATING"===e.phase){let r=e.completed;return r.result.draggableId===t.draggableId?null:l(t.draggableId,r.result.draggableId,r.impact,r.afterCritical)}return null}}();return(r,n)=>e(r,n)||t(r,n)||lz},{dropAnimationFinished:rk},null,{context:lt,areStatePropsEqual:lU})(e=>{let t=(0,l.useRef)(null),r=eS((e=null)=>{t.current=e},[]),n=eS(()=>t.current,[]),{contextId:i,dragHandleUsageInstructionsId:s,registry:d}=lF(lo),{type:c,droppableId:u}=lF(lk),p=eR(()=>({id:e.draggableId,index:e.index,type:c,droppableId:u}),[e.draggableId,e.index,c,u]),{children:f,draggableId:g,isEnabled:m,shouldRespectForcePress:h,canDragInteractiveElements:b,isClone:x,mapped:y,dropAnimationFinished:v}=e;x||function(e){let t=li("draggable"),{descriptor:r,registry:n,getDraggableRef:a,canDragInteractiveElements:i,shouldRespectForcePress:o,isEnabled:s}=e,d=eR(()=>({canDragInteractiveElements:i,shouldRespectForcePress:o,isEnabled:s}),[i,s,o]),c=eS(e=>{let t=a();return t||eN(),function(e,t,r=ej){let n=window.getComputedStyle(t),l=eu(t.getBoundingClientRect(),n),a=ec(l,r);return{descriptor:e,placeholder:{client:l,tagName:t.tagName.toLowerCase(),display:n.display},displaceBy:{x:l.marginBox.width,y:l.marginBox.height},client:l,page:a}}(r,t,e)},[r,a]),u=eR(()=>({uniqueId:t,descriptor:r,options:d,getDimension:c}),[r,c,d,t]),p=(0,l.useRef)(u),f=(0,l.useRef)(!0);n4(()=>(n.draggable.register(p.current),()=>n.draggable.unregister(p.current)),[n.draggable]),n4(()=>{if(f.current){f.current=!1;return}let e=p.current;p.current=u,n.draggable.update(u,e)},[u,n.draggable])}(eR(()=>({descriptor:p,registry:d,getDraggableRef:n,canDragInteractiveElements:b,shouldRespectForcePress:h,isEnabled:m}),[p,d,n,b,h,m]));let I=eR(()=>m?{tabIndex:0,role:"button","aria-describedby":s,"data-rfd-drag-handle-draggable-id":g,"data-rfd-drag-handle-context-id":i,draggable:!1,onDragStart:lW}:null,[i,s,g,m]),N=eS(e=>{"DRAGGING"===y.type&&y.dropping&&"transform"===e.propertyName&&(0,o.flushSync)(v)},[v,y]),D=eR(()=>({innerRef:r,draggableProps:{"data-rfd-draggable-context-id":i,"data-rfd-draggable-id":g,style:function(e){return"DRAGGING"===e.type?function(e){let t=e.dimension.client,{offset:r,combineWith:n,dropping:l}=e,a=!!n,i=lM(e),o=!!l,s=o?rz.drop(r,a):rz.moveTo(r);return{position:"fixed",top:t.marginBox.top,left:t.marginBox.left,boxSizing:"border-box",width:t.borderBox.width,height:t.borderBox.height,transition:lG(i,l),transform:s,opacity:l_(a,o),zIndex:o?lT.dropAnimating:lT.dragging,pointerEvents:"none"}}(e):{transform:rz.moveTo(e.offset),transition:e.shouldAnimateDisplacement?void 0:"none"}}(y),onTransitionEnd:"DRAGGING"===y.type&&y.dropping?N:void 0},dragHandleProps:I}),[i,I,g,y,N,r]),E=eR(()=>({draggableId:p.id,type:p.type,source:{index:p.index,droppableId:p.droppableId}}),[p.droppableId,p.id,p.index,p.type]);return a().createElement(a().Fragment,null,f(D,y.snapshot,E))});function lQ(e){return lF(lk).isUsingCloneFor!==e.draggableId||e.isClone?a().createElement(lY,e):null}function lJ(e){let t="boolean"!=typeof e.isDragDisabled||!e.isDragDisabled,r=!!e.disableInteractiveElementBlocking,n=!!e.shouldRespectForcePress;return a().createElement(lQ,eg({},e,{isClone:!1,isEnabled:t,canDragInteractiveElements:r,shouldRespectForcePress:n}))}let lX=e=>t=>e===t,lK=lX("scroll"),l0=lX("auto");lX("visible");let l1=(e,t)=>t(e.overflowX)||t(e.overflowY),l2=e=>{let t=window.getComputedStyle(e),r={overflowX:t.overflowX,overflowY:t.overflowY};return l1(r,lK)||l1(r,l0)},l4=()=>!1,l6=e=>null==e?null:e===document.body?l4()?e:null:e===document.documentElement?null:l2(e)?e:l6(e.parentElement);var l3=e=>({x:e.scrollLeft,y:e.scrollTop});let l7=e=>!!e&&("fixed"===window.getComputedStyle(e).position||l7(e.parentElement));var l5=e=>({closestScrollable:l6(e),isFixedOnPage:l7(e)}),l9=({descriptor:e,isEnabled:t,isCombineEnabled:r,isFixedOnPage:n,direction:l,client:a,page:i,closest:o})=>{let s=(()=>{if(!o)return null;let{scrollSize:e,client:t}=o,r=ng({scrollHeight:e.scrollHeight,scrollWidth:e.scrollWidth,height:t.paddingBox.height,width:t.paddingBox.width});return{pageMarginBox:o.page.marginBox,frameClient:t,scrollSize:e,shouldClipSubject:o.shouldClipSubject,scroll:{initial:o.scroll,current:o.scroll,max:r,diff:{value:ej,displacement:ej}}}})(),d="vertical"===l?tr:tn,c=eq({page:i,withPlaceholder:null,axis:d,frame:s});return{descriptor:e,isCombineEnabled:r,isFixedOnPage:n,axis:d,isEnabled:t,client:a,page:i,frame:s,subject:c}};let l8=(e,t)=>{let r=ep(e);if(!t||e!==t)return r;let n=r.paddingBox.top-t.scrollTop,l=r.paddingBox.left-t.scrollLeft,a=n+t.scrollHeight;return eo({borderBox:el({top:n,right:l+t.scrollWidth,bottom:a,left:l},r.border),margin:r.margin,border:r.border,padding:r.padding})};var ae=({ref:e,descriptor:t,env:r,windowScroll:n,direction:l,isDropDisabled:a,isCombineEnabled:i,shouldClipSubject:o})=>{let s=r.closestScrollable,d=l8(e,s),c=ec(d,n),u=(()=>{if(!s)return null;let e=ep(s),t={scrollHeight:s.scrollHeight,scrollWidth:s.scrollWidth};return{client:e,page:ec(e,n),scroll:l3(s),scrollSize:t,shouldClipSubject:o}})();return l9({descriptor:t,isEnabled:!a,isCombineEnabled:i,isFixedOnPage:r.isFixedOnPage,direction:l,client:d,page:c,closest:u})};let at={passive:!1},ar={passive:!0};var an=e=>e.shouldPublishImmediately?at:ar;let al=e=>e&&e.env.closestScrollable||null;function aa(){}let ai={width:0,height:0,margin:{top:0,right:0,bottom:0,left:0}},ao=({isAnimatingOpenOnMount:e,placeholder:t,animate:r})=>e||"close"===r?ai:{height:t.client.borderBox.height,width:t.client.borderBox.width,margin:t.client.margin},as=({isAnimatingOpenOnMount:e,placeholder:t,animate:r})=>{let n=ao({isAnimatingOpenOnMount:e,placeholder:t,animate:r});return{display:t.display,boxSizing:"border-box",width:n.width,height:n.height,marginTop:n.margin.top,marginRight:n.margin.right,marginBottom:n.margin.bottom,marginLeft:n.margin.left,flexShrink:"0",flexGrow:"0",pointerEvents:"none",transition:"none"!==r?rZ.placeholder:null}};var ad=a().memo(e=>{let t=(0,l.useRef)(null),r=eS(()=>{t.current&&(clearTimeout(t.current),t.current=null)},[]),{animate:n,onTransitionEnd:i,onClose:o,contextId:s}=e,[d,c]=(0,l.useState)("open"===e.animate);(0,l.useEffect)(()=>d?"open"!==n?(r(),c(!1),aa):t.current?aa:(t.current=setTimeout(()=>{t.current=null,c(!1)}),r):aa,[n,d,r]);let u=eS(e=>{"height"===e.propertyName&&(i(),"close"===n&&o())},[n,o,i]),p=as({isAnimatingOpenOnMount:d,animate:e.animate,placeholder:e.placeholder});return a().createElement(e.placeholder.tagName,{style:p,"data-rfd-placeholder-context-id":s,onTransitionEnd:u,ref:e.innerRef})});function ac(e){return"boolean"==typeof e}function au(e,t){t.forEach(t=>t(e))}let ap=[function({props:e}){e.droppableId||eN(),"string"!=typeof e.droppableId&&eN()},function({props:e}){ac(e.isDropDisabled)||eN(),ac(e.isCombineEnabled)||eN(),ac(e.ignoreContainerClipping)||eN()},function({getDroppableRef:e}){l$(e())}],af=[function({props:e,getPlaceholderRef:t}){if(!e.placeholder||t())return}],ag=[function({props:e}){e.renderClone||eN()},function({getPlaceholderRef:e}){e()&&eN()}];class am extends a().PureComponent{constructor(...e){super(...e),this.state={isVisible:!!this.props.on,data:this.props.on,animate:this.props.shouldAnimate&&this.props.on?"open":"none"},this.onClose=()=>{"close"===this.state.animate&&this.setState({isVisible:!1})}}static getDerivedStateFromProps(e,t){return e.shouldAnimate?e.on?{isVisible:!0,data:e.on,animate:"open"}:t.isVisible?{isVisible:!0,data:t.data,animate:"close"}:{isVisible:!1,animate:"close",data:null}:{isVisible:!!e.on,data:e.on,animate:"none"}}render(){if(!this.state.isVisible)return null;let e={onClose:this.onClose,data:this.state.data,animate:this.state.animate};return this.props.children(e)}}let ah={mode:"standard",type:"DEFAULT",direction:"vertical",isDropDisabled:!1,isCombineEnabled:!1,ignoreContainerClipping:!1,renderClone:null,getContainerForClone:function(){return document.body||eN(),document.body}},ab=e=>{let t,r={...e};for(t in ah)void 0===e[t]&&(r={...r,[t]:ah[t]});return r},ax=(e,t)=>e===t.droppable.type,ay=(e,t)=>t.draggables[e.draggable.id],av=et(()=>{let e={placeholder:null,shouldAnimatePlaceholder:!0,snapshot:{isDraggingOver:!1,draggingOverWith:null,draggingFromThisWith:null,isUsingPlaceholder:!1},useClone:null},t={...e,shouldAnimatePlaceholder:!1},r=eY(e=>({draggableId:e.id,type:e.type,source:{index:e.index,droppableId:e.droppableId}})),n=eY((n,l,a,i,o,s)=>{let d=o.descriptor.id;if(o.descriptor.droppableId===n){let e=s?{render:s,dragging:r(o.descriptor)}:null;return{placeholder:o.placeholder,shouldAnimatePlaceholder:!1,snapshot:{isDraggingOver:a,draggingOverWith:a?d:null,draggingFromThisWith:d,isUsingPlaceholder:!0},useClone:e}}return l?i?{placeholder:o.placeholder,shouldAnimatePlaceholder:!0,snapshot:{isDraggingOver:a,draggingOverWith:d,draggingFromThisWith:null,isUsingPlaceholder:!0},useClone:null}:e:t});return(r,l)=>{let a=ab(l),i=a.droppableId,o=a.type,s=!a.isDropDisabled,d=a.renderClone;if(lm(r)){let e=r.critical;if(!ax(o,e))return t;let l=ay(e,r.dimensions),a=tJ(r.impact)===i;return n(i,s,a,a,l,d)}if("DROP_ANIMATING"===r.phase){let e=r.completed;if(!ax(o,e.critical))return t;let l=ay(e.critical,r.dimensions);return n(i,s,lH(e.result)===i,tJ(e.impact)===i,l,d)}if("IDLE"===r.phase&&r.completed&&!r.shouldFlush){let n=r.completed;if(!ax(o,n.critical))return t;let l=tJ(n.impact)===i,a=!!(n.impact.at&&"COMBINE"===n.impact.at.type),s=n.critical.droppable.id===i;if(l)return a?e:t;if(s)return e}return t}},{updateViewportMaxScroll:e=>({type:"UPDATE_VIEWPORT_MAX_SCROLL",payload:e})},(e,t,r)=>({...ab(r),...e,...t}),{context:lt,areStatePropsEqual:lU})(e=>{let t=(0,l.useContext)(lo);t||eN();let{contextId:r,isMovementAllowed:n}=t,i=(0,l.useRef)(null),o=(0,l.useRef)(null),{children:d,droppableId:c,type:u,mode:p,direction:f,ignoreContainerClipping:g,isDropDisabled:m,isCombineEnabled:h,snapshot:b,useClone:x,updateViewportMaxScroll:y,getContainerForClone:v}=e,I=eS(()=>i.current,[]),N=eS((e=null)=>{i.current=e},[]),D=eS(()=>o.current,[]),E=eS((e=null)=>{o.current=e},[]),w=eS(()=>{n()&&y({maxScroll:nh()})},[n,y]);!function(e){let t=(0,l.useRef)(null),r=lF(lo),n=li("droppable"),{registry:a,marshal:i}=r,o=lg(e),s=eR(()=>({id:e.droppableId,type:e.type,mode:e.mode}),[e.droppableId,e.mode,e.type]),d=(0,l.useRef)(s),c=eR(()=>eY((e,r)=>{t.current||eN(),i.updateDroppableScroll(s.id,{x:e,y:r})}),[s.id,i]),u=eS(()=>{let e=t.current;return e&&e.env.closestScrollable?l3(e.env.closestScrollable):ej},[]),p=eS(()=>{let e=u();c(e.x,e.y)},[u,c]),f=eR(()=>ef(p),[p]),g=eS(()=>{let e=t.current,r=al(e);if(e&&r||eN(),e.scrollOptions.shouldPublishImmediately){p();return}f()},[f,p]),m=eS((e,n)=>{t.current&&eN();let l=o.current,a=l.getDroppableRef();a||eN();let i=l5(a),d={ref:a,descriptor:s,env:i,scrollOptions:n};t.current=d;let c=ae({ref:a,descriptor:s,env:i,windowScroll:e,direction:l.direction,isDropDisabled:l.isDropDisabled,isCombineEnabled:l.isCombineEnabled,shouldClipSubject:!l.ignoreContainerClipping}),u=i.closestScrollable;return u&&(u.setAttribute(nK.contextId,r.contextId),u.addEventListener("scroll",g,an(d.scrollOptions))),c},[r.contextId,s,g,o]),h=eS(()=>{let e=t.current,r=al(e);return e&&r||eN(),l3(r)},[]),b=eS(()=>{let e=t.current;e||eN();let r=al(e);t.current=null,r&&(f.cancel(),r.removeAttribute(nK.contextId),r.removeEventListener("scroll",g,an(e.scrollOptions)))},[g,f]),x=eS(e=>{let r=t.current;r||eN();let n=al(r);n||eN(),n.scrollTop+=e.y,n.scrollLeft+=e.x},[]),y=eR(()=>({getDimensionAndWatchScroll:m,getScrollWhileDragging:h,dragStopped:b,scroll:x}),[b,m,h,x]),v=eR(()=>({uniqueId:n,descriptor:s,callbacks:y}),[y,s,n]);n4(()=>(d.current=v.descriptor,a.droppable.register(v),()=>{t.current&&b(),a.droppable.unregister(v)}),[y,s,b,v,i,a.droppable]),n4(()=>{t.current&&i.updateDroppableIsEnabled(d.current.id,!e.isDropDisabled)},[e.isDropDisabled,i]),n4(()=>{t.current&&i.updateDroppableIsCombineEnabled(d.current.id,e.isCombineEnabled)},[e.isCombineEnabled,i])}({droppableId:c,type:u,mode:p,direction:f,isDropDisabled:m,isCombineEnabled:h,ignoreContainerClipping:g,getDroppableRef:I});let A=eR(()=>a().createElement(am,{on:e.placeholder,shouldAnimate:e.shouldAnimatePlaceholder},({onClose:e,data:t,animate:n})=>a().createElement(ad,{placeholder:t,onClose:e,innerRef:E,animate:n,contextId:r,onTransitionEnd:w})),[r,w,e.placeholder,e.shouldAnimatePlaceholder,E]),P=eR(()=>({innerRef:N,placeholder:A,droppableProps:{"data-rfd-droppable-id":c,"data-rfd-droppable-context-id":r}}),[r,c,A,N]),C=x?x.dragging.draggableId:null,O=eR(()=>({droppableId:c,type:u,isUsingCloneFor:C}),[c,C,u]);return a().createElement(lk.Provider,{value:O},d(P,b),function(){if(!x)return null;let{dragging:e,render:t}=x,r=a().createElement(lQ,{draggableId:e.draggableId,index:e.source.index,isClone:!0,isEnabled:!0,shouldRespectForcePress:!1,canDragInteractiveElements:!0},(r,n)=>t(r,n,e));return s().createPortal(r,v())}())});var aI=r(61351),aN=r(16212),aD=r(69436),aE=r(92549),aw=r(17470),aA=r(51838),aP=r(46064),aC=r(28765),aO=r(53148),aR=r(71206),aS=r(40626),aj=r(17910),aL=r(48411),aB=r(18822),aT=r(44669),aG=r(20783),a_=r.n(aG);let aM=[{id:"new",title:"New Leads",status:"NEW",color:"bg-gray-100 border-gray-300"},{id:"contacted",title:"Contacted",status:"CONTACTED",color:"bg-blue-100 border-blue-300"},{id:"qualified",title:"Qualified",status:"QUALIFIED",color:"bg-green-100 border-green-300"},{id:"proposal",title:"Proposal",status:"PROPOSAL",color:"bg-yellow-100 border-yellow-300"},{id:"negotiation",title:"Negotiation",status:"NEGOTIATION",color:"bg-orange-100 border-orange-300"},{id:"won",title:"Closed Won",status:"CLOSED_WON",color:"bg-green-100 border-green-500"},{id:"lost",title:"Closed Lost",status:"CLOSED_LOST",color:"bg-red-100 border-red-300"}];function ak(){let{data:e}=(0,i.useSession)(),[t,r]=(0,l.useState)([]),[a,o]=(0,l.useState)(!0),[s,d]=(0,l.useState)(""),[c,u]=(0,l.useState)("all"),[p,f]=(0,l.useState)("all"),[g,m]=(0,l.useState)("all"),h=async()=>{try{o(!0);let e=new URLSearchParams({limit:"1000",...s&&{search:s},..."all"!==c&&{source:c},..."all"!==p&&{priority:p},..."all"!==g&&{assignedTo:g}}),t=await fetch(`/api/leads?${e}`);if(!t.ok)throw Error("Failed to fetch leads");let n=await t.json(),l=aM.map(e=>({...e,leads:n.leads.filter(t=>t.status===e.status),count:n.leads.filter(t=>t.status===e.status).length}));r(l)}catch(e){aT.toast.error("Failed to load pipeline data"),console.error("Error fetching leads:",e)}finally{o(!1)}};(0,l.useEffect)(()=>{e?.user?.companyId&&h()},[e?.user?.companyId,s,c,p,g]);let b=async e=>{let{destination:n,source:l,draggableId:a}=e;if(!n||n.droppableId===l.droppableId&&n.index===l.index)return;let i=t.find(e=>e.id===l.droppableId),o=t.find(e=>e.id===n.droppableId);if(!i||!o)return;let s=i.leads.find(e=>e.id===a);if(s)try{if(!(await fetch(`/api/leads/${a}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:o.status})})).ok)throw Error("Failed to update lead status");let e=t.map(e=>{if(e.id===l.droppableId)return{...e,leads:e.leads.filter(e=>e.id!==a),count:e.count-1};if(e.id===n.droppableId){let t={...s,status:o.status},r=[...e.leads];return r.splice(n.index,0,t),{...e,leads:r,count:e.count+1}}return e});r(e),aT.toast.success(`Lead moved to ${o.title}`)}catch(e){aT.toast.error("Failed to update lead status"),console.error("Error updating lead:",e)}};return a?n.jsx("div",{className:"flex items-center justify-center h-64",children:(0,n.jsxs)("div",{className:"text-center",children:[n.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),n.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Loading Pipeline"}),n.jsx("p",{className:"text-gray-500",children:"Please wait while we fetch your leads..."})]})}):(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[n.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Lead Pipeline"}),n.jsx("p",{className:"text-gray-500",children:"Drag and drop leads through your sales pipeline"})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[n.jsx(aN.z,{asChild:!0,children:(0,n.jsxs)(a_(),{href:"/dashboard/leads/new",children:[n.jsx(aA.Z,{className:"h-4 w-4 mr-2"}),"Add Lead"]})}),n.jsx(aN.z,{variant:"outline",asChild:!0,children:(0,n.jsxs)(a_(),{href:"/dashboard/leads",children:[n.jsx(aP.Z,{className:"h-4 w-4 mr-2"}),"List View"]})})]})]}),n.jsx(aI.Zb,{children:n.jsx(aI.aY,{className:"p-4",children:(0,n.jsxs)("div",{className:"flex flex-wrap items-center gap-4",children:[n.jsx("div",{className:"flex-1 min-w-64",children:(0,n.jsxs)("div",{className:"relative",children:[n.jsx(aC.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),n.jsx(aE.I,{placeholder:"Search leads...",value:s,onChange:e=>d(e.target.value),className:"pl-10"})]})}),(0,n.jsxs)(aw.Ph,{value:c,onValueChange:u,children:[n.jsx(aw.i4,{className:"w-40",children:n.jsx(aw.ki,{placeholder:"Source"})}),(0,n.jsxs)(aw.Bw,{children:[n.jsx(aw.Ql,{value:"all",children:"All Sources"}),n.jsx(aw.Ql,{value:"WEBSITE",children:"Website"}),n.jsx(aw.Ql,{value:"REFERRAL",children:"Referral"}),n.jsx(aw.Ql,{value:"SOCIAL_MEDIA",children:"Social Media"}),n.jsx(aw.Ql,{value:"EMAIL_CAMPAIGN",children:"Email Campaign"}),n.jsx(aw.Ql,{value:"COLD_CALL",children:"Cold Call"}),n.jsx(aw.Ql,{value:"TRADE_SHOW",children:"Trade Show"}),n.jsx(aw.Ql,{value:"PARTNER",children:"Partner"}),n.jsx(aw.Ql,{value:"OTHER",children:"Other"})]})]}),(0,n.jsxs)(aw.Ph,{value:p,onValueChange:f,children:[n.jsx(aw.i4,{className:"w-40",children:n.jsx(aw.ki,{placeholder:"Priority"})}),(0,n.jsxs)(aw.Bw,{children:[n.jsx(aw.Ql,{value:"all",children:"All Priorities"}),n.jsx(aw.Ql,{value:"LOW",children:"Low"}),n.jsx(aw.Ql,{value:"MEDIUM",children:"Medium"}),n.jsx(aw.Ql,{value:"HIGH",children:"High"}),n.jsx(aw.Ql,{value:"URGENT",children:"Urgent"})]})]})]})})}),n.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4",children:t.map(e=>n.jsx(aI.Zb,{className:`${e.color} border-2`,children:(0,n.jsxs)(aI.aY,{className:"p-4 text-center",children:[n.jsx("div",{className:"text-2xl font-bold text-gray-900",children:e.count}),n.jsx("div",{className:"text-sm text-gray-600",children:e.title})]})},e.id))}),n.jsx(lB,{onDragEnd:b,children:n.jsx("div",{className:"flex space-x-4 overflow-x-auto pb-4",children:t.map(e=>n.jsx("div",{className:"flex-shrink-0 w-80",children:(0,n.jsxs)(aI.Zb,{className:`${e.color} border-2`,children:[n.jsx(aI.Ol,{className:"pb-3",children:(0,n.jsxs)(aI.ll,{className:"flex items-center justify-between text-sm font-medium",children:[n.jsx("span",{children:e.title}),n.jsx(aD.C,{variant:"secondary",className:"text-xs",children:e.count})]})}),n.jsx(av,{droppableId:e.id,children:(t,r)=>(0,n.jsxs)("div",{ref:t.innerRef,...t.droppableProps,className:`min-h-96 p-2 space-y-2 ${r.isDraggingOver?"bg-gray-50":""}`,children:[e.leads.map((e,t)=>n.jsx(lJ,{draggableId:e.id,index:t,children:(t,r)=>n.jsx("div",{ref:t.innerRef,...t.draggableProps,...t.dragHandleProps,className:`${r.isDragging?"rotate-2 shadow-lg":""}`,children:n.jsx(a$,{lead:e})})},e.id)),t.placeholder,0===e.leads.length&&(0,n.jsxs)("div",{className:"text-center py-8 text-gray-400",children:[n.jsx(aP.Z,{className:"h-8 w-8 mx-auto mb-2"}),n.jsx("p",{className:"text-sm",children:"No leads in this stage"})]})]})})]})},e.id))})})]})}function a$({lead:e}){return n.jsx(aI.Zb,{className:"bg-white hover:shadow-md transition-shadow cursor-pointer",children:n.jsx(aI.aY,{className:"p-4",children:(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("div",{className:"flex items-start justify-between",children:[(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsxs)("h3",{className:"font-medium text-sm text-gray-900 truncate",children:[e.firstName," ",e.lastName]}),e.companyName&&n.jsx("p",{className:"text-xs text-gray-500 truncate",children:e.companyName}),e.title&&n.jsx("p",{className:"text-xs text-gray-400 truncate",children:e.title})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-1",children:[(e=>{switch(e){case"LOW":return n.jsx(aD.C,{variant:"secondary",className:"text-xs",children:"Low"});case"MEDIUM":return n.jsx(aD.C,{className:"bg-blue-100 text-blue-800 text-xs",children:"Medium"});case"HIGH":return n.jsx(aD.C,{className:"bg-orange-100 text-orange-800 text-xs",children:"High"});case"URGENT":return n.jsx(aD.C,{variant:"destructive",className:"text-xs",children:"Urgent"});default:return n.jsx(aD.C,{variant:"secondary",className:"text-xs",children:e})}})(e.priority),n.jsx(aN.z,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0",asChild:!0,children:n.jsx(a_(),{href:`/dashboard/leads/${e.id}`,children:n.jsx(aO.Z,{className:"h-3 w-3"})})})]})]}),(0,n.jsxs)("div",{className:"space-y-1",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2 text-xs text-gray-600",children:[n.jsx(aR.Z,{className:"h-3 w-3"}),n.jsx("span",{className:"truncate",children:e.email})]}),e.phone&&(0,n.jsxs)("div",{className:"flex items-center space-x-2 text-xs text-gray-600",children:[n.jsx(aS.Z,{className:"h-3 w-3"}),n.jsx("span",{children:e.phone})]})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between text-xs",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[n.jsx(aj.Z,{className:"h-3 w-3 text-blue-600"}),(0,n.jsxs)("span",{className:"text-gray-600",children:["Score: ",e.score,"/100"]})]}),e.budget&&(0,n.jsxs)("div",{className:"flex items-center space-x-1",children:[n.jsx(aL.Z,{className:"h-3 w-3 text-green-600"}),(0,n.jsxs)("span",{className:"text-gray-600",children:["$",e.budget.toLocaleString()]})]})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between pt-2 border-t border-gray-100",children:[n.jsx("div",{className:`text-xs ${(e=>{switch(e){case"WEBSITE":return"text-blue-600";case"REFERRAL":return"text-green-600";case"SOCIAL_MEDIA":return"text-purple-600";case"EMAIL_CAMPAIGN":return"text-orange-600";case"COLD_CALL":return"text-red-600";case"TRADE_SHOW":return"text-indigo-600";case"PARTNER":return"text-pink-600";default:return"text-gray-600"}})(e.source)}`,children:e.source.replace("_"," ")}),n.jsx("div",{className:"text-xs text-gray-400",children:new Date(e.createdAt).toLocaleDateString()})]}),e.assignedTo&&(0,n.jsxs)("div",{className:"flex items-center space-x-2 text-xs text-gray-500",children:[n.jsx(aB.Z,{className:"h-3 w-3"}),n.jsx("span",{className:"truncate",children:e.assignedTo.name})]})]})})})}},92549:(e,t,r)=>{"use strict";r.d(t,{I:()=>i});var n=r(95344),l=r(3729),a=r(91626);let i=l.forwardRef(({className:e,type:t,...r},l)=>n.jsx("input",{type:t,className:(0,a.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:l,...r}));i.displayName="Input"},17470:(e,t,r)=>{"use strict";r.d(t,{Bw:()=>m,Ph:()=>c,Ql:()=>h,i4:()=>p,ki:()=>u});var n=r(95344),l=r(3729),a=r(1146),i=r(25390),o=r(12704),s=r(62312),d=r(91626);let c=a.fC;a.ZA;let u=a.B4,p=l.forwardRef(({className:e,children:t,...r},l)=>(0,n.jsxs)(a.xz,{ref:l,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...r,children:[t,n.jsx(a.JO,{asChild:!0,children:n.jsx(i.Z,{className:"h-4 w-4 opacity-50"})})]}));p.displayName=a.xz.displayName;let f=l.forwardRef(({className:e,...t},r)=>n.jsx(a.u_,{ref:r,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:n.jsx(o.Z,{className:"h-4 w-4"})}));f.displayName=a.u_.displayName;let g=l.forwardRef(({className:e,...t},r)=>n.jsx(a.$G,{ref:r,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:n.jsx(i.Z,{className:"h-4 w-4"})}));g.displayName=a.$G.displayName;let m=l.forwardRef(({className:e,children:t,position:r="popper",...l},i)=>n.jsx(a.h_,{children:(0,n.jsxs)(a.VY,{ref:i,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...l,children:[n.jsx(f,{}),n.jsx(a.l_,{className:(0,d.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),n.jsx(g,{})]})}));m.displayName=a.VY.displayName,l.forwardRef(({className:e,...t},r)=>n.jsx(a.__,{ref:r,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=a.__.displayName;let h=l.forwardRef(({className:e,children:t,...r},l)=>(0,n.jsxs)(a.ck,{ref:l,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...r,children:[n.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:n.jsx(a.wU,{children:n.jsx(s.Z,{className:"h-4 w-4"})})}),n.jsx(a.eT,{children:t})]}));h.displayName=a.ck.displayName,l.forwardRef(({className:e,...t},r)=>n.jsx(a.Z0,{ref:r,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=a.Z0.displayName},48411:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},53148:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},71542:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("FileCheck",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["path",{d:"m9 15 2 2 4-4",key:"1grp1n"}]])},71206:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},91917:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},40626:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},51838:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},74243:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Receipt",[["path",{d:"M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1-2-1Z",key:"wqdwcb"}],["path",{d:"M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8",key:"1h4pet"}],["path",{d:"M12 17V7",key:"pyj7ub"}]])},17910:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},46064:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},28240:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},43231:(e,t,r)=>{"use strict";/**
 * @license React
 * use-sync-external-store-with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var n=r(3729);"function"==typeof Object.is&&Object.is,n.useSyncExternalStore,n.useRef,n.useEffect,n.useMemo,n.useDebugValue},40687:(e,t,r)=>{"use strict";r(43231)},93398:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>a,__esModule:()=>l,default:()=>i});let n=(0,r(86843).createProxy)(String.raw`C:\proj\nextjs-saas\app\dashboard\leads\pipeline\page.tsx`),{__esModule:l,$$typeof:a}=n,i=n.default}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[1638,7948,6671,4626,7792,2506,8830,2125,5045],()=>r(87455));module.exports=n})();