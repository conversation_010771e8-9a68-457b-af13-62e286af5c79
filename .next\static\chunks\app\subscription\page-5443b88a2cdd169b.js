(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1930],{72894:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62898).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},28203:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62898).Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},6141:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62898).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},71738:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62898).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},97332:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62898).Z)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},76637:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62898).Z)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]])},15713:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62898).Z)("Receipt",[["path",{d:"M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1-2-1Z",key:"wqdwcb"}],["path",{d:"M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8",key:"1h4pet"}],["path",{d:"M12 17V7",key:"pyj7ub"}]])},85790:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62898).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},25750:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62898).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},43680:function(e,t,s){Promise.resolve().then(s.bind(s,94801))},94801:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return w}});var r={};s.r(r);var n=s(57437),a=s(2265),i=s(82749),l=s(24033),c=s(27815),o=s(85754),d=s(31478),u=s(10500),x=s(40110),m=s(71738),f=s(28203),p=s(6141),h=s(25750),g=s(97332),y=s(85790),v=s(76637),b=s(15713),j=s(72894),N=s(71424);function w(){let{data:e}=(0,i.useSession)(),t=(0,l.useRouter)(),[s,w]=(0,a.useState)(null),[k,Z]=(0,a.useState)(!0);(0,a.useEffect)(()=>{(null==e?void 0:e.user)&&C()},[e]);let C=async()=>{try{let e=await fetch("/api/subscription"),t=await e.json();t.success?w(t.data):N.Am.error("Failed to load subscription data")}catch(e){console.error("Error fetching subscription:",e),N.Am.error("Failed to load subscription data")}finally{Z(!1)}},P=async()=>{try{let e=await fetch("/api/subscription/portal",{method:"POST"}),t=await e.json();t.success?window.location.href=t.data.url:N.Am.error(t.error||"Failed to open billing portal")}catch(e){console.error("Error opening billing portal:",e),N.Am.error("Failed to open billing portal")}},R=e=>e>=90?"bg-red-500":e>=75?"bg-yellow-500":"bg-green-500";if(k)return(0,n.jsx)("div",{className:"container mx-auto p-6",children:(0,n.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,n.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})})});if(!(null==s?void 0:s.hasActiveSubscription))return(0,n.jsx)("div",{className:"container mx-auto p-6",children:(0,n.jsxs)("div",{className:"text-center py-12",children:[(0,n.jsx)(m.Z,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),(0,n.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"No Active Subscription"}),(0,n.jsx)("p",{className:"text-gray-600 mb-6",children:"You don't have an active subscription. Choose a plan to get started."}),(0,n.jsx)(o.z,{onClick:()=>t.push("/pricing"),children:"View Pricing Plans"})]})});let{subscription:A,usage:Y}=s;return(0,n.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Subscription"}),(0,n.jsx)("p",{className:"text-gray-600",children:"Manage your subscription and view usage"})]}),(0,n.jsx)(o.z,{variant:"outline",onClick:()=>t.push("/pricing"),children:"Change Plan"})]}),(0,n.jsxs)(x.mQ,{defaultValue:"overview",className:"space-y-6",children:[(0,n.jsxs)(x.dr,{children:[(0,n.jsx)(x.SP,{value:"overview",children:"Overview"}),(0,n.jsx)(x.SP,{value:"usage",children:"Usage"}),(0,n.jsx)(x.SP,{value:"billing",children:"Billing"})]}),(0,n.jsxs)(x.nU,{value:"overview",className:"space-y-6",children:[(0,n.jsxs)(c.Zb,{children:[(0,n.jsx)(c.Ol,{children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)(c.ll,{className:"flex items-center gap-2",children:[(0,n.jsx)(m.Z,{className:"h-5 w-5"}),"Current Plan"]}),(0,n.jsx)(c.SZ,{children:"Your active subscription details"})]}),(0,n.jsx)(d.C,{className:(e=>{switch(e){case"ACTIVE":return"bg-green-100 text-green-800";case"TRIALING":return"bg-blue-100 text-blue-800";case"PAST_DUE":return"bg-yellow-100 text-yellow-800";case"CANCELED":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}})(A.status),children:A.status})]})}),(0,n.jsx)(c.aY,{className:"space-y-4",children:(0,n.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-xl font-semibold",children:A.pricingPlan.name}),(0,n.jsx)("p",{className:"text-gray-600",children:A.pricingPlan.description}),(0,n.jsxs)("div",{className:"mt-2",children:[(0,n.jsxs)("span",{className:"text-2xl font-bold",children:["$","YEARLY"===A.billingCycle?(A.pricingPlan.yearlyPrice/12).toFixed(0):A.pricingPlan.monthlyPrice]}),(0,n.jsx)("span",{className:"text-gray-500",children:"/month"}),"YEARLY"===A.billingCycle&&(0,n.jsx)(d.C,{variant:"secondary",className:"ml-2",children:"Billed Yearly"})]})]}),(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("span",{className:"flex items-center text-sm text-gray-600",children:[(0,n.jsx)(f.Z,{className:"h-4 w-4 mr-2"}),"TRIALING"===A.status?"Trial ends":"Next billing"]}),(0,n.jsx)("span",{className:"font-medium",children:A.daysUntilRenewal>0?"".concat(A.daysUntilRenewal," days"):"Today"})]}),"TRIALING"===A.status&&(0,n.jsx)("div",{className:"p-3 bg-blue-50 rounded-lg",children:(0,n.jsxs)("div",{className:"flex items-center text-blue-700",children:[(0,n.jsx)(p.Z,{className:"h-4 w-4 mr-2"}),(0,n.jsx)("span",{className:"text-sm font-medium",children:"Free trial active"})]})})]})]})})]}),(0,n.jsxs)("div",{className:"grid md:grid-cols-3 gap-6",children:[(0,n.jsx)(c.Zb,{children:(0,n.jsxs)(c.aY,{className:"p-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm text-gray-600",children:"Users"}),(0,n.jsxs)("p",{className:"text-2xl font-bold",children:[Y.users.current,"/",Y.users.limit]})]}),(0,n.jsx)(h.Z,{className:"h-8 w-8 text-blue-500"})]}),(0,n.jsx)(u.E,{value:Y.users.percentage,className:"mt-3",style:{"--progress-background":R(Y.users.percentage)}})]})}),(0,n.jsx)(c.Zb,{children:(0,n.jsxs)(c.aY,{className:"p-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm text-gray-600",children:"Storage"}),(0,n.jsx)("p",{className:"text-2xl font-bold",children:Y.storage.currentFormatted}),(0,n.jsxs)("p",{className:"text-xs text-gray-500",children:["of ",Y.storage.limitFormatted]})]}),(0,n.jsx)(g.Z,{className:"h-8 w-8 text-green-500"})]}),(0,n.jsx)(u.E,{value:Y.storage.percentage,className:"mt-3",style:{"--progress-background":R(Y.storage.percentage)}})]})}),(0,n.jsx)(c.Zb,{children:(0,n.jsxs)(c.aY,{className:"p-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm text-gray-600",children:"Customers"}),(0,n.jsxs)("p",{className:"text-2xl font-bold",children:[Y.customers.current,"/",Y.customers.limit]})]}),(0,n.jsx)(y.Z,{className:"h-8 w-8 text-purple-500"})]}),(0,n.jsx)(u.E,{value:Y.customers.percentage,className:"mt-3",style:{"--progress-background":R(Y.customers.percentage)}})]})})]})]}),(0,n.jsx)(x.nU,{value:"usage",className:"space-y-6",children:(0,n.jsxs)(c.Zb,{children:[(0,n.jsxs)(c.Ol,{children:[(0,n.jsx)(c.ll,{children:"Usage Details"}),(0,n.jsx)(c.SZ,{children:"Monitor your usage across all plan limits"})]}),(0,n.jsx)(c.aY,{children:(0,n.jsx)("div",{className:"space-y-6",children:[{key:"users",label:"Users",icon:h.Z},{key:"customers",label:"Customers",icon:y.Z},{key:"quotations",label:"Quotations",icon:v.Z},{key:"invoices",label:"Invoices",icon:b.Z},{key:"contracts",label:"Contracts",icon:r.FileContract},{key:"storage",label:"Storage",icon:g.Z}].map(e=>{let{key:t,label:s,icon:r}=e,a=Y[t],i=a.percentage>=80;return(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(r,{className:"h-4 w-4 text-gray-500"}),(0,n.jsx)("span",{className:"font-medium",children:s}),i&&(0,n.jsx)(j.Z,{className:"h-4 w-4 text-yellow-500"})]}),(0,n.jsx)("span",{className:"text-sm text-gray-600",children:"storage"===t?"".concat(a.currentFormatted," / ").concat(a.limitFormatted):"".concat(a.current," / ").concat(a.limit)})]}),(0,n.jsx)(u.E,{value:a.percentage,className:"h-2 ".concat(i?"bg-yellow-100":""),style:{"--progress-background":R(a.percentage)}}),(0,n.jsxs)("div",{className:"flex justify-between text-xs text-gray-500",children:[(0,n.jsxs)("span",{children:[a.percentage,"% used"]}),i&&(0,n.jsx)("span",{className:"text-yellow-600 font-medium",children:"Approaching limit"})]})]},t)})})})]})}),(0,n.jsx)(x.nU,{value:"billing",className:"space-y-6",children:(0,n.jsxs)(c.Zb,{children:[(0,n.jsxs)(c.Ol,{children:[(0,n.jsx)(c.ll,{children:"Billing Information"}),(0,n.jsx)(c.SZ,{children:"Manage your billing details and payment methods"})]}),(0,n.jsx)(c.aY,{children:A.stripeCustomerId?(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-medium",children:"Payment Methods"}),(0,n.jsx)("p",{className:"text-sm text-gray-600",children:"Manage your payment methods and billing information"})]}),(0,n.jsxs)(o.z,{onClick:P,children:[(0,n.jsx)(m.Z,{className:"h-4 w-4 mr-2"}),"Manage Billing"]})]}),(0,n.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,n.jsx)("h4",{className:"font-medium mb-2",children:"Current Plan"}),(0,n.jsxs)("p",{className:"text-2xl font-bold",children:["$","YEARLY"===A.billingCycle?(A.pricingPlan.yearlyPrice/12).toFixed(0):A.pricingPlan.monthlyPrice]}),(0,n.jsxs)("p",{className:"text-sm text-gray-600",children:["per month ","YEARLY"===A.billingCycle&&"(billed annually)"]})]}),(0,n.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,n.jsx)("h4",{className:"font-medium mb-2",children:"Next Payment"}),(0,n.jsx)("p",{className:"text-lg font-semibold",children:A.daysUntilRenewal>0?"".concat(A.daysUntilRenewal," days"):"Today"}),(0,n.jsx)("p",{className:"text-sm text-gray-600",children:new Date(A.currentPeriodEnd).toLocaleDateString()})]})]})]}):(0,n.jsxs)("div",{className:"text-center py-8",children:[(0,n.jsx)(m.Z,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Payment Method Required"}),(0,n.jsx)("p",{className:"text-gray-600 mb-4",children:"You're currently on a free plan that doesn't require payment."}),(0,n.jsx)(o.z,{variant:"outline",onClick:()=>t.push("/pricing"),children:"Upgrade Plan"})]})})]})})]})]})}},31478:function(e,t,s){"use strict";s.d(t,{C:function(){return l}});var r=s(57437);s(2265);var n=s(96061),a=s(1657);let i=(0,n.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600",info:"border-transparent bg-blue-500 text-white hover:bg-blue-600"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:s,...n}=e;return(0,r.jsx)("div",{className:(0,a.cn)(i({variant:s}),t),...n})}},85754:function(e,t,s){"use strict";s.d(t,{z:function(){return o}});var r=s(57437),n=s(2265),a=s(67256),i=s(96061),l=s(1657);let c=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=n.forwardRef((e,t)=>{let{className:s,variant:n,size:i,asChild:o=!1,...d}=e,u=o?a.g7:"button";return(0,r.jsx)(u,{className:(0,l.cn)(c({variant:n,size:i,className:s})),ref:t,...d})});o.displayName="Button"},27815:function(e,t,s){"use strict";s.d(t,{Ol:function(){return l},SZ:function(){return o},Zb:function(){return i},aY:function(){return d},eW:function(){return u},ll:function(){return c}});var r=s(57437),n=s(2265),a=s(1657);let i=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...n})});i.displayName="Card";let l=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",s),...n})});l.displayName="CardHeader";let c=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)("h3",{ref:t,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",s),...n})});c.displayName="CardTitle";let o=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)("p",{ref:t,className:(0,a.cn)("text-sm text-muted-foreground",s),...n})});o.displayName="CardDescription";let d=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("p-6 pt-0",s),...n})});d.displayName="CardContent";let u=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("flex items-center p-6 pt-0",s),...n})});u.displayName="CardFooter"},10500:function(e,t,s){"use strict";s.d(t,{E:function(){return l}});var r=s(57437),n=s(2265),a=s(23177),i=s(1657);let l=n.forwardRef((e,t)=>{let{className:s,value:n,...l}=e;return(0,r.jsx)(a.fC,{ref:t,className:(0,i.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",s),...l,children:(0,r.jsx)(a.z$,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(n||0),"%)")}})})});l.displayName=a.fC.displayName},40110:function(e,t,s){"use strict";s.d(t,{SP:function(){return o},dr:function(){return c},mQ:function(){return l},nU:function(){return d}});var r=s(57437),n=s(2265),a=s(34522),i=s(1657);let l=a.fC,c=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)(a.aV,{ref:t,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",s),...n})});c.displayName=a.aV.displayName;let o=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)(a.xz,{ref:t,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",s),...n})});o.displayName=a.xz.displayName;let d=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)(a.VY,{ref:t,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",s),...n})});d.displayName=a.VY.displayName},1657:function(e,t,s){"use strict";s.d(t,{cn:function(){return a}});var r=s(57042),n=s(74769);function a(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,n.m6)((0,r.W)(t))}},24033:function(e,t,s){e.exports=s(15313)},23177:function(e,t,s){"use strict";s.d(t,{fC:function(){return b},z$:function(){return j}});var r=s(2265),n=s(56989),a=s(9381),i=s(57437),l="Progress",[c,o]=(0,n.b)(l),[d,u]=c(l),x=r.forwardRef((e,t)=>{var s,r;let{__scopeProgress:n,value:l=null,max:c,getValueLabel:o=p,...u}=e;(c||0===c)&&!y(c)&&console.error((s=`${c}`,`Invalid prop \`max\` of value \`${s}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let x=y(c)?c:100;null===l||v(l,x)||console.error((r=`${l}`,`Invalid prop \`value\` of value \`${r}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let m=v(l,x)?l:null,f=g(m)?o(m,x):void 0;return(0,i.jsx)(d,{scope:n,value:m,max:x,children:(0,i.jsx)(a.WV.div,{"aria-valuemax":x,"aria-valuemin":0,"aria-valuenow":g(m)?m:void 0,"aria-valuetext":f,role:"progressbar","data-state":h(m,x),"data-value":m??void 0,"data-max":x,...u,ref:t})})});x.displayName=l;var m="ProgressIndicator",f=r.forwardRef((e,t)=>{let{__scopeProgress:s,...r}=e,n=u(m,s);return(0,i.jsx)(a.WV.div,{"data-state":h(n.value,n.max),"data-value":n.value??void 0,"data-max":n.max,...r,ref:t})});function p(e,t){return`${Math.round(e/t*100)}%`}function h(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function g(e){return"number"==typeof e}function y(e){return g(e)&&!isNaN(e)&&e>0}function v(e,t){return g(e)&&!isNaN(e)&&e<=t&&e>=0}f.displayName=m;var b=x,j=f}},function(e){e.O(0,[6723,9502,2749,4522,1424,2971,4938,1744],function(){return e(e.s=43680)}),_N_E=e.O()}]);