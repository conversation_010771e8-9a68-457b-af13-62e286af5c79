"use strict";(()=>{var e={};e.id=4058,e.ids=[4058],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},92762:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>b,originalPathname:()=>I,patchFetch:()=>h,requestAsyncStorage:()=>y,routeModule:()=>m,serverHooks:()=>w,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>_});var r={};a.r(r),a.d(r,{GET:()=>d,POST:()=>p});var s=a(95419),n=a(69108),i=a(99678),o=a(78070),u=a(81355),c=a(3205),l=a(9108);async function d(e){try{let t=await (0,u.getServerSession)(c.L);if(!t?.user?.id||t?.user?.role!=="SUPER_ADMIN")return o.Z.json({error:"Super admin access required"},{status:403});let{searchParams:a}=new URL(e.url),r=parseInt(a.get("page")||"1"),s=parseInt(a.get("limit")||"20"),n=a.get("status")||"",i=a.get("plan")||"",d=a.get("search")||"",p=(r-1)*s,m={};n&&"all"!==n&&(m.status=n),i&&"all"!==i&&(m.plan=i),d&&(m.company={OR:[{name:{contains:d,mode:"insensitive"}},{email:{contains:d,mode:"insensitive"}}]});let[y,g]=await Promise.all([l._.subscription.findMany({where:m,skip:p,take:s,include:{company:{select:{id:!0,name:!0,email:!0,status:!0,industry:!0,size:!0,owner:{select:{id:!0,name:!0,email:!0,avatar:!0}},_count:{select:{members:!0,customers:!0,quotations:!0,invoices:!0}}}}},orderBy:{createdAt:"desc"}}),l._.subscription.count({where:m})]),[w,b,_,I,h,v,f]=await Promise.all([l._.subscription.groupBy({by:["status"],_count:{id:!0}}),l._.subscription.groupBy({by:["plan"],_count:{id:!0}}),l._.subscription.aggregate({where:{status:"ACTIVE"},_sum:{amount:!0},_avg:{amount:!0},_count:{id:!0}}),l._.subscription.findMany({where:{status:"ACTIVE",billingCycle:"MONTHLY"},select:{amount:!0}}),l._.subscription.findMany({where:{status:"ACTIVE",billingCycle:"YEARLY"},select:{amount:!0}}),l._.subscription.count({where:{status:"CANCELLED",updatedAt:{gte:new Date(Date.now()-2592e6)}}}),l._.subscription.count({where:{status:"ACTIVE",createdAt:{gte:new Date(Date.now()-2592e6)}}})]),E=I.reduce((e,t)=>e+Number(t.amount),0)+h.reduce((e,t)=>e+Number(t.amount),0)/12;return o.Z.json({subscriptions:y.map(e=>({...e,company:{...e.company,metrics:{totalUsers:e.company._count.members,totalCustomers:e.company._count.customers,totalQuotations:e.company._count.quotations,totalInvoices:e.company._count.invoices}}})),pagination:{page:r,limit:s,total:g,pages:Math.ceil(g/s)},stats:{total:g,byStatus:w.map(e=>({status:e.status,count:e._count.id})),byPlan:b.map(e=>({plan:e.plan,count:e._count.id})),revenue:{total:Number(_._sum.amount||0),average:Number(_._avg.amount||0),activeSubscriptions:_._count.id,mrr:Math.round(E),arr:Math.round(12*E)},metrics:{churnCount:v,newSubscriptions:f,churnRate:g>0?v/g*100:0,growthRate:g>0?f/g*100:0}}})}catch(e){return console.error("Error fetching subscriptions:",e),o.Z.json({error:"Failed to fetch subscriptions"},{status:500})}}async function p(e){try{let t;let a=await (0,u.getServerSession)(c.L);if(!a?.user?.id||a?.user?.role!=="SUPER_ADMIN")return o.Z.json({error:"Super admin access required"},{status:403});let{companyId:r,plan:s,status:n,amount:i,billingCycle:d,startDate:p,endDate:m,trialEndDate:y,stripeSubscriptionId:g,stripeCustomerId:w}=await e.json();if(!r||!s||!i)return o.Z.json({error:"Company ID, plan, and amount are required"},{status:400});if(!await l._.company.findUnique({where:{id:r}}))return o.Z.json({error:"Company not found"},{status:404});let b=await l._.subscription.findUnique({where:{companyId:r}});return b?(t=await l._.subscription.update({where:{companyId:r},data:{plan:s,status:n||"ACTIVE",amount:i,billingCycle:d||"MONTHLY",startDate:p?new Date(p):void 0,endDate:m?new Date(m):void 0,trialEndDate:y?new Date(y):void 0,stripeSubscriptionId:g,stripeCustomerId:w},include:{company:{select:{name:!0,email:!0}}}}),await l._.auditLog.create({data:{action:"SUBSCRIPTION_UPDATED",entityType:"Subscription",entityId:t.id,userId:a.user.id,userEmail:a.user.email,userRole:a.user.role,companyId:r,newValues:{plan:s,status:n,amount:i,billingCycle:d},metadata:{updatedByAdmin:!0,adminId:a.user.id}}})):(t=await l._.subscription.create({data:{companyId:r,plan:s,status:n||"ACTIVE",amount:i,billingCycle:d||"MONTHLY",startDate:p?new Date(p):new Date,endDate:m?new Date(m):void 0,trialEndDate:y?new Date(y):void 0,stripeSubscriptionId:g,stripeCustomerId:w},include:{company:{select:{name:!0,email:!0}}}}),await l._.auditLog.create({data:{action:"SUBSCRIPTION_CREATED",entityType:"Subscription",entityId:t.id,userId:a.user.id,userEmail:a.user.email,userRole:a.user.role,companyId:r,newValues:{plan:s,status:n,amount:i,billingCycle:d},metadata:{createdByAdmin:!0,adminId:a.user.id}}})),o.Z.json({subscription:t,message:b?"Subscription updated successfully":"Subscription created successfully"},{status:b?200:201})}catch(e){return console.error("Error managing subscription:",e),o.Z.json({error:"Failed to manage subscription"},{status:500})}}let m=new s.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/super-admin/subscriptions/route",pathname:"/api/super-admin/subscriptions",filename:"route",bundlePath:"app/api/super-admin/subscriptions/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\super-admin\\subscriptions\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:y,staticGenerationAsyncStorage:g,serverHooks:w,headerHooks:b,staticGenerationBailout:_}=m,I="/api/super-admin/subscriptions/route";function h(){return(0,i.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:g})}},3205:(e,t,a)=>{a.d(t,{L:()=>c});var r=a(86485),s=a(10375),n=a(50694),i=a(6521),o=a.n(i),u=a(9108);let c={providers:[(0,r.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await u._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),a=t?.companyId;if(!a&&t){let e=await u._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(a=e?.id)&&await u._.user.update({where:{id:t.id},data:{companyId:a}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await o().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await u._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:a}}catch(e){return console.error("Authentication error:",e),null}}}),(0,s.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,n.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,a)=>{a.d(t,{_:()=>s});let r=require("@prisma/client"),s=globalThis.prisma??new r.PrismaClient}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[1638,6206,6521,2455,4520],()=>a(92762));module.exports=r})();