"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/super-admin/subscriptions/route";
exports.ids = ["app/api/super-admin/subscriptions/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Fsubscriptions%2Froute&page=%2Fapi%2Fsuper-admin%2Fsubscriptions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Fsubscriptions%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Fsubscriptions%2Froute&page=%2Fapi%2Fsuper-admin%2Fsubscriptions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Fsubscriptions%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_proj_nextjs_saas_app_api_super_admin_subscriptions_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/super-admin/subscriptions/route.ts */ \"(rsc)/./app/api/super-admin/subscriptions/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/super-admin/subscriptions/route\",\n        pathname: \"/api/super-admin/subscriptions\",\n        filename: \"route\",\n        bundlePath: \"app/api/super-admin/subscriptions/route\"\n    },\n    resolvedPagePath: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\api\\\\super-admin\\\\subscriptions\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_proj_nextjs_saas_app_api_super_admin_subscriptions_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/super-admin/subscriptions/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Fsubscriptions%2Froute&page=%2Fapi%2Fsuper-admin%2Fsubscriptions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Fsubscriptions%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/super-admin/subscriptions/route.ts":
/*!****************************************************!*\
  !*** ./app/api/super-admin/subscriptions/route.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n// GET /api/super-admin/subscriptions - Get all subscriptions with analytics\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id || session?.user?.role !== \"SUPER_ADMIN\") {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Super admin access required\"\n            }, {\n                status: 403\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get(\"page\") || \"1\");\n        const limit = parseInt(searchParams.get(\"limit\") || \"20\");\n        const status = searchParams.get(\"status\") || \"\";\n        const plan = searchParams.get(\"plan\") || \"\";\n        const search = searchParams.get(\"search\") || \"\";\n        const skip = (page - 1) * limit;\n        // Build where clause\n        const where = {};\n        if (status && status !== \"all\") {\n            where.status = status;\n        }\n        if (plan && plan !== \"all\") {\n            where.plan = plan;\n        }\n        if (search) {\n            where.company = {\n                OR: [\n                    {\n                        name: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    },\n                    {\n                        email: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    }\n                ]\n            };\n        }\n        // Get subscriptions with pagination\n        const [subscriptions, total] = await Promise.all([\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.subscription.findMany({\n                where,\n                skip,\n                take: limit,\n                include: {\n                    company: {\n                        select: {\n                            id: true,\n                            name: true,\n                            email: true,\n                            status: true,\n                            industry: true,\n                            size: true,\n                            owner: {\n                                select: {\n                                    id: true,\n                                    name: true,\n                                    email: true,\n                                    avatar: true\n                                }\n                            },\n                            _count: {\n                                select: {\n                                    members: true,\n                                    customers: true,\n                                    quotations: true,\n                                    invoices: true\n                                }\n                            }\n                        }\n                    }\n                },\n                orderBy: {\n                    createdAt: \"desc\"\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.subscription.count({\n                where\n            })\n        ]);\n        // Get subscription statistics\n        const stats = await Promise.all([\n            // Count by status\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.subscription.groupBy({\n                by: [\n                    \"status\"\n                ],\n                _count: {\n                    id: true\n                }\n            }),\n            // Count by plan\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.subscription.groupBy({\n                by: [\n                    \"plan\"\n                ],\n                _count: {\n                    id: true\n                }\n            }),\n            // Revenue metrics\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.subscription.aggregate({\n                where: {\n                    status: \"ACTIVE\"\n                },\n                _sum: {\n                    amount: true\n                },\n                _avg: {\n                    amount: true\n                },\n                _count: {\n                    id: true\n                }\n            }),\n            // Monthly recurring revenue\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.subscription.findMany({\n                where: {\n                    status: \"ACTIVE\",\n                    billingCycle: \"MONTHLY\"\n                },\n                select: {\n                    amount: true\n                }\n            }),\n            // Yearly recurring revenue\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.subscription.findMany({\n                where: {\n                    status: \"ACTIVE\",\n                    billingCycle: \"YEARLY\"\n                },\n                select: {\n                    amount: true\n                }\n            }),\n            // Churn rate (cancelled in last 30 days)\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.subscription.count({\n                where: {\n                    status: \"CANCELLED\",\n                    updatedAt: {\n                        gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)\n                    }\n                }\n            }),\n            // New subscriptions (last 30 days)\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.subscription.count({\n                where: {\n                    status: \"ACTIVE\",\n                    createdAt: {\n                        gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)\n                    }\n                }\n            })\n        ]);\n        const [byStatus, byPlan, revenueMetrics, monthlyRevenue, yearlyRevenue, churnCount, newSubscriptions] = stats;\n        // Calculate MRR and ARR\n        const mrr = monthlyRevenue.reduce((sum, sub)=>sum + Number(sub.amount), 0) + yearlyRevenue.reduce((sum, sub)=>sum + Number(sub.amount), 0) / 12;\n        const arr = mrr * 12;\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            subscriptions: subscriptions.map((sub)=>({\n                    ...sub,\n                    company: {\n                        ...sub.company,\n                        metrics: {\n                            totalUsers: sub.company._count.members,\n                            totalCustomers: sub.company._count.customers,\n                            totalQuotations: sub.company._count.quotations,\n                            totalInvoices: sub.company._count.invoices\n                        }\n                    }\n                })),\n            pagination: {\n                page,\n                limit,\n                total,\n                pages: Math.ceil(total / limit)\n            },\n            stats: {\n                total,\n                byStatus: byStatus.map((item)=>({\n                        status: item.status,\n                        count: item._count.id\n                    })),\n                byPlan: byPlan.map((item)=>({\n                        plan: item.plan,\n                        count: item._count.id\n                    })),\n                revenue: {\n                    total: Number(revenueMetrics._sum.amount || 0),\n                    average: Number(revenueMetrics._avg.amount || 0),\n                    activeSubscriptions: revenueMetrics._count.id,\n                    mrr: Math.round(mrr),\n                    arr: Math.round(arr)\n                },\n                metrics: {\n                    churnCount,\n                    newSubscriptions,\n                    churnRate: total > 0 ? churnCount / total * 100 : 0,\n                    growthRate: total > 0 ? newSubscriptions / total * 100 : 0\n                }\n            }\n        });\n    } catch (error) {\n        console.error(\"Error fetching subscriptions:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to fetch subscriptions\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/super-admin/subscriptions - Create or update subscription\nasync function POST(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id || session?.user?.role !== \"SUPER_ADMIN\") {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Super admin access required\"\n            }, {\n                status: 403\n            });\n        }\n        const body = await request.json();\n        const { companyId, plan, status, amount, billingCycle, startDate, endDate, trialEndDate, stripeSubscriptionId, stripeCustomerId } = body;\n        // Validate required fields\n        if (!companyId || !plan || !amount) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Company ID, plan, and amount are required\"\n            }, {\n                status: 400\n            });\n        }\n        // Check if company exists\n        const company = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.company.findUnique({\n            where: {\n                id: companyId\n            }\n        });\n        if (!company) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Company not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Check if subscription already exists\n        const existingSubscription = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.subscription.findUnique({\n            where: {\n                companyId\n            }\n        });\n        let subscription;\n        if (existingSubscription) {\n            // Update existing subscription\n            subscription = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.subscription.update({\n                where: {\n                    companyId\n                },\n                data: {\n                    plan,\n                    status: status || \"ACTIVE\",\n                    amount,\n                    billingCycle: billingCycle || \"MONTHLY\",\n                    startDate: startDate ? new Date(startDate) : undefined,\n                    endDate: endDate ? new Date(endDate) : undefined,\n                    trialEndDate: trialEndDate ? new Date(trialEndDate) : undefined,\n                    stripeSubscriptionId,\n                    stripeCustomerId\n                },\n                include: {\n                    company: {\n                        select: {\n                            name: true,\n                            email: true\n                        }\n                    }\n                }\n            });\n            // Log the action\n            await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.auditLog.create({\n                data: {\n                    action: \"SUBSCRIPTION_UPDATED\",\n                    entityType: \"Subscription\",\n                    entityId: subscription.id,\n                    userId: session.user.id,\n                    userEmail: session.user.email,\n                    userRole: session.user.role,\n                    companyId,\n                    newValues: {\n                        plan,\n                        status,\n                        amount,\n                        billingCycle\n                    },\n                    metadata: {\n                        updatedByAdmin: true,\n                        adminId: session.user.id\n                    }\n                }\n            });\n        } else {\n            // Create new subscription\n            subscription = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.subscription.create({\n                data: {\n                    companyId,\n                    plan,\n                    status: status || \"ACTIVE\",\n                    amount,\n                    billingCycle: billingCycle || \"MONTHLY\",\n                    startDate: startDate ? new Date(startDate) : new Date(),\n                    endDate: endDate ? new Date(endDate) : undefined,\n                    trialEndDate: trialEndDate ? new Date(trialEndDate) : undefined,\n                    stripeSubscriptionId,\n                    stripeCustomerId\n                },\n                include: {\n                    company: {\n                        select: {\n                            name: true,\n                            email: true\n                        }\n                    }\n                }\n            });\n            // Log the action\n            await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.auditLog.create({\n                data: {\n                    action: \"SUBSCRIPTION_CREATED\",\n                    entityType: \"Subscription\",\n                    entityId: subscription.id,\n                    userId: session.user.id,\n                    userEmail: session.user.email,\n                    userRole: session.user.role,\n                    companyId,\n                    newValues: {\n                        plan,\n                        status,\n                        amount,\n                        billingCycle\n                    },\n                    metadata: {\n                        createdByAdmin: true,\n                        adminId: session.user.id\n                    }\n                }\n            });\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            subscription,\n            message: existingSubscription ? \"Subscription updated successfully\" : \"Subscription created successfully\"\n        }, {\n            status: existingSubscription ? 200 : 201\n        });\n    } catch (error) {\n        console.error(\"Error managing subscription:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to manage subscription\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/super-admin/subscriptions/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/github */ \"(rsc)/./node_modules/next-auth/providers/github.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n\nconst authOptions = {\n    // Using JWT strategy instead of database adapter for now\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                try {\n                    if (!credentials?.email || !credentials?.password) {\n                        console.log(\"Missing credentials\");\n                        return null;\n                    }\n                    console.log(\"Attempting to authenticate user:\", credentials.email);\n                    const user = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.findUnique({\n                        where: {\n                            email: credentials.email\n                        },\n                        select: {\n                            id: true,\n                            email: true,\n                            name: true,\n                            password: true,\n                            role: true,\n                            companyId: true\n                        }\n                    });\n                    // Get the company ID - either as member or owner\n                    let finalCompanyId = user?.companyId;\n                    if (!finalCompanyId && user) {\n                        const ownedCompany = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.company.findFirst({\n                            where: {\n                                ownerId: user.id\n                            },\n                            select: {\n                                id: true\n                            }\n                        });\n                        finalCompanyId = ownedCompany?.id;\n                        // If user is company owner, update their companyId for future queries\n                        if (finalCompanyId) {\n                            await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                                where: {\n                                    id: user.id\n                                },\n                                data: {\n                                    companyId: finalCompanyId\n                                }\n                            });\n                        }\n                    }\n                    if (!user) {\n                        console.log(\"User not found:\", credentials.email);\n                        return null;\n                    }\n                    if (!user.password) {\n                        console.log(\"User has no password set:\", credentials.email);\n                        return null;\n                    }\n                    const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_3___default().compare(credentials.password, user.password);\n                    if (!isPasswordValid) {\n                        console.log(\"Invalid password for user:\", credentials.email);\n                        return null;\n                    }\n                    // Update last login\n                    await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                        where: {\n                            id: user.id\n                        },\n                        data: {\n                            lastLoginAt: new Date(),\n                            loginCount: {\n                                increment: 1\n                            }\n                        }\n                    });\n                    console.log(\"User authenticated successfully:\", user.email);\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name,\n                        role: user.role,\n                        companyId: finalCompanyId\n                    };\n                } catch (error) {\n                    console.error(\"Authentication error:\", error);\n                    return null;\n                }\n            }\n        }),\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            clientId: process.env.GITHUB_CLIENT_ID,\n            clientSecret: process.env.GITHUB_CLIENT_SECRET\n        })\n    ],\n    session: {\n        strategy: \"jwt\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                console.log(\"JWT callback - user data:\", {\n                    id: user.id,\n                    email: user.email,\n                    role: user.role,\n                    companyId: user.companyId\n                });\n                token.role = user.role;\n                token.companyId = user.companyId;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n                session.user.companyId = token.companyId;\n                console.log(\"Session callback - final session:\", {\n                    id: session.user.id,\n                    email: session.user.email,\n                    role: session.user.role,\n                    companyId: session.user.companyId\n                });\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        signUp: \"/auth/signup\",\n        error: \"/auth/error\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zYWFzLWFwcC8uL2xpYi9wcmlzbWEudHM/OTgyMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/lru-cache","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Fsubscriptions%2Froute&page=%2Fapi%2Fsuper-admin%2Fsubscriptions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Fsubscriptions%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();