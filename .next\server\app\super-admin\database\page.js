(()=>{var e={};e.id=451,e.ids=[451],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57979:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>u,tree:()=>c});var t=a(50482),r=a(69108),i=a(62563),n=a.n(i),l=a(68300),d={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);a.d(s,d);let c=["",{children:["super-admin",{children:["database",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,8644)),"C:\\proj\\nextjs-saas\\app\\super-admin\\database\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,11285)),"C:\\proj\\nextjs-saas\\app\\super-admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,59504)),"C:\\proj\\nextjs-saas\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,69361,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\proj\\nextjs-saas\\app\\super-admin\\database\\page.tsx"],x="/super-admin/database/page",m={require:a,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/super-admin/database/page",pathname:"/super-admin/database",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},31165:(e,s,a)=>{Promise.resolve().then(a.bind(a,50276))},50276:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>S});var t=a(95344),r=a(3729),i=a(61351),n=a(69436),l=a(16212),d=a(76461),c=a(25757),o=a(81036),x=a(7060),m=a(45961),u=a(88534),h=a(33733),p=a(50340),j=a(37121),f=a(25545),v=a(13746),b=a(96885),N=a(89895),y=a(38271),g=a(79200),w=a(73557),k=a(3380);function S(){let[e,s]=(0,r.useState)(!0),[a,S]=(0,r.useState)(new Date),[C]=(0,r.useState)([{name:"Total Size",value:"2.4",unit:"GB",status:"good",description:"Total database size across all tables"},{name:"Active Connections",value:24,status:"good",description:"Current active database connections"},{name:"Query Performance",value:"89",unit:"ms",status:"warning",description:"Average query execution time"},{name:"Cache Hit Rate",value:"94.2",unit:"%",status:"good",description:"Database query cache efficiency"},{name:"Disk Usage",value:"68",unit:"%",status:"warning",description:"Database storage utilization"},{name:"Backup Status",value:"Success",status:"good",description:"Last backup completion status"}]),[D]=(0,r.useState)([{name:"users",rows:15420,size:"245 MB",lastUpdated:new Date(Date.now()-12e4),status:"healthy"},{name:"companies",rows:1250,size:"89 MB",lastUpdated:new Date(Date.now()-3e5),status:"healthy"},{name:"subscriptions",rows:3420,size:"156 MB",lastUpdated:new Date(Date.now()-6e5),status:"healthy"},{name:"invoices",rows:8950,size:"1.2 GB",lastUpdated:new Date(Date.now()-9e5),status:"warning"},{name:"audit_logs",rows:125e3,size:"890 MB",lastUpdated:new Date(Date.now()-6e4),status:"healthy"}]),[Z]=(0,r.useState)([{id:"1",user:"app_user",database:"nextjs_saas_db",host:"********",state:"active",duration:"00:02:15",query:"SELECT * FROM users WHERE company_id = ?"},{id:"2",user:"admin",database:"nextjs_saas_db",host:"********",state:"idle",duration:"00:15:30"},{id:"3",user:"backup_user",database:"nextjs_saas_db",host:"********",state:"active",duration:"01:45:20",query:"BACKUP DATABASE TO /backups/daily_backup.sql"},{id:"4",user:"app_user",database:"nextjs_saas_db",host:"********",state:"waiting",duration:"00:00:45",query:"UPDATE subscriptions SET status = ? WHERE id = ?"}]);(0,r.useEffect)(()=>{let e=setTimeout(()=>{s(!1)},1e3);return()=>clearTimeout(e)},[]);let P=e=>{switch(e){case"good":case"healthy":return t.jsx(x.Z,{className:"h-4 w-4 text-green-500"});case"warning":return t.jsx(m.Z,{className:"h-4 w-4 text-yellow-500"});case"critical":case"error":return t.jsx(m.Z,{className:"h-4 w-4 text-red-500"});default:return t.jsx(u.Z,{className:"h-4 w-4 text-gray-500"})}},R=e=>{switch(e){case"good":case"healthy":return"default";case"warning":return"secondary";case"critical":case"error":return"destructive";default:return"outline"}},T=e=>{switch(e){case"active":return"default";case"idle":return"secondary";case"waiting":return"destructive";default:return"outline"}};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[t.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Database Management"}),t.jsx("p",{className:"text-muted-foreground",children:"Monitor database performance, connections, and manage data"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(n.C,{variant:"outline",className:"text-xs",children:["Last updated: ",a.toLocaleTimeString()]}),(0,t.jsxs)(l.z,{onClick:()=>{s(!0),S(new Date),setTimeout(()=>s(!1),1e3)},disabled:e,size:"sm",children:[t.jsx(h.Z,{className:`h-4 w-4 mr-2 ${e?"animate-spin":""}`}),"Refresh"]})]})]}),t.jsx("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-3",children:C.map((e,s)=>(0,t.jsxs)(i.Zb,{children:[(0,t.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t.jsx(i.ll,{className:"text-sm font-medium",children:e.name}),P(e.status)]}),(0,t.jsxs)(i.aY,{children:[(0,t.jsxs)("div",{className:"text-2xl font-bold",children:[e.value,e.unit]}),t.jsx("p",{className:"text-xs text-muted-foreground mt-1",children:e.description})]})]},s))}),(0,t.jsxs)(c.mQ,{defaultValue:"overview",className:"space-y-4",children:[(0,t.jsxs)(c.dr,{children:[t.jsx(c.SP,{value:"overview",children:"Overview"}),t.jsx(c.SP,{value:"tables",children:"Tables"}),t.jsx(c.SP,{value:"connections",children:"Connections"}),t.jsx(c.SP,{value:"performance",children:"Performance"}),t.jsx(c.SP,{value:"backup",children:"Backup & Restore"})]}),t.jsx(c.nU,{value:"overview",className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,t.jsxs)(i.Zb,{children:[(0,t.jsxs)(i.Ol,{children:[(0,t.jsxs)(i.ll,{className:"flex items-center",children:[t.jsx(p.Z,{className:"h-5 w-5 mr-2"}),"Database Statistics"]}),t.jsx(i.SZ,{children:"Key database metrics and health indicators"})]}),t.jsx(i.aY,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx("span",{className:"text-sm font-medium",children:"Storage Usage"}),t.jsx("span",{className:"text-sm",children:"68%"})]}),t.jsx(d.E,{value:68,className:"h-2"}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx("span",{className:"text-sm font-medium",children:"Connection Pool"}),t.jsx("span",{className:"text-sm",children:"24/100"})]}),t.jsx(d.E,{value:24,className:"h-2"}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx("span",{className:"text-sm font-medium",children:"Query Cache"}),t.jsx("span",{className:"text-sm",children:"94.2%"})]}),t.jsx(d.E,{value:94.2,className:"h-2"})]})})]}),(0,t.jsxs)(i.Zb,{children:[(0,t.jsxs)(i.Ol,{children:[(0,t.jsxs)(i.ll,{className:"flex items-center",children:[t.jsx(u.Z,{className:"h-5 w-5 mr-2"}),"Real-time Activity"]}),t.jsx(i.SZ,{children:"Current database activity and operations"})]}),t.jsx(i.aY,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),t.jsx("span",{className:"text-sm",children:"Queries/sec"})]}),t.jsx("span",{className:"text-sm font-medium",children:"145"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),t.jsx("span",{className:"text-sm",children:"Transactions/sec"})]}),t.jsx("span",{className:"text-sm font-medium",children:"89"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx("div",{className:"w-2 h-2 bg-yellow-500 rounded-full"}),t.jsx("span",{className:"text-sm",children:"Slow queries"})]}),t.jsx("span",{className:"text-sm font-medium",children:"3"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),t.jsx("span",{className:"text-sm",children:"Failed queries"})]}),t.jsx("span",{className:"text-sm font-medium",children:"0"})]})]})})]})]})}),t.jsx(c.nU,{value:"tables",className:"space-y-4",children:(0,t.jsxs)(i.Zb,{children:[(0,t.jsxs)(i.Ol,{children:[t.jsx(i.ll,{children:"Database Tables"}),t.jsx(i.SZ,{children:"Overview of all database tables and their status"})]}),t.jsx(i.aY,{children:(0,t.jsxs)(o.iA,{children:[t.jsx(o.xD,{children:(0,t.jsxs)(o.SC,{children:[t.jsx(o.ss,{children:"Table Name"}),t.jsx(o.ss,{children:"Rows"}),t.jsx(o.ss,{children:"Size"}),t.jsx(o.ss,{children:"Last Updated"}),t.jsx(o.ss,{children:"Status"}),t.jsx(o.ss,{children:"Actions"})]})}),t.jsx(o.RM,{children:D.map(e=>(0,t.jsxs)(o.SC,{children:[t.jsx(o.pj,{className:"font-medium",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(j.Z,{className:"h-4 w-4"}),t.jsx("span",{children:e.name})]})}),t.jsx(o.pj,{children:e.rows.toLocaleString()}),t.jsx(o.pj,{children:e.size}),t.jsx(o.pj,{children:(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[t.jsx(f.Z,{className:"h-3 w-3"}),t.jsx("span",{className:"text-sm",children:e.lastUpdated.toLocaleTimeString()})]})}),t.jsx(o.pj,{children:t.jsx(n.C,{variant:R(e.status),className:"capitalize",children:e.status})}),t.jsx(o.pj,{children:(0,t.jsxs)("div",{className:"flex space-x-1",children:[t.jsx(l.z,{size:"sm",variant:"outline",children:t.jsx(v.Z,{className:"h-3 w-3"})}),t.jsx(l.z,{size:"sm",variant:"outline",children:t.jsx(b.Z,{className:"h-3 w-3"})})]})})]},e.name))})]})})]})}),t.jsx(c.nU,{value:"connections",className:"space-y-4",children:(0,t.jsxs)(i.Zb,{children:[(0,t.jsxs)(i.Ol,{children:[t.jsx(i.ll,{children:"Active Database Connections"}),t.jsx(i.SZ,{children:"Current active connections to the database"})]}),t.jsx(i.aY,{children:(0,t.jsxs)(o.iA,{children:[t.jsx(o.xD,{children:(0,t.jsxs)(o.SC,{children:[t.jsx(o.ss,{children:"User"}),t.jsx(o.ss,{children:"Database"}),t.jsx(o.ss,{children:"Host"}),t.jsx(o.ss,{children:"State"}),t.jsx(o.ss,{children:"Duration"}),t.jsx(o.ss,{children:"Query"}),t.jsx(o.ss,{children:"Actions"})]})}),t.jsx(o.RM,{children:Z.map(e=>(0,t.jsxs)(o.SC,{children:[t.jsx(o.pj,{className:"font-medium",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(N.Z,{className:"h-4 w-4"}),t.jsx("span",{children:e.user})]})}),t.jsx(o.pj,{children:e.database}),t.jsx(o.pj,{children:e.host}),t.jsx(o.pj,{children:t.jsx(n.C,{variant:T(e.state),className:"capitalize",children:e.state})}),t.jsx(o.pj,{children:e.duration}),t.jsx(o.pj,{children:t.jsx("div",{className:"max-w-xs truncate text-sm",children:e.query||"No active query"})}),t.jsx(o.pj,{children:t.jsx(l.z,{size:"sm",variant:"outline",children:t.jsx(y.Z,{className:"h-3 w-3"})})})]},e.id))})]})})]})}),t.jsx(c.nU,{value:"performance",className:"space-y-4",children:(0,t.jsxs)(i.Zb,{children:[(0,t.jsxs)(i.Ol,{children:[(0,t.jsxs)(i.ll,{className:"flex items-center",children:[t.jsx(g.Z,{className:"h-5 w-5 mr-2"}),"Query Performance"]}),t.jsx(i.SZ,{children:"Database query performance metrics and optimization"})]}),t.jsx(i.aY,{children:(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-3",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx("div",{className:"text-sm font-medium",children:"Average Query Time"}),t.jsx("div",{className:"text-2xl font-bold",children:"89ms"}),t.jsx("div",{className:"text-xs text-muted-foreground",children:"+12ms from yesterday"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx("div",{className:"text-sm font-medium",children:"Slow Queries"}),t.jsx("div",{className:"text-2xl font-bold",children:"3"}),t.jsx("div",{className:"text-xs text-muted-foreground",children:"Queries > 1000ms"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx("div",{className:"text-sm font-medium",children:"Index Usage"}),t.jsx("div",{className:"text-2xl font-bold",children:"96.8%"}),t.jsx("div",{className:"text-xs text-muted-foreground",children:"Queries using indexes"})]})]})})]})}),t.jsx(c.nU,{value:"backup",className:"space-y-4",children:(0,t.jsxs)(i.Zb,{children:[(0,t.jsxs)(i.Ol,{children:[(0,t.jsxs)(i.ll,{className:"flex items-center",children:[t.jsx(w.Z,{className:"h-5 w-5 mr-2"}),"Backup & Restore"]}),t.jsx(i.SZ,{children:"Database backup status and restore operations"})]}),t.jsx(i.aY,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx("div",{className:"text-sm font-medium",children:"Last Backup"}),t.jsx("div",{className:"text-lg font-bold",children:"2 hours ago"}),t.jsx("div",{className:"text-xs text-muted-foreground",children:"Daily backup completed successfully"}),t.jsx(n.C,{variant:"default",children:"Success"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx("div",{className:"text-sm font-medium",children:"Backup Size"}),t.jsx("div",{className:"text-lg font-bold",children:"1.8 GB"}),t.jsx("div",{className:"text-xs text-muted-foreground",children:"Compressed backup file size"})]})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsxs)(l.z,{children:[t.jsx(b.Z,{className:"h-4 w-4 mr-2"}),"Create Backup"]}),(0,t.jsxs)(l.z,{variant:"outline",children:[t.jsx(k.Z,{className:"h-4 w-4 mr-2"}),"Restore from Backup"]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx("div",{className:"text-sm font-medium",children:"Backup Schedule"}),t.jsx("div",{className:"text-sm text-muted-foreground",children:"Daily backups at 2:00 AM UTC"}),t.jsx("div",{className:"text-sm text-muted-foreground",children:"Weekly full backups on Sundays"}),t.jsx("div",{className:"text-sm text-muted-foreground",children:"Retention: 30 days for daily, 12 weeks for weekly"})]})]})})]})})]})]})}},76461:(e,s,a)=>{"use strict";a.d(s,{E:()=>y});var t=a(95344),r=a(3729),i=a(98462),n=a(62409),l="Progress",[d,c]=(0,i.b)(l),[o,x]=d(l),m=r.forwardRef((e,s)=>{var a,r;let{__scopeProgress:i,value:l=null,max:d,getValueLabel:c=p,...x}=e;(d||0===d)&&!v(d)&&console.error((a=`${d}`,`Invalid prop \`max\` of value \`${a}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let m=v(d)?d:100;null===l||b(l,m)||console.error((r=`${l}`,`Invalid prop \`value\` of value \`${r}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let u=b(l,m)?l:null,h=f(u)?c(u,m):void 0;return(0,t.jsx)(o,{scope:i,value:u,max:m,children:(0,t.jsx)(n.WV.div,{"aria-valuemax":m,"aria-valuemin":0,"aria-valuenow":f(u)?u:void 0,"aria-valuetext":h,role:"progressbar","data-state":j(u,m),"data-value":u??void 0,"data-max":m,...x,ref:s})})});m.displayName=l;var u="ProgressIndicator",h=r.forwardRef((e,s)=>{let{__scopeProgress:a,...r}=e,i=x(u,a);return(0,t.jsx)(n.WV.div,{"data-state":j(i.value,i.max),"data-value":i.value??void 0,"data-max":i.max,...r,ref:s})});function p(e,s){return`${Math.round(e/s*100)}%`}function j(e,s){return null==e?"indeterminate":e===s?"complete":"loading"}function f(e){return"number"==typeof e}function v(e){return f(e)&&!isNaN(e)&&e>0}function b(e,s){return f(e)&&!isNaN(e)&&e<=s&&e>=0}h.displayName=u;var N=a(91626);let y=r.forwardRef(({className:e,value:s,...a},r)=>t.jsx(m,{ref:r,className:(0,N.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...a,children:t.jsx(h,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(s||0)}%)`}})}));y.displayName=m.displayName},81036:(e,s,a)=>{"use strict";a.d(s,{RM:()=>d,SC:()=>c,iA:()=>n,pj:()=>x,ss:()=>o,xD:()=>l});var t=a(95344),r=a(3729),i=a(91626);let n=r.forwardRef(({className:e,...s},a)=>t.jsx("div",{className:"relative w-full overflow-auto",children:t.jsx("table",{ref:a,className:(0,i.cn)("w-full caption-bottom text-sm",e),...s})}));n.displayName="Table";let l=r.forwardRef(({className:e,...s},a)=>t.jsx("thead",{ref:a,className:(0,i.cn)("[&_tr]:border-b",e),...s}));l.displayName="TableHeader";let d=r.forwardRef(({className:e,...s},a)=>t.jsx("tbody",{ref:a,className:(0,i.cn)("[&_tr:last-child]:border-0",e),...s}));d.displayName="TableBody",r.forwardRef(({className:e,...s},a)=>t.jsx("tfoot",{ref:a,className:(0,i.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...s})).displayName="TableFooter";let c=r.forwardRef(({className:e,...s},a)=>t.jsx("tr",{ref:a,className:(0,i.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...s}));c.displayName="TableRow";let o=r.forwardRef(({className:e,...s},a)=>t.jsx("th",{ref:a,className:(0,i.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...s}));o.displayName="TableHead";let x=r.forwardRef(({className:e,...s},a)=>t.jsx("td",{ref:a,className:(0,i.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...s}));x.displayName="TableCell",r.forwardRef(({className:e,...s},a)=>t.jsx("caption",{ref:a,className:(0,i.cn)("mt-4 text-sm text-muted-foreground",e),...s})).displayName="TableCaption"},25757:(e,s,a)=>{"use strict";a.d(s,{mQ:()=>P,nU:()=>_,dr:()=>R,SP:()=>T});var t=a(95344),r=a(3729),i=a(85222),n=a(98462),l=a(34504),d=a(43234),c=a(62409),o=a(3975),x=a(33183),m=a(99048),u="Tabs",[h,p]=(0,n.b)(u,[l.Pc]),j=(0,l.Pc)(),[f,v]=h(u),b=r.forwardRef((e,s)=>{let{__scopeTabs:a,value:r,onValueChange:i,defaultValue:n,orientation:l="horizontal",dir:d,activationMode:h="automatic",...p}=e,j=(0,o.gm)(d),[v,b]=(0,x.T)({prop:r,onChange:i,defaultProp:n??"",caller:u});return(0,t.jsx)(f,{scope:a,baseId:(0,m.M)(),value:v,onValueChange:b,orientation:l,dir:j,activationMode:h,children:(0,t.jsx)(c.WV.div,{dir:j,"data-orientation":l,...p,ref:s})})});b.displayName=u;var N="TabsList",y=r.forwardRef((e,s)=>{let{__scopeTabs:a,loop:r=!0,...i}=e,n=v(N,a),d=j(a);return(0,t.jsx)(l.fC,{asChild:!0,...d,orientation:n.orientation,dir:n.dir,loop:r,children:(0,t.jsx)(c.WV.div,{role:"tablist","aria-orientation":n.orientation,...i,ref:s})})});y.displayName=N;var g="TabsTrigger",w=r.forwardRef((e,s)=>{let{__scopeTabs:a,value:r,disabled:n=!1,...d}=e,o=v(g,a),x=j(a),m=C(o.baseId,r),u=D(o.baseId,r),h=r===o.value;return(0,t.jsx)(l.ck,{asChild:!0,...x,focusable:!n,active:h,children:(0,t.jsx)(c.WV.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":u,"data-state":h?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:m,...d,ref:s,onMouseDown:(0,i.M)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():o.onValueChange(r)}),onKeyDown:(0,i.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&o.onValueChange(r)}),onFocus:(0,i.M)(e.onFocus,()=>{let e="manual"!==o.activationMode;h||n||!e||o.onValueChange(r)})})})});w.displayName=g;var k="TabsContent",S=r.forwardRef((e,s)=>{let{__scopeTabs:a,value:i,forceMount:n,children:l,...o}=e,x=v(k,a),m=C(x.baseId,i),u=D(x.baseId,i),h=i===x.value,p=r.useRef(h);return r.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,t.jsx)(d.z,{present:n||h,children:({present:a})=>(0,t.jsx)(c.WV.div,{"data-state":h?"active":"inactive","data-orientation":x.orientation,role:"tabpanel","aria-labelledby":m,hidden:!a,id:u,tabIndex:0,...o,ref:s,style:{...e.style,animationDuration:p.current?"0s":void 0},children:a&&l})})});function C(e,s){return`${e}-trigger-${s}`}function D(e,s){return`${e}-content-${s}`}S.displayName=k;var Z=a(91626);let P=b,R=r.forwardRef(({className:e,...s},a)=>t.jsx(y,{ref:a,className:(0,Z.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s}));R.displayName=y.displayName;let T=r.forwardRef(({className:e,...s},a)=>t.jsx(w,{ref:a,className:(0,Z.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s}));T.displayName=w.displayName;let _=r.forwardRef(({className:e,...s},a)=>t.jsx(S,{ref:a,className:(0,Z.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));_.displayName=S.displayName},96885:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},73557:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("HardDrive",[["line",{x1:"22",x2:"2",y1:"12",y2:"12",key:"1y58io"}],["path",{d:"M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z",key:"oot6mr"}],["line",{x1:"6",x2:"6.01",y1:"16",y2:"16",key:"sgf278"}],["line",{x1:"10",x2:"10.01",y1:"16",y2:"16",key:"1l4acy"}]])},38271:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},3380:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},8644:(e,s,a)=>{"use strict";a.r(s),a.d(s,{$$typeof:()=>i,__esModule:()=>r,default:()=>n});let t=(0,a(86843).createProxy)(String.raw`C:\proj\nextjs-saas\app\super-admin\database\page.tsx`),{__esModule:r,$$typeof:i}=t,n=t.default}};var s=require("../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[1638,7948,6671,4626,7792,2506,1729,2125,3965],()=>a(57979));module.exports=t})();