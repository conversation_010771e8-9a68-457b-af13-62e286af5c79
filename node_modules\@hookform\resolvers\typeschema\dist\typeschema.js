var e=require("@hookform/resolvers"),r=require("react-hook-form");exports.typeschemaResolver=function(s,t,a){return void 0===a&&(a={}),function(t,i,o){try{var n=function(){if(u.issues){var s=function(e,s){for(var t=Object.assign([],e),a={};t.length;){var i=e[0];if(i.path){var o=i.path.join(".");if(a[o]||(a[o]={message:i.message,type:""}),s){var n=a[o].types,u=n&&n[""];a[o]=r.appendErrors(o,s,a,"",u?[].concat(u,i.message):i.message)}t.shift()}}return a}(u.issues,!o.shouldUseNativeValidation&&"all"===o.criteriaMode);return{values:{},errors:e.toNestErrors(s,o)}}return o.shouldUseNativeValidation&&e.validateFieldsNatively({},o),{values:a.raw?Object.assign({},t):u.value,errors:{}}},u=s["~standard"].validate(t),v=function(){if(u instanceof Promise)return Promise.resolve(u).then(function(e){u=e})}();return Promise.resolve(v&&v.then?v.then(n):n())}catch(e){return Promise.reject(e)}}};
//# sourceMappingURL=typeschema.js.map
