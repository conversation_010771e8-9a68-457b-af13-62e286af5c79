(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2456],{43149:function(e,t,s){Promise.resolve().then(s.bind(s,26108))},26108:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return C}});var r=s(57437),a=s(2265),l=s(82749),n=s(24033),i=s(27815),d=s(85754),c=s(45179),o=s(31478),u=s(50660),x=s(19160),m=s(45509),f=s(42706),h=s(67972),p=s(90998),j=s(72894),g=s(74056),N=s(76637),y=s(64280),v=s(35817),b=s(49036),w=s(41827),R=s(99670);function C(){var e,t,s,C;let{data:A,status:k}=(0,l.useSession)(),[T,S]=(0,a.useState)([]),[E,Z]=(0,a.useState)(null),[I,L]=(0,a.useState)(!0),[z,V]=(0,a.useState)(""),[_,D]=(0,a.useState)("all"),[O,H]=(0,a.useState)("all"),[Y,Q]=(0,a.useState)("all"),[U,P]=(0,a.useState)(1),[M,B]=(0,a.useState)(1),[F,G]=(0,a.useState)(null);if("loading"===k)return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})});"unauthenticated"===k&&(0,n.redirect)("/auth/signin"),(null==A?void 0:null===(e=A.user)||void 0===e?void 0:e.role)!=="SUPER_ADMIN"&&(0,n.redirect)("/dashboard");let $=async()=>{try{L(!0);let e=new URLSearchParams({page:U.toString(),limit:"50",...z&&{search:z},..._&&"all"!==_&&{action:_},...O&&"all"!==O&&{severity:O},...Y&&"all"!==Y&&{entityType:Y}}),t=await fetch("/api/super-admin/audit-logs?".concat(e));if(!t.ok)throw Error("Failed to fetch audit logs");let s=await t.json();S(s.auditLogs),Z(s.stats),B(s.pagination.pages)}catch(e){console.error("Error fetching audit logs:",e)}finally{L(!1)}};(0,a.useEffect)(()=>{$()},[U,z,_,O,Y]);let q=e=>(0,r.jsx)(o.C,{variant:{LOW:"outline",INFO:"default",MEDIUM:"secondary",HIGH:"destructive",CRITICAL:"destructive"}[e]||"default",children:(0,r.jsx)("span",{className:{LOW:"text-gray-600",INFO:"text-blue-600",MEDIUM:"text-yellow-600",HIGH:"text-orange-600",CRITICAL:"text-red-600"}[e]||"text-gray-600",children:e})}),W=e=>new Date(e).toLocaleString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",second:"2-digit"}),J=e=>e.includes("LOGIN")||e.includes("AUTH")?h.Z:e.includes("CREATE")||e.includes("UPDATE")||e.includes("DELETE")?p.Z:e.includes("SECURITY")||e.includes("ALERT")?j.Z:g.Z;return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(N.Z,{className:"h-8 w-8 text-blue-600"}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Audit Logs"})]}),(0,r.jsx)("p",{className:"text-gray-500 mt-1",children:"Track all system activities and changes"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)(d.z,{variant:"outline",onClick:$,disabled:I,children:[(0,r.jsx)(y.Z,{className:"h-4 w-4 mr-2 ".concat(I?"animate-spin":"")}),"Refresh"]}),(0,r.jsxs)(d.z,{variant:"outline",children:[(0,r.jsx)(v.Z,{className:"h-4 w-4 mr-2"}),"Export"]})]})]}),E&&(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,r.jsx)(i.Zb,{children:(0,r.jsx)(i.aY,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Total Logs"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:E.total.toLocaleString()})]}),(0,r.jsx)(N.Z,{className:"h-8 w-8 text-blue-600"})]})})}),(0,r.jsx)(i.Zb,{children:(0,r.jsx)(i.aY,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Last 24 Hours"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-green-600",children:E.last24Hours})]}),(0,r.jsx)(p.Z,{className:"h-8 w-8 text-green-600"})]})})}),(0,r.jsx)(i.Zb,{children:(0,r.jsx)(i.aY,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Critical Events"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-red-600",children:(null===(t=E.bySeverity.find(e=>"CRITICAL"===e.severity))||void 0===t?void 0:t.count)||0})]}),(0,r.jsx)(j.Z,{className:"h-8 w-8 text-red-600"})]})})}),(0,r.jsx)(i.Zb,{children:(0,r.jsxs)(i.aY,{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Top Action"}),(0,r.jsx)("p",{className:"text-lg font-bold text-purple-600",children:(null===(s=E.byAction[0])||void 0===s?void 0:s.action)||"N/A"})]}),(0,r.jsx)(b.Z,{className:"h-8 w-8 text-purple-600"})]}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[(null===(C=E.byAction[0])||void 0===C?void 0:C.count)||0," occurrences"]})]})})]}),(0,r.jsx)(i.Zb,{children:(0,r.jsx)(i.aY,{className:"p-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4",children:[(0,r.jsx)("div",{className:"lg:col-span-2",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(w.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,r.jsx)(c.I,{placeholder:"Search logs...",value:z,onChange:e=>V(e.target.value),className:"pl-10"})]})}),(0,r.jsxs)(m.Ph,{value:_,onValueChange:D,children:[(0,r.jsx)(m.i4,{children:(0,r.jsx)(m.ki,{placeholder:"All Actions"})}),(0,r.jsxs)(m.Bw,{children:[(0,r.jsx)(m.Ql,{value:"all",children:"All Actions"}),null==E?void 0:E.byAction.slice(0,10).map(e=>(0,r.jsxs)(m.Ql,{value:e.action,children:[e.action," (",e.count,")"]},e.action))]})]}),(0,r.jsxs)(m.Ph,{value:O,onValueChange:H,children:[(0,r.jsx)(m.i4,{children:(0,r.jsx)(m.ki,{placeholder:"All Severities"})}),(0,r.jsxs)(m.Bw,{children:[(0,r.jsx)(m.Ql,{value:"all",children:"All Severities"}),(0,r.jsx)(m.Ql,{value:"LOW",children:"Low"}),(0,r.jsx)(m.Ql,{value:"INFO",children:"Info"}),(0,r.jsx)(m.Ql,{value:"MEDIUM",children:"Medium"}),(0,r.jsx)(m.Ql,{value:"HIGH",children:"High"}),(0,r.jsx)(m.Ql,{value:"CRITICAL",children:"Critical"})]})]}),(0,r.jsxs)(m.Ph,{value:Y,onValueChange:Q,children:[(0,r.jsx)(m.i4,{children:(0,r.jsx)(m.ki,{placeholder:"All Entities"})}),(0,r.jsxs)(m.Bw,{children:[(0,r.jsx)(m.Ql,{value:"all",children:"All Entities"}),null==E?void 0:E.byEntityType.slice(0,10).map(e=>(0,r.jsxs)(m.Ql,{value:e.entityType,children:[e.entityType," (",e.count,")"]},e.entityType))]})]})]})})}),(0,r.jsxs)(i.Zb,{children:[(0,r.jsx)(i.Ol,{children:(0,r.jsxs)(i.ll,{children:["Audit Logs (",T.length,")"]})}),(0,r.jsx)(i.aY,{children:I?(0,r.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)(x.iA,{children:[(0,r.jsx)(x.xD,{children:(0,r.jsxs)(x.SC,{children:[(0,r.jsx)(x.ss,{children:"Action"}),(0,r.jsx)(x.ss,{children:"User"}),(0,r.jsx)(x.ss,{children:"Entity"}),(0,r.jsx)(x.ss,{children:"Severity"}),(0,r.jsx)(x.ss,{children:"IP Address"}),(0,r.jsx)(x.ss,{children:"Timestamp"}),(0,r.jsx)(x.ss,{children:"Actions"})]})}),(0,r.jsx)(x.RM,{children:T.map(e=>{var t,s;let a=J(e.action);return(0,r.jsxs)(x.SC,{children:[(0,r.jsx)(x.pj,{children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(a,{className:"h-4 w-4 text-gray-500"}),(0,r.jsx)("span",{className:"font-medium",children:e.action})]})}),(0,r.jsx)(x.pj,{children:e.user?(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)(u.qE,{className:"h-6 w-6",children:[(0,r.jsx)(u.F$,{src:e.user.avatar,alt:e.user.name}),(0,r.jsx)(u.Q5,{children:(null===(s=e.user.name)||void 0===s?void 0:null===(t=s.charAt(0))||void 0===t?void 0:t.toUpperCase())||"U"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-sm",children:e.user.name}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:e.userRole})]})]}):(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-sm",children:e.userEmail||"System"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:e.userRole||"N/A"})]})}),(0,r.jsx)(x.pj,{children:(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-sm",children:e.entityType}),e.entityId&&(0,r.jsxs)("p",{className:"text-xs text-gray-500 font-mono",children:[e.entityId.slice(0,8),"..."]})]})}),(0,r.jsx)(x.pj,{children:q(e.severity)}),(0,r.jsx)(x.pj,{children:(0,r.jsx)("span",{className:"font-mono text-sm",children:e.ipAddress||"N/A"})}),(0,r.jsx)(x.pj,{children:(0,r.jsx)("div",{className:"text-sm",children:(0,r.jsx)("p",{children:W(e.createdAt)})})}),(0,r.jsx)(x.pj,{children:(0,r.jsxs)(f.Vq,{children:[(0,r.jsx)(f.hg,{asChild:!0,children:(0,r.jsx)(d.z,{variant:"ghost",size:"sm",onClick:()=>G(e),children:(0,r.jsx)(R.Z,{className:"h-4 w-4"})})}),(0,r.jsxs)(f.cZ,{className:"max-w-2xl",children:[(0,r.jsxs)(f.fK,{children:[(0,r.jsx)(f.$N,{children:"Audit Log Details"}),(0,r.jsx)(f.Be,{children:"Detailed information about this audit log entry"})]}),F&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Action"}),(0,r.jsx)("p",{className:"text-sm",children:F.action})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Severity"}),(0,r.jsx)("div",{className:"mt-1",children:q(F.severity)})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Entity Type"}),(0,r.jsx)("p",{className:"text-sm",children:F.entityType})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Entity ID"}),(0,r.jsx)("p",{className:"text-sm font-mono",children:F.entityId||"N/A"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"IP Address"}),(0,r.jsx)("p",{className:"text-sm font-mono",children:F.ipAddress||"N/A"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Timestamp"}),(0,r.jsx)("p",{className:"text-sm",children:W(F.createdAt)})]})]}),F.oldValues&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Old Values"}),(0,r.jsx)("pre",{className:"text-xs bg-gray-100 p-3 rounded mt-1 overflow-auto",children:JSON.stringify(F.oldValues,null,2)})]}),F.newValues&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"New Values"}),(0,r.jsx)("pre",{className:"text-xs bg-gray-100 p-3 rounded mt-1 overflow-auto",children:JSON.stringify(F.newValues,null,2)})]}),F.metadata&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Metadata"}),(0,r.jsx)("pre",{className:"text-xs bg-gray-100 p-3 rounded mt-1 overflow-auto",children:JSON.stringify(F.metadata,null,2)})]})]})]})]})})]},e.id)})})]})})})]}),M>1&&(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["Page ",U," of ",M]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(d.z,{variant:"outline",onClick:()=>P(U-1),disabled:1===U,children:"Previous"}),(0,r.jsx)(d.z,{variant:"outline",onClick:()=>P(U+1),disabled:U===M,children:"Next"})]})]})]})}},50660:function(e,t,s){"use strict";s.d(t,{F$:function(){return d},Q5:function(){return c},qE:function(){return i}});var r=s(57437),a=s(2265),l=s(61465),n=s(1657);let i=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(l.fC,{ref:t,className:(0,n.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",s),...a})});i.displayName=l.fC.displayName;let d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(l.Ee,{ref:t,className:(0,n.cn)("aspect-square h-full w-full",s),...a})});d.displayName=l.Ee.displayName;let c=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(l.NY,{ref:t,className:(0,n.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",s),...a})});c.displayName=l.NY.displayName},31478:function(e,t,s){"use strict";s.d(t,{C:function(){return i}});var r=s(57437);s(2265);var a=s(96061),l=s(1657);let n=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600",info:"border-transparent bg-blue-500 text-white hover:bg-blue-600"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:s,...a}=e;return(0,r.jsx)("div",{className:(0,l.cn)(n({variant:s}),t),...a})}},85754:function(e,t,s){"use strict";s.d(t,{z:function(){return c}});var r=s(57437),a=s(2265),l=s(67256),n=s(96061),i=s(1657);let d=(0,n.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,t)=>{let{className:s,variant:a,size:n,asChild:c=!1,...o}=e,u=c?l.g7:"button";return(0,r.jsx)(u,{className:(0,i.cn)(d({variant:a,size:n,className:s})),ref:t,...o})});c.displayName="Button"},27815:function(e,t,s){"use strict";s.d(t,{Ol:function(){return i},SZ:function(){return c},Zb:function(){return n},aY:function(){return o},eW:function(){return u},ll:function(){return d}});var r=s(57437),a=s(2265),l=s(1657);let n=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...a})});n.displayName="Card";let i=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",s),...a})});i.displayName="CardHeader";let d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("h3",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",s),...a})});d.displayName="CardTitle";let c=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("p",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",s),...a})});c.displayName="CardDescription";let o=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",s),...a})});o.displayName="CardContent";let u=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",s),...a})});u.displayName="CardFooter"},42706:function(e,t,s){"use strict";s.d(t,{$N:function(){return h},Be:function(){return p},Vq:function(){return d},cN:function(){return f},cZ:function(){return x},fK:function(){return m},hg:function(){return c},t9:function(){return u}});var r=s(57437),a=s(2265),l=s(28712),n=s(82549),i=s(1657);let d=l.fC,c=l.xz,o=l.h_;l.x8;let u=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(l.aV,{ref:t,className:(0,i.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...a})});u.displayName=l.aV.displayName;let x=a.forwardRef((e,t)=>{let{className:s,children:a,...d}=e;return(0,r.jsxs)(o,{children:[(0,r.jsx)(u,{}),(0,r.jsxs)(l.VY,{ref:t,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",s),...d,children:[a,(0,r.jsxs)(l.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,r.jsx)(n.Z,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});x.displayName=l.VY.displayName;let m=e=>{let{className:t,...s}=e;return(0,r.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...s})};m.displayName="DialogHeader";let f=e=>{let{className:t,...s}=e;return(0,r.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...s})};f.displayName="DialogFooter";let h=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(l.Dx,{ref:t,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",s),...a})});h.displayName=l.Dx.displayName;let p=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(l.dk,{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",s),...a})});p.displayName=l.dk.displayName},45179:function(e,t,s){"use strict";s.d(t,{I:function(){return n}});var r=s(57437),a=s(2265),l=s(1657);let n=a.forwardRef((e,t)=>{let{className:s,type:a,...n}=e;return(0,r.jsx)("input",{type:a,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:t,...n})});n.displayName="Input"},45509:function(e,t,s){"use strict";s.d(t,{Bw:function(){return h},Ph:function(){return o},Ql:function(){return p},i4:function(){return x},ki:function(){return u}});var r=s(57437),a=s(2265),l=s(99530),n=s(83523),i=s(9224),d=s(62442),c=s(1657);let o=l.fC;l.ZA;let u=l.B4,x=a.forwardRef((e,t)=>{let{className:s,children:a,...i}=e;return(0,r.jsxs)(l.xz,{ref:t,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",s),...i,children:[a,(0,r.jsx)(l.JO,{asChild:!0,children:(0,r.jsx)(n.Z,{className:"h-4 w-4 opacity-50"})})]})});x.displayName=l.xz.displayName;let m=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(l.u_,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",s),...a,children:(0,r.jsx)(i.Z,{className:"h-4 w-4"})})});m.displayName=l.u_.displayName;let f=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(l.$G,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",s),...a,children:(0,r.jsx)(n.Z,{className:"h-4 w-4"})})});f.displayName=l.$G.displayName;let h=a.forwardRef((e,t)=>{let{className:s,children:a,position:n="popper",...i}=e;return(0,r.jsx)(l.h_,{children:(0,r.jsxs)(l.VY,{ref:t,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:n,...i,children:[(0,r.jsx)(m,{}),(0,r.jsx)(l.l_,{className:(0,c.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),(0,r.jsx)(f,{})]})})});h.displayName=l.VY.displayName,a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(l.__,{ref:t,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",s),...a})}).displayName=l.__.displayName;let p=a.forwardRef((e,t)=>{let{className:s,children:a,...n}=e;return(0,r.jsxs)(l.ck,{ref:t,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...n,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(l.wU,{children:(0,r.jsx)(d.Z,{className:"h-4 w-4"})})}),(0,r.jsx)(l.eT,{children:a})]})});p.displayName=l.ck.displayName,a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(l.Z0,{ref:t,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",s),...a})}).displayName=l.Z0.displayName},19160:function(e,t,s){"use strict";s.d(t,{RM:function(){return d},SC:function(){return c},iA:function(){return n},pj:function(){return u},ss:function(){return o},xD:function(){return i}});var r=s(57437),a=s(2265),l=s(1657);let n=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{className:"relative w-full overflow-auto",children:(0,r.jsx)("table",{ref:t,className:(0,l.cn)("w-full caption-bottom text-sm",s),...a})})});n.displayName="Table";let i=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("thead",{ref:t,className:(0,l.cn)("[&_tr]:border-b",s),...a})});i.displayName="TableHeader";let d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("tbody",{ref:t,className:(0,l.cn)("[&_tr:last-child]:border-0",s),...a})});d.displayName="TableBody",a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("tfoot",{ref:t,className:(0,l.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",s),...a})}).displayName="TableFooter";let c=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("tr",{ref:t,className:(0,l.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",s),...a})});c.displayName="TableRow";let o=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("th",{ref:t,className:(0,l.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",s),...a})});o.displayName="TableHead";let u=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("td",{ref:t,className:(0,l.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",s),...a})});u.displayName="TableCell",a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("caption",{ref:t,className:(0,l.cn)("mt-4 text-sm text-muted-foreground",s),...a})}).displayName="TableCaption"},1657:function(e,t,s){"use strict";s.d(t,{cn:function(){return l}});var r=s(57042),a=s(74769);function l(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.m6)((0,r.W)(t))}}},function(e){e.O(0,[6723,9502,2749,1706,4138,4997,8741,2971,4938,1744],function(){return e(e.s=43149)}),_N_E=e.O()}]);