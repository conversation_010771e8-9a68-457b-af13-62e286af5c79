(()=>{var e={};e.id=2461,e.ids=[2461],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},24513:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>u,tree:()=>c});var a=t(50482),r=t(69108),i=t(62563),l=t.n(i),n=t(68300),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let c=["",{children:["super-admin",{children:["alerts",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,61635)),"C:\\proj\\nextjs-saas\\app\\super-admin\\alerts\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,11285)),"C:\\proj\\nextjs-saas\\app\\super-admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,59504)),"C:\\proj\\nextjs-saas\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\proj\\nextjs-saas\\app\\super-admin\\alerts\\page.tsx"],x="/super-admin/alerts/page",m={require:t,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/super-admin/alerts/page",pathname:"/super-admin/alerts",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},92635:(e,s,t)=>{Promise.resolve().then(t.bind(t,29622))},29622:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>A});var a=t(95344),r=t(3729),i=t(61351),l=t(69436),n=t(16212),d=t(71809),c=t(1586),o=t(25757),x=t(81036),m=t(17470),u=t(75203),h=t(23485),f=t(79200),p=t(89895),j=t(99046),y=t(30304),v=t(45961),g=t(33733),N=t(33037),b=t(73229),w=t(7060),k=t(17418),C=t(25545),S=t(53148),Z=t(1222);function A(){let[e,s]=(0,r.useState)(!0),[t,A]=(0,r.useState)(new Date),[R,P]=(0,r.useState)("all"),[D,_]=(0,r.useState)("all"),[M,T]=(0,r.useState)([{id:"1",title:"High CPU Usage Detected",description:"Server CPU usage has exceeded 90% for the last 5 minutes",type:"performance",severity:"high",status:"active",timestamp:new Date(Date.now()-3e5),source:"Performance Monitor",affectedUsers:150},{id:"2",title:"Failed Login Attempts",description:"Multiple failed login attempts detected from IP *************",type:"security",severity:"critical",status:"active",timestamp:new Date(Date.now()-6e5),source:"Security Monitor"},{id:"3",title:"Database Connection Pool Full",description:"Database connection pool has reached maximum capacity",type:"database",severity:"medium",status:"acknowledged",timestamp:new Date(Date.now()-9e5),source:"Database Monitor",affectedUsers:50},{id:"4",title:"Disk Space Low",description:"Server disk space is below 10% on /var partition",type:"system",severity:"medium",status:"resolved",timestamp:new Date(Date.now()-18e5),source:"System Monitor"},{id:"5",title:"Unusual User Activity",description:"User <EMAIL> accessed sensitive data outside normal hours",type:"user",severity:"low",status:"dismissed",timestamp:new Date(Date.now()-27e5),source:"User Activity Monitor"}]),[z]=(0,r.useState)([{id:"1",name:"High CPU Usage",description:"Alert when CPU usage exceeds threshold",type:"performance",enabled:!0,threshold:"85%",actions:["email","slack"]},{id:"2",name:"Failed Login Attempts",description:"Alert on multiple failed login attempts",type:"security",enabled:!0,threshold:"5 attempts",actions:["email","sms"]},{id:"3",name:"Database Errors",description:"Alert on database connection errors",type:"database",enabled:!0,threshold:"10 errors/min",actions:["email"]}]);(0,r.useEffect)(()=>{let e=setTimeout(()=>{s(!1)},1e3);return()=>clearTimeout(e)},[]);let U=(e,s)=>{T(t=>t.map(t=>t.id===e?{...t,status:s}:t))},q=e=>{switch(e){case"critical":case"high":return"destructive";case"medium":return"secondary";default:return"outline"}},E=e=>{switch(e){case"active":return"destructive";case"acknowledged":return"secondary";case"resolved":return"default";default:return"outline"}},V=e=>{switch(e){case"system":return a.jsx(u.Z,{className:"h-4 w-4"});case"security":return a.jsx(h.Z,{className:"h-4 w-4"});case"performance":return a.jsx(f.Z,{className:"h-4 w-4"});case"user":return a.jsx(p.Z,{className:"h-4 w-4"});case"database":return a.jsx(j.Z,{className:"h-4 w-4"});case"network":return a.jsx(y.Z,{className:"h-4 w-4"});default:return a.jsx(v.Z,{className:"h-4 w-4"})}},O=M.filter(e=>{let s="all"===R||e.status===R,t="all"===D||e.severity===D;return s&&t}),Q={total:M.length,active:M.filter(e=>"active"===e.status).length,critical:M.filter(e=>"critical"===e.severity).length,acknowledged:M.filter(e=>"acknowledged"===e.status).length};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Alert Management"}),a.jsx("p",{className:"text-muted-foreground",children:"Monitor and manage system alerts and notifications"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(l.C,{variant:"outline",className:"text-xs",children:["Last updated: ",t.toLocaleTimeString()]}),(0,a.jsxs)(n.z,{onClick:()=>{s(!0),A(new Date),setTimeout(()=>s(!1),1e3)},disabled:e,size:"sm",children:[a.jsx(g.Z,{className:`h-4 w-4 mr-2 ${e?"animate-spin":""}`}),"Refresh"]})]})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(i.ll,{className:"text-sm font-medium",children:"Total Alerts"}),a.jsx(N.Z,{className:"h-4 w-4 text-blue-500"})]}),(0,a.jsxs)(i.aY,{children:[a.jsx("div",{className:"text-2xl font-bold",children:Q.total}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"All time alerts"})]})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(i.ll,{className:"text-sm font-medium",children:"Active Alerts"}),a.jsx(v.Z,{className:"h-4 w-4 text-red-500"})]}),(0,a.jsxs)(i.aY,{children:[a.jsx("div",{className:"text-2xl font-bold text-red-600",children:Q.active}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"Requiring attention"})]})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(i.ll,{className:"text-sm font-medium",children:"Critical Alerts"}),a.jsx(b.Z,{className:"h-4 w-4 text-red-500"})]}),(0,a.jsxs)(i.aY,{children:[a.jsx("div",{className:"text-2xl font-bold text-red-600",children:Q.critical}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"High priority"})]})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(i.ll,{className:"text-sm font-medium",children:"Acknowledged"}),a.jsx(w.Z,{className:"h-4 w-4 text-yellow-500"})]}),(0,a.jsxs)(i.aY,{children:[a.jsx("div",{className:"text-2xl font-bold text-yellow-600",children:Q.acknowledged}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"Being handled"})]})]})]}),(0,a.jsxs)(o.mQ,{defaultValue:"alerts",className:"space-y-4",children:[(0,a.jsxs)(o.dr,{children:[a.jsx(o.SP,{value:"alerts",children:"Active Alerts"}),a.jsx(o.SP,{value:"rules",children:"Alert Rules"}),a.jsx(o.SP,{value:"settings",children:"Notification Settings"}),a.jsx(o.SP,{value:"history",children:"Alert History"})]}),(0,a.jsxs)(o.nU,{value:"alerts",className:"space-y-4",children:[(0,a.jsxs)(i.Zb,{children:[a.jsx(i.Ol,{children:(0,a.jsxs)(i.ll,{className:"flex items-center",children:[a.jsx(k.Z,{className:"h-5 w-5 mr-2"}),"Filters"]})}),a.jsx(i.aY,{children:(0,a.jsxs)("div",{className:"flex space-x-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(c._,{children:"Status"}),(0,a.jsxs)(m.Ph,{value:R,onValueChange:P,children:[a.jsx(m.i4,{className:"w-40",children:a.jsx(m.ki,{})}),(0,a.jsxs)(m.Bw,{children:[a.jsx(m.Ql,{value:"all",children:"All Status"}),a.jsx(m.Ql,{value:"active",children:"Active"}),a.jsx(m.Ql,{value:"acknowledged",children:"Acknowledged"}),a.jsx(m.Ql,{value:"resolved",children:"Resolved"}),a.jsx(m.Ql,{value:"dismissed",children:"Dismissed"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(c._,{children:"Severity"}),(0,a.jsxs)(m.Ph,{value:D,onValueChange:_,children:[a.jsx(m.i4,{className:"w-40",children:a.jsx(m.ki,{})}),(0,a.jsxs)(m.Bw,{children:[a.jsx(m.Ql,{value:"all",children:"All Severity"}),a.jsx(m.Ql,{value:"critical",children:"Critical"}),a.jsx(m.Ql,{value:"high",children:"High"}),a.jsx(m.Ql,{value:"medium",children:"Medium"}),a.jsx(m.Ql,{value:"low",children:"Low"})]})]})]})]})})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{children:[a.jsx(i.ll,{children:"System Alerts"}),(0,a.jsxs)(i.SZ,{children:["Current alerts and their status (",O.length," of ",M.length," alerts)"]})]}),a.jsx(i.aY,{children:(0,a.jsxs)(x.iA,{children:[a.jsx(x.xD,{children:(0,a.jsxs)(x.SC,{children:[a.jsx(x.ss,{children:"Alert"}),a.jsx(x.ss,{children:"Type"}),a.jsx(x.ss,{children:"Severity"}),a.jsx(x.ss,{children:"Status"}),a.jsx(x.ss,{children:"Time"}),a.jsx(x.ss,{children:"Actions"})]})}),a.jsx(x.RM,{children:O.map(e=>(0,a.jsxs)(x.SC,{children:[a.jsx(x.pj,{children:(0,a.jsxs)("div",{className:"space-y-1",children:[a.jsx("div",{className:"font-medium",children:e.title}),a.jsx("div",{className:"text-xs text-muted-foreground",children:e.description}),(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Source: ",e.source]}),e.affectedUsers&&(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Affected users: ",e.affectedUsers]})]})}),a.jsx(x.pj,{children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[V(e.type),a.jsx("span",{className:"capitalize",children:e.type})]})}),a.jsx(x.pj,{children:a.jsx(l.C,{variant:q(e.severity),className:"capitalize",children:e.severity})}),a.jsx(x.pj,{children:a.jsx(l.C,{variant:E(e.status),className:"capitalize",children:e.status})}),a.jsx(x.pj,{children:(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[a.jsx(C.Z,{className:"h-3 w-3"}),a.jsx("span",{className:"text-sm",children:e.timestamp.toLocaleTimeString()})]})}),a.jsx(x.pj,{children:(0,a.jsxs)("div",{className:"flex space-x-1",children:["active"===e.status&&a.jsx(n.z,{size:"sm",variant:"outline",onClick:()=>U(e.id,"acknowledged"),children:a.jsx(S.Z,{className:"h-3 w-3"})}),"acknowledged"===e.status&&a.jsx(n.z,{size:"sm",variant:"outline",onClick:()=>U(e.id,"resolved"),children:a.jsx(w.Z,{className:"h-3 w-3"})}),a.jsx(n.z,{size:"sm",variant:"outline",onClick:()=>U(e.id,"dismissed"),children:a.jsx(Z.Z,{className:"h-3 w-3"})})]})})]},e.id))})]})})]})]}),a.jsx(o.nU,{value:"rules",className:"space-y-4",children:(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{children:[a.jsx(i.ll,{children:"Alert Rules Configuration"}),a.jsx(i.SZ,{children:"Configure when and how alerts are triggered"})]}),a.jsx(i.aY,{children:a.jsx("div",{className:"space-y-4",children:z.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[a.jsx("div",{className:"font-medium",children:e.name}),a.jsx("div",{className:"text-sm text-muted-foreground",children:e.description}),(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Threshold: ",e.threshold," | Actions: ",e.actions.join(", ")]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(l.C,{variant:"outline",className:"capitalize",children:e.type}),a.jsx(d.r,{checked:e.enabled})]})]},e.id))})})]})}),a.jsx(o.nU,{value:"settings",className:"space-y-4",children:(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{children:[a.jsx(i.ll,{children:"Notification Settings"}),a.jsx(i.SZ,{children:"Configure how you receive alert notifications"})]}),a.jsx(i.aY,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("h3",{className:"text-lg font-medium",children:"Notification Channels"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx(c._,{children:"Email Notifications"}),a.jsx("p",{className:"text-sm text-muted-foreground",children:"Receive alerts via email"})]}),a.jsx(d.r,{defaultChecked:!0})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx(c._,{children:"SMS Notifications"}),a.jsx("p",{className:"text-sm text-muted-foreground",children:"Receive critical alerts via SMS"})]}),a.jsx(d.r,{defaultChecked:!0})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx(c._,{children:"Slack Integration"}),a.jsx("p",{className:"text-sm text-muted-foreground",children:"Send alerts to Slack channels"})]}),a.jsx(d.r,{})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("h3",{className:"text-lg font-medium",children:"Alert Frequency"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx(c._,{children:"Immediate Alerts"}),a.jsx("p",{className:"text-sm text-muted-foreground",children:"Send alerts immediately for critical issues"})]}),a.jsx(d.r,{defaultChecked:!0})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx(c._,{children:"Daily Digest"}),a.jsx("p",{className:"text-sm text-muted-foreground",children:"Daily summary of all alerts"})]}),a.jsx(d.r,{defaultChecked:!0})]})]})]})]})})]})}),a.jsx(o.nU,{value:"history",className:"space-y-4",children:(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{children:[a.jsx(i.ll,{children:"Alert History"}),a.jsx(i.SZ,{children:"Historical view of all alerts and their resolution"})]}),a.jsx(i.aY,{children:a.jsx("div",{className:"space-y-4",children:(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-3",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("div",{className:"text-sm font-medium",children:"Total Alerts (30 days)"}),a.jsx("div",{className:"text-2xl font-bold",children:"127"}),a.jsx("div",{className:"text-xs text-muted-foreground",children:"+12% from last month"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("div",{className:"text-sm font-medium",children:"Average Resolution Time"}),a.jsx("div",{className:"text-2xl font-bold",children:"24m"}),a.jsx("div",{className:"text-xs text-muted-foreground",children:"-8% from last month"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("div",{className:"text-sm font-medium",children:"False Positive Rate"}),a.jsx("div",{className:"text-2xl font-bold",children:"3.2%"}),a.jsx("div",{className:"text-xs text-muted-foreground",children:"-1.1% from last month"})]})]})})})]})})]})]})}},1586:(e,s,t)=>{"use strict";t.d(s,{_:()=>c});var a=t(95344),r=t(3729),i=t(14217),l=t(49247),n=t(91626);let d=(0,l.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=r.forwardRef(({className:e,...s},t)=>a.jsx(i.f,{ref:t,className:(0,n.cn)(d(),e),...s}));c.displayName=i.f.displayName},17470:(e,s,t)=>{"use strict";t.d(s,{Bw:()=>f,Ph:()=>o,Ql:()=>p,i4:()=>m,ki:()=>x});var a=t(95344),r=t(3729),i=t(1146),l=t(25390),n=t(12704),d=t(62312),c=t(91626);let o=i.fC;i.ZA;let x=i.B4,m=r.forwardRef(({className:e,children:s,...t},r)=>(0,a.jsxs)(i.xz,{ref:r,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...t,children:[s,a.jsx(i.JO,{asChild:!0,children:a.jsx(l.Z,{className:"h-4 w-4 opacity-50"})})]}));m.displayName=i.xz.displayName;let u=r.forwardRef(({className:e,...s},t)=>a.jsx(i.u_,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:a.jsx(n.Z,{className:"h-4 w-4"})}));u.displayName=i.u_.displayName;let h=r.forwardRef(({className:e,...s},t)=>a.jsx(i.$G,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:a.jsx(l.Z,{className:"h-4 w-4"})}));h.displayName=i.$G.displayName;let f=r.forwardRef(({className:e,children:s,position:t="popper",...r},l)=>a.jsx(i.h_,{children:(0,a.jsxs)(i.VY,{ref:l,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:t,...r,children:[a.jsx(u,{}),a.jsx(i.l_,{className:(0,c.cn)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),a.jsx(h,{})]})}));f.displayName=i.VY.displayName,r.forwardRef(({className:e,...s},t)=>a.jsx(i.__,{ref:t,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...s})).displayName=i.__.displayName;let p=r.forwardRef(({className:e,children:s,...t},r)=>(0,a.jsxs)(i.ck,{ref:r,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[a.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:a.jsx(i.wU,{children:a.jsx(d.Z,{className:"h-4 w-4"})})}),a.jsx(i.eT,{children:s})]}));p.displayName=i.ck.displayName,r.forwardRef(({className:e,...s},t)=>a.jsx(i.Z0,{ref:t,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",e),...s})).displayName=i.Z0.displayName},71809:(e,s,t)=>{"use strict";t.d(s,{r:()=>w});var a=t(95344),r=t(3729),i=t(85222),l=t(31405),n=t(98462),d=t(33183),c=t(92062),o=t(63085),x=t(62409),m="Switch",[u,h]=(0,n.b)(m),[f,p]=u(m),j=r.forwardRef((e,s)=>{let{__scopeSwitch:t,name:n,checked:c,defaultChecked:o,required:u,disabled:h,value:p="on",onCheckedChange:j,form:y,...v}=e,[b,w]=r.useState(null),k=(0,l.e)(s,e=>w(e)),C=r.useRef(!1),S=!b||y||!!b.closest("form"),[Z,A]=(0,d.T)({prop:c,defaultProp:o??!1,onChange:j,caller:m});return(0,a.jsxs)(f,{scope:t,checked:Z,disabled:h,children:[(0,a.jsx)(x.WV.button,{type:"button",role:"switch","aria-checked":Z,"aria-required":u,"data-state":N(Z),"data-disabled":h?"":void 0,disabled:h,value:p,...v,ref:k,onClick:(0,i.M)(e.onClick,e=>{A(e=>!e),S&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),S&&(0,a.jsx)(g,{control:b,bubbles:!C.current,name:n,value:p,checked:Z,required:u,disabled:h,form:y,style:{transform:"translateX(-100%)"}})]})});j.displayName=m;var y="SwitchThumb",v=r.forwardRef((e,s)=>{let{__scopeSwitch:t,...r}=e,i=p(y,t);return(0,a.jsx)(x.WV.span,{"data-state":N(i.checked),"data-disabled":i.disabled?"":void 0,...r,ref:s})});v.displayName=y;var g=r.forwardRef(({__scopeSwitch:e,control:s,checked:t,bubbles:i=!0,...n},d)=>{let x=r.useRef(null),m=(0,l.e)(x,d),u=(0,c.D)(t),h=(0,o.t)(s);return r.useEffect(()=>{let e=x.current;if(!e)return;let s=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(u!==t&&s){let a=new Event("click",{bubbles:i});s.call(e,t),e.dispatchEvent(a)}},[u,t,i]),(0,a.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:t,...n,tabIndex:-1,ref:m,style:{...n.style,...h,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function N(e){return e?"checked":"unchecked"}g.displayName="SwitchBubbleInput";var b=t(91626);let w=r.forwardRef(({className:e,...s},t)=>a.jsx(j,{className:(0,b.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...s,ref:t,children:a.jsx(v,{className:(0,b.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));w.displayName=j.displayName},81036:(e,s,t)=>{"use strict";t.d(s,{RM:()=>d,SC:()=>c,iA:()=>l,pj:()=>x,ss:()=>o,xD:()=>n});var a=t(95344),r=t(3729),i=t(91626);let l=r.forwardRef(({className:e,...s},t)=>a.jsx("div",{className:"relative w-full overflow-auto",children:a.jsx("table",{ref:t,className:(0,i.cn)("w-full caption-bottom text-sm",e),...s})}));l.displayName="Table";let n=r.forwardRef(({className:e,...s},t)=>a.jsx("thead",{ref:t,className:(0,i.cn)("[&_tr]:border-b",e),...s}));n.displayName="TableHeader";let d=r.forwardRef(({className:e,...s},t)=>a.jsx("tbody",{ref:t,className:(0,i.cn)("[&_tr:last-child]:border-0",e),...s}));d.displayName="TableBody",r.forwardRef(({className:e,...s},t)=>a.jsx("tfoot",{ref:t,className:(0,i.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...s})).displayName="TableFooter";let c=r.forwardRef(({className:e,...s},t)=>a.jsx("tr",{ref:t,className:(0,i.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...s}));c.displayName="TableRow";let o=r.forwardRef(({className:e,...s},t)=>a.jsx("th",{ref:t,className:(0,i.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...s}));o.displayName="TableHead";let x=r.forwardRef(({className:e,...s},t)=>a.jsx("td",{ref:t,className:(0,i.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...s}));x.displayName="TableCell",r.forwardRef(({className:e,...s},t)=>a.jsx("caption",{ref:t,className:(0,i.cn)("mt-4 text-sm text-muted-foreground",e),...s})).displayName="TableCaption"},25757:(e,s,t)=>{"use strict";t.d(s,{mQ:()=>R,nU:()=>_,dr:()=>P,SP:()=>D});var a=t(95344),r=t(3729),i=t(85222),l=t(98462),n=t(34504),d=t(43234),c=t(62409),o=t(3975),x=t(33183),m=t(99048),u="Tabs",[h,f]=(0,l.b)(u,[n.Pc]),p=(0,n.Pc)(),[j,y]=h(u),v=r.forwardRef((e,s)=>{let{__scopeTabs:t,value:r,onValueChange:i,defaultValue:l,orientation:n="horizontal",dir:d,activationMode:h="automatic",...f}=e,p=(0,o.gm)(d),[y,v]=(0,x.T)({prop:r,onChange:i,defaultProp:l??"",caller:u});return(0,a.jsx)(j,{scope:t,baseId:(0,m.M)(),value:y,onValueChange:v,orientation:n,dir:p,activationMode:h,children:(0,a.jsx)(c.WV.div,{dir:p,"data-orientation":n,...f,ref:s})})});v.displayName=u;var g="TabsList",N=r.forwardRef((e,s)=>{let{__scopeTabs:t,loop:r=!0,...i}=e,l=y(g,t),d=p(t);return(0,a.jsx)(n.fC,{asChild:!0,...d,orientation:l.orientation,dir:l.dir,loop:r,children:(0,a.jsx)(c.WV.div,{role:"tablist","aria-orientation":l.orientation,...i,ref:s})})});N.displayName=g;var b="TabsTrigger",w=r.forwardRef((e,s)=>{let{__scopeTabs:t,value:r,disabled:l=!1,...d}=e,o=y(b,t),x=p(t),m=S(o.baseId,r),u=Z(o.baseId,r),h=r===o.value;return(0,a.jsx)(n.ck,{asChild:!0,...x,focusable:!l,active:h,children:(0,a.jsx)(c.WV.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":u,"data-state":h?"active":"inactive","data-disabled":l?"":void 0,disabled:l,id:m,...d,ref:s,onMouseDown:(0,i.M)(e.onMouseDown,e=>{l||0!==e.button||!1!==e.ctrlKey?e.preventDefault():o.onValueChange(r)}),onKeyDown:(0,i.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&o.onValueChange(r)}),onFocus:(0,i.M)(e.onFocus,()=>{let e="manual"!==o.activationMode;h||l||!e||o.onValueChange(r)})})})});w.displayName=b;var k="TabsContent",C=r.forwardRef((e,s)=>{let{__scopeTabs:t,value:i,forceMount:l,children:n,...o}=e,x=y(k,t),m=S(x.baseId,i),u=Z(x.baseId,i),h=i===x.value,f=r.useRef(h);return r.useEffect(()=>{let e=requestAnimationFrame(()=>f.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,a.jsx)(d.z,{present:l||h,children:({present:t})=>(0,a.jsx)(c.WV.div,{"data-state":h?"active":"inactive","data-orientation":x.orientation,role:"tabpanel","aria-labelledby":m,hidden:!t,id:u,tabIndex:0,...o,ref:s,style:{...e.style,animationDuration:f.current?"0s":void 0},children:t&&n})})});function S(e,s){return`${e}-trigger-${s}`}function Z(e,s){return`${e}-content-${s}`}C.displayName=k;var A=t(91626);let R=v,P=r.forwardRef(({className:e,...s},t)=>a.jsx(N,{ref:t,className:(0,A.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s}));P.displayName=N.displayName;let D=r.forwardRef(({className:e,...s},t)=>a.jsx(w,{ref:t,className:(0,A.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s}));D.displayName=w.displayName;let _=r.forwardRef(({className:e,...s},t)=>a.jsx(C,{ref:t,className:(0,A.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));_.displayName=C.displayName},1222:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},53148:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},17418:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},75203:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]])},73229:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},61635:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>i,__esModule:()=>r,default:()=>l});let a=(0,t(86843).createProxy)(String.raw`C:\proj\nextjs-saas\app\super-admin\alerts\page.tsx`),{__esModule:r,$$typeof:i}=a,l=a.default},14217:(e,s,t)=>{"use strict";t.d(s,{f:()=>n});var a=t(3729),r=t(62409),i=t(95344),l=a.forwardRef((e,s)=>(0,i.jsx)(r.WV.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));l.displayName="Label";var n=l}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[1638,7948,6671,4626,7792,2506,8830,1729,2125,3965],()=>t(24513));module.exports=a})();