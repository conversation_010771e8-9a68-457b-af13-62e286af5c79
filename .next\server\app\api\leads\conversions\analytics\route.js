"use strict";(()=>{var e={};e.id=7240,e.ids=[7240],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},21348:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>v,originalPathname:()=>w,patchFetch:()=>_,requestAsyncStorage:()=>y,routeModule:()=>m,serverHooks:()=>I,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>h});var r={};a.r(r),a.d(r,{GET:()=>u});var n=a(95419),o=a(69108),s=a(99678),i=a(78070),c=a(81355),l=a(3205),d=a(9108);async function u(e){try{let t=await (0,c.getServerSession)(l.L);if(!t?.user?.id||!t?.user?.companyId)return i.Z.json({error:"Unauthorized"},{status:401});let{searchParams:a}=new URL(e.url),r=a.get("period")||"30",n=new Date;n.setDate(n.getDate()-parseInt(r));let[o,s,u,m,y,g,I]=await Promise.all([d._.lead.count({where:{companyId:t.user.companyId,createdAt:{gte:n}}}),d._.lead.count({where:{companyId:t.user.companyId,status:"CONVERTED",convertedAt:{gte:n}}}),d._.leadConversion.findMany({where:{companyId:t.user.companyId,createdAt:{gte:n}},include:{lead:{select:{id:!0,firstName:!0,lastName:!0,companyName:!0,score:!0,source:!0}},customer:{select:{id:!0,name:!0,email:!0,company:!0}},salesRep:{select:{id:!0,name:!0,email:!0}}},orderBy:{createdAt:"desc"}}),d._.leadConversion.groupBy({by:["conversionType"],where:{companyId:t.user.companyId,createdAt:{gte:n}},_count:{id:!0},_sum:{conversionValue:!0}}),d._.$queryRaw`
        SELECT 
          DATE_TRUNC('month', "createdAt") as month,
          COUNT(*)::int as conversions,
          SUM("conversionValue")::float as total_value
        FROM "LeadConversion" 
        WHERE "companyId" = ${t.user.companyId}
          AND "createdAt" >= ${new Date(Date.now()-31536e6)}
        GROUP BY DATE_TRUNC('month', "createdAt")
        ORDER BY month DESC
        LIMIT 12
      `,d._.leadConversion.groupBy({by:["salesRepId"],where:{companyId:t.user.companyId,createdAt:{gte:n}},_count:{id:!0},_sum:{conversionValue:!0},orderBy:{_count:{id:"desc"}},take:10}),Promise.all([d._.lead.count({where:{companyId:t.user.companyId,createdAt:{gte:n}}}),d._.lead.count({where:{companyId:t.user.companyId,createdAt:{gte:n},status:{in:["QUALIFIED","PROPOSAL","NEGOTIATION"]}}}),d._.lead.count({where:{companyId:t.user.companyId,createdAt:{gte:n},status:{in:["PROPOSAL","NEGOTIATION"]}}}),d._.lead.count({where:{companyId:t.user.companyId,createdAt:{gte:n},status:"CONVERTED"}})])]),v=o>0?s/o*100:0,h=await p(t.user.companyId,n),w=u.reduce((e,t)=>e+(t.conversionValue||0),0),_=g.map(e=>e.salesRepId).filter(Boolean),A=await d._.user.findMany({where:{id:{in:_},companyId:t.user.companyId},select:{id:!0,name:!0,email:!0}}),T=g.map(e=>{let t=A.find(t=>t.id===e.salesRepId);return{...e,salesRep:t,conversionRate:o>0?e._count.id/o*100:0}}),[f,E,C,R]=I,x=await d._.lead.groupBy({by:["source"],where:{companyId:t.user.companyId,createdAt:{gte:n}},_count:{id:!0}}),q=await d._.lead.groupBy({by:["source"],where:{companyId:t.user.companyId,status:"CONVERTED",convertedAt:{gte:n}},_count:{id:!0}}),O=x.map(e=>{let t=q.find(t=>t.source===e.source)?._count.id||0;return{source:e.source,totalLeads:e._count.id,convertedLeads:t,conversionRate:e._count.id>0?t/e._count.id*100:0}});return i.Z.json({summary:{totalLeads:o,convertedLeads:s,conversionRate:Math.round(100*v)/100,totalConversions:u.length,totalConversionValue:w,averageConversionValue:u.length>0?w/u.length:0,averageConversionTime:Math.round(h)},conversionsByType:m.map(e=>({type:e.conversionType,count:e._count.id,totalValue:e._sum.conversionValue||0,averageValue:e._count.id>0?(e._sum.conversionValue||0)/e._count.id:0})),conversionsByMonth:y,topPerformers:T,conversionFunnel:[{stage:"Total Leads",count:f,percentage:100},{stage:"Qualified",count:E,percentage:f>0?E/f*100:0},{stage:"Proposal",count:C,percentage:f>0?C/f*100:0},{stage:"Converted",count:R,percentage:f>0?R/f*100:0}],sourceAnalysis:O,recentConversions:u.slice(0,10),trends:{conversionRate:v,averageValue:u.length>0?w/u.length:0,timeToConvert:h}})}catch(e){return console.error("Error fetching conversion analytics:",e),i.Z.json({error:"Failed to fetch conversion analytics"},{status:500})}}async function p(e,t){let a=await d._.leadConversion.findMany({where:{companyId:e,createdAt:{gte:t}},include:{lead:{select:{createdAt:!0}}}});return 0===a.length?0:a.reduce((e,t)=>{let a=new Date(t.lead.createdAt);return e+(new Date(t.createdAt).getTime()-a.getTime())},0)/a.length/864e5}let m=new n.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/leads/conversions/analytics/route",pathname:"/api/leads/conversions/analytics",filename:"route",bundlePath:"app/api/leads/conversions/analytics/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\leads\\conversions\\analytics\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:y,staticGenerationAsyncStorage:g,serverHooks:I,headerHooks:v,staticGenerationBailout:h}=m,w="/api/leads/conversions/analytics/route";function _(){return(0,s.patchFetch)({serverHooks:I,staticGenerationAsyncStorage:g})}},3205:(e,t,a)=>{a.d(t,{L:()=>l});var r=a(86485),n=a(10375),o=a(50694),s=a(6521),i=a.n(s),c=a(9108);let l={providers:[(0,r.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await c._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),a=t?.companyId;if(!a&&t){let e=await c._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(a=e?.id)&&await c._.user.update({where:{id:t.id},data:{companyId:a}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await i().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await c._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:a}}catch(e){return console.error("Authentication error:",e),null}}}),(0,n.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,o.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,a)=>{a.d(t,{_:()=>n});let r=require("@prisma/client"),n=globalThis.prisma??new r.PrismaClient}};var t=require("../../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[1638,6206,6521,2455,4520],()=>a(21348));module.exports=r})();