import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Validation schema for contract update
const contractUpdateSchema = z.object({
  title: z.string().min(1, 'Title is required').optional(),
  description: z.string().optional().nullable(),
  customerId: z.string().min(1, 'Customer is required').optional(),
  quotationId: z.string().optional().nullable(),
  invoiceId: z.string().optional().nullable(),
  type: z.enum(['SERVICE', 'PRODUCT', 'SUBSCRIPTION', 'MAINTENANCE', 'CONSULTING', 'OTHER']).optional(),
  status: z.enum(['DRAFT', 'REVIEW', 'SENT', 'SIGNED', 'ACTIVE', 'COMPLETED', 'CANCELLED', 'EXPIRED']).optional(),
  value: z.number().min(0, 'Contract value must be positive').optional().nullable(),
  currency: z.string().optional(),
  startDate: z.string().optional().nullable(),
  endDate: z.string().optional().nullable(),
  renewalDate: z.string().optional().nullable(),
  autoRenewal: z.boolean().optional(),
  renewalPeriod: z.number().optional().nullable(),
  terms: z.string().optional().nullable(),
  conditions: z.string().optional().nullable(),
  notes: z.string().optional().nullable(),
  templateId: z.string().optional().nullable(),
  signatureRequired: z.boolean().optional(),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).optional(),
  tags: z.array(z.string()).optional(),
  assignedToId: z.string().optional().nullable()
})

// GET /api/contracts/[id] - Get single contract
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const contract = await prisma.contract.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId || undefined
      },
      include: {
        customer: {
          select: { 
            id: true, 
            name: true, 
            email: true, 
            company: true, 
            phone: true, 
            address: true, 
            city: true, 
            state: true, 
            country: true, 
            postalCode: true 
          }
        },
        quotation: {
          select: { id: true, quotationNumber: true, title: true, total: true }
        },
        invoice: {
          select: { id: true, invoiceNumber: true, total: true, status: true }
        },
        template: {
          select: { id: true, name: true, type: true, content: true }
        },
        createdBy: {
          select: { name: true, email: true }
        },
        assignedTo: {
          select: { name: true, email: true }
        },
        signatures: {
          orderBy: { createdAt: 'desc' },
          include: {
            signedBy: { select: { name: true, email: true } }
          }
        },
        documents: {
          orderBy: { createdAt: 'desc' },
          include: {
            uploadedBy: { select: { name: true } }
          }
        },
        activities: {
          orderBy: { createdAt: 'desc' },
          take: 20,
          include: {
            createdBy: { select: { name: true } }
          }
        },
        _count: {
          select: {
            activities: true,
            signatures: true,
            documents: true
          }
        }
      }
    })

    if (!contract) {
      return NextResponse.json({ error: 'Contract not found' }, { status: 404 })
    }

    return NextResponse.json(contract)
  } catch (error) {
    console.error('Error fetching contract:', error)
    return NextResponse.json(
      { error: 'Failed to fetch contract' },
      { status: 500 }
    )
  }
}

// PUT /api/contracts/[id] - Update contract
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = contractUpdateSchema.parse(body)

    // Check if contract exists and belongs to user's company
    const existingContract = await prisma.contract.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId || undefined
      }
    })

    if (!existingContract) {
      return NextResponse.json({ error: 'Contract not found' }, { status: 404 })
    }

    // Check if contract can be edited (not signed or completed)
    if (existingContract.status === 'SIGNED' || existingContract.status === 'COMPLETED') {
      return NextResponse.json(
        { error: 'Cannot edit signed or completed contracts' },
        { status: 400 }
      )
    }

    // Prepare update data
    const updateData: any = { ...validatedData }

    if (validatedData.startDate) {
      updateData.startDate = new Date(validatedData.startDate)
    }

    if (validatedData.endDate) {
      updateData.endDate = new Date(validatedData.endDate)
    }

    if (validatedData.renewalDate) {
      updateData.renewalDate = new Date(validatedData.renewalDate)
    }

    // Update contract in a transaction
    const contract = await prisma.$transaction(async (tx) => {
      const updatedContract = await tx.contract.update({
        where: { id: params.id },
        data: updateData,
        include: {
          customer: {
            select: { id: true, name: true, email: true, company: true }
          },
          quotation: {
            select: { id: true, quotationNumber: true, title: true }
          },
          invoice: {
            select: { id: true, invoiceNumber: true }
          },
          template: {
            select: { id: true, name: true, type: true }
          },
          createdBy: {
            select: { name: true, email: true }
          },
          assignedTo: {
            select: { name: true, email: true }
          }
        }
      })

      // Log activity if status changed
      if (validatedData.status && validatedData.status !== existingContract.status) {
        await tx.activity.create({
          data: {
            type: 'STATUS_CHANGE',
            title: 'Contract Status Updated',
            description: `Contract status changed from ${existingContract.status} to ${validatedData.status}`,
            contractId: params.id,
            customerId: updatedContract.customerId,
            quotationId: updatedContract.quotationId,
            invoiceId: updatedContract.invoiceId,
            companyId: session.user.companyId!,
            createdById: session.user.id
          }
        })
      }

      return updatedContract
    })

    return NextResponse.json(contract)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating contract:', error)
    return NextResponse.json(
      { error: 'Failed to update contract' },
      { status: 500 }
    )
  }
}

// DELETE /api/contracts/[id] - Delete contract
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if contract exists and belongs to user's company
    const contract = await prisma.contract.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId || undefined
      },
      include: {
        _count: {
          select: {
            signatures: true,
            documents: true
          }
        }
      }
    })

    if (!contract) {
      return NextResponse.json({ error: 'Contract not found' }, { status: 404 })
    }

    // Check if contract has signatures or documents
    if (contract._count.signatures > 0 || contract._count.documents > 0) {
      return NextResponse.json(
        { 
          error: 'Cannot delete contract with existing signatures or documents',
          details: contract._count
        },
        { status: 400 }
      )
    }

    // Check if contract is signed or active
    if (contract.status === 'SIGNED' || contract.status === 'ACTIVE') {
      return NextResponse.json(
        { error: 'Cannot delete signed or active contracts' },
        { status: 400 }
      )
    }

    await prisma.contract.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ message: 'Contract deleted successfully' })
  } catch (error) {
    console.error('Error deleting contract:', error)
    return NextResponse.json(
      { error: 'Failed to delete contract' },
      { status: 500 }
    )
  }
}
