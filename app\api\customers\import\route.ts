import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Validation schema for customer import
const importCustomerSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Invalid email').optional().nullable(),
  phone: z.string().optional().nullable(),
  company: z.string().optional().nullable(),
  address: z.string().optional().nullable(),
  city: z.string().optional().nullable(),
  state: z.string().optional().nullable(),
  country: z.string().optional().nullable(),
  postalCode: z.string().optional().nullable(),
  industry: z.string().optional().nullable(),
  website: z.string().url('Invalid website URL').optional().nullable().or(z.literal('')),
  notes: z.string().optional().nullable(),
  status: z.enum(['ACTIVE', 'INACTIVE', 'PROSPECT']).default('PROSPECT'),
  tags: z.array(z.string()).default([])
})

const importRequestSchema = z.object({
  customers: z.array(importCustomerSchema),
  options: z.object({
    skipDuplicates: z.boolean().default(true),
    updateExisting: z.boolean().default(false)
  }).default({})
})

// POST /api/customers/import - Import customers from CSV/JSON
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = importRequestSchema.parse(body)
    const { customers, options } = validatedData

    const results = {
      total: customers.length,
      imported: 0,
      updated: 0,
      skipped: 0,
      errors: [] as Array<{ row: number; error: string; data: any }>
    }

    // Process each customer
    for (let i = 0; i < customers.length; i++) {
      const customerData = customers[i]
      
      try {
        // Check for existing customer by email
        let existingCustomer = null
        if (customerData.email) {
          existingCustomer = await prisma.customer.findFirst({
            where: {
              email: customerData.email,
              companyId: session.user.companyId
            }
          })
        }

        if (existingCustomer) {
          if (options.updateExisting) {
            // Update existing customer
            await prisma.customer.update({
              where: { id: existingCustomer.id },
              data: {
                ...customerData,
                updatedAt: new Date()
              }
            })
            results.updated++
          } else if (options.skipDuplicates) {
            // Skip duplicate
            results.skipped++
          } else {
            // Report as error
            results.errors.push({
              row: i + 1,
              error: 'Customer with this email already exists',
              data: customerData
            })
          }
        } else {
          // Create new customer
          const newCustomer = await prisma.customer.create({
            data: {
              ...customerData,
              companyId: session.user.companyId,
              createdById: session.user.id
            }
          })

          // Log activity
          await prisma.activity.create({
            data: {
              type: 'NOTE',
              title: 'Customer Imported',
              description: `Customer "${newCustomer.name}" was imported`,
              customerId: newCustomer.id,
              companyId: session.user.companyId,
              createdById: session.user.id
            }
          })

          results.imported++
        }
      } catch (error) {
        results.errors.push({
          row: i + 1,
          error: error instanceof Error ? error.message : 'Unknown error',
          data: customerData
        })
      }
    }

    return NextResponse.json({
      success: true,
      results
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error importing customers:', error)
    return NextResponse.json(
      { error: 'Failed to import customers' },
      { status: 500 }
    )
  }
}

// GET /api/customers/import - Get import template
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const format = searchParams.get('format') || 'json'

    const template = {
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '******-123-4567',
      company: 'Acme Corporation',
      address: '123 Main Street',
      city: 'New York',
      state: 'NY',
      country: 'USA',
      postalCode: '10001',
      industry: 'Technology',
      website: 'https://example.com',
      notes: 'Important customer notes',
      status: 'PROSPECT',
      tags: ['vip', 'enterprise']
    }

    if (format === 'csv') {
      // Generate CSV template
      const headers = Object.keys(template)
      const csvHeaders = headers.join(',')
      const csvRow = headers.map(header => {
        const value = template[header as keyof typeof template]
        if (Array.isArray(value)) {
          return `"${value.join(';')}"`
        }
        return `"${value}"`
      }).join(',')
      
      const csvContent = `${csvHeaders}\n${csvRow}`
      
      return new Response(csvContent, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': 'attachment; filename="customer-import-template.csv"'
        }
      })
    }

    // Return JSON template
    return NextResponse.json({
      template,
      instructions: {
        name: 'Required. Customer full name',
        email: 'Optional. Must be valid email format',
        phone: 'Optional. Customer phone number',
        company: 'Optional. Company name',
        address: 'Optional. Street address',
        city: 'Optional. City name',
        state: 'Optional. State/Province',
        country: 'Optional. Country name',
        postalCode: 'Optional. ZIP/Postal code',
        industry: 'Optional. Industry sector',
        website: 'Optional. Must be valid URL',
        notes: 'Optional. Additional notes',
        status: 'Optional. One of: ACTIVE, INACTIVE, PROSPECT (default: PROSPECT)',
        tags: 'Optional. Array of tags or semicolon-separated string'
      },
      example: [template]
    })

  } catch (error) {
    console.error('Error generating import template:', error)
    return NextResponse.json(
      { error: 'Failed to generate template' },
      { status: 500 }
    )
  }
}
