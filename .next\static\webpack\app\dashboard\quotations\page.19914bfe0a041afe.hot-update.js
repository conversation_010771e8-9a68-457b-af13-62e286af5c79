"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/quotations/page",{

/***/ "(app-pages-browser)/./components/quotations/quotation-form.tsx":
/*!**************************************************!*\
  !*** ./components/quotations/quotation-form.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuotationForm: function() { return /* binding */ QuotationForm; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/v3/types.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calculator_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ QuotationForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst quotationItemSchema = zod__WEBPACK_IMPORTED_MODULE_8__.object({\n    description: zod__WEBPACK_IMPORTED_MODULE_8__.string().min(1, \"Description is required\"),\n    quantity: zod__WEBPACK_IMPORTED_MODULE_8__.number().min(1, \"Quantity must be at least 1\"),\n    unitPrice: zod__WEBPACK_IMPORTED_MODULE_8__.number().min(0, \"Unit price must be positive\"),\n    discount: zod__WEBPACK_IMPORTED_MODULE_8__.number().min(0).max(100).default(0),\n    taxRate: zod__WEBPACK_IMPORTED_MODULE_8__.number().min(0).max(100).default(0)\n});\nconst quotationSchema = zod__WEBPACK_IMPORTED_MODULE_8__.object({\n    title: zod__WEBPACK_IMPORTED_MODULE_8__.string().min(1, \"Title is required\"),\n    description: zod__WEBPACK_IMPORTED_MODULE_8__.string().optional(),\n    customerId: zod__WEBPACK_IMPORTED_MODULE_8__.string().min(1, \"Customer is required\"),\n    leadId: zod__WEBPACK_IMPORTED_MODULE_8__.string().optional(),\n    status: zod__WEBPACK_IMPORTED_MODULE_8__[\"enum\"]([\n        \"DRAFT\",\n        \"SENT\",\n        \"VIEWED\",\n        \"ACCEPTED\",\n        \"REJECTED\",\n        \"EXPIRED\"\n    ]).default(\"DRAFT\"),\n    validUntil: zod__WEBPACK_IMPORTED_MODULE_8__.string().optional(),\n    terms: zod__WEBPACK_IMPORTED_MODULE_8__.string().optional(),\n    notes: zod__WEBPACK_IMPORTED_MODULE_8__.string().optional(),\n    paymentTerms: zod__WEBPACK_IMPORTED_MODULE_8__.string().optional(),\n    paymentDueDays: zod__WEBPACK_IMPORTED_MODULE_8__.number().min(0).default(30),\n    items: zod__WEBPACK_IMPORTED_MODULE_8__.array(quotationItemSchema).min(1, \"At least one item is required\"),\n    taxRate: zod__WEBPACK_IMPORTED_MODULE_8__.number().min(0).max(100).default(0),\n    discountType: zod__WEBPACK_IMPORTED_MODULE_8__[\"enum\"]([\n        \"PERCENTAGE\",\n        \"FIXED\"\n    ]).default(\"PERCENTAGE\"),\n    discountValue: zod__WEBPACK_IMPORTED_MODULE_8__.number().min(0).default(0)\n});\nfunction QuotationForm(param) {\n    let { isOpen, onClose, onSuccess, quotation, mode, preselectedCustomerId, preselectedLeadId } = param;\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [customers, setCustomers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [leads, setLeads] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const { register, handleSubmit, formState: { errors }, reset, watch, control, setValue } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_9__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(quotationSchema),\n        defaultValues: quotation ? {\n            title: quotation.title,\n            description: quotation.description || \"\",\n            customerId: quotation.customerId || preselectedCustomerId || \"\",\n            leadId: quotation.leadId || preselectedLeadId || \"\",\n            status: quotation.status || \"DRAFT\",\n            validUntil: quotation.validUntil ? new Date(quotation.validUntil).toISOString().split(\"T\")[0] : \"\",\n            terms: quotation.terms || \"\",\n            notes: quotation.notes || \"\",\n            items: quotation.items || [\n                {\n                    description: \"\",\n                    quantity: 1,\n                    unitPrice: 0,\n                    discount: 0,\n                    taxRate: 0\n                }\n            ],\n            taxRate: quotation.taxRate || 0,\n            discountType: quotation.discountType || \"PERCENTAGE\",\n            discountValue: quotation.discountValue || 0\n        } : {\n            status: \"DRAFT\",\n            customerId: preselectedCustomerId || \"\",\n            leadId: preselectedLeadId || \"\",\n            items: [\n                {\n                    description: \"\",\n                    quantity: 1,\n                    unitPrice: 0,\n                    discount: 0,\n                    taxRate: 0\n                }\n            ],\n            taxRate: 0,\n            discountType: \"PERCENTAGE\",\n            discountValue: 0\n        }\n    });\n    const { fields, append, remove } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_9__.useFieldArray)({\n        control,\n        name: \"items\"\n    });\n    const watchedItems = watch(\"items\");\n    const watchedTaxRate = watch(\"taxRate\");\n    const watchedDiscountType = watch(\"discountType\");\n    const watchedDiscountValue = watch(\"discountValue\");\n    // Fetch customers and leads\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchData = async ()=>{\n            try {\n                const [customersRes, leadsRes] = await Promise.all([\n                    fetch(\"/api/customers?limit=100\"),\n                    fetch(\"/api/leads?limit=100\")\n                ]);\n                if (customersRes.ok) {\n                    const customersData = await customersRes.json();\n                    setCustomers(customersData.customers);\n                }\n                if (leadsRes.ok) {\n                    const leadsData = await leadsRes.json();\n                    setLeads(leadsData.leads);\n                }\n            } catch (error) {\n                console.error(\"Error fetching data:\", error);\n            }\n        };\n        if (isOpen) {\n            fetchData();\n        }\n    }, [\n        isOpen\n    ]);\n    // Calculate totals\n    const calculateTotals = ()=>{\n        const subtotal = watchedItems.reduce((sum, item)=>{\n            const itemTotal = (item.quantity || 0) * (item.unitPrice || 0);\n            const discountAmount = itemTotal * (item.discount || 0) / 100;\n            const afterDiscount = itemTotal - discountAmount;\n            const taxAmount = afterDiscount * (item.taxRate || 0) / 100;\n            return sum + afterDiscount + taxAmount;\n        }, 0);\n        let total = subtotal;\n        if (watchedDiscountType === \"PERCENTAGE\") {\n            total = subtotal - subtotal * (watchedDiscountValue || 0) / 100;\n        } else {\n            total = subtotal - (watchedDiscountValue || 0);\n        }\n        const finalTaxAmount = total * (watchedTaxRate || 0) / 100;\n        const finalTotal = total + finalTaxAmount;\n        return {\n            subtotal: Math.round(subtotal * 100) / 100,\n            total: Math.round(finalTotal * 100) / 100,\n            taxAmount: Math.round(finalTaxAmount * 100) / 100,\n            discountAmount: watchedDiscountType === \"PERCENTAGE\" ? Math.round(subtotal * (watchedDiscountValue || 0) / 100 * 100) / 100 : watchedDiscountValue || 0\n        };\n    };\n    const totals = calculateTotals();\n    const addItem = ()=>{\n        append({\n            description: \"\",\n            quantity: 1,\n            unitPrice: 0,\n            discount: 0,\n            taxRate: 0\n        });\n    };\n    const removeItem = (index)=>{\n        if (fields.length > 1) {\n            remove(index);\n        }\n    };\n    const onSubmit = async (data)=>{\n        setIsLoading(true);\n        try {\n            const url = mode === \"create\" ? \"/api/quotations\" : \"/api/quotations/\".concat(quotation.id);\n            const method = mode === \"create\" ? \"POST\" : \"PUT\";\n            const response = await fetch(url, {\n                method,\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(data)\n            });\n            if (!response.ok) {\n                const error = await response.json();\n                throw new Error(error.error || \"Failed to save quotation\");\n            }\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Quotation \".concat(mode === \"create\" ? \"created\" : \"updated\", \" successfully\"));\n            reset();\n            onSuccess();\n            onClose();\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_7__.toast.error(error instanceof Error ? error.message : \"An error occurred\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleClose = ()=>{\n        reset();\n        onClose();\n    };\n    const statusOptions = [\n        {\n            value: \"DRAFT\",\n            label: \"Draft\"\n        },\n        {\n            value: \"SENT\",\n            label: \"Sent\"\n        },\n        {\n            value: \"VIEWED\",\n            label: \"Viewed\"\n        },\n        {\n            value: \"ACCEPTED\",\n            label: \"Accepted\"\n        },\n        {\n            value: \"REJECTED\",\n            label: \"Rejected\"\n        },\n        {\n            value: \"EXPIRED\",\n            label: \"Expired\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.Dialog, {\n        open: isOpen,\n        onOpenChange: handleClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogContent, {\n            className: \"max-w-6xl max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogTitle, {\n                            children: mode === \"create\" ? \"Create New Quotation\" : \"Edit Quotation\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogDescription, {\n                            children: mode === \"create\" ? \"Create a new quotation with items, pricing, and terms.\" : \"Update the quotation information and items.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit(onSubmit),\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            htmlFor: \"title\",\n                                            children: \"Title *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            id: \"title\",\n                                            ...register(\"title\"),\n                                            placeholder: \"Quotation title\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 15\n                                        }, this),\n                                        errors.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-600 mt-1\",\n                                            children: errors.title.message\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            htmlFor: \"status\",\n                                            children: \"Status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            id: \"status\",\n                                            ...register(\"status\"),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: statusOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: option.value,\n                                                    children: option.label\n                                                }, option.value, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            htmlFor: \"customerId\",\n                                            children: \"Customer *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            id: \"customerId\",\n                                            ...register(\"customerId\"),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select a customer\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 17\n                                                }, this),\n                                                customers.map((customer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: customer.id,\n                                                        children: [\n                                                            customer.name,\n                                                            \" \",\n                                                            customer.company && \"(\".concat(customer.company, \")\")\n                                                        ]\n                                                    }, customer.id, true, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 15\n                                        }, this),\n                                        errors.customerId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-600 mt-1\",\n                                            children: errors.customerId.message\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            htmlFor: \"leadId\",\n                                            children: \"Related Lead\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            id: \"leadId\",\n                                            ...register(\"leadId\"),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select a lead (optional)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 17\n                                                }, this),\n                                                leads.map((lead)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: lead.id,\n                                                        children: lead.name\n                                                    }, lead.id, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            htmlFor: \"validUntil\",\n                                            children: \"Valid Until\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            id: \"validUntil\",\n                                            type: \"date\",\n                                            ...register(\"validUntil\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                    htmlFor: \"description\",\n                                    children: \"Description\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    id: \"description\",\n                                    ...register(\"description\"),\n                                    rows: 3,\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                    placeholder: \"Brief description of the quotation...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium\",\n                                            children: \"Items\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            type: \"button\",\n                                            onClick: addItem,\n                                            size: \"sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Add Item\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: fields.map((field, index)=>{\n                                        var _errors_items_index, _errors_items, _errors_items_index_description, _errors_items_index1;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-12 gap-2 items-end p-4 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            htmlFor: \"items.\".concat(index, \".description\"),\n                                                            children: \"Description *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            ...register(\"items.\".concat(index, \".description\")),\n                                                            placeholder: \"Item description\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        ((_errors_items = errors.items) === null || _errors_items === void 0 ? void 0 : (_errors_items_index = _errors_items[index]) === null || _errors_items_index === void 0 ? void 0 : _errors_items_index.description) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-red-600 mt-1\",\n                                                            children: (_errors_items_index1 = errors.items[index]) === null || _errors_items_index1 === void 0 ? void 0 : (_errors_items_index_description = _errors_items_index1.description) === null || _errors_items_index_description === void 0 ? void 0 : _errors_items_index_description.message\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 348,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            htmlFor: \"items.\".concat(index, \".quantity\"),\n                                                            children: \"Qty *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 355,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            type: \"number\",\n                                                            min: \"1\",\n                                                            ...register(\"items.\".concat(index, \".quantity\"), {\n                                                                valueAsNumber: true\n                                                            }),\n                                                            placeholder: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 356,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                    lineNumber: 354,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            htmlFor: \"items.\".concat(index, \".unitPrice\"),\n                                                            children: \"Unit Price *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            type: \"number\",\n                                                            min: \"0\",\n                                                            step: \"0.01\",\n                                                            ...register(\"items.\".concat(index, \".unitPrice\"), {\n                                                                valueAsNumber: true\n                                                            }),\n                                                            placeholder: \"0.00\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            htmlFor: \"items.\".concat(index, \".discount\"),\n                                                            children: \"Disc %\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            type: \"number\",\n                                                            min: \"0\",\n                                                            max: \"100\",\n                                                            ...register(\"items.\".concat(index, \".discount\"), {\n                                                                valueAsNumber: true\n                                                            }),\n                                                            placeholder: \"0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            htmlFor: \"items.\".concat(index, \".taxRate\"),\n                                                            children: \"Tax %\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            type: \"number\",\n                                                            min: \"0\",\n                                                            max: \"100\",\n                                                            ...register(\"items.\".concat(index, \".taxRate\"), {\n                                                                valueAsNumber: true\n                                                            }),\n                                                            placeholder: \"0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            children: \"Total\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 398,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"px-3 py-2 bg-gray-50 rounded-md text-sm\",\n                                                            children: [\n                                                                \"$\",\n                                                                (()=>{\n                                                                    const item = watchedItems[index];\n                                                                    if (!item) return \"0.00\";\n                                                                    const itemTotal = (item.quantity || 0) * (item.unitPrice || 0);\n                                                                    const discountAmount = itemTotal * (item.discount || 0) / 100;\n                                                                    const afterDiscount = itemTotal - discountAmount;\n                                                                    const taxAmount = afterDiscount * (item.taxRate || 0) / 100;\n                                                                    return (afterDiscount + taxAmount).toFixed(2);\n                                                                })()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        type: \"button\",\n                                                        variant: \"destructive\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>removeItem(index),\n                                                        disabled: fields.length === 1,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 420,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, field.id, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 17\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium\",\n                                            children: \"Settings\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            htmlFor: \"discountType\",\n                                                            children: \"Discount Type\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            id: \"discountType\",\n                                                            ...register(\"discountType\"),\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"PERCENTAGE\",\n                                                                    children: \"Percentage\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                                    lineNumber: 441,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"FIXED\",\n                                                                    children: \"Fixed Amount\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                                    lineNumber: 442,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            htmlFor: \"discountValue\",\n                                                            children: [\n                                                                \"Discount \",\n                                                                watchedDiscountType === \"PERCENTAGE\" ? \"%\" : \"$\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 447,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            type: \"number\",\n                                                            min: \"0\",\n                                                            step: \"0.01\",\n                                                            ...register(\"discountValue\", {\n                                                                valueAsNumber: true\n                                                            }),\n                                                            placeholder: \"0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 450,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"taxRate\",\n                                                    children: \"Overall Tax Rate (%)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    max: \"100\",\n                                                    step: \"0.01\",\n                                                    ...register(\"taxRate\", {\n                                                        valueAsNumber: true\n                                                    }),\n                                                    placeholder: \"0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                    lineNumber: 462,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                    lineNumber: 430,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Totals\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 p-4 bg-gray-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Subtotal:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 481,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"$\",\n                                                                totals.subtotal.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 17\n                                                }, this),\n                                                totals.discountAmount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-red-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Discount:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 487,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"-$\",\n                                                                totals.discountAmount.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 488,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 19\n                                                }, this),\n                                                totals.taxAmount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Tax:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 494,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"$\",\n                                                                totals.taxAmount.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 495,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                    lineNumber: 493,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between font-bold text-lg border-t pt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Total:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 500,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"$\",\n                                                                totals.total.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 501,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 479,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                    lineNumber: 473,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                            lineNumber: 429,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            htmlFor: \"terms\",\n                                            children: \"Terms & Conditions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 510,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            id: \"terms\",\n                                            ...register(\"terms\"),\n                                            rows: 4,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"Payment terms, delivery conditions, etc...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 511,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                    lineNumber: 509,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            htmlFor: \"notes\",\n                                            children: \"Internal Notes\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            id: \"notes\",\n                                            ...register(\"notes\"),\n                                            rows: 4,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"Internal notes (not visible to customer)...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 522,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                            lineNumber: 508,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    type: \"button\",\n                                    variant: \"outline\",\n                                    onClick: handleClose,\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                    lineNumber: 533,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    type: \"submit\",\n                                    disabled: isLoading,\n                                    children: isLoading ? \"Saving...\" : mode === \"create\" ? \"Create Quotation\" : \"Update Quotation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                    lineNumber: 536,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                            lineNumber: 532,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n            lineNumber: 229,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n        lineNumber: 228,\n        columnNumber: 5\n    }, this);\n}\n_s(QuotationForm, \"1Ky/yd/swgE2r/wOn58sNmnXBqg=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_9__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_9__.useFieldArray\n    ];\n});\n_c = QuotationForm;\nvar _c;\n$RefreshReg$(_c, \"QuotationForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/quotations/quotation-form.tsx\n"));

/***/ })

});