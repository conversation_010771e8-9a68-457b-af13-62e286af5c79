'use client'

import { useState, useRef } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Upload, FileText, CheckCircle, XCircle, AlertCircle } from 'lucide-react'
import { toast } from 'react-hot-toast'

interface ImportResult {
  total: number
  imported: number
  updated: number
  skipped: number
  errors: Array<{
    row: number
    error: string
    data: any
  }>
}

interface CustomerImportProps {
  open: boolean
  onClose: () => void
  onSuccess: () => void
}

export function CustomerImport({ open, onClose, onSuccess }: CustomerImportProps) {
  const [file, setFile] = useState<File | null>(null)
  const [importing, setImporting] = useState(false)
  const [result, setResult] = useState<ImportResult | null>(null)
  const [options, setOptions] = useState({
    skipDuplicates: true,
    updateExisting: false
  })
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0]
    if (selectedFile) {
      if (selectedFile.type === 'text/csv' || selectedFile.name.endsWith('.csv')) {
        setFile(selectedFile)
        setResult(null)
      } else {
        toast.error('Please select a CSV file')
      }
    }
  }

  const parseCSV = (csvText: string) => {
    const lines = csvText.split('\n').filter(line => line.trim())
    if (lines.length < 2) {
      throw new Error('CSV file must have at least a header row and one data row')
    }

    const headers = lines[0].split(',').map(header => header.trim().replace(/"/g, ''))
    const customers = []

    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(value => value.trim().replace(/"/g, ''))
      const customer: any = {}

      headers.forEach((header, index) => {
        const value = values[index] || ''
        
        // Map CSV headers to customer fields
        switch (header.toLowerCase()) {
          case 'name':
            customer.name = value
            break
          case 'email':
            customer.email = value || null
            break
          case 'phone':
            customer.phone = value || null
            break
          case 'company':
            customer.company = value || null
            break
          case 'address':
            customer.address = value || null
            break
          case 'city':
            customer.city = value || null
            break
          case 'state':
            customer.state = value || null
            break
          case 'country':
            customer.country = value || null
            break
          case 'postalcode':
          case 'postal_code':
          case 'zip':
            customer.postalCode = value || null
            break
          case 'industry':
            customer.industry = value || null
            break
          case 'website':
            customer.website = value || null
            break
          case 'notes':
            customer.notes = value || null
            break
          case 'status':
            customer.status = ['ACTIVE', 'INACTIVE', 'PROSPECT'].includes(value.toUpperCase()) 
              ? value.toUpperCase() 
              : 'PROSPECT'
            break
          case 'tags':
            customer.tags = value ? value.split(';').map(tag => tag.trim()).filter(Boolean) : []
            break
        }
      })

      if (customer.name) {
        customers.push(customer)
      }
    }

    return customers
  }

  const handleImport = async () => {
    if (!file) {
      toast.error('Please select a file to import')
      return
    }

    setImporting(true)
    try {
      const csvText = await file.text()
      const customers = parseCSV(csvText)

      if (customers.length === 0) {
        throw new Error('No valid customer data found in the file')
      }

      const response = await fetch('/api/customers/import', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          customers,
          options
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to import customers')
      }

      const data = await response.json()
      setResult(data.results)

      if (data.results.imported > 0 || data.results.updated > 0) {
        toast.success(`Successfully processed ${data.results.imported + data.results.updated} customers`)
        onSuccess()
      }

    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to import customers')
      console.error('Import error:', error)
    } finally {
      setImporting(false)
    }
  }

  const handleClose = () => {
    setFile(null)
    setResult(null)
    setOptions({ skipDuplicates: true, updateExisting: false })
    onClose()
  }

  const handleDownloadTemplate = async () => {
    try {
      const response = await fetch('/api/customers/import?format=csv')
      if (!response.ok) {
        throw new Error('Failed to download template')
      }

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = 'customer-import-template.csv'
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)

      toast.success('Template downloaded successfully')
    } catch (error) {
      toast.error('Failed to download template')
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Import Customers</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {!result ? (
            <>
              {/* File Upload */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Upload className="h-5 w-5 mr-2" />
                    Upload CSV File
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="file">Select CSV File</Label>
                    <Input
                      id="file"
                      type="file"
                      accept=".csv"
                      onChange={handleFileSelect}
                      ref={fileInputRef}
                    />
                  </div>

                  {file && (
                    <div className="p-3 bg-green-50 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <FileText className="h-4 w-4 text-green-600" />
                        <span className="text-sm font-medium text-green-800">{file.name}</span>
                        <Badge variant="outline">{(file.size / 1024).toFixed(1)} KB</Badge>
                      </div>
                    </div>
                  )}

                  <div className="text-sm text-gray-600">
                    <p>Upload a CSV file with customer data. The file should include columns like:</p>
                    <p className="mt-1 font-mono text-xs bg-gray-100 p-2 rounded">
                      name, email, phone, company, address, city, state, country, postalCode, industry, website, notes, status, tags
                    </p>
                  </div>

                  <Button variant="outline" onClick={handleDownloadTemplate} className="w-full">
                    <FileText className="h-4 w-4 mr-2" />
                    Download Template
                  </Button>
                </CardContent>
              </Card>

              {/* Import Options */}
              <Card>
                <CardHeader>
                  <CardTitle>Import Options</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="skipDuplicates"
                      checked={options.skipDuplicates}
                      onCheckedChange={(checked) => 
                        setOptions(prev => ({ ...prev, skipDuplicates: checked as boolean }))
                      }
                    />
                    <Label htmlFor="skipDuplicates">Skip duplicate customers (by email)</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="updateExisting"
                      checked={options.updateExisting}
                      onCheckedChange={(checked) => 
                        setOptions(prev => ({ ...prev, updateExisting: checked as boolean }))
                      }
                    />
                    <Label htmlFor="updateExisting">Update existing customers</Label>
                  </div>

                  <div className="text-sm text-gray-600">
                    <p>• Skip duplicates: Existing customers with the same email will be ignored</p>
                    <p>• Update existing: Existing customers will be updated with new data</p>
                  </div>
                </CardContent>
              </Card>
            </>
          ) : (
            /* Import Results */
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
                  Import Results
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{result.total}</div>
                    <div className="text-sm text-gray-500">Total</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{result.imported}</div>
                    <div className="text-sm text-gray-500">Imported</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">{result.updated}</div>
                    <div className="text-sm text-gray-500">Updated</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-600">{result.skipped}</div>
                    <div className="text-sm text-gray-500">Skipped</div>
                  </div>
                </div>

                {result.errors.length > 0 && (
                  <>
                    <Separator />
                    <div>
                      <h4 className="font-medium text-red-600 mb-2 flex items-center">
                        <XCircle className="h-4 w-4 mr-2" />
                        Errors ({result.errors.length})
                      </h4>
                      <div className="space-y-2 max-h-40 overflow-y-auto">
                        {result.errors.map((error, index) => (
                          <div key={index} className="p-2 bg-red-50 rounded text-sm">
                            <div className="font-medium text-red-800">Row {error.row}: {error.error}</div>
                            <div className="text-red-600 text-xs mt-1">
                              Data: {JSON.stringify(error.data)}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          )}

          {/* Actions */}
          <div className="flex justify-end space-x-2 pt-4 border-t">
            <Button variant="outline" onClick={handleClose}>
              {result ? 'Close' : 'Cancel'}
            </Button>
            {!result && (
              <Button onClick={handleImport} disabled={!file || importing}>
                {importing ? 'Importing...' : 'Import Customers'}
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
