(()=>{var e={};e.id=2048,e.ids=[2048],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},26149:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c});var r=t(50482),a=t(69108),n=t(62563),l=t.n(n),i=t(68300),o={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);t.d(s,o);let c=["",{children:["auth",{children:["reset-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,99297)),"C:\\proj\\nextjs-saas\\app\\auth\\reset-password\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,59504)),"C:\\proj\\nextjs-saas\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\proj\\nextjs-saas\\app\\auth\\reset-password\\page.tsx"],u="/auth/reset-password/page",m={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/auth/reset-password/page",pathname:"/auth/reset-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},54431:(e,s,t)=>{Promise.resolve().then(t.bind(t,81783))},64588:(e,s,t)=>{Promise.resolve().then(t.bind(t,56189)),Promise.resolve().then(t.bind(t,44669))},19634:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,2583,23)),Promise.resolve().then(t.t.bind(t,26840,23)),Promise.resolve().then(t.t.bind(t,38771,23)),Promise.resolve().then(t.t.bind(t,13225,23)),Promise.resolve().then(t.t.bind(t,9295,23)),Promise.resolve().then(t.t.bind(t,43982,23))},81783:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>v});var r=t(95344),a=t(3729),n=t(22254),l=t(20783),i=t.n(l),o=t(16212),c=t(92549),d=t(1586),u=t(61351),m=t(91700),x=t(7060),p=t(66138),h=t(63024),f=t(47958),g=t(1222),y=t(53148),b=t(44669);function v(){let e;let[s,t]=(0,a.useState)(""),[l,v]=(0,a.useState)(""),[j,w]=(0,a.useState)(!1),[N,S]=(0,a.useState)(!1),[C,P]=(0,a.useState)(!1),[k,Z]=(0,a.useState)(!1),[_,R]=(0,a.useState)(""),[B,q]=(0,a.useState)(null),M=(0,n.useRouter)(),z=(0,n.useSearchParams)().get("token");(0,a.useEffect)(()=>{if(!z){R("Invalid reset link"),q(!1);return}E(z)},[z]);let E=async e=>{try{let s=await fetch("/api/auth/verify-reset-token",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({token:e})});if(s.ok)q(!0);else{let e=await s.json();R(e.error||"Invalid or expired reset link"),q(!1)}}catch(e){console.error("Token verification error:",e),R("Failed to verify reset link"),q(!1)}},T=e=>e.length<8?"Password must be at least 8 characters long":/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(e)?null:"Password must contain at least one uppercase letter, one lowercase letter, and one number",I=async e=>{e.preventDefault(),R("");let t=T(s);if(t){R(t);return}if(s!==l){R("Passwords do not match");return}P(!0);try{let e=await fetch("/api/auth/reset-password",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({token:z,password:s})}),t=await e.json();e.ok?(Z(!0),b.toast.success("Password reset successfully!"),setTimeout(()=>{M.push("/auth/signin?reset=success")},3e3)):R(t.error||"Failed to reset password")}catch(e){console.error("Reset password error:",e),R("An error occurred. Please try again.")}finally{P(!1)}},A=(e=0,s.length>=8&&e++,/[a-z]/.test(s)&&e++,/[A-Z]/.test(s)&&e++,/\d/.test(s)&&e++,/[^a-zA-Z\d]/.test(s)&&e++,e),O=(e=>{switch(e){case 0:case 1:return{label:"Very Weak",color:"text-red-600"};case 2:return{label:"Weak",color:"text-orange-600"};case 3:return{label:"Fair",color:"text-yellow-600"};case 4:return{label:"Good",color:"text-blue-600"};case 5:return{label:"Strong",color:"text-green-600"};default:return{label:"",color:""}}})(A);return null===B?r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 p-4",children:(0,r.jsxs)("div",{className:"w-full max-w-md",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center mb-8",children:[r.jsx(m.Z,{className:"h-8 w-8 text-blue-600 mr-2"}),r.jsx("span",{className:"text-2xl font-bold text-gray-900",children:"Business SaaS"})]}),r.jsx(u.Zb,{children:r.jsx(u.aY,{className:"flex items-center justify-center py-8",children:(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"}),r.jsx("p",{className:"text-gray-600",children:"Verifying reset link..."})]})})})]})}):k?r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 p-4",children:(0,r.jsxs)("div",{className:"w-full max-w-md",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center mb-8",children:[r.jsx(m.Z,{className:"h-8 w-8 text-blue-600 mr-2"}),r.jsx("span",{className:"text-2xl font-bold text-gray-900",children:"Business SaaS"})]}),(0,r.jsxs)(u.Zb,{children:[(0,r.jsxs)(u.Ol,{className:"text-center",children:[r.jsx("div",{className:"flex justify-center mb-4",children:r.jsx(x.Z,{className:"h-12 w-12 text-green-600"})}),r.jsx(u.ll,{className:"text-2xl",children:"Password reset successful!"}),r.jsx(u.SZ,{children:"Your password has been updated. You can now sign in with your new password."})]}),(0,r.jsxs)(u.aY,{className:"space-y-4",children:[r.jsx(o.z,{asChild:!0,className:"w-full",children:r.jsx(i(),{href:"/auth/signin",children:"Continue to Sign In"})}),r.jsx("p",{className:"text-sm text-center text-gray-500",children:"Redirecting automatically in 3 seconds..."})]})]})]})}):B?r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 p-4",children:(0,r.jsxs)("div",{className:"w-full max-w-md",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center mb-8",children:[r.jsx(m.Z,{className:"h-8 w-8 text-blue-600 mr-2"}),r.jsx("span",{className:"text-2xl font-bold text-gray-900",children:"Business SaaS"})]}),(0,r.jsxs)(u.Zb,{children:[(0,r.jsxs)(u.Ol,{className:"space-y-1",children:[r.jsx(u.ll,{className:"text-2xl text-center",children:"Reset your password"}),r.jsx(u.SZ,{className:"text-center",children:"Enter your new password below"})]}),(0,r.jsxs)(u.aY,{children:[(0,r.jsxs)("form",{onSubmit:I,className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"password",children:"New Password"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx(f.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),r.jsx(c.I,{id:"password",type:j?"text":"password",placeholder:"Enter your new password",value:s,onChange:e=>t(e.target.value),className:"pl-10 pr-10",required:!0}),r.jsx(o.z,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>w(!j),children:j?r.jsx(g.Z,{className:"h-4 w-4"}):r.jsx(y.Z,{className:"h-4 w-4"})})]}),s&&r.jsx("div",{className:"space-y-2",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("div",{className:"flex-1 bg-gray-200 rounded-full h-2",children:r.jsx("div",{className:`h-2 rounded-full transition-all duration-300 ${A<=1?"bg-red-500":2===A?"bg-orange-500":3===A?"bg-yellow-500":4===A?"bg-blue-500":"bg-green-500"}`,style:{width:`${A/5*100}%`}})}),r.jsx("span",{className:`text-xs font-medium ${O.color}`,children:O.label})]})})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"confirmPassword",children:"Confirm New Password"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx(f.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),r.jsx(c.I,{id:"confirmPassword",type:N?"text":"password",placeholder:"Confirm your new password",value:l,onChange:e=>v(e.target.value),className:"pl-10 pr-10",required:!0}),r.jsx(o.z,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>S(!N),children:N?r.jsx(g.Z,{className:"h-4 w-4"}):r.jsx(y.Z,{className:"h-4 w-4"})})]})]}),_&&(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-red-600 bg-red-50 p-3 rounded-md",children:[r.jsx(p.Z,{className:"h-4 w-4"}),r.jsx("span",{children:_})]}),r.jsx(o.z,{type:"submit",className:"w-full",disabled:C,children:C?"Resetting Password...":"Reset Password"})]}),r.jsx("div",{className:"mt-6 text-center",children:(0,r.jsxs)(i(),{href:"/auth/signin",className:"inline-flex items-center text-sm text-blue-600 hover:underline",children:[r.jsx(h.Z,{className:"h-4 w-4 mr-1"}),"Back to Sign In"]})})]})]})]})}):r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 p-4",children:(0,r.jsxs)("div",{className:"w-full max-w-md",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center mb-8",children:[r.jsx(m.Z,{className:"h-8 w-8 text-blue-600 mr-2"}),r.jsx("span",{className:"text-2xl font-bold text-gray-900",children:"Business SaaS"})]}),(0,r.jsxs)(u.Zb,{children:[(0,r.jsxs)(u.Ol,{className:"text-center",children:[r.jsx("div",{className:"flex justify-center mb-4",children:r.jsx(p.Z,{className:"h-12 w-12 text-red-600"})}),r.jsx(u.ll,{className:"text-2xl",children:"Invalid reset link"}),r.jsx(u.SZ,{children:"This password reset link is invalid or has expired."})]}),(0,r.jsxs)(u.aY,{className:"space-y-4",children:[_&&r.jsx("div",{className:"p-4 bg-red-50 border border-red-200 rounded-lg text-red-800 text-center",children:_}),(0,r.jsxs)("div",{className:"space-y-3",children:[r.jsx(o.z,{asChild:!0,className:"w-full",children:r.jsx(i(),{href:"/auth/forgot-password",children:"Request New Reset Link"})}),r.jsx(o.z,{variant:"outline",asChild:!0,className:"w-full",children:(0,r.jsxs)(i(),{href:"/auth/signin",children:[r.jsx(h.Z,{className:"h-4 w-4 mr-2"}),"Back to Sign In"]})})]})]})]})]})})}},56189:(e,s,t)=>{"use strict";t.r(s),t.d(s,{Providers:()=>d});var r=t(95344),a=t(47674),n=t(6256),l=t(19115),i=t(26274),o=t(3729),c=t(66091);function d({children:e}){let[s]=(0,o.useState)(()=>new l.S({defaultOptions:{queries:{staleTime:6e4,retry:1}}}));return r.jsx(a.SessionProvider,{children:r.jsx(i.aH,{client:s,children:r.jsx(c.lY,{children:r.jsx(n.f,{attribute:"class",defaultTheme:"light",enableSystem:!0,disableTransitionOnChange:!0,children:e})})})})}},16212:(e,s,t)=>{"use strict";t.d(s,{z:()=>c});var r=t(95344),a=t(3729),n=t(32751),l=t(49247),i=t(91626);let o=(0,l.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef(({className:e,variant:s,size:t,asChild:a=!1,...l},c)=>{let d=a?n.g7:"button";return r.jsx(d,{className:(0,i.cn)(o({variant:s,size:t,className:e})),ref:c,...l})});c.displayName="Button"},61351:(e,s,t)=>{"use strict";t.d(s,{Ol:()=>i,SZ:()=>c,Zb:()=>l,aY:()=>d,eW:()=>u,ll:()=>o});var r=t(95344),a=t(3729),n=t(91626);let l=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));l.displayName="Card";let i=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...s}));i.displayName="CardHeader";let o=a.forwardRef(({className:e,...s},t)=>r.jsx("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));o.displayName="CardTitle";let c=a.forwardRef(({className:e,...s},t)=>r.jsx("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",e),...s}));c.displayName="CardDescription";let d=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,n.cn)("p-6 pt-0",e),...s}));d.displayName="CardContent";let u=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",e),...s}));u.displayName="CardFooter"},92549:(e,s,t)=>{"use strict";t.d(s,{I:()=>l});var r=t(95344),a=t(3729),n=t(91626);let l=a.forwardRef(({className:e,type:s,...t},a)=>r.jsx("input",{type:s,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...t}));l.displayName="Input"},1586:(e,s,t)=>{"use strict";t.d(s,{_:()=>c});var r=t(95344),a=t(3729),n=t(14217),l=t(49247),i=t(91626);let o=(0,l.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=a.forwardRef(({className:e,...s},t)=>r.jsx(n.f,{ref:t,className:(0,i.cn)(o(),e),...s}));c.displayName=n.f.displayName},66091:(e,s,t)=>{"use strict";t.d(s,{TC:()=>o,lY:()=>i});var r=t(95344),a=t(3729);let n={appName:"SaaS Platform",logoUrl:"",faviconUrl:"",primaryColor:"#3b82f6",secondaryColor:"#64748b",accentColor:"#10b981",backgroundColor:"#ffffff",textColor:"#1f2937",theme:"light",fontFamily:"Inter, sans-serif",customCss:""},l=(0,a.createContext)(void 0);function i({children:e}){let[s,t]=(0,a.useState)(n),[i,o]=(0,a.useState)(!0);(0,a.useEffect)(()=>{c()},[]);let c=async()=>{try{let e=await fetch("/api/global-config/branding"),s=await e.json();s.success&&s.branding?(t({...n,...s.branding}),d({...n,...s.branding})):d(n)}catch(e){console.error("Error fetching branding config:",e),d(n)}finally{o(!1)}},d=e=>{let s=document.documentElement;if(s.style.setProperty("--primary-color",e.primaryColor),s.style.setProperty("--secondary-color",e.secondaryColor),s.style.setProperty("--accent-color",e.accentColor),s.style.setProperty("--background-color",e.backgroundColor),s.style.setProperty("--text-color",e.textColor),s.style.setProperty("--font-family",e.fontFamily),document.body.className=document.body.className.replace(/theme-\w+/g,""),document.body.classList.add(`theme-${e.theme}`),document.title=e.appName,e.faviconUrl){let s=document.querySelector('link[rel="icon"]');s||((s=document.createElement("link")).rel="icon",document.head.appendChild(s)),s.href=e.faviconUrl}let t=document.getElementById("custom-branding-css");e.customCss?(t||((t=document.createElement("style")).id="custom-branding-css",document.head.appendChild(t)),t.textContent=e.customCss):t&&t.remove();let r=document.querySelector('meta[name="theme-color"]');r||((r=document.createElement("meta")).name="theme-color",document.head.appendChild(r)),r.content=e.primaryColor};return r.jsx(l.Provider,{value:{branding:s,updateBranding:e=>{let r={...s,...e};t(r),d(r)},loading:i},children:e})}function o(){let e=(0,a.useContext)(l);if(void 0===e)throw Error("useBranding must be used within a BrandingProvider");return e}},91626:(e,s,t)=>{"use strict";t.d(s,{cn:()=>n});var r=t(56815),a=t(79377);function n(...e){return(0,a.m6)((0,r.W)(e))}},66138:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},63024:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},7060:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1222:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},53148:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},47958:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},22254:(e,s,t)=>{e.exports=t(14767)},99297:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>n,__esModule:()=>a,default:()=>l});let r=(0,t(86843).createProxy)(String.raw`C:\proj\nextjs-saas\app\auth\reset-password\page.tsx`),{__esModule:a,$$typeof:n}=r,l=r.default},59504:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x,metadata:()=>m});var r=t(25036),a=t(80265),n=t.n(a),l=t(86843);let i=(0,l.createProxy)(String.raw`C:\proj\nextjs-saas\components\providers.tsx`),{__esModule:o,$$typeof:c}=i;i.default;let d=(0,l.createProxy)(String.raw`C:\proj\nextjs-saas\components\providers.tsx#Providers`);var u=t(69636);t(67272);let m={title:{default:"Business SaaS - Complete Business Management Solution",template:"%s | Business SaaS"},description:"Complete SaaS solution for business management including CRM, invoicing, quotations, and more.",keywords:["SaaS","Business Management","CRM","Invoicing","Quotations"],authors:[{name:"Business SaaS Team"}],creator:"Business SaaS",openGraph:{type:"website",locale:"en_US",url:process.env.NEXT_PUBLIC_APP_URL,title:"Business SaaS - Complete Business Management Solution",description:"Complete SaaS solution for business management including CRM, invoicing, quotations, and more.",siteName:"Business SaaS"},twitter:{card:"summary_large_image",title:"Business SaaS - Complete Business Management Solution",description:"Complete SaaS solution for business management including CRM, invoicing, quotations, and more.",creator:"@businesssaas"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function x({children:e}){return r.jsx("html",{lang:"en",suppressHydrationWarning:!0,children:r.jsx("body",{className:n().className,children:(0,r.jsxs)(d,{children:[e,r.jsx(u.x7,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#4ade80",secondary:"#fff"}},error:{duration:4e3,iconTheme:{primary:"#ef4444",secondary:"#fff"}}}})]})})})}},67272:()=>{},14217:(e,s,t)=>{"use strict";t.d(s,{f:()=>i});var r=t(3729),a=t(62409),n=t(95344),l=r.forwardRef((e,s)=>(0,n.jsx)(a.WV.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));l.displayName="Label";var i=l},62409:(e,s,t)=>{"use strict";t.d(s,{WV:()=>i,jH:()=>o});var r=t(3729),a=t(81202),n=t(32751),l=t(95344),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,s)=>{let t=(0,n.Z8)(`Primitive.${s}`),a=r.forwardRef((e,r)=>{let{asChild:a,...n}=e,i=a?t:s;return(0,l.jsx)(i,{...n,ref:r})});return a.displayName=`Primitive.${s}`,{...e,[s]:a}},{});function o(e,s){e&&a.flushSync(()=>e.dispatchEvent(s))}}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[1638,7948,6671,4626],()=>t(26149));module.exports=r})();