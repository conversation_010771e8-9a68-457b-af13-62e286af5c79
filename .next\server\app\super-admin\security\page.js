(()=>{var e={};e.id=3105,e.ids=[3105],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},29984:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>o,routeModule:()=>x,tree:()=>d});var a=t(50482),i=t(69108),r=t(62563),n=t.n(r),c=t(68300),l={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>c[e]);t.d(s,l);let d=["",{children:["super-admin",{children:["security",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,3186)),"C:\\proj\\nextjs-saas\\app\\super-admin\\security\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,11285)),"C:\\proj\\nextjs-saas\\app\\super-admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,59504)),"C:\\proj\\nextjs-saas\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\proj\\nextjs-saas\\app\\super-admin\\security\\page.tsx"],u="/super-admin/security/page",m={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/super-admin/security/page",pathname:"/super-admin/security",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},97260:(e,s,t)=>{Promise.resolve().then(t.bind(t,8291))},8291:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>Z});var a=t(95344),i=t(3729),r=t(61351),n=t(69436),c=t(16212),l=t(71809),d=t(1586),o=t(25757),u=t(81036),m=t(15366),x=t(73229),p=t(82965),h=t(53148),f=t(45961),j=t(88534),v=t(47958),y=t(23485),g=t(13746),b=t(33733),N=t(80508),w=t(25545),k=t(7060);function Z(){let[e,s]=(0,i.useState)(!0),[t,Z]=(0,i.useState)(new Date),[S]=(0,i.useState)([{id:"1",type:"failed_login",user:"<EMAIL>",description:"Multiple failed login attempts",timestamp:new Date(Date.now()-3e5),severity:"high",location:"New York, US",ip:"*************"},{id:"2",type:"login",user:"<EMAIL>",description:"Successful admin login",timestamp:new Date(Date.now()-9e5),severity:"low",location:"London, UK",ip:"********"},{id:"3",type:"permission_change",user:"<EMAIL>",description:"User permissions modified",timestamp:new Date(Date.now()-18e5),severity:"medium",location:"London, UK",ip:"********"},{id:"4",type:"suspicious_activity",user:"<EMAIL>",description:"Unusual data access pattern detected",timestamp:new Date(Date.now()-27e5),severity:"critical",location:"Unknown",ip:"***********"}]),[C,P]=(0,i.useState)([{id:"1",name:"Two-Factor Authentication",description:"Require 2FA for all admin accounts",enabled:!0,category:"authentication"},{id:"2",name:"Session Timeout",description:"Automatically log out inactive users",enabled:!0,category:"authentication"},{id:"3",name:"IP Whitelist",description:"Restrict access to approved IP addresses",enabled:!1,category:"access_control"},{id:"4",name:"Audit Logging",description:"Log all administrative actions",enabled:!0,category:"monitoring"},{id:"5",name:"Data Encryption",description:"Encrypt sensitive data at rest",enabled:!0,category:"encryption"},{id:"6",name:"Intrusion Detection",description:"Monitor for suspicious activities",enabled:!0,category:"monitoring"}]);(0,i.useEffect)(()=>{let e=setTimeout(()=>{s(!1)},1e3);return()=>clearTimeout(e)},[]);let R=e=>{P(s=>s.map(s=>s.id===e?{...s,enabled:!s.enabled}:s))},M=e=>{switch(e){case"critical":case"high":return"destructive";case"medium":return"secondary";default:return"outline"}},_=e=>{switch(e){case"login":return a.jsx(m.Z,{className:"h-4 w-4 text-green-500"});case"failed_login":return a.jsx(x.Z,{className:"h-4 w-4 text-red-500"});case"permission_change":return a.jsx(p.Z,{className:"h-4 w-4 text-yellow-500"});case"data_access":return a.jsx(h.Z,{className:"h-4 w-4 text-blue-500"});case"suspicious_activity":return a.jsx(f.Z,{className:"h-4 w-4 text-red-500"});default:return a.jsx(j.Z,{className:"h-4 w-4 text-gray-500"})}},T=e=>{switch(e){case"authentication":return a.jsx(m.Z,{className:"h-4 w-4"});case"access_control":return a.jsx(v.Z,{className:"h-4 w-4"});case"monitoring":return a.jsx(h.Z,{className:"h-4 w-4"});case"encryption":return a.jsx(y.Z,{className:"h-4 w-4"});default:return a.jsx(g.Z,{className:"h-4 w-4"})}},D={totalEvents:S.length,criticalEvents:S.filter(e=>"critical"===e.severity).length,activeThreats:S.filter(e=>"critical"===e.severity||"high"===e.severity).length,securityScore:85};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Security Center"}),a.jsx("p",{className:"text-muted-foreground",children:"Monitor security events and manage security settings"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(n.C,{variant:"outline",className:"text-xs",children:["Last updated: ",t.toLocaleTimeString()]}),(0,a.jsxs)(c.z,{onClick:()=>{s(!0),Z(new Date),setTimeout(()=>s(!1),1e3)},disabled:e,size:"sm",children:[a.jsx(b.Z,{className:`h-4 w-4 mr-2 ${e?"animate-spin":""}`}),"Refresh"]})]})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(r.ll,{className:"text-sm font-medium",children:"Security Score"}),a.jsx(y.Z,{className:"h-4 w-4 text-green-500"})]}),(0,a.jsxs)(r.aY,{children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:[D.securityScore,"%"]}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"Overall security rating"})]})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(r.ll,{className:"text-sm font-medium",children:"Active Threats"}),a.jsx(f.Z,{className:"h-4 w-4 text-red-500"})]}),(0,a.jsxs)(r.aY,{children:[a.jsx("div",{className:"text-2xl font-bold text-red-600",children:D.activeThreats}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"Requiring attention"})]})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(r.ll,{className:"text-sm font-medium",children:"Security Events"}),a.jsx(j.Z,{className:"h-4 w-4 text-blue-500"})]}),(0,a.jsxs)(r.aY,{children:[a.jsx("div",{className:"text-2xl font-bold",children:D.totalEvents}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"Last 24 hours"})]})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(r.ll,{className:"text-sm font-medium",children:"Critical Events"}),a.jsx(x.Z,{className:"h-4 w-4 text-red-500"})]}),(0,a.jsxs)(r.aY,{children:[a.jsx("div",{className:"text-2xl font-bold text-red-600",children:D.criticalEvents}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"Immediate action needed"})]})]})]}),(0,a.jsxs)(o.mQ,{defaultValue:"events",className:"space-y-4",children:[(0,a.jsxs)(o.dr,{children:[a.jsx(o.SP,{value:"events",children:"Security Events"}),a.jsx(o.SP,{value:"settings",children:"Security Settings"}),a.jsx(o.SP,{value:"policies",children:"Access Policies"}),a.jsx(o.SP,{value:"monitoring",children:"Monitoring"})]}),a.jsx(o.nU,{value:"events",className:"space-y-4",children:(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{children:[a.jsx(r.ll,{children:"Recent Security Events"}),a.jsx(r.SZ,{children:"Latest security events and activities across the system"})]}),a.jsx(r.aY,{children:(0,a.jsxs)(u.iA,{children:[a.jsx(u.xD,{children:(0,a.jsxs)(u.SC,{children:[a.jsx(u.ss,{children:"Event"}),a.jsx(u.ss,{children:"User"}),a.jsx(u.ss,{children:"Location"}),a.jsx(u.ss,{children:"Time"}),a.jsx(u.ss,{children:"Severity"})]})}),a.jsx(u.RM,{children:S.map(e=>(0,a.jsxs)(u.SC,{children:[a.jsx(u.pj,{children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[_(e.type),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium",children:e.description}),(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:["IP: ",e.ip]})]})]})}),a.jsx(u.pj,{className:"font-medium",children:e.user}),a.jsx(u.pj,{children:(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[a.jsx(N.Z,{className:"h-3 w-3"}),a.jsx("span",{className:"text-sm",children:e.location})]})}),a.jsx(u.pj,{children:(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[a.jsx(w.Z,{className:"h-3 w-3"}),a.jsx("span",{className:"text-sm",children:e.timestamp.toLocaleTimeString()})]})}),a.jsx(u.pj,{children:a.jsx(n.C,{variant:M(e.severity),className:"capitalize",children:e.severity})})]},e.id))})]})})]})}),a.jsx(o.nU,{value:"settings",className:"space-y-4",children:(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{children:[a.jsx(r.ll,{children:"Security Configuration"}),a.jsx(r.SZ,{children:"Manage security settings and policies"})]}),a.jsx(r.aY,{children:a.jsx("div",{className:"space-y-6",children:["authentication","access_control","monitoring","encryption"].map(e=>(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-medium mb-3 capitalize flex items-center space-x-2",children:[T(e),a.jsx("span",{children:e.replace("_"," ")})]}),a.jsx("div",{className:"space-y-3",children:C.filter(s=>s.category===e).map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[a.jsx(d._,{htmlFor:e.id,className:"text-sm font-medium",children:e.name}),a.jsx("p",{className:"text-xs text-muted-foreground",children:e.description})]}),a.jsx(l.r,{id:e.id,checked:e.enabled,onCheckedChange:()=>R(e.id)})]},e.id))})]},e))})})]})}),a.jsx(o.nU,{value:"policies",className:"space-y-4",children:(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{children:[a.jsx(r.ll,{children:"Access Control Policies"}),a.jsx(r.SZ,{children:"Manage user access policies and permissions"})]}),a.jsx(r.aY,{children:a.jsx("div",{className:"space-y-4",children:(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("div",{className:"text-sm font-medium",children:"Password Policy"}),a.jsx("div",{className:"text-xs text-muted-foreground",children:"Minimum 8 characters, uppercase, lowercase, numbers, and symbols required"}),a.jsx(n.C,{variant:"outline",className:"text-xs",children:"Active"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("div",{className:"text-sm font-medium",children:"Session Management"}),a.jsx("div",{className:"text-xs text-muted-foreground",children:"30-minute timeout for inactive sessions"}),a.jsx(n.C,{variant:"outline",className:"text-xs",children:"Active"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("div",{className:"text-sm font-medium",children:"Role-Based Access"}),a.jsx("div",{className:"text-xs text-muted-foreground",children:"Permissions based on user roles and responsibilities"}),a.jsx(n.C,{variant:"outline",className:"text-xs",children:"Active"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("div",{className:"text-sm font-medium",children:"API Rate Limiting"}),a.jsx("div",{className:"text-xs text-muted-foreground",children:"1000 requests per hour per user"}),a.jsx(n.C,{variant:"outline",className:"text-xs",children:"Active"})]})]})})})]})}),a.jsx(o.nU,{value:"monitoring",className:"space-y-4",children:(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{children:[a.jsx(r.ll,{children:"Security Monitoring"}),a.jsx(r.SZ,{children:"Real-time security monitoring and threat detection"})]}),a.jsx(r.aY,{children:(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-3",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("div",{className:"text-sm font-medium",children:"Intrusion Detection"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(k.Z,{className:"h-4 w-4 text-green-500"}),a.jsx("span",{className:"text-sm",children:"Active"})]}),a.jsx("div",{className:"text-xs text-muted-foreground",children:"Monitoring for suspicious activities"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("div",{className:"text-sm font-medium",children:"Vulnerability Scanning"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(k.Z,{className:"h-4 w-4 text-green-500"}),a.jsx("span",{className:"text-sm",children:"Active"})]}),a.jsx("div",{className:"text-xs text-muted-foreground",children:"Daily automated security scans"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("div",{className:"text-sm font-medium",children:"Threat Intelligence"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(k.Z,{className:"h-4 w-4 text-green-500"}),a.jsx("span",{className:"text-sm",children:"Active"})]}),a.jsx("div",{className:"text-xs text-muted-foreground",children:"Real-time threat feed integration"})]})]})})]})})]})]})}},1586:(e,s,t)=>{"use strict";t.d(s,{_:()=>d});var a=t(95344),i=t(3729),r=t(14217),n=t(49247),c=t(91626);let l=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=i.forwardRef(({className:e,...s},t)=>a.jsx(r.f,{ref:t,className:(0,c.cn)(l(),e),...s}));d.displayName=r.f.displayName},71809:(e,s,t)=>{"use strict";t.d(s,{r:()=>w});var a=t(95344),i=t(3729),r=t(85222),n=t(31405),c=t(98462),l=t(33183),d=t(92062),o=t(63085),u=t(62409),m="Switch",[x,p]=(0,c.b)(m),[h,f]=x(m),j=i.forwardRef((e,s)=>{let{__scopeSwitch:t,name:c,checked:d,defaultChecked:o,required:x,disabled:p,value:f="on",onCheckedChange:j,form:v,...y}=e,[N,w]=i.useState(null),k=(0,n.e)(s,e=>w(e)),Z=i.useRef(!1),S=!N||v||!!N.closest("form"),[C,P]=(0,l.T)({prop:d,defaultProp:o??!1,onChange:j,caller:m});return(0,a.jsxs)(h,{scope:t,checked:C,disabled:p,children:[(0,a.jsx)(u.WV.button,{type:"button",role:"switch","aria-checked":C,"aria-required":x,"data-state":b(C),"data-disabled":p?"":void 0,disabled:p,value:f,...y,ref:k,onClick:(0,r.M)(e.onClick,e=>{P(e=>!e),S&&(Z.current=e.isPropagationStopped(),Z.current||e.stopPropagation())})}),S&&(0,a.jsx)(g,{control:N,bubbles:!Z.current,name:c,value:f,checked:C,required:x,disabled:p,form:v,style:{transform:"translateX(-100%)"}})]})});j.displayName=m;var v="SwitchThumb",y=i.forwardRef((e,s)=>{let{__scopeSwitch:t,...i}=e,r=f(v,t);return(0,a.jsx)(u.WV.span,{"data-state":b(r.checked),"data-disabled":r.disabled?"":void 0,...i,ref:s})});y.displayName=v;var g=i.forwardRef(({__scopeSwitch:e,control:s,checked:t,bubbles:r=!0,...c},l)=>{let u=i.useRef(null),m=(0,n.e)(u,l),x=(0,d.D)(t),p=(0,o.t)(s);return i.useEffect(()=>{let e=u.current;if(!e)return;let s=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(x!==t&&s){let a=new Event("click",{bubbles:r});s.call(e,t),e.dispatchEvent(a)}},[x,t,r]),(0,a.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:t,...c,tabIndex:-1,ref:m,style:{...c.style,...p,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function b(e){return e?"checked":"unchecked"}g.displayName="SwitchBubbleInput";var N=t(91626);let w=i.forwardRef(({className:e,...s},t)=>a.jsx(j,{className:(0,N.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...s,ref:t,children:a.jsx(y,{className:(0,N.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));w.displayName=j.displayName},81036:(e,s,t)=>{"use strict";t.d(s,{RM:()=>l,SC:()=>d,iA:()=>n,pj:()=>u,ss:()=>o,xD:()=>c});var a=t(95344),i=t(3729),r=t(91626);let n=i.forwardRef(({className:e,...s},t)=>a.jsx("div",{className:"relative w-full overflow-auto",children:a.jsx("table",{ref:t,className:(0,r.cn)("w-full caption-bottom text-sm",e),...s})}));n.displayName="Table";let c=i.forwardRef(({className:e,...s},t)=>a.jsx("thead",{ref:t,className:(0,r.cn)("[&_tr]:border-b",e),...s}));c.displayName="TableHeader";let l=i.forwardRef(({className:e,...s},t)=>a.jsx("tbody",{ref:t,className:(0,r.cn)("[&_tr:last-child]:border-0",e),...s}));l.displayName="TableBody",i.forwardRef(({className:e,...s},t)=>a.jsx("tfoot",{ref:t,className:(0,r.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...s})).displayName="TableFooter";let d=i.forwardRef(({className:e,...s},t)=>a.jsx("tr",{ref:t,className:(0,r.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...s}));d.displayName="TableRow";let o=i.forwardRef(({className:e,...s},t)=>a.jsx("th",{ref:t,className:(0,r.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...s}));o.displayName="TableHead";let u=i.forwardRef(({className:e,...s},t)=>a.jsx("td",{ref:t,className:(0,r.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...s}));u.displayName="TableCell",i.forwardRef(({className:e,...s},t)=>a.jsx("caption",{ref:t,className:(0,r.cn)("mt-4 text-sm text-muted-foreground",e),...s})).displayName="TableCaption"},25757:(e,s,t)=>{"use strict";t.d(s,{mQ:()=>R,nU:()=>T,dr:()=>M,SP:()=>_});var a=t(95344),i=t(3729),r=t(85222),n=t(98462),c=t(34504),l=t(43234),d=t(62409),o=t(3975),u=t(33183),m=t(99048),x="Tabs",[p,h]=(0,n.b)(x,[c.Pc]),f=(0,c.Pc)(),[j,v]=p(x),y=i.forwardRef((e,s)=>{let{__scopeTabs:t,value:i,onValueChange:r,defaultValue:n,orientation:c="horizontal",dir:l,activationMode:p="automatic",...h}=e,f=(0,o.gm)(l),[v,y]=(0,u.T)({prop:i,onChange:r,defaultProp:n??"",caller:x});return(0,a.jsx)(j,{scope:t,baseId:(0,m.M)(),value:v,onValueChange:y,orientation:c,dir:f,activationMode:p,children:(0,a.jsx)(d.WV.div,{dir:f,"data-orientation":c,...h,ref:s})})});y.displayName=x;var g="TabsList",b=i.forwardRef((e,s)=>{let{__scopeTabs:t,loop:i=!0,...r}=e,n=v(g,t),l=f(t);return(0,a.jsx)(c.fC,{asChild:!0,...l,orientation:n.orientation,dir:n.dir,loop:i,children:(0,a.jsx)(d.WV.div,{role:"tablist","aria-orientation":n.orientation,...r,ref:s})})});b.displayName=g;var N="TabsTrigger",w=i.forwardRef((e,s)=>{let{__scopeTabs:t,value:i,disabled:n=!1,...l}=e,o=v(N,t),u=f(t),m=S(o.baseId,i),x=C(o.baseId,i),p=i===o.value;return(0,a.jsx)(c.ck,{asChild:!0,...u,focusable:!n,active:p,children:(0,a.jsx)(d.WV.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":x,"data-state":p?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:m,...l,ref:s,onMouseDown:(0,r.M)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():o.onValueChange(i)}),onKeyDown:(0,r.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&o.onValueChange(i)}),onFocus:(0,r.M)(e.onFocus,()=>{let e="manual"!==o.activationMode;p||n||!e||o.onValueChange(i)})})})});w.displayName=N;var k="TabsContent",Z=i.forwardRef((e,s)=>{let{__scopeTabs:t,value:r,forceMount:n,children:c,...o}=e,u=v(k,t),m=S(u.baseId,r),x=C(u.baseId,r),p=r===u.value,h=i.useRef(p);return i.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,a.jsx)(l.z,{present:n||p,children:({present:t})=>(0,a.jsx)(d.WV.div,{"data-state":p?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":m,hidden:!t,id:x,tabIndex:0,...o,ref:s,style:{...e.style,animationDuration:h.current?"0s":void 0},children:t&&c})})});function S(e,s){return`${e}-trigger-${s}`}function C(e,s){return`${e}-content-${s}`}Z.displayName=k;var P=t(91626);let R=y,M=i.forwardRef(({className:e,...s},t)=>a.jsx(b,{ref:t,className:(0,P.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s}));M.displayName=b.displayName;let _=i.forwardRef(({className:e,...s},t)=>a.jsx(w,{ref:t,className:(0,P.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s}));_.displayName=w.displayName;let T=i.forwardRef(({className:e,...s},t)=>a.jsx(Z,{ref:t,className:(0,P.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));T.displayName=Z.displayName},53148:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},80508:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},15366:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("UserCheck",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]])},73229:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},3186:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>r,__esModule:()=>i,default:()=>n});let a=(0,t(86843).createProxy)(String.raw`C:\proj\nextjs-saas\app\super-admin\security\page.tsx`),{__esModule:i,$$typeof:r}=a,n=a.default},14217:(e,s,t)=>{"use strict";t.d(s,{f:()=>c});var a=t(3729),i=t(62409),r=t(95344),n=a.forwardRef((e,s)=>(0,r.jsx)(i.WV.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));n.displayName="Label";var c=n},92062:(e,s,t)=>{"use strict";t.d(s,{D:()=>i});var a=t(3729);function i(e){let s=a.useRef({value:e,previous:e});return a.useMemo(()=>(s.current.value!==e&&(s.current.previous=s.current.value,s.current.value=e),s.current.previous),[e])}}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[1638,7948,6671,4626,7792,2506,1729,2125,3965],()=>t(29984));module.exports=a})();