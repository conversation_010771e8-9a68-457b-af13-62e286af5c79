'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { FileSignature, User, Trash2, Calendar, Shield } from 'lucide-react'
import { toast } from 'react-hot-toast'

interface Contract {
  id: string
  contractNumber: string
  title: string
  status: string
  signatureRequired: boolean
  customer: {
    id: string
    name: string
    company: string | null
  }
}

interface Signature {
  id: string
  signerName: string
  signerEmail: string
  signerRole: string | null
  signatureType: string
  signedAt: string
  ipAddress: string | null
  userAgent: string | null
  notes: string | null
  signedBy: {
    name: string | null
    email: string | null
  }
  createdAt: string
}

interface SignatureModalProps {
  open: boolean
  contract: Contract | null
  onClose: () => void
  onSuccess: () => void
}

export function SignatureModal({ open, contract, onClose, onSuccess }: SignatureModalProps) {
  const [signatures, setSignatures] = useState<Signature[]>([])
  const [loading, setLoading] = useState(false)
  const [submitting, setSubmitting] = useState(false)
  const [formData, setFormData] = useState({
    signerName: '',
    signerEmail: '',
    signerRole: '',
    signatureType: 'ELECTRONIC',
    notes: ''
  })

  const fetchSignatures = async () => {
    if (!contract) return

    try {
      setLoading(true)
      const response = await fetch(`/api/contracts/${contract.id}/signatures`)
      if (!response.ok) {
        throw new Error('Failed to fetch signatures')
      }

      const data = await response.json()
      setSignatures(data.signatures)
    } catch (error) {
      toast.error('Failed to load signatures')
      console.error('Error fetching signatures:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (open && contract) {
      fetchSignatures()
      // Pre-fill with customer information
      setFormData(prev => ({
        ...prev,
        signerName: contract.customer.name,
        signerEmail: '',
        signerRole: 'Client'
      }))
    }
  }, [open, contract])

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!contract) return

    if (!formData.signerName || !formData.signerEmail) {
      toast.error('Signer name and email are required')
      return
    }

    // Check if email already signed
    const existingSignature = signatures.find(
      sig => sig.signerEmail.toLowerCase() === formData.signerEmail.toLowerCase()
    )

    if (existingSignature) {
      toast.error('This email has already signed the contract')
      return
    }

    setSubmitting(true)
    try {
      const response = await fetch(`/api/contracts/${contract.id}/signatures`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          signerName: formData.signerName,
          signerEmail: formData.signerEmail,
          signerRole: formData.signerRole || undefined,
          signatureType: formData.signatureType,
          notes: formData.notes || undefined
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to record signature')
      }

      toast.success('Signature recorded successfully!')
      
      // Reset form
      setFormData({
        signerName: '',
        signerEmail: '',
        signerRole: '',
        signatureType: 'ELECTRONIC',
        notes: ''
      })

      // Refresh signatures and notify parent
      await fetchSignatures()
      onSuccess()
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to record signature')
    } finally {
      setSubmitting(false)
    }
  }

  const handleDeleteSignature = async (signatureId: string) => {
    if (!contract) return

    if (!confirm('Are you sure you want to delete this signature?')) {
      return
    }

    try {
      const response = await fetch(`/api/contracts/${contract.id}/signatures?signatureId=${signatureId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to delete signature')
      }

      toast.success('Signature deleted successfully!')
      await fetchSignatures()
      onSuccess()
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to delete signature')
    }
  }

  const handleClose = () => {
    setFormData({
      signerName: '',
      signerEmail: '',
      signerRole: '',
      signatureType: 'ELECTRONIC',
      notes: ''
    })
    setSignatures([])
    onClose()
  }

  if (!contract) return null

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DRAFT':
        return 'bg-gray-100 text-gray-800'
      case 'REVIEW':
        return 'bg-yellow-100 text-yellow-800'
      case 'SENT':
        return 'bg-blue-100 text-blue-800'
      case 'SIGNED':
        return 'bg-green-100 text-green-800'
      case 'ACTIVE':
        return 'bg-emerald-100 text-emerald-800'
      case 'COMPLETED':
        return 'bg-purple-100 text-purple-800'
      case 'CANCELLED':
        return 'bg-red-100 text-red-800'
      case 'EXPIRED':
        return 'bg-orange-100 text-orange-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const canAddSignature = ['SENT', 'REVIEW'].includes(contract.status)

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <FileSignature className="h-5 w-5 mr-2" />
            Signature Management
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Contract Summary */}
          <div className="p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-2">
                <FileSignature className="h-5 w-5 text-blue-600" />
                <span className="font-semibold">{contract.contractNumber}</span>
                <Badge className={getStatusColor(contract.status)}>
                  {contract.status}
                </Badge>
              </div>
              <div className="flex items-center space-x-2">
                <Shield className="h-4 w-4 text-gray-500" />
                <span className="text-sm text-gray-600">
                  Signature {contract.signatureRequired ? 'Required' : 'Optional'}
                </span>
              </div>
            </div>
            <div className="text-sm text-gray-600">
              <p className="font-medium">{contract.title}</p>
              <div className="flex items-center mt-1">
                <User className="h-4 w-4 mr-1" />
                <span>{contract.customer.name}</span>
                {contract.customer.company && (
                  <span className="ml-1">({contract.customer.company})</span>
                )}
              </div>
            </div>
          </div>

          {/* Record New Signature */}
          {canAddSignature && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Record New Signature</h3>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="signerName">Signer Name *</Label>
                    <Input
                      id="signerName"
                      value={formData.signerName}
                      onChange={(e) => handleInputChange('signerName', e.target.value)}
                      placeholder="Full name of signer"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="signerEmail">Signer Email *</Label>
                    <Input
                      id="signerEmail"
                      type="email"
                      value={formData.signerEmail}
                      onChange={(e) => handleInputChange('signerEmail', e.target.value)}
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="signerRole">Role (optional)</Label>
                    <Input
                      id="signerRole"
                      value={formData.signerRole}
                      onChange={(e) => handleInputChange('signerRole', e.target.value)}
                      placeholder="e.g., CEO, Manager, Client"
                    />
                  </div>
                  <div>
                    <Label htmlFor="signatureType">Signature Type</Label>
                    <Select value={formData.signatureType} onValueChange={(value) => handleInputChange('signatureType', value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="ELECTRONIC">Electronic</SelectItem>
                        <SelectItem value="DIGITAL">Digital</SelectItem>
                        <SelectItem value="WET_SIGNATURE">Wet Signature</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="notes">Notes (optional)</Label>
                  <Textarea
                    id="notes"
                    value={formData.notes}
                    onChange={(e) => handleInputChange('notes', e.target.value)}
                    placeholder="Additional notes about this signature..."
                    rows={3}
                  />
                </div>

                <Button type="submit" disabled={submitting}>
                  {submitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Recording...
                    </>
                  ) : (
                    <>
                      <FileSignature className="h-4 w-4 mr-2" />
                      Record Signature
                    </>
                  )}
                </Button>
              </form>
            </div>
          )}

          {!canAddSignature && (
            <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p className="text-yellow-800">
                Signatures can only be added when the contract is in "Sent" or "Review" status.
              </p>
            </div>
          )}

          <Separator />

          {/* Signature History */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Signature History</h3>
            {loading ? (
              <div className="flex items-center justify-center py-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              </div>
            ) : signatures.length === 0 ? (
              <p className="text-gray-500 text-center py-4">No signatures recorded</p>
            ) : (
              <div className="space-y-3">
                {signatures.map((signature) => (
                  <div key={signature.id} className="flex items-center justify-between p-4 bg-green-50 rounded-lg border border-green-200">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <FileSignature className="h-4 w-4 text-green-600" />
                        <span className="font-semibold text-green-800">Signed</span>
                        <Badge variant="outline">{signature.signatureType}</Badge>
                      </div>
                      <div className="text-sm space-y-1">
                        <div><strong>Name:</strong> {signature.signerName}</div>
                        <div><strong>Email:</strong> {signature.signerEmail}</div>
                        {signature.signerRole && (
                          <div><strong>Role:</strong> {signature.signerRole}</div>
                        )}
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-3 w-3" />
                          <span><strong>Signed:</strong> {new Date(signature.signedAt).toLocaleString()}</span>
                        </div>
                        {signature.notes && (
                          <div><strong>Notes:</strong> {signature.notes}</div>
                        )}
                        <div className="text-xs text-gray-500 mt-2">
                          IP: {signature.ipAddress || 'Unknown'} • 
                          Recorded by {signature.signedBy.name || signature.signedBy.email} on{' '}
                          {new Date(signature.createdAt).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteSignature(signature.id)}
                      className="text-red-600 hover:text-red-700 ml-4"
                      disabled={contract.status === 'ACTIVE'}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex justify-end pt-4 border-t">
            <Button variant="outline" onClick={handleClose}>
              Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
