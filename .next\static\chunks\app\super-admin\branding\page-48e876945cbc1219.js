(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5162],{99670:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(62898).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},70612:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(62898).Z)("Monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]])},20597:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(62898).Z)("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",key:"1xcu5"}],["circle",{cx:"17.5",cy:"10.5",r:".5",key:"736e4u"}],["circle",{cx:"8.5",cy:"7.5",r:".5",key:"clrty"}],["circle",{cx:"6.5",cy:"12.5",r:".5",key:"1s4xz9"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]])},64280:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(62898).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},92295:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(62898).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},21271:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(62898).Z)("Smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]])},31541:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(62898).Z)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},15244:function(e,t,r){Promise.resolve().then(r.bind(r,77003))},77003:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return w}});var a=r(57437),s=r(2265),o=r(82749),i=r(24033),n=r(27815),l=r(85754),c=r(45179),d=r(49842),u=r(23444),m=r(40110),p=r(20597),x=r(64280),h=r(99670),f=r(92295),g=r(70612),y=r(62898);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let v=(0,y.Z)("Tablet",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["line",{x1:"12",x2:"12.01",y1:"18",y2:"18",key:"1dp563"}]]);var b=r(21271);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let j=(0,y.Z)("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);var N=r(31541);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let k=(0,y.Z)("Type",[["polyline",{points:"4 7 4 4 20 4 20 7",key:"1nosan"}],["line",{x1:"9",x2:"15",y1:"20",y2:"20",key:"swin9y"}],["line",{x1:"12",x2:"12",y1:"4",y2:"20",key:"1tx1rr"}]]);var C=r(5925);function w(){var e;let{data:t,status:r}=(0,o.useSession)(),[y,w]=(0,s.useState)({logoUrl:"",logoUrlDark:"",faviconUrl:"",loginBackgroundUrl:"",primaryColor:"#3b82f6",secondaryColor:"#64748b",accentColor:"#10b981",backgroundColor:"#ffffff",textColor:"#1f2937",fontFamily:"Inter",headingFont:"Inter",fontSize:"14px",theme:"light",borderRadius:"8px",customCss:"",footerText:"Built with ❤️ by Your Company",copyrightText:"\xa9 2024 Your Company. All rights reserved.",socialLinks:{website:"",twitter:"",linkedin:"",facebook:"",instagram:"",youtube:""}}),[S,F]=(0,s.useState)(!0),[z,I]=(0,s.useState)(!1),[T,Z]=(0,s.useState)("desktop");if("loading"===r)return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})});"unauthenticated"===r&&(0,i.redirect)("/auth/signin"),(null==t?void 0:null===(e=t.user)||void 0===e?void 0:e.role)!=="SUPER_ADMIN"&&(0,i.redirect)("/dashboard");let E=async()=>{try{F(!0);let e=await fetch("/api/super-admin/branding"),t=await e.json();t.success&&w({...y,...t.branding})}catch(e){console.error("Error fetching branding:",e),C.toast.error("Failed to load branding configuration")}finally{F(!1)}},L=async()=>{try{I(!0);let e=await fetch("/api/super-admin/branding",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(y)}),t=await e.json();t.success?C.toast.success("Branding configuration saved successfully"):C.toast.error(t.error||"Failed to save branding configuration")}catch(e){console.error("Error saving branding:",e),C.toast.error("Failed to save branding configuration")}finally{I(!1)}};(0,s.useEffect)(()=>{E()},[]);let _=(e,t)=>{w(r=>({...r,[e]:t}))},P=(e,t)=>{w(r=>({...r,socialLinks:{...r.socialLinks,[e]:t}}))},D=async(e,t)=>{try{let r=new FormData;r.append("file",e),r.append("type",t);let a=await fetch("/api/super-admin/branding/upload",{method:"POST",body:r}),s=await a.json();s.success?(_("logo"===t?"logoUrl":"logoDark"===t?"logoUrlDark":"favicon"===t?"faviconUrl":"loginBackgroundUrl",s.url),C.toast.success("File uploaded successfully")):C.toast.error(s.error||"Failed to upload file")}catch(e){console.error("Error uploading file:",e),C.toast.error("Failed to upload file")}};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(p.Z,{className:"h-8 w-8 text-purple-600"}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Branding & Customization"})]}),(0,a.jsx)("p",{className:"text-gray-500 mt-1",children:"Customize your application's look and feel"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(l.z,{variant:"outline",onClick:E,disabled:S,children:[(0,a.jsx)(x.Z,{className:"h-4 w-4 mr-2 ".concat(S?"animate-spin":"")}),"Refresh"]}),(0,a.jsxs)(l.z,{variant:"outline",children:[(0,a.jsx)(h.Z,{className:"h-4 w-4 mr-2"}),"Preview"]}),(0,a.jsxs)(l.z,{onClick:L,disabled:z,children:[(0,a.jsx)(f.Z,{className:"h-4 w-4 mr-2 ".concat(z?"animate-spin":"")}),"Save Changes"]})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsx)(n.Ol,{children:(0,a.jsxs)(n.ll,{className:"flex items-center",children:[(0,a.jsx)(g.Z,{className:"h-5 w-5 mr-2"}),"Preview Mode"]})}),(0,a.jsx)(n.aY,{children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(l.z,{variant:"desktop"===T?"default":"outline",size:"sm",onClick:()=>Z("desktop"),children:[(0,a.jsx)(g.Z,{className:"h-4 w-4 mr-2"}),"Desktop"]}),(0,a.jsxs)(l.z,{variant:"tablet"===T?"default":"outline",size:"sm",onClick:()=>Z("tablet"),children:[(0,a.jsx)(v,{className:"h-4 w-4 mr-2"}),"Tablet"]}),(0,a.jsxs)(l.z,{variant:"mobile"===T?"default":"outline",size:"sm",onClick:()=>Z("mobile"),children:[(0,a.jsx)(b.Z,{className:"h-4 w-4 mr-2"}),"Mobile"]})]})})]}),(0,a.jsxs)(m.mQ,{defaultValue:"logos",className:"space-y-6",children:[(0,a.jsxs)(m.dr,{className:"grid w-full grid-cols-5",children:[(0,a.jsx)(m.SP,{value:"logos",children:"Logos & Images"}),(0,a.jsx)(m.SP,{value:"colors",children:"Colors"}),(0,a.jsx)(m.SP,{value:"typography",children:"Typography"}),(0,a.jsx)(m.SP,{value:"theme",children:"Theme"}),(0,a.jsx)(m.SP,{value:"footer",children:"Footer & Social"})]}),(0,a.jsx)(m.nU,{value:"logos",children:(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{children:[(0,a.jsxs)(n.ll,{className:"flex items-center",children:[(0,a.jsx)(j,{className:"h-5 w-5 mr-2"}),"Logos & Images"]}),(0,a.jsx)(n.SZ,{children:"Upload and manage your brand assets"})]}),(0,a.jsx)(n.aY,{className:"space-y-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(d._,{children:"Main Logo (Light Theme)"}),(0,a.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center",children:[y.logoUrl?(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("img",{src:y.logoUrl,alt:"Logo",className:"max-h-16 mx-auto"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Current logo"})]}):(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(j,{className:"h-12 w-12 mx-auto text-gray-400"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"No logo uploaded"})]}),(0,a.jsxs)(l.z,{variant:"outline",size:"sm",className:"mt-2",onClick:()=>{var e;return null===(e=document.getElementById("logo-upload"))||void 0===e?void 0:e.click()},children:[(0,a.jsx)(N.Z,{className:"h-4 w-4 mr-2"}),"Upload Logo"]}),(0,a.jsx)("input",{id:"logo-upload",type:"file",accept:"image/*",className:"hidden",onChange:e=>{var t;let r=null===(t=e.target.files)||void 0===t?void 0:t[0];r&&D(r,"logo")}})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(d._,{children:"Logo (Dark Theme)"}),(0,a.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center bg-gray-900",children:[y.logoUrlDark?(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("img",{src:y.logoUrlDark,alt:"Dark Logo",className:"max-h-16 mx-auto"}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Current dark logo"})]}):(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(j,{className:"h-12 w-12 mx-auto text-gray-400"}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"No dark logo uploaded"})]}),(0,a.jsxs)(l.z,{variant:"outline",size:"sm",className:"mt-2",onClick:()=>{var e;return null===(e=document.getElementById("logo-dark-upload"))||void 0===e?void 0:e.click()},children:[(0,a.jsx)(N.Z,{className:"h-4 w-4 mr-2"}),"Upload Dark Logo"]}),(0,a.jsx)("input",{id:"logo-dark-upload",type:"file",accept:"image/*",className:"hidden",onChange:e=>{var t;let r=null===(t=e.target.files)||void 0===t?void 0:t[0];r&&D(r,"logoDark")}})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(d._,{children:"Favicon"}),(0,a.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center",children:[y.faviconUrl?(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("img",{src:y.faviconUrl,alt:"Favicon",className:"w-8 h-8 mx-auto"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Current favicon"})]}):(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(j,{className:"h-8 w-8 mx-auto text-gray-400"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"No favicon uploaded"})]}),(0,a.jsxs)(l.z,{variant:"outline",size:"sm",className:"mt-2",onClick:()=>{var e;return null===(e=document.getElementById("favicon-upload"))||void 0===e?void 0:e.click()},children:[(0,a.jsx)(N.Z,{className:"h-4 w-4 mr-2"}),"Upload Favicon"]}),(0,a.jsx)("input",{id:"favicon-upload",type:"file",accept:"image/x-icon,image/png",className:"hidden",onChange:e=>{var t;let r=null===(t=e.target.files)||void 0===t?void 0:t[0];r&&D(r,"favicon")}})]}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Recommended: 32x32px ICO or PNG"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(d._,{children:"Login Background"}),(0,a.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center",children:[y.loginBackgroundUrl?(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("img",{src:y.loginBackgroundUrl,alt:"Background",className:"max-h-16 mx-auto rounded"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Current background"})]}):(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(j,{className:"h-12 w-12 mx-auto text-gray-400"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"No background uploaded"})]}),(0,a.jsxs)(l.z,{variant:"outline",size:"sm",className:"mt-2",onClick:()=>{var e;return null===(e=document.getElementById("background-upload"))||void 0===e?void 0:e.click()},children:[(0,a.jsx)(N.Z,{className:"h-4 w-4 mr-2"}),"Upload Background"]}),(0,a.jsx)("input",{id:"background-upload",type:"file",accept:"image/*",className:"hidden",onChange:e=>{var t;let r=null===(t=e.target.files)||void 0===t?void 0:t[0];r&&D(r,"background")}})]}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Recommended: 1920x1080px or higher"})]})]})})]})}),(0,a.jsx)(m.nU,{value:"colors",children:(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{children:[(0,a.jsxs)(n.ll,{className:"flex items-center",children:[(0,a.jsx)(p.Z,{className:"h-5 w-5 mr-2"}),"Color Scheme"]}),(0,a.jsx)(n.SZ,{children:"Customize your application's color palette"})]}),(0,a.jsxs)(n.aY,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d._,{htmlFor:"primaryColor",children:"Primary Color"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"color",id:"primaryColor",value:y.primaryColor,onChange:e=>_("primaryColor",e.target.value),className:"w-12 h-10 rounded border border-gray-300"}),(0,a.jsx)(c.I,{value:y.primaryColor,onChange:e=>_("primaryColor",e.target.value),placeholder:"#3b82f6"})]}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Main brand color for buttons, links, etc."})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d._,{htmlFor:"secondaryColor",children:"Secondary Color"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"color",id:"secondaryColor",value:y.secondaryColor,onChange:e=>_("secondaryColor",e.target.value),className:"w-12 h-10 rounded border border-gray-300"}),(0,a.jsx)(c.I,{value:y.secondaryColor,onChange:e=>_("secondaryColor",e.target.value),placeholder:"#64748b"})]}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Secondary elements and borders"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d._,{htmlFor:"accentColor",children:"Accent Color"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"color",id:"accentColor",value:y.accentColor,onChange:e=>_("accentColor",e.target.value),className:"w-12 h-10 rounded border border-gray-300"}),(0,a.jsx)(c.I,{value:y.accentColor,onChange:e=>_("accentColor",e.target.value),placeholder:"#10b981"})]}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Success states and highlights"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d._,{htmlFor:"backgroundColor",children:"Background Color"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"color",id:"backgroundColor",value:y.backgroundColor,onChange:e=>_("backgroundColor",e.target.value),className:"w-12 h-10 rounded border border-gray-300"}),(0,a.jsx)(c.I,{value:y.backgroundColor,onChange:e=>_("backgroundColor",e.target.value),placeholder:"#ffffff"})]}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Main background color"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d._,{htmlFor:"textColor",children:"Text Color"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"color",id:"textColor",value:y.textColor,onChange:e=>_("textColor",e.target.value),className:"w-12 h-10 rounded border border-gray-300"}),(0,a.jsx)(c.I,{value:y.textColor,onChange:e=>_("textColor",e.target.value),placeholder:"#1f2937"})]}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Primary text color"})]})]}),(0,a.jsxs)("div",{className:"mt-8",children:[(0,a.jsx)(d._,{className:"text-base font-medium",children:"Color Preview"}),(0,a.jsx)("div",{className:"mt-4 p-6 rounded-lg border",style:{backgroundColor:y.backgroundColor},children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{style:{color:y.textColor},className:"text-xl font-semibold",children:"Sample Heading"}),(0,a.jsx)("p",{style:{color:y.textColor},className:"opacity-80",children:"This is how your text will look with the selected colors."}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("button",{style:{backgroundColor:y.primaryColor},className:"px-4 py-2 text-white rounded-md",children:"Primary Button"}),(0,a.jsx)("button",{style:{backgroundColor:"transparent",color:y.secondaryColor,borderColor:y.secondaryColor},className:"px-4 py-2 border rounded-md",children:"Secondary Button"}),(0,a.jsx)("span",{style:{color:y.accentColor},className:"font-medium",children:"Accent Text"})]})]})})]})]})]})}),(0,a.jsx)(m.nU,{value:"typography",children:(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{children:[(0,a.jsxs)(n.ll,{className:"flex items-center",children:[(0,a.jsx)(k,{className:"h-5 w-5 mr-2"}),"Typography"]}),(0,a.jsx)(n.SZ,{children:"Configure fonts and text styling"})]}),(0,a.jsxs)(n.aY,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d._,{htmlFor:"fontFamily",children:"Body Font"}),(0,a.jsxs)("select",{id:"fontFamily",value:y.fontFamily,onChange:e=>_("fontFamily",e.target.value),className:"w-full p-2 border border-gray-300 rounded-md",children:[(0,a.jsx)("option",{value:"Inter",children:"Inter"}),(0,a.jsx)("option",{value:"Roboto",children:"Roboto"}),(0,a.jsx)("option",{value:"Open Sans",children:"Open Sans"}),(0,a.jsx)("option",{value:"Lato",children:"Lato"}),(0,a.jsx)("option",{value:"Poppins",children:"Poppins"}),(0,a.jsx)("option",{value:"Montserrat",children:"Montserrat"}),(0,a.jsx)("option",{value:"Source Sans Pro",children:"Source Sans Pro"}),(0,a.jsx)("option",{value:"system-ui",children:"System UI"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d._,{htmlFor:"headingFont",children:"Heading Font"}),(0,a.jsxs)("select",{id:"headingFont",value:y.headingFont,onChange:e=>_("headingFont",e.target.value),className:"w-full p-2 border border-gray-300 rounded-md",children:[(0,a.jsx)("option",{value:"Inter",children:"Inter"}),(0,a.jsx)("option",{value:"Roboto",children:"Roboto"}),(0,a.jsx)("option",{value:"Open Sans",children:"Open Sans"}),(0,a.jsx)("option",{value:"Lato",children:"Lato"}),(0,a.jsx)("option",{value:"Poppins",children:"Poppins"}),(0,a.jsx)("option",{value:"Montserrat",children:"Montserrat"}),(0,a.jsx)("option",{value:"Playfair Display",children:"Playfair Display"}),(0,a.jsx)("option",{value:"Merriweather",children:"Merriweather"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d._,{htmlFor:"fontSize",children:"Base Font Size"}),(0,a.jsxs)("select",{id:"fontSize",value:y.fontSize,onChange:e=>_("fontSize",e.target.value),className:"w-full p-2 border border-gray-300 rounded-md",children:[(0,a.jsx)("option",{value:"12px",children:"12px - Small"}),(0,a.jsx)("option",{value:"14px",children:"14px - Default"}),(0,a.jsx)("option",{value:"16px",children:"16px - Large"}),(0,a.jsx)("option",{value:"18px",children:"18px - Extra Large"})]})]})]}),(0,a.jsxs)("div",{className:"mt-8",children:[(0,a.jsx)(d._,{className:"text-base font-medium",children:"Typography Preview"}),(0,a.jsx)("div",{className:"mt-4 p-6 rounded-lg border bg-white",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h1",{style:{fontFamily:y.headingFont,fontSize:"2rem",color:y.textColor},className:"font-bold",children:"Main Heading (H1)"}),(0,a.jsx)("h2",{style:{fontFamily:y.headingFont,fontSize:"1.5rem",color:y.textColor},className:"font-semibold",children:"Section Heading (H2)"}),(0,a.jsx)("h3",{style:{fontFamily:y.headingFont,fontSize:"1.25rem",color:y.textColor},className:"font-medium",children:"Subsection Heading (H3)"}),(0,a.jsx)("p",{style:{fontFamily:y.fontFamily,fontSize:y.fontSize,color:y.textColor},children:"This is body text using the selected font family and size. It demonstrates how regular paragraph text will appear throughout your application."}),(0,a.jsx)("p",{style:{fontFamily:y.fontFamily,fontSize:y.fontSize,color:y.secondaryColor},className:"text-sm",children:"This is secondary text, often used for descriptions and less important information."})]})})]})]})]})}),(0,a.jsx)(m.nU,{value:"theme",children:(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{children:[(0,a.jsx)(n.ll,{children:"Theme Settings"}),(0,a.jsx)(n.SZ,{children:"Configure theme preferences and custom styling"})]}),(0,a.jsxs)(n.aY,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d._,{htmlFor:"theme",children:"Default Theme"}),(0,a.jsxs)("select",{id:"theme",value:y.theme,onChange:e=>_("theme",e.target.value),className:"w-full p-2 border border-gray-300 rounded-md",children:[(0,a.jsx)("option",{value:"light",children:"Light"}),(0,a.jsx)("option",{value:"dark",children:"Dark"}),(0,a.jsx)("option",{value:"auto",children:"Auto (System Preference)"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d._,{htmlFor:"borderRadius",children:"Border Radius"}),(0,a.jsxs)("select",{id:"borderRadius",value:y.borderRadius,onChange:e=>_("borderRadius",e.target.value),className:"w-full p-2 border border-gray-300 rounded-md",children:[(0,a.jsx)("option",{value:"0px",children:"Sharp (0px)"}),(0,a.jsx)("option",{value:"4px",children:"Small (4px)"}),(0,a.jsx)("option",{value:"8px",children:"Medium (8px)"}),(0,a.jsx)("option",{value:"12px",children:"Large (12px)"}),(0,a.jsx)("option",{value:"16px",children:"Extra Large (16px)"}),(0,a.jsx)("option",{value:"9999px",children:"Pill (9999px)"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d._,{htmlFor:"customCss",children:"Custom CSS"}),(0,a.jsx)(u.g,{id:"customCss",value:y.customCss,onChange:e=>_("customCss",e.target.value),placeholder:"/* Add your custom CSS here */ .custom-class { /* Your styles */ }",rows:10,className:"font-mono text-sm"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Add custom CSS to override default styles. Use with caution as this can affect application functionality."})]})]})]})}),(0,a.jsx)(m.nU,{value:"footer",children:(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{children:[(0,a.jsx)(n.ll,{children:"Footer & Social Links"}),(0,a.jsx)(n.SZ,{children:"Configure footer content and social media links"})]}),(0,a.jsxs)(n.aY,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d._,{htmlFor:"footerText",children:"Footer Text"}),(0,a.jsx)(c.I,{id:"footerText",value:y.footerText,onChange:e=>_("footerText",e.target.value),placeholder:"Built with ❤️ by Your Company"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d._,{htmlFor:"copyrightText",children:"Copyright Text"}),(0,a.jsx)(c.I,{id:"copyrightText",value:y.copyrightText,onChange:e=>_("copyrightText",e.target.value),placeholder:"\xa9 2024 Your Company. All rights reserved."})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(d._,{className:"text-base font-medium",children:"Social Media Links"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d._,{htmlFor:"website",children:"Website"}),(0,a.jsx)(c.I,{id:"website",value:y.socialLinks.website,onChange:e=>P("website",e.target.value),placeholder:"https://yourcompany.com"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d._,{htmlFor:"twitter",children:"Twitter"}),(0,a.jsx)(c.I,{id:"twitter",value:y.socialLinks.twitter,onChange:e=>P("twitter",e.target.value),placeholder:"https://twitter.com/yourcompany"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d._,{htmlFor:"linkedin",children:"LinkedIn"}),(0,a.jsx)(c.I,{id:"linkedin",value:y.socialLinks.linkedin,onChange:e=>P("linkedin",e.target.value),placeholder:"https://linkedin.com/company/yourcompany"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d._,{htmlFor:"facebook",children:"Facebook"}),(0,a.jsx)(c.I,{id:"facebook",value:y.socialLinks.facebook,onChange:e=>P("facebook",e.target.value),placeholder:"https://facebook.com/yourcompany"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d._,{htmlFor:"instagram",children:"Instagram"}),(0,a.jsx)(c.I,{id:"instagram",value:y.socialLinks.instagram,onChange:e=>P("instagram",e.target.value),placeholder:"https://instagram.com/yourcompany"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d._,{htmlFor:"youtube",children:"YouTube"}),(0,a.jsx)(c.I,{id:"youtube",value:y.socialLinks.youtube,onChange:e=>P("youtube",e.target.value),placeholder:"https://youtube.com/c/yourcompany"})]})]})]})]})]})})]})]})}},85754:function(e,t,r){"use strict";r.d(t,{z:function(){return c}});var a=r(57437),s=r(2265),o=r(67256),i=r(96061),n=r(1657);let l=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=s.forwardRef((e,t)=>{let{className:r,variant:s,size:i,asChild:c=!1,...d}=e,u=c?o.g7:"button";return(0,a.jsx)(u,{className:(0,n.cn)(l({variant:s,size:i,className:r})),ref:t,...d})});c.displayName="Button"},27815:function(e,t,r){"use strict";r.d(t,{Ol:function(){return n},SZ:function(){return c},Zb:function(){return i},aY:function(){return d},eW:function(){return u},ll:function(){return l}});var a=r(57437),s=r(2265),o=r(1657);let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,o.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...s})});i.displayName="Card";let n=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",r),...s})});n.displayName="CardHeader";let l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("h3",{ref:t,className:(0,o.cn)("text-2xl font-semibold leading-none tracking-tight",r),...s})});l.displayName="CardTitle";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("p",{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",r),...s})});c.displayName="CardDescription";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,o.cn)("p-6 pt-0",r),...s})});d.displayName="CardContent";let u=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,o.cn)("flex items-center p-6 pt-0",r),...s})});u.displayName="CardFooter"},45179:function(e,t,r){"use strict";r.d(t,{I:function(){return i}});var a=r(57437),s=r(2265),o=r(1657);let i=s.forwardRef((e,t)=>{let{className:r,type:s,...i}=e;return(0,a.jsx)("input",{type:s,className:(0,o.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...i})});i.displayName="Input"},49842:function(e,t,r){"use strict";r.d(t,{_:function(){return c}});var a=r(57437),s=r(2265),o=r(36743),i=r(96061),n=r(1657);let l=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(o.f,{ref:t,className:(0,n.cn)(l(),r),...s})});c.displayName=o.f.displayName},40110:function(e,t,r){"use strict";r.d(t,{SP:function(){return c},dr:function(){return l},mQ:function(){return n},nU:function(){return d}});var a=r(57437),s=r(2265),o=r(34522),i=r(1657);let n=o.fC,l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(o.aV,{ref:t,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",r),...s})});l.displayName=o.aV.displayName;let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(o.xz,{ref:t,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",r),...s})});c.displayName=o.xz.displayName;let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(o.VY,{ref:t,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",r),...s})});d.displayName=o.VY.displayName},23444:function(e,t,r){"use strict";r.d(t,{g:function(){return i}});var a=r(57437),s=r(2265),o=r(1657);let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("textarea",{className:(0,o.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...s})});i.displayName="Textarea"},1657:function(e,t,r){"use strict";r.d(t,{cn:function(){return o}});var a=r(57042),s=r(74769);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.m6)((0,a.W)(t))}},24033:function(e,t,r){e.exports=r(15313)},36743:function(e,t,r){"use strict";r.d(t,{f:function(){return n}});var a=r(2265),s=r(9381),o=r(57437),i=a.forwardRef((e,t)=>(0,o.jsx)(s.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var n=i},5925:function(e,t,r){"use strict";let a,s;r.r(t),r.d(t,{CheckmarkIcon:function(){return G},ErrorIcon:function(){return Y},LoaderIcon:function(){return q},ToastBar:function(){return en},ToastIcon:function(){return et},Toaster:function(){return eu},default:function(){return em},resolveValue:function(){return C},toast:function(){return P},useToaster:function(){return B},useToasterStore:function(){return E}});var o,i=r(2265);let n={data:""},l=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||n,c=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,d=/\/\*[^]*?\*\/|  +/g,u=/\n+/g,m=(e,t)=>{let r="",a="",s="";for(let o in e){let i=e[o];"@"==o[0]?"i"==o[1]?r=o+" "+i+";":a+="f"==o[1]?m(i,o):o+"{"+m(i,"k"==o[1]?"":t)+"}":"object"==typeof i?a+=m(i,t?t.replace(/([^,])+/g,e=>o.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)):o):null!=i&&(o=/^--/.test(o)?o:o.replace(/[A-Z]/g,"-$&").toLowerCase(),s+=m.p?m.p(o,i):o+":"+i+";")}return r+(t&&s?t+"{"+s+"}":s)+a},p={},x=e=>{if("object"==typeof e){let t="";for(let r in e)t+=r+x(e[r]);return t}return e},h=(e,t,r,a,s)=>{var o;let i=x(e),n=p[i]||(p[i]=(e=>{let t=0,r=11;for(;t<e.length;)r=101*r+e.charCodeAt(t++)>>>0;return"go"+r})(i));if(!p[n]){let t=i!==e?e:(e=>{let t,r,a=[{}];for(;t=c.exec(e.replace(d,""));)t[4]?a.shift():t[3]?(r=t[3].replace(u," ").trim(),a.unshift(a[0][r]=a[0][r]||{})):a[0][t[1]]=t[2].replace(u," ").trim();return a[0]})(e);p[n]=m(s?{["@keyframes "+n]:t}:t,r?"":"."+n)}let l=r&&p.g?p.g:null;return r&&(p.g=p[n]),o=p[n],l?t.data=t.data.replace(l,o):-1===t.data.indexOf(o)&&(t.data=a?o+t.data:t.data+o),n},f=(e,t,r)=>e.reduce((e,a,s)=>{let o=t[s];if(o&&o.call){let e=o(r),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;o=t?"."+t:e&&"object"==typeof e?e.props?"":m(e,""):!1===e?"":e}return e+a+(null==o?"":o)},"");function g(e){let t=this||{},r=e.call?e(t.p):e;return h(r.unshift?r.raw?f(r,[].slice.call(arguments,1),t.p):r.reduce((e,r)=>Object.assign(e,r&&r.call?r(t.p):r),{}):r,l(t.target),t.g,t.o,t.k)}g.bind({g:1});let y,v,b,j=g.bind({k:1});function N(e,t){let r=this||{};return function(){let a=arguments;function s(o,i){let n=Object.assign({},o),l=n.className||s.className;r.p=Object.assign({theme:v&&v()},n),r.o=/ *go\d+/.test(l),n.className=g.apply(r,a)+(l?" "+l:""),t&&(n.ref=i);let c=e;return e[0]&&(c=n.as||e,delete n.as),b&&c[0]&&b(n),y(c,n)}return t?t(s):s}}var k=e=>"function"==typeof e,C=(e,t)=>k(e)?e(t):e,w=(a=0,()=>(++a).toString()),S=()=>{if(void 0===s&&"u">typeof window){let e=matchMedia("(prefers-reduced-motion: reduce)");s=!e||e.matches}return s},F=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case 2:let{toast:r}=t;return F(e,{type:e.toasts.find(e=>e.id===r.id)?1:0,toast:r});case 3:let{toastId:a}=t;return{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,dismissed:!0,visible:!1}:e)};case 4:return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let s=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(e=>({...e,pauseDuration:e.pauseDuration+s}))}}},z=[],I={toasts:[],pausedAt:void 0},T=e=>{I=F(I,e),z.forEach(e=>{e(I)})},Z={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},E=(e={})=>{let[t,r]=(0,i.useState)(I),a=(0,i.useRef)(I);(0,i.useEffect)(()=>(a.current!==I&&r(I),z.push(r),()=>{let e=z.indexOf(r);e>-1&&z.splice(e,1)}),[]);let s=t.toasts.map(t=>{var r,a,s;return{...e,...e[t.type],...t,removeDelay:t.removeDelay||(null==(r=e[t.type])?void 0:r.removeDelay)||(null==e?void 0:e.removeDelay),duration:t.duration||(null==(a=e[t.type])?void 0:a.duration)||(null==e?void 0:e.duration)||Z[t.type],style:{...e.style,...null==(s=e[t.type])?void 0:s.style,...t.style}}});return{...t,toasts:s}},L=(e,t="blank",r)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...r,id:(null==r?void 0:r.id)||w()}),_=e=>(t,r)=>{let a=L(t,e,r);return T({type:2,toast:a}),a.id},P=(e,t)=>_("blank")(e,t);P.error=_("error"),P.success=_("success"),P.loading=_("loading"),P.custom=_("custom"),P.dismiss=e=>{T({type:3,toastId:e})},P.remove=e=>T({type:4,toastId:e}),P.promise=(e,t,r)=>{let a=P.loading(t.loading,{...r,...null==r?void 0:r.loading});return"function"==typeof e&&(e=e()),e.then(e=>{let s=t.success?C(t.success,e):void 0;return s?P.success(s,{id:a,...r,...null==r?void 0:r.success}):P.dismiss(a),e}).catch(e=>{let s=t.error?C(t.error,e):void 0;s?P.error(s,{id:a,...r,...null==r?void 0:r.error}):P.dismiss(a)}),e};var D=(e,t)=>{T({type:1,toast:{id:e,height:t}})},M=()=>{T({type:5,time:Date.now()})},U=new Map,R=1e3,O=(e,t=R)=>{if(U.has(e))return;let r=setTimeout(()=>{U.delete(e),T({type:4,toastId:e})},t);U.set(e,r)},B=e=>{let{toasts:t,pausedAt:r}=E(e);(0,i.useEffect)(()=>{if(r)return;let e=Date.now(),a=t.map(t=>{if(t.duration===1/0)return;let r=(t.duration||0)+t.pauseDuration-(e-t.createdAt);if(r<0){t.visible&&P.dismiss(t.id);return}return setTimeout(()=>P.dismiss(t.id),r)});return()=>{a.forEach(e=>e&&clearTimeout(e))}},[t,r]);let a=(0,i.useCallback)(()=>{r&&T({type:6,time:Date.now()})},[r]),s=(0,i.useCallback)((e,r)=>{let{reverseOrder:a=!1,gutter:s=8,defaultPosition:o}=r||{},i=t.filter(t=>(t.position||o)===(e.position||o)&&t.height),n=i.findIndex(t=>t.id===e.id),l=i.filter((e,t)=>t<n&&e.visible).length;return i.filter(e=>e.visible).slice(...a?[l+1]:[0,l]).reduce((e,t)=>e+(t.height||0)+s,0)},[t]);return(0,i.useEffect)(()=>{t.forEach(e=>{if(e.dismissed)O(e.id,e.removeDelay);else{let t=U.get(e.id);t&&(clearTimeout(t),U.delete(e.id))}})},[t]),{toasts:t,handlers:{updateHeight:D,startPause:M,endPause:a,calculateOffset:s}}},$=j`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,A=j`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,H=j`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,Y=N("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${$} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${A} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${H} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,V=j`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,q=N("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${V} 1s linear infinite;
`,W=j`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,Q=j`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,G=N("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${W} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${Q} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,J=N("div")`
  position: absolute;
`,K=N("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,X=j`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,ee=N("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${X} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,et=({toast:e})=>{let{icon:t,type:r,iconTheme:a}=e;return void 0!==t?"string"==typeof t?i.createElement(ee,null,t):t:"blank"===r?null:i.createElement(K,null,i.createElement(q,{...a}),"loading"!==r&&i.createElement(J,null,"error"===r?i.createElement(Y,{...a}):i.createElement(G,{...a})))},er=e=>`
0% {transform: translate3d(0,${-200*e}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,ea=e=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${-150*e}%,-1px) scale(.6); opacity:0;}
`,es=N("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,eo=N("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,ei=(e,t)=>{let r=e.includes("top")?1:-1,[a,s]=S()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[er(r),ea(r)];return{animation:t?`${j(a)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${j(s)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},en=i.memo(({toast:e,position:t,style:r,children:a})=>{let s=e.height?ei(e.position||t||"top-center",e.visible):{opacity:0},o=i.createElement(et,{toast:e}),n=i.createElement(eo,{...e.ariaProps},C(e.message,e));return i.createElement(es,{className:e.className,style:{...s,...r,...e.style}},"function"==typeof a?a({icon:o,message:n}):i.createElement(i.Fragment,null,o,n))});o=i.createElement,m.p=void 0,y=o,v=void 0,b=void 0;var el=({id:e,className:t,style:r,onHeightUpdate:a,children:s})=>{let o=i.useCallback(t=>{if(t){let r=()=>{a(e,t.getBoundingClientRect().height)};r(),new MutationObserver(r).observe(t,{subtree:!0,childList:!0,characterData:!0})}},[e,a]);return i.createElement("div",{ref:o,className:t,style:r},s)},ec=(e,t)=>{let r=e.includes("top"),a=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:S()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(r?1:-1)}px)`,...r?{top:0}:{bottom:0},...a}},ed=g`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,eu=({reverseOrder:e,position:t="top-center",toastOptions:r,gutter:a,children:s,containerStyle:o,containerClassName:n})=>{let{toasts:l,handlers:c}=B(r);return i.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...o},className:n,onMouseEnter:c.startPause,onMouseLeave:c.endPause},l.map(r=>{let o=r.position||t,n=ec(o,c.calculateOffset(r,{reverseOrder:e,gutter:a,defaultPosition:t}));return i.createElement(el,{id:r.id,key:r.id,onHeightUpdate:c.updateHeight,className:r.visible?ed:"",style:n},"custom"===r.type?C(r.message,r):s?s(r):i.createElement(en,{toast:r,position:o}))}))},em=P}},function(e){e.O(0,[6723,9502,2749,4522,2971,4938,1744],function(){return e(e.s=15244)}),_N_E=e.O()}]);