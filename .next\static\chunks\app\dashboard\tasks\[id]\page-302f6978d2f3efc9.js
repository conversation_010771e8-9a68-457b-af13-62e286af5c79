(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5892],{60091:function(e,t,s){Promise.resolve().then(s.bind(s,35142))},35142:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return k}});var a=s(57437),r=s(2265),n=s(24033),l=s(27815),i=s(85754),d=s(31478),c=s(47934),o=s(10548),u=s(11981),m=s(73067),x=s(49617),f=s(45367),h=s(99155),p=s(33673),g=s(53711),j=s(67972),N=s(28203),y=s(6141),b=s(5925),v=s(61396),w=s.n(v);function k(e){let{params:t}=e,[s,v]=(0,r.useState)(null),[k,C]=(0,r.useState)(!0),[D,Z]=(0,r.useState)(!1),R=(0,n.useRouter)(),z=async()=>{try{C(!0);let e=await fetch("/api/tasks/".concat(t.id));if(!e.ok){if(404===e.status){b.toast.error("Task not found"),R.push("/dashboard/tasks");return}throw Error("Failed to fetch task")}let s=await e.json();v(s.task)}catch(e){b.toast.error("Failed to load task"),console.error("Error fetching task:",e)}finally{C(!1)}};(0,r.useEffect)(()=>{z()},[t.id]);let E=async()=>{if(confirm("Are you sure you want to delete this task?"))try{if(!(await fetch("/api/tasks/".concat(t.id),{method:"DELETE"})).ok)throw Error("Failed to delete task");b.toast.success("Task deleted successfully"),R.push("/dashboard/tasks")}catch(e){b.toast.error("Failed to delete task"),console.error("Error deleting task:",e)}},T=async e=>{try{if(!(await fetch("/api/tasks/".concat(t.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:e})})).ok)throw Error("Failed to update task status");b.toast.success("Task status updated"),z()}catch(e){b.toast.error("Failed to update task status"),console.error("Error updating task status:",e)}};if(k)return(0,a.jsx)("div",{className:"container mx-auto py-6",children:(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})})});if(!s)return(0,a.jsx)("div",{className:"container mx-auto py-6",children:(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(u.Z,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,a.jsx)("p",{className:"text-gray-500",children:"Task not found"}),(0,a.jsx)(i.z,{asChild:!0,className:"mt-4",children:(0,a.jsxs)(w(),{href:"/dashboard/tasks",children:[(0,a.jsx)(m.Z,{className:"h-4 w-4 mr-2"}),"Back to Tasks"]})})]})});let O=s.dueDate&&new Date(s.dueDate)<new Date&&"DONE"!==s.status;return(0,a.jsxs)("div",{className:"container mx-auto py-6 space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(i.z,{variant:"outline",asChild:!0,children:(0,a.jsxs)(w(),{href:"/dashboard/tasks",children:[(0,a.jsx)(m.Z,{className:"h-4 w-4 mr-2"}),"Back to Tasks"]})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:s.title}),(0,a.jsx)("p",{className:"text-gray-600",children:"Task Details"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(i.z,{variant:"outline",onClick:()=>Z(!0),children:[(0,a.jsx)(x.Z,{className:"h-4 w-4 mr-2"}),"Edit"]}),(0,a.jsxs)(i.z,{variant:"outline",onClick:E,className:"text-red-600 hover:text-red-700",children:[(0,a.jsx)(f.Z,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,a.jsxs)(l.Zb,{children:[(0,a.jsx)(l.Ol,{children:(0,a.jsx)(l.ll,{children:"Task Information"})}),(0,a.jsxs)(l.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(e=>{let t={TODO:{label:"To Do",className:"bg-gray-100 text-gray-800"},IN_PROGRESS:{label:"In Progress",className:"bg-blue-100 text-blue-800"},REVIEW:{label:"Review",className:"bg-yellow-100 text-yellow-800"},DONE:{label:"Done",className:"bg-green-100 text-green-800"},CANCELLED:{label:"Cancelled",className:"bg-red-100 text-red-800"}},s=t[e]||t.TODO;return(0,a.jsx)(d.C,{className:s.className,children:s.label})})(s.status),(e=>{let t={LOW:{label:"Low",className:"bg-gray-100 text-gray-600"},MEDIUM:{label:"Medium",className:"bg-blue-100 text-blue-600"},HIGH:{label:"High",className:"bg-orange-100 text-orange-600"},URGENT:{label:"Urgent",className:"bg-red-100 text-red-600"},CRITICAL:{label:"Critical",className:"bg-red-200 text-red-800"}},s=t[e]||t.MEDIUM;return(0,a.jsx)(d.C,{className:s.className,children:s.label})})(s.priority),(0,a.jsx)(d.C,{variant:"outline",children:s.type}),s.category&&(0,a.jsx)(d.C,{variant:"outline",children:s.category})]}),"DONE"!==s.status&&(0,a.jsxs)(i.z,{onClick:()=>T("DONE"),size:"sm",children:[(0,a.jsx)(h.Z,{className:"h-4 w-4 mr-2"}),"Mark Complete"]})]}),s.description&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Description"}),(0,a.jsx)("p",{className:"text-gray-700 whitespace-pre-wrap",children:s.description})]}),s.tags.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Tags"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:s.tags.map((e,t)=>(0,a.jsxs)(d.C,{variant:"secondary",className:"flex items-center space-x-1",children:[(0,a.jsx)(p.Z,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:e})]},t))})]})]})]}),(s.lead||s.customer||s.quotation||s.invoice||s.contract)&&(0,a.jsxs)(l.Zb,{children:[(0,a.jsx)(l.Ol,{children:(0,a.jsx)(l.ll,{children:"Related Records"})}),(0,a.jsxs)(l.aY,{className:"space-y-4",children:[s.lead&&(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-blue-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:"Lead"}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[s.lead.firstName," ",s.lead.lastName,s.lead.companyName&&" (".concat(s.lead.companyName,")")]})]}),(0,a.jsx)(i.z,{variant:"outline",size:"sm",asChild:!0,children:(0,a.jsx)(w(),{href:"/dashboard/leads/".concat(s.lead.id),children:(0,a.jsx)(g.Z,{className:"h-4 w-4"})})})]}),s.customer&&(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-green-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:"Customer"}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[s.customer.name,s.customer.company&&" (".concat(s.customer.company,")")]})]}),(0,a.jsx)(i.z,{variant:"outline",size:"sm",asChild:!0,children:(0,a.jsx)(w(),{href:"/dashboard/customers/".concat(s.customer.id),children:(0,a.jsx)(g.Z,{className:"h-4 w-4"})})})]}),s.quotation&&(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-purple-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:"Quotation"}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[s.quotation.title," - $",s.quotation.total.toLocaleString()]})]}),(0,a.jsx)(i.z,{variant:"outline",size:"sm",asChild:!0,children:(0,a.jsx)(w(),{href:"/dashboard/quotations/".concat(s.quotation.id),children:(0,a.jsx)(g.Z,{className:"h-4 w-4"})})})]})]})]})]}),(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)(l.Zb,{children:[(0,a.jsx)(l.Ol,{children:(0,a.jsx)(l.ll,{children:"Details"})}),(0,a.jsxs)(l.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(j.Z,{className:"h-4 w-4 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Assigned To"}),(0,a.jsx)("p",{className:"font-medium",children:s.assignedTo?s.assignedTo.name||s.assignedTo.email:"Unassigned"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(j.Z,{className:"h-4 w-4 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Created By"}),(0,a.jsx)("p",{className:"font-medium",children:s.createdBy.name||s.createdBy.email})]})]}),(0,a.jsx)(c.Z,{}),s.startDate&&(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(N.Z,{className:"h-4 w-4 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Start Date"}),(0,a.jsx)("p",{className:"font-medium",children:new Date(s.startDate).toLocaleDateString()})]})]}),s.dueDate&&(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(N.Z,{className:"h-4 w-4 ".concat(O?"text-red-500":"text-gray-400")}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Due Date"}),(0,a.jsxs)("p",{className:"font-medium ".concat(O?"text-red-600":""),children:[new Date(s.dueDate).toLocaleDateString(),O&&" (Overdue)"]})]})]}),s.completedAt&&(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(h.Z,{className:"h-4 w-4 text-green-500"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Completed"}),(0,a.jsx)("p",{className:"font-medium text-green-600",children:new Date(s.completedAt).toLocaleDateString()})]})]}),(0,a.jsx)(c.Z,{}),(s.estimatedHours||s.actualHours)&&(0,a.jsxs)("div",{className:"space-y-2",children:[s.estimatedHours&&(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(y.Z,{className:"h-4 w-4 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Estimated Hours"}),(0,a.jsxs)("p",{className:"font-medium",children:[s.estimatedHours,"h"]})]})]}),s.actualHours&&(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(y.Z,{className:"h-4 w-4 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Actual Hours"}),(0,a.jsxs)("p",{className:"font-medium",children:[s.actualHours,"h"]})]})]})]}),(0,a.jsx)(c.Z,{}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[(0,a.jsxs)("p",{children:["Created: ",new Date(s.createdAt).toLocaleString()]}),(0,a.jsxs)("p",{children:["Updated: ",new Date(s.updatedAt).toLocaleString()]})]})]})]})})]}),D&&(0,a.jsx)(o.t,{task:s,onClose:()=>Z(!1),onSuccess:()=>{Z(!1),z()}})]})}},31478:function(e,t,s){"use strict";s.d(t,{C:function(){return i}});var a=s(57437);s(2265);var r=s(96061),n=s(1657);let l=(0,r.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600",info:"border-transparent bg-blue-500 text-white hover:bg-blue-600"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:s,...r}=e;return(0,a.jsx)("div",{className:(0,n.cn)(l({variant:s}),t),...r})}},85754:function(e,t,s){"use strict";s.d(t,{z:function(){return c}});var a=s(57437),r=s(2265),n=s(67256),l=s(96061),i=s(1657);let d=(0,l.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=r.forwardRef((e,t)=>{let{className:s,variant:r,size:l,asChild:c=!1,...o}=e,u=c?n.g7:"button";return(0,a.jsx)(u,{className:(0,i.cn)(d({variant:r,size:l,className:s})),ref:t,...o})});c.displayName="Button"},27815:function(e,t,s){"use strict";s.d(t,{Ol:function(){return i},SZ:function(){return c},Zb:function(){return l},aY:function(){return o},eW:function(){return u},ll:function(){return d}});var a=s(57437),r=s(2265),n=s(1657);let l=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...r})});l.displayName="Card";let i=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",s),...r})});i.displayName="CardHeader";let d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",s),...r})});d.displayName="CardTitle";let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",s),...r})});c.displayName="CardDescription";let o=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",s),...r})});o.displayName="CardContent";let u=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",s),...r})});u.displayName="CardFooter"},42706:function(e,t,s){"use strict";s.d(t,{$N:function(){return h},Be:function(){return p},Vq:function(){return d},cN:function(){return f},cZ:function(){return m},fK:function(){return x},hg:function(){return c},t9:function(){return u}});var a=s(57437),r=s(2265),n=s(28712),l=s(82549),i=s(1657);let d=n.fC,c=n.xz,o=n.h_;n.x8;let u=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.aV,{ref:t,className:(0,i.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...r})});u.displayName=n.aV.displayName;let m=r.forwardRef((e,t)=>{let{className:s,children:r,...d}=e;return(0,a.jsxs)(o,{children:[(0,a.jsx)(u,{}),(0,a.jsxs)(n.VY,{ref:t,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",s),...d,children:[r,(0,a.jsxs)(n.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(l.Z,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});m.displayName=n.VY.displayName;let x=e=>{let{className:t,...s}=e;return(0,a.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...s})};x.displayName="DialogHeader";let f=e=>{let{className:t,...s}=e;return(0,a.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...s})};f.displayName="DialogFooter";let h=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.Dx,{ref:t,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",s),...r})});h.displayName=n.Dx.displayName;let p=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.dk,{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",s),...r})});p.displayName=n.dk.displayName},45179:function(e,t,s){"use strict";s.d(t,{I:function(){return l}});var a=s(57437),r=s(2265),n=s(1657);let l=r.forwardRef((e,t)=>{let{className:s,type:r,...l}=e;return(0,a.jsx)("input",{type:r,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:t,...l})});l.displayName="Input"},49842:function(e,t,s){"use strict";s.d(t,{_:function(){return c}});var a=s(57437),r=s(2265),n=s(36743),l=s(96061),i=s(1657);let d=(0,l.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.f,{ref:t,className:(0,i.cn)(d(),s),...r})});c.displayName=n.f.displayName},45509:function(e,t,s){"use strict";s.d(t,{Bw:function(){return h},Ph:function(){return o},Ql:function(){return p},i4:function(){return m},ki:function(){return u}});var a=s(57437),r=s(2265),n=s(99530),l=s(83523),i=s(9224),d=s(62442),c=s(1657);let o=n.fC;n.ZA;let u=n.B4,m=r.forwardRef((e,t)=>{let{className:s,children:r,...i}=e;return(0,a.jsxs)(n.xz,{ref:t,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",s),...i,children:[r,(0,a.jsx)(n.JO,{asChild:!0,children:(0,a.jsx)(l.Z,{className:"h-4 w-4 opacity-50"})})]})});m.displayName=n.xz.displayName;let x=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.u_,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",s),...r,children:(0,a.jsx)(i.Z,{className:"h-4 w-4"})})});x.displayName=n.u_.displayName;let f=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.$G,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",s),...r,children:(0,a.jsx)(l.Z,{className:"h-4 w-4"})})});f.displayName=n.$G.displayName;let h=r.forwardRef((e,t)=>{let{className:s,children:r,position:l="popper",...i}=e;return(0,a.jsx)(n.h_,{children:(0,a.jsxs)(n.VY,{ref:t,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===l&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:l,...i,children:[(0,a.jsx)(x,{}),(0,a.jsx)(n.l_,{className:(0,c.cn)("p-1","popper"===l&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),(0,a.jsx)(f,{})]})})});h.displayName=n.VY.displayName,r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.__,{ref:t,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",s),...r})}).displayName=n.__.displayName;let p=r.forwardRef((e,t)=>{let{className:s,children:r,...l}=e;return(0,a.jsxs)(n.ck,{ref:t,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...l,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(n.wU,{children:(0,a.jsx)(d.Z,{className:"h-4 w-4"})})}),(0,a.jsx)(n.eT,{children:r})]})});p.displayName=n.ck.displayName,r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.Z0,{ref:t,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",s),...r})}).displayName=n.Z0.displayName},47934:function(e,t,s){"use strict";s.d(t,{Z:function(){return i}});var a=s(57437),r=s(2265),n=s(26823),l=s(1657);let i=r.forwardRef((e,t)=>{let{className:s,orientation:r="horizontal",decorative:i=!0,...d}=e;return(0,a.jsx)(n.f,{ref:t,decorative:i,orientation:r,className:(0,l.cn)("shrink-0 bg-border","horizontal"===r?"h-[1px] w-full":"h-full w-[1px]",s),...d})});i.displayName=n.f.displayName},1657:function(e,t,s){"use strict";s.d(t,{cn:function(){return n}});var a=s(57042),r=s(74769);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.m6)((0,a.W)(t))}}},function(e){e.O(0,[6723,9502,1706,4138,1396,4997,2977,548,2971,4938,1744],function(){return e(e.s=60091)}),_N_E=e.O()}]);