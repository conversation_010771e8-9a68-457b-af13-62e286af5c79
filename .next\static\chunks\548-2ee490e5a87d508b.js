"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[548],{10548:function(e,s,a){a.d(s,{t:function(){return j}});var t=a(57437),l=a(2265),i=a(85754),r=a(45179),d=a(49842),n=a(23444),c=a(45509),o=a(42706),u=a(31478),h=a(82549),x=a(5925);function j(e){let{task:s,onClose:a,onSuccess:j}=e,[v,m]=(0,l.useState)({title:"",description:"",status:"TODO",priority:"MEDIUM",dueDate:"",startDate:"",assignedToId:"",leadId:"",customerId:"",type:"GENERAL",category:"",estimatedHours:"",actualHours:"",tags:[],customFields:{}}),[g,p]=(0,l.useState)([]),[y,f]=(0,l.useState)([]),[I,D]=(0,l.useState)([]),[N,E]=(0,l.useState)(!1),[C,k]=(0,l.useState)("");(0,l.useEffect)(()=>{if(s){var e,a;m({title:s.title,description:s.description||"",status:s.status,priority:s.priority,dueDate:s.dueDate?new Date(s.dueDate).toISOString().slice(0,16):"",startDate:s.startDate?new Date(s.startDate).toISOString().slice(0,16):"",assignedToId:s.assignedToId||"",leadId:"",customerId:"",type:s.type,category:s.category||"",estimatedHours:(null===(e=s.estimatedHours)||void 0===e?void 0:e.toString())||"",actualHours:(null===(a=s.actualHours)||void 0===a?void 0:a.toString())||"",tags:s.tags,customFields:s.customFields})}T(),w(),S()},[s]);let T=async()=>{try{p([])}catch(e){console.error("Error fetching users:",e)}},w=async()=>{try{let e=await fetch("/api/leads?limit=100");if(e.ok){let s=await e.json();f(s.leads||[])}}catch(e){console.error("Error fetching leads:",e)}},S=async()=>{try{let e=await fetch("/api/customers?limit=100");if(e.ok){let s=await e.json();D(s.customers||[])}}catch(e){console.error("Error fetching customers:",e)}},H=(e,s)=>{m(a=>({...a,[e]:s}))},b=()=>{C.trim()&&!v.tags.includes(C.trim())&&(m(e=>({...e,tags:[...e.tags,C.trim()]})),k(""))},Q=e=>{m(s=>({...s,tags:s.tags.filter(s=>s!==e)}))},F=async e=>{e.preventDefault(),E(!0);try{let e={...v,estimatedHours:v.estimatedHours?parseFloat(v.estimatedHours):void 0,actualHours:v.actualHours?parseFloat(v.actualHours):void 0,dueDate:v.dueDate||void 0,startDate:v.startDate||void 0,assignedToId:"unassigned"===v.assignedToId?null:v.assignedToId||void 0,leadId:"none"===v.leadId?null:v.leadId||void 0,customerId:"none"===v.customerId?null:v.customerId||void 0,category:v.category||void 0},a=s?"/api/tasks/".concat(s.id):"/api/tasks",t=await fetch(a,{method:s?"PUT":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to save task")}x.toast.success(s?"Task updated successfully!":"Task created successfully!"),j()}catch(e){x.toast.error(e instanceof Error?e.message:"Failed to save task")}finally{E(!1)}};return(0,t.jsx)(o.Vq,{open:!0,onOpenChange:a,children:(0,t.jsxs)(o.cZ,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsx)(o.fK,{children:(0,t.jsx)(o.$N,{children:s?"Edit Task":"Create New Task"})}),(0,t.jsxs)("form",{onSubmit:F,className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(d._,{htmlFor:"title",children:"Title *"}),(0,t.jsx)(r.I,{id:"title",value:v.title,onChange:e=>H("title",e.target.value),placeholder:"Enter task title",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(d._,{htmlFor:"description",children:"Description"}),(0,t.jsx)(n.g,{id:"description",value:v.description,onChange:e=>H("description",e.target.value),placeholder:"Enter task description",rows:3})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(d._,{htmlFor:"status",children:"Status"}),(0,t.jsxs)(c.Ph,{value:v.status,onValueChange:e=>H("status",e),children:[(0,t.jsx)(c.i4,{children:(0,t.jsx)(c.ki,{})}),(0,t.jsxs)(c.Bw,{children:[(0,t.jsx)(c.Ql,{value:"TODO",children:"To Do"}),(0,t.jsx)(c.Ql,{value:"IN_PROGRESS",children:"In Progress"}),(0,t.jsx)(c.Ql,{value:"REVIEW",children:"Review"}),(0,t.jsx)(c.Ql,{value:"DONE",children:"Done"}),(0,t.jsx)(c.Ql,{value:"CANCELLED",children:"Cancelled"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(d._,{htmlFor:"priority",children:"Priority"}),(0,t.jsxs)(c.Ph,{value:v.priority,onValueChange:e=>H("priority",e),children:[(0,t.jsx)(c.i4,{children:(0,t.jsx)(c.ki,{})}),(0,t.jsxs)(c.Bw,{children:[(0,t.jsx)(c.Ql,{value:"LOW",children:"Low"}),(0,t.jsx)(c.Ql,{value:"MEDIUM",children:"Medium"}),(0,t.jsx)(c.Ql,{value:"HIGH",children:"High"}),(0,t.jsx)(c.Ql,{value:"URGENT",children:"Urgent"}),(0,t.jsx)(c.Ql,{value:"CRITICAL",children:"Critical"})]})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(d._,{htmlFor:"type",children:"Type"}),(0,t.jsxs)(c.Ph,{value:v.type,onValueChange:e=>H("type",e),children:[(0,t.jsx)(c.i4,{children:(0,t.jsx)(c.ki,{})}),(0,t.jsxs)(c.Bw,{children:[(0,t.jsx)(c.Ql,{value:"GENERAL",children:"General"}),(0,t.jsx)(c.Ql,{value:"ONBOARDING",children:"Onboarding"}),(0,t.jsx)(c.Ql,{value:"FOLLOW_UP",children:"Follow Up"}),(0,t.jsx)(c.Ql,{value:"REVIEW",children:"Review"}),(0,t.jsx)(c.Ql,{value:"SUPPORT",children:"Support"}),(0,t.jsx)(c.Ql,{value:"DEVELOPMENT",children:"Development"}),(0,t.jsx)(c.Ql,{value:"MARKETING",children:"Marketing"}),(0,t.jsx)(c.Ql,{value:"SALES",children:"Sales"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(d._,{htmlFor:"category",children:"Category"}),(0,t.jsx)(r.I,{id:"category",value:v.category,onChange:e=>H("category",e.target.value),placeholder:"Enter category"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(d._,{htmlFor:"assignedToId",children:"Assigned To"}),(0,t.jsxs)(c.Ph,{value:v.assignedToId,onValueChange:e=>H("assignedToId",e),children:[(0,t.jsx)(c.i4,{children:(0,t.jsx)(c.ki,{placeholder:"Select assignee"})}),(0,t.jsxs)(c.Bw,{children:[(0,t.jsx)(c.Ql,{value:"unassigned",children:"Unassigned"}),g.map(e=>(0,t.jsx)(c.Ql,{value:e.id,children:e.name||e.email},e.id))]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(d._,{htmlFor:"startDate",children:"Start Date"}),(0,t.jsx)(r.I,{id:"startDate",type:"datetime-local",value:v.startDate,onChange:e=>H("startDate",e.target.value)})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(d._,{htmlFor:"dueDate",children:"Due Date"}),(0,t.jsx)(r.I,{id:"dueDate",type:"datetime-local",value:v.dueDate,onChange:e=>H("dueDate",e.target.value)})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(d._,{htmlFor:"estimatedHours",children:"Estimated Hours"}),(0,t.jsx)(r.I,{id:"estimatedHours",type:"number",step:"0.5",min:"0",value:v.estimatedHours,onChange:e=>H("estimatedHours",e.target.value),placeholder:"0"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(d._,{htmlFor:"actualHours",children:"Actual Hours"}),(0,t.jsx)(r.I,{id:"actualHours",type:"number",step:"0.5",min:"0",value:v.actualHours,onChange:e=>H("actualHours",e.target.value),placeholder:"0"})]})]})]}),(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(d._,{htmlFor:"leadId",children:"Related Lead"}),(0,t.jsxs)(c.Ph,{value:v.leadId,onValueChange:e=>H("leadId",e),children:[(0,t.jsx)(c.i4,{children:(0,t.jsx)(c.ki,{placeholder:"Select lead"})}),(0,t.jsxs)(c.Bw,{children:[(0,t.jsx)(c.Ql,{value:"none",children:"No lead"}),y.map(e=>(0,t.jsxs)(c.Ql,{value:e.id,children:[e.firstName," ",e.lastName," ",e.companyName&&"(".concat(e.companyName,")")]},e.id))]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(d._,{htmlFor:"customerId",children:"Related Customer"}),(0,t.jsxs)(c.Ph,{value:v.customerId,onValueChange:e=>H("customerId",e),children:[(0,t.jsx)(c.i4,{children:(0,t.jsx)(c.ki,{placeholder:"Select customer"})}),(0,t.jsxs)(c.Bw,{children:[(0,t.jsx)(c.Ql,{value:"none",children:"No customer"}),I.map(e=>(0,t.jsxs)(c.Ql,{value:e.id,children:[e.name," ",e.company&&"(".concat(e.company,")")]},e.id))]})]})]})]})}),(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsxs)("div",{children:[(0,t.jsx)(d._,{children:"Tags"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,t.jsx)(r.I,{value:C,onChange:e=>k(e.target.value),placeholder:"Add a tag",onKeyPress:e=>{"Enter"===e.key&&(e.preventDefault(),b())}}),(0,t.jsx)(i.z,{type:"button",onClick:b,size:"sm",children:"Add"})]}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:v.tags.map((e,s)=>(0,t.jsxs)(u.C,{variant:"secondary",className:"flex items-center space-x-1",children:[(0,t.jsx)("span",{children:e}),(0,t.jsx)(h.Z,{className:"h-3 w-3 cursor-pointer",onClick:()=>Q(e)})]},s))})]})}),(0,t.jsxs)("div",{className:"flex justify-end space-x-2 pt-4 border-t",children:[(0,t.jsx)(i.z,{type:"button",variant:"outline",onClick:a,children:"Cancel"}),(0,t.jsx)(i.z,{type:"submit",disabled:N,children:N?"Saving...":s?"Update Task":"Create Task"})]})]})]})})}},23444:function(e,s,a){a.d(s,{g:function(){return r}});var t=a(57437),l=a(2265),i=a(1657);let r=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:s,...l})});r.displayName="Textarea"}}]);