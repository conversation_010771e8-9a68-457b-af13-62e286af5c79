{"version": 3, "sources": ["../../src/useSuspenseQuery.ts"], "sourcesContent": ["'use client'\nimport { QueryObserver, skipToken } from '@tanstack/query-core'\nimport { useBaseQuery } from './useBaseQuery'\nimport { defaultThrowOnError } from './suspense'\nimport type { UseSuspenseQueryOptions, UseSuspenseQueryResult } from './types'\nimport type { DefaultError, QueryClient, QueryKey } from '@tanstack/query-core'\n\nexport function useSuspenseQuery<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: UseSuspenseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  queryClient?: QueryClient,\n): UseSuspenseQueryResult<TData, TError> {\n  if (process.env.NODE_ENV !== 'production') {\n    if ((options.queryFn as any) === skipToken) {\n      console.error('skipToken is not allowed for useSuspenseQuery')\n    }\n  }\n\n  return useBaseQuery(\n    {\n      ...options,\n      enabled: true,\n      suspense: true,\n      throwOnError: defaultThrowOnError,\n      placeholderData: undefined,\n    },\n    QueryObserver,\n    queryClient,\n  ) as UseSuspenseQueryResult<TData, TError>\n}\n"], "mappings": ";;;AACA,SAAS,eAAe,iBAAiB;AACzC,SAAS,oBAAoB;AAC7B,SAAS,2BAA2B;AAI7B,SAAS,iBAMd,SACA,aACuC;AACvC,MAAI,QAAQ,IAAI,aAAa,cAAc;AACzC,QAAK,QAAQ,YAAoB,WAAW;AAC1C,cAAQ,MAAM,+CAA+C;AAAA,IAC/D;AAAA,EACF;AAEA,SAAO;AAAA,IACL;AAAA,MACE,GAAG;AAAA,MACH,SAAS;AAAA,MACT,UAAU;AAAA,MACV,cAAc;AAAA,MACd,iBAAiB;AAAA,IACnB;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;", "names": []}