"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/super-admin/pricing-plans/page",{

/***/ "(app-pages-browser)/./app/super-admin/pricing-plans/page.tsx":
/*!************************************************!*\
  !*** ./app/super-admin/pricing-plans/page.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PricingPlansManagementPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Database,DollarSign,Edit,Eye,Plus,RefreshCw,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Database,DollarSign,Edit,Eye,Plus,RefreshCw,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Database,DollarSign,Edit,Eye,Plus,RefreshCw,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Database,DollarSign,Edit,Eye,Plus,RefreshCw,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Database,DollarSign,Edit,Eye,Plus,RefreshCw,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Database,DollarSign,Edit,Eye,Plus,RefreshCw,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Database,DollarSign,Edit,Eye,Plus,RefreshCw,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Database,DollarSign,Edit,Eye,Plus,RefreshCw,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Database,DollarSign,Edit,Eye,Plus,RefreshCw,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Database,DollarSign,Edit,Eye,Plus,RefreshCw,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction PricingPlansManagementPage() {\n    var _session_user;\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [plans, setPlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showCreateDialog, setShowCreateDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEditDialog, setShowEditDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingPlan, setEditingPlan] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        description: \"\",\n        monthlyPrice: 0,\n        yearlyPrice: 0,\n        currency: \"USD\",\n        maxUsers: 1,\n        maxCompanies: 1,\n        maxCustomers: 10,\n        maxQuotations: 5,\n        maxInvoices: 5,\n        maxContracts: 1,\n        maxStorage: 1073741824,\n        isActive: true,\n        isPublic: true,\n        trialDays: 0,\n        sortOrder: 0,\n        features: {\n            basicReporting: true,\n            emailSupport: true,\n            mobileApp: false,\n            advancedAnalytics: false,\n            customBranding: false,\n            apiAccess: false,\n            prioritySupport: false,\n            customIntegrations: false,\n            advancedSecurity: false,\n            dedicatedManager: false\n        }\n    });\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                lineNumber: 95,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n            lineNumber: 94,\n            columnNumber: 7\n        }, this);\n    }\n    if (status === \"unauthenticated\") {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.redirect)(\"/auth/signin\");\n    }\n    if ((session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.role) !== \"SUPER_ADMIN\") {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.redirect)(\"/dashboard\");\n    }\n    const fetchPlans = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/pricing-plans?includeInactive=true\");\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const data = await response.json();\n            console.log(\"Fetched plans data:\", data) // Debug log\n            ;\n            if (data.success && data.data) {\n                setPlans(data.data);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"Loaded \".concat(data.data.length, \" pricing plans\"));\n            } else {\n                console.error(\"API response error:\", data);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_9__.toast.error(data.error || \"Failed to load pricing plans\");\n            }\n        } catch (error) {\n            console.error(\"Error fetching plans:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Failed to load pricing plans\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchPlans();\n    }, []);\n    const handleToggleActive = async (planId, isActive)=>{\n        try {\n            const response = await fetch(\"/api/pricing-plans/\".concat(planId), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    isActive\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"Plan \".concat(isActive ? \"activated\" : \"deactivated\", \" successfully\"));\n                fetchPlans();\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_9__.toast.error(data.error || \"Failed to update plan\");\n            }\n        } catch (error) {\n            console.error(\"Error updating plan:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Failed to update plan\");\n        }\n    };\n    const handleDeletePlan = async (planId)=>{\n        if (!confirm(\"Are you sure you want to delete this pricing plan? This action cannot be undone.\")) {\n            return;\n        }\n        try {\n            const response = await fetch(\"/api/pricing-plans/\".concat(planId), {\n                method: \"DELETE\"\n            });\n            const data = await response.json();\n            if (data.success) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"Plan deleted successfully\");\n                fetchPlans();\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_9__.toast.error(data.error || \"Failed to delete plan\");\n            }\n        } catch (error) {\n            console.error(\"Error deleting plan:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Failed to delete plan\");\n        }\n    };\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\",\n            minimumFractionDigits: 0\n        }).format(amount);\n    };\n    const getFeatureCount = (features)=>{\n        return Object.values(features).filter(Boolean).length;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row sm:items-center justify-between gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"Pricing Plans Management\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 mt-1\",\n                                children: \"Manage subscription plans, pricing, and features\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                variant: \"outline\",\n                                onClick: fetchPlans,\n                                disabled: loading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 \".concat(loading ? \"animate-spin\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Add Plan\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Total Plans\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: plans.length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Active Plans\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-green-600\",\n                                                children: plans.filter((p)=>p.isActive).length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-8 w-8 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Public Plans\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-purple-600\",\n                                                children: plans.filter((p)=>p.isPublic).length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-8 w-8 text-purple-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                lineNumber: 223,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                children: [\n                                    \"Pricing Plans (\",\n                                    plans.length,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                children: \"Manage your subscription plans, pricing, and features\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                        lineNumber: 267,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center py-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.Table, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableRow, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                    children: \"Plan\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                    children: \"Pricing\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                    children: \"Limits\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                    children: \"Features\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                    children: \"Actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableBody, {\n                                        children: plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableRow, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold\",\n                                                                    children: plan.name.charAt(0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                    lineNumber: 296,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium text-gray-900\",\n                                                                                    children: plan.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                                    lineNumber: 301,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                plan.name.toLowerCase() === \"pro\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                    className: \"h-4 w-4 text-yellow-500\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                                    lineNumber: 303,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                            lineNumber: 300,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: plan.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                            lineNumber: 306,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                    lineNumber: 299,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: [\n                                                                        formatCurrency(plan.monthlyPrice),\n                                                                        \"/month\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                    lineNumber: 312,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                plan.yearlyPrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: [\n                                                                        formatCurrency(plan.yearlyPrice),\n                                                                        \"/year (\",\n                                                                        plan.yearlyDiscount,\n                                                                        \"% off)\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                    lineNumber: 314,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                plan.trialDays > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                    variant: \"secondary\",\n                                                                    className: \"text-xs mt-1\",\n                                                                    children: [\n                                                                        plan.trialDays,\n                                                                        \"-day trial\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                    lineNumber: 319,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                            lineNumber: 311,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-gray-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                            lineNumber: 328,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                plan.maxUsers,\n                                                                                \" users\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                            lineNumber: 329,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                    lineNumber: 327,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-gray-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                            lineNumber: 332,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: plan.formattedStorage\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                            lineNumber: 333,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                    lineNumber: 331,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: [\n                                                                        plan.maxCustomers,\n                                                                        \" customers, \",\n                                                                        plan.maxQuotations,\n                                                                        \" quotes\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                    lineNumber: 335,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                            variant: \"outline\",\n                                                            children: [\n                                                                getFeatureCount(plan.features),\n                                                                \" features\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                            lineNumber: 341,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_7__.Switch, {\n                                                                            checked: plan.isActive,\n                                                                            onCheckedChange: (checked)=>handleToggleActive(plan.id, checked)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                            lineNumber: 348,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm\",\n                                                                            children: plan.isActive ? \"Active\" : \"Inactive\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                            lineNumber: 352,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                    lineNumber: 347,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                plan.isPublic && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                    variant: \"secondary\",\n                                                                    className: \"text-xs\",\n                                                                    children: \"Public\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                    lineNumber: 357,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"sm\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                        lineNumber: 366,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                    lineNumber: 365,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"sm\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                        lineNumber: 369,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                    lineNumber: 368,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>handleDeletePlan(plan.id),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_DollarSign_Edit_Eye_Plus_RefreshCw_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                        lineNumber: 376,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                                    lineNumber: 371,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                            lineNumber: 364,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, plan.id, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n                lineNumber: 266,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\pricing-plans\\\\page.tsx\",\n        lineNumber: 200,\n        columnNumber: 5\n    }, this);\n}\n_s(PricingPlansManagementPage, \"y5y7foUXkRaM5jF1H2QQOnSNci4=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession\n    ];\n});\n_c = PricingPlansManagementPage;\nvar _c;\n$RefreshReg$(_c, \"PricingPlansManagementPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/super-admin/pricing-plans/page.tsx\n"));

/***/ })

});