"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/css-box-model";
exports.ids = ["vendor-chunks/css-box-model"];
exports.modules = {

/***/ "(ssr)/./node_modules/css-box-model/dist/css-box-model.esm.js":
/*!**************************************************************!*\
  !*** ./node_modules/css-box-model/dist/css-box-model.esm.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateBox: () => (/* binding */ calculateBox),\n/* harmony export */   createBox: () => (/* binding */ createBox),\n/* harmony export */   expand: () => (/* binding */ expand),\n/* harmony export */   getBox: () => (/* binding */ getBox),\n/* harmony export */   getRect: () => (/* binding */ getRect),\n/* harmony export */   offset: () => (/* binding */ offset),\n/* harmony export */   shrink: () => (/* binding */ shrink),\n/* harmony export */   withScroll: () => (/* binding */ withScroll)\n/* harmony export */ });\n/* harmony import */ var tiny_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tiny-invariant */ \"(ssr)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\");\n\nvar getRect = function getRect(_ref) {\n    var top = _ref.top, right = _ref.right, bottom = _ref.bottom, left = _ref.left;\n    var width = right - left;\n    var height = bottom - top;\n    var rect = {\n        top: top,\n        right: right,\n        bottom: bottom,\n        left: left,\n        width: width,\n        height: height,\n        x: left,\n        y: top,\n        center: {\n            x: (right + left) / 2,\n            y: (bottom + top) / 2\n        }\n    };\n    return rect;\n};\nvar expand = function expand(target, expandBy) {\n    return {\n        top: target.top - expandBy.top,\n        left: target.left - expandBy.left,\n        bottom: target.bottom + expandBy.bottom,\n        right: target.right + expandBy.right\n    };\n};\nvar shrink = function shrink(target, shrinkBy) {\n    return {\n        top: target.top + shrinkBy.top,\n        left: target.left + shrinkBy.left,\n        bottom: target.bottom - shrinkBy.bottom,\n        right: target.right - shrinkBy.right\n    };\n};\nvar shift = function shift(target, shiftBy) {\n    return {\n        top: target.top + shiftBy.y,\n        left: target.left + shiftBy.x,\n        bottom: target.bottom + shiftBy.y,\n        right: target.right + shiftBy.x\n    };\n};\nvar noSpacing = {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n};\nvar createBox = function createBox(_ref2) {\n    var borderBox = _ref2.borderBox, _ref2$margin = _ref2.margin, margin = _ref2$margin === void 0 ? noSpacing : _ref2$margin, _ref2$border = _ref2.border, border = _ref2$border === void 0 ? noSpacing : _ref2$border, _ref2$padding = _ref2.padding, padding = _ref2$padding === void 0 ? noSpacing : _ref2$padding;\n    var marginBox = getRect(expand(borderBox, margin));\n    var paddingBox = getRect(shrink(borderBox, border));\n    var contentBox = getRect(shrink(paddingBox, padding));\n    return {\n        marginBox: marginBox,\n        borderBox: getRect(borderBox),\n        paddingBox: paddingBox,\n        contentBox: contentBox,\n        margin: margin,\n        border: border,\n        padding: padding\n    };\n};\nvar parse = function parse(raw) {\n    var value = raw.slice(0, -2);\n    var suffix = raw.slice(-2);\n    if (suffix !== \"px\") {\n        return 0;\n    }\n    var result = Number(value);\n    !!isNaN(result) ?  true ? (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(false, \"Could not parse value [raw: \" + raw + \", without suffix: \" + value + \"]\") : 0 : void 0;\n    return result;\n};\nvar getWindowScroll = function getWindowScroll() {\n    return {\n        x: window.pageXOffset,\n        y: window.pageYOffset\n    };\n};\nvar offset = function offset(original, change) {\n    var borderBox = original.borderBox, border = original.border, margin = original.margin, padding = original.padding;\n    var shifted = shift(borderBox, change);\n    return createBox({\n        borderBox: shifted,\n        border: border,\n        margin: margin,\n        padding: padding\n    });\n};\nvar withScroll = function withScroll(original, scroll) {\n    if (scroll === void 0) {\n        scroll = getWindowScroll();\n    }\n    return offset(original, scroll);\n};\nvar calculateBox = function calculateBox(borderBox, styles) {\n    var margin = {\n        top: parse(styles.marginTop),\n        right: parse(styles.marginRight),\n        bottom: parse(styles.marginBottom),\n        left: parse(styles.marginLeft)\n    };\n    var padding = {\n        top: parse(styles.paddingTop),\n        right: parse(styles.paddingRight),\n        bottom: parse(styles.paddingBottom),\n        left: parse(styles.paddingLeft)\n    };\n    var border = {\n        top: parse(styles.borderTopWidth),\n        right: parse(styles.borderRightWidth),\n        bottom: parse(styles.borderBottomWidth),\n        left: parse(styles.borderLeftWidth)\n    };\n    return createBox({\n        borderBox: borderBox,\n        margin: margin,\n        padding: padding,\n        border: border\n    });\n};\nvar getBox = function getBox(el) {\n    var borderBox = el.getBoundingClientRect();\n    var styles = window.getComputedStyle(el);\n    return calculateBox(borderBox, styles);\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/css-box-model/dist/css-box-model.esm.js\n");

/***/ })

};
;