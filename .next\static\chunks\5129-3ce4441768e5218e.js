"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5129],{5129:function(e,s,t){t.d(s,{A:function(){return y}});var a=t(57437),r=t(2265),i=t(61865),l=t(37570),o=t(92160),n=t(85754),d=t(45179),c=t(49842),m=t(31478),h=t(42706),u=t(82549),p=t(9883),x=t(5925);let j=o.Ry({name:o.Z_().min(1,"Name is required"),email:o.Z_().email("Invalid email").optional().or(o.i0("")),phone:o.Z_().optional(),company:o.Z_().optional(),address:o.Z_().optional(),city:o.Z_().optional(),state:o.Z_().optional(),country:o.Z_().optional(),postalCode:o.Z_().optional(),industry:o.Z_().optional(),website:o.Z_().url("Invalid website URL").optional().or(o.i0("")),notes:o.Z_().optional(),status:o.Km(["ACTIVE","INACTIVE","PROSPECT"]).default("ACTIVE")});function y(e){let{isOpen:s,onClose:t,onSuccess:o,customer:y,mode:C}=e,[v,f]=(0,r.useState)(!1),[g,_]=(0,r.useState)((null==y?void 0:y.tags)||[]),[N,I]=(0,r.useState)(""),{register:b,handleSubmit:w,formState:{errors:E},reset:A,setValue:F}=(0,i.cI)({resolver:(0,l.F)(j),defaultValues:y?{name:y.name,email:y.email||"",phone:y.phone||"",company:y.company||"",address:y.address||"",city:y.city||"",state:y.state||"",country:y.country||"",postalCode:y.postalCode||"",industry:y.industry||"",website:y.website||"",notes:y.notes||"",status:y.status||"ACTIVE"}:{status:"ACTIVE"}}),Z=()=>{N.trim()&&!g.includes(N.trim())&&(_([...g,N.trim()]),I(""))},S=e=>{_(g.filter(s=>s!==e))},T=async e=>{f(!0);try{let s={...e,tags:g},a="create"===C?"/api/customers":"/api/customers/".concat(y.id),r=await fetch(a,{method:"create"===C?"POST":"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(!r.ok){let e=await r.json();throw Error(e.error||"Failed to save customer")}x.toast.success("Customer ".concat("create"===C?"created":"updated"," successfully")),A(),_([]),o(),t()}catch(e){x.toast.error(e instanceof Error?e.message:"An error occurred")}finally{f(!1)}},P=()=>{A(),_([]),I(""),t()};return(0,a.jsx)(h.Vq,{open:s,onOpenChange:P,children:(0,a.jsxs)(h.cZ,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)(h.fK,{children:[(0,a.jsx)(h.$N,{children:"create"===C?"Add New Customer":"Edit Customer"}),(0,a.jsx)(h.Be,{children:"create"===C?"Create a new customer profile with their contact information and details.":"Update the customer information and details."})]}),(0,a.jsxs)("form",{onSubmit:w(T),className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"name",children:"Name *"}),(0,a.jsx)(d.I,{id:"name",...b("name"),placeholder:"Customer name"}),E.name&&(0,a.jsx)("p",{className:"text-sm text-red-600 mt-1",children:E.name.message})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"email",children:"Email"}),(0,a.jsx)(d.I,{id:"email",type:"email",...b("email"),placeholder:"<EMAIL>"}),E.email&&(0,a.jsx)("p",{className:"text-sm text-red-600 mt-1",children:E.email.message})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"phone",children:"Phone"}),(0,a.jsx)(d.I,{id:"phone",...b("phone"),placeholder:"+****************"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"company",children:"Company"}),(0,a.jsx)(d.I,{id:"company",...b("company"),placeholder:"Company name"})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Address Information"}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"address",children:"Address"}),(0,a.jsx)(d.I,{id:"address",...b("address"),placeholder:"Street address"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"city",children:"City"}),(0,a.jsx)(d.I,{id:"city",...b("city"),placeholder:"City"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"state",children:"State"}),(0,a.jsx)(d.I,{id:"state",...b("state"),placeholder:"State"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"postalCode",children:"Postal Code"}),(0,a.jsx)(d.I,{id:"postalCode",...b("postalCode"),placeholder:"12345"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"country",children:"Country"}),(0,a.jsx)(d.I,{id:"country",...b("country"),placeholder:"Country"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"industry",children:"Industry"}),(0,a.jsx)(d.I,{id:"industry",...b("industry"),placeholder:"Technology, Healthcare, etc."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"website",children:"Website"}),(0,a.jsx)(d.I,{id:"website",...b("website"),placeholder:"https://example.com"}),E.website&&(0,a.jsx)("p",{className:"text-sm text-red-600 mt-1",children:E.website.message})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"status",children:"Status"}),(0,a.jsxs)("select",{id:"status",...b("status"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"ACTIVE",children:"Active"}),(0,a.jsx)("option",{value:"INACTIVE",children:"Inactive"}),(0,a.jsx)("option",{value:"PROSPECT",children:"Prospect"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{children:"Tags"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mb-2",children:g.map(e=>(0,a.jsxs)(m.C,{variant:"secondary",className:"flex items-center gap-1",children:[e,(0,a.jsx)(u.Z,{className:"h-3 w-3 cursor-pointer",onClick:()=>S(e)})]},e))}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(d.I,{value:N,onChange:e=>I(e.target.value),placeholder:"Add a tag",onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),Z())}),(0,a.jsx)(n.z,{type:"button",onClick:Z,size:"sm",children:(0,a.jsx)(p.Z,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"notes",children:"Notes"}),(0,a.jsx)("textarea",{id:"notes",...b("notes"),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Additional notes about the customer..."})]}),(0,a.jsxs)(h.cN,{children:[(0,a.jsx)(n.z,{type:"button",variant:"outline",onClick:P,children:"Cancel"}),(0,a.jsx)(n.z,{type:"submit",disabled:v,children:v?"Saving...":"create"===C?"Create Customer":"Update Customer"})]})]})]})})}}}]);