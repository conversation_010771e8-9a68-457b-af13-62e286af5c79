/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/oauth";
exports.ids = ["vendor-chunks/oauth"];
exports.modules = {

/***/ "(rsc)/./node_modules/oauth/index.js":
/*!*************************************!*\
  !*** ./node_modules/oauth/index.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("exports.OAuth = __webpack_require__(/*! ./lib/oauth */ \"(rsc)/./node_modules/oauth/lib/oauth.js\").OAuth;\nexports.OAuthEcho = __webpack_require__(/*! ./lib/oauth */ \"(rsc)/./node_modules/oauth/lib/oauth.js\").OAuthEcho;\nexports.OAuth2 = __webpack_require__(/*! ./lib/oauth2 */ \"(rsc)/./node_modules/oauth/lib/oauth2.js\").OAuth2;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb2F1dGgvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUFBLHVHQUE0QztBQUM1Q0EsK0dBQW9EO0FBQ3BEQSwyR0FBK0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0anMtc2Fhcy1hcHAvLi9ub2RlX21vZHVsZXMvb2F1dGgvaW5kZXguanM/NTg5NSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnRzLk9BdXRoID0gcmVxdWlyZShcIi4vbGliL29hdXRoXCIpLk9BdXRoO1xuZXhwb3J0cy5PQXV0aEVjaG8gPSByZXF1aXJlKFwiLi9saWIvb2F1dGhcIikuT0F1dGhFY2hvO1xuZXhwb3J0cy5PQXV0aDIgPSByZXF1aXJlKFwiLi9saWIvb2F1dGgyXCIpLk9BdXRoMjsiXSwibmFtZXMiOlsiZXhwb3J0cyIsIk9BdXRoIiwicmVxdWlyZSIsIk9BdXRoRWNobyIsIk9BdXRoMiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/oauth/lib/_utils.js":
/*!******************************************!*\
  !*** ./node_modules/oauth/lib/_utils.js ***!
  \******************************************/
/***/ ((module) => {

eval("// Returns true if this is a host that closes *before* it ends?!?!\nmodule.exports.isAnEarlyCloseHost = function(hostName) {\n    return hostName && hostName.match(\".*google(apis)?.com$\");\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb2F1dGgvbGliL191dGlscy5qcyIsIm1hcHBpbmdzIjoiQUFBQSxrRUFBa0U7QUFDbEVBLGlDQUFpQyxHQUFFLFNBQVVHLFFBQVE7SUFDbkQsT0FBT0EsWUFBWUEsU0FBU0MsS0FBSyxDQUFDO0FBQ3BDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dGpzLXNhYXMtYXBwLy4vbm9kZV9tb2R1bGVzL29hdXRoL2xpYi9fdXRpbHMuanM/MTM5MyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBSZXR1cm5zIHRydWUgaWYgdGhpcyBpcyBhIGhvc3QgdGhhdCBjbG9zZXMgKmJlZm9yZSogaXQgZW5kcz8hPyFcbm1vZHVsZS5leHBvcnRzLmlzQW5FYXJseUNsb3NlSG9zdD0gZnVuY3Rpb24oIGhvc3ROYW1lICkge1xuICByZXR1cm4gaG9zdE5hbWUgJiYgaG9zdE5hbWUubWF0Y2goXCIuKmdvb2dsZShhcGlzKT8uY29tJFwiKVxufSJdLCJuYW1lcyI6WyJtb2R1bGUiLCJleHBvcnRzIiwiaXNBbkVhcmx5Q2xvc2VIb3N0IiwiaG9zdE5hbWUiLCJtYXRjaCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth/lib/_utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/oauth/lib/oauth.js":
/*!*****************************************!*\
  !*** ./node_modules/oauth/lib/oauth.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var crypto = __webpack_require__(/*! crypto */ \"crypto\"), sha1 = __webpack_require__(/*! ./sha1 */ \"(rsc)/./node_modules/oauth/lib/sha1.js\"), http = __webpack_require__(/*! http */ \"http\"), https = __webpack_require__(/*! https */ \"https\"), URL = __webpack_require__(/*! url */ \"url\"), querystring = __webpack_require__(/*! querystring */ \"querystring\"), OAuthUtils = __webpack_require__(/*! ./_utils */ \"(rsc)/./node_modules/oauth/lib/_utils.js\");\nexports.OAuth = function(requestUrl, accessUrl, consumerKey, consumerSecret, version, authorize_callback, signatureMethod, nonceSize, customHeaders) {\n    this._isEcho = false;\n    this._requestUrl = requestUrl;\n    this._accessUrl = accessUrl;\n    this._consumerKey = consumerKey;\n    this._consumerSecret = this._encodeData(consumerSecret);\n    if (signatureMethod == \"RSA-SHA1\") {\n        this._privateKey = consumerSecret;\n    }\n    this._version = version;\n    if (authorize_callback === undefined) {\n        this._authorize_callback = \"oob\";\n    } else {\n        this._authorize_callback = authorize_callback;\n    }\n    if (signatureMethod != \"PLAINTEXT\" && signatureMethod != \"HMAC-SHA1\" && signatureMethod != \"RSA-SHA1\") throw new Error(\"Un-supported signature method: \" + signatureMethod);\n    this._signatureMethod = signatureMethod;\n    this._nonceSize = nonceSize || 32;\n    this._headers = customHeaders || {\n        \"Accept\": \"*/*\",\n        \"Connection\": \"close\",\n        \"User-Agent\": \"Node authentication\"\n    };\n    this._clientOptions = this._defaultClientOptions = {\n        \"requestTokenHttpMethod\": \"POST\",\n        \"accessTokenHttpMethod\": \"POST\",\n        \"followRedirects\": true\n    };\n    this._oauthParameterSeperator = \",\";\n};\nexports.OAuthEcho = function(realm, verify_credentials, consumerKey, consumerSecret, version, signatureMethod, nonceSize, customHeaders) {\n    this._isEcho = true;\n    this._realm = realm;\n    this._verifyCredentials = verify_credentials;\n    this._consumerKey = consumerKey;\n    this._consumerSecret = this._encodeData(consumerSecret);\n    if (signatureMethod == \"RSA-SHA1\") {\n        this._privateKey = consumerSecret;\n    }\n    this._version = version;\n    if (signatureMethod != \"PLAINTEXT\" && signatureMethod != \"HMAC-SHA1\" && signatureMethod != \"RSA-SHA1\") throw new Error(\"Un-supported signature method: \" + signatureMethod);\n    this._signatureMethod = signatureMethod;\n    this._nonceSize = nonceSize || 32;\n    this._headers = customHeaders || {\n        \"Accept\": \"*/*\",\n        \"Connection\": \"close\",\n        \"User-Agent\": \"Node authentication\"\n    };\n    this._oauthParameterSeperator = \",\";\n};\nexports.OAuthEcho.prototype = exports.OAuth.prototype;\nexports.OAuth.prototype._getTimestamp = function() {\n    return Math.floor(new Date().getTime() / 1000);\n};\nexports.OAuth.prototype._encodeData = function(toEncode) {\n    if (toEncode == null || toEncode == \"\") return \"\";\n    else {\n        var result = encodeURIComponent(toEncode);\n        // Fix the mismatch between OAuth's  RFC3986's and Javascript's beliefs in what is right and wrong ;)\n        return result.replace(/\\!/g, \"%21\").replace(/\\'/g, \"%27\").replace(/\\(/g, \"%28\").replace(/\\)/g, \"%29\").replace(/\\*/g, \"%2A\");\n    }\n};\nexports.OAuth.prototype._decodeData = function(toDecode) {\n    if (toDecode != null) {\n        toDecode = toDecode.replace(/\\+/g, \" \");\n    }\n    return decodeURIComponent(toDecode);\n};\nexports.OAuth.prototype._getSignature = function(method, url, parameters, tokenSecret) {\n    var signatureBase = this._createSignatureBase(method, url, parameters);\n    return this._createSignature(signatureBase, tokenSecret);\n};\nexports.OAuth.prototype._normalizeUrl = function(url) {\n    var parsedUrl = URL.parse(url, true);\n    var port = \"\";\n    if (parsedUrl.port) {\n        if (parsedUrl.protocol == \"http:\" && parsedUrl.port != \"80\" || parsedUrl.protocol == \"https:\" && parsedUrl.port != \"443\") {\n            port = \":\" + parsedUrl.port;\n        }\n    }\n    if (!parsedUrl.pathname || parsedUrl.pathname == \"\") parsedUrl.pathname = \"/\";\n    return parsedUrl.protocol + \"//\" + parsedUrl.hostname + port + parsedUrl.pathname;\n};\n// Is the parameter considered an OAuth parameter\nexports.OAuth.prototype._isParameterNameAnOAuthParameter = function(parameter) {\n    var m = parameter.match(\"^oauth_\");\n    if (m && m[0] === \"oauth_\") {\n        return true;\n    } else {\n        return false;\n    }\n};\n// build the OAuth request authorization header\nexports.OAuth.prototype._buildAuthorizationHeaders = function(orderedParameters) {\n    var authHeader = \"OAuth \";\n    if (this._isEcho) {\n        authHeader += 'realm=\"' + this._realm + '\",';\n    }\n    for(var i = 0; i < orderedParameters.length; i++){\n        // Whilst the all the parameters should be included within the signature, only the oauth_ arguments\n        // should appear within the authorization header.\n        if (this._isParameterNameAnOAuthParameter(orderedParameters[i][0])) {\n            authHeader += \"\" + this._encodeData(orderedParameters[i][0]) + '=\"' + this._encodeData(orderedParameters[i][1]) + '\"' + this._oauthParameterSeperator;\n        }\n    }\n    authHeader = authHeader.substring(0, authHeader.length - this._oauthParameterSeperator.length);\n    return authHeader;\n};\n// Takes an object literal that represents the arguments, and returns an array\n// of argument/value pairs.\nexports.OAuth.prototype._makeArrayOfArgumentsHash = function(argumentsHash) {\n    var argument_pairs = [];\n    for(var key in argumentsHash){\n        if (argumentsHash.hasOwnProperty(key)) {\n            var value = argumentsHash[key];\n            if (Array.isArray(value)) {\n                for(var i = 0; i < value.length; i++){\n                    argument_pairs[argument_pairs.length] = [\n                        key,\n                        value[i]\n                    ];\n                }\n            } else {\n                argument_pairs[argument_pairs.length] = [\n                    key,\n                    value\n                ];\n            }\n        }\n    }\n    return argument_pairs;\n};\n// Sorts the encoded key value pairs by encoded name, then encoded value\nexports.OAuth.prototype._sortRequestParams = function(argument_pairs) {\n    // Sort by name, then value.\n    argument_pairs.sort(function(a, b) {\n        if (a[0] == b[0]) {\n            return a[1] < b[1] ? -1 : 1;\n        } else return a[0] < b[0] ? -1 : 1;\n    });\n    return argument_pairs;\n};\nexports.OAuth.prototype._normaliseRequestParams = function(args) {\n    var argument_pairs = this._makeArrayOfArgumentsHash(args);\n    // First encode them #3.4.1.3.2 .1\n    for(var i = 0; i < argument_pairs.length; i++){\n        argument_pairs[i][0] = this._encodeData(argument_pairs[i][0]);\n        argument_pairs[i][1] = this._encodeData(argument_pairs[i][1]);\n    }\n    // Then sort them #3.4.1.3.2 .2\n    argument_pairs = this._sortRequestParams(argument_pairs);\n    // Then concatenate together #3.4.1.3.2 .3 & .4\n    var args = \"\";\n    for(var i = 0; i < argument_pairs.length; i++){\n        args += argument_pairs[i][0];\n        args += \"=\";\n        args += argument_pairs[i][1];\n        if (i < argument_pairs.length - 1) args += \"&\";\n    }\n    return args;\n};\nexports.OAuth.prototype._createSignatureBase = function(method, url, parameters) {\n    url = this._encodeData(this._normalizeUrl(url));\n    parameters = this._encodeData(parameters);\n    return method.toUpperCase() + \"&\" + url + \"&\" + parameters;\n};\nexports.OAuth.prototype._createSignature = function(signatureBase, tokenSecret) {\n    if (tokenSecret === undefined) var tokenSecret = \"\";\n    else tokenSecret = this._encodeData(tokenSecret);\n    // consumerSecret is already encoded\n    var key = this._consumerSecret + \"&\" + tokenSecret;\n    var hash = \"\";\n    if (this._signatureMethod == \"PLAINTEXT\") {\n        hash = key;\n    } else if (this._signatureMethod == \"RSA-SHA1\") {\n        key = this._privateKey || \"\";\n        hash = crypto.createSign(\"RSA-SHA1\").update(signatureBase).sign(key, \"base64\");\n    } else {\n        if (crypto.Hmac) {\n            hash = crypto.createHmac(\"sha1\", key).update(signatureBase).digest(\"base64\");\n        } else {\n            hash = sha1.HMACSHA1(key, signatureBase);\n        }\n    }\n    return hash;\n};\nexports.OAuth.prototype.NONCE_CHARS = [\n    \"a\",\n    \"b\",\n    \"c\",\n    \"d\",\n    \"e\",\n    \"f\",\n    \"g\",\n    \"h\",\n    \"i\",\n    \"j\",\n    \"k\",\n    \"l\",\n    \"m\",\n    \"n\",\n    \"o\",\n    \"p\",\n    \"q\",\n    \"r\",\n    \"s\",\n    \"t\",\n    \"u\",\n    \"v\",\n    \"w\",\n    \"x\",\n    \"y\",\n    \"z\",\n    \"A\",\n    \"B\",\n    \"C\",\n    \"D\",\n    \"E\",\n    \"F\",\n    \"G\",\n    \"H\",\n    \"I\",\n    \"J\",\n    \"K\",\n    \"L\",\n    \"M\",\n    \"N\",\n    \"O\",\n    \"P\",\n    \"Q\",\n    \"R\",\n    \"S\",\n    \"T\",\n    \"U\",\n    \"V\",\n    \"W\",\n    \"X\",\n    \"Y\",\n    \"Z\",\n    \"0\",\n    \"1\",\n    \"2\",\n    \"3\",\n    \"4\",\n    \"5\",\n    \"6\",\n    \"7\",\n    \"8\",\n    \"9\"\n];\nexports.OAuth.prototype._getNonce = function(nonceSize) {\n    var result = [];\n    var chars = this.NONCE_CHARS;\n    var char_pos;\n    var nonce_chars_length = chars.length;\n    for(var i = 0; i < nonceSize; i++){\n        char_pos = Math.floor(Math.random() * nonce_chars_length);\n        result[i] = chars[char_pos];\n    }\n    return result.join(\"\");\n};\nexports.OAuth.prototype._createClient = function(port, hostname, method, path, headers, sslEnabled) {\n    var options = {\n        host: hostname,\n        port: port,\n        path: path,\n        method: method,\n        headers: headers\n    };\n    var httpModel;\n    if (sslEnabled) {\n        httpModel = https;\n    } else {\n        httpModel = http;\n    }\n    return httpModel.request(options);\n};\nexports.OAuth.prototype._prepareParameters = function(oauth_token, oauth_token_secret, method, url, extra_params) {\n    var oauthParameters = {\n        \"oauth_timestamp\": this._getTimestamp(),\n        \"oauth_nonce\": this._getNonce(this._nonceSize),\n        \"oauth_version\": this._version,\n        \"oauth_signature_method\": this._signatureMethod,\n        \"oauth_consumer_key\": this._consumerKey\n    };\n    if (oauth_token) {\n        oauthParameters[\"oauth_token\"] = oauth_token;\n    }\n    var sig;\n    if (this._isEcho) {\n        sig = this._getSignature(\"GET\", this._verifyCredentials, this._normaliseRequestParams(oauthParameters), oauth_token_secret);\n    } else {\n        if (extra_params) {\n            for(var key in extra_params){\n                if (extra_params.hasOwnProperty(key)) oauthParameters[key] = extra_params[key];\n            }\n        }\n        var parsedUrl = URL.parse(url, false);\n        if (parsedUrl.query) {\n            var key2;\n            var extraParameters = querystring.parse(parsedUrl.query);\n            for(var key in extraParameters){\n                var value = extraParameters[key];\n                if (typeof value == \"object\") {\n                    // TODO: This probably should be recursive\n                    for(key2 in value){\n                        oauthParameters[key + \"[\" + key2 + \"]\"] = value[key2];\n                    }\n                } else {\n                    oauthParameters[key] = value;\n                }\n            }\n        }\n        sig = this._getSignature(method, url, this._normaliseRequestParams(oauthParameters), oauth_token_secret);\n    }\n    var orderedParameters = this._sortRequestParams(this._makeArrayOfArgumentsHash(oauthParameters));\n    orderedParameters[orderedParameters.length] = [\n        \"oauth_signature\",\n        sig\n    ];\n    return orderedParameters;\n};\nexports.OAuth.prototype._performSecureRequest = function(oauth_token, oauth_token_secret, method, url, extra_params, post_body, post_content_type, callback) {\n    var orderedParameters = this._prepareParameters(oauth_token, oauth_token_secret, method, url, extra_params);\n    if (!post_content_type) {\n        post_content_type = \"application/x-www-form-urlencoded\";\n    }\n    var parsedUrl = URL.parse(url, false);\n    if (parsedUrl.protocol == \"http:\" && !parsedUrl.port) parsedUrl.port = 80;\n    if (parsedUrl.protocol == \"https:\" && !parsedUrl.port) parsedUrl.port = 443;\n    var headers = {};\n    var authorization = this._buildAuthorizationHeaders(orderedParameters);\n    if (this._isEcho) {\n        headers[\"X-Verify-Credentials-Authorization\"] = authorization;\n    } else {\n        headers[\"Authorization\"] = authorization;\n    }\n    headers[\"Host\"] = parsedUrl.host;\n    for(var key in this._headers){\n        if (this._headers.hasOwnProperty(key)) {\n            headers[key] = this._headers[key];\n        }\n    }\n    // Filter out any passed extra_params that are really to do with OAuth\n    for(var key in extra_params){\n        if (this._isParameterNameAnOAuthParameter(key)) {\n            delete extra_params[key];\n        }\n    }\n    if ((method == \"POST\" || method == \"PUT\") && post_body == null && extra_params != null) {\n        // Fix the mismatch between the output of querystring.stringify() and this._encodeData()\n        post_body = querystring.stringify(extra_params).replace(/\\!/g, \"%21\").replace(/\\'/g, \"%27\").replace(/\\(/g, \"%28\").replace(/\\)/g, \"%29\").replace(/\\*/g, \"%2A\");\n    }\n    if (post_body) {\n        if (Buffer.isBuffer(post_body)) {\n            headers[\"Content-length\"] = post_body.length;\n        } else {\n            headers[\"Content-length\"] = Buffer.byteLength(post_body);\n        }\n    } else {\n        headers[\"Content-length\"] = 0;\n    }\n    headers[\"Content-Type\"] = post_content_type;\n    var path;\n    if (!parsedUrl.pathname || parsedUrl.pathname == \"\") parsedUrl.pathname = \"/\";\n    if (parsedUrl.query) path = parsedUrl.pathname + \"?\" + parsedUrl.query;\n    else path = parsedUrl.pathname;\n    var request;\n    if (parsedUrl.protocol == \"https:\") {\n        request = this._createClient(parsedUrl.port, parsedUrl.hostname, method, path, headers, true);\n    } else {\n        request = this._createClient(parsedUrl.port, parsedUrl.hostname, method, path, headers);\n    }\n    var clientOptions = this._clientOptions;\n    if (callback) {\n        var data = \"\";\n        var self = this;\n        // Some hosts *cough* google appear to close the connection early / send no content-length header\n        // allow this behaviour.\n        var allowEarlyClose = OAuthUtils.isAnEarlyCloseHost(parsedUrl.hostname);\n        var callbackCalled = false;\n        var passBackControl = function(response) {\n            if (!callbackCalled) {\n                callbackCalled = true;\n                if (response.statusCode >= 200 && response.statusCode <= 299) {\n                    callback(null, data, response);\n                } else {\n                    // Follow 301 or 302 redirects with Location HTTP header\n                    if ((response.statusCode == 301 || response.statusCode == 302) && clientOptions.followRedirects && response.headers && response.headers.location) {\n                        self._performSecureRequest(oauth_token, oauth_token_secret, method, response.headers.location, extra_params, post_body, post_content_type, callback);\n                    } else {\n                        callback({\n                            statusCode: response.statusCode,\n                            data: data\n                        }, data, response);\n                    }\n                }\n            }\n        };\n        request.on(\"response\", function(response) {\n            response.setEncoding(\"utf8\");\n            response.on(\"data\", function(chunk) {\n                data += chunk;\n            });\n            response.on(\"end\", function() {\n                passBackControl(response);\n            });\n            response.on(\"close\", function() {\n                if (allowEarlyClose) {\n                    passBackControl(response);\n                }\n            });\n        });\n        request.on(\"error\", function(err) {\n            if (!callbackCalled) {\n                callbackCalled = true;\n                callback(err);\n            }\n        });\n        if ((method == \"POST\" || method == \"PUT\") && post_body != null && post_body != \"\") {\n            request.write(post_body);\n        }\n        request.end();\n    } else {\n        if ((method == \"POST\" || method == \"PUT\") && post_body != null && post_body != \"\") {\n            request.write(post_body);\n        }\n        return request;\n    }\n    return;\n};\nexports.OAuth.prototype.setClientOptions = function(options) {\n    var key, mergedOptions = {}, hasOwnProperty = Object.prototype.hasOwnProperty;\n    for(key in this._defaultClientOptions){\n        if (!hasOwnProperty.call(options, key)) {\n            mergedOptions[key] = this._defaultClientOptions[key];\n        } else {\n            mergedOptions[key] = options[key];\n        }\n    }\n    this._clientOptions = mergedOptions;\n};\nexports.OAuth.prototype.getOAuthAccessToken = function(oauth_token, oauth_token_secret, oauth_verifier, callback) {\n    var extraParams = {};\n    if (typeof oauth_verifier == \"function\") {\n        callback = oauth_verifier;\n    } else {\n        extraParams.oauth_verifier = oauth_verifier;\n    }\n    this._performSecureRequest(oauth_token, oauth_token_secret, this._clientOptions.accessTokenHttpMethod, this._accessUrl, extraParams, null, null, function(error, data, response) {\n        if (error) callback(error);\n        else {\n            var results = querystring.parse(data);\n            var oauth_access_token = results[\"oauth_token\"];\n            delete results[\"oauth_token\"];\n            var oauth_access_token_secret = results[\"oauth_token_secret\"];\n            delete results[\"oauth_token_secret\"];\n            callback(null, oauth_access_token, oauth_access_token_secret, results);\n        }\n    });\n};\n// Deprecated\nexports.OAuth.prototype.getProtectedResource = function(url, method, oauth_token, oauth_token_secret, callback) {\n    this._performSecureRequest(oauth_token, oauth_token_secret, method, url, null, \"\", null, callback);\n};\nexports.OAuth.prototype[\"delete\"] = function(url, oauth_token, oauth_token_secret, callback) {\n    return this._performSecureRequest(oauth_token, oauth_token_secret, \"DELETE\", url, null, \"\", null, callback);\n};\nexports.OAuth.prototype.get = function(url, oauth_token, oauth_token_secret, callback) {\n    return this._performSecureRequest(oauth_token, oauth_token_secret, \"GET\", url, null, \"\", null, callback);\n};\nexports.OAuth.prototype._putOrPost = function(method, url, oauth_token, oauth_token_secret, post_body, post_content_type, callback) {\n    var extra_params = null;\n    if (typeof post_content_type == \"function\") {\n        callback = post_content_type;\n        post_content_type = null;\n    }\n    if (typeof post_body != \"string\" && !Buffer.isBuffer(post_body)) {\n        post_content_type = \"application/x-www-form-urlencoded\";\n        extra_params = post_body;\n        post_body = null;\n    }\n    return this._performSecureRequest(oauth_token, oauth_token_secret, method, url, extra_params, post_body, post_content_type, callback);\n};\nexports.OAuth.prototype.put = function(url, oauth_token, oauth_token_secret, post_body, post_content_type, callback) {\n    return this._putOrPost(\"PUT\", url, oauth_token, oauth_token_secret, post_body, post_content_type, callback);\n};\nexports.OAuth.prototype.post = function(url, oauth_token, oauth_token_secret, post_body, post_content_type, callback) {\n    return this._putOrPost(\"POST\", url, oauth_token, oauth_token_secret, post_body, post_content_type, callback);\n};\n/**\n * Gets a request token from the OAuth provider and passes that information back\n * to the calling code.\n *\n * The callback should expect a function of the following form:\n *\n * function(err, token, token_secret, parsedQueryString) {}\n *\n * This method has optional parameters so can be called in the following 2 ways:\n *\n * 1) Primary use case: Does a basic request with no extra parameters\n *  getOAuthRequestToken( callbackFunction )\n *\n * 2) As above but allows for provision of extra parameters to be sent as part of the query to the server.\n *  getOAuthRequestToken( extraParams, callbackFunction )\n *\n * N.B. This method will HTTP POST verbs by default, if you wish to override this behaviour you will\n * need to provide a requestTokenHttpMethod option when creating the client.\n *\n **/ exports.OAuth.prototype.getOAuthRequestToken = function(extraParams, callback) {\n    if (typeof extraParams == \"function\") {\n        callback = extraParams;\n        extraParams = {};\n    }\n    // Callbacks are 1.0A related\n    if (this._authorize_callback) {\n        extraParams[\"oauth_callback\"] = this._authorize_callback;\n    }\n    this._performSecureRequest(null, null, this._clientOptions.requestTokenHttpMethod, this._requestUrl, extraParams, null, null, function(error, data, response) {\n        if (error) callback(error);\n        else {\n            var results = querystring.parse(data);\n            var oauth_token = results[\"oauth_token\"];\n            var oauth_token_secret = results[\"oauth_token_secret\"];\n            delete results[\"oauth_token\"];\n            delete results[\"oauth_token_secret\"];\n            callback(null, oauth_token, oauth_token_secret, results);\n        }\n    });\n};\nexports.OAuth.prototype.signUrl = function(url, oauth_token, oauth_token_secret, method) {\n    if (method === undefined) {\n        var method = \"GET\";\n    }\n    var orderedParameters = this._prepareParameters(oauth_token, oauth_token_secret, method, url, {});\n    var parsedUrl = URL.parse(url, false);\n    var query = \"\";\n    for(var i = 0; i < orderedParameters.length; i++){\n        query += orderedParameters[i][0] + \"=\" + this._encodeData(orderedParameters[i][1]) + \"&\";\n    }\n    query = query.substring(0, query.length - 1);\n    return parsedUrl.protocol + \"//\" + parsedUrl.host + parsedUrl.pathname + \"?\" + query;\n};\nexports.OAuth.prototype.authHeader = function(url, oauth_token, oauth_token_secret, method) {\n    if (method === undefined) {\n        var method = \"GET\";\n    }\n    var orderedParameters = this._prepareParameters(oauth_token, oauth_token_secret, method, url, {});\n    return this._buildAuthorizationHeaders(orderedParameters);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth/lib/oauth.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/oauth/lib/oauth2.js":
/*!******************************************!*\
  !*** ./node_modules/oauth/lib/oauth2.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var querystring = __webpack_require__(/*! querystring */ \"querystring\"), crypto = __webpack_require__(/*! crypto */ \"crypto\"), https = __webpack_require__(/*! https */ \"https\"), http = __webpack_require__(/*! http */ \"http\"), URL = __webpack_require__(/*! url */ \"url\"), OAuthUtils = __webpack_require__(/*! ./_utils */ \"(rsc)/./node_modules/oauth/lib/_utils.js\");\nexports.OAuth2 = function(clientId, clientSecret, baseSite, authorizePath, accessTokenPath, customHeaders) {\n    this._clientId = clientId;\n    this._clientSecret = clientSecret;\n    this._baseSite = baseSite;\n    this._authorizeUrl = authorizePath || \"/oauth/authorize\";\n    this._accessTokenUrl = accessTokenPath || \"/oauth/access_token\";\n    this._accessTokenName = \"access_token\";\n    this._authMethod = \"Bearer\";\n    this._customHeaders = customHeaders || {};\n    this._useAuthorizationHeaderForGET = false;\n    //our agent\n    this._agent = undefined;\n};\n// Allows you to set an agent to use instead of the default HTTP or\n// HTTPS agents. Useful when dealing with your own certificates.\nexports.OAuth2.prototype.setAgent = function(agent) {\n    this._agent = agent;\n};\n// This 'hack' method is required for sites that don't use\n// 'access_token' as the name of the access token (for requests).\n// ( http://tools.ietf.org/html/draft-ietf-oauth-v2-16#section-7 )\n// it isn't clear what the correct value should be atm, so allowing\n// for specific (temporary?) override for now.\nexports.OAuth2.prototype.setAccessTokenName = function(name) {\n    this._accessTokenName = name;\n};\n// Sets the authorization method for Authorization header.\n// e.g. Authorization: Bearer <token>  # \"Bearer\" is the authorization method.\nexports.OAuth2.prototype.setAuthMethod = function(authMethod) {\n    this._authMethod = authMethod;\n};\n// If you use the OAuth2 exposed 'get' method (and don't construct your own _request call )\n// this will specify whether to use an 'Authorize' header instead of passing the access_token as a query parameter\nexports.OAuth2.prototype.useAuthorizationHeaderforGET = function(useIt) {\n    this._useAuthorizationHeaderForGET = useIt;\n};\nexports.OAuth2.prototype._getAccessTokenUrl = function() {\n    return this._baseSite + this._accessTokenUrl; /* + \"?\" + querystring.stringify(params); */ \n};\n// Build the authorization header. In particular, build the part after the colon.\n// e.g. Authorization: Bearer <token>  # Build \"Bearer <token>\"\nexports.OAuth2.prototype.buildAuthHeader = function(token) {\n    return this._authMethod + \" \" + token;\n};\nexports.OAuth2.prototype._chooseHttpLibrary = function(parsedUrl) {\n    var http_library = https;\n    // As this is OAUth2, we *assume* https unless told explicitly otherwise.\n    if (parsedUrl.protocol != \"https:\") {\n        http_library = http;\n    }\n    return http_library;\n};\nexports.OAuth2.prototype._request = function(method, url, headers, post_body, access_token, callback) {\n    var parsedUrl = URL.parse(url, true);\n    if (parsedUrl.protocol == \"https:\" && !parsedUrl.port) {\n        parsedUrl.port = 443;\n    }\n    var http_library = this._chooseHttpLibrary(parsedUrl);\n    var realHeaders = {};\n    for(var key in this._customHeaders){\n        realHeaders[key] = this._customHeaders[key];\n    }\n    if (headers) {\n        for(var key in headers){\n            realHeaders[key] = headers[key];\n        }\n    }\n    realHeaders[\"Host\"] = parsedUrl.host;\n    if (!realHeaders[\"User-Agent\"]) {\n        realHeaders[\"User-Agent\"] = \"Node-oauth\";\n    }\n    if (post_body) {\n        if (Buffer.isBuffer(post_body)) {\n            realHeaders[\"Content-Length\"] = post_body.length;\n        } else {\n            realHeaders[\"Content-Length\"] = Buffer.byteLength(post_body);\n        }\n    } else {\n        realHeaders[\"Content-length\"] = 0;\n    }\n    if (access_token && !(\"Authorization\" in realHeaders)) {\n        if (!parsedUrl.query) parsedUrl.query = {};\n        parsedUrl.query[this._accessTokenName] = access_token;\n    }\n    var queryStr = querystring.stringify(parsedUrl.query);\n    if (queryStr) queryStr = \"?\" + queryStr;\n    var options = {\n        host: parsedUrl.hostname,\n        port: parsedUrl.port,\n        path: parsedUrl.pathname + queryStr,\n        method: method,\n        headers: realHeaders\n    };\n    this._executeRequest(http_library, options, post_body, callback);\n};\nexports.OAuth2.prototype._executeRequest = function(http_library, options, post_body, callback) {\n    // Some hosts *cough* google appear to close the connection early / send no content-length header\n    // allow this behaviour.\n    var allowEarlyClose = OAuthUtils.isAnEarlyCloseHost(options.host);\n    var callbackCalled = false;\n    function passBackControl(response, result) {\n        if (!callbackCalled) {\n            callbackCalled = true;\n            if (!(response.statusCode >= 200 && response.statusCode <= 299) && response.statusCode != 301 && response.statusCode != 302) {\n                callback({\n                    statusCode: response.statusCode,\n                    data: result\n                });\n            } else {\n                callback(null, result, response);\n            }\n        }\n    }\n    var result = \"\";\n    //set the agent on the request options\n    if (this._agent) {\n        options.agent = this._agent;\n    }\n    var request = http_library.request(options);\n    request.on(\"response\", function(response) {\n        response.on(\"data\", function(chunk) {\n            result += chunk;\n        });\n        response.on(\"close\", function(err) {\n            if (allowEarlyClose) {\n                passBackControl(response, result);\n            }\n        });\n        response.addListener(\"end\", function() {\n            passBackControl(response, result);\n        });\n    });\n    request.on(\"error\", function(e) {\n        callbackCalled = true;\n        callback(e);\n    });\n    if ((options.method == \"POST\" || options.method == \"PUT\") && post_body) {\n        request.write(post_body);\n    }\n    request.end();\n};\nexports.OAuth2.prototype.getAuthorizeUrl = function(params) {\n    var params = params || {};\n    params[\"client_id\"] = this._clientId;\n    return this._baseSite + this._authorizeUrl + \"?\" + querystring.stringify(params);\n};\nexports.OAuth2.prototype.getOAuthAccessToken = function(code, params, callback) {\n    var params = params || {};\n    params[\"client_id\"] = this._clientId;\n    params[\"client_secret\"] = this._clientSecret;\n    var codeParam = params.grant_type === \"refresh_token\" ? \"refresh_token\" : \"code\";\n    params[codeParam] = code;\n    var post_data = querystring.stringify(params);\n    var post_headers = {\n        \"Content-Type\": \"application/x-www-form-urlencoded\"\n    };\n    this._request(\"POST\", this._getAccessTokenUrl(), post_headers, post_data, null, function(error, data, response) {\n        if (error) callback(error);\n        else {\n            var results;\n            try {\n                // As of http://tools.ietf.org/html/draft-ietf-oauth-v2-07\n                // responses should be in JSON\n                results = JSON.parse(data);\n            } catch (e) {\n                // .... However both Facebook + Github currently use rev05 of the spec\n                // and neither seem to specify a content-type correctly in their response headers :(\n                // clients of these services will suffer a *minor* performance cost of the exception\n                // being thrown\n                results = querystring.parse(data);\n            }\n            var access_token = results[\"access_token\"];\n            var refresh_token = results[\"refresh_token\"];\n            delete results[\"refresh_token\"];\n            callback(null, access_token, refresh_token, results); // callback results =-=\n        }\n    });\n};\n// Deprecated\nexports.OAuth2.prototype.getProtectedResource = function(url, access_token, callback) {\n    this._request(\"GET\", url, {}, \"\", access_token, callback);\n};\nexports.OAuth2.prototype.get = function(url, access_token, callback) {\n    if (this._useAuthorizationHeaderForGET) {\n        var headers = {\n            \"Authorization\": this.buildAuthHeader(access_token)\n        };\n        access_token = null;\n    } else {\n        headers = {};\n    }\n    this._request(\"GET\", url, headers, \"\", access_token, callback);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth/lib/oauth2.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/oauth/lib/sha1.js":
/*!****************************************!*\
  !*** ./node_modules/oauth/lib/sha1.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("/*\n * A JavaScript implementation of the Secure Hash Algorithm, SHA-1, as defined\n * in FIPS 180-1\n * Version 2.2 Copyright Paul Johnston 2000 - 2009.\n * Other contributors: Greg Holt, Andrew Kepert, Ydnar, Lostinet\n * Distributed under the BSD License\n * See http://pajhome.org.uk/crypt/md5 for details.\n */ /*\n * Configurable variables. You may need to tweak these to be compatible with\n * the server-side, but the defaults work in most cases.\n */ var hexcase = 1; /* hex output format. 0 - lowercase; 1 - uppercase        */ \nvar b64pad = \"=\"; /* base-64 pad character. \"=\" for strict RFC compliance   */ \n/*\n * These are the functions you'll usually want to call\n * They take string arguments and return either hex or base-64 encoded strings\n */ function hex_sha1(s) {\n    return rstr2hex(rstr_sha1(str2rstr_utf8(s)));\n}\nfunction b64_sha1(s) {\n    return rstr2b64(rstr_sha1(str2rstr_utf8(s)));\n}\nfunction any_sha1(s, e) {\n    return rstr2any(rstr_sha1(str2rstr_utf8(s)), e);\n}\nfunction hex_hmac_sha1(k, d) {\n    return rstr2hex(rstr_hmac_sha1(str2rstr_utf8(k), str2rstr_utf8(d)));\n}\nfunction b64_hmac_sha1(k, d) {\n    return rstr2b64(rstr_hmac_sha1(str2rstr_utf8(k), str2rstr_utf8(d)));\n}\nfunction any_hmac_sha1(k, d, e) {\n    return rstr2any(rstr_hmac_sha1(str2rstr_utf8(k), str2rstr_utf8(d)), e);\n}\n/*\n * Perform a simple self-test to see if the VM is working\n */ function sha1_vm_test() {\n    return hex_sha1(\"abc\").toLowerCase() == \"a9993e364706816aba3e25717850c26c9cd0d89d\";\n}\n/*\n * Calculate the SHA1 of a raw string\n */ function rstr_sha1(s) {\n    return binb2rstr(binb_sha1(rstr2binb(s), s.length * 8));\n}\n/*\n * Calculate the HMAC-SHA1 of a key and some data (raw strings)\n */ function rstr_hmac_sha1(key, data) {\n    var bkey = rstr2binb(key);\n    if (bkey.length > 16) bkey = binb_sha1(bkey, key.length * 8);\n    var ipad = Array(16), opad = Array(16);\n    for(var i = 0; i < 16; i++){\n        ipad[i] = bkey[i] ^ 0x36363636;\n        opad[i] = bkey[i] ^ 0x5C5C5C5C;\n    }\n    var hash = binb_sha1(ipad.concat(rstr2binb(data)), 512 + data.length * 8);\n    return binb2rstr(binb_sha1(opad.concat(hash), 512 + 160));\n}\n/*\n * Convert a raw string to a hex string\n */ function rstr2hex(input) {\n    try {\n        hexcase;\n    } catch (e) {\n        hexcase = 0;\n    }\n    var hex_tab = hexcase ? \"0123456789ABCDEF\" : \"0123456789abcdef\";\n    var output = \"\";\n    var x;\n    for(var i = 0; i < input.length; i++){\n        x = input.charCodeAt(i);\n        output += hex_tab.charAt(x >>> 4 & 0x0F) + hex_tab.charAt(x & 0x0F);\n    }\n    return output;\n}\n/*\n * Convert a raw string to a base-64 string\n */ function rstr2b64(input) {\n    try {\n        b64pad;\n    } catch (e) {\n        b64pad = \"\";\n    }\n    var tab = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\";\n    var output = \"\";\n    var len = input.length;\n    for(var i = 0; i < len; i += 3){\n        var triplet = input.charCodeAt(i) << 16 | (i + 1 < len ? input.charCodeAt(i + 1) << 8 : 0) | (i + 2 < len ? input.charCodeAt(i + 2) : 0);\n        for(var j = 0; j < 4; j++){\n            if (i * 8 + j * 6 > input.length * 8) output += b64pad;\n            else output += tab.charAt(triplet >>> 6 * (3 - j) & 0x3F);\n        }\n    }\n    return output;\n}\n/*\n * Convert a raw string to an arbitrary string encoding\n */ function rstr2any(input, encoding) {\n    var divisor = encoding.length;\n    var remainders = Array();\n    var i, q, x, quotient;\n    /* Convert to an array of 16-bit big-endian values, forming the dividend */ var dividend = Array(Math.ceil(input.length / 2));\n    for(i = 0; i < dividend.length; i++){\n        dividend[i] = input.charCodeAt(i * 2) << 8 | input.charCodeAt(i * 2 + 1);\n    }\n    /*\n   * Repeatedly perform a long division. The binary array forms the dividend,\n   * the length of the encoding is the divisor. Once computed, the quotient\n   * forms the dividend for the next step. We stop when the dividend is zero.\n   * All remainders are stored for later use.\n   */ while(dividend.length > 0){\n        quotient = Array();\n        x = 0;\n        for(i = 0; i < dividend.length; i++){\n            x = (x << 16) + dividend[i];\n            q = Math.floor(x / divisor);\n            x -= q * divisor;\n            if (quotient.length > 0 || q > 0) quotient[quotient.length] = q;\n        }\n        remainders[remainders.length] = x;\n        dividend = quotient;\n    }\n    /* Convert the remainders to the output string */ var output = \"\";\n    for(i = remainders.length - 1; i >= 0; i--)output += encoding.charAt(remainders[i]);\n    /* Append leading zero equivalents */ var full_length = Math.ceil(input.length * 8 / (Math.log(encoding.length) / Math.log(2)));\n    for(i = output.length; i < full_length; i++)output = encoding[0] + output;\n    return output;\n}\n/*\n * Encode a string as utf-8.\n * For efficiency, this assumes the input is valid utf-16.\n */ function str2rstr_utf8(input) {\n    var output = \"\";\n    var i = -1;\n    var x, y;\n    while(++i < input.length){\n        /* Decode utf-16 surrogate pairs */ x = input.charCodeAt(i);\n        y = i + 1 < input.length ? input.charCodeAt(i + 1) : 0;\n        if (0xD800 <= x && x <= 0xDBFF && 0xDC00 <= y && y <= 0xDFFF) {\n            x = 0x10000 + ((x & 0x03FF) << 10) + (y & 0x03FF);\n            i++;\n        }\n        /* Encode output as utf-8 */ if (x <= 0x7F) output += String.fromCharCode(x);\n        else if (x <= 0x7FF) output += String.fromCharCode(0xC0 | x >>> 6 & 0x1F, 0x80 | x & 0x3F);\n        else if (x <= 0xFFFF) output += String.fromCharCode(0xE0 | x >>> 12 & 0x0F, 0x80 | x >>> 6 & 0x3F, 0x80 | x & 0x3F);\n        else if (x <= 0x1FFFFF) output += String.fromCharCode(0xF0 | x >>> 18 & 0x07, 0x80 | x >>> 12 & 0x3F, 0x80 | x >>> 6 & 0x3F, 0x80 | x & 0x3F);\n    }\n    return output;\n}\n/*\n * Encode a string as utf-16\n */ function str2rstr_utf16le(input) {\n    var output = \"\";\n    for(var i = 0; i < input.length; i++)output += String.fromCharCode(input.charCodeAt(i) & 0xFF, input.charCodeAt(i) >>> 8 & 0xFF);\n    return output;\n}\nfunction str2rstr_utf16be(input) {\n    var output = \"\";\n    for(var i = 0; i < input.length; i++)output += String.fromCharCode(input.charCodeAt(i) >>> 8 & 0xFF, input.charCodeAt(i) & 0xFF);\n    return output;\n}\n/*\n * Convert a raw string to an array of big-endian words\n * Characters >255 have their high-byte silently ignored.\n */ function rstr2binb(input) {\n    var output = Array(input.length >> 2);\n    for(var i = 0; i < output.length; i++)output[i] = 0;\n    for(var i = 0; i < input.length * 8; i += 8)output[i >> 5] |= (input.charCodeAt(i / 8) & 0xFF) << 24 - i % 32;\n    return output;\n}\n/*\n * Convert an array of big-endian words to a string\n */ function binb2rstr(input) {\n    var output = \"\";\n    for(var i = 0; i < input.length * 32; i += 8)output += String.fromCharCode(input[i >> 5] >>> 24 - i % 32 & 0xFF);\n    return output;\n}\n/*\n * Calculate the SHA-1 of an array of big-endian words, and a bit length\n */ function binb_sha1(x, len) {\n    /* append padding */ x[len >> 5] |= 0x80 << 24 - len % 32;\n    x[(len + 64 >> 9 << 4) + 15] = len;\n    var w = Array(80);\n    var a = 1732584193;\n    var b = -271733879;\n    var c = -1732584194;\n    var d = 271733878;\n    var e = -1009589776;\n    for(var i = 0; i < x.length; i += 16){\n        var olda = a;\n        var oldb = b;\n        var oldc = c;\n        var oldd = d;\n        var olde = e;\n        for(var j = 0; j < 80; j++){\n            if (j < 16) w[j] = x[i + j];\n            else w[j] = bit_rol(w[j - 3] ^ w[j - 8] ^ w[j - 14] ^ w[j - 16], 1);\n            var t = safe_add(safe_add(bit_rol(a, 5), sha1_ft(j, b, c, d)), safe_add(safe_add(e, w[j]), sha1_kt(j)));\n            e = d;\n            d = c;\n            c = bit_rol(b, 30);\n            b = a;\n            a = t;\n        }\n        a = safe_add(a, olda);\n        b = safe_add(b, oldb);\n        c = safe_add(c, oldc);\n        d = safe_add(d, oldd);\n        e = safe_add(e, olde);\n    }\n    return Array(a, b, c, d, e);\n}\n/*\n * Perform the appropriate triplet combination function for the current\n * iteration\n */ function sha1_ft(t, b, c, d) {\n    if (t < 20) return b & c | ~b & d;\n    if (t < 40) return b ^ c ^ d;\n    if (t < 60) return b & c | b & d | c & d;\n    return b ^ c ^ d;\n}\n/*\n * Determine the appropriate additive constant for the current iteration\n */ function sha1_kt(t) {\n    return t < 20 ? 1518500249 : t < 40 ? 1859775393 : t < 60 ? -1894007588 : -899497514;\n}\n/*\n * Add integers, wrapping at 2^32. This uses 16-bit operations internally\n * to work around bugs in some JS interpreters.\n */ function safe_add(x, y) {\n    var lsw = (x & 0xFFFF) + (y & 0xFFFF);\n    var msw = (x >> 16) + (y >> 16) + (lsw >> 16);\n    return msw << 16 | lsw & 0xFFFF;\n}\n/*\n * Bitwise rotate a 32-bit number to the left.\n */ function bit_rol(num, cnt) {\n    return num << cnt | num >>> 32 - cnt;\n}\nexports.HMACSHA1 = function(key, data) {\n    return b64_hmac_sha1(key, data);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth/lib/sha1.js\n");

/***/ })

};
;