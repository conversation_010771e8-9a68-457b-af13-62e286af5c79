import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { createStripePortalSession } from '@/lib/stripe'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get the company's subscription
    const subscription = await prisma.subscription.findFirst({
      where: {
        companyId: session.user.companyId,
        status: { in: ['ACTIVE', 'TRIALING', 'PAST_DUE'] }
      }
    })

    if (!subscription || !subscription.stripeCustomerId) {
      return NextResponse.json(
        { success: false, error: 'No active Stripe subscription found' },
        { status: 404 }
      )
    }

    // Create Stripe billing portal session
    const portalSession = await createStripePortalSession({
      customerId: subscription.stripeCustomerId,
      returnUrl: `${process.env.NEXTAUTH_URL}/subscription`
    })

    return NextResponse.json({
      success: true,
      data: {
        url: portalSession.url
      }
    })

  } catch (error) {
    console.error('Error creating billing portal session:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create billing portal session' },
      { status: 500 }
    )
  }
}
