"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5385],{99670:function(e,t,n){n.d(t,{Z:function(){return l}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,n(62898).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},17472:function(e,t,n){n.d(t,{Z:function(){return l}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,n(62898).Z)("MoreHorizontal",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},49617:function(e,t,n){n.d(t,{Z:function(){return l}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,n(62898).Z)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},9883:function(e,t,n){n.d(t,{Z:function(){return l}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,n(62898).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},64280:function(e,t,n){n.d(t,{Z:function(){return l}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,n(62898).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},41827:function(e,t,n){n.d(t,{Z:function(){return l}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,n(62898).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},66654:function(e,t,n){n.d(t,{Z:function(){return l}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,n(62898).Z)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},45367:function(e,t,n){n.d(t,{Z:function(){return l}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,n(62898).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},28712:function(e,t,n){n.d(t,{Dx:function(){return el},VY:function(){return en},aV:function(){return et},dk:function(){return eo},fC:function(){return J},h_:function(){return ee},x8:function(){return er},xz:function(){return Q}});var l=n(2265),o=n(85744),r=n(42210),i=n(56989),u=n(20966),a=n(73763),s=n(79249),g=n(52759),d=n(52730),c=n(85606),f=n(9381),p=n(31244),m=n(73386),v=n(85859),w=n(67256),h=n(57437),C="Dialog",[R,b]=(0,i.b)(C),[S,F]=R(C),M=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:r,onOpenChange:i,modal:s=!0}=e,g=l.useRef(null),d=l.useRef(null),[c,f]=(0,a.T)({prop:o,defaultProp:r??!1,onChange:i,caller:C});return(0,h.jsx)(S,{scope:t,triggerRef:g,contentRef:d,contentId:(0,u.M)(),titleId:(0,u.M)(),descriptionId:(0,u.M)(),open:c,onOpenChange:f,onOpenToggle:l.useCallback(()=>f(e=>!e),[f]),modal:s,children:n})};M.displayName=C;var y="DialogTrigger",I=l.forwardRef((e,t)=>{let{__scopeDialog:n,...l}=e,i=F(y,n),u=(0,r.e)(t,i.triggerRef);return(0,h.jsx)(f.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":Z(i.open),...l,ref:u,onClick:(0,o.M)(e.onClick,i.onOpenToggle)})});I.displayName=y;var x="DialogPortal",[V,P]=R(x,{forceMount:void 0}),_=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:r}=e,i=F(x,t);return(0,h.jsx)(V,{scope:t,forceMount:n,children:l.Children.map(o,e=>(0,h.jsx)(c.z,{present:n||i.open,children:(0,h.jsx)(d.h,{asChild:!0,container:r,children:e})}))})};_.displayName=x;var E="DialogOverlay",D=l.forwardRef((e,t)=>{let n=P(E,e.__scopeDialog),{forceMount:l=n.forceMount,...o}=e,r=F(E,e.__scopeDialog);return r.modal?(0,h.jsx)(c.z,{present:l||r.open,children:(0,h.jsx)(L,{...o,ref:t})}):null});D.displayName=E;var A=(0,w.Z8)("DialogOverlay.RemoveScroll"),L=l.forwardRef((e,t)=>{let{__scopeDialog:n,...l}=e,o=F(E,n);return(0,h.jsx)(m.Z,{as:A,allowPinchZoom:!0,shards:[o.contentRef],children:(0,h.jsx)(f.WV.div,{"data-state":Z(o.open),...l,ref:t,style:{pointerEvents:"auto",...l.style}})})}),G="DialogContent",O=l.forwardRef((e,t)=>{let n=P(G,e.__scopeDialog),{forceMount:l=n.forceMount,...o}=e,r=F(G,e.__scopeDialog);return(0,h.jsx)(c.z,{present:l||r.open,children:r.modal?(0,h.jsx)(H,{...o,ref:t}):(0,h.jsx)(z,{...o,ref:t})})});O.displayName=G;var H=l.forwardRef((e,t)=>{let n=F(G,e.__scopeDialog),i=l.useRef(null),u=(0,r.e)(t,n.contentRef,i);return l.useEffect(()=>{let e=i.current;if(e)return(0,v.Ry)(e)},[]),(0,h.jsx)(T,{...e,ref:u,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{e.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:(0,o.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault())})}),z=l.forwardRef((e,t)=>{let n=F(G,e.__scopeDialog),o=l.useRef(!1),r=l.useRef(!1);return(0,h.jsx)(T,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(o.current||n.triggerRef.current?.focus(),t.preventDefault()),o.current=!1,r.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(r.current=!0));let l=t.target;n.triggerRef.current?.contains(l)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&r.current&&t.preventDefault()}})}),T=l.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:u,...a}=e,d=F(G,n),c=l.useRef(null),f=(0,r.e)(t,c);return(0,p.EW)(),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(g.M,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:i,onUnmountAutoFocus:u,children:(0,h.jsx)(s.XB,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":Z(d.open),...a,ref:f,onDismiss:()=>d.onOpenChange(!1)})}),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(X,{titleId:d.titleId}),(0,h.jsx)(Y,{contentRef:c,descriptionId:d.descriptionId})]})]})}),k="DialogTitle",N=l.forwardRef((e,t)=>{let{__scopeDialog:n,...l}=e,o=F(k,n);return(0,h.jsx)(f.WV.h2,{id:o.titleId,...l,ref:t})});N.displayName=k;var j="DialogDescription",B=l.forwardRef((e,t)=>{let{__scopeDialog:n,...l}=e,o=F(j,n);return(0,h.jsx)(f.WV.p,{id:o.descriptionId,...l,ref:t})});B.displayName=j;var q="DialogClose",U=l.forwardRef((e,t)=>{let{__scopeDialog:n,...l}=e,r=F(q,n);return(0,h.jsx)(f.WV.button,{type:"button",...l,ref:t,onClick:(0,o.M)(e.onClick,()=>r.onOpenChange(!1))})});function Z(e){return e?"open":"closed"}U.displayName=q;var W="DialogTitleWarning",[$,K]=(0,i.k)(W,{contentName:G,titleName:k,docsSlug:"dialog"}),X=({titleId:e})=>{let t=K(W),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return l.useEffect(()=>{e&&!document.getElementById(e)&&console.error(n)},[n,e]),null},Y=({contentRef:e,descriptionId:t})=>{let n=K("DialogDescriptionWarning"),o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${n.contentName}}.`;return l.useEffect(()=>{let n=e.current?.getAttribute("aria-describedby");t&&n&&!document.getElementById(t)&&console.warn(o)},[o,e,t]),null},J=M,Q=I,ee=_,et=D,en=O,el=N,eo=B,er=U},85606:function(e,t,n){n.d(t,{z:function(){return i}});var l=n(2265),o=n(42210),r=n(51030),i=e=>{let t,n;let{present:i,children:a}=e,s=function(e){var t,n;let[o,i]=l.useState(),a=l.useRef(null),s=l.useRef(e),g=l.useRef("none"),[d,c]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},l.useReducer((e,t)=>n[e][t]??e,t));return l.useEffect(()=>{let e=u(a.current);g.current="mounted"===d?e:"none"},[d]),(0,r.b)(()=>{let t=a.current,n=s.current;if(n!==e){let l=g.current,o=u(t);e?c("MOUNT"):"none"===o||t?.display==="none"?c("UNMOUNT"):n&&l!==o?c("ANIMATION_OUT"):c("UNMOUNT"),s.current=e}},[e,c]),(0,r.b)(()=>{if(o){let e;let t=o.ownerDocument.defaultView??window,n=n=>{let l=u(a.current).includes(n.animationName);if(n.target===o&&l&&(c("ANIMATION_END"),!s.current)){let n=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=n)})}},l=e=>{e.target===o&&(g.current=u(a.current))};return o.addEventListener("animationstart",l),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",l),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}c("ANIMATION_END")},[o,c]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:l.useCallback(e=>{a.current=e?getComputedStyle(e):null,i(e)},[])}}(i),g="function"==typeof a?a({present:s.isPresent}):l.Children.only(a),d=(0,o.e)(s.ref,(t=Object.getOwnPropertyDescriptor(g.props,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning?g.ref:(t=Object.getOwnPropertyDescriptor(g,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning?g.props.ref:g.props.ref||g.ref);return"function"==typeof a||s.isPresent?l.cloneElement(g,{ref:d}):null};function u(e){return e?.animationName||"none"}i.displayName="Presence"},44356:function(e,t,n){n.d(t,{Pc:function(){return b},ck:function(){return E},fC:function(){return _}});var l=n(2265),o=n(85744),r=n(27733),i=n(42210),u=n(56989),a=n(20966),s=n(9381),g=n(16459),d=n(73763),c=n(65400),f=n(57437),p="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},v="RovingFocusGroup",[w,h,C]=(0,r.B)(v),[R,b]=(0,u.b)(v,[C]),[S,F]=R(v),M=l.forwardRef((e,t)=>(0,f.jsx)(w.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(w.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(y,{...e,ref:t})})}));M.displayName=v;var y=l.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:r,loop:u=!1,dir:a,currentTabStopId:w,defaultCurrentTabStopId:C,onCurrentTabStopIdChange:R,onEntryFocus:b,preventScrollOnEntryFocus:F=!1,...M}=e,y=l.useRef(null),I=(0,i.e)(t,y),x=(0,c.gm)(a),[V,_]=(0,d.T)({prop:w,defaultProp:C??null,onChange:R,caller:v}),[E,D]=l.useState(!1),A=(0,g.W)(b),L=h(n),G=l.useRef(!1),[O,H]=l.useState(0);return l.useEffect(()=>{let e=y.current;if(e)return e.addEventListener(p,A),()=>e.removeEventListener(p,A)},[A]),(0,f.jsx)(S,{scope:n,orientation:r,dir:x,loop:u,currentTabStopId:V,onItemFocus:l.useCallback(e=>_(e),[_]),onItemShiftTab:l.useCallback(()=>D(!0),[]),onFocusableItemAdd:l.useCallback(()=>H(e=>e+1),[]),onFocusableItemRemove:l.useCallback(()=>H(e=>e-1),[]),children:(0,f.jsx)(s.WV.div,{tabIndex:E||0===O?-1:0,"data-orientation":r,...M,ref:I,style:{outline:"none",...e.style},onMouseDown:(0,o.M)(e.onMouseDown,()=>{G.current=!0}),onFocus:(0,o.M)(e.onFocus,e=>{let t=!G.current;if(e.target===e.currentTarget&&t&&!E){let t=new CustomEvent(p,m);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=L().filter(e=>e.focusable);P([e.find(e=>e.active),e.find(e=>e.id===V),...e].filter(Boolean).map(e=>e.ref.current),F)}}G.current=!1}),onBlur:(0,o.M)(e.onBlur,()=>D(!1))})})}),I="RovingFocusGroupItem",x=l.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:r=!0,active:i=!1,tabStopId:u,children:g,...d}=e,c=(0,a.M)(),p=u||c,m=F(I,n),v=m.currentTabStopId===p,C=h(n),{onFocusableItemAdd:R,onFocusableItemRemove:b,currentTabStopId:S}=m;return l.useEffect(()=>{if(r)return R(),()=>b()},[r,R,b]),(0,f.jsx)(w.ItemSlot,{scope:n,id:p,focusable:r,active:i,children:(0,f.jsx)(s.WV.span,{tabIndex:v?0:-1,"data-orientation":m.orientation,...d,ref:t,onMouseDown:(0,o.M)(e.onMouseDown,e=>{r?m.onItemFocus(p):e.preventDefault()}),onFocus:(0,o.M)(e.onFocus,()=>m.onItemFocus(p)),onKeyDown:(0,o.M)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){m.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,n){var l;let o=(l=e.key,"rtl"!==n?l:"ArrowLeft"===l?"ArrowRight":"ArrowRight"===l?"ArrowLeft":l);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return V[o]}(e,m.orientation,m.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let o=C().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)o.reverse();else if("prev"===t||"next"===t){var n,l;"prev"===t&&o.reverse();let r=o.indexOf(e.currentTarget);o=m.loop?(n=o,l=r+1,n.map((e,t)=>n[(l+t)%n.length])):o.slice(r+1)}setTimeout(()=>P(o))}}),children:"function"==typeof g?g({isCurrentTabStop:v,hasTabStop:null!=S}):g})})});x.displayName=I;var V={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function P(e,t=!1){let n=document.activeElement;for(let l of e)if(l===n||(l.focus({preventScroll:t}),document.activeElement!==n))return}var _=M,E=x},3216:function(e,t,n){n.d(t,{b7:function(){return i},ie:function(){return r}});var l=n(2265),o=n(7660);/**
   * react-table
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function r(e,t){return e?"function"==typeof e&&(()=>{let t=Object.getPrototypeOf(e);return t.prototype&&t.prototype.isReactComponent})()||"function"==typeof e||"object"==typeof e&&"symbol"==typeof e.$$typeof&&["react.memo","react.forward_ref"].includes(e.$$typeof.description)?l.createElement(e,t):e:null}function i(e){let t={state:{},onStateChange:()=>{},renderFallbackValue:null,...e},[n]=l.useState(()=>({current:(0,o.W_)(t)})),[r,i]=l.useState(()=>n.current.initialState);return n.current.setOptions(t=>({...t,...e,state:{...r,...e.state},onStateChange:t=>{i(t),null==e.onStateChange||e.onStateChange(t)}})),n.current}},7660:function(e,t,n){function l(e,t){return"function"==typeof e?e(t):e}function o(e,t){return n=>{t.setState(t=>({...t,[e]:l(n,t[e])}))}}function r(e){return e instanceof Function}function i(e,t,n){let l,o=[];return r=>{let i,u;n.key&&n.debug&&(i=Date.now());let a=e(r);if(!(a.length!==o.length||a.some((e,t)=>o[t]!==e)))return l;if(o=a,n.key&&n.debug&&(u=Date.now()),l=t(...a),null==n||null==n.onChange||n.onChange(l),n.key&&n.debug&&null!=n&&n.debug()){let e=Math.round((Date.now()-i)*100)/100,t=Math.round((Date.now()-u)*100)/100,l=t/16,o=(e,t)=>{for(e=String(e);e.length<t;)e=" "+e;return e};console.info(`%c⏱ ${o(t,5)} /${o(e,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*l,120))}deg 100% 31%);`,null==n?void 0:n.key)}return l}}function u(e,t,n,l){return{debug:()=>{var n;return null!=(n=null==e?void 0:e.debugAll)?n:e[t]},key:!1,onChange:l}}n.d(t,{G_:function(){return Z},W_:function(){return B},sC:function(){return q},tj:function(){return W},vL:function(){return U}});let a="debugHeaders";function s(e,t,n){var l;let o={id:null!=(l=n.id)?l:t.id,column:t,index:n.index,isPlaceholder:!!n.isPlaceholder,placeholderId:n.placeholderId,depth:n.depth,subHeaders:[],colSpan:0,rowSpan:0,headerGroup:null,getLeafHeaders:()=>{let e=[],t=n=>{n.subHeaders&&n.subHeaders.length&&n.subHeaders.map(t),e.push(n)};return t(o),e},getContext:()=>({table:e,header:o,column:t})};return e._features.forEach(t=>{null==t.createHeader||t.createHeader(o,e)}),o}function g(e,t,n,l){var o,r;let i=0,u=function(e,t){void 0===t&&(t=1),i=Math.max(i,t),e.filter(e=>e.getIsVisible()).forEach(e=>{var n;null!=(n=e.columns)&&n.length&&u(e.columns,t+1)},0)};u(e);let a=[],g=(e,t)=>{let o={depth:t,id:[l,`${t}`].filter(Boolean).join("_"),headers:[]},r=[];e.forEach(e=>{let i;let u=[...r].reverse()[0],a=e.column.depth===o.depth,g=!1;if(a&&e.column.parent?i=e.column.parent:(i=e.column,g=!0),u&&(null==u?void 0:u.column)===i)u.subHeaders.push(e);else{let o=s(n,i,{id:[l,t,i.id,null==e?void 0:e.id].filter(Boolean).join("_"),isPlaceholder:g,placeholderId:g?`${r.filter(e=>e.column===i).length}`:void 0,depth:t,index:r.length});o.subHeaders.push(e),r.push(o)}o.headers.push(e),e.headerGroup=o}),a.push(o),t>0&&g(r,t-1)};g(t.map((e,t)=>s(n,e,{depth:i,index:t})),i-1),a.reverse();let d=e=>e.filter(e=>e.column.getIsVisible()).map(e=>{let t=0,n=0,l=[0];return e.subHeaders&&e.subHeaders.length?(l=[],d(e.subHeaders).forEach(e=>{let{colSpan:n,rowSpan:o}=e;t+=n,l.push(o)})):t=1,n+=Math.min(...l),e.colSpan=t,e.rowSpan=n,{colSpan:t,rowSpan:n}});return d(null!=(o=null==(r=a[0])?void 0:r.headers)?o:[]),a}let d=(e,t,n,l,o,r,a)=>{let s={id:t,index:l,original:n,depth:o,parentId:a,_valuesCache:{},_uniqueValuesCache:{},getValue:t=>{if(s._valuesCache.hasOwnProperty(t))return s._valuesCache[t];let n=e.getColumn(t);if(null!=n&&n.accessorFn)return s._valuesCache[t]=n.accessorFn(s.original,l),s._valuesCache[t]},getUniqueValues:t=>{if(s._uniqueValuesCache.hasOwnProperty(t))return s._uniqueValuesCache[t];let n=e.getColumn(t);return null!=n&&n.accessorFn?(n.columnDef.getUniqueValues?s._uniqueValuesCache[t]=n.columnDef.getUniqueValues(s.original,l):s._uniqueValuesCache[t]=[s.getValue(t)],s._uniqueValuesCache[t]):void 0},renderValue:t=>{var n;return null!=(n=s.getValue(t))?n:e.options.renderFallbackValue},subRows:null!=r?r:[],getLeafRows:()=>(function(e,t){let n=[],l=e=>{e.forEach(e=>{n.push(e);let o=t(e);null!=o&&o.length&&l(o)})};return l(e),n})(s.subRows,e=>e.subRows),getParentRow:()=>s.parentId?e.getRow(s.parentId,!0):void 0,getParentRows:()=>{let e=[],t=s;for(;;){let n=t.getParentRow();if(!n)break;e.push(n),t=n}return e.reverse()},getAllCells:i(()=>[e.getAllLeafColumns()],t=>t.map(t=>(function(e,t,n,l){let o={id:`${t.id}_${n.id}`,row:t,column:n,getValue:()=>t.getValue(l),renderValue:()=>{var t;return null!=(t=o.getValue())?t:e.options.renderFallbackValue},getContext:i(()=>[e,n,t,o],(e,t,n,l)=>({table:e,column:t,row:n,cell:l,getValue:l.getValue,renderValue:l.renderValue}),u(e.options,"debugCells","cell.getContext"))};return e._features.forEach(l=>{null==l.createCell||l.createCell(o,n,t,e)},{}),o})(e,s,t,t.id)),u(e.options,"debugRows","getAllCells")),_getAllCellsByColumnId:i(()=>[s.getAllCells()],e=>e.reduce((e,t)=>(e[t.column.id]=t,e),{}),u(e.options,"debugRows","getAllCellsByColumnId"))};for(let t=0;t<e._features.length;t++){let n=e._features[t];null==n||null==n.createRow||n.createRow(s,e)}return s},c=(e,t,n)=>{var l,o;let r=null==n||null==(l=n.toString())?void 0:l.toLowerCase();return!!(null==(o=e.getValue(t))||null==(o=o.toString())||null==(o=o.toLowerCase())?void 0:o.includes(r))};c.autoRemove=e=>S(e);let f=(e,t,n)=>{var l;return!!(null==(l=e.getValue(t))||null==(l=l.toString())?void 0:l.includes(n))};f.autoRemove=e=>S(e);let p=(e,t,n)=>{var l;return(null==(l=e.getValue(t))||null==(l=l.toString())?void 0:l.toLowerCase())===(null==n?void 0:n.toLowerCase())};p.autoRemove=e=>S(e);let m=(e,t,n)=>{var l;return null==(l=e.getValue(t))?void 0:l.includes(n)};m.autoRemove=e=>S(e);let v=(e,t,n)=>!n.some(n=>{var l;return!(null!=(l=e.getValue(t))&&l.includes(n))});v.autoRemove=e=>S(e)||!(null!=e&&e.length);let w=(e,t,n)=>n.some(n=>{var l;return null==(l=e.getValue(t))?void 0:l.includes(n)});w.autoRemove=e=>S(e)||!(null!=e&&e.length);let h=(e,t,n)=>e.getValue(t)===n;h.autoRemove=e=>S(e);let C=(e,t,n)=>e.getValue(t)==n;C.autoRemove=e=>S(e);let R=(e,t,n)=>{let[l,o]=n,r=e.getValue(t);return r>=l&&r<=o};R.resolveFilterValue=e=>{let[t,n]=e,l="number"!=typeof t?parseFloat(t):t,o="number"!=typeof n?parseFloat(n):n,r=null===t||Number.isNaN(l)?-1/0:l,i=null===n||Number.isNaN(o)?1/0:o;if(r>i){let e=r;r=i,i=e}return[r,i]},R.autoRemove=e=>S(e)||S(e[0])&&S(e[1]);let b={includesString:c,includesStringSensitive:f,equalsString:p,arrIncludes:m,arrIncludesAll:v,arrIncludesSome:w,equals:h,weakEquals:C,inNumberRange:R};function S(e){return null==e||""===e}function F(e,t,n){return!!e&&!!e.autoRemove&&e.autoRemove(t,n)||void 0===t||"string"==typeof t&&!t}let M={sum:(e,t,n)=>n.reduce((t,n)=>{let l=n.getValue(e);return t+("number"==typeof l?l:0)},0),min:(e,t,n)=>{let l;return n.forEach(t=>{let n=t.getValue(e);null!=n&&(l>n||void 0===l&&n>=n)&&(l=n)}),l},max:(e,t,n)=>{let l;return n.forEach(t=>{let n=t.getValue(e);null!=n&&(l<n||void 0===l&&n>=n)&&(l=n)}),l},extent:(e,t,n)=>{let l,o;return n.forEach(t=>{let n=t.getValue(e);null!=n&&(void 0===l?n>=n&&(l=o=n):(l>n&&(l=n),o<n&&(o=n)))}),[l,o]},mean:(e,t)=>{let n=0,l=0;if(t.forEach(t=>{let o=t.getValue(e);null!=o&&(o=+o)>=o&&(++n,l+=o)}),n)return l/n},median:(e,t)=>{if(!t.length)return;let n=t.map(t=>t.getValue(e));if(!(Array.isArray(n)&&n.every(e=>"number"==typeof e)))return;if(1===n.length)return n[0];let l=Math.floor(n.length/2),o=n.sort((e,t)=>e-t);return n.length%2!=0?o[l]:(o[l-1]+o[l])/2},unique:(e,t)=>Array.from(new Set(t.map(t=>t.getValue(e))).values()),uniqueCount:(e,t)=>new Set(t.map(t=>t.getValue(e))).size,count:(e,t)=>t.length},y=()=>({left:[],right:[]}),I={size:150,minSize:20,maxSize:Number.MAX_SAFE_INTEGER},x=()=>({startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]}),V=null;function P(e){return"touchstart"===e.type}function _(e,t){return t?"center"===t?e.getCenterVisibleLeafColumns():"left"===t?e.getLeftVisibleLeafColumns():e.getRightVisibleLeafColumns():e.getVisibleLeafColumns()}let E=()=>({pageIndex:0,pageSize:10}),D=()=>({top:[],bottom:[]}),A=(e,t,n,l,o)=>{var r;let i=o.getRow(t,!0);n?(i.getCanMultiSelect()||Object.keys(e).forEach(t=>delete e[t]),i.getCanSelect()&&(e[t]=!0)):delete e[t],l&&null!=(r=i.subRows)&&r.length&&i.getCanSelectSubRows()&&i.subRows.forEach(t=>A(e,t.id,n,l,o))};function L(e,t){let n=e.getState().rowSelection,l=[],o={},r=function(e,t){return e.map(e=>{var t;let i=G(e,n);if(i&&(l.push(e),o[e.id]=e),null!=(t=e.subRows)&&t.length&&(e={...e,subRows:r(e.subRows)}),i)return e}).filter(Boolean)};return{rows:r(t.rows),flatRows:l,rowsById:o}}function G(e,t){var n;return null!=(n=t[e.id])&&n}function O(e,t,n){var l;if(!(null!=(l=e.subRows)&&l.length))return!1;let o=!0,r=!1;return e.subRows.forEach(e=>{if((!r||o)&&(e.getCanSelect()&&(G(e,t)?r=!0:o=!1),e.subRows&&e.subRows.length)){let n=O(e,t);"all"===n?r=!0:("some"===n&&(r=!0),o=!1)}}),o?"all":!!r&&"some"}let H=/([0-9]+)/gm;function z(e,t){return e===t?0:e>t?1:-1}function T(e){return"number"==typeof e?isNaN(e)||e===1/0||e===-1/0?"":String(e):"string"==typeof e?e:""}function k(e,t){let n=e.split(H).filter(Boolean),l=t.split(H).filter(Boolean);for(;n.length&&l.length;){let e=n.shift(),t=l.shift(),o=parseInt(e,10),r=parseInt(t,10),i=[o,r].sort();if(isNaN(i[0])){if(e>t)return 1;if(t>e)return -1;continue}if(isNaN(i[1]))return isNaN(o)?-1:1;if(o>r)return 1;if(r>o)return -1}return n.length-l.length}let N={alphanumeric:(e,t,n)=>k(T(e.getValue(n)).toLowerCase(),T(t.getValue(n)).toLowerCase()),alphanumericCaseSensitive:(e,t,n)=>k(T(e.getValue(n)),T(t.getValue(n))),text:(e,t,n)=>z(T(e.getValue(n)).toLowerCase(),T(t.getValue(n)).toLowerCase()),textCaseSensitive:(e,t,n)=>z(T(e.getValue(n)),T(t.getValue(n))),datetime:(e,t,n)=>{let l=e.getValue(n),o=t.getValue(n);return l>o?1:l<o?-1:0},basic:(e,t,n)=>z(e.getValue(n),t.getValue(n))},j=[{createTable:e=>{e.getHeaderGroups=i(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,l,o)=>{var r,i;let u=null!=(r=null==l?void 0:l.map(e=>n.find(t=>t.id===e)).filter(Boolean))?r:[],a=null!=(i=null==o?void 0:o.map(e=>n.find(t=>t.id===e)).filter(Boolean))?i:[];return g(t,[...u,...n.filter(e=>!(null!=l&&l.includes(e.id))&&!(null!=o&&o.includes(e.id))),...a],e)},u(e.options,a,"getHeaderGroups")),e.getCenterHeaderGroups=i(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,l,o)=>g(t,n=n.filter(e=>!(null!=l&&l.includes(e.id))&&!(null!=o&&o.includes(e.id))),e,"center"),u(e.options,a,"getCenterHeaderGroups")),e.getLeftHeaderGroups=i(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left],(t,n,l)=>{var o;return g(t,null!=(o=null==l?void 0:l.map(e=>n.find(t=>t.id===e)).filter(Boolean))?o:[],e,"left")},u(e.options,a,"getLeftHeaderGroups")),e.getRightHeaderGroups=i(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.right],(t,n,l)=>{var o;return g(t,null!=(o=null==l?void 0:l.map(e=>n.find(t=>t.id===e)).filter(Boolean))?o:[],e,"right")},u(e.options,a,"getRightHeaderGroups")),e.getFooterGroups=i(()=>[e.getHeaderGroups()],e=>[...e].reverse(),u(e.options,a,"getFooterGroups")),e.getLeftFooterGroups=i(()=>[e.getLeftHeaderGroups()],e=>[...e].reverse(),u(e.options,a,"getLeftFooterGroups")),e.getCenterFooterGroups=i(()=>[e.getCenterHeaderGroups()],e=>[...e].reverse(),u(e.options,a,"getCenterFooterGroups")),e.getRightFooterGroups=i(()=>[e.getRightHeaderGroups()],e=>[...e].reverse(),u(e.options,a,"getRightFooterGroups")),e.getFlatHeaders=i(()=>[e.getHeaderGroups()],e=>e.map(e=>e.headers).flat(),u(e.options,a,"getFlatHeaders")),e.getLeftFlatHeaders=i(()=>[e.getLeftHeaderGroups()],e=>e.map(e=>e.headers).flat(),u(e.options,a,"getLeftFlatHeaders")),e.getCenterFlatHeaders=i(()=>[e.getCenterHeaderGroups()],e=>e.map(e=>e.headers).flat(),u(e.options,a,"getCenterFlatHeaders")),e.getRightFlatHeaders=i(()=>[e.getRightHeaderGroups()],e=>e.map(e=>e.headers).flat(),u(e.options,a,"getRightFlatHeaders")),e.getCenterLeafHeaders=i(()=>[e.getCenterFlatHeaders()],e=>e.filter(e=>{var t;return!(null!=(t=e.subHeaders)&&t.length)}),u(e.options,a,"getCenterLeafHeaders")),e.getLeftLeafHeaders=i(()=>[e.getLeftFlatHeaders()],e=>e.filter(e=>{var t;return!(null!=(t=e.subHeaders)&&t.length)}),u(e.options,a,"getLeftLeafHeaders")),e.getRightLeafHeaders=i(()=>[e.getRightFlatHeaders()],e=>e.filter(e=>{var t;return!(null!=(t=e.subHeaders)&&t.length)}),u(e.options,a,"getRightLeafHeaders")),e.getLeafHeaders=i(()=>[e.getLeftHeaderGroups(),e.getCenterHeaderGroups(),e.getRightHeaderGroups()],(e,t,n)=>{var l,o,r,i,u,a;return[...null!=(l=null==(o=e[0])?void 0:o.headers)?l:[],...null!=(r=null==(i=t[0])?void 0:i.headers)?r:[],...null!=(u=null==(a=n[0])?void 0:a.headers)?u:[]].map(e=>e.getLeafHeaders()).flat()},u(e.options,a,"getLeafHeaders"))}},{getInitialState:e=>({columnVisibility:{},...e}),getDefaultOptions:e=>({onColumnVisibilityChange:o("columnVisibility",e)}),createColumn:(e,t)=>{e.toggleVisibility=n=>{e.getCanHide()&&t.setColumnVisibility(t=>({...t,[e.id]:null!=n?n:!e.getIsVisible()}))},e.getIsVisible=()=>{var n,l;let o=e.columns;return null==(n=o.length?o.some(e=>e.getIsVisible()):null==(l=t.getState().columnVisibility)?void 0:l[e.id])||n},e.getCanHide=()=>{var n,l;return(null==(n=e.columnDef.enableHiding)||n)&&(null==(l=t.options.enableHiding)||l)},e.getToggleVisibilityHandler=()=>t=>{null==e.toggleVisibility||e.toggleVisibility(t.target.checked)}},createRow:(e,t)=>{e._getAllVisibleCells=i(()=>[e.getAllCells(),t.getState().columnVisibility],e=>e.filter(e=>e.column.getIsVisible()),u(t.options,"debugRows","_getAllVisibleCells")),e.getVisibleCells=i(()=>[e.getLeftVisibleCells(),e.getCenterVisibleCells(),e.getRightVisibleCells()],(e,t,n)=>[...e,...t,...n],u(t.options,"debugRows","getVisibleCells"))},createTable:e=>{let t=(t,n)=>i(()=>[n(),n().filter(e=>e.getIsVisible()).map(e=>e.id).join("_")],e=>e.filter(e=>null==e.getIsVisible?void 0:e.getIsVisible()),u(e.options,"debugColumns",t));e.getVisibleFlatColumns=t("getVisibleFlatColumns",()=>e.getAllFlatColumns()),e.getVisibleLeafColumns=t("getVisibleLeafColumns",()=>e.getAllLeafColumns()),e.getLeftVisibleLeafColumns=t("getLeftVisibleLeafColumns",()=>e.getLeftLeafColumns()),e.getRightVisibleLeafColumns=t("getRightVisibleLeafColumns",()=>e.getRightLeafColumns()),e.getCenterVisibleLeafColumns=t("getCenterVisibleLeafColumns",()=>e.getCenterLeafColumns()),e.setColumnVisibility=t=>null==e.options.onColumnVisibilityChange?void 0:e.options.onColumnVisibilityChange(t),e.resetColumnVisibility=t=>{var n;e.setColumnVisibility(t?{}:null!=(n=e.initialState.columnVisibility)?n:{})},e.toggleAllColumnsVisible=t=>{var n;t=null!=(n=t)?n:!e.getIsAllColumnsVisible(),e.setColumnVisibility(e.getAllLeafColumns().reduce((e,n)=>({...e,[n.id]:t||!(null!=n.getCanHide&&n.getCanHide())}),{}))},e.getIsAllColumnsVisible=()=>!e.getAllLeafColumns().some(e=>!(null!=e.getIsVisible&&e.getIsVisible())),e.getIsSomeColumnsVisible=()=>e.getAllLeafColumns().some(e=>null==e.getIsVisible?void 0:e.getIsVisible()),e.getToggleAllColumnsVisibilityHandler=()=>t=>{var n;e.toggleAllColumnsVisible(null==(n=t.target)?void 0:n.checked)}}},{getInitialState:e=>({columnOrder:[],...e}),getDefaultOptions:e=>({onColumnOrderChange:o("columnOrder",e)}),createColumn:(e,t)=>{e.getIndex=i(e=>[_(t,e)],t=>t.findIndex(t=>t.id===e.id),u(t.options,"debugColumns","getIndex")),e.getIsFirstColumn=n=>{var l;return(null==(l=_(t,n)[0])?void 0:l.id)===e.id},e.getIsLastColumn=n=>{var l;let o=_(t,n);return(null==(l=o[o.length-1])?void 0:l.id)===e.id}},createTable:e=>{e.setColumnOrder=t=>null==e.options.onColumnOrderChange?void 0:e.options.onColumnOrderChange(t),e.resetColumnOrder=t=>{var n;e.setColumnOrder(t?[]:null!=(n=e.initialState.columnOrder)?n:[])},e._getOrderColumnsFn=i(()=>[e.getState().columnOrder,e.getState().grouping,e.options.groupedColumnMode],(e,t,n)=>l=>{let o=[];if(null!=e&&e.length){let t=[...e],n=[...l];for(;n.length&&t.length;){let e=t.shift(),l=n.findIndex(t=>t.id===e);l>-1&&o.push(n.splice(l,1)[0])}o=[...o,...n]}else o=l;return function(e,t,n){if(!(null!=t&&t.length)||!n)return e;let l=e.filter(e=>!t.includes(e.id));return"remove"===n?l:[...t.map(t=>e.find(e=>e.id===t)).filter(Boolean),...l]}(o,t,n)},u(e.options,"debugTable","_getOrderColumnsFn"))}},{getInitialState:e=>({columnPinning:y(),...e}),getDefaultOptions:e=>({onColumnPinningChange:o("columnPinning",e)}),createColumn:(e,t)=>{e.pin=n=>{let l=e.getLeafColumns().map(e=>e.id).filter(Boolean);t.setColumnPinning(e=>{var t,o,r,i,u,a;return"right"===n?{left:(null!=(r=null==e?void 0:e.left)?r:[]).filter(e=>!(null!=l&&l.includes(e))),right:[...(null!=(i=null==e?void 0:e.right)?i:[]).filter(e=>!(null!=l&&l.includes(e))),...l]}:"left"===n?{left:[...(null!=(u=null==e?void 0:e.left)?u:[]).filter(e=>!(null!=l&&l.includes(e))),...l],right:(null!=(a=null==e?void 0:e.right)?a:[]).filter(e=>!(null!=l&&l.includes(e)))}:{left:(null!=(t=null==e?void 0:e.left)?t:[]).filter(e=>!(null!=l&&l.includes(e))),right:(null!=(o=null==e?void 0:e.right)?o:[]).filter(e=>!(null!=l&&l.includes(e)))}})},e.getCanPin=()=>e.getLeafColumns().some(e=>{var n,l,o;return(null==(n=e.columnDef.enablePinning)||n)&&(null==(l=null!=(o=t.options.enableColumnPinning)?o:t.options.enablePinning)||l)}),e.getIsPinned=()=>{let n=e.getLeafColumns().map(e=>e.id),{left:l,right:o}=t.getState().columnPinning,r=n.some(e=>null==l?void 0:l.includes(e)),i=n.some(e=>null==o?void 0:o.includes(e));return r?"left":!!i&&"right"},e.getPinnedIndex=()=>{var n,l;let o=e.getIsPinned();return o?null!=(n=null==(l=t.getState().columnPinning)||null==(l=l[o])?void 0:l.indexOf(e.id))?n:-1:0}},createRow:(e,t)=>{e.getCenterVisibleCells=i(()=>[e._getAllVisibleCells(),t.getState().columnPinning.left,t.getState().columnPinning.right],(e,t,n)=>{let l=[...null!=t?t:[],...null!=n?n:[]];return e.filter(e=>!l.includes(e.column.id))},u(t.options,"debugRows","getCenterVisibleCells")),e.getLeftVisibleCells=i(()=>[e._getAllVisibleCells(),t.getState().columnPinning.left],(e,t)=>(null!=t?t:[]).map(t=>e.find(e=>e.column.id===t)).filter(Boolean).map(e=>({...e,position:"left"})),u(t.options,"debugRows","getLeftVisibleCells")),e.getRightVisibleCells=i(()=>[e._getAllVisibleCells(),t.getState().columnPinning.right],(e,t)=>(null!=t?t:[]).map(t=>e.find(e=>e.column.id===t)).filter(Boolean).map(e=>({...e,position:"right"})),u(t.options,"debugRows","getRightVisibleCells"))},createTable:e=>{e.setColumnPinning=t=>null==e.options.onColumnPinningChange?void 0:e.options.onColumnPinningChange(t),e.resetColumnPinning=t=>{var n,l;return e.setColumnPinning(t?y():null!=(n=null==(l=e.initialState)?void 0:l.columnPinning)?n:y())},e.getIsSomeColumnsPinned=t=>{var n,l,o;let r=e.getState().columnPinning;return t?!!(null==(n=r[t])?void 0:n.length):!!((null==(l=r.left)?void 0:l.length)||(null==(o=r.right)?void 0:o.length))},e.getLeftLeafColumns=i(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left],(e,t)=>(null!=t?t:[]).map(t=>e.find(e=>e.id===t)).filter(Boolean),u(e.options,"debugColumns","getLeftLeafColumns")),e.getRightLeafColumns=i(()=>[e.getAllLeafColumns(),e.getState().columnPinning.right],(e,t)=>(null!=t?t:[]).map(t=>e.find(e=>e.id===t)).filter(Boolean),u(e.options,"debugColumns","getRightLeafColumns")),e.getCenterLeafColumns=i(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(e,t,n)=>{let l=[...null!=t?t:[],...null!=n?n:[]];return e.filter(e=>!l.includes(e.id))},u(e.options,"debugColumns","getCenterLeafColumns"))}},{createColumn:(e,t)=>{e._getFacetedRowModel=t.options.getFacetedRowModel&&t.options.getFacetedRowModel(t,e.id),e.getFacetedRowModel=()=>e._getFacetedRowModel?e._getFacetedRowModel():t.getPreFilteredRowModel(),e._getFacetedUniqueValues=t.options.getFacetedUniqueValues&&t.options.getFacetedUniqueValues(t,e.id),e.getFacetedUniqueValues=()=>e._getFacetedUniqueValues?e._getFacetedUniqueValues():new Map,e._getFacetedMinMaxValues=t.options.getFacetedMinMaxValues&&t.options.getFacetedMinMaxValues(t,e.id),e.getFacetedMinMaxValues=()=>{if(e._getFacetedMinMaxValues)return e._getFacetedMinMaxValues()}}},{getDefaultColumnDef:()=>({filterFn:"auto"}),getInitialState:e=>({columnFilters:[],...e}),getDefaultOptions:e=>({onColumnFiltersChange:o("columnFilters",e),filterFromLeafRows:!1,maxLeafRowFilterDepth:100}),createColumn:(e,t)=>{e.getAutoFilterFn=()=>{let n=t.getCoreRowModel().flatRows[0],l=null==n?void 0:n.getValue(e.id);return"string"==typeof l?b.includesString:"number"==typeof l?b.inNumberRange:"boolean"==typeof l||null!==l&&"object"==typeof l?b.equals:Array.isArray(l)?b.arrIncludes:b.weakEquals},e.getFilterFn=()=>{var n,l;return r(e.columnDef.filterFn)?e.columnDef.filterFn:"auto"===e.columnDef.filterFn?e.getAutoFilterFn():null!=(n=null==(l=t.options.filterFns)?void 0:l[e.columnDef.filterFn])?n:b[e.columnDef.filterFn]},e.getCanFilter=()=>{var n,l,o;return(null==(n=e.columnDef.enableColumnFilter)||n)&&(null==(l=t.options.enableColumnFilters)||l)&&(null==(o=t.options.enableFilters)||o)&&!!e.accessorFn},e.getIsFiltered=()=>e.getFilterIndex()>-1,e.getFilterValue=()=>{var n;return null==(n=t.getState().columnFilters)||null==(n=n.find(t=>t.id===e.id))?void 0:n.value},e.getFilterIndex=()=>{var n,l;return null!=(n=null==(l=t.getState().columnFilters)?void 0:l.findIndex(t=>t.id===e.id))?n:-1},e.setFilterValue=n=>{t.setColumnFilters(t=>{var o,r;let i=e.getFilterFn(),u=null==t?void 0:t.find(t=>t.id===e.id),a=l(n,u?u.value:void 0);if(F(i,a,e))return null!=(o=null==t?void 0:t.filter(t=>t.id!==e.id))?o:[];let s={id:e.id,value:a};return u?null!=(r=null==t?void 0:t.map(t=>t.id===e.id?s:t))?r:[]:null!=t&&t.length?[...t,s]:[s]})}},createRow:(e,t)=>{e.columnFilters={},e.columnFiltersMeta={}},createTable:e=>{e.setColumnFilters=t=>{let n=e.getAllLeafColumns();null==e.options.onColumnFiltersChange||e.options.onColumnFiltersChange(e=>{var o;return null==(o=l(t,e))?void 0:o.filter(e=>{let t=n.find(t=>t.id===e.id);return!(t&&F(t.getFilterFn(),e.value,t))})})},e.resetColumnFilters=t=>{var n,l;e.setColumnFilters(t?[]:null!=(n=null==(l=e.initialState)?void 0:l.columnFilters)?n:[])},e.getPreFilteredRowModel=()=>e.getCoreRowModel(),e.getFilteredRowModel=()=>(!e._getFilteredRowModel&&e.options.getFilteredRowModel&&(e._getFilteredRowModel=e.options.getFilteredRowModel(e)),e.options.manualFiltering||!e._getFilteredRowModel)?e.getPreFilteredRowModel():e._getFilteredRowModel()}},{createTable:e=>{e._getGlobalFacetedRowModel=e.options.getFacetedRowModel&&e.options.getFacetedRowModel(e,"__global__"),e.getGlobalFacetedRowModel=()=>e.options.manualFiltering||!e._getGlobalFacetedRowModel?e.getPreFilteredRowModel():e._getGlobalFacetedRowModel(),e._getGlobalFacetedUniqueValues=e.options.getFacetedUniqueValues&&e.options.getFacetedUniqueValues(e,"__global__"),e.getGlobalFacetedUniqueValues=()=>e._getGlobalFacetedUniqueValues?e._getGlobalFacetedUniqueValues():new Map,e._getGlobalFacetedMinMaxValues=e.options.getFacetedMinMaxValues&&e.options.getFacetedMinMaxValues(e,"__global__"),e.getGlobalFacetedMinMaxValues=()=>{if(e._getGlobalFacetedMinMaxValues)return e._getGlobalFacetedMinMaxValues()}}},{getInitialState:e=>({globalFilter:void 0,...e}),getDefaultOptions:e=>({onGlobalFilterChange:o("globalFilter",e),globalFilterFn:"auto",getColumnCanGlobalFilter:t=>{var n;let l=null==(n=e.getCoreRowModel().flatRows[0])||null==(n=n._getAllCellsByColumnId()[t.id])?void 0:n.getValue();return"string"==typeof l||"number"==typeof l}}),createColumn:(e,t)=>{e.getCanGlobalFilter=()=>{var n,l,o,r;return(null==(n=e.columnDef.enableGlobalFilter)||n)&&(null==(l=t.options.enableGlobalFilter)||l)&&(null==(o=t.options.enableFilters)||o)&&(null==(r=null==t.options.getColumnCanGlobalFilter?void 0:t.options.getColumnCanGlobalFilter(e))||r)&&!!e.accessorFn}},createTable:e=>{e.getGlobalAutoFilterFn=()=>b.includesString,e.getGlobalFilterFn=()=>{var t,n;let{globalFilterFn:l}=e.options;return r(l)?l:"auto"===l?e.getGlobalAutoFilterFn():null!=(t=null==(n=e.options.filterFns)?void 0:n[l])?t:b[l]},e.setGlobalFilter=t=>{null==e.options.onGlobalFilterChange||e.options.onGlobalFilterChange(t)},e.resetGlobalFilter=t=>{e.setGlobalFilter(t?void 0:e.initialState.globalFilter)}}},{getInitialState:e=>({sorting:[],...e}),getDefaultColumnDef:()=>({sortingFn:"auto",sortUndefined:1}),getDefaultOptions:e=>({onSortingChange:o("sorting",e),isMultiSortEvent:e=>e.shiftKey}),createColumn:(e,t)=>{e.getAutoSortingFn=()=>{let n=t.getFilteredRowModel().flatRows.slice(10),l=!1;for(let t of n){let n=null==t?void 0:t.getValue(e.id);if("[object Date]"===Object.prototype.toString.call(n))return N.datetime;if("string"==typeof n&&(l=!0,n.split(H).length>1))return N.alphanumeric}return l?N.text:N.basic},e.getAutoSortDir=()=>{let n=t.getFilteredRowModel().flatRows[0];return"string"==typeof(null==n?void 0:n.getValue(e.id))?"asc":"desc"},e.getSortingFn=()=>{var n,l;if(!e)throw Error();return r(e.columnDef.sortingFn)?e.columnDef.sortingFn:"auto"===e.columnDef.sortingFn?e.getAutoSortingFn():null!=(n=null==(l=t.options.sortingFns)?void 0:l[e.columnDef.sortingFn])?n:N[e.columnDef.sortingFn]},e.toggleSorting=(n,l)=>{let o=e.getNextSortingOrder(),r=null!=n;t.setSorting(i=>{let u;let a=null==i?void 0:i.find(t=>t.id===e.id),s=null==i?void 0:i.findIndex(t=>t.id===e.id),g=[],d=r?n:"desc"===o;if("toggle"!=(u=null!=i&&i.length&&e.getCanMultiSort()&&l?a?"toggle":"add":null!=i&&i.length&&s!==i.length-1?"replace":a?"toggle":"replace")||r||o||(u="remove"),"add"===u){var c;(g=[...i,{id:e.id,desc:d}]).splice(0,g.length-(null!=(c=t.options.maxMultiSortColCount)?c:Number.MAX_SAFE_INTEGER))}else g="toggle"===u?i.map(t=>t.id===e.id?{...t,desc:d}:t):"remove"===u?i.filter(t=>t.id!==e.id):[{id:e.id,desc:d}];return g})},e.getFirstSortDir=()=>{var n,l;return(null!=(n=null!=(l=e.columnDef.sortDescFirst)?l:t.options.sortDescFirst)?n:"desc"===e.getAutoSortDir())?"desc":"asc"},e.getNextSortingOrder=n=>{var l,o;let r=e.getFirstSortDir(),i=e.getIsSorted();return i?(i===r||null!=(l=t.options.enableSortingRemoval)&&!l||!!n&&null!=(o=t.options.enableMultiRemove)&&!o)&&("desc"===i?"asc":"desc"):r},e.getCanSort=()=>{var n,l;return(null==(n=e.columnDef.enableSorting)||n)&&(null==(l=t.options.enableSorting)||l)&&!!e.accessorFn},e.getCanMultiSort=()=>{var n,l;return null!=(n=null!=(l=e.columnDef.enableMultiSort)?l:t.options.enableMultiSort)?n:!!e.accessorFn},e.getIsSorted=()=>{var n;let l=null==(n=t.getState().sorting)?void 0:n.find(t=>t.id===e.id);return!!l&&(l.desc?"desc":"asc")},e.getSortIndex=()=>{var n,l;return null!=(n=null==(l=t.getState().sorting)?void 0:l.findIndex(t=>t.id===e.id))?n:-1},e.clearSorting=()=>{t.setSorting(t=>null!=t&&t.length?t.filter(t=>t.id!==e.id):[])},e.getToggleSortingHandler=()=>{let n=e.getCanSort();return l=>{n&&(null==l.persist||l.persist(),null==e.toggleSorting||e.toggleSorting(void 0,!!e.getCanMultiSort()&&(null==t.options.isMultiSortEvent?void 0:t.options.isMultiSortEvent(l))))}}},createTable:e=>{e.setSorting=t=>null==e.options.onSortingChange?void 0:e.options.onSortingChange(t),e.resetSorting=t=>{var n,l;e.setSorting(t?[]:null!=(n=null==(l=e.initialState)?void 0:l.sorting)?n:[])},e.getPreSortedRowModel=()=>e.getGroupedRowModel(),e.getSortedRowModel=()=>(!e._getSortedRowModel&&e.options.getSortedRowModel&&(e._getSortedRowModel=e.options.getSortedRowModel(e)),e.options.manualSorting||!e._getSortedRowModel)?e.getPreSortedRowModel():e._getSortedRowModel()}},{getDefaultColumnDef:()=>({aggregatedCell:e=>{var t,n;return null!=(t=null==(n=e.getValue())||null==n.toString?void 0:n.toString())?t:null},aggregationFn:"auto"}),getInitialState:e=>({grouping:[],...e}),getDefaultOptions:e=>({onGroupingChange:o("grouping",e),groupedColumnMode:"reorder"}),createColumn:(e,t)=>{e.toggleGrouping=()=>{t.setGrouping(t=>null!=t&&t.includes(e.id)?t.filter(t=>t!==e.id):[...null!=t?t:[],e.id])},e.getCanGroup=()=>{var n,l;return(null==(n=e.columnDef.enableGrouping)||n)&&(null==(l=t.options.enableGrouping)||l)&&(!!e.accessorFn||!!e.columnDef.getGroupingValue)},e.getIsGrouped=()=>{var n;return null==(n=t.getState().grouping)?void 0:n.includes(e.id)},e.getGroupedIndex=()=>{var n;return null==(n=t.getState().grouping)?void 0:n.indexOf(e.id)},e.getToggleGroupingHandler=()=>{let t=e.getCanGroup();return()=>{t&&e.toggleGrouping()}},e.getAutoAggregationFn=()=>{let n=t.getCoreRowModel().flatRows[0],l=null==n?void 0:n.getValue(e.id);return"number"==typeof l?M.sum:"[object Date]"===Object.prototype.toString.call(l)?M.extent:void 0},e.getAggregationFn=()=>{var n,l;if(!e)throw Error();return r(e.columnDef.aggregationFn)?e.columnDef.aggregationFn:"auto"===e.columnDef.aggregationFn?e.getAutoAggregationFn():null!=(n=null==(l=t.options.aggregationFns)?void 0:l[e.columnDef.aggregationFn])?n:M[e.columnDef.aggregationFn]}},createTable:e=>{e.setGrouping=t=>null==e.options.onGroupingChange?void 0:e.options.onGroupingChange(t),e.resetGrouping=t=>{var n,l;e.setGrouping(t?[]:null!=(n=null==(l=e.initialState)?void 0:l.grouping)?n:[])},e.getPreGroupedRowModel=()=>e.getFilteredRowModel(),e.getGroupedRowModel=()=>(!e._getGroupedRowModel&&e.options.getGroupedRowModel&&(e._getGroupedRowModel=e.options.getGroupedRowModel(e)),e.options.manualGrouping||!e._getGroupedRowModel)?e.getPreGroupedRowModel():e._getGroupedRowModel()},createRow:(e,t)=>{e.getIsGrouped=()=>!!e.groupingColumnId,e.getGroupingValue=n=>{if(e._groupingValuesCache.hasOwnProperty(n))return e._groupingValuesCache[n];let l=t.getColumn(n);return null!=l&&l.columnDef.getGroupingValue?(e._groupingValuesCache[n]=l.columnDef.getGroupingValue(e.original),e._groupingValuesCache[n]):e.getValue(n)},e._groupingValuesCache={}},createCell:(e,t,n,l)=>{e.getIsGrouped=()=>t.getIsGrouped()&&t.id===n.groupingColumnId,e.getIsPlaceholder=()=>!e.getIsGrouped()&&t.getIsGrouped(),e.getIsAggregated=()=>{var t;return!e.getIsGrouped()&&!e.getIsPlaceholder()&&!!(null!=(t=n.subRows)&&t.length)}}},{getInitialState:e=>({expanded:{},...e}),getDefaultOptions:e=>({onExpandedChange:o("expanded",e),paginateExpandedRows:!0}),createTable:e=>{let t=!1,n=!1;e._autoResetExpanded=()=>{var l,o;if(!t){e._queue(()=>{t=!0});return}if(null!=(l=null!=(o=e.options.autoResetAll)?o:e.options.autoResetExpanded)?l:!e.options.manualExpanding){if(n)return;n=!0,e._queue(()=>{e.resetExpanded(),n=!1})}},e.setExpanded=t=>null==e.options.onExpandedChange?void 0:e.options.onExpandedChange(t),e.toggleAllRowsExpanded=t=>{(null!=t?t:!e.getIsAllRowsExpanded())?e.setExpanded(!0):e.setExpanded({})},e.resetExpanded=t=>{var n,l;e.setExpanded(t?{}:null!=(n=null==(l=e.initialState)?void 0:l.expanded)?n:{})},e.getCanSomeRowsExpand=()=>e.getPrePaginationRowModel().flatRows.some(e=>e.getCanExpand()),e.getToggleAllRowsExpandedHandler=()=>t=>{null==t.persist||t.persist(),e.toggleAllRowsExpanded()},e.getIsSomeRowsExpanded=()=>{let t=e.getState().expanded;return!0===t||Object.values(t).some(Boolean)},e.getIsAllRowsExpanded=()=>{let t=e.getState().expanded;return"boolean"==typeof t?!0===t:!(!Object.keys(t).length||e.getRowModel().flatRows.some(e=>!e.getIsExpanded()))},e.getExpandedDepth=()=>{let t=0;return(!0===e.getState().expanded?Object.keys(e.getRowModel().rowsById):Object.keys(e.getState().expanded)).forEach(e=>{let n=e.split(".");t=Math.max(t,n.length)}),t},e.getPreExpandedRowModel=()=>e.getSortedRowModel(),e.getExpandedRowModel=()=>(!e._getExpandedRowModel&&e.options.getExpandedRowModel&&(e._getExpandedRowModel=e.options.getExpandedRowModel(e)),e.options.manualExpanding||!e._getExpandedRowModel)?e.getPreExpandedRowModel():e._getExpandedRowModel()},createRow:(e,t)=>{e.toggleExpanded=n=>{t.setExpanded(l=>{var o;let r=!0===l||!!(null!=l&&l[e.id]),i={};if(!0===l?Object.keys(t.getRowModel().rowsById).forEach(e=>{i[e]=!0}):i=l,n=null!=(o=n)?o:!r,!r&&n)return{...i,[e.id]:!0};if(r&&!n){let{[e.id]:t,...n}=i;return n}return l})},e.getIsExpanded=()=>{var n;let l=t.getState().expanded;return!!(null!=(n=null==t.options.getIsRowExpanded?void 0:t.options.getIsRowExpanded(e))?n:!0===l||(null==l?void 0:l[e.id]))},e.getCanExpand=()=>{var n,l,o;return null!=(n=null==t.options.getRowCanExpand?void 0:t.options.getRowCanExpand(e))?n:(null==(l=t.options.enableExpanding)||l)&&!!(null!=(o=e.subRows)&&o.length)},e.getIsAllParentsExpanded=()=>{let n=!0,l=e;for(;n&&l.parentId;)n=(l=t.getRow(l.parentId,!0)).getIsExpanded();return n},e.getToggleExpandedHandler=()=>{let t=e.getCanExpand();return()=>{t&&e.toggleExpanded()}}}},{getInitialState:e=>({...e,pagination:{...E(),...null==e?void 0:e.pagination}}),getDefaultOptions:e=>({onPaginationChange:o("pagination",e)}),createTable:e=>{let t=!1,n=!1;e._autoResetPageIndex=()=>{var l,o;if(!t){e._queue(()=>{t=!0});return}if(null!=(l=null!=(o=e.options.autoResetAll)?o:e.options.autoResetPageIndex)?l:!e.options.manualPagination){if(n)return;n=!0,e._queue(()=>{e.resetPageIndex(),n=!1})}},e.setPagination=t=>null==e.options.onPaginationChange?void 0:e.options.onPaginationChange(e=>l(t,e)),e.resetPagination=t=>{var n;e.setPagination(t?E():null!=(n=e.initialState.pagination)?n:E())},e.setPageIndex=t=>{e.setPagination(n=>{let o=l(t,n.pageIndex);return o=Math.max(0,Math.min(o,void 0===e.options.pageCount||-1===e.options.pageCount?Number.MAX_SAFE_INTEGER:e.options.pageCount-1)),{...n,pageIndex:o}})},e.resetPageIndex=t=>{var n,l;e.setPageIndex(t?0:null!=(n=null==(l=e.initialState)||null==(l=l.pagination)?void 0:l.pageIndex)?n:0)},e.resetPageSize=t=>{var n,l;e.setPageSize(t?10:null!=(n=null==(l=e.initialState)||null==(l=l.pagination)?void 0:l.pageSize)?n:10)},e.setPageSize=t=>{e.setPagination(e=>{let n=Math.max(1,l(t,e.pageSize)),o=e.pageSize*e.pageIndex;return{...e,pageIndex:Math.floor(o/n),pageSize:n}})},e.setPageCount=t=>e.setPagination(n=>{var o;let r=l(t,null!=(o=e.options.pageCount)?o:-1);return"number"==typeof r&&(r=Math.max(-1,r)),{...n,pageCount:r}}),e.getPageOptions=i(()=>[e.getPageCount()],e=>{let t=[];return e&&e>0&&(t=[...Array(e)].fill(null).map((e,t)=>t)),t},u(e.options,"debugTable","getPageOptions")),e.getCanPreviousPage=()=>e.getState().pagination.pageIndex>0,e.getCanNextPage=()=>{let{pageIndex:t}=e.getState().pagination,n=e.getPageCount();return -1===n||0!==n&&t<n-1},e.previousPage=()=>e.setPageIndex(e=>e-1),e.nextPage=()=>e.setPageIndex(e=>e+1),e.firstPage=()=>e.setPageIndex(0),e.lastPage=()=>e.setPageIndex(e.getPageCount()-1),e.getPrePaginationRowModel=()=>e.getExpandedRowModel(),e.getPaginationRowModel=()=>(!e._getPaginationRowModel&&e.options.getPaginationRowModel&&(e._getPaginationRowModel=e.options.getPaginationRowModel(e)),e.options.manualPagination||!e._getPaginationRowModel)?e.getPrePaginationRowModel():e._getPaginationRowModel(),e.getPageCount=()=>{var t;return null!=(t=e.options.pageCount)?t:Math.ceil(e.getRowCount()/e.getState().pagination.pageSize)},e.getRowCount=()=>{var t;return null!=(t=e.options.rowCount)?t:e.getPrePaginationRowModel().rows.length}}},{getInitialState:e=>({rowPinning:D(),...e}),getDefaultOptions:e=>({onRowPinningChange:o("rowPinning",e)}),createRow:(e,t)=>{e.pin=(n,l,o)=>{let r=l?e.getLeafRows().map(e=>{let{id:t}=e;return t}):[],i=o?e.getParentRows().map(e=>{let{id:t}=e;return t}):[],u=new Set([...i,e.id,...r]);t.setRowPinning(e=>{var t,l,o,r,i,a;return"bottom"===n?{top:(null!=(o=null==e?void 0:e.top)?o:[]).filter(e=>!(null!=u&&u.has(e))),bottom:[...(null!=(r=null==e?void 0:e.bottom)?r:[]).filter(e=>!(null!=u&&u.has(e))),...Array.from(u)]}:"top"===n?{top:[...(null!=(i=null==e?void 0:e.top)?i:[]).filter(e=>!(null!=u&&u.has(e))),...Array.from(u)],bottom:(null!=(a=null==e?void 0:e.bottom)?a:[]).filter(e=>!(null!=u&&u.has(e)))}:{top:(null!=(t=null==e?void 0:e.top)?t:[]).filter(e=>!(null!=u&&u.has(e))),bottom:(null!=(l=null==e?void 0:e.bottom)?l:[]).filter(e=>!(null!=u&&u.has(e)))}})},e.getCanPin=()=>{var n;let{enableRowPinning:l,enablePinning:o}=t.options;return"function"==typeof l?l(e):null==(n=null!=l?l:o)||n},e.getIsPinned=()=>{let n=[e.id],{top:l,bottom:o}=t.getState().rowPinning,r=n.some(e=>null==l?void 0:l.includes(e)),i=n.some(e=>null==o?void 0:o.includes(e));return r?"top":!!i&&"bottom"},e.getPinnedIndex=()=>{var n,l;let o=e.getIsPinned();if(!o)return -1;let r=null==(n="top"===o?t.getTopRows():t.getBottomRows())?void 0:n.map(e=>{let{id:t}=e;return t});return null!=(l=null==r?void 0:r.indexOf(e.id))?l:-1}},createTable:e=>{e.setRowPinning=t=>null==e.options.onRowPinningChange?void 0:e.options.onRowPinningChange(t),e.resetRowPinning=t=>{var n,l;return e.setRowPinning(t?D():null!=(n=null==(l=e.initialState)?void 0:l.rowPinning)?n:D())},e.getIsSomeRowsPinned=t=>{var n,l,o;let r=e.getState().rowPinning;return t?!!(null==(n=r[t])?void 0:n.length):!!((null==(l=r.top)?void 0:l.length)||(null==(o=r.bottom)?void 0:o.length))},e._getPinnedRows=(t,n,l)=>{var o;return(null==(o=e.options.keepPinnedRows)||o?(null!=n?n:[]).map(t=>{let n=e.getRow(t,!0);return n.getIsAllParentsExpanded()?n:null}):(null!=n?n:[]).map(e=>t.find(t=>t.id===e))).filter(Boolean).map(e=>({...e,position:l}))},e.getTopRows=i(()=>[e.getRowModel().rows,e.getState().rowPinning.top],(t,n)=>e._getPinnedRows(t,n,"top"),u(e.options,"debugRows","getTopRows")),e.getBottomRows=i(()=>[e.getRowModel().rows,e.getState().rowPinning.bottom],(t,n)=>e._getPinnedRows(t,n,"bottom"),u(e.options,"debugRows","getBottomRows")),e.getCenterRows=i(()=>[e.getRowModel().rows,e.getState().rowPinning.top,e.getState().rowPinning.bottom],(e,t,n)=>{let l=new Set([...null!=t?t:[],...null!=n?n:[]]);return e.filter(e=>!l.has(e.id))},u(e.options,"debugRows","getCenterRows"))}},{getInitialState:e=>({rowSelection:{},...e}),getDefaultOptions:e=>({onRowSelectionChange:o("rowSelection",e),enableRowSelection:!0,enableMultiRowSelection:!0,enableSubRowSelection:!0}),createTable:e=>{e.setRowSelection=t=>null==e.options.onRowSelectionChange?void 0:e.options.onRowSelectionChange(t),e.resetRowSelection=t=>{var n;return e.setRowSelection(t?{}:null!=(n=e.initialState.rowSelection)?n:{})},e.toggleAllRowsSelected=t=>{e.setRowSelection(n=>{t=void 0!==t?t:!e.getIsAllRowsSelected();let l={...n},o=e.getPreGroupedRowModel().flatRows;return t?o.forEach(e=>{e.getCanSelect()&&(l[e.id]=!0)}):o.forEach(e=>{delete l[e.id]}),l})},e.toggleAllPageRowsSelected=t=>e.setRowSelection(n=>{let l=void 0!==t?t:!e.getIsAllPageRowsSelected(),o={...n};return e.getRowModel().rows.forEach(t=>{A(o,t.id,l,!0,e)}),o}),e.getPreSelectedRowModel=()=>e.getCoreRowModel(),e.getSelectedRowModel=i(()=>[e.getState().rowSelection,e.getCoreRowModel()],(t,n)=>Object.keys(t).length?L(e,n):{rows:[],flatRows:[],rowsById:{}},u(e.options,"debugTable","getSelectedRowModel")),e.getFilteredSelectedRowModel=i(()=>[e.getState().rowSelection,e.getFilteredRowModel()],(t,n)=>Object.keys(t).length?L(e,n):{rows:[],flatRows:[],rowsById:{}},u(e.options,"debugTable","getFilteredSelectedRowModel")),e.getGroupedSelectedRowModel=i(()=>[e.getState().rowSelection,e.getSortedRowModel()],(t,n)=>Object.keys(t).length?L(e,n):{rows:[],flatRows:[],rowsById:{}},u(e.options,"debugTable","getGroupedSelectedRowModel")),e.getIsAllRowsSelected=()=>{let t=e.getFilteredRowModel().flatRows,{rowSelection:n}=e.getState(),l=!!(t.length&&Object.keys(n).length);return l&&t.some(e=>e.getCanSelect()&&!n[e.id])&&(l=!1),l},e.getIsAllPageRowsSelected=()=>{let t=e.getPaginationRowModel().flatRows.filter(e=>e.getCanSelect()),{rowSelection:n}=e.getState(),l=!!t.length;return l&&t.some(e=>!n[e.id])&&(l=!1),l},e.getIsSomeRowsSelected=()=>{var t;let n=Object.keys(null!=(t=e.getState().rowSelection)?t:{}).length;return n>0&&n<e.getFilteredRowModel().flatRows.length},e.getIsSomePageRowsSelected=()=>{let t=e.getPaginationRowModel().flatRows;return!e.getIsAllPageRowsSelected()&&t.filter(e=>e.getCanSelect()).some(e=>e.getIsSelected()||e.getIsSomeSelected())},e.getToggleAllRowsSelectedHandler=()=>t=>{e.toggleAllRowsSelected(t.target.checked)},e.getToggleAllPageRowsSelectedHandler=()=>t=>{e.toggleAllPageRowsSelected(t.target.checked)}},createRow:(e,t)=>{e.toggleSelected=(n,l)=>{let o=e.getIsSelected();t.setRowSelection(r=>{var i;if(n=void 0!==n?n:!o,e.getCanSelect()&&o===n)return r;let u={...r};return A(u,e.id,n,null==(i=null==l?void 0:l.selectChildren)||i,t),u})},e.getIsSelected=()=>{let{rowSelection:n}=t.getState();return G(e,n)},e.getIsSomeSelected=()=>{let{rowSelection:n}=t.getState();return"some"===O(e,n)},e.getIsAllSubRowsSelected=()=>{let{rowSelection:n}=t.getState();return"all"===O(e,n)},e.getCanSelect=()=>{var n;return"function"==typeof t.options.enableRowSelection?t.options.enableRowSelection(e):null==(n=t.options.enableRowSelection)||n},e.getCanSelectSubRows=()=>{var n;return"function"==typeof t.options.enableSubRowSelection?t.options.enableSubRowSelection(e):null==(n=t.options.enableSubRowSelection)||n},e.getCanMultiSelect=()=>{var n;return"function"==typeof t.options.enableMultiRowSelection?t.options.enableMultiRowSelection(e):null==(n=t.options.enableMultiRowSelection)||n},e.getToggleSelectedHandler=()=>{let t=e.getCanSelect();return n=>{var l;t&&e.toggleSelected(null==(l=n.target)?void 0:l.checked)}}}},{getDefaultColumnDef:()=>I,getInitialState:e=>({columnSizing:{},columnSizingInfo:x(),...e}),getDefaultOptions:e=>({columnResizeMode:"onEnd",columnResizeDirection:"ltr",onColumnSizingChange:o("columnSizing",e),onColumnSizingInfoChange:o("columnSizingInfo",e)}),createColumn:(e,t)=>{e.getSize=()=>{var n,l,o;let r=t.getState().columnSizing[e.id];return Math.min(Math.max(null!=(n=e.columnDef.minSize)?n:I.minSize,null!=(l=null!=r?r:e.columnDef.size)?l:I.size),null!=(o=e.columnDef.maxSize)?o:I.maxSize)},e.getStart=i(e=>[e,_(t,e),t.getState().columnSizing],(t,n)=>n.slice(0,e.getIndex(t)).reduce((e,t)=>e+t.getSize(),0),u(t.options,"debugColumns","getStart")),e.getAfter=i(e=>[e,_(t,e),t.getState().columnSizing],(t,n)=>n.slice(e.getIndex(t)+1).reduce((e,t)=>e+t.getSize(),0),u(t.options,"debugColumns","getAfter")),e.resetSize=()=>{t.setColumnSizing(t=>{let{[e.id]:n,...l}=t;return l})},e.getCanResize=()=>{var n,l;return(null==(n=e.columnDef.enableResizing)||n)&&(null==(l=t.options.enableColumnResizing)||l)},e.getIsResizing=()=>t.getState().columnSizingInfo.isResizingColumn===e.id},createHeader:(e,t)=>{e.getSize=()=>{let t=0,n=e=>{if(e.subHeaders.length)e.subHeaders.forEach(n);else{var l;t+=null!=(l=e.column.getSize())?l:0}};return n(e),t},e.getStart=()=>{if(e.index>0){let t=e.headerGroup.headers[e.index-1];return t.getStart()+t.getSize()}return 0},e.getResizeHandler=n=>{let l=t.getColumn(e.column.id),o=null==l?void 0:l.getCanResize();return r=>{if(!l||!o||(null==r.persist||r.persist(),P(r)&&r.touches&&r.touches.length>1))return;let i=e.getSize(),u=e?e.getLeafHeaders().map(e=>[e.column.id,e.column.getSize()]):[[l.id,l.getSize()]],a=P(r)?Math.round(r.touches[0].clientX):r.clientX,s={},g=(e,n)=>{"number"==typeof n&&(t.setColumnSizingInfo(e=>{var l,o;let r="rtl"===t.options.columnResizeDirection?-1:1,i=(n-(null!=(l=null==e?void 0:e.startOffset)?l:0))*r,u=Math.max(i/(null!=(o=null==e?void 0:e.startSize)?o:0),-.999999);return e.columnSizingStart.forEach(e=>{let[t,n]=e;s[t]=Math.round(100*Math.max(n+n*u,0))/100}),{...e,deltaOffset:i,deltaPercentage:u}}),("onChange"===t.options.columnResizeMode||"end"===e)&&t.setColumnSizing(e=>({...e,...s})))},d=e=>g("move",e),c=e=>{g("end",e),t.setColumnSizingInfo(e=>({...e,isResizingColumn:!1,startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,columnSizingStart:[]}))},f=n||("undefined"!=typeof document?document:null),p={moveHandler:e=>d(e.clientX),upHandler:e=>{null==f||f.removeEventListener("mousemove",p.moveHandler),null==f||f.removeEventListener("mouseup",p.upHandler),c(e.clientX)}},m={moveHandler:e=>(e.cancelable&&(e.preventDefault(),e.stopPropagation()),d(e.touches[0].clientX),!1),upHandler:e=>{var t;null==f||f.removeEventListener("touchmove",m.moveHandler),null==f||f.removeEventListener("touchend",m.upHandler),e.cancelable&&(e.preventDefault(),e.stopPropagation()),c(null==(t=e.touches[0])?void 0:t.clientX)}},v=!!function(){if("boolean"==typeof V)return V;let e=!1;try{let t=()=>{};window.addEventListener("test",t,{get passive(){return e=!0,!1}}),window.removeEventListener("test",t)}catch(t){e=!1}return V=e}()&&{passive:!1};P(r)?(null==f||f.addEventListener("touchmove",m.moveHandler,v),null==f||f.addEventListener("touchend",m.upHandler,v)):(null==f||f.addEventListener("mousemove",p.moveHandler,v),null==f||f.addEventListener("mouseup",p.upHandler,v)),t.setColumnSizingInfo(e=>({...e,startOffset:a,startSize:i,deltaOffset:0,deltaPercentage:0,columnSizingStart:u,isResizingColumn:l.id}))}}},createTable:e=>{e.setColumnSizing=t=>null==e.options.onColumnSizingChange?void 0:e.options.onColumnSizingChange(t),e.setColumnSizingInfo=t=>null==e.options.onColumnSizingInfoChange?void 0:e.options.onColumnSizingInfoChange(t),e.resetColumnSizing=t=>{var n;e.setColumnSizing(t?{}:null!=(n=e.initialState.columnSizing)?n:{})},e.resetHeaderSizeInfo=t=>{var n;e.setColumnSizingInfo(t?x():null!=(n=e.initialState.columnSizingInfo)?n:x())},e.getTotalSize=()=>{var t,n;return null!=(t=null==(n=e.getHeaderGroups()[0])?void 0:n.headers.reduce((e,t)=>e+t.getSize(),0))?t:0},e.getLeftTotalSize=()=>{var t,n;return null!=(t=null==(n=e.getLeftHeaderGroups()[0])?void 0:n.headers.reduce((e,t)=>e+t.getSize(),0))?t:0},e.getCenterTotalSize=()=>{var t,n;return null!=(t=null==(n=e.getCenterHeaderGroups()[0])?void 0:n.headers.reduce((e,t)=>e+t.getSize(),0))?t:0},e.getRightTotalSize=()=>{var t,n;return null!=(t=null==(n=e.getRightHeaderGroups()[0])?void 0:n.headers.reduce((e,t)=>e+t.getSize(),0))?t:0}}}];function B(e){var t,n;let o=[...j,...null!=(t=e._features)?t:[]],r={_features:o},a=r._features.reduce((e,t)=>Object.assign(e,null==t.getDefaultOptions?void 0:t.getDefaultOptions(r)),{}),s=e=>r.options.mergeOptions?r.options.mergeOptions(a,e):{...a,...e},g={...null!=(n=e.initialState)?n:{}};r._features.forEach(e=>{var t;g=null!=(t=null==e.getInitialState?void 0:e.getInitialState(g))?t:g});let d=[],c=!1,f={_features:o,options:{...a,...e},initialState:g,_queue:e=>{d.push(e),c||(c=!0,Promise.resolve().then(()=>{for(;d.length;)d.shift()();c=!1}).catch(e=>setTimeout(()=>{throw e})))},reset:()=>{r.setState(r.initialState)},setOptions:e=>{let t=l(e,r.options);r.options=s(t)},getState:()=>r.options.state,setState:e=>{null==r.options.onStateChange||r.options.onStateChange(e)},_getRowId:(e,t,n)=>{var l;return null!=(l=null==r.options.getRowId?void 0:r.options.getRowId(e,t,n))?l:`${n?[n.id,t].join("."):t}`},getCoreRowModel:()=>(r._getCoreRowModel||(r._getCoreRowModel=r.options.getCoreRowModel(r)),r._getCoreRowModel()),getRowModel:()=>r.getPaginationRowModel(),getRow:(e,t)=>{let n=(t?r.getPrePaginationRowModel():r.getRowModel()).rowsById[e];if(!n&&!(n=r.getCoreRowModel().rowsById[e]))throw Error();return n},_getDefaultColumnDef:i(()=>[r.options.defaultColumn],e=>{var t;return e=null!=(t=e)?t:{},{header:e=>{let t=e.header.column.columnDef;return t.accessorKey?t.accessorKey:t.accessorFn?t.id:null},cell:e=>{var t,n;return null!=(t=null==(n=e.renderValue())||null==n.toString?void 0:n.toString())?t:null},...r._features.reduce((e,t)=>Object.assign(e,null==t.getDefaultColumnDef?void 0:t.getDefaultColumnDef()),{}),...e}},u(e,"debugColumns","_getDefaultColumnDef")),_getColumnDefs:()=>r.options.columns,getAllColumns:i(()=>[r._getColumnDefs()],e=>{let t=function(e,n,l){return void 0===l&&(l=0),e.map(e=>{let o=function(e,t,n,l){var o,r;let a;let s={...e._getDefaultColumnDef(),...t},g=s.accessorKey,d=null!=(o=null!=(r=s.id)?r:g?"function"==typeof String.prototype.replaceAll?g.replaceAll(".","_"):g.replace(/\./g,"_"):void 0)?o:"string"==typeof s.header?s.header:void 0;if(s.accessorFn?a=s.accessorFn:g&&(a=g.includes(".")?e=>{let t=e;for(let e of g.split(".")){var n;t=null==(n=t)?void 0:n[e]}return t}:e=>e[s.accessorKey]),!d)throw Error();let c={id:`${String(d)}`,accessorFn:a,parent:l,depth:n,columnDef:s,columns:[],getFlatColumns:i(()=>[!0],()=>{var e;return[c,...null==(e=c.columns)?void 0:e.flatMap(e=>e.getFlatColumns())]},u(e.options,"debugColumns","column.getFlatColumns")),getLeafColumns:i(()=>[e._getOrderColumnsFn()],e=>{var t;return null!=(t=c.columns)&&t.length?e(c.columns.flatMap(e=>e.getLeafColumns())):[c]},u(e.options,"debugColumns","column.getLeafColumns"))};for(let t of e._features)null==t.createColumn||t.createColumn(c,e);return c}(r,e,l,n);return o.columns=e.columns?t(e.columns,o,l+1):[],o})};return t(e)},u(e,"debugColumns","getAllColumns")),getAllFlatColumns:i(()=>[r.getAllColumns()],e=>e.flatMap(e=>e.getFlatColumns()),u(e,"debugColumns","getAllFlatColumns")),_getAllFlatColumnsById:i(()=>[r.getAllFlatColumns()],e=>e.reduce((e,t)=>(e[t.id]=t,e),{}),u(e,"debugColumns","getAllFlatColumnsById")),getAllLeafColumns:i(()=>[r.getAllColumns(),r._getOrderColumnsFn()],(e,t)=>t(e.flatMap(e=>e.getLeafColumns())),u(e,"debugColumns","getAllLeafColumns")),getColumn:e=>r._getAllFlatColumnsById()[e]};Object.assign(r,f);for(let e=0;e<r._features.length;e++){let t=r._features[e];null==t||null==t.createTable||t.createTable(r)}return r}function q(){return e=>i(()=>[e.options.data],t=>{let n={rows:[],flatRows:[],rowsById:{}},l=function(t,o,r){void 0===o&&(o=0);let i=[];for(let a=0;a<t.length;a++){let s=d(e,e._getRowId(t[a],a,r),t[a],a,o,void 0,null==r?void 0:r.id);if(n.flatRows.push(s),n.rowsById[s.id]=s,i.push(s),e.options.getSubRows){var u;s.originalSubRows=e.options.getSubRows(t[a],a),null!=(u=s.originalSubRows)&&u.length&&(s.subRows=l(s.originalSubRows,o+1,s))}}return i};return n.rows=l(t),n},u(e.options,"debugTable","getRowModel",()=>e._autoResetPageIndex()))}function U(){return e=>i(()=>[e.getPreFilteredRowModel(),e.getState().columnFilters,e.getState().globalFilter],(t,n,l)=>{var o,r;let i,u;if(!t.rows.length||!(null!=n&&n.length)&&!l){for(let e=0;e<t.flatRows.length;e++)t.flatRows[e].columnFilters={},t.flatRows[e].columnFiltersMeta={};return t}let a=[],s=[];(null!=n?n:[]).forEach(t=>{var n;let l=e.getColumn(t.id);if(!l)return;let o=l.getFilterFn();o&&a.push({id:t.id,filterFn:o,resolvedValue:null!=(n=null==o.resolveFilterValue?void 0:o.resolveFilterValue(t.value))?n:t.value})});let g=(null!=n?n:[]).map(e=>e.id),c=e.getGlobalFilterFn(),f=e.getAllLeafColumns().filter(e=>e.getCanGlobalFilter());l&&c&&f.length&&(g.push("__global__"),f.forEach(e=>{var t;s.push({id:e.id,filterFn:c,resolvedValue:null!=(t=null==c.resolveFilterValue?void 0:c.resolveFilterValue(l))?t:l})}));for(let e=0;e<t.flatRows.length;e++){let n=t.flatRows[e];if(n.columnFilters={},a.length)for(let e=0;e<a.length;e++){let t=(i=a[e]).id;n.columnFilters[t]=i.filterFn(n,t,i.resolvedValue,e=>{n.columnFiltersMeta[t]=e})}if(s.length){for(let e=0;e<s.length;e++){let t=(u=s[e]).id;if(u.filterFn(n,t,u.resolvedValue,e=>{n.columnFiltersMeta[t]=e})){n.columnFilters.__global__=!0;break}}!0!==n.columnFilters.__global__&&(n.columnFilters.__global__=!1)}}return o=t.rows,r=e=>{for(let t=0;t<g.length;t++)if(!1===e.columnFilters[g[t]])return!1;return!0},e.options.filterFromLeafRows?function(e,t,n){var l;let o=[],r={},i=null!=(l=n.options.maxLeafRowFilterDepth)?l:100,u=function(e,l){void 0===l&&(l=0);let a=[];for(let g=0;g<e.length;g++){var s;let c=e[g],f=d(n,c.id,c.original,c.index,c.depth,void 0,c.parentId);if(f.columnFilters=c.columnFilters,null!=(s=c.subRows)&&s.length&&l<i){if(f.subRows=u(c.subRows,l+1),t(c=f)&&!f.subRows.length||t(c)||f.subRows.length){a.push(c),r[c.id]=c,o.push(c);continue}}else t(c=f)&&(a.push(c),r[c.id]=c,o.push(c))}return a};return{rows:u(e),flatRows:o,rowsById:r}}(o,r,e):function(e,t,n){var l;let o=[],r={},i=null!=(l=n.options.maxLeafRowFilterDepth)?l:100,u=function(e,l){void 0===l&&(l=0);let a=[];for(let g=0;g<e.length;g++){let c=e[g];if(t(c)){var s;if(null!=(s=c.subRows)&&s.length&&l<i){let e=d(n,c.id,c.original,c.index,c.depth,void 0,c.parentId);e.subRows=u(c.subRows,l+1),c=e}a.push(c),o.push(c),r[c.id]=c}}return a};return{rows:u(e),flatRows:o,rowsById:r}}(o,r,e)},u(e.options,"debugTable","getFilteredRowModel",()=>e._autoResetPageIndex()))}function Z(e){return e=>i(()=>[e.getState().pagination,e.getPrePaginationRowModel(),e.options.paginateExpandedRows?void 0:e.getState().expanded],(t,n)=>{let l;if(!n.rows.length)return n;let{pageSize:o,pageIndex:r}=t,{rows:i,flatRows:u,rowsById:a}=n,s=o*r;i=i.slice(s,s+o),(l=e.options.paginateExpandedRows?{rows:i,flatRows:u,rowsById:a}:function(e){let t=[],n=e=>{var l;t.push(e),null!=(l=e.subRows)&&l.length&&e.getIsExpanded()&&e.subRows.forEach(n)};return e.rows.forEach(n),{rows:t,flatRows:e.flatRows,rowsById:e.rowsById}}({rows:i,flatRows:u,rowsById:a})).flatRows=[];let g=e=>{l.flatRows.push(e),e.subRows.length&&e.subRows.forEach(g)};return l.rows.forEach(g),l},u(e.options,"debugTable","getPaginationRowModel"))}function W(){return e=>i(()=>[e.getState().sorting,e.getPreSortedRowModel()],(t,n)=>{if(!n.rows.length||!(null!=t&&t.length))return n;let l=e.getState().sorting,o=[],r=l.filter(t=>{var n;return null==(n=e.getColumn(t.id))?void 0:n.getCanSort()}),i={};r.forEach(t=>{let n=e.getColumn(t.id);n&&(i[t.id]={sortUndefined:n.columnDef.sortUndefined,invertSorting:n.columnDef.invertSorting,sortingFn:n.getSortingFn()})});let u=e=>{let t=e.map(e=>({...e}));return t.sort((e,t)=>{for(let l=0;l<r.length;l+=1){var n;let o=r[l],u=i[o.id],a=u.sortUndefined,s=null!=(n=null==o?void 0:o.desc)&&n,g=0;if(a){let n=e.getValue(o.id),l=t.getValue(o.id),r=void 0===n,i=void 0===l;if(r||i){if("first"===a)return r?-1:1;if("last"===a)return r?1:-1;g=r&&i?0:r?a:-a}}if(0===g&&(g=u.sortingFn(e,t,o.id)),0!==g)return s&&(g*=-1),u.invertSorting&&(g*=-1),g}return e.index-t.index}),t.forEach(e=>{var t;o.push(e),null!=(t=e.subRows)&&t.length&&(e.subRows=u(e.subRows))}),t};return{rows:u(n.rows),flatRows:o,rowsById:n.rowsById}},u(e.options,"debugTable","getSortedRowModel",()=>e._autoResetPageIndex()))}}}]);