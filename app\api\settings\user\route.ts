import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const userSettingsSchema = z.object({
  // Display Preferences
  theme: z.string().default('light'),
  language: z.string().default('en'),
  timezone: z.string().default('UTC'),
  dateFormat: z.string().default('MM/dd/yyyy'),
  timeFormat: z.string().default('12'),
  
  // Dashboard Settings
  dashboardLayout: z.any().optional(),
  defaultView: z.string().default('dashboard'),
  itemsPerPage: z.number().min(10).max(100).default(25),
  
  // Notification Preferences
  emailNotifications: z.boolean().default(true),
  pushNotifications: z.boolean().default(true),
  smsNotifications: z.boolean().default(false),
  notificationTypes: z.any().optional(),
  
  // Privacy Settings
  profileVisibility: z.string().default('company'),
  showEmail: z.boolean().default(false),
  showPhone: z.boolean().default(false),
  
  // Accessibility Settings
  fontSize: z.string().default('medium'),
  highContrast: z.boolean().default(false),
  reducedMotion: z.boolean().default(false),
  
  // Workflow Settings
  autoSave: z.boolean().default(true),
  confirmActions: z.boolean().default(true),
  shortcuts: z.any().optional(),
  
  // Custom Preferences
  customPreferences: z.any().optional()
})

// GET /api/settings/user - Get user settings
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const settings = await prisma.userSettings.findUnique({
      where: {
        userId: session.user.id
      },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            firstName: true,
            lastName: true,
            avatar: true,
            phone: true,
            title: true,
            department: true,
            bio: true,
            timezone: true,
            language: true
          }
        }
      }
    })

    // If no settings exist, create default settings
    if (!settings) {
      const defaultSettings = await prisma.userSettings.create({
        data: {
          userId: session.user.id,
          theme: 'light',
          language: 'en',
          timezone: 'UTC',
          dateFormat: 'MM/dd/yyyy',
          timeFormat: '12',
          defaultView: 'dashboard',
          itemsPerPage: 25,
          emailNotifications: true,
          pushNotifications: true,
          smsNotifications: false,
          profileVisibility: 'company',
          showEmail: false,
          showPhone: false,
          fontSize: 'medium',
          highContrast: false,
          reducedMotion: false,
          autoSave: true,
          confirmActions: true,
          dashboardLayout: {
            widgets: [
              { id: 'overview', position: { x: 0, y: 0, w: 12, h: 4 }, enabled: true },
              { id: 'recent-activities', position: { x: 0, y: 4, w: 6, h: 6 }, enabled: true },
              { id: 'quick-stats', position: { x: 6, y: 4, w: 6, h: 6 }, enabled: true },
              { id: 'tasks', position: { x: 0, y: 10, w: 12, h: 6 }, enabled: true }
            ]
          },
          notificationTypes: {
            newQuotation: true,
            quotationApproved: true,
            quotationRejected: true,
            invoiceCreated: true,
            invoicePaid: true,
            invoiceOverdue: true,
            contractSigned: true,
            contractExpiring: true,
            taskAssigned: true,
            taskDue: true,
            taskCompleted: true,
            teamUpdates: true,
            systemUpdates: false,
            marketingEmails: false
          },
          shortcuts: {
            newQuotation: 'ctrl+shift+q',
            newInvoice: 'ctrl+shift+i',
            newContract: 'ctrl+shift+c',
            newTask: 'ctrl+shift+t',
            search: 'ctrl+k',
            dashboard: 'ctrl+shift+d'
          }
        },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              firstName: true,
              lastName: true,
              avatar: true,
              phone: true,
              title: true,
              department: true,
              bio: true,
              timezone: true,
              language: true
            }
          }
        }
      })

      return NextResponse.json({
        settings: defaultSettings
      })
    }

    return NextResponse.json({
      settings
    })

  } catch (error) {
    console.error('Error fetching user settings:', error)
    return NextResponse.json(
      { error: 'Failed to fetch user settings' },
      { status: 500 }
    )
  }
}

// PUT /api/settings/user - Update user settings
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = userSettingsSchema.parse(body)

    const settings = await prisma.$transaction(async (tx) => {
      // Update or create user settings
      const updatedSettings = await tx.userSettings.upsert({
        where: {
          userId: session.user.id
        },
        update: {
          ...validatedData,
          updatedAt: new Date()
        },
        create: {
          ...validatedData,
          userId: session.user.id
        },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              firstName: true,
              lastName: true,
              avatar: true,
              phone: true,
              title: true,
              department: true,
              bio: true,
              timezone: true,
              language: true
            }
          }
        }
      })

      // Also update user profile timezone and language if provided
      if (validatedData.timezone || validatedData.language) {
        await tx.user.update({
          where: {
            id: session.user.id
          },
          data: {
            ...(validatedData.timezone && { timezone: validatedData.timezone }),
            ...(validatedData.language && { language: validatedData.language })
          }
        })
      }

      // Log activity
      if (session.user.companyId) {
        await tx.activity.create({
          data: {
            type: 'SETTINGS',
            title: 'User Settings Updated',
            description: 'User preferences were updated',
            companyId: session.user.companyId,
            createdById: session.user.id
          }
        })
      }

      return updatedSettings
    })

    return NextResponse.json({
      settings,
      message: 'User settings updated successfully'
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating user settings:', error)
    return NextResponse.json(
      { error: 'Failed to update user settings' },
      { status: 500 }
    )
  }
}

// PATCH /api/settings/user - Partially update user settings
export async function PATCH(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    
    // Get current settings
    const currentSettings = await prisma.userSettings.findUnique({
      where: {
        userId: session.user.id
      }
    })

    if (!currentSettings) {
      return NextResponse.json(
        { error: 'User settings not found' },
        { status: 404 }
      )
    }

    // Merge with current settings
    const mergedData = {
      ...currentSettings,
      ...body,
      updatedAt: new Date()
    }

    // Remove non-updatable fields
    delete mergedData.id
    delete mergedData.userId
    delete mergedData.createdAt

    const validatedData = userSettingsSchema.parse(mergedData)

    const settings = await prisma.userSettings.update({
      where: {
        userId: session.user.id
      },
      data: validatedData,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            firstName: true,
            lastName: true,
            avatar: true,
            phone: true,
            title: true,
            department: true,
            bio: true,
            timezone: true,
            language: true
          }
        }
      }
    })

    return NextResponse.json({
      settings,
      message: 'User settings updated successfully'
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating user settings:', error)
    return NextResponse.json(
      { error: 'Failed to update user settings' },
      { status: 500 }
    )
  }
}
