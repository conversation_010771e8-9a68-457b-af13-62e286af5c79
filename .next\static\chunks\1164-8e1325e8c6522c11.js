(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1164],{90998:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]])},72894:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},73067:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},92919:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]])},28203:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},13008:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6141:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},56224:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},71738:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},35817:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},76637:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]])},49617:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},9883:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},76020:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]])},45367:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},67972:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},82549:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},24033:function(e,t,n){e.exports=n(15313)},85744:function(e,t,n){"use strict";function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}n.d(t,{M:function(){return r}})},56989:function(e,t,n){"use strict";n.d(t,{b:function(){return u},k:function(){return i}});var r=n(2265),o=n(57437);function i(e,t){let n=r.createContext(t),i=e=>{let{children:t,...i}=e,u=r.useMemo(()=>i,Object.values(i));return(0,o.jsx)(n.Provider,{value:u,children:t})};return i.displayName=e+"Provider",[i,function(o){let i=r.useContext(n);if(i)return i;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function u(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return i.scopeName=e,[function(t,i){let u=r.createContext(i),l=n.length;n=[...n,i];let c=t=>{let{scope:n,children:i,...c}=t,a=n?.[e]?.[l]||u,s=r.useMemo(()=>c,Object.values(c));return(0,o.jsx)(a.Provider,{value:s,children:i})};return c.displayName=t+"Provider",[c,function(n,o){let c=o?.[e]?.[l]||u,a=r.useContext(c);if(a)return a;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(i,...t)]}},28712:function(e,t,n){"use strict";n.d(t,{Dx:function(){return er},VY:function(){return en},aV:function(){return et},dk:function(){return eo},fC:function(){return J},h_:function(){return ee},x8:function(){return ei},xz:function(){return Q}});var r=n(2265),o=n(85744),i=n(42210),u=n(56989),l=n(20966),c=n(73763),a=n(79249),s=n(52759),d=n(52730),f=n(85606),p=n(9381),y=n(31244),m=n(73386),h=n(85859),v=n(67256),g=n(57437),x="Dialog",[k,M]=(0,u.b)(x),[w,N]=k(x),b=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:i,onOpenChange:u,modal:a=!0}=e,s=r.useRef(null),d=r.useRef(null),[f,p]=(0,c.T)({prop:o,defaultProp:i??!1,onChange:u,caller:x});return(0,g.jsx)(w,{scope:t,triggerRef:s,contentRef:d,contentId:(0,l.M)(),titleId:(0,l.M)(),descriptionId:(0,l.M)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:a,children:n})};b.displayName=x;var Z="DialogTrigger",C=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,u=N(Z,n),l=(0,i.e)(t,u.triggerRef);return(0,g.jsx)(p.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":u.open,"aria-controls":u.contentId,"data-state":q(u.open),...r,ref:l,onClick:(0,o.M)(e.onClick,u.onOpenToggle)})});C.displayName=Z;var j="DialogPortal",[D,R]=k(j,{forceMount:void 0}),E=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:i}=e,u=N(j,t);return(0,g.jsx)(D,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,g.jsx)(f.z,{present:n||u.open,children:(0,g.jsx)(d.h,{asChild:!0,container:i,children:e})}))})};E.displayName=j;var O="DialogOverlay",I=r.forwardRef((e,t)=>{let n=R(O,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=N(O,e.__scopeDialog);return i.modal?(0,g.jsx)(f.z,{present:r||i.open,children:(0,g.jsx)(T,{...o,ref:t})}):null});I.displayName=O;var _=(0,v.Z8)("DialogOverlay.RemoveScroll"),T=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=N(O,n);return(0,g.jsx)(m.Z,{as:_,allowPinchZoom:!0,shards:[o.contentRef],children:(0,g.jsx)(p.WV.div,{"data-state":q(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),P="DialogContent",A=r.forwardRef((e,t)=>{let n=R(P,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=N(P,e.__scopeDialog);return(0,g.jsx)(f.z,{present:r||i.open,children:i.modal?(0,g.jsx)(S,{...o,ref:t}):(0,g.jsx)(F,{...o,ref:t})})});A.displayName=P;var S=r.forwardRef((e,t)=>{let n=N(P,e.__scopeDialog),u=r.useRef(null),l=(0,i.e)(t,n.contentRef,u);return r.useEffect(()=>{let e=u.current;if(e)return(0,h.Ry)(e)},[]),(0,g.jsx)($,{...e,ref:l,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{e.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:(0,o.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault())})}),F=r.forwardRef((e,t)=>{let n=N(P,e.__scopeDialog),o=r.useRef(!1),i=r.useRef(!1);return(0,g.jsx)($,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(o.current||n.triggerRef.current?.focus(),t.preventDefault()),o.current=!1,i.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(i.current=!0));let r=t.target;n.triggerRef.current?.contains(r)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),$=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:u,onCloseAutoFocus:l,...c}=e,d=N(P,n),f=r.useRef(null),p=(0,i.e)(t,f);return(0,y.EW)(),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(s.M,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:u,onUnmountAutoFocus:l,children:(0,g.jsx)(a.XB,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":q(d.open),...c,ref:p,onDismiss:()=>d.onOpenChange(!1)})}),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(K,{titleId:d.titleId}),(0,g.jsx)(G,{contentRef:f,descriptionId:d.descriptionId})]})]})}),V="DialogTitle",W=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=N(V,n);return(0,g.jsx)(p.WV.h2,{id:o.titleId,...r,ref:t})});W.displayName=V;var z="DialogDescription",U=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=N(z,n);return(0,g.jsx)(p.WV.p,{id:o.descriptionId,...r,ref:t})});U.displayName=z;var L="DialogClose",H=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=N(L,n);return(0,g.jsx)(p.WV.button,{type:"button",...r,ref:t,onClick:(0,o.M)(e.onClick,()=>i.onOpenChange(!1))})});function q(e){return e?"open":"closed"}H.displayName=L;var B="DialogTitleWarning",[X,Y]=(0,u.k)(B,{contentName:P,titleName:V,docsSlug:"dialog"}),K=({titleId:e})=>{let t=Y(B),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return r.useEffect(()=>{e&&!document.getElementById(e)&&console.error(n)},[n,e]),null},G=({contentRef:e,descriptionId:t})=>{let n=Y("DialogDescriptionWarning"),o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${n.contentName}}.`;return r.useEffect(()=>{let n=e.current?.getAttribute("aria-describedby");t&&n&&!document.getElementById(t)&&console.warn(o)},[o,e,t]),null},J=b,Q=C,ee=E,et=I,en=A,er=W,eo=U,ei=H},20966:function(e,t,n){"use strict";n.d(t,{M:function(){return c}});var r,o=n(2265),i=n(51030),u=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),l=0;function c(e){let[t,n]=o.useState(u());return(0,i.b)(()=>{e||n(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}},85606:function(e,t,n){"use strict";n.d(t,{z:function(){return u}});var r=n(2265),o=n(42210),i=n(51030),u=e=>{let t,n;let{present:u,children:c}=e,a=function(e){var t,n;let[o,u]=r.useState(),c=r.useRef(null),a=r.useRef(e),s=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>n[e][t]??e,t));return r.useEffect(()=>{let e=l(c.current);s.current="mounted"===d?e:"none"},[d]),(0,i.b)(()=>{let t=c.current,n=a.current;if(n!==e){let r=s.current,o=l(t);e?f("MOUNT"):"none"===o||t?.display==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),a.current=e}},[e,f]),(0,i.b)(()=>{if(o){let e;let t=o.ownerDocument.defaultView??window,n=n=>{let r=l(c.current).includes(n.animationName);if(n.target===o&&r&&(f("ANIMATION_END"),!a.current)){let n=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=n)})}},r=e=>{e.target===o&&(s.current=l(c.current))};return o.addEventListener("animationstart",r),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",r),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{c.current=e?getComputedStyle(e):null,u(e)},[])}}(u),s="function"==typeof c?c({present:a.isPresent}):r.Children.only(c),d=(0,o.e)(a.ref,(t=Object.getOwnPropertyDescriptor(s.props,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning?s.ref:(t=Object.getOwnPropertyDescriptor(s,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning?s.props.ref:s.props.ref||s.ref);return"function"==typeof c||a.isPresent?r.cloneElement(s,{ref:d}):null};function l(e){return e?.animationName||"none"}u.displayName="Presence"},9381:function(e,t,n){"use strict";n.d(t,{WV:function(){return l},jH:function(){return c}});var r=n(2265),o=n(54887),i=n(67256),u=n(57437),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,i.Z8)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e,l=o?n:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,u.jsx)(l,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function c(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},16459:function(e,t,n){"use strict";n.d(t,{W:function(){return o}});var r=n(2265);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},73763:function(e,t,n){"use strict";n.d(t,{T:function(){return l}});var r,o=n(2265),i=n(51030),u=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||i.b;function l({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,l,c]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),i=o.useRef(n),l=o.useRef(t);return u(()=>{l.current=t},[t]),o.useEffect(()=>{i.current!==n&&(l.current?.(n),i.current=n)},[n,i]),[n,r,l]}({defaultProp:t,onChange:n}),a=void 0!==e,s=a?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==a){let t=a?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=a},[a,r])}return[s,o.useCallback(t=>{if(a){let n="function"==typeof t?t(e):t;n!==e&&c.current?.(n)}else l(t)},[a,e,l,c])]}Symbol("RADIX:SYNC_STATE")},51030:function(e,t,n){"use strict";n.d(t,{b:function(){return o}});var r=n(2265),o=globalThis?.document?r.useLayoutEffect:()=>{}}}]);