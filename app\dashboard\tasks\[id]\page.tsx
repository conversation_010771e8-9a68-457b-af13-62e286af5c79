'use client'

import { useState, useEffect } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { TaskForm } from '@/components/tasks/task-form'
import {
  CheckSquare,
  Calendar,
  User,
  Clock,
  Tag,
  ArrowLeft,
  Edit,
  Trash2,
  ExternalLink,
  AlertCircle
} from 'lucide-react'
import { toast } from 'react-hot-toast'
import Link from 'next/link'

interface Task {
  id: string
  title: string
  description: string | null
  status: 'TODO' | 'IN_PROGRESS' | 'REVIEW' | 'DONE' | 'CANCELLED'
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT' | 'CRITICAL'
  dueDate: string | null
  startDate: string | null
  completedAt: string | null
  type: string
  category: string | null
  estimatedHours: number | null
  actualHours: number | null
  tags: string[]
  customFields: Record<string, any>
  assignedTo: {
    id: string
    name: string | null
    email: string
  } | null
  createdBy: {
    id: string
    name: string | null
    email: string
  }
  lead: {
    id: string
    firstName: string
    lastName: string
    companyName: string | null
    email: string
    phone: string | null
  } | null
  customer: {
    id: string
    name: string
    email: string
    company: string | null
    phone: string | null
  } | null
  quotation: {
    id: string
    title: string
    total: number
    status: string
  } | null
  invoice: {
    id: string
    invoiceNumber: string
    total: number
    status: string
  } | null
  contract: {
    id: string
    title: string
    status: string
    startDate: string | null
    endDate: string | null
  } | null
  createdAt: string
  updatedAt: string
}

export default function TaskDetailPage({ params }: { params: { id: string } }) {
  const [task, setTask] = useState<Task | null>(null)
  const [loading, setLoading] = useState(true)
  const [showEditForm, setShowEditForm] = useState(false)
  const router = useRouter()

  const fetchTask = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/tasks/${params.id}`)
      if (!response.ok) {
        if (response.status === 404) {
          toast.error('Task not found')
          router.push('/dashboard/tasks')
          return
        }
        throw new Error('Failed to fetch task')
      }

      const data = await response.json()
      setTask(data.task)
    } catch (error) {
      toast.error('Failed to load task')
      console.error('Error fetching task:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchTask()
  }, [params.id])

  const handleDeleteTask = async () => {
    if (!confirm('Are you sure you want to delete this task?')) return

    try {
      const response = await fetch(`/api/tasks/${params.id}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error('Failed to delete task')
      }

      toast.success('Task deleted successfully')
      router.push('/dashboard/tasks')
    } catch (error) {
      toast.error('Failed to delete task')
      console.error('Error deleting task:', error)
    }
  }

  const handleStatusChange = async (newStatus: string) => {
    try {
      const response = await fetch(`/api/tasks/${params.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      })

      if (!response.ok) {
        throw new Error('Failed to update task status')
      }

      toast.success('Task status updated')
      fetchTask()
    } catch (error) {
      toast.error('Failed to update task status')
      console.error('Error updating task status:', error)
    }
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      TODO: { label: 'To Do', className: 'bg-gray-100 text-gray-800' },
      IN_PROGRESS: { label: 'In Progress', className: 'bg-blue-100 text-blue-800' },
      REVIEW: { label: 'Review', className: 'bg-yellow-100 text-yellow-800' },
      DONE: { label: 'Done', className: 'bg-green-100 text-green-800' },
      CANCELLED: { label: 'Cancelled', className: 'bg-red-100 text-red-800' }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.TODO
    return <Badge className={config.className}>{config.label}</Badge>
  }

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      LOW: { label: 'Low', className: 'bg-gray-100 text-gray-600' },
      MEDIUM: { label: 'Medium', className: 'bg-blue-100 text-blue-600' },
      HIGH: { label: 'High', className: 'bg-orange-100 text-orange-600' },
      URGENT: { label: 'Urgent', className: 'bg-red-100 text-red-600' },
      CRITICAL: { label: 'Critical', className: 'bg-red-200 text-red-800' }
    }

    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.MEDIUM
    return <Badge className={config.className}>{config.label}</Badge>
  }

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    )
  }

  if (!task) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center py-8">
          <AlertCircle className="h-12 w-12 mx-auto mb-4 text-gray-300" />
          <p className="text-gray-500">Task not found</p>
          <Button asChild className="mt-4">
            <Link href="/dashboard/tasks">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Tasks
            </Link>
          </Button>
        </div>
      </div>
    )
  }

  const isOverdue = task.dueDate && new Date(task.dueDate) < new Date() && task.status !== 'DONE'

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" asChild>
            <Link href="/dashboard/tasks">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Tasks
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{task.title}</h1>
            <p className="text-gray-600">Task Details</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => setShowEditForm(true)}>
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
          <Button variant="outline" onClick={handleDeleteTask} className="text-red-600 hover:text-red-700">
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Task Information */}
          <Card>
            <CardHeader>
              <CardTitle>Task Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  {getStatusBadge(task.status)}
                  {getPriorityBadge(task.priority)}
                  <Badge variant="outline">{task.type}</Badge>
                  {task.category && <Badge variant="outline">{task.category}</Badge>}
                </div>
                {task.status !== 'DONE' && (
                  <Button onClick={() => handleStatusChange('DONE')} size="sm">
                    <CheckSquare className="h-4 w-4 mr-2" />
                    Mark Complete
                  </Button>
                )}
              </div>

              {task.description && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Description</h4>
                  <p className="text-gray-700 whitespace-pre-wrap">{task.description}</p>
                </div>
              )}

              {task.tags.length > 0 && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Tags</h4>
                  <div className="flex flex-wrap gap-2">
                    {task.tags.map((tag, index) => (
                      <Badge key={index} variant="secondary" className="flex items-center space-x-1">
                        <Tag className="h-3 w-3" />
                        <span>{tag}</span>
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Related Records */}
          {(task.lead || task.customer || task.quotation || task.invoice || task.contract) && (
            <Card>
              <CardHeader>
                <CardTitle>Related Records</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {task.lead && (
                  <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                    <div>
                      <p className="font-medium">Lead</p>
                      <p className="text-sm text-gray-600">
                        {task.lead.firstName} {task.lead.lastName}
                        {task.lead.companyName && ` (${task.lead.companyName})`}
                      </p>
                    </div>
                    <Button variant="outline" size="sm" asChild>
                      <Link href={`/dashboard/leads/${task.lead.id}`}>
                        <ExternalLink className="h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                )}

                {task.customer && (
                  <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                    <div>
                      <p className="font-medium">Customer</p>
                      <p className="text-sm text-gray-600">
                        {task.customer.name}
                        {task.customer.company && ` (${task.customer.company})`}
                      </p>
                    </div>
                    <Button variant="outline" size="sm" asChild>
                      <Link href={`/dashboard/customers/${task.customer.id}`}>
                        <ExternalLink className="h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                )}

                {task.quotation && (
                  <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                    <div>
                      <p className="font-medium">Quotation</p>
                      <p className="text-sm text-gray-600">
                        {task.quotation.title} - ${task.quotation.total.toLocaleString()}
                      </p>
                    </div>
                    <Button variant="outline" size="sm" asChild>
                      <Link href={`/dashboard/quotations/${task.quotation.id}`}>
                        <ExternalLink className="h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Task Details */}
          <Card>
            <CardHeader>
              <CardTitle>Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-3">
                <User className="h-4 w-4 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-500">Assigned To</p>
                  <p className="font-medium">
                    {task.assignedTo ? (task.assignedTo.name || task.assignedTo.email) : 'Unassigned'}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <User className="h-4 w-4 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-500">Created By</p>
                  <p className="font-medium">{task.createdBy.name || task.createdBy.email}</p>
                </div>
              </div>

              <Separator />

              {task.startDate && (
                <div className="flex items-center space-x-3">
                  <Calendar className="h-4 w-4 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-500">Start Date</p>
                    <p className="font-medium">{new Date(task.startDate).toLocaleDateString()}</p>
                  </div>
                </div>
              )}

              {task.dueDate && (
                <div className="flex items-center space-x-3">
                  <Calendar className={`h-4 w-4 ${isOverdue ? 'text-red-500' : 'text-gray-400'}`} />
                  <div>
                    <p className="text-sm text-gray-500">Due Date</p>
                    <p className={`font-medium ${isOverdue ? 'text-red-600' : ''}`}>
                      {new Date(task.dueDate).toLocaleDateString()}
                      {isOverdue && ' (Overdue)'}
                    </p>
                  </div>
                </div>
              )}

              {task.completedAt && (
                <div className="flex items-center space-x-3">
                  <CheckSquare className="h-4 w-4 text-green-500" />
                  <div>
                    <p className="text-sm text-gray-500">Completed</p>
                    <p className="font-medium text-green-600">
                      {new Date(task.completedAt).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              )}

              <Separator />

              {(task.estimatedHours || task.actualHours) && (
                <div className="space-y-2">
                  {task.estimatedHours && (
                    <div className="flex items-center space-x-3">
                      <Clock className="h-4 w-4 text-gray-400" />
                      <div>
                        <p className="text-sm text-gray-500">Estimated Hours</p>
                        <p className="font-medium">{task.estimatedHours}h</p>
                      </div>
                    </div>
                  )}

                  {task.actualHours && (
                    <div className="flex items-center space-x-3">
                      <Clock className="h-4 w-4 text-gray-400" />
                      <div>
                        <p className="text-sm text-gray-500">Actual Hours</p>
                        <p className="font-medium">{task.actualHours}h</p>
                      </div>
                    </div>
                  )}
                </div>
              )}

              <Separator />

              <div className="text-xs text-gray-500">
                <p>Created: {new Date(task.createdAt).toLocaleString()}</p>
                <p>Updated: {new Date(task.updatedAt).toLocaleString()}</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Edit Form Modal */}
      {showEditForm && (
        <TaskForm
          task={task}
          onClose={() => setShowEditForm(false)}
          onSuccess={() => {
            setShowEditForm(false)
            fetchTask()
          }}
        />
      )}
    </div>
  )
}
