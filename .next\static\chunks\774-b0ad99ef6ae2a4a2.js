"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[774],{6141:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},41298:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},35817:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},42482:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},81016:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Flame",[["path",{d:"M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z",key:"96xj49"}]])},1295:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},12741:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},92295:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},3928:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Snowflake",[["line",{x1:"2",x2:"22",y1:"12",y2:"12",key:"1dnqot"}],["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"m20 16-4-4 4-4",key:"rquw4f"}],["path",{d:"m4 8 4 4-4 4",key:"12s3z9"}],["path",{d:"m16 4-4 4-4-4",key:"1tumq1"}],["path",{d:"m8 20 4-4 4 4",key:"9p200w"}]])},55340:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},44135:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},89275:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Thermometer",[["path",{d:"M14 4v10.54a4 4 0 1 1-4 0V4a2 2 0 0 1 4 0Z",key:"17jzev"}]])},85790:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},52431:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("UserCheck",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]])},74527:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},66062:function(e,t,n){n.d(t,{fC:function(){return g},z$:function(){return w}});var r=n(2265),a=n(42210),i=n(56989),o=n(85744),l=n(73763),c=n(85184),d=n(94977),u=n(85606),s=n(9381),p=n(57437),y="Checkbox",[f,h]=(0,i.b)(y),[k,m]=f(y);function x(e){let{__scopeCheckbox:t,checked:n,children:a,defaultChecked:i,disabled:o,form:c,name:d,onCheckedChange:u,required:s,value:f="on",internal_do_not_use_render:h}=e,[m,x]=(0,l.T)({prop:n,defaultProp:i??!1,onChange:u,caller:y}),[v,b]=r.useState(null),[g,Z]=r.useState(null),w=r.useRef(!1),C=!v||!!c||!!v.closest("form"),j={checked:m,disabled:o,setChecked:x,control:v,setControl:b,name:d,form:c,value:f,hasConsumerStoppedPropagationRef:w,required:s,defaultChecked:!M(i)&&i,isFormControl:C,bubbleInput:g,setBubbleInput:Z};return(0,p.jsx)(k,{scope:t,...j,children:"function"==typeof h?h(j):a})}var v="CheckboxTrigger",b=r.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:n,...i},l)=>{let{control:c,value:d,disabled:u,checked:y,required:f,setControl:h,setChecked:k,hasConsumerStoppedPropagationRef:x,isFormControl:b,bubbleInput:g}=m(v,e),Z=(0,a.e)(l,h),w=r.useRef(y);return r.useEffect(()=>{let e=c?.form;if(e){let t=()=>k(w.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[c,k]),(0,p.jsx)(s.WV.button,{type:"button",role:"checkbox","aria-checked":M(y)?"mixed":y,"aria-required":f,"data-state":R(y),"data-disabled":u?"":void 0,disabled:u,value:d,...i,ref:Z,onKeyDown:(0,o.M)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,o.M)(n,e=>{k(e=>!!M(e)||!e),g&&b&&(x.current=e.isPropagationStopped(),x.current||e.stopPropagation())})})});b.displayName=v;var g=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,name:r,checked:a,defaultChecked:i,required:o,disabled:l,value:c,onCheckedChange:d,form:u,...s}=e;return(0,p.jsx)(x,{__scopeCheckbox:n,checked:a,defaultChecked:i,disabled:l,required:o,onCheckedChange:d,name:r,form:u,value:c,internal_do_not_use_render:({isFormControl:e})=>(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(b,{...s,ref:t,__scopeCheckbox:n}),e&&(0,p.jsx)(j,{__scopeCheckbox:n})]})})});g.displayName=y;var Z="CheckboxIndicator",w=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,forceMount:r,...a}=e,i=m(Z,n);return(0,p.jsx)(u.z,{present:r||M(i.checked)||!0===i.checked,children:(0,p.jsx)(s.WV.span,{"data-state":R(i.checked),"data-disabled":i.disabled?"":void 0,...a,ref:t,style:{pointerEvents:"none",...e.style}})})});w.displayName=Z;var C="CheckboxBubbleInput",j=r.forwardRef(({__scopeCheckbox:e,...t},n)=>{let{control:i,hasConsumerStoppedPropagationRef:o,checked:l,defaultChecked:u,required:y,disabled:f,name:h,value:k,form:x,bubbleInput:v,setBubbleInput:b}=m(C,e),g=(0,a.e)(n,b),Z=(0,c.D)(l),w=(0,d.t)(i);r.useEffect(()=>{if(!v)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!o.current;if(Z!==l&&e){let n=new Event("click",{bubbles:t});v.indeterminate=M(l),e.call(v,!M(l)&&l),v.dispatchEvent(n)}},[v,Z,l,o]);let j=r.useRef(!M(l)&&l);return(0,p.jsx)(s.WV.input,{type:"checkbox","aria-hidden":!0,defaultChecked:u??j.current,required:y,disabled:f,name:h,value:k,form:x,...t,tabIndex:-1,ref:g,style:{...t.style,...w,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function M(e){return"indeterminate"===e}function R(e){return M(e)?"indeterminate":e?"checked":"unchecked"}j.displayName=C},91927:function(e,t,n){n.d(t,{Fw:function(){return Z},fC:function(){return j},wy:function(){return b}});var r=n(2265),a=n(85744),i=n(56989),o=n(73763),l=n(51030),c=n(42210),d=n(9381),u=n(85606),s=n(20966),p=n(57437),y="Collapsible",[f,h]=(0,i.b)(y),[k,m]=f(y),x=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,open:a,defaultOpen:i,disabled:l,onOpenChange:c,...u}=e,[f,h]=(0,o.T)({prop:a,defaultProp:i??!1,onChange:c,caller:y});return(0,p.jsx)(k,{scope:n,disabled:l,contentId:(0,s.M)(),open:f,onOpenToggle:r.useCallback(()=>h(e=>!e),[h]),children:(0,p.jsx)(d.WV.div,{"data-state":C(f),"data-disabled":l?"":void 0,...u,ref:t})})});x.displayName=y;var v="CollapsibleTrigger",b=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,...r}=e,i=m(v,n);return(0,p.jsx)(d.WV.button,{type:"button","aria-controls":i.contentId,"aria-expanded":i.open||!1,"data-state":C(i.open),"data-disabled":i.disabled?"":void 0,disabled:i.disabled,...r,ref:t,onClick:(0,a.M)(e.onClick,i.onOpenToggle)})});b.displayName=v;var g="CollapsibleContent",Z=r.forwardRef((e,t)=>{let{forceMount:n,...r}=e,a=m(g,e.__scopeCollapsible);return(0,p.jsx)(u.z,{present:n||a.open,children:({present:e})=>(0,p.jsx)(w,{...r,ref:t,present:e})})});Z.displayName=g;var w=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,present:a,children:i,...o}=e,u=m(g,n),[s,y]=r.useState(a),f=r.useRef(null),h=(0,c.e)(t,f),k=r.useRef(0),x=k.current,v=r.useRef(0),b=v.current,Z=u.open||s,w=r.useRef(Z),j=r.useRef(void 0);return r.useEffect(()=>{let e=requestAnimationFrame(()=>w.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,l.b)(()=>{let e=f.current;if(e){j.current=j.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();k.current=t.height,v.current=t.width,w.current||(e.style.transitionDuration=j.current.transitionDuration,e.style.animationName=j.current.animationName),y(a)}},[u.open,a]),(0,p.jsx)(d.WV.div,{"data-state":C(u.open),"data-disabled":u.disabled?"":void 0,id:u.contentId,hidden:!Z,...o,ref:h,style:{"--radix-collapsible-content-height":x?`${x}px`:void 0,"--radix-collapsible-content-width":b?`${b}px`:void 0,...e.style},children:Z&&i})});function C(e){return e?"open":"closed"}var j=x}}]);