"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/invoices/route";
exports.ids = ["app/api/invoices/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "./action-async-storage.external?8652":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external?0211":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external?137c":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Finvoices%2Froute&page=%2Fapi%2Finvoices%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finvoices%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Finvoices%2Froute&page=%2Fapi%2Finvoices%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finvoices%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_proj_nextjs_saas_app_api_invoices_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/invoices/route.ts */ \"(rsc)/./app/api/invoices/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/invoices/route\",\n        pathname: \"/api/invoices\",\n        filename: \"route\",\n        bundlePath: \"app/api/invoices/route\"\n    },\n    resolvedPagePath: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\api\\\\invoices\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_proj_nextjs_saas_app_api_invoices_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/invoices/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Finvoices%2Froute&page=%2Fapi%2Finvoices%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finvoices%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/invoices/route.ts":
/*!***********************************!*\
  !*** ./app/api/invoices/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/ZodError.js\");\n\n\n\n\n\n// Validation schema for invoice items\nconst invoiceItemSchema = zod__WEBPACK_IMPORTED_MODULE_4__.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_4__.string().min(1, \"Name is required\"),\n    description: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional(),\n    quantity: zod__WEBPACK_IMPORTED_MODULE_4__.number().min(1, \"Quantity must be at least 1\"),\n    unitPrice: zod__WEBPACK_IMPORTED_MODULE_4__.number().min(0, \"Unit price must be positive\"),\n    itemId: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional()\n});\n// Validation schema for invoice creation/update\nconst invoiceSchema = zod__WEBPACK_IMPORTED_MODULE_4__.object({\n    invoiceNumber: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional(),\n    title: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional(),\n    customerId: zod__WEBPACK_IMPORTED_MODULE_4__.string().min(1, \"Customer is required\"),\n    quotationId: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    status: zod__WEBPACK_IMPORTED_MODULE_4__[\"enum\"]([\n        \"DRAFT\",\n        \"SENT\",\n        \"PAID\",\n        \"OVERDUE\",\n        \"CANCELLED\"\n    ]).default(\"DRAFT\"),\n    dueDate: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional(),\n    terms: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    notes: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    items: zod__WEBPACK_IMPORTED_MODULE_4__.array(invoiceItemSchema).min(1, \"At least one item is required\"),\n    taxRate: zod__WEBPACK_IMPORTED_MODULE_4__.number().min(0).max(100).optional().default(0)\n});\n// GET /api/invoices - List invoices with filtering and pagination\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get(\"page\") || \"1\");\n        const limit = parseInt(searchParams.get(\"limit\") || \"10\");\n        const search = searchParams.get(\"search\") || \"\";\n        const status = searchParams.get(\"status\") || \"\";\n        const customerId = searchParams.get(\"customerId\") || \"\";\n        const sortBy = searchParams.get(\"sortBy\") || \"createdAt\";\n        const sortOrder = searchParams.get(\"sortOrder\") || \"desc\";\n        const skip = (page - 1) * limit;\n        // Build where clause\n        const where = {\n            companyId: session.user.companyId || undefined\n        };\n        if (search) {\n            where.OR = [\n                {\n                    invoiceNumber: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    customer: {\n                        name: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    }\n                },\n                {\n                    customer: {\n                        companyName: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    }\n                }\n            ];\n        }\n        if (status) {\n            where.status = status;\n        }\n        if (customerId) {\n            where.customerId = customerId;\n        }\n        // Get invoices with pagination\n        const [invoices, total] = await Promise.all([\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.invoice.findMany({\n                where,\n                skip,\n                take: limit,\n                orderBy: {\n                    [sortBy]: sortOrder\n                },\n                include: {\n                    customer: {\n                        select: {\n                            id: true,\n                            name: true,\n                            email: true,\n                            companyName: true\n                        }\n                    },\n                    quotation: {\n                        select: {\n                            id: true,\n                            quotationNumber: true,\n                            title: true\n                        }\n                    },\n                    createdBy: {\n                        select: {\n                            name: true,\n                            email: true\n                        }\n                    },\n                    items: true,\n                    transactions: true,\n                    _count: {\n                        select: {\n                            transactions: true,\n                            activities: true\n                        }\n                    }\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.invoice.count({\n                where\n            })\n        ]);\n        // Return invoices with existing totals from database\n        const invoicesWithTotals = invoices.map((invoice)=>{\n            // Convert all potential BigInt fields to strings/numbers\n            const cleanInvoice = JSON.parse(JSON.stringify(invoice, (key, value)=>typeof value === \"bigint\" ? value.toString() : value));\n            return {\n                ...cleanInvoice,\n                subtotal: Number(cleanInvoice.subtotal),\n                total: Number(cleanInvoice.total),\n                taxAmount: Number(cleanInvoice.taxAmount),\n                paidAmount: Number(cleanInvoice.paidAmount),\n                balanceAmount: Number(cleanInvoice.balanceAmount)\n            };\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            invoices: invoicesWithTotals,\n            pagination: {\n                page,\n                limit,\n                total,\n                pages: Math.ceil(total / limit)\n            }\n        });\n    } catch (error) {\n        console.error(\"Error fetching invoices:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to fetch invoices\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/invoices - Create new invoice\nasync function POST(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const validatedData = invoiceSchema.parse(body);\n        // Generate invoice number if not provided\n        let invoiceNumber = validatedData.invoiceNumber;\n        if (!invoiceNumber) {\n            const currentYear = new Date().getFullYear();\n            const lastInvoice = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.invoice.findFirst({\n                where: {\n                    companyId: session.user.companyId || undefined,\n                    invoiceNumber: {\n                        startsWith: `INV-${currentYear}-`\n                    }\n                },\n                orderBy: {\n                    invoiceNumber: \"desc\"\n                }\n            });\n            let nextNumber = 1;\n            if (lastInvoice) {\n                const lastNumber = parseInt(lastInvoice.invoiceNumber.split(\"-\")[2]);\n                nextNumber = lastNumber + 1;\n            }\n            invoiceNumber = `INV-${currentYear}-${nextNumber.toString().padStart(4, \"0\")}`;\n        }\n        // Calculate totals\n        const subtotal = validatedData.items.reduce((sum, item)=>{\n            return sum + item.quantity * item.unitPrice;\n        }, 0);\n        const taxAmount = subtotal * (validatedData.taxRate || 0) / 100;\n        const total = subtotal + taxAmount;\n        // Prepare invoice data\n        const invoiceData = {\n            invoiceNumber,\n            title: validatedData.title || `Invoice ${invoiceNumber}`,\n            customerId: validatedData.customerId,\n            quotationId: validatedData.quotationId,\n            status: validatedData.status,\n            subtotal,\n            taxRate: validatedData.taxRate || 0,\n            taxAmount,\n            total,\n            terms: validatedData.terms,\n            notes: validatedData.notes,\n            companyId: session.user.companyId,\n            createdById: session.user.id\n        };\n        if (validatedData.dueDate) {\n            invoiceData.dueDate = new Date(validatedData.dueDate);\n        } else {\n            // Default to 30 days from now\n            const dueDate = new Date();\n            dueDate.setDate(dueDate.getDate() + 30);\n            invoiceData.dueDate = dueDate;\n        }\n        // Create invoice with items in a transaction\n        const invoice = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.$transaction(async (tx)=>{\n            const newInvoice = await tx.invoice.create({\n                data: {\n                    ...invoiceData,\n                    items: {\n                        create: validatedData.items.map((item)=>({\n                                name: item.name,\n                                description: item.description,\n                                quantity: item.quantity,\n                                unitPrice: item.unitPrice,\n                                total: item.quantity * item.unitPrice,\n                                itemId: item.itemId\n                            }))\n                    }\n                },\n                include: {\n                    customer: {\n                        select: {\n                            id: true,\n                            name: true,\n                            email: true,\n                            companyName: true\n                        }\n                    },\n                    quotation: {\n                        select: {\n                            id: true,\n                            quotationNumber: true,\n                            title: true\n                        }\n                    },\n                    createdBy: {\n                        select: {\n                            name: true,\n                            email: true\n                        }\n                    },\n                    items: true,\n                    _count: {\n                        select: {\n                            transactions: true,\n                            activities: true\n                        }\n                    }\n                }\n            });\n            // If created from quotation, update quotation status\n            if (newInvoice.quotationId) {\n                await tx.quotation.update({\n                    where: {\n                        id: newInvoice.quotationId\n                    },\n                    data: {\n                        status: \"ACCEPTED\"\n                    }\n                });\n            }\n            return newInvoice;\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(invoice, {\n            status: 201\n        });\n    } catch (error) {\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_5__.ZodError) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Validation failed\",\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        console.error(\"Error creating invoice:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to create invoice\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/invoices/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/github */ \"(rsc)/./node_modules/next-auth/providers/github.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n\nconst authOptions = {\n    // Using JWT strategy instead of database adapter for now\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                try {\n                    if (!credentials?.email || !credentials?.password) {\n                        console.log(\"Missing credentials\");\n                        return null;\n                    }\n                    console.log(\"Attempting to authenticate user:\", credentials.email);\n                    const user = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.findUnique({\n                        where: {\n                            email: credentials.email\n                        },\n                        select: {\n                            id: true,\n                            email: true,\n                            name: true,\n                            password: true,\n                            role: true,\n                            companyId: true\n                        }\n                    });\n                    // Get the company ID - either as member or owner\n                    let finalCompanyId = user?.companyId;\n                    if (!finalCompanyId && user) {\n                        const ownedCompany = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.company.findFirst({\n                            where: {\n                                ownerId: user.id\n                            },\n                            select: {\n                                id: true\n                            }\n                        });\n                        finalCompanyId = ownedCompany?.id;\n                        // If user is company owner, update their companyId for future queries\n                        if (finalCompanyId) {\n                            await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                                where: {\n                                    id: user.id\n                                },\n                                data: {\n                                    companyId: finalCompanyId\n                                }\n                            });\n                        }\n                    }\n                    if (!user) {\n                        console.log(\"User not found:\", credentials.email);\n                        return null;\n                    }\n                    if (!user.password) {\n                        console.log(\"User has no password set:\", credentials.email);\n                        return null;\n                    }\n                    const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_3___default().compare(credentials.password, user.password);\n                    if (!isPasswordValid) {\n                        console.log(\"Invalid password for user:\", credentials.email);\n                        return null;\n                    }\n                    // Update last login\n                    await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                        where: {\n                            id: user.id\n                        },\n                        data: {\n                            lastLoginAt: new Date(),\n                            loginCount: {\n                                increment: 1\n                            }\n                        }\n                    });\n                    console.log(\"User authenticated successfully:\", user.email);\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name,\n                        role: user.role,\n                        companyId: finalCompanyId\n                    };\n                } catch (error) {\n                    console.error(\"Authentication error:\", error);\n                    return null;\n                }\n            }\n        }),\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            clientId: process.env.GITHUB_CLIENT_ID,\n            clientSecret: process.env.GITHUB_CLIENT_SECRET\n        })\n    ],\n    session: {\n        strategy: \"jwt\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                console.log(\"JWT callback - user data:\", {\n                    id: user.id,\n                    email: user.email,\n                    role: user.role,\n                    companyId: user.companyId\n                });\n                token.role = user.role;\n                token.companyId = user.companyId;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n                session.user.companyId = token.companyId;\n                console.log(\"Session callback - final session:\", {\n                    id: session.user.id,\n                    email: session.user.email,\n                    role: session.user.role,\n                    companyId: session.user.companyId\n                });\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        signUp: \"/auth/signup\",\n        error: \"/auth/error\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zYWFzLWFwcC8uL2xpYi9wcmlzbWEudHM/OTgyMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/zod","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/lru-cache","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Finvoices%2Froute&page=%2Fapi%2Finvoices%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finvoices%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();