"use strict";(()=>{var e={};e.id=1243,e.ids=[1243],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},96890:(e,t,i)=>{i.r(t),i.d(t,{headerHooks:()=>x,originalPathname:()=>f,patchFetch:()=>_,requestAsyncStorage:()=>w,routeModule:()=>v,serverHooks:()=>h,staticGenerationAsyncStorage:()=>q,staticGenerationBailout:()=>b});var a={};i.r(a),i.d(a,{GET:()=>I,POST:()=>g});var r=i(95419),n=i(69108),o=i(99678),s=i(78070),u=i(81355),l=i(3205),c=i(9108),d=i(25252),m=i(52178);let p=d.Ry({name:d.Z_().min(1,"Name is required"),description:d.Z_().optional(),quantity:d.Rx().min(1,"Quantity must be at least 1"),unitPrice:d.Rx().min(0,"Unit price must be positive"),itemId:d.Z_().optional()}),y=d.Ry({invoiceNumber:d.Z_().optional(),title:d.Z_().optional(),customerId:d.Z_().min(1,"Customer is required"),quotationId:d.Z_().optional().nullable(),status:d.Km(["DRAFT","SENT","PAID","OVERDUE","CANCELLED"]).default("DRAFT"),dueDate:d.Z_().optional(),terms:d.Z_().optional().nullable(),notes:d.Z_().optional().nullable(),items:d.IX(p).min(1,"At least one item is required"),taxRate:d.Rx().min(0).max(100).optional().default(0)});async function I(e){try{let t=await (0,u.getServerSession)(l.L);if(!t?.user?.id)return s.Z.json({error:"Unauthorized"},{status:401});let{searchParams:i}=new URL(e.url),a=parseInt(i.get("page")||"1"),r=parseInt(i.get("limit")||"10"),n=i.get("search")||"",o=i.get("status")||"",d=i.get("customerId")||"",m=i.get("sortBy")||"createdAt",p=i.get("sortOrder")||"desc",y=(a-1)*r,I={companyId:t.user.companyId||void 0};n&&(I.OR=[{invoiceNumber:{contains:n,mode:"insensitive"}},{customer:{name:{contains:n,mode:"insensitive"}}},{customer:{companyName:{contains:n,mode:"insensitive"}}}]),o&&(I.status=o),d&&(I.customerId=d);let[g,v]=await Promise.all([c._.invoice.findMany({where:I,skip:y,take:r,orderBy:{[m]:p},include:{customer:{select:{id:!0,name:!0,email:!0,companyName:!0}},quotation:{select:{id:!0,quotationNumber:!0,title:!0}},createdBy:{select:{name:!0,email:!0}},items:!0,transactions:!0,_count:{select:{transactions:!0,activities:!0}}}}),c._.invoice.count({where:I})]),w=g.map(e=>{let t=JSON.parse(JSON.stringify(e,(e,t)=>"bigint"==typeof t?t.toString():t));return{...t,subtotal:Number(t.subtotal),total:Number(t.total),taxAmount:Number(t.taxAmount),paidAmount:Number(t.paidAmount),balanceAmount:Number(t.balanceAmount)}});return s.Z.json({invoices:w,pagination:{page:a,limit:r,total:v,pages:Math.ceil(v/r)}})}catch(e){return console.error("Error fetching invoices:",e),s.Z.json({error:"Failed to fetch invoices"},{status:500})}}async function g(e){try{let t=await (0,u.getServerSession)(l.L);if(!t?.user?.id)return s.Z.json({error:"Unauthorized"},{status:401});let i=await e.json(),a=y.parse(i),r=a.invoiceNumber;if(!r){let e=new Date().getFullYear(),i=await c._.invoice.findFirst({where:{companyId:t.user.companyId||void 0,invoiceNumber:{startsWith:`INV-${e}-`}},orderBy:{invoiceNumber:"desc"}}),a=1;i&&(a=parseInt(i.invoiceNumber.split("-")[2])+1),r=`INV-${e}-${a.toString().padStart(4,"0")}`}let n=a.items.reduce((e,t)=>e+t.quantity*t.unitPrice,0),o=n*(a.taxRate||0)/100,d={invoiceNumber:r,title:a.title||`Invoice ${r}`,customerId:a.customerId,quotationId:a.quotationId,status:a.status,subtotal:n,taxRate:a.taxRate||0,taxAmount:o,total:n+o,terms:a.terms,notes:a.notes,companyId:t.user.companyId,createdById:t.user.id};if(a.dueDate)d.dueDate=new Date(a.dueDate);else{let e=new Date;e.setDate(e.getDate()+30),d.dueDate=e}let m=await c._.$transaction(async e=>{let t=await e.invoice.create({data:{...d,items:{create:a.items.map(e=>({name:e.name,description:e.description,quantity:e.quantity,unitPrice:e.unitPrice,total:e.quantity*e.unitPrice,itemId:e.itemId}))}},include:{customer:{select:{id:!0,name:!0,email:!0,companyName:!0}},quotation:{select:{id:!0,quotationNumber:!0,title:!0}},createdBy:{select:{name:!0,email:!0}},items:!0,_count:{select:{transactions:!0,activities:!0}}}});return t.quotationId&&await e.quotation.update({where:{id:t.quotationId},data:{status:"ACCEPTED"}}),t});return s.Z.json(m,{status:201})}catch(e){if(e instanceof m.jm)return s.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("Error creating invoice:",e),s.Z.json({error:"Failed to create invoice"},{status:500})}}let v=new r.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/invoices/route",pathname:"/api/invoices",filename:"route",bundlePath:"app/api/invoices/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\invoices\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:w,staticGenerationAsyncStorage:q,serverHooks:h,headerHooks:x,staticGenerationBailout:b}=v,f="/api/invoices/route";function _(){return(0,o.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:q})}},3205:(e,t,i)=>{i.d(t,{L:()=>l});var a=i(86485),r=i(10375),n=i(50694),o=i(6521),s=i.n(o),u=i(9108);let l={providers:[(0,a.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await u._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),i=t?.companyId;if(!i&&t){let e=await u._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(i=e?.id)&&await u._.user.update({where:{id:t.id},data:{companyId:i}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await s().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await u._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:i}}catch(e){return console.error("Authentication error:",e),null}}}),(0,r.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,n.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,i)=>{i.d(t,{_:()=>r});let a=require("@prisma/client"),r=globalThis.prisma??new a.PrismaClient}};var t=require("../../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),a=t.X(0,[1638,6206,6521,2455,4520,5252],()=>i(96890));module.exports=a})();