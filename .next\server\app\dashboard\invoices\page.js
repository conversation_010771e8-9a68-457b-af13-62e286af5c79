(()=>{var e={};e.id=4490,e.ids=[4490],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},35969:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>d});var a=t(50482),l=t(69108),r=t(62563),n=t.n(r),i=t(68300),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);t.d(s,c);let d=["",{children:["dashboard",{children:["invoices",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,92697)),"C:\\proj\\nextjs-saas\\app\\dashboard\\invoices\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,96339)),"C:\\proj\\nextjs-saas\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,59504)),"C:\\proj\\nextjs-saas\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\proj\\nextjs-saas\\app\\dashboard\\invoices\\page.tsx"],x="/dashboard/invoices/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:l.x.APP_PAGE,page:"/dashboard/invoices/page",pathname:"/dashboard/invoices",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},70731:(e,s,t)=>{Promise.resolve().then(t.bind(t,68012))},68012:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>U});var a=t(95344),l=t(3729),r=t(47674),n=t(61351),i=t(16212),c=t(69436),d=t(10763),o=t(14411),x=t(17470),m=t(50340),h=t(33733),u=t(37121),j=t(48411),p=t(7060),v=t(45961),N=t(17910),f=t(25545),y=t(46064),g=t(85674),b=t(89895),w=t(55794),D=t(44669);function C(){let[e,s]=(0,l.useState)(null),[t,r]=(0,l.useState)(!0),[d,o]=(0,l.useState)("30"),C=async()=>{try{r(!0);let e=await fetch(`/api/invoices/analytics?period=${d}`);if(!e.ok)throw Error("Failed to fetch analytics");let t=await e.json();s(t)}catch(e){D.toast.error("Failed to load invoice analytics"),console.error("Error fetching analytics:",e)}finally{r(!1)}};(0,l.useEffect)(()=>{C()},[d]);let Z=e=>{switch(e){case"DRAFT":default:return"bg-gray-100 text-gray-800";case"SENT":return"bg-blue-100 text-blue-800";case"PAID":return"bg-green-100 text-green-800";case"OVERDUE":return"bg-red-100 text-red-800";case"CANCELLED":return"bg-orange-100 text-orange-800"}},P=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e),S=e=>{switch(e){case"0-30 days":return"text-green-600";case"31-60 days":return"text-yellow-600";case"61-90 days":return"text-orange-600";case"90+ days":return"text-red-600";default:return"text-gray-600"}};return t?a.jsx("div",{className:"flex items-center justify-center py-8",children:a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):e?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("h3",{className:"text-lg font-semibold",children:"Invoice Analytics"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(x.Ph,{value:d,onValueChange:o,children:[a.jsx(x.i4,{className:"w-32",children:a.jsx(x.ki,{})}),(0,a.jsxs)(x.Bw,{children:[a.jsx(x.Ql,{value:"7",children:"Last 7 days"}),a.jsx(x.Ql,{value:"30",children:"Last 30 days"}),a.jsx(x.Ql,{value:"90",children:"Last 90 days"}),a.jsx(x.Ql,{value:"365",children:"Last year"})]})]}),(0,a.jsxs)(i.z,{variant:"outline",onClick:C,size:"sm",children:[a.jsx(h.Z,{className:"h-4 w-4 mr-2"}),"Refresh"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-blue-100 rounded-full",children:a.jsx(u.Z,{className:"h-6 w-6 text-blue-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Total Invoices"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.totalInvoices})]})]})})}),a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-green-100 rounded-full",children:a.jsx(j.Z,{className:"h-6 w-6 text-green-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Total Invoiced"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:P(e.summary.totalInvoicedAmount)})]})]})})}),a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-purple-100 rounded-full",children:a.jsx(p.Z,{className:"h-6 w-6 text-purple-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Total Paid"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:P(e.summary.totalPaidAmount)})]})]})})}),a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-red-100 rounded-full",children:a.jsx(v.Z,{className:"h-6 w-6 text-red-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Outstanding"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:P(e.summary.outstandingAmount)})]})]})})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-indigo-100 rounded-full",children:a.jsx(N.Z,{className:"h-6 w-6 text-indigo-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Collection Rate"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[e.summary.collectionRate.toFixed(1),"%"]})]})]})})}),a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-pink-100 rounded-full",children:a.jsx(f.Z,{className:"h-6 w-6 text-pink-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Avg Days to Pay"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[e.summary.avgDaysToPayment.toFixed(1),"d"]})]})]})})}),a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-orange-100 rounded-full",children:a.jsx(y.Z,{className:"h-6 w-6 text-orange-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Avg Invoice Value"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:P(e.summary.averageValue)})]})]})})}),a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-teal-100 rounded-full",children:a.jsx(g.Z,{className:"h-6 w-6 text-teal-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Paid Invoices"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.paidInvoicesCount})]})]})})})]}),(0,a.jsxs)(n.Zb,{children:[a.jsx(n.Ol,{children:(0,a.jsxs)(n.ll,{className:"flex items-center",children:[a.jsx(j.Z,{className:"h-5 w-5 mr-2"}),"Revenue Overview"]})}),a.jsx(n.aY,{children:(0,a.jsxs)("div",{className:"grid grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("p",{className:"text-2xl font-bold text-green-600",children:P(e.revenueMetrics.totalRevenue)}),a.jsx("p",{className:"text-sm text-gray-500",children:"Total Revenue"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("p",{className:"text-2xl font-bold text-blue-600",children:P(e.revenueMetrics.pendingRevenue)}),a.jsx("p",{className:"text-sm text-gray-500",children:"Pending Revenue"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("p",{className:"text-2xl font-bold text-purple-600",children:P(e.revenueMetrics.thisMonthRevenue)}),a.jsx("p",{className:"text-sm text-gray-500",children:"This Month"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("p",{className:"text-2xl font-bold text-orange-600",children:P(e.revenueMetrics.projectedRevenue)}),a.jsx("p",{className:"text-sm text-gray-500",children:"Projected Total"})]})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)(n.Zb,{children:[a.jsx(n.Ol,{children:a.jsx(n.ll,{children:"Invoices by Status"})}),a.jsx(n.aY,{children:a.jsx("div",{className:"space-y-3",children:e.invoicesByStatus.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("div",{className:"flex items-center space-x-2",children:a.jsx(c.C,{className:Z(e.status),children:e.status})}),(0,a.jsxs)("div",{className:"text-right",children:[a.jsx("span",{className:"font-semibold",children:e.count}),a.jsx("p",{className:"text-sm text-gray-500",children:P(e.value)})]})]},e.status))})})]}),(0,a.jsxs)(n.Zb,{children:[a.jsx(n.Ol,{children:(0,a.jsxs)(n.ll,{className:"flex items-center",children:[a.jsx(f.Z,{className:"h-5 w-5 mr-2"}),"Aging Report"]})}),a.jsx(n.aY,{children:a.jsx("div",{className:"space-y-3",children:e.agingReport.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("div",{className:"flex items-center space-x-2",children:a.jsx("span",{className:`font-medium ${S(e.period)}`,children:e.period})}),(0,a.jsxs)("div",{className:"text-right",children:[a.jsx("span",{className:"font-semibold",children:e.count}),a.jsx("p",{className:`text-sm ${S(e.period)}`,children:P(e.amount)})]})]},e.period))})})]})]}),(0,a.jsxs)(n.Zb,{children:[a.jsx(n.Ol,{children:(0,a.jsxs)(n.ll,{className:"flex items-center",children:[a.jsx(b.Z,{className:"h-5 w-5 mr-2"}),"Top Customers by Invoice Value"]})}),a.jsx(n.aY,{children:a.jsx("div",{className:"space-y-3",children:e.customerInvoices.slice(0,5).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center",children:(0,a.jsxs)("span",{className:"text-xs font-semibold text-blue-600",children:["#",s+1]})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium",children:e.customer.name}),a.jsx("p",{className:"text-sm text-gray-500",children:e.customer.company})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[a.jsx("p",{className:"font-semibold text-green-600",children:P(e.totalValue)}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[e.invoiceCount," invoices"]}),e.outstandingAmount>0&&(0,a.jsxs)("p",{className:"text-sm text-red-500",children:[P(e.outstandingAmount)," outstanding"]})]})]},e.customer.id))})})]}),e.overdueInvoices.length>0&&(0,a.jsxs)(n.Zb,{children:[a.jsx(n.Ol,{children:(0,a.jsxs)(n.ll,{className:"flex items-center",children:[a.jsx(v.Z,{className:"h-5 w-5 mr-2 text-red-600"}),"Overdue Invoices"]})}),a.jsx(n.aY,{children:a.jsx("div",{className:"space-y-3",children:e.overdueInvoices.slice(0,5).map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium",children:e.invoiceNumber}),a.jsx("p",{className:"text-sm text-gray-600",children:e.customer.name}),(0,a.jsxs)("p",{className:"text-sm text-red-600",children:[e.daysOverdue," days overdue"]})]}),(0,a.jsxs)("div",{className:"text-right",children:[a.jsx("p",{className:"font-semibold text-red-600",children:P(e.total-e.paidAmount)}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Due: ",new Date(e.dueDate).toLocaleDateString()]})]})]},e.id))})})]}),(0,a.jsxs)(n.Zb,{children:[a.jsx(n.Ol,{children:(0,a.jsxs)(n.ll,{className:"flex items-center",children:[a.jsx(w.Z,{className:"h-5 w-5 mr-2"}),"Recent Invoices (Last 7 Days)"]})}),a.jsx(n.aY,{children:a.jsx("div",{className:"space-y-3",children:0===e.recentInvoices.length?a.jsx("p",{className:"text-gray-500 text-center py-4",children:"No recent invoices"}):e.recentInvoices.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium",children:e.invoiceNumber}),a.jsx("p",{className:"text-sm text-gray-600",children:e.title||"No title"}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[e.customer.name," • ",new Date(e.createdAt).toLocaleDateString()]})]}),(0,a.jsxs)("div",{className:"text-right",children:[a.jsx("p",{className:"font-semibold text-green-600",children:P(e.total)}),a.jsx(c.C,{className:Z(e.status),variant:"outline",children:e.status})]})]},e.id))})})]})]}):(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[a.jsx(m.Z,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),a.jsx("p",{children:"Failed to load analytics data"})]})}var Z=t(92549),P=t(1586),S=t(93601),E=t(16802),A=t(57341),k=t(18822),I=t(38271);function R({open:e,invoice:s,onClose:t,onSuccess:r}){let[n,d]=(0,l.useState)([]),[o,m]=(0,l.useState)(!1),[h,p]=(0,l.useState)(!1),[v,N]=(0,l.useState)({amount:"",paymentDate:new Date().toISOString().split("T")[0],paymentMethod:"CASH",reference:"",notes:""}),f=async()=>{if(s)try{m(!0);let e=await fetch(`/api/invoices/${s.id}/payments`);if(!e.ok)throw Error("Failed to fetch payments");let t=await e.json();d(t.payments)}catch(e){D.toast.error("Failed to load payments"),console.error("Error fetching payments:",e)}finally{m(!1)}};(0,l.useEffect)(()=>{if(e&&s){f();let e=s.total-s.paidAmount;N(s=>({...s,amount:e>0?e.toString():""}))}},[e,s]);let y=(e,s)=>{N(t=>({...t,[e]:s}))},b=async e=>{if(e.preventDefault(),!s)return;let t=parseFloat(v.amount);if(isNaN(t)||t<=0){D.toast.error("Please enter a valid payment amount");return}let a=s.total-s.paidAmount;if(t>a){D.toast.error(`Payment amount cannot exceed remaining balance of $${a.toFixed(2)}`);return}p(!0);try{let e=await fetch(`/api/invoices/${s.id}/payments`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({amount:t,paymentDate:v.paymentDate,paymentMethod:v.paymentMethod,reference:v.reference||void 0,notes:v.notes||void 0})});if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to record payment")}D.toast.success("Payment recorded successfully!"),N({amount:"",paymentDate:new Date().toISOString().split("T")[0],paymentMethod:"CASH",reference:"",notes:""}),await f(),r()}catch(e){D.toast.error(e instanceof Error?e.message:"Failed to record payment")}finally{p(!1)}},C=async e=>{if(s&&confirm("Are you sure you want to delete this payment?"))try{let t=await fetch(`/api/invoices/${s.id}/payments?paymentId=${e}`,{method:"DELETE"});if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to delete payment")}D.toast.success("Payment deleted successfully!"),await f(),r()}catch(e){D.toast.error(e instanceof Error?e.message:"Failed to delete payment")}},R=()=>{N({amount:"",paymentDate:new Date().toISOString().split("T")[0],paymentMethod:"CASH",reference:"",notes:""}),d([]),t()};if(!s)return null;let F=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e),O=s.total-s.paidAmount;return a.jsx(E.Vq,{open:e,onOpenChange:R,children:(0,a.jsxs)(E.cZ,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)(E.fK,{children:[(0,a.jsxs)(E.$N,{className:"flex items-center",children:[a.jsx(g.Z,{className:"h-5 w-5 mr-2"}),"Payment Management"]}),a.jsx(E.Be,{children:"Record and manage payments for this invoice. View payment history and track remaining balance."})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(u.Z,{className:"h-5 w-5 text-blue-600"}),a.jsx("span",{className:"font-semibold",children:s.invoiceNumber}),a.jsx(c.C,{className:(e=>{switch(e){case"DRAFT":default:return"bg-gray-100 text-gray-800";case"SENT":return"bg-blue-100 text-blue-800";case"PAID":return"bg-green-100 text-green-800";case"OVERDUE":return"bg-red-100 text-red-800";case"CANCELLED":return"bg-orange-100 text-orange-800"}})(s.status),children:s.status})]}),a.jsx("span",{className:"text-lg font-bold text-green-600",children:F(s.total)})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[a.jsx("p",{className:"font-medium",children:s.title||"No title"}),(0,a.jsxs)("div",{className:"flex items-center mt-1",children:[a.jsx(k.Z,{className:"h-4 w-4 mr-1"}),a.jsx("span",{children:s.customer.name}),s.customer.company&&(0,a.jsxs)("span",{className:"ml-1",children:["(",s.customer.company,")"]})]})]}),(0,a.jsxs)("div",{className:"mt-3 grid grid-cols-3 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[a.jsx("span",{className:"text-gray-500",children:"Total:"}),a.jsx("div",{className:"font-semibold",children:F(s.total)})]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"text-gray-500",children:"Paid:"}),a.jsx("div",{className:"font-semibold text-green-600",children:F(s.paidAmount)})]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"text-gray-500",children:"Balance:"}),a.jsx("div",{className:`font-semibold ${O>0?"text-red-600":"text-green-600"}`,children:F(O)})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("h3",{className:"text-lg font-semibold",children:"Record New Payment"}),(0,a.jsxs)("form",{onSubmit:b,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx(P._,{htmlFor:"amount",children:"Payment Amount *"}),a.jsx(Z.I,{id:"amount",type:"number",step:"0.01",min:"0.01",max:O,value:v.amount,onChange:e=>y("amount",e.target.value),placeholder:"0.00",required:!0})]}),(0,a.jsxs)("div",{children:[a.jsx(P._,{htmlFor:"paymentDate",children:"Payment Date *"}),a.jsx(Z.I,{id:"paymentDate",type:"date",value:v.paymentDate,onChange:e=>y("paymentDate",e.target.value),required:!0})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx(P._,{htmlFor:"paymentMethod",children:"Payment Method *"}),(0,a.jsxs)(x.Ph,{value:v.paymentMethod,onValueChange:e=>y("paymentMethod",e),children:[a.jsx(x.i4,{children:a.jsx(x.ki,{})}),(0,a.jsxs)(x.Bw,{children:[a.jsx(x.Ql,{value:"CASH",children:"Cash"}),a.jsx(x.Ql,{value:"CHECK",children:"Check"}),a.jsx(x.Ql,{value:"CREDIT_CARD",children:"Credit Card"}),a.jsx(x.Ql,{value:"BANK_TRANSFER",children:"Bank Transfer"}),a.jsx(x.Ql,{value:"PAYPAL",children:"PayPal"}),a.jsx(x.Ql,{value:"OTHER",children:"Other"})]})]})]}),(0,a.jsxs)("div",{children:[a.jsx(P._,{htmlFor:"reference",children:"Reference (optional)"}),a.jsx(Z.I,{id:"reference",value:v.reference,onChange:e=>y("reference",e.target.value),placeholder:"Check #, Transaction ID, etc."})]})]}),(0,a.jsxs)("div",{children:[a.jsx(P._,{htmlFor:"notes",children:"Notes (optional)"}),a.jsx(S.g,{id:"notes",value:v.notes,onChange:e=>y("notes",e.target.value),placeholder:"Additional notes about this payment...",rows:3})]}),a.jsx(i.z,{type:"submit",disabled:h||O<=0,children:h?(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Recording..."]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(j.Z,{className:"h-4 w-4 mr-2"}),"Record Payment"]})})]})]}),a.jsx(A.Z,{}),(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("h3",{className:"text-lg font-semibold",children:"Payment History"}),o?a.jsx("div",{className:"flex items-center justify-center py-4",children:a.jsx("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"})}):0===n.length?a.jsx("p",{className:"text-gray-500 text-center py-4",children:"No payments recorded"}):a.jsx("div",{className:"space-y-3",children:n.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("span",{className:"font-semibold",children:F(e.amount)}),a.jsx(c.C,{variant:"outline",children:e.paymentMethod})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[a.jsx(w.Z,{className:"h-3 w-3"}),a.jsx("span",{children:new Date(e.paymentDate).toLocaleDateString()})]}),e.reference&&(0,a.jsxs)("div",{children:["Reference: ",e.reference]}),e.notes&&(0,a.jsxs)("div",{children:["Notes: ",e.notes]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["Recorded by ",e.createdBy.name||e.createdBy.email," on"," ",new Date(e.createdAt).toLocaleDateString()]})]})]}),a.jsx(i.z,{variant:"outline",size:"sm",onClick:()=>C(e.id),className:"text-red-600 hover:text-red-700",children:a.jsx(I.Z,{className:"h-4 w-4"})})]},e.id))})]}),a.jsx("div",{className:"flex justify-end pt-4 border-t",children:a.jsx(i.z,{variant:"outline",onClick:R,children:"Close"})})]})]})})}var F=t(62093),O=t(53148),T=t(75695),L=t(1960),_=t(36135),V=t(96885),$=t(51838),M=t(20886),Y=t(20783),q=t.n(Y);function U(){let{data:e}=(0,r.useSession)(),[s,t]=(0,l.useState)([]),[x,h]=(0,l.useState)(!0),[N,y]=(0,l.useState)(!1),[b,Z]=(0,l.useState)(null),[P,S]=(0,l.useState)(!1),[E,A]=(0,l.useState)(!1),[Y,U]=(0,l.useState)(null),[z,B]=(0,l.useState)({total:0,draft:0,sent:0,paid:0,overdue:0,totalValue:0,totalPaid:0}),K=(0,l.useCallback)(async()=>{try{let e=await fetch("/api/invoices");if(!e.ok)throw Error("Failed to fetch invoices");let s=await e.json();t(s.invoices);let a=s.invoices.length,l=s.invoices.filter(e=>"DRAFT"===e.status).length,r=s.invoices.filter(e=>"SENT"===e.status).length,n=s.invoices.filter(e=>"PAID"===e.status).length,i=s.invoices.filter(e=>"OVERDUE"===e.status).length,c=s.invoices.reduce((e,s)=>e+(s.total||0),0),d=s.invoices.filter(e=>"PAID"===e.status).reduce((e,s)=>e+(s.total||0),0);B({total:a,draft:l,sent:r,paid:n,overdue:i,totalValue:c,totalPaid:d})}catch(e){D.toast.error("Failed to load invoices"),console.error("Error fetching invoices:",e)}finally{h(!1)}},[]);(0,l.useEffect)(()=>{K()},[K]);let Q=(0,l.useCallback)(async e=>{if(confirm(`Are you sure you want to delete invoice "${e.invoiceNumber}"?`))try{let s=await fetch(`/api/invoices/${e.id}`,{method:"DELETE"});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to delete invoice")}D.toast.success("Invoice deleted successfully"),K()}catch(e){D.toast.error(e instanceof Error?e.message:"Failed to delete invoice")}},[K]),H=(0,l.useCallback)(e=>{Z(e),y(!0)},[]),X=(0,l.useCallback)(async e=>{try{let s=await fetch(`/api/invoices/${e}/pdf`);if(!s.ok)throw Error("Failed to generate PDF");let t=await s.blob(),a=window.URL.createObjectURL(t),l=document.createElement("a");l.href=a,l.download=`invoice-${e}.html`,document.body.appendChild(l),l.click(),window.URL.revokeObjectURL(a),document.body.removeChild(l),D.toast.success("PDF downloaded successfully")}catch(e){D.toast.error("Failed to download PDF"),console.error("Error downloading PDF:",e)}},[]),G=(0,l.useCallback)(e=>{U(e),A(!0)},[]),W=(0,l.useCallback)(e=>()=>H(e),[H]),J=(0,l.useCallback)(e=>()=>Q(e),[Q]),ee=(0,l.useCallback)(e=>()=>X(e),[X]),es=(0,l.useCallback)(e=>()=>G(e),[G]),et=e=>{if(!e)return a.jsx(c.C,{variant:"secondary",children:"Unknown"});switch(e){case"DRAFT":return a.jsx(c.C,{variant:"secondary",children:"Draft"});case"SENT":return a.jsx(c.C,{variant:"info",children:"Sent"});case"VIEWED":return a.jsx(c.C,{variant:"warning",children:"Viewed"});case"PAID":return a.jsx(c.C,{variant:"success",children:"Paid"});case"OVERDUE":return a.jsx(c.C,{variant:"destructive",children:"Overdue"});case"CANCELLED":return a.jsx(c.C,{variant:"secondary",children:"Cancelled"});default:return a.jsx(c.C,{variant:"secondary",children:e})}},ea=[{accessorKey:"invoiceNumber",header:"Invoice",cell:({row:e})=>{let s=e.original;return(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:a.jsx(u.Z,{className:"h-4 w-4 text-green-600"})}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium",children:s.invoiceNumber}),a.jsx("div",{className:"text-sm text-gray-500",children:s.quotation?`From ${s.quotation.quotationNumber}`:"Direct Invoice"})]})]})}},{accessorKey:"customer",header:"Customer",cell:({row:e})=>{let s=e.original.customer;return(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium",children:s.name}),s.companyName&&a.jsx("div",{className:"text-sm text-gray-500",children:s.companyName})]})}},{accessorKey:"status",header:"Status",cell:({row:e})=>et(e.getValue("status"))},{accessorKey:"total",header:"Amount",cell:({row:e})=>{let s=e.getValue("total");return(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(j.Z,{className:"h-3 w-3 text-green-600"}),(0,a.jsxs)("span",{className:"font-medium",children:["$",s.toLocaleString()]})]})}},{accessorKey:"issueDate",header:"Issue Date",cell:({row:e})=>{let s=e.getValue("issueDate");return(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(w.Z,{className:"h-3 w-3 text-gray-400"}),a.jsx("span",{className:"text-sm",children:new Date(s).toLocaleDateString()})]})}},{accessorKey:"dueDate",header:"Due Date",cell:({row:e})=>{let s=new Date(e.getValue("dueDate")),t=s<new Date&&"PAID"!==e.original.status;return(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(w.Z,{className:`h-3 w-3 ${t?"text-red-400":"text-gray-400"}`}),a.jsx("span",{className:`text-sm ${t?"text-red-600":""}`,children:s.toLocaleDateString()})]})}},{accessorKey:"createdBy",header:"Created By",cell:({row:e})=>{let s=e.original.createdBy;return(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(k.Z,{className:"h-3 w-3 text-gray-400"}),a.jsx("span",{className:"text-sm",children:s.name||"Unknown"})]})}},{id:"actions",cell:({row:e})=>{let s=e.original,t=W(s),l=J(s),r=ee(s.id),n=es(s);return(0,a.jsxs)(M.h_,{children:[a.jsx(M.$F,{asChild:!0,children:a.jsx(i.z,{variant:"ghost",className:"h-8 w-8 p-0",children:a.jsx(F.Z,{className:"h-4 w-4"})})}),(0,a.jsxs)(M.AW,{align:"end",children:[a.jsx(M.Ju,{children:"Actions"}),a.jsx(M.Xi,{asChild:!0,children:(0,a.jsxs)(q(),{href:`/dashboard/invoices/${s.id}`,children:[a.jsx(O.Z,{className:"mr-2 h-4 w-4"}),"View Details"]})}),(0,a.jsxs)(M.Xi,{onClick:t,children:[a.jsx(T.Z,{className:"mr-2 h-4 w-4"}),"Edit"]}),(0,a.jsxs)(M.Xi,{children:[a.jsx(L.Z,{className:"mr-2 h-4 w-4"}),"Duplicate"]}),a.jsx(M.VD,{}),(0,a.jsxs)(M.Xi,{children:[a.jsx(_.Z,{className:"mr-2 h-4 w-4"}),"Send to Customer"]}),(0,a.jsxs)(M.Xi,{onClick:r,children:[a.jsx(V.Z,{className:"mr-2 h-4 w-4"}),"Download PDF"]}),(0,a.jsxs)(M.Xi,{onClick:n,children:[a.jsx(g.Z,{className:"mr-2 h-4 w-4"}),"Record Payment"]}),a.jsx(M.VD,{}),(0,a.jsxs)(M.Xi,{onClick:l,className:"text-red-600",disabled:"PAID"===s.status||(s._count?.transactions??0)>0,children:[a.jsx(I.Z,{className:"mr-2 h-4 w-4"}),"Delete"]})]})]})}}];return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Invoices"}),a.jsx("p",{className:"text-gray-600 mt-1",children:"Create and manage your invoices"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(i.z,{variant:"outline",onClick:()=>S(!P),children:[a.jsx(m.Z,{className:"h-4 w-4 mr-2"}),"Analytics"]}),(0,a.jsxs)(i.z,{onClick:()=>y(!0),children:[a.jsx($.Z,{className:"h-4 w-4 mr-2"}),"New Invoice"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4",children:[(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(n.ll,{className:"text-sm font-medium",children:"Total Invoices"}),a.jsx(u.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(n.aY,{children:[a.jsx("div",{className:"text-2xl font-bold",children:z.total}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"All invoices"})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(n.ll,{className:"text-sm font-medium",children:"Draft"}),a.jsx(f.Z,{className:"h-4 w-4 text-gray-600"})]}),(0,a.jsxs)(n.aY,{children:[a.jsx("div",{className:"text-2xl font-bold",children:z.draft}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"Draft invoices"})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(n.ll,{className:"text-sm font-medium",children:"Sent"}),a.jsx(_.Z,{className:"h-4 w-4 text-blue-600"})]}),(0,a.jsxs)(n.aY,{children:[a.jsx("div",{className:"text-2xl font-bold",children:z.sent}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"Sent to customers"})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(n.ll,{className:"text-sm font-medium",children:"Paid"}),a.jsx(p.Z,{className:"h-4 w-4 text-green-600"})]}),(0,a.jsxs)(n.aY,{children:[a.jsx("div",{className:"text-2xl font-bold",children:z.paid}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"Paid invoices"})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(n.ll,{className:"text-sm font-medium",children:"Overdue"}),a.jsx(v.Z,{className:"h-4 w-4 text-red-600"})]}),(0,a.jsxs)(n.aY,{children:[a.jsx("div",{className:"text-2xl font-bold",children:z.overdue}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"Overdue invoices"})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(n.ll,{className:"text-sm font-medium",children:"Total Value"}),a.jsx(j.Z,{className:"h-4 w-4 text-green-600"})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:["$",z.totalValue.toLocaleString()]}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"Total invoice value"})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(n.ll,{className:"text-sm font-medium",children:"Total Paid"}),a.jsx(g.Z,{className:"h-4 w-4 text-green-600"})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:["$",z.totalPaid.toLocaleString()]}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"Total payments received"})]})]})]}),P&&a.jsx(C,{}),(0,a.jsxs)(n.Zb,{children:[a.jsx(n.Ol,{children:a.jsx(n.ll,{children:"Invoice Management"})}),a.jsx(n.aY,{children:x?a.jsx("div",{className:"flex items-center justify-center py-8",children:a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):a.jsx(d.w,{columns:ea,data:s,searchPlaceholder:"Search invoices..."})})]}),a.jsx(o.q,{isOpen:N,onClose:()=>{y(!1),Z(null)},onSuccess:K,invoice:b,mode:b?"edit":"create"}),a.jsx(R,{open:E,invoice:Y,onClose:()=>{A(!1),U(null)},onSuccess:()=>{A(!1),U(null),K()}})]})}},57341:(e,s,t)=>{"use strict";t.d(s,{Z:()=>o});var a=t(95344),l=t(3729),r=t(62409),n="horizontal",i=["horizontal","vertical"],c=l.forwardRef((e,s)=>{let{decorative:t,orientation:l=n,...c}=e,d=i.includes(l)?l:n;return(0,a.jsx)(r.WV.div,{"data-orientation":d,...t?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...c,ref:s})});c.displayName="Separator";var d=t(91626);let o=l.forwardRef(({className:e,orientation:s="horizontal",decorative:t=!0,...l},r)=>a.jsx(c,{ref:r,decorative:t,orientation:s,className:(0,d.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",e),...l}));o.displayName=c.displayName},93601:(e,s,t)=>{"use strict";t.d(s,{g:()=>n});var a=t(95344),l=t(3729),r=t(91626);let n=l.forwardRef(({className:e,...s},t)=>a.jsx("textarea",{className:(0,r.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:t,...s}));n.displayName="Textarea"},48411:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},46064:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},92697:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>r,__esModule:()=>l,default:()=>n});let a=(0,t(86843).createProxy)(String.raw`C:\proj\nextjs-saas\app\dashboard\invoices\page.tsx`),{__esModule:l,$$typeof:r}=a,n=a.default}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[1638,7948,6671,4626,7792,2506,8830,7150,3117,2125,5045,5232,4957],()=>t(35969));module.exports=a})();