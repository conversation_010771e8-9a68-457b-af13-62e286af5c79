'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Package,
  DollarSign,
  TrendingUp,
  AlertTriangle,
  BarChart3,
  RefreshCw,
  ShoppingCart,
  Warehouse,
  Target,
  TrendingDown,
  Activity,
  PieChart
} from 'lucide-react'
import { toast } from 'react-hot-toast'

interface ItemAnalytics {
  summary: {
    totalItems: number
    activeItems: number
    inactiveItems: number
    trackedItems: number
    lowStockCount: number
    totalStockQuantity: number
    avgStockPerItem: number
    avgUnitPrice: number
    minPrice: number
    maxPrice: number
    itemsWithCostPrice: number
    totalStockValue: number
    totalCostValue: number
    usedItemsCount: number
    totalUsageCount: number
    totalUsageValue: number
  }
  itemsByCategory: Array<{
    category: string
    count: number
    avgPrice: number
    totalStock: number
  }>
  itemsByStatus: Array<{
    status: string
    count: number
    avgPrice: number
  }>
  topSellingItems: Array<{
    id: string
    name: string
    category: string | null
    unitPrice: number
    stockQuantity: number | null
    usageCount: number
    totalQuantitySold: number
    totalRevenue: number
  }>
  lowStockItems: Array<{
    id: string
    name: string
    sku: string | null
    category: string | null
    stockQuantity: number | null
    lowStockAlert: number | null
    unitPrice: number
    stockValue: number
  }>
  recentItems: Array<{
    id: string
    name: string
    sku: string | null
    category: string | null
    unitPrice: number
    stockQuantity: number | null
    trackInventory: boolean
    active: boolean
    createdAt: string
  }>
  categoryPerformance: Array<{
    category: string
    itemCount: number
    avgPrice: number
    totalStock: number
    trackedItems: number
    activeItems: number
    usageCount: number
  }>
  profitabilityAnalysis: Array<{
    id: string
    name: string
    category: string | null
    unitPrice: number
    costPrice: number
    profitMarginPercent: number | null
    profitPerUnit: number | null
    usageCount: number
  }>
  period: number
}

export function ItemAnalytics() {
  const [analytics, setAnalytics] = useState<ItemAnalytics | null>(null)
  const [loading, setLoading] = useState(true)
  const [period, setPeriod] = useState('30')

  const fetchAnalytics = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/items/analytics?period=${period}`)
      if (!response.ok) {
        throw new Error('Failed to fetch analytics')
      }

      const data = await response.json()
      setAnalytics(data)
    } catch (error) {
      toast.error('Failed to load item analytics')
      console.error('Error fetching analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchAnalytics()
  }, [period])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!analytics) {
    return (
      <div className="text-center py-8 text-gray-500">
        <BarChart3 className="h-12 w-12 mx-auto mb-4 text-gray-300" />
        <p>Failed to load analytics data</p>
      </div>
    )
  }

  const stockUtilization = analytics.summary.totalStockQuantity > 0 
    ? ((analytics.summary.usedItemsCount / analytics.summary.totalItems) * 100)
    : 0

  const profitMargin = analytics.summary.totalStockValue > 0 && analytics.summary.totalCostValue > 0
    ? (((analytics.summary.totalStockValue - analytics.summary.totalCostValue) / analytics.summary.totalStockValue) * 100)
    : 0

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Item Analytics</h3>
        <div className="flex items-center space-x-2">
          <Select value={period} onValueChange={setPeriod}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">Last 7 days</SelectItem>
              <SelectItem value="30">Last 30 days</SelectItem>
              <SelectItem value="90">Last 90 days</SelectItem>
              <SelectItem value="365">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={fetchAnalytics} size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-blue-100 rounded-full">
                <Package className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Total Items</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.summary.totalItems}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-green-100 rounded-full">
                <Activity className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Active Items</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.summary.activeItems}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-purple-100 rounded-full">
                <Warehouse className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Tracked Items</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.summary.trackedItems}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-orange-100 rounded-full">
                <AlertTriangle className="h-6 w-6 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Low Stock</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.summary.lowStockCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Financial Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-emerald-100 rounded-full">
                <DollarSign className="h-6 w-6 text-emerald-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Stock Value</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(analytics.summary.totalStockValue)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-indigo-100 rounded-full">
                <TrendingUp className="h-6 w-6 text-indigo-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Avg Unit Price</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(analytics.summary.avgUnitPrice)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-pink-100 rounded-full">
                <Target className="h-6 w-6 text-pink-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Usage Value</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(analytics.summary.totalUsageValue)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-teal-100 rounded-full">
                <PieChart className="h-6 w-6 text-teal-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Profit Margin</p>
                <p className="text-2xl font-bold text-gray-900">
                  {profitMargin.toFixed(1)}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Inventory Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Warehouse className="h-5 w-5 mr-2" />
            Inventory Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600">
                {formatNumber(analytics.summary.totalStockQuantity)}
              </p>
              <p className="text-sm text-gray-500">Total Stock Quantity</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">
                {analytics.summary.avgStockPerItem}
              </p>
              <p className="text-sm text-gray-500">Avg Stock per Item</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-purple-600">
                {stockUtilization.toFixed(1)}%
              </p>
              <p className="text-sm text-gray-500">Stock Utilization</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-orange-600">
                {analytics.summary.itemsWithCostPrice}
              </p>
              <p className="text-sm text-gray-500">Items with Cost Price</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Charts and Breakdowns */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Items by Category */}
        <Card>
          <CardHeader>
            <CardTitle>Items by Category</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.itemsByCategory.map((item) => (
                <div key={item.category} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline">
                      {item.category}
                    </Badge>
                  </div>
                  <div className="text-right">
                    <span className="font-semibold">{item.count}</span>
                    <p className="text-sm text-gray-500">
                      Avg: {formatCurrency(item.avgPrice)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Items by Status */}
        <Card>
          <CardHeader>
            <CardTitle>Items by Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.itemsByStatus.map((item) => (
                <div key={item.status} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Badge className={item.status === 'Active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                      {item.status}
                    </Badge>
                  </div>
                  <div className="text-right">
                    <span className="font-semibold">{item.count}</span>
                    <p className="text-sm text-gray-500">
                      Avg: {formatCurrency(item.avgPrice)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Selling Items */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <ShoppingCart className="h-5 w-5 mr-2" />
            Top Selling Items
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {analytics.topSellingItems.slice(0, 5).map((item, index) => (
              <div key={item.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-xs font-semibold text-blue-600">#{index + 1}</span>
                  </div>
                  <div>
                    <p className="font-medium">{item.name}</p>
                    <p className="text-sm text-gray-500">{item.category || 'Uncategorized'}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-green-600">
                    {formatCurrency(item.totalRevenue)}
                  </p>
                  <p className="text-sm text-gray-500">
                    {item.usageCount} uses • {formatNumber(item.totalQuantitySold)} sold
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Low Stock Alert */}
      {analytics.lowStockItems.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2 text-orange-600" />
              Low Stock Alert
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.lowStockItems.slice(0, 5).map((item) => (
                <div key={item.id} className="flex items-center justify-between p-3 bg-orange-50 rounded-lg border border-orange-200">
                  <div>
                    <p className="font-medium">{item.name}</p>
                    <p className="text-sm text-gray-600">{item.sku || 'No SKU'} • {item.category || 'Uncategorized'}</p>
                    <div className="flex items-center space-x-2 mt-1">
                      <p className="text-sm text-orange-600">
                        Stock: {item.stockQuantity} / Alert: {item.lowStockAlert}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-green-600">
                      {formatCurrency(item.stockValue)}
                    </p>
                    <p className="text-sm text-gray-500">
                      {formatCurrency(item.unitPrice)} each
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Category Performance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <BarChart3 className="h-5 w-5 mr-2" />
            Category Performance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {analytics.categoryPerformance.slice(0, 5).map((category) => (
              <div key={category.category} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <p className="font-medium">{category.category}</p>
                  <div className="flex items-center space-x-4 mt-1 text-sm text-gray-500">
                    <span>{category.itemCount} items</span>
                    <span>{category.activeItems} active</span>
                    <span>{category.trackedItems} tracked</span>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-blue-600">
                    {formatCurrency(category.avgPrice)}
                  </p>
                  <p className="text-sm text-gray-500">
                    {category.usageCount} uses • {formatNumber(category.totalStock)} stock
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Profitability Analysis */}
      {analytics.profitabilityAnalysis.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingUp className="h-5 w-5 mr-2" />
              Most Profitable Items
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.profitabilityAnalysis.slice(0, 5).map((item) => (
                <div key={item.id} className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200">
                  <div>
                    <p className="font-medium">{item.name}</p>
                    <p className="text-sm text-gray-600">{item.category || 'Uncategorized'}</p>
                    <div className="flex items-center space-x-2 mt-1">
                      <p className="text-sm text-green-600">
                        Margin: {item.profitMarginPercent?.toFixed(1)}%
                      </p>
                      <p className="text-sm text-gray-500">
                        {item.usageCount} uses
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-green-600">
                      {formatCurrency(item.profitPerUnit || 0)}
                    </p>
                    <p className="text-sm text-gray-500">
                      profit per unit
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recent Items */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Package className="h-5 w-5 mr-2" />
            Recent Items (Last 7 Days)
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {analytics.recentItems.length === 0 ? (
              <p className="text-gray-500 text-center py-4">No recent items</p>
            ) : (
              analytics.recentItems.map((item) => (
                <div key={item.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium">{item.name}</p>
                    <p className="text-sm text-gray-600">
                      {item.sku || 'No SKU'} • {item.category || 'Uncategorized'}
                    </p>
                    <div className="flex items-center space-x-2 mt-1">
                      <Badge className={item.active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'} variant="outline">
                        {item.active ? 'Active' : 'Inactive'}
                      </Badge>
                      {item.trackInventory && (
                        <Badge variant="outline">
                          Tracked
                        </Badge>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-green-600">
                      {formatCurrency(item.unitPrice)}
                    </p>
                    <p className="text-sm text-gray-500">
                      {item.trackInventory ? `Stock: ${item.stockQuantity || 0}` : 'No tracking'}
                    </p>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
