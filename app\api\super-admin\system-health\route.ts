import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import os from 'os'

// GET /api/super-admin/system-health - Get system health metrics
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || session?.user?.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Super admin access required' }, { status: 403 })
    }

    // Get system metrics
    const systemMetrics = {
      // CPU and Memory
      cpuUsage: process.cpuUsage(),
      memoryUsage: process.memoryUsage(),
      
      // System info
      platform: os.platform(),
      arch: os.arch(),
      nodeVersion: process.version,
      uptime: process.uptime(),
      
      // OS metrics
      totalMemory: os.totalmem(),
      freeMemory: os.freemem(),
      loadAverage: os.loadavg(),
      cpuCount: os.cpus().length
    }

    // Database health check
    const dbHealthStart = Date.now()
    try {
      await prisma.$queryRaw`SELECT 1`
      const dbResponseTime = Date.now() - dbHealthStart
      
      // Get database stats
      const [
        totalUsers,
        totalCompanies,
        totalActivities,
        recentErrors,
        dbSize
      ] = await Promise.all([
        prisma.user.count(),
        prisma.company.count(),
        prisma.activity.count(),
        prisma.systemLog.count({
          where: {
            level: 'ERROR',
            createdAt: {
              gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
            }
          }
        }),
        // Approximate database size (MySQL specific)
        prisma.$queryRaw`
          SELECT 
            ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
          FROM information_schema.tables 
          WHERE table_schema = DATABASE()
        `
      ])

      const databaseHealth = {
        status: 'healthy',
        responseTime: dbResponseTime,
        totalUsers,
        totalCompanies,
        totalActivities,
        recentErrors,
        size: (dbSize as any[])[0]?.size_mb || 0
      }

      // Calculate health scores
      const memoryUsagePercent = ((systemMetrics.totalMemory - systemMetrics.freeMemory) / systemMetrics.totalMemory) * 100
      const errorRate = totalActivities > 0 ? (recentErrors / totalActivities) * 100 : 0
      
      let overallStatus = 'healthy'
      if (memoryUsagePercent > 90 || errorRate > 5 || dbResponseTime > 1000) {
        overallStatus = 'critical'
      } else if (memoryUsagePercent > 80 || errorRate > 2 || dbResponseTime > 500) {
        overallStatus = 'warning'
      }

      // Get recent system logs
      const recentLogs = await prisma.systemLog.findMany({
        where: {
          level: { in: ['ERROR', 'WARN'] }
        },
        take: 10,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          level: true,
          message: true,
          source: true,
          category: true,
          createdAt: true
        }
      })

      // Get performance metrics over time
      const performanceMetrics = await prisma.systemHealth.findMany({
        take: 24, // Last 24 entries
        orderBy: { createdAt: 'desc' },
        select: {
          cpuUsage: true,
          memoryUsage: true,
          diskUsage: true,
          dbResponseTime: true,
          activeUsers: true,
          requestsPerMinute: true,
          errorRate: true,
          status: true,
          createdAt: true
        }
      })

      // Store current health metrics
      await prisma.systemHealth.create({
        data: {
          cpuUsage: memoryUsagePercent,
          memoryUsage: memoryUsagePercent,
          diskUsage: 0, // Would need additional logic to calculate
          dbConnections: 1, // Simplified
          dbResponseTime: dbResponseTime,
          activeUsers: await prisma.user.count({ where: { status: 'ACTIVE' } }),
          requestsPerMinute: 0, // Would need request tracking
          errorRate: errorRate,
          status: overallStatus.toUpperCase() as any,
          uptime: systemMetrics.uptime,
          metrics: {
            nodeVersion: systemMetrics.nodeVersion,
            platform: systemMetrics.platform,
            arch: systemMetrics.arch,
            cpuCount: systemMetrics.cpuCount,
            totalMemory: systemMetrics.totalMemory,
            freeMemory: systemMetrics.freeMemory,
            loadAverage: systemMetrics.loadAverage
          }
        }
      })

      return NextResponse.json({
        status: overallStatus,
        timestamp: new Date().toISOString(),
        system: {
          platform: systemMetrics.platform,
          arch: systemMetrics.arch,
          nodeVersion: systemMetrics.nodeVersion,
          uptime: systemMetrics.uptime,
          cpuCount: systemMetrics.cpuCount,
          memory: {
            total: systemMetrics.totalMemory,
            free: systemMetrics.freeMemory,
            used: systemMetrics.totalMemory - systemMetrics.freeMemory,
            usagePercent: memoryUsagePercent
          },
          loadAverage: systemMetrics.loadAverage
        },
        database: databaseHealth,
        metrics: {
          errorRate,
          responseTime: dbResponseTime,
          memoryUsage: memoryUsagePercent
        },
        recentLogs,
        performanceHistory: performanceMetrics.reverse() // Oldest first for charts
      })

    } catch (dbError) {
      console.error('Database health check failed:', dbError)
      return NextResponse.json({
        status: 'critical',
        timestamp: new Date().toISOString(),
        system: systemMetrics,
        database: {
          status: 'unhealthy',
          error: 'Database connection failed'
        },
        error: 'Database health check failed'
      })
    }

  } catch (error) {
    console.error('Error checking system health:', error)
    return NextResponse.json(
      { error: 'Failed to check system health' },
      { status: 500 }
    )
  }
}

// POST /api/super-admin/system-health - Trigger health check and cleanup
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || session?.user?.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Super admin access required' }, { status: 403 })
    }

    const body = await request.json()
    const { action } = body

    switch (action) {
      case 'cleanup_logs':
        // Clean up old system logs (older than 30 days)
        const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
        const deletedLogs = await prisma.systemLog.deleteMany({
          where: {
            createdAt: { lt: thirtyDaysAgo }
          }
        })

        await prisma.auditLog.create({
          data: {
            action: 'SYSTEM_CLEANUP',
            entityType: 'SystemLog',
            userId: session.user.id,
            userEmail: session.user.email,
            userRole: session.user.role,
            metadata: {
              deletedCount: deletedLogs.count,
              cutoffDate: thirtyDaysAgo
            }
          }
        })

        return NextResponse.json({
          message: `Cleaned up ${deletedLogs.count} old log entries`,
          deletedCount: deletedLogs.count
        })

      case 'cleanup_health_metrics':
        // Clean up old health metrics (older than 7 days)
        const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
        const deletedMetrics = await prisma.systemHealth.deleteMany({
          where: {
            createdAt: { lt: sevenDaysAgo }
          }
        })

        return NextResponse.json({
          message: `Cleaned up ${deletedMetrics.count} old health metrics`,
          deletedCount: deletedMetrics.count
        })

      case 'force_health_check':
        // Force a comprehensive health check
        // This would trigger the GET endpoint logic
        return NextResponse.json({
          message: 'Health check triggered',
          timestamp: new Date().toISOString()
        })

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('Error performing system health action:', error)
    return NextResponse.json(
      { error: 'Failed to perform system health action' },
      { status: 500 }
    )
  }
}
