import { prisma } from '@/lib/prisma'

export interface UsageLimits {
  users: number
  customers: number
  quotations: number
  invoices: number
  contracts: number
  storage: number
}

export interface UsageCheck {
  allowed: boolean
  reason?: string
  current: number
  limit: number
  percentage: number
}

export async function checkUsageLimit(
  companyId: string, 
  resource: keyof UsageLimits
): Promise<UsageCheck> {
  try {
    // Get current subscription and limits
    const subscription = await prisma.subscription.findFirst({
      where: {
        companyId,
        status: { in: ['ACTIVE', 'TRIALING', 'PAST_DUE'] }
      },
      include: {
        pricingPlan: true
      }
    })

    if (!subscription) {
      return {
        allowed: false,
        reason: 'No active subscription found',
        current: 0,
        limit: 0,
        percentage: 0
      }
    }

    // Get current usage count
    let currentUsage = 0
    let limit = 0

    switch (resource) {
      case 'users':
        currentUsage = await prisma.user.count({ where: { companyId } })
        limit = subscription.pricingPlan.maxUsers
        break
      case 'customers':
        currentUsage = await prisma.customer.count({ where: { companyId } })
        limit = subscription.pricingPlan.maxCustomers
        break
      case 'quotations':
        currentUsage = await prisma.quotation.count({ where: { companyId } })
        limit = subscription.pricingPlan.maxQuotations
        break
      case 'invoices':
        currentUsage = await prisma.invoice.count({ where: { companyId } })
        limit = subscription.pricingPlan.maxInvoices
        break
      case 'contracts':
        currentUsage = await prisma.contract.count({ where: { companyId } })
        limit = subscription.pricingPlan.maxContracts
        break
      case 'storage':
        currentUsage = await calculateStorageUsage(companyId)
        limit = Number(subscription.pricingPlan.maxStorage)
        break
      default:
        throw new Error(`Unknown resource: ${resource}`)
    }

    const percentage = (currentUsage / limit) * 100
    const allowed = currentUsage < limit

    return {
      allowed,
      reason: allowed ? undefined : `${resource} limit exceeded (${currentUsage}/${limit})`,
      current: currentUsage,
      limit,
      percentage: Math.round(percentage)
    }

  } catch (error) {
    console.error('Error checking usage limit:', error)
    return {
      allowed: false,
      reason: 'Error checking usage limits',
      current: 0,
      limit: 0,
      percentage: 0
    }
  }
}

export async function checkMultipleUsageLimits(
  companyId: string,
  resources: (keyof UsageLimits)[]
): Promise<Record<keyof UsageLimits, UsageCheck>> {
  const results = {} as Record<keyof UsageLimits, UsageCheck>
  
  for (const resource of resources) {
    results[resource] = await checkUsageLimit(companyId, resource)
  }
  
  return results
}

export async function getAllUsageLimits(companyId: string): Promise<Record<keyof UsageLimits, UsageCheck>> {
  return checkMultipleUsageLimits(companyId, [
    'users',
    'customers', 
    'quotations',
    'invoices',
    'contracts',
    'storage'
  ])
}

export async function canCreateResource(
  companyId: string,
  resource: keyof UsageLimits
): Promise<boolean> {
  const check = await checkUsageLimit(companyId, resource)
  return check.allowed
}

export async function getUsageSummary(companyId: string) {
  const subscription = await prisma.subscription.findFirst({
    where: {
      companyId,
      status: { in: ['ACTIVE', 'TRIALING', 'PAST_DUE'] }
    },
    include: {
      pricingPlan: true
    }
  })

  if (!subscription) {
    return null
  }

  const [
    userCount,
    customerCount,
    quotationCount,
    invoiceCount,
    contractCount,
    storageUsed
  ] = await Promise.all([
    prisma.user.count({ where: { companyId } }),
    prisma.customer.count({ where: { companyId } }),
    prisma.quotation.count({ where: { companyId } }),
    prisma.invoice.count({ where: { companyId } }),
    prisma.contract.count({ where: { companyId } }),
    calculateStorageUsage(companyId)
  ])

  return {
    subscription: {
      planName: subscription.pricingPlan.name,
      status: subscription.status,
      billingCycle: subscription.billingCycle
    },
    usage: {
      users: {
        current: userCount,
        limit: subscription.pricingPlan.maxUsers,
        percentage: Math.round((userCount / subscription.pricingPlan.maxUsers) * 100)
      },
      customers: {
        current: customerCount,
        limit: subscription.pricingPlan.maxCustomers,
        percentage: Math.round((customerCount / subscription.pricingPlan.maxCustomers) * 100)
      },
      quotations: {
        current: quotationCount,
        limit: subscription.pricingPlan.maxQuotations,
        percentage: Math.round((quotationCount / subscription.pricingPlan.maxQuotations) * 100)
      },
      invoices: {
        current: invoiceCount,
        limit: subscription.pricingPlan.maxInvoices,
        percentage: Math.round((invoiceCount / subscription.pricingPlan.maxInvoices) * 100)
      },
      contracts: {
        current: contractCount,
        limit: subscription.pricingPlan.maxContracts,
        percentage: Math.round((contractCount / subscription.pricingPlan.maxContracts) * 100)
      },
      storage: {
        current: storageUsed,
        limit: Number(subscription.pricingPlan.maxStorage),
        percentage: Math.round((storageUsed / Number(subscription.pricingPlan.maxStorage)) * 100),
        currentFormatted: formatBytes(storageUsed),
        limitFormatted: formatBytes(Number(subscription.pricingPlan.maxStorage))
      }
    }
  }
}

async function calculateStorageUsage(companyId: string): Promise<number> {
  // Mock storage calculation - in real app, calculate actual file sizes
  const [quotationCount, invoiceCount, contractCount] = await Promise.all([
    prisma.quotation.count({ where: { companyId } }),
    prisma.invoice.count({ where: { companyId } }),
    prisma.contract.count({ where: { companyId } })
  ])

  // Estimate: 50KB per quotation, 75KB per invoice, 100KB per contract
  const estimatedBytes = (quotationCount * 50000) + (invoiceCount * 75000) + (contractCount * 100000)
  
  // Add some randomness for demo purposes
  return estimatedBytes + Math.floor(Math.random() * 10000000)
}

function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// Middleware function to check limits before API operations
export async function withUsageLimit<T>(
  companyId: string,
  resource: keyof UsageLimits,
  operation: () => Promise<T>
): Promise<T> {
  const check = await checkUsageLimit(companyId, resource)
  
  if (!check.allowed) {
    throw new Error(check.reason || 'Usage limit exceeded')
  }
  
  return operation()
}
