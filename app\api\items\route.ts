import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const itemSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  sku: z.string().optional(),
  category: z.string().optional(),
  unitPrice: z.number().min(0, 'Unit price must be positive'),
  costPrice: z.number().min(0, 'Cost price must be positive').optional(),
  currency: z.string().default('USD'),
  trackInventory: z.boolean().default(false),
  stockQuantity: z.number().int().min(0).optional(),
  lowStockAlert: z.number().int().min(0).optional(),
  taxable: z.boolean().default(true),
  taxRate: z.number().min(0).max(100).default(0),
  accountingCode: z.string().optional(),
  active: z.boolean().default(true)
})

// GET /api/items - Get all items
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const search = searchParams.get('search') || ''
    const category = searchParams.get('category') || ''
    const status = searchParams.get('status') || ''
    const trackInventory = searchParams.get('trackInventory')
    const lowStock = searchParams.get('lowStock') === 'true'

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {
      companyId: session.user.companyId
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { sku: { contains: search, mode: 'insensitive' } },
        { category: { contains: search, mode: 'insensitive' } }
      ]
    }

    if (category) {
      where.category = category
    }

    if (status === 'active') {
      where.active = true
    } else if (status === 'inactive') {
      where.active = false
    }

    if (trackInventory === 'true') {
      where.trackInventory = true
    } else if (trackInventory === 'false') {
      where.trackInventory = false
    }

    if (lowStock) {
      where.trackInventory = true
      where.AND = [
        { stockQuantity: { not: null } },
        { lowStockAlert: { not: null } },
        { stockQuantity: { lte: prisma.item.fields.lowStockAlert } }
      ]
    }

    const [items, total] = await Promise.all([
      prisma.item.findMany({
        where,
        include: {
          createdBy: {
            select: {
              name: true,
              email: true
            }
          },
          quotationItems: {
            select: {
              id: true,
              quantity: true,
              quotation: {
                select: {
                  id: true,
                  quotationNumber: true
                }
              }
            }
          },
          invoiceItems: {
            select: {
              id: true,
              quantity: true,
              invoice: {
                select: {
                  id: true,
                  invoiceNumber: true
                }
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.item.count({ where })
    ])

    // Calculate usage statistics for each item
    const itemsWithStats = items.map(item => {
      const quotationUsage = item.quotationItems.reduce((sum, qi) => sum + qi.quantity, 0)
      const invoiceUsage = item.invoiceItems.reduce((sum, ii) => sum + ii.quantity, 0)
      const totalUsage = quotationUsage + invoiceUsage
      const usageCount = item.quotationItems.length + item.invoiceItems.length

      return {
        id: item.id,
        name: item.name,
        description: item.description,
        sku: item.sku,
        category: item.category,
        unitPrice: Number(item.unitPrice),
        costPrice: item.costPrice ? Number(item.costPrice) : null,
        currency: item.currency,
        trackInventory: item.trackInventory,
        stockQuantity: item.stockQuantity,
        lowStockAlert: item.lowStockAlert,
        taxable: item.taxable,
        taxRate: Number(item.taxRate),
        accountingCode: item.accountingCode,
        active: item.active,
        createdBy: item.createdBy,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
        usage: {
          totalQuantity: totalUsage,
          usageCount,
          quotationUsage,
          invoiceUsage,
          recentQuotations: item.quotationItems.slice(0, 3).map(qi => qi.quotation),
          recentInvoices: item.invoiceItems.slice(0, 3).map(ii => ii.invoice)
        },
        stockValue: item.trackInventory && item.stockQuantity 
          ? Number(item.unitPrice) * item.stockQuantity 
          : 0,
        profitMargin: item.costPrice 
          ? ((Number(item.unitPrice) - Number(item.costPrice)) / Number(item.unitPrice)) * 100
          : null,
        isLowStock: item.trackInventory && item.stockQuantity !== null && item.lowStockAlert !== null
          ? item.stockQuantity <= item.lowStockAlert
          : false
      }
    })

    return NextResponse.json({
      items: itemsWithStats,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Error fetching items:', error)
    return NextResponse.json(
      { error: 'Failed to fetch items' },
      { status: 500 }
    )
  }
}

// POST /api/items - Create new item
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = itemSchema.parse(body)

    // Check for duplicate SKU if provided
    if (validatedData.sku) {
      const existingItem = await prisma.item.findFirst({
        where: {
          sku: validatedData.sku,
          companyId: session.user.companyId
        }
      })

      if (existingItem) {
        return NextResponse.json(
          { error: 'An item with this SKU already exists' },
          { status: 400 }
        )
      }
    }

    // Validate inventory settings
    if (validatedData.trackInventory) {
      if (validatedData.stockQuantity === undefined) {
        return NextResponse.json(
          { error: 'Stock quantity is required when inventory tracking is enabled' },
          { status: 400 }
        )
      }
    } else {
      // Clear inventory fields if not tracking
      validatedData.stockQuantity = undefined
      validatedData.lowStockAlert = undefined
    }

    const item = await prisma.$transaction(async (tx) => {
      // Create the item
      const newItem = await tx.item.create({
        data: {
          ...validatedData,
          companyId: session.user.companyId!,
          createdById: session.user.id
        },
        include: {
          createdBy: {
            select: {
              name: true,
              email: true
            }
          }
        }
      })

      // Log activity
      await tx.activity.create({
        data: {
          type: 'ITEM',
          title: 'Item Created',
          description: `Item "${validatedData.name}" was created`,
          itemId: newItem.id,
          companyId: session.user.companyId!,
          createdById: session.user.id
        }
      })

      return newItem
    })

    return NextResponse.json({
      item: {
        id: item.id,
        name: item.name,
        description: item.description,
        sku: item.sku,
        category: item.category,
        unitPrice: Number(item.unitPrice),
        costPrice: item.costPrice ? Number(item.costPrice) : null,
        currency: item.currency,
        trackInventory: item.trackInventory,
        stockQuantity: item.stockQuantity,
        lowStockAlert: item.lowStockAlert,
        taxable: item.taxable,
        taxRate: Number(item.taxRate),
        accountingCode: item.accountingCode,
        active: item.active,
        createdBy: item.createdBy,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
        usage: {
          totalQuantity: 0,
          usageCount: 0,
          quotationUsage: 0,
          invoiceUsage: 0,
          recentQuotations: [],
          recentInvoices: []
        },
        stockValue: item.trackInventory && item.stockQuantity 
          ? Number(item.unitPrice) * item.stockQuantity 
          : 0,
        profitMargin: item.costPrice 
          ? ((Number(item.unitPrice) - Number(item.costPrice)) / Number(item.unitPrice)) * 100
          : null,
        isLowStock: false
      },
      message: 'Item created successfully'
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating item:', error)
    return NextResponse.json(
      { error: 'Failed to create item' },
      { status: 500 }
    )
  }
}
