import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'
import {
  withAuth,
  withErrorHandling,
  validateSchema,
  getPaginationParams,
  getSearchParams,
  successResponse,
  ApiError,
  ApiErrorType,
  sanitizeInput,
  commonSchemas
} from '@/lib/api-utils'

const itemSchema = z.object({
  name: z.string().min(1, 'Name is required').max(255, 'Name too long'),
  description: z.string().max(1000, 'Description too long').optional().nullable(),
  sku: z.string().max(100, 'SKU too long').optional().nullable(),
  category: z.string().max(100, 'Category too long').optional().nullable(),
  unitPrice: commonSchemas.decimal,
  costPrice: commonSchemas.decimal.optional().nullable(),
  currency: commonSchemas.currency,
  trackInventory: z.boolean().default(false),
  stockQuantity: z.number().int().min(0, 'Stock quantity must be non-negative').optional().nullable(),
  lowStockAlert: z.number().int().min(0, 'Low stock alert must be non-negative').optional().nullable(),
  taxable: z.boolean().default(true),
  taxRate: z.number().min(0, 'Tax rate must be non-negative').max(100, 'Tax rate cannot exceed 100%').default(0),
  accountingCode: z.string().max(50, 'Accounting code too long').optional().nullable(),
  active: z.boolean().default(true),
  tags: z.array(z.string()).optional().default([])
})

// GET /api/items - Get all items
export const GET = withErrorHandling(async (request: NextRequest) => {
  const { companyId } = await withAuth(request)
  const { page, limit, skip } = getPaginationParams(request)
  const { search } = getSearchParams(request)
  
  const { searchParams } = new URL(request.url)
  const category = searchParams.get('category') || ''
  const status = searchParams.get('status') || ''
  const trackInventory = searchParams.get('trackInventory')
  const lowStock = searchParams.get('lowStock') === 'true'

  // Build where clause
  const where: any = {
    companyId
  }

  if (search) {
    where.OR = [
      { name: { contains: search, mode: 'insensitive' } },
      { description: { contains: search, mode: 'insensitive' } },
      { sku: { contains: search, mode: 'insensitive' } },
      { category: { contains: search, mode: 'insensitive' } }
    ]
  }

  if (category) {
    where.category = category
  }

  if (status === 'active') {
    where.active = true
  } else if (status === 'inactive') {
    where.active = false
  }

  if (trackInventory === 'true') {
    where.trackInventory = true
  } else if (trackInventory === 'false') {
    where.trackInventory = false
  }

  if (lowStock) {
    where.trackInventory = true
    where.stockQuantity = { not: null }
    where.lowStockAlert = { not: null }
    // Note: Complex comparison needs to be handled in application logic
  }

  const [items, total] = await Promise.all([
    prisma.item.findMany({
      where,
      include: {
        quotationItems: {
          select: {
            id: true,
            quantity: true,
            quotation: {
              select: {
                id: true,
                quotationNumber: true
              }
            }
          }
        },
        invoiceItems: {
          select: {
            id: true,
            quantity: true,
            invoice: {
              select: {
                id: true,
                invoiceNumber: true
              }
            }
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      skip,
      take: limit
    }),
    prisma.item.count({ where })
  ])

  // Calculate usage statistics for each item
  const itemsWithStats = items.map(item => {
    const quotationUsage = item.quotationItems.reduce((sum, qi) => sum + qi.quantity, 0)
    const invoiceUsage = item.invoiceItems.reduce((sum, ii) => sum + ii.quantity, 0)
    const totalUsage = quotationUsage + invoiceUsage
    const usageCount = item.quotationItems.length + item.invoiceItems.length

    return {
      id: item.id,
      name: item.name,
      description: item.description,
      sku: item.sku,
      category: item.category,
      unitPrice: Number(item.unitPrice),
      costPrice: item.costPrice ? Number(item.costPrice) : null,
      currency: item.currency,
      trackInventory: item.trackInventory,
      stockQuantity: item.stockQuantity,
      lowStockAlert: item.lowStockAlert,
      taxable: item.taxable,
      taxRate: Number(item.taxRate),
      accountingCode: item.accountingCode,
      active: item.active,
      tags: item.tags || [],
      createdAt: item.createdAt,
      updatedAt: item.updatedAt,
      usage: {
        totalQuantity: totalUsage,
        usageCount,
        quotationUsage,
        invoiceUsage,
        recentQuotations: item.quotationItems.slice(0, 3).map(qi => qi.quotation),
        recentInvoices: item.invoiceItems.slice(0, 3).map(ii => ii.invoice)
      },
      stockValue: item.trackInventory && item.stockQuantity 
        ? Number(item.unitPrice) * item.stockQuantity 
        : 0,
      profitMargin: item.costPrice 
        ? ((Number(item.unitPrice) - Number(item.costPrice)) / Number(item.unitPrice)) * 100
        : null,
      isLowStock: item.trackInventory && item.stockQuantity !== null && item.lowStockAlert !== null
        ? item.stockQuantity <= item.lowStockAlert
        : false
    }
  })

  return successResponse(
    itemsWithStats,
    'Items retrieved successfully',
    {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit)
    }
  )
})

// POST /api/items - Create new item
export const POST = withErrorHandling(async (request: NextRequest) => {
  const { user, companyId } = await withAuth(request)
  
  const body = await request.json()
  const sanitizedData = sanitizeInput(body)
  const validatedData = validateSchema(itemSchema, sanitizedData)

  // Check for duplicate SKU if provided
  if (validatedData.sku) {
    const existingItem = await prisma.item.findFirst({
      where: {
        sku: validatedData.sku,
        companyId
      }
    })

    if (existingItem) {
      throw new ApiError(
        'An item with this SKU already exists',
        409,
        ApiErrorType.CONFLICT
      )
    }
  }

  // Validate inventory settings
  if (validatedData.trackInventory) {
    if (validatedData.stockQuantity === undefined || validatedData.stockQuantity === null) {
      throw new ApiError(
        'Stock quantity is required when inventory tracking is enabled',
        400,
        ApiErrorType.VALIDATION_ERROR
      )
    }
  } else {
    // Clear inventory fields if not tracking
    validatedData.stockQuantity = null
    validatedData.lowStockAlert = null
  }

    const item = await prisma.$transaction(async (tx) => {
      // Create the item
      const newItem = await tx.item.create({
        data: {
          ...validatedData,
          companyId
        }
      })

      // Log activity
      await tx.activity.create({
        data: {
          type: 'SYSTEM',
          title: 'Item Created',
          description: `Item "${validatedData.name}" was created`,
          companyId,
          createdById: user.id
        }
      })

      return newItem
    })

    return NextResponse.json({
      item: {
        id: item.id,
        name: item.name,
        description: item.description,
        sku: item.sku,
        category: item.category,
        unitPrice: Number(item.unitPrice),
        costPrice: item.costPrice ? Number(item.costPrice) : null,
        currency: item.currency,
        trackInventory: item.trackInventory,
        stockQuantity: item.stockQuantity,
        lowStockAlert: item.lowStockAlert,
        taxable: item.taxable,
        taxRate: Number(item.taxRate),
        accountingCode: item.accountingCode,
        active: item.active,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
        usage: {
          totalQuantity: 0,
          usageCount: 0,
          quotationUsage: 0,
          invoiceUsage: 0,
          recentQuotations: [],
          recentInvoices: []
        },
        stockValue: item.trackInventory && item.stockQuantity
          ? Number(item.unitPrice) * item.stockQuantity
          : 0,
        profitMargin: item.costPrice
          ? ((Number(item.unitPrice) - Number(item.costPrice)) / Number(item.unitPrice)) * 100
          : null,
        isLowStock: false
      },
      message: 'Item created successfully'
    })
})
