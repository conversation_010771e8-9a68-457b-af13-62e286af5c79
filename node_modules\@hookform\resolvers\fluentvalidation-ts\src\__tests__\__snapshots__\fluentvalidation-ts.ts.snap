// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`fluentValidationResolver > should return a single error from fluentValidationResolver when validation fails 1`] = `
{
  "errors": {
    "birthYear": {
      "message": "Value is not valid",
      "ref": undefined,
      "type": "validation",
    },
    "email": {
      "message": "Not a valid email address",
      "ref": {
        "name": "email",
      },
      "type": "validation",
    },
    "password": {
      "message": "One uppercase character",
      "ref": {
        "name": "password",
      },
      "type": "validation",
    },
    "repeatPassword": {
      "message": "Value is not valid",
      "ref": undefined,
      "type": "validation",
    },
  },
  "values": {},
}
`;

exports[`fluentValidationResolver > should return a single error from fluentValidationResolver with \`mode: sync\` when validation fails 1`] = `
{
  "errors": {
    "birthYear": {
      "message": "Value is not valid",
      "ref": undefined,
      "type": "validation",
    },
    "email": {
      "message": "Not a valid email address",
      "ref": {
        "name": "email",
      },
      "type": "validation",
    },
    "password": {
      "message": "One uppercase character",
      "ref": {
        "name": "password",
      },
      "type": "validation",
    },
    "repeatPassword": {
      "message": "Value is not valid",
      "ref": undefined,
      "type": "validation",
    },
  },
  "values": {},
}
`;

exports[`fluentValidationResolver > should return all the errors from fluentValidationResolver when validation fails with \`validateAllFieldCriteria\` set to true 1`] = `
{
  "errors": {
    "birthYear": {
      "message": "Value is not valid",
      "ref": undefined,
      "type": "validation",
    },
    "email": {
      "message": "Not a valid email address",
      "ref": {
        "name": "email",
      },
      "type": "validation",
    },
    "password": {
      "message": "One uppercase character",
      "ref": {
        "name": "password",
      },
      "type": "validation",
    },
    "repeatPassword": {
      "message": "Value is not valid",
      "ref": undefined,
      "type": "validation",
    },
  },
  "values": {},
}
`;

exports[`fluentValidationResolver > should return all the errors from fluentValidationResolver when validation fails with \`validateAllFieldCriteria\` set to true and \`mode: sync\` 1`] = `
{
  "errors": {
    "birthYear": {
      "message": "Value is not valid",
      "ref": undefined,
      "type": "validation",
    },
    "email": {
      "message": "Not a valid email address",
      "ref": {
        "name": "email",
      },
      "type": "validation",
    },
    "password": {
      "message": "One uppercase character",
      "ref": {
        "name": "password",
      },
      "type": "validation",
    },
    "repeatPassword": {
      "message": "Value is not valid",
      "ref": undefined,
      "type": "validation",
    },
  },
  "values": {},
}
`;
