'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { redirect } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { 
  Smartphone, 
  Save, 
  RefreshCw, 
  CreditCard,
  Banknote,
  QrCode,
  Wallet,
  Building,
  Globe,
  CheckCircle,
  XCircle,
  Plus,
  Trash2,
  Edit
} from 'lucide-react'
import { toast } from 'react-hot-toast'

interface PaymentMethod {
  id: string
  name: string
  type: 'card' | 'bank' | 'wallet' | 'upi' | 'crypto' | 'other'
  provider: string
  isEnabled: boolean
  supportedCurrencies: string[]
  processingFee: {
    percentage: number
    fixed: number
    currency: string
  }
  minimumAmount: number
  maximumAmount: number
  icon: string
  description: string
  config: Record<string, any>
}

interface PaymentMethodsConfig {
  // Card Payments
  creditCard: {
    enabled: boolean
    supportedBrands: string[]
    requireCVV: boolean
    require3DS: boolean
  }
  debitCard: {
    enabled: boolean
    supportedBrands: string[]
    requireCVV: boolean
    require3DS: boolean
  }
  
  // Digital Wallets
  applePay: {
    enabled: boolean
    merchantId: string
  }
  googlePay: {
    enabled: boolean
    merchantId: string
  }
  paypal: {
    enabled: boolean
    clientId: string
  }
  
  // Bank Transfers
  netBanking: {
    enabled: boolean
    supportedBanks: string[]
  }
  ach: {
    enabled: boolean
    verificationMethod: 'instant' | 'micro-deposits'
  }
  wire: {
    enabled: boolean
    requireManualApproval: boolean
  }
  
  // UPI & QR Codes
  upi: {
    enabled: boolean
    supportedApps: string[]
    qrCodeEnabled: boolean
  }
  
  // Cryptocurrency
  bitcoin: {
    enabled: boolean
    walletAddress: string
  }
  ethereum: {
    enabled: boolean
    walletAddress: string
  }
  
  // Buy Now Pay Later
  klarna: {
    enabled: boolean
    clientId: string
  }
  afterpay: {
    enabled: boolean
    merchantId: string
  }
}

export default function PaymentMethodsPage() {
  const { data: session, status } = useSession()
  const [methods, setMethods] = useState<PaymentMethod[]>([])
  const [config, setConfig] = useState<PaymentMethodsConfig>({
    creditCard: {
      enabled: true,
      supportedBrands: ['visa', 'mastercard', 'amex', 'discover'],
      requireCVV: true,
      require3DS: false
    },
    debitCard: {
      enabled: true,
      supportedBrands: ['visa', 'mastercard'],
      requireCVV: true,
      require3DS: false
    },
    applePay: {
      enabled: false,
      merchantId: ''
    },
    googlePay: {
      enabled: false,
      merchantId: ''
    },
    paypal: {
      enabled: false,
      clientId: ''
    },
    netBanking: {
      enabled: false,
      supportedBanks: []
    },
    ach: {
      enabled: false,
      verificationMethod: 'instant'
    },
    wire: {
      enabled: false,
      requireManualApproval: true
    },
    upi: {
      enabled: false,
      supportedApps: ['gpay', 'phonepe', 'paytm'],
      qrCodeEnabled: true
    },
    bitcoin: {
      enabled: false,
      walletAddress: ''
    },
    ethereum: {
      enabled: false,
      walletAddress: ''
    },
    klarna: {
      enabled: false,
      clientId: ''
    },
    afterpay: {
      enabled: false,
      merchantId: ''
    }
  })
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (status === 'unauthenticated') {
    redirect('/auth/signin')
  }

  if (session?.user?.role !== 'SUPER_ADMIN') {
    redirect('/dashboard')
  }

  const fetchMethods = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/super-admin/payment-methods')
      const data = await response.json()
      
      if (data.success) {
        setMethods(data.methods)
        setConfig({ ...config, ...data.config })
      }
    } catch (error) {
      console.error('Error fetching payment methods:', error)
      toast.error('Failed to load payment methods')
    } finally {
      setLoading(false)
    }
  }

  const saveConfig = async () => {
    try {
      setSaving(true)
      const response = await fetch('/api/super-admin/payment-methods', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
      })

      const data = await response.json()
      
      if (data.success) {
        toast.success('Payment methods configuration saved successfully')
        fetchMethods()
      } else {
        toast.error(data.error || 'Failed to save configuration')
      }
    } catch (error) {
      console.error('Error saving config:', error)
      toast.error('Failed to save configuration')
    } finally {
      setSaving(false)
    }
  }

  useEffect(() => {
    fetchMethods()
  }, [])

  const updateConfig = (method: keyof PaymentMethodsConfig, key: string, value: any) => {
    setConfig(prev => ({
      ...prev,
      [method]: {
        ...prev[method],
        [key]: value
      }
    }))
  }

  const getMethodIcon = (type: string) => {
    switch (type) {
      case 'card':
        return <CreditCard className="h-5 w-5" />
      case 'bank':
        return <Building className="h-5 w-5" />
      case 'wallet':
        return <Wallet className="h-5 w-5" />
      case 'upi':
        return <QrCode className="h-5 w-5" />
      case 'crypto':
        return <Banknote className="h-5 w-5" />
      default:
        return <Smartphone className="h-5 w-5" />
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <div className="flex items-center space-x-3">
            <Smartphone className="h-8 w-8 text-blue-600" />
            <h1 className="text-3xl font-bold text-gray-900">Payment Methods</h1>
          </div>
          <p className="text-gray-500 mt-1">Configure available payment options for customers</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={fetchMethods} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button onClick={saveConfig} disabled={saving}>
            <Save className={`h-4 w-4 mr-2 ${saving ? 'animate-spin' : ''}`} />
            Save Configuration
          </Button>
        </div>
      </div>

      {/* Methods Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Card Payments</p>
                <p className="text-2xl font-bold text-blue-600">
                  {(config.creditCard.enabled ? 1 : 0) + (config.debitCard.enabled ? 1 : 0)}
                </p>
              </div>
              <CreditCard className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Digital Wallets</p>
                <p className="text-2xl font-bold text-green-600">
                  {(config.applePay.enabled ? 1 : 0) + (config.googlePay.enabled ? 1 : 0) + (config.paypal.enabled ? 1 : 0)}
                </p>
              </div>
              <Wallet className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Bank Transfers</p>
                <p className="text-2xl font-bold text-purple-600">
                  {(config.netBanking.enabled ? 1 : 0) + (config.ach.enabled ? 1 : 0) + (config.wire.enabled ? 1 : 0)}
                </p>
              </div>
              <Building className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Alternative Methods</p>
                <p className="text-2xl font-bold text-orange-600">
                  {(config.upi.enabled ? 1 : 0) + (config.bitcoin.enabled ? 1 : 0) + (config.klarna.enabled ? 1 : 0)}
                </p>
              </div>
              <QrCode className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Payment Methods Configuration */}
      <Tabs defaultValue="cards" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="cards">Cards</TabsTrigger>
          <TabsTrigger value="wallets">Digital Wallets</TabsTrigger>
          <TabsTrigger value="banking">Banking</TabsTrigger>
          <TabsTrigger value="alternative">Alternative</TabsTrigger>
          <TabsTrigger value="bnpl">Buy Now Pay Later</TabsTrigger>
        </TabsList>

        {/* Card Payments */}
        <TabsContent value="cards">
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <CreditCard className="h-5 w-5 mr-2" />
                  Credit Cards
                </CardTitle>
                <CardDescription>
                  Configure credit card payment acceptance
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <Label htmlFor="creditCardEnabled" className="text-base font-medium">
                      Accept Credit Cards
                    </Label>
                    <p className="text-sm text-gray-500">
                      Enable credit card payments via Visa, Mastercard, Amex, etc.
                    </p>
                  </div>
                  <Switch
                    id="creditCardEnabled"
                    checked={config.creditCard.enabled}
                    onCheckedChange={(checked) => updateConfig('creditCard', 'enabled', checked)}
                  />
                </div>

                {config.creditCard.enabled && (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label>Supported Card Brands</Label>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                        {['visa', 'mastercard', 'amex', 'discover', 'jcb', 'diners'].map((brand) => (
                          <div key={brand} className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              id={`credit-${brand}`}
                              checked={config.creditCard.supportedBrands.includes(brand)}
                              onChange={(e) => {
                                const brands = e.target.checked
                                  ? [...config.creditCard.supportedBrands, brand]
                                  : config.creditCard.supportedBrands.filter(b => b !== brand)
                                updateConfig('creditCard', 'supportedBrands', brands)
                              }}
                              className="rounded"
                            />
                            <Label htmlFor={`credit-${brand}`} className="text-sm capitalize">
                              {brand}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="flex items-center justify-between p-4 border rounded-lg">
                        <div>
                          <Label htmlFor="creditCVV" className="text-base font-medium">
                            Require CVV
                          </Label>
                          <p className="text-sm text-gray-500">
                            Require CVV code for enhanced security
                          </p>
                        </div>
                        <Switch
                          id="creditCVV"
                          checked={config.creditCard.requireCVV}
                          onCheckedChange={(checked) => updateConfig('creditCard', 'requireCVV', checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between p-4 border rounded-lg">
                        <div>
                          <Label htmlFor="credit3DS" className="text-base font-medium">
                            3D Secure
                          </Label>
                          <p className="text-sm text-gray-500">
                            Enable 3D Secure authentication
                          </p>
                        </div>
                        <Switch
                          id="credit3DS"
                          checked={config.creditCard.require3DS}
                          onCheckedChange={(checked) => updateConfig('creditCard', 'require3DS', checked)}
                        />
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <CreditCard className="h-5 w-5 mr-2" />
                  Debit Cards
                </CardTitle>
                <CardDescription>
                  Configure debit card payment acceptance
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <Label htmlFor="debitCardEnabled" className="text-base font-medium">
                      Accept Debit Cards
                    </Label>
                    <p className="text-sm text-gray-500">
                      Enable debit card payments
                    </p>
                  </div>
                  <Switch
                    id="debitCardEnabled"
                    checked={config.debitCard.enabled}
                    onCheckedChange={(checked) => updateConfig('debitCard', 'enabled', checked)}
                  />
                </div>

                {config.debitCard.enabled && (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label>Supported Card Brands</Label>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                        {['visa', 'mastercard', 'maestro', 'rupay'].map((brand) => (
                          <div key={brand} className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              id={`debit-${brand}`}
                              checked={config.debitCard.supportedBrands.includes(brand)}
                              onChange={(e) => {
                                const brands = e.target.checked
                                  ? [...config.debitCard.supportedBrands, brand]
                                  : config.debitCard.supportedBrands.filter(b => b !== brand)
                                updateConfig('debitCard', 'supportedBrands', brands)
                              }}
                              className="rounded"
                            />
                            <Label htmlFor={`debit-${brand}`} className="text-sm capitalize">
                              {brand}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
