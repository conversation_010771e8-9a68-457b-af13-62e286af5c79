(()=>{var e={};e.id=1123,e.ids=[1123],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},59629:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>d});var a=t(50482),r=t(69108),l=t(62563),i=t.n(l),n=t(68300),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d=["",{children:["dashboard",{children:["items",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,4350)),"C:\\proj\\nextjs-saas\\app\\dashboard\\items\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,96339)),"C:\\proj\\nextjs-saas\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,59504)),"C:\\proj\\nextjs-saas\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\proj\\nextjs-saas\\app\\dashboard\\items\\page.tsx"],x="/dashboard/items/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/items/page",pathname:"/dashboard/items",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},11920:(e,s,t)=>{Promise.resolve().then(t.bind(t,17324))},17324:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>U});var a=t(95344),r=t(3729),l=t(16212),i=t(92549),n=t(69436),c=t(17470),d=t(81036),o=t(20886),x=t(61351),m=t(50340),h=t(51838),u=t(91917),p=t(88534),j=t(69224);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let f=(0,j.Z)("Warehouse",[["path",{d:"M22 8.35V20a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V8.35A2 2 0 0 1 3.26 6.5l8-3.2a2 2 0 0 1 1.48 0l8 3.2A2 2 0 0 1 22 8.35Z",key:"gksnxg"}],["path",{d:"M6 18h12",key:"9pbo8z"}],["path",{d:"M6 14h12",key:"4cwo0f"}],["rect",{width:"12",height:"12",x:"6",y:"10",key:"apd30q"}]]);var g=t(45961),y=t(48411),v=t(28765),N=t(62093),b=t(75695),k=t(38271),w=t(44669),C=t(1586),S=t(93601),I=t(71809),P=t(16802),Z=t(57341),R=t(55329);let M=["Electronics","Software","Hardware","Services","Consulting","Training","Support","Maintenance","Subscription","Digital Products","Physical Products","Other"],A=[{value:"USD",label:"USD ($)"},{value:"EUR",label:"EUR (€)"},{value:"GBP",label:"GBP (\xa3)"},{value:"CAD",label:"CAD (C$)"},{value:"AUD",label:"AUD (A$)"}];function D({isOpen:e,onClose:s,onSuccess:t,item:n,mode:d}){let[o,x]=(0,r.useState)(!1),[m,h]=(0,r.useState)({name:"",description:"",sku:"",category:"",unitPrice:0,costPrice:"",currency:"USD",trackInventory:!1,stockQuantity:"",lowStockAlert:"",taxable:!0,taxRate:0,accountingCode:"",active:!0});(0,r.useEffect)(()=>{n&&"edit"===d?h({name:n.name,description:n.description||"",sku:n.sku||"",category:n.category||"",unitPrice:n.unitPrice,costPrice:n.costPrice?.toString()||"",currency:n.currency,trackInventory:n.trackInventory,stockQuantity:n.stockQuantity?.toString()||"",lowStockAlert:n.lowStockAlert?.toString()||"",taxable:n.taxable,taxRate:n.taxRate,accountingCode:n.accountingCode||"",active:n.active}):h({name:"",description:"",sku:"",category:"",unitPrice:0,costPrice:"",currency:"USD",trackInventory:!1,stockQuantity:"",lowStockAlert:"",taxable:!0,taxRate:0,accountingCode:"",active:!0})},[n,d,e]);let p=(e,s)=>{h(t=>({...t,[e]:s}))},j=async e=>{if(e.preventDefault(),!m.name.trim()){w.toast.error("Item name is required");return}if(m.unitPrice<0){w.toast.error("Unit price must be positive");return}if(m.costPrice&&0>parseFloat(m.costPrice)){w.toast.error("Cost price must be positive");return}if(m.trackInventory&&!m.stockQuantity){w.toast.error("Stock quantity is required when inventory tracking is enabled");return}x(!0);try{let e={name:m.name.trim(),description:m.description.trim()||void 0,sku:m.sku.trim()||void 0,category:m.category||void 0,unitPrice:m.unitPrice,costPrice:m.costPrice?parseFloat(m.costPrice):void 0,currency:m.currency,trackInventory:m.trackInventory,stockQuantity:m.trackInventory&&m.stockQuantity?parseInt(m.stockQuantity):void 0,lowStockAlert:m.trackInventory&&m.lowStockAlert?parseInt(m.lowStockAlert):void 0,taxable:m.taxable,taxRate:m.taxRate,accountingCode:m.accountingCode.trim()||void 0,active:m.active},a="edit"===d?`/api/items/${n?.id}`:"/api/items",r="edit"===d?"PUT":"POST",l=await fetch(a,{method:r,headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!l.ok){let e=await l.json();throw Error(e.error||`Failed to ${d} item`)}w.toast.success(`Item ${"edit"===d?"updated":"created"} successfully!`),t(),s()}catch(e){w.toast.error(e instanceof Error?e.message:`Failed to ${d} item`)}finally{x(!1)}},g=m.costPrice&&m.unitPrice>0?(m.unitPrice-parseFloat(m.costPrice))/m.unitPrice*100:0,v=m.trackInventory&&m.stockQuantity?m.unitPrice*parseInt(m.stockQuantity):0;return a.jsx(P.Vq,{open:e,onOpenChange:s,children:(0,a.jsxs)(P.cZ,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[a.jsx(P.fK,{children:(0,a.jsxs)(P.$N,{className:"flex items-center",children:[a.jsx(u.Z,{className:"h-5 w-5 mr-2"}),"edit"===d?"Edit Item":"Create New Item"]})}),(0,a.jsxs)("form",{onSubmit:j,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold flex items-center",children:[a.jsx(u.Z,{className:"h-4 w-4 mr-2"}),"Basic Information"]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx(C._,{htmlFor:"name",children:"Item Name *"}),a.jsx(i.I,{id:"name",value:m.name,onChange:e=>p("name",e.target.value),placeholder:"Enter item name",required:!0})]}),(0,a.jsxs)("div",{children:[a.jsx(C._,{htmlFor:"sku",children:"SKU"}),a.jsx(i.I,{id:"sku",value:m.sku,onChange:e=>p("sku",e.target.value),placeholder:"Stock Keeping Unit"})]})]}),(0,a.jsxs)("div",{children:[a.jsx(C._,{htmlFor:"description",children:"Description"}),a.jsx(S.g,{id:"description",value:m.description,onChange:e=>p("description",e.target.value),placeholder:"Item description...",rows:3})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx(C._,{htmlFor:"category",children:"Category"}),(0,a.jsxs)(c.Ph,{value:m.category,onValueChange:e=>p("category",e),children:[a.jsx(c.i4,{children:a.jsx(c.ki,{placeholder:"Select category"})}),a.jsx(c.Bw,{children:M.map(e=>a.jsx(c.Ql,{value:e,children:e},e))})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(I.r,{id:"active",checked:m.active,onCheckedChange:e=>p("active",e)}),a.jsx(C._,{htmlFor:"active",children:"Active"})]})]})]}),a.jsx(Z.Z,{}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold flex items-center",children:[a.jsx(y.Z,{className:"h-4 w-4 mr-2"}),"Pricing"]}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx(C._,{htmlFor:"unitPrice",children:"Unit Price *"}),a.jsx(i.I,{id:"unitPrice",type:"number",step:"0.01",min:"0",value:m.unitPrice,onChange:e=>p("unitPrice",parseFloat(e.target.value)||0),placeholder:"0.00",required:!0})]}),(0,a.jsxs)("div",{children:[a.jsx(C._,{htmlFor:"costPrice",children:"Cost Price"}),a.jsx(i.I,{id:"costPrice",type:"number",step:"0.01",min:"0",value:m.costPrice,onChange:e=>p("costPrice",e.target.value),placeholder:"0.00"})]}),(0,a.jsxs)("div",{children:[a.jsx(C._,{htmlFor:"currency",children:"Currency"}),(0,a.jsxs)(c.Ph,{value:m.currency,onValueChange:e=>p("currency",e),children:[a.jsx(c.i4,{children:a.jsx(c.ki,{})}),a.jsx(c.Bw,{children:A.map(e=>a.jsx(c.Ql,{value:e.value,children:e.label},e.value))})]})]})]}),m.costPrice&&(0,a.jsxs)("div",{className:"p-3 bg-green-50 rounded-lg border border-green-200",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-sm font-medium text-green-800",children:"Profit Margin:"}),(0,a.jsxs)("span",{className:"text-lg font-bold text-green-600",children:[g.toFixed(1),"%"]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between mt-1",children:[a.jsx("span",{className:"text-sm text-green-700",children:"Profit per Unit:"}),a.jsx("span",{className:"text-sm font-semibold text-green-600",children:new Intl.NumberFormat("en-US",{style:"currency",currency:m.currency}).format(m.unitPrice-parseFloat(m.costPrice))})]})]})]}),a.jsx(Z.Z,{}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold flex items-center",children:[a.jsx(f,{className:"h-4 w-4 mr-2"}),"Inventory Management"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(I.r,{id:"trackInventory",checked:m.trackInventory,onCheckedChange:e=>p("trackInventory",e)}),a.jsx(C._,{htmlFor:"trackInventory",children:"Track Inventory"})]})]}),m.trackInventory&&(0,a.jsxs)("div",{className:"space-y-4 p-4 bg-blue-50 rounded-lg border border-blue-200",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx(C._,{htmlFor:"stockQuantity",children:"Stock Quantity *"}),a.jsx(i.I,{id:"stockQuantity",type:"number",min:"0",value:m.stockQuantity,onChange:e=>p("stockQuantity",e.target.value),placeholder:"0",required:m.trackInventory})]}),(0,a.jsxs)("div",{children:[a.jsx(C._,{htmlFor:"lowStockAlert",children:"Low Stock Alert"}),a.jsx(i.I,{id:"lowStockAlert",type:"number",min:"0",value:m.lowStockAlert,onChange:e=>p("lowStockAlert",e.target.value),placeholder:"10"})]})]}),m.stockQuantity&&a.jsx("div",{className:"p-3 bg-blue-100 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-sm font-medium text-blue-800",children:"Stock Value:"}),a.jsx("span",{className:"text-lg font-bold text-blue-600",children:new Intl.NumberFormat("en-US",{style:"currency",currency:m.currency}).format(v)})]})})]})]}),a.jsx(Z.Z,{}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold flex items-center",children:[a.jsx(R.Z,{className:"h-4 w-4 mr-2"}),"Tax & Accounting"]}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(I.r,{id:"taxable",checked:m.taxable,onCheckedChange:e=>p("taxable",e)}),a.jsx(C._,{htmlFor:"taxable",children:"Taxable"})]}),(0,a.jsxs)("div",{children:[a.jsx(C._,{htmlFor:"taxRate",children:"Tax Rate (%)"}),a.jsx(i.I,{id:"taxRate",type:"number",step:"0.01",min:"0",max:"100",value:m.taxRate,onChange:e=>p("taxRate",parseFloat(e.target.value)||0),placeholder:"0.00",disabled:!m.taxable})]}),(0,a.jsxs)("div",{children:[a.jsx(C._,{htmlFor:"accountingCode",children:"Accounting Code"}),a.jsx(i.I,{id:"accountingCode",value:m.accountingCode,onChange:e=>p("accountingCode",e.target.value),placeholder:"e.g., 4000"})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-2 pt-4 border-t",children:[a.jsx(l.z,{type:"button",variant:"outline",onClick:s,children:"Cancel"}),a.jsx(l.z,{type:"submit",disabled:o,children:o?(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"edit"===d?"Updating...":"Creating..."]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(u.Z,{className:"h-4 w-4 mr-2"}),"edit"===d?"Update Item":"Create Item"]})})]})]})]})})}var F=t(33733),_=t(46064),V=t(17910),z=t(21096);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let T=(0,j.Z)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]]);function E(){let[e,s]=(0,r.useState)(null),[t,i]=(0,r.useState)(!0),[d,o]=(0,r.useState)("30"),h=async()=>{try{i(!0);let e=await fetch(`/api/items/analytics?period=${d}`);if(!e.ok)throw Error("Failed to fetch analytics");let t=await e.json();s(t)}catch(e){w.toast.error("Failed to load item analytics"),console.error("Error fetching analytics:",e)}finally{i(!1)}};(0,r.useEffect)(()=>{h()},[d]);let j=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e),v=e=>new Intl.NumberFormat("en-US").format(e);if(t)return a.jsx("div",{className:"flex items-center justify-center py-8",children:a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})});if(!e)return(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[a.jsx(m.Z,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),a.jsx("p",{children:"Failed to load analytics data"})]});let N=e.summary.totalStockQuantity>0?e.summary.usedItemsCount/e.summary.totalItems*100:0,b=e.summary.totalStockValue>0&&e.summary.totalCostValue>0?(e.summary.totalStockValue-e.summary.totalCostValue)/e.summary.totalStockValue*100:0;return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("h3",{className:"text-lg font-semibold",children:"Item Analytics"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(c.Ph,{value:d,onValueChange:o,children:[a.jsx(c.i4,{className:"w-32",children:a.jsx(c.ki,{})}),(0,a.jsxs)(c.Bw,{children:[a.jsx(c.Ql,{value:"7",children:"Last 7 days"}),a.jsx(c.Ql,{value:"30",children:"Last 30 days"}),a.jsx(c.Ql,{value:"90",children:"Last 90 days"}),a.jsx(c.Ql,{value:"365",children:"Last year"})]})]}),(0,a.jsxs)(l.z,{variant:"outline",onClick:h,size:"sm",children:[a.jsx(F.Z,{className:"h-4 w-4 mr-2"}),"Refresh"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[a.jsx(x.Zb,{children:a.jsx(x.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-blue-100 rounded-full",children:a.jsx(u.Z,{className:"h-6 w-6 text-blue-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Total Items"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.totalItems})]})]})})}),a.jsx(x.Zb,{children:a.jsx(x.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-green-100 rounded-full",children:a.jsx(p.Z,{className:"h-6 w-6 text-green-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Active Items"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.activeItems})]})]})})}),a.jsx(x.Zb,{children:a.jsx(x.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-purple-100 rounded-full",children:a.jsx(f,{className:"h-6 w-6 text-purple-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Tracked Items"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.trackedItems})]})]})})}),a.jsx(x.Zb,{children:a.jsx(x.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-orange-100 rounded-full",children:a.jsx(g.Z,{className:"h-6 w-6 text-orange-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Low Stock"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.lowStockCount})]})]})})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[a.jsx(x.Zb,{children:a.jsx(x.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-emerald-100 rounded-full",children:a.jsx(y.Z,{className:"h-6 w-6 text-emerald-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Stock Value"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:j(e.summary.totalStockValue)})]})]})})}),a.jsx(x.Zb,{children:a.jsx(x.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-indigo-100 rounded-full",children:a.jsx(_.Z,{className:"h-6 w-6 text-indigo-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Avg Unit Price"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:j(e.summary.avgUnitPrice)})]})]})})}),a.jsx(x.Zb,{children:a.jsx(x.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-pink-100 rounded-full",children:a.jsx(V.Z,{className:"h-6 w-6 text-pink-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Usage Value"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:j(e.summary.totalUsageValue)})]})]})})}),a.jsx(x.Zb,{children:a.jsx(x.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-teal-100 rounded-full",children:a.jsx(z.Z,{className:"h-6 w-6 text-teal-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Profit Margin"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[b.toFixed(1),"%"]})]})]})})})]}),(0,a.jsxs)(x.Zb,{children:[a.jsx(x.Ol,{children:(0,a.jsxs)(x.ll,{className:"flex items-center",children:[a.jsx(f,{className:"h-5 w-5 mr-2"}),"Inventory Overview"]})}),a.jsx(x.aY,{children:(0,a.jsxs)("div",{className:"grid grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("p",{className:"text-2xl font-bold text-blue-600",children:v(e.summary.totalStockQuantity)}),a.jsx("p",{className:"text-sm text-gray-500",children:"Total Stock Quantity"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("p",{className:"text-2xl font-bold text-green-600",children:e.summary.avgStockPerItem}),a.jsx("p",{className:"text-sm text-gray-500",children:"Avg Stock per Item"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("p",{className:"text-2xl font-bold text-purple-600",children:[N.toFixed(1),"%"]}),a.jsx("p",{className:"text-sm text-gray-500",children:"Stock Utilization"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("p",{className:"text-2xl font-bold text-orange-600",children:e.summary.itemsWithCostPrice}),a.jsx("p",{className:"text-sm text-gray-500",children:"Items with Cost Price"})]})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)(x.Zb,{children:[a.jsx(x.Ol,{children:a.jsx(x.ll,{children:"Items by Category"})}),a.jsx(x.aY,{children:a.jsx("div",{className:"space-y-3",children:e.itemsByCategory.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("div",{className:"flex items-center space-x-2",children:a.jsx(n.C,{variant:"outline",children:e.category})}),(0,a.jsxs)("div",{className:"text-right",children:[a.jsx("span",{className:"font-semibold",children:e.count}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Avg: ",j(e.avgPrice)]})]})]},e.category))})})]}),(0,a.jsxs)(x.Zb,{children:[a.jsx(x.Ol,{children:a.jsx(x.ll,{children:"Items by Status"})}),a.jsx(x.aY,{children:a.jsx("div",{className:"space-y-3",children:e.itemsByStatus.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("div",{className:"flex items-center space-x-2",children:a.jsx(n.C,{className:"Active"===e.status?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800",children:e.status})}),(0,a.jsxs)("div",{className:"text-right",children:[a.jsx("span",{className:"font-semibold",children:e.count}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Avg: ",j(e.avgPrice)]})]})]},e.status))})})]})]}),(0,a.jsxs)(x.Zb,{children:[a.jsx(x.Ol,{children:(0,a.jsxs)(x.ll,{className:"flex items-center",children:[a.jsx(T,{className:"h-5 w-5 mr-2"}),"Top Selling Items"]})}),a.jsx(x.aY,{children:a.jsx("div",{className:"space-y-3",children:e.topSellingItems.slice(0,5).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center",children:(0,a.jsxs)("span",{className:"text-xs font-semibold text-blue-600",children:["#",s+1]})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium",children:e.name}),a.jsx("p",{className:"text-sm text-gray-500",children:e.category||"Uncategorized"})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[a.jsx("p",{className:"font-semibold text-green-600",children:j(e.totalRevenue)}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[e.usageCount," uses • ",v(e.totalQuantitySold)," sold"]})]})]},e.id))})})]}),e.lowStockItems.length>0&&(0,a.jsxs)(x.Zb,{children:[a.jsx(x.Ol,{children:(0,a.jsxs)(x.ll,{className:"flex items-center",children:[a.jsx(g.Z,{className:"h-5 w-5 mr-2 text-orange-600"}),"Low Stock Alert"]})}),a.jsx(x.aY,{children:a.jsx("div",{className:"space-y-3",children:e.lowStockItems.slice(0,5).map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-orange-50 rounded-lg border border-orange-200",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[e.sku||"No SKU"," • ",e.category||"Uncategorized"]}),a.jsx("div",{className:"flex items-center space-x-2 mt-1",children:(0,a.jsxs)("p",{className:"text-sm text-orange-600",children:["Stock: ",e.stockQuantity," / Alert: ",e.lowStockAlert]})})]}),(0,a.jsxs)("div",{className:"text-right",children:[a.jsx("p",{className:"font-semibold text-green-600",children:j(e.stockValue)}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[j(e.unitPrice)," each"]})]})]},e.id))})})]}),(0,a.jsxs)(x.Zb,{children:[a.jsx(x.Ol,{children:(0,a.jsxs)(x.ll,{className:"flex items-center",children:[a.jsx(m.Z,{className:"h-5 w-5 mr-2"}),"Category Performance"]})}),a.jsx(x.aY,{children:a.jsx("div",{className:"space-y-3",children:e.categoryPerformance.slice(0,5).map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium",children:e.category}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 mt-1 text-sm text-gray-500",children:[(0,a.jsxs)("span",{children:[e.itemCount," items"]}),(0,a.jsxs)("span",{children:[e.activeItems," active"]}),(0,a.jsxs)("span",{children:[e.trackedItems," tracked"]})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[a.jsx("p",{className:"font-semibold text-blue-600",children:j(e.avgPrice)}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[e.usageCount," uses • ",v(e.totalStock)," stock"]})]})]},e.category))})})]}),e.profitabilityAnalysis.length>0&&(0,a.jsxs)(x.Zb,{children:[a.jsx(x.Ol,{children:(0,a.jsxs)(x.ll,{className:"flex items-center",children:[a.jsx(_.Z,{className:"h-5 w-5 mr-2"}),"Most Profitable Items"]})}),a.jsx(x.aY,{children:a.jsx("div",{className:"space-y-3",children:e.profitabilityAnalysis.slice(0,5).map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium",children:e.name}),a.jsx("p",{className:"text-sm text-gray-600",children:e.category||"Uncategorized"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,a.jsxs)("p",{className:"text-sm text-green-600",children:["Margin: ",e.profitMarginPercent?.toFixed(1),"%"]}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[e.usageCount," uses"]})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[a.jsx("p",{className:"font-semibold text-green-600",children:j(e.profitPerUnit||0)}),a.jsx("p",{className:"text-sm text-gray-500",children:"profit per unit"})]})]},e.id))})})]}),(0,a.jsxs)(x.Zb,{children:[a.jsx(x.Ol,{children:(0,a.jsxs)(x.ll,{className:"flex items-center",children:[a.jsx(u.Z,{className:"h-5 w-5 mr-2"}),"Recent Items (Last 7 Days)"]})}),a.jsx(x.aY,{children:a.jsx("div",{className:"space-y-3",children:0===e.recentItems.length?a.jsx("p",{className:"text-gray-500 text-center py-4",children:"No recent items"}):e.recentItems.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[e.sku||"No SKU"," • ",e.category||"Uncategorized"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[a.jsx(n.C,{className:e.active?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800",variant:"outline",children:e.active?"Active":"Inactive"}),e.trackInventory&&a.jsx(n.C,{variant:"outline",children:"Tracked"})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[a.jsx("p",{className:"font-semibold text-green-600",children:j(e.unitPrice)}),a.jsx("p",{className:"text-sm text-gray-500",children:e.trackInventory?`Stock: ${e.stockQuantity||0}`:"No tracking"})]})]},e.id))})})]})]})}function U(){let[e,s]=(0,r.useState)([]),[t,j]=(0,r.useState)(!0),[C,S]=(0,r.useState)(!1),[I,P]=(0,r.useState)(null),[Z,R]=(0,r.useState)(!1),[M,A]=(0,r.useState)(""),[F,_]=(0,r.useState)(""),[V,z]=(0,r.useState)(""),[T,U]=(0,r.useState)(""),[Q,O]=(0,r.useState)(!1),[q,Y]=(0,r.useState)(1),[$,L]=(0,r.useState)(1),[B,H]=(0,r.useState)(0),W=async()=>{try{j(!0);let e=new URLSearchParams({page:q.toString(),limit:"50",...M&&{search:M},...F&&"all"!==F&&{category:F},...V&&"all"!==V&&{status:V},...T&&"all"!==T&&{trackInventory:T},...Q&&{lowStock:"true"}}),t=await fetch(`/api/items?${e}`);if(!t.ok)throw Error("Failed to fetch items");let a=await t.json();s(a.items),L(a.pagination.pages),H(a.pagination.total)}catch(e){w.toast.error("Failed to load items"),console.error("Error fetching items:",e)}finally{j(!1)}};(0,r.useEffect)(()=>{W()},[q,M,F,V,T,Q]);let K=()=>{Y(1),W()},G=e=>{P(e),S(!0)},X=async e=>{if(confirm(`Are you sure you want to delete "${e.name}"?`))try{let s=await fetch(`/api/items/${e.id}`,{method:"DELETE"});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to delete item")}w.toast.success("Item deleted successfully"),W()}catch(e){w.toast.error(e instanceof Error?e.message:"Failed to delete item")}},J=(e,s="USD")=>new Intl.NumberFormat("en-US",{style:"currency",currency:s}).format(e),ee=e=>e?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800",es=e=>{if(!e)return"bg-gray-100 text-gray-800";let s=["bg-blue-100 text-blue-800","bg-purple-100 text-purple-800","bg-green-100 text-green-800","bg-orange-100 text-orange-800","bg-pink-100 text-pink-800","bg-indigo-100 text-indigo-800"];return s[Math.abs(e.split("").reduce((e,s)=>(e=(e<<5)-e+s.charCodeAt(0))&e,0))%s.length]},et=e.filter(e=>e.active).length,ea=e.filter(e=>e.trackInventory).length,er=e.filter(e=>e.isLowStock).length,el=e.reduce((e,s)=>e+s.stockValue,0),ei=Array.from(new Set(e.map(e=>e.category).filter(Boolean)));return t&&0===e.length?a.jsx("div",{className:"flex items-center justify-center min-h-screen",children:a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,a.jsxs)("div",{className:"container mx-auto py-6 space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Items"}),a.jsx("p",{className:"text-gray-600 mt-1",children:"Manage your products and services"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(l.z,{variant:"outline",onClick:()=>R(!Z),children:[a.jsx(m.Z,{className:"h-4 w-4 mr-2"}),"Analytics"]}),(0,a.jsxs)(l.z,{onClick:()=>S(!0),children:[a.jsx(h.Z,{className:"h-4 w-4 mr-2"}),"New Item"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[a.jsx(x.Zb,{children:a.jsx(x.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-blue-100 rounded-full",children:a.jsx(u.Z,{className:"h-6 w-6 text-blue-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Total Items"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:B})]})]})})}),a.jsx(x.Zb,{children:a.jsx(x.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-green-100 rounded-full",children:a.jsx(p.Z,{className:"h-6 w-6 text-green-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Active Items"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:et})]})]})})}),a.jsx(x.Zb,{children:a.jsx(x.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-purple-100 rounded-full",children:a.jsx(f,{className:"h-6 w-6 text-purple-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Tracked Items"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:ea})]})]})})}),a.jsx(x.Zb,{children:a.jsx(x.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-orange-100 rounded-full",children:a.jsx(g.Z,{className:"h-6 w-6 text-orange-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Low Stock"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:er})]})]})})})]}),a.jsx(x.Zb,{children:a.jsx(x.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-emerald-100 rounded-full",children:a.jsx(y.Z,{className:"h-6 w-6 text-emerald-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Total Stock Value"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:J(el)})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Avg per Item"}),a.jsx("p",{className:"text-lg font-semibold text-gray-700",children:J(ea>0?el/ea:0)})]})]})})}),Z&&a.jsx(E,{}),a.jsx(x.Zb,{children:a.jsx(x.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-4",children:[a.jsx("div",{className:"flex-1 min-w-64",children:(0,a.jsxs)("div",{className:"relative",children:[a.jsx(v.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),a.jsx(i.I,{placeholder:"Search items...",value:M,onChange:e=>A(e.target.value),onKeyPress:e=>"Enter"===e.key&&K(),className:"pl-10"})]})}),(0,a.jsxs)(c.Ph,{value:F,onValueChange:_,children:[a.jsx(c.i4,{className:"w-48",children:a.jsx(c.ki,{placeholder:"All Categories"})}),(0,a.jsxs)(c.Bw,{children:[a.jsx(c.Ql,{value:"all",children:"All Categories"}),ei.map(e=>a.jsx(c.Ql,{value:e,children:e},e))]})]}),(0,a.jsxs)(c.Ph,{value:V,onValueChange:z,children:[a.jsx(c.i4,{className:"w-32",children:a.jsx(c.ki,{placeholder:"Status"})}),(0,a.jsxs)(c.Bw,{children:[a.jsx(c.Ql,{value:"all",children:"All Status"}),a.jsx(c.Ql,{value:"active",children:"Active"}),a.jsx(c.Ql,{value:"inactive",children:"Inactive"})]})]}),(0,a.jsxs)(c.Ph,{value:T,onValueChange:U,children:[a.jsx(c.i4,{className:"w-40",children:a.jsx(c.ki,{placeholder:"Inventory"})}),(0,a.jsxs)(c.Bw,{children:[a.jsx(c.Ql,{value:"all",children:"All Items"}),a.jsx(c.Ql,{value:"true",children:"Tracked"}),a.jsx(c.Ql,{value:"false",children:"Not Tracked"})]})]}),(0,a.jsxs)(l.z,{variant:Q?"default":"outline",onClick:()=>O(!Q),size:"sm",children:[a.jsx(g.Z,{className:"h-4 w-4 mr-2"}),"Low Stock"]}),(0,a.jsxs)(l.z,{onClick:K,size:"sm",children:[a.jsx(v.Z,{className:"h-4 w-4 mr-2"}),"Search"]})]})})}),(0,a.jsxs)(x.Zb,{children:[a.jsx(x.Ol,{children:(0,a.jsxs)(x.ll,{children:["Items (",B,")"]})}),(0,a.jsxs)(x.aY,{children:[a.jsx("div",{className:"overflow-x-auto",children:(0,a.jsxs)(d.iA,{children:[a.jsx(d.xD,{children:(0,a.jsxs)(d.SC,{children:[a.jsx(d.ss,{children:"Item"}),a.jsx(d.ss,{children:"Category"}),a.jsx(d.ss,{children:"Price"}),a.jsx(d.ss,{children:"Stock"}),a.jsx(d.ss,{children:"Usage"}),a.jsx(d.ss,{children:"Status"}),a.jsx(d.ss,{className:"w-12"})]})}),a.jsx(d.RM,{children:t?a.jsx(d.SC,{children:a.jsx(d.pj,{colSpan:7,className:"text-center py-8",children:a.jsx("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"})})}):0===e.length?a.jsx(d.SC,{children:a.jsx(d.pj,{colSpan:7,className:"text-center py-8 text-gray-500",children:"No items found"})}):e.map(e=>(0,a.jsxs)(d.SC,{children:[a.jsx(d.pj,{children:(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium",children:e.name}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[e.sku&&`SKU: ${e.sku}`,e.description&&a.jsx("div",{className:"truncate max-w-xs",children:e.description})]})]})}),a.jsx(d.pj,{children:e.category?a.jsx(n.C,{className:es(e.category),variant:"outline",children:e.category}):a.jsx("span",{className:"text-gray-400",children:"-"})}),a.jsx(d.pj,{children:(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium",children:J(e.unitPrice,e.currency)}),e.costPrice&&(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["Cost: ",J(e.costPrice,e.currency),e.profitMargin&&(0,a.jsxs)("span",{className:"ml-1 text-green-600",children:["(",e.profitMargin.toFixed(1),"%)"]})]})]})}),a.jsx(d.pj,{children:e.trackInventory?(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:`font-medium ${e.isLowStock?"text-orange-600":""}`,children:[e.stockQuantity||0,e.isLowStock&&a.jsx(g.Z,{className:"inline h-4 w-4 ml-1 text-orange-600"})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["Value: ",J(e.stockValue,e.currency)]})]}):a.jsx("span",{className:"text-gray-400",children:"Not tracked"})}),a.jsx(d.pj,{children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"font-medium",children:[e.usage.usageCount," uses"]}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[e.usage.totalQuantity," total qty"]})]})}),a.jsx(d.pj,{children:a.jsx(n.C,{className:ee(e.active),variant:"outline",children:e.active?"Active":"Inactive"})}),a.jsx(d.pj,{children:(0,a.jsxs)(o.h_,{children:[a.jsx(o.$F,{asChild:!0,children:a.jsx(l.z,{variant:"ghost",size:"sm",children:a.jsx(N.Z,{className:"h-4 w-4"})})}),(0,a.jsxs)(o.AW,{align:"end",children:[(0,a.jsxs)(o.Xi,{onClick:()=>G(e),children:[a.jsx(b.Z,{className:"mr-2 h-4 w-4"}),"Edit"]}),(0,a.jsxs)(o.Xi,{onClick:()=>X(e),children:[a.jsx(k.Z,{className:"mr-2 h-4 w-4"}),"Delete"]})]})]})})]},e.id))})]})}),$>1&&(0,a.jsxs)("div",{className:"flex items-center justify-between mt-4",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["Showing ",(q-1)*50+1," to ",Math.min(50*q,B)," of ",B," items"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(l.z,{variant:"outline",size:"sm",onClick:()=>Y(e=>Math.max(1,e-1)),disabled:1===q,children:"Previous"}),(0,a.jsxs)("span",{className:"text-sm",children:["Page ",q," of ",$]}),a.jsx(l.z,{variant:"outline",size:"sm",onClick:()=>Y(e=>Math.min($,e+1)),disabled:q===$,children:"Next"})]})]})]})]}),a.jsx(D,{isOpen:C,onClose:()=>{S(!1),P(null)},onSuccess:W,item:I,mode:I?"edit":"create"})]})}},16802:(e,s,t)=>{"use strict";t.d(s,{$N:()=>p,Be:()=>j,Vq:()=>c,cN:()=>u,cZ:()=>m,fK:()=>h,hg:()=>d,t9:()=>x});var a=t(95344),r=t(3729),l=t(88794),i=t(14513),n=t(91626);let c=l.fC,d=l.xz,o=l.h_;l.x8;let x=r.forwardRef(({className:e,...s},t)=>a.jsx(l.aV,{ref:t,className:(0,n.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...s}));x.displayName=l.aV.displayName;let m=r.forwardRef(({className:e,children:s,...t},r)=>(0,a.jsxs)(o,{children:[a.jsx(x,{}),(0,a.jsxs)(l.VY,{ref:r,className:(0,n.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...t,children:[s,(0,a.jsxs)(l.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[a.jsx(i.Z,{className:"h-4 w-4"}),a.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));m.displayName=l.VY.displayName;let h=({className:e,...s})=>a.jsx("div",{className:(0,n.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...s});h.displayName="DialogHeader";let u=({className:e,...s})=>a.jsx("div",{className:(0,n.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...s});u.displayName="DialogFooter";let p=r.forwardRef(({className:e,...s},t)=>a.jsx(l.Dx,{ref:t,className:(0,n.cn)("text-lg font-semibold leading-none tracking-tight",e),...s}));p.displayName=l.Dx.displayName;let j=r.forwardRef(({className:e,...s},t)=>a.jsx(l.dk,{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",e),...s}));j.displayName=l.dk.displayName},92549:(e,s,t)=>{"use strict";t.d(s,{I:()=>i});var a=t(95344),r=t(3729),l=t(91626);let i=r.forwardRef(({className:e,type:s,...t},r)=>a.jsx("input",{type:s,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...t}));i.displayName="Input"},1586:(e,s,t)=>{"use strict";t.d(s,{_:()=>d});var a=t(95344),r=t(3729),l=t(14217),i=t(49247),n=t(91626);let c=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef(({className:e,...s},t)=>a.jsx(l.f,{ref:t,className:(0,n.cn)(c(),e),...s}));d.displayName=l.f.displayName},17470:(e,s,t)=>{"use strict";t.d(s,{Bw:()=>p,Ph:()=>o,Ql:()=>j,i4:()=>m,ki:()=>x});var a=t(95344),r=t(3729),l=t(1146),i=t(25390),n=t(12704),c=t(62312),d=t(91626);let o=l.fC;l.ZA;let x=l.B4,m=r.forwardRef(({className:e,children:s,...t},r)=>(0,a.jsxs)(l.xz,{ref:r,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...t,children:[s,a.jsx(l.JO,{asChild:!0,children:a.jsx(i.Z,{className:"h-4 w-4 opacity-50"})})]}));m.displayName=l.xz.displayName;let h=r.forwardRef(({className:e,...s},t)=>a.jsx(l.u_,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:a.jsx(n.Z,{className:"h-4 w-4"})}));h.displayName=l.u_.displayName;let u=r.forwardRef(({className:e,...s},t)=>a.jsx(l.$G,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:a.jsx(i.Z,{className:"h-4 w-4"})}));u.displayName=l.$G.displayName;let p=r.forwardRef(({className:e,children:s,position:t="popper",...r},i)=>a.jsx(l.h_,{children:(0,a.jsxs)(l.VY,{ref:i,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:t,...r,children:[a.jsx(h,{}),a.jsx(l.l_,{className:(0,d.cn)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),a.jsx(u,{})]})}));p.displayName=l.VY.displayName,r.forwardRef(({className:e,...s},t)=>a.jsx(l.__,{ref:t,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...s})).displayName=l.__.displayName;let j=r.forwardRef(({className:e,children:s,...t},r)=>(0,a.jsxs)(l.ck,{ref:r,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[a.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:a.jsx(l.wU,{children:a.jsx(c.Z,{className:"h-4 w-4"})})}),a.jsx(l.eT,{children:s})]}));j.displayName=l.ck.displayName,r.forwardRef(({className:e,...s},t)=>a.jsx(l.Z0,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...s})).displayName=l.Z0.displayName},57341:(e,s,t)=>{"use strict";t.d(s,{Z:()=>o});var a=t(95344),r=t(3729),l=t(62409),i="horizontal",n=["horizontal","vertical"],c=r.forwardRef((e,s)=>{let{decorative:t,orientation:r=i,...c}=e,d=n.includes(r)?r:i;return(0,a.jsx)(l.WV.div,{"data-orientation":d,...t?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...c,ref:s})});c.displayName="Separator";var d=t(91626);let o=r.forwardRef(({className:e,orientation:s="horizontal",decorative:t=!0,...r},l)=>a.jsx(c,{ref:l,decorative:t,orientation:s,className:(0,d.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",e),...r}));o.displayName=c.displayName},71809:(e,s,t)=>{"use strict";t.d(s,{r:()=>k});var a=t(95344),r=t(3729),l=t(85222),i=t(31405),n=t(98462),c=t(33183),d=t(92062),o=t(63085),x=t(62409),m="Switch",[h,u]=(0,n.b)(m),[p,j]=h(m),f=r.forwardRef((e,s)=>{let{__scopeSwitch:t,name:n,checked:d,defaultChecked:o,required:h,disabled:u,value:j="on",onCheckedChange:f,form:g,...y}=e,[b,k]=r.useState(null),w=(0,i.e)(s,e=>k(e)),C=r.useRef(!1),S=!b||g||!!b.closest("form"),[I,P]=(0,c.T)({prop:d,defaultProp:o??!1,onChange:f,caller:m});return(0,a.jsxs)(p,{scope:t,checked:I,disabled:u,children:[(0,a.jsx)(x.WV.button,{type:"button",role:"switch","aria-checked":I,"aria-required":h,"data-state":N(I),"data-disabled":u?"":void 0,disabled:u,value:j,...y,ref:w,onClick:(0,l.M)(e.onClick,e=>{P(e=>!e),S&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),S&&(0,a.jsx)(v,{control:b,bubbles:!C.current,name:n,value:j,checked:I,required:h,disabled:u,form:g,style:{transform:"translateX(-100%)"}})]})});f.displayName=m;var g="SwitchThumb",y=r.forwardRef((e,s)=>{let{__scopeSwitch:t,...r}=e,l=j(g,t);return(0,a.jsx)(x.WV.span,{"data-state":N(l.checked),"data-disabled":l.disabled?"":void 0,...r,ref:s})});y.displayName=g;var v=r.forwardRef(({__scopeSwitch:e,control:s,checked:t,bubbles:l=!0,...n},c)=>{let x=r.useRef(null),m=(0,i.e)(x,c),h=(0,d.D)(t),u=(0,o.t)(s);return r.useEffect(()=>{let e=x.current;if(!e)return;let s=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(h!==t&&s){let a=new Event("click",{bubbles:l});s.call(e,t),e.dispatchEvent(a)}},[h,t,l]),(0,a.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:t,...n,tabIndex:-1,ref:m,style:{...n.style,...u,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function N(e){return e?"checked":"unchecked"}v.displayName="SwitchBubbleInput";var b=t(91626);let k=r.forwardRef(({className:e,...s},t)=>a.jsx(f,{className:(0,b.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...s,ref:t,children:a.jsx(y,{className:(0,b.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));k.displayName=f.displayName},81036:(e,s,t)=>{"use strict";t.d(s,{RM:()=>c,SC:()=>d,iA:()=>i,pj:()=>x,ss:()=>o,xD:()=>n});var a=t(95344),r=t(3729),l=t(91626);let i=r.forwardRef(({className:e,...s},t)=>a.jsx("div",{className:"relative w-full overflow-auto",children:a.jsx("table",{ref:t,className:(0,l.cn)("w-full caption-bottom text-sm",e),...s})}));i.displayName="Table";let n=r.forwardRef(({className:e,...s},t)=>a.jsx("thead",{ref:t,className:(0,l.cn)("[&_tr]:border-b",e),...s}));n.displayName="TableHeader";let c=r.forwardRef(({className:e,...s},t)=>a.jsx("tbody",{ref:t,className:(0,l.cn)("[&_tr:last-child]:border-0",e),...s}));c.displayName="TableBody",r.forwardRef(({className:e,...s},t)=>a.jsx("tfoot",{ref:t,className:(0,l.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...s})).displayName="TableFooter";let d=r.forwardRef(({className:e,...s},t)=>a.jsx("tr",{ref:t,className:(0,l.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...s}));d.displayName="TableRow";let o=r.forwardRef(({className:e,...s},t)=>a.jsx("th",{ref:t,className:(0,l.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...s}));o.displayName="TableHead";let x=r.forwardRef(({className:e,...s},t)=>a.jsx("td",{ref:t,className:(0,l.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...s}));x.displayName="TableCell",r.forwardRef(({className:e,...s},t)=>a.jsx("caption",{ref:t,className:(0,l.cn)("mt-4 text-sm text-muted-foreground",e),...s})).displayName="TableCaption"},93601:(e,s,t)=>{"use strict";t.d(s,{g:()=>i});var a=t(95344),r=t(3729),l=t(91626);let i=r.forwardRef(({className:e,...s},t)=>a.jsx("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:t,...s}));i.displayName="Textarea"},88534:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]])},45961:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},55329:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]])},48411:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},71542:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("FileCheck",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["path",{d:"m9 15 2 2 4-4",key:"1grp1n"}]])},62093:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("MoreHorizontal",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},91917:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},75695:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},21096:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("PieChart",[["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}],["path",{d:"M22 12A10 10 0 0 0 12 2v10z",key:"1rfc4y"}]])},51838:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},74243:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Receipt",[["path",{d:"M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1-2-1Z",key:"wqdwcb"}],["path",{d:"M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8",key:"1h4pet"}],["path",{d:"M12 17V7",key:"pyj7ub"}]])},33733:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},17910:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},38271:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},46064:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},28240:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},4350:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>l,__esModule:()=>r,default:()=>i});let a=(0,t(86843).createProxy)(String.raw`C:\proj\nextjs-saas\app\dashboard\items\page.tsx`),{__esModule:r,$$typeof:l}=a,i=a.default},88794:(e,s,t)=>{"use strict";t.d(s,{Dx:()=>ea,VY:()=>et,aV:()=>es,dk:()=>er,fC:()=>X,h_:()=>ee,x8:()=>el,xz:()=>J});var a=t(3729),r=t(85222),l=t(31405),i=t(98462),n=t(99048),c=t(33183),d=t(44155),o=t(27386),x=t(31179),m=t(43234),h=t(62409),u=t(1106),p=t(71210),j=t(45904),f=t(32751),g=t(95344),y="Dialog",[v,N]=(0,i.b)(y),[b,k]=v(y),w=e=>{let{__scopeDialog:s,children:t,open:r,defaultOpen:l,onOpenChange:i,modal:d=!0}=e,o=a.useRef(null),x=a.useRef(null),[m,h]=(0,c.T)({prop:r,defaultProp:l??!1,onChange:i,caller:y});return(0,g.jsx)(b,{scope:s,triggerRef:o,contentRef:x,contentId:(0,n.M)(),titleId:(0,n.M)(),descriptionId:(0,n.M)(),open:m,onOpenChange:h,onOpenToggle:a.useCallback(()=>h(e=>!e),[h]),modal:d,children:t})};w.displayName=y;var C="DialogTrigger",S=a.forwardRef((e,s)=>{let{__scopeDialog:t,...a}=e,i=k(C,t),n=(0,l.e)(s,i.triggerRef);return(0,g.jsx)(h.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":L(i.open),...a,ref:n,onClick:(0,r.M)(e.onClick,i.onOpenToggle)})});S.displayName=C;var I="DialogPortal",[P,Z]=v(I,{forceMount:void 0}),R=e=>{let{__scopeDialog:s,forceMount:t,children:r,container:l}=e,i=k(I,s);return(0,g.jsx)(P,{scope:s,forceMount:t,children:a.Children.map(r,e=>(0,g.jsx)(m.z,{present:t||i.open,children:(0,g.jsx)(x.h,{asChild:!0,container:l,children:e})}))})};R.displayName=I;var M="DialogOverlay",A=a.forwardRef((e,s)=>{let t=Z(M,e.__scopeDialog),{forceMount:a=t.forceMount,...r}=e,l=k(M,e.__scopeDialog);return l.modal?(0,g.jsx)(m.z,{present:a||l.open,children:(0,g.jsx)(F,{...r,ref:s})}):null});A.displayName=M;var D=(0,f.Z8)("DialogOverlay.RemoveScroll"),F=a.forwardRef((e,s)=>{let{__scopeDialog:t,...a}=e,r=k(M,t);return(0,g.jsx)(p.Z,{as:D,allowPinchZoom:!0,shards:[r.contentRef],children:(0,g.jsx)(h.WV.div,{"data-state":L(r.open),...a,ref:s,style:{pointerEvents:"auto",...a.style}})})}),_="DialogContent",V=a.forwardRef((e,s)=>{let t=Z(_,e.__scopeDialog),{forceMount:a=t.forceMount,...r}=e,l=k(_,e.__scopeDialog);return(0,g.jsx)(m.z,{present:a||l.open,children:l.modal?(0,g.jsx)(z,{...r,ref:s}):(0,g.jsx)(T,{...r,ref:s})})});V.displayName=_;var z=a.forwardRef((e,s)=>{let t=k(_,e.__scopeDialog),i=a.useRef(null),n=(0,l.e)(s,t.contentRef,i);return a.useEffect(()=>{let e=i.current;if(e)return(0,j.Ry)(e)},[]),(0,g.jsx)(E,{...e,ref:n,trapFocus:t.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,r.M)(e.onCloseAutoFocus,e=>{e.preventDefault(),t.triggerRef.current?.focus()}),onPointerDownOutside:(0,r.M)(e.onPointerDownOutside,e=>{let s=e.detail.originalEvent,t=0===s.button&&!0===s.ctrlKey;(2===s.button||t)&&e.preventDefault()}),onFocusOutside:(0,r.M)(e.onFocusOutside,e=>e.preventDefault())})}),T=a.forwardRef((e,s)=>{let t=k(_,e.__scopeDialog),r=a.useRef(!1),l=a.useRef(!1);return(0,g.jsx)(E,{...e,ref:s,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:s=>{e.onCloseAutoFocus?.(s),s.defaultPrevented||(r.current||t.triggerRef.current?.focus(),s.preventDefault()),r.current=!1,l.current=!1},onInteractOutside:s=>{e.onInteractOutside?.(s),s.defaultPrevented||(r.current=!0,"pointerdown"!==s.detail.originalEvent.type||(l.current=!0));let a=s.target;t.triggerRef.current?.contains(a)&&s.preventDefault(),"focusin"===s.detail.originalEvent.type&&l.current&&s.preventDefault()}})}),E=a.forwardRef((e,s)=>{let{__scopeDialog:t,trapFocus:r,onOpenAutoFocus:i,onCloseAutoFocus:n,...c}=e,x=k(_,t),m=a.useRef(null),h=(0,l.e)(s,m);return(0,u.EW)(),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(o.M,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:i,onUnmountAutoFocus:n,children:(0,g.jsx)(d.XB,{role:"dialog",id:x.contentId,"aria-describedby":x.descriptionId,"aria-labelledby":x.titleId,"data-state":L(x.open),...c,ref:h,onDismiss:()=>x.onOpenChange(!1)})}),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(K,{titleId:x.titleId}),(0,g.jsx)(G,{contentRef:m,descriptionId:x.descriptionId})]})]})}),U="DialogTitle",Q=a.forwardRef((e,s)=>{let{__scopeDialog:t,...a}=e,r=k(U,t);return(0,g.jsx)(h.WV.h2,{id:r.titleId,...a,ref:s})});Q.displayName=U;var O="DialogDescription",q=a.forwardRef((e,s)=>{let{__scopeDialog:t,...a}=e,r=k(O,t);return(0,g.jsx)(h.WV.p,{id:r.descriptionId,...a,ref:s})});q.displayName=O;var Y="DialogClose",$=a.forwardRef((e,s)=>{let{__scopeDialog:t,...a}=e,l=k(Y,t);return(0,g.jsx)(h.WV.button,{type:"button",...a,ref:s,onClick:(0,r.M)(e.onClick,()=>l.onOpenChange(!1))})});function L(e){return e?"open":"closed"}$.displayName=Y;var B="DialogTitleWarning",[H,W]=(0,i.k)(B,{contentName:_,titleName:U,docsSlug:"dialog"}),K=({titleId:e})=>{let s=W(B),t=`\`${s.contentName}\` requires a \`${s.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${s.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${s.docsSlug}`;return a.useEffect(()=>{e&&!document.getElementById(e)&&console.error(t)},[t,e]),null},G=({contentRef:e,descriptionId:s})=>{let t=W("DialogDescriptionWarning"),r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${t.contentName}}.`;return a.useEffect(()=>{let t=e.current?.getAttribute("aria-describedby");s&&t&&!document.getElementById(s)&&console.warn(r)},[r,e,s]),null},X=w,J=S,ee=R,es=A,et=V,ea=Q,er=q,el=$},14217:(e,s,t)=>{"use strict";t.d(s,{f:()=>n});var a=t(3729),r=t(62409),l=t(95344),i=a.forwardRef((e,s)=>(0,l.jsx)(r.WV.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));i.displayName="Label";var n=i}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[1638,7948,6671,4626,7792,2506,8830,2125,5045],()=>t(59629));module.exports=a})();