"use strict";(()=>{var e={};e.id=5273,e.ids=[5273],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},2407:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>E,originalPathname:()=>v,patchFetch:()=>R,requestAsyncStorage:()=>x,routeModule:()=>q,serverHooks:()=>h,staticGenerationAsyncStorage:()=>w,staticGenerationBailout:()=>f});var r={};a.r(r),a.d(r,{GET:()=>I,POST:()=>g});var i=a(95419),o=a(69108),n=a(99678),s=a(78070),l=a(81355),u=a(3205),c=a(9108),d=a(25252),p=a(52178);let m=d.Ry({description:d.Z_().min(1,"Description is required"),quantity:d.Rx().min(1,"Quantity must be at least 1"),unitPrice:d.Rx().min(0,"Unit price must be positive"),discount:d.Rx().min(0).max(100).optional().default(0),taxRate:d.Rx().min(0).max(100).optional().default(0)}),y=d.Ry({title:d.Z_().min(1,"Title is required"),description:d.Z_().optional().nullable(),customerId:d.Z_().min(1,"Customer is required"),status:d.Km(["DRAFT","SENT","VIEWED","ACCEPTED","REJECTED","EXPIRED"]).default("DRAFT"),validUntil:d.Z_().optional().nullable(),terms:d.Z_().optional().nullable(),notes:d.Z_().optional().nullable(),paymentTerms:d.Z_().optional().nullable(),paymentDueDays:d.Rx().min(0).optional().default(30),items:d.IX(m).min(1,"At least one item is required"),taxRate:d.Rx().min(0).max(100).optional().default(0),discountType:d.Km(["PERCENTAGE","FIXED"]).optional().default("PERCENTAGE"),discountValue:d.Rx().min(0).optional().default(0)});async function I(e){try{let t=await (0,l.getServerSession)(u.L);if(!t?.user?.id)return s.Z.json({error:"Unauthorized"},{status:401});let{searchParams:a}=new URL(e.url),r=parseInt(a.get("page")||"1"),i=parseInt(a.get("limit")||"10"),o=a.get("search")||"",n=a.get("status")||"",d=a.get("customerId")||"",p=a.get("sortBy")||"createdAt",m=a.get("sortOrder")||"desc",y=(r-1)*i,I={companyId:t.user.companyId||void 0};o&&(I.OR=[{title:{contains:o,mode:"insensitive"}},{description:{contains:o,mode:"insensitive"}},{quotationNumber:{contains:o,mode:"insensitive"}},{customer:{name:{contains:o,mode:"insensitive"}}}]),n&&(I.status=n),d&&(I.customerId=d);let[g,q]=await Promise.all([c._.quotation.findMany({where:I,skip:y,take:i,orderBy:{[p]:m},include:{customer:{select:{id:!0,name:!0,email:!0,companyName:!0}},createdBy:{select:{name:!0,email:!0}},items:{orderBy:{createdAt:"asc"}},_count:{select:{activities:!0}}}}),c._.quotation.count({where:I})]),x=g.map(e=>{let t=e.items.reduce((e,t)=>{let a=t.quantity*t.unitPrice,r=a*t.discount/100,i=a-r,o=i*t.taxRate/100;return e+i+o},0),a=t,r=(a="PERCENTAGE"===e.discountType?t-t*e.discountValue/100:t-e.discountValue)*e.taxRate/100,i=a+r;return{...e,subtotal:Math.round(100*t)/100,total:Math.round(100*i)/100}});return s.Z.json({quotations:x,pagination:{page:r,limit:i,total:q,pages:Math.ceil(q/i)}})}catch(e){return console.error("Error fetching quotations:",e),s.Z.json({error:"Failed to fetch quotations"},{status:500})}}async function g(e){try{let t=await (0,l.getServerSession)(u.L);if(!t?.user?.id)return s.Z.json({error:"Unauthorized"},{status:401});let a=await e.json(),r=y.parse(a),i=new Date().getFullYear(),o=await c._.quotation.findFirst({where:{companyId:t.user.companyId||void 0,quotationNumber:{startsWith:`QUO-${i}-`}},orderBy:{quotationNumber:"desc"}}),n=1;o&&(n=parseInt(o.quotationNumber.split("-")[2])+1);let d=`QUO-${i}-${n.toString().padStart(4,"0")}`,p={...r,quotationNumber:d,companyId:t.user.companyId,createdById:t.user.id};r.validUntil&&(p.validUntil=new Date(r.validUntil));let m=await c._.$transaction(async e=>{let a=await e.quotation.create({data:{...p,items:{create:r.items.map(e=>({...e,companyId:t.user.companyId}))}},include:{customer:{select:{id:!0,name:!0,email:!0,companyName:!0}},createdBy:{select:{name:!0,email:!0}},items:{orderBy:{createdAt:"asc"}}}});return await e.activity.create({data:{type:"NOTE",title:"Quotation Created",description:`Quotation "${a.title}" (${d}) was created`,quotationId:a.id,customerId:a.customerId,companyId:t.user.companyId,createdById:t.user.id}}),a});return s.Z.json(m,{status:201})}catch(e){if(e instanceof p.jm)return s.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("Error creating quotation:",e),s.Z.json({error:"Failed to create quotation"},{status:500})}}let q=new i.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/quotations/route",pathname:"/api/quotations",filename:"route",bundlePath:"app/api/quotations/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\quotations\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:x,staticGenerationAsyncStorage:w,serverHooks:h,headerHooks:E,staticGenerationBailout:f}=q,v="/api/quotations/route";function R(){return(0,n.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:w})}},3205:(e,t,a)=>{a.d(t,{L:()=>u});var r=a(86485),i=a(10375),o=a(50694),n=a(6521),s=a.n(n),l=a(9108);let u={providers:[(0,r.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await l._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),a=t?.companyId;if(!a&&t){let e=await l._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(a=e?.id)&&await l._.user.update({where:{id:t.id},data:{companyId:a}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await s().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await l._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:a}}catch(e){return console.error("Authentication error:",e),null}}}),(0,i.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,o.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,a)=>{a.d(t,{_:()=>i});let r=require("@prisma/client"),i=globalThis.prisma??new r.PrismaClient}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[1638,6206,6521,2455,4520,5252],()=>a(2407));module.exports=r})();