"use strict";(()=>{var e={};e.id=4531,e.ids=[4531],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},32081:e=>{e.exports=require("child_process")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},25532:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>_,originalPathname:()=>f,patchFetch:()=>E,requestAsyncStorage:()=>h,routeModule:()=>y,serverHooks:()=>g,staticGenerationAsyncStorage:()=>I,staticGenerationBailout:()=>w});var s={};r.r(s),r.d(s,{GET:()=>p,POST:()=>m});var a=r(95419),n=r(69108),o=r(99678),i=r(78070),c=r(81355),u=r(3205),l=r(9108),d=r(15922);async function p(e){try{let e=await (0,c.getServerSession)(u.L);if(!e?.user?.companyId)return i.Z.json({success:!1,error:"Unauthorized"},{status:401});let t=await l._.subscription.findFirst({where:{companyId:e.user.companyId,status:{in:["ACTIVE","TRIALING","PAST_DUE","CANCELED"]}}});if(!t||!t.stripeCustomerId)return i.Z.json({success:!0,data:{paymentMethods:[],defaultPaymentMethod:null}});let r=await d.Ag.customers.retrieve(t.stripeCustomerId);if(r.deleted)return i.Z.json({success:!1,error:"Customer not found"},{status:404});let s=(await d.Ag.paymentMethods.list({customer:t.stripeCustomerId,type:"card"})).data.map(e=>({id:e.id,type:e.type,card:e.card?{brand:e.card.brand,last4:e.card.last4,expMonth:e.card.exp_month,expYear:e.card.exp_year,funding:e.card.funding,country:e.card.country}:null,created:new Date(1e3*e.created),isDefault:e.id===r.invoice_settings.default_payment_method}));return i.Z.json({success:!0,data:{paymentMethods:s,defaultPaymentMethod:r.invoice_settings.default_payment_method}})}catch(e){return console.error("Error fetching payment methods:",e),i.Z.json({success:!1,error:"Failed to fetch payment methods"},{status:500})}}async function m(e){try{let t=await (0,c.getServerSession)(u.L);if(!t?.user?.companyId)return i.Z.json({success:!1,error:"Unauthorized"},{status:401});let{action:r,paymentMethodId:s}=await e.json(),a=await l._.subscription.findFirst({where:{companyId:t.user.companyId,status:{in:["ACTIVE","TRIALING","PAST_DUE","CANCELED"]}}});if(!a||!a.stripeCustomerId)return i.Z.json({success:!1,error:"No subscription found"},{status:404});switch(r){case"set_default":return await d.Ag.customers.update(a.stripeCustomerId,{invoice_settings:{default_payment_method:s}}),i.Z.json({success:!0,message:"Default payment method updated"});case"delete":return await d.Ag.paymentMethods.detach(s),i.Z.json({success:!0,message:"Payment method deleted"});case"create_setup_intent":let n=await d.Ag.setupIntents.create({customer:a.stripeCustomerId,payment_method_types:["card"],usage:"off_session"});return i.Z.json({success:!0,data:{clientSecret:n.client_secret,setupIntentId:n.id}});default:return i.Z.json({success:!1,error:"Invalid action"},{status:400})}}catch(e){return console.error("Error handling payment method action:",e),i.Z.json({success:!1,error:"Failed to handle payment method action"},{status:500})}}let y=new a.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/billing/payment-methods/route",pathname:"/api/billing/payment-methods",filename:"route",bundlePath:"app/api/billing/payment-methods/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\billing\\payment-methods\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:h,staticGenerationAsyncStorage:I,serverHooks:g,headerHooks:_,staticGenerationBailout:w}=y,f="/api/billing/payment-methods/route";function E(){return(0,o.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:I})}},3205:(e,t,r)=>{r.d(t,{L:()=>u});var s=r(86485),a=r(10375),n=r(50694),o=r(6521),i=r.n(o),c=r(9108);let u={providers:[(0,s.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await c._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),r=t?.companyId;if(!r&&t){let e=await c._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(r=e?.id)&&await c._.user.update({where:{id:t.id},data:{companyId:r}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await i().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await c._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:r}}catch(e){return console.error("Authentication error:",e),null}}}),(0,a.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,n.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,r)=>{r.d(t,{_:()=>a});let s=require("@prisma/client"),a=globalThis.prisma??new s.PrismaClient},15922:(e,t,r)=>{r.d(t,{$t:()=>u,Ag:()=>a,FL:()=>c,R:()=>i,Sh:()=>o,hT:()=>n});var s=r(91211);if(!process.env.STRIPE_SECRET_KEY)throw Error("STRIPE_SECRET_KEY is not set in environment variables");let a=new s.Z(process.env.STRIPE_SECRET_KEY,{apiVersion:"2024-06-20",typescript:!0}),n=async e=>(await a.customers.list({email:e,limit:1})).data[0]||null,o=async e=>await a.customers.create({email:e.email,name:e.name,metadata:{companyId:e.companyId}}),i=async e=>{let t={customer:e.customerId,payment_method_types:["card"],line_items:[{price:e.priceId,quantity:1}],mode:"subscription",success_url:e.successUrl,cancel_url:e.cancelUrl,allow_promotion_codes:!0};return e.trialPeriodDays&&e.trialPeriodDays>0&&(t.subscription_data={trial_period_days:e.trialPeriodDays}),await a.checkout.sessions.create(t)},c=async e=>await a.billingPortal.sessions.create({customer:e.customerId,return_url:e.returnUrl}),u=(e,t,r)=>a.webhooks.constructEvent(e,t,r)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1638,6206,6521,2455,4520,1211],()=>r(25532));module.exports=s})();