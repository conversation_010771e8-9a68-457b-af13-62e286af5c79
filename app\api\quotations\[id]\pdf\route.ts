import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET /api/quotations/[id]/pdf - Generate PDF for quotation
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Fetch quotation with all related data
    const quotation = await prisma.quotation.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId
      },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            company: true,
            address: true,
            city: true,
            state: true,
            country: true,
            postalCode: true
          }
        },
        items: {
          orderBy: { createdAt: 'asc' }
        },
        createdBy: {
          select: {
            name: true,
            email: true
          }
        },
        company: {
          select: {
            name: true,
            email: true,
            phone: true,
            address: true,
            city: true,
            state: true,
            country: true,
            postalCode: true,
            website: true,
            logo: true
          }
        }
      }
    })

    if (!quotation) {
      return NextResponse.json({ error: 'Quotation not found' }, { status: 404 })
    }

    // Calculate totals
    const itemTotals = quotation.items.map(item => {
      const lineTotal = Number(item.quantity) * Number(item.unitPrice)
      const discountAmount = (lineTotal * Number(item.discount)) / 100
      const subtotal = lineTotal - discountAmount
      const taxAmount = (subtotal * Number(item.taxRate)) / 100
      const total = subtotal + taxAmount

      return {
        ...item,
        lineTotal: Math.round(lineTotal * 100) / 100,
        discountAmount: Math.round(discountAmount * 100) / 100,
        subtotal: Math.round(subtotal * 100) / 100,
        taxAmount: Math.round(taxAmount * 100) / 100,
        total: Math.round(total * 100) / 100
      }
    })

    const subtotal = itemTotals.reduce((sum, item) => sum + item.subtotal, 0)
    const totalDiscount = itemTotals.reduce((sum, item) => sum + item.discountAmount, 0)
    const totalTax = itemTotals.reduce((sum, item) => sum + item.taxAmount, 0)
    const grandTotal = subtotal + totalTax

    // Apply quotation-level discount
    const quotationDiscountAmount = quotation.discountType === 'PERCENTAGE'
      ? (subtotal * Number(quotation.discountValue)) / 100
      : Number(quotation.discountValue)

    const finalSubtotal = subtotal - quotationDiscountAmount
    const finalTax = (finalSubtotal * Number(quotation.taxRate)) / 100
    const finalTotal = finalSubtotal + finalTax

    // Generate HTML for PDF
    const html = generateQuotationHTML({
      quotation,
      customer: quotation.customer,
      company: quotation.company,
      items: itemTotals,
      totals: {
        subtotal: Math.round(subtotal * 100) / 100,
        totalDiscount: Math.round(totalDiscount * 100) / 100,
        quotationDiscount: Math.round(quotationDiscountAmount * 100) / 100,
        finalSubtotal: Math.round(finalSubtotal * 100) / 100,
        totalTax: Math.round(finalTax * 100) / 100,
        grandTotal: Math.round(finalTotal * 100) / 100
      }
    })

    // For now, return HTML (in production, you'd use a PDF library like Puppeteer)
    return new Response(html, {
      headers: {
        'Content-Type': 'text/html',
        'Content-Disposition': `inline; filename="quotation-${quotation.quotationNumber}.html"`
      }
    })

  } catch (error) {
    console.error('Error generating quotation PDF:', error)
    return NextResponse.json(
      { error: 'Failed to generate PDF' },
      { status: 500 }
    )
  }
}

function generateQuotationHTML(data: any) {
  const { quotation, customer, company, items, totals } = data

  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quotation ${quotation.quotationNumber}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 40px;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 20px;
        }
        .company-info {
            flex: 1;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 10px;
        }
        .quotation-info {
            text-align: right;
            flex: 1;
        }
        .quotation-title {
            font-size: 28px;
            font-weight: bold;
            color: #3b82f6;
            margin-bottom: 10px;
        }
        .quotation-number {
            font-size: 18px;
            color: #6b7280;
            margin-bottom: 5px;
        }
        .customer-section {
            margin: 30px 0;
            padding: 20px;
            background-color: #f9fafb;
            border-radius: 8px;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 15px;
            border-bottom: 1px solid #d1d5db;
            padding-bottom: 5px;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
        }
        .items-table th,
        .items-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        .items-table th {
            background-color: #f3f4f6;
            font-weight: bold;
            color: #374151;
        }
        .items-table .number {
            text-align: right;
        }
        .totals-section {
            margin-top: 30px;
            padding: 20px;
            background-color: #f9fafb;
            border-radius: 8px;
        }
        .totals-table {
            width: 100%;
            max-width: 400px;
            margin-left: auto;
        }
        .totals-table td {
            padding: 8px 12px;
            border: none;
        }
        .totals-table .label {
            text-align: right;
            font-weight: 500;
        }
        .totals-table .amount {
            text-align: right;
            font-weight: bold;
        }
        .grand-total {
            border-top: 2px solid #374151;
            font-size: 18px;
            color: #1f2937;
        }
        .terms-section {
            margin-top: 40px;
            padding: 20px;
            background-color: #fef3c7;
            border-radius: 8px;
            border-left: 4px solid #f59e0b;
        }
        .notes-section {
            margin-top: 20px;
            padding: 20px;
            background-color: #eff6ff;
            border-radius: 8px;
            border-left: 4px solid #3b82f6;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            color: #6b7280;
            font-size: 14px;
            border-top: 1px solid #e5e7eb;
            padding-top: 20px;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-draft { background-color: #f3f4f6; color: #374151; }
        .status-sent { background-color: #dbeafe; color: #1d4ed8; }
        .status-viewed { background-color: #fef3c7; color: #d97706; }
        .status-accepted { background-color: #d1fae5; color: #065f46; }
        .status-rejected { background-color: #fee2e2; color: #dc2626; }
        .status-expired { background-color: #fed7aa; color: #ea580c; }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-info">
            <div class="company-name">${company.name}</div>
            <div>${company.address || ''}</div>
            <div>${company.city || ''}, ${company.state || ''} ${company.postalCode || ''}</div>
            <div>${company.country || ''}</div>
            <div>Email: ${company.email || ''}</div>
            <div>Phone: ${company.phone || ''}</div>
            ${company.website ? `<div>Website: ${company.website}</div>` : ''}
        </div>
        <div class="quotation-info">
            <div class="quotation-title">QUOTATION</div>
            <div class="quotation-number">${quotation.quotationNumber}</div>
            <div>Date: ${new Date(quotation.createdAt).toLocaleDateString()}</div>
            ${quotation.validUntil ? `<div>Valid Until: ${new Date(quotation.validUntil).toLocaleDateString()}</div>` : ''}
            <div class="status-badge status-${quotation.status.toLowerCase()}">${quotation.status}</div>
        </div>
    </div>

    <div class="customer-section">
        <div class="section-title">Bill To:</div>
        <div><strong>${customer.name}</strong></div>
        ${customer.company ? `<div>${customer.company}</div>` : ''}
        ${customer.address ? `<div>${customer.address}</div>` : ''}
        ${customer.city || customer.state || customer.postalCode ? 
          `<div>${customer.city || ''}, ${customer.state || ''} ${customer.postalCode || ''}</div>` : ''}
        ${customer.country ? `<div>${customer.country}</div>` : ''}
        ${customer.email ? `<div>Email: ${customer.email}</div>` : ''}
        ${customer.phone ? `<div>Phone: ${customer.phone}</div>` : ''}
    </div>

    ${quotation.title ? `
    <div class="section-title">Project: ${quotation.title}</div>
    ` : ''}

    ${quotation.description ? `
    <div style="margin: 20px 0;">
        <div class="section-title">Description:</div>
        <p>${quotation.description}</p>
    </div>
    ` : ''}

    <table class="items-table">
        <thead>
            <tr>
                <th>Description</th>
                <th class="number">Qty</th>
                <th class="number">Unit Price</th>
                <th class="number">Discount</th>
                <th class="number">Tax</th>
                <th class="number">Total</th>
            </tr>
        </thead>
        <tbody>
            ${items.map(item => `
                <tr>
                    <td>
                        <strong>${item.description}</strong>
                    </td>
                    <td class="number">${Number(item.quantity)}</td>
                    <td class="number">$${Number(item.unitPrice).toFixed(2)}</td>
                    <td class="number">${Number(item.discount)}%</td>
                    <td class="number">${Number(item.taxRate)}%</td>
                    <td class="number">$${item.total.toFixed(2)}</td>
                </tr>
            `).join('')}
        </tbody>
    </table>

    <div class="totals-section">
        <table class="totals-table">
            <tr>
                <td class="label">Subtotal:</td>
                <td class="amount">$${totals.subtotal.toFixed(2)}</td>
            </tr>
            ${totals.quotationDiscount > 0 ? `
            <tr>
                <td class="label">Discount:</td>
                <td class="amount">-$${totals.quotationDiscount.toFixed(2)}</td>
            </tr>
            ` : ''}
            <tr>
                <td class="label">Tax:</td>
                <td class="amount">$${totals.totalTax.toFixed(2)}</td>
            </tr>
            <tr class="grand-total">
                <td class="label">Total:</td>
                <td class="amount">$${totals.grandTotal.toFixed(2)}</td>
            </tr>
        </table>
    </div>

    ${quotation.terms ? `
    <div class="terms-section">
        <div class="section-title">Terms & Conditions:</div>
        <p>${quotation.terms}</p>
    </div>
    ` : ''}

    ${quotation.notes ? `
    <div class="notes-section">
        <div class="section-title">Notes:</div>
        <p>${quotation.notes}</p>
    </div>
    ` : ''}

    <div class="footer">
        <p>Thank you for your business!</p>
        <p>Generated on ${new Date().toLocaleDateString()} by ${quotation.createdBy.name || quotation.createdBy.email}</p>
    </div>
</body>
</html>
  `
}
