"use strict";(()=>{var e={};e.id=1255,e.ids=[1255],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},93351:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>g,originalPathname:()=>_,patchFetch:()=>v,requestAsyncStorage:()=>p,routeModule:()=>m,serverHooks:()=>I,staticGenerationAsyncStorage:()=>y,staticGenerationBailout:()=>w});var r={};a.r(r),a.d(r,{GET:()=>d});var n=a(95419),o=a(69108),s=a(99678),c=a(78070),u=a(81355),i=a(3205),l=a(9108);async function d(e){try{let t=await (0,u.getServerSession)(i.L);if(!t?.user?.id||!t?.user?.companyId)return c.Z.json({error:"Unauthorized"},{status:401});let{searchParams:a}=new URL(e.url),r=a.get("period")||"30",n=new Date;n.setDate(n.getDate()-parseInt(r));let[o,s,d,m,p,y,I,g,w,_,v,h]=await Promise.all([l._.contract.count({where:{companyId:t.user.companyId,createdAt:{gte:n}}}),l._.contract.groupBy({by:["status"],where:{companyId:t.user.companyId,createdAt:{gte:n}},_count:{id:!0},_sum:{value:!0}}),l._.contract.groupBy({by:["type"],where:{companyId:t.user.companyId,createdAt:{gte:n}},_count:{id:!0},_sum:{value:!0}}),Promise.resolve([{month:new Date().toISOString().split("T")[0],contract_count:await l._.contract.count({where:{companyId:t.user.companyId}}),total_value:await l._.contract.aggregate({where:{companyId:t.user.companyId},_sum:{value:!0}}).then(e=>Number(e._sum.value||0)),signed_count:await l._.contract.count({where:{companyId:t.user.companyId,status:"SIGNED"}}),active_count:await l._.contract.count({where:{companyId:t.user.companyId,status:"ACTIVE"}})}]),Promise.all([l._.contract.count({where:{companyId:t.user.companyId,status:"ACTIVE"}}),l._.contract.count({where:{companyId:t.user.companyId,status:"SIGNED",createdAt:{gte:n}}}),l._.contract.count({where:{companyId:t.user.companyId,status:{in:["SENT","REVIEW"]},signatureRequired:!0}}),l._.contract.count({where:{companyId:t.user.companyId,autoRenewal:!0,status:"ACTIVE"}})]),l._.contract.findMany({where:{companyId:t.user.companyId,createdAt:{gte:n},value:{not:null}},include:{customer:{select:{id:!0,name:!0,company:!0,email:!0}},createdBy:{select:{name:!0,email:!0}}},orderBy:{value:"desc"},take:10}),l._.contract.findMany({where:{companyId:t.user.companyId,createdAt:{gte:new Date(Date.now()-6048e5)}},include:{customer:{select:{id:!0,name:!0,company:!0}},createdBy:{select:{name:!0,email:!0}}},orderBy:{createdAt:"desc"},take:10}),Promise.all([l._.contract.count({where:{companyId:t.user.companyId,status:"ACTIVE",endDate:{gte:new Date,lte:new Date(Date.now()+2592e6)}}}),l._.contract.count({where:{companyId:t.user.companyId,status:"ACTIVE",endDate:{gte:new Date,lte:new Date(Date.now()+7776e6)}}}),l._.contract.aggregate({where:{companyId:t.user.companyId,autoRenewal:!0,status:"ACTIVE"},_sum:{value:!0}}),Promise.resolve([{avg_duration_days:365}])]),l._.contract.groupBy({by:["customerId"],where:{companyId:t.user.companyId,createdAt:{gte:n}},_count:{id:!0},_sum:{value:!0},orderBy:{_sum:{value:"desc"}},take:10}),Promise.all([l._.contract.aggregate({where:{companyId:t.user.companyId,createdAt:{gte:n}},_sum:{value:!0}}),l._.contract.aggregate({where:{companyId:t.user.companyId,status:"ACTIVE"},_sum:{value:!0}}),l._.contract.aggregate({where:{companyId:t.user.companyId,createdAt:{gte:n},value:{not:null}},_avg:{value:!0}})]),l._.contract.findMany({where:{companyId:t.user.companyId,status:"ACTIVE",endDate:{gte:new Date,lte:new Date(Date.now()+2592e6)}},include:{customer:{select:{id:!0,name:!0,company:!0,email:!0}}},orderBy:{endDate:"asc"},take:10}),Promise.all([Promise.resolve([{avg_days_to_signature:7.5}]),Promise.resolve([{signature_rate:await l._.contract.count({where:{companyId:t.user.companyId,status:{in:["SIGNED","ACTIVE"]}}}).then(async e=>{let a=await l._.contract.count({where:{companyId:t.user.companyId,status:{in:["SENT","REVIEW","SIGNED","ACTIVE"]}}});return a>0?e/a*100:0})}])])]),D=w.map(e=>e.customerId).filter(Boolean),E=await l._.customer.findMany({where:{id:{in:D},companyId:t.user.companyId},select:{id:!0,name:!0,company:!0,email:!0}}),A=w.map(e=>({customer:E.find(t=>t.id===e.customerId)||{id:e.customerId,name:"Unknown",company:null,email:null},contractCount:e._count.id,totalValue:Number(e._sum.value||0)})),[b,x,T,f]=p,[N,C,q,B]=g,[S,P,V]=_,[R,G]=h,j=B[0]?.avg_duration_days||0,k=R[0]?.avg_days_to_signature||0,M=G[0]?.signature_rate||0;return c.Z.json({summary:{totalContracts:o,activeContracts:b,signedContracts:x,pendingSignatures:T,autoRenewalContracts:f,totalValue:Number(S._sum.value||0),activeValue:Number(P._sum.value||0),averageValue:Number(V._avg.value||0),avgDurationDays:Math.round(j),avgSignatureDays:Math.round(100*k)/100,signatureRate:Math.round(100*M)/100},contractsByStatus:s.map(e=>({status:e.status,count:e._count.id,value:Number(e._sum.value||0)})),contractsByType:d.map(e=>({type:e.type,count:e._count.id,value:Number(e._sum.value||0)})),contractsByMonth:m,renewalAnalysis:{expiring30Days:N,expiring90Days:C,autoRenewalValue:Number(q._sum.value||0),avgDurationDays:Math.round(j)},topContracts:y.map(e=>({id:e.id,contractNumber:e.contractNumber,title:e.title,value:Number(e.value||0),status:e.status,type:e.type,customer:e.customer,createdBy:e.createdBy,createdAt:e.createdAt,startDate:e.startDate,endDate:e.endDate})),recentContracts:I.map(e=>({id:e.id,contractNumber:e.contractNumber,title:e.title,value:Number(e.value||0),status:e.status,type:e.type,customer:e.customer,createdBy:e.createdBy,createdAt:e.createdAt})),customerContracts:A,expiringContracts:v.map(e=>({id:e.id,contractNumber:e.contractNumber,title:e.title,value:Number(e.value||0),endDate:e.endDate,daysUntilExpiry:Math.ceil((new Date(e.endDate).getTime()-new Date().getTime())/864e5),autoRenewal:e.autoRenewal,customer:e.customer})),period:parseInt(r)})}catch(e){return console.error("Error fetching contract analytics:",e),c.Z.json({error:"Failed to fetch contract analytics"},{status:500})}}let m=new n.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/contracts/analytics/route",pathname:"/api/contracts/analytics",filename:"route",bundlePath:"app/api/contracts/analytics/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\contracts\\analytics\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:p,staticGenerationAsyncStorage:y,serverHooks:I,headerHooks:g,staticGenerationBailout:w}=m,_="/api/contracts/analytics/route";function v(){return(0,s.patchFetch)({serverHooks:I,staticGenerationAsyncStorage:y})}},3205:(e,t,a)=>{a.d(t,{L:()=>i});var r=a(86485),n=a(10375),o=a(50694),s=a(6521),c=a.n(s),u=a(9108);let i={providers:[(0,r.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await u._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),a=t?.companyId;if(!a&&t){let e=await u._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(a=e?.id)&&await u._.user.update({where:{id:t.id},data:{companyId:a}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await c().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await u._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:a}}catch(e){return console.error("Authentication error:",e),null}}}),(0,n.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,o.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,a)=>{a.d(t,{_:()=>n});let r=require("@prisma/client"),n=globalThis.prisma??new r.PrismaClient}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[1638,6206,6521,2455,4520],()=>a(93351));module.exports=r})();