'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { redirect } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import {
  Monitor,
  Save,
  RefreshCw,
  Eye,
  Upload,
  Plus,
  Trash2,
  Edit,
  Image as ImageIcon,
  Type,
  Layout,
  Star,
  Users,
  CheckCircle,
  ArrowRight,
  Quote
} from 'lucide-react'
import { toast } from 'react-hot-toast'

interface CMSContent {
  // Hero Section
  hero: {
    enabled: boolean
    title: string
    subtitle: string
    description: string
    primaryCTA: {
      text: string
      link: string
    }
    secondaryCTA: {
      text: string
      link: string
    }
    backgroundImage: string
    backgroundVideo: string
  }
  
  // Features Section
  features: {
    enabled: boolean
    title: string
    subtitle: string
    items: Array<{
      id: string
      title: string
      description: string
      icon: string
      image: string
    }>
  }
  
  // Pricing Section
  pricing: {
    enabled: boolean
    title: string
    subtitle: string
    showPricingTable: boolean
    customMessage: string
  }
  
  // Testimonials Section
  testimonials: {
    enabled: boolean
    title: string
    subtitle: string
    items: Array<{
      id: string
      name: string
      role: string
      company: string
      content: string
      avatar: string
      rating: number
    }>
  }
  
  // FAQ Section
  faq: {
    enabled: boolean
    title: string
    subtitle: string
    items: Array<{
      id: string
      question: string
      answer: string
    }>
  }
  
  // CTA Section
  cta: {
    enabled: boolean
    title: string
    description: string
    buttonText: string
    buttonLink: string
    backgroundImage: string
  }
  
  // Footer
  footer: {
    enabled: boolean
    companyDescription: string
    links: Array<{
      id: string
      title: string
      items: Array<{
        id: string
        text: string
        link: string
      }>
    }>
    socialLinks: {
      twitter: string
      linkedin: string
      facebook: string
      instagram: string
    }
    copyrightText: string
  }
  
  // SEO
  seo: {
    title: string
    description: string
    keywords: string
    ogImage: string
  }
}

export default function CMSPage() {
  const { data: session, status } = useSession()
  const [content, setContent] = useState<CMSContent>({
    hero: {
      enabled: true,
      title: 'Build Your SaaS Business',
      subtitle: 'The Complete Platform',
      description: 'Everything you need to launch, grow, and scale your SaaS business. From customer management to billing, we\'ve got you covered.',
      primaryCTA: {
        text: 'Start Free Trial',
        link: '/auth/signup'
      },
      secondaryCTA: {
        text: 'Watch Demo',
        link: '/demo'
      },
      backgroundImage: '',
      backgroundVideo: ''
    },
    features: {
      enabled: true,
      title: 'Everything You Need',
      subtitle: 'Powerful Features',
      items: [
        {
          id: '1',
          title: 'Customer Management',
          description: 'Manage your customers, track interactions, and build lasting relationships.',
          icon: 'users',
          image: ''
        },
        {
          id: '2',
          title: 'Subscription Billing',
          description: 'Automated billing, invoicing, and payment processing for recurring revenue.',
          icon: 'credit-card',
          image: ''
        },
        {
          id: '3',
          title: 'Analytics & Reports',
          description: 'Comprehensive analytics to track your business performance and growth.',
          icon: 'bar-chart',
          image: ''
        }
      ]
    },
    pricing: {
      enabled: true,
      title: 'Simple, Transparent Pricing',
      subtitle: 'Choose the plan that fits your needs',
      showPricingTable: true,
      customMessage: ''
    },
    testimonials: {
      enabled: true,
      title: 'What Our Customers Say',
      subtitle: 'Trusted by thousands of businesses',
      items: [
        {
          id: '1',
          name: 'John Smith',
          role: 'CEO',
          company: 'TechCorp',
          content: 'This platform has transformed how we manage our SaaS business. Highly recommended!',
          avatar: '',
          rating: 5
        }
      ]
    },
    faq: {
      enabled: true,
      title: 'Frequently Asked Questions',
      subtitle: 'Everything you need to know',
      items: [
        {
          id: '1',
          question: 'How do I get started?',
          answer: 'Simply sign up for a free trial and follow our onboarding guide to set up your account.'
        },
        {
          id: '2',
          question: 'Can I cancel anytime?',
          answer: 'Yes, you can cancel your subscription at any time. No long-term contracts or cancellation fees.'
        }
      ]
    },
    cta: {
      enabled: true,
      title: 'Ready to Get Started?',
      description: 'Join thousands of businesses already using our platform to grow their SaaS.',
      buttonText: 'Start Your Free Trial',
      buttonLink: '/auth/signup',
      backgroundImage: ''
    },
    footer: {
      enabled: true,
      companyDescription: 'The complete SaaS platform for modern businesses.',
      links: [
        {
          id: '1',
          title: 'Product',
          items: [
            { id: '1', text: 'Features', link: '/features' },
            { id: '2', text: 'Pricing', link: '/pricing' },
            { id: '3', text: 'Security', link: '/security' }
          ]
        },
        {
          id: '2',
          title: 'Company',
          items: [
            { id: '1', text: 'About', link: '/about' },
            { id: '2', text: 'Blog', link: '/blog' },
            { id: '3', text: 'Careers', link: '/careers' }
          ]
        }
      ],
      socialLinks: {
        twitter: '',
        linkedin: '',
        facebook: '',
        instagram: ''
      },
      copyrightText: '© 2024 Your Company. All rights reserved.'
    },
    seo: {
      title: 'SaaS Platform - Build Your Business',
      description: 'The complete SaaS platform for modern businesses. Customer management, billing, analytics, and more.',
      keywords: 'saas, platform, business, customer management, billing, analytics',
      ogImage: ''
    }
  })
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [previewMode, setPreviewMode] = useState(false)

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (status === 'unauthenticated') {
    redirect('/auth/signin')
  }

  if (session?.user?.role !== 'SUPER_ADMIN') {
    redirect('/dashboard')
  }

  const fetchContent = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/super-admin/cms', {
        credentials: 'include'
      })
      const data = await response.json()
      
      if (data.success) {
        setContent({ ...content, ...data.content })
      }
    } catch (error) {
      console.error('Error fetching CMS content:', error)
      toast.error('Failed to load CMS content')
    } finally {
      setLoading(false)
    }
  }

  const saveContent = async () => {
    try {
      setSaving(true)
      const response = await fetch('/api/super-admin/cms', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify(content)
      })

      const data = await response.json()
      
      if (data.success) {
        toast.success('CMS content saved successfully')
      } else {
        toast.error(data.error || 'Failed to save CMS content')
      }
    } catch (error) {
      console.error('Error saving CMS content:', error)
      toast.error('Failed to save CMS content')
    } finally {
      setSaving(false)
    }
  }

  useEffect(() => {
    fetchContent()
  }, [])

  const updateContent = (section: keyof CMSContent, key: string, value: any) => {
    setContent(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value
      }
    }))
  }

  // Feature management functions
  const addFeature = () => {
    const newFeature = {
      id: Date.now().toString(),
      title: 'New Feature',
      description: 'Feature description',
      icon: 'star',
      image: ''
    }
    setContent(prev => ({
      ...prev,
      features: {
        ...prev.features,
        items: [...(prev.features?.items || []), newFeature]
      }
    }))
  }

  const removeFeature = (id: string) => {
    setContent(prev => ({
      ...prev,
      features: {
        ...prev.features,
        items: prev.features?.items?.filter(item => item.id !== id) || []
      }
    }))
  }

  const updateFeature = (id: string, key: string, value: any) => {
    setContent(prev => ({
      ...prev,
      features: {
        ...prev.features,
        items: prev.features?.items?.map(item =>
          item.id === id ? { ...item, [key]: value } : item
        ) || []
      }
    }))
  }

  // Testimonial management functions
  const addTestimonial = () => {
    const newTestimonial = {
      id: Date.now().toString(),
      name: 'Customer Name',
      role: 'CEO',
      company: 'Company Name',
      content: 'Great testimonial content...',
      avatar: '',
      rating: 5
    }
    setContent(prev => ({
      ...prev,
      testimonials: {
        ...prev.testimonials,
        items: [...(prev.testimonials?.items || []), newTestimonial]
      }
    }))
  }

  const removeTestimonial = (id: string) => {
    setContent(prev => ({
      ...prev,
      testimonials: {
        ...prev.testimonials,
        items: prev.testimonials?.items?.filter(item => item.id !== id) || []
      }
    }))
  }

  const updateTestimonial = (id: string, key: string, value: any) => {
    setContent(prev => ({
      ...prev,
      testimonials: {
        ...prev.testimonials,
        items: prev.testimonials?.items?.map(item =>
          item.id === id ? { ...item, [key]: value } : item
        ) || []
      }
    }))
  }

  // FAQ management functions
  const addFAQ = () => {
    const newFAQ = {
      id: Date.now().toString(),
      question: 'New question?',
      answer: 'Answer to the question.'
    }
    setContent(prev => ({
      ...prev,
      faq: {
        ...prev.faq,
        items: [...(prev.faq?.items || []), newFAQ]
      }
    }))
  }

  const removeFAQ = (id: string) => {
    setContent(prev => ({
      ...prev,
      faq: {
        ...prev.faq,
        items: prev.faq?.items?.filter(item => item.id !== id) || []
      }
    }))
  }

  const updateFAQ = (id: string, key: string, value: any) => {
    setContent(prev => ({
      ...prev,
      faq: {
        ...prev.faq,
        items: prev.faq?.items?.map(item =>
          item.id === id ? { ...item, [key]: value } : item
        ) || []
      }
    }))
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <div className="flex items-center space-x-3">
            <Monitor className="h-8 w-8 text-indigo-600" />
            <h1 className="text-3xl font-bold text-gray-900">Landing Page CMS</h1>
          </div>
          <p className="text-gray-500 mt-1">Manage your landing page content and layout</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={fetchContent} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline" onClick={() => setPreviewMode(!previewMode)}>
            <Eye className="h-4 w-4 mr-2" />
            {previewMode ? 'Edit Mode' : 'Preview'}
          </Button>
          <Button onClick={saveContent} disabled={saving}>
            <Save className={`h-4 w-4 mr-2 ${saving ? 'animate-spin' : ''}`} />
            Save Changes
          </Button>
        </div>
      </div>

      {/* Content Management */}
      <Tabs defaultValue="hero" className="space-y-6">
        <TabsList className="grid w-full grid-cols-7">
          <TabsTrigger value="hero">Hero</TabsTrigger>
          <TabsTrigger value="features">Features</TabsTrigger>
          <TabsTrigger value="pricing">Pricing</TabsTrigger>
          <TabsTrigger value="testimonials">Testimonials</TabsTrigger>
          <TabsTrigger value="faq">FAQ</TabsTrigger>
          <TabsTrigger value="cta">CTA</TabsTrigger>
          <TabsTrigger value="seo">SEO</TabsTrigger>
        </TabsList>

        {/* Hero Section */}
        <TabsContent value="hero">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Layout className="h-5 w-5 mr-2" />
                Hero Section
              </CardTitle>
              <CardDescription>
                Configure the main hero section of your landing page
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <Label htmlFor="heroEnabled" className="text-base font-medium">
                    Enable Hero Section
                  </Label>
                  <p className="text-sm text-gray-500">
                    Show the hero section on your landing page
                  </p>
                </div>
                <Switch
                  id="heroEnabled"
                  checked={content.hero.enabled}
                  onCheckedChange={(checked) => updateContent('hero', 'enabled', checked)}
                />
              </div>

              {content.hero.enabled && (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="heroTitle">Main Title</Label>
                      <Input
                        id="heroTitle"
                        value={content.hero.title}
                        onChange={(e) => updateContent('hero', 'title', e.target.value)}
                        placeholder="Your main headline"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="heroSubtitle">Subtitle</Label>
                      <Input
                        id="heroSubtitle"
                        value={content.hero.subtitle}
                        onChange={(e) => updateContent('hero', 'subtitle', e.target.value)}
                        placeholder="Supporting headline"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="heroDescription">Description</Label>
                    <Textarea
                      id="heroDescription"
                      value={content.hero.description}
                      onChange={(e) => updateContent('hero', 'description', e.target.value)}
                      placeholder="Describe your product or service"
                      rows={3}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="primaryCTAText">Primary Button Text</Label>
                      <Input
                        id="primaryCTAText"
                        value={content.hero.primaryCTA.text}
                        onChange={(e) => updateContent('hero', 'primaryCTA', { ...content.hero.primaryCTA, text: e.target.value })}
                        placeholder="Get Started"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="primaryCTALink">Primary Button Link</Label>
                      <Input
                        id="primaryCTALink"
                        value={content.hero.primaryCTA.link}
                        onChange={(e) => updateContent('hero', 'primaryCTA', { ...content.hero.primaryCTA, link: e.target.value })}
                        placeholder="/auth/signup"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="secondaryCTAText">Secondary Button Text</Label>
                      <Input
                        id="secondaryCTAText"
                        value={content.hero.secondaryCTA.text}
                        onChange={(e) => updateContent('hero', 'secondaryCTA', { ...content.hero.secondaryCTA, text: e.target.value })}
                        placeholder="Learn More"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="secondaryCTALink">Secondary Button Link</Label>
                      <Input
                        id="secondaryCTALink"
                        value={content.hero.secondaryCTA.link}
                        onChange={(e) => updateContent('hero', 'secondaryCTA', { ...content.hero.secondaryCTA, link: e.target.value })}
                        placeholder="/demo"
                      />
                    </div>
                  </div>

                  {/* Background Media */}
                  <div className="space-y-4">
                    <Label className="text-base font-medium">Background Media</Label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="heroBackgroundImage">Background Image URL</Label>
                        <Input
                          id="heroBackgroundImage"
                          value={content.hero.backgroundImage}
                          onChange={(e) => updateContent('hero', 'backgroundImage', e.target.value)}
                          placeholder="https://example.com/hero-bg.jpg"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="heroBackgroundVideo">Background Video URL</Label>
                        <Input
                          id="heroBackgroundVideo"
                          value={content.hero.backgroundVideo}
                          onChange={(e) => updateContent('hero', 'backgroundVideo', e.target.value)}
                          placeholder="https://example.com/hero-video.mp4"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Features Section */}
        <TabsContent value="features">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Star className="h-5 w-5 mr-2" />
                Features Section
              </CardTitle>
              <CardDescription>
                Configure the features showcase section
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <Label htmlFor="featuresEnabled" className="text-base font-medium">
                    Enable Features Section
                  </Label>
                  <p className="text-sm text-gray-500">
                    Show the features section on your landing page
                  </p>
                </div>
                <Switch
                  id="featuresEnabled"
                  checked={content.features?.enabled || false}
                  onCheckedChange={(checked) => updateContent('features', 'enabled', checked)}
                />
              </div>

              {content.features?.enabled && (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="featuresTitle">Section Title</Label>
                      <Input
                        id="featuresTitle"
                        value={content.features?.title || ''}
                        onChange={(e) => updateContent('features', 'title', e.target.value)}
                        placeholder="Everything You Need"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="featuresSubtitle">Section Subtitle</Label>
                      <Input
                        id="featuresSubtitle"
                        value={content.features?.subtitle || ''}
                        onChange={(e) => updateContent('features', 'subtitle', e.target.value)}
                        placeholder="Powerful Features"
                      />
                    </div>
                  </div>

                  {/* Feature Items */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Label className="text-base font-medium">Feature Items</Label>
                      <Button onClick={addFeature} size="sm">
                        <Plus className="h-4 w-4 mr-2" />
                        Add Feature
                      </Button>
                    </div>

                    <div className="space-y-4">
                      {content.features?.items?.map((feature, index) => (
                        <Card key={feature.id} className="p-4">
                          <div className="flex items-start justify-between mb-4">
                            <h4 className="font-medium">Feature {index + 1}</h4>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeFeature(feature.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label>Feature Title</Label>
                              <Input
                                value={feature.title}
                                onChange={(e) => updateFeature(feature.id, 'title', e.target.value)}
                                placeholder="Feature title"
                              />
                            </div>
                            <div className="space-y-2">
                              <Label>Icon Name</Label>
                              <select
                                value={feature.icon}
                                onChange={(e) => updateFeature(feature.id, 'icon', e.target.value)}
                                className="w-full p-2 border border-gray-300 rounded-md"
                              >
                                <option value="users">Users</option>
                                <option value="credit-card">Credit Card</option>
                                <option value="bar-chart">Bar Chart</option>
                                <option value="building">Building</option>
                                <option value="shield">Shield</option>
                                <option value="globe">Globe</option>
                                <option value="zap">Zap</option>
                                <option value="file-text">File Text</option>
                              </select>
                            </div>
                          </div>
                          <div className="space-y-2 mt-4">
                            <Label>Feature Description</Label>
                            <Textarea
                              value={feature.description}
                              onChange={(e) => updateFeature(feature.id, 'description', e.target.value)}
                              placeholder="Feature description"
                              rows={2}
                            />
                          </div>
                        </Card>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Testimonials Section */}
        <TabsContent value="testimonials">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Quote className="h-5 w-5 mr-2" />
                Testimonials Section
              </CardTitle>
              <CardDescription>
                Manage customer testimonials and reviews
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <Label htmlFor="testimonialsEnabled" className="text-base font-medium">
                    Enable Testimonials Section
                  </Label>
                  <p className="text-sm text-gray-500">
                    Show customer testimonials on your landing page
                  </p>
                </div>
                <Switch
                  id="testimonialsEnabled"
                  checked={content.testimonials?.enabled || false}
                  onCheckedChange={(checked) => updateContent('testimonials', 'enabled', checked)}
                />
              </div>

              {content.testimonials?.enabled && (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="testimonialsTitle">Section Title</Label>
                      <Input
                        id="testimonialsTitle"
                        value={content.testimonials?.title || ''}
                        onChange={(e) => updateContent('testimonials', 'title', e.target.value)}
                        placeholder="What Our Customers Say"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="testimonialsSubtitle">Section Subtitle</Label>
                      <Input
                        id="testimonialsSubtitle"
                        value={content.testimonials?.subtitle || ''}
                        onChange={(e) => updateContent('testimonials', 'subtitle', e.target.value)}
                        placeholder="Trusted by thousands of businesses"
                      />
                    </div>
                  </div>

                  {/* Testimonial Items */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Label className="text-base font-medium">Testimonials</Label>
                      <Button onClick={addTestimonial} size="sm">
                        <Plus className="h-4 w-4 mr-2" />
                        Add Testimonial
                      </Button>
                    </div>

                    <div className="space-y-4">
                      {content.testimonials?.items?.map((testimonial, index) => (
                        <Card key={testimonial.id} className="p-4">
                          <div className="flex items-start justify-between mb-4">
                            <h4 className="font-medium">Testimonial {index + 1}</h4>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeTestimonial(testimonial.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div className="space-y-2">
                              <Label>Customer Name</Label>
                              <Input
                                value={testimonial.name}
                                onChange={(e) => updateTestimonial(testimonial.id, 'name', e.target.value)}
                                placeholder="John Smith"
                              />
                            </div>
                            <div className="space-y-2">
                              <Label>Role</Label>
                              <Input
                                value={testimonial.role}
                                onChange={(e) => updateTestimonial(testimonial.id, 'role', e.target.value)}
                                placeholder="CEO"
                              />
                            </div>
                            <div className="space-y-2">
                              <Label>Company</Label>
                              <Input
                                value={testimonial.company}
                                onChange={(e) => updateTestimonial(testimonial.id, 'company', e.target.value)}
                                placeholder="TechCorp"
                              />
                            </div>
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                            <div className="space-y-2">
                              <Label>Rating (1-5)</Label>
                              <Input
                                type="number"
                                min="1"
                                max="5"
                                value={testimonial.rating}
                                onChange={(e) => updateTestimonial(testimonial.id, 'rating', parseInt(e.target.value) || 5)}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label>Avatar URL (optional)</Label>
                              <Input
                                value={testimonial.avatar}
                                onChange={(e) => updateTestimonial(testimonial.id, 'avatar', e.target.value)}
                                placeholder="https://example.com/avatar.jpg"
                              />
                            </div>
                          </div>
                          <div className="space-y-2 mt-4">
                            <Label>Testimonial Content</Label>
                            <Textarea
                              value={testimonial.content}
                              onChange={(e) => updateTestimonial(testimonial.id, 'content', e.target.value)}
                              placeholder="This platform has transformed our business..."
                              rows={3}
                            />
                          </div>
                        </Card>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* FAQ Section */}
        <TabsContent value="faq">
          <Card>
            <CardHeader>
              <CardTitle>FAQ Section</CardTitle>
              <CardDescription>
                Manage frequently asked questions
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <Label htmlFor="faqEnabled" className="text-base font-medium">
                    Enable FAQ Section
                  </Label>
                  <p className="text-sm text-gray-500">
                    Show FAQ section on your landing page
                  </p>
                </div>
                <Switch
                  id="faqEnabled"
                  checked={content.faq?.enabled || false}
                  onCheckedChange={(checked) => updateContent('faq', 'enabled', checked)}
                />
              </div>

              {content.faq?.enabled && (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="faqTitle">Section Title</Label>
                      <Input
                        id="faqTitle"
                        value={content.faq?.title || ''}
                        onChange={(e) => updateContent('faq', 'title', e.target.value)}
                        placeholder="Frequently Asked Questions"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="faqSubtitle">Section Subtitle</Label>
                      <Input
                        id="faqSubtitle"
                        value={content.faq?.subtitle || ''}
                        onChange={(e) => updateContent('faq', 'subtitle', e.target.value)}
                        placeholder="Everything you need to know"
                      />
                    </div>
                  </div>

                  {/* FAQ Items */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Label className="text-base font-medium">FAQ Items</Label>
                      <Button onClick={addFAQ} size="sm">
                        <Plus className="h-4 w-4 mr-2" />
                        Add FAQ
                      </Button>
                    </div>

                    <div className="space-y-4">
                      {content.faq?.items?.map((faq, index) => (
                        <Card key={faq.id} className="p-4">
                          <div className="flex items-start justify-between mb-4">
                            <h4 className="font-medium">FAQ {index + 1}</h4>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeFAQ(faq.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                          <div className="space-y-4">
                            <div className="space-y-2">
                              <Label>Question</Label>
                              <Input
                                value={faq.question}
                                onChange={(e) => updateFAQ(faq.id, 'question', e.target.value)}
                                placeholder="What is your question?"
                              />
                            </div>
                            <div className="space-y-2">
                              <Label>Answer</Label>
                              <Textarea
                                value={faq.answer}
                                onChange={(e) => updateFAQ(faq.id, 'answer', e.target.value)}
                                placeholder="Provide a detailed answer..."
                                rows={3}
                              />
                            </div>
                          </div>
                        </Card>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* CTA Section */}
        <TabsContent value="cta">
          <Card>
            <CardHeader>
              <CardTitle>Call-to-Action Section</CardTitle>
              <CardDescription>
                Configure the final call-to-action section
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <Label htmlFor="ctaEnabled" className="text-base font-medium">
                    Enable CTA Section
                  </Label>
                  <p className="text-sm text-gray-500">
                    Show the call-to-action section on your landing page
                  </p>
                </div>
                <Switch
                  id="ctaEnabled"
                  checked={content.cta?.enabled || false}
                  onCheckedChange={(checked) => updateContent('cta', 'enabled', checked)}
                />
              </div>

              {content.cta?.enabled && (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="ctaTitle">CTA Title</Label>
                      <Input
                        id="ctaTitle"
                        value={content.cta?.title || ''}
                        onChange={(e) => updateContent('cta', 'title', e.target.value)}
                        placeholder="Ready to Get Started?"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="ctaButtonText">Button Text</Label>
                      <Input
                        id="ctaButtonText"
                        value={content.cta?.buttonText || ''}
                        onChange={(e) => updateContent('cta', 'buttonText', e.target.value)}
                        placeholder="Start Your Free Trial"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="ctaDescription">CTA Description</Label>
                    <Textarea
                      id="ctaDescription"
                      value={content.cta?.description || ''}
                      onChange={(e) => updateContent('cta', 'description', e.target.value)}
                      placeholder="Join thousands of businesses already using our platform..."
                      rows={3}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="ctaButtonLink">Button Link</Label>
                      <Input
                        id="ctaButtonLink"
                        value={content.cta?.buttonLink || ''}
                        onChange={(e) => updateContent('cta', 'buttonLink', e.target.value)}
                        placeholder="/auth/signup"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="ctaBackgroundImage">Background Image URL</Label>
                      <Input
                        id="ctaBackgroundImage"
                        value={content.cta?.backgroundImage || ''}
                        onChange={(e) => updateContent('cta', 'backgroundImage', e.target.value)}
                        placeholder="https://example.com/cta-bg.jpg"
                      />
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* SEO Section */}
        <TabsContent value="seo">
          <Card>
            <CardHeader>
              <CardTitle>SEO Settings</CardTitle>
              <CardDescription>
                Configure SEO meta tags and social sharing
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="seoTitle">Page Title</Label>
                  <Input
                    id="seoTitle"
                    value={content.seo?.title || ''}
                    onChange={(e) => updateContent('seo', 'title', e.target.value)}
                    placeholder="SaaS Platform - Build Your Business"
                  />
                  <p className="text-xs text-gray-500">Recommended: 50-60 characters</p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="seoKeywords">Keywords</Label>
                  <Input
                    id="seoKeywords"
                    value={content.seo?.keywords || ''}
                    onChange={(e) => updateContent('seo', 'keywords', e.target.value)}
                    placeholder="saas, platform, business, management"
                  />
                  <p className="text-xs text-gray-500">Comma-separated keywords</p>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="seoDescription">Meta Description</Label>
                <Textarea
                  id="seoDescription"
                  value={content.seo?.description || ''}
                  onChange={(e) => updateContent('seo', 'description', e.target.value)}
                  placeholder="The complete SaaS platform for modern businesses..."
                  rows={3}
                />
                <p className="text-xs text-gray-500">Recommended: 150-160 characters</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="seoOgImage">Open Graph Image URL</Label>
                <Input
                  id="seoOgImage"
                  value={content.seo?.ogImage || ''}
                  onChange={(e) => updateContent('seo', 'ogImage', e.target.value)}
                  placeholder="https://example.com/og-image.jpg"
                />
                <p className="text-xs text-gray-500">Recommended: 1200x630px</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
