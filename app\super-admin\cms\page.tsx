'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { redirect } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { 
  Monitor, 
  Save, 
  RefreshCw, 
  Eye,
  Upload,
  Plus,
  Trash2,
  Edit,
  Image as ImageIcon,
  Type,
  Layout,
  Star,
  Users,
  CheckCircle,
  ArrowRight
} from 'lucide-react'
import { toast } from 'react-hot-toast'

interface CMSContent {
  // Hero Section
  hero: {
    enabled: boolean
    title: string
    subtitle: string
    description: string
    primaryCTA: {
      text: string
      link: string
    }
    secondaryCTA: {
      text: string
      link: string
    }
    backgroundImage: string
    backgroundVideo: string
  }
  
  // Features Section
  features: {
    enabled: boolean
    title: string
    subtitle: string
    items: Array<{
      id: string
      title: string
      description: string
      icon: string
      image: string
    }>
  }
  
  // Pricing Section
  pricing: {
    enabled: boolean
    title: string
    subtitle: string
    showPricingTable: boolean
    customMessage: string
  }
  
  // Testimonials Section
  testimonials: {
    enabled: boolean
    title: string
    subtitle: string
    items: Array<{
      id: string
      name: string
      role: string
      company: string
      content: string
      avatar: string
      rating: number
    }>
  }
  
  // FAQ Section
  faq: {
    enabled: boolean
    title: string
    subtitle: string
    items: Array<{
      id: string
      question: string
      answer: string
    }>
  }
  
  // CTA Section
  cta: {
    enabled: boolean
    title: string
    description: string
    buttonText: string
    buttonLink: string
    backgroundImage: string
  }
  
  // Footer
  footer: {
    enabled: boolean
    companyDescription: string
    links: Array<{
      id: string
      title: string
      items: Array<{
        id: string
        text: string
        link: string
      }>
    }>
    socialLinks: {
      twitter: string
      linkedin: string
      facebook: string
      instagram: string
    }
    copyrightText: string
  }
  
  // SEO
  seo: {
    title: string
    description: string
    keywords: string
    ogImage: string
  }
}

export default function CMSPage() {
  const { data: session, status } = useSession()
  const [content, setContent] = useState<CMSContent>({
    hero: {
      enabled: true,
      title: 'Build Your SaaS Business',
      subtitle: 'The Complete Platform',
      description: 'Everything you need to launch, grow, and scale your SaaS business. From customer management to billing, we\'ve got you covered.',
      primaryCTA: {
        text: 'Start Free Trial',
        link: '/auth/signup'
      },
      secondaryCTA: {
        text: 'Watch Demo',
        link: '/demo'
      },
      backgroundImage: '',
      backgroundVideo: ''
    },
    features: {
      enabled: true,
      title: 'Everything You Need',
      subtitle: 'Powerful Features',
      items: [
        {
          id: '1',
          title: 'Customer Management',
          description: 'Manage your customers, track interactions, and build lasting relationships.',
          icon: 'users',
          image: ''
        },
        {
          id: '2',
          title: 'Subscription Billing',
          description: 'Automated billing, invoicing, and payment processing for recurring revenue.',
          icon: 'credit-card',
          image: ''
        },
        {
          id: '3',
          title: 'Analytics & Reports',
          description: 'Comprehensive analytics to track your business performance and growth.',
          icon: 'bar-chart',
          image: ''
        }
      ]
    },
    pricing: {
      enabled: true,
      title: 'Simple, Transparent Pricing',
      subtitle: 'Choose the plan that fits your needs',
      showPricingTable: true,
      customMessage: ''
    },
    testimonials: {
      enabled: true,
      title: 'What Our Customers Say',
      subtitle: 'Trusted by thousands of businesses',
      items: [
        {
          id: '1',
          name: 'John Smith',
          role: 'CEO',
          company: 'TechCorp',
          content: 'This platform has transformed how we manage our SaaS business. Highly recommended!',
          avatar: '',
          rating: 5
        }
      ]
    },
    faq: {
      enabled: true,
      title: 'Frequently Asked Questions',
      subtitle: 'Everything you need to know',
      items: [
        {
          id: '1',
          question: 'How do I get started?',
          answer: 'Simply sign up for a free trial and follow our onboarding guide to set up your account.'
        },
        {
          id: '2',
          question: 'Can I cancel anytime?',
          answer: 'Yes, you can cancel your subscription at any time. No long-term contracts or cancellation fees.'
        }
      ]
    },
    cta: {
      enabled: true,
      title: 'Ready to Get Started?',
      description: 'Join thousands of businesses already using our platform to grow their SaaS.',
      buttonText: 'Start Your Free Trial',
      buttonLink: '/auth/signup',
      backgroundImage: ''
    },
    footer: {
      enabled: true,
      companyDescription: 'The complete SaaS platform for modern businesses.',
      links: [
        {
          id: '1',
          title: 'Product',
          items: [
            { id: '1', text: 'Features', link: '/features' },
            { id: '2', text: 'Pricing', link: '/pricing' },
            { id: '3', text: 'Security', link: '/security' }
          ]
        },
        {
          id: '2',
          title: 'Company',
          items: [
            { id: '1', text: 'About', link: '/about' },
            { id: '2', text: 'Blog', link: '/blog' },
            { id: '3', text: 'Careers', link: '/careers' }
          ]
        }
      ],
      socialLinks: {
        twitter: '',
        linkedin: '',
        facebook: '',
        instagram: ''
      },
      copyrightText: '© 2024 Your Company. All rights reserved.'
    },
    seo: {
      title: 'SaaS Platform - Build Your Business',
      description: 'The complete SaaS platform for modern businesses. Customer management, billing, analytics, and more.',
      keywords: 'saas, platform, business, customer management, billing, analytics',
      ogImage: ''
    }
  })
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [previewMode, setPreviewMode] = useState(false)

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (status === 'unauthenticated') {
    redirect('/auth/signin')
  }

  if (session?.user?.role !== 'SUPER_ADMIN') {
    redirect('/dashboard')
  }

  const fetchContent = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/super-admin/cms')
      const data = await response.json()
      
      if (data.success) {
        setContent({ ...content, ...data.content })
      }
    } catch (error) {
      console.error('Error fetching CMS content:', error)
      toast.error('Failed to load CMS content')
    } finally {
      setLoading(false)
    }
  }

  const saveContent = async () => {
    try {
      setSaving(true)
      const response = await fetch('/api/super-admin/cms', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(content)
      })

      const data = await response.json()
      
      if (data.success) {
        toast.success('CMS content saved successfully')
      } else {
        toast.error(data.error || 'Failed to save CMS content')
      }
    } catch (error) {
      console.error('Error saving CMS content:', error)
      toast.error('Failed to save CMS content')
    } finally {
      setSaving(false)
    }
  }

  useEffect(() => {
    fetchContent()
  }, [])

  const updateContent = (section: keyof CMSContent, key: string, value: any) => {
    setContent(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value
      }
    }))
  }

  const addFeature = () => {
    const newFeature = {
      id: Date.now().toString(),
      title: 'New Feature',
      description: 'Feature description',
      icon: 'star',
      image: ''
    }
    setContent(prev => ({
      ...prev,
      features: {
        ...prev.features,
        items: [...prev.features.items, newFeature]
      }
    }))
  }

  const removeFeature = (id: string) => {
    setContent(prev => ({
      ...prev,
      features: {
        ...prev.features,
        items: prev.features.items.filter(item => item.id !== id)
      }
    }))
  }

  const updateFeature = (id: string, key: string, value: any) => {
    setContent(prev => ({
      ...prev,
      features: {
        ...prev.features,
        items: prev.features.items.map(item =>
          item.id === id ? { ...item, [key]: value } : item
        )
      }
    }))
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <div className="flex items-center space-x-3">
            <Monitor className="h-8 w-8 text-indigo-600" />
            <h1 className="text-3xl font-bold text-gray-900">Landing Page CMS</h1>
          </div>
          <p className="text-gray-500 mt-1">Manage your landing page content and layout</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={fetchContent} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline" onClick={() => setPreviewMode(!previewMode)}>
            <Eye className="h-4 w-4 mr-2" />
            {previewMode ? 'Edit Mode' : 'Preview'}
          </Button>
          <Button onClick={saveContent} disabled={saving}>
            <Save className={`h-4 w-4 mr-2 ${saving ? 'animate-spin' : ''}`} />
            Save Changes
          </Button>
        </div>
      </div>

      {/* Content Management */}
      <Tabs defaultValue="hero" className="space-y-6">
        <TabsList className="grid w-full grid-cols-7">
          <TabsTrigger value="hero">Hero</TabsTrigger>
          <TabsTrigger value="features">Features</TabsTrigger>
          <TabsTrigger value="pricing">Pricing</TabsTrigger>
          <TabsTrigger value="testimonials">Testimonials</TabsTrigger>
          <TabsTrigger value="faq">FAQ</TabsTrigger>
          <TabsTrigger value="cta">CTA</TabsTrigger>
          <TabsTrigger value="seo">SEO</TabsTrigger>
        </TabsList>

        {/* Hero Section */}
        <TabsContent value="hero">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Layout className="h-5 w-5 mr-2" />
                Hero Section
              </CardTitle>
              <CardDescription>
                Configure the main hero section of your landing page
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <Label htmlFor="heroEnabled" className="text-base font-medium">
                    Enable Hero Section
                  </Label>
                  <p className="text-sm text-gray-500">
                    Show the hero section on your landing page
                  </p>
                </div>
                <Switch
                  id="heroEnabled"
                  checked={content.hero.enabled}
                  onCheckedChange={(checked) => updateContent('hero', 'enabled', checked)}
                />
              </div>

              {content.hero.enabled && (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="heroTitle">Main Title</Label>
                      <Input
                        id="heroTitle"
                        value={content.hero.title}
                        onChange={(e) => updateContent('hero', 'title', e.target.value)}
                        placeholder="Your main headline"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="heroSubtitle">Subtitle</Label>
                      <Input
                        id="heroSubtitle"
                        value={content.hero.subtitle}
                        onChange={(e) => updateContent('hero', 'subtitle', e.target.value)}
                        placeholder="Supporting headline"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="heroDescription">Description</Label>
                    <Textarea
                      id="heroDescription"
                      value={content.hero.description}
                      onChange={(e) => updateContent('hero', 'description', e.target.value)}
                      placeholder="Describe your product or service"
                      rows={3}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="primaryCTAText">Primary Button Text</Label>
                      <Input
                        id="primaryCTAText"
                        value={content.hero.primaryCTA.text}
                        onChange={(e) => updateContent('hero', 'primaryCTA', { ...content.hero.primaryCTA, text: e.target.value })}
                        placeholder="Get Started"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="primaryCTALink">Primary Button Link</Label>
                      <Input
                        id="primaryCTALink"
                        value={content.hero.primaryCTA.link}
                        onChange={(e) => updateContent('hero', 'primaryCTA', { ...content.hero.primaryCTA, link: e.target.value })}
                        placeholder="/auth/signup"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="secondaryCTAText">Secondary Button Text</Label>
                      <Input
                        id="secondaryCTAText"
                        value={content.hero.secondaryCTA.text}
                        onChange={(e) => updateContent('hero', 'secondaryCTA', { ...content.hero.secondaryCTA, text: e.target.value })}
                        placeholder="Learn More"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="secondaryCTALink">Secondary Button Link</Label>
                      <Input
                        id="secondaryCTALink"
                        value={content.hero.secondaryCTA.link}
                        onChange={(e) => updateContent('hero', 'secondaryCTA', { ...content.hero.secondaryCTA, link: e.target.value })}
                        placeholder="/demo"
                      />
                    </div>
                  </div>

                  {/* Background Media */}
                  <div className="space-y-4">
                    <Label className="text-base font-medium">Background Media</Label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="heroBackgroundImage">Background Image URL</Label>
                        <Input
                          id="heroBackgroundImage"
                          value={content.hero.backgroundImage}
                          onChange={(e) => updateContent('hero', 'backgroundImage', e.target.value)}
                          placeholder="https://example.com/hero-bg.jpg"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="heroBackgroundVideo">Background Video URL</Label>
                        <Input
                          id="heroBackgroundVideo"
                          value={content.hero.backgroundVideo}
                          onChange={(e) => updateContent('hero', 'backgroundVideo', e.target.value)}
                          placeholder="https://example.com/hero-video.mp4"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
