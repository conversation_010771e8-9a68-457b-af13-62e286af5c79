(()=>{var e={};e.id=3802,e.ids=[3802],e.modules={30517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48373:(e,r,t)=>{"use strict";t.r(r),t.d(r,{headerHooks:()=>d,originalPathname:()=>b,patchFetch:()=>c,requestAsyncStorage:()=>m,routeModule:()=>i,serverHooks:()=>x,staticGenerationAsyncStorage:()=>u,staticGenerationBailout:()=>p});var o=t(95419),s=t(69108),a=t(99678),n=t(900);let i=new o.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/items/route",pathname:"/api/items",filename:"route",bundlePath:"app/api/items/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\items\\route.ts",nextConfigOutput:"",userland:n}),{requestAsyncStorage:m,staticGenerationAsyncStorage:u,serverHooks:x,headerHooks:d,staticGenerationBailout:p}=i,b="/api/items/route";function c(){return(0,a.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:u})}},900:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError: \n  \x1b[38;2;255;30;30m\xd7\x1b[0m Expected ',', got 'catch'\n     ╭─[\x1b[38;2;92;157;255;1;4mC:\\proj\\nextjs-saas\\app\\api\\items\\route.ts\x1b[0m:277:1]\n \x1b[2m277\x1b[0m │       message: 'Item created successfully'\n \x1b[2m278\x1b[0m │     })\n \x1b[2m279\x1b[0m │ \n \x1b[2m280\x1b[0m │   } catch (error) {\n     \xb7 \x1b[38;2;246;87;248m    ─────\x1b[0m\n \x1b[2m281\x1b[0m │     if (error instanceof z.ZodError) {\n \x1b[2m282\x1b[0m │       return NextResponse.json(\n \x1b[2m283\x1b[0m │         { error: 'Validation failed', details: error.errors },\n     ╰────\n\n\nCaused by:\n    Syntax Error")},95419:(e,r,t)=>{"use strict";e.exports=t(30517)}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[1638],()=>t(48373));module.exports=o})();