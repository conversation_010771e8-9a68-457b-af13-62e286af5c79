"use strict";(()=>{var e={};e.id=3802,e.ids=[3802],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},78596:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>N,originalPathname:()=>_,patchFetch:()=>b,requestAsyncStorage:()=>x,routeModule:()=>k,serverHooks:()=>h,staticGenerationAsyncStorage:()=>O,staticGenerationBailout:()=>T});var n,a={};r.r(a),r.d(a,{GET:()=>v,POST:()=>f});var i=r(95419),o=r(69108),s=r(99678),c=r(78070),u=r(9108),l=r(25252),d=r(81355),m=r(3205),p=r(52178);!function(e){e.VALIDATION_ERROR="VALIDATION_ERROR",e.AUTHENTICATION_ERROR="AUTHENTICATION_ERROR",e.AUTHORIZATION_ERROR="AUTHORIZATION_ERROR",e.NOT_FOUND="NOT_FOUND",e.CONFLICT="CONFLICT",e.RATE_LIMIT="RATE_LIMIT",e.INTERNAL_ERROR="INTERNAL_ERROR",e.DATABASE_ERROR="DATABASE_ERROR"}(n||(n={}));class y extends Error{constructor(e,t=500,r="INTERNAL_ERROR",n){super(e),this.statusCode=t,this.type=r,this.details=n,this.name="ApiError"}}async function I(e){let t=await (0,d.getServerSession)(m.L);if(!t?.user?.id)throw new y("Authentication required",401,"AUTHENTICATION_ERROR");if(!t.user.companyId)throw new y("Company context required",401,"AUTHORIZATION_ERROR");return{user:t.user,companyId:t.user.companyId}}function g(e,t,r){return e instanceof y?c.Z.json({success:!1,error:e.message,details:e.details},{status:e.statusCode}):c.Z.json({success:!1,error:"string"==typeof e?e:"Internal server error",details:r},{status:t||500})}function R(e){return async(t,r)=>{try{return await e(t,r)}catch(e){if(console.error("API Error:",e),e instanceof y)return g(e);if(e instanceof p.jm)return g(new y("Validation failed",400,"VALIDATION_ERROR",e.errors));if("P2002"===e.code)return g(new y("Duplicate entry found",409,"CONFLICT"));if("P2025"===e.code)return g(new y("Record not found",404,"NOT_FOUND"));return g(new y("Internal server error",500,"INTERNAL_ERROR"))}}}let w={id:l.Z_().cuid(),email:l.Z_().email("Invalid email address"),phone:l.Z_().regex(/^\+?[\d\s\-\(\)]+$/,"Invalid phone number").optional(),url:l.Z_().url("Invalid URL").optional(),currency:l.Z_().length(3,"Currency must be 3 characters"),decimal:l.Rx().min(0,"Amount must be positive"),pagination:l.Ry({page:l.Rx().int().min(1).default(1),limit:l.Rx().int().min(1).max(100).default(10)})},A=l.Ry({name:l.Z_().min(1,"Name is required").max(255,"Name too long"),description:l.Z_().max(1e3,"Description too long").optional().nullable(),sku:l.Z_().max(100,"SKU too long").optional().nullable(),category:l.Z_().max(100,"Category too long").optional().nullable(),unitPrice:w.decimal,costPrice:w.decimal.optional().nullable(),currency:w.currency,trackInventory:l.O7().default(!1),stockQuantity:l.Rx().int().min(0,"Stock quantity must be non-negative").optional().nullable(),lowStockAlert:l.Rx().int().min(0,"Low stock alert must be non-negative").optional().nullable(),taxable:l.O7().default(!0),taxRate:l.Rx().min(0,"Tax rate must be non-negative").max(100,"Tax rate cannot exceed 100%").default(0),accountingCode:l.Z_().max(50,"Accounting code too long").optional().nullable(),active:l.O7().default(!0),tags:l.IX(l.Z_()).optional().default([])}),v=R(async e=>{var t;let{companyId:r}=await I(e),{page:n,limit:a,skip:i}=function(e){let{searchParams:t}=new URL(e.url),r=Math.max(1,parseInt(t.get("page")||"1")),n=Math.min(100,Math.max(1,parseInt(t.get("limit")||"10")));return{page:r,limit:n,skip:(r-1)*n}}(e),{search:o}=function(e){let{searchParams:t}=new URL(e.url);return{search:t.get("search")||"",sortBy:t.get("sortBy")||"createdAt",sortOrder:t.get("sortOrder")||"desc",status:t.get("status")||"",startDate:t.get("startDate"),endDate:t.get("endDate")}}(e),{searchParams:s}=new URL(e.url),l=s.get("category")||"",d=s.get("status")||"",m=s.get("trackInventory"),p="true"===s.get("lowStock"),y={companyId:r};o&&(y.OR=[{name:{contains:o,mode:"insensitive"}},{description:{contains:o,mode:"insensitive"}},{sku:{contains:o,mode:"insensitive"}},{category:{contains:o,mode:"insensitive"}}]),l&&(y.category=l),"active"===d?y.active=!0:"inactive"===d&&(y.active=!1),"true"===m?y.trackInventory=!0:"false"===m&&(y.trackInventory=!1),p&&(y.trackInventory=!0,y.stockQuantity={not:null},y.lowStockAlert={not:null});let[g,R]=await Promise.all([u._.item.findMany({where:y,include:{quotationItems:{select:{id:!0,quantity:!0,quotation:{select:{id:!0,quotationNumber:!0}}}},invoiceItems:{select:{id:!0,quantity:!0,invoice:{select:{id:!0,invoiceNumber:!0}}}}},orderBy:{createdAt:"desc"},skip:i,take:a}),u._.item.count({where:y})]);return t=g.map(e=>{let t=e.quotationItems.reduce((e,t)=>e+t.quantity,0),r=e.invoiceItems.reduce((e,t)=>e+t.quantity,0),n=e.quotationItems.length+e.invoiceItems.length;return{id:e.id,name:e.name,description:e.description,sku:e.sku,category:e.category,unitPrice:Number(e.unitPrice),costPrice:e.costPrice?Number(e.costPrice):null,currency:e.currency,trackInventory:e.trackInventory,stockQuantity:e.stockQuantity,lowStockAlert:e.lowStockAlert,taxable:e.taxable,taxRate:Number(e.taxRate),accountingCode:e.accountingCode,active:e.active,tags:e.tags||[],createdAt:e.createdAt,updatedAt:e.updatedAt,usage:{totalQuantity:t+r,usageCount:n,quotationUsage:t,invoiceUsage:r,recentQuotations:e.quotationItems.slice(0,3).map(e=>e.quotation),recentInvoices:e.invoiceItems.slice(0,3).map(e=>e.invoice)},stockValue:e.trackInventory&&e.stockQuantity?Number(e.unitPrice)*e.stockQuantity:0,profitMargin:e.costPrice?(Number(e.unitPrice)-Number(e.costPrice))/Number(e.unitPrice)*100:null,isLowStock:!!e.trackInventory&&null!==e.stockQuantity&&null!==e.lowStockAlert&&e.stockQuantity<=e.lowStockAlert}}),c.Z.json({success:!0,data:t,message:"Items retrieved successfully",pagination:{page:n,limit:a,total:R,pages:Math.ceil(R/a)}})}),f=R(async e=>{let{user:t,companyId:r}=await I(e),a=function(e,t){try{return e.parse(t)}catch(e){if(e instanceof p.jm)throw new y("Validation failed",400,"VALIDATION_ERROR",e.errors);throw e}}(A,function e(t){if("string"==typeof t)return t.trim();if(Array.isArray(t))return t.map(e);if(t&&"object"==typeof t){let r={};for(let[n,a]of Object.entries(t))r[n]=e(a);return r}return t}(await e.json()));if(a.sku&&await u._.item.findFirst({where:{sku:a.sku,companyId:r}}))throw new y("An item with this SKU already exists",409,n.CONFLICT);if(a.trackInventory){if(void 0===a.stockQuantity||null===a.stockQuantity)throw new y("Stock quantity is required when inventory tracking is enabled",400,n.VALIDATION_ERROR)}else a.stockQuantity=null,a.lowStockAlert=null;let i=await u._.$transaction(async e=>{let n=await e.item.create({data:{...a,companyId:r}});return await e.activity.create({data:{type:"SYSTEM",title:"Item Created",description:`Item "${a.name}" was created`,companyId:r,createdById:t.id}}),n});return c.Z.json({item:{id:i.id,name:i.name,description:i.description,sku:i.sku,category:i.category,unitPrice:Number(i.unitPrice),costPrice:i.costPrice?Number(i.costPrice):null,currency:i.currency,trackInventory:i.trackInventory,stockQuantity:i.stockQuantity,lowStockAlert:i.lowStockAlert,taxable:i.taxable,taxRate:Number(i.taxRate),accountingCode:i.accountingCode,active:i.active,createdAt:i.createdAt,updatedAt:i.updatedAt,usage:{totalQuantity:0,usageCount:0,quotationUsage:0,invoiceUsage:0,recentQuotations:[],recentInvoices:[]},stockValue:i.trackInventory&&i.stockQuantity?Number(i.unitPrice)*i.stockQuantity:0,profitMargin:i.costPrice?(Number(i.unitPrice)-Number(i.costPrice))/Number(i.unitPrice)*100:null,isLowStock:!1},message:"Item created successfully"})}),k=new i.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/items/route",pathname:"/api/items",filename:"route",bundlePath:"app/api/items/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\items\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:x,staticGenerationAsyncStorage:O,serverHooks:h,headerHooks:N,staticGenerationBailout:T}=k,_="/api/items/route";function b(){return(0,s.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:O})}},3205:(e,t,r)=>{r.d(t,{L:()=>u});var n=r(86485),a=r(10375),i=r(50694),o=r(6521),s=r.n(o),c=r(9108);let u={providers:[(0,n.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await c._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),r=t?.companyId;if(!r&&t){let e=await c._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(r=e?.id)&&await c._.user.update({where:{id:t.id},data:{companyId:r}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await s().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await c._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:r}}catch(e){return console.error("Authentication error:",e),null}}}),(0,a.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,i.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,r)=>{r.d(t,{_:()=>a});let n=require("@prisma/client"),a=globalThis.prisma??new n.PrismaClient}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[1638,6206,6521,2455,4520,5252],()=>r(78596));module.exports=n})();