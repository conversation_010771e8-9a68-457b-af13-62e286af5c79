"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/items/route";
exports.ids = ["app/api/items/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fitems%2Froute&page=%2Fapi%2Fitems%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fitems%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fitems%2Froute&page=%2Fapi%2Fitems%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fitems%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_proj_nextjs_saas_app_api_items_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/items/route.ts */ \"(rsc)/./app/api/items/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/items/route\",\n        pathname: \"/api/items\",\n        filename: \"route\",\n        bundlePath: \"app/api/items/route\"\n    },\n    resolvedPagePath: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\api\\\\items\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_proj_nextjs_saas_app_api_items_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/items/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fitems%2Froute&page=%2Fapi%2Fitems%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fitems%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/items/route.ts":
/*!********************************!*\
  !*** ./app/api/items/route.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/ZodError.js\");\n\n\n\n\n\nconst itemSchema = zod__WEBPACK_IMPORTED_MODULE_4__.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_4__.string().min(1, \"Name is required\"),\n    description: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional(),\n    sku: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional(),\n    category: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional(),\n    unitPrice: zod__WEBPACK_IMPORTED_MODULE_4__.number().min(0, \"Unit price must be positive\"),\n    costPrice: zod__WEBPACK_IMPORTED_MODULE_4__.number().min(0, \"Cost price must be positive\").optional(),\n    currency: zod__WEBPACK_IMPORTED_MODULE_4__.string().default(\"USD\"),\n    trackInventory: zod__WEBPACK_IMPORTED_MODULE_4__.boolean().default(false),\n    stockQuantity: zod__WEBPACK_IMPORTED_MODULE_4__.number().int().min(0).optional(),\n    lowStockAlert: zod__WEBPACK_IMPORTED_MODULE_4__.number().int().min(0).optional(),\n    taxable: zod__WEBPACK_IMPORTED_MODULE_4__.boolean().default(true),\n    taxRate: zod__WEBPACK_IMPORTED_MODULE_4__.number().min(0).max(100).default(0),\n    accountingCode: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional(),\n    active: zod__WEBPACK_IMPORTED_MODULE_4__.boolean().default(true)\n});\n// GET /api/items - Get all items\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id || !session?.user?.companyId) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get(\"page\") || \"1\");\n        const limit = parseInt(searchParams.get(\"limit\") || \"50\");\n        const search = searchParams.get(\"search\") || \"\";\n        const category = searchParams.get(\"category\") || \"\";\n        const status = searchParams.get(\"status\") || \"\";\n        const trackInventory = searchParams.get(\"trackInventory\");\n        const lowStock = searchParams.get(\"lowStock\") === \"true\";\n        const skip = (page - 1) * limit;\n        // Build where clause\n        const where = {\n            companyId: session.user.companyId\n        };\n        if (search) {\n            where.OR = [\n                {\n                    name: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    description: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    sku: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    category: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                }\n            ];\n        }\n        if (category) {\n            where.category = category;\n        }\n        if (status === \"active\") {\n            where.active = true;\n        } else if (status === \"inactive\") {\n            where.active = false;\n        }\n        if (trackInventory === \"true\") {\n            where.trackInventory = true;\n        } else if (trackInventory === \"false\") {\n            where.trackInventory = false;\n        }\n        if (lowStock) {\n            where.trackInventory = true;\n            where.AND = [\n                {\n                    stockQuantity: {\n                        not: null\n                    }\n                },\n                {\n                    lowStockAlert: {\n                        not: null\n                    }\n                },\n                {\n                    stockQuantity: {\n                        lte: _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.item.fields.lowStockAlert\n                    }\n                }\n            ];\n        }\n        const [items, total] = await Promise.all([\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.item.findMany({\n                where,\n                include: {\n                    createdBy: {\n                        select: {\n                            name: true,\n                            email: true\n                        }\n                    },\n                    quotationItems: {\n                        select: {\n                            id: true,\n                            quantity: true,\n                            quotation: {\n                                select: {\n                                    id: true,\n                                    quotationNumber: true\n                                }\n                            }\n                        }\n                    },\n                    invoiceItems: {\n                        select: {\n                            id: true,\n                            quantity: true,\n                            invoice: {\n                                select: {\n                                    id: true,\n                                    invoiceNumber: true\n                                }\n                            }\n                        }\n                    }\n                },\n                orderBy: {\n                    createdAt: \"desc\"\n                },\n                skip,\n                take: limit\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.item.count({\n                where\n            })\n        ]);\n        // Calculate usage statistics for each item\n        const itemsWithStats = items.map((item)=>{\n            const quotationUsage = item.quotationItems.reduce((sum, qi)=>sum + qi.quantity, 0);\n            const invoiceUsage = item.invoiceItems.reduce((sum, ii)=>sum + ii.quantity, 0);\n            const totalUsage = quotationUsage + invoiceUsage;\n            const usageCount = item.quotationItems.length + item.invoiceItems.length;\n            return {\n                id: item.id,\n                name: item.name,\n                description: item.description,\n                sku: item.sku,\n                category: item.category,\n                unitPrice: Number(item.unitPrice),\n                costPrice: item.costPrice ? Number(item.costPrice) : null,\n                currency: item.currency,\n                trackInventory: item.trackInventory,\n                stockQuantity: item.stockQuantity,\n                lowStockAlert: item.lowStockAlert,\n                taxable: item.taxable,\n                taxRate: Number(item.taxRate),\n                accountingCode: item.accountingCode,\n                active: item.active,\n                createdBy: item.createdBy,\n                createdAt: item.createdAt,\n                updatedAt: item.updatedAt,\n                usage: {\n                    totalQuantity: totalUsage,\n                    usageCount,\n                    quotationUsage,\n                    invoiceUsage,\n                    recentQuotations: item.quotationItems.slice(0, 3).map((qi)=>qi.quotation),\n                    recentInvoices: item.invoiceItems.slice(0, 3).map((ii)=>ii.invoice)\n                },\n                stockValue: item.trackInventory && item.stockQuantity ? Number(item.unitPrice) * item.stockQuantity : 0,\n                profitMargin: item.costPrice ? (Number(item.unitPrice) - Number(item.costPrice)) / Number(item.unitPrice) * 100 : null,\n                isLowStock: item.trackInventory && item.stockQuantity !== null && item.lowStockAlert !== null ? item.stockQuantity <= item.lowStockAlert : false\n            };\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            items: itemsWithStats,\n            pagination: {\n                page,\n                limit,\n                total,\n                pages: Math.ceil(total / limit)\n            }\n        });\n    } catch (error) {\n        console.error(\"Error fetching items:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to fetch items\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/items - Create new item\nasync function POST(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id || !session?.user?.companyId) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const validatedData = itemSchema.parse(body);\n        // Check for duplicate SKU if provided\n        if (validatedData.sku) {\n            const existingItem = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.item.findFirst({\n                where: {\n                    sku: validatedData.sku,\n                    companyId: session.user.companyId\n                }\n            });\n            if (existingItem) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"An item with this SKU already exists\"\n                }, {\n                    status: 400\n                });\n            }\n        }\n        // Validate inventory settings\n        if (validatedData.trackInventory) {\n            if (validatedData.stockQuantity === undefined) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"Stock quantity is required when inventory tracking is enabled\"\n                }, {\n                    status: 400\n                });\n            }\n        } else {\n            // Clear inventory fields if not tracking\n            validatedData.stockQuantity = undefined;\n            validatedData.lowStockAlert = undefined;\n        }\n        const item = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.$transaction(async (tx)=>{\n            // Create the item\n            const newItem = await tx.item.create({\n                data: {\n                    ...validatedData,\n                    companyId: session.user.companyId,\n                    createdById: session.user.id\n                },\n                include: {\n                    createdBy: {\n                        select: {\n                            name: true,\n                            email: true\n                        }\n                    }\n                }\n            });\n            // Log activity\n            await tx.activity.create({\n                data: {\n                    type: \"ITEM\",\n                    title: \"Item Created\",\n                    description: `Item \"${validatedData.name}\" was created`,\n                    itemId: newItem.id,\n                    companyId: session.user.companyId,\n                    createdById: session.user.id\n                }\n            });\n            return newItem;\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            item: {\n                id: item.id,\n                name: item.name,\n                description: item.description,\n                sku: item.sku,\n                category: item.category,\n                unitPrice: Number(item.unitPrice),\n                costPrice: item.costPrice ? Number(item.costPrice) : null,\n                currency: item.currency,\n                trackInventory: item.trackInventory,\n                stockQuantity: item.stockQuantity,\n                lowStockAlert: item.lowStockAlert,\n                taxable: item.taxable,\n                taxRate: Number(item.taxRate),\n                accountingCode: item.accountingCode,\n                active: item.active,\n                createdBy: item.createdBy,\n                createdAt: item.createdAt,\n                updatedAt: item.updatedAt,\n                usage: {\n                    totalQuantity: 0,\n                    usageCount: 0,\n                    quotationUsage: 0,\n                    invoiceUsage: 0,\n                    recentQuotations: [],\n                    recentInvoices: []\n                },\n                stockValue: item.trackInventory && item.stockQuantity ? Number(item.unitPrice) * item.stockQuantity : 0,\n                profitMargin: item.costPrice ? (Number(item.unitPrice) - Number(item.costPrice)) / Number(item.unitPrice) * 100 : null,\n                isLowStock: false\n            },\n            message: \"Item created successfully\"\n        });\n    } catch (error) {\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_5__.ZodError) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Validation failed\",\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        console.error(\"Error creating item:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to create item\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvYXBpL2l0ZW1zL3JvdXRlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUF1RDtBQUNYO0FBQ0o7QUFDSDtBQUNkO0FBRXZCLE1BQU1LLGFBQWFELHVDQUFRLENBQUM7SUFDMUJHLE1BQU1ILHVDQUFRLEdBQUdLLEdBQUcsQ0FBQyxHQUFHO0lBQ3hCQyxhQUFhTix1Q0FBUSxHQUFHTyxRQUFRO0lBQ2hDQyxLQUFLUix1Q0FBUSxHQUFHTyxRQUFRO0lBQ3hCRSxVQUFVVCx1Q0FBUSxHQUFHTyxRQUFRO0lBQzdCRyxXQUFXVix1Q0FBUSxHQUFHSyxHQUFHLENBQUMsR0FBRztJQUM3Qk8sV0FBV1osdUNBQVEsR0FBR0ssR0FBRyxDQUFDLEdBQUcsK0JBQStCRSxRQUFRO0lBQ3BFTSxVQUFVYix1Q0FBUSxHQUFHYyxPQUFPLENBQUM7SUFDN0JDLGdCQUFnQmYsd0NBQVMsR0FBR2MsT0FBTyxDQUFDO0lBQ3BDRyxlQUFlakIsdUNBQVEsR0FBR2tCLEdBQUcsR0FBR2IsR0FBRyxDQUFDLEdBQUdFLFFBQVE7SUFDL0NZLGVBQWVuQix1Q0FBUSxHQUFHa0IsR0FBRyxHQUFHYixHQUFHLENBQUMsR0FBR0UsUUFBUTtJQUMvQ2EsU0FBU3BCLHdDQUFTLEdBQUdjLE9BQU8sQ0FBQztJQUM3Qk8sU0FBU3JCLHVDQUFRLEdBQUdLLEdBQUcsQ0FBQyxHQUFHaUIsR0FBRyxDQUFDLEtBQUtSLE9BQU8sQ0FBQztJQUM1Q1MsZ0JBQWdCdkIsdUNBQVEsR0FBR08sUUFBUTtJQUNuQ2lCLFFBQVF4Qix3Q0FBUyxHQUFHYyxPQUFPLENBQUM7QUFDOUI7QUFFQSxpQ0FBaUM7QUFDMUIsZUFBZVcsSUFBSUMsT0FBb0I7SUFDNUMsSUFBSTtRQUNGLE1BQU1DLFVBQVUsTUFBTTlCLDJEQUFnQkEsQ0FBQ0Msa0RBQVdBO1FBQ2xELElBQUksQ0FBQzZCLFNBQVNDLE1BQU1DLE1BQU0sQ0FBQ0YsU0FBU0MsTUFBTUUsV0FBVztZQUNuRCxPQUFPbEMsa0ZBQVlBLENBQUNtQyxJQUFJLENBQUM7Z0JBQUVDLE9BQU87WUFBZSxHQUFHO2dCQUFFQyxRQUFRO1lBQUk7UUFDcEU7UUFFQSxNQUFNLEVBQUVDLFlBQVksRUFBRSxHQUFHLElBQUlDLElBQUlULFFBQVFVLEdBQUc7UUFDNUMsTUFBTUMsT0FBT0MsU0FBU0osYUFBYUssR0FBRyxDQUFDLFdBQVc7UUFDbEQsTUFBTUMsUUFBUUYsU0FBU0osYUFBYUssR0FBRyxDQUFDLFlBQVk7UUFDcEQsTUFBTUUsU0FBU1AsYUFBYUssR0FBRyxDQUFDLGFBQWE7UUFDN0MsTUFBTTlCLFdBQVd5QixhQUFhSyxHQUFHLENBQUMsZUFBZTtRQUNqRCxNQUFNTixTQUFTQyxhQUFhSyxHQUFHLENBQUMsYUFBYTtRQUM3QyxNQUFNeEIsaUJBQWlCbUIsYUFBYUssR0FBRyxDQUFDO1FBQ3hDLE1BQU1HLFdBQVdSLGFBQWFLLEdBQUcsQ0FBQyxnQkFBZ0I7UUFFbEQsTUFBTUksT0FBTyxDQUFDTixPQUFPLEtBQUtHO1FBRTFCLHFCQUFxQjtRQUNyQixNQUFNSSxRQUFhO1lBQ2pCZCxXQUFXSCxRQUFRQyxJQUFJLENBQUNFLFNBQVM7UUFDbkM7UUFFQSxJQUFJVyxRQUFRO1lBQ1ZHLE1BQU1DLEVBQUUsR0FBRztnQkFDVDtvQkFBRTFDLE1BQU07d0JBQUUyQyxVQUFVTDt3QkFBUU0sTUFBTTtvQkFBYztnQkFBRTtnQkFDbEQ7b0JBQUV6QyxhQUFhO3dCQUFFd0MsVUFBVUw7d0JBQVFNLE1BQU07b0JBQWM7Z0JBQUU7Z0JBQ3pEO29CQUFFdkMsS0FBSzt3QkFBRXNDLFVBQVVMO3dCQUFRTSxNQUFNO29CQUFjO2dCQUFFO2dCQUNqRDtvQkFBRXRDLFVBQVU7d0JBQUVxQyxVQUFVTDt3QkFBUU0sTUFBTTtvQkFBYztnQkFBRTthQUN2RDtRQUNIO1FBRUEsSUFBSXRDLFVBQVU7WUFDWm1DLE1BQU1uQyxRQUFRLEdBQUdBO1FBQ25CO1FBRUEsSUFBSXdCLFdBQVcsVUFBVTtZQUN2QlcsTUFBTXBCLE1BQU0sR0FBRztRQUNqQixPQUFPLElBQUlTLFdBQVcsWUFBWTtZQUNoQ1csTUFBTXBCLE1BQU0sR0FBRztRQUNqQjtRQUVBLElBQUlULG1CQUFtQixRQUFRO1lBQzdCNkIsTUFBTTdCLGNBQWMsR0FBRztRQUN6QixPQUFPLElBQUlBLG1CQUFtQixTQUFTO1lBQ3JDNkIsTUFBTTdCLGNBQWMsR0FBRztRQUN6QjtRQUVBLElBQUkyQixVQUFVO1lBQ1pFLE1BQU03QixjQUFjLEdBQUc7WUFDdkI2QixNQUFNSSxHQUFHLEdBQUc7Z0JBQ1Y7b0JBQUUvQixlQUFlO3dCQUFFZ0MsS0FBSztvQkFBSztnQkFBRTtnQkFDL0I7b0JBQUU5QixlQUFlO3dCQUFFOEIsS0FBSztvQkFBSztnQkFBRTtnQkFDL0I7b0JBQUVoQyxlQUFlO3dCQUFFaUMsS0FBS25ELCtDQUFNQSxDQUFDb0QsSUFBSSxDQUFDQyxNQUFNLENBQUNqQyxhQUFhO29CQUFDO2dCQUFFO2FBQzVEO1FBQ0g7UUFFQSxNQUFNLENBQUNrQyxPQUFPQyxNQUFNLEdBQUcsTUFBTUMsUUFBUUMsR0FBRyxDQUFDO1lBQ3ZDekQsK0NBQU1BLENBQUNvRCxJQUFJLENBQUNNLFFBQVEsQ0FBQztnQkFDbkJiO2dCQUNBYyxTQUFTO29CQUNQQyxXQUFXO3dCQUNUQyxRQUFROzRCQUNOekQsTUFBTTs0QkFDTjBELE9BQU87d0JBQ1Q7b0JBQ0Y7b0JBQ0FDLGdCQUFnQjt3QkFDZEYsUUFBUTs0QkFDTi9CLElBQUk7NEJBQ0prQyxVQUFVOzRCQUNWQyxXQUFXO2dDQUNUSixRQUFRO29DQUNOL0IsSUFBSTtvQ0FDSm9DLGlCQUFpQjtnQ0FDbkI7NEJBQ0Y7d0JBQ0Y7b0JBQ0Y7b0JBQ0FDLGNBQWM7d0JBQ1pOLFFBQVE7NEJBQ04vQixJQUFJOzRCQUNKa0MsVUFBVTs0QkFDVkksU0FBUztnQ0FDUFAsUUFBUTtvQ0FDTi9CLElBQUk7b0NBQ0p1QyxlQUFlO2dDQUNqQjs0QkFDRjt3QkFDRjtvQkFDRjtnQkFDRjtnQkFDQUMsU0FBUztvQkFBRUMsV0FBVztnQkFBTztnQkFDN0IzQjtnQkFDQTRCLE1BQU0vQjtZQUNSO1lBQ0F6QywrQ0FBTUEsQ0FBQ29ELElBQUksQ0FBQ3FCLEtBQUssQ0FBQztnQkFBRTVCO1lBQU07U0FDM0I7UUFFRCwyQ0FBMkM7UUFDM0MsTUFBTTZCLGlCQUFpQnBCLE1BQU1xQixHQUFHLENBQUN2QixDQUFBQTtZQUMvQixNQUFNd0IsaUJBQWlCeEIsS0FBS1csY0FBYyxDQUFDYyxNQUFNLENBQUMsQ0FBQ0MsS0FBS0MsS0FBT0QsTUFBTUMsR0FBR2YsUUFBUSxFQUFFO1lBQ2xGLE1BQU1nQixlQUFlNUIsS0FBS2UsWUFBWSxDQUFDVSxNQUFNLENBQUMsQ0FBQ0MsS0FBS0csS0FBT0gsTUFBTUcsR0FBR2pCLFFBQVEsRUFBRTtZQUM5RSxNQUFNa0IsYUFBYU4saUJBQWlCSTtZQUNwQyxNQUFNRyxhQUFhL0IsS0FBS1csY0FBYyxDQUFDcUIsTUFBTSxHQUFHaEMsS0FBS2UsWUFBWSxDQUFDaUIsTUFBTTtZQUV4RSxPQUFPO2dCQUNMdEQsSUFBSXNCLEtBQUt0QixFQUFFO2dCQUNYMUIsTUFBTWdELEtBQUtoRCxJQUFJO2dCQUNmRyxhQUFhNkMsS0FBSzdDLFdBQVc7Z0JBQzdCRSxLQUFLMkMsS0FBSzNDLEdBQUc7Z0JBQ2JDLFVBQVUwQyxLQUFLMUMsUUFBUTtnQkFDdkJDLFdBQVcwRSxPQUFPakMsS0FBS3pDLFNBQVM7Z0JBQ2hDRSxXQUFXdUMsS0FBS3ZDLFNBQVMsR0FBR3dFLE9BQU9qQyxLQUFLdkMsU0FBUyxJQUFJO2dCQUNyREMsVUFBVXNDLEtBQUt0QyxRQUFRO2dCQUN2QkUsZ0JBQWdCb0MsS0FBS3BDLGNBQWM7Z0JBQ25DRSxlQUFla0MsS0FBS2xDLGFBQWE7Z0JBQ2pDRSxlQUFlZ0MsS0FBS2hDLGFBQWE7Z0JBQ2pDQyxTQUFTK0IsS0FBSy9CLE9BQU87Z0JBQ3JCQyxTQUFTK0QsT0FBT2pDLEtBQUs5QixPQUFPO2dCQUM1QkUsZ0JBQWdCNEIsS0FBSzVCLGNBQWM7Z0JBQ25DQyxRQUFRMkIsS0FBSzNCLE1BQU07Z0JBQ25CbUMsV0FBV1IsS0FBS1EsU0FBUztnQkFDekJXLFdBQVduQixLQUFLbUIsU0FBUztnQkFDekJlLFdBQVdsQyxLQUFLa0MsU0FBUztnQkFDekJDLE9BQU87b0JBQ0xDLGVBQWVOO29CQUNmQztvQkFDQVA7b0JBQ0FJO29CQUNBUyxrQkFBa0JyQyxLQUFLVyxjQUFjLENBQUMyQixLQUFLLENBQUMsR0FBRyxHQUFHZixHQUFHLENBQUNJLENBQUFBLEtBQU1BLEdBQUdkLFNBQVM7b0JBQ3hFMEIsZ0JBQWdCdkMsS0FBS2UsWUFBWSxDQUFDdUIsS0FBSyxDQUFDLEdBQUcsR0FBR2YsR0FBRyxDQUFDTSxDQUFBQSxLQUFNQSxHQUFHYixPQUFPO2dCQUNwRTtnQkFDQXdCLFlBQVl4QyxLQUFLcEMsY0FBYyxJQUFJb0MsS0FBS2xDLGFBQWEsR0FDakRtRSxPQUFPakMsS0FBS3pDLFNBQVMsSUFBSXlDLEtBQUtsQyxhQUFhLEdBQzNDO2dCQUNKMkUsY0FBY3pDLEtBQUt2QyxTQUFTLEdBQ3hCLENBQUV3RSxPQUFPakMsS0FBS3pDLFNBQVMsSUFBSTBFLE9BQU9qQyxLQUFLdkMsU0FBUyxLQUFLd0UsT0FBT2pDLEtBQUt6QyxTQUFTLElBQUssTUFDL0U7Z0JBQ0ptRixZQUFZMUMsS0FBS3BDLGNBQWMsSUFBSW9DLEtBQUtsQyxhQUFhLEtBQUssUUFBUWtDLEtBQUtoQyxhQUFhLEtBQUssT0FDckZnQyxLQUFLbEMsYUFBYSxJQUFJa0MsS0FBS2hDLGFBQWEsR0FDeEM7WUFDTjtRQUNGO1FBRUEsT0FBT3ZCLGtGQUFZQSxDQUFDbUMsSUFBSSxDQUFDO1lBQ3ZCc0IsT0FBT29CO1lBQ1BxQixZQUFZO2dCQUNWekQ7Z0JBQ0FHO2dCQUNBYztnQkFDQXlDLE9BQU9DLEtBQUtDLElBQUksQ0FBQzNDLFFBQVFkO1lBQzNCO1FBQ0Y7SUFFRixFQUFFLE9BQU9SLE9BQU87UUFDZGtFLFFBQVFsRSxLQUFLLENBQUMseUJBQXlCQTtRQUN2QyxPQUFPcEMsa0ZBQVlBLENBQUNtQyxJQUFJLENBQ3RCO1lBQUVDLE9BQU87UUFBd0IsR0FDakM7WUFBRUMsUUFBUTtRQUFJO0lBRWxCO0FBQ0Y7QUFFQSxvQ0FBb0M7QUFDN0IsZUFBZWtFLEtBQUt6RSxPQUFvQjtJQUM3QyxJQUFJO1FBQ0YsTUFBTUMsVUFBVSxNQUFNOUIsMkRBQWdCQSxDQUFDQyxrREFBV0E7UUFDbEQsSUFBSSxDQUFDNkIsU0FBU0MsTUFBTUMsTUFBTSxDQUFDRixTQUFTQyxNQUFNRSxXQUFXO1lBQ25ELE9BQU9sQyxrRkFBWUEsQ0FBQ21DLElBQUksQ0FBQztnQkFBRUMsT0FBTztZQUFlLEdBQUc7Z0JBQUVDLFFBQVE7WUFBSTtRQUNwRTtRQUVBLE1BQU1tRSxPQUFPLE1BQU0xRSxRQUFRSyxJQUFJO1FBQy9CLE1BQU1zRSxnQkFBZ0JwRyxXQUFXcUcsS0FBSyxDQUFDRjtRQUV2QyxzQ0FBc0M7UUFDdEMsSUFBSUMsY0FBYzdGLEdBQUcsRUFBRTtZQUNyQixNQUFNK0YsZUFBZSxNQUFNeEcsK0NBQU1BLENBQUNvRCxJQUFJLENBQUNxRCxTQUFTLENBQUM7Z0JBQy9DNUQsT0FBTztvQkFDTHBDLEtBQUs2RixjQUFjN0YsR0FBRztvQkFDdEJzQixXQUFXSCxRQUFRQyxJQUFJLENBQUNFLFNBQVM7Z0JBQ25DO1lBQ0Y7WUFFQSxJQUFJeUUsY0FBYztnQkFDaEIsT0FBTzNHLGtGQUFZQSxDQUFDbUMsSUFBSSxDQUN0QjtvQkFBRUMsT0FBTztnQkFBdUMsR0FDaEQ7b0JBQUVDLFFBQVE7Z0JBQUk7WUFFbEI7UUFDRjtRQUVBLDhCQUE4QjtRQUM5QixJQUFJb0UsY0FBY3RGLGNBQWMsRUFBRTtZQUNoQyxJQUFJc0YsY0FBY3BGLGFBQWEsS0FBS3dGLFdBQVc7Z0JBQzdDLE9BQU83RyxrRkFBWUEsQ0FBQ21DLElBQUksQ0FDdEI7b0JBQUVDLE9BQU87Z0JBQWdFLEdBQ3pFO29CQUFFQyxRQUFRO2dCQUFJO1lBRWxCO1FBQ0YsT0FBTztZQUNMLHlDQUF5QztZQUN6Q29FLGNBQWNwRixhQUFhLEdBQUd3RjtZQUM5QkosY0FBY2xGLGFBQWEsR0FBR3NGO1FBQ2hDO1FBRUEsTUFBTXRELE9BQU8sTUFBTXBELCtDQUFNQSxDQUFDMkcsWUFBWSxDQUFDLE9BQU9DO1lBQzVDLGtCQUFrQjtZQUNsQixNQUFNQyxVQUFVLE1BQU1ELEdBQUd4RCxJQUFJLENBQUMwRCxNQUFNLENBQUM7Z0JBQ25DQyxNQUFNO29CQUNKLEdBQUdULGFBQWE7b0JBQ2hCdkUsV0FBV0gsUUFBUUMsSUFBSSxDQUFDRSxTQUFTO29CQUNqQ2lGLGFBQWFwRixRQUFRQyxJQUFJLENBQUNDLEVBQUU7Z0JBQzlCO2dCQUNBNkIsU0FBUztvQkFDUEMsV0FBVzt3QkFDVEMsUUFBUTs0QkFDTnpELE1BQU07NEJBQ04wRCxPQUFPO3dCQUNUO29CQUNGO2dCQUNGO1lBQ0Y7WUFFQSxlQUFlO1lBQ2YsTUFBTThDLEdBQUdLLFFBQVEsQ0FBQ0gsTUFBTSxDQUFDO2dCQUN2QkMsTUFBTTtvQkFDSkcsTUFBTTtvQkFDTkMsT0FBTztvQkFDUDVHLGFBQWEsQ0FBQyxNQUFNLEVBQUUrRixjQUFjbEcsSUFBSSxDQUFDLGFBQWEsQ0FBQztvQkFDdkRnSCxRQUFRUCxRQUFRL0UsRUFBRTtvQkFDbEJDLFdBQVdILFFBQVFDLElBQUksQ0FBQ0UsU0FBUztvQkFDakNpRixhQUFhcEYsUUFBUUMsSUFBSSxDQUFDQyxFQUFFO2dCQUM5QjtZQUNGO1lBRUEsT0FBTytFO1FBQ1Q7UUFFQSxPQUFPaEgsa0ZBQVlBLENBQUNtQyxJQUFJLENBQUM7WUFDdkJvQixNQUFNO2dCQUNKdEIsSUFBSXNCLEtBQUt0QixFQUFFO2dCQUNYMUIsTUFBTWdELEtBQUtoRCxJQUFJO2dCQUNmRyxhQUFhNkMsS0FBSzdDLFdBQVc7Z0JBQzdCRSxLQUFLMkMsS0FBSzNDLEdBQUc7Z0JBQ2JDLFVBQVUwQyxLQUFLMUMsUUFBUTtnQkFDdkJDLFdBQVcwRSxPQUFPakMsS0FBS3pDLFNBQVM7Z0JBQ2hDRSxXQUFXdUMsS0FBS3ZDLFNBQVMsR0FBR3dFLE9BQU9qQyxLQUFLdkMsU0FBUyxJQUFJO2dCQUNyREMsVUFBVXNDLEtBQUt0QyxRQUFRO2dCQUN2QkUsZ0JBQWdCb0MsS0FBS3BDLGNBQWM7Z0JBQ25DRSxlQUFla0MsS0FBS2xDLGFBQWE7Z0JBQ2pDRSxlQUFlZ0MsS0FBS2hDLGFBQWE7Z0JBQ2pDQyxTQUFTK0IsS0FBSy9CLE9BQU87Z0JBQ3JCQyxTQUFTK0QsT0FBT2pDLEtBQUs5QixPQUFPO2dCQUM1QkUsZ0JBQWdCNEIsS0FBSzVCLGNBQWM7Z0JBQ25DQyxRQUFRMkIsS0FBSzNCLE1BQU07Z0JBQ25CbUMsV0FBV1IsS0FBS1EsU0FBUztnQkFDekJXLFdBQVduQixLQUFLbUIsU0FBUztnQkFDekJlLFdBQVdsQyxLQUFLa0MsU0FBUztnQkFDekJDLE9BQU87b0JBQ0xDLGVBQWU7b0JBQ2ZMLFlBQVk7b0JBQ1pQLGdCQUFnQjtvQkFDaEJJLGNBQWM7b0JBQ2RTLGtCQUFrQixFQUFFO29CQUNwQkUsZ0JBQWdCLEVBQUU7Z0JBQ3BCO2dCQUNBQyxZQUFZeEMsS0FBS3BDLGNBQWMsSUFBSW9DLEtBQUtsQyxhQUFhLEdBQ2pEbUUsT0FBT2pDLEtBQUt6QyxTQUFTLElBQUl5QyxLQUFLbEMsYUFBYSxHQUMzQztnQkFDSjJFLGNBQWN6QyxLQUFLdkMsU0FBUyxHQUN4QixDQUFFd0UsT0FBT2pDLEtBQUt6QyxTQUFTLElBQUkwRSxPQUFPakMsS0FBS3ZDLFNBQVMsS0FBS3dFLE9BQU9qQyxLQUFLekMsU0FBUyxJQUFLLE1BQy9FO2dCQUNKbUYsWUFBWTtZQUNkO1lBQ0F1QixTQUFTO1FBQ1g7SUFFRixFQUFFLE9BQU9wRixPQUFPO1FBQ2QsSUFBSUEsaUJBQWlCaEMseUNBQVUsRUFBRTtZQUMvQixPQUFPSixrRkFBWUEsQ0FBQ21DLElBQUksQ0FDdEI7Z0JBQUVDLE9BQU87Z0JBQXFCc0YsU0FBU3RGLE1BQU11RixNQUFNO1lBQUMsR0FDcEQ7Z0JBQUV0RixRQUFRO1lBQUk7UUFFbEI7UUFFQWlFLFFBQVFsRSxLQUFLLENBQUMsd0JBQXdCQTtRQUN0QyxPQUFPcEMsa0ZBQVlBLENBQUNtQyxJQUFJLENBQ3RCO1lBQUVDLE9BQU87UUFBd0IsR0FDakM7WUFBRUMsUUFBUTtRQUFJO0lBRWxCO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0anMtc2Fhcy1hcHAvLi9hcHAvYXBpL2l0ZW1zL3JvdXRlLnRzPzZlZDMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dFJlcXVlc3QsIE5leHRSZXNwb25zZSB9IGZyb20gJ25leHQvc2VydmVyJ1xuaW1wb3J0IHsgZ2V0U2VydmVyU2Vzc2lvbiB9IGZyb20gJ25leHQtYXV0aCdcbmltcG9ydCB7IGF1dGhPcHRpb25zIH0gZnJvbSAnQC9saWIvYXV0aCdcbmltcG9ydCB7IHByaXNtYSB9IGZyb20gJ0AvbGliL3ByaXNtYSdcbmltcG9ydCB7IHogfSBmcm9tICd6b2QnXG5cbmNvbnN0IGl0ZW1TY2hlbWEgPSB6Lm9iamVjdCh7XG4gIG5hbWU6IHouc3RyaW5nKCkubWluKDEsICdOYW1lIGlzIHJlcXVpcmVkJyksXG4gIGRlc2NyaXB0aW9uOiB6LnN0cmluZygpLm9wdGlvbmFsKCksXG4gIHNrdTogei5zdHJpbmcoKS5vcHRpb25hbCgpLFxuICBjYXRlZ29yeTogei5zdHJpbmcoKS5vcHRpb25hbCgpLFxuICB1bml0UHJpY2U6IHoubnVtYmVyKCkubWluKDAsICdVbml0IHByaWNlIG11c3QgYmUgcG9zaXRpdmUnKSxcbiAgY29zdFByaWNlOiB6Lm51bWJlcigpLm1pbigwLCAnQ29zdCBwcmljZSBtdXN0IGJlIHBvc2l0aXZlJykub3B0aW9uYWwoKSxcbiAgY3VycmVuY3k6IHouc3RyaW5nKCkuZGVmYXVsdCgnVVNEJyksXG4gIHRyYWNrSW52ZW50b3J5OiB6LmJvb2xlYW4oKS5kZWZhdWx0KGZhbHNlKSxcbiAgc3RvY2tRdWFudGl0eTogei5udW1iZXIoKS5pbnQoKS5taW4oMCkub3B0aW9uYWwoKSxcbiAgbG93U3RvY2tBbGVydDogei5udW1iZXIoKS5pbnQoKS5taW4oMCkub3B0aW9uYWwoKSxcbiAgdGF4YWJsZTogei5ib29sZWFuKCkuZGVmYXVsdCh0cnVlKSxcbiAgdGF4UmF0ZTogei5udW1iZXIoKS5taW4oMCkubWF4KDEwMCkuZGVmYXVsdCgwKSxcbiAgYWNjb3VudGluZ0NvZGU6IHouc3RyaW5nKCkub3B0aW9uYWwoKSxcbiAgYWN0aXZlOiB6LmJvb2xlYW4oKS5kZWZhdWx0KHRydWUpXG59KVxuXG4vLyBHRVQgL2FwaS9pdGVtcyAtIEdldCBhbGwgaXRlbXNcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBHRVQocmVxdWVzdDogTmV4dFJlcXVlc3QpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCBzZXNzaW9uID0gYXdhaXQgZ2V0U2VydmVyU2Vzc2lvbihhdXRoT3B0aW9ucylcbiAgICBpZiAoIXNlc3Npb24/LnVzZXI/LmlkIHx8ICFzZXNzaW9uPy51c2VyPy5jb21wYW55SWQpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiAnVW5hdXRob3JpemVkJyB9LCB7IHN0YXR1czogNDAxIH0pXG4gICAgfVxuXG4gICAgY29uc3QgeyBzZWFyY2hQYXJhbXMgfSA9IG5ldyBVUkwocmVxdWVzdC51cmwpXG4gICAgY29uc3QgcGFnZSA9IHBhcnNlSW50KHNlYXJjaFBhcmFtcy5nZXQoJ3BhZ2UnKSB8fCAnMScpXG4gICAgY29uc3QgbGltaXQgPSBwYXJzZUludChzZWFyY2hQYXJhbXMuZ2V0KCdsaW1pdCcpIHx8ICc1MCcpXG4gICAgY29uc3Qgc2VhcmNoID0gc2VhcmNoUGFyYW1zLmdldCgnc2VhcmNoJykgfHwgJydcbiAgICBjb25zdCBjYXRlZ29yeSA9IHNlYXJjaFBhcmFtcy5nZXQoJ2NhdGVnb3J5JykgfHwgJydcbiAgICBjb25zdCBzdGF0dXMgPSBzZWFyY2hQYXJhbXMuZ2V0KCdzdGF0dXMnKSB8fCAnJ1xuICAgIGNvbnN0IHRyYWNrSW52ZW50b3J5ID0gc2VhcmNoUGFyYW1zLmdldCgndHJhY2tJbnZlbnRvcnknKVxuICAgIGNvbnN0IGxvd1N0b2NrID0gc2VhcmNoUGFyYW1zLmdldCgnbG93U3RvY2snKSA9PT0gJ3RydWUnXG5cbiAgICBjb25zdCBza2lwID0gKHBhZ2UgLSAxKSAqIGxpbWl0XG5cbiAgICAvLyBCdWlsZCB3aGVyZSBjbGF1c2VcbiAgICBjb25zdCB3aGVyZTogYW55ID0ge1xuICAgICAgY29tcGFueUlkOiBzZXNzaW9uLnVzZXIuY29tcGFueUlkXG4gICAgfVxuXG4gICAgaWYgKHNlYXJjaCkge1xuICAgICAgd2hlcmUuT1IgPSBbXG4gICAgICAgIHsgbmFtZTogeyBjb250YWluczogc2VhcmNoLCBtb2RlOiAnaW5zZW5zaXRpdmUnIH0gfSxcbiAgICAgICAgeyBkZXNjcmlwdGlvbjogeyBjb250YWluczogc2VhcmNoLCBtb2RlOiAnaW5zZW5zaXRpdmUnIH0gfSxcbiAgICAgICAgeyBza3U6IHsgY29udGFpbnM6IHNlYXJjaCwgbW9kZTogJ2luc2Vuc2l0aXZlJyB9IH0sXG4gICAgICAgIHsgY2F0ZWdvcnk6IHsgY29udGFpbnM6IHNlYXJjaCwgbW9kZTogJ2luc2Vuc2l0aXZlJyB9IH1cbiAgICAgIF1cbiAgICB9XG5cbiAgICBpZiAoY2F0ZWdvcnkpIHtcbiAgICAgIHdoZXJlLmNhdGVnb3J5ID0gY2F0ZWdvcnlcbiAgICB9XG5cbiAgICBpZiAoc3RhdHVzID09PSAnYWN0aXZlJykge1xuICAgICAgd2hlcmUuYWN0aXZlID0gdHJ1ZVxuICAgIH0gZWxzZSBpZiAoc3RhdHVzID09PSAnaW5hY3RpdmUnKSB7XG4gICAgICB3aGVyZS5hY3RpdmUgPSBmYWxzZVxuICAgIH1cblxuICAgIGlmICh0cmFja0ludmVudG9yeSA9PT0gJ3RydWUnKSB7XG4gICAgICB3aGVyZS50cmFja0ludmVudG9yeSA9IHRydWVcbiAgICB9IGVsc2UgaWYgKHRyYWNrSW52ZW50b3J5ID09PSAnZmFsc2UnKSB7XG4gICAgICB3aGVyZS50cmFja0ludmVudG9yeSA9IGZhbHNlXG4gICAgfVxuXG4gICAgaWYgKGxvd1N0b2NrKSB7XG4gICAgICB3aGVyZS50cmFja0ludmVudG9yeSA9IHRydWVcbiAgICAgIHdoZXJlLkFORCA9IFtcbiAgICAgICAgeyBzdG9ja1F1YW50aXR5OiB7IG5vdDogbnVsbCB9IH0sXG4gICAgICAgIHsgbG93U3RvY2tBbGVydDogeyBub3Q6IG51bGwgfSB9LFxuICAgICAgICB7IHN0b2NrUXVhbnRpdHk6IHsgbHRlOiBwcmlzbWEuaXRlbS5maWVsZHMubG93U3RvY2tBbGVydCB9IH1cbiAgICAgIF1cbiAgICB9XG5cbiAgICBjb25zdCBbaXRlbXMsIHRvdGFsXSA9IGF3YWl0IFByb21pc2UuYWxsKFtcbiAgICAgIHByaXNtYS5pdGVtLmZpbmRNYW55KHtcbiAgICAgICAgd2hlcmUsXG4gICAgICAgIGluY2x1ZGU6IHtcbiAgICAgICAgICBjcmVhdGVkQnk6IHtcbiAgICAgICAgICAgIHNlbGVjdDoge1xuICAgICAgICAgICAgICBuYW1lOiB0cnVlLFxuICAgICAgICAgICAgICBlbWFpbDogdHJ1ZVxuICAgICAgICAgICAgfVxuICAgICAgICAgIH0sXG4gICAgICAgICAgcXVvdGF0aW9uSXRlbXM6IHtcbiAgICAgICAgICAgIHNlbGVjdDoge1xuICAgICAgICAgICAgICBpZDogdHJ1ZSxcbiAgICAgICAgICAgICAgcXVhbnRpdHk6IHRydWUsXG4gICAgICAgICAgICAgIHF1b3RhdGlvbjoge1xuICAgICAgICAgICAgICAgIHNlbGVjdDoge1xuICAgICAgICAgICAgICAgICAgaWQ6IHRydWUsXG4gICAgICAgICAgICAgICAgICBxdW90YXRpb25OdW1iZXI6IHRydWVcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9LFxuICAgICAgICAgIGludm9pY2VJdGVtczoge1xuICAgICAgICAgICAgc2VsZWN0OiB7XG4gICAgICAgICAgICAgIGlkOiB0cnVlLFxuICAgICAgICAgICAgICBxdWFudGl0eTogdHJ1ZSxcbiAgICAgICAgICAgICAgaW52b2ljZToge1xuICAgICAgICAgICAgICAgIHNlbGVjdDoge1xuICAgICAgICAgICAgICAgICAgaWQ6IHRydWUsXG4gICAgICAgICAgICAgICAgICBpbnZvaWNlTnVtYmVyOiB0cnVlXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgICBvcmRlckJ5OiB7IGNyZWF0ZWRBdDogJ2Rlc2MnIH0sXG4gICAgICAgIHNraXAsXG4gICAgICAgIHRha2U6IGxpbWl0XG4gICAgICB9KSxcbiAgICAgIHByaXNtYS5pdGVtLmNvdW50KHsgd2hlcmUgfSlcbiAgICBdKVxuXG4gICAgLy8gQ2FsY3VsYXRlIHVzYWdlIHN0YXRpc3RpY3MgZm9yIGVhY2ggaXRlbVxuICAgIGNvbnN0IGl0ZW1zV2l0aFN0YXRzID0gaXRlbXMubWFwKGl0ZW0gPT4ge1xuICAgICAgY29uc3QgcXVvdGF0aW9uVXNhZ2UgPSBpdGVtLnF1b3RhdGlvbkl0ZW1zLnJlZHVjZSgoc3VtLCBxaSkgPT4gc3VtICsgcWkucXVhbnRpdHksIDApXG4gICAgICBjb25zdCBpbnZvaWNlVXNhZ2UgPSBpdGVtLmludm9pY2VJdGVtcy5yZWR1Y2UoKHN1bSwgaWkpID0+IHN1bSArIGlpLnF1YW50aXR5LCAwKVxuICAgICAgY29uc3QgdG90YWxVc2FnZSA9IHF1b3RhdGlvblVzYWdlICsgaW52b2ljZVVzYWdlXG4gICAgICBjb25zdCB1c2FnZUNvdW50ID0gaXRlbS5xdW90YXRpb25JdGVtcy5sZW5ndGggKyBpdGVtLmludm9pY2VJdGVtcy5sZW5ndGhcblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgaWQ6IGl0ZW0uaWQsXG4gICAgICAgIG5hbWU6IGl0ZW0ubmFtZSxcbiAgICAgICAgZGVzY3JpcHRpb246IGl0ZW0uZGVzY3JpcHRpb24sXG4gICAgICAgIHNrdTogaXRlbS5za3UsXG4gICAgICAgIGNhdGVnb3J5OiBpdGVtLmNhdGVnb3J5LFxuICAgICAgICB1bml0UHJpY2U6IE51bWJlcihpdGVtLnVuaXRQcmljZSksXG4gICAgICAgIGNvc3RQcmljZTogaXRlbS5jb3N0UHJpY2UgPyBOdW1iZXIoaXRlbS5jb3N0UHJpY2UpIDogbnVsbCxcbiAgICAgICAgY3VycmVuY3k6IGl0ZW0uY3VycmVuY3ksXG4gICAgICAgIHRyYWNrSW52ZW50b3J5OiBpdGVtLnRyYWNrSW52ZW50b3J5LFxuICAgICAgICBzdG9ja1F1YW50aXR5OiBpdGVtLnN0b2NrUXVhbnRpdHksXG4gICAgICAgIGxvd1N0b2NrQWxlcnQ6IGl0ZW0ubG93U3RvY2tBbGVydCxcbiAgICAgICAgdGF4YWJsZTogaXRlbS50YXhhYmxlLFxuICAgICAgICB0YXhSYXRlOiBOdW1iZXIoaXRlbS50YXhSYXRlKSxcbiAgICAgICAgYWNjb3VudGluZ0NvZGU6IGl0ZW0uYWNjb3VudGluZ0NvZGUsXG4gICAgICAgIGFjdGl2ZTogaXRlbS5hY3RpdmUsXG4gICAgICAgIGNyZWF0ZWRCeTogaXRlbS5jcmVhdGVkQnksXG4gICAgICAgIGNyZWF0ZWRBdDogaXRlbS5jcmVhdGVkQXQsXG4gICAgICAgIHVwZGF0ZWRBdDogaXRlbS51cGRhdGVkQXQsXG4gICAgICAgIHVzYWdlOiB7XG4gICAgICAgICAgdG90YWxRdWFudGl0eTogdG90YWxVc2FnZSxcbiAgICAgICAgICB1c2FnZUNvdW50LFxuICAgICAgICAgIHF1b3RhdGlvblVzYWdlLFxuICAgICAgICAgIGludm9pY2VVc2FnZSxcbiAgICAgICAgICByZWNlbnRRdW90YXRpb25zOiBpdGVtLnF1b3RhdGlvbkl0ZW1zLnNsaWNlKDAsIDMpLm1hcChxaSA9PiBxaS5xdW90YXRpb24pLFxuICAgICAgICAgIHJlY2VudEludm9pY2VzOiBpdGVtLmludm9pY2VJdGVtcy5zbGljZSgwLCAzKS5tYXAoaWkgPT4gaWkuaW52b2ljZSlcbiAgICAgICAgfSxcbiAgICAgICAgc3RvY2tWYWx1ZTogaXRlbS50cmFja0ludmVudG9yeSAmJiBpdGVtLnN0b2NrUXVhbnRpdHkgXG4gICAgICAgICAgPyBOdW1iZXIoaXRlbS51bml0UHJpY2UpICogaXRlbS5zdG9ja1F1YW50aXR5IFxuICAgICAgICAgIDogMCxcbiAgICAgICAgcHJvZml0TWFyZ2luOiBpdGVtLmNvc3RQcmljZSBcbiAgICAgICAgICA/ICgoTnVtYmVyKGl0ZW0udW5pdFByaWNlKSAtIE51bWJlcihpdGVtLmNvc3RQcmljZSkpIC8gTnVtYmVyKGl0ZW0udW5pdFByaWNlKSkgKiAxMDBcbiAgICAgICAgICA6IG51bGwsXG4gICAgICAgIGlzTG93U3RvY2s6IGl0ZW0udHJhY2tJbnZlbnRvcnkgJiYgaXRlbS5zdG9ja1F1YW50aXR5ICE9PSBudWxsICYmIGl0ZW0ubG93U3RvY2tBbGVydCAhPT0gbnVsbFxuICAgICAgICAgID8gaXRlbS5zdG9ja1F1YW50aXR5IDw9IGl0ZW0ubG93U3RvY2tBbGVydFxuICAgICAgICAgIDogZmFsc2VcbiAgICAgIH1cbiAgICB9KVxuXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgIGl0ZW1zOiBpdGVtc1dpdGhTdGF0cyxcbiAgICAgIHBhZ2luYXRpb246IHtcbiAgICAgICAgcGFnZSxcbiAgICAgICAgbGltaXQsXG4gICAgICAgIHRvdGFsLFxuICAgICAgICBwYWdlczogTWF0aC5jZWlsKHRvdGFsIC8gbGltaXQpXG4gICAgICB9XG4gICAgfSlcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIGl0ZW1zOicsIGVycm9yKVxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgZXJyb3I6ICdGYWlsZWQgdG8gZmV0Y2ggaXRlbXMnIH0sXG4gICAgICB7IHN0YXR1czogNTAwIH1cbiAgICApXG4gIH1cbn1cblxuLy8gUE9TVCAvYXBpL2l0ZW1zIC0gQ3JlYXRlIG5ldyBpdGVtXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gUE9TVChyZXF1ZXN0OiBOZXh0UmVxdWVzdCkge1xuICB0cnkge1xuICAgIGNvbnN0IHNlc3Npb24gPSBhd2FpdCBnZXRTZXJ2ZXJTZXNzaW9uKGF1dGhPcHRpb25zKVxuICAgIGlmICghc2Vzc2lvbj8udXNlcj8uaWQgfHwgIXNlc3Npb24/LnVzZXI/LmNvbXBhbnlJZCkge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgZXJyb3I6ICdVbmF1dGhvcml6ZWQnIH0sIHsgc3RhdHVzOiA0MDEgfSlcbiAgICB9XG5cbiAgICBjb25zdCBib2R5ID0gYXdhaXQgcmVxdWVzdC5qc29uKClcbiAgICBjb25zdCB2YWxpZGF0ZWREYXRhID0gaXRlbVNjaGVtYS5wYXJzZShib2R5KVxuXG4gICAgLy8gQ2hlY2sgZm9yIGR1cGxpY2F0ZSBTS1UgaWYgcHJvdmlkZWRcbiAgICBpZiAodmFsaWRhdGVkRGF0YS5za3UpIHtcbiAgICAgIGNvbnN0IGV4aXN0aW5nSXRlbSA9IGF3YWl0IHByaXNtYS5pdGVtLmZpbmRGaXJzdCh7XG4gICAgICAgIHdoZXJlOiB7XG4gICAgICAgICAgc2t1OiB2YWxpZGF0ZWREYXRhLnNrdSxcbiAgICAgICAgICBjb21wYW55SWQ6IHNlc3Npb24udXNlci5jb21wYW55SWRcbiAgICAgICAgfVxuICAgICAgfSlcblxuICAgICAgaWYgKGV4aXN0aW5nSXRlbSkge1xuICAgICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgICAgeyBlcnJvcjogJ0FuIGl0ZW0gd2l0aCB0aGlzIFNLVSBhbHJlYWR5IGV4aXN0cycgfSxcbiAgICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgICAgKVxuICAgICAgfVxuICAgIH1cblxuICAgIC8vIFZhbGlkYXRlIGludmVudG9yeSBzZXR0aW5nc1xuICAgIGlmICh2YWxpZGF0ZWREYXRhLnRyYWNrSW52ZW50b3J5KSB7XG4gICAgICBpZiAodmFsaWRhdGVkRGF0YS5zdG9ja1F1YW50aXR5ID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICAgIHsgZXJyb3I6ICdTdG9jayBxdWFudGl0eSBpcyByZXF1aXJlZCB3aGVuIGludmVudG9yeSB0cmFja2luZyBpcyBlbmFibGVkJyB9LFxuICAgICAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICAgICApXG4gICAgICB9XG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIENsZWFyIGludmVudG9yeSBmaWVsZHMgaWYgbm90IHRyYWNraW5nXG4gICAgICB2YWxpZGF0ZWREYXRhLnN0b2NrUXVhbnRpdHkgPSB1bmRlZmluZWRcbiAgICAgIHZhbGlkYXRlZERhdGEubG93U3RvY2tBbGVydCA9IHVuZGVmaW5lZFxuICAgIH1cblxuICAgIGNvbnN0IGl0ZW0gPSBhd2FpdCBwcmlzbWEuJHRyYW5zYWN0aW9uKGFzeW5jICh0eCkgPT4ge1xuICAgICAgLy8gQ3JlYXRlIHRoZSBpdGVtXG4gICAgICBjb25zdCBuZXdJdGVtID0gYXdhaXQgdHguaXRlbS5jcmVhdGUoe1xuICAgICAgICBkYXRhOiB7XG4gICAgICAgICAgLi4udmFsaWRhdGVkRGF0YSxcbiAgICAgICAgICBjb21wYW55SWQ6IHNlc3Npb24udXNlci5jb21wYW55SWQhLFxuICAgICAgICAgIGNyZWF0ZWRCeUlkOiBzZXNzaW9uLnVzZXIuaWRcbiAgICAgICAgfSxcbiAgICAgICAgaW5jbHVkZToge1xuICAgICAgICAgIGNyZWF0ZWRCeToge1xuICAgICAgICAgICAgc2VsZWN0OiB7XG4gICAgICAgICAgICAgIG5hbWU6IHRydWUsXG4gICAgICAgICAgICAgIGVtYWlsOiB0cnVlXG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9KVxuXG4gICAgICAvLyBMb2cgYWN0aXZpdHlcbiAgICAgIGF3YWl0IHR4LmFjdGl2aXR5LmNyZWF0ZSh7XG4gICAgICAgIGRhdGE6IHtcbiAgICAgICAgICB0eXBlOiAnSVRFTScsXG4gICAgICAgICAgdGl0bGU6ICdJdGVtIENyZWF0ZWQnLFxuICAgICAgICAgIGRlc2NyaXB0aW9uOiBgSXRlbSBcIiR7dmFsaWRhdGVkRGF0YS5uYW1lfVwiIHdhcyBjcmVhdGVkYCxcbiAgICAgICAgICBpdGVtSWQ6IG5ld0l0ZW0uaWQsXG4gICAgICAgICAgY29tcGFueUlkOiBzZXNzaW9uLnVzZXIuY29tcGFueUlkISxcbiAgICAgICAgICBjcmVhdGVkQnlJZDogc2Vzc2lvbi51c2VyLmlkXG4gICAgICAgIH1cbiAgICAgIH0pXG5cbiAgICAgIHJldHVybiBuZXdJdGVtXG4gICAgfSlcblxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICBpdGVtOiB7XG4gICAgICAgIGlkOiBpdGVtLmlkLFxuICAgICAgICBuYW1lOiBpdGVtLm5hbWUsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBpdGVtLmRlc2NyaXB0aW9uLFxuICAgICAgICBza3U6IGl0ZW0uc2t1LFxuICAgICAgICBjYXRlZ29yeTogaXRlbS5jYXRlZ29yeSxcbiAgICAgICAgdW5pdFByaWNlOiBOdW1iZXIoaXRlbS51bml0UHJpY2UpLFxuICAgICAgICBjb3N0UHJpY2U6IGl0ZW0uY29zdFByaWNlID8gTnVtYmVyKGl0ZW0uY29zdFByaWNlKSA6IG51bGwsXG4gICAgICAgIGN1cnJlbmN5OiBpdGVtLmN1cnJlbmN5LFxuICAgICAgICB0cmFja0ludmVudG9yeTogaXRlbS50cmFja0ludmVudG9yeSxcbiAgICAgICAgc3RvY2tRdWFudGl0eTogaXRlbS5zdG9ja1F1YW50aXR5LFxuICAgICAgICBsb3dTdG9ja0FsZXJ0OiBpdGVtLmxvd1N0b2NrQWxlcnQsXG4gICAgICAgIHRheGFibGU6IGl0ZW0udGF4YWJsZSxcbiAgICAgICAgdGF4UmF0ZTogTnVtYmVyKGl0ZW0udGF4UmF0ZSksXG4gICAgICAgIGFjY291bnRpbmdDb2RlOiBpdGVtLmFjY291bnRpbmdDb2RlLFxuICAgICAgICBhY3RpdmU6IGl0ZW0uYWN0aXZlLFxuICAgICAgICBjcmVhdGVkQnk6IGl0ZW0uY3JlYXRlZEJ5LFxuICAgICAgICBjcmVhdGVkQXQ6IGl0ZW0uY3JlYXRlZEF0LFxuICAgICAgICB1cGRhdGVkQXQ6IGl0ZW0udXBkYXRlZEF0LFxuICAgICAgICB1c2FnZToge1xuICAgICAgICAgIHRvdGFsUXVhbnRpdHk6IDAsXG4gICAgICAgICAgdXNhZ2VDb3VudDogMCxcbiAgICAgICAgICBxdW90YXRpb25Vc2FnZTogMCxcbiAgICAgICAgICBpbnZvaWNlVXNhZ2U6IDAsXG4gICAgICAgICAgcmVjZW50UXVvdGF0aW9uczogW10sXG4gICAgICAgICAgcmVjZW50SW52b2ljZXM6IFtdXG4gICAgICAgIH0sXG4gICAgICAgIHN0b2NrVmFsdWU6IGl0ZW0udHJhY2tJbnZlbnRvcnkgJiYgaXRlbS5zdG9ja1F1YW50aXR5IFxuICAgICAgICAgID8gTnVtYmVyKGl0ZW0udW5pdFByaWNlKSAqIGl0ZW0uc3RvY2tRdWFudGl0eSBcbiAgICAgICAgICA6IDAsXG4gICAgICAgIHByb2ZpdE1hcmdpbjogaXRlbS5jb3N0UHJpY2UgXG4gICAgICAgICAgPyAoKE51bWJlcihpdGVtLnVuaXRQcmljZSkgLSBOdW1iZXIoaXRlbS5jb3N0UHJpY2UpKSAvIE51bWJlcihpdGVtLnVuaXRQcmljZSkpICogMTAwXG4gICAgICAgICAgOiBudWxsLFxuICAgICAgICBpc0xvd1N0b2NrOiBmYWxzZVxuICAgICAgfSxcbiAgICAgIG1lc3NhZ2U6ICdJdGVtIGNyZWF0ZWQgc3VjY2Vzc2Z1bGx5J1xuICAgIH0pXG5cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBpZiAoZXJyb3IgaW5zdGFuY2VvZiB6LlpvZEVycm9yKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHsgZXJyb3I6ICdWYWxpZGF0aW9uIGZhaWxlZCcsIGRldGFpbHM6IGVycm9yLmVycm9ycyB9LFxuICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgIClcbiAgICB9XG5cbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjcmVhdGluZyBpdGVtOicsIGVycm9yKVxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgZXJyb3I6ICdGYWlsZWQgdG8gY3JlYXRlIGl0ZW0nIH0sXG4gICAgICB7IHN0YXR1czogNTAwIH1cbiAgICApXG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJOZXh0UmVzcG9uc2UiLCJnZXRTZXJ2ZXJTZXNzaW9uIiwiYXV0aE9wdGlvbnMiLCJwcmlzbWEiLCJ6IiwiaXRlbVNjaGVtYSIsIm9iamVjdCIsIm5hbWUiLCJzdHJpbmciLCJtaW4iLCJkZXNjcmlwdGlvbiIsIm9wdGlvbmFsIiwic2t1IiwiY2F0ZWdvcnkiLCJ1bml0UHJpY2UiLCJudW1iZXIiLCJjb3N0UHJpY2UiLCJjdXJyZW5jeSIsImRlZmF1bHQiLCJ0cmFja0ludmVudG9yeSIsImJvb2xlYW4iLCJzdG9ja1F1YW50aXR5IiwiaW50IiwibG93U3RvY2tBbGVydCIsInRheGFibGUiLCJ0YXhSYXRlIiwibWF4IiwiYWNjb3VudGluZ0NvZGUiLCJhY3RpdmUiLCJHRVQiLCJyZXF1ZXN0Iiwic2Vzc2lvbiIsInVzZXIiLCJpZCIsImNvbXBhbnlJZCIsImpzb24iLCJlcnJvciIsInN0YXR1cyIsInNlYXJjaFBhcmFtcyIsIlVSTCIsInVybCIsInBhZ2UiLCJwYXJzZUludCIsImdldCIsImxpbWl0Iiwic2VhcmNoIiwibG93U3RvY2siLCJza2lwIiwid2hlcmUiLCJPUiIsImNvbnRhaW5zIiwibW9kZSIsIkFORCIsIm5vdCIsImx0ZSIsIml0ZW0iLCJmaWVsZHMiLCJpdGVtcyIsInRvdGFsIiwiUHJvbWlzZSIsImFsbCIsImZpbmRNYW55IiwiaW5jbHVkZSIsImNyZWF0ZWRCeSIsInNlbGVjdCIsImVtYWlsIiwicXVvdGF0aW9uSXRlbXMiLCJxdWFudGl0eSIsInF1b3RhdGlvbiIsInF1b3RhdGlvbk51bWJlciIsImludm9pY2VJdGVtcyIsImludm9pY2UiLCJpbnZvaWNlTnVtYmVyIiwib3JkZXJCeSIsImNyZWF0ZWRBdCIsInRha2UiLCJjb3VudCIsIml0ZW1zV2l0aFN0YXRzIiwibWFwIiwicXVvdGF0aW9uVXNhZ2UiLCJyZWR1Y2UiLCJzdW0iLCJxaSIsImludm9pY2VVc2FnZSIsImlpIiwidG90YWxVc2FnZSIsInVzYWdlQ291bnQiLCJsZW5ndGgiLCJOdW1iZXIiLCJ1cGRhdGVkQXQiLCJ1c2FnZSIsInRvdGFsUXVhbnRpdHkiLCJyZWNlbnRRdW90YXRpb25zIiwic2xpY2UiLCJyZWNlbnRJbnZvaWNlcyIsInN0b2NrVmFsdWUiLCJwcm9maXRNYXJnaW4iLCJpc0xvd1N0b2NrIiwicGFnaW5hdGlvbiIsInBhZ2VzIiwiTWF0aCIsImNlaWwiLCJjb25zb2xlIiwiUE9TVCIsImJvZHkiLCJ2YWxpZGF0ZWREYXRhIiwicGFyc2UiLCJleGlzdGluZ0l0ZW0iLCJmaW5kRmlyc3QiLCJ1bmRlZmluZWQiLCIkdHJhbnNhY3Rpb24iLCJ0eCIsIm5ld0l0ZW0iLCJjcmVhdGUiLCJkYXRhIiwiY3JlYXRlZEJ5SWQiLCJhY3Rpdml0eSIsInR5cGUiLCJ0aXRsZSIsIml0ZW1JZCIsIm1lc3NhZ2UiLCJab2RFcnJvciIsImRldGFpbHMiLCJlcnJvcnMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/api/items/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/github */ \"(rsc)/./node_modules/next-auth/providers/github.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n\nconst authOptions = {\n    // Using JWT strategy instead of database adapter for now\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                try {\n                    if (!credentials?.email || !credentials?.password) {\n                        console.log(\"Missing credentials\");\n                        return null;\n                    }\n                    console.log(\"Attempting to authenticate user:\", credentials.email);\n                    const user = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.findUnique({\n                        where: {\n                            email: credentials.email\n                        },\n                        select: {\n                            id: true,\n                            email: true,\n                            name: true,\n                            password: true,\n                            role: true,\n                            companyId: true\n                        }\n                    });\n                    // Get the company ID - either as member or owner\n                    let finalCompanyId = user?.companyId;\n                    if (!finalCompanyId && user) {\n                        const ownedCompany = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.company.findFirst({\n                            where: {\n                                ownerId: user.id\n                            },\n                            select: {\n                                id: true\n                            }\n                        });\n                        finalCompanyId = ownedCompany?.id;\n                        // If user is company owner, update their companyId for future queries\n                        if (finalCompanyId) {\n                            await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                                where: {\n                                    id: user.id\n                                },\n                                data: {\n                                    companyId: finalCompanyId\n                                }\n                            });\n                        }\n                    }\n                    if (!user) {\n                        console.log(\"User not found:\", credentials.email);\n                        return null;\n                    }\n                    if (!user.password) {\n                        console.log(\"User has no password set:\", credentials.email);\n                        return null;\n                    }\n                    const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_3___default().compare(credentials.password, user.password);\n                    if (!isPasswordValid) {\n                        console.log(\"Invalid password for user:\", credentials.email);\n                        return null;\n                    }\n                    // Update last login\n                    await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                        where: {\n                            id: user.id\n                        },\n                        data: {\n                            lastLoginAt: new Date(),\n                            loginCount: {\n                                increment: 1\n                            }\n                        }\n                    });\n                    console.log(\"User authenticated successfully:\", user.email);\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name,\n                        role: user.role,\n                        companyId: finalCompanyId\n                    };\n                } catch (error) {\n                    console.error(\"Authentication error:\", error);\n                    return null;\n                }\n            }\n        }),\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            clientId: process.env.GITHUB_CLIENT_ID,\n            clientSecret: process.env.GITHUB_CLIENT_SECRET\n        })\n    ],\n    session: {\n        strategy: \"jwt\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                console.log(\"JWT callback - user data:\", {\n                    id: user.id,\n                    email: user.email,\n                    role: user.role,\n                    companyId: user.companyId\n                });\n                token.role = user.role;\n                token.companyId = user.companyId;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n                session.user.companyId = token.companyId;\n                // Ensure no company object is ever set on the session\n                if (session.user.company) {\n                    delete session.user.company;\n                }\n                console.log(\"Session callback - final session:\", {\n                    id: session.user.id,\n                    email: session.user.email,\n                    role: session.user.role,\n                    companyId: session.user.companyId\n                });\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        signUp: \"/auth/signup\",\n        error: \"/auth/error\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zYWFzLWFwcC8uL2xpYi9wcmlzbWEudHM/OTgyMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/zod","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/lru-cache","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fitems%2Froute&page=%2Fapi%2Fitems%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fitems%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();