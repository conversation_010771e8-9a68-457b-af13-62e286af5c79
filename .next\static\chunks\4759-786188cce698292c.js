(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4759],{49168:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Crown",[["path",{d:"m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7zm3 16h14",key:"zkxr6b"}]])},99670:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},17472:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("MoreHorizontal",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},49617:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},9883:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},64280:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},41827:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},49036:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},52431:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("UserCheck",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]])},73056:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("UserX",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]])},25750:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},82549:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},24033:function(e,t,n){e.exports=n(15313)},99808:function(e,t,n){"use strict";/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=n(2265),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=r.useState,u=r.useEffect,a=r.useLayoutEffect,s=r.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!o(e,n)}catch(e){return!0}}var l="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=i({inst:{value:n,getSnapshot:t}}),o=r[0].inst,l=r[1];return a(function(){o.value=n,o.getSnapshot=t,c(o)&&l({inst:o})},[e,n,t]),u(function(){return c(o)&&l({inst:o}),e(function(){c(o)&&l({inst:o})})},[e]),s(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:l},26272:function(e,t,n){"use strict";e.exports=n(99808)},61465:function(e,t,n){"use strict";n.d(t,{NY:function(){return b},Ee:function(){return N},fC:function(){return M}});var r=n(2265),o=n(56989),i=n(16459),u=n(51030),a=n(9381),s=n(26272);function c(){return()=>{}}var l=n(57437),d="Avatar",[f,p]=(0,o.b)(d),[y,m]=f(d),v=r.forwardRef((e,t)=>{let{__scopeAvatar:n,...o}=e,[i,u]=r.useState("idle");return(0,l.jsx)(y,{scope:n,imageLoadingStatus:i,onImageLoadingStatusChange:u,children:(0,l.jsx)(a.WV.span,{...o,ref:t})})});v.displayName=d;var g="AvatarImage",h=r.forwardRef((e,t)=>{let{__scopeAvatar:n,src:o,onLoadingStatusChange:d=()=>{},...f}=e,p=m(g,n),y=function(e,{referrerPolicy:t,crossOrigin:n}){let o=(0,s.useSyncExternalStore)(c,()=>!0,()=>!1),i=r.useRef(null),a=o?(i.current||(i.current=new window.Image),i.current):null,[l,d]=r.useState(()=>w(a,e));return(0,u.b)(()=>{d(w(a,e))},[a,e]),(0,u.b)(()=>{let e=e=>()=>{d(e)};if(!a)return;let r=e("loaded"),o=e("error");return a.addEventListener("load",r),a.addEventListener("error",o),t&&(a.referrerPolicy=t),"string"==typeof n&&(a.crossOrigin=n),()=>{a.removeEventListener("load",r),a.removeEventListener("error",o)}},[a,n,t]),l}(o,f),v=(0,i.W)(e=>{d(e),p.onImageLoadingStatusChange(e)});return(0,u.b)(()=>{"idle"!==y&&v(y)},[y,v]),"loaded"===y?(0,l.jsx)(a.WV.img,{...f,ref:t,src:o}):null});h.displayName=g;var k="AvatarFallback",x=r.forwardRef((e,t)=>{let{__scopeAvatar:n,delayMs:o,...i}=e,u=m(k,n),[s,c]=r.useState(void 0===o);return r.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>c(!0),o);return()=>window.clearTimeout(e)}},[o]),s&&"loaded"!==u.imageLoadingStatus?(0,l.jsx)(a.WV.span,{...i,ref:t}):null});function w(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}x.displayName=k;var M=v,N=h,b=x},28712:function(e,t,n){"use strict";n.d(t,{Dx:function(){return er},VY:function(){return en},aV:function(){return et},dk:function(){return eo},fC:function(){return J},h_:function(){return ee},x8:function(){return ei},xz:function(){return Q}});var r=n(2265),o=n(85744),i=n(42210),u=n(56989),a=n(20966),s=n(73763),c=n(79249),l=n(52759),d=n(52730),f=n(85606),p=n(9381),y=n(31244),m=n(73386),v=n(85859),g=n(67256),h=n(57437),k="Dialog",[x,w]=(0,u.b)(k),[M,N]=x(k),b=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:i,onOpenChange:u,modal:c=!0}=e,l=r.useRef(null),d=r.useRef(null),[f,p]=(0,s.T)({prop:o,defaultProp:i??!1,onChange:u,caller:k});return(0,h.jsx)(M,{scope:t,triggerRef:l,contentRef:d,contentId:(0,a.M)(),titleId:(0,a.M)(),descriptionId:(0,a.M)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:c,children:n})};b.displayName=k;var E="DialogTrigger",D=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,u=N(E,n),a=(0,i.e)(t,u.triggerRef);return(0,h.jsx)(p.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":u.open,"aria-controls":u.contentId,"data-state":$(u.open),...r,ref:a,onClick:(0,o.M)(e.onClick,u.onOpenToggle)})});D.displayName=E;var R="DialogPortal",[O,j]=x(R,{forceMount:void 0}),I=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:i}=e,u=N(R,t);return(0,h.jsx)(O,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,h.jsx)(f.z,{present:n||u.open,children:(0,h.jsx)(d.h,{asChild:!0,container:i,children:e})}))})};I.displayName=R;var C="DialogOverlay",S=r.forwardRef((e,t)=>{let n=j(C,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=N(C,e.__scopeDialog);return i.modal?(0,h.jsx)(f.z,{present:r||i.open,children:(0,h.jsx)(T,{...o,ref:t})}):null});S.displayName=C;var Z=(0,g.Z8)("DialogOverlay.RemoveScroll"),T=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=N(C,n);return(0,h.jsx)(m.Z,{as:Z,allowPinchZoom:!0,shards:[o.contentRef],children:(0,h.jsx)(p.WV.div,{"data-state":$(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),_="DialogContent",A=r.forwardRef((e,t)=>{let n=j(_,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=N(_,e.__scopeDialog);return(0,h.jsx)(f.z,{present:r||i.open,children:i.modal?(0,h.jsx)(F,{...o,ref:t}):(0,h.jsx)(P,{...o,ref:t})})});A.displayName=_;var F=r.forwardRef((e,t)=>{let n=N(_,e.__scopeDialog),u=r.useRef(null),a=(0,i.e)(t,n.contentRef,u);return r.useEffect(()=>{let e=u.current;if(e)return(0,v.Ry)(e)},[]),(0,h.jsx)(W,{...e,ref:a,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{e.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:(0,o.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault())})}),P=r.forwardRef((e,t)=>{let n=N(_,e.__scopeDialog),o=r.useRef(!1),i=r.useRef(!1);return(0,h.jsx)(W,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(o.current||n.triggerRef.current?.focus(),t.preventDefault()),o.current=!1,i.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(i.current=!0));let r=t.target;n.triggerRef.current?.contains(r)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),W=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:u,onCloseAutoFocus:a,...s}=e,d=N(_,n),f=r.useRef(null),p=(0,i.e)(t,f);return(0,y.EW)(),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(l.M,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:u,onUnmountAutoFocus:a,children:(0,h.jsx)(c.XB,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":$(d.open),...s,ref:p,onDismiss:()=>d.onOpenChange(!1)})}),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(K,{titleId:d.titleId}),(0,h.jsx)(G,{contentRef:f,descriptionId:d.descriptionId})]})]})}),L="DialogTitle",U=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=N(L,n);return(0,h.jsx)(p.WV.h2,{id:o.titleId,...r,ref:t})});U.displayName=L;var V="DialogDescription",z=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=N(V,n);return(0,h.jsx)(p.WV.p,{id:o.descriptionId,...r,ref:t})});z.displayName=V;var q="DialogClose",H=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=N(q,n);return(0,h.jsx)(p.WV.button,{type:"button",...r,ref:t,onClick:(0,o.M)(e.onClick,()=>i.onOpenChange(!1))})});function $(e){return e?"open":"closed"}H.displayName=q;var B="DialogTitleWarning",[X,Y]=(0,u.k)(B,{contentName:_,titleName:L,docsSlug:"dialog"}),K=({titleId:e})=>{let t=Y(B),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return r.useEffect(()=>{e&&!document.getElementById(e)&&console.error(n)},[n,e]),null},G=({contentRef:e,descriptionId:t})=>{let n=Y("DialogDescriptionWarning"),o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${n.contentName}}.`;return r.useEffect(()=>{let n=e.current?.getAttribute("aria-describedby");t&&n&&!document.getElementById(t)&&console.warn(o)},[o,e,t]),null},J=b,Q=D,ee=I,et=S,en=A,er=U,eo=z,ei=H},85606:function(e,t,n){"use strict";n.d(t,{z:function(){return u}});var r=n(2265),o=n(42210),i=n(51030),u=e=>{let t,n;let{present:u,children:s}=e,c=function(e){var t,n;let[o,u]=r.useState(),s=r.useRef(null),c=r.useRef(e),l=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>n[e][t]??e,t));return r.useEffect(()=>{let e=a(s.current);l.current="mounted"===d?e:"none"},[d]),(0,i.b)(()=>{let t=s.current,n=c.current;if(n!==e){let r=l.current,o=a(t);e?f("MOUNT"):"none"===o||t?.display==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),c.current=e}},[e,f]),(0,i.b)(()=>{if(o){let e;let t=o.ownerDocument.defaultView??window,n=n=>{let r=a(s.current).includes(n.animationName);if(n.target===o&&r&&(f("ANIMATION_END"),!c.current)){let n=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=n)})}},r=e=>{e.target===o&&(l.current=a(s.current))};return o.addEventListener("animationstart",r),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",r),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{s.current=e?getComputedStyle(e):null,u(e)},[])}}(u),l="function"==typeof s?s({present:c.isPresent}):r.Children.only(s),d=(0,o.e)(c.ref,(t=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning?l.ref:(t=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning?l.props.ref:l.props.ref||l.ref);return"function"==typeof s||c.isPresent?r.cloneElement(l,{ref:d}):null};function a(e){return e?.animationName||"none"}u.displayName="Presence"}}]);