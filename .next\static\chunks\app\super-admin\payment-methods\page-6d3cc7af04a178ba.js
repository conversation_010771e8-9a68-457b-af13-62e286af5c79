(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8144],{84826:function(e,s,r){Promise.resolve().then(r.bind(r,387))},387:function(e,s,r){"use strict";r.r(s),r.d(s,{default:function(){return v}});var a=r(57437),t=r(2265),n=r(82749),d=r(24033),i=r(27815),c=r(85754),l=r(49842),o=r(86443),u=r(40110),m=r(71738),x=r(22812),f=r(34822),h=r(13546);r(94286);var p=r(21271),b=r(64280),g=r(92295),j=r(5925);function v(){var e;let{data:s,status:r}=(0,n.useSession)(),[v,y]=(0,t.useState)([]),[N,C]=(0,t.useState)({creditCard:{enabled:!0,supportedBrands:["visa","mastercard","amex","discover"],requireCVV:!0,require3DS:!1},debitCard:{enabled:!0,supportedBrands:["visa","mastercard"],requireCVV:!0,require3DS:!1},applePay:{enabled:!1,merchantId:""},googlePay:{enabled:!1,merchantId:""},paypal:{enabled:!1,clientId:""},netBanking:{enabled:!1,supportedBanks:[]},ach:{enabled:!1,verificationMethod:"instant"},wire:{enabled:!1,requireManualApproval:!0},upi:{enabled:!1,supportedApps:["gpay","phonepe","paytm"],qrCodeEnabled:!0},bitcoin:{enabled:!1,walletAddress:""},ethereum:{enabled:!1,walletAddress:""},klarna:{enabled:!1,clientId:""},afterpay:{enabled:!1,merchantId:""}}),[w,k]=(0,t.useState)(!0),[S,V]=(0,t.useState)(!1);if("loading"===r)return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})});"unauthenticated"===r&&(0,d.redirect)("/auth/signin"),(null==s?void 0:null===(e=s.user)||void 0===e?void 0:e.role)!=="SUPER_ADMIN"&&(0,d.redirect)("/dashboard");let B=async()=>{try{k(!0);let e=await fetch("/api/super-admin/payment-methods"),s=await e.json();s.success&&(y(s.methods),C({...N,...s.config}))}catch(e){console.error("Error fetching payment methods:",e),j.toast.error("Failed to load payment methods")}finally{k(!1)}},Z=async()=>{try{V(!0);let e=await fetch("/api/super-admin/payment-methods",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(N)}),s=await e.json();s.success?(j.toast.success("Payment methods configuration saved successfully"),B()):j.toast.error(s.error||"Failed to save configuration")}catch(e){console.error("Error saving config:",e),j.toast.error("Failed to save configuration")}finally{V(!1)}};(0,t.useEffect)(()=>{B()},[]);let P=(e,s,r)=>{C(a=>({...a,[e]:{...a[e],[s]:r}}))};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(p.Z,{className:"h-8 w-8 text-blue-600"}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Payment Methods"})]}),(0,a.jsx)("p",{className:"text-gray-500 mt-1",children:"Configure available payment options for customers"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(c.z,{variant:"outline",onClick:B,disabled:w,children:[(0,a.jsx)(b.Z,{className:"h-4 w-4 mr-2 ".concat(w?"animate-spin":"")}),"Refresh"]}),(0,a.jsxs)(c.z,{onClick:Z,disabled:S,children:[(0,a.jsx)(g.Z,{className:"h-4 w-4 mr-2 ".concat(S?"animate-spin":"")}),"Save Configuration"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsx)(i.Zb,{children:(0,a.jsx)(i.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Card Payments"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:(N.creditCard.enabled?1:0)+(N.debitCard.enabled?1:0)})]}),(0,a.jsx)(m.Z,{className:"h-8 w-8 text-blue-600"})]})})}),(0,a.jsx)(i.Zb,{children:(0,a.jsx)(i.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Digital Wallets"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-green-600",children:(N.applePay.enabled?1:0)+(N.googlePay.enabled?1:0)+(N.paypal.enabled?1:0)})]}),(0,a.jsx)(f.Z,{className:"h-8 w-8 text-green-600"})]})})}),(0,a.jsx)(i.Zb,{children:(0,a.jsx)(i.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Bank Transfers"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-purple-600",children:(N.netBanking.enabled?1:0)+(N.ach.enabled?1:0)+(N.wire.enabled?1:0)})]}),(0,a.jsx)(x.Z,{className:"h-8 w-8 text-purple-600"})]})})}),(0,a.jsx)(i.Zb,{children:(0,a.jsx)(i.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Alternative Methods"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-orange-600",children:(N.upi.enabled?1:0)+(N.bitcoin.enabled?1:0)+(N.klarna.enabled?1:0)})]}),(0,a.jsx)(h.Z,{className:"h-8 w-8 text-orange-600"})]})})})]}),(0,a.jsxs)(u.mQ,{defaultValue:"cards",className:"space-y-6",children:[(0,a.jsxs)(u.dr,{className:"grid w-full grid-cols-5",children:[(0,a.jsx)(u.SP,{value:"cards",children:"Cards"}),(0,a.jsx)(u.SP,{value:"wallets",children:"Digital Wallets"}),(0,a.jsx)(u.SP,{value:"banking",children:"Banking"}),(0,a.jsx)(u.SP,{value:"alternative",children:"Alternative"}),(0,a.jsx)(u.SP,{value:"bnpl",children:"Buy Now Pay Later"})]}),(0,a.jsx)(u.nU,{value:"cards",children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{children:[(0,a.jsxs)(i.ll,{className:"flex items-center",children:[(0,a.jsx)(m.Z,{className:"h-5 w-5 mr-2"}),"Credit Cards"]}),(0,a.jsx)(i.SZ,{children:"Configure credit card payment acceptance"})]}),(0,a.jsxs)(i.aY,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(l._,{htmlFor:"creditCardEnabled",className:"text-base font-medium",children:"Accept Credit Cards"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Enable credit card payments via Visa, Mastercard, Amex, etc."})]}),(0,a.jsx)(o.r,{id:"creditCardEnabled",checked:N.creditCard.enabled,onCheckedChange:e=>P("creditCard","enabled",e)})]}),N.creditCard.enabled&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(l._,{children:"Supported Card Brands"}),(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-2",children:["visa","mastercard","amex","discover","jcb","diners"].map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",id:"credit-".concat(e),checked:N.creditCard.supportedBrands.includes(e),onChange:s=>{P("creditCard","supportedBrands",s.target.checked?[...N.creditCard.supportedBrands,e]:N.creditCard.supportedBrands.filter(s=>s!==e))},className:"rounded"}),(0,a.jsx)(l._,{htmlFor:"credit-".concat(e),className:"text-sm capitalize",children:e})]},e))})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(l._,{htmlFor:"creditCVV",className:"text-base font-medium",children:"Require CVV"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Require CVV code for enhanced security"})]}),(0,a.jsx)(o.r,{id:"creditCVV",checked:N.creditCard.requireCVV,onCheckedChange:e=>P("creditCard","requireCVV",e)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(l._,{htmlFor:"credit3DS",className:"text-base font-medium",children:"3D Secure"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Enable 3D Secure authentication"})]}),(0,a.jsx)(o.r,{id:"credit3DS",checked:N.creditCard.require3DS,onCheckedChange:e=>P("creditCard","require3DS",e)})]})]})]})]})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{children:[(0,a.jsxs)(i.ll,{className:"flex items-center",children:[(0,a.jsx)(m.Z,{className:"h-5 w-5 mr-2"}),"Debit Cards"]}),(0,a.jsx)(i.SZ,{children:"Configure debit card payment acceptance"})]}),(0,a.jsxs)(i.aY,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(l._,{htmlFor:"debitCardEnabled",className:"text-base font-medium",children:"Accept Debit Cards"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Enable debit card payments"})]}),(0,a.jsx)(o.r,{id:"debitCardEnabled",checked:N.debitCard.enabled,onCheckedChange:e=>P("debitCard","enabled",e)})]}),N.debitCard.enabled&&(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(l._,{children:"Supported Card Brands"}),(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-2",children:["visa","mastercard","maestro","rupay"].map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",id:"debit-".concat(e),checked:N.debitCard.supportedBrands.includes(e),onChange:s=>{P("debitCard","supportedBrands",s.target.checked?[...N.debitCard.supportedBrands,e]:N.debitCard.supportedBrands.filter(s=>s!==e))},className:"rounded"}),(0,a.jsx)(l._,{htmlFor:"debit-".concat(e),className:"text-sm capitalize",children:e})]},e))})]})})]})]})]})})]})]})}},85754:function(e,s,r){"use strict";r.d(s,{z:function(){return l}});var a=r(57437),t=r(2265),n=r(67256),d=r(96061),i=r(1657);let c=(0,d.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=t.forwardRef((e,s)=>{let{className:r,variant:t,size:d,asChild:l=!1,...o}=e,u=l?n.g7:"button";return(0,a.jsx)(u,{className:(0,i.cn)(c({variant:t,size:d,className:r})),ref:s,...o})});l.displayName="Button"},27815:function(e,s,r){"use strict";r.d(s,{Ol:function(){return i},SZ:function(){return l},Zb:function(){return d},aY:function(){return o},eW:function(){return u},ll:function(){return c}});var a=r(57437),t=r(2265),n=r(1657);let d=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...t})});d.displayName="Card";let i=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...t})});i.displayName="CardHeader";let c=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("h3",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",r),...t})});c.displayName="CardTitle";let l=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",r),...t})});l.displayName="CardDescription";let o=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("p-6 pt-0",r),...t})});o.displayName="CardContent";let u=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",r),...t})});u.displayName="CardFooter"},49842:function(e,s,r){"use strict";r.d(s,{_:function(){return l}});var a=r(57437),t=r(2265),n=r(36743),d=r(96061),i=r(1657);let c=(0,d.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)(n.f,{ref:s,className:(0,i.cn)(c(),r),...t})});l.displayName=n.f.displayName},86443:function(e,s,r){"use strict";r.d(s,{r:function(){return i}});var a=r(57437),t=r(2265),n=r(92376),d=r(1657);let i=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)(n.fC,{className:(0,d.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",r),...t,ref:s,children:(0,a.jsx)(n.bU,{className:(0,d.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});i.displayName=n.fC.displayName},40110:function(e,s,r){"use strict";r.d(s,{SP:function(){return l},dr:function(){return c},mQ:function(){return i},nU:function(){return o}});var a=r(57437),t=r(2265),n=r(34522),d=r(1657);let i=n.fC,c=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)(n.aV,{ref:s,className:(0,d.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",r),...t})});c.displayName=n.aV.displayName;let l=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)(n.xz,{ref:s,className:(0,d.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",r),...t})});l.displayName=n.xz.displayName;let o=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)(n.VY,{ref:s,className:(0,d.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",r),...t})});o.displayName=n.VY.displayName},1657:function(e,s,r){"use strict";r.d(s,{cn:function(){return n}});var a=r(57042),t=r(74769);function n(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return(0,t.m6)((0,a.W)(s))}}},function(e){e.O(0,[6723,9502,2749,4522,7474,2971,4938,1744],function(){return e(e.s=84826)}),_N_E=e.O()}]);