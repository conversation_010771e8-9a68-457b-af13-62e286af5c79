import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get branding configuration from database
    const configs = await prisma.brandingConfig.findMany().catch(() => [])
    
    // Convert array to object for easier access
    const configObject = configs.reduce((acc, config) => {
      let value = config.value
      
      // Parse JSON values
      if (config.type === 'boolean') {
        value = value === 'true'
      } else if (config.type === 'number') {
        value = parseInt(value)
      } else if (config.type === 'json') {
        try {
          value = JSON.parse(value)
        } catch (e) {
          value = {}
        }
      }
      
      acc[config.key] = value
      return acc
    }, {} as Record<string, any>)

    return NextResponse.json({
      success: true,
      branding: configObject
    })
  } catch (error) {
    console.error('Error fetching branding config:', error)
    return NextResponse.json({
      success: true,
      branding: {} // Return empty object if table doesn't exist yet
    })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const branding = await request.json()

    // Update or create branding configuration entries
    const updates = []
    
    for (const [key, value] of Object.entries(branding)) {
      let stringValue = String(value)
      let type = 'string'
      
      if (typeof value === 'boolean') {
        type = 'boolean'
        stringValue = value.toString()
      } else if (typeof value === 'number') {
        type = 'number'
        stringValue = value.toString()
      } else if (typeof value === 'object' && value !== null) {
        type = 'json'
        stringValue = JSON.stringify(value)
      }

      try {
        updates.push(
          prisma.brandingConfig.upsert({
            where: { key },
            update: {
              value: stringValue,
              type,
              updatedAt: new Date()
            },
            create: {
              key,
              value: stringValue,
              type,
              description: `Branding configuration for ${key}`
            }
          })
        )
      } catch (error) {
        console.error(`Error updating branding config for ${key}:`, error)
      }
    }

    try {
      await Promise.all(updates)
    } catch (error) {
      console.error('Error saving branding config:', error)
      // Continue even if some updates fail
    }

    // Log the configuration change
    try {
      await prisma.auditLog.create({
        data: {
          action: 'UPDATE_BRANDING_CONFIG',
          entityType: 'BRANDING_CONFIG',
          entityId: 'branding',
          userId: session.user.id,
          details: {
            updatedKeys: Object.keys(branding),
            timestamp: new Date().toISOString()
          }
        }
      })
    } catch (error) {
      // Ignore audit log errors
      console.error('Error creating audit log:', error)
    }

    return NextResponse.json({
      success: true,
      message: 'Branding configuration updated successfully'
    })
  } catch (error) {
    console.error('Error updating branding config:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
