'use client'

import { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  TrendingUp,
  TrendingDown,
  Users,
  DollarSign,
  Target,
  Clock,
  Award,
  BarChart3,
  Pie<PERSON>hart,
  Calendar,
  RefreshCw
} from 'lucide-react'
import { toast } from 'react-hot-toast'

interface ConversionAnalytics {
  summary: {
    totalLeads: number
    convertedLeads: number
    conversionRate: number
    totalConversions: number
    totalConversionValue: number
    averageConversionValue: number
    averageConversionTime: number
  }
  conversionsByType: Array<{
    type: string
    count: number
    totalValue: number
    averageValue: number
  }>
  conversionsByMonth: Array<{
    month: string
    conversions: number
    total_value: number
  }>
  topPerformers: Array<{
    salesRepId: string
    _count: { id: number }
    _sum: { conversionValue: number }
    salesRep: {
      id: string
      name: string
      email: string
    }
    conversionRate: number
  }>
  conversionFunnel: Array<{
    stage: string
    count: number
    percentage: number
  }>
  sourceAnalysis: Array<{
    source: string
    totalLeads: number
    convertedLeads: number
    conversionRate: number
  }>
  recentConversions: Array<{
    id: string
    conversionType: string
    conversionValue: number
    createdAt: string
    lead: {
      firstName: string
      lastName: string
      companyName: string
    }
    customer: {
      name: string
    }
  }>
  trends: {
    conversionRate: number
    averageValue: number
    timeToConvert: number
  }
}

export function ConversionAnalytics() {
  const [analytics, setAnalytics] = useState<ConversionAnalytics | null>(null)
  const [loading, setLoading] = useState(true)
  const [period, setPeriod] = useState('30')

  const fetchAnalytics = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/leads/conversions/analytics?period=${period}`)
      if (!response.ok) {
        throw new Error('Failed to fetch analytics')
      }

      const data = await response.json()
      setAnalytics(data)
    } catch (error) {
      toast.error('Failed to load conversion analytics')
      console.error('Error fetching analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchAnalytics()
  }, [period])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!analytics) {
    return (
      <div className="text-center py-8 text-gray-500">
        <BarChart3 className="h-12 w-12 mx-auto mb-4 text-gray-300" />
        <p>Failed to load analytics data</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">Conversion Analytics</h2>
        <div className="flex items-center space-x-2">
          <Select value={period} onValueChange={setPeriod}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">Last 7 days</SelectItem>
              <SelectItem value="30">Last 30 days</SelectItem>
              <SelectItem value="90">Last 90 days</SelectItem>
              <SelectItem value="365">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={fetchAnalytics} size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-blue-100 rounded-full">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Total Leads</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.summary.totalLeads}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-green-100 rounded-full">
                <Target className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Converted</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.summary.convertedLeads}</p>
                <p className="text-xs text-green-600">
                  {formatPercentage(analytics.summary.conversionRate)} rate
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-purple-100 rounded-full">
                <DollarSign className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Total Value</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(analytics.summary.totalConversionValue)}
                </p>
                <p className="text-xs text-purple-600">
                  {formatCurrency(analytics.summary.averageConversionValue)} avg
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-orange-100 rounded-full">
                <Clock className="h-6 w-6 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Avg Time</p>
                <p className="text-2xl font-bold text-gray-900">
                  {Math.round(analytics.summary.averageConversionTime)}d
                </p>
                <p className="text-xs text-orange-600">to convert</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Conversion Funnel */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <BarChart3 className="h-5 w-5 mr-2" />
            Conversion Funnel
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {analytics.conversionFunnel.map((stage, index) => (
              <div key={stage.stage} className="relative">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">{stage.stage}</span>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-500">{stage.count}</span>
                    <span className="text-sm font-medium">{formatPercentage(stage.percentage)}</span>
                  </div>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div
                    className={`h-3 rounded-full ${
                      index === 0 ? 'bg-blue-600' :
                      index === 1 ? 'bg-yellow-600' :
                      index === 2 ? 'bg-orange-600' :
                      'bg-green-600'
                    }`}
                    style={{ width: `${stage.percentage}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Conversions by Type and Source Analysis */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <PieChart className="h-5 w-5 mr-2" />
              Conversions by Type
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.conversionsByType.map((type) => (
                <div key={type.type} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium">{type.type}</p>
                    <p className="text-sm text-gray-500">{type.count} conversions</p>
                  </div>
                  <div className="text-right">
                    <p className="font-bold">{formatCurrency(type.totalValue)}</p>
                    <p className="text-sm text-gray-500">{formatCurrency(type.averageValue)} avg</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Lead Source Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.sourceAnalysis.map((source) => (
                <div key={source.source} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium">{source.source || 'Unknown'}</p>
                    <p className="text-sm text-gray-500">{source.totalLeads} leads</p>
                  </div>
                  <div className="text-right">
                    <p className="font-bold">{source.convertedLeads} converted</p>
                    <p className="text-sm text-gray-500">{formatPercentage(source.conversionRate)}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Performers */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Award className="h-5 w-5 mr-2" />
            Top Performing Sales Reps
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {analytics.topPerformers.slice(0, 5).map((performer, index) => (
              <div key={performer.salesRepId} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full">
                    <span className="text-sm font-bold text-blue-600">#{index + 1}</span>
                  </div>
                  <div>
                    <p className="font-medium">{performer.salesRep?.name || 'Unknown'}</p>
                    <p className="text-sm text-gray-500">{performer.salesRep?.email}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-bold">{performer._count.id} conversions</p>
                  <p className="text-sm text-gray-500">
                    {formatCurrency(performer._sum.conversionValue || 0)} total
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Conversions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="h-5 w-5 mr-2" />
            Recent Conversions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {analytics.recentConversions.map((conversion) => (
              <div key={conversion.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-1">
                    <p className="font-medium">
                      {conversion.lead.firstName} {conversion.lead.lastName}
                    </p>
                    <Badge variant="outline" className="text-xs">
                      {conversion.conversionType}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-500">
                    {conversion.lead.companyName} → {conversion.customer.name}
                  </p>
                  <p className="text-xs text-gray-400">
                    {new Date(conversion.createdAt).toLocaleDateString()}
                  </p>
                </div>
                <div className="text-right">
                  <p className="font-bold text-green-600">
                    {formatCurrency(conversion.conversionValue || 0)}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
