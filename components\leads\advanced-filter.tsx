'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'
import {
  Filter,
  Search,
  X,
  ChevronDown,
  ChevronUp,
  Save,
  Download,
  RefreshCw,
  Star,
  Trash2,
  Plus
} from 'lucide-react'
import { toast } from 'react-hot-toast'

interface FilterState {
  search: string
  status: string[]
  priority: string[]
  source: string[]
  industry: string[]
  companySize: string[]
  scoreMin: number | undefined
  scoreMax: number | undefined
  temperature: string[]
  createdAfter: string
  createdBefore: string
  lastActivityAfter: string
  lastActivityBefore: string
  convertedAfter: string
  convertedBefore: string
  budgetMin: number | undefined
  budgetMax: number | undefined
  hasBudget: boolean | undefined
  hasActivities: boolean | undefined
  activityCountMin: number | undefined
  activityCountMax: number | undefined
  hasRecentActivity: boolean | undefined
  assignedTo: string[]
  unassigned: boolean | undefined
  isQualified: boolean | undefined
  hasPhone: boolean | undefined
  hasEmail: boolean | undefined
  hasWebsite: boolean | undefined
  hasTimeline: boolean | undefined
  isConverted: boolean | undefined
  conversionType: string[]
}

interface SavedFilter {
  id: string
  name: string
  description: string | null
  filters: Record<string, any>
  isPublic: boolean
  isDefault: boolean
  createdBy: {
    id: string
    name: string | null
    email: string
  }
  createdAt: string
}

interface AdvancedFilterProps {
  onFilterChange: (filters: FilterState) => void
  onApplyFilters: (filters: FilterState) => void
  initialFilters?: Partial<FilterState>
  loading?: boolean
}

const initialFilterState: FilterState = {
  search: '',
  status: [],
  priority: [],
  source: [],
  industry: [],
  companySize: [],
  scoreMin: undefined,
  scoreMax: undefined,
  temperature: [],
  createdAfter: '',
  createdBefore: '',
  lastActivityAfter: '',
  lastActivityBefore: '',
  convertedAfter: '',
  convertedBefore: '',
  budgetMin: undefined,
  budgetMax: undefined,
  hasBudget: undefined,
  hasActivities: undefined,
  activityCountMin: undefined,
  activityCountMax: undefined,
  hasRecentActivity: undefined,
  assignedTo: [],
  unassigned: undefined,
  isQualified: undefined,
  hasPhone: undefined,
  hasEmail: undefined,
  hasWebsite: undefined,
  hasTimeline: undefined,
  isConverted: undefined,
  conversionType: []
}

export function AdvancedFilter({
  onFilterChange,
  onApplyFilters,
  initialFilters = {},
  loading = false
}: AdvancedFilterProps) {
  const [filters, setFilters] = useState<FilterState>({ ...initialFilterState, ...initialFilters })
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [savedFilters, setSavedFilters] = useState<SavedFilter[]>([])
  const [showSaveDialog, setShowSaveDialog] = useState(false)
  const [saveFilterName, setSaveFilterName] = useState('')
  const [saveFilterDescription, setSaveFilterDescription] = useState('')
  const [saveAsPublic, setSaveAsPublic] = useState(false)
  const [saveAsDefault, setSaveAsDefault] = useState(false)

  // Collapsible sections state
  const [sectionsOpen, setSectionsOpen] = useState({
    basic: true,
    scoring: false,
    dates: false,
    budget: false,
    activity: false,
    assignment: false,
    qualification: false,
    conversion: false
  })

  useEffect(() => {
    fetchSavedFilters()
  }, [])

  const fetchSavedFilters = async () => {
    try {
      const response = await fetch('/api/leads/saved-filters?includePublic=true')
      if (response.ok) {
        const data = await response.json()
        setSavedFilters(data.savedFilters)
      }
    } catch (error) {
      console.error('Error fetching saved filters:', error)
    }
  }

  const handleFilterChange = (key: keyof FilterState, value: any) => {
    const newFilters = { ...filters, [key]: value }
    setFilters(newFilters)
    onFilterChange(newFilters)
  }

  const handleArrayFilterChange = (key: keyof FilterState, value: string, checked: boolean) => {
    const currentArray = filters[key] as string[]
    let newArray: string[]

    if (checked) {
      newArray = [...currentArray, value]
    } else {
      newArray = currentArray.filter(item => item !== value)
    }

    handleFilterChange(key, newArray)
  }

  const handleApplyFilters = () => {
    onApplyFilters(filters)
  }

  const handleClearFilters = () => {
    const clearedFilters = { ...initialFilterState }
    setFilters(clearedFilters)
    onFilterChange(clearedFilters)
    onApplyFilters(clearedFilters)
  }

  const handleLoadSavedFilter = (savedFilter: SavedFilter) => {
    const loadedFilters = { ...initialFilterState, ...savedFilter.filters }
    setFilters(loadedFilters)
    onFilterChange(loadedFilters)
    onApplyFilters(loadedFilters)
    toast.success(`Loaded filter: ${savedFilter.name}`)
  }

  const handleSaveFilter = async () => {
    if (!saveFilterName.trim()) {
      toast.error('Filter name is required')
      return
    }

    try {
      const response = await fetch('/api/leads/saved-filters', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: saveFilterName,
          description: saveFilterDescription || undefined,
          filters,
          isPublic: saveAsPublic,
          isDefault: saveAsDefault
        }),
      })

      if (response.ok) {
        toast.success('Filter saved successfully')
        setShowSaveDialog(false)
        setSaveFilterName('')
        setSaveFilterDescription('')
        setSaveAsPublic(false)
        setSaveAsDefault(false)
        fetchSavedFilters()
      } else {
        throw new Error('Failed to save filter')
      }
    } catch (error) {
      toast.error('Failed to save filter')
    }
  }

  const handleDeleteSavedFilter = async (filterId: string) => {
    if (!confirm('Are you sure you want to delete this saved filter?')) return

    try {
      const response = await fetch(`/api/leads/saved-filters?id=${filterId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        toast.success('Filter deleted successfully')
        fetchSavedFilters()
      } else {
        throw new Error('Failed to delete filter')
      }
    } catch (error) {
      toast.error('Failed to delete filter')
    }
  }

  const toggleSection = (section: keyof typeof sectionsOpen) => {
    setSectionsOpen(prev => ({ ...prev, [section]: !prev[section] }))
  }

  const getActiveFilterCount = () => {
    let count = 0
    if (filters.search) count++
    if (filters.status.length > 0) count++
    if (filters.priority.length > 0) count++
    if (filters.source.length > 0) count++
    if (filters.industry.length > 0) count++
    if (filters.companySize.length > 0) count++
    if (filters.scoreMin !== undefined || filters.scoreMax !== undefined) count++
    if (filters.temperature.length > 0) count++
    if (filters.createdAfter || filters.createdBefore) count++
    if (filters.lastActivityAfter || filters.lastActivityBefore) count++
    if (filters.convertedAfter || filters.convertedBefore) count++
    if (filters.budgetMin !== undefined || filters.budgetMax !== undefined) count++
    if (filters.hasBudget !== undefined) count++
    if (filters.hasActivities !== undefined) count++
    if (filters.activityCountMin !== undefined || filters.activityCountMax !== undefined) count++
    if (filters.hasRecentActivity !== undefined) count++
    if (filters.assignedTo.length > 0) count++
    if (filters.unassigned !== undefined) count++
    if (filters.isQualified !== undefined) count++
    if (filters.hasPhone !== undefined) count++
    if (filters.hasEmail !== undefined) count++
    if (filters.hasWebsite !== undefined) count++
    if (filters.hasTimeline !== undefined) count++
    if (filters.isConverted !== undefined) count++
    if (filters.conversionType.length > 0) count++
    return count
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center">
            <Filter className="h-5 w-5 mr-2" />
            Advanced Filters
            {getActiveFilterCount() > 0 && (
              <Badge variant="secondary" className="ml-2">
                {getActiveFilterCount()} active
              </Badge>
            )}
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={() => setShowSaveDialog(true)}>
              <Save className="h-4 w-4 mr-2" />
              Save Filter
            </Button>
            <Button variant="outline" size="sm" onClick={handleClearFilters}>
              <X className="h-4 w-4 mr-2" />
              Clear All
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Saved Filters */}
        {savedFilters.length > 0 && (
          <div>
            <Label className="text-sm font-medium mb-2 block">Saved Filters</Label>
            <div className="flex flex-wrap gap-2">
              {savedFilters.map((savedFilter) => (
                <div key={savedFilter.id} className="flex items-center space-x-1">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleLoadSavedFilter(savedFilter)}
                    className="flex items-center space-x-1"
                  >
                    {savedFilter.isDefault && <Star className="h-3 w-3 text-yellow-500" />}
                    <span>{savedFilter.name}</span>
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDeleteSavedFilter(savedFilter.id)}
                    className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Quick Search */}
        <div>
          <Label htmlFor="search">Quick Search</Label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              id="search"
              placeholder="Search leads by name, email, company..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Basic Filters */}
        <Collapsible open={sectionsOpen.basic} onOpenChange={() => toggleSection('basic')}>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" className="w-full justify-between p-0 h-auto">
              <span className="font-medium">Basic Filters</span>
              {sectionsOpen.basic ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-4 mt-4">
            {/* Status Filter */}
            <div>
              <Label>Status</Label>
              <div className="flex flex-wrap gap-2 mt-2">
                {['NEW', 'CONTACTED', 'QUALIFIED', 'PROPOSAL', 'NEGOTIATION', 'CONVERTED', 'LOST'].map((status) => (
                  <div key={status} className="flex items-center space-x-2">
                    <Checkbox
                      id={`status-${status}`}
                      checked={filters.status.includes(status)}
                      onCheckedChange={(checked) => handleArrayFilterChange('status', status, !!checked)}
                    />
                    <Label htmlFor={`status-${status}`} className="text-sm">{status}</Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Priority Filter */}
            <div>
              <Label>Priority</Label>
              <div className="flex flex-wrap gap-2 mt-2">
                {['LOW', 'MEDIUM', 'HIGH', 'URGENT'].map((priority) => (
                  <div key={priority} className="flex items-center space-x-2">
                    <Checkbox
                      id={`priority-${priority}`}
                      checked={filters.priority.includes(priority)}
                      onCheckedChange={(checked) => handleArrayFilterChange('priority', priority, !!checked)}
                    />
                    <Label htmlFor={`priority-${priority}`} className="text-sm">{priority}</Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Source Filter */}
            <div>
              <Label>Lead Source</Label>
              <div className="flex flex-wrap gap-2 mt-2">
                {['WEBSITE', 'REFERRAL', 'SOCIAL_MEDIA', 'EMAIL_CAMPAIGN', 'COLD_CALL', 'TRADE_SHOW', 'PARTNER', 'OTHER'].map((source) => (
                  <div key={source} className="flex items-center space-x-2">
                    <Checkbox
                      id={`source-${source}`}
                      checked={filters.source.includes(source)}
                      onCheckedChange={(checked) => handleArrayFilterChange('source', source, !!checked)}
                    />
                    <Label htmlFor={`source-${source}`} className="text-sm">{source.replace('_', ' ')}</Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Industry Filter */}
            <div>
              <Label>Industry</Label>
              <div className="flex flex-wrap gap-2 mt-2">
                {['TECHNOLOGY', 'FINANCE', 'HEALTHCARE', 'MANUFACTURING', 'RETAIL', 'EDUCATION', 'OTHER'].map((industry) => (
                  <div key={industry} className="flex items-center space-x-2">
                    <Checkbox
                      id={`industry-${industry}`}
                      checked={filters.industry.includes(industry)}
                      onCheckedChange={(checked) => handleArrayFilterChange('industry', industry, !!checked)}
                    />
                    <Label htmlFor={`industry-${industry}`} className="text-sm">{industry}</Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Company Size Filter */}
            <div>
              <Label>Company Size</Label>
              <div className="flex flex-wrap gap-2 mt-2">
                {['STARTUP', 'SMALL', 'MEDIUM', 'LARGE', 'ENTERPRISE'].map((size) => (
                  <div key={size} className="flex items-center space-x-2">
                    <Checkbox
                      id={`size-${size}`}
                      checked={filters.companySize.includes(size)}
                      onCheckedChange={(checked) => handleArrayFilterChange('companySize', size, !!checked)}
                    />
                    <Label htmlFor={`size-${size}`} className="text-sm">{size}</Label>
                  </div>
                ))}
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>

        {/* Scoring Filters */}
        <Collapsible open={sectionsOpen.scoring} onOpenChange={() => toggleSection('scoring')}>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" className="w-full justify-between p-0 h-auto">
              <span className="font-medium">Scoring & Temperature</span>
              {sectionsOpen.scoring ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-4 mt-4">
            {/* Score Range */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="scoreMin">Min Score</Label>
                <Input
                  id="scoreMin"
                  type="number"
                  min="0"
                  max="100"
                  placeholder="0"
                  value={filters.scoreMin || ''}
                  onChange={(e) => handleFilterChange('scoreMin', e.target.value ? parseInt(e.target.value) : undefined)}
                />
              </div>
              <div>
                <Label htmlFor="scoreMax">Max Score</Label>
                <Input
                  id="scoreMax"
                  type="number"
                  min="0"
                  max="100"
                  placeholder="100"
                  value={filters.scoreMax || ''}
                  onChange={(e) => handleFilterChange('scoreMax', e.target.value ? parseInt(e.target.value) : undefined)}
                />
              </div>
            </div>

            {/* Temperature Filter */}
            <div>
              <Label>Lead Temperature</Label>
              <div className="flex flex-wrap gap-2 mt-2">
                {['HOT', 'WARM', 'COLD'].map((temp) => (
                  <div key={temp} className="flex items-center space-x-2">
                    <Checkbox
                      id={`temp-${temp}`}
                      checked={filters.temperature.includes(temp)}
                      onCheckedChange={(checked) => handleArrayFilterChange('temperature', temp, !!checked)}
                    />
                    <Label htmlFor={`temp-${temp}`} className="text-sm">{temp}</Label>
                  </div>
                ))}
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>

        {/* Date Filters */}
        <Collapsible open={sectionsOpen.dates} onOpenChange={() => toggleSection('dates')}>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" className="w-full justify-between p-0 h-auto">
              <span className="font-medium">Date Filters</span>
              {sectionsOpen.dates ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-4 mt-4">
            {/* Created Date Range */}
            <div>
              <Label>Created Date Range</Label>
              <div className="grid grid-cols-2 gap-4 mt-2">
                <div>
                  <Label htmlFor="createdAfter" className="text-sm">From</Label>
                  <Input
                    id="createdAfter"
                    type="date"
                    value={filters.createdAfter}
                    onChange={(e) => handleFilterChange('createdAfter', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="createdBefore" className="text-sm">To</Label>
                  <Input
                    id="createdBefore"
                    type="date"
                    value={filters.createdBefore}
                    onChange={(e) => handleFilterChange('createdBefore', e.target.value)}
                  />
                </div>
              </div>
            </div>

            {/* Last Activity Date Range */}
            <div>
              <Label>Last Activity Date Range</Label>
              <div className="grid grid-cols-2 gap-4 mt-2">
                <div>
                  <Label htmlFor="lastActivityAfter" className="text-sm">From</Label>
                  <Input
                    id="lastActivityAfter"
                    type="date"
                    value={filters.lastActivityAfter}
                    onChange={(e) => handleFilterChange('lastActivityAfter', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="lastActivityBefore" className="text-sm">To</Label>
                  <Input
                    id="lastActivityBefore"
                    type="date"
                    value={filters.lastActivityBefore}
                    onChange={(e) => handleFilterChange('lastActivityBefore', e.target.value)}
                  />
                </div>
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>

        {/* Budget Filters */}
        <Collapsible open={sectionsOpen.budget} onOpenChange={() => toggleSection('budget')}>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" className="w-full justify-between p-0 h-auto">
              <span className="font-medium">Budget Filters</span>
              {sectionsOpen.budget ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-4 mt-4">
            {/* Budget Range */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="budgetMin">Min Budget ($)</Label>
                <Input
                  id="budgetMin"
                  type="number"
                  min="0"
                  placeholder="0"
                  value={filters.budgetMin || ''}
                  onChange={(e) => handleFilterChange('budgetMin', e.target.value ? parseFloat(e.target.value) : undefined)}
                />
              </div>
              <div>
                <Label htmlFor="budgetMax">Max Budget ($)</Label>
                <Input
                  id="budgetMax"
                  type="number"
                  min="0"
                  placeholder="No limit"
                  value={filters.budgetMax || ''}
                  onChange={(e) => handleFilterChange('budgetMax', e.target.value ? parseFloat(e.target.value) : undefined)}
                />
              </div>
            </div>

            {/* Budget Status */}
            <div>
              <Label>Budget Status</Label>
              <div className="flex items-center space-x-4 mt-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="hasBudget"
                    checked={filters.hasBudget === true}
                    onCheckedChange={(checked) => handleFilterChange('hasBudget', checked ? true : undefined)}
                  />
                  <Label htmlFor="hasBudget" className="text-sm">Has Budget</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="noBudget"
                    checked={filters.hasBudget === false}
                    onCheckedChange={(checked) => handleFilterChange('hasBudget', checked ? false : undefined)}
                  />
                  <Label htmlFor="noBudget" className="text-sm">No Budget</Label>
                </div>
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>

        {/* Activity Filters */}
        <Collapsible open={sectionsOpen.activity} onOpenChange={() => toggleSection('activity')}>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" className="w-full justify-between p-0 h-auto">
              <span className="font-medium">Activity Filters</span>
              {sectionsOpen.activity ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-4 mt-4">
            {/* Activity Count Range */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="activityCountMin">Min Activities</Label>
                <Input
                  id="activityCountMin"
                  type="number"
                  min="0"
                  placeholder="0"
                  value={filters.activityCountMin || ''}
                  onChange={(e) => handleFilterChange('activityCountMin', e.target.value ? parseInt(e.target.value) : undefined)}
                />
              </div>
              <div>
                <Label htmlFor="activityCountMax">Max Activities</Label>
                <Input
                  id="activityCountMax"
                  type="number"
                  min="0"
                  placeholder="No limit"
                  value={filters.activityCountMax || ''}
                  onChange={(e) => handleFilterChange('activityCountMax', e.target.value ? parseInt(e.target.value) : undefined)}
                />
              </div>
            </div>

            {/* Activity Status */}
            <div>
              <Label>Activity Status</Label>
              <div className="flex flex-wrap gap-4 mt-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="hasActivities"
                    checked={filters.hasActivities === true}
                    onCheckedChange={(checked) => handleFilterChange('hasActivities', checked ? true : undefined)}
                  />
                  <Label htmlFor="hasActivities" className="text-sm">Has Activities</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="noActivities"
                    checked={filters.hasActivities === false}
                    onCheckedChange={(checked) => handleFilterChange('hasActivities', checked ? false : undefined)}
                  />
                  <Label htmlFor="noActivities" className="text-sm">No Activities</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="hasRecentActivity"
                    checked={filters.hasRecentActivity === true}
                    onCheckedChange={(checked) => handleFilterChange('hasRecentActivity', checked ? true : undefined)}
                  />
                  <Label htmlFor="hasRecentActivity" className="text-sm">Recent Activity (7 days)</Label>
                </div>
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>

        {/* Qualification Filters */}
        <Collapsible open={sectionsOpen.qualification} onOpenChange={() => toggleSection('qualification')}>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" className="w-full justify-between p-0 h-auto">
              <span className="font-medium">Qualification Filters</span>
              {sectionsOpen.qualification ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-4 mt-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="hasPhone"
                  checked={filters.hasPhone === true}
                  onCheckedChange={(checked) => handleFilterChange('hasPhone', checked ? true : undefined)}
                />
                <Label htmlFor="hasPhone" className="text-sm">Has Phone</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="hasEmail"
                  checked={filters.hasEmail === true}
                  onCheckedChange={(checked) => handleFilterChange('hasEmail', checked ? true : undefined)}
                />
                <Label htmlFor="hasEmail" className="text-sm">Has Email</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="hasWebsite"
                  checked={filters.hasWebsite === true}
                  onCheckedChange={(checked) => handleFilterChange('hasWebsite', checked ? true : undefined)}
                />
                <Label htmlFor="hasWebsite" className="text-sm">Has Website</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="hasTimeline"
                  checked={filters.hasTimeline === true}
                  onCheckedChange={(checked) => handleFilterChange('hasTimeline', checked ? true : undefined)}
                />
                <Label htmlFor="hasTimeline" className="text-sm">Has Timeline</Label>
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>

        {/* Conversion Filters */}
        <Collapsible open={sectionsOpen.conversion} onOpenChange={() => toggleSection('conversion')}>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" className="w-full justify-between p-0 h-auto">
              <span className="font-medium">Conversion Filters</span>
              {sectionsOpen.conversion ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-4 mt-4">
            {/* Conversion Status */}
            <div>
              <Label>Conversion Status</Label>
              <div className="flex items-center space-x-4 mt-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isConverted"
                    checked={filters.isConverted === true}
                    onCheckedChange={(checked) => handleFilterChange('isConverted', checked ? true : undefined)}
                  />
                  <Label htmlFor="isConverted" className="text-sm">Converted</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="notConverted"
                    checked={filters.isConverted === false}
                    onCheckedChange={(checked) => handleFilterChange('isConverted', checked ? false : undefined)}
                  />
                  <Label htmlFor="notConverted" className="text-sm">Not Converted</Label>
                </div>
              </div>
            </div>

            {/* Conversion Type */}
            <div>
              <Label>Conversion Type</Label>
              <div className="flex flex-wrap gap-2 mt-2">
                {['DIRECT', 'QUOTATION', 'PROPOSAL', 'TRIAL', 'DEMO'].map((type) => (
                  <div key={type} className="flex items-center space-x-2">
                    <Checkbox
                      id={`conversion-${type}`}
                      checked={filters.conversionType.includes(type)}
                      onCheckedChange={(checked) => handleArrayFilterChange('conversionType', type, !!checked)}
                    />
                    <Label htmlFor={`conversion-${type}`} className="text-sm">{type}</Label>
                  </div>
                ))}
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>

        {/* Action Buttons */}
        <div className="flex items-center justify-between pt-4 border-t">
          <div className="flex items-center space-x-2">
            <Button onClick={handleApplyFilters} disabled={loading}>
              {loading ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Filter className="h-4 w-4 mr-2" />
              )}
              Apply Filters
            </Button>
            <Button variant="outline" onClick={handleClearFilters}>
              <X className="h-4 w-4 mr-2" />
              Clear All
            </Button>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>
      </CardContent>

      {/* Save Filter Dialog */}
      <Dialog open={showSaveDialog} onOpenChange={setShowSaveDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Save Filter</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="filterName">Filter Name *</Label>
              <Input
                id="filterName"
                value={saveFilterName}
                onChange={(e) => setSaveFilterName(e.target.value)}
                placeholder="Enter filter name"
              />
            </div>
            <div>
              <Label htmlFor="filterDescription">Description</Label>
              <Input
                id="filterDescription"
                value={saveFilterDescription}
                onChange={(e) => setSaveFilterDescription(e.target.value)}
                placeholder="Optional description"
              />
            </div>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="saveAsPublic"
                  checked={saveAsPublic}
                  onCheckedChange={(checked) => setSaveAsPublic(!!checked)}
                />
                <Label htmlFor="saveAsPublic" className="text-sm">
                  Make this filter available to all team members
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="saveAsDefault"
                  checked={saveAsDefault}
                  onCheckedChange={(checked) => setSaveAsDefault(!!checked)}
                />
                <Label htmlFor="saveAsDefault" className="text-sm">
                  Set as my default filter
                </Label>
              </div>
            </div>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setShowSaveDialog(false)}>
                Cancel
              </Button>
              <Button onClick={handleSaveFilter}>
                Save Filter
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </Card>
  )
}