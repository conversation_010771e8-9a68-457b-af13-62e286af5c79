"use strict";(()=>{var e={};e.id=4863,e.ids=[4863],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},34342:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>g,originalPathname:()=>_,patchFetch:()=>h,requestAsyncStorage:()=>p,routeModule:()=>m,serverHooks:()=>I,staticGenerationAsyncStorage:()=>y,staticGenerationBailout:()=>w});var o={};a.r(o),a.d(o,{GET:()=>d});var r=a(95419),n=a(69108),s=a(99678),i=a(78070),u=a(81355),c=a(3205),l=a(9108);async function d(e){try{let t=await (0,u.getServerSession)(c.L);if(!t?.user?.id||!t?.user?.companyId)return i.Z.json({error:"Unauthorized"},{status:401});let{searchParams:a}=new URL(e.url),o=a.get("period")||"30",r=new Date;r.setDate(r.getDate()-parseInt(o));let[n,s,d,m,p,y,I,g,w,_]=await Promise.all([l._.quotation.count({where:{companyId:t.user.companyId,createdAt:{gte:r}}}),l._.quotation.groupBy({by:["status"],where:{companyId:t.user.companyId,createdAt:{gte:r}},_count:{id:!0},_sum:{total:!0}}),Promise.resolve([{month:new Date().toISOString().split("T")[0],quotation_count:await l._.quotation.count({where:{companyId:t.user.companyId}}),total_value:await l._.quotation.aggregate({where:{companyId:t.user.companyId},_sum:{total:!0}}).then(e=>Number(e._sum.total||0))}]),Promise.all([l._.quotation.count({where:{companyId:t.user.companyId,status:{in:["SENT","VIEWED","ACCEPTED","REJECTED"]},createdAt:{gte:r}}}),l._.quotation.count({where:{companyId:t.user.companyId,status:"ACCEPTED",createdAt:{gte:r}}}),l._.quotation.count({where:{companyId:t.user.companyId,status:{in:["VIEWED","ACCEPTED","REJECTED"]},createdAt:{gte:r}}})]),l._.quotation.aggregate({where:{companyId:t.user.companyId,createdAt:{gte:r}},_avg:{total:!0}}),l._.quotation.findMany({where:{companyId:t.user.companyId,createdAt:{gte:r}},include:{customer:{select:{id:!0,name:!0,company:!0,email:!0}},createdBy:{select:{name:!0,email:!0}}},orderBy:{total:"desc"},take:10}),l._.quotation.findMany({where:{companyId:t.user.companyId,createdAt:{gte:new Date(Date.now()-6048e5)}},include:{customer:{select:{id:!0,name:!0,company:!0}},createdBy:{select:{name:!0,email:!0}}},orderBy:{createdAt:"desc"},take:10}),Promise.resolve([{date:new Date().toISOString().split("T")[0],quotations_created:await l._.quotation.count({where:{companyId:t.user.companyId}}),quotations_accepted:await l._.quotation.count({where:{companyId:t.user.companyId,status:"ACCEPTED"}}),total_value:await l._.quotation.aggregate({where:{companyId:t.user.companyId},_sum:{total:!0}}).then(e=>Number(e._sum.total||0))}]),l._.quotation.groupBy({by:["customerId"],where:{companyId:t.user.companyId,createdAt:{gte:r}},_count:{id:!0},_sum:{total:!0},orderBy:{_sum:{total:"desc"}},take:10}),Promise.all([l._.quotation.aggregate({where:{companyId:t.user.companyId,status:{in:["DRAFT","SENT","VIEWED"]},validUntil:{gte:new Date}},_sum:{total:!0}}),l._.quotation.aggregate({where:{companyId:t.user.companyId,status:"ACCEPTED",createdAt:{gte:r}},_sum:{total:!0}}),l._.quotation.count({where:{companyId:t.user.companyId,status:{in:["DRAFT","SENT","VIEWED"]},validUntil:{lt:new Date}}})])]),h=w.map(e=>e.customerId).filter(Boolean),q=await l._.customer.findMany({where:{id:{in:h},companyId:t.user.companyId},select:{id:!0,name:!0,company:!0,email:!0}}),E=w.map(e=>({customer:q.find(t=>t.id===e.customerId)||{id:e.customerId,name:"Unknown",company:null,email:null},quotationCount:e._count.id,totalValue:Number(e._sum.total||0)})),[A,v,T]=m,[f,b,D]=_,x=Number(f._sum.total||0),C=Number(b._sum.total||0);s.reduce((e,t)=>(e[t.status]=Number(t._sum.total||0),e),{});let N=await l._.quotation.findMany({where:{companyId:t.user.companyId,status:"ACCEPTED",acceptedAt:{not:null},createdAt:{gte:r}},select:{createdAt:!0,acceptedAt:!0}}),P=N.length>0?N.reduce((e,t)=>{let a=new Date(t.acceptedAt).getTime()-new Date(t.createdAt).getTime();return e+a},0)/N.length/864e5:0;return i.Z.json({summary:{totalQuotations:n,totalValue:s.reduce((e,t)=>e+Number(t._sum.total||0),0),averageValue:Number(p._avg.total||0),acceptanceRate:Math.round(100*(A>0?v/A*100:0))/100,viewRate:Math.round(100*(A>0?T/A*100:0))/100,avgTimeToAcceptance:Math.round(100*P)/100,pendingRevenue:x,confirmedRevenue:C,expiredQuotations:D},quotationsByStatus:s.map(e=>({status:e.status,count:e._count.id,value:Number(e._sum.total||0)})),quotationsByMonth:d,quotationTrends:g,topQuotations:y.map(e=>({id:e.id,quotationNumber:e.quotationNumber,title:e.title,total:Number(e.total),status:e.status,customer:e.customer,createdBy:e.createdBy,createdAt:e.createdAt})),recentQuotations:I.map(e=>({id:e.id,quotationNumber:e.quotationNumber,title:e.title,total:Number(e.total),status:e.status,customer:e.customer,createdBy:e.createdBy,createdAt:e.createdAt})),customerQuotations:E,revenueForecast:{pending:x,confirmed:C,potential:x+C},period:parseInt(o)})}catch(e){return console.error("Error fetching quotation analytics:",e),i.Z.json({error:"Failed to fetch quotation analytics"},{status:500})}}let m=new r.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/quotations/analytics/route",pathname:"/api/quotations/analytics",filename:"route",bundlePath:"app/api/quotations/analytics/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\quotations\\analytics\\route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:p,staticGenerationAsyncStorage:y,serverHooks:I,headerHooks:g,staticGenerationBailout:w}=m,_="/api/quotations/analytics/route";function h(){return(0,s.patchFetch)({serverHooks:I,staticGenerationAsyncStorage:y})}},3205:(e,t,a)=>{a.d(t,{L:()=>c});var o=a(86485),r=a(10375),n=a(50694),s=a(6521),i=a.n(s),u=a(9108);let c={providers:[(0,o.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await u._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),a=t?.companyId;if(!a&&t){let e=await u._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(a=e?.id)&&await u._.user.update({where:{id:t.id},data:{companyId:a}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await i().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await u._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:a}}catch(e){return console.error("Authentication error:",e),null}}}),(0,r.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,n.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,a)=>{a.d(t,{_:()=>r});let o=require("@prisma/client"),r=globalThis.prisma??new o.PrismaClient}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),o=t.X(0,[1638,6206,6521,2455,4520],()=>a(34342));module.exports=o})();