'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { redirect } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  FileText,
  Search,
  Filter,
  Download,
  Trash2,
  Eye,
  RefreshCw,
  Calendar,
  User,
  Activity,
  AlertTriangle,
  Info,
  Shield
} from 'lucide-react'

interface AuditLog {
  id: string
  action: string
  entityType: string
  entityId?: string
  user?: {
    id: string
    name: string
    email: string
    avatar?: string
  }
  userEmail?: string
  userRole?: string
  company?: {
    id: string
    name: string
  }
  ipAddress?: string
  userAgent?: string
  requestUrl?: string
  requestMethod?: string
  oldValues?: any
  newValues?: any
  metadata?: any
  severity: string
  createdAt: string
}

interface AuditStats {
  total: number
  last24Hours: number
  byAction: Array<{ action: string; count: number }>
  bySeverity: Array<{ severity: string; count: number }>
  byEntityType: Array<{ entityType: string; count: number }>
  byUser: Array<{ userId: string; user?: any; count: number }>
}

export default function AuditLogsPage() {
  const { data: session, status } = useSession()
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([])
  const [stats, setStats] = useState<AuditStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [actionFilter, setActionFilter] = useState('all')
  const [severityFilter, setSeverityFilter] = useState('all')
  const [entityTypeFilter, setEntityTypeFilter] = useState('all')
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [selectedLog, setSelectedLog] = useState<AuditLog | null>(null)

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (status === 'unauthenticated') {
    redirect('/auth/signin')
  }

  if (session?.user?.role !== 'SUPER_ADMIN') {
    redirect('/dashboard')
  }

  const fetchAuditLogs = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '50',
        ...(searchTerm && { search: searchTerm }),
        ...(actionFilter && actionFilter !== 'all' && { action: actionFilter }),
        ...(severityFilter && severityFilter !== 'all' && { severity: severityFilter }),
        ...(entityTypeFilter && entityTypeFilter !== 'all' && { entityType: entityTypeFilter })
      })

      const response = await fetch(`/api/super-admin/audit-logs?${params}`)
      if (!response.ok) throw new Error('Failed to fetch audit logs')

      const data = await response.json()
      setAuditLogs(data.auditLogs)
      setStats(data.stats)
      setTotalPages(data.pagination.pages)
    } catch (error) {
      console.error('Error fetching audit logs:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchAuditLogs()
  }, [page, searchTerm, actionFilter, severityFilter, entityTypeFilter])

  const getSeverityBadge = (severity: string) => {
    const variants = {
      LOW: 'outline',
      INFO: 'default',
      MEDIUM: 'secondary',
      HIGH: 'destructive',
      CRITICAL: 'destructive'
    } as const

    const colors = {
      LOW: 'text-gray-600',
      INFO: 'text-blue-600',
      MEDIUM: 'text-yellow-600',
      HIGH: 'text-orange-600',
      CRITICAL: 'text-red-600'
    }

    return (
      <Badge variant={variants[severity as keyof typeof variants] || 'default'}>
        <span className={colors[severity as keyof typeof colors] || 'text-gray-600'}>
          {severity}
        </span>
      </Badge>
    )
  }

  const formatDate = (date: string) => {
    return new Date(date).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  const getActionIcon = (action: string) => {
    if (action.includes('LOGIN') || action.includes('AUTH')) return User
    if (action.includes('CREATE') || action.includes('UPDATE') || action.includes('DELETE')) return Activity
    if (action.includes('SECURITY') || action.includes('ALERT')) return AlertTriangle
    return Info
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <div className="flex items-center space-x-3">
            <FileText className="h-8 w-8 text-blue-600" />
            <h1 className="text-3xl font-bold text-gray-900">Audit Logs</h1>
          </div>
          <p className="text-gray-500 mt-1">Track all system activities and changes</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={fetchAuditLogs} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">Total Logs</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.total.toLocaleString()}</p>
                </div>
                <FileText className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">Last 24 Hours</p>
                  <p className="text-2xl font-bold text-green-600">{stats.last24Hours}</p>
                </div>
                <Activity className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">Critical Events</p>
                  <p className="text-2xl font-bold text-red-600">
                    {stats.bySeverity.find(s => s.severity === 'CRITICAL')?.count || 0}
                  </p>
                </div>
                <AlertTriangle className="h-8 w-8 text-red-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">Top Action</p>
                  <p className="text-lg font-bold text-purple-600">
                    {stats.byAction[0]?.action || 'N/A'}
                  </p>
                </div>
                <Shield className="h-8 w-8 text-purple-600" />
              </div>
              <p className="text-xs text-gray-500 mt-1">
                {stats.byAction[0]?.count || 0} occurrences
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div className="lg:col-span-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search logs..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={actionFilter} onValueChange={setActionFilter}>
              <SelectTrigger>
                <SelectValue placeholder="All Actions" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Actions</SelectItem>
                {stats?.byAction.slice(0, 10).map((item) => (
                  <SelectItem key={item.action} value={item.action}>
                    {item.action} ({item.count})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={severityFilter} onValueChange={setSeverityFilter}>
              <SelectTrigger>
                <SelectValue placeholder="All Severities" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Severities</SelectItem>
                <SelectItem value="LOW">Low</SelectItem>
                <SelectItem value="INFO">Info</SelectItem>
                <SelectItem value="MEDIUM">Medium</SelectItem>
                <SelectItem value="HIGH">High</SelectItem>
                <SelectItem value="CRITICAL">Critical</SelectItem>
              </SelectContent>
            </Select>
            <Select value={entityTypeFilter} onValueChange={setEntityTypeFilter}>
              <SelectTrigger>
                <SelectValue placeholder="All Entities" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Entities</SelectItem>
                {stats?.byEntityType.slice(0, 10).map((item) => (
                  <SelectItem key={item.entityType} value={item.entityType}>
                    {item.entityType} ({item.count})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Audit Logs Table */}
      <Card>
        <CardHeader>
          <CardTitle>Audit Logs ({auditLogs.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Action</TableHead>
                    <TableHead>User</TableHead>
                    <TableHead>Entity</TableHead>
                    <TableHead>Severity</TableHead>
                    <TableHead>IP Address</TableHead>
                    <TableHead>Timestamp</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {auditLogs.map((log) => {
                    const ActionIcon = getActionIcon(log.action)
                    return (
                      <TableRow key={log.id}>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <ActionIcon className="h-4 w-4 text-gray-500" />
                            <span className="font-medium">{log.action}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          {log.user ? (
                            <div className="flex items-center space-x-2">
                              <Avatar className="h-6 w-6">
                                <AvatarImage src={log.user.avatar} alt={log.user.name} />
                                <AvatarFallback>
                                  {log.user.name?.charAt(0)?.toUpperCase() || 'U'}
                                </AvatarFallback>
                              </Avatar>
                              <div>
                                <p className="font-medium text-sm">{log.user.name}</p>
                                <p className="text-xs text-gray-500">{log.userRole}</p>
                              </div>
                            </div>
                          ) : (
                            <div>
                              <p className="font-medium text-sm">{log.userEmail || 'System'}</p>
                              <p className="text-xs text-gray-500">{log.userRole || 'N/A'}</p>
                            </div>
                          )}
                        </TableCell>
                        <TableCell>
                          <div>
                            <p className="font-medium text-sm">{log.entityType}</p>
                            {log.entityId && (
                              <p className="text-xs text-gray-500 font-mono">{log.entityId.slice(0, 8)}...</p>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>{getSeverityBadge(log.severity)}</TableCell>
                        <TableCell>
                          <span className="font-mono text-sm">{log.ipAddress || 'N/A'}</span>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            <p>{formatDate(log.createdAt)}</p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Dialog>
                            <DialogTrigger asChild>
                              <Button variant="ghost" size="sm" onClick={() => setSelectedLog(log)}>
                                <Eye className="h-4 w-4" />
                              </Button>
                            </DialogTrigger>
                            <DialogContent className="max-w-2xl">
                              <DialogHeader>
                                <DialogTitle>Audit Log Details</DialogTitle>
                                <DialogDescription>
                                  Detailed information about this audit log entry
                                </DialogDescription>
                              </DialogHeader>
                              {selectedLog && (
                                <div className="space-y-4">
                                  <div className="grid grid-cols-2 gap-4">
                                    <div>
                                      <label className="text-sm font-medium text-gray-500">Action</label>
                                      <p className="text-sm">{selectedLog.action}</p>
                                    </div>
                                    <div>
                                      <label className="text-sm font-medium text-gray-500">Severity</label>
                                      <div className="mt-1">{getSeverityBadge(selectedLog.severity)}</div>
                                    </div>
                                    <div>
                                      <label className="text-sm font-medium text-gray-500">Entity Type</label>
                                      <p className="text-sm">{selectedLog.entityType}</p>
                                    </div>
                                    <div>
                                      <label className="text-sm font-medium text-gray-500">Entity ID</label>
                                      <p className="text-sm font-mono">{selectedLog.entityId || 'N/A'}</p>
                                    </div>
                                    <div>
                                      <label className="text-sm font-medium text-gray-500">IP Address</label>
                                      <p className="text-sm font-mono">{selectedLog.ipAddress || 'N/A'}</p>
                                    </div>
                                    <div>
                                      <label className="text-sm font-medium text-gray-500">Timestamp</label>
                                      <p className="text-sm">{formatDate(selectedLog.createdAt)}</p>
                                    </div>
                                  </div>
                                  
                                  {selectedLog.oldValues && (
                                    <div>
                                      <label className="text-sm font-medium text-gray-500">Old Values</label>
                                      <pre className="text-xs bg-gray-100 p-3 rounded mt-1 overflow-auto">
                                        {JSON.stringify(selectedLog.oldValues, null, 2)}
                                      </pre>
                                    </div>
                                  )}
                                  
                                  {selectedLog.newValues && (
                                    <div>
                                      <label className="text-sm font-medium text-gray-500">New Values</label>
                                      <pre className="text-xs bg-gray-100 p-3 rounded mt-1 overflow-auto">
                                        {JSON.stringify(selectedLog.newValues, null, 2)}
                                      </pre>
                                    </div>
                                  )}
                                  
                                  {selectedLog.metadata && (
                                    <div>
                                      <label className="text-sm font-medium text-gray-500">Metadata</label>
                                      <pre className="text-xs bg-gray-100 p-3 rounded mt-1 overflow-auto">
                                        {JSON.stringify(selectedLog.metadata, null, 2)}
                                      </pre>
                                    </div>
                                  )}
                                </div>
                              )}
                            </DialogContent>
                          </Dialog>
                        </TableCell>
                      </TableRow>
                    )
                  })}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-500">
            Page {page} of {totalPages}
          </p>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              onClick={() => setPage(page - 1)}
              disabled={page === 1}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              onClick={() => setPage(page + 1)}
              disabled={page === totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
