"use strict";(()=>{var e={};e.id=7224,e.ids=[7224],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},1333:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>A,originalPathname:()=>E,patchFetch:()=>P,requestAsyncStorage:()=>h,routeModule:()=>f,serverHooks:()=>g,staticGenerationAsyncStorage:()=>v,staticGenerationBailout:()=>x});var a={};r.r(a),r.d(a,{DELETE:()=>w,GET:()=>y,POST:()=>I});var n=r(95419),o=r(69108),i=r(99678),s=r(78070),d=r(81355),c=r(3205),u=r(9108),l=r(25252),m=r(52178);let p=l.Ry({amount:l.Rx().min(.01,"Payment amount must be greater than 0"),paymentDate:l.Z_().optional(),paymentMethod:l.Km(["CASH","CHECK","CREDIT_CARD","BANK_TRANSFER","PAYPAL","OTHER"]).default("CASH"),reference:l.Z_().optional(),notes:l.Z_().optional()});async function y(e,{params:t}){try{let e=await (0,d.getServerSession)(c.L);if(!e?.user?.id||!e?.user?.companyId)return s.Z.json({error:"Unauthorized"},{status:401});if(!await u._.invoice.findFirst({where:{id:t.id,companyId:e.user.companyId}}))return s.Z.json({error:"Invoice not found"},{status:404});let r=await u._.transaction.findMany({where:{invoiceId:t.id,type:"PAYMENT"},include:{createdBy:{select:{name:!0,email:!0}}},orderBy:{createdAt:"desc"}});return s.Z.json({payments:r.map(e=>({id:e.id,amount:Number(e.amount),paymentDate:e.date,paymentMethod:e.method,reference:e.reference,notes:e.notes,createdBy:e.createdBy,createdAt:e.createdAt}))})}catch(e){return console.error("Error fetching payments:",e),s.Z.json({error:"Failed to fetch payments"},{status:500})}}async function I(e,{params:t}){try{let r=await (0,d.getServerSession)(c.L);if(!r?.user?.id||!r?.user?.companyId)return s.Z.json({error:"Unauthorized"},{status:401});let a=await e.json(),n=p.parse(a),o=await u._.invoice.findFirst({where:{id:t.id,companyId:r.user.companyId},include:{transactions:{where:{type:"PAYMENT"}}}});if(!o)return s.Z.json({error:"Invoice not found"},{status:404});let i=o.transactions.reduce((e,t)=>e+Number(t.amount),0),l=Number(o.total)-i;if(n.amount>l)return s.Z.json({error:`Payment amount cannot exceed remaining balance of ${l.toFixed(2)}`},{status:400});let m=n.paymentDate?new Date(n.paymentDate):new Date,y=await u._.$transaction(async e=>{let a=await e.transaction.create({data:{type:"PAYMENT",amount:n.amount,date:m,method:n.paymentMethod,reference:n.reference,notes:n.notes,invoiceId:t.id,customerId:o.customerId,companyId:r.user.companyId,createdById:r.user.id},include:{createdBy:{select:{name:!0,email:!0}}}}),s=i+n.amount,d=s>=Number(o.total);return await e.invoice.update({where:{id:t.id},data:{paidAmount:s,status:d?"PAID":o.status,paidAt:d?new Date:o.paidAt}}),await e.activity.create({data:{type:"PAYMENT",title:"Payment Recorded",description:`Payment of $${n.amount.toFixed(2)} recorded for invoice ${o.invoiceNumber}`,invoiceId:t.id,customerId:o.customerId,companyId:r.user.companyId,createdById:r.user.id}}),a});return s.Z.json({payment:{id:y.id,amount:Number(y.amount),paymentDate:y.date,paymentMethod:y.method,reference:y.reference,notes:y.notes,createdBy:y.createdBy,createdAt:y.createdAt},message:"Payment recorded successfully"})}catch(e){if(e instanceof m.jm)return s.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("Error recording payment:",e),s.Z.json({error:"Failed to record payment"},{status:500})}}async function w(e,{params:t}){try{let r=await (0,d.getServerSession)(c.L);if(!r?.user?.id||!r?.user?.companyId)return s.Z.json({error:"Unauthorized"},{status:401});let{searchParams:a}=new URL(e.url),n=a.get("paymentId");if(!n)return s.Z.json({error:"Payment ID is required"},{status:400});let o=await u._.transaction.findFirst({where:{id:n,invoiceId:t.id,type:"PAYMENT",companyId:r.user.companyId},include:{invoice:!0}});if(!o)return s.Z.json({error:"Payment not found"},{status:404});return await u._.$transaction(async e=>{await e.transaction.delete({where:{id:n}});let a=(await e.transaction.findMany({where:{invoiceId:t.id,type:"PAYMENT",id:{not:n}}})).reduce((e,t)=>e+Number(t.amount),0),i=a>=Number(o.invoice.total);await e.invoice.update({where:{id:t.id},data:{paidAmount:a,status:i?"PAID":a>0?"SENT":o.invoice.status,paidAt:i?o.invoice.paidAt:null}}),await e.activity.create({data:{type:"PAYMENT",title:"Payment Deleted",description:`Payment of $${Number(o.amount).toFixed(2)} was deleted from invoice ${o.invoice.invoiceNumber}`,invoiceId:t.id,customerId:o.invoice.customerId,companyId:r.user.companyId,createdById:r.user.id}})}),s.Z.json({message:"Payment deleted successfully"})}catch(e){return console.error("Error deleting payment:",e),s.Z.json({error:"Failed to delete payment"},{status:500})}}let f=new n.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/invoices/[id]/payments/route",pathname:"/api/invoices/[id]/payments",filename:"route",bundlePath:"app/api/invoices/[id]/payments/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\invoices\\[id]\\payments\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:h,staticGenerationAsyncStorage:v,serverHooks:g,headerHooks:A,staticGenerationBailout:x}=f,E="/api/invoices/[id]/payments/route";function P(){return(0,i.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:v})}},3205:(e,t,r)=>{r.d(t,{L:()=>c});var a=r(86485),n=r(10375),o=r(50694),i=r(6521),s=r.n(i),d=r(9108);let c={providers:[(0,a.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await d._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),r=t?.companyId;if(!r&&t){let e=await d._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(r=e?.id)&&await d._.user.update({where:{id:t.id},data:{companyId:r}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await s().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await d._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:r}}catch(e){return console.error("Authentication error:",e),null}}}),(0,n.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,o.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,r)=>{r.d(t,{_:()=>n});let a=require("@prisma/client"),n=globalThis.prisma??new a.PrismaClient}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,6206,6521,2455,4520,5252],()=>r(1333));module.exports=a})();