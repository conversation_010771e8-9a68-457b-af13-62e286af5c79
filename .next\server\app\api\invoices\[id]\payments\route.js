"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/invoices/[id]/payments/route";
exports.ids = ["app/api/invoices/[id]/payments/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Finvoices%2F%5Bid%5D%2Fpayments%2Froute&page=%2Fapi%2Finvoices%2F%5Bid%5D%2Fpayments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finvoices%2F%5Bid%5D%2Fpayments%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Finvoices%2F%5Bid%5D%2Fpayments%2Froute&page=%2Fapi%2Finvoices%2F%5Bid%5D%2Fpayments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finvoices%2F%5Bid%5D%2Fpayments%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_proj_nextjs_saas_app_api_invoices_id_payments_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/invoices/[id]/payments/route.ts */ \"(rsc)/./app/api/invoices/[id]/payments/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/invoices/[id]/payments/route\",\n        pathname: \"/api/invoices/[id]/payments\",\n        filename: \"route\",\n        bundlePath: \"app/api/invoices/[id]/payments/route\"\n    },\n    resolvedPagePath: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\api\\\\invoices\\\\[id]\\\\payments\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_proj_nextjs_saas_app_api_invoices_id_payments_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/invoices/[id]/payments/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Finvoices%2F%5Bid%5D%2Fpayments%2Froute&page=%2Fapi%2Finvoices%2F%5Bid%5D%2Fpayments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finvoices%2F%5Bid%5D%2Fpayments%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/invoices/[id]/payments/route.ts":
/*!*************************************************!*\
  !*** ./app/api/invoices/[id]/payments/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/ZodError.js\");\n\n\n\n\n\nconst paymentSchema = zod__WEBPACK_IMPORTED_MODULE_4__.object({\n    amount: zod__WEBPACK_IMPORTED_MODULE_4__.number().min(0.01, \"Payment amount must be greater than 0\"),\n    paymentDate: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional(),\n    paymentMethod: zod__WEBPACK_IMPORTED_MODULE_4__[\"enum\"]([\n        \"CASH\",\n        \"CHECK\",\n        \"CREDIT_CARD\",\n        \"BANK_TRANSFER\",\n        \"PAYPAL\",\n        \"OTHER\"\n    ]).default(\"CASH\"),\n    reference: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional(),\n    notes: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional()\n});\n// GET /api/invoices/[id]/payments - Get payments for invoice\nasync function GET(request, { params }) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id || !session?.user?.companyId) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        // Verify invoice exists and belongs to user's company\n        const invoice = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.invoice.findFirst({\n            where: {\n                id: params.id,\n                companyId: session.user.companyId\n            }\n        });\n        if (!invoice) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Invoice not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Get all payments for this invoice\n        const payments = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.transaction.findMany({\n            where: {\n                invoiceId: params.id,\n                type: \"PAYMENT\"\n            },\n            include: {\n                createdBy: {\n                    select: {\n                        name: true,\n                        email: true\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: \"desc\"\n            }\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            payments: payments.map((payment)=>({\n                    id: payment.id,\n                    amount: Number(payment.amount),\n                    paymentDate: payment.date,\n                    paymentMethod: payment.method,\n                    reference: payment.reference,\n                    notes: payment.notes,\n                    createdBy: payment.createdBy,\n                    createdAt: payment.createdAt\n                }))\n        });\n    } catch (error) {\n        console.error(\"Error fetching payments:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to fetch payments\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/invoices/[id]/payments - Record payment for invoice\nasync function POST(request, { params }) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id || !session?.user?.companyId) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const validatedData = paymentSchema.parse(body);\n        // Get invoice with current payment status\n        const invoice = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.invoice.findFirst({\n            where: {\n                id: params.id,\n                companyId: session.user.companyId\n            },\n            include: {\n                transactions: {\n                    where: {\n                        type: \"PAYMENT\"\n                    }\n                }\n            }\n        });\n        if (!invoice) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Invoice not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Calculate current paid amount\n        const currentPaidAmount = invoice.transactions.reduce((sum, transaction)=>sum + Number(transaction.amount), 0);\n        // Validate payment amount\n        const remainingAmount = Number(invoice.total) - currentPaidAmount;\n        if (validatedData.amount > remainingAmount) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: `Payment amount cannot exceed remaining balance of ${remainingAmount.toFixed(2)}`\n            }, {\n                status: 400\n            });\n        }\n        // Create payment transaction\n        const paymentDate = validatedData.paymentDate ? new Date(validatedData.paymentDate) : new Date();\n        const payment = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.$transaction(async (tx)=>{\n            // Create payment transaction\n            const newPayment = await tx.transaction.create({\n                data: {\n                    type: \"PAYMENT\",\n                    amount: validatedData.amount,\n                    date: paymentDate,\n                    method: validatedData.paymentMethod,\n                    reference: validatedData.reference,\n                    notes: validatedData.notes,\n                    invoiceId: params.id,\n                    customerId: invoice.customerId,\n                    companyId: session.user.companyId,\n                    createdById: session.user.id\n                },\n                include: {\n                    createdBy: {\n                        select: {\n                            name: true,\n                            email: true\n                        }\n                    }\n                }\n            });\n            // Calculate new paid amount\n            const newPaidAmount = currentPaidAmount + validatedData.amount;\n            const isFullyPaid = newPaidAmount >= Number(invoice.total);\n            // Update invoice paid amount and status\n            await tx.invoice.update({\n                where: {\n                    id: params.id\n                },\n                data: {\n                    paidAmount: newPaidAmount,\n                    status: isFullyPaid ? \"PAID\" : invoice.status,\n                    paidAt: isFullyPaid ? new Date() : invoice.paidAt\n                }\n            });\n            // Log activity\n            await tx.activity.create({\n                data: {\n                    type: \"PAYMENT\",\n                    title: \"Payment Recorded\",\n                    description: `Payment of $${validatedData.amount.toFixed(2)} recorded for invoice ${invoice.invoiceNumber}`,\n                    invoiceId: params.id,\n                    customerId: invoice.customerId,\n                    companyId: session.user.companyId,\n                    createdById: session.user.id\n                }\n            });\n            return newPayment;\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            payment: {\n                id: payment.id,\n                amount: Number(payment.amount),\n                paymentDate: payment.date,\n                paymentMethod: payment.method,\n                reference: payment.reference,\n                notes: payment.notes,\n                createdBy: payment.createdBy,\n                createdAt: payment.createdAt\n            },\n            message: \"Payment recorded successfully\"\n        });\n    } catch (error) {\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_5__.ZodError) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Validation failed\",\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        console.error(\"Error recording payment:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to record payment\"\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE /api/invoices/[id]/payments/[paymentId] - Delete payment\nasync function DELETE(request, { params }) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id || !session?.user?.companyId) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const paymentId = searchParams.get(\"paymentId\");\n        if (!paymentId) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Payment ID is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Verify payment exists and belongs to the invoice and company\n        const payment = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.transaction.findFirst({\n            where: {\n                id: paymentId,\n                invoiceId: params.id,\n                type: \"PAYMENT\",\n                companyId: session.user.companyId\n            },\n            include: {\n                invoice: true\n            }\n        });\n        if (!payment) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Payment not found\"\n            }, {\n                status: 404\n            });\n        }\n        await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.$transaction(async (tx)=>{\n            // Delete the payment\n            await tx.transaction.delete({\n                where: {\n                    id: paymentId\n                }\n            });\n            // Recalculate invoice paid amount\n            const remainingPayments = await tx.transaction.findMany({\n                where: {\n                    invoiceId: params.id,\n                    type: \"PAYMENT\",\n                    id: {\n                        not: paymentId\n                    }\n                }\n            });\n            const newPaidAmount = remainingPayments.reduce((sum, transaction)=>sum + Number(transaction.amount), 0);\n            const isFullyPaid = newPaidAmount >= Number(payment.invoice.total);\n            // Update invoice\n            await tx.invoice.update({\n                where: {\n                    id: params.id\n                },\n                data: {\n                    paidAmount: newPaidAmount,\n                    status: isFullyPaid ? \"PAID\" : newPaidAmount > 0 ? \"SENT\" : payment.invoice.status,\n                    paidAt: isFullyPaid ? payment.invoice.paidAt : null\n                }\n            });\n            // Log activity\n            await tx.activity.create({\n                data: {\n                    type: \"PAYMENT\",\n                    title: \"Payment Deleted\",\n                    description: `Payment of $${Number(payment.amount).toFixed(2)} was deleted from invoice ${payment.invoice.invoiceNumber}`,\n                    invoiceId: params.id,\n                    customerId: payment.invoice.customerId,\n                    companyId: session.user.companyId,\n                    createdById: session.user.id\n                }\n            });\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            message: \"Payment deleted successfully\"\n        });\n    } catch (error) {\n        console.error(\"Error deleting payment:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to delete payment\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/invoices/[id]/payments/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/github */ \"(rsc)/./node_modules/next-auth/providers/github.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n\nconst authOptions = {\n    // Using JWT strategy instead of database adapter for now\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                try {\n                    if (!credentials?.email || !credentials?.password) {\n                        console.log(\"Missing credentials\");\n                        return null;\n                    }\n                    console.log(\"Attempting to authenticate user:\", credentials.email);\n                    const user = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.findUnique({\n                        where: {\n                            email: credentials.email\n                        },\n                        select: {\n                            id: true,\n                            email: true,\n                            name: true,\n                            password: true,\n                            role: true,\n                            companyId: true\n                        }\n                    });\n                    // Get the company ID - either as member or owner\n                    let finalCompanyId = user?.companyId;\n                    if (!finalCompanyId && user) {\n                        const ownedCompany = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.company.findFirst({\n                            where: {\n                                ownerId: user.id\n                            },\n                            select: {\n                                id: true\n                            }\n                        });\n                        finalCompanyId = ownedCompany?.id;\n                        // If user is company owner, update their companyId for future queries\n                        if (finalCompanyId) {\n                            await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                                where: {\n                                    id: user.id\n                                },\n                                data: {\n                                    companyId: finalCompanyId\n                                }\n                            });\n                        }\n                    }\n                    if (!user) {\n                        console.log(\"User not found:\", credentials.email);\n                        return null;\n                    }\n                    if (!user.password) {\n                        console.log(\"User has no password set:\", credentials.email);\n                        return null;\n                    }\n                    const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_3___default().compare(credentials.password, user.password);\n                    if (!isPasswordValid) {\n                        console.log(\"Invalid password for user:\", credentials.email);\n                        return null;\n                    }\n                    // Update last login\n                    await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                        where: {\n                            id: user.id\n                        },\n                        data: {\n                            lastLoginAt: new Date(),\n                            loginCount: {\n                                increment: 1\n                            }\n                        }\n                    });\n                    console.log(\"User authenticated successfully:\", user.email);\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name,\n                        role: user.role,\n                        companyId: finalCompanyId\n                    };\n                } catch (error) {\n                    console.error(\"Authentication error:\", error);\n                    return null;\n                }\n            }\n        }),\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            clientId: process.env.GITHUB_CLIENT_ID,\n            clientSecret: process.env.GITHUB_CLIENT_SECRET\n        })\n    ],\n    session: {\n        strategy: \"jwt\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                console.log(\"JWT callback - user data:\", {\n                    id: user.id,\n                    email: user.email,\n                    role: user.role,\n                    companyId: user.companyId\n                });\n                token.role = user.role;\n                token.companyId = user.companyId;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n                session.user.companyId = token.companyId;\n                // Ensure no company object is ever set on the session\n                if (session.user.company) {\n                    delete session.user.company;\n                }\n                console.log(\"Session callback - final session:\", {\n                    id: session.user.id,\n                    email: session.user.email,\n                    role: session.user.role,\n                    companyId: session.user.companyId\n                });\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        signUp: \"/auth/signup\",\n        error: \"/auth/error\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zYWFzLWFwcC8uL2xpYi9wcmlzbWEudHM/OTgyMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/zod","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/lru-cache","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Finvoices%2F%5Bid%5D%2Fpayments%2Froute&page=%2Fapi%2Finvoices%2F%5Bid%5D%2Fpayments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finvoices%2F%5Bid%5D%2Fpayments%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();