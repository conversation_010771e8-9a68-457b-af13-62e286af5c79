import { ClassConstructor, ClassTransformOptions } from 'class-transformer';
import { ValidatorOptions } from 'class-validator';
import { Resolver } from 'react-hook-form';
/**
 * Creates a resolver for react-hook-form using class-validator schema validation
 * @param {ClassConstructor<Schema>} schema - The class-validator schema to validate against
 * @param {Object} schemaOptions - Additional schema validation options
 * @param {Object} resolverOptions - Additional resolver configuration
 * @param {string} [resolverOptions.mode='async'] - Validation mode
 * @returns {Resolver<Schema>} A resolver function compatible with react-hook-form
 * @example
 * class Schema {
 *   @Matches(/^\w+$/)
 *   @Length(3, 30)
 *   username: string;
 *   age: number
 * }
 *
 * useForm({
 *   resolver: classValidatorResolver(Schema)
 * });
 */
export declare function classValidatorResolver<Schema extends Record<string, any>>(schema: ClassConstructor<Schema>, schemaOptions?: {
    validator?: ValidatorOptions;
    transformer?: ClassTransformOptions;
}, resolverOptions?: {
    mode?: 'async' | 'sync';
    raw?: boolean;
}): Resolver<Schema>;
