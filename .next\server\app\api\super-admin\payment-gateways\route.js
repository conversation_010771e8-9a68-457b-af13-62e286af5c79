"use strict";(()=>{var e={};e.id=953,e.ids=[953],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},18275:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>h,originalPathname:()=>I,patchFetch:()=>v,requestAsyncStorage:()=>m,routeModule:()=>y,serverHooks:()=>w,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>f});var a={};t.r(a),t.d(a,{GET:()=>p,POST:()=>d});var s=t(95419),o=t(69108),n=t(99678),i=t(78070),l=t(81355),c=t(3205),u=t(9108);async function p(e){try{let e=await (0,l.getServerSession)(c.L);if(!e?.user||"SUPER_ADMIN"!==e.user.role)return i.Z.json({success:!1,error:"Unauthorized"},{status:401});let r=[],t={};try{t=(r=await u._.paymentGatewayConfig.findMany()).reduce((e,r)=>(e[r.provider]={enabled:r.isEnabled,live:r.isLive,...r.config},e),{})}catch(e){console.error("Error fetching payment gateways:",e),t={stripe:{enabled:!1,live:!1,publishableKey:"",secretKey:"",webhookSecret:"",supportedMethods:["card","apple_pay","google_pay"]},paypal:{enabled:!1,live:!1,clientId:"",clientSecret:"",webhookId:"",supportedMethods:["paypal","venmo"]},razorpay:{enabled:!1,live:!1,keyId:"",keySecret:"",webhookSecret:"",supportedMethods:["card","netbanking","upi","wallet"]},square:{enabled:!1,live:!1,applicationId:"",accessToken:"",webhookSignatureKey:"",supportedMethods:["card","apple_pay","google_pay"]}}}return i.Z.json({success:!0,gateways:r,config:t})}catch(e){return console.error("Error fetching payment gateways:",e),i.Z.json({success:!1,error:"Internal server error"},{status:500})}}async function d(e){try{let r=await (0,l.getServerSession)(c.L);if(!r?.user||"SUPER_ADMIN"!==r.user.role)return i.Z.json({success:!1,error:"Unauthorized"},{status:401});let t=await e.json(),a=[];for(let[e,r]of Object.entries(t)){let{enabled:t,live:s,...o}=r;try{a.push(u._.paymentGatewayConfig.upsert({where:{provider:e},update:{isEnabled:t,isLive:s,config:o,updatedAt:new Date},create:{provider:e,isEnabled:t,isLive:s,config:o}}))}catch(r){console.error(`Error updating payment gateway config for ${e}:`,r)}}try{await Promise.all(a)}catch(e){console.error("Error saving payment gateway config:",e)}try{await u._.auditLog.create({data:{action:"UPDATE_PAYMENT_GATEWAYS",entityType:"PAYMENT_GATEWAY_CONFIG",entityId:"payment_gateways",userId:r.user.id,details:{updatedProviders:Object.keys(t),timestamp:new Date().toISOString()}}})}catch(e){console.error("Error creating audit log:",e)}return i.Z.json({success:!0,message:"Payment gateway configuration updated successfully"})}catch(e){return console.error("Error updating payment gateway config:",e),i.Z.json({success:!1,error:"Internal server error"},{status:500})}}let y=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/super-admin/payment-gateways/route",pathname:"/api/super-admin/payment-gateways",filename:"route",bundlePath:"app/api/super-admin/payment-gateways/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\super-admin\\payment-gateways\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:m,staticGenerationAsyncStorage:g,serverHooks:w,headerHooks:h,staticGenerationBailout:f}=y,I="/api/super-admin/payment-gateways/route";function v(){return(0,n.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:g})}},3205:(e,r,t)=>{t.d(r,{L:()=>c});var a=t(86485),s=t(10375),o=t(50694),n=t(6521),i=t.n(n),l=t(9108);let c={providers:[(0,a.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let r=await l._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),t=r?.companyId;if(!t&&r){let e=await l._.company.findFirst({where:{ownerId:r.id},select:{id:!0}});(t=e?.id)&&await l._.user.update({where:{id:r.id},data:{companyId:t}})}if(!r)return console.log("User not found:",e.email),null;if(!r.password)return console.log("User has no password set:",e.email),null;if(!await i().compare(e.password,r.password))return console.log("Invalid password for user:",e.email),null;return await l._.user.update({where:{id:r.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",r.email),{id:r.id,email:r.email,name:r.name,role:r.role,companyId:t}}catch(e){return console.error("Authentication error:",e),null}}}),(0,s.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,o.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(console.log("JWT callback - user data:",{id:r.id,email:r.email,role:r.role,companyId:r.companyId}),e.role=r.role,e.companyId=r.companyId),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role,e.user.companyId=r.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,r,t)=>{t.d(r,{_:()=>s});let a=require("@prisma/client"),s=globalThis.prisma??new a.PrismaClient}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[1638,6206,6521,2455,4520],()=>t(18275));module.exports=a})();