"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/super-admin/users/route";
exports.ids = ["app/api/super-admin/users/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Fusers%2Froute&page=%2Fapi%2Fsuper-admin%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Fusers%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Fusers%2Froute&page=%2Fapi%2Fsuper-admin%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Fusers%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_proj_nextjs_saas_app_api_super_admin_users_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/super-admin/users/route.ts */ \"(rsc)/./app/api/super-admin/users/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/super-admin/users/route\",\n        pathname: \"/api/super-admin/users\",\n        filename: \"route\",\n        bundlePath: \"app/api/super-admin/users/route\"\n    },\n    resolvedPagePath: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\api\\\\super-admin\\\\users\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_proj_nextjs_saas_app_api_super_admin_users_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/super-admin/users/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Fusers%2Froute&page=%2Fapi%2Fsuper-admin%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Fusers%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/super-admin/users/route.ts":
/*!********************************************!*\
  !*** ./app/api/super-admin/users/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\n// GET /api/super-admin/users - Get all users with admin details\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id || session?.user?.role !== \"SUPER_ADMIN\") {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Super admin access required\"\n            }, {\n                status: 403\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get(\"page\") || \"1\");\n        const limit = parseInt(searchParams.get(\"limit\") || \"20\");\n        const search = searchParams.get(\"search\") || \"\";\n        const role = searchParams.get(\"role\") || \"\";\n        const status = searchParams.get(\"status\") || \"\";\n        const companyId = searchParams.get(\"companyId\") || \"\";\n        const skip = (page - 1) * limit;\n        // Build where clause\n        const where = {};\n        if (search) {\n            where.OR = [\n                {\n                    name: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    email: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    firstName: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    lastName: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                }\n            ];\n        }\n        if (role && role !== \"all\") {\n            where.role = role;\n        }\n        if (status && status !== \"all\") {\n            where.status = status;\n        }\n        if (companyId) {\n            where.companyId = companyId;\n        }\n        // Get users with pagination\n        const [users, total] = await Promise.all([\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findMany({\n                where,\n                skip,\n                take: limit,\n                include: {\n                    company: {\n                        select: {\n                            id: true,\n                            name: true,\n                            status: true,\n                            industry: true\n                        }\n                    },\n                    ownedCompany: {\n                        select: {\n                            id: true,\n                            name: true,\n                            status: true\n                        }\n                    },\n                    _count: {\n                        select: {\n                            createdLeads: true,\n                            createdCustomers: true,\n                            createdQuotations: true,\n                            createdInvoices: true,\n                            createdContracts: true,\n                            activities: true\n                        }\n                    }\n                },\n                orderBy: {\n                    createdAt: \"desc\"\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.count({\n                where\n            })\n        ]);\n        // Get user statistics\n        const stats = await Promise.all([\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.count({\n                where: {\n                    status: \"ACTIVE\"\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.count({\n                where: {\n                    status: \"INACTIVE\"\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.count({\n                where: {\n                    status: \"SUSPENDED\"\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.count({\n                where: {\n                    status: \"PENDING\"\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.groupBy({\n                by: [\n                    \"role\"\n                ],\n                _count: {\n                    id: true\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.count({\n                where: {\n                    createdAt: {\n                        gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days\n                    }\n                }\n            })\n        ]);\n        const [activeUsers, inactiveUsers, suspendedUsers, pendingUsers, usersByRole, newUsers] = stats;\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            users: users.map((user)=>({\n                    id: user.id,\n                    email: user.email,\n                    name: user.name,\n                    firstName: user.firstName,\n                    lastName: user.lastName,\n                    avatar: user.avatar,\n                    phone: user.phone,\n                    role: user.role,\n                    status: user.status,\n                    title: user.title,\n                    department: user.department,\n                    timezone: user.timezone,\n                    language: user.language,\n                    emailVerified: user.emailVerified,\n                    twoFactorEnabled: user.twoFactorEnabled,\n                    lastLoginAt: user.lastLoginAt,\n                    loginCount: user.loginCount,\n                    lastActiveAt: user.lastActiveAt,\n                    company: user.company,\n                    ownedCompany: user.ownedCompany,\n                    stats: {\n                        leadsCreated: user._count.createdLeads,\n                        customersCreated: user._count.createdCustomers,\n                        quotationsCreated: user._count.createdQuotations,\n                        invoicesCreated: user._count.createdInvoices,\n                        contractsCreated: user._count.createdContracts,\n                        activitiesCount: user._count.activities\n                    },\n                    createdAt: user.createdAt,\n                    updatedAt: user.updatedAt\n                })),\n            pagination: {\n                page,\n                limit,\n                total,\n                pages: Math.ceil(total / limit)\n            },\n            stats: {\n                total,\n                active: activeUsers,\n                inactive: inactiveUsers,\n                suspended: suspendedUsers,\n                pending: pendingUsers,\n                new: newUsers,\n                byRole: usersByRole.map((item)=>({\n                        role: item.role,\n                        count: item._count.id\n                    }))\n            }\n        });\n    } catch (error) {\n        console.error(\"Error fetching users:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to fetch users\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/super-admin/users - Create new user (admin only)\nasync function POST(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id || session?.user?.role !== \"SUPER_ADMIN\") {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Super admin access required\"\n            }, {\n                status: 403\n            });\n        }\n        const body = await request.json();\n        const { email, password, name, firstName, lastName, phone, role, status, title, department, companyId, sendWelcomeEmail = true } = body;\n        // Validate required fields\n        if (!email || !password || !name) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Email, password, and name are required\"\n            }, {\n                status: 400\n            });\n        }\n        // Check if user already exists\n        const existingUser = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findUnique({\n            where: {\n                email\n            }\n        });\n        if (existingUser) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"User with this email already exists\"\n            }, {\n                status: 400\n            });\n        }\n        // Hash password\n        const hashedPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_4___default().hash(password, 12);\n        // Create user\n        const user = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.create({\n            data: {\n                email,\n                password: hashedPassword,\n                name,\n                firstName,\n                lastName,\n                phone,\n                role: role || \"USER\",\n                status: status || \"ACTIVE\",\n                title,\n                department,\n                companyId,\n                emailVerified: new Date() // Auto-verify for admin-created users\n            },\n            include: {\n                company: {\n                    select: {\n                        id: true,\n                        name: true,\n                        status: true\n                    }\n                }\n            }\n        });\n        // Log the action\n        await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.auditLog.create({\n            data: {\n                action: \"USER_CREATED\",\n                entityType: \"User\",\n                entityId: user.id,\n                userId: session.user.id,\n                userEmail: session.user.email,\n                userRole: session.user.role,\n                newValues: {\n                    email: user.email,\n                    name: user.name,\n                    role: user.role,\n                    status: user.status\n                },\n                metadata: {\n                    createdByAdmin: true,\n                    adminId: session.user.id\n                }\n            }\n        });\n        // Remove password from response\n        const { password: _, ...userResponse } = user;\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            user: userResponse,\n            message: \"User created successfully\"\n        }, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"Error creating user:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to create user\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/super-admin/users/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/github */ \"(rsc)/./node_modules/next-auth/providers/github.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n\nconst authOptions = {\n    // Using JWT strategy instead of database adapter for now\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                try {\n                    if (!credentials?.email || !credentials?.password) {\n                        console.log(\"Missing credentials\");\n                        return null;\n                    }\n                    console.log(\"Attempting to authenticate user:\", credentials.email);\n                    const user = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.findUnique({\n                        where: {\n                            email: credentials.email\n                        },\n                        select: {\n                            id: true,\n                            email: true,\n                            name: true,\n                            password: true,\n                            role: true,\n                            companyId: true\n                        }\n                    });\n                    // Get the company ID - either as member or owner\n                    let finalCompanyId = user?.companyId;\n                    if (!finalCompanyId && user) {\n                        const ownedCompany = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.company.findFirst({\n                            where: {\n                                ownerId: user.id\n                            },\n                            select: {\n                                id: true\n                            }\n                        });\n                        finalCompanyId = ownedCompany?.id;\n                        // If user is company owner, update their companyId for future queries\n                        if (finalCompanyId) {\n                            await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                                where: {\n                                    id: user.id\n                                },\n                                data: {\n                                    companyId: finalCompanyId\n                                }\n                            });\n                        }\n                    }\n                    if (!user) {\n                        console.log(\"User not found:\", credentials.email);\n                        return null;\n                    }\n                    if (!user.password) {\n                        console.log(\"User has no password set:\", credentials.email);\n                        return null;\n                    }\n                    const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_3___default().compare(credentials.password, user.password);\n                    if (!isPasswordValid) {\n                        console.log(\"Invalid password for user:\", credentials.email);\n                        return null;\n                    }\n                    // Update last login\n                    await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                        where: {\n                            id: user.id\n                        },\n                        data: {\n                            lastLoginAt: new Date(),\n                            loginCount: {\n                                increment: 1\n                            }\n                        }\n                    });\n                    console.log(\"User authenticated successfully:\", user.email);\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name,\n                        role: user.role,\n                        companyId: finalCompanyId\n                    };\n                } catch (error) {\n                    console.error(\"Authentication error:\", error);\n                    return null;\n                }\n            }\n        }),\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            clientId: process.env.GITHUB_CLIENT_ID,\n            clientSecret: process.env.GITHUB_CLIENT_SECRET\n        })\n    ],\n    session: {\n        strategy: \"jwt\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                console.log(\"JWT callback - user data:\", {\n                    id: user.id,\n                    email: user.email,\n                    role: user.role,\n                    companyId: user.companyId\n                });\n                token.role = user.role;\n                token.companyId = user.companyId;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n                session.user.companyId = token.companyId;\n                console.log(\"Session callback - final session:\", {\n                    id: session.user.id,\n                    email: session.user.email,\n                    role: session.user.role,\n                    companyId: session.user.companyId\n                });\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        signUp: \"/auth/signup\",\n        error: \"/auth/error\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zYWFzLWFwcC8uL2xpYi9wcmlzbWEudHM/OTgyMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/lru-cache","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Fusers%2Froute&page=%2Fapi%2Fsuper-admin%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Fusers%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();