"use strict";(()=>{var e={};e.id=7270,e.ids=[7270],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},69631:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>_,originalPathname:()=>v,patchFetch:()=>E,requestAsyncStorage:()=>y,routeModule:()=>w,serverHooks:()=>I,staticGenerationAsyncStorage:()=>h,staticGenerationBailout:()=>f});var a={};r.r(a),r.d(a,{GET:()=>m,POST:()=>g});var s=r(95419),n=r(69108),i=r(99678),o=r(78070),u=r(81355),l=r(3205),c=r(9108),d=r(6521),p=r.n(d);async function m(e){try{let t=await (0,u.getServerSession)(l.L);if(!t?.user?.id||t?.user?.role!=="SUPER_ADMIN")return o.Z.json({error:"Super admin access required"},{status:403});let{searchParams:r}=new URL(e.url),a=parseInt(r.get("page")||"1"),s=parseInt(r.get("limit")||"20"),n=r.get("search")||"",i=r.get("role")||"",d=r.get("status")||"",p=r.get("companyId")||"",m=(a-1)*s,g={};n&&(g.OR=[{name:{contains:n,mode:"insensitive"}},{email:{contains:n,mode:"insensitive"}},{firstName:{contains:n,mode:"insensitive"}},{lastName:{contains:n,mode:"insensitive"}}]),i&&"all"!==i&&(g.role=i),d&&"all"!==d&&(g.status=d),p&&(g.companyId=p);let[w,y]=await Promise.all([c._.user.findMany({where:g,skip:m,take:s,include:{company:{select:{id:!0,name:!0,status:!0,industry:!0}},ownedCompany:{select:{id:!0,name:!0,status:!0}},_count:{select:{createdLeads:!0,createdCustomers:!0,createdQuotations:!0,createdInvoices:!0,createdContracts:!0,activities:!0}}},orderBy:{createdAt:"desc"}}),c._.user.count({where:g})]),[h,I,_,f,v,E]=await Promise.all([c._.user.count({where:{status:"ACTIVE"}}),c._.user.count({where:{status:"INACTIVE"}}),c._.user.count({where:{status:"SUSPENDED"}}),c._.user.count({where:{status:"PENDING"}}),c._.user.groupBy({by:["role"],_count:{id:!0}}),c._.user.count({where:{createdAt:{gte:new Date(Date.now()-2592e6)}}})]);return o.Z.json({users:w.map(e=>({id:e.id,email:e.email,name:e.name,firstName:e.firstName,lastName:e.lastName,avatar:e.avatar,phone:e.phone,role:e.role,status:e.status,title:e.title,department:e.department,timezone:e.timezone,language:e.language,emailVerified:e.emailVerified,twoFactorEnabled:e.twoFactorEnabled,lastLoginAt:e.lastLoginAt,loginCount:e.loginCount,lastActiveAt:e.lastActiveAt,company:e.company,ownedCompany:e.ownedCompany,stats:{leadsCreated:e._count.createdLeads,customersCreated:e._count.createdCustomers,quotationsCreated:e._count.createdQuotations,invoicesCreated:e._count.createdInvoices,contractsCreated:e._count.createdContracts,activitiesCount:e._count.activities},createdAt:e.createdAt,updatedAt:e.updatedAt})),pagination:{page:a,limit:s,total:y,pages:Math.ceil(y/s)},stats:{total:y,active:h,inactive:I,suspended:_,pending:f,new:E,byRole:v.map(e=>({role:e.role,count:e._count.id}))}})}catch(e){return console.error("Error fetching users:",e),o.Z.json({error:"Failed to fetch users"},{status:500})}}async function g(e){try{let t=await (0,u.getServerSession)(l.L);if(!t?.user?.id||t?.user?.role!=="SUPER_ADMIN")return o.Z.json({error:"Super admin access required"},{status:403});let{email:r,password:a,name:s,firstName:n,lastName:i,phone:d,role:m,status:g,title:w,department:y,companyId:h,sendWelcomeEmail:I=!0}=await e.json();if(!r||!a||!s)return o.Z.json({error:"Email, password, and name are required"},{status:400});if(await c._.user.findUnique({where:{email:r}}))return o.Z.json({error:"User with this email already exists"},{status:400});let _=await p().hash(a,12),f=await c._.user.create({data:{email:r,password:_,name:s,firstName:n,lastName:i,phone:d,role:m||"USER",status:g||"ACTIVE",title:w,department:y,companyId:h,emailVerified:new Date},include:{company:{select:{id:!0,name:!0,status:!0}}}});await c._.auditLog.create({data:{action:"USER_CREATED",entityType:"User",entityId:f.id,userId:t.user.id,userEmail:t.user.email,userRole:t.user.role,newValues:{email:f.email,name:f.name,role:f.role,status:f.status},metadata:{createdByAdmin:!0,adminId:t.user.id}}});let{password:v,...E}=f;return o.Z.json({user:E,message:"User created successfully"},{status:201})}catch(e){return console.error("Error creating user:",e),o.Z.json({error:"Failed to create user"},{status:500})}}let w=new s.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/super-admin/users/route",pathname:"/api/super-admin/users",filename:"route",bundlePath:"app/api/super-admin/users/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\super-admin\\users\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:y,staticGenerationAsyncStorage:h,serverHooks:I,headerHooks:_,staticGenerationBailout:f}=w,v="/api/super-admin/users/route";function E(){return(0,i.patchFetch)({serverHooks:I,staticGenerationAsyncStorage:h})}},3205:(e,t,r)=>{r.d(t,{L:()=>l});var a=r(86485),s=r(10375),n=r(50694),i=r(6521),o=r.n(i),u=r(9108);let l={providers:[(0,a.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await u._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),r=t?.companyId;if(!r&&t){let e=await u._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(r=e?.id)&&await u._.user.update({where:{id:t.id},data:{companyId:r}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await o().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await u._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:r}}catch(e){return console.error("Authentication error:",e),null}}}),(0,s.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,n.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,r)=>{r.d(t,{_:()=>s});let a=require("@prisma/client"),s=globalThis.prisma??new a.PrismaClient}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,6206,6521,2455,4520],()=>r(69631));module.exports=a})();