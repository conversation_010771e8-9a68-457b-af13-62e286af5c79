"use strict";(()=>{var e={};e.id=1005,e.ids=[1005],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},93896:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>_,originalPathname:()=>v,patchFetch:()=>f,requestAsyncStorage:()=>g,routeModule:()=>m,serverHooks:()=>I,staticGenerationAsyncStorage:()=>w,staticGenerationBailout:()=>h});var a={};r.r(a),r.d(a,{DELETE:()=>y,GET:()=>c,POST:()=>p});var s=r(95419),i=r(69108),n=r(99678),o=r(78070),u=r(81355),d=r(3205),l=r(9108);async function c(e){try{let t=await (0,u.getServerSession)(d.L);if(!t?.user?.id||t?.user?.role!=="SUPER_ADMIN")return o.Z.json({error:"Super admin access required"},{status:403});let{searchParams:r}=new URL(e.url),a=parseInt(r.get("page")||"1"),s=parseInt(r.get("limit")||"50"),i=r.get("action")||"",n=r.get("entityType")||"",c=r.get("userId")||"",p=r.get("companyId")||"",y=r.get("severity")||"",m=r.get("startDate"),g=r.get("endDate"),w=r.get("search")||"",I=(a-1)*s,_={};i&&"all"!==i&&(_.action={contains:i,mode:"insensitive"}),n&&"all"!==n&&(_.entityType=n),c&&(_.userId=c),p&&(_.companyId=p),y&&"all"!==y&&(_.severity=y),(m||g)&&(_.createdAt={},m&&(_.createdAt.gte=new Date(m)),g&&(_.createdAt.lte=new Date(g))),w&&(_.OR=[{action:{contains:w,mode:"insensitive"}},{entityType:{contains:w,mode:"insensitive"}},{userEmail:{contains:w,mode:"insensitive"}},{ipAddress:{contains:w,mode:"insensitive"}}]);let[h,v]=await Promise.all([l._.auditLog.findMany({where:_,skip:I,take:s,include:{user:{select:{id:!0,name:!0,email:!0,avatar:!0}},company:{select:{id:!0,name:!0}}},orderBy:{createdAt:"desc"}}),l._.auditLog.count({where:_})]),[f,A,E,q,L]=await Promise.all([l._.auditLog.count({where:{createdAt:{gte:new Date(Date.now()-864e5)}}}),l._.auditLog.groupBy({by:["action"],_count:{id:!0},orderBy:{_count:{id:"desc"}},take:10}),l._.auditLog.groupBy({by:["severity"],_count:{id:!0}}),l._.auditLog.groupBy({by:["entityType"],_count:{id:!0},orderBy:{_count:{id:"desc"}},take:10}),l._.auditLog.groupBy({by:["userId"],_count:{id:!0},where:{userId:{not:null}},orderBy:{_count:{id:"desc"}},take:10})]),x=L.map(e=>e.userId).filter(Boolean),T=(await l._.user.findMany({where:{id:{in:x}},select:{id:!0,name:!0,email:!0}})).reduce((e,t)=>(e[t.id]=t,e),{});return o.Z.json({auditLogs:h.map(e=>({id:e.id,action:e.action,entityType:e.entityType,entityId:e.entityId,user:e.user,userEmail:e.userEmail,userRole:e.userRole,company:e.company,ipAddress:e.ipAddress,userAgent:e.userAgent,requestUrl:e.requestUrl,requestMethod:e.requestMethod,oldValues:e.oldValues,newValues:e.newValues,metadata:e.metadata,severity:e.severity,createdAt:e.createdAt})),pagination:{page:a,limit:s,total:v,pages:Math.ceil(v/s)},stats:{total:v,last24Hours:f,byAction:A.map(e=>({action:e.action,count:e._count.id})),bySeverity:E.map(e=>({severity:e.severity,count:e._count.id})),byEntityType:q.map(e=>({entityType:e.entityType,count:e._count.id})),byUser:L.map(e=>({userId:e.userId,user:T[e.userId],count:e._count.id}))}})}catch(e){return console.error("Error fetching audit logs:",e),o.Z.json({error:"Failed to fetch audit logs"},{status:500})}}async function p(e){try{let t=await (0,u.getServerSession)(d.L);if(!t?.user?.id||t?.user?.role!=="SUPER_ADMIN")return o.Z.json({error:"Super admin access required"},{status:403});let{action:r,entityType:a,entityId:s,oldValues:i,newValues:n,metadata:c,severity:p="INFO"}=await e.json();if(!r||!a)return o.Z.json({error:"Action and entityType are required"},{status:400});let y=await l._.auditLog.create({data:{action:r,entityType:a,entityId:s,userId:t.user.id,userEmail:t.user.email,userRole:t.user.role,oldValues:i,newValues:n,metadata:{...c,manualEntry:!0,createdByAdmin:!0},severity:p}});return o.Z.json({auditLog:y,message:"Audit log entry created successfully"},{status:201})}catch(e){return console.error("Error creating audit log:",e),o.Z.json({error:"Failed to create audit log"},{status:500})}}async function y(e){try{let t=await (0,u.getServerSession)(d.L);if(!t?.user?.id||t?.user?.role!=="SUPER_ADMIN")return o.Z.json({error:"Super admin access required"},{status:403});let{searchParams:r}=new URL(e.url),a=parseInt(r.get("days")||"90"),s=r.get("severity");if(a<30)return o.Z.json({error:"Cannot delete logs newer than 30 days"},{status:400});let i=new Date(Date.now()-864e5*a),n={createdAt:{lt:i}};s&&(n.severity=s);let c=await l._.auditLog.deleteMany({where:n});return await l._.auditLog.create({data:{action:"AUDIT_LOG_CLEANUP",entityType:"AuditLog",userId:t.user.id,userEmail:t.user.email,userRole:t.user.role,metadata:{deletedCount:c.count,cutoffDate:i,days:a,severity:s},severity:"INFO"}}),o.Z.json({message:`Deleted ${c.count} audit log entries older than ${a} days`,deletedCount:c.count})}catch(e){return console.error("Error deleting audit logs:",e),o.Z.json({error:"Failed to delete audit logs"},{status:500})}}let m=new s.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/super-admin/audit-logs/route",pathname:"/api/super-admin/audit-logs",filename:"route",bundlePath:"app/api/super-admin/audit-logs/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\super-admin\\audit-logs\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:g,staticGenerationAsyncStorage:w,serverHooks:I,headerHooks:_,staticGenerationBailout:h}=m,v="/api/super-admin/audit-logs/route";function f(){return(0,n.patchFetch)({serverHooks:I,staticGenerationAsyncStorage:w})}},3205:(e,t,r)=>{r.d(t,{L:()=>d});var a=r(86485),s=r(10375),i=r(50694),n=r(6521),o=r.n(n),u=r(9108);let d={providers:[(0,a.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await u._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),r=t?.companyId;if(!r&&t){let e=await u._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(r=e?.id)&&await u._.user.update({where:{id:t.id},data:{companyId:r}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await o().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await u._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:r}}catch(e){return console.error("Authentication error:",e),null}}}),(0,s.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,i.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,r)=>{r.d(t,{_:()=>s});let a=require("@prisma/client"),s=globalThis.prisma??new a.PrismaClient}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,6206,6521,2455,4520],()=>r(93896));module.exports=a})();