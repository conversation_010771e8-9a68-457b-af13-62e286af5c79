"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/super-admin/audit-logs/route";
exports.ids = ["app/api/super-admin/audit-logs/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Faudit-logs%2Froute&page=%2Fapi%2Fsuper-admin%2Faudit-logs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Faudit-logs%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Faudit-logs%2Froute&page=%2Fapi%2Fsuper-admin%2Faudit-logs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Faudit-logs%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_proj_nextjs_saas_app_api_super_admin_audit_logs_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/super-admin/audit-logs/route.ts */ \"(rsc)/./app/api/super-admin/audit-logs/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/super-admin/audit-logs/route\",\n        pathname: \"/api/super-admin/audit-logs\",\n        filename: \"route\",\n        bundlePath: \"app/api/super-admin/audit-logs/route\"\n    },\n    resolvedPagePath: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\api\\\\super-admin\\\\audit-logs\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_proj_nextjs_saas_app_api_super_admin_audit_logs_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/super-admin/audit-logs/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Faudit-logs%2Froute&page=%2Fapi%2Fsuper-admin%2Faudit-logs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Faudit-logs%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/super-admin/audit-logs/route.ts":
/*!*************************************************!*\
  !*** ./app/api/super-admin/audit-logs/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n// GET /api/super-admin/audit-logs - Get audit logs with filtering\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id || session?.user?.role !== \"SUPER_ADMIN\") {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Super admin access required\"\n            }, {\n                status: 403\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get(\"page\") || \"1\");\n        const limit = parseInt(searchParams.get(\"limit\") || \"50\");\n        const action = searchParams.get(\"action\") || \"\";\n        const entityType = searchParams.get(\"entityType\") || \"\";\n        const userId = searchParams.get(\"userId\") || \"\";\n        const companyId = searchParams.get(\"companyId\") || \"\";\n        const severity = searchParams.get(\"severity\") || \"\";\n        const startDate = searchParams.get(\"startDate\");\n        const endDate = searchParams.get(\"endDate\");\n        const search = searchParams.get(\"search\") || \"\";\n        const skip = (page - 1) * limit;\n        // Build where clause\n        const where = {};\n        if (action && action !== \"all\") {\n            where.action = {\n                contains: action,\n                mode: \"insensitive\"\n            };\n        }\n        if (entityType && entityType !== \"all\") {\n            where.entityType = entityType;\n        }\n        if (userId) {\n            where.userId = userId;\n        }\n        if (companyId) {\n            where.companyId = companyId;\n        }\n        if (severity && severity !== \"all\") {\n            where.severity = severity;\n        }\n        if (startDate || endDate) {\n            where.createdAt = {};\n            if (startDate) {\n                where.createdAt.gte = new Date(startDate);\n            }\n            if (endDate) {\n                where.createdAt.lte = new Date(endDate);\n            }\n        }\n        if (search) {\n            where.OR = [\n                {\n                    action: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    entityType: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    userEmail: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    ipAddress: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                }\n            ];\n        }\n        // Get audit logs with pagination\n        const [auditLogs, total] = await Promise.all([\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.auditLog.findMany({\n                where,\n                skip,\n                take: limit,\n                include: {\n                    user: {\n                        select: {\n                            id: true,\n                            name: true,\n                            email: true,\n                            avatar: true\n                        }\n                    },\n                    company: {\n                        select: {\n                            id: true,\n                            name: true\n                        }\n                    }\n                },\n                orderBy: {\n                    createdAt: \"desc\"\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.auditLog.count({\n                where\n            })\n        ]);\n        // Get statistics\n        const stats = await Promise.all([\n            // Actions in the last 24 hours\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.auditLog.count({\n                where: {\n                    createdAt: {\n                        gte: new Date(Date.now() - 24 * 60 * 60 * 1000)\n                    }\n                }\n            }),\n            // Group by action\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.auditLog.groupBy({\n                by: [\n                    \"action\"\n                ],\n                _count: {\n                    id: true\n                },\n                orderBy: {\n                    _count: {\n                        id: \"desc\"\n                    }\n                },\n                take: 10\n            }),\n            // Group by severity\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.auditLog.groupBy({\n                by: [\n                    \"severity\"\n                ],\n                _count: {\n                    id: true\n                }\n            }),\n            // Group by entity type\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.auditLog.groupBy({\n                by: [\n                    \"entityType\"\n                ],\n                _count: {\n                    id: true\n                },\n                orderBy: {\n                    _count: {\n                        id: \"desc\"\n                    }\n                },\n                take: 10\n            }),\n            // Most active users\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.auditLog.groupBy({\n                by: [\n                    \"userId\"\n                ],\n                _count: {\n                    id: true\n                },\n                where: {\n                    userId: {\n                        not: null\n                    }\n                },\n                orderBy: {\n                    _count: {\n                        id: \"desc\"\n                    }\n                },\n                take: 10\n            })\n        ]);\n        const [last24Hours, byAction, bySeverity, byEntityType, byUser] = stats;\n        // Get user details for most active users\n        const userIds = byUser.map((item)=>item.userId).filter(Boolean);\n        const users = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findMany({\n            where: {\n                id: {\n                    in: userIds\n                }\n            },\n            select: {\n                id: true,\n                name: true,\n                email: true\n            }\n        });\n        const userMap = users.reduce((acc, user)=>{\n            acc[user.id] = user;\n            return acc;\n        }, {});\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            auditLogs: auditLogs.map((log)=>({\n                    id: log.id,\n                    action: log.action,\n                    entityType: log.entityType,\n                    entityId: log.entityId,\n                    user: log.user,\n                    userEmail: log.userEmail,\n                    userRole: log.userRole,\n                    company: log.company,\n                    ipAddress: log.ipAddress,\n                    userAgent: log.userAgent,\n                    requestUrl: log.requestUrl,\n                    requestMethod: log.requestMethod,\n                    oldValues: log.oldValues,\n                    newValues: log.newValues,\n                    metadata: log.metadata,\n                    severity: log.severity,\n                    createdAt: log.createdAt\n                })),\n            pagination: {\n                page,\n                limit,\n                total,\n                pages: Math.ceil(total / limit)\n            },\n            stats: {\n                total,\n                last24Hours,\n                byAction: byAction.map((item)=>({\n                        action: item.action,\n                        count: item._count.id\n                    })),\n                bySeverity: bySeverity.map((item)=>({\n                        severity: item.severity,\n                        count: item._count.id\n                    })),\n                byEntityType: byEntityType.map((item)=>({\n                        entityType: item.entityType,\n                        count: item._count.id\n                    })),\n                byUser: byUser.map((item)=>({\n                        userId: item.userId,\n                        user: userMap[item.userId],\n                        count: item._count.id\n                    }))\n            }\n        });\n    } catch (error) {\n        console.error(\"Error fetching audit logs:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to fetch audit logs\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/super-admin/audit-logs - Create audit log entry (for testing or manual entries)\nasync function POST(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id || session?.user?.role !== \"SUPER_ADMIN\") {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Super admin access required\"\n            }, {\n                status: 403\n            });\n        }\n        const body = await request.json();\n        const { action, entityType, entityId, oldValues, newValues, metadata, severity = \"INFO\" } = body;\n        if (!action || !entityType) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Action and entityType are required\"\n            }, {\n                status: 400\n            });\n        }\n        const auditLog = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.auditLog.create({\n            data: {\n                action,\n                entityType,\n                entityId,\n                userId: session.user.id,\n                userEmail: session.user.email,\n                userRole: session.user.role,\n                oldValues,\n                newValues,\n                metadata: {\n                    ...metadata,\n                    manualEntry: true,\n                    createdByAdmin: true\n                },\n                severity\n            }\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            auditLog,\n            message: \"Audit log entry created successfully\"\n        }, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"Error creating audit log:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to create audit log\"\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE /api/super-admin/audit-logs - Bulk delete old audit logs\nasync function DELETE(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id || session?.user?.role !== \"SUPER_ADMIN\") {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Super admin access required\"\n            }, {\n                status: 403\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const days = parseInt(searchParams.get(\"days\") || \"90\");\n        const severity = searchParams.get(\"severity\");\n        if (days < 30) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Cannot delete logs newer than 30 days\"\n            }, {\n                status: 400\n            });\n        }\n        const cutoffDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);\n        const where = {\n            createdAt: {\n                lt: cutoffDate\n            }\n        };\n        if (severity) {\n            where.severity = severity;\n        }\n        const deletedLogs = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.auditLog.deleteMany({\n            where\n        });\n        // Log the cleanup action\n        await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.auditLog.create({\n            data: {\n                action: \"AUDIT_LOG_CLEANUP\",\n                entityType: \"AuditLog\",\n                userId: session.user.id,\n                userEmail: session.user.email,\n                userRole: session.user.role,\n                metadata: {\n                    deletedCount: deletedLogs.count,\n                    cutoffDate,\n                    days,\n                    severity\n                },\n                severity: \"INFO\"\n            }\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            message: `Deleted ${deletedLogs.count} audit log entries older than ${days} days`,\n            deletedCount: deletedLogs.count\n        });\n    } catch (error) {\n        console.error(\"Error deleting audit logs:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to delete audit logs\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/super-admin/audit-logs/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/github */ \"(rsc)/./node_modules/next-auth/providers/github.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n\nconst authOptions = {\n    // Using JWT strategy instead of database adapter for now\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                try {\n                    if (!credentials?.email || !credentials?.password) {\n                        console.log(\"Missing credentials\");\n                        return null;\n                    }\n                    console.log(\"Attempting to authenticate user:\", credentials.email);\n                    const user = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.findUnique({\n                        where: {\n                            email: credentials.email\n                        },\n                        select: {\n                            id: true,\n                            email: true,\n                            name: true,\n                            password: true,\n                            role: true,\n                            companyId: true\n                        }\n                    });\n                    // Get the company ID - either as member or owner\n                    let finalCompanyId = user?.companyId;\n                    if (!finalCompanyId && user) {\n                        const ownedCompany = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.company.findFirst({\n                            where: {\n                                ownerId: user.id\n                            },\n                            select: {\n                                id: true\n                            }\n                        });\n                        finalCompanyId = ownedCompany?.id;\n                        // If user is company owner, update their companyId for future queries\n                        if (finalCompanyId) {\n                            await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                                where: {\n                                    id: user.id\n                                },\n                                data: {\n                                    companyId: finalCompanyId\n                                }\n                            });\n                        }\n                    }\n                    if (!user) {\n                        console.log(\"User not found:\", credentials.email);\n                        return null;\n                    }\n                    if (!user.password) {\n                        console.log(\"User has no password set:\", credentials.email);\n                        return null;\n                    }\n                    const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_3___default().compare(credentials.password, user.password);\n                    if (!isPasswordValid) {\n                        console.log(\"Invalid password for user:\", credentials.email);\n                        return null;\n                    }\n                    // Update last login\n                    await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                        where: {\n                            id: user.id\n                        },\n                        data: {\n                            lastLoginAt: new Date(),\n                            loginCount: {\n                                increment: 1\n                            }\n                        }\n                    });\n                    console.log(\"User authenticated successfully:\", user.email);\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name,\n                        role: user.role,\n                        companyId: finalCompanyId\n                    };\n                } catch (error) {\n                    console.error(\"Authentication error:\", error);\n                    return null;\n                }\n            }\n        }),\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            clientId: process.env.GITHUB_CLIENT_ID,\n            clientSecret: process.env.GITHUB_CLIENT_SECRET\n        })\n    ],\n    session: {\n        strategy: \"jwt\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                console.log(\"JWT callback - user data:\", {\n                    id: user.id,\n                    email: user.email,\n                    role: user.role,\n                    companyId: user.companyId\n                });\n                token.role = user.role;\n                token.companyId = user.companyId;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n                session.user.companyId = token.companyId;\n                console.log(\"Session callback - final session:\", {\n                    id: session.user.id,\n                    email: session.user.email,\n                    role: session.user.role,\n                    companyId: session.user.companyId\n                });\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        signUp: \"/auth/signup\",\n        error: \"/auth/error\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zYWFzLWFwcC8uL2xpYi9wcmlzbWEudHM/OTgyMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/lru-cache","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsuper-admin%2Faudit-logs%2Froute&page=%2Fapi%2Fsuper-admin%2Faudit-logs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsuper-admin%2Faudit-logs%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();