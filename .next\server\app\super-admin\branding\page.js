(()=>{var e={};e.id=5162,e.ids=[5162],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},56320:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>t.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>d,routeModule:()=>u,tree:()=>c});var r=a(50482),o=a(69108),l=a(62563),t=a.n(l),n=a(68300),i={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>n[e]);a.d(s,i);let c=["",{children:["super-admin",{children:["branding",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,92566)),"C:\\proj\\nextjs-saas\\app\\super-admin\\branding\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,11285)),"C:\\proj\\nextjs-saas\\app\\super-admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,59504)),"C:\\proj\\nextjs-saas\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,69361,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\proj\\nextjs-saas\\app\\super-admin\\branding\\page.tsx"],x="/super-admin/branding/page",m={require:a,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/super-admin/branding/page",pathname:"/super-admin/branding",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},42659:(e,s,a)=>{Promise.resolve().then(a.bind(a,26480))},26480:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>w});var r=a(95344),o=a(3729),l=a(47674),t=a(22254),n=a(61351),i=a(16212),c=a(92549),d=a(1586),x=a(93601),m=a(25757),u=a(70009),h=a(33733),p=a(53148),g=a(31498),y=a(73101),j=a(69224);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let f=(0,j.Z)("Tablet",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["line",{x1:"12",x2:"12.01",y1:"18",y2:"18",key:"1dp563"}]]);var v=a(16588);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let b=(0,j.Z)("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);var N=a(3380);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let C=(0,j.Z)("Type",[["polyline",{points:"4 7 4 4 20 4 20 7",key:"1nosan"}],["line",{x1:"9",x2:"15",y1:"20",y2:"20",key:"swin9y"}],["line",{x1:"12",x2:"12",y1:"4",y2:"20",key:"1tx1rr"}]]);var k=a(44669);function w(){let{data:e,status:s}=(0,l.useSession)(),[a,j]=(0,o.useState)({logoUrl:"",logoUrlDark:"",faviconUrl:"",loginBackgroundUrl:"",primaryColor:"#3b82f6",secondaryColor:"#64748b",accentColor:"#10b981",backgroundColor:"#ffffff",textColor:"#1f2937",fontFamily:"Inter",headingFont:"Inter",fontSize:"14px",theme:"light",borderRadius:"8px",customCss:"",footerText:"Built with ❤️ by Your Company",copyrightText:"\xa9 2024 Your Company. All rights reserved.",socialLinks:{website:"",twitter:"",linkedin:"",facebook:"",instagram:"",youtube:""}}),[w,S]=(0,o.useState)(!0),[F,P]=(0,o.useState)(!1),[_,T]=(0,o.useState)("desktop");if("loading"===s)return r.jsx("div",{className:"min-h-screen flex items-center justify-center",children:r.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})});"unauthenticated"===s&&(0,t.redirect)("/auth/signin"),e?.user?.role!=="SUPER_ADMIN"&&(0,t.redirect)("/dashboard");let z=async()=>{try{S(!0);let e=await fetch("/api/super-admin/branding"),s=await e.json();s.success&&j({...a,...s.branding})}catch(e){console.error("Error fetching branding:",e),k.toast.error("Failed to load branding configuration")}finally{S(!1)}},I=async()=>{try{P(!0);let e=await fetch("/api/super-admin/branding",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)}),s=await e.json();s.success?k.toast.success("Branding configuration saved successfully"):k.toast.error(s.error||"Failed to save branding configuration")}catch(e){console.error("Error saving branding:",e),k.toast.error("Failed to save branding configuration")}finally{P(!1)}};(0,o.useEffect)(()=>{z()},[]);let L=(e,s)=>{j(a=>({...a,[e]:s}))},Z=(e,s)=>{j(a=>({...a,socialLinks:{...a.socialLinks,[e]:s}}))},M=async(e,s)=>{try{let a=new FormData;a.append("file",e),a.append("type",s);let r=await fetch("/api/super-admin/branding/upload",{method:"POST",body:a}),o=await r.json();o.success?(L("logo"===s?"logoUrl":"logoDark"===s?"logoUrlDark":"favicon"===s?"faviconUrl":"loginBackgroundUrl",o.url),k.toast.success("File uploaded successfully")):k.toast.error(o.error||"Failed to upload file")}catch(e){console.error("Error uploading file:",e),k.toast.error("Failed to upload file")}};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx(u.Z,{className:"h-8 w-8 text-purple-600"}),r.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Branding & Customization"})]}),r.jsx("p",{className:"text-gray-500 mt-1",children:"Customize your application's look and feel"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)(i.z,{variant:"outline",onClick:z,disabled:w,children:[r.jsx(h.Z,{className:`h-4 w-4 mr-2 ${w?"animate-spin":""}`}),"Refresh"]}),(0,r.jsxs)(i.z,{variant:"outline",children:[r.jsx(p.Z,{className:"h-4 w-4 mr-2"}),"Preview"]}),(0,r.jsxs)(i.z,{onClick:I,disabled:F,children:[r.jsx(g.Z,{className:`h-4 w-4 mr-2 ${F?"animate-spin":""}`}),"Save Changes"]})]})]}),(0,r.jsxs)(n.Zb,{children:[r.jsx(n.Ol,{children:(0,r.jsxs)(n.ll,{className:"flex items-center",children:[r.jsx(y.Z,{className:"h-5 w-5 mr-2"}),"Preview Mode"]})}),r.jsx(n.aY,{children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)(i.z,{variant:"desktop"===_?"default":"outline",size:"sm",onClick:()=>T("desktop"),children:[r.jsx(y.Z,{className:"h-4 w-4 mr-2"}),"Desktop"]}),(0,r.jsxs)(i.z,{variant:"tablet"===_?"default":"outline",size:"sm",onClick:()=>T("tablet"),children:[r.jsx(f,{className:"h-4 w-4 mr-2"}),"Tablet"]}),(0,r.jsxs)(i.z,{variant:"mobile"===_?"default":"outline",size:"sm",onClick:()=>T("mobile"),children:[r.jsx(v.Z,{className:"h-4 w-4 mr-2"}),"Mobile"]})]})})]}),(0,r.jsxs)(m.mQ,{defaultValue:"logos",className:"space-y-6",children:[(0,r.jsxs)(m.dr,{className:"grid w-full grid-cols-5",children:[r.jsx(m.SP,{value:"logos",children:"Logos & Images"}),r.jsx(m.SP,{value:"colors",children:"Colors"}),r.jsx(m.SP,{value:"typography",children:"Typography"}),r.jsx(m.SP,{value:"theme",children:"Theme"}),r.jsx(m.SP,{value:"footer",children:"Footer & Social"})]}),r.jsx(m.nU,{value:"logos",children:(0,r.jsxs)(n.Zb,{children:[(0,r.jsxs)(n.Ol,{children:[(0,r.jsxs)(n.ll,{className:"flex items-center",children:[r.jsx(b,{className:"h-5 w-5 mr-2"}),"Logos & Images"]}),r.jsx(n.SZ,{children:"Upload and manage your brand assets"})]}),r.jsx(n.aY,{className:"space-y-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx(d._,{children:"Main Logo (Light Theme)"}),(0,r.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center",children:[a.logoUrl?(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx("img",{src:a.logoUrl,alt:"Logo",className:"max-h-16 mx-auto"}),r.jsx("p",{className:"text-sm text-gray-500",children:"Current logo"})]}):(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(b,{className:"h-12 w-12 mx-auto text-gray-400"}),r.jsx("p",{className:"text-sm text-gray-500",children:"No logo uploaded"})]}),(0,r.jsxs)(i.z,{variant:"outline",size:"sm",className:"mt-2",onClick:()=>document.getElementById("logo-upload")?.click(),children:[r.jsx(N.Z,{className:"h-4 w-4 mr-2"}),"Upload Logo"]}),r.jsx("input",{id:"logo-upload",type:"file",accept:"image/*",className:"hidden",onChange:e=>{let s=e.target.files?.[0];s&&M(s,"logo")}})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx(d._,{children:"Logo (Dark Theme)"}),(0,r.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center bg-gray-900",children:[a.logoUrlDark?(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx("img",{src:a.logoUrlDark,alt:"Dark Logo",className:"max-h-16 mx-auto"}),r.jsx("p",{className:"text-sm text-gray-400",children:"Current dark logo"})]}):(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(b,{className:"h-12 w-12 mx-auto text-gray-400"}),r.jsx("p",{className:"text-sm text-gray-400",children:"No dark logo uploaded"})]}),(0,r.jsxs)(i.z,{variant:"outline",size:"sm",className:"mt-2",onClick:()=>document.getElementById("logo-dark-upload")?.click(),children:[r.jsx(N.Z,{className:"h-4 w-4 mr-2"}),"Upload Dark Logo"]}),r.jsx("input",{id:"logo-dark-upload",type:"file",accept:"image/*",className:"hidden",onChange:e=>{let s=e.target.files?.[0];s&&M(s,"logoDark")}})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx(d._,{children:"Favicon"}),(0,r.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center",children:[a.faviconUrl?(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx("img",{src:a.faviconUrl,alt:"Favicon",className:"w-8 h-8 mx-auto"}),r.jsx("p",{className:"text-sm text-gray-500",children:"Current favicon"})]}):(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(b,{className:"h-8 w-8 mx-auto text-gray-400"}),r.jsx("p",{className:"text-sm text-gray-500",children:"No favicon uploaded"})]}),(0,r.jsxs)(i.z,{variant:"outline",size:"sm",className:"mt-2",onClick:()=>document.getElementById("favicon-upload")?.click(),children:[r.jsx(N.Z,{className:"h-4 w-4 mr-2"}),"Upload Favicon"]}),r.jsx("input",{id:"favicon-upload",type:"file",accept:"image/x-icon,image/png",className:"hidden",onChange:e=>{let s=e.target.files?.[0];s&&M(s,"favicon")}})]}),r.jsx("p",{className:"text-xs text-gray-500",children:"Recommended: 32x32px ICO or PNG"})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx(d._,{children:"Login Background"}),(0,r.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center",children:[a.loginBackgroundUrl?(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx("img",{src:a.loginBackgroundUrl,alt:"Background",className:"max-h-16 mx-auto rounded"}),r.jsx("p",{className:"text-sm text-gray-500",children:"Current background"})]}):(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(b,{className:"h-12 w-12 mx-auto text-gray-400"}),r.jsx("p",{className:"text-sm text-gray-500",children:"No background uploaded"})]}),(0,r.jsxs)(i.z,{variant:"outline",size:"sm",className:"mt-2",onClick:()=>document.getElementById("background-upload")?.click(),children:[r.jsx(N.Z,{className:"h-4 w-4 mr-2"}),"Upload Background"]}),r.jsx("input",{id:"background-upload",type:"file",accept:"image/*",className:"hidden",onChange:e=>{let s=e.target.files?.[0];s&&M(s,"background")}})]}),r.jsx("p",{className:"text-xs text-gray-500",children:"Recommended: 1920x1080px or higher"})]})]})})]})}),r.jsx(m.nU,{value:"colors",children:(0,r.jsxs)(n.Zb,{children:[(0,r.jsxs)(n.Ol,{children:[(0,r.jsxs)(n.ll,{className:"flex items-center",children:[r.jsx(u.Z,{className:"h-5 w-5 mr-2"}),"Color Scheme"]}),r.jsx(n.SZ,{children:"Customize your application's color palette"})]}),(0,r.jsxs)(n.aY,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"primaryColor",children:"Primary Color"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("input",{type:"color",id:"primaryColor",value:a.primaryColor,onChange:e=>L("primaryColor",e.target.value),className:"w-12 h-10 rounded border border-gray-300"}),r.jsx(c.I,{value:a.primaryColor,onChange:e=>L("primaryColor",e.target.value),placeholder:"#3b82f6"})]}),r.jsx("p",{className:"text-xs text-gray-500",children:"Main brand color for buttons, links, etc."})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"secondaryColor",children:"Secondary Color"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("input",{type:"color",id:"secondaryColor",value:a.secondaryColor,onChange:e=>L("secondaryColor",e.target.value),className:"w-12 h-10 rounded border border-gray-300"}),r.jsx(c.I,{value:a.secondaryColor,onChange:e=>L("secondaryColor",e.target.value),placeholder:"#64748b"})]}),r.jsx("p",{className:"text-xs text-gray-500",children:"Secondary elements and borders"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"accentColor",children:"Accent Color"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("input",{type:"color",id:"accentColor",value:a.accentColor,onChange:e=>L("accentColor",e.target.value),className:"w-12 h-10 rounded border border-gray-300"}),r.jsx(c.I,{value:a.accentColor,onChange:e=>L("accentColor",e.target.value),placeholder:"#10b981"})]}),r.jsx("p",{className:"text-xs text-gray-500",children:"Success states and highlights"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"backgroundColor",children:"Background Color"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("input",{type:"color",id:"backgroundColor",value:a.backgroundColor,onChange:e=>L("backgroundColor",e.target.value),className:"w-12 h-10 rounded border border-gray-300"}),r.jsx(c.I,{value:a.backgroundColor,onChange:e=>L("backgroundColor",e.target.value),placeholder:"#ffffff"})]}),r.jsx("p",{className:"text-xs text-gray-500",children:"Main background color"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"textColor",children:"Text Color"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("input",{type:"color",id:"textColor",value:a.textColor,onChange:e=>L("textColor",e.target.value),className:"w-12 h-10 rounded border border-gray-300"}),r.jsx(c.I,{value:a.textColor,onChange:e=>L("textColor",e.target.value),placeholder:"#1f2937"})]}),r.jsx("p",{className:"text-xs text-gray-500",children:"Primary text color"})]})]}),(0,r.jsxs)("div",{className:"mt-8",children:[r.jsx(d._,{className:"text-base font-medium",children:"Color Preview"}),r.jsx("div",{className:"mt-4 p-6 rounded-lg border",style:{backgroundColor:a.backgroundColor},children:(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx("h3",{style:{color:a.textColor},className:"text-xl font-semibold",children:"Sample Heading"}),r.jsx("p",{style:{color:a.textColor},className:"opacity-80",children:"This is how your text will look with the selected colors."}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx("button",{style:{backgroundColor:a.primaryColor},className:"px-4 py-2 text-white rounded-md",children:"Primary Button"}),r.jsx("button",{style:{backgroundColor:"transparent",color:a.secondaryColor,borderColor:a.secondaryColor},className:"px-4 py-2 border rounded-md",children:"Secondary Button"}),r.jsx("span",{style:{color:a.accentColor},className:"font-medium",children:"Accent Text"})]})]})})]})]})]})}),r.jsx(m.nU,{value:"typography",children:(0,r.jsxs)(n.Zb,{children:[(0,r.jsxs)(n.Ol,{children:[(0,r.jsxs)(n.ll,{className:"flex items-center",children:[r.jsx(C,{className:"h-5 w-5 mr-2"}),"Typography"]}),r.jsx(n.SZ,{children:"Configure fonts and text styling"})]}),(0,r.jsxs)(n.aY,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"fontFamily",children:"Body Font"}),(0,r.jsxs)("select",{id:"fontFamily",value:a.fontFamily,onChange:e=>L("fontFamily",e.target.value),className:"w-full p-2 border border-gray-300 rounded-md",children:[r.jsx("option",{value:"Inter",children:"Inter"}),r.jsx("option",{value:"Roboto",children:"Roboto"}),r.jsx("option",{value:"Open Sans",children:"Open Sans"}),r.jsx("option",{value:"Lato",children:"Lato"}),r.jsx("option",{value:"Poppins",children:"Poppins"}),r.jsx("option",{value:"Montserrat",children:"Montserrat"}),r.jsx("option",{value:"Source Sans Pro",children:"Source Sans Pro"}),r.jsx("option",{value:"system-ui",children:"System UI"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"headingFont",children:"Heading Font"}),(0,r.jsxs)("select",{id:"headingFont",value:a.headingFont,onChange:e=>L("headingFont",e.target.value),className:"w-full p-2 border border-gray-300 rounded-md",children:[r.jsx("option",{value:"Inter",children:"Inter"}),r.jsx("option",{value:"Roboto",children:"Roboto"}),r.jsx("option",{value:"Open Sans",children:"Open Sans"}),r.jsx("option",{value:"Lato",children:"Lato"}),r.jsx("option",{value:"Poppins",children:"Poppins"}),r.jsx("option",{value:"Montserrat",children:"Montserrat"}),r.jsx("option",{value:"Playfair Display",children:"Playfair Display"}),r.jsx("option",{value:"Merriweather",children:"Merriweather"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"fontSize",children:"Base Font Size"}),(0,r.jsxs)("select",{id:"fontSize",value:a.fontSize,onChange:e=>L("fontSize",e.target.value),className:"w-full p-2 border border-gray-300 rounded-md",children:[r.jsx("option",{value:"12px",children:"12px - Small"}),r.jsx("option",{value:"14px",children:"14px - Default"}),r.jsx("option",{value:"16px",children:"16px - Large"}),r.jsx("option",{value:"18px",children:"18px - Extra Large"})]})]})]}),(0,r.jsxs)("div",{className:"mt-8",children:[r.jsx(d._,{className:"text-base font-medium",children:"Typography Preview"}),r.jsx("div",{className:"mt-4 p-6 rounded-lg border bg-white",children:(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx("h1",{style:{fontFamily:a.headingFont,fontSize:"2rem",color:a.textColor},className:"font-bold",children:"Main Heading (H1)"}),r.jsx("h2",{style:{fontFamily:a.headingFont,fontSize:"1.5rem",color:a.textColor},className:"font-semibold",children:"Section Heading (H2)"}),r.jsx("h3",{style:{fontFamily:a.headingFont,fontSize:"1.25rem",color:a.textColor},className:"font-medium",children:"Subsection Heading (H3)"}),r.jsx("p",{style:{fontFamily:a.fontFamily,fontSize:a.fontSize,color:a.textColor},children:"This is body text using the selected font family and size. It demonstrates how regular paragraph text will appear throughout your application."}),r.jsx("p",{style:{fontFamily:a.fontFamily,fontSize:a.fontSize,color:a.secondaryColor},className:"text-sm",children:"This is secondary text, often used for descriptions and less important information."})]})})]})]})]})}),r.jsx(m.nU,{value:"theme",children:(0,r.jsxs)(n.Zb,{children:[(0,r.jsxs)(n.Ol,{children:[r.jsx(n.ll,{children:"Theme Settings"}),r.jsx(n.SZ,{children:"Configure theme preferences and custom styling"})]}),(0,r.jsxs)(n.aY,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"theme",children:"Default Theme"}),(0,r.jsxs)("select",{id:"theme",value:a.theme,onChange:e=>L("theme",e.target.value),className:"w-full p-2 border border-gray-300 rounded-md",children:[r.jsx("option",{value:"light",children:"Light"}),r.jsx("option",{value:"dark",children:"Dark"}),r.jsx("option",{value:"auto",children:"Auto (System Preference)"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"borderRadius",children:"Border Radius"}),(0,r.jsxs)("select",{id:"borderRadius",value:a.borderRadius,onChange:e=>L("borderRadius",e.target.value),className:"w-full p-2 border border-gray-300 rounded-md",children:[r.jsx("option",{value:"0px",children:"Sharp (0px)"}),r.jsx("option",{value:"4px",children:"Small (4px)"}),r.jsx("option",{value:"8px",children:"Medium (8px)"}),r.jsx("option",{value:"12px",children:"Large (12px)"}),r.jsx("option",{value:"16px",children:"Extra Large (16px)"}),r.jsx("option",{value:"9999px",children:"Pill (9999px)"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"customCss",children:"Custom CSS"}),r.jsx(x.g,{id:"customCss",value:a.customCss,onChange:e=>L("customCss",e.target.value),placeholder:"/* Add your custom CSS here */ .custom-class { /* Your styles */ }",rows:10,className:"font-mono text-sm"}),r.jsx("p",{className:"text-xs text-gray-500",children:"Add custom CSS to override default styles. Use with caution as this can affect application functionality."})]})]})]})}),r.jsx(m.nU,{value:"footer",children:(0,r.jsxs)(n.Zb,{children:[(0,r.jsxs)(n.Ol,{children:[r.jsx(n.ll,{children:"Footer & Social Links"}),r.jsx(n.SZ,{children:"Configure footer content and social media links"})]}),(0,r.jsxs)(n.aY,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"footerText",children:"Footer Text"}),r.jsx(c.I,{id:"footerText",value:a.footerText,onChange:e=>L("footerText",e.target.value),placeholder:"Built with ❤️ by Your Company"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"copyrightText",children:"Copyright Text"}),r.jsx(c.I,{id:"copyrightText",value:a.copyrightText,onChange:e=>L("copyrightText",e.target.value),placeholder:"\xa9 2024 Your Company. All rights reserved."})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx(d._,{className:"text-base font-medium",children:"Social Media Links"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"website",children:"Website"}),r.jsx(c.I,{id:"website",value:a.socialLinks.website,onChange:e=>Z("website",e.target.value),placeholder:"https://yourcompany.com"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"twitter",children:"Twitter"}),r.jsx(c.I,{id:"twitter",value:a.socialLinks.twitter,onChange:e=>Z("twitter",e.target.value),placeholder:"https://twitter.com/yourcompany"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"linkedin",children:"LinkedIn"}),r.jsx(c.I,{id:"linkedin",value:a.socialLinks.linkedin,onChange:e=>Z("linkedin",e.target.value),placeholder:"https://linkedin.com/company/yourcompany"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"facebook",children:"Facebook"}),r.jsx(c.I,{id:"facebook",value:a.socialLinks.facebook,onChange:e=>Z("facebook",e.target.value),placeholder:"https://facebook.com/yourcompany"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"instagram",children:"Instagram"}),r.jsx(c.I,{id:"instagram",value:a.socialLinks.instagram,onChange:e=>Z("instagram",e.target.value),placeholder:"https://instagram.com/yourcompany"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"youtube",children:"YouTube"}),r.jsx(c.I,{id:"youtube",value:a.socialLinks.youtube,onChange:e=>Z("youtube",e.target.value),placeholder:"https://youtube.com/c/yourcompany"})]})]})]})]})]})})]})]})}},92549:(e,s,a)=>{"use strict";a.d(s,{I:()=>t});var r=a(95344),o=a(3729),l=a(91626);let t=o.forwardRef(({className:e,type:s,...a},o)=>r.jsx("input",{type:s,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:o,...a}));t.displayName="Input"},1586:(e,s,a)=>{"use strict";a.d(s,{_:()=>c});var r=a(95344),o=a(3729),l=a(14217),t=a(49247),n=a(91626);let i=(0,t.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=o.forwardRef(({className:e,...s},a)=>r.jsx(l.f,{ref:a,className:(0,n.cn)(i(),e),...s}));c.displayName=l.f.displayName},25757:(e,s,a)=>{"use strict";a.d(s,{mQ:()=>_,nU:()=>I,dr:()=>T,SP:()=>z});var r=a(95344),o=a(3729),l=a(85222),t=a(98462),n=a(34504),i=a(43234),c=a(62409),d=a(3975),x=a(33183),m=a(99048),u="Tabs",[h,p]=(0,t.b)(u,[n.Pc]),g=(0,n.Pc)(),[y,j]=h(u),f=o.forwardRef((e,s)=>{let{__scopeTabs:a,value:o,onValueChange:l,defaultValue:t,orientation:n="horizontal",dir:i,activationMode:h="automatic",...p}=e,g=(0,d.gm)(i),[j,f]=(0,x.T)({prop:o,onChange:l,defaultProp:t??"",caller:u});return(0,r.jsx)(y,{scope:a,baseId:(0,m.M)(),value:j,onValueChange:f,orientation:n,dir:g,activationMode:h,children:(0,r.jsx)(c.WV.div,{dir:g,"data-orientation":n,...p,ref:s})})});f.displayName=u;var v="TabsList",b=o.forwardRef((e,s)=>{let{__scopeTabs:a,loop:o=!0,...l}=e,t=j(v,a),i=g(a);return(0,r.jsx)(n.fC,{asChild:!0,...i,orientation:t.orientation,dir:t.dir,loop:o,children:(0,r.jsx)(c.WV.div,{role:"tablist","aria-orientation":t.orientation,...l,ref:s})})});b.displayName=v;var N="TabsTrigger",C=o.forwardRef((e,s)=>{let{__scopeTabs:a,value:o,disabled:t=!1,...i}=e,d=j(N,a),x=g(a),m=S(d.baseId,o),u=F(d.baseId,o),h=o===d.value;return(0,r.jsx)(n.ck,{asChild:!0,...x,focusable:!t,active:h,children:(0,r.jsx)(c.WV.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":u,"data-state":h?"active":"inactive","data-disabled":t?"":void 0,disabled:t,id:m,...i,ref:s,onMouseDown:(0,l.M)(e.onMouseDown,e=>{t||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(o)}),onKeyDown:(0,l.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(o)}),onFocus:(0,l.M)(e.onFocus,()=>{let e="manual"!==d.activationMode;h||t||!e||d.onValueChange(o)})})})});C.displayName=N;var k="TabsContent",w=o.forwardRef((e,s)=>{let{__scopeTabs:a,value:l,forceMount:t,children:n,...d}=e,x=j(k,a),m=S(x.baseId,l),u=F(x.baseId,l),h=l===x.value,p=o.useRef(h);return o.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,r.jsx)(i.z,{present:t||h,children:({present:a})=>(0,r.jsx)(c.WV.div,{"data-state":h?"active":"inactive","data-orientation":x.orientation,role:"tabpanel","aria-labelledby":m,hidden:!a,id:u,tabIndex:0,...d,ref:s,style:{...e.style,animationDuration:p.current?"0s":void 0},children:a&&n})})});function S(e,s){return`${e}-trigger-${s}`}function F(e,s){return`${e}-content-${s}`}w.displayName=k;var P=a(91626);let _=f,T=o.forwardRef(({className:e,...s},a)=>r.jsx(b,{ref:a,className:(0,P.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s}));T.displayName=b.displayName;let z=o.forwardRef(({className:e,...s},a)=>r.jsx(C,{ref:a,className:(0,P.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s}));z.displayName=C.displayName;let I=o.forwardRef(({className:e,...s},a)=>r.jsx(w,{ref:a,className:(0,P.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));I.displayName=w.displayName},93601:(e,s,a)=>{"use strict";a.d(s,{g:()=>t});var r=a(95344),o=a(3729),l=a(91626);let t=o.forwardRef(({className:e,...s},a)=>r.jsx("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...s}));t.displayName="Textarea"},53148:(e,s,a)=>{"use strict";a.d(s,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(69224).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},31498:(e,s,a)=>{"use strict";a.d(s,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(69224).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},3380:(e,s,a)=>{"use strict";a.d(s,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(69224).Z)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},92566:(e,s,a)=>{"use strict";a.r(s),a.d(s,{$$typeof:()=>l,__esModule:()=>o,default:()=>t});let r=(0,a(86843).createProxy)(String.raw`C:\proj\nextjs-saas\app\super-admin\branding\page.tsx`),{__esModule:o,$$typeof:l}=r,t=r.default},14217:(e,s,a)=>{"use strict";a.d(s,{f:()=>n});var r=a(3729),o=a(62409),l=a(95344),t=r.forwardRef((e,s)=>(0,l.jsx)(o.WV.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));t.displayName="Label";var n=t}};var s=require("../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),r=s.X(0,[1638,7948,6671,4626,7792,2506,1729,2125,3965],()=>a(56320));module.exports=r})();