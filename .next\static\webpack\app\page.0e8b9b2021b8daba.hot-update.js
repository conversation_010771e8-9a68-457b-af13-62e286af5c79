"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/landing/landing-page-content.tsx":
/*!*****************************************************!*\
  !*** ./components/landing/landing-page-content.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LandingPageContent: function() { return /* binding */ LandingPageContent; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Quote_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Quote,Shield,Star,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Quote_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Quote,Shield,Star,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Quote_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Quote,Shield,Star,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Quote_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Quote,Shield,Star,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Quote_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Quote,Shield,Star,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Quote_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Quote,Shield,Star,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Quote_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Quote,Shield,Star,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Quote_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Quote,Shield,Star,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Quote_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Quote,Shield,Star,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Quote_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Quote,Shield,Star,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Quote_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Quote,Shield,Star,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/quote.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Quote_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Quote,Shield,Star,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Quote_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Quote,Shield,Star,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_6__);\n/* __next_internal_client_entry_do_not_use__ LandingPageContent auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Default fallback content\nconst defaultContent = {\n    hero: {\n        enabled: true,\n        title: \"Build Your SaaS Business\",\n        subtitle: \"The Complete Platform\",\n        description: \"Everything you need to launch, grow, and scale your SaaS business. From customer management to billing, we've got you covered.\",\n        primaryCTA: {\n            text: \"Start Free Trial\",\n            link: \"/auth/signup\"\n        },\n        secondaryCTA: {\n            text: \"Watch Demo\",\n            link: \"/demo\"\n        },\n        backgroundImage: \"\",\n        backgroundVideo: \"\"\n    },\n    features: {\n        enabled: true,\n        title: \"Everything You Need\",\n        subtitle: \"Powerful Features\",\n        items: [\n            {\n                id: \"1\",\n                title: \"Customer Management\",\n                description: \"Manage your customers, track interactions, and build lasting relationships.\",\n                icon: \"users\",\n                image: \"\"\n            },\n            {\n                id: \"2\",\n                title: \"Subscription Billing\",\n                description: \"Automated billing, invoicing, and payment processing for recurring revenue.\",\n                icon: \"credit-card\",\n                image: \"\"\n            },\n            {\n                id: \"3\",\n                title: \"Analytics & Reports\",\n                description: \"Comprehensive analytics to track your business performance and growth.\",\n                icon: \"bar-chart\",\n                image: \"\"\n            },\n            {\n                id: \"4\",\n                title: \"Multi-Tenant Architecture\",\n                description: \"Secure data isolation with company-based access control and team management.\",\n                icon: \"building\",\n                image: \"\"\n            },\n            {\n                id: \"5\",\n                title: \"Enterprise Security\",\n                description: \"Role-based access control with audit logs and data encryption.\",\n                icon: \"shield\",\n                image: \"\"\n            },\n            {\n                id: \"6\",\n                title: \"Global Ready\",\n                description: \"Multi-currency support and localization for worldwide businesses.\",\n                icon: \"globe\",\n                image: \"\"\n            }\n        ]\n    },\n    pricing: {\n        enabled: true,\n        title: \"Simple, Transparent Pricing\",\n        subtitle: \"Choose the plan that fits your needs\",\n        showPricingTable: true,\n        customMessage: \"\"\n    },\n    testimonials: {\n        enabled: true,\n        title: \"What Our Customers Say\",\n        subtitle: \"Trusted by thousands of businesses\",\n        items: [\n            {\n                id: \"1\",\n                name: \"John Smith\",\n                role: \"CEO\",\n                company: \"TechCorp\",\n                content: \"This platform has transformed how we manage our SaaS business. The automation features alone have saved us countless hours.\",\n                avatar: \"\",\n                rating: 5\n            },\n            {\n                id: \"2\",\n                name: \"Sarah Johnson\",\n                role: \"Founder\",\n                company: \"StartupXYZ\",\n                content: \"The best investment we've made for our business. The customer management features are incredibly powerful.\",\n                avatar: \"\",\n                rating: 5\n            },\n            {\n                id: \"3\",\n                name: \"Mike Chen\",\n                role: \"CTO\",\n                company: \"InnovateLab\",\n                content: \"Excellent platform with great support. The analytics help us make data-driven decisions every day.\",\n                avatar: \"\",\n                rating: 5\n            }\n        ]\n    },\n    faq: {\n        enabled: true,\n        title: \"Frequently Asked Questions\",\n        subtitle: \"Everything you need to know\",\n        items: [\n            {\n                id: \"1\",\n                question: \"How do I get started?\",\n                answer: \"Simply sign up for a free trial and follow our onboarding guide to set up your account. Our team is here to help you every step of the way.\"\n            },\n            {\n                id: \"2\",\n                question: \"Can I cancel anytime?\",\n                answer: \"Yes, you can cancel your subscription at any time. No long-term contracts or cancellation fees. Your data will remain accessible during the notice period.\"\n            },\n            {\n                id: \"3\",\n                question: \"Is my data secure?\",\n                answer: \"Absolutely. We use enterprise-grade security measures including encryption, regular backups, and compliance with industry standards like SOC 2 and GDPR.\"\n            },\n            {\n                id: \"4\",\n                question: \"Do you offer customer support?\",\n                answer: \"Yes, we provide 24/7 customer support via email, chat, and phone. Our premium plans also include dedicated account managers.\"\n            },\n            {\n                id: \"5\",\n                question: \"Can I integrate with other tools?\",\n                answer: \"Yes, we offer integrations with popular tools like Slack, Zapier, QuickBooks, and many more. We also provide a robust API for custom integrations.\"\n            }\n        ]\n    },\n    cta: {\n        enabled: true,\n        title: \"Ready to Get Started?\",\n        description: \"Join thousands of businesses already using our platform to grow their SaaS.\",\n        buttonText: \"Start Your Free Trial\",\n        buttonLink: \"/auth/signup\",\n        backgroundImage: \"\"\n    },\n    footer: {\n        enabled: true,\n        companyDescription: \"The complete SaaS platform for modern businesses.\",\n        links: [\n            {\n                id: \"1\",\n                title: \"Product\",\n                items: [\n                    {\n                        id: \"1\",\n                        text: \"Features\",\n                        link: \"/features\"\n                    },\n                    {\n                        id: \"2\",\n                        text: \"Pricing\",\n                        link: \"/pricing\"\n                    },\n                    {\n                        id: \"3\",\n                        text: \"Security\",\n                        link: \"/security\"\n                    },\n                    {\n                        id: \"4\",\n                        text: \"Integrations\",\n                        link: \"/integrations\"\n                    }\n                ]\n            },\n            {\n                id: \"2\",\n                title: \"Company\",\n                items: [\n                    {\n                        id: \"1\",\n                        text: \"About\",\n                        link: \"/about\"\n                    },\n                    {\n                        id: \"2\",\n                        text: \"Blog\",\n                        link: \"/blog\"\n                    },\n                    {\n                        id: \"3\",\n                        text: \"Careers\",\n                        link: \"/careers\"\n                    },\n                    {\n                        id: \"4\",\n                        text: \"Contact\",\n                        link: \"/contact\"\n                    }\n                ]\n            },\n            {\n                id: \"3\",\n                title: \"Support\",\n                items: [\n                    {\n                        id: \"1\",\n                        text: \"Help Center\",\n                        link: \"/help\"\n                    },\n                    {\n                        id: \"2\",\n                        text: \"Documentation\",\n                        link: \"/docs\"\n                    },\n                    {\n                        id: \"3\",\n                        text: \"API Reference\",\n                        link: \"/api\"\n                    },\n                    {\n                        id: \"4\",\n                        text: \"Status\",\n                        link: \"/status\"\n                    }\n                ]\n            }\n        ],\n        socialLinks: {\n            twitter: \"https://twitter.com/yourcompany\",\n            linkedin: \"https://linkedin.com/company/yourcompany\",\n            facebook: \"https://facebook.com/yourcompany\",\n            instagram: \"https://instagram.com/yourcompany\"\n        },\n        copyrightText: \"\\xa9 2024 Your Company. All rights reserved.\"\n    },\n    seo: {\n        title: \"SaaS Platform - Build Your Business\",\n        description: \"The complete SaaS platform for modern businesses. Customer management, billing, analytics, and more.\",\n        keywords: \"saas, platform, business, customer management, billing, analytics\",\n        ogImage: \"\"\n    }\n};\nconst getIconComponent = (iconName)=>{\n    const icons = {\n        users: _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Quote_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        \"credit-card\": _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Quote_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        \"bar-chart\": _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Quote_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        building: _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Quote_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        shield: _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Quote_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        globe: _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Quote_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        zap: _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Quote_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        \"file-text\": _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Quote_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n    };\n    return icons[iconName] || _barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Quote_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n};\nconst formatStorage = (bytes)=>{\n    const gb = bytes / (1024 * 1024 * 1024);\n    return gb >= 1 ? \"\".concat(gb, \"GB\") : \"\".concat(Math.round(gb * 1024), \"MB\");\n};\nconst getFeatureList = (plan)=>{\n    const features = [];\n    // Add usage limits\n    features.push(\"Up to \".concat(plan.maxUsers, \" users\"));\n    features.push(\"\".concat(plan.maxCompanies, \" \").concat(plan.maxCompanies === 1 ? \"company\" : \"companies\"));\n    features.push(\"\".concat(plan.maxCustomers, \" customers\"));\n    features.push(\"\".concat(plan.maxQuotations, \" quotations/month\"));\n    features.push(\"\".concat(plan.maxInvoices, \" invoices/month\"));\n    features.push(\"\".concat(formatStorage(plan.maxStorage), \" storage\"));\n    // Add feature flags\n    if (plan.features.basicReporting) features.push(\"Basic reporting\");\n    if (plan.features.emailSupport) features.push(\"Email support\");\n    if (plan.features.mobileApp) features.push(\"Mobile app access\");\n    if (plan.features.advancedAnalytics) features.push(\"Advanced analytics\");\n    if (plan.features.customBranding) features.push(\"Custom branding\");\n    if (plan.features.apiAccess) features.push(\"API access\");\n    if (plan.features.prioritySupport) features.push(\"Priority support\");\n    if (plan.features.customIntegrations) features.push(\"Custom integrations\");\n    if (plan.features.advancedSecurity) features.push(\"Advanced security\");\n    if (plan.features.dedicatedManager) features.push(\"Dedicated account manager\");\n    return features;\n};\nfunction LandingPageContent() {\n    var _content_hero, _content_features, _content_testimonials, _content_faq, _content_cta, _content_footer;\n    _s();\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultContent);\n    const [plans, setPlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [openFAQ, setOpenFAQ] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isYearly, setIsYearly] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchData = async ()=>{\n            try {\n                // Fetch CMS content\n                const cmsResponse = await fetch(\"/api/super-admin/cms\");\n                const cmsData = await cmsResponse.json();\n                if (cmsData.success && cmsData.content) {\n                    // Merge with default content to ensure all sections exist\n                    setContent({\n                        ...defaultContent,\n                        ...cmsData.content\n                    });\n                }\n                // Fetch pricing plans\n                const plansResponse = await fetch(\"/api/pricing-plans?publicOnly=true\");\n                const plansData = await plansResponse.json();\n                if (plansData.success) {\n                    // Sort plans by sortOrder and filter active public plans\n                    const activePlans = plansData.data.filter((plan)=>plan.isActive && plan.isPublic).sort((a, b)=>a.sortOrder - b.sortOrder);\n                    setPlans(activePlans);\n                }\n            } catch (error) {\n                console.error(\"Error fetching data:\", error);\n            // Use default content on error\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchData();\n    }, []);\n    const getPrice = (plan)=>{\n        if (isYearly && plan.yearlyPrice) {\n            return plan.yearlyPrice / 12 // Show monthly equivalent\n            ;\n        }\n        return plan.monthlyPrice;\n    };\n    const getYearlyDiscount = (plan)=>{\n        if (!plan.yearlyPrice || !plan.monthlyPrice) return 0;\n        const yearlyMonthly = plan.yearlyPrice / 12;\n        const discount = (plan.monthlyPrice - yearlyMonthly) / plan.monthlyPrice * 100;\n        return Math.round(discount);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 469,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n            lineNumber: 468,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-4 flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Quote_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-8 w-8 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 480,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"SaaS Platform\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 479,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                    href: \"/pricing\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        children: \"Pricing\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                    href: \"/auth/signin\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        children: \"Sign In\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 488,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 487,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                    href: \"/auth/signup\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        children: \"Get Started\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 490,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 483,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 478,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 477,\n                columnNumber: 7\n            }, this),\n            ((_content_hero = content.hero) === null || _content_hero === void 0 ? void 0 : _content_hero.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4 relative overflow-hidden\",\n                children: [\n                    content.hero.backgroundImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 z-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_6___default()), {\n                            src: content.hero.backgroundImage,\n                            alt: \"Hero Background\",\n                            fill: true,\n                            className: \"object-cover opacity-20\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 502,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                        lineNumber: 501,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto text-center relative z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto\",\n                            children: [\n                                content.hero.subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                    className: \"mb-4 text-sm px-4 py-2\",\n                                    children: content.hero.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 513,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-5xl md:text-6xl font-bold text-gray-900 mb-6\",\n                                    children: content.hero.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 517,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n                                    children: content.hero.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-4 justify-center mb-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: content.hero.primaryCTA.link,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                size: \"lg\",\n                                                className: \"text-lg px-8 py-3\",\n                                                children: [\n                                                    content.hero.primaryCTA.text,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Quote_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"ml-2 h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 525,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 524,\n                                            columnNumber: 17\n                                        }, this),\n                                        content.hero.secondaryCTA.text && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: content.hero.secondaryCTA.link,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                size: \"lg\",\n                                                variant: \"outline\",\n                                                className: \"text-lg px-8 py-3\",\n                                                children: content.hero.secondaryCTA.text\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 532,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 531,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 523,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 511,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                        lineNumber: 510,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 499,\n                columnNumber: 9\n            }, this),\n            ((_content_features = content.features) === null || _content_features === void 0 ? void 0 : _content_features.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                    children: content.features.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 548,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                                    children: content.features.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 551,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 547,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: content.features.items.map((feature)=>{\n                                const IconComponent = getIconComponent(feature.icon);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"border-0 shadow-lg hover:shadow-xl transition-shadow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                        className: \"h-6 w-6 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 563,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 562,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-xl\",\n                                                    children: feature.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 565,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 561,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                className: \"text-gray-600\",\n                                                children: feature.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 568,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 567,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, feature.id, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 560,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 556,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 546,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 545,\n                columnNumber: 9\n            }, this),\n            ((_content_testimonials = content.testimonials) === null || _content_testimonials === void 0 ? void 0 : _content_testimonials.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                    children: content.testimonials.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 585,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                                    children: content.testimonials.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 588,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 584,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: content.testimonials.items.map((testimonial)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"border-0 shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    ...Array(testimonial.rating)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Quote_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-5 w-5 text-yellow-400 fill-current\"\n                                                    }, i, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 599,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 597,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Quote_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-8 w-8 text-gray-300 mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 602,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-6 italic\",\n                                                children: [\n                                                    '\"',\n                                                    testimonial.content,\n                                                    '\"'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 603,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    testimonial.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                                        src: testimonial.avatar,\n                                                        alt: testimonial.name,\n                                                        width: 48,\n                                                        height: 48,\n                                                        className: \"rounded-full mr-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 608,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-gray-200 rounded-full mr-4 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Quote_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-6 w-6 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 617,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 616,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-semibold text-gray-900\",\n                                                                children: testimonial.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                lineNumber: 621,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: [\n                                                                    testimonial.role,\n                                                                    \", \",\n                                                                    testimonial.company\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                lineNumber: 622,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 620,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 606,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 596,\n                                        columnNumber: 19\n                                    }, this)\n                                }, testimonial.id, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 595,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 593,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 583,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 582,\n                columnNumber: 9\n            }, this),\n            ((_content_faq = content.faq) === null || _content_faq === void 0 ? void 0 : _content_faq.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto max-w-4xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                    children: content.faq.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 638,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600\",\n                                    children: content.faq.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 641,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 637,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: content.faq.items.map((faq)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"border shadow-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"p-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"w-full p-6 text-left flex items-center justify-between hover:bg-gray-50\",\n                                                onClick: ()=>setOpenFAQ(openFAQ === faq.id ? null : faq.id),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold text-gray-900\",\n                                                        children: faq.question\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 654,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    openFAQ === faq.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Quote_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 656,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Quote_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 658,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 650,\n                                                columnNumber: 21\n                                            }, this),\n                                            openFAQ === faq.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-6 pb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: faq.answer\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 663,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 662,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 649,\n                                        columnNumber: 19\n                                    }, this)\n                                }, faq.id, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 648,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 646,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 636,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 635,\n                columnNumber: 9\n            }, this),\n            ((_content_cta = content.cta) === null || _content_cta === void 0 ? void 0 : _content_cta.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4 bg-blue-600 relative overflow-hidden\",\n                children: [\n                    content.cta.backgroundImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 z-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_6___default()), {\n                            src: content.cta.backgroundImage,\n                            alt: \"CTA Background\",\n                            fill: true,\n                            className: \"object-cover opacity-20\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 679,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                        lineNumber: 678,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto text-center relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl font-bold text-white mb-6\",\n                                children: content.cta.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                lineNumber: 688,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-blue-100 mb-8 max-w-2xl mx-auto\",\n                                children: content.cta.description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                lineNumber: 691,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                href: content.cta.buttonLink,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    size: \"lg\",\n                                    variant: \"secondary\",\n                                    className: \"text-lg px-8 py-3\",\n                                    children: [\n                                        content.cta.buttonText,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Quote_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"ml-2 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 697,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 695,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                lineNumber: 694,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                        lineNumber: 687,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 676,\n                columnNumber: 9\n            }, this),\n            ((_content_footer = content.footer) === null || _content_footer === void 0 ? void 0 : _content_footer.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-gray-900 text-white py-12 px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-4 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Quote_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-6 w-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 711,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-lg font-bold\",\n                                                    children: \"SaaS Platform\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 712,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 710,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400\",\n                                            children: content.footer.companyDescription\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 714,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 709,\n                                    columnNumber: 15\n                                }, this),\n                                content.footer.links.map((linkGroup)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-4\",\n                                                children: linkGroup.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 720,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-2 text-gray-400\",\n                                                children: linkGroup.items.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                            href: link.link,\n                                                            className: \"hover:text-white\",\n                                                            children: link.text\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 724,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, link.id, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 723,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 721,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, linkGroup.id, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 719,\n                                        columnNumber: 17\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 708,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: content.footer.copyrightText\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                lineNumber: 734,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 733,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 707,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 706,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n        lineNumber: 475,\n        columnNumber: 5\n    }, this);\n}\n_s(LandingPageContent, \"Ob77mu/u4HMnkCjOlnHJhwEopZQ=\");\n_c = LandingPageContent;\nvar _c;\n$RefreshReg$(_c, \"LandingPageContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/landing/landing-page-content.tsx\n"));

/***/ })

});