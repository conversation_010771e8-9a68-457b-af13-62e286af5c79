import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const sendQuotationSchema = z.object({
  to: z.string().email('Invalid email address'),
  cc: z.array(z.string().email()).optional().default([]),
  subject: z.string().min(1, 'Subject is required'),
  message: z.string().min(1, 'Message is required'),
  includePDF: z.boolean().default(true)
})

// POST /api/quotations/[id]/send - Send quotation via email
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = sendQuotationSchema.parse(body)

    // Fetch quotation with all related data
    const quotation = await prisma.quotation.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId
      },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
            company: true
          }
        },
        items: {
          orderBy: { createdAt: 'asc' }
        },
        createdBy: {
          select: {
            name: true,
            email: true
          }
        },
        companyRef: {
          select: {
            name: true,
            email: true,
            phone: true,
            website: true
          }
        }
      }
    })

    if (!quotation) {
      return NextResponse.json({ error: 'Quotation not found' }, { status: 404 })
    }

    // Calculate totals
    const itemTotals = quotation.items.map(item => {
      const lineTotal = Number(item.quantity) * Number(item.unitPrice)
      const discountAmount = (lineTotal * Number(item.discount)) / 100
      const subtotal = lineTotal - discountAmount
      const taxAmount = (subtotal * Number(item.taxRate)) / 100
      const total = subtotal + taxAmount
      return { ...item, total }
    })

    const subtotal = itemTotals.reduce((sum, item) => sum + (Number(item.quantity) * Number(item.unitPrice)), 0)
    const quotationDiscountAmount = quotation.discountType === 'PERCENTAGE'
      ? (subtotal * Number(quotation.discountValue)) / 100
      : Number(quotation.discountValue)
    const finalSubtotal = subtotal - quotationDiscountAmount
    const finalTax = (finalSubtotal * Number(quotation.taxRate)) / 100
    const finalTotal = finalSubtotal + finalTax

    // Generate email content
    const emailHTML = generateQuotationEmailHTML({
      quotation,
      customer: quotation.customer,
      company: quotation.companyRef,
      items: itemTotals,
      total: finalTotal,
      message: validatedData.message
    })

    // In a real application, you would integrate with an email service like SendGrid, Mailgun, etc.
    // For now, we'll simulate sending and log the email content
    console.log('Email would be sent with the following details:')
    console.log('To:', validatedData.to)
    console.log('CC:', validatedData.cc)
    console.log('Subject:', validatedData.subject)
    console.log('HTML Content:', emailHTML)

    // Update quotation status to SENT if it was DRAFT
    if (quotation.status === 'DRAFT') {
      await prisma.quotation.update({
        where: { id: params.id },
        data: { 
          status: 'SENT',
          sentAt: new Date()
        }
      })
    }

    // Log activity
    await prisma.activity.create({
      data: {
        type: 'EMAIL',
        title: 'Quotation Sent',
        description: `Quotation ${quotation.quotationNumber} was sent to ${validatedData.to}`,
        quotationId: quotation.id,
        customerId: quotation.customerId,
        companyId: session.user.companyId,
        createdById: session.user.id
      }
    })

    // In development, return the email content for preview
    if (process.env.NODE_ENV === 'development') {
      return NextResponse.json({
        success: true,
        message: 'Quotation sent successfully (development mode)',
        emailPreview: {
          to: validatedData.to,
          cc: validatedData.cc,
          subject: validatedData.subject,
          html: emailHTML
        }
      })
    }

    return NextResponse.json({
      success: true,
      message: 'Quotation sent successfully'
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error sending quotation:', error)
    return NextResponse.json(
      { error: 'Failed to send quotation' },
      { status: 500 }
    )
  }
}

function generateQuotationEmailHTML(data: any) {
  const { quotation, customer, company, items, total, message } = data

  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quotation ${quotation.quotationNumber}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
            padding: 0;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: bold;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .content {
            padding: 30px;
        }
        .message-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid #667eea;
        }
        .quotation-details {
            background-color: #fff;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #f1f3f4;
        }
        .detail-row:last-child {
            border-bottom: none;
        }
        .detail-label {
            font-weight: 600;
            color: #495057;
        }
        .detail-value {
            color: #212529;
        }
        .items-section {
            margin-bottom: 30px;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .items-table th {
            background-color: #667eea;
            color: white;
            padding: 15px 10px;
            text-align: left;
            font-weight: 600;
        }
        .items-table td {
            padding: 12px 10px;
            border-bottom: 1px solid #e9ecef;
        }
        .items-table tr:last-child td {
            border-bottom: none;
        }
        .total-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 30px;
        }
        .total-amount {
            font-size: 32px;
            font-weight: bold;
            margin: 10px 0;
        }
        .cta-section {
            text-align: center;
            margin: 30px 0;
        }
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            font-size: 16px;
            transition: transform 0.2s;
        }
        .cta-button:hover {
            transform: translateY(-2px);
        }
        .footer {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #6c757d;
            font-size: 14px;
        }
        .company-info {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }
        @media (max-width: 600px) {
            .container {
                margin: 0;
                width: 100%;
            }
            .content {
                padding: 20px;
            }
            .items-table {
                font-size: 14px;
            }
            .items-table th,
            .items-table td {
                padding: 8px 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Quotation ${quotation.quotationNumber}</h1>
            <p>From ${company.name}</p>
        </div>
        
        <div class="content">
            <div class="message-section">
                <p>${message}</p>
            </div>
            
            <div class="quotation-details">
                <h3 style="margin-top: 0; color: #495057;">Quotation Details</h3>
                <div class="detail-row">
                    <span class="detail-label">Quotation Number:</span>
                    <span class="detail-value">${quotation.quotationNumber}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Date:</span>
                    <span class="detail-value">${new Date(quotation.createdAt).toLocaleDateString()}</span>
                </div>
                ${quotation.validUntil ? `
                <div class="detail-row">
                    <span class="detail-label">Valid Until:</span>
                    <span class="detail-value">${new Date(quotation.validUntil).toLocaleDateString()}</span>
                </div>
                ` : ''}
                <div class="detail-row">
                    <span class="detail-label">Customer:</span>
                    <span class="detail-value">${customer.name}${customer.company ? ` (${customer.company})` : ''}</span>
                </div>
            </div>
            
            <div class="items-section">
                <h3 style="color: #495057;">Items</h3>
                <table class="items-table">
                    <thead>
                        <tr>
                            <th>Description</th>
                            <th style="text-align: center;">Qty</th>
                            <th style="text-align: right;">Unit Price</th>
                            <th style="text-align: right;">Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${items.map(item => `
                            <tr>
                                <td><strong>${item.description}</strong></td>
                                <td style="text-align: center;">${Number(item.quantity)}</td>
                                <td style="text-align: right;">$${Number(item.unitPrice).toFixed(2)}</td>
                                <td style="text-align: right;">$${item.total.toFixed(2)}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
            
            <div class="total-section">
                <h3 style="margin: 0;">Total Amount</h3>
                <div class="total-amount">$${total.toFixed(2)}</div>
                <p style="margin: 0; opacity: 0.9;">All prices are in USD</p>
            </div>
            
            <div class="cta-section">
                <a href="${process.env.NEXTAUTH_URL}/quotations/${quotation.id}/view" class="cta-button">
                    View Full Quotation
                </a>
            </div>
            
            ${quotation.terms ? `
            <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
                <h4 style="margin-top: 0; color: #856404;">Terms & Conditions</h4>
                <p style="margin-bottom: 0; color: #856404;">${quotation.terms}</p>
            </div>
            ` : ''}
            
            ${quotation.notes ? `
            <div style="background-color: #d1ecf1; border: 1px solid #bee5eb; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
                <h4 style="margin-top: 0; color: #0c5460;">Additional Notes</h4>
                <p style="margin-bottom: 0; color: #0c5460;">${quotation.notes}</p>
            </div>
            ` : ''}
        </div>
        
        <div class="footer">
            <div class="company-info">
                <strong>${company.name}</strong><br>
                ${company.email ? `Email: ${company.email}<br>` : ''}
                ${company.phone ? `Phone: ${company.phone}<br>` : ''}
                ${company.website ? `Website: ${company.website}` : ''}
            </div>
            <p style="margin-top: 20px;">
                Thank you for your business! If you have any questions about this quotation, 
                please don't hesitate to contact us.
            </p>
        </div>
    </div>
</body>
</html>
  `
}
