"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4997],{83523:function(e,t,r){r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(62898).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},9224:function(e,t,r){r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(62898).Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},99530:function(e,t,r){r.d(t,{VY:function(){return e_},ZA:function(){return eW},JO:function(){return eN},ck:function(){return eA},wU:function(){return eO},eT:function(){return eB},__:function(){return eH},h_:function(){return eV},fC:function(){return eI},$G:function(){return eF},u_:function(){return eK},Z0:function(){return eU},xz:function(){return eD},B4:function(){return eP},l_:function(){return eL}});var n=r(2265),o=r(54887);function l(e,[t,r]){return Math.min(r,Math.max(t,e))}var a=r(85744),i=r(27733),s=r(42210),u=r(56989),c=r(65400),d=r(79249),p=r(31244),f=r(52759),h=r(20966),v=r(83995),m=r(52730),w=r(9381),g=r(67256),x=r(16459),y=r(73763),b=r(51030),S=r(85184),C=r(57437),M=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});n.forwardRef((e,t)=>(0,C.jsx)(w.WV.span,{...e,ref:t,style:{...M,...e.style}})).displayName="VisuallyHidden";var j=r(85859),T=r(73386),k=[" ","Enter","ArrowUp","ArrowDown"],R=[" ","Enter"],E="Select",[I,D,P]=(0,i.B)(E),[N,V]=(0,u.b)(E,[P,v.D7]),_=(0,v.D7)(),[L,W]=N(E),[H,A]=N(E),B=e=>{let{__scopeSelect:t,children:r,open:o,defaultOpen:l,onOpenChange:a,value:i,defaultValue:s,onValueChange:u,dir:d,name:p,autoComplete:f,disabled:m,required:w,form:g}=e,x=_(t),[b,S]=n.useState(null),[M,j]=n.useState(null),[T,k]=n.useState(!1),R=(0,c.gm)(d),[D,P]=(0,y.T)({prop:o,defaultProp:l??!1,onChange:a,caller:E}),[N,V]=(0,y.T)({prop:i,defaultProp:s,onChange:u,caller:E}),W=n.useRef(null),A=!b||g||!!b.closest("form"),[B,O]=n.useState(new Set),K=Array.from(B).map(e=>e.props.value).join(";");return(0,C.jsx)(v.fC,{...x,children:(0,C.jsxs)(L,{required:w,scope:t,trigger:b,onTriggerChange:S,valueNode:M,onValueNodeChange:j,valueNodeHasChildren:T,onValueNodeHasChildrenChange:k,contentId:(0,h.M)(),value:N,onValueChange:V,open:D,onOpenChange:P,dir:R,triggerPointerDownPosRef:W,disabled:m,children:[(0,C.jsx)(I.Provider,{scope:t,children:(0,C.jsx)(H,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{O(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{O(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),A?(0,C.jsxs)(eT,{"aria-hidden":!0,required:w,tabIndex:-1,name:p,autoComplete:f,value:N,onChange:e=>V(e.target.value),disabled:m,form:g,children:[void 0===N?(0,C.jsx)("option",{value:""}):null,Array.from(B)]},K):null]})})};B.displayName=E;var O="SelectTrigger",K=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:o=!1,...l}=e,i=_(r),u=W(O,r),c=u.disabled||o,d=(0,s.e)(t,u.onTriggerChange),p=D(r),f=n.useRef("touch"),[h,m,g]=eR(e=>{let t=p().filter(e=>!e.disabled),r=t.find(e=>e.value===u.value),n=eE(t,e,r);void 0!==n&&u.onValueChange(n.value)}),x=e=>{c||(u.onOpenChange(!0),g()),e&&(u.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,C.jsx)(v.ee,{asChild:!0,...i,children:(0,C.jsx)(w.WV.button,{type:"button",role:"combobox","aria-controls":u.contentId,"aria-expanded":u.open,"aria-required":u.required,"aria-autocomplete":"none",dir:u.dir,"data-state":u.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":ek(u.value)?"":void 0,...l,ref:d,onClick:(0,a.M)(l.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&x(e)}),onPointerDown:(0,a.M)(l.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(x(e),e.preventDefault())}),onKeyDown:(0,a.M)(l.onKeyDown,e=>{let t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&k.includes(e.key)&&(x(),e.preventDefault())})})})});K.displayName=O;var F="SelectValue",U=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:o,children:l,placeholder:a="",...i}=e,u=W(F,r),{onValueNodeHasChildrenChange:c}=u,d=void 0!==l,p=(0,s.e)(t,u.onValueNodeChange);return(0,b.b)(()=>{c(d)},[c,d]),(0,C.jsx)(w.WV.span,{...i,ref:p,style:{pointerEvents:"none"},children:ek(u.value)?(0,C.jsx)(C.Fragment,{children:a}):l})});U.displayName=F;var z=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...o}=e;return(0,C.jsx)(w.WV.span,{"aria-hidden":!0,...o,ref:t,children:n||"▼"})});z.displayName="SelectIcon";var Z=e=>(0,C.jsx)(m.h,{asChild:!0,...e});Z.displayName="SelectPortal";var q="SelectContent",Y=n.forwardRef((e,t)=>{let r=W(q,e.__scopeSelect),[l,a]=n.useState();return((0,b.b)(()=>{a(new DocumentFragment)},[]),r.open)?(0,C.jsx)($,{...e,ref:t}):l?o.createPortal((0,C.jsx)(X,{scope:e.__scopeSelect,children:(0,C.jsx)(I.Slot,{scope:e.__scopeSelect,children:(0,C.jsx)("div",{children:e.children})})}),l):null});Y.displayName=q;var[X,G]=N(q),J=(0,g.Z8)("SelectContent.RemoveScroll"),$=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:o="item-aligned",onCloseAutoFocus:l,onEscapeKeyDown:i,onPointerDownOutside:u,side:c,sideOffset:h,align:v,alignOffset:m,arrowPadding:w,collisionBoundary:g,collisionPadding:x,sticky:y,hideWhenDetached:b,avoidCollisions:S,...M}=e,k=W(q,r),[R,E]=n.useState(null),[I,P]=n.useState(null),N=(0,s.e)(t,e=>E(e)),[V,_]=n.useState(null),[L,H]=n.useState(null),A=D(r),[B,O]=n.useState(!1),K=n.useRef(!1);n.useEffect(()=>{if(R)return(0,j.Ry)(R)},[R]),(0,p.EW)();let F=n.useCallback(e=>{let[t,...r]=A().map(e=>e.ref.current),[n]=r.slice(-1),o=document.activeElement;for(let r of e)if(r===o||(r?.scrollIntoView({block:"nearest"}),r===t&&I&&(I.scrollTop=0),r===n&&I&&(I.scrollTop=I.scrollHeight),r?.focus(),document.activeElement!==o))return},[A,I]),U=n.useCallback(()=>F([V,R]),[F,V,R]);n.useEffect(()=>{B&&U()},[B,U]);let{onOpenChange:z,triggerPointerDownPosRef:Z}=k;n.useEffect(()=>{if(R){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(Z.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(Z.current?.y??0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():R.contains(r.target)||z(!1),document.removeEventListener("pointermove",t),Z.current=null};return null!==Z.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[R,z,Z]),n.useEffect(()=>{let e=()=>z(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[z]);let[Y,G]=eR(e=>{let t=A().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eE(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),$=n.useCallback((e,t,r)=>{let n=!K.current&&!r;(void 0!==k.value&&k.value===t||n)&&(_(e),n&&(K.current=!0))},[k.value]),et=n.useCallback(()=>R?.focus(),[R]),er=n.useCallback((e,t,r)=>{let n=!K.current&&!r;(void 0!==k.value&&k.value===t||n)&&H(e)},[k.value]),en="popper"===o?ee:Q,eo=en===ee?{side:c,sideOffset:h,align:v,alignOffset:m,arrowPadding:w,collisionBoundary:g,collisionPadding:x,sticky:y,hideWhenDetached:b,avoidCollisions:S}:{};return(0,C.jsx)(X,{scope:r,content:R,viewport:I,onViewportChange:P,itemRefCallback:$,selectedItem:V,onItemLeave:et,itemTextRefCallback:er,focusSelectedItem:U,selectedItemText:L,position:o,isPositioned:B,searchRef:Y,children:(0,C.jsx)(T.Z,{as:J,allowPinchZoom:!0,children:(0,C.jsx)(f.M,{asChild:!0,trapped:k.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.M)(l,e=>{k.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,C.jsx)(d.XB,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>k.onOpenChange(!1),children:(0,C.jsx)(en,{role:"listbox",id:k.contentId,"data-state":k.open?"open":"closed",dir:k.dir,onContextMenu:e=>e.preventDefault(),...M,...eo,onPlaced:()=>O(!0),ref:N,style:{display:"flex",flexDirection:"column",outline:"none",...M.style},onKeyDown:(0,a.M)(M.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||G(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=A().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>F(t)),e.preventDefault()}})})})})})})});$.displayName="SelectContentImpl";var Q=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:o,...a}=e,i=W(q,r),u=G(q,r),[c,d]=n.useState(null),[p,f]=n.useState(null),h=(0,s.e)(t,e=>f(e)),v=D(r),m=n.useRef(!1),g=n.useRef(!0),{viewport:x,selectedItem:y,selectedItemText:S,focusSelectedItem:M}=u,j=n.useCallback(()=>{if(i.trigger&&i.valueNode&&c&&p&&x&&y&&S){let e=i.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),r=i.valueNode.getBoundingClientRect(),n=S.getBoundingClientRect();if("rtl"!==i.dir){let o=n.left-t.left,a=r.left-o,i=e.left-a,s=e.width+i,u=Math.max(s,t.width),d=l(a,[10,Math.max(10,window.innerWidth-10-u)]);c.style.minWidth=s+"px",c.style.left=d+"px"}else{let o=t.right-n.right,a=window.innerWidth-r.right-o,i=window.innerWidth-e.right-a,s=e.width+i,u=Math.max(s,t.width),d=l(a,[10,Math.max(10,window.innerWidth-10-u)]);c.style.minWidth=s+"px",c.style.right=d+"px"}let a=v(),s=window.innerHeight-20,u=x.scrollHeight,d=window.getComputedStyle(p),f=parseInt(d.borderTopWidth,10),h=parseInt(d.paddingTop,10),w=parseInt(d.borderBottomWidth,10),g=f+h+u+parseInt(d.paddingBottom,10)+w,b=Math.min(5*y.offsetHeight,g),C=window.getComputedStyle(x),M=parseInt(C.paddingTop,10),j=parseInt(C.paddingBottom,10),T=e.top+e.height/2-10,k=y.offsetHeight/2,R=f+h+(y.offsetTop+k);if(R<=T){let e=a.length>0&&y===a[a.length-1].ref.current;c.style.bottom="0px";let t=p.clientHeight-x.offsetTop-x.offsetHeight;c.style.height=R+Math.max(s-T,k+(e?j:0)+t+w)+"px"}else{let e=a.length>0&&y===a[0].ref.current;c.style.top="0px";let t=Math.max(T,f+x.offsetTop+(e?M:0)+k);c.style.height=t+(g-R)+"px",x.scrollTop=R-T+x.offsetTop}c.style.margin="10px 0",c.style.minHeight=b+"px",c.style.maxHeight=s+"px",o?.(),requestAnimationFrame(()=>m.current=!0)}},[v,i.trigger,i.valueNode,c,p,x,y,S,i.dir,o]);(0,b.b)(()=>j(),[j]);let[T,k]=n.useState();(0,b.b)(()=>{p&&k(window.getComputedStyle(p).zIndex)},[p]);let R=n.useCallback(e=>{e&&!0===g.current&&(j(),M?.(),g.current=!1)},[j,M]);return(0,C.jsx)(et,{scope:r,contentWrapper:c,shouldExpandOnScrollRef:m,onScrollButtonChange:R,children:(0,C.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:T},children:(0,C.jsx)(w.WV.div,{...a,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});Q.displayName="SelectItemAlignedPosition";var ee=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:o=10,...l}=e,a=_(r);return(0,C.jsx)(v.VY,{...a,...l,ref:t,align:n,collisionPadding:o,style:{boxSizing:"border-box",...l.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ee.displayName="SelectPopperPosition";var[et,er]=N(q,{}),en="SelectViewport",eo=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:o,...l}=e,i=G(en,r),u=er(en,r),c=(0,s.e)(t,i.onViewportChange),d=n.useRef(0);return(0,C.jsxs)(C.Fragment,{children:[(0,C.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,C.jsx)(I.Slot,{scope:r,children:(0,C.jsx)(w.WV.div,{"data-radix-select-viewport":"",role:"presentation",...l,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...l.style},onScroll:(0,a.M)(l.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=u;if(n?.current&&r){let e=Math.abs(d.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,o=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(o<n){let l=o+e,a=Math.min(n,l),i=l-a;r.style.height=a+"px","0px"===r.style.bottom&&(t.scrollTop=i>0?i:0,r.style.justifyContent="flex-end")}}}d.current=t.scrollTop})})})]})});eo.displayName=en;var el="SelectGroup",[ea,ei]=N(el),es=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=(0,h.M)();return(0,C.jsx)(ea,{scope:r,id:o,children:(0,C.jsx)(w.WV.div,{role:"group","aria-labelledby":o,...n,ref:t})})});es.displayName=el;var eu="SelectLabel",ec=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=ei(eu,r);return(0,C.jsx)(w.WV.div,{id:o.id,...n,ref:t})});ec.displayName=eu;var ed="SelectItem",[ep,ef]=N(ed),eh=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:o,disabled:l=!1,textValue:i,...u}=e,c=W(ed,r),d=G(ed,r),p=c.value===o,[f,v]=n.useState(i??""),[m,g]=n.useState(!1),x=(0,s.e)(t,e=>d.itemRefCallback?.(e,o,l)),y=(0,h.M)(),b=n.useRef("touch"),S=()=>{l||(c.onValueChange(o),c.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,C.jsx)(ep,{scope:r,value:o,disabled:l,textId:y,isSelected:p,onItemTextChange:n.useCallback(e=>{v(t=>t||(e?.textContent??"").trim())},[]),children:(0,C.jsx)(I.ItemSlot,{scope:r,value:o,disabled:l,textValue:f,children:(0,C.jsx)(w.WV.div,{role:"option","aria-labelledby":y,"data-highlighted":m?"":void 0,"aria-selected":p&&m,"data-state":p?"checked":"unchecked","aria-disabled":l||void 0,"data-disabled":l?"":void 0,tabIndex:l?void 0:-1,...u,ref:x,onFocus:(0,a.M)(u.onFocus,()=>g(!0)),onBlur:(0,a.M)(u.onBlur,()=>g(!1)),onClick:(0,a.M)(u.onClick,()=>{"mouse"!==b.current&&S()}),onPointerUp:(0,a.M)(u.onPointerUp,()=>{"mouse"===b.current&&S()}),onPointerDown:(0,a.M)(u.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,a.M)(u.onPointerMove,e=>{b.current=e.pointerType,l?d.onItemLeave?.():"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.M)(u.onPointerLeave,e=>{e.currentTarget===document.activeElement&&d.onItemLeave?.()}),onKeyDown:(0,a.M)(u.onKeyDown,e=>{d.searchRef?.current!==""&&" "===e.key||(R.includes(e.key)&&S()," "===e.key&&e.preventDefault())})})})})});eh.displayName=ed;var ev="SelectItemText",em=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:l,style:a,...i}=e,u=W(ev,r),c=G(ev,r),d=ef(ev,r),p=A(ev,r),[f,h]=n.useState(null),v=(0,s.e)(t,e=>h(e),d.onItemTextChange,e=>c.itemTextRefCallback?.(e,d.value,d.disabled)),m=f?.textContent,g=n.useMemo(()=>(0,C.jsx)("option",{value:d.value,disabled:d.disabled,children:m},d.value),[d.disabled,d.value,m]),{onNativeOptionAdd:x,onNativeOptionRemove:y}=p;return(0,b.b)(()=>(x(g),()=>y(g)),[x,y,g]),(0,C.jsxs)(C.Fragment,{children:[(0,C.jsx)(w.WV.span,{id:d.textId,...i,ref:v}),d.isSelected&&u.valueNode&&!u.valueNodeHasChildren?o.createPortal(i.children,u.valueNode):null]})});em.displayName=ev;var ew="SelectItemIndicator",eg=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return ef(ew,r).isSelected?(0,C.jsx)(w.WV.span,{"aria-hidden":!0,...n,ref:t}):null});eg.displayName=ew;var ex="SelectScrollUpButton",ey=n.forwardRef((e,t)=>{let r=G(ex,e.__scopeSelect),o=er(ex,e.__scopeSelect),[l,a]=n.useState(!1),i=(0,s.e)(t,o.onScrollButtonChange);return(0,b.b)(()=>{if(r.viewport&&r.isPositioned){let e=function(){a(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),l?(0,C.jsx)(eC,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ey.displayName=ex;var eb="SelectScrollDownButton",eS=n.forwardRef((e,t)=>{let r=G(eb,e.__scopeSelect),o=er(eb,e.__scopeSelect),[l,a]=n.useState(!1),i=(0,s.e)(t,o.onScrollButtonChange);return(0,b.b)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),l?(0,C.jsx)(eC,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eS.displayName=eb;var eC=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:o,...l}=e,i=G("SelectScrollButton",r),s=n.useRef(null),u=D(r),c=n.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return n.useEffect(()=>()=>c(),[c]),(0,b.b)(()=>{let e=u().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[u]),(0,C.jsx)(w.WV.div,{"aria-hidden":!0,...l,ref:t,style:{flexShrink:0,...l.style},onPointerDown:(0,a.M)(l.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(o,50))}),onPointerMove:(0,a.M)(l.onPointerMove,()=>{i.onItemLeave?.(),null===s.current&&(s.current=window.setInterval(o,50))}),onPointerLeave:(0,a.M)(l.onPointerLeave,()=>{c()})})}),eM=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,C.jsx)(w.WV.div,{"aria-hidden":!0,...n,ref:t})});eM.displayName="SelectSeparator";var ej="SelectArrow";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=_(r),l=W(ej,r),a=G(ej,r);return l.open&&"popper"===a.position?(0,C.jsx)(v.Eh,{...o,...n,ref:t}):null}).displayName=ej;var eT=n.forwardRef(({__scopeSelect:e,value:t,...r},o)=>{let l=n.useRef(null),a=(0,s.e)(o,l),i=(0,S.D)(t);return n.useEffect(()=>{let e=l.current;if(!e)return;let r=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(i!==t&&r){let n=new Event("change",{bubbles:!0});r.call(e,t),e.dispatchEvent(n)}},[i,t]),(0,C.jsx)(w.WV.select,{...r,style:{...M,...r.style},ref:a,defaultValue:t})});function ek(e){return""===e||void 0===e}function eR(e){let t=(0,x.W)(e),r=n.useRef(""),o=n.useRef(0),l=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),a=n.useCallback(()=>{r.current="",window.clearTimeout(o.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),[r,l,a]}function eE(e,t,r){var n;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,l=(n=Math.max(r?e.indexOf(r):-1,0),e.map((t,r)=>e[(n+r)%e.length]));1===o.length&&(l=l.filter(e=>e!==r));let a=l.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return a!==r?a:void 0}eT.displayName="SelectBubbleInput";var eI=B,eD=K,eP=U,eN=z,eV=Z,e_=Y,eL=eo,eW=es,eH=ec,eA=eh,eB=em,eO=eg,eK=ey,eF=eS,eU=eM},85184:function(e,t,r){r.d(t,{D:function(){return o}});var n=r(2265);function o(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}}}]);