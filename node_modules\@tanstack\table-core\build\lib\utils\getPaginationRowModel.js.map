{"version": 3, "file": "getPaginationRowModel.js", "sources": ["../../../src/utils/getPaginationRowModel.ts"], "sourcesContent": ["import { Table, RowModel, Row, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\nimport { expandRows } from './getExpandedRowModel'\n\nexport function getPaginationRowModel<TData extends RowData>(opts?: {\n  initialSync: boolean\n}): (table: Table<TData>) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [\n        table.getState().pagination,\n        table.getPrePaginationRowModel(),\n        table.options.paginateExpandedRows\n          ? undefined\n          : table.getState().expanded,\n      ],\n      (pagination, rowModel) => {\n        if (!rowModel.rows.length) {\n          return rowModel\n        }\n\n        const { pageSize, pageIndex } = pagination\n        let { rows, flatRows, rowsById } = rowModel\n        const pageStart = pageSize * pageIndex\n        const pageEnd = pageStart + pageSize\n\n        rows = rows.slice(pageStart, pageEnd)\n\n        let paginatedRowModel: RowModel<TData>\n\n        if (!table.options.paginateExpandedRows) {\n          paginatedRowModel = expandRows({\n            rows,\n            flatRows,\n            rowsById,\n          })\n        } else {\n          paginatedRowModel = {\n            rows,\n            flatRows,\n            rowsById,\n          }\n        }\n\n        paginatedRowModel.flatRows = []\n\n        const handleRow = (row: Row<TData>) => {\n          paginatedRowModel.flatRows.push(row)\n          if (row.subRows.length) {\n            row.subRows.forEach(handleRow)\n          }\n        }\n\n        paginatedRowModel.rows.forEach(handleRow)\n\n        return paginatedRowModel\n      },\n      getMemoOptions(table.options, 'debugTable', 'getPaginationRowModel')\n    )\n}\n"], "names": ["getPaginationRowModel", "opts", "table", "memo", "getState", "pagination", "getPrePaginationRowModel", "options", "paginateExpandedRows", "undefined", "expanded", "rowModel", "rows", "length", "pageSize", "pageIndex", "flatRows", "rowsById", "pageStart", "pageEnd", "slice", "paginatedRowModel", "expandRows", "handleRow", "row", "push", "subRows", "for<PERSON>ach", "getMemoOptions"], "mappings": ";;;;;;;;;;;;;;;AAIO,SAASA,qBAAqBA,CAAwBC,IAE5D,EAAkD;AACjD,EAAA,OAAOC,KAAK,IACVC,UAAI,CACF,MAAM,CACJD,KAAK,CAACE,QAAQ,EAAE,CAACC,UAAU,EAC3BH,KAAK,CAACI,wBAAwB,EAAE,EAChCJ,KAAK,CAACK,OAAO,CAACC,oBAAoB,GAC9BC,SAAS,GACTP,KAAK,CAACE,QAAQ,EAAE,CAACM,QAAQ,CAC9B,EACD,CAACL,UAAU,EAAEM,QAAQ,KAAK;AACxB,IAAA,IAAI,CAACA,QAAQ,CAACC,IAAI,CAACC,MAAM,EAAE;AACzB,MAAA,OAAOF,QAAQ,CAAA;AACjB,KAAA;IAEA,MAAM;MAAEG,QAAQ;AAAEC,MAAAA,SAAAA;AAAU,KAAC,GAAGV,UAAU,CAAA;IAC1C,IAAI;MAAEO,IAAI;MAAEI,QAAQ;AAAEC,MAAAA,QAAAA;AAAS,KAAC,GAAGN,QAAQ,CAAA;AAC3C,IAAA,MAAMO,SAAS,GAAGJ,QAAQ,GAAGC,SAAS,CAAA;AACtC,IAAA,MAAMI,OAAO,GAAGD,SAAS,GAAGJ,QAAQ,CAAA;IAEpCF,IAAI,GAAGA,IAAI,CAACQ,KAAK,CAACF,SAAS,EAAEC,OAAO,CAAC,CAAA;AAErC,IAAA,IAAIE,iBAAkC,CAAA;AAEtC,IAAA,IAAI,CAACnB,KAAK,CAACK,OAAO,CAACC,oBAAoB,EAAE;MACvCa,iBAAiB,GAAGC,8BAAU,CAAC;QAC7BV,IAAI;QACJI,QAAQ;AACRC,QAAAA,QAAAA;AACF,OAAC,CAAC,CAAA;AACJ,KAAC,MAAM;AACLI,MAAAA,iBAAiB,GAAG;QAClBT,IAAI;QACJI,QAAQ;AACRC,QAAAA,QAAAA;OACD,CAAA;AACH,KAAA;IAEAI,iBAAiB,CAACL,QAAQ,GAAG,EAAE,CAAA;IAE/B,MAAMO,SAAS,GAAIC,GAAe,IAAK;AACrCH,MAAAA,iBAAiB,CAACL,QAAQ,CAACS,IAAI,CAACD,GAAG,CAAC,CAAA;AACpC,MAAA,IAAIA,GAAG,CAACE,OAAO,CAACb,MAAM,EAAE;AACtBW,QAAAA,GAAG,CAACE,OAAO,CAACC,OAAO,CAACJ,SAAS,CAAC,CAAA;AAChC,OAAA;KACD,CAAA;AAEDF,IAAAA,iBAAiB,CAACT,IAAI,CAACe,OAAO,CAACJ,SAAS,CAAC,CAAA;AAEzC,IAAA,OAAOF,iBAAiB,CAAA;GACzB,EACDO,oBAAc,CAAC1B,KAAK,CAACK,OAAO,EAAE,YAAY,EAAE,uBAAuB,CACrE,CAAC,CAAA;AACL;;;;"}