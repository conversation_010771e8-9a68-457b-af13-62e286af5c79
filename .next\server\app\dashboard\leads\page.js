(()=>{var e={};e.id=4348,e.ids=[4348],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},35777:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,originalPathname:()=>h,pages:()=>o,routeModule:()=>m,tree:()=>d});var a=t(50482),i=t(69108),r=t(62563),l=t.n(r),n=t(68300),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d=["",{children:["dashboard",{children:["leads",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,25627)),"C:\\proj\\nextjs-saas\\app\\dashboard\\leads\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,96339)),"C:\\proj\\nextjs-saas\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,59504)),"C:\\proj\\nextjs-saas\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\proj\\nextjs-saas\\app\\dashboard\\leads\\page.tsx"],h="/dashboard/leads/page",x={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/dashboard/leads/page",pathname:"/dashboard/leads",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},77886:(e,s,t)=>{Promise.resolve().then(t.bind(t,59136))},59136:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>eo});var a=t(95344),i=t(3729),r=t(47674),l=t(61351),n=t(16212),c=t(69436),d=t(10763),o=t(68008),h=t(92549),x=t(1586),m=t(84332),u=t(16802),j=t(85222),p=t(98462),v=t(33183),f=t(16069),y=t(31405),g=t(62409),N=t(43234),C=t(99048),b="Collapsible",[w,k]=(0,p.b)(b),[A,E]=w(b),F=i.forwardRef((e,s)=>{let{__scopeCollapsible:t,open:r,defaultOpen:l,disabled:n,onOpenChange:c,...d}=e,[o,h]=(0,v.T)({prop:r,defaultProp:l??!1,onChange:c,caller:b});return(0,a.jsx)(A,{scope:t,disabled:n,contentId:(0,C.M)(),open:o,onOpenToggle:i.useCallback(()=>h(e=>!e),[h]),children:(0,a.jsx)(g.WV.div,{"data-state":O(o),"data-disabled":n?"":void 0,...d,ref:s})})});F.displayName=b;var M="CollapsibleTrigger",Z=i.forwardRef((e,s)=>{let{__scopeCollapsible:t,...i}=e,r=E(M,t);return(0,a.jsx)(g.WV.button,{type:"button","aria-controls":r.contentId,"aria-expanded":r.open||!1,"data-state":O(r.open),"data-disabled":r.disabled?"":void 0,disabled:r.disabled,...i,ref:s,onClick:(0,j.M)(e.onClick,r.onOpenToggle)})});Z.displayName=M;var _="CollapsibleContent",S=i.forwardRef((e,s)=>{let{forceMount:t,...i}=e,r=E(_,e.__scopeCollapsible);return(0,a.jsx)(N.z,{present:t||r.open,children:({present:e})=>(0,a.jsx)(T,{...i,ref:s,present:e})})});S.displayName=_;var T=i.forwardRef((e,s)=>{let{__scopeCollapsible:t,present:r,children:l,...n}=e,c=E(_,t),[d,o]=i.useState(r),h=i.useRef(null),x=(0,y.e)(s,h),m=i.useRef(0),u=m.current,j=i.useRef(0),p=j.current,v=c.open||d,N=i.useRef(v),C=i.useRef(void 0);return i.useEffect(()=>{let e=requestAnimationFrame(()=>N.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,f.b)(()=>{let e=h.current;if(e){C.current=C.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let s=e.getBoundingClientRect();m.current=s.height,j.current=s.width,N.current||(e.style.transitionDuration=C.current.transitionDuration,e.style.animationName=C.current.animationName),o(r)}},[c.open,r]),(0,a.jsx)(g.WV.div,{"data-state":O(c.open),"data-disabled":c.disabled?"":void 0,id:c.contentId,hidden:!v,...n,ref:x,style:{"--radix-collapsible-content-height":u?`${u}px`:void 0,"--radix-collapsible-content-width":p?`${p}px`:void 0,...e.style},children:v&&l})});function O(e){return e?"open":"closed"}var L=t(17418),R=t(31498),I=t(14513),P=t(76755),D=t(38271),z=t(28765),q=t(12704),B=t(25390),$=t(33733),W=t(96885),H=t(44669);let X={search:"",status:[],priority:[],source:[],industry:[],companySize:[],scoreMin:void 0,scoreMax:void 0,temperature:[],createdAfter:"",createdBefore:"",lastActivityAfter:"",lastActivityBefore:"",convertedAfter:"",convertedBefore:"",budgetMin:void 0,budgetMax:void 0,hasBudget:void 0,hasActivities:void 0,activityCountMin:void 0,activityCountMax:void 0,hasRecentActivity:void 0,assignedTo:[],unassigned:void 0,isQualified:void 0,hasPhone:void 0,hasEmail:void 0,hasWebsite:void 0,hasTimeline:void 0,isConverted:void 0,conversionType:[]};function V({onFilterChange:e,onApplyFilters:s,initialFilters:t={},loading:r=!1}){let[d,o]=(0,i.useState)({...X,...t}),[j,p]=(0,i.useState)(!1),[v,f]=(0,i.useState)([]),[y,g]=(0,i.useState)(!1),[N,C]=(0,i.useState)(""),[b,w]=(0,i.useState)(""),[k,A]=(0,i.useState)(!1),[E,M]=(0,i.useState)(!1),[_,T]=(0,i.useState)({basic:!0,scoring:!1,dates:!1,budget:!1,activity:!1,assignment:!1,qualification:!1,conversion:!1});(0,i.useEffect)(()=>{O()},[]);let O=async()=>{try{let e=await fetch("/api/leads/saved-filters?includePublic=true");if(e.ok){let s=await e.json();f(s.savedFilters)}}catch(e){console.error("Error fetching saved filters:",e)}},V=(s,t)=>{let a={...d,[s]:t};o(a),e(a)},U=(e,s,t)=>{let a=d[e];V(e,t?[...a,s]:a.filter(e=>e!==s))},G=()=>{let t={...X};o(t),e(t),s(t)},Q=t=>{let a={...X,...t.filters};o(a),e(a),s(a),H.toast.success(`Loaded filter: ${t.name}`)},K=async()=>{if(!N.trim()){H.toast.error("Filter name is required");return}try{if((await fetch("/api/leads/saved-filters",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:N,description:b||void 0,filters:d,isPublic:k,isDefault:E})})).ok)H.toast.success("Filter saved successfully"),g(!1),C(""),w(""),A(!1),M(!1),O();else throw Error("Failed to save filter")}catch(e){H.toast.error("Failed to save filter")}},Y=async e=>{if(confirm("Are you sure you want to delete this saved filter?"))try{if((await fetch(`/api/leads/saved-filters?id=${e}`,{method:"DELETE"})).ok)H.toast.success("Filter deleted successfully"),O();else throw Error("Failed to delete filter")}catch(e){H.toast.error("Failed to delete filter")}},J=e=>{T(s=>({...s,[e]:!s[e]}))},ee=()=>{let e=0;return d.search&&e++,d.status.length>0&&e++,d.priority.length>0&&e++,d.source.length>0&&e++,d.industry.length>0&&e++,d.companySize.length>0&&e++,(void 0!==d.scoreMin||void 0!==d.scoreMax)&&e++,d.temperature.length>0&&e++,(d.createdAfter||d.createdBefore)&&e++,(d.lastActivityAfter||d.lastActivityBefore)&&e++,(d.convertedAfter||d.convertedBefore)&&e++,(void 0!==d.budgetMin||void 0!==d.budgetMax)&&e++,void 0!==d.hasBudget&&e++,void 0!==d.hasActivities&&e++,(void 0!==d.activityCountMin||void 0!==d.activityCountMax)&&e++,void 0!==d.hasRecentActivity&&e++,d.assignedTo.length>0&&e++,void 0!==d.unassigned&&e++,void 0!==d.isQualified&&e++,void 0!==d.hasPhone&&e++,void 0!==d.hasEmail&&e++,void 0!==d.hasWebsite&&e++,void 0!==d.hasTimeline&&e++,void 0!==d.isConverted&&e++,d.conversionType.length>0&&e++,e};return(0,a.jsxs)(l.Zb,{children:[a.jsx(l.Ol,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(l.ll,{className:"flex items-center",children:[a.jsx(L.Z,{className:"h-5 w-5 mr-2"}),"Advanced Filters",ee()>0&&(0,a.jsxs)(c.C,{variant:"secondary",className:"ml-2",children:[ee()," active"]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(n.z,{variant:"outline",size:"sm",onClick:()=>g(!0),children:[a.jsx(R.Z,{className:"h-4 w-4 mr-2"}),"Save Filter"]}),(0,a.jsxs)(n.z,{variant:"outline",size:"sm",onClick:G,children:[a.jsx(I.Z,{className:"h-4 w-4 mr-2"}),"Clear All"]})]})]})}),(0,a.jsxs)(l.aY,{className:"space-y-4",children:[v.length>0&&(0,a.jsxs)("div",{children:[a.jsx(x._,{className:"text-sm font-medium mb-2 block",children:"Saved Filters"}),a.jsx("div",{className:"flex flex-wrap gap-2",children:v.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsxs)(n.z,{variant:"outline",size:"sm",onClick:()=>Q(e),className:"flex items-center space-x-1",children:[e.isDefault&&a.jsx(P.Z,{className:"h-3 w-3 text-yellow-500"}),a.jsx("span",{children:e.name})]}),a.jsx(n.z,{variant:"ghost",size:"sm",onClick:()=>Y(e.id),className:"h-8 w-8 p-0 text-red-600 hover:text-red-700",children:a.jsx(D.Z,{className:"h-3 w-3"})})]},e.id))})]}),(0,a.jsxs)("div",{children:[a.jsx(x._,{htmlFor:"search",children:"Quick Search"}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx(z.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),a.jsx(h.I,{id:"search",placeholder:"Search leads by name, email, company...",value:d.search,onChange:e=>V("search",e.target.value),className:"pl-10"})]})]}),(0,a.jsxs)(F,{open:_.basic,onOpenChange:()=>J("basic"),children:[a.jsx(Z,{asChild:!0,children:(0,a.jsxs)(n.z,{variant:"ghost",className:"w-full justify-between p-0 h-auto",children:[a.jsx("span",{className:"font-medium",children:"Basic Filters"}),_.basic?a.jsx(q.Z,{className:"h-4 w-4"}):a.jsx(B.Z,{className:"h-4 w-4"})]})}),(0,a.jsxs)(S,{className:"space-y-4 mt-4",children:[(0,a.jsxs)("div",{children:[a.jsx(x._,{children:"Status"}),a.jsx("div",{className:"flex flex-wrap gap-2 mt-2",children:["NEW","CONTACTED","QUALIFIED","PROPOSAL","NEGOTIATION","CONVERTED","LOST"].map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(m.X,{id:`status-${e}`,checked:d.status.includes(e),onCheckedChange:s=>U("status",e,!!s)}),a.jsx(x._,{htmlFor:`status-${e}`,className:"text-sm",children:e})]},e))})]}),(0,a.jsxs)("div",{children:[a.jsx(x._,{children:"Priority"}),a.jsx("div",{className:"flex flex-wrap gap-2 mt-2",children:["LOW","MEDIUM","HIGH","URGENT"].map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(m.X,{id:`priority-${e}`,checked:d.priority.includes(e),onCheckedChange:s=>U("priority",e,!!s)}),a.jsx(x._,{htmlFor:`priority-${e}`,className:"text-sm",children:e})]},e))})]}),(0,a.jsxs)("div",{children:[a.jsx(x._,{children:"Lead Source"}),a.jsx("div",{className:"flex flex-wrap gap-2 mt-2",children:["WEBSITE","REFERRAL","SOCIAL_MEDIA","EMAIL_CAMPAIGN","COLD_CALL","TRADE_SHOW","PARTNER","OTHER"].map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(m.X,{id:`source-${e}`,checked:d.source.includes(e),onCheckedChange:s=>U("source",e,!!s)}),a.jsx(x._,{htmlFor:`source-${e}`,className:"text-sm",children:e.replace("_"," ")})]},e))})]}),(0,a.jsxs)("div",{children:[a.jsx(x._,{children:"Industry"}),a.jsx("div",{className:"flex flex-wrap gap-2 mt-2",children:["TECHNOLOGY","FINANCE","HEALTHCARE","MANUFACTURING","RETAIL","EDUCATION","OTHER"].map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(m.X,{id:`industry-${e}`,checked:d.industry.includes(e),onCheckedChange:s=>U("industry",e,!!s)}),a.jsx(x._,{htmlFor:`industry-${e}`,className:"text-sm",children:e})]},e))})]}),(0,a.jsxs)("div",{children:[a.jsx(x._,{children:"Company Size"}),a.jsx("div",{className:"flex flex-wrap gap-2 mt-2",children:["STARTUP","SMALL","MEDIUM","LARGE","ENTERPRISE"].map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(m.X,{id:`size-${e}`,checked:d.companySize.includes(e),onCheckedChange:s=>U("companySize",e,!!s)}),a.jsx(x._,{htmlFor:`size-${e}`,className:"text-sm",children:e})]},e))})]})]})]}),(0,a.jsxs)(F,{open:_.scoring,onOpenChange:()=>J("scoring"),children:[a.jsx(Z,{asChild:!0,children:(0,a.jsxs)(n.z,{variant:"ghost",className:"w-full justify-between p-0 h-auto",children:[a.jsx("span",{className:"font-medium",children:"Scoring & Temperature"}),_.scoring?a.jsx(q.Z,{className:"h-4 w-4"}):a.jsx(B.Z,{className:"h-4 w-4"})]})}),(0,a.jsxs)(S,{className:"space-y-4 mt-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx(x._,{htmlFor:"scoreMin",children:"Min Score"}),a.jsx(h.I,{id:"scoreMin",type:"number",min:"0",max:"100",placeholder:"0",value:d.scoreMin||"",onChange:e=>V("scoreMin",e.target.value?parseInt(e.target.value):void 0)})]}),(0,a.jsxs)("div",{children:[a.jsx(x._,{htmlFor:"scoreMax",children:"Max Score"}),a.jsx(h.I,{id:"scoreMax",type:"number",min:"0",max:"100",placeholder:"100",value:d.scoreMax||"",onChange:e=>V("scoreMax",e.target.value?parseInt(e.target.value):void 0)})]})]}),(0,a.jsxs)("div",{children:[a.jsx(x._,{children:"Lead Temperature"}),a.jsx("div",{className:"flex flex-wrap gap-2 mt-2",children:["HOT","WARM","COLD"].map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(m.X,{id:`temp-${e}`,checked:d.temperature.includes(e),onCheckedChange:s=>U("temperature",e,!!s)}),a.jsx(x._,{htmlFor:`temp-${e}`,className:"text-sm",children:e})]},e))})]})]})]}),(0,a.jsxs)(F,{open:_.dates,onOpenChange:()=>J("dates"),children:[a.jsx(Z,{asChild:!0,children:(0,a.jsxs)(n.z,{variant:"ghost",className:"w-full justify-between p-0 h-auto",children:[a.jsx("span",{className:"font-medium",children:"Date Filters"}),_.dates?a.jsx(q.Z,{className:"h-4 w-4"}):a.jsx(B.Z,{className:"h-4 w-4"})]})}),(0,a.jsxs)(S,{className:"space-y-4 mt-4",children:[(0,a.jsxs)("div",{children:[a.jsx(x._,{children:"Created Date Range"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 mt-2",children:[(0,a.jsxs)("div",{children:[a.jsx(x._,{htmlFor:"createdAfter",className:"text-sm",children:"From"}),a.jsx(h.I,{id:"createdAfter",type:"date",value:d.createdAfter,onChange:e=>V("createdAfter",e.target.value)})]}),(0,a.jsxs)("div",{children:[a.jsx(x._,{htmlFor:"createdBefore",className:"text-sm",children:"To"}),a.jsx(h.I,{id:"createdBefore",type:"date",value:d.createdBefore,onChange:e=>V("createdBefore",e.target.value)})]})]})]}),(0,a.jsxs)("div",{children:[a.jsx(x._,{children:"Last Activity Date Range"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 mt-2",children:[(0,a.jsxs)("div",{children:[a.jsx(x._,{htmlFor:"lastActivityAfter",className:"text-sm",children:"From"}),a.jsx(h.I,{id:"lastActivityAfter",type:"date",value:d.lastActivityAfter,onChange:e=>V("lastActivityAfter",e.target.value)})]}),(0,a.jsxs)("div",{children:[a.jsx(x._,{htmlFor:"lastActivityBefore",className:"text-sm",children:"To"}),a.jsx(h.I,{id:"lastActivityBefore",type:"date",value:d.lastActivityBefore,onChange:e=>V("lastActivityBefore",e.target.value)})]})]})]})]})]}),(0,a.jsxs)(F,{open:_.budget,onOpenChange:()=>J("budget"),children:[a.jsx(Z,{asChild:!0,children:(0,a.jsxs)(n.z,{variant:"ghost",className:"w-full justify-between p-0 h-auto",children:[a.jsx("span",{className:"font-medium",children:"Budget Filters"}),_.budget?a.jsx(q.Z,{className:"h-4 w-4"}):a.jsx(B.Z,{className:"h-4 w-4"})]})}),(0,a.jsxs)(S,{className:"space-y-4 mt-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx(x._,{htmlFor:"budgetMin",children:"Min Budget ($)"}),a.jsx(h.I,{id:"budgetMin",type:"number",min:"0",placeholder:"0",value:d.budgetMin||"",onChange:e=>V("budgetMin",e.target.value?parseFloat(e.target.value):void 0)})]}),(0,a.jsxs)("div",{children:[a.jsx(x._,{htmlFor:"budgetMax",children:"Max Budget ($)"}),a.jsx(h.I,{id:"budgetMax",type:"number",min:"0",placeholder:"No limit",value:d.budgetMax||"",onChange:e=>V("budgetMax",e.target.value?parseFloat(e.target.value):void 0)})]})]}),(0,a.jsxs)("div",{children:[a.jsx(x._,{children:"Budget Status"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 mt-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(m.X,{id:"hasBudget",checked:!0===d.hasBudget,onCheckedChange:e=>V("hasBudget",!!e||void 0)}),a.jsx(x._,{htmlFor:"hasBudget",className:"text-sm",children:"Has Budget"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(m.X,{id:"noBudget",checked:!1===d.hasBudget,onCheckedChange:e=>V("hasBudget",!e&&void 0)}),a.jsx(x._,{htmlFor:"noBudget",className:"text-sm",children:"No Budget"})]})]})]})]})]}),(0,a.jsxs)(F,{open:_.activity,onOpenChange:()=>J("activity"),children:[a.jsx(Z,{asChild:!0,children:(0,a.jsxs)(n.z,{variant:"ghost",className:"w-full justify-between p-0 h-auto",children:[a.jsx("span",{className:"font-medium",children:"Activity Filters"}),_.activity?a.jsx(q.Z,{className:"h-4 w-4"}):a.jsx(B.Z,{className:"h-4 w-4"})]})}),(0,a.jsxs)(S,{className:"space-y-4 mt-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx(x._,{htmlFor:"activityCountMin",children:"Min Activities"}),a.jsx(h.I,{id:"activityCountMin",type:"number",min:"0",placeholder:"0",value:d.activityCountMin||"",onChange:e=>V("activityCountMin",e.target.value?parseInt(e.target.value):void 0)})]}),(0,a.jsxs)("div",{children:[a.jsx(x._,{htmlFor:"activityCountMax",children:"Max Activities"}),a.jsx(h.I,{id:"activityCountMax",type:"number",min:"0",placeholder:"No limit",value:d.activityCountMax||"",onChange:e=>V("activityCountMax",e.target.value?parseInt(e.target.value):void 0)})]})]}),(0,a.jsxs)("div",{children:[a.jsx(x._,{children:"Activity Status"}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-4 mt-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(m.X,{id:"hasActivities",checked:!0===d.hasActivities,onCheckedChange:e=>V("hasActivities",!!e||void 0)}),a.jsx(x._,{htmlFor:"hasActivities",className:"text-sm",children:"Has Activities"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(m.X,{id:"noActivities",checked:!1===d.hasActivities,onCheckedChange:e=>V("hasActivities",!e&&void 0)}),a.jsx(x._,{htmlFor:"noActivities",className:"text-sm",children:"No Activities"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(m.X,{id:"hasRecentActivity",checked:!0===d.hasRecentActivity,onCheckedChange:e=>V("hasRecentActivity",!!e||void 0)}),a.jsx(x._,{htmlFor:"hasRecentActivity",className:"text-sm",children:"Recent Activity (7 days)"})]})]})]})]})]}),(0,a.jsxs)(F,{open:_.qualification,onOpenChange:()=>J("qualification"),children:[a.jsx(Z,{asChild:!0,children:(0,a.jsxs)(n.z,{variant:"ghost",className:"w-full justify-between p-0 h-auto",children:[a.jsx("span",{className:"font-medium",children:"Qualification Filters"}),_.qualification?a.jsx(q.Z,{className:"h-4 w-4"}):a.jsx(B.Z,{className:"h-4 w-4"})]})}),a.jsx(S,{className:"space-y-4 mt-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(m.X,{id:"hasPhone",checked:!0===d.hasPhone,onCheckedChange:e=>V("hasPhone",!!e||void 0)}),a.jsx(x._,{htmlFor:"hasPhone",className:"text-sm",children:"Has Phone"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(m.X,{id:"hasEmail",checked:!0===d.hasEmail,onCheckedChange:e=>V("hasEmail",!!e||void 0)}),a.jsx(x._,{htmlFor:"hasEmail",className:"text-sm",children:"Has Email"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(m.X,{id:"hasWebsite",checked:!0===d.hasWebsite,onCheckedChange:e=>V("hasWebsite",!!e||void 0)}),a.jsx(x._,{htmlFor:"hasWebsite",className:"text-sm",children:"Has Website"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(m.X,{id:"hasTimeline",checked:!0===d.hasTimeline,onCheckedChange:e=>V("hasTimeline",!!e||void 0)}),a.jsx(x._,{htmlFor:"hasTimeline",className:"text-sm",children:"Has Timeline"})]})]})})]}),(0,a.jsxs)(F,{open:_.conversion,onOpenChange:()=>J("conversion"),children:[a.jsx(Z,{asChild:!0,children:(0,a.jsxs)(n.z,{variant:"ghost",className:"w-full justify-between p-0 h-auto",children:[a.jsx("span",{className:"font-medium",children:"Conversion Filters"}),_.conversion?a.jsx(q.Z,{className:"h-4 w-4"}):a.jsx(B.Z,{className:"h-4 w-4"})]})}),(0,a.jsxs)(S,{className:"space-y-4 mt-4",children:[(0,a.jsxs)("div",{children:[a.jsx(x._,{children:"Conversion Status"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 mt-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(m.X,{id:"isConverted",checked:!0===d.isConverted,onCheckedChange:e=>V("isConverted",!!e||void 0)}),a.jsx(x._,{htmlFor:"isConverted",className:"text-sm",children:"Converted"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(m.X,{id:"notConverted",checked:!1===d.isConverted,onCheckedChange:e=>V("isConverted",!e&&void 0)}),a.jsx(x._,{htmlFor:"notConverted",className:"text-sm",children:"Not Converted"})]})]})]}),(0,a.jsxs)("div",{children:[a.jsx(x._,{children:"Conversion Type"}),a.jsx("div",{className:"flex flex-wrap gap-2 mt-2",children:["DIRECT","QUOTATION","PROPOSAL","TRIAL","DEMO"].map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(m.X,{id:`conversion-${e}`,checked:d.conversionType.includes(e),onCheckedChange:s=>U("conversionType",e,!!s)}),a.jsx(x._,{htmlFor:`conversion-${e}`,className:"text-sm",children:e})]},e))})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(n.z,{onClick:()=>{s(d)},disabled:r,children:[r?a.jsx($.Z,{className:"h-4 w-4 mr-2 animate-spin"}):a.jsx(L.Z,{className:"h-4 w-4 mr-2"}),"Apply Filters"]}),(0,a.jsxs)(n.z,{variant:"outline",onClick:G,children:[a.jsx(I.Z,{className:"h-4 w-4 mr-2"}),"Clear All"]})]}),a.jsx("div",{className:"flex items-center space-x-2",children:(0,a.jsxs)(n.z,{variant:"outline",size:"sm",children:[a.jsx(W.Z,{className:"h-4 w-4 mr-2"}),"Export"]})})]})]}),a.jsx(u.Vq,{open:y,onOpenChange:g,children:(0,a.jsxs)(u.cZ,{children:[a.jsx(u.fK,{children:a.jsx(u.$N,{children:"Save Filter"})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[a.jsx(x._,{htmlFor:"filterName",children:"Filter Name *"}),a.jsx(h.I,{id:"filterName",value:N,onChange:e=>C(e.target.value),placeholder:"Enter filter name"})]}),(0,a.jsxs)("div",{children:[a.jsx(x._,{htmlFor:"filterDescription",children:"Description"}),a.jsx(h.I,{id:"filterDescription",value:b,onChange:e=>w(e.target.value),placeholder:"Optional description"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(m.X,{id:"saveAsPublic",checked:k,onCheckedChange:e=>A(!!e)}),a.jsx(x._,{htmlFor:"saveAsPublic",className:"text-sm",children:"Make this filter available to all team members"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(m.X,{id:"saveAsDefault",checked:E,onCheckedChange:e=>M(!!e)}),a.jsx(x._,{htmlFor:"saveAsDefault",className:"text-sm",children:"Set as my default filter"})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-2",children:[a.jsx(n.z,{variant:"outline",onClick:()=>g(!1),children:"Cancel"}),a.jsx(n.z,{onClick:K,children:"Save Filter"})]})]})]})})]})}var U=t(46064),G=t(71206),Q=t(40626),K=t(15366),Y=t(48411),J=t(62093),ee=t(53148),es=t(75695),et=t(17910),ea=t(51838),ei=t(28240),er=t(25545),el=t(20886),en=t(20783),ec=t.n(en),ed=t(27109);function eo(){let{data:e}=(0,r.useSession)(),[s,t]=(0,i.useState)([]),[h,x]=(0,i.useState)([]),[m,u]=(0,i.useState)(!0),[j,p]=(0,i.useState)(!1),[v,f]=(0,i.useState)(!1),[y,g]=(0,i.useState)(null),[N,C]=(0,i.useState)(!1),[b,w]=(0,i.useState)({}),[k,A]=(0,i.useState)({total:0,new:0,qualified:0,closedWon:0,totalValue:0}),E=async()=>{try{let e=await fetch("/api/leads");if(!e.ok)throw Error("Failed to fetch leads");let s=await e.json();t(s.leads),x(s.leads);let a=s.leads.length,i=s.leads.filter(e=>"NEW"===e.status).length,r=s.leads.filter(e=>"QUALIFIED"===e.status).length,l=s.leads.filter(e=>"CLOSED_WON"===e.status).length,n=s.leads.reduce((e,s)=>e+(s.budget||0),0);A({total:a,new:i,qualified:r,closedWon:l,totalValue:n})}catch(e){H.toast.error("Failed to load leads"),console.error("Error fetching leads:",e)}finally{u(!1)}},F=async e=>{try{p(!0);let s=await fetch("/api/leads/filter",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok)throw Error("Failed to apply filters");let t=await s.json();x(t.leads),H.toast.success(`Found ${t.leads.length} leads matching your criteria`)}catch(e){H.toast.error("Failed to apply filters"),console.error("Error applying filters:",e)}finally{p(!1)}};(0,i.useEffect)(()=>{E()},[]);let M=async e=>{if(confirm(`Are you sure you want to delete "${e.firstName} ${e.lastName}"?`))try{let s=await fetch(`/api/leads/${e.id}`,{method:"DELETE"});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to delete lead")}H.toast.success("Lead deleted successfully"),E()}catch(e){H.toast.error(e instanceof Error?e.message:"Failed to delete lead")}},Z=e=>{g(e),f(!0)},_=e=>{if(!e)return a.jsx(c.C,{variant:"secondary",children:"Unknown"});switch(e){case"NEW":return a.jsx(c.C,{variant:"secondary",children:"New"});case"CONTACTED":return a.jsx(c.C,{variant:"info",children:"Contacted"});case"QUALIFIED":return a.jsx(c.C,{variant:"success",children:"Qualified"});case"PROPOSAL":return a.jsx(c.C,{variant:"warning",children:"Proposal"});case"NEGOTIATION":return a.jsx(c.C,{variant:"warning",children:"Negotiation"});case"CLOSED_WON":return a.jsx(c.C,{variant:"success",children:"Closed Won"});case"CLOSED_LOST":return a.jsx(c.C,{variant:"destructive",children:"Closed Lost"});default:return a.jsx(c.C,{variant:"secondary",children:e})}},S=e=>{if(!e)return a.jsx(c.C,{variant:"secondary",className:"text-xs",children:"Unknown"});switch(e){case"LOW":return a.jsx(c.C,{variant:"secondary",className:"text-xs",children:"Low"});case"MEDIUM":return a.jsx(c.C,{variant:"info",className:"text-xs",children:"Medium"});case"HIGH":return a.jsx(c.C,{variant:"warning",className:"text-xs",children:"High"});case"URGENT":return a.jsx(c.C,{variant:"destructive",className:"text-xs",children:"Urgent"});default:return a.jsx(c.C,{variant:"secondary",className:"text-xs",children:e})}},T=[{accessorKey:"firstName",header:"Lead",cell:({row:e})=>{let s=e.original;return(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:a.jsx(U.Z,{className:"h-4 w-4 text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"font-medium",children:[s.firstName," ",s.lastName]}),s.companyName&&a.jsx("div",{className:"text-sm text-gray-500",children:s.companyName}),s.title&&a.jsx("div",{className:"text-xs text-gray-400",children:s.title})]})]})}},{accessorKey:"email",header:"Contact",cell:({row:e})=>{let s=e.original;return(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[a.jsx(G.Z,{className:"h-3 w-3 text-gray-400"}),a.jsx("span",{children:s.email})]}),s.phone&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[a.jsx(Q.Z,{className:"h-3 w-3 text-gray-400"}),a.jsx("span",{children:s.phone})]})]})}},{accessorKey:"status",header:"Status",cell:({row:e})=>{let s=e.getValue("status");return(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[_(s),"CONVERTED"===s&&a.jsx(K.Z,{className:"h-3 w-3 text-green-600",title:"Converted to Customer"})]})}},{accessorKey:"priority",header:"Priority",cell:({row:e})=>S(e.getValue("priority"))},{accessorKey:"budget",header:"Budget",cell:({row:e})=>{let s=e.getValue("budget");return s?(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(Y.Z,{className:"h-3 w-3 text-green-600"}),(0,a.jsxs)("span",{className:"font-medium",children:["$",s.toLocaleString()]})]}):a.jsx("span",{className:"text-gray-400 text-sm",children:"-"})}},{accessorKey:"score",header:"Score",cell:({row:e})=>{let s=e.getValue("score");return(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(ed.q9,{score:s}),(0,a.jsxs)("span",{className:"font-medium",children:[s,"/100"]})]})}},{accessorKey:"source",header:"Source",cell:({row:e})=>{let s=e.getValue("source");return s?a.jsx(c.C,{variant:"outline",className:"text-xs",children:s}):a.jsx("span",{className:"text-gray-400 text-sm",children:"-"})}},{id:"actions",cell:({row:e})=>{let s=e.original;return(0,a.jsxs)(el.h_,{children:[a.jsx(el.$F,{asChild:!0,children:a.jsx(n.z,{variant:"ghost",className:"h-8 w-8 p-0",children:a.jsx(J.Z,{className:"h-4 w-4"})})}),(0,a.jsxs)(el.AW,{align:"end",children:[a.jsx(el.Ju,{children:"Actions"}),a.jsx(el.Xi,{asChild:!0,children:(0,a.jsxs)(ec(),{href:`/dashboard/leads/${s.id}`,children:[a.jsx(ee.Z,{className:"mr-2 h-4 w-4"}),"View Details"]})}),(0,a.jsxs)(el.Xi,{onClick:()=>Z(s),children:[a.jsx(es.Z,{className:"mr-2 h-4 w-4"}),"Edit"]}),a.jsx(el.VD,{}),(0,a.jsxs)(el.Xi,{onClick:()=>M(s),className:"text-red-600",children:[a.jsx(D.Z,{className:"mr-2 h-4 w-4"}),"Delete"]})]})]})}}];return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Leads"}),a.jsx("p",{className:"text-gray-600 mt-1",children:"Track and convert your business leads"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(n.z,{variant:"outline",asChild:!0,children:(0,a.jsxs)(ec(),{href:"/dashboard/leads/pipeline",children:[a.jsx(U.Z,{className:"h-4 w-4 mr-2"}),"Pipeline View"]})}),a.jsx(n.z,{variant:"outline",asChild:!0,children:(0,a.jsxs)(ec(),{href:"/dashboard/leads/scoring",children:[a.jsx(et.Z,{className:"h-4 w-4 mr-2"}),"Scoring Analytics"]})}),a.jsx(n.z,{variant:"outline",asChild:!0,children:(0,a.jsxs)(ec(),{href:"/dashboard/leads/conversions",children:[a.jsx(K.Z,{className:"h-4 w-4 mr-2"}),"Conversion Analytics"]})}),(0,a.jsxs)(n.z,{variant:"outline",onClick:()=>C(!N),children:[a.jsx(L.Z,{className:"h-4 w-4 mr-2"}),"Advanced Filter",Object.keys(b).length>0&&a.jsx(c.C,{variant:"secondary",className:"ml-2",children:Object.keys(b).length})]}),(0,a.jsxs)(n.z,{className:"flex items-center space-x-2",onClick:()=>f(!0),children:[a.jsx(ea.Z,{className:"h-4 w-4"}),a.jsx("span",{children:"Add Lead"})]})]})]}),N&&a.jsx(V,{onFilterChange:e=>{w(e)},onApplyFilters:F,initialFilters:b,loading:j}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-6",children:[(0,a.jsxs)(l.Zb,{children:[(0,a.jsxs)(l.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(l.ll,{className:"text-sm font-medium",children:"Total Leads"}),a.jsx(ei.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(l.aY,{children:[a.jsx("div",{className:"text-2xl font-bold",children:k.total}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"All leads"})]})]}),(0,a.jsxs)(l.Zb,{children:[(0,a.jsxs)(l.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(l.ll,{className:"text-sm font-medium",children:"New"}),a.jsx(et.Z,{className:"h-4 w-4 text-blue-600"})]}),(0,a.jsxs)(l.aY,{children:[a.jsx("div",{className:"text-2xl font-bold",children:k.new}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"New leads"})]})]}),(0,a.jsxs)(l.Zb,{children:[(0,a.jsxs)(l.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(l.ll,{className:"text-sm font-medium",children:"Qualified"}),a.jsx(U.Z,{className:"h-4 w-4 text-green-600"})]}),(0,a.jsxs)(l.aY,{children:[a.jsx("div",{className:"text-2xl font-bold",children:k.qualified}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"Qualified leads"})]})]}),(0,a.jsxs)(l.Zb,{children:[(0,a.jsxs)(l.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(l.ll,{className:"text-sm font-medium",children:"Closed Won"}),a.jsx(er.Z,{className:"h-4 w-4 text-purple-600"})]}),(0,a.jsxs)(l.aY,{children:[a.jsx("div",{className:"text-2xl font-bold",children:k.closedWon}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"Successful conversions"})]})]}),(0,a.jsxs)(l.Zb,{children:[(0,a.jsxs)(l.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(l.ll,{className:"text-sm font-medium",children:"Pipeline Value"}),a.jsx(Y.Z,{className:"h-4 w-4 text-green-600"})]}),(0,a.jsxs)(l.aY,{children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:["$",k.totalValue.toLocaleString()]}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"Total estimated value"})]})]})]}),(0,a.jsxs)(l.Zb,{children:[a.jsx(l.Ol,{children:a.jsx(l.ll,{children:"Lead Pipeline"})}),a.jsx(l.aY,{children:m?a.jsx("div",{className:"flex items-center justify-center py-8",children:a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):a.jsx(d.w,{columns:T,data:h,searchPlaceholder:"Search leads..."})})]}),a.jsx(o.p,{isOpen:v,onClose:()=>{f(!1),g(null)},onSuccess:E,lead:y,mode:y?"edit":"create"})]})}},27109:(e,s,t)=>{"use strict";t.d(s,{q9:()=>h,OT:()=>x});var a=t(95344),i=t(69436),r=t(69224);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,r.Z)("Flame",[["path",{d:"M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z",key:"96xj49"}]]),n=(0,r.Z)("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]),c=(0,r.Z)("Snowflake",[["line",{x1:"2",x2:"22",y1:"12",y2:"12",key:"1dnqot"}],["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"m20 16-4-4 4-4",key:"rquw4f"}],["path",{d:"m4 8 4 4-4 4",key:"12s3z9"}],["path",{d:"m16 4-4 4-4-4",key:"1tumq1"}],["path",{d:"m8 20 4-4 4 4",key:"9p200w"}]]);var d=t(66985);function o({score:e,size:s="md",showIcon:t=!0,showLabel:r=!0}){let o=e>=70?"HOT":e>=40?"WARM":"COLD",h=(e=>{switch(e){case"HOT":return{color:"text-red-600 bg-red-100 border-red-200",icon:l,label:"Hot Lead",description:"High priority, ready to convert"};case"WARM":return{color:"text-orange-600 bg-orange-100 border-orange-200",icon:n,label:"Warm Lead",description:"Good potential, needs nurturing"};case"COLD":return{color:"text-blue-600 bg-blue-100 border-blue-200",icon:c,label:"Cold Lead",description:"Low engagement, requires attention"};default:return{color:"text-gray-600 bg-gray-100 border-gray-200",icon:d.Z,label:"Unknown",description:"Temperature not determined"}}})(o),x=(e=>{switch(e){case"sm":return{badge:"text-xs px-2 py-1",icon:"h-3 w-3",text:"text-xs"};case"lg":return{badge:"text-base px-4 py-2",icon:"h-5 w-5",text:"text-base"};default:return{badge:"text-sm px-3 py-1",icon:"h-4 w-4",text:"text-sm"}}})(s),m=h.icon;return(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(i.C,{className:`${h.color} border ${x.badge} font-medium`,variant:"outline",children:(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[t&&a.jsx(m,{className:x.icon}),a.jsx("span",{children:o})]})}),r&&(0,a.jsxs)("div",{className:"flex flex-col",children:[a.jsx("span",{className:`${x.text} font-medium text-gray-900`,children:h.label}),"lg"===s&&a.jsx("span",{className:"text-xs text-gray-500",children:h.description})]})]})}function h({score:e}){return a.jsx(o,{score:e,size:"sm",showLabel:!1})}function x({score:e}){return a.jsx(o,{score:e,size:"lg",showIcon:!0,showLabel:!0})}},84332:(e,s,t)=>{"use strict";t.d(s,{X:()=>Z});var a=t(95344),i=t(3729),r=t(31405),l=t(98462),n=t(85222),c=t(33183),d=t(92062),o=t(63085),h=t(43234),x=t(62409),m="Checkbox",[u,j]=(0,l.b)(m),[p,v]=u(m);function f(e){let{__scopeCheckbox:s,checked:t,children:r,defaultChecked:l,disabled:n,form:d,name:o,onCheckedChange:h,required:x,value:u="on",internal_do_not_use_render:j}=e,[v,f]=(0,c.T)({prop:t,defaultProp:l??!1,onChange:h,caller:m}),[y,g]=i.useState(null),[N,C]=i.useState(null),b=i.useRef(!1),w=!y||!!d||!!y.closest("form"),k={checked:v,disabled:n,setChecked:f,control:y,setControl:g,name:o,form:d,value:u,hasConsumerStoppedPropagationRef:b,required:x,defaultChecked:!A(l)&&l,isFormControl:w,bubbleInput:N,setBubbleInput:C};return(0,a.jsx)(p,{scope:s,...k,children:"function"==typeof j?j(k):r})}var y="CheckboxTrigger",g=i.forwardRef(({__scopeCheckbox:e,onKeyDown:s,onClick:t,...l},c)=>{let{control:d,value:o,disabled:h,checked:m,required:u,setControl:j,setChecked:p,hasConsumerStoppedPropagationRef:f,isFormControl:g,bubbleInput:N}=v(y,e),C=(0,r.e)(c,j),b=i.useRef(m);return i.useEffect(()=>{let e=d?.form;if(e){let s=()=>p(b.current);return e.addEventListener("reset",s),()=>e.removeEventListener("reset",s)}},[d,p]),(0,a.jsx)(x.WV.button,{type:"button",role:"checkbox","aria-checked":A(m)?"mixed":m,"aria-required":u,"data-state":E(m),"data-disabled":h?"":void 0,disabled:h,value:o,...l,ref:C,onKeyDown:(0,n.M)(s,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,n.M)(t,e=>{p(e=>!!A(e)||!e),N&&g&&(f.current=e.isPropagationStopped(),f.current||e.stopPropagation())})})});g.displayName=y;var N=i.forwardRef((e,s)=>{let{__scopeCheckbox:t,name:i,checked:r,defaultChecked:l,required:n,disabled:c,value:d,onCheckedChange:o,form:h,...x}=e;return(0,a.jsx)(f,{__scopeCheckbox:t,checked:r,defaultChecked:l,disabled:c,required:n,onCheckedChange:o,name:i,form:h,value:d,internal_do_not_use_render:({isFormControl:e})=>(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g,{...x,ref:s,__scopeCheckbox:t}),e&&(0,a.jsx)(k,{__scopeCheckbox:t})]})})});N.displayName=m;var C="CheckboxIndicator",b=i.forwardRef((e,s)=>{let{__scopeCheckbox:t,forceMount:i,...r}=e,l=v(C,t);return(0,a.jsx)(h.z,{present:i||A(l.checked)||!0===l.checked,children:(0,a.jsx)(x.WV.span,{"data-state":E(l.checked),"data-disabled":l.disabled?"":void 0,...r,ref:s,style:{pointerEvents:"none",...e.style}})})});b.displayName=C;var w="CheckboxBubbleInput",k=i.forwardRef(({__scopeCheckbox:e,...s},t)=>{let{control:l,hasConsumerStoppedPropagationRef:n,checked:c,defaultChecked:h,required:m,disabled:u,name:j,value:p,form:f,bubbleInput:y,setBubbleInput:g}=v(w,e),N=(0,r.e)(t,g),C=(0,d.D)(c),b=(0,o.t)(l);i.useEffect(()=>{if(!y)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,s=!n.current;if(C!==c&&e){let t=new Event("click",{bubbles:s});y.indeterminate=A(c),e.call(y,!A(c)&&c),y.dispatchEvent(t)}},[y,C,c,n]);let k=i.useRef(!A(c)&&c);return(0,a.jsx)(x.WV.input,{type:"checkbox","aria-hidden":!0,defaultChecked:h??k.current,required:m,disabled:u,name:j,value:p,form:f,...s,tabIndex:-1,ref:N,style:{...s.style,...b,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function A(e){return"indeterminate"===e}function E(e){return A(e)?"indeterminate":e?"checked":"unchecked"}k.displayName=w;var F=t(62312),M=t(91626);let Z=i.forwardRef(({className:e,...s},t)=>a.jsx(N,{ref:t,className:(0,M.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...s,children:a.jsx(b,{className:(0,M.cn)("flex items-center justify-center text-current"),children:a.jsx(F.Z,{className:"h-4 w-4"})})}));Z.displayName=N.displayName},25545:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},48411:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},96885:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},17418:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},71206:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},40626:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},31498:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},76755:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},66985:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Thermometer",[["path",{d:"M14 4v10.54a4 4 0 1 1-4 0V4a2 2 0 0 1 4 0Z",key:"17jzev"}]])},46064:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},15366:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("UserCheck",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]])},25627:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>r,__esModule:()=>i,default:()=>l});let a=(0,t(86843).createProxy)(String.raw`C:\proj\nextjs-saas\app\dashboard\leads\page.tsx`),{__esModule:i,$$typeof:r}=a,l=a.default}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[1638,7948,6671,4626,7792,2506,8830,7150,3117,2125,5045,5232,8008],()=>t(35777));module.exports=a})();