'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Target,
  TrendingUp,
  TrendingDown,
  Thermometer,
  CheckCircle,
  AlertCircle,
  Clock,
  Edit,
  BarChart3,
  Lightbulb,
  History
} from 'lucide-react'
import { toast } from 'react-hot-toast'

interface ScoreBreakdown {
  category: string
  points: number
  maxPoints: number
  description: string
}

interface QualificationStatus {
  isQualified: boolean
  qualificationScore: number
  criteria: Record<string, boolean>
  missingCriteria: string[]
}

interface Recommendation {
  type: string
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
  message: string
  action: string
}

interface ScoreHistory {
  id: string
  previousScore: number
  newScore: number
  changeReason: string
  isManual: boolean
  createdAt: string
  createdBy: {
    id: string
    name: string | null
    email: string
  }
}

interface LeadScoringData {
  leadId: string
  currentScore: number
  automatedScore: number
  temperature: 'HOT' | 'WARM' | 'COLD'
  qualificationStatus: QualificationStatus
  scoreBreakdown: ScoreBreakdown[]
  scoreHistory: ScoreHistory[]
  recommendations: Recommendation[]
}

interface LeadScoringProps {
  leadId: string
}

export function LeadScoring({ leadId }: LeadScoringProps) {
  const [data, setData] = useState<LeadScoringData | null>(null)
  const [loading, setLoading] = useState(true)
  const [showEditScore, setShowEditScore] = useState(false)
  const [showHistory, setShowHistory] = useState(false)
  const [manualScore, setManualScore] = useState('')
  const [reason, setReason] = useState('')
  const [qualificationNotes, setQualificationNotes] = useState('')
  const [updating, setUpdating] = useState(false)

  const fetchScoringData = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/leads/${leadId}/score`)
      if (!response.ok) {
        throw new Error('Failed to fetch scoring data')
      }

      const scoringData = await response.json()
      setData(scoringData)
    } catch (error) {
      toast.error('Failed to load scoring data')
      console.error('Error fetching scoring data:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (leadId) {
      fetchScoringData()
    }
  }, [leadId])

  const handleUpdateScore = async () => {
    if (!manualScore && !qualificationNotes) {
      toast.error('Please provide a score or qualification notes')
      return
    }

    try {
      setUpdating(true)
      const response = await fetch(`/api/leads/${leadId}/score`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          manualScore: manualScore ? parseInt(manualScore) : undefined,
          reason,
          qualificationNotes
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update score')
      }

      toast.success('Lead score updated successfully')
      setShowEditScore(false)
      setManualScore('')
      setReason('')
      setQualificationNotes('')
      fetchScoringData()
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to update score')
    } finally {
      setUpdating(false)
    }
  }

  const getTemperatureColor = (temperature: string) => {
    switch (temperature) {
      case 'HOT':
        return 'text-red-600 bg-red-100'
      case 'WARM':
        return 'text-orange-600 bg-orange-100'
      case 'COLD':
        return 'text-blue-600 bg-blue-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT':
        return 'text-red-600 bg-red-100'
      case 'HIGH':
        return 'text-orange-600 bg-orange-100'
      case 'MEDIUM':
        return 'text-yellow-600 bg-yellow-100'
      case 'LOW':
        return 'text-green-600 bg-green-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!data) {
    return (
      <div className="text-center py-8 text-gray-500">
        <AlertCircle className="h-12 w-12 mx-auto mb-4 text-gray-300" />
        <p>Failed to load scoring data</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Score Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-blue-100 rounded-full">
                <Target className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Current Score</p>
                <p className="text-2xl font-bold text-gray-900">{data.currentScore}/100</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-purple-100 rounded-full">
                <BarChart3 className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Automated Score</p>
                <p className="text-2xl font-bold text-gray-900">{data.automatedScore}/100</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-orange-100 rounded-full">
                <Thermometer className="h-6 w-6 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Temperature</p>
                <Badge className={`${getTemperatureColor(data.temperature)} text-sm`}>
                  {data.temperature}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Actions */}
      <div className="flex items-center space-x-2">
        <Button onClick={() => setShowEditScore(true)} variant="outline">
          <Edit className="h-4 w-4 mr-2" />
          Update Score
        </Button>
        <Button onClick={() => setShowHistory(true)} variant="outline">
          <History className="h-4 w-4 mr-2" />
          Score History
        </Button>
      </div>

      {/* Qualification Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <CheckCircle className="h-5 w-5 mr-2" />
              Qualification Status
            </div>
            <Badge className={data.qualificationStatus.isQualified ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
              {data.qualificationStatus.isQualified ? 'QUALIFIED' : 'NOT QUALIFIED'}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Qualification Score</span>
              <span className="text-lg font-bold text-gray-900">
                {data.qualificationStatus.qualificationScore}%
              </span>
            </div>

            {/* Progress Bar */}
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div
                className={`h-3 rounded-full ${
                  data.qualificationStatus.qualificationScore >= 60
                    ? 'bg-green-600'
                    : data.qualificationStatus.qualificationScore >= 40
                    ? 'bg-yellow-600'
                    : 'bg-red-600'
                }`}
                style={{ width: `${data.qualificationStatus.qualificationScore}%` }}
              ></div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(data.qualificationStatus.criteria).map(([criterion, met]) => (
                <div key={criterion} className="flex items-center space-x-2">
                  {met ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <AlertCircle className="h-4 w-4 text-red-600" />
                  )}
                  <span className={`text-sm ${met ? 'text-green-700' : 'text-red-700'}`}>
                    {criterion.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                  </span>
                </div>
              ))}
            </div>

            {/* Missing Criteria */}
            {data.qualificationStatus.missingCriteria.length > 0 && (
              <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p className="text-sm font-medium text-yellow-800 mb-2">Missing Qualification Criteria:</p>
                <ul className="text-sm text-yellow-700 space-y-1">
                  {data.qualificationStatus.missingCriteria.map((criterion) => (
                    <li key={criterion} className="flex items-center space-x-2">
                      <AlertCircle className="h-3 w-3" />
                      <span>{criterion.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Score Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>Score Breakdown</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {data.scoreBreakdown.map((item, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">{item.category}</span>
                  <span className="text-sm text-gray-500">
                    {item.points}/{item.maxPoints} points
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full"
                    style={{ width: `${(item.points / item.maxPoints) * 100}%` }}
                  ></div>
                </div>
                <p className="text-xs text-gray-500">{item.description}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recommendations */}
      {data.recommendations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Lightbulb className="h-5 w-5 mr-2" />
              Recommendations
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {data.recommendations.map((rec, index) => (
                <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                  <Badge className={`${getPriorityColor(rec.priority)} text-xs`}>
                    {rec.priority}
                  </Badge>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">{rec.message}</p>
                    <p className="text-xs text-gray-500 mt-1">Action: {rec.action}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Edit Score Dialog */}
      <Dialog open={showEditScore} onOpenChange={setShowEditScore}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Update Lead Score</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="manualScore">Manual Score (0-100)</Label>
              <Input
                id="manualScore"
                type="number"
                min="0"
                max="100"
                value={manualScore}
                onChange={(e) => setManualScore(e.target.value)}
                placeholder="Enter score"
              />
            </div>
            <div>
              <Label htmlFor="reason">Reason for Change</Label>
              <Input
                id="reason"
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                placeholder="Why are you changing the score?"
              />
            </div>
            <div>
              <Label htmlFor="qualificationNotes">Qualification Notes</Label>
              <Textarea
                id="qualificationNotes"
                value={qualificationNotes}
                onChange={(e) => setQualificationNotes(e.target.value)}
                placeholder="Add qualification notes..."
                rows={3}
              />
            </div>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setShowEditScore(false)}>
                Cancel
              </Button>
              <Button onClick={handleUpdateScore} disabled={updating}>
                {updating ? 'Updating...' : 'Update Score'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Score History Dialog */}
      <Dialog open={showHistory} onOpenChange={setShowHistory}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Score History</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {data.scoreHistory.length === 0 ? (
              <p className="text-center text-gray-500 py-8">No score history available</p>
            ) : (
              data.scoreHistory.map((entry) => (
                <div key={entry.id} className="flex items-start space-x-3 p-3 border rounded-lg">
                  <div className="flex-shrink-0">
                    {entry.newScore > entry.previousScore ? (
                      <TrendingUp className="h-5 w-5 text-green-600" />
                    ) : entry.newScore < entry.previousScore ? (
                      <TrendingDown className="h-5 w-5 text-red-600" />
                    ) : (
                      <Target className="h-5 w-5 text-gray-600" />
                    )}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <span className="font-medium">
                        {entry.previousScore} → {entry.newScore}
                      </span>
                      <Badge variant={entry.isManual ? 'default' : 'secondary'} className="text-xs">
                        {entry.isManual ? 'Manual' : 'Automatic'}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600">{entry.changeReason}</p>
                    <div className="flex items-center space-x-2 mt-2 text-xs text-gray-500">
                      <Clock className="h-3 w-3" />
                      <span>{new Date(entry.createdAt).toLocaleString()}</span>
                      <span>by {entry.createdBy.name || entry.createdBy.email}</span>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
