"use strict";(()=>{var e={};e.id=3654,e.ids=[3654],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6113:e=>{e.exports=require("crypto")},77817:(e,a,r)=>{r.r(a),r.d(a,{headerHooks:()=>I,originalPathname:()=>A,patchFetch:()=>T,requestAsyncStorage:()=>E,routeModule:()=>w,serverHooks:()=>g,staticGenerationAsyncStorage:()=>N,staticGenerationBailout:()=>h});var t={};r.r(t),r.d(t,{POST:()=>f});var n=r(95419),i=r(69108),o=r(99678),s=r(78070),d=r(6521),u=r.n(d),m=r(9108),l=r(25252),c=r(52178),p=r(11896);let y=l.Ry({firstName:l.Z_().min(1,"First name is required"),lastName:l.Z_().min(1,"Last name is required"),email:l.Z_().email("Invalid email address"),password:l.Z_().min(8,"Password must be at least 8 characters").regex(/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,"Password must contain at least one uppercase letter, one lowercase letter, and one number"),companyName:l.Z_().min(1,"Company name is required"),companySize:l.Km(["STARTUP","SMALL","MEDIUM","LARGE","ENTERPRISE"]),industry:l.Km(["TECHNOLOGY","FINANCE","HEALTHCARE","MANUFACTURING","RETAIL","EDUCATION","CONSULTING","REAL_ESTATE","OTHER"]),agreeToMarketing:l.O7().default(!1)});async function f(e){try{let a=await e.json(),r=y.parse(a);if(await m._.user.findUnique({where:{email:r.email}}))return s.Z.json({error:"User already exists"},{status:400});let t=await u().hash(r.password,12),n=(0,p.cl)(),i=new Date(Date.now()+864e5),o=await m._.$transaction(async e=>{let a=await e.company.create({data:{name:r.companyName,size:r.companySize,industry:r.industry,status:"TRIAL",trialEndsAt:new Date(Date.now()+12096e5),settings:{allowUserInvitations:!0,requireEmailVerification:!0,maxUsers:function(e){switch(e){case"STARTUP":default:return 5;case"SMALL":return 25;case"MEDIUM":return 100;case"LARGE":return 500;case"ENTERPRISE":return 1e3}}(r.companySize)}}}),o=await e.user.create({data:{firstName:r.firstName,lastName:r.lastName,name:`${r.firstName} ${r.lastName}`,email:r.email,password:t,role:"OWNER",companyId:a.id,emailVerified:!1,emailVerificationToken:n,emailVerificationExpires:i,marketingOptIn:r.agreeToMarketing,onboardingCompleted:!1}});return await e.company.update({where:{id:a.id},data:{ownerId:o.id}}),await e.leadStatus.createMany({data:[{name:"New",color:"#3B82F6",order:1,companyId:a.id},{name:"Contacted",color:"#8B5CF6",order:2,companyId:a.id},{name:"Qualified",color:"#F59E0B",order:3,companyId:a.id},{name:"Proposal",color:"#10B981",order:4,companyId:a.id},{name:"Negotiation",color:"#EF4444",order:5,companyId:a.id},{name:"Converted",color:"#059669",order:6,companyId:a.id},{name:"Lost",color:"#6B7280",order:7,companyId:a.id}]}),await e.task.create({data:{title:"Welcome to Business SaaS!",description:"Complete your profile setup and explore the dashboard features.",status:"PENDING",priority:"HIGH",dueDate:new Date(Date.now()+6048e5),assignedToId:o.id,companyId:a.id,createdById:o.id,type:"ONBOARDING"}}),{user:o,company:a}});try{await (0,p.zk)(r.email,`${r.firstName} ${r.lastName}`,n)}catch(e){console.error("Failed to send verification email:",e)}return console.log(`New user signup: ${r.email} for company: ${r.companyName}`),s.Z.json({message:"Account created successfully",user:{id:o.user.id,email:o.user.email,name:o.user.name,emailVerified:o.user.emailVerified},company:{id:o.company.id,name:o.company.name,status:o.company.status}},{status:201})}catch(e){if(e instanceof c.jm)return s.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("Signup error:",e),s.Z.json({error:"Internal server error"},{status:500})}}let w=new n.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/auth/signup/route",pathname:"/api/auth/signup",filename:"route",bundlePath:"app/api/auth/signup/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\auth\\signup\\route.ts",nextConfigOutput:"",userland:t}),{requestAsyncStorage:E,staticGenerationAsyncStorage:N,serverHooks:g,headerHooks:I,staticGenerationBailout:h}=w,A="/api/auth/signup/route";function T(){return(0,o.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:N})}},11896:(e,a,r)=>{r.d(a,{LS:()=>u,cl:()=>o,uE:()=>s,zk:()=>d});var t=r(6113),n=r.n(t);let i={from:process.env.EMAIL_FROM||"<EMAIL>",baseUrl:"http://localhost:3000"};function o(){return n().randomBytes(32).toString("hex")}function s(){return n().randomBytes(32).toString("hex")}async function d(e,a,r){i.baseUrl,i.from,console.log("\uD83D\uDCE7 Email would be sent to:",e)}async function u(e,a,r){i.baseUrl,i.from,console.log("\uD83D\uDCE7 Password reset email would be sent to:",e)}},9108:(e,a,r)=>{r.d(a,{_:()=>n});let t=require("@prisma/client"),n=globalThis.prisma??new t.PrismaClient}};var a=require("../../../../webpack-runtime.js");a.C(e);var r=e=>a(a.s=e),t=a.X(0,[1638,6206,6521,5252],()=>r(77817));module.exports=t})();