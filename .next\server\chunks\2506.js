exports.id=2506,exports.ids=[2506],exports.modules={45904:(e,t,n)=>{"use strict";n.d(t,{Ry:()=>c});var r=new WeakMap,o=new WeakMap,a={},i=0,l=function(e){return e&&(e.host||l(e.parentNode))},u=function(e,t,n,u){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=l(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});a[n]||(a[n]=new WeakMap);var s=a[n],d=[],f=new Set,p=new Set(c),h=function(e){!e||f.has(e)||(f.add(e),h(e.parentNode))};c.forEach(h);var m=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))m(e);else try{var t=e.getAttribute(u),a=null!==t&&"false"!==t,i=(r.get(e)||0)+1,l=(s.get(e)||0)+1;r.set(e,i),s.set(e,l),d.push(e),1===i&&a&&o.set(e,!0),1===l&&e.setAttribute(n,"true"),a||e.setAttribute(u,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),f.clear(),i++,function(){d.forEach(function(e){var t=r.get(e)-1,a=s.get(e)-1;r.set(e,t),s.set(e,a),t||(o.has(e)||e.removeAttribute(u),o.delete(e)),a||e.removeAttribute(n)}),--i||(r=new WeakMap,r=new WeakMap,o=new WeakMap,a={})}},c=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r,o=Array.from(Array.isArray(e)?e:[e]),a=t||(r=e,"undefined"==typeof document?null:(Array.isArray(r)?r[0]:r).ownerDocument.body);return a?(o.push.apply(o,Array.from(a.querySelectorAll("[aria-live], script"))),u(o,a,n,"aria-hidden")):function(){return null}}},50340:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},33037:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},62312:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},71532:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},97751:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},82958:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},1750:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("Crown",[["path",{d:"m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7zm3 16h14",key:"zkxr6b"}]])},37121:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]])},2273:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},48120:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},98200:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},28765:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},13746:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},23485:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},18822:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},89895:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},14513:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},22254:(e,t,n)=>{e.exports=n(14767)},71210:(e,t,n)=>{"use strict";n.d(t,{Z:()=>F});var r,o=function(){return(o=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function a(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create,Object.create;var i=("function"==typeof SuppressedError&&SuppressedError,n(3729)),l="right-scroll-bar-position",u="width-before-scroll-bar";function c(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var s=i.useEffect,d=new WeakMap;function f(e){return e}var p=function(e){void 0===e&&(e={});var t,n,r,a=(void 0===t&&(t=f),n=[],r=!1,{read:function(){if(r)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var o=t(e,r);return n.push(o),function(){n=n.filter(function(e){return e!==o})}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var o=n;n=[],o.forEach(e),t=n}var a=function(){var n=t;t=[],n.forEach(e)},i=function(){return Promise.resolve().then(a)};i(),n={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),n}}}});return a.options=o({async:!0,ssr:!1},e),a}(),h=function(){},m=i.forwardRef(function(e,t){var n,r,l,u,f=i.useRef(null),m=i.useState({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:h}),v=m[0],g=m[1],y=e.forwardProps,w=e.children,x=e.className,b=e.removeScrollBar,E=e.enabled,M=e.shards,C=e.sideCar,k=e.noRelative,R=e.noIsolation,S=e.inert,A=e.allowPinchZoom,P=e.as,D=e.gapMode,j=a(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),L=(n=[f,t],r=function(e){return n.forEach(function(t){return c(t,e)})},(l=(0,i.useState)(function(){return{value:null,callback:r,facade:{get current(){return l.value},set current(value){var e=l.value;e!==value&&(l.value=value,l.callback(value,e))}}}})[0]).callback=r,u=l.facade,s(function(){var e=d.get(u);if(e){var t=new Set(e),r=new Set(n),o=u.current;t.forEach(function(e){r.has(e)||c(e,null)}),r.forEach(function(e){t.has(e)||c(e,o)})}d.set(u,n)},[n]),u),T=o(o({},j),v);return i.createElement(i.Fragment,null,E&&i.createElement(C,{sideCar:p,removeScrollBar:b,shards:M,noRelative:k,noIsolation:R,inert:S,setCallbacks:g,allowPinchZoom:!!A,lockRef:f,gapMode:D}),y?i.cloneElement(i.Children.only(w),o(o({},T),{ref:L})):i.createElement(void 0===P?"div":P,o({},T,{className:x,ref:L}),w))});m.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},m.classNames={fullWidth:u,zeroRight:l};var v=function(e){var t=e.sideCar,n=a(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return i.createElement(r,o({},n))};v.isSideCarExport=!0;var g=function(){var e=0,t=null;return{add:function(o){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=r||n.nc;return t&&e.setAttribute("nonce",t),e}())){var a,i;(a=t).styleSheet?a.styleSheet.cssText=o:a.appendChild(document.createTextNode(o)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},y=function(){var e=g();return function(t,n){i.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},w=function(){var e=y();return function(t){return e(t.styles,t.dynamic),null}},x={left:0,top:0,right:0,gap:0},b=w(),E="data-scroll-locked",M=function(e,t,n,r){var o=e.left,a=e.top,i=e.right,c=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(c,"px ").concat(r,";\n  }\n  body[").concat(E,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(c,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(c,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(l," {\n    right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(u," {\n    margin-right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(l," .").concat(l," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(E,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(c,"px;\n  }\n")},C=function(){var e=parseInt(document.body.getAttribute(E)||"0",10);return isFinite(e)?e:0},k=function(){i.useEffect(function(){return document.body.setAttribute(E,(C()+1).toString()),function(){var e=C()-1;e<=0?document.body.removeAttribute(E):document.body.setAttribute(E,e.toString())}},[])},R=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;k();var a=i.useMemo(function(){return x},[o]);return i.createElement(b,{styles:M(a,!t,o,n?"":"!important")})},S=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===n[t])},A=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),P(e,r)){var o=D(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},P=function(e,t){return"v"===e?S(t,"overflowY"):S(t,"overflowX")},D=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},j=function(e,t,n,r,o){var a,i=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),l=i*r,u=n.target,c=t.contains(u),s=!1,d=l>0,f=0,p=0;do{if(!u)break;var h=D(e,u),m=h[0],v=h[1]-h[2]-i*m;(m||v)&&P(e,u)&&(f+=v,p+=m);var g=u.parentNode;u=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&l>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-l>p)&&(s=!0),s},L=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},T=function(e){return[e.deltaX,e.deltaY]},O=function(e){return e&&"current"in e?e.current:e},N=0,I=[];let W=(p.useMedium(function(e){var t=i.useRef([]),n=i.useRef([0,0]),r=i.useRef(),o=i.useState(N++)[0],a=i.useState(w)[0],l=i.useRef(e);i.useEffect(function(){l.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(O),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=i.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var o,a=L(e),i=n.current,u="deltaX"in e?e.deltaX:i[0]-a[0],c="deltaY"in e?e.deltaY:i[1]-a[1],s=e.target,d=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=A(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=A(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return j(p,t,e,"h"===p?u:c,!0)},[]),c=i.useCallback(function(e){if(I.length&&I[I.length-1]===a){var n="deltaY"in e?T(e):L(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(l.current.shards||[]).map(O).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=i.useCallback(function(e,n,r,o){var a={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),d=i.useCallback(function(e){n.current=L(e),r.current=void 0},[]),f=i.useCallback(function(t){s(t.type,T(t),t.target,u(t,e.lockRef.current))},[]),p=i.useCallback(function(t){s(t.type,L(t),t.target,u(t,e.lockRef.current))},[]);i.useEffect(function(){return I.push(a),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,!1),document.addEventListener("touchmove",c,!1),document.addEventListener("touchstart",d,!1),function(){I=I.filter(function(e){return e!==a}),document.removeEventListener("wheel",c,!1),document.removeEventListener("touchmove",c,!1),document.removeEventListener("touchstart",d,!1)}},[]);var h=e.removeScrollBar,m=e.inert;return i.createElement(i.Fragment,null,m?i.createElement(a,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?i.createElement(R,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}),v);var _=i.forwardRef(function(e,t){return i.createElement(m,o({},e,{ref:t,sideCar:W}))});_.classNames=m.classNames;let F=_},30080:(e,t,n)=>{"use strict";/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=n(3729);"function"==typeof Object.is&&Object.is,r.useState,r.useEffect,r.useLayoutEffect,r.useDebugValue,t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:function(e,t){return t()}},8145:(e,t,n)=>{"use strict";e.exports=n(30080)},15480:(e,t,n)=>{"use strict";n.d(t,{NY:()=>C,Ee:()=>M,fC:()=>E});var r=n(3729),o=n(98462),a=n(2256),i=n(16069),l=n(62409),u=n(8145);function c(){return()=>{}}var s=n(95344),d="Avatar",[f,p]=(0,o.b)(d),[h,m]=f(d),v=r.forwardRef((e,t)=>{let{__scopeAvatar:n,...o}=e,[a,i]=r.useState("idle");return(0,s.jsx)(h,{scope:n,imageLoadingStatus:a,onImageLoadingStatusChange:i,children:(0,s.jsx)(l.WV.span,{...o,ref:t})})});v.displayName=d;var g="AvatarImage",y=r.forwardRef((e,t)=>{let{__scopeAvatar:n,src:o,onLoadingStatusChange:d=()=>{},...f}=e,p=m(g,n),h=function(e,{referrerPolicy:t,crossOrigin:n}){let o=(0,u.useSyncExternalStore)(c,()=>!0,()=>!1),a=r.useRef(null),l=o?(a.current||(a.current=new window.Image),a.current):null,[s,d]=r.useState(()=>b(l,e));return(0,i.b)(()=>{d(b(l,e))},[l,e]),(0,i.b)(()=>{let e=e=>()=>{d(e)};if(!l)return;let r=e("loaded"),o=e("error");return l.addEventListener("load",r),l.addEventListener("error",o),t&&(l.referrerPolicy=t),"string"==typeof n&&(l.crossOrigin=n),()=>{l.removeEventListener("load",r),l.removeEventListener("error",o)}},[l,n,t]),s}(o,f),v=(0,a.W)(e=>{d(e),p.onImageLoadingStatusChange(e)});return(0,i.b)(()=>{"idle"!==h&&v(h)},[h,v]),"loaded"===h?(0,s.jsx)(l.WV.img,{...f,ref:t,src:o}):null});y.displayName=g;var w="AvatarFallback",x=r.forwardRef((e,t)=>{let{__scopeAvatar:n,delayMs:o,...a}=e,i=m(w,n),[u,c]=r.useState(void 0===o);return r.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>c(!0),o);return()=>window.clearTimeout(e)}},[o]),u&&"loaded"!==i.imageLoadingStatus?(0,s.jsx)(l.WV.span,{...a,ref:t}):null});function b(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}x.displayName=w;var E=v,M=y,C=x},44155:(e,t,n)=>{"use strict";n.d(t,{XB:()=>f});var r,o=n(3729),a=n(85222),i=n(62409),l=n(31405),u=n(2256),c=n(95344),s="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:f,onPointerDownOutside:m,onFocusOutside:v,onInteractOutside:g,onDismiss:y,...w}=e,x=o.useContext(d),[b,E]=o.useState(null),M=b?.ownerDocument??globalThis?.document,[,C]=o.useState({}),k=(0,l.e)(t,e=>E(e)),R=Array.from(x.layers),[S]=[...x.layersWithOutsidePointerEventsDisabled].slice(-1),A=R.indexOf(S),P=b?R.indexOf(b):-1,D=x.layersWithOutsidePointerEventsDisabled.size>0,j=P>=A,L=function(e,t=globalThis?.document){let n=(0,u.W)(e),r=o.useRef(!1),a=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){h("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",a.current),a.current=r,t.addEventListener("click",a.current,{once:!0})):r()}else t.removeEventListener("click",a.current);r.current=!1},o=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(o),t.removeEventListener("pointerdown",e),t.removeEventListener("click",a.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...x.branches].some(e=>e.contains(t));!j||n||(m?.(e),g?.(e),e.defaultPrevented||y?.())},M),T=function(e,t=globalThis?.document){let n=(0,u.W)(e),r=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!r.current&&h("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;[...x.branches].some(e=>e.contains(t))||(v?.(e),g?.(e),e.defaultPrevented||y?.())},M);return function(e,t=globalThis?.document){let n=(0,u.W)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{P!==x.layers.size-1||(f?.(e),!e.defaultPrevented&&y&&(e.preventDefault(),y()))},M),o.useEffect(()=>{if(b)return n&&(0===x.layersWithOutsidePointerEventsDisabled.size&&(r=M.body.style.pointerEvents,M.body.style.pointerEvents="none"),x.layersWithOutsidePointerEventsDisabled.add(b)),x.layers.add(b),p(),()=>{n&&1===x.layersWithOutsidePointerEventsDisabled.size&&(M.body.style.pointerEvents=r)}},[b,M,n,x]),o.useEffect(()=>()=>{b&&(x.layers.delete(b),x.layersWithOutsidePointerEventsDisabled.delete(b),p())},[b,x]),o.useEffect(()=>{let e=()=>C({});return document.addEventListener(s,e),()=>document.removeEventListener(s,e)},[]),(0,c.jsx)(i.WV.div,{...w,ref:k,style:{pointerEvents:D?j?"auto":"none":void 0,...e.style},onFocusCapture:(0,a.M)(e.onFocusCapture,T.onFocusCapture),onBlurCapture:(0,a.M)(e.onBlurCapture,T.onBlurCapture),onPointerDownCapture:(0,a.M)(e.onPointerDownCapture,L.onPointerDownCapture)})});function p(){let e=new CustomEvent(s);document.dispatchEvent(e)}function h(e,t,n,{discrete:r}){let o=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?(0,i.jH)(o,a):o.dispatchEvent(a)}f.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(d),r=o.useRef(null),a=(0,l.e)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,c.jsx)(i.WV.div,{...e,ref:a})}).displayName="DismissableLayerBranch"},28473:(e,t,n)=>{"use strict";n.d(t,{oC:()=>e6,VY:()=>e4,ZA:()=>e3,ck:()=>e5,wU:()=>te,__:()=>e7,Uv:()=>e2,Ee:()=>e9,Rk:()=>e8,fC:()=>e0,Z0:()=>tt,Tr:()=>tn,tu:()=>to,fF:()=>tr,xz:()=>e1});var r=n(3729),o=n(85222),a=n(31405),i=n(98462),l=n(33183),u=n(62409),c=n(77411),s=n(3975),d=n(44155),f=n(1106),p=n(27386),h=n(99048),m=n(37574),v=n(31179),g=n(43234),y=n(34504),w=n(32751),x=n(2256),b=n(45904),E=n(71210),M=n(95344),C=["Enter"," "],k=["ArrowUp","PageDown","End"],R=["ArrowDown","PageUp","Home",...k],S={ltr:[...C,"ArrowRight"],rtl:[...C,"ArrowLeft"]},A={ltr:["ArrowLeft"],rtl:["ArrowRight"]},P="Menu",[D,j,L]=(0,c.B)(P),[T,O]=(0,i.b)(P,[L,m.D7,y.Pc]),N=(0,m.D7)(),I=(0,y.Pc)(),[W,_]=T(P),[F,Z]=T(P),V=e=>{let{__scopeMenu:t,open:n=!1,children:o,dir:a,onOpenChange:i,modal:l=!0}=e,u=N(t),[c,d]=r.useState(null),f=r.useRef(!1),p=(0,x.W)(i),h=(0,s.gm)(a);return r.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,M.jsx)(m.fC,{...u,children:(0,M.jsx)(W,{scope:t,open:n,onOpenChange:p,content:c,onContentChange:d,children:(0,M.jsx)(F,{scope:t,onClose:r.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:h,modal:l,children:o})})})};V.displayName=P;var B=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=N(n);return(0,M.jsx)(m.ee,{...o,...r,ref:t})});B.displayName="MenuAnchor";var z="MenuPortal",[H,K]=T(z,{forceMount:void 0}),X=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:o}=e,a=_(z,t);return(0,M.jsx)(H,{scope:t,forceMount:n,children:(0,M.jsx)(g.z,{present:n||a.open,children:(0,M.jsx)(v.h,{asChild:!0,container:o,children:r})})})};X.displayName=z;var Y="MenuContent",[U,$]=T(Y),q=r.forwardRef((e,t)=>{let n=K(Y,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,a=_(Y,e.__scopeMenu),i=Z(Y,e.__scopeMenu);return(0,M.jsx)(D.Provider,{scope:e.__scopeMenu,children:(0,M.jsx)(g.z,{present:r||a.open,children:(0,M.jsx)(D.Slot,{scope:e.__scopeMenu,children:i.modal?(0,M.jsx)(G,{...o,ref:t}):(0,M.jsx)(J,{...o,ref:t})})})})}),G=r.forwardRef((e,t)=>{let n=_(Y,e.__scopeMenu),i=r.useRef(null),l=(0,a.e)(t,i);return r.useEffect(()=>{let e=i.current;if(e)return(0,b.Ry)(e)},[]),(0,M.jsx)(ee,{...e,ref:l,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),J=r.forwardRef((e,t)=>{let n=_(Y,e.__scopeMenu);return(0,M.jsx)(ee,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),Q=(0,w.Z8)("MenuContent.ScrollLock"),ee=r.forwardRef((e,t)=>{let{__scopeMenu:n,loop:i=!1,trapFocus:l,onOpenAutoFocus:u,onCloseAutoFocus:c,disableOutsidePointerEvents:s,onEntryFocus:h,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:w,onInteractOutside:x,onDismiss:b,disableOutsideScroll:C,...S}=e,A=_(Y,n),P=Z(Y,n),D=N(n),L=I(n),T=j(n),[O,W]=r.useState(null),F=r.useRef(null),V=(0,a.e)(t,F,A.onContentChange),B=r.useRef(0),z=r.useRef(""),H=r.useRef(0),K=r.useRef(null),X=r.useRef("right"),$=r.useRef(0),q=C?E.Z:r.Fragment,G=e=>{let t=z.current+e,n=T().filter(e=>!e.disabled),r=document.activeElement,o=n.find(e=>e.ref.current===r)?.textValue,a=function(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=(r=Math.max(n?e.indexOf(n):-1,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(a=a.filter(e=>e!==n));let i=a.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return i!==n?i:void 0}(n.map(e=>e.textValue),t,o),i=n.find(e=>e.textValue===a)?.ref.current;(function e(t){z.current=t,window.clearTimeout(B.current),""!==t&&(B.current=window.setTimeout(()=>e(""),1e3))})(t),i&&setTimeout(()=>i.focus())};r.useEffect(()=>()=>window.clearTimeout(B.current),[]),(0,f.EW)();let J=r.useCallback(e=>X.current===K.current?.side&&function(e,t){return!!t&&function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,a=t.length-1;e<t.length;a=e++){let i=t[e],l=t[a],u=i.x,c=i.y,s=l.x,d=l.y;c>r!=d>r&&n<(s-u)*(r-c)/(d-c)+u&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,K.current?.area),[]);return(0,M.jsx)(U,{scope:n,searchRef:z,onItemEnter:r.useCallback(e=>{J(e)&&e.preventDefault()},[J]),onItemLeave:r.useCallback(e=>{J(e)||(F.current?.focus(),W(null))},[J]),onTriggerLeave:r.useCallback(e=>{J(e)&&e.preventDefault()},[J]),pointerGraceTimerRef:H,onPointerGraceIntentChange:r.useCallback(e=>{K.current=e},[]),children:(0,M.jsx)(q,{...C?{as:Q,allowPinchZoom:!0}:void 0,children:(0,M.jsx)(p.M,{asChild:!0,trapped:l,onMountAutoFocus:(0,o.M)(u,e=>{e.preventDefault(),F.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:c,children:(0,M.jsx)(d.XB,{asChild:!0,disableOutsidePointerEvents:s,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:w,onInteractOutside:x,onDismiss:b,children:(0,M.jsx)(y.fC,{asChild:!0,...L,dir:P.dir,orientation:"vertical",loop:i,currentTabStopId:O,onCurrentTabStopIdChange:W,onEntryFocus:(0,o.M)(h,e=>{P.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,M.jsx)(m.VY,{role:"menu","aria-orientation":"vertical","data-state":eA(A.open),"data-radix-menu-content":"",dir:P.dir,...D,...S,ref:V,style:{outline:"none",...S.style},onKeyDown:(0,o.M)(S.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&G(e.key));let o=F.current;if(e.target!==o||!R.includes(e.key))return;e.preventDefault();let a=T().filter(e=>!e.disabled).map(e=>e.ref.current);k.includes(e.key)&&a.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(a)}),onBlur:(0,o.M)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(B.current),z.current="")}),onPointerMove:(0,o.M)(e.onPointerMove,ej(e=>{let t=e.target,n=$.current!==e.clientX;if(e.currentTarget.contains(t)&&n){let t=e.clientX>$.current?"right":"left";X.current=t,$.current=e.clientX}}))})})})})})})});q.displayName=Y;var et=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,M.jsx)(u.WV.div,{role:"group",...r,ref:t})});et.displayName="MenuGroup";var en=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,M.jsx)(u.WV.div,{...r,ref:t})});en.displayName="MenuLabel";var er="MenuItem",eo="menu.itemSelect",ea=r.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:i,...l}=e,c=r.useRef(null),s=Z(er,e.__scopeMenu),d=$(er,e.__scopeMenu),f=(0,a.e)(t,c),p=r.useRef(!1);return(0,M.jsx)(ei,{...l,ref:f,disabled:n,onClick:(0,o.M)(e.onClick,()=>{let e=c.current;if(!n&&e){let t=new CustomEvent(eo,{bubbles:!0,cancelable:!0});e.addEventListener(eo,e=>i?.(e),{once:!0}),(0,u.jH)(e,t),t.defaultPrevented?p.current=!1:s.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),p.current=!0},onPointerUp:(0,o.M)(e.onPointerUp,e=>{p.current||e.currentTarget?.click()}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{let t=""!==d.searchRef.current;!n&&(!t||" "!==e.key)&&C.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ea.displayName=er;var ei=r.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:i=!1,textValue:l,...c}=e,s=$(er,n),d=I(n),f=r.useRef(null),p=(0,a.e)(t,f),[h,m]=r.useState(!1),[v,g]=r.useState("");return r.useEffect(()=>{let e=f.current;e&&g((e.textContent??"").trim())},[c.children]),(0,M.jsx)(D.ItemSlot,{scope:n,disabled:i,textValue:l??v,children:(0,M.jsx)(y.ck,{asChild:!0,...d,focusable:!i,children:(0,M.jsx)(u.WV.div,{role:"menuitem","data-highlighted":h?"":void 0,"aria-disabled":i||void 0,"data-disabled":i?"":void 0,...c,ref:p,onPointerMove:(0,o.M)(e.onPointerMove,ej(e=>{i?s.onItemLeave(e):(s.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.M)(e.onPointerLeave,ej(e=>s.onItemLeave(e))),onFocus:(0,o.M)(e.onFocus,()=>m(!0)),onBlur:(0,o.M)(e.onBlur,()=>m(!1))})})})}),el=r.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...a}=e;return(0,M.jsx)(em,{scope:e.__scopeMenu,checked:n,children:(0,M.jsx)(ea,{role:"menuitemcheckbox","aria-checked":eP(n)?"mixed":n,...a,ref:t,"data-state":eD(n),onSelect:(0,o.M)(a.onSelect,()=>r?.(!!eP(n)||!n),{checkForDefaultPrevented:!1})})})});el.displayName="MenuCheckboxItem";var eu="MenuRadioGroup",[ec,es]=T(eu,{value:void 0,onValueChange:()=>{}}),ed=r.forwardRef((e,t)=>{let{value:n,onValueChange:r,...o}=e,a=(0,x.W)(r);return(0,M.jsx)(ec,{scope:e.__scopeMenu,value:n,onValueChange:a,children:(0,M.jsx)(et,{...o,ref:t})})});ed.displayName=eu;var ef="MenuRadioItem",ep=r.forwardRef((e,t)=>{let{value:n,...r}=e,a=es(ef,e.__scopeMenu),i=n===a.value;return(0,M.jsx)(em,{scope:e.__scopeMenu,checked:i,children:(0,M.jsx)(ea,{role:"menuitemradio","aria-checked":i,...r,ref:t,"data-state":eD(i),onSelect:(0,o.M)(r.onSelect,()=>a.onValueChange?.(n),{checkForDefaultPrevented:!1})})})});ep.displayName=ef;var eh="MenuItemIndicator",[em,ev]=T(eh,{checked:!1}),eg=r.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...o}=e,a=ev(eh,n);return(0,M.jsx)(g.z,{present:r||eP(a.checked)||!0===a.checked,children:(0,M.jsx)(u.WV.span,{...o,ref:t,"data-state":eD(a.checked)})})});eg.displayName=eh;var ey=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,M.jsx)(u.WV.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});ey.displayName="MenuSeparator";var ew=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=N(n);return(0,M.jsx)(m.Eh,{...o,...r,ref:t})});ew.displayName="MenuArrow";var ex="MenuSub",[eb,eE]=T(ex),eM=e=>{let{__scopeMenu:t,children:n,open:o=!1,onOpenChange:a}=e,i=_(ex,t),l=N(t),[u,c]=r.useState(null),[s,d]=r.useState(null),f=(0,x.W)(a);return r.useEffect(()=>(!1===i.open&&f(!1),()=>f(!1)),[i.open,f]),(0,M.jsx)(m.fC,{...l,children:(0,M.jsx)(W,{scope:t,open:o,onOpenChange:f,content:s,onContentChange:d,children:(0,M.jsx)(eb,{scope:t,contentId:(0,h.M)(),triggerId:(0,h.M)(),trigger:u,onTriggerChange:c,children:n})})})};eM.displayName=ex;var eC="MenuSubTrigger",ek=r.forwardRef((e,t)=>{let n=_(eC,e.__scopeMenu),i=Z(eC,e.__scopeMenu),l=eE(eC,e.__scopeMenu),u=$(eC,e.__scopeMenu),c=r.useRef(null),{pointerGraceTimerRef:s,onPointerGraceIntentChange:d}=u,f={__scopeMenu:e.__scopeMenu},p=r.useCallback(()=>{c.current&&window.clearTimeout(c.current),c.current=null},[]);return r.useEffect(()=>p,[p]),r.useEffect(()=>{let e=s.current;return()=>{window.clearTimeout(e),d(null)}},[s,d]),(0,M.jsx)(B,{asChild:!0,...f,children:(0,M.jsx)(ei,{id:l.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":l.contentId,"data-state":eA(n.open),...e,ref:(0,a.F)(t,l.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,o.M)(e.onPointerMove,ej(t=>{u.onItemEnter(t),t.defaultPrevented||e.disabled||n.open||c.current||(u.onPointerGraceIntentChange(null),c.current=window.setTimeout(()=>{n.onOpenChange(!0),p()},100))})),onPointerLeave:(0,o.M)(e.onPointerLeave,ej(e=>{p();let t=n.content?.getBoundingClientRect();if(t){let r=n.content?.dataset.side,o="right"===r,a=t[o?"left":"right"],i=t[o?"right":"left"];u.onPointerGraceIntentChange({area:[{x:e.clientX+(o?-5:5),y:e.clientY},{x:a,y:t.top},{x:i,y:t.top},{x:i,y:t.bottom},{x:a,y:t.bottom}],side:r}),window.clearTimeout(s.current),s.current=window.setTimeout(()=>u.onPointerGraceIntentChange(null),300)}else{if(u.onTriggerLeave(e),e.defaultPrevented)return;u.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.M)(e.onKeyDown,t=>{let r=""!==u.searchRef.current;!e.disabled&&(!r||" "!==t.key)&&S[i.dir].includes(t.key)&&(n.onOpenChange(!0),n.content?.focus(),t.preventDefault())})})})});ek.displayName=eC;var eR="MenuSubContent",eS=r.forwardRef((e,t)=>{let n=K(Y,e.__scopeMenu),{forceMount:i=n.forceMount,...l}=e,u=_(Y,e.__scopeMenu),c=Z(Y,e.__scopeMenu),s=eE(eR,e.__scopeMenu),d=r.useRef(null),f=(0,a.e)(t,d);return(0,M.jsx)(D.Provider,{scope:e.__scopeMenu,children:(0,M.jsx)(g.z,{present:i||u.open,children:(0,M.jsx)(D.Slot,{scope:e.__scopeMenu,children:(0,M.jsx)(ee,{id:s.contentId,"aria-labelledby":s.triggerId,...l,ref:f,align:"start",side:"rtl"===c.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{c.isUsingKeyboardRef.current&&d.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>{e.target!==s.trigger&&u.onOpenChange(!1)}),onEscapeKeyDown:(0,o.M)(e.onEscapeKeyDown,e=>{c.onClose(),e.preventDefault()}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=A[c.dir].includes(e.key);t&&n&&(u.onOpenChange(!1),s.trigger?.focus(),e.preventDefault())})})})})})});function eA(e){return e?"open":"closed"}function eP(e){return"indeterminate"===e}function eD(e){return eP(e)?"indeterminate":e?"checked":"unchecked"}function ej(e){return t=>"mouse"===t.pointerType?e(t):void 0}eS.displayName=eR;var eL="DropdownMenu",[eT,eO]=(0,i.b)(eL,[O]),eN=O(),[eI,eW]=eT(eL),e_=e=>{let{__scopeDropdownMenu:t,children:n,dir:o,open:a,defaultOpen:i,onOpenChange:u,modal:c=!0}=e,s=eN(t),d=r.useRef(null),[f,p]=(0,l.T)({prop:a,defaultProp:i??!1,onChange:u,caller:eL});return(0,M.jsx)(eI,{scope:t,triggerId:(0,h.M)(),triggerRef:d,contentId:(0,h.M)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:c,children:(0,M.jsx)(V,{...s,open:f,onOpenChange:p,dir:o,modal:c,children:n})})};e_.displayName=eL;var eF="DropdownMenuTrigger",eZ=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...i}=e,l=eW(eF,n),c=eN(n);return(0,M.jsx)(B,{asChild:!0,...c,children:(0,M.jsx)(u.WV.button,{type:"button",id:l.triggerId,"aria-haspopup":"menu","aria-expanded":l.open,"aria-controls":l.open?l.contentId:void 0,"data-state":l.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...i,ref:(0,a.F)(t,l.triggerRef),onPointerDown:(0,o.M)(e.onPointerDown,e=>{r||0!==e.button||!1!==e.ctrlKey||(l.onOpenToggle(),l.open||e.preventDefault())}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&l.onOpenToggle(),"ArrowDown"===e.key&&l.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eZ.displayName=eF;var eV=e=>{let{__scopeDropdownMenu:t,...n}=e,r=eN(t);return(0,M.jsx)(X,{...r,...n})};eV.displayName="DropdownMenuPortal";var eB="DropdownMenuContent",ez=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...a}=e,i=eW(eB,n),l=eN(n),u=r.useRef(!1);return(0,M.jsx)(q,{id:i.contentId,"aria-labelledby":i.triggerId,...l,...a,ref:t,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{u.current||i.triggerRef.current?.focus(),u.current=!1,e.preventDefault()}),onInteractOutside:(0,o.M)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!i.modal||r)&&(u.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});ez.displayName=eB;var eH=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eN(n);return(0,M.jsx)(et,{...o,...r,ref:t})});eH.displayName="DropdownMenuGroup";var eK=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eN(n);return(0,M.jsx)(en,{...o,...r,ref:t})});eK.displayName="DropdownMenuLabel";var eX=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eN(n);return(0,M.jsx)(ea,{...o,...r,ref:t})});eX.displayName="DropdownMenuItem";var eY=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eN(n);return(0,M.jsx)(el,{...o,...r,ref:t})});eY.displayName="DropdownMenuCheckboxItem";var eU=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eN(n);return(0,M.jsx)(ed,{...o,...r,ref:t})});eU.displayName="DropdownMenuRadioGroup";var e$=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eN(n);return(0,M.jsx)(ep,{...o,...r,ref:t})});e$.displayName="DropdownMenuRadioItem";var eq=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eN(n);return(0,M.jsx)(eg,{...o,...r,ref:t})});eq.displayName="DropdownMenuItemIndicator";var eG=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eN(n);return(0,M.jsx)(ey,{...o,...r,ref:t})});eG.displayName="DropdownMenuSeparator",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eN(n);return(0,M.jsx)(ew,{...o,...r,ref:t})}).displayName="DropdownMenuArrow";var eJ=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eN(n);return(0,M.jsx)(ek,{...o,...r,ref:t})});eJ.displayName="DropdownMenuSubTrigger";var eQ=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eN(n);return(0,M.jsx)(eS,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eQ.displayName="DropdownMenuSubContent";var e0=e_,e1=eZ,e2=eV,e4=ez,e3=eH,e7=eK,e5=eX,e6=eY,e9=eU,e8=e$,te=eq,tt=eG,tn=e=>{let{__scopeDropdownMenu:t,children:n,open:r,onOpenChange:o,defaultOpen:a}=e,i=eN(t),[u,c]=(0,l.T)({prop:r,defaultProp:a??!1,onChange:o,caller:"DropdownMenuSub"});return(0,M.jsx)(eM,{...i,open:u,onOpenChange:c,children:n})},tr=eJ,to=eQ},1106:(e,t,n)=>{"use strict";n.d(t,{EW:()=>a});var r=n(3729),o=0;function a(){r.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??i()),document.body.insertAdjacentElement("beforeend",e[1]??i()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function i(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},27386:(e,t,n)=>{"use strict";n.d(t,{M:()=>d});var r=n(3729),o=n(31405),a=n(62409),i=n(2256),l=n(95344),u="focusScope.autoFocusOnMount",c="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:v,onUnmountAutoFocus:g,...y}=e,[w,x]=r.useState(null),b=(0,i.W)(v),E=(0,i.W)(g),M=r.useRef(null),C=(0,o.e)(t,e=>x(e)),k=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(k.paused||!w)return;let t=e.target;w.contains(t)?M.current=t:h(M.current,{select:!0})},t=function(e){if(k.paused||!w)return;let t=e.relatedTarget;null===t||w.contains(t)||h(M.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&h(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,w,k.paused]),r.useEffect(()=>{if(w){m.add(k);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(u,s);w.addEventListener(u,b),w.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(h(r,{select:t}),document.activeElement!==n)return}(f(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&h(w))}return()=>{w.removeEventListener(u,b),setTimeout(()=>{let t=new CustomEvent(c,s);w.addEventListener(c,E),w.dispatchEvent(t),t.defaultPrevented||h(e??document.body,{select:!0}),w.removeEventListener(c,E),m.remove(k)},0)}}},[w,b,E,k]);let R=r.useCallback(e=>{if(!n&&!d||k.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,a]=function(e){let t=f(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&a?e.shiftKey||r!==a?e.shiftKey&&r===o&&(e.preventDefault(),n&&h(a,{select:!0})):(e.preventDefault(),n&&h(o,{select:!0})):r===t&&e.preventDefault()}},[n,d,k.paused]);return(0,l.jsx)(a.WV.div,{tabIndex:-1,...y,ref:C,onKeyDown:R})});function f(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function h(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var m=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=v(e,t)).unshift(t)},remove(t){e=v(e,t),e[0]?.resume()}}}();function v(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},37574:(e,t,n)=>{"use strict";n.d(t,{ee:()=>eJ,Eh:()=>e0,VY:()=>eQ,fC:()=>eG,D7:()=>eN});var r=n(3729);let o=["top","right","bottom","left"],a=Math.min,i=Math.max,l=Math.round,u=Math.floor,c=e=>({x:e,y:e}),s={left:"right",right:"left",bottom:"top",top:"bottom"},d={start:"end",end:"start"};function f(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function v(e){return"y"===e?"height":"width"}let g=new Set(["top","bottom"]);function y(e){return g.has(p(e))?"y":"x"}function w(e){return e.replace(/start|end/g,e=>d[e])}let x=["left","right"],b=["right","left"],E=["top","bottom"],M=["bottom","top"];function C(e){return e.replace(/left|right|bottom|top/g,e=>s[e])}function k(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function R(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function S(e,t,n){let r,{reference:o,floating:a}=e,i=y(t),l=m(y(t)),u=v(l),c=p(t),s="y"===i,d=o.x+o.width/2-a.width/2,f=o.y+o.height/2-a.height/2,g=o[u]/2-a[u]/2;switch(c){case"top":r={x:d,y:o.y-a.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-a.width,y:f};break;default:r={x:o.x,y:o.y}}switch(h(t)){case"start":r[l]-=g*(n&&s?-1:1);break;case"end":r[l]+=g*(n&&s?-1:1)}return r}let A=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:a=[],platform:i}=n,l=a.filter(Boolean),u=await (null==i.isRTL?void 0:i.isRTL(t)),c=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:d}=S(c,r,u),f=r,p={},h=0;for(let n=0;n<l.length;n++){let{name:a,fn:m}=l[n],{x:v,y:g,data:y,reset:w}=await m({x:s,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:c,platform:i,elements:{reference:e,floating:t}});s=null!=v?v:s,d=null!=g?g:d,p={...p,[a]:{...p[a],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(c=!0===w.rects?await i.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:d}=S(c,f,u)),n=-1)}return{x:s,y:d,placement:f,strategy:o,middlewareData:p}};async function P(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:a,rects:i,elements:l,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:d="floating",altBoundary:p=!1,padding:h=0}=f(t,e),m=k(h),v=l[p?"floating"===d?"reference":"floating":d],g=R(await a.getClippingRect({element:null==(n=await (null==a.isElement?void 0:a.isElement(v)))||n?v:v.contextElement||await (null==a.getDocumentElement?void 0:a.getDocumentElement(l.floating)),boundary:c,rootBoundary:s,strategy:u})),y="floating"===d?{x:r,y:o,width:i.floating.width,height:i.floating.height}:i.reference,w=await (null==a.getOffsetParent?void 0:a.getOffsetParent(l.floating)),x=await (null==a.isElement?void 0:a.isElement(w))&&await (null==a.getScale?void 0:a.getScale(w))||{x:1,y:1},b=R(a.convertOffsetParentRelativeRectToViewportRelativeRect?await a.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:y,offsetParent:w,strategy:u}):y);return{top:(g.top-b.top+m.top)/x.y,bottom:(b.bottom-g.bottom+m.bottom)/x.y,left:(g.left-b.left+m.left)/x.x,right:(b.right-g.right+m.right)/x.x}}function D(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function j(e){return o.some(t=>e[t]>=0)}let L=new Set(["left","top"]);async function T(e,t){let{placement:n,platform:r,elements:o}=e,a=await (null==r.isRTL?void 0:r.isRTL(o.floating)),i=p(n),l=h(n),u="y"===y(n),c=L.has(i)?-1:1,s=a&&u?-1:1,d=f(t,e),{mainAxis:m,crossAxis:v,alignmentAxis:g}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return l&&"number"==typeof g&&(v="end"===l?-1*g:g),u?{x:v*s,y:m*c}:{x:m*c,y:v*s}}function O(e){var t;return t=0,"#document"}function N(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function I(e){var t,n;return null==(t=(n=0,e.document||window.document))?void 0:t.documentElement}let W=new Set(["inline","contents"]);function _(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=Y(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!W.has(o)}let F=[":popover-open",":modal"];function Z(e){return F.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let V=["transform","translate","scale","rotate","perspective"],B=["transform","translate","scale","rotate","perspective","filter"],z=["paint","layout","strict","content"];function H(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let K=new Set(["html","body","#document"]);function X(e){return K.has(O(e))}function Y(e){return N(e).getComputedStyle(e)}function U(e){return{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function $(e){return"html"===O(e)?e:e.assignedSlot||e.parentNode||I(e)}function q(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){var n;let r=$(t);return(n=r,K.has(O(n)))?t.ownerDocument?t.ownerDocument.body:t.body:e(r)}(e),a=o===(null==(r=e.ownerDocument)?void 0:r.body),i=N(o);if(a){let e=G(i);return t.concat(i,i.visualViewport||[],_(o)?o:[],e&&n?q(e):[])}return t.concat(o,q(o,[],n))}function G(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function J(e){return e.contextElement}function Q(e){return J(e),c(1)}let ee=c(0);function et(e){let t=N(e);return H()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:ee}function en(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let a=e.getBoundingClientRect(),i=J(e),l=c(1);t&&(r||(l=Q(e)));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===N(i))&&o)?et(i):c(0),s=(a.left+u.x)/l.x,d=(a.top+u.y)/l.y,f=a.width/l.x,p=a.height/l.y;if(i){let e=N(i),t=G(e);for(;t&&r&&r!==e;){let n=Q(t),r=t.getBoundingClientRect(),o=Y(t),a=r.left+(t.clientLeft+parseFloat(o.paddingLeft))*n.x,i=r.top+(t.clientTop+parseFloat(o.paddingTop))*n.y;s*=n.x,d*=n.y,f*=n.x,p*=n.y,s+=a,d+=i,t=G(e=N(t))}}return R({width:f,height:p,x:s,y:d})}function er(e,t){let n=U(e).scrollLeft;return t?t.left+n:en(I(e)).left+n}function eo(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:er(e,r)),y:r.top+t.scrollTop}}function ea(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=N(e),r=I(e),o=n.visualViewport,a=r.clientWidth,i=r.clientHeight,l=0,u=0;if(o){a=o.width,i=o.height;let e=H();(!e||e&&"fixed"===t)&&(l=o.offsetLeft,u=o.offsetTop)}return{width:a,height:i,x:l,y:u}}(e,n);else if("document"===t)r=function(e){let t=I(e),n=U(e),r=e.ownerDocument.body,o=i(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),a=i(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+er(e),u=-n.scrollTop;return"rtl"===Y(r).direction&&(l+=i(t.clientWidth,r.clientWidth)-o),{width:o,height:a,x:l,y:u}}(I(e));else{let n=et(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return R(r)}function ei(e,t){let n=N(e);if(Z(e))return n;{var r;let t=$(e);for(;t&&(r=t,!K.has(O(r)));)t=$(t);return n}}let el=async function(e){let t=this.getOffsetParent||ei,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=I(t),o="fixed"===n,a=en(e,!0,o,t),i={scrollLeft:0,scrollTop:0},l=c(0);if(!o){("body"!==O(t)||_(r))&&(i=U(t));r&&(l.x=er(r))}o&&r&&(l.x=er(r));let u=!r||o?c(0):eo(r,i);return{x:a.left+i.scrollLeft-l.x-u.x,y:a.top+i.scrollTop-l.y-u.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eu={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,a="fixed"===o,i=I(r),l=!!t&&Z(t.floating);if(r===i||l&&a)return n;let u={scrollLeft:0,scrollTop:0},s=c(1),d=c(0);a||("body"!==O(r)||_(i))&&(u=U(r));let f=!i||a?c(0):eo(i,u,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-u.scrollLeft*s.x+d.x+f.x,y:n.y*s.y-u.scrollTop*s.y+d.y+f.y}},getDocumentElement:I,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,l=[..."clippingAncestors"===n?Z(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=q(e,[],!1).filter(e=>!1);return"fixed"===Y(e).position&&$(e),t.set(e,r),r}(t,this._c):[].concat(n),r],u=l[0],c=l.reduce((e,n)=>{let r=ea(t,n,o);return e.top=i(r.top,e.top),e.right=a(r.right,e.right),e.bottom=a(r.bottom,e.bottom),e.left=i(r.left,e.left),e},ea(t,u,o));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}},getOffsetParent:ei,getElementRects:el,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=function(e){let t=Y(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=n,a=r,i=l(n)!==o||l(r)!==a;return i&&(n=o,r=a),{width:n,height:r,$:i}}(e);return{width:t,height:n}},getScale:Q,isElement:function(e){return!1},isRTL:function(e){return"rtl"===Y(e).direction}};function ec(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let es=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:l,platform:u,elements:c,middlewareData:s}=t,{element:d,padding:p=0}=f(e,t)||{};if(null==d)return{};let g=k(p),w={x:n,y:r},x=m(y(o)),b=v(x),E=await u.getDimensions(d),M="y"===x,C=M?"clientHeight":"clientWidth",R=l.reference[b]+l.reference[x]-w[x]-l.floating[b],S=w[x]-l.reference[x],A=await (null==u.getOffsetParent?void 0:u.getOffsetParent(d)),P=A?A[C]:0;P&&await (null==u.isElement?void 0:u.isElement(A))||(P=c.floating[C]||l.floating[b]);let D=P/2-E[b]/2-1,j=a(g[M?"top":"left"],D),L=a(g[M?"bottom":"right"],D),T=P-E[b]-L,O=P/2-E[b]/2+(R/2-S/2),N=i(j,a(O,T)),I=!s.arrow&&null!=h(o)&&O!==N&&l.reference[b]/2-(O<j?j:L)-E[b]/2<0,W=I?O<j?O-j:O-T:0;return{[x]:w[x]+W,data:{[x]:N,centerOffset:O-N-W,...I&&{alignmentOffset:W}},reset:I}}}),ed=(e,t,n)=>{let r=new Map,o={platform:eu,...n},a={...o.platform,_c:r};return A(e,t,{...o,platform:a})};var ef=n(81202),ep="undefined"!=typeof document?r.useLayoutEffect:function(){};function eh(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eh(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!eh(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function em(e,t){return Math.round(1*t)/1}function ev(e){let t=r.useRef(e);return ep(()=>{t.current=e}),t}let eg=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?es({element:n.current,padding:r}).fn(t):{}:n?es({element:n,padding:r}).fn(t):{}}}),ey=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:a,placement:i,middlewareData:l}=t,u=await T(t,e);return i===(null==(n=l.offset)?void 0:n.placement)&&null!=(r=l.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:a+u.y,data:{...u,placement:i}}}}}(e),options:[e,t]}),ew=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:l=!0,crossAxis:u=!1,limiter:c={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=f(e,t),d={x:n,y:r},h=await P(t,s),v=y(p(o)),g=m(v),w=d[g],x=d[v];if(l){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=w+h[e],r=w-h[t];w=i(n,a(w,r))}if(u){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=x+h[e],r=x-h[t];x=i(n,a(x,r))}let b=c.fn({...t,[g]:w,[v]:x});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[g]:l,[v]:u}}}}}}(e),options:[e,t]}),ex=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:a,middlewareData:i}=t,{offset:l=0,mainAxis:u=!0,crossAxis:c=!0}=f(e,t),s={x:n,y:r},d=y(o),h=m(d),v=s[h],g=s[d],w=f(l,t),x="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(u){let e="y"===h?"height":"width",t=a.reference[h]-a.floating[e]+x.mainAxis,n=a.reference[h]+a.reference[e]-x.mainAxis;v<t?v=t:v>n&&(v=n)}if(c){var b,E;let e="y"===h?"width":"height",t=L.has(p(o)),n=a.reference[d]-a.floating[e]+(t&&(null==(b=i.offset)?void 0:b[d])||0)+(t?0:x.crossAxis),r=a.reference[d]+a.reference[e]+(t?0:(null==(E=i.offset)?void 0:E[d])||0)-(t?x.crossAxis:0);g<n?g=n:g>r&&(g=r)}return{[h]:v,[d]:g}}}}(e),options:[e,t]}),eb=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,a,i;let{placement:l,middlewareData:u,rects:c,initialPlacement:s,platform:d,elements:g}=t,{mainAxis:k=!0,crossAxis:R=!0,fallbackPlacements:S,fallbackStrategy:A="bestFit",fallbackAxisSideDirection:D="none",flipAlignment:j=!0,...L}=f(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let T=p(l),O=y(s),N=p(s)===s,I=await (null==d.isRTL?void 0:d.isRTL(g.floating)),W=S||(N||!j?[C(s)]:function(e){let t=C(e);return[w(e),t,w(t)]}(s)),_="none"!==D;!S&&_&&W.push(...function(e,t,n,r){let o=h(e),a=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?b:x;return t?x:b;case"left":case"right":return t?E:M;default:return[]}}(p(e),"start"===n,r);return o&&(a=a.map(e=>e+"-"+o),t&&(a=a.concat(a.map(w)))),a}(s,j,D,I));let F=[s,...W],Z=await P(t,L),V=[],B=(null==(r=u.flip)?void 0:r.overflows)||[];if(k&&V.push(Z[T]),R){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),o=m(y(e)),a=v(o),i="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[a]>t.floating[a]&&(i=C(i)),[i,C(i)]}(l,c,I);V.push(Z[e[0]],Z[e[1]])}if(B=[...B,{placement:l,overflows:V}],!V.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=F[e];if(t&&(!("alignment"===R&&O!==y(t))||B.every(e=>e.overflows[0]>0&&y(e.placement)===O)))return{data:{index:e,overflows:B},reset:{placement:t}};let n=null==(a=B.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:a.placement;if(!n)switch(A){case"bestFit":{let e=null==(i=B.filter(e=>{if(_){let t=y(e.placement);return t===O||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:i[0];e&&(n=e);break}case"initialPlacement":n=s}if(l!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eE=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,l;let{placement:u,rects:c,platform:s,elements:d}=t,{apply:m=()=>{},...v}=f(e,t),g=await P(t,v),w=p(u),x=h(u),b="y"===y(u),{width:E,height:M}=c.floating;"top"===w||"bottom"===w?(o=w,l=x===(await (null==s.isRTL?void 0:s.isRTL(d.floating))?"start":"end")?"left":"right"):(l=w,o="end"===x?"top":"bottom");let C=M-g.top-g.bottom,k=E-g.left-g.right,R=a(M-g[o],C),S=a(E-g[l],k),A=!t.middlewareData.shift,D=R,j=S;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(j=k),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(D=C),A&&!x){let e=i(g.left,0),t=i(g.right,0),n=i(g.top,0),r=i(g.bottom,0);b?j=E-2*(0!==e||0!==t?e+t:i(g.left,g.right)):D=M-2*(0!==n||0!==r?n+r:i(g.top,g.bottom))}await m({...t,availableWidth:j,availableHeight:D});let L=await s.getDimensions(d.floating);return E!==L.width||M!==L.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eM=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=f(e,t);switch(r){case"referenceHidden":{let e=D(await P(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:j(e)}}}case"escaped":{let e=D(await P(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:j(e)}}}default:return{}}}}}(e),options:[e,t]}),eC=(e,t)=>({...eg(e),options:[e,t]});var ek=n(62409),eR=n(95344),eS=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...a}=e;return(0,eR.jsx)(ek.WV.svg,{...a,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eR.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eS.displayName="Arrow";var eA=n(31405),eP=n(98462),eD=n(2256),ej=n(16069),eL=n(63085),eT="Popper",[eO,eN]=(0,eP.b)(eT),[eI,eW]=eO(eT),e_=e=>{let{__scopePopper:t,children:n}=e,[o,a]=r.useState(null);return(0,eR.jsx)(eI,{scope:t,anchor:o,onAnchorChange:a,children:n})};e_.displayName=eT;var eF="PopperAnchor",eZ=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...a}=e,i=eW(eF,n),l=r.useRef(null),u=(0,eA.e)(t,l);return r.useEffect(()=>{i.onAnchorChange(o?.current||l.current)}),o?null:(0,eR.jsx)(ek.WV.div,{...a,ref:u})});eZ.displayName=eF;var eV="PopperContent",[eB,ez]=eO(eV),eH=r.forwardRef((e,t)=>{let{__scopePopper:n,side:o="bottom",sideOffset:l=0,align:c="center",alignOffset:s=0,arrowPadding:d=0,avoidCollisions:f=!0,collisionBoundary:p=[],collisionPadding:h=0,sticky:m="partial",hideWhenDetached:v=!1,updatePositionStrategy:g="optimized",onPlaced:y,...w}=e,x=eW(eV,n),[b,E]=r.useState(null),M=(0,eA.e)(t,e=>E(e)),[C,k]=r.useState(null),R=(0,eL.t)(C),S=R?.width??0,A=R?.height??0,P="number"==typeof h?h:{top:0,right:0,bottom:0,left:0,...h},D=Array.isArray(p)?p:[p],j=D.length>0,L={padding:P,boundary:D.filter(eU),altBoundary:j},{refs:T,floatingStyles:O,placement:N,isPositioned:W,middlewareData:_}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:a,elements:{reference:i,floating:l}={},transform:u=!0,whileElementsMounted:c,open:s}=e,[d,f]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(o);eh(p,o)||h(o);let[m,v]=r.useState(null),[g,y]=r.useState(null),w=r.useCallback(e=>{e!==M.current&&(M.current=e,v(e))},[]),x=r.useCallback(e=>{e!==C.current&&(C.current=e,y(e))},[]),b=i||m,E=l||g,M=r.useRef(null),C=r.useRef(null),k=r.useRef(d),R=null!=c,S=ev(c),A=ev(a),P=ev(s),D=r.useCallback(()=>{if(!M.current||!C.current)return;let e={placement:t,strategy:n,middleware:p};A.current&&(e.platform=A.current),ed(M.current,C.current,e).then(e=>{let t={...e,isPositioned:!1!==P.current};j.current&&!eh(k.current,t)&&(k.current=t,ef.flushSync(()=>{f(t)}))})},[p,t,n,A,P]);ep(()=>{!1===s&&k.current.isPositioned&&(k.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[s]);let j=r.useRef(!1);ep(()=>(j.current=!0,()=>{j.current=!1}),[]),ep(()=>{if(b&&(M.current=b),E&&(C.current=E),b&&E){if(S.current)return S.current(b,E,D);D()}},[b,E,D,S,R]);let L=r.useMemo(()=>({reference:M,floating:C,setReference:w,setFloating:x}),[w,x]),T=r.useMemo(()=>({reference:b,floating:E}),[b,E]),O=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!T.floating)return e;let t=em(T.floating,d.x),r=em(T.floating,d.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...(T.floating,!1)}:{position:n,left:t,top:r}},[n,u,T.floating,d.x,d.y]);return r.useMemo(()=>({...d,update:D,refs:L,elements:T,floatingStyles:O}),[d,D,L,T,O])}({strategy:"fixed",placement:o+("center"!==c?"-"+c:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:l=!0,ancestorResize:c=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:f=!1}=r,p=J(e),h=l||c?[...p?q(p):[],...q(t)]:[];h.forEach(e=>{l&&e.addEventListener("scroll",n,{passive:!0}),c&&e.addEventListener("resize",n)});let m=p&&d?function(e,t){let n,r=null,o=I(e);function l(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return function c(s,d){void 0===s&&(s=!1),void 0===d&&(d=1),l();let f=e.getBoundingClientRect(),{left:p,top:h,width:m,height:v}=f;if(s||t(),!m||!v)return;let g=u(h),y=u(o.clientWidth-(p+m)),w={rootMargin:-g+"px "+-y+"px "+-u(o.clientHeight-(h+v))+"px "+-u(p)+"px",threshold:i(0,a(1,d))||1},x=!0;function b(t){let r=t[0].intersectionRatio;if(r!==d){if(!x)return c();r?c(!1,r):n=setTimeout(()=>{c(!1,1e-7)},1e3)}1!==r||ec(f,e.getBoundingClientRect())||c(),x=!1}try{r=new IntersectionObserver(b,{...w,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(b,w)}r.observe(e)}(!0),l}(p,n):null,v=-1,g=null;s&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),p&&!f&&g.observe(p),g.observe(t));let y=f?en(e):null;return f&&function t(){let r=en(e);y&&!ec(y,r)&&n(),y=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{l&&e.removeEventListener("scroll",n),c&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=g)||e.disconnect(),g=null,f&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===g}),elements:{reference:x.anchor},middleware:[ey({mainAxis:l+A,alignmentAxis:s}),f&&ew({mainAxis:!0,crossAxis:!1,limiter:"partial"===m?ex():void 0,...L}),f&&eb({...L}),eE({...L,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:a}=t.reference,i=e.floating.style;i.setProperty("--radix-popper-available-width",`${n}px`),i.setProperty("--radix-popper-available-height",`${r}px`),i.setProperty("--radix-popper-anchor-width",`${o}px`),i.setProperty("--radix-popper-anchor-height",`${a}px`)}}),C&&eC({element:C,padding:d}),e$({arrowWidth:S,arrowHeight:A}),v&&eM({strategy:"referenceHidden",...L})]}),[F,Z]=eq(N),V=(0,eD.W)(y);(0,ej.b)(()=>{W&&V?.()},[W,V]);let B=_.arrow?.x,z=_.arrow?.y,H=_.arrow?.centerOffset!==0,[K,X]=r.useState();return(0,ej.b)(()=>{b&&X(window.getComputedStyle(b).zIndex)},[b]),(0,eR.jsx)("div",{ref:T.setFloating,"data-radix-popper-content-wrapper":"",style:{...O,transform:W?O.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:K,"--radix-popper-transform-origin":[_.transformOrigin?.x,_.transformOrigin?.y].join(" "),..._.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eR.jsx)(eB,{scope:n,placedSide:F,onArrowChange:k,arrowX:B,arrowY:z,shouldHideArrow:H,children:(0,eR.jsx)(ek.WV.div,{"data-side":F,"data-align":Z,...w,ref:M,style:{...w.style,animation:W?void 0:"none"}})})})});eH.displayName=eV;var eK="PopperArrow",eX={top:"bottom",right:"left",bottom:"top",left:"right"},eY=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=ez(eK,n),a=eX[o.placedSide];return(0,eR.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[a]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eR.jsx)(eS,{...r,ref:t,style:{...r.style,display:"block"}})})});function eU(e){return null!==e}eY.displayName=eK;var e$=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,a=o.arrow?.centerOffset!==0,i=a?0:e.arrowWidth,l=a?0:e.arrowHeight,[u,c]=eq(n),s={start:"0%",center:"50%",end:"100%"}[c],d=(o.arrow?.x??0)+i/2,f=(o.arrow?.y??0)+l/2,p="",h="";return"bottom"===u?(p=a?s:`${d}px`,h=`${-l}px`):"top"===u?(p=a?s:`${d}px`,h=`${r.floating.height+l}px`):"right"===u?(p=`${-l}px`,h=a?s:`${f}px`):"left"===u&&(p=`${r.floating.width+l}px`,h=a?s:`${f}px`),{data:{x:p,y:h}}}});function eq(e){let[t,n="center"]=e.split("-");return[t,n]}var eG=e_,eJ=eZ,eQ=eH,e0=eY},31179:(e,t,n)=>{"use strict";n.d(t,{h:()=>u});var r=n(3729),o=n(81202),a=n(62409),i=n(16069),l=n(95344),u=r.forwardRef((e,t)=>{let{container:n,...u}=e,[c,s]=r.useState(!1);(0,i.b)(()=>s(!0),[]);let d=n||c&&globalThis?.document?.body;return d?o.createPortal((0,l.jsx)(a.WV.div,{...u,ref:t}),d):null});u.displayName="Portal"},63085:(e,t,n)=>{"use strict";n.d(t,{t:()=>a});var r=n(3729),o=n(16069);function a(e){let[t,n]=r.useState(void 0);return(0,o.b)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let a=t[0];if("borderBoxSize"in a){let e=a.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}}};