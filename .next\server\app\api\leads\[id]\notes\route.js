"use strict";(()=>{var e={};e.id=1671,e.ids=[1671],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},63524:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>x,originalPathname:()=>v,patchFetch:()=>_,requestAsyncStorage:()=>w,routeModule:()=>I,serverHooks:()=>h,staticGenerationAsyncStorage:()=>f,staticGenerationBailout:()=>q});var a={};t.r(a),t.d(a,{GET:()=>y,POST:()=>g});var s=t(95419),n=t(69108),i=t(99678),o=t(78070),l=t(81355),d=t(3205),u=t(9108),c=t(25252),p=t(52178);let m=c.Ry({title:c.Z_().min(1,"Title is required"),content:c.Z_().min(1,"Content is required"),isPrivate:c.O7().default(!1)});async function y(e,{params:r}){try{let t=await (0,l.getServerSession)(d.L);if(!t?.user?.id||!t?.user?.companyId)return o.Z.json({error:"Unauthorized"},{status:401});let{searchParams:a}=new URL(e.url),s=parseInt(a.get("page")||"1"),n=parseInt(a.get("limit")||"20");if(!await u._.lead.findFirst({where:{id:r.id,companyId:t.user.companyId}}))return o.Z.json({error:"Lead not found"},{status:404});let i={leadId:r.id,OR:[{isPrivate:!1},{isPrivate:!0,createdById:t.user.id}]},[c,p]=await Promise.all([u._.leadNote.findMany({where:i,include:{createdBy:{select:{id:!0,name:!0,email:!0}}},orderBy:{createdAt:"desc"},skip:(s-1)*n,take:n}),u._.leadNote.count({where:i})]);return o.Z.json({notes:c,pagination:{page:s,limit:n,total:p,pages:Math.ceil(p/n)}})}catch(e){return console.error("Error fetching notes:",e),o.Z.json({error:"Failed to fetch notes"},{status:500})}}async function g(e,{params:r}){try{let t=await (0,l.getServerSession)(d.L);if(!t?.user?.id||!t?.user?.companyId)return o.Z.json({error:"Unauthorized"},{status:401});let a=await e.json(),s=m.parse(a);if(!await u._.lead.findFirst({where:{id:r.id,companyId:t.user.companyId}}))return o.Z.json({error:"Lead not found"},{status:404});let n=await u._.leadNote.create({data:{...s,leadId:r.id,companyId:t.user.companyId,createdById:t.user.id},include:{createdBy:{select:{id:!0,name:!0,email:!0}}}});return o.Z.json({note:n},{status:201})}catch(e){if(e instanceof p.jm)return o.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("Error creating note:",e),o.Z.json({error:"Failed to create note"},{status:500})}}let I=new s.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/leads/[id]/notes/route",pathname:"/api/leads/[id]/notes",filename:"route",bundlePath:"app/api/leads/[id]/notes/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\leads\\[id]\\notes\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:w,staticGenerationAsyncStorage:f,serverHooks:h,headerHooks:x,staticGenerationBailout:q}=I,v="/api/leads/[id]/notes/route";function _(){return(0,i.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:f})}},3205:(e,r,t)=>{t.d(r,{L:()=>d});var a=t(86485),s=t(10375),n=t(50694),i=t(6521),o=t.n(i),l=t(9108);let d={providers:[(0,a.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let r=await l._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),t=r?.companyId;if(!t&&r){let e=await l._.company.findFirst({where:{ownerId:r.id},select:{id:!0}});(t=e?.id)&&await l._.user.update({where:{id:r.id},data:{companyId:t}})}if(!r)return console.log("User not found:",e.email),null;if(!r.password)return console.log("User has no password set:",e.email),null;if(!await o().compare(e.password,r.password))return console.log("Invalid password for user:",e.email),null;return await l._.user.update({where:{id:r.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",r.email),{id:r.id,email:r.email,name:r.name,role:r.role,companyId:t}}catch(e){return console.error("Authentication error:",e),null}}}),(0,s.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,n.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(console.log("JWT callback - user data:",{id:r.id,email:r.email,role:r.role,companyId:r.companyId}),e.role=r.role,e.companyId=r.companyId),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role,e.user.companyId=r.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,r,t)=>{t.d(r,{_:()=>s});let a=require("@prisma/client"),s=globalThis.prisma??new a.PrismaClient}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[1638,6206,6521,2455,4520,5252],()=>t(63524));module.exports=a})();