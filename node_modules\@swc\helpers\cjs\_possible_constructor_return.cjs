"use strict";

var _assert_this_initialized = require("./_assert_this_initialized.cjs");
var _type_of = require("./_type_of.cjs");

exports._ = exports._possible_constructor_return = _possible_constructor_return;
function _possible_constructor_return(self, call) {
    if (call && (_type_of._(call) === "object" || typeof call === "function")) return call;

    return _assert_this_initialized._(self);
}
