"use strict";exports.id=4957,exports.ids=[4957],exports.modules={14411:(e,t,s)=>{s.d(t,{q:()=>v});var i=s(95344),a=s(3729),n=s(60708),l=s(55697),r=s(12374),d=s(16212),o=s(92549),c=s(1586),m=s(16802),u=s(51838),x=s(38271),h=s(55329),p=s(44669);let y=r.Ry({name:r.Z_().min(1,"Name is required"),description:r.Z_().optional(),quantity:r.Rx().min(1,"Quantity must be at least 1"),unitPrice:r.Rx().min(0,"Unit price must be positive"),itemId:r.Z_().optional()}),j=r.Ry({title:r.Z_().optional(),customerId:r.Z_().min(1,"Customer is required"),quotationId:r.Z_().optional(),status:r.Km(["DRAFT","SENT","PAID","OVERDUE","CANCELLED"]).default("DRAFT"),dueDate:r.Z_().optional(),terms:r.Z_().optional(),notes:r.Z_().optional(),items:r.IX(y).min(1,"At least one item is required"),taxRate:r.Rx().min(0).max(100).default(0)});function v({isOpen:e,onClose:t,onSuccess:s,invoice:r,mode:y,preselectedCustomerId:v,preselectedQuotationId:g}){let[f,b]=(0,a.useState)(!1),[N,I]=(0,a.useState)([]),[k,w]=(0,a.useState)([]),{register:q,handleSubmit:D,formState:{errors:Z},reset:C,watch:R,control:T,setValue:F}=(0,n.cI)({resolver:(0,l.F)(j),defaultValues:r?{title:r.title||"",customerId:r.customerId||v||"",quotationId:r.quotationId||g||"",status:r.status||"DRAFT",dueDate:r.dueDate?new Date(r.dueDate).toISOString().split("T")[0]:(()=>{let e=new Date;return e.setDate(e.getDate()+30),e.toISOString().split("T")[0]})(),terms:r.terms||"",notes:r.notes||"",items:r.items?.map(e=>({name:e.name||e.description||"",description:e.description||"",quantity:e.quantity||1,unitPrice:e.unitPrice||0,itemId:e.itemId||""}))||[{name:"",description:"",quantity:1,unitPrice:0,itemId:""}],taxRate:r.taxRate||0}:{status:"DRAFT",customerId:v||"",quotationId:g||"",dueDate:(()=>{let e=new Date;return e.setDate(e.getDate()+30),e.toISOString().split("T")[0]})(),items:[{name:"",description:"",quantity:1,unitPrice:0,itemId:""}],taxRate:0}}),{fields:S,append:_,remove:A}=(0,n.Dq)({control:T,name:"items"}),P=R("items"),E=R("taxRate");(0,a.useEffect)(()=>{let t=async()=>{try{let[e,t]=await Promise.all([fetch("/api/customers?limit=100"),fetch("/api/quotations?limit=100&status=ACCEPTED")]);if(e.ok){let t=await e.json();I(t.customers)}if(t.ok){let e=await t.json();w(e.quotations)}}catch(e){console.error("Error fetching data:",e)}};e&&t()},[e]);let $=R("quotationId");(0,a.useEffect)(()=>{$&&"create"===y&&(async()=>{try{let e=await fetch(`/api/quotations/${$}`);if(e.ok){let t=await e.json();t.items&&t.items.length>0&&(F("items",t.items.map(e=>({description:e.description,quantity:e.quantity,unitPrice:e.unitPrice,discount:e.discount,taxRate:e.taxRate}))),F("taxRate",t.taxRate),F("discountType",t.discountType),F("discountValue",t.discountValue),F("terms",t.terms||""))}}catch(e){console.error("Error loading quotation items:",e)}})()},[$,y,F]);let M=(()=>{let e=P.reduce((e,t)=>e+(t.quantity||0)*(t.unitPrice||0),0),t=e*(E||0)/100;return{subtotal:Math.round(100*e)/100,total:Math.round(100*(e+t))/100,taxAmount:Math.round(100*t)/100}})(),z=e=>{S.length>1&&A(e)},O=async e=>{b(!0);try{let i="create"===y?"/api/invoices":`/api/invoices/${r.id}`,a=await fetch(i,{method:"create"===y?"POST":"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!a.ok){let e=await a.json();throw Error(e.error||"Failed to save invoice")}p.toast.success(`Invoice ${"create"===y?"created":"updated"} successfully`),C(),s(),t()}catch(e){p.toast.error(e instanceof Error?e.message:"An error occurred")}finally{b(!1)}},V=()=>{C(),t()};return i.jsx(m.Vq,{open:e,onOpenChange:V,children:(0,i.jsxs)(m.cZ,{className:"max-w-6xl max-h-[90vh] overflow-y-auto",children:[(0,i.jsxs)(m.fK,{children:[i.jsx(m.$N,{children:"create"===y?"Create New Invoice":"Edit Invoice"}),i.jsx(m.Be,{children:"create"===y?"Create a new invoice with items, pricing, and payment terms.":"Update the invoice information and items."})]}),(0,i.jsxs)("form",{onSubmit:D(O),className:"space-y-6",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"md:col-span-2",children:[i.jsx(c._,{htmlFor:"title",children:"Invoice Title"}),i.jsx(o.I,{id:"title",...q("title"),placeholder:"Invoice title (optional)"})]}),(0,i.jsxs)("div",{children:[i.jsx(c._,{htmlFor:"customerId",children:"Customer *"}),(0,i.jsxs)("select",{id:"customerId",...q("customerId"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[i.jsx("option",{value:"",children:"Select a customer"}),N.map(e=>(0,i.jsxs)("option",{value:e.id,children:[e.name," ",e.company&&`(${e.company})`]},e.id))]}),Z.customerId&&i.jsx("p",{className:"text-sm text-red-600 mt-1",children:Z.customerId.message})]}),(0,i.jsxs)("div",{children:[i.jsx(c._,{htmlFor:"quotationId",children:"Related Quotation"}),(0,i.jsxs)("select",{id:"quotationId",...q("quotationId"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[i.jsx("option",{value:"",children:"Select a quotation (optional)"}),k.map(e=>(0,i.jsxs)("option",{value:e.id,children:[e.quotationNumber," - ",e.title]},e.id))]})]}),(0,i.jsxs)("div",{children:[i.jsx(c._,{htmlFor:"status",children:"Status"}),i.jsx("select",{id:"status",...q("status"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[{value:"DRAFT",label:"Draft"},{value:"SENT",label:"Sent"},{value:"PAID",label:"Paid"},{value:"OVERDUE",label:"Overdue"},{value:"CANCELLED",label:"Cancelled"}].map(e=>i.jsx("option",{value:e.value,children:e.label},e.value))})]}),(0,i.jsxs)("div",{children:[i.jsx(c._,{htmlFor:"dueDate",children:"Due Date"}),i.jsx(o.I,{id:"dueDate",type:"date",...q("dueDate")})]})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[i.jsx("h3",{className:"text-lg font-medium",children:"Items"}),(0,i.jsxs)(d.z,{type:"button",onClick:()=>{_({name:"",description:"",quantity:1,unitPrice:0,itemId:""})},size:"sm",children:[i.jsx(u.Z,{className:"h-4 w-4 mr-2"}),"Add Item"]})]}),i.jsx("div",{className:"space-y-4",children:S.map((e,t)=>(0,i.jsxs)("div",{className:"grid grid-cols-12 gap-2 items-end p-4 border rounded-lg",children:[(0,i.jsxs)("div",{className:"col-span-3",children:[i.jsx(c._,{htmlFor:`items.${t}.name`,children:"Name *"}),i.jsx(o.I,{...q(`items.${t}.name`),placeholder:"Item name"}),Z.items?.[t]?.name&&i.jsx("p",{className:"text-sm text-red-600 mt-1",children:Z.items[t]?.name?.message})]}),(0,i.jsxs)("div",{className:"col-span-3",children:[i.jsx(c._,{htmlFor:`items.${t}.description`,children:"Description"}),i.jsx(o.I,{...q(`items.${t}.description`),placeholder:"Item description"})]}),(0,i.jsxs)("div",{className:"col-span-2",children:[i.jsx(c._,{htmlFor:`items.${t}.quantity`,children:"Qty *"}),i.jsx(o.I,{type:"number",min:"1",...q(`items.${t}.quantity`,{valueAsNumber:!0}),placeholder:"1"})]}),(0,i.jsxs)("div",{className:"col-span-2",children:[i.jsx(c._,{htmlFor:`items.${t}.unitPrice`,children:"Unit Price *"}),i.jsx(o.I,{type:"number",min:"0",step:"0.01",...q(`items.${t}.unitPrice`,{valueAsNumber:!0}),placeholder:"0.00"})]}),(0,i.jsxs)("div",{className:"col-span-1",children:[i.jsx(c._,{children:"Total"}),(0,i.jsxs)("div",{className:"px-3 py-2 bg-gray-50 rounded-md text-sm",children:["$",(()=>{let e=P[t];return e?((e.quantity||0)*(e.unitPrice||0)).toFixed(2):"0.00"})()]})]}),i.jsx("div",{className:"col-span-1",children:i.jsx(d.z,{type:"button",variant:"destructive",size:"sm",onClick:()=>z(t),disabled:1===S.length,children:i.jsx(x.Z,{className:"h-4 w-4"})})})]},e.id))})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{className:"space-y-4",children:[i.jsx("h3",{className:"text-lg font-medium",children:"Tax Settings"}),(0,i.jsxs)("div",{children:[i.jsx(c._,{htmlFor:"taxRate",children:"Tax Rate (%)"}),i.jsx(o.I,{type:"number",min:"0",max:"100",step:"0.01",...q("taxRate",{valueAsNumber:!0}),placeholder:"0"})]})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("h3",{className:"text-lg font-medium flex items-center",children:[i.jsx(h.Z,{className:"h-5 w-5 mr-2"}),"Totals"]}),(0,i.jsxs)("div",{className:"space-y-2 p-4 bg-gray-50 rounded-lg",children:[(0,i.jsxs)("div",{className:"flex justify-between",children:[i.jsx("span",{children:"Subtotal:"}),(0,i.jsxs)("span",{children:["$",M.subtotal.toFixed(2)]})]}),M.taxAmount>0&&(0,i.jsxs)("div",{className:"flex justify-between",children:[i.jsx("span",{children:"Tax:"}),(0,i.jsxs)("span",{children:["$",M.taxAmount.toFixed(2)]})]}),(0,i.jsxs)("div",{className:"flex justify-between font-bold text-lg border-t pt-2",children:[i.jsx("span",{children:"Total:"}),(0,i.jsxs)("span",{children:["$",M.total.toFixed(2)]})]})]})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[i.jsx(c._,{htmlFor:"terms",children:"Terms & Conditions"}),i.jsx("textarea",{id:"terms",...q("terms"),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Payment terms, late fees, etc..."})]}),(0,i.jsxs)("div",{children:[i.jsx(c._,{htmlFor:"notes",children:"Internal Notes"}),i.jsx("textarea",{id:"notes",...q("notes"),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Internal notes (not visible to customer)..."})]})]}),(0,i.jsxs)(m.cN,{children:[i.jsx(d.z,{type:"button",variant:"outline",onClick:V,children:"Cancel"}),i.jsx(d.z,{type:"submit",disabled:f,children:f?"Saving...":"create"===y?"Create Invoice":"Update Invoice"})]})]})]})})}},45961:(e,t,s)=>{s.d(t,{Z:()=>i});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,s(69224).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},55329:(e,t,s)=>{s.d(t,{Z:()=>i});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,s(69224).Z)("Calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]])},55794:(e,t,s)=>{s.d(t,{Z:()=>i});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,s(69224).Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},7060:(e,t,s)=>{s.d(t,{Z:()=>i});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,s(69224).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},25545:(e,t,s)=>{s.d(t,{Z:()=>i});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,s(69224).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},1960:(e,t,s)=>{s.d(t,{Z:()=>i});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,s(69224).Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},96885:(e,t,s)=>{s.d(t,{Z:()=>i});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,s(69224).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},36135:(e,t,s)=>{s.d(t,{Z:()=>i});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,s(69224).Z)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]])}};