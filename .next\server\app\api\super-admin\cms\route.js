"use strict";(()=>{var e={};e.id=4155,e.ids=[4155],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},56068:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>w,originalPathname:()=>S,patchFetch:()=>b,requestAsyncStorage:()=>g,routeModule:()=>m,serverHooks:()=>h,staticGenerationAsyncStorage:()=>y,staticGenerationBailout:()=>f});var s={};t.r(s),t.d(s,{GET:()=>d,POST:()=>p});var n=t(95419),o=t(69108),a=t(99678),i=t(78070),c=t(81355),l=t(3205),u=t(9108);async function d(e){try{let e={};try{e=(await u._.cMSContent.findMany()).reduce((e,r)=>(e[r.section]=r.content,e),{})}catch(r){console.error("Error fetching CMS content:",r),e={hero:{enabled:!0,title:"Build Your SaaS Business",subtitle:"The Complete Platform",description:"Everything you need to launch, grow, and scale your SaaS business. From customer management to billing, we've got you covered.",primaryCTA:{text:"Start Free Trial",link:"/auth/signup"},secondaryCTA:{text:"Watch Demo",link:"/demo"},backgroundImage:"",backgroundVideo:""},features:{enabled:!0,title:"Everything You Need",subtitle:"Powerful Features",items:[{id:"1",title:"Customer Management",description:"Manage your customers, track interactions, and build lasting relationships.",icon:"users",image:""},{id:"2",title:"Subscription Billing",description:"Automated billing, invoicing, and payment processing for recurring revenue.",icon:"credit-card",image:""},{id:"3",title:"Analytics & Reports",description:"Comprehensive analytics to track your business performance and growth.",icon:"bar-chart",image:""}]},pricing:{enabled:!0,title:"Simple, Transparent Pricing",subtitle:"Choose the plan that fits your needs",showPricingTable:!0,customMessage:""},seo:{title:"SaaS Platform - Build Your Business",description:"The complete SaaS platform for modern businesses. Customer management, billing, analytics, and more.",keywords:"saas, platform, business, customer management, billing, analytics",ogImage:""}}}return i.Z.json({success:!0,content:e})}catch(e){return console.error("Error fetching CMS content:",e),i.Z.json({success:!1,error:"Internal server error"},{status:500})}}async function p(e){try{let r=await (0,c.getServerSession)(l.L);if(!r?.user||"SUPER_ADMIN"!==r.user.role)return i.Z.json({success:!1,error:"Unauthorized"},{status:401});let t=await e.json();console.log("Received CMS content to save:",JSON.stringify(t,null,2));let s=[];for(let[e,r]of Object.entries(t))try{s.push(u._.cMSContent.upsert({where:{section:e},update:{content:r,updatedAt:new Date},create:{section:e,content:r,isEnabled:!0}}))}catch(r){console.error(`Error updating CMS content for ${e}:`,r)}try{let e=await Promise.all(s);console.log("Successfully saved CMS content:",e.length,"sections updated")}catch(e){console.error("Error saving CMS content:",e)}try{await u._.auditLog.create({data:{action:"UPDATE_CMS_CONTENT",entityType:"CMS_CONTENT",entityId:"cms",userId:r.user.id,details:{updatedSections:Object.keys(t),timestamp:new Date().toISOString()}}})}catch(e){console.error("Error creating audit log:",e)}return i.Z.json({success:!0,message:"CMS content updated successfully"})}catch(e){return console.error("Error updating CMS content:",e),i.Z.json({success:!1,error:"Internal server error"},{status:500})}}let m=new n.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/super-admin/cms/route",pathname:"/api/super-admin/cms",filename:"route",bundlePath:"app/api/super-admin/cms/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\super-admin\\cms\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:g,staticGenerationAsyncStorage:y,serverHooks:h,headerHooks:w,staticGenerationBailout:f}=m,S="/api/super-admin/cms/route";function b(){return(0,a.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:y})}},3205:(e,r,t)=>{t.d(r,{L:()=>l});var s=t(86485),n=t(10375),o=t(50694),a=t(6521),i=t.n(a),c=t(9108);let l={providers:[(0,s.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let r=await c._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),t=r?.companyId;if(!t&&r){let e=await c._.company.findFirst({where:{ownerId:r.id},select:{id:!0}});(t=e?.id)&&await c._.user.update({where:{id:r.id},data:{companyId:t}})}if(!r)return console.log("User not found:",e.email),null;if(!r.password)return console.log("User has no password set:",e.email),null;if(!await i().compare(e.password,r.password))return console.log("Invalid password for user:",e.email),null;return await c._.user.update({where:{id:r.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",r.email),{id:r.id,email:r.email,name:r.name,role:r.role,companyId:t}}catch(e){return console.error("Authentication error:",e),null}}}),(0,n.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,o.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(console.log("JWT callback - user data:",{id:r.id,email:r.email,role:r.role,companyId:r.companyId}),e.role=r.role,e.companyId=r.companyId),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role,e.user.companyId=r.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,r,t)=>{t.d(r,{_:()=>n});let s=require("@prisma/client"),n=globalThis.prisma??new s.PrismaClient}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[1638,6206,6521,2455,4520],()=>t(56068));module.exports=s})();