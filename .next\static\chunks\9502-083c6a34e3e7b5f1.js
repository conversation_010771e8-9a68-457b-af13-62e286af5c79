"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9502],{85744:function(e,t,n){n.d(t,{M:function(){return r}});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},27733:function(e,t,n){n.d(t,{B:function(){return c}});var r=n(2265),o=n(56989),u=n(42210),i=n(67256),l=n(57437);function c(e){let t=e+"CollectionProvider",[n,c]=(0,o.b)(t),[f,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),a=e=>{let{scope:t,children:n}=e,o=r.useRef(null),u=r.useRef(new Map).current;return(0,l.jsx)(f,{scope:t,itemMap:u,collectionRef:o,children:n})};a.displayName=t;let d=e+"CollectionSlot",m=(0,i.Z8)(d),p=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=s(d,n),i=(0,u.e)(t,o.collectionRef);return(0,l.jsx)(m,{ref:i,children:r})});p.displayName=d;let v=e+"CollectionItemSlot",b="data-radix-collection-item",h=(0,i.Z8)(v),w=r.forwardRef((e,t)=>{let{scope:n,children:o,...i}=e,c=r.useRef(null),f=(0,u.e)(t,c),a=s(v,n);return r.useEffect(()=>(a.itemMap.set(c,{ref:c,...i}),()=>void a.itemMap.delete(c))),(0,l.jsx)(h,{[b]:"",ref:f,children:o})});return w.displayName=v,[{Provider:a,Slot:p,ItemSlot:w},function(t){let n=s(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${b}]`));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},c]}},56989:function(e,t,n){n.d(t,{b:function(){return i},k:function(){return u}});var r=n(2265),o=n(57437);function u(e,t){let n=r.createContext(t),u=e=>{let{children:t,...u}=e,i=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(n.Provider,{value:i,children:t})};return u.displayName=e+"Provider",[u,function(o){let u=r.useContext(n);if(u)return u;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function i(e,t=[]){let n=[],u=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return u.scopeName=e,[function(t,u){let i=r.createContext(u),l=n.length;n=[...n,u];let c=t=>{let{scope:n,children:u,...c}=t,f=n?.[e]?.[l]||i,s=r.useMemo(()=>c,Object.values(c));return(0,o.jsx)(f.Provider,{value:s,children:u})};return c.displayName=t+"Provider",[c,function(n,o){let c=o?.[e]?.[l]||i,f=r.useContext(c);if(f)return f;if(void 0!==u)return u;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(u,...t)]}},65400:function(e,t,n){n.d(t,{gm:function(){return u}});var r=n(2265);n(57437);var o=r.createContext(void 0);function u(e){let t=r.useContext(o);return e||t||"ltr"}},20966:function(e,t,n){n.d(t,{M:function(){return c}});var r,o=n(2265),u=n(51030),i=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),l=0;function c(e){let[t,n]=o.useState(i());return(0,u.b)(()=>{e||n(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}},9381:function(e,t,n){n.d(t,{WV:function(){return l},jH:function(){return c}});var r=n(2265),o=n(54887),u=n(67256),i=n(57437),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,u.Z8)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...u}=e,l=o?n:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(l,{...u,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function c(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},16459:function(e,t,n){n.d(t,{W:function(){return o}});var r=n(2265);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},73763:function(e,t,n){n.d(t,{T:function(){return l}});var r,o=n(2265),u=n(51030),i=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||u.b;function l({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[u,l,c]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),u=o.useRef(n),l=o.useRef(t);return i(()=>{l.current=t},[t]),o.useEffect(()=>{u.current!==n&&(l.current?.(n),u.current=n)},[n,u]),[n,r,l]}({defaultProp:t,onChange:n}),f=void 0!==e,s=f?e:u;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==f){let t=f?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=f},[f,r])}return[s,o.useCallback(t=>{if(f){let n="function"==typeof t?t(e):t;n!==e&&c.current?.(n)}else l(t)},[f,e,l,c])]}Symbol("RADIX:SYNC_STATE")},51030:function(e,t,n){n.d(t,{b:function(){return o}});var r=n(2265),o=globalThis?.document?r.useLayoutEffect:()=>{}}}]);