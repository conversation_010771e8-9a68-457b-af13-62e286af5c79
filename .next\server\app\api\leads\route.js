"use strict";(()=>{var e={};e.id=5350,e.ids=[5350],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},72564:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>x,originalPathname:()=>v,patchFetch:()=>E,requestAsyncStorage:()=>w,routeModule:()=>g,serverHooks:()=>_,staticGenerationAsyncStorage:()=>h,staticGenerationBailout:()=>f});var r={};a.r(r),a.d(r,{GET:()=>y,POST:()=>I});var s=a(95419),o=a(69108),n=a(99678),i=a(78070),l=a(81355),c=a(3205),d=a(9108),u=a(25252),p=a(52178);let m=u.Ry({title:u.Z_().min(1,"Title is required"),description:u.Z_().optional().nullable(),status:u.Km(["NEW","CONTACTED","QUALIFIED","PROPOSAL","NEGOTIATION","CLOSED_WON","CLOSED_LOST"]).default("NEW"),priority:u.Km(["LOW","MEDIUM","HIGH","URGENT"]).default("MEDIUM"),source:u.Z_().optional().nullable(),value:u.Rx().min(0).optional().nullable(),expectedCloseDate:u.Z_().optional().nullable(),customerId:u.Z_().optional().nullable(),contactName:u.Z_().optional().nullable(),contactEmail:u.Z_().email("Invalid email").optional().nullable(),contactPhone:u.Z_().optional().nullable(),company:u.Z_().optional().nullable(),notes:u.Z_().optional().nullable(),tags:u.IX(u.Z_()).optional()});async function y(e){try{let t=await (0,l.getServerSession)(c.L);if(!t?.user?.id)return i.Z.json({error:"Unauthorized"},{status:401});let{searchParams:a}=new URL(e.url),r=parseInt(a.get("page")||"1"),s=parseInt(a.get("limit")||"10"),o=a.get("search")||"",n=a.get("status")||"",u=a.get("priority")||"",p=a.get("sortBy")||"createdAt",m=a.get("sortOrder")||"desc",y=(r-1)*s,I={companyId:t.user.companyId||void 0};o&&(I.OR=[{title:{contains:o,mode:"insensitive"}},{description:{contains:o,mode:"insensitive"}},{contactName:{contains:o,mode:"insensitive"}},{contactEmail:{contains:o,mode:"insensitive"}},{company:{contains:o,mode:"insensitive"}}]),n&&(I.status=n),u&&(I.priority=u);let[g,w]=await Promise.all([d._.lead.findMany({where:I,skip:y,take:s,orderBy:{[p]:m},include:{customer:{select:{id:!0,name:!0,email:!0,companyName:!0}},createdBy:{select:{name:!0,email:!0}},_count:{select:{activities:!0,leadNotes:!0,tasks:!0,documents:!0}}}}),d._.lead.count({where:I})]);return i.Z.json({leads:g,pagination:{page:r,limit:s,total:w,pages:Math.ceil(w/s)}})}catch(e){return console.error("Error fetching leads:",e),i.Z.json({error:"Failed to fetch leads"},{status:500})}}async function I(e){try{let t=await (0,l.getServerSession)(c.L);if(!t?.user?.id)return i.Z.json({error:"Unauthorized"},{status:401});let a=await e.json(),r=m.parse(a),s={...r,tags:r.tags||[],companyId:t.user.companyId,createdById:t.user.id};r.expectedCloseDate&&(s.expectedCloseDate=new Date(r.expectedCloseDate));let o=await d._.lead.create({data:s,include:{customer:{select:{id:!0,name:!0,email:!0,companyName:!0}},createdBy:{select:{name:!0,email:!0}},_count:{select:{activities:!0,quotations:!0}}}});return await d._.activity.create({data:{type:"NOTE",title:"Lead Created",description:`Lead "${o.title}" was created`,leadId:o.id,customerId:o.customerId,companyId:t.user.companyId,createdById:t.user.id}}),i.Z.json(o,{status:201})}catch(e){if(e instanceof p.jm)return i.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("Error creating lead:",e),i.Z.json({error:"Failed to create lead"},{status:500})}}let g=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/leads/route",pathname:"/api/leads",filename:"route",bundlePath:"app/api/leads/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\leads\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:w,staticGenerationAsyncStorage:h,serverHooks:_,headerHooks:x,staticGenerationBailout:f}=g,v="/api/leads/route";function E(){return(0,n.patchFetch)({serverHooks:_,staticGenerationAsyncStorage:h})}},3205:(e,t,a)=>{a.d(t,{L:()=>c});var r=a(86485),s=a(10375),o=a(50694),n=a(6521),i=a.n(n),l=a(9108);let c={providers:[(0,r.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await l._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),a=t?.companyId;if(!a&&t){let e=await l._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(a=e?.id)&&await l._.user.update({where:{id:t.id},data:{companyId:a}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await i().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await l._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:a}}catch(e){return console.error("Authentication error:",e),null}}}),(0,s.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,o.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,a)=>{a.d(t,{_:()=>s});let r=require("@prisma/client"),s=globalThis.prisma??new r.PrismaClient}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[1638,6206,6521,2455,4520,5252],()=>a(72564));module.exports=r})();