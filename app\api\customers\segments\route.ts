import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const segmentType = searchParams.get('type') || 'all'

    // Define customer segments
    const segments = {
      // High Value Customers (>$10k revenue)
      highValue: {
        name: 'High Value Customers',
        description: 'Customers with total revenue over $10,000',
        where: {
          companyId: session.user.companyId,
          invoices: {
            some: {
              status: 'PAID',
              total: { gte: 10000 }
            }
          }
        }
      },

      // VIP Customers (>$50k revenue)
      vip: {
        name: 'VIP Customers',
        description: 'Customers with total revenue over $50,000',
        where: {
          companyId: session.user.companyId,
          invoices: {
            some: {
              status: 'PAID',
              total: { gte: 50000 }
            }
          }
        }
      },

      // Active Customers (recent activity)
      active: {
        name: 'Active Customers',
        description: 'Customers with activity in the last 30 days',
        where: {
          companyId: session.user.companyId,
          status: 'ACTIVE',
          OR: [
            {
              activities: {
                some: {
                  createdAt: {
                    gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
                  }
                }
              }
            },
            {
              invoices: {
                some: {
                  createdAt: {
                    gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
                  }
                }
              }
            },
            {
              quotations: {
                some: {
                  createdAt: {
                    gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
                  }
                }
              }
            }
          ]
        }
      },

      // Inactive Customers (no recent activity)
      inactive: {
        name: 'Inactive Customers',
        description: 'Customers with no activity in the last 90 days',
        where: {
          companyId: session.user.companyId,
          status: { not: 'PROSPECT' },
          AND: [
            {
              activities: {
                none: {
                  createdAt: {
                    gte: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000)
                  }
                }
              }
            },
            {
              invoices: {
                none: {
                  createdAt: {
                    gte: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000)
                  }
                }
              }
            },
            {
              quotations: {
                none: {
                  createdAt: {
                    gte: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000)
                  }
                }
              }
            }
          ]
        }
      },

      // New Customers (last 30 days)
      new: {
        name: 'New Customers',
        description: 'Customers added in the last 30 days',
        where: {
          companyId: session.user.companyId,
          createdAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
          }
        }
      },

      // Prospects (no invoices yet)
      prospects: {
        name: 'Prospects',
        description: 'Potential customers with no invoices yet',
        where: {
          companyId: session.user.companyId,
          status: 'PROSPECT',
          invoices: { none: {} }
        }
      },

      // At Risk Customers (no activity in 60 days but had recent invoices)
      atRisk: {
        name: 'At Risk Customers',
        description: 'Customers with no recent activity but previous purchases',
        where: {
          companyId: session.user.companyId,
          status: 'ACTIVE',
          invoices: {
            some: {
              status: 'PAID',
              createdAt: {
                gte: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000)
              }
            }
          },
          AND: [
            {
              activities: {
                none: {
                  createdAt: {
                    gte: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000)
                  }
                }
              }
            },
            {
              invoices: {
                none: {
                  createdAt: {
                    gte: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000)
                  }
                }
              }
            }
          ]
        }
      },

      // Frequent Buyers (multiple invoices)
      frequent: {
        name: 'Frequent Buyers',
        description: 'Customers with 3+ paid invoices',
        where: {
          companyId: session.user.companyId,
          invoices: {
            some: { status: 'PAID' }
          }
        }
      }
    }

    if (segmentType !== 'all' && !segments[segmentType as keyof typeof segments]) {
      return NextResponse.json({ error: 'Invalid segment type' }, { status: 400 })
    }

    // Get segment data
    const segmentData = []

    if (segmentType === 'all') {
      // Get all segments
      for (const [key, segment] of Object.entries(segments)) {
        const count = await prisma.customer.count({ where: segment.where })
        
        // For frequent buyers, we need a more complex query
        let customers = []
        if (key === 'frequent') {
          const frequentCustomers = await prisma.customer.findMany({
            where: segment.where,
            include: {
              _count: {
                select: {
                  invoices: {
                    where: { status: 'PAID' }
                  }
                }
              }
            }
          })
          customers = frequentCustomers.filter(c => c._count.invoices >= 3)
        } else {
          customers = await prisma.customer.findMany({
            where: segment.where,
            take: 5,
            include: {
              invoices: {
                where: { status: 'PAID' },
                select: { total: true }
              }
            }
          })
        }

        segmentData.push({
          id: key,
          name: segment.name,
          description: segment.description,
          count: key === 'frequent' ? customers.length : count,
          customers: customers.slice(0, 5).map(customer => ({
            id: customer.id,
            name: customer.name,
            email: customer.email,
            company: customer.company,
            status: customer.status,
            totalRevenue: customer.invoices?.reduce((sum, inv) => sum + inv.total, 0) || 0
          }))
        })
      }
    } else {
      // Get specific segment
      const segment = segments[segmentType as keyof typeof segments]
      let customers = []
      let count = 0

      if (segmentType === 'frequent') {
        const frequentCustomers = await prisma.customer.findMany({
          where: segment.where,
          include: {
            _count: {
              select: {
                invoices: {
                  where: { status: 'PAID' }
                }
              }
            },
            invoices: {
              where: { status: 'PAID' },
              select: { total: true }
            }
          }
        })
        customers = frequentCustomers.filter(c => c._count.invoices >= 3)
        count = customers.length
      } else {
        count = await prisma.customer.count({ where: segment.where })
        customers = await prisma.customer.findMany({
          where: segment.where,
          include: {
            invoices: {
              where: { status: 'PAID' },
              select: { total: true }
            }
          },
          orderBy: { createdAt: 'desc' }
        })
      }

      segmentData.push({
        id: segmentType,
        name: segment.name,
        description: segment.description,
        count,
        customers: customers.map(customer => ({
          id: customer.id,
          name: customer.name,
          email: customer.email,
          company: customer.company,
          status: customer.status,
          totalRevenue: customer.invoices?.reduce((sum, inv) => sum + inv.total, 0) || 0,
          createdAt: customer.createdAt
        }))
      })
    }

    return NextResponse.json({
      segments: segmentData,
      totalSegments: Object.keys(segments).length
    })

  } catch (error) {
    console.error('Error fetching customer segments:', error)
    return NextResponse.json(
      { error: 'Failed to fetch customer segments' },
      { status: 500 }
    )
  }
}
