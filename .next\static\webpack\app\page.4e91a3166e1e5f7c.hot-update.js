"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/landing/landing-page-content.tsx":
/*!*****************************************************!*\
  !*** ./components/landing/landing-page-content.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LandingPageContent: function() { return /* binding */ LandingPageContent; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/quote.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_6__);\n/* __next_internal_client_entry_do_not_use__ LandingPageContent auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Default fallback content\nconst defaultContent = {\n    hero: {\n        enabled: true,\n        title: \"Build Your SaaS Business\",\n        subtitle: \"The Complete Platform\",\n        description: \"Everything you need to launch, grow, and scale your SaaS business. From customer management to billing, we've got you covered.\",\n        primaryCTA: {\n            text: \"Start Free Trial\",\n            link: \"/auth/signup\"\n        },\n        secondaryCTA: {\n            text: \"Watch Demo\",\n            link: \"/demo\"\n        },\n        backgroundImage: \"\",\n        backgroundVideo: \"\"\n    },\n    features: {\n        enabled: true,\n        title: \"Everything You Need\",\n        subtitle: \"Powerful Features\",\n        items: [\n            {\n                id: \"1\",\n                title: \"Customer Management\",\n                description: \"Manage your customers, track interactions, and build lasting relationships.\",\n                icon: \"users\",\n                image: \"\"\n            },\n            {\n                id: \"2\",\n                title: \"Subscription Billing\",\n                description: \"Automated billing, invoicing, and payment processing for recurring revenue.\",\n                icon: \"credit-card\",\n                image: \"\"\n            },\n            {\n                id: \"3\",\n                title: \"Analytics & Reports\",\n                description: \"Comprehensive analytics to track your business performance and growth.\",\n                icon: \"bar-chart\",\n                image: \"\"\n            },\n            {\n                id: \"4\",\n                title: \"Multi-Tenant Architecture\",\n                description: \"Secure data isolation with company-based access control and team management.\",\n                icon: \"building\",\n                image: \"\"\n            },\n            {\n                id: \"5\",\n                title: \"Enterprise Security\",\n                description: \"Role-based access control with audit logs and data encryption.\",\n                icon: \"shield\",\n                image: \"\"\n            },\n            {\n                id: \"6\",\n                title: \"Global Ready\",\n                description: \"Multi-currency support and localization for worldwide businesses.\",\n                icon: \"globe\",\n                image: \"\"\n            }\n        ]\n    },\n    pricing: {\n        enabled: true,\n        title: \"Simple, Transparent Pricing\",\n        subtitle: \"Choose the plan that fits your needs\",\n        showPricingTable: true,\n        customMessage: \"\"\n    },\n    testimonials: {\n        enabled: true,\n        title: \"What Our Customers Say\",\n        subtitle: \"Trusted by thousands of businesses\",\n        items: [\n            {\n                id: \"1\",\n                name: \"John Smith\",\n                role: \"CEO\",\n                company: \"TechCorp\",\n                content: \"This platform has transformed how we manage our SaaS business. The automation features alone have saved us countless hours.\",\n                avatar: \"\",\n                rating: 5\n            },\n            {\n                id: \"2\",\n                name: \"Sarah Johnson\",\n                role: \"Founder\",\n                company: \"StartupXYZ\",\n                content: \"The best investment we've made for our business. The customer management features are incredibly powerful.\",\n                avatar: \"\",\n                rating: 5\n            },\n            {\n                id: \"3\",\n                name: \"Mike Chen\",\n                role: \"CTO\",\n                company: \"InnovateLab\",\n                content: \"Excellent platform with great support. The analytics help us make data-driven decisions every day.\",\n                avatar: \"\",\n                rating: 5\n            }\n        ]\n    },\n    faq: {\n        enabled: true,\n        title: \"Frequently Asked Questions\",\n        subtitle: \"Everything you need to know\",\n        items: [\n            {\n                id: \"1\",\n                question: \"How do I get started?\",\n                answer: \"Simply sign up for a free trial and follow our onboarding guide to set up your account. Our team is here to help you every step of the way.\"\n            },\n            {\n                id: \"2\",\n                question: \"Can I cancel anytime?\",\n                answer: \"Yes, you can cancel your subscription at any time. No long-term contracts or cancellation fees. Your data will remain accessible during the notice period.\"\n            },\n            {\n                id: \"3\",\n                question: \"Is my data secure?\",\n                answer: \"Absolutely. We use enterprise-grade security measures including encryption, regular backups, and compliance with industry standards like SOC 2 and GDPR.\"\n            },\n            {\n                id: \"4\",\n                question: \"Do you offer customer support?\",\n                answer: \"Yes, we provide 24/7 customer support via email, chat, and phone. Our premium plans also include dedicated account managers.\"\n            },\n            {\n                id: \"5\",\n                question: \"Can I integrate with other tools?\",\n                answer: \"Yes, we offer integrations with popular tools like Slack, Zapier, QuickBooks, and many more. We also provide a robust API for custom integrations.\"\n            }\n        ]\n    },\n    cta: {\n        enabled: true,\n        title: \"Ready to Get Started?\",\n        description: \"Join thousands of businesses already using our platform to grow their SaaS.\",\n        buttonText: \"Start Your Free Trial\",\n        buttonLink: \"/auth/signup\",\n        backgroundImage: \"\"\n    },\n    footer: {\n        enabled: true,\n        companyDescription: \"The complete SaaS platform for modern businesses.\",\n        links: [\n            {\n                id: \"1\",\n                title: \"Product\",\n                items: [\n                    {\n                        id: \"1\",\n                        text: \"Features\",\n                        link: \"/features\"\n                    },\n                    {\n                        id: \"2\",\n                        text: \"Pricing\",\n                        link: \"/pricing\"\n                    },\n                    {\n                        id: \"3\",\n                        text: \"Security\",\n                        link: \"/security\"\n                    },\n                    {\n                        id: \"4\",\n                        text: \"Integrations\",\n                        link: \"/integrations\"\n                    }\n                ]\n            },\n            {\n                id: \"2\",\n                title: \"Company\",\n                items: [\n                    {\n                        id: \"1\",\n                        text: \"About\",\n                        link: \"/about\"\n                    },\n                    {\n                        id: \"2\",\n                        text: \"Blog\",\n                        link: \"/blog\"\n                    },\n                    {\n                        id: \"3\",\n                        text: \"Careers\",\n                        link: \"/careers\"\n                    },\n                    {\n                        id: \"4\",\n                        text: \"Contact\",\n                        link: \"/contact\"\n                    }\n                ]\n            },\n            {\n                id: \"3\",\n                title: \"Support\",\n                items: [\n                    {\n                        id: \"1\",\n                        text: \"Help Center\",\n                        link: \"/help\"\n                    },\n                    {\n                        id: \"2\",\n                        text: \"Documentation\",\n                        link: \"/docs\"\n                    },\n                    {\n                        id: \"3\",\n                        text: \"API Reference\",\n                        link: \"/api\"\n                    },\n                    {\n                        id: \"4\",\n                        text: \"Status\",\n                        link: \"/status\"\n                    }\n                ]\n            }\n        ],\n        socialLinks: {\n            twitter: \"https://twitter.com/yourcompany\",\n            linkedin: \"https://linkedin.com/company/yourcompany\",\n            facebook: \"https://facebook.com/yourcompany\",\n            instagram: \"https://instagram.com/yourcompany\"\n        },\n        copyrightText: \"\\xa9 2024 Your Company. All rights reserved.\"\n    },\n    seo: {\n        title: \"SaaS Platform - Build Your Business\",\n        description: \"The complete SaaS platform for modern businesses. Customer management, billing, analytics, and more.\",\n        keywords: \"saas, platform, business, customer management, billing, analytics\",\n        ogImage: \"\"\n    }\n};\nconst getIconComponent = (iconName)=>{\n    const icons = {\n        users: _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        \"credit-card\": _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        \"bar-chart\": _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        building: _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        shield: _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        globe: _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        zap: _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        \"file-text\": _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n    };\n    return icons[iconName] || _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n};\nconst formatStorage = (bytes)=>{\n    const gb = bytes / (1024 * 1024 * 1024);\n    return gb >= 1 ? \"\".concat(gb, \"GB\") : \"\".concat(Math.round(gb * 1024), \"MB\");\n};\nconst getFeatureList = (plan)=>{\n    const features = [];\n    // Add usage limits\n    features.push(\"Up to \".concat(plan.maxUsers, \" users\"));\n    features.push(\"\".concat(plan.maxCompanies, \" \").concat(plan.maxCompanies === 1 ? \"company\" : \"companies\"));\n    features.push(\"\".concat(plan.maxCustomers, \" customers\"));\n    features.push(\"\".concat(plan.maxQuotations, \" quotations/month\"));\n    features.push(\"\".concat(plan.maxInvoices, \" invoices/month\"));\n    features.push(\"\".concat(formatStorage(plan.maxStorage), \" storage\"));\n    // Add feature flags\n    if (plan.features.basicReporting) features.push(\"Basic reporting\");\n    if (plan.features.emailSupport) features.push(\"Email support\");\n    if (plan.features.mobileApp) features.push(\"Mobile app access\");\n    if (plan.features.advancedAnalytics) features.push(\"Advanced analytics\");\n    if (plan.features.customBranding) features.push(\"Custom branding\");\n    if (plan.features.apiAccess) features.push(\"API access\");\n    if (plan.features.prioritySupport) features.push(\"Priority support\");\n    if (plan.features.customIntegrations) features.push(\"Custom integrations\");\n    if (plan.features.advancedSecurity) features.push(\"Advanced security\");\n    if (plan.features.dedicatedManager) features.push(\"Dedicated account manager\");\n    return features;\n};\nfunction LandingPageContent() {\n    var _content_hero, _content_features, _content_pricing, _content_testimonials, _content_faq, _content_cta, _content_footer;\n    _s();\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultContent);\n    const [plans, setPlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [openFAQ, setOpenFAQ] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isYearly, setIsYearly] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchData = async ()=>{\n            try {\n                // Fetch CMS content\n                const cmsResponse = await fetch(\"/api/super-admin/cms\");\n                const cmsData = await cmsResponse.json();\n                if (cmsData.success && cmsData.content) {\n                    // Merge with default content to ensure all sections exist\n                    setContent({\n                        ...defaultContent,\n                        ...cmsData.content\n                    });\n                }\n                // Fetch pricing plans\n                const plansResponse = await fetch(\"/api/pricing-plans?publicOnly=true\");\n                const plansData = await plansResponse.json();\n                if (plansData.success) {\n                    // Sort plans by sortOrder and filter active public plans\n                    const activePlans = plansData.data.filter((plan)=>plan.isActive && plan.isPublic).sort((a, b)=>a.sortOrder - b.sortOrder);\n                    setPlans(activePlans);\n                }\n            } catch (error) {\n                console.error(\"Error fetching data:\", error);\n            // Use default content on error\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchData();\n    }, []);\n    const getPrice = (plan)=>{\n        if (isYearly && plan.yearlyPrice) {\n            return plan.yearlyPrice / 12 // Show monthly equivalent\n            ;\n        }\n        return plan.monthlyPrice;\n    };\n    const getYearlyDiscount = (plan)=>{\n        if (!plan.yearlyPrice || !plan.monthlyPrice) return 0;\n        const yearlyMonthly = plan.yearlyPrice / 12;\n        const discount = (plan.monthlyPrice - yearlyMonthly) / plan.monthlyPrice * 100;\n        return Math.round(discount);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 469,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n            lineNumber: 468,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"border-b bg-white/95 backdrop-blur-sm sticky top-0 z-50 shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-8 w-8 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: \"SaaS Platform\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"hidden md:flex items-center space-x-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#features\",\n                                            className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                            children: \"Features\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#pricing\",\n                                            className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                            children: \"Pricing\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#testimonials\",\n                                            className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                            children: \"Testimonials\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#faq\",\n                                            className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                            children: \"FAQ\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 497,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/contact\",\n                                            className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                            children: \"Contact\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 487,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/auth/signin\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"ghost\",\n                                                className: \"text-gray-600 hover:text-gray-900\",\n                                                children: \"Sign In\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/auth/signup\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                className: \"bg-blue-600 hover:bg-blue-700\",\n                                                children: \"Get Started Free\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 506,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"md:hidden p-2\",\n                                    onClick: ()=>setMobileMenuOpen(!mobileMenuOpen),\n                                    children: mobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-6 w-6 text-gray-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-6 w-6 text-gray-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 527,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 479,\n                            columnNumber: 11\n                        }, this),\n                        mobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden mt-4 pb-4 border-t\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex flex-col space-y-4 pt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#features\",\n                                        className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"Features\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#pricing\",\n                                        className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"Pricing\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 543,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#testimonials\",\n                                        className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"Testimonials\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 550,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#faq\",\n                                        className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"FAQ\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 557,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                        href: \"/contact\",\n                                        className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"Contact\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 564,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col space-y-2 pt-4 border-t\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                href: \"/auth/signin\",\n                                                onClick: ()=>setMobileMenuOpen(false),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"ghost\",\n                                                    className: \"w-full justify-start\",\n                                                    children: \"Sign In\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 573,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 572,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                href: \"/auth/signup\",\n                                                onClick: ()=>setMobileMenuOpen(false),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    className: \"w-full bg-blue-600 hover:bg-blue-700\",\n                                                    children: \"Get Started Free\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 578,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 577,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 571,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                lineNumber: 535,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 534,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 478,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 477,\n                columnNumber: 7\n            }, this),\n            ((_content_hero = content.hero) === null || _content_hero === void 0 ? void 0 : _content_hero.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4 relative overflow-hidden\",\n                children: [\n                    content.hero.backgroundImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 z-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_6___default()), {\n                            src: content.hero.backgroundImage,\n                            alt: \"Hero Background\",\n                            fill: true,\n                            className: \"object-cover opacity-20\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 594,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                        lineNumber: 593,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto text-center relative z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto\",\n                            children: [\n                                content.hero.subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                    className: \"mb-4 text-sm px-4 py-2\",\n                                    children: content.hero.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 605,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-5xl md:text-6xl font-bold text-gray-900 mb-6\",\n                                    children: content.hero.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 609,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n                                    children: content.hero.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 612,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-4 justify-center mb-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: content.hero.primaryCTA.link,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                size: \"lg\",\n                                                className: \"text-lg px-8 py-3\",\n                                                children: [\n                                                    content.hero.primaryCTA.text,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"ml-2 h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 619,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 617,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 616,\n                                            columnNumber: 17\n                                        }, this),\n                                        content.hero.secondaryCTA.text && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: content.hero.secondaryCTA.link,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                size: \"lg\",\n                                                variant: \"outline\",\n                                                className: \"text-lg px-8 py-3\",\n                                                children: content.hero.secondaryCTA.text\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 623,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 615,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 603,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                        lineNumber: 602,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 591,\n                columnNumber: 9\n            }, this),\n            ((_content_features = content.features) === null || _content_features === void 0 ? void 0 : _content_features.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"features\",\n                className: \"py-20 px-4 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                    children: content.features.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 640,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                                    children: content.features.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 643,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 639,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: content.features.items.map((feature)=>{\n                                const IconComponent = getIconComponent(feature.icon);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"border-0 shadow-lg hover:shadow-xl transition-shadow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                        className: \"h-6 w-6 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 655,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 654,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-xl\",\n                                                    children: feature.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 657,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 653,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                className: \"text-gray-600\",\n                                                children: feature.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 660,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 659,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, feature.id, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 652,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 648,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 638,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 637,\n                columnNumber: 9\n            }, this),\n            ((_content_pricing = content.pricing) === null || _content_pricing === void 0 ? void 0 : _content_pricing.enabled) && plans.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"pricing\",\n                className: \"py-20 px-4 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                    children: content.pricing.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 677,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 max-w-2xl mx-auto mb-8\",\n                                    children: content.pricing.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 680,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-4 mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm \".concat(!isYearly ? \"text-gray-900 font-medium\" : \"text-gray-500\"),\n                                            children: \"Monthly\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 686,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Switch, {\n                                            checked: isYearly,\n                                            onCheckedChange: setIsYearly\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 689,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm \".concat(isYearly ? \"text-gray-900 font-medium\" : \"text-gray-500\"),\n                                            children: \"Yearly\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 693,\n                                            columnNumber: 17\n                                        }, this),\n                                        plans.some((plan)=>getYearlyDiscount(plan) > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            className: \"ml-2 bg-green-100 text-green-800\",\n                                            children: [\n                                                \"Save up to \",\n                                                Math.max(...plans.map(getYearlyDiscount)),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 697,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 685,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 676,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto\",\n                            children: plans.map((plan, index)=>{\n                                const features = getFeatureList(plan);\n                                const price = getPrice(plan);\n                                const discount = getYearlyDiscount(plan);\n                                const isPopular = index === 1 // Middle plan is popular\n                                ;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"relative \".concat(isPopular ? \"border-blue-500 shadow-xl scale-105\" : \"border-gray-200 shadow-lg\", \" bg-white\"),\n                                    children: [\n                                        isPopular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -top-4 left-1/2 transform -translate-x-1/2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                className: \"bg-blue-500 text-white px-4 py-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 717,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    \"Most Popular\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 716,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 715,\n                                            columnNumber: 23\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            className: \"text-center pb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: plan.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 724,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                    className: \"text-gray-600 mt-2\",\n                                                    children: plan.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 725,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-baseline justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-5xl font-bold text-gray-900\",\n                                                                    children: [\n                                                                        \"$\",\n                                                                        price.toFixed(0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                    lineNumber: 731,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-500 ml-1\",\n                                                                    children: \"/month\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                    lineNumber: 734,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 730,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        isYearly && discount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-green-600 mt-2\",\n                                                            children: [\n                                                                \"Save \",\n                                                                discount,\n                                                                \"% with yearly billing\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 737,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        plan.trialDays > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-blue-600 mt-2\",\n                                                            children: [\n                                                                plan.trialDays,\n                                                                \"-day free trial\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 742,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 729,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 723,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        features.slice(0, 8).map((feature, featureIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-green-500 flex-shrink-0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                        lineNumber: 753,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-700 text-sm\",\n                                                                        children: feature\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                        lineNumber: 754,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, featureIndex, true, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                lineNumber: 752,\n                                                                columnNumber: 27\n                                                            }, this)),\n                                                        features.length > 8 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500 italic\",\n                                                            children: [\n                                                                \"+\",\n                                                                features.length - 8,\n                                                                \" more features\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 758,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 750,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                    href: \"/auth/signup\",\n                                                    className: \"block\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        className: \"w-full \".concat(isPopular ? \"bg-blue-600 hover:bg-blue-700\" : \"\"),\n                                                        variant: isPopular ? \"default\" : \"outline\",\n                                                        size: \"lg\",\n                                                        children: [\n                                                            \"Get Started\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"ml-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                lineNumber: 771,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 765,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 764,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 749,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, plan.id, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 713,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 705,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mt-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"max-w-2xl mx-auto border-gray-200 shadow-lg bg-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                            children: \"Need something custom?\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 784,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-6\",\n                                            children: \"Contact our sales team for enterprise pricing, custom features, and dedicated support.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 787,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    size: \"lg\",\n                                                    variant: \"outline\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 792,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Contact Sales\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 791,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    size: \"lg\",\n                                                    variant: \"ghost\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 796,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Schedule Demo\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 795,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 790,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 783,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                lineNumber: 782,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 781,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 675,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 674,\n                columnNumber: 9\n            }, this),\n            ((_content_testimonials = content.testimonials) === null || _content_testimonials === void 0 ? void 0 : _content_testimonials.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"testimonials\",\n                className: \"py-20 px-4 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                    children: content.testimonials.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 812,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                                    children: content.testimonials.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 815,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 811,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: content.testimonials.items.map((testimonial)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"border-0 shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    ...Array(testimonial.rating)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-5 w-5 text-yellow-400 fill-current\"\n                                                    }, i, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 826,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 824,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"h-8 w-8 text-gray-300 mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 829,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-6 italic\",\n                                                children: [\n                                                    '\"',\n                                                    testimonial.content,\n                                                    '\"'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 830,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    testimonial.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                                        src: testimonial.avatar,\n                                                        alt: testimonial.name,\n                                                        width: 48,\n                                                        height: 48,\n                                                        className: \"rounded-full mr-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 835,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-gray-200 rounded-full mr-4 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-6 w-6 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 844,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 843,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-semibold text-gray-900\",\n                                                                children: testimonial.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                lineNumber: 848,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: [\n                                                                    testimonial.role,\n                                                                    \", \",\n                                                                    testimonial.company\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                lineNumber: 849,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 847,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 833,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 823,\n                                        columnNumber: 19\n                                    }, this)\n                                }, testimonial.id, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 822,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 820,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 810,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 809,\n                columnNumber: 9\n            }, this),\n            ((_content_faq = content.faq) === null || _content_faq === void 0 ? void 0 : _content_faq.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto max-w-4xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                    children: content.faq.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 865,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600\",\n                                    children: content.faq.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 868,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 864,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: content.faq.items.map((faq)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"border shadow-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"p-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"w-full p-6 text-left flex items-center justify-between hover:bg-gray-50\",\n                                                onClick: ()=>setOpenFAQ(openFAQ === faq.id ? null : faq.id),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold text-gray-900\",\n                                                        children: faq.question\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 881,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    openFAQ === faq.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 883,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 885,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 877,\n                                                columnNumber: 21\n                                            }, this),\n                                            openFAQ === faq.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-6 pb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: faq.answer\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 890,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 889,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 876,\n                                        columnNumber: 19\n                                    }, this)\n                                }, faq.id, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 875,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 873,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 863,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 862,\n                columnNumber: 9\n            }, this),\n            ((_content_cta = content.cta) === null || _content_cta === void 0 ? void 0 : _content_cta.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4 bg-blue-600 relative overflow-hidden\",\n                children: [\n                    content.cta.backgroundImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 z-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_6___default()), {\n                            src: content.cta.backgroundImage,\n                            alt: \"CTA Background\",\n                            fill: true,\n                            className: \"object-cover opacity-20\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 906,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                        lineNumber: 905,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto text-center relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl font-bold text-white mb-6\",\n                                children: content.cta.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                lineNumber: 915,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-blue-100 mb-8 max-w-2xl mx-auto\",\n                                children: content.cta.description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                lineNumber: 918,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                href: content.cta.buttonLink,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    size: \"lg\",\n                                    variant: \"secondary\",\n                                    className: \"text-lg px-8 py-3\",\n                                    children: [\n                                        content.cta.buttonText,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"ml-2 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 924,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 922,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                lineNumber: 921,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                        lineNumber: 914,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 903,\n                columnNumber: 9\n            }, this),\n            ((_content_footer = content.footer) === null || _content_footer === void 0 ? void 0 : _content_footer.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-gray-900 text-white py-12 px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-4 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-6 w-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 938,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-lg font-bold\",\n                                                    children: \"SaaS Platform\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 939,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 937,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400\",\n                                            children: content.footer.companyDescription\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 941,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 936,\n                                    columnNumber: 15\n                                }, this),\n                                content.footer.links.map((linkGroup)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-4\",\n                                                children: linkGroup.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 947,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-2 text-gray-400\",\n                                                children: linkGroup.items.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                            href: link.link,\n                                                            className: \"hover:text-white\",\n                                                            children: link.text\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 951,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, link.id, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 950,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 948,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, linkGroup.id, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 946,\n                                        columnNumber: 17\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 935,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: content.footer.copyrightText\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                lineNumber: 961,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 960,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 934,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 933,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n        lineNumber: 475,\n        columnNumber: 5\n    }, this);\n}\n_s(LandingPageContent, \"Ob77mu/u4HMnkCjOlnHJhwEopZQ=\");\n_c = LandingPageContent;\nvar _c;\n$RefreshReg$(_c, \"LandingPageContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/landing/landing-page-content.tsx\n"));

/***/ })

});