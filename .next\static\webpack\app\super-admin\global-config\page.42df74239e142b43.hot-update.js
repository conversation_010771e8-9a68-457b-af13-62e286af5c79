"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/super-admin/global-config/page",{

/***/ "(app-pages-browser)/./app/super-admin/global-config/page.tsx":
/*!************************************************!*\
  !*** ./app/super-admin/global-config/page.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GlobalConfigPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building_Eye_EyeOff_Globe_Lock_Mail_RefreshCw_Save_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Eye,EyeOff,Globe,Lock,Mail,RefreshCw,Save,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Eye_EyeOff_Globe_Lock_Mail_RefreshCw_Save_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Eye,EyeOff,Globe,Lock,Mail,RefreshCw,Save,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Eye_EyeOff_Globe_Lock_Mail_RefreshCw_Save_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Eye,EyeOff,Globe,Lock,Mail,RefreshCw,Save,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Eye_EyeOff_Globe_Lock_Mail_RefreshCw_Save_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Eye,EyeOff,Globe,Lock,Mail,RefreshCw,Save,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Eye_EyeOff_Globe_Lock_Mail_RefreshCw_Save_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Eye,EyeOff,Globe,Lock,Mail,RefreshCw,Save,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Eye_EyeOff_Globe_Lock_Mail_RefreshCw_Save_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Eye,EyeOff,Globe,Lock,Mail,RefreshCw,Save,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Eye_EyeOff_Globe_Lock_Mail_RefreshCw_Save_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Eye,EyeOff,Globe,Lock,Mail,RefreshCw,Save,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Eye_EyeOff_Globe_Lock_Mail_RefreshCw_Save_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Eye,EyeOff,Globe,Lock,Mail,RefreshCw,Save,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Eye_EyeOff_Globe_Lock_Mail_RefreshCw_Save_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Eye,EyeOff,Globe,Lock,Mail,RefreshCw,Save,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Eye_EyeOff_Globe_Lock_Mail_RefreshCw_Save_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Eye,EyeOff,Globe,Lock,Mail,RefreshCw,Save,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Eye_EyeOff_Globe_Lock_Mail_RefreshCw_Save_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Eye,EyeOff,Globe,Lock,Mail,RefreshCw,Save,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction GlobalConfigPage() {\n    var _session_user;\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [config, setConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        appName: \"SaaS Platform\",\n        appDescription: \"Modern SaaS application for business management\",\n        appUrl: \"https://yourapp.com\",\n        supportEmail: \"<EMAIL>\",\n        companyName: \"Your Company\",\n        companyAddress: \"123 Business St, City, Country\",\n        companyPhone: \"+****************\",\n        logoUrl: \"\",\n        faviconUrl: \"\",\n        primaryColor: \"#3b82f6\",\n        secondaryColor: \"#64748b\",\n        accentColor: \"#10b981\",\n        backgroundColor: \"#ffffff\",\n        textColor: \"#1f2937\",\n        theme: \"light\",\n        fontFamily: \"Inter, sans-serif\",\n        customCss: \"\",\n        timezone: \"UTC\",\n        dateFormat: \"MM/DD/YYYY\",\n        currency: \"USD\",\n        language: \"en\",\n        enableRegistration: true,\n        enableTrials: true,\n        enableMultiTenant: true,\n        enableApiAccess: true,\n        enableWebhooks: true,\n        sessionTimeout: 24,\n        passwordMinLength: 8,\n        requireTwoFactor: false,\n        allowSocialLogin: true,\n        emailProvider: \"smtp\",\n        smtpHost: \"\",\n        smtpPort: 587,\n        smtpUsername: \"\",\n        smtpPassword: \"\",\n        smtpSecure: true,\n        maxUsersPerCompany: 100,\n        maxCompaniesPerUser: 5,\n        defaultStorageLimit: 5,\n        maintenanceMode: false,\n        maintenanceMessage: \"System is under maintenance. Please check back later.\"\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                lineNumber: 145,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n            lineNumber: 144,\n            columnNumber: 7\n        }, this);\n    }\n    if (status === \"unauthenticated\") {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.redirect)(\"/auth/signin\");\n    }\n    if ((session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.role) !== \"SUPER_ADMIN\") {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.redirect)(\"/dashboard\");\n    }\n    const fetchConfig = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/super-admin/global-config\");\n            const data = await response.json();\n            if (data.success) {\n                setConfig({\n                    ...config,\n                    ...data.config\n                });\n            }\n        } catch (error) {\n            console.error(\"Error fetching config:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_12__.toast.error(\"Failed to load configuration\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const saveConfig = async ()=>{\n        try {\n            setSaving(true);\n            const response = await fetch(\"/api/super-admin/global-config\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(config)\n            });\n            const data = await response.json();\n            if (data.success) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_12__.toast.success(\"Configuration saved successfully\");\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_12__.toast.error(data.error || \"Failed to save configuration\");\n            }\n        } catch (error) {\n            console.error(\"Error saving config:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_12__.toast.error(\"Failed to save configuration\");\n        } finally{\n            setSaving(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchConfig();\n    }, []);\n    const updateConfig = (key, value)=>{\n        setConfig((prev)=>({\n                ...prev,\n                [key]: value\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row sm:items-center justify-between gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Eye_EyeOff_Globe_Lock_Mail_RefreshCw_Save_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"Global Configuration\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 mt-1\",\n                                children: \"Manage system-wide settings and configuration\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                variant: \"outline\",\n                                onClick: fetchConfig,\n                                disabled: loading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Eye_EyeOff_Globe_Lock_Mail_RefreshCw_Save_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 \".concat(loading ? \"animate-spin\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                onClick: saveConfig,\n                                disabled: saving,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Eye_EyeOff_Globe_Lock_Mail_RefreshCw_Save_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 \".concat(saving ? \"animate-spin\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Save Changes\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                lineNumber: 212,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_11__.Tabs, {\n                defaultValue: \"application\",\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_11__.TabsList, {\n                        className: \"grid w-full grid-cols-7\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_11__.TabsTrigger, {\n                                value: \"application\",\n                                children: \"Application\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_11__.TabsTrigger, {\n                                value: \"branding\",\n                                children: \"Branding\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_11__.TabsTrigger, {\n                                value: \"system\",\n                                children: \"System\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_11__.TabsTrigger, {\n                                value: \"features\",\n                                children: \"Features\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_11__.TabsTrigger, {\n                                value: \"security\",\n                                children: \"Security\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_11__.TabsTrigger, {\n                                value: \"email\",\n                                children: \"Email\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_11__.TabsTrigger, {\n                                value: \"limits\",\n                                children: \"Limits\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_11__.TabsContent, {\n                        value: \"application\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Eye_EyeOff_Globe_Lock_Mail_RefreshCw_Save_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Application Settings\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"Basic application information and company details\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            htmlFor: \"appName\",\n                                                            children: \"Application Name\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                            id: \"appName\",\n                                                            value: config.appName,\n                                                            onChange: (e)=>updateConfig(\"appName\", e.target.value),\n                                                            placeholder: \"Your SaaS App\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                            lineNumber: 260,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            htmlFor: \"appUrl\",\n                                                            children: \"Application URL\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                            id: \"appUrl\",\n                                                            value: config.appUrl,\n                                                            onChange: (e)=>updateConfig(\"appUrl\", e.target.value),\n                                                            placeholder: \"https://yourapp.com\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                    htmlFor: \"appDescription\",\n                                                    children: \"Application Description\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__.Textarea, {\n                                                    id: \"appDescription\",\n                                                    value: config.appDescription,\n                                                    onChange: (e)=>updateConfig(\"appDescription\", e.target.value),\n                                                    placeholder: \"Brief description of your application\",\n                                                    rows: 3\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            htmlFor: \"companyName\",\n                                                            children: \"Company Name\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                            id: \"companyName\",\n                                                            value: config.companyName,\n                                                            onChange: (e)=>updateConfig(\"companyName\", e.target.value),\n                                                            placeholder: \"Your Company Inc.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            htmlFor: \"supportEmail\",\n                                                            children: \"Support Email\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                            id: \"supportEmail\",\n                                                            type: \"email\",\n                                                            value: config.supportEmail,\n                                                            onChange: (e)=>updateConfig(\"supportEmail\", e.target.value),\n                                                            placeholder: \"<EMAIL>\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            htmlFor: \"companyPhone\",\n                                                            children: \"Company Phone\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                            lineNumber: 313,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                            id: \"companyPhone\",\n                                                            value: config.companyPhone,\n                                                            onChange: (e)=>updateConfig(\"companyPhone\", e.target.value),\n                                                            placeholder: \"+****************\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                            lineNumber: 314,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            htmlFor: \"companyAddress\",\n                                                            children: \"Company Address\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                            lineNumber: 322,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                            id: \"companyAddress\",\n                                                            value: config.companyAddress,\n                                                            onChange: (e)=>updateConfig(\"companyAddress\", e.target.value),\n                                                            placeholder: \"123 Business St, City, Country\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                            lineNumber: 323,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_11__.TabsContent, {\n                        value: \"system\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Eye_EyeOff_Globe_Lock_Mail_RefreshCw_Save_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"System Settings\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"Configure system-wide preferences and defaults\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            htmlFor: \"timezone\",\n                                                            children: \"Default Timezone\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                            value: config.timezone,\n                                                            onValueChange: (value)=>updateConfig(\"timezone\", value),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {}, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                        lineNumber: 353,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                    lineNumber: 352,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                            value: \"UTC\",\n                                                                            children: \"UTC\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                            lineNumber: 356,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                            value: \"America/New_York\",\n                                                                            children: \"Eastern Time\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                            lineNumber: 357,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                            value: \"America/Chicago\",\n                                                                            children: \"Central Time\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                            lineNumber: 358,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                            value: \"America/Denver\",\n                                                                            children: \"Mountain Time\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                            lineNumber: 359,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                            value: \"America/Los_Angeles\",\n                                                                            children: \"Pacific Time\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                            lineNumber: 360,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                            value: \"Europe/London\",\n                                                                            children: \"London\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                            lineNumber: 361,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                            value: \"Europe/Paris\",\n                                                                            children: \"Paris\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                            lineNumber: 362,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                            value: \"Asia/Tokyo\",\n                                                                            children: \"Tokyo\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                            lineNumber: 363,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                            value: \"Asia/Shanghai\",\n                                                                            children: \"Shanghai\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                            lineNumber: 364,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                            value: \"Asia/Kolkata\",\n                                                                            children: \"India\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                            lineNumber: 365,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                    lineNumber: 355,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                            lineNumber: 351,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            htmlFor: \"dateFormat\",\n                                                            children: \"Date Format\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                            value: config.dateFormat,\n                                                            onValueChange: (value)=>updateConfig(\"dateFormat\", value),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {}, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                        lineNumber: 373,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                    lineNumber: 372,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                            value: \"MM/DD/YYYY\",\n                                                                            children: \"MM/DD/YYYY\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                            lineNumber: 376,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                            value: \"DD/MM/YYYY\",\n                                                                            children: \"DD/MM/YYYY\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                            lineNumber: 377,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                            value: \"YYYY-MM-DD\",\n                                                                            children: \"YYYY-MM-DD\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                            lineNumber: 378,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                            value: \"DD-MM-YYYY\",\n                                                                            children: \"DD-MM-YYYY\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                            lineNumber: 379,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                    lineNumber: 375,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                            lineNumber: 371,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            htmlFor: \"currency\",\n                                                            children: \"Default Currency\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                            value: config.currency,\n                                                            onValueChange: (value)=>updateConfig(\"currency\", value),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {}, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                        lineNumber: 390,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                    lineNumber: 389,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                            value: \"USD\",\n                                                                            children: \"USD - US Dollar\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                            lineNumber: 393,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                            value: \"EUR\",\n                                                                            children: \"EUR - Euro\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                            lineNumber: 394,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                            value: \"GBP\",\n                                                                            children: \"GBP - British Pound\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                            lineNumber: 395,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                            value: \"JPY\",\n                                                                            children: \"JPY - Japanese Yen\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                            lineNumber: 396,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                            value: \"INR\",\n                                                                            children: \"INR - Indian Rupee\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                            lineNumber: 397,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                            value: \"CAD\",\n                                                                            children: \"CAD - Canadian Dollar\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                            lineNumber: 398,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                            value: \"AUD\",\n                                                                            children: \"AUD - Australian Dollar\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                            lineNumber: 399,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                    lineNumber: 392,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            htmlFor: \"language\",\n                                                            children: \"Default Language\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                            lineNumber: 404,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                            value: config.language,\n                                                            onValueChange: (value)=>updateConfig(\"language\", value),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {}, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                        lineNumber: 407,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                    lineNumber: 406,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                            value: \"en\",\n                                                                            children: \"English\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                            lineNumber: 410,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                            value: \"es\",\n                                                                            children: \"Spanish\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                            lineNumber: 411,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                            value: \"fr\",\n                                                                            children: \"French\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                            lineNumber: 412,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                            value: \"de\",\n                                                                            children: \"German\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                            lineNumber: 413,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                            value: \"it\",\n                                                                            children: \"Italian\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                            lineNumber: 414,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                            value: \"pt\",\n                                                                            children: \"Portuguese\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                            lineNumber: 415,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                            value: \"ja\",\n                                                                            children: \"Japanese\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                            lineNumber: 416,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                            value: \"ko\",\n                                                                            children: \"Korean\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                            lineNumber: 417,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                            value: \"zh\",\n                                                                            children: \"Chinese\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                            lineNumber: 418,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                            value: \"hi\",\n                                                                            children: \"Hindi\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                            lineNumber: 419,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                    lineNumber: 409,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                            lineNumber: 405,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4 p-4 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"maintenanceMode\",\n                                                                    className: \"text-base font-medium\",\n                                                                    children: \"Maintenance Mode\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                    lineNumber: 429,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: \"Enable to put the application in maintenance mode\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                    lineNumber: 432,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                            lineNumber: 428,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                            id: \"maintenanceMode\",\n                                                            checked: config.maintenanceMode,\n                                                            onCheckedChange: (checked)=>updateConfig(\"maintenanceMode\", checked)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 17\n                                                }, this),\n                                                config.maintenanceMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            htmlFor: \"maintenanceMessage\",\n                                                            children: \"Maintenance Message\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__.Textarea, {\n                                                            id: \"maintenanceMessage\",\n                                                            value: config.maintenanceMessage,\n                                                            onChange: (e)=>updateConfig(\"maintenanceMessage\", e.target.value),\n                                                            placeholder: \"System is under maintenance...\",\n                                                            rows: 2\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                        lineNumber: 336,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_11__.TabsContent, {\n                        value: \"features\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Eye_EyeOff_Globe_Lock_Mail_RefreshCw_Save_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Feature Flags\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                            lineNumber: 463,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"Enable or disable application features\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                            lineNumber: 467,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                    lineNumber: 462,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-4 border rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                        htmlFor: \"enableRegistration\",\n                                                                        className: \"text-base font-medium\",\n                                                                        children: \"User Registration\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                        lineNumber: 476,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: \"Allow new users to register accounts\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                        lineNumber: 479,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                lineNumber: 475,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                                id: \"enableRegistration\",\n                                                                checked: config.enableRegistration,\n                                                                onCheckedChange: (checked)=>updateConfig(\"enableRegistration\", checked)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                lineNumber: 483,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                        lineNumber: 474,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-4 border rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                        htmlFor: \"enableTrials\",\n                                                                        className: \"text-base font-medium\",\n                                                                        children: \"Free Trials\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                        lineNumber: 492,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: \"Enable free trial periods for new users\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                        lineNumber: 495,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                lineNumber: 491,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                                id: \"enableTrials\",\n                                                                checked: config.enableTrials,\n                                                                onCheckedChange: (checked)=>updateConfig(\"enableTrials\", checked)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                lineNumber: 499,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                        lineNumber: 490,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-4 border rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                        htmlFor: \"enableMultiTenant\",\n                                                                        className: \"text-base font-medium\",\n                                                                        children: \"Multi-Tenant\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                        lineNumber: 508,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: \"Allow users to manage multiple companies\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                        lineNumber: 511,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                lineNumber: 507,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                                id: \"enableMultiTenant\",\n                                                                checked: config.enableMultiTenant,\n                                                                onCheckedChange: (checked)=>updateConfig(\"enableMultiTenant\", checked)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                lineNumber: 515,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                        lineNumber: 506,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-4 border rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                        htmlFor: \"enableApiAccess\",\n                                                                        className: \"text-base font-medium\",\n                                                                        children: \"API Access\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                        lineNumber: 526,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: \"Enable REST API access for integrations\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                        lineNumber: 529,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                lineNumber: 525,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                                id: \"enableApiAccess\",\n                                                                checked: config.enableApiAccess,\n                                                                onCheckedChange: (checked)=>updateConfig(\"enableApiAccess\", checked)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                lineNumber: 533,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                        lineNumber: 524,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-4 border rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                        htmlFor: \"enableWebhooks\",\n                                                                        className: \"text-base font-medium\",\n                                                                        children: \"Webhooks\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                        lineNumber: 542,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: \"Enable webhook notifications for events\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                        lineNumber: 545,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                lineNumber: 541,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                                id: \"enableWebhooks\",\n                                                                checked: config.enableWebhooks,\n                                                                onCheckedChange: (checked)=>updateConfig(\"enableWebhooks\", checked)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                lineNumber: 549,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                        lineNumber: 540,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-4 border rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                        htmlFor: \"allowSocialLogin\",\n                                                                        className: \"text-base font-medium\",\n                                                                        children: \"Social Login\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                        lineNumber: 558,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: \"Allow login with Google, GitHub, etc.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                        lineNumber: 561,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                lineNumber: 557,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                                id: \"allowSocialLogin\",\n                                                                checked: config.allowSocialLogin,\n                                                                onCheckedChange: (checked)=>updateConfig(\"allowSocialLogin\", checked)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                lineNumber: 565,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                        lineNumber: 556,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                lineNumber: 523,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                    lineNumber: 471,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                            lineNumber: 461,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                        lineNumber: 460,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_11__.TabsContent, {\n                        value: \"security\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Eye_EyeOff_Globe_Lock_Mail_RefreshCw_Save_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                    lineNumber: 582,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Security Settings\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                            lineNumber: 581,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"Configure security policies and authentication settings\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                            lineNumber: 585,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                    lineNumber: 580,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            htmlFor: \"sessionTimeout\",\n                                                            children: \"Session Timeout (hours)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                            lineNumber: 592,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                            id: \"sessionTimeout\",\n                                                            type: \"number\",\n                                                            value: config.sessionTimeout,\n                                                            onChange: (e)=>updateConfig(\"sessionTimeout\", parseInt(e.target.value) || 24),\n                                                            min: \"1\",\n                                                            max: \"168\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                            lineNumber: 593,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: \"Maximum session duration before auto-logout\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                            lineNumber: 601,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                    lineNumber: 591,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            htmlFor: \"passwordMinLength\",\n                                                            children: \"Minimum Password Length\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                            lineNumber: 604,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                            id: \"passwordMinLength\",\n                                                            type: \"number\",\n                                                            value: config.passwordMinLength,\n                                                            onChange: (e)=>updateConfig(\"passwordMinLength\", parseInt(e.target.value) || 8),\n                                                            min: \"6\",\n                                                            max: \"32\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                            lineNumber: 605,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: \"Minimum characters required for passwords\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                            lineNumber: 613,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                    lineNumber: 603,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                            lineNumber: 590,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-4 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            htmlFor: \"requireTwoFactor\",\n                                                            className: \"text-base font-medium\",\n                                                            children: \"Require Two-Factor Authentication\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                            lineNumber: 619,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Force all users to enable 2FA for enhanced security\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                            lineNumber: 622,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                    lineNumber: 618,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                    id: \"requireTwoFactor\",\n                                                    checked: config.requireTwoFactor,\n                                                    onCheckedChange: (checked)=>updateConfig(\"requireTwoFactor\", checked)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                    lineNumber: 626,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                            lineNumber: 617,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                    lineNumber: 589,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                            lineNumber: 579,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                        lineNumber: 578,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_11__.TabsContent, {\n                        value: \"email\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Eye_EyeOff_Globe_Lock_Mail_RefreshCw_Save_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                    lineNumber: 641,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Email Configuration\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                            lineNumber: 640,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"Configure SMTP settings for transactional emails\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                            lineNumber: 644,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                    lineNumber: 639,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                    htmlFor: \"emailProvider\",\n                                                    children: \"Email Provider\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                    lineNumber: 650,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                    value: config.emailProvider,\n                                                    onValueChange: (value)=>updateConfig(\"emailProvider\", value),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {}, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                lineNumber: 653,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                            lineNumber: 652,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                    value: \"smtp\",\n                                                                    children: \"Custom SMTP\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                    lineNumber: 656,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                    value: \"sendgrid\",\n                                                                    children: \"SendGrid\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                    lineNumber: 657,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                    value: \"mailgun\",\n                                                                    children: \"Mailgun\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                    lineNumber: 658,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                    value: \"ses\",\n                                                                    children: \"Amazon SES\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                    lineNumber: 659,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                    value: \"postmark\",\n                                                                    children: \"Postmark\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                    lineNumber: 660,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                            lineNumber: 655,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                    lineNumber: 651,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                            lineNumber: 649,\n                                            columnNumber: 15\n                                        }, this),\n                                        config.emailProvider === \"smtp\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"smtpHost\",\n                                                                    children: \"SMTP Host\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                    lineNumber: 669,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    id: \"smtpHost\",\n                                                                    value: config.smtpHost,\n                                                                    onChange: (e)=>updateConfig(\"smtpHost\", e.target.value),\n                                                                    placeholder: \"smtp.gmail.com\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                    lineNumber: 670,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                            lineNumber: 668,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"smtpPort\",\n                                                                    children: \"SMTP Port\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                    lineNumber: 678,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    id: \"smtpPort\",\n                                                                    type: \"number\",\n                                                                    value: config.smtpPort,\n                                                                    onChange: (e)=>updateConfig(\"smtpPort\", parseInt(e.target.value) || 587),\n                                                                    placeholder: \"587\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                    lineNumber: 679,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                            lineNumber: 677,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                    lineNumber: 667,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"smtpUsername\",\n                                                                    children: \"SMTP Username\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                    lineNumber: 691,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    id: \"smtpUsername\",\n                                                                    value: config.smtpUsername,\n                                                                    onChange: (e)=>updateConfig(\"smtpUsername\", e.target.value),\n                                                                    placeholder: \"<EMAIL>\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                    lineNumber: 692,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                            lineNumber: 690,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"smtpPassword\",\n                                                                    children: \"SMTP Password\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                    lineNumber: 700,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                            id: \"smtpPassword\",\n                                                                            type: showPassword ? \"text\" : \"password\",\n                                                                            value: config.smtpPassword,\n                                                                            onChange: (e)=>updateConfig(\"smtpPassword\", e.target.value),\n                                                                            placeholder: \"your-app-password\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                            lineNumber: 702,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                            type: \"button\",\n                                                                            variant: \"ghost\",\n                                                                            size: \"sm\",\n                                                                            className: \"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent\",\n                                                                            onClick: ()=>setShowPassword(!showPassword),\n                                                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Eye_EyeOff_Globe_Lock_Mail_RefreshCw_Save_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                                lineNumber: 716,\n                                                                                columnNumber: 43\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Eye_EyeOff_Globe_Lock_Mail_RefreshCw_Save_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                                lineNumber: 716,\n                                                                                columnNumber: 76\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                            lineNumber: 709,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                    lineNumber: 701,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                            lineNumber: 699,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                    lineNumber: 689,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-4 border rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"smtpSecure\",\n                                                                    className: \"text-base font-medium\",\n                                                                    children: \"Use TLS/SSL\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                    lineNumber: 724,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: \"Enable secure connection for SMTP\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                                    lineNumber: 727,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                            lineNumber: 723,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                            id: \"smtpSecure\",\n                                                            checked: config.smtpSecure,\n                                                            onCheckedChange: (checked)=>updateConfig(\"smtpSecure\", checked)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                            lineNumber: 731,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                    lineNumber: 722,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                            lineNumber: 666,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                    lineNumber: 648,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                            lineNumber: 638,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                        lineNumber: 637,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_11__.TabsContent, {\n                        value: \"limits\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Eye_EyeOff_Globe_Lock_Mail_RefreshCw_Save_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                    lineNumber: 748,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"System Limits\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                            lineNumber: 747,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"Configure default limits and quotas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                            lineNumber: 751,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                    lineNumber: 746,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"maxUsersPerCompany\",\n                                                        children: \"Max Users per Company\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                        lineNumber: 758,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        id: \"maxUsersPerCompany\",\n                                                        type: \"number\",\n                                                        value: config.maxUsersPerCompany,\n                                                        onChange: (e)=>updateConfig(\"maxUsersPerCompany\", parseInt(e.target.value) || 100),\n                                                        min: \"1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                        lineNumber: 759,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"Default limit for new companies\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                        lineNumber: 766,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                lineNumber: 757,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"maxCompaniesPerUser\",\n                                                        children: \"Max Companies per User\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                        lineNumber: 769,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        id: \"maxCompaniesPerUser\",\n                                                        type: \"number\",\n                                                        value: config.maxCompaniesPerUser,\n                                                        onChange: (e)=>updateConfig(\"maxCompaniesPerUser\", parseInt(e.target.value) || 5),\n                                                        min: \"1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                        lineNumber: 770,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"How many companies a user can join\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                        lineNumber: 777,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                lineNumber: 768,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"defaultStorageLimit\",\n                                                        children: \"Default Storage Limit (GB)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                        lineNumber: 780,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        id: \"defaultStorageLimit\",\n                                                        type: \"number\",\n                                                        value: config.defaultStorageLimit,\n                                                        onChange: (e)=>updateConfig(\"defaultStorageLimit\", parseInt(e.target.value) || 5),\n                                                        min: \"1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                        lineNumber: 781,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"Default storage quota for new companies\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                        lineNumber: 788,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                                lineNumber: 779,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                        lineNumber: 756,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                                    lineNumber: 755,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                            lineNumber: 745,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                        lineNumber: 744,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\global-config\\\\page.tsx\",\n        lineNumber: 210,\n        columnNumber: 5\n    }, this);\n}\n_s(GlobalConfigPage, \"puGX4YYjPvz5hO02KkTimZ/IgJI=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession\n    ];\n});\n_c = GlobalConfigPage;\nvar _c;\n$RefreshReg$(_c, \"GlobalConfigPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/super-admin/global-config/page.tsx\n"));

/***/ })

});