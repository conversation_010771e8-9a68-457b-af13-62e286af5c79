(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4159],{59616:function(e,s,t){Promise.resolve().then(t.bind(t,49540))},49540:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return ed}});var a=t(57437),r=t(2265),n=t(24033),i=t(82749),l=t(27815),c=t(85754),o=t(31478),d=t(40110),x=t(56950),m=t(93930),u=t(76637),h=t(12741),f=t(1295),p=t(28203),j=t(99155),v=t(90998),g=t(13008),N=t(6141),y=t(82104),b=t(11981),w=t(9883),C=t(67972),D=t(33673),S=t(17472),E=t(49617),Z=t(45367),T=t(5925),k=t(61865),R=t(37570),U=t(92160),L=t(42706),O=t(45179),A=t(49842),I=t(23444),F=t(45509),_=t(54900);let q=U.Ry({type:U.Km(["NOTE","CALL","EMAIL","MEETING","TASK"]),title:U.Z_().min(1,"Title is required"),description:U.Z_().optional(),status:U.Km(["PENDING","COMPLETED","CANCELLED"]),priority:U.Km(["LOW","MEDIUM","HIGH","URGENT"]),scheduledAt:U.Z_().optional(),duration:U.Rx().optional(),outcome:U.Z_().optional(),followUpRequired:U.O7(),followUpDate:U.Z_().optional(),tags:U.Z_().optional()});function P(e){let{isOpen:s,onClose:t,onSuccess:n,leadId:i,activity:l,mode:o}=e,[d,x]=(0,r.useState)(!1),{register:m,handleSubmit:u,setValue:h,watch:f,reset:p,formState:{errors:j}}=(0,k.cI)({resolver:(0,R.F)(q),defaultValues:{type:"NOTE",status:"COMPLETED",priority:"MEDIUM",followUpRequired:!1}}),v=f("followUpRequired"),g=f("type");(0,r.useEffect)(()=>{l&&"edit"===o?(h("type",l.type),h("title",l.title),h("description",l.description||""),h("status",l.status),h("priority",l.priority),h("scheduledAt",l.scheduledAt?new Date(l.scheduledAt).toISOString().slice(0,16):""),h("duration",l.duration||void 0),h("outcome",l.outcome||""),h("followUpRequired",l.followUpRequired),h("followUpDate",l.followUpDate?new Date(l.followUpDate).toISOString().slice(0,16):""),h("tags",l.tags.join(", "))):p({type:"NOTE",status:"COMPLETED",priority:"MEDIUM",followUpRequired:!1})},[l,o,h,p]);let N=async e=>{try{x(!0);let s={...e,description:e.description||null,scheduledAt:e.scheduledAt||null,duration:e.duration||null,outcome:e.outcome||null,followUpDate:e.followUpRequired&&e.followUpDate?e.followUpDate:null,tags:e.tags?e.tags.split(",").map(e=>e.trim()).filter(Boolean):[]},t="edit"===o?"/api/activities/".concat(l.id):"/api/leads/".concat(i,"/activities"),a="edit"===o?"PUT":"POST",r=await fetch(t,{method:a,headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(!r.ok){let e=await r.json();throw Error(e.error||"Failed to ".concat(o," activity"))}T.toast.success("Activity ".concat("edit"===o?"updated":"created"," successfully")),n()}catch(e){T.toast.error(e instanceof Error?e.message:"Failed to ".concat(o," activity"))}finally{x(!1)}};return(0,a.jsx)(L.Vq,{open:s,onOpenChange:t,children:(0,a.jsxs)(L.cZ,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsx)(L.fK,{children:(0,a.jsx)(L.$N,{children:"edit"===o?"Edit Activity":"Add New Activity"})}),(0,a.jsxs)("form",{onSubmit:u(N),className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(A._,{htmlFor:"type",children:"Activity Type"}),(0,a.jsxs)(F.Ph,{onValueChange:e=>h("type",e),children:[(0,a.jsx)(F.i4,{children:(0,a.jsx)(F.ki,{placeholder:"Select type"})}),(0,a.jsxs)(F.Bw,{children:[(0,a.jsx)(F.Ql,{value:"NOTE",children:"Note"}),(0,a.jsx)(F.Ql,{value:"CALL",children:"Call"}),(0,a.jsx)(F.Ql,{value:"EMAIL",children:"Email"}),(0,a.jsx)(F.Ql,{value:"MEETING",children:"Meeting"}),(0,a.jsx)(F.Ql,{value:"TASK",children:"Task"})]})]}),j.type&&(0,a.jsx)("p",{className:"text-sm text-red-600 mt-1",children:j.type.message})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(A._,{htmlFor:"status",children:"Status"}),(0,a.jsxs)(F.Ph,{onValueChange:e=>h("status",e),children:[(0,a.jsx)(F.i4,{children:(0,a.jsx)(F.ki,{placeholder:"Select status"})}),(0,a.jsxs)(F.Bw,{children:[(0,a.jsx)(F.Ql,{value:"PENDING",children:"Pending"}),(0,a.jsx)(F.Ql,{value:"COMPLETED",children:"Completed"}),(0,a.jsx)(F.Ql,{value:"CANCELLED",children:"Cancelled"})]})]}),j.status&&(0,a.jsx)("p",{className:"text-sm text-red-600 mt-1",children:j.status.message})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(A._,{htmlFor:"title",children:"Title"}),(0,a.jsx)(O.I,{id:"title",...m("title"),placeholder:"Enter activity title"}),j.title&&(0,a.jsx)("p",{className:"text-sm text-red-600 mt-1",children:j.title.message})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(A._,{htmlFor:"description",children:"Description"}),(0,a.jsx)(I.g,{id:"description",...m("description"),placeholder:"Enter activity description",rows:3})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(A._,{htmlFor:"priority",children:"Priority"}),(0,a.jsxs)(F.Ph,{onValueChange:e=>h("priority",e),children:[(0,a.jsx)(F.i4,{children:(0,a.jsx)(F.ki,{placeholder:"Select priority"})}),(0,a.jsxs)(F.Bw,{children:[(0,a.jsx)(F.Ql,{value:"LOW",children:"Low"}),(0,a.jsx)(F.Ql,{value:"MEDIUM",children:"Medium"}),(0,a.jsx)(F.Ql,{value:"HIGH",children:"High"}),(0,a.jsx)(F.Ql,{value:"URGENT",children:"Urgent"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(A._,{htmlFor:"scheduledAt",children:"Scheduled Date & Time"}),(0,a.jsx)(O.I,{id:"scheduledAt",type:"datetime-local",...m("scheduledAt")})]})]}),("CALL"===g||"MEETING"===g)&&(0,a.jsxs)("div",{children:[(0,a.jsx)(A._,{htmlFor:"duration",children:"Duration (minutes)"}),(0,a.jsx)(O.I,{id:"duration",type:"number",...m("duration",{valueAsNumber:!0}),placeholder:"Enter duration in minutes"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(A._,{htmlFor:"outcome",children:"Outcome"}),(0,a.jsx)(I.g,{id:"outcome",...m("outcome"),placeholder:"What was the outcome of this activity?",rows:2})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(_.X,{id:"followUpRequired",onCheckedChange:e=>h("followUpRequired",!!e)}),(0,a.jsx)(A._,{htmlFor:"followUpRequired",children:"Follow-up required"})]}),v&&(0,a.jsxs)("div",{children:[(0,a.jsx)(A._,{htmlFor:"followUpDate",children:"Follow-up Date & Time"}),(0,a.jsx)(O.I,{id:"followUpDate",type:"datetime-local",...m("followUpDate")})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(A._,{htmlFor:"tags",children:"Tags"}),(0,a.jsx)(O.I,{id:"tags",...m("tags"),placeholder:"Enter tags separated by commas"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Separate multiple tags with commas"})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-2 pt-4",children:[(0,a.jsx)(c.z,{type:"button",variant:"outline",onClick:t,children:"Cancel"}),(0,a.jsx)(c.z,{type:"submit",disabled:d,children:d?"Saving...":"edit"===o?"Update Activity":"Create Activity"})]})]})]})})}function z(e){let{leadId:s}=e,[t,n]=(0,r.useState)([]),[i,d]=(0,r.useState)(!0),[x,k]=(0,r.useState)(!1),[R,U]=(0,r.useState)(null),[L,O]=(0,r.useState)("all"),A=async()=>{try{d(!0);let e=new URLSearchParams({limit:"50"});"all"!==L&&e.append("type",L);let t=await fetch("/api/leads/".concat(s,"/activities?").concat(e));if(!t.ok)throw Error("Failed to fetch activities");let a=await t.json();n(a.activities)}catch(e){T.toast.error("Failed to load activities"),console.error("Error fetching activities:",e)}finally{d(!1)}};(0,r.useEffect)(()=>{s&&A()},[s,L]);let I=async e=>{if(confirm("Are you sure you want to delete this activity?"))try{if(!(await fetch("/api/activities/".concat(e),{method:"DELETE"})).ok)throw Error("Failed to delete activity");T.toast.success("Activity deleted successfully"),A()}catch(e){T.toast.error("Failed to delete activity"),console.error("Error deleting activity:",e)}},F=e=>{switch(e){case"NOTE":return(0,a.jsx)(u.Z,{className:"h-4 w-4"});case"CALL":return(0,a.jsx)(h.Z,{className:"h-4 w-4"});case"EMAIL":return(0,a.jsx)(f.Z,{className:"h-4 w-4"});case"MEETING":return(0,a.jsx)(p.Z,{className:"h-4 w-4"});case"TASK":return(0,a.jsx)(j.Z,{className:"h-4 w-4"});default:return(0,a.jsx)(v.Z,{className:"h-4 w-4"})}},_=e=>{switch(e){case"NOTE":return"text-blue-600 bg-blue-100";case"CALL":return"text-green-600 bg-green-100";case"EMAIL":return"text-purple-600 bg-purple-100";case"MEETING":return"text-orange-600 bg-orange-100";case"TASK":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}},q=e=>{switch(e){case"COMPLETED":return(0,a.jsx)(g.Z,{className:"h-4 w-4 text-green-600"});case"PENDING":return(0,a.jsx)(N.Z,{className:"h-4 w-4 text-yellow-600"});case"CANCELLED":return(0,a.jsx)(y.Z,{className:"h-4 w-4 text-red-600"});default:return(0,a.jsx)(b.Z,{className:"h-4 w-4 text-gray-600"})}},z=e=>{switch(e){case"LOW":return(0,a.jsx)(o.C,{variant:"secondary",className:"text-xs",children:"Low"});case"MEDIUM":return(0,a.jsx)(o.C,{className:"bg-blue-100 text-blue-800 text-xs",children:"Medium"});case"HIGH":return(0,a.jsx)(o.C,{className:"bg-orange-100 text-orange-800 text-xs",children:"High"});case"URGENT":return(0,a.jsx)(o.C,{variant:"destructive",className:"text-xs",children:"Urgent"});default:return(0,a.jsx)(o.C,{variant:"secondary",className:"text-xs",children:e})}};return i?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Activity Timeline"}),(0,a.jsxs)("select",{value:L,onChange:e=>O(e.target.value),className:"text-sm border rounded px-2 py-1",children:[(0,a.jsx)("option",{value:"all",children:"All Activities"}),(0,a.jsx)("option",{value:"NOTE",children:"Notes"}),(0,a.jsx)("option",{value:"CALL",children:"Calls"}),(0,a.jsx)("option",{value:"EMAIL",children:"Emails"}),(0,a.jsx)("option",{value:"MEETING",children:"Meetings"}),(0,a.jsx)("option",{value:"TASK",children:"Tasks"})]})]}),(0,a.jsxs)(c.z,{onClick:()=>k(!0),size:"sm",children:[(0,a.jsx)(w.Z,{className:"h-4 w-4 mr-2"}),"Add Activity"]})]}),(0,a.jsx)("div",{className:"space-y-4",children:0===t.length?(0,a.jsx)(l.Zb,{children:(0,a.jsxs)(l.aY,{className:"py-8 text-center text-gray-500",children:[(0,a.jsx)(v.Z,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,a.jsx)("p",{children:"No activities found"}),(0,a.jsx)("p",{className:"text-sm",children:"Start by adding a note, call, or meeting"})]})}):t.map(e=>(0,a.jsx)(l.Zb,{className:"relative",children:(0,a.jsx)(l.aY,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,a.jsx)("div",{className:"p-2 rounded-full ".concat(_(e.type)),children:F(e.type)}),(0,a.jsx)("div",{className:"flex-1 min-w-0",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:e.title}),z(e.priority),q(e.status)]}),e.description&&(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:e.description}),(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-4 text-xs text-gray-500 mb-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(C.Z,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:e.createdBy.name||e.createdBy.email})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(N.Z,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:new Date(e.createdAt).toLocaleString()})]}),e.scheduledAt&&(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(p.Z,{className:"h-3 w-3"}),(0,a.jsxs)("span",{children:["Scheduled: ",new Date(e.scheduledAt).toLocaleString()]})]}),e.duration&&(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(N.Z,{className:"h-3 w-3"}),(0,a.jsxs)("span",{children:[e.duration," minutes"]})]})]}),e.outcome&&(0,a.jsx)("div",{className:"bg-gray-50 rounded p-2 mb-2",children:(0,a.jsxs)("p",{className:"text-sm text-gray-700",children:[(0,a.jsx)("strong",{children:"Outcome:"})," ",e.outcome]})}),e.followUpRequired&&e.followUpDate&&(0,a.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded p-2 mb-2",children:(0,a.jsxs)("p",{className:"text-sm text-yellow-800",children:[(0,a.jsx)("strong",{children:"Follow-up required:"})," ",new Date(e.followUpDate).toLocaleDateString()]})}),e.tags.length>0&&(0,a.jsxs)("div",{className:"flex items-center space-x-1 mb-2",children:[(0,a.jsx)(D.Z,{className:"h-3 w-3 text-gray-400"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-1",children:e.tags.map((e,s)=>(0,a.jsx)(o.C,{variant:"outline",className:"text-xs",children:e},s))})]})]}),(0,a.jsxs)(m.h_,{children:[(0,a.jsx)(m.$F,{asChild:!0,children:(0,a.jsx)(c.z,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",children:(0,a.jsx)(S.Z,{className:"h-4 w-4"})})}),(0,a.jsxs)(m.AW,{align:"end",children:[(0,a.jsxs)(m.Xi,{onClick:()=>U(e),children:[(0,a.jsx)(E.Z,{className:"h-4 w-4 mr-2"}),"Edit"]}),(0,a.jsxs)(m.Xi,{onClick:()=>I(e.id),className:"text-red-600",children:[(0,a.jsx)(Z.Z,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]})]})})]})})},e.id))}),(x||R)&&(0,a.jsx)(P,{isOpen:x||!!R,onClose:()=>{k(!1),U(null)},onSuccess:()=>{k(!1),U(null),A()},leadId:s,activity:R,mode:R?"edit":"create"})]})}var M=t(2882),Q=t(82549),V=t(92295);function Y(e){let{leadId:s}=e,[t,n]=(0,r.useState)([]),[i,o]=(0,r.useState)(!0),[d,x]=(0,r.useState)(!1),[u,h]=(0,r.useState)(null),[f,p]=(0,r.useState)({title:"",content:"",isPrivate:!1}),[j,v]=(0,r.useState)(!1),g=async()=>{try{o(!0);let e=await fetch("/api/leads/".concat(s,"/notes"));if(!e.ok)throw Error("Failed to fetch notes");let t=await e.json();n(t.notes)}catch(e){T.toast.error("Failed to load notes"),console.error("Error fetching notes:",e)}finally{o(!1)}};(0,r.useEffect)(()=>{s&&g()},[s]);let y=async e=>{if(e.preventDefault(),!f.title.trim()||!f.content.trim()){T.toast.error("Title and content are required");return}try{v(!0);let e=u?"/api/lead-notes/".concat(u.id):"/api/leads/".concat(s,"/notes"),t=await fetch(e,{method:u?"PUT":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(f)});if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to save note")}T.toast.success("Note ".concat(u?"updated":"created"," successfully")),x(!1),h(null),p({title:"",content:"",isPrivate:!1}),g()}catch(e){T.toast.error(e instanceof Error?e.message:"Failed to save note")}finally{v(!1)}},b=e=>{h(e),p({title:e.title,content:e.content,isPrivate:e.isPrivate}),x(!0)},D=async e=>{if(confirm("Are you sure you want to delete this note?"))try{if(!(await fetch("/api/lead-notes/".concat(e),{method:"DELETE"})).ok)throw Error("Failed to delete note");T.toast.success("Note deleted successfully"),g()}catch(e){T.toast.error("Failed to delete note"),console.error("Error deleting note:",e)}},k=()=>{x(!1),h(null),p({title:"",content:"",isPrivate:!1})};return i?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Lead Notes"}),(0,a.jsxs)(c.z,{onClick:()=>x(!0),size:"sm",children:[(0,a.jsx)(w.Z,{className:"h-4 w-4 mr-2"}),"Add Note"]})]}),(0,a.jsx)("div",{className:"space-y-4",children:0===t.length?(0,a.jsx)(l.Zb,{children:(0,a.jsxs)(l.aY,{className:"py-8 text-center text-gray-500",children:[(0,a.jsx)(M.Z,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,a.jsx)("p",{children:"No notes found"}),(0,a.jsx)("p",{className:"text-sm",children:"Add your first note to get started"})]})}):t.map(e=>(0,a.jsxs)(l.Zb,{children:[(0,a.jsx)(l.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)(l.ll,{className:"text-base",children:e.title}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500 mt-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(C.Z,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:e.createdBy.name||e.createdBy.email})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(N.Z,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:new Date(e.createdAt).toLocaleString()})]}),e.isPrivate&&(0,a.jsx)("span",{className:"bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs",children:"Private"})]})]}),(0,a.jsxs)(m.h_,{children:[(0,a.jsx)(m.$F,{asChild:!0,children:(0,a.jsx)(c.z,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",children:(0,a.jsx)(S.Z,{className:"h-4 w-4"})})}),(0,a.jsxs)(m.AW,{align:"end",children:[(0,a.jsxs)(m.Xi,{onClick:()=>b(e),children:[(0,a.jsx)(E.Z,{className:"h-4 w-4 mr-2"}),"Edit"]}),(0,a.jsxs)(m.Xi,{onClick:()=>D(e.id),className:"text-red-600",children:[(0,a.jsx)(Z.Z,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]})]})}),(0,a.jsxs)(l.aY,{children:[(0,a.jsx)("div",{className:"whitespace-pre-wrap text-sm text-gray-700",children:e.content}),e.updatedAt!==e.createdAt&&(0,a.jsxs)("div",{className:"text-xs text-gray-400 mt-2",children:["Last updated: ",new Date(e.updatedAt).toLocaleString()]})]})]},e.id))}),(0,a.jsx)(L.Vq,{open:d,onOpenChange:k,children:(0,a.jsxs)(L.cZ,{className:"max-w-2xl",children:[(0,a.jsx)(L.fK,{children:(0,a.jsx)(L.$N,{children:u?"Edit Note":"Add New Note"})}),(0,a.jsxs)("form",{onSubmit:y,className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(A._,{htmlFor:"title",children:"Title"}),(0,a.jsx)(O.I,{id:"title",value:f.title,onChange:e=>p({...f,title:e.target.value}),placeholder:"Enter note title",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(A._,{htmlFor:"content",children:"Content"}),(0,a.jsx)(I.g,{id:"content",value:f.content,onChange:e=>p({...f,content:e.target.value}),placeholder:"Enter note content",rows:6,required:!0})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",id:"isPrivate",checked:f.isPrivate,onChange:e=>p({...f,isPrivate:e.target.checked}),className:"rounded"}),(0,a.jsx)(A._,{htmlFor:"isPrivate",className:"text-sm",children:"Private note (only visible to you)"})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-2 pt-4",children:[(0,a.jsxs)(c.z,{type:"button",variant:"outline",onClick:k,children:[(0,a.jsx)(Q.Z,{className:"h-4 w-4 mr-2"}),"Cancel"]}),(0,a.jsxs)(c.z,{type:"submit",disabled:j,children:[(0,a.jsx)(V.Z,{className:"h-4 w-4 mr-2"}),j?"Saving...":u?"Update Note":"Save Note"]})]})]})]})})]})}var H=t(66654),G=t(92457),W=t(89275),B=t(38244),$=t(8759),K=t(85790),X=t(74522);function J(e){let{leadId:s}=e,[t,n]=(0,r.useState)(null),[i,d]=(0,r.useState)(!0),[x,m]=(0,r.useState)(!1),[u,h]=(0,r.useState)(!1),[f,p]=(0,r.useState)(""),[j,v]=(0,r.useState)(""),[y,w]=(0,r.useState)(""),[C,D]=(0,r.useState)(!1),S=async()=>{try{d(!0);let e=await fetch("/api/leads/".concat(s,"/score"));if(!e.ok)throw Error("Failed to fetch scoring data");let t=await e.json();n(t)}catch(e){T.toast.error("Failed to load scoring data"),console.error("Error fetching scoring data:",e)}finally{d(!1)}};(0,r.useEffect)(()=>{s&&S()},[s]);let Z=async()=>{if(!f&&!y){T.toast.error("Please provide a score or qualification notes");return}try{D(!0);let e=await fetch("/api/leads/".concat(s,"/score"),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({manualScore:f?parseInt(f):void 0,reason:j,qualificationNotes:y})});if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to update score")}T.toast.success("Lead score updated successfully"),m(!1),p(""),v(""),w(""),S()}catch(e){T.toast.error(e instanceof Error?e.message:"Failed to update score")}finally{D(!1)}},k=e=>{switch(e){case"URGENT":return"text-red-600 bg-red-100";case"HIGH":return"text-orange-600 bg-orange-100";case"MEDIUM":return"text-yellow-600 bg-yellow-100";case"LOW":return"text-green-600 bg-green-100";default:return"text-gray-600 bg-gray-100"}};return i?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):t?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsx)(l.Zb,{children:(0,a.jsx)(l.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-blue-100 rounded-full",children:(0,a.jsx)(H.Z,{className:"h-6 w-6 text-blue-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Current Score"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[t.currentScore,"/100"]})]})]})})}),(0,a.jsx)(l.Zb,{children:(0,a.jsx)(l.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-purple-100 rounded-full",children:(0,a.jsx)(G.Z,{className:"h-6 w-6 text-purple-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Automated Score"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[t.automatedScore,"/100"]})]})]})})}),(0,a.jsx)(l.Zb,{children:(0,a.jsx)(l.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-orange-100 rounded-full",children:(0,a.jsx)(W.Z,{className:"h-6 w-6 text-orange-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Temperature"}),(0,a.jsx)(o.C,{className:"".concat((e=>{switch(e){case"HOT":return"text-red-600 bg-red-100";case"WARM":return"text-orange-600 bg-orange-100";case"COLD":return"text-blue-600 bg-blue-100";default:return"text-gray-600 bg-gray-100"}})(t.temperature)," text-sm"),children:t.temperature})]})]})})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(c.z,{onClick:()=>m(!0),variant:"outline",children:[(0,a.jsx)(E.Z,{className:"h-4 w-4 mr-2"}),"Update Score"]}),(0,a.jsxs)(c.z,{onClick:()=>h(!0),variant:"outline",children:[(0,a.jsx)(B.Z,{className:"h-4 w-4 mr-2"}),"Score History"]})]}),(0,a.jsxs)(l.Zb,{children:[(0,a.jsx)(l.Ol,{children:(0,a.jsxs)(l.ll,{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(g.Z,{className:"h-5 w-5 mr-2"}),"Qualification Status"]}),(0,a.jsx)(o.C,{className:t.qualificationStatus.isQualified?"bg-green-100 text-green-800":"bg-red-100 text-red-800",children:t.qualificationStatus.isQualified?"QUALIFIED":"NOT QUALIFIED"})]})}),(0,a.jsx)(l.aY,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"Qualification Score"}),(0,a.jsxs)("span",{className:"text-lg font-bold text-gray-900",children:[t.qualificationStatus.qualificationScore,"%"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3",children:(0,a.jsx)("div",{className:"h-3 rounded-full ".concat(t.qualificationStatus.qualificationScore>=60?"bg-green-600":t.qualificationStatus.qualificationScore>=40?"bg-yellow-600":"bg-red-600"),style:{width:"".concat(t.qualificationStatus.qualificationScore,"%")}})}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:Object.entries(t.qualificationStatus.criteria).map(e=>{let[s,t]=e;return(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[t?(0,a.jsx)(g.Z,{className:"h-4 w-4 text-green-600"}):(0,a.jsx)(b.Z,{className:"h-4 w-4 text-red-600"}),(0,a.jsx)("span",{className:"text-sm ".concat(t?"text-green-700":"text-red-700"),children:s.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase())})]},s)})}),t.qualificationStatus.missingCriteria.length>0&&(0,a.jsxs)("div",{className:"mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-yellow-800 mb-2",children:"Missing Qualification Criteria:"}),(0,a.jsx)("ul",{className:"text-sm text-yellow-700 space-y-1",children:t.qualificationStatus.missingCriteria.map(e=>(0,a.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,a.jsx)(b.Z,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:e.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase())})]},e))})]})]})})]}),(0,a.jsxs)(l.Zb,{children:[(0,a.jsx)(l.Ol,{children:(0,a.jsx)(l.ll,{children:"Score Breakdown"})}),(0,a.jsx)(l.aY,{children:(0,a.jsx)("div",{className:"space-y-4",children:t.scoreBreakdown.map((e,s)=>(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:e.category}),(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[e.points,"/",e.maxPoints," points"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"".concat(e.points/e.maxPoints*100,"%")}})}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:e.description})]},s))})})]}),t.recommendations.length>0&&(0,a.jsxs)(l.Zb,{children:[(0,a.jsx)(l.Ol,{children:(0,a.jsxs)(l.ll,{className:"flex items-center",children:[(0,a.jsx)($.Z,{className:"h-5 w-5 mr-2"}),"Recommendations"]})}),(0,a.jsx)(l.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:t.recommendations.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-start space-x-3 p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)(o.C,{className:"".concat(k(e.priority)," text-xs"),children:e.priority}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.message}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["Action: ",e.action]})]})]},s))})})]}),(0,a.jsx)(L.Vq,{open:x,onOpenChange:m,children:(0,a.jsxs)(L.cZ,{className:"max-w-md",children:[(0,a.jsx)(L.fK,{children:(0,a.jsx)(L.$N,{children:"Update Lead Score"})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(A._,{htmlFor:"manualScore",children:"Manual Score (0-100)"}),(0,a.jsx)(O.I,{id:"manualScore",type:"number",min:"0",max:"100",value:f,onChange:e=>p(e.target.value),placeholder:"Enter score"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(A._,{htmlFor:"reason",children:"Reason for Change"}),(0,a.jsx)(O.I,{id:"reason",value:j,onChange:e=>v(e.target.value),placeholder:"Why are you changing the score?"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(A._,{htmlFor:"qualificationNotes",children:"Qualification Notes"}),(0,a.jsx)(I.g,{id:"qualificationNotes",value:y,onChange:e=>w(e.target.value),placeholder:"Add qualification notes...",rows:3})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,a.jsx)(c.z,{variant:"outline",onClick:()=>m(!1),children:"Cancel"}),(0,a.jsx)(c.z,{onClick:Z,disabled:C,children:C?"Updating...":"Update Score"})]})]})]})}),(0,a.jsx)(L.Vq,{open:u,onOpenChange:h,children:(0,a.jsxs)(L.cZ,{className:"max-w-2xl max-h-[80vh] overflow-y-auto",children:[(0,a.jsx)(L.fK,{children:(0,a.jsx)(L.$N,{children:"Score History"})}),(0,a.jsx)("div",{className:"space-y-4",children:0===t.scoreHistory.length?(0,a.jsx)("p",{className:"text-center text-gray-500 py-8",children:"No score history available"}):t.scoreHistory.map(e=>(0,a.jsxs)("div",{className:"flex items-start space-x-3 p-3 border rounded-lg",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:e.newScore>e.previousScore?(0,a.jsx)(K.Z,{className:"h-5 w-5 text-green-600"}):e.newScore<e.previousScore?(0,a.jsx)(X.Z,{className:"h-5 w-5 text-red-600"}):(0,a.jsx)(H.Z,{className:"h-5 w-5 text-gray-600"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,a.jsxs)("span",{className:"font-medium",children:[e.previousScore," → ",e.newScore]}),(0,a.jsx)(o.C,{variant:e.isManual?"default":"secondary",className:"text-xs",children:e.isManual?"Manual":"Automatic"})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:e.changeReason}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-2 text-xs text-gray-500",children:[(0,a.jsx)(N.Z,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:new Date(e.createdAt).toLocaleString()}),(0,a.jsxs)("span",{children:["by ",e.createdBy.name||e.createdBy.email]})]})]})]},e.id))})]})})]}):(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)(b.Z,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,a.jsx)("p",{children:"Failed to load scoring data"})]})}var ee=t(89673),es=t(52431),et=t(68291);function ea(e){let{leadId:s,onConversionComplete:t}=e,[n,i]=(0,r.useState)(null),[d,x]=(0,r.useState)(!0),[m,h]=(0,r.useState)(!1),[f,p]=(0,r.useState)(!1),[j,v]=(0,r.useState)({customerData:{name:"",email:"",phone:"",company:"",address:"",city:"",state:"",zipCode:"",country:"",website:"",industry:"",companySize:"",notes:""},conversionData:{conversionType:"DIRECT",conversionReason:"",conversionValue:"",conversionDate:new Date().toISOString().slice(0,16),salesRepId:"",conversionNotes:"",followUpRequired:!1,followUpDate:""},createQuotation:!1,quotationData:{title:"",description:"",validUntil:"",items:[]}}),N=async()=>{try{x(!0);let e=await fetch("/api/leads/".concat(s,"/convert"));if(!e.ok)throw Error("Failed to fetch conversion data");let t=await e.json();i(t),t.lead&&v(e=>({...e,customerData:{...e.customerData,name:"".concat(t.lead.firstName," ").concat(t.lead.lastName).trim(),email:t.lead.email||"",phone:t.lead.phone||"",company:t.lead.companyName||"",website:t.lead.website||"",industry:t.lead.industry||"",companySize:t.lead.companySize||""}}))}catch(e){T.toast.error("Failed to load conversion data"),console.error("Error fetching conversion data:",e)}finally{x(!1)}};(0,r.useEffect)(()=>{s&&N()},[s]);let y=async()=>{try{p(!0);let e={customerData:j.customerData,conversionData:{...j.conversionData,conversionValue:j.conversionData.conversionValue?parseFloat(j.conversionData.conversionValue):void 0},createQuotation:j.createQuotation,quotationData:j.createQuotation?j.quotationData:void 0},a=await fetch("/api/leads/".concat(s,"/convert"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!a.ok){let e=await a.json();throw Error(e.error||"Failed to convert lead")}await a.json(),T.toast.success("Lead converted successfully!"),h(!1),N(),null==t||t()}catch(e){T.toast.error(e instanceof Error?e.message:"Failed to convert lead")}finally{p(!1)}},w=e=>{switch(e){case"GOOD":return"text-green-600";case"FAIR":return"text-yellow-600";case"POOR":return"text-red-600";default:return"text-gray-600"}},D=e=>{switch(e){case"URGENT":return"text-red-600 bg-red-100";case"HIGH":return"text-orange-600 bg-orange-100";case"MEDIUM":return"text-yellow-600 bg-yellow-100";case"LOW":return"text-green-600 bg-green-100";default:return"text-gray-600 bg-gray-100"}};if(d)return(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})});if(!n)return(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)(b.Z,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,a.jsx)("p",{children:"Failed to load conversion data"})]});if(n.isConverted){var S,E,Z,k,R;return(0,a.jsxs)(l.Zb,{children:[(0,a.jsx)(l.Ol,{children:(0,a.jsxs)(l.ll,{className:"flex items-center text-green-600",children:[(0,a.jsx)(g.Z,{className:"h-5 w-5 mr-2"}),"Lead Successfully Converted"]})}),(0,a.jsx)(l.aY,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(A._,{className:"text-sm font-medium text-gray-500",children:"Customer"}),(0,a.jsx)("p",{className:"text-lg font-semibold",children:null===(S=n.customer)||void 0===S?void 0:S.name})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(A._,{className:"text-sm font-medium text-gray-500",children:"Conversion Date"}),(0,a.jsx)("p",{className:"text-lg font-semibold",children:(null===(E=n.conversion)||void 0===E?void 0:E.conversionDate)?new Date(n.conversion.conversionDate).toLocaleDateString():"N/A"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(A._,{className:"text-sm font-medium text-gray-500",children:"Conversion Type"}),(0,a.jsx)(o.C,{className:"mt-1",children:null===(Z=n.conversion)||void 0===Z?void 0:Z.conversionType})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(A._,{className:"text-sm font-medium text-gray-500",children:"Conversion Value"}),(0,a.jsx)("p",{className:"text-lg font-semibold",children:(null===(k=n.conversion)||void 0===k?void 0:k.conversionValue)?"$".concat(n.conversion.conversionValue.toLocaleString()):"Not specified"})]})]}),(null===(R=n.conversion)||void 0===R?void 0:R.conversionNotes)&&(0,a.jsxs)("div",{children:[(0,a.jsx)(A._,{className:"text-sm font-medium text-gray-500",children:"Notes"}),(0,a.jsx)("p",{className:"text-sm text-gray-700 mt-1",children:n.conversion.conversionNotes})]})]})})]})}return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(l.Zb,{children:[(0,a.jsx)(l.Ol,{children:(0,a.jsxs)(l.ll,{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(H.Z,{className:"h-5 w-5 mr-2"}),"Conversion Readiness"]}),(0,a.jsxs)(o.C,{className:"".concat((e=>{switch(e){case"HIGH":return"text-green-600 bg-green-100";case"MEDIUM":return"text-yellow-600 bg-yellow-100";case"LOW":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}})(n.conversionReadiness.level)," text-sm"),children:[n.conversionReadiness.level," (",n.conversionReadiness.percentage,"%)"]})]})}),(0,a.jsx)(l.aY,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3",children:(0,a.jsx)("div",{className:"h-3 rounded-full ".concat("HIGH"===n.conversionReadiness.level?"bg-green-600":"MEDIUM"===n.conversionReadiness.level?"bg-yellow-600":"bg-red-600"),style:{width:"".concat(n.conversionReadiness.percentage,"%")}})}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:n.conversionReadiness.factors.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:e.factor}),(0,a.jsx)("p",{className:"text-xs ".concat(w(e.status)),children:e.status})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("p",{className:"text-sm font-bold",children:[e.points,"/",e.maxPoints]}),(0,a.jsx)("div",{className:"w-16 bg-gray-200 rounded-full h-1 mt-1",children:(0,a.jsx)("div",{className:"bg-blue-600 h-1 rounded-full",style:{width:"".concat(e.points/e.maxPoints*100,"%")}})})]})]},s))})]})})]}),n.conversionReadiness.recommendations.length>0&&(0,a.jsxs)(l.Zb,{children:[(0,a.jsx)(l.Ol,{children:(0,a.jsx)(l.ll,{children:"Conversion Recommendations"})}),(0,a.jsx)(l.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:n.conversionReadiness.recommendations.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-start space-x-3 p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)(o.C,{className:"".concat(D(e.priority)," text-xs"),children:e.priority}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.message}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["Action: ",e.action]})]})]},s))})})]}),(0,a.jsxs)(l.Zb,{children:[(0,a.jsx)(l.Ol,{children:(0,a.jsxs)(l.ll,{className:"flex items-center",children:[(0,a.jsx)(es.Z,{className:"h-5 w-5 mr-2"}),"Convert Lead to Customer"]})}),(0,a.jsx)(l.aY,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Ready to convert this lead to a customer? This will create a new customer record and update the lead status."}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 mt-2 text-xs text-gray-500",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(C.Z,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:"Customer record will be created"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(et.Z,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:"Lead status will be updated"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(u.Z,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:"Optional quotation can be generated"})]})]})]}),(0,a.jsxs)(c.z,{onClick:()=>h(!0),className:"flex items-center space-x-2",disabled:"LOW"===n.conversionReadiness.level,children:[(0,a.jsx)(es.Z,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Convert Lead"})]})]})})]}),(0,a.jsx)(L.Vq,{open:m,onOpenChange:h,children:(0,a.jsxs)(L.cZ,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsx)(L.fK,{children:(0,a.jsx)(L.$N,{children:"Convert Lead to Customer"})}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Customer Information"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(A._,{htmlFor:"name",children:"Full Name *"}),(0,a.jsx)(O.I,{id:"name",value:j.customerData.name,onChange:e=>v(s=>({...s,customerData:{...s.customerData,name:e.target.value}})),required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(A._,{htmlFor:"email",children:"Email *"}),(0,a.jsx)(O.I,{id:"email",type:"email",value:j.customerData.email,onChange:e=>v(s=>({...s,customerData:{...s.customerData,email:e.target.value}})),required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(A._,{htmlFor:"phone",children:"Phone"}),(0,a.jsx)(O.I,{id:"phone",value:j.customerData.phone,onChange:e=>v(s=>({...s,customerData:{...s.customerData,phone:e.target.value}}))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(A._,{htmlFor:"company",children:"Company"}),(0,a.jsx)(O.I,{id:"company",value:j.customerData.company,onChange:e=>v(s=>({...s,customerData:{...s.customerData,company:e.target.value}}))})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Conversion Details"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(A._,{htmlFor:"conversionType",children:"Conversion Type"}),(0,a.jsxs)(F.Ph,{value:j.conversionData.conversionType,onValueChange:e=>v(s=>({...s,conversionData:{...s.conversionData,conversionType:e}})),children:[(0,a.jsx)(F.i4,{children:(0,a.jsx)(F.ki,{})}),(0,a.jsxs)(F.Bw,{children:[(0,a.jsx)(F.Ql,{value:"DIRECT",children:"Direct Sale"}),(0,a.jsx)(F.Ql,{value:"QUOTATION",children:"Through Quotation"}),(0,a.jsx)(F.Ql,{value:"PROPOSAL",children:"Through Proposal"}),(0,a.jsx)(F.Ql,{value:"TRIAL",children:"After Trial"}),(0,a.jsx)(F.Ql,{value:"DEMO",children:"After Demo"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(A._,{htmlFor:"conversionValue",children:"Conversion Value ($)"}),(0,a.jsx)(O.I,{id:"conversionValue",type:"number",step:"0.01",value:j.conversionData.conversionValue,onChange:e=>v(s=>({...s,conversionData:{...s.conversionData,conversionValue:e.target.value}}))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(A._,{htmlFor:"conversionDate",children:"Conversion Date"}),(0,a.jsx)(O.I,{id:"conversionDate",type:"datetime-local",value:j.conversionData.conversionDate,onChange:e=>v(s=>({...s,conversionData:{...s.conversionData,conversionDate:e.target.value}}))})]})]}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)(A._,{htmlFor:"conversionNotes",children:"Conversion Notes"}),(0,a.jsx)(I.g,{id:"conversionNotes",value:j.conversionData.conversionNotes,onChange:e=>v(s=>({...s,conversionData:{...s.conversionData,conversionNotes:e.target.value}})),rows:3})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,a.jsx)(_.X,{id:"followUpRequired",checked:j.conversionData.followUpRequired,onCheckedChange:e=>v(s=>({...s,conversionData:{...s.conversionData,followUpRequired:!!e}}))}),(0,a.jsx)(A._,{htmlFor:"followUpRequired",children:"Schedule follow-up"})]}),j.conversionData.followUpRequired&&(0,a.jsxs)("div",{children:[(0,a.jsx)(A._,{htmlFor:"followUpDate",children:"Follow-up Date"}),(0,a.jsx)(O.I,{id:"followUpDate",type:"datetime-local",value:j.conversionData.followUpDate,onChange:e=>v(s=>({...s,conversionData:{...s.conversionData,followUpDate:e.target.value}}))})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,a.jsx)(_.X,{id:"createQuotation",checked:j.createQuotation,onCheckedChange:e=>v(s=>({...s,createQuotation:!!e}))}),(0,a.jsx)(A._,{htmlFor:"createQuotation",children:"Create quotation for this customer"})]}),j.createQuotation&&(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(A._,{htmlFor:"quotationTitle",children:"Quotation Title"}),(0,a.jsx)(O.I,{id:"quotationTitle",value:j.quotationData.title,onChange:e=>v(s=>({...s,quotationData:{...s.quotationData,title:e.target.value}}))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(A._,{htmlFor:"validUntil",children:"Valid Until"}),(0,a.jsx)(O.I,{id:"validUntil",type:"date",value:j.quotationData.validUntil,onChange:e=>v(s=>({...s,quotationData:{...s.quotationData,validUntil:e.target.value}}))})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-2 pt-4 border-t",children:[(0,a.jsx)(c.z,{variant:"outline",onClick:()=>h(!1),children:"Cancel"}),(0,a.jsx)(c.z,{onClick:y,disabled:f||!j.customerData.name||!j.customerData.email,children:f?"Converting...":"Convert Lead"})]})]})]})})]})}var er=t(73067),en=t(41298),ei=t(28956),el=t(98253),ec=t(61396),eo=t.n(ec);function ed(){let e=(0,n.useParams)(),s=(0,n.useRouter)(),{data:t}=(0,i.useSession)(),[m,p]=(0,r.useState)(null),[j,v]=(0,r.useState)(!0),[N,y]=(0,r.useState)(!1),[w,D]=(0,r.useState)("overview"),S=async()=>{try{let t=await fetch("/api/leads/".concat(e.id));if(!t.ok){if(404===t.status){T.toast.error("Lead not found"),s.push("/dashboard/leads");return}throw Error("Failed to fetch lead")}let a=await t.json();p(a.lead)}catch(e){T.toast.error("Failed to load lead details"),console.error("Error fetching lead:",e)}finally{v(!1)}};(0,r.useEffect)(()=>{e.id&&S()},[e.id]);let k=async()=>{if(m&&confirm('Are you sure you want to delete "'.concat(m.firstName," ").concat(m.lastName,'"?')))try{let e=await fetch("/api/leads/".concat(m.id),{method:"DELETE"});if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to delete lead")}T.toast.success("Lead deleted successfully"),s.push("/dashboard/leads")}catch(e){T.toast.error(e instanceof Error?e.message:"Failed to delete lead")}};return j?(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):m?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(c.z,{variant:"ghost",size:"sm",asChild:!0,children:(0,a.jsxs)(eo(),{href:"/dashboard/leads",children:[(0,a.jsx)(er.Z,{className:"h-4 w-4 mr-2"}),"Back to Leads"]})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-2xl font-bold text-gray-900",children:[m.firstName," ",m.lastName]}),(0,a.jsx)("p",{className:"text-gray-500",children:m.title&&m.companyName?"".concat(m.title," at ").concat(m.companyName):m.title||m.companyName||"Lead Details"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(ee.OT,{score:m.score}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(c.z,{variant:"outline",onClick:()=>y(!0),children:[(0,a.jsx)(E.Z,{className:"h-4 w-4 mr-2"}),"Edit"]}),(0,a.jsxs)(c.z,{variant:"destructive",onClick:k,children:[(0,a.jsx)(Z.Z,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsx)(l.Zb,{children:(0,a.jsx)(l.aY,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(K.Z,{className:"h-5 w-5 text-blue-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Status"}),(e=>{switch(e){case"NEW":return(0,a.jsx)(o.C,{variant:"secondary",children:"New"});case"CONTACTED":return(0,a.jsx)(o.C,{className:"bg-blue-100 text-blue-800",children:"Contacted"});case"QUALIFIED":return(0,a.jsx)(o.C,{className:"bg-green-100 text-green-800",children:"Qualified"});case"PROPOSAL":return(0,a.jsx)(o.C,{className:"bg-yellow-100 text-yellow-800",children:"Proposal"});case"NEGOTIATION":return(0,a.jsx)(o.C,{className:"bg-orange-100 text-orange-800",children:"Negotiation"});case"CLOSED_WON":return(0,a.jsx)(o.C,{className:"bg-green-100 text-green-800",children:"Closed Won"});case"CLOSED_LOST":return(0,a.jsx)(o.C,{variant:"destructive",children:"Closed Lost"});default:return(0,a.jsx)(o.C,{variant:"secondary",children:e})}})(m.status)]})]})})}),(0,a.jsx)(l.Zb,{children:(0,a.jsx)(l.aY,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(b.Z,{className:"h-5 w-5 text-orange-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Priority"}),(e=>{switch(e){case"LOW":return(0,a.jsx)(o.C,{variant:"secondary",className:"text-xs",children:"Low"});case"MEDIUM":return(0,a.jsx)(o.C,{className:"bg-blue-100 text-blue-800 text-xs",children:"Medium"});case"HIGH":return(0,a.jsx)(o.C,{className:"bg-orange-100 text-orange-800 text-xs",children:"High"});case"URGENT":return(0,a.jsx)(o.C,{variant:"destructive",className:"text-xs",children:"Urgent"});default:return(0,a.jsx)(o.C,{variant:"secondary",className:"text-xs",children:e})}})(m.priority)]})]})})}),(0,a.jsx)(l.Zb,{children:(0,a.jsx)(l.aY,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(H.Z,{className:"h-5 w-5 text-green-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Score"}),(0,a.jsxs)("p",{className:"font-semibold",children:[m.score,"/100"]})]})]})})}),(0,a.jsx)(l.Zb,{children:(0,a.jsx)(l.aY,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(en.Z,{className:"h-5 w-5 text-purple-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Budget"}),(0,a.jsx)("p",{className:"font-semibold",children:m.budget?"$".concat(m.budget.toLocaleString()):"Not specified"})]})]})})})]}),(0,a.jsxs)(d.mQ,{value:w,onValueChange:D,children:[(0,a.jsxs)(d.dr,{children:[(0,a.jsx)(d.SP,{value:"overview",children:"Overview"}),(0,a.jsxs)(d.SP,{value:"activities",children:["Activities (",m._count.activities,")"]}),(0,a.jsxs)(d.SP,{value:"notes",children:["Notes (",m._count.leadNotes,")"]}),(0,a.jsx)(d.SP,{value:"scoring",children:"Scoring & Qualification"}),(0,a.jsx)(d.SP,{value:"conversion",children:"Conversion"}),(0,a.jsxs)(d.SP,{value:"tasks",children:["Tasks (",m._count.tasks,")"]}),(0,a.jsxs)(d.SP,{value:"documents",children:["Documents (",m._count.documents,")"]})]}),(0,a.jsx)(d.nU,{value:"overview",className:"space-y-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(l.Zb,{children:[(0,a.jsx)(l.Ol,{children:(0,a.jsxs)(l.ll,{className:"flex items-center",children:[(0,a.jsx)(C.Z,{className:"h-5 w-5 mr-2"}),"Contact Information"]})}),(0,a.jsxs)(l.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(f.Z,{className:"h-4 w-4 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Email"}),(0,a.jsx)("p",{className:"font-medium",children:m.email})]})]}),m.phone&&(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(h.Z,{className:"h-4 w-4 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Phone"}),(0,a.jsx)("p",{className:"font-medium",children:m.phone})]})]}),m.website&&(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(ei.Z,{className:"h-4 w-4 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Website"}),(0,a.jsx)("a",{href:m.website,target:"_blank",rel:"noopener noreferrer",className:"font-medium text-blue-600 hover:underline",children:m.website})]})]})]})]}),(0,a.jsxs)(l.Zb,{children:[(0,a.jsx)(l.Ol,{children:(0,a.jsxs)(l.ll,{className:"flex items-center",children:[(0,a.jsx)(el.Z,{className:"h-5 w-5 mr-2"}),"Company Information"]})}),(0,a.jsxs)(l.aY,{className:"space-y-4",children:[m.companyName&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Company"}),(0,a.jsx)("p",{className:"font-medium",children:m.companyName})]}),m.title&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Job Title"}),(0,a.jsx)("p",{className:"font-medium",children:m.title})]}),m.industry&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Industry"}),(0,a.jsx)("p",{className:"font-medium",children:m.industry})]}),m.companySize&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Company Size"}),(0,a.jsx)("p",{className:"font-medium",children:m.companySize})]})]})]})]})}),(0,a.jsx)(d.nU,{value:"activities",children:(0,a.jsx)(z,{leadId:m.id})}),(0,a.jsx)(d.nU,{value:"notes",children:(0,a.jsx)(Y,{leadId:m.id})}),(0,a.jsx)(d.nU,{value:"scoring",children:(0,a.jsx)(J,{leadId:m.id})}),(0,a.jsx)(d.nU,{value:"conversion",children:(0,a.jsx)(ea,{leadId:m.id,onConversionComplete:()=>window.location.reload()})}),(0,a.jsx)(d.nU,{value:"tasks",children:(0,a.jsxs)(l.Zb,{children:[(0,a.jsx)(l.Ol,{children:(0,a.jsx)(l.ll,{children:"Related Tasks"})}),(0,a.jsx)(l.aY,{children:(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)(g.Z,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,a.jsx)("p",{children:"Task management will be implemented next"})]})})]})}),(0,a.jsx)(d.nU,{value:"documents",children:(0,a.jsxs)(l.Zb,{children:[(0,a.jsx)(l.Ol,{children:(0,a.jsx)(l.ll,{children:"Documents"})}),(0,a.jsx)(l.aY,{children:(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)(u.Z,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,a.jsx)("p",{children:"Document management will be implemented next"})]})})]})})]}),N&&(0,a.jsx)(x.p,{isOpen:N,onClose:()=>y(!1),onSuccess:()=>{y(!1),S()},lead:m,mode:"edit"})]}):(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(b.Z,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Lead Not Found"}),(0,a.jsx)("p",{className:"text-gray-500 mb-4",children:"The lead you're looking for doesn't exist."}),(0,a.jsx)(c.z,{asChild:!0,children:(0,a.jsx)(eo(),{href:"/dashboard/leads",children:"Back to Leads"})})]})})}},89673:function(e,s,t){"use strict";t.d(s,{OT:function(){return x},q9:function(){return d}});var a=t(57437),r=t(31478),n=t(81016),i=t(44135),l=t(3928),c=t(89275);function o(e){let{score:s,size:t="md",showIcon:o=!0,showLabel:d=!0}=e,x=s>=70?"HOT":s>=40?"WARM":"COLD",m=(e=>{switch(e){case"HOT":return{color:"text-red-600 bg-red-100 border-red-200",icon:n.Z,label:"Hot Lead",description:"High priority, ready to convert"};case"WARM":return{color:"text-orange-600 bg-orange-100 border-orange-200",icon:i.Z,label:"Warm Lead",description:"Good potential, needs nurturing"};case"COLD":return{color:"text-blue-600 bg-blue-100 border-blue-200",icon:l.Z,label:"Cold Lead",description:"Low engagement, requires attention"};default:return{color:"text-gray-600 bg-gray-100 border-gray-200",icon:c.Z,label:"Unknown",description:"Temperature not determined"}}})(x),u=(e=>{switch(e){case"sm":return{badge:"text-xs px-2 py-1",icon:"h-3 w-3",text:"text-xs"};case"lg":return{badge:"text-base px-4 py-2",icon:"h-5 w-5",text:"text-base"};default:return{badge:"text-sm px-3 py-1",icon:"h-4 w-4",text:"text-sm"}}})(t),h=m.icon;return(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(r.C,{className:"".concat(m.color," border ").concat(u.badge," font-medium"),variant:"outline",children:(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[o&&(0,a.jsx)(h,{className:u.icon}),(0,a.jsx)("span",{children:x})]})}),d&&(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"".concat(u.text," font-medium text-gray-900"),children:m.label}),"lg"===t&&(0,a.jsx)("span",{className:"text-xs text-gray-500",children:m.description})]})]})}function d(e){let{score:s}=e;return(0,a.jsx)(o,{score:s,size:"sm",showLabel:!1})}function x(e){let{score:s}=e;return(0,a.jsx)(o,{score:s,size:"lg",showIcon:!0,showLabel:!0})}},31478:function(e,s,t){"use strict";t.d(s,{C:function(){return l}});var a=t(57437);t(2265);var r=t(96061),n=t(1657);let i=(0,r.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600",info:"border-transparent bg-blue-500 text-white hover:bg-blue-600"}},defaultVariants:{variant:"default"}});function l(e){let{className:s,variant:t,...r}=e;return(0,a.jsx)("div",{className:(0,n.cn)(i({variant:t}),s),...r})}},85754:function(e,s,t){"use strict";t.d(s,{z:function(){return o}});var a=t(57437),r=t(2265),n=t(67256),i=t(96061),l=t(1657);let c=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=r.forwardRef((e,s)=>{let{className:t,variant:r,size:i,asChild:o=!1,...d}=e,x=o?n.g7:"button";return(0,a.jsx)(x,{className:(0,l.cn)(c({variant:r,size:i,className:t})),ref:s,...d})});o.displayName="Button"},27815:function(e,s,t){"use strict";t.d(s,{Ol:function(){return l},SZ:function(){return o},Zb:function(){return i},aY:function(){return d},eW:function(){return x},ll:function(){return c}});var a=t(57437),r=t(2265),n=t(1657);let i=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...r})});i.displayName="Card";let l=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",t),...r})});l.displayName="CardHeader";let c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("h3",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",t),...r})});c.displayName="CardTitle";let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",t),...r})});o.displayName="CardDescription";let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("p-6 pt-0",t),...r})});d.displayName="CardContent";let x=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",t),...r})});x.displayName="CardFooter"},54900:function(e,s,t){"use strict";t.d(s,{X:function(){return c}});var a=t(57437),r=t(2265),n=t(66062),i=t(62442),l=t(1657);let c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(n.fC,{ref:s,className:(0,l.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",t),...r,children:(0,a.jsx)(n.z$,{className:(0,l.cn)("flex items-center justify-center text-current"),children:(0,a.jsx)(i.Z,{className:"h-4 w-4"})})})});c.displayName=n.fC.displayName},42706:function(e,s,t){"use strict";t.d(s,{$N:function(){return f},Be:function(){return p},Vq:function(){return c},cN:function(){return h},cZ:function(){return m},fK:function(){return u},hg:function(){return o},t9:function(){return x}});var a=t(57437),r=t(2265),n=t(28712),i=t(82549),l=t(1657);let c=n.fC,o=n.xz,d=n.h_;n.x8;let x=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(n.aV,{ref:s,className:(0,l.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...r})});x.displayName=n.aV.displayName;let m=r.forwardRef((e,s)=>{let{className:t,children:r,...c}=e;return(0,a.jsxs)(d,{children:[(0,a.jsx)(x,{}),(0,a.jsxs)(n.VY,{ref:s,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",t),...c,children:[r,(0,a.jsxs)(n.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(i.Z,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});m.displayName=n.VY.displayName;let u=e=>{let{className:s,...t}=e;return(0,a.jsx)("div",{className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",s),...t})};u.displayName="DialogHeader";let h=e=>{let{className:s,...t}=e;return(0,a.jsx)("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",s),...t})};h.displayName="DialogFooter";let f=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(n.Dx,{ref:s,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",t),...r})});f.displayName=n.Dx.displayName;let p=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(n.dk,{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",t),...r})});p.displayName=n.dk.displayName},93930:function(e,s,t){"use strict";t.d(s,{$F:function(){return x},AW:function(){return m},Ju:function(){return f},VD:function(){return p},Xi:function(){return u},bO:function(){return h},h_:function(){return d}});var a=t(57437),r=t(2265),n=t(23291),i=t(17158),l=t(62442),c=t(76369),o=t(1657);let d=n.fC,x=n.xz;n.ZA,n.Uv,n.Tr,n.Ee,r.forwardRef((e,s)=>{let{className:t,inset:r,children:l,...c}=e;return(0,a.jsxs)(n.fF,{ref:s,className:(0,o.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",r&&"pl-8",t),...c,children:[l,(0,a.jsx)(i.Z,{className:"ml-auto h-4 w-4"})]})}).displayName=n.fF.displayName,r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(n.tu,{ref:s,className:(0,o.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...r})}).displayName=n.tu.displayName;let m=r.forwardRef((e,s)=>{let{className:t,sideOffset:r=4,...i}=e;return(0,a.jsx)(n.Uv,{children:(0,a.jsx)(n.VY,{ref:s,sideOffset:r,className:(0,o.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...i})})});m.displayName=n.VY.displayName;let u=r.forwardRef((e,s)=>{let{className:t,inset:r,...i}=e;return(0,a.jsx)(n.ck,{ref:s,className:(0,o.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r&&"pl-8",t),...i})});u.displayName=n.ck.displayName;let h=r.forwardRef((e,s)=>{let{className:t,children:r,checked:i,...c}=e;return(0,a.jsxs)(n.oC,{ref:s,className:(0,o.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),checked:i,...c,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(n.wU,{children:(0,a.jsx)(l.Z,{className:"h-4 w-4"})})}),r]})});h.displayName=n.oC.displayName,r.forwardRef((e,s)=>{let{className:t,children:r,...i}=e;return(0,a.jsxs)(n.Rk,{ref:s,className:(0,o.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...i,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(n.wU,{children:(0,a.jsx)(c.Z,{className:"h-2 w-2 fill-current"})})}),r]})}).displayName=n.Rk.displayName;let f=r.forwardRef((e,s)=>{let{className:t,inset:r,...i}=e;return(0,a.jsx)(n.__,{ref:s,className:(0,o.cn)("px-2 py-1.5 text-sm font-semibold",r&&"pl-8",t),...i})});f.displayName=n.__.displayName;let p=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(n.Z0,{ref:s,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",t),...r})});p.displayName=n.Z0.displayName},45179:function(e,s,t){"use strict";t.d(s,{I:function(){return i}});var a=t(57437),r=t(2265),n=t(1657);let i=r.forwardRef((e,s)=>{let{className:t,type:r,...i}=e;return(0,a.jsx)("input",{type:r,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...i})});i.displayName="Input"},49842:function(e,s,t){"use strict";t.d(s,{_:function(){return o}});var a=t(57437),r=t(2265),n=t(36743),i=t(96061),l=t(1657);let c=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(n.f,{ref:s,className:(0,l.cn)(c(),t),...r})});o.displayName=n.f.displayName},45509:function(e,s,t){"use strict";t.d(s,{Bw:function(){return f},Ph:function(){return d},Ql:function(){return p},i4:function(){return m},ki:function(){return x}});var a=t(57437),r=t(2265),n=t(99530),i=t(83523),l=t(9224),c=t(62442),o=t(1657);let d=n.fC;n.ZA;let x=n.B4,m=r.forwardRef((e,s)=>{let{className:t,children:r,...l}=e;return(0,a.jsxs)(n.xz,{ref:s,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),...l,children:[r,(0,a.jsx)(n.JO,{asChild:!0,children:(0,a.jsx)(i.Z,{className:"h-4 w-4 opacity-50"})})]})});m.displayName=n.xz.displayName;let u=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(n.u_,{ref:s,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,a.jsx)(l.Z,{className:"h-4 w-4"})})});u.displayName=n.u_.displayName;let h=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(n.$G,{ref:s,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,a.jsx)(i.Z,{className:"h-4 w-4"})})});h.displayName=n.$G.displayName;let f=r.forwardRef((e,s)=>{let{className:t,children:r,position:i="popper",...l}=e;return(0,a.jsx)(n.h_,{children:(0,a.jsxs)(n.VY,{ref:s,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:i,...l,children:[(0,a.jsx)(u,{}),(0,a.jsx)(n.l_,{className:(0,o.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),(0,a.jsx)(h,{})]})})});f.displayName=n.VY.displayName,r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(n.__,{ref:s,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",t),...r})}).displayName=n.__.displayName;let p=r.forwardRef((e,s)=>{let{className:t,children:r,...i}=e;return(0,a.jsxs)(n.ck,{ref:s,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...i,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(n.wU,{children:(0,a.jsx)(c.Z,{className:"h-4 w-4"})})}),(0,a.jsx)(n.eT,{children:r})]})});p.displayName=n.ck.displayName,r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(n.Z0,{ref:s,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",t),...r})}).displayName=n.Z0.displayName},40110:function(e,s,t){"use strict";t.d(s,{SP:function(){return o},dr:function(){return c},mQ:function(){return l},nU:function(){return d}});var a=t(57437),r=t(2265),n=t(34522),i=t(1657);let l=n.fC,c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(n.aV,{ref:s,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),...r})});c.displayName=n.aV.displayName;let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(n.xz,{ref:s,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",t),...r})});o.displayName=n.xz.displayName;let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(n.VY,{ref:s,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...r})});d.displayName=n.VY.displayName},1657:function(e,s,t){"use strict";t.d(s,{cn:function(){return n}});var a=t(57042),r=t(74769);function n(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,r.m6)((0,a.W)(s))}}},function(e){e.O(0,[6723,9502,2749,1706,4138,1396,4997,4522,2881,2012,4290,6950,2971,4938,1744],function(){return e(e.s=59616)}),_N_E=e.O()}]);