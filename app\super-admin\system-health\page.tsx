'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { redirect } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Activity,
  Server,
  Database,
  Cpu,
  HardDrive,
  MemoryStick,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Zap,
  Users,
  TrendingUp,
  TrendingDown
} from 'lucide-react'

interface SystemHealth {
  status: string
  timestamp: string
  system: {
    platform: string
    arch: string
    nodeVersion: string
    uptime: number
    cpuCount: number
    memory: {
      total: number
      free: number
      used: number
      usagePercent: number
    }
    loadAverage: number[]
  }
  database: {
    status: string
    responseTime: number
    totalUsers: number
    totalCompanies: number
    totalActivities: number
    recentErrors: number
    size: number
  }
  metrics: {
    errorRate: number
    responseTime: number
    memoryUsage: number
  }
  recentLogs: Array<{
    id: string
    level: string
    message: string
    source: string
    category?: string
    createdAt: string
  }>
  performanceHistory: Array<{
    cpuUsage?: number
    memoryUsage?: number
    diskUsage?: number
    dbResponseTime?: number
    activeUsers?: number
    requestsPerMinute?: number
    errorRate?: number
    status: string
    createdAt: string
  }>
}

export default function SystemHealthPage() {
  const { data: session, status } = useSession()
  const [healthData, setHealthData] = useState<SystemHealth | null>(null)
  const [loading, setLoading] = useState(true)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (status === 'unauthenticated') {
    redirect('/auth/signin')
  }

  if (session?.user?.role !== 'SUPER_ADMIN') {
    redirect('/dashboard')
  }

  const fetchHealthData = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/super-admin/system-health')
      if (!response.ok) throw new Error('Failed to fetch system health')

      const data = await response.json()
      setHealthData(data)
      setLastUpdated(new Date())
    } catch (error) {
      console.error('Error fetching system health:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchHealthData()
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchHealthData, 30000)
    return () => clearInterval(interval)
  }, [])

  const getStatusBadge = (status: string) => {
    const config = {
      healthy: { variant: 'default' as const, icon: CheckCircle, color: 'text-green-600' },
      warning: { variant: 'secondary' as const, icon: AlertTriangle, color: 'text-yellow-600' },
      critical: { variant: 'destructive' as const, icon: XCircle, color: 'text-red-600' },
      unhealthy: { variant: 'destructive' as const, icon: XCircle, color: 'text-red-600' }
    }

    const statusConfig = config[status as keyof typeof config] || config.unhealthy
    const Icon = statusConfig.icon

    return (
      <Badge variant={statusConfig.variant} className="flex items-center space-x-1">
        <Icon className="h-3 w-3" />
        <span>{status.toUpperCase()}</span>
      </Badge>
    )
  }

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400)
    const hours = Math.floor((seconds % 86400) / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    
    if (days > 0) return `${days}d ${hours}h ${minutes}m`
    if (hours > 0) return `${hours}h ${minutes}m`
    return `${minutes}m`
  }

  const getLogLevelBadge = (level: string) => {
    const variants = {
      ERROR: 'destructive',
      WARN: 'secondary',
      INFO: 'default',
      DEBUG: 'outline'
    } as const

    return (
      <Badge variant={variants[level as keyof typeof variants] || 'default'}>
        {level}
      </Badge>
    )
  }

  if (!healthData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <div className="flex items-center space-x-3">
            <Activity className="h-8 w-8 text-blue-600" />
            <h1 className="text-3xl font-bold text-gray-900">System Health</h1>
            {getStatusBadge(healthData.status)}
          </div>
          <p className="text-gray-500 mt-1">Real-time system monitoring and diagnostics</p>
          {lastUpdated && (
            <div className="flex items-center space-x-1 mt-2 text-sm text-gray-500">
              <Clock className="h-4 w-4" />
              <span>Last updated: {lastUpdated.toLocaleTimeString()}</span>
            </div>
          )}
        </div>
        <Button onClick={fetchHealthData} disabled={loading}>
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* System Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">CPU Usage</p>
                <p className="text-2xl font-bold text-gray-900">
                  {healthData.metrics.memoryUsage.toFixed(1)}%
                </p>
              </div>
              <Cpu className="h-8 w-8 text-blue-600" />
            </div>
            <Progress value={healthData.metrics.memoryUsage} className="mt-3" />
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Memory Usage</p>
                <p className="text-2xl font-bold text-gray-900">
                  {healthData.system.memory.usagePercent.toFixed(1)}%
                </p>
              </div>
              <MemoryStick className="h-8 w-8 text-green-600" />
            </div>
            <Progress value={healthData.system.memory.usagePercent} className="mt-3" />
            <p className="text-xs text-gray-500 mt-2">
              {formatBytes(healthData.system.memory.used)} / {formatBytes(healthData.system.memory.total)}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Database Response</p>
                <p className="text-2xl font-bold text-gray-900">
                  {healthData.database.responseTime}ms
                </p>
              </div>
              <Database className="h-8 w-8 text-purple-600" />
            </div>
            <div className="mt-3">
              {getStatusBadge(healthData.database.status)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">System Uptime</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatUptime(healthData.system.uptime)}
                </p>
              </div>
              <Server className="h-8 w-8 text-orange-600" />
            </div>
            <p className="text-xs text-gray-500 mt-3">
              {healthData.system.platform} {healthData.system.arch}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Database Metrics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Database className="h-5 w-5" />
            <span>Database Metrics</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600">{healthData.database.totalUsers}</p>
              <p className="text-sm text-gray-500">Total Users</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">{healthData.database.totalCompanies}</p>
              <p className="text-sm text-gray-500">Total Companies</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-purple-600">{healthData.database.totalActivities}</p>
              <p className="text-sm text-gray-500">Total Activities</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-red-600">{healthData.database.recentErrors}</p>
              <p className="text-sm text-gray-500">Recent Errors (24h)</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* System Information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>System Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-500">Platform:</span>
                <span className="font-medium">{healthData.system.platform}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Architecture:</span>
                <span className="font-medium">{healthData.system.arch}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Node.js Version:</span>
                <span className="font-medium">{healthData.system.nodeVersion}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">CPU Cores:</span>
                <span className="font-medium">{healthData.system.cpuCount}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Load Average:</span>
                <span className="font-medium">
                  {healthData.system.loadAverage.map(load => load.toFixed(2)).join(', ')}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Database Size:</span>
                <span className="font-medium">{healthData.database.size} MB</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Recent System Logs</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 max-h-64 overflow-y-auto">
              {healthData.recentLogs.map((log) => (
                <div key={log.id} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                  <div className="flex-shrink-0">
                    {getLogLevelBadge(log.level)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {log.message}
                    </p>
                    <div className="flex items-center space-x-2 mt-1">
                      <span className="text-xs text-gray-500">{log.source}</span>
                      {log.category && (
                        <span className="text-xs text-gray-400">• {log.category}</span>
                      )}
                      <span className="text-xs text-gray-400">
                        • {new Date(log.createdAt).toLocaleTimeString()}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
