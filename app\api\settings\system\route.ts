import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const systemSettingSchema = z.object({
  key: z.string().min(1),
  value: z.any(),
  category: z.string().min(1),
  description: z.string().optional(),
  isPublic: z.boolean().default(false),
  isEditable: z.boolean().default(true)
})

// GET /api/settings/system - Get system settings
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')
    const publicOnly = searchParams.get('public') === 'true'

    const where: any = {}
    
    if (category) {
      where.category = category
    }
    
    if (publicOnly) {
      where.isPublic = true
    }

    const settings = await prisma.systemSettings.findMany({
      where,
      orderBy: [
        { category: 'asc' },
        { key: 'asc' }
      ]
    })

    // Group settings by category
    const groupedSettings = settings.reduce((acc, setting) => {
      if (!acc[setting.category]) {
        acc[setting.category] = []
      }
      acc[setting.category].push(setting)
      return acc
    }, {} as Record<string, typeof settings>)

    return NextResponse.json({
      settings: groupedSettings,
      categories: Object.keys(groupedSettings)
    })

  } catch (error) {
    console.error('Error fetching system settings:', error)
    return NextResponse.json(
      { error: 'Failed to fetch system settings' },
      { status: 500 }
    )
  }
}

// POST /api/settings/system - Create system setting
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin privileges (you might want to implement role-based access)
    // For now, we'll allow any authenticated user to create system settings
    
    const body = await request.json()
    const validatedData = systemSettingSchema.parse(body)

    // Check if setting already exists
    const existingSetting = await prisma.systemSettings.findUnique({
      where: {
        key: validatedData.key
      }
    })

    if (existingSetting) {
      return NextResponse.json(
        { error: 'A setting with this key already exists' },
        { status: 400 }
      )
    }

    const setting = await prisma.systemSettings.create({
      data: validatedData
    })

    return NextResponse.json({
      setting,
      message: 'System setting created successfully'
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating system setting:', error)
    return NextResponse.json(
      { error: 'Failed to create system setting' },
      { status: 500 }
    )
  }
}

// PUT /api/settings/system - Update system setting
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { key, ...updateData } = body

    if (!key) {
      return NextResponse.json(
        { error: 'Setting key is required' },
        { status: 400 }
      )
    }

    // Check if setting exists and is editable
    const existingSetting = await prisma.systemSettings.findUnique({
      where: {
        key
      }
    })

    if (!existingSetting) {
      return NextResponse.json(
        { error: 'Setting not found' },
        { status: 404 }
      )
    }

    if (!existingSetting.isEditable) {
      return NextResponse.json(
        { error: 'This setting is not editable' },
        { status: 403 }
      )
    }

    const setting = await prisma.systemSettings.update({
      where: {
        key
      },
      data: {
        ...updateData,
        updatedAt: new Date()
      }
    })

    return NextResponse.json({
      setting,
      message: 'System setting updated successfully'
    })

  } catch (error) {
    console.error('Error updating system setting:', error)
    return NextResponse.json(
      { error: 'Failed to update system setting' },
      { status: 500 }
    )
  }
}

// DELETE /api/settings/system - Delete system setting
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const key = searchParams.get('key')

    if (!key) {
      return NextResponse.json(
        { error: 'Setting key is required' },
        { status: 400 }
      )
    }

    // Check if setting exists and is editable
    const existingSetting = await prisma.systemSettings.findUnique({
      where: {
        key
      }
    })

    if (!existingSetting) {
      return NextResponse.json(
        { error: 'Setting not found' },
        { status: 404 }
      )
    }

    if (!existingSetting.isEditable) {
      return NextResponse.json(
        { error: 'This setting cannot be deleted' },
        { status: 403 }
      )
    }

    await prisma.systemSettings.delete({
      where: {
        key
      }
    })

    return NextResponse.json({
      message: 'System setting deleted successfully'
    })

  } catch (error) {
    console.error('Error deleting system setting:', error)
    return NextResponse.json(
      { error: 'Failed to delete system setting' },
      { status: 500 }
    )
  }
}
