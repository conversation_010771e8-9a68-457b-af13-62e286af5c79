(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4534],{36781:function(e,r,s){Promise.resolve().then(s.bind(s,78399))},78399:function(e,r,s){"use strict";s.r(r),s.d(r,{default:function(){return P}});var t=s(57437),n=s(2265),a=s(82749),i=s(24033),l=s(27815),c=s(85754),d=s(31478),o=s(47934),u=s(35490),m=s(76369),x=s(1657);let f=n.forwardRef((e,r)=>{let{className:s,...n}=e;return(0,t.jsx)(u.fC,{className:(0,x.cn)("grid gap-2",s),...n,ref:r})});f.displayName=u.fC.displayName;let h=n.forwardRef((e,r)=>{let{className:s,...n}=e;return(0,t.jsx)(u.ck,{ref:r,className:(0,x.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),...n,children:(0,t.jsx)(u.z$,{className:"flex items-center justify-center",children:(0,t.jsx)(m.Z,{className:"h-2.5 w-2.5 fill-current text-current"})})})});h.displayName=u.ck.displayName;var y=s(49842),p=s(73067),j=s(25750),b=s(97332),g=s(62442),v=s(6141),N=s(49036),w=s(71738),C=s(71424);function k(){let{data:e}=(0,a.useSession)(),r=(0,i.useRouter)(),s=(0,i.useSearchParams)(),[u,m]=(0,n.useState)(null),[x,k]=(0,n.useState)("monthly"),[P,S]=(0,n.useState)(!0),[Z,R]=(0,n.useState)(!1),z=s.get("plan"),F=s.get("billing")||"monthly";(0,n.useEffect)(()=>{k(F)},[F]),(0,n.useEffect)(()=>{z?_():r.push("/pricing")},[z]);let _=async()=>{try{let e=await fetch("/api/pricing-plans/".concat(z)),s=await e.json();s.success?m(s.data):(C.Am.error("Plan not found"),r.push("/pricing"))}catch(e){console.error("Error fetching plan:",e),C.Am.error("Failed to load plan details"),r.push("/pricing")}finally{S(!1)}},A=async()=>{if(u&&(null==e?void 0:e.user)){R(!0);try{let e=await fetch("/api/subscription",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({planId:u.id,billingCycle:x.toUpperCase(),useStripe:u.monthlyPrice>0})}),s=await e.json();s.success?s.data.checkoutUrl?window.location.href=s.data.checkoutUrl:(C.Am.success("Subscription created successfully!"),r.push("/subscription")):C.Am.error(s.error||"Failed to create subscription")}catch(e){console.error("Error creating subscription:",e),C.Am.error("Failed to create subscription")}finally{R(!1)}}};if(P)return(0,t.jsx)("div",{className:"container mx-auto p-6",children:(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})})});if(!u)return(0,t.jsx)("div",{className:"container mx-auto p-6",children:(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Plan not found"}),(0,t.jsx)(c.z,{onClick:()=>r.push("/pricing"),children:"Back to Pricing"})]})});let D="yearly"===x&&u.yearlyPrice?u.yearlyPrice/12:u.monthlyPrice,O="yearly"===x&&u.yearlyPrice?u.yearlyPrice:u.monthlyPrice,Y="yearly"===x&&u.yearlyDiscount>0;return(0,t.jsxs)("div",{className:"container mx-auto p-6 max-w-4xl",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4 mb-8",children:[(0,t.jsxs)(c.z,{variant:"ghost",size:"sm",onClick:()=>r.back(),children:[(0,t.jsx)(p.Z,{className:"h-4 w-4 mr-2"}),"Back"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Complete Your Subscription"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Review your plan and start your subscription"})]})]}),(0,t.jsxs)("div",{className:"grid lg:grid-cols-2 gap-8",children:[(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsxs)(l.Zb,{children:[(0,t.jsxs)(l.Ol,{children:[(0,t.jsxs)(l.ll,{className:"flex items-center justify-between",children:[u.name,"pro"===u.name.toLowerCase()&&(0,t.jsx)(d.C,{children:"Most Popular"})]}),(0,t.jsx)(l.SZ,{children:u.description})]}),(0,t.jsxs)(l.aY,{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(y._,{className:"text-base font-medium",children:"Billing Cycle"}),(0,t.jsxs)(f,{value:x,onValueChange:e=>k(e),className:"mt-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 p-3 border rounded-lg",children:[(0,t.jsx)(h,{value:"monthly",id:"monthly"}),(0,t.jsx)(y._,{htmlFor:"monthly",className:"flex-1 cursor-pointer",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{children:"Monthly"}),(0,t.jsxs)("span",{className:"font-medium",children:["$",u.monthlyPrice,"/month"]})]})})]}),u.yearlyPrice&&(0,t.jsxs)("div",{className:"flex items-center space-x-2 p-3 border rounded-lg",children:[(0,t.jsx)(h,{value:"yearly",id:"yearly"}),(0,t.jsx)(y._,{htmlFor:"yearly",className:"flex-1 cursor-pointer",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{children:"Yearly"}),Y&&(0,t.jsxs)(d.C,{variant:"secondary",className:"text-xs",children:[u.yearlyDiscount,"% off"]})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsxs)("div",{className:"font-medium",children:["$",(u.yearlyPrice/12).toFixed(0),"/month"]}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:["$",u.yearlyPrice,"/year"]})]})]})})]})]})]}),(0,t.jsx)(o.Z,{}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-3",children:"What's included:"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)(j.Z,{className:"h-4 w-4 mr-2 text-gray-400"}),"Users"]}),(0,t.jsx)("span",{className:"font-medium",children:u.maxUsers})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)(b.Z,{className:"h-4 w-4 mr-2 text-gray-400"}),"Storage"]}),(0,t.jsx)("span",{className:"font-medium",children:u.formattedStorage})]}),Object.entries(u.features).map(e=>{let[r,s]=e;return s&&(0,t.jsxs)("div",{className:"flex items-center text-sm",children:[(0,t.jsx)(g.Z,{className:"h-4 w-4 mr-2 text-green-500"}),(0,t.jsx)("span",{children:r.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase())})]},r)})]})]}),u.trialDays>0&&(0,t.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center text-blue-700",children:[(0,t.jsx)(v.Z,{className:"h-4 w-4 mr-2"}),(0,t.jsxs)("span",{className:"text-sm font-medium",children:[u.trialDays,"-day free trial included"]})]}),(0,t.jsx)("p",{className:"text-xs text-blue-600 mt-1",children:"You won't be charged until your trial ends"})]})]})]})}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(l.Zb,{children:[(0,t.jsx)(l.Ol,{children:(0,t.jsx)(l.ll,{children:"Order Summary"})}),(0,t.jsxs)(l.aY,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsxs)("span",{children:[u.name," Plan"]}),(0,t.jsxs)("span",{children:["$",D.toFixed(0),"/month"]})]}),"yearly"===x&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm text-gray-600",children:[(0,t.jsx)("span",{children:"Billing cycle"}),(0,t.jsx)("span",{children:"Annual"})]}),Y&&(0,t.jsxs)("div",{className:"flex justify-between text-sm text-green-600",children:[(0,t.jsx)("span",{children:"Annual discount"}),(0,t.jsxs)("span",{children:["-",u.yearlyDiscount,"%"]})]})]}),(0,t.jsx)(o.Z,{}),(0,t.jsxs)("div",{className:"flex justify-between font-medium text-lg",children:[(0,t.jsxs)("span",{children:["Total ","yearly"===x?"per year":"per month"]}),(0,t.jsxs)("span",{children:["$",O]})]}),u.trialDays>0&&(0,t.jsx)("div",{className:"text-sm text-gray-600",children:(0,t.jsxs)("p",{children:["Free for ",u.trialDays," days, then $",O," ","yearly"===x?"per year":"per month"]})})]})]}),(0,t.jsx)(l.Zb,{children:(0,t.jsx)(l.aY,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,t.jsx)(N.Z,{className:"h-4 w-4 mr-2 text-green-500"}),(0,t.jsx)("span",{children:"Secure checkout powered by industry-standard encryption"})]})})}),(0,t.jsx)(c.z,{className:"w-full",size:"lg",onClick:A,disabled:Z,children:Z?(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Processing..."]}):(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(w.Z,{className:"h-4 w-4 mr-2"}),u.trialDays>0?"Start Free Trial":"Subscribe Now"]})}),(0,t.jsx)("p",{className:"text-xs text-gray-500 text-center",children:"By subscribing, you agree to our Terms of Service and Privacy Policy. You can cancel anytime."})]})]})]})}function P(){return(0,t.jsx)(n.Suspense,{fallback:(0,t.jsx)("div",{className:"container mx-auto p-6",children:(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})})}),children:(0,t.jsx)(k,{})})}},31478:function(e,r,s){"use strict";s.d(r,{C:function(){return l}});var t=s(57437);s(2265);var n=s(96061),a=s(1657);let i=(0,n.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600",info:"border-transparent bg-blue-500 text-white hover:bg-blue-600"}},defaultVariants:{variant:"default"}});function l(e){let{className:r,variant:s,...n}=e;return(0,t.jsx)("div",{className:(0,a.cn)(i({variant:s}),r),...n})}},85754:function(e,r,s){"use strict";s.d(r,{z:function(){return d}});var t=s(57437),n=s(2265),a=s(67256),i=s(96061),l=s(1657);let c=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=n.forwardRef((e,r)=>{let{className:s,variant:n,size:i,asChild:d=!1,...o}=e,u=d?a.g7:"button";return(0,t.jsx)(u,{className:(0,l.cn)(c({variant:n,size:i,className:s})),ref:r,...o})});d.displayName="Button"},27815:function(e,r,s){"use strict";s.d(r,{Ol:function(){return l},SZ:function(){return d},Zb:function(){return i},aY:function(){return o},eW:function(){return u},ll:function(){return c}});var t=s(57437),n=s(2265),a=s(1657);let i=n.forwardRef((e,r)=>{let{className:s,...n}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...n})});i.displayName="Card";let l=n.forwardRef((e,r)=>{let{className:s,...n}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",s),...n})});l.displayName="CardHeader";let c=n.forwardRef((e,r)=>{let{className:s,...n}=e;return(0,t.jsx)("h3",{ref:r,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",s),...n})});c.displayName="CardTitle";let d=n.forwardRef((e,r)=>{let{className:s,...n}=e;return(0,t.jsx)("p",{ref:r,className:(0,a.cn)("text-sm text-muted-foreground",s),...n})});d.displayName="CardDescription";let o=n.forwardRef((e,r)=>{let{className:s,...n}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("p-6 pt-0",s),...n})});o.displayName="CardContent";let u=n.forwardRef((e,r)=>{let{className:s,...n}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("flex items-center p-6 pt-0",s),...n})});u.displayName="CardFooter"},49842:function(e,r,s){"use strict";s.d(r,{_:function(){return d}});var t=s(57437),n=s(2265),a=s(36743),i=s(96061),l=s(1657);let c=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=n.forwardRef((e,r)=>{let{className:s,...n}=e;return(0,t.jsx)(a.f,{ref:r,className:(0,l.cn)(c(),s),...n})});d.displayName=a.f.displayName},47934:function(e,r,s){"use strict";s.d(r,{Z:function(){return l}});var t=s(57437),n=s(2265),a=s(26823),i=s(1657);let l=n.forwardRef((e,r)=>{let{className:s,orientation:n="horizontal",decorative:l=!0,...c}=e;return(0,t.jsx)(a.f,{ref:r,decorative:l,orientation:n,className:(0,i.cn)("shrink-0 bg-border","horizontal"===n?"h-[1px] w-full":"h-full w-[1px]",s),...c})});l.displayName=a.f.displayName},1657:function(e,r,s){"use strict";s.d(r,{cn:function(){return a}});var t=s(57042),n=s(74769);function a(){for(var e=arguments.length,r=Array(e),s=0;s<e;s++)r[s]=arguments[s];return(0,n.m6)((0,t.W)(r))}}},function(e){e.O(0,[6723,9502,2749,1424,4160,2971,4938,1744],function(){return e(e.s=36781)}),_N_E=e.O()}]);