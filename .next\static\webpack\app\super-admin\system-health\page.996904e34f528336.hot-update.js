"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/super-admin/system-health/page",{

/***/ "(app-pages-browser)/./app/super-admin/system-health/page.tsx":
/*!************************************************!*\
  !*** ./app/super-admin/system-health/page.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SystemHealthPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./components/ui/progress.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Cpu,Database,MemoryStick,RefreshCw,Server,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Cpu,Database,MemoryStick,RefreshCw,Server,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Cpu,Database,MemoryStick,RefreshCw,Server,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Cpu,Database,MemoryStick,RefreshCw,Server,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Cpu,Database,MemoryStick,RefreshCw,Server,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Cpu,Database,MemoryStick,RefreshCw,Server,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Cpu,Database,MemoryStick,RefreshCw,Server,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Cpu,Database,MemoryStick,RefreshCw,Server,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/memory-stick.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Cpu,Database,MemoryStick,RefreshCw,Server,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Cpu,Database,MemoryStick,RefreshCw,Server,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction SystemHealthPage() {\n    var _session_user, _healthData_metrics, _healthData_metrics1, _healthData_system_memory, _healthData_system, _healthData_system_memory1, _healthData_system1, _healthData_system2, _healthData_database, _healthData_database1, _healthData_system3;\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [healthData, setHealthData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastUpdated, setLastUpdated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                lineNumber: 97,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n            lineNumber: 96,\n            columnNumber: 7\n        }, this);\n    }\n    if (status === \"unauthenticated\") {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.redirect)(\"/auth/signin\");\n    }\n    if ((session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.role) !== \"SUPER_ADMIN\") {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.redirect)(\"/dashboard\");\n    }\n    const fetchHealthData = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/super-admin/system-health\");\n            if (!response.ok) throw new Error(\"Failed to fetch system health\");\n            const data = await response.json();\n            setHealthData(data);\n            setLastUpdated(new Date());\n        } catch (error) {\n            console.error(\"Error fetching system health:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchHealthData();\n        // Auto-refresh every 30 seconds\n        const interval = setInterval(fetchHealthData, 30000);\n        return ()=>clearInterval(interval);\n    }, []);\n    const getStatusBadge = (status)=>{\n        const config = {\n            healthy: {\n                variant: \"default\",\n                icon: _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                color: \"text-green-600\"\n            },\n            warning: {\n                variant: \"secondary\",\n                icon: _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                color: \"text-yellow-600\"\n            },\n            critical: {\n                variant: \"destructive\",\n                icon: _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                color: \"text-red-600\"\n            },\n            unhealthy: {\n                variant: \"destructive\",\n                icon: _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                color: \"text-red-600\"\n            }\n        };\n        const statusConfig = config[status] || config.unhealthy;\n        const Icon = statusConfig.icon;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n            variant: statusConfig.variant,\n            className: \"flex items-center space-x-1\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                    className: \"h-3 w-3\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: status.toUpperCase()\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n            lineNumber: 146,\n            columnNumber: 7\n        }, this);\n    };\n    const formatBytes = (bytes)=>{\n        if (bytes === 0) return \"0 Bytes\";\n        const k = 1024;\n        const sizes = [\n            \"Bytes\",\n            \"KB\",\n            \"MB\",\n            \"GB\",\n            \"TB\"\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n    };\n    const formatUptime = (seconds)=>{\n        const days = Math.floor(seconds / 86400);\n        const hours = Math.floor(seconds % 86400 / 3600);\n        const minutes = Math.floor(seconds % 3600 / 60);\n        if (days > 0) return \"\".concat(days, \"d \").concat(hours, \"h \").concat(minutes, \"m\");\n        if (hours > 0) return \"\".concat(hours, \"h \").concat(minutes, \"m\");\n        return \"\".concat(minutes, \"m\");\n    };\n    const getLogLevelBadge = (level)=>{\n        const variants = {\n            ERROR: \"destructive\",\n            WARN: \"secondary\",\n            INFO: \"default\",\n            DEBUG: \"outline\"\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n            variant: variants[level] || \"default\",\n            children: level\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n            lineNumber: 180,\n            columnNumber: 7\n        }, this);\n    };\n    if (!healthData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                lineNumber: 189,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n            lineNumber: 188,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row sm:items-center justify-between gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"System Health\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, this),\n                                    getStatusBadge(healthData.status)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 mt-1\",\n                                children: \"Real-time system monitoring and diagnostics\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, this),\n                            lastUpdated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1 mt-2 text-sm text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Last updated: \",\n                                            lastUpdated.toLocaleTimeString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        onClick: fetchHealthData,\n                        disabled: loading,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-4 w-4 mr-2 \".concat(loading ? \"animate-spin\" : \"\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, this),\n                            \"Refresh\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-500\",\n                                                    children: \"CPU Usage\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: [\n                                                        (healthData === null || healthData === void 0 ? void 0 : (_healthData_metrics = healthData.metrics) === null || _healthData_metrics === void 0 ? void 0 : _healthData_metrics.memoryUsage) ? healthData.metrics.memoryUsage.toFixed(1) : \"0.0\",\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-8 w-8 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_7__.Progress, {\n                                    value: (healthData === null || healthData === void 0 ? void 0 : (_healthData_metrics1 = healthData.metrics) === null || _healthData_metrics1 === void 0 ? void 0 : _healthData_metrics1.memoryUsage) || 0,\n                                    className: \"mt-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-500\",\n                                                    children: \"Memory Usage\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: [\n                                                        (healthData === null || healthData === void 0 ? void 0 : (_healthData_system = healthData.system) === null || _healthData_system === void 0 ? void 0 : (_healthData_system_memory = _healthData_system.memory) === null || _healthData_system_memory === void 0 ? void 0 : _healthData_system_memory.usagePercent) ? healthData.system.memory.usagePercent.toFixed(1) : \"0.0\",\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-8 w-8 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_7__.Progress, {\n                                    value: (healthData === null || healthData === void 0 ? void 0 : (_healthData_system1 = healthData.system) === null || _healthData_system1 === void 0 ? void 0 : (_healthData_system_memory1 = _healthData_system1.memory) === null || _healthData_system_memory1 === void 0 ? void 0 : _healthData_system_memory1.usagePercent) || 0,\n                                    className: \"mt-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-2\",\n                                    children: (healthData === null || healthData === void 0 ? void 0 : (_healthData_system2 = healthData.system) === null || _healthData_system2 === void 0 ? void 0 : _healthData_system2.memory) ? \"\".concat(formatBytes(healthData.system.memory.used), \" / \").concat(formatBytes(healthData.system.memory.total)) : \"N/A\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-500\",\n                                                    children: \"Database Response\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: [\n                                                        (healthData === null || healthData === void 0 ? void 0 : (_healthData_database = healthData.database) === null || _healthData_database === void 0 ? void 0 : _healthData_database.responseTime) || 0,\n                                                        \"ms\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-8 w-8 text-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-3\",\n                                    children: getStatusBadge((healthData === null || healthData === void 0 ? void 0 : (_healthData_database1 = healthData.database) === null || _healthData_database1 === void 0 ? void 0 : _healthData_database1.status) || \"unknown\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-500\",\n                                                    children: \"System Uptime\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: (healthData === null || healthData === void 0 ? void 0 : (_healthData_system3 = healthData.system) === null || _healthData_system3 === void 0 ? void 0 : _healthData_system3.uptime) ? formatUptime(healthData.system.uptime) : \"N/A\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-8 w-8 text-orange-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-3\",\n                                    children: (healthData === null || healthData === void 0 ? void 0 : healthData.system) ? \"\".concat(healthData.system.platform, \" \").concat(healthData.system.arch) : \"N/A\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Database Metrics\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-blue-600\",\n                                            children: healthData.database.totalUsers\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"Total Users\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-green-600\",\n                                            children: healthData.database.totalCompanies\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"Total Companies\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-purple-600\",\n                                            children: healthData.database.totalActivities\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"Total Activities\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-red-600\",\n                                            children: healthData.database.recentErrors\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"Recent Errors (24h)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                lineNumber: 289,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                    children: \"System Information\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-500\",\n                                                    children: \"Platform:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: healthData.system.platform\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-500\",\n                                                    children: \"Architecture:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: healthData.system.arch\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-500\",\n                                                    children: \"Node.js Version:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: healthData.system.nodeVersion\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-500\",\n                                                    children: \"CPU Cores:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: healthData.system.cpuCount\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-500\",\n                                                    children: \"Load Average:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: healthData.system.loadAverage.map((load)=>load.toFixed(2)).join(\", \")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-500\",\n                                                    children: \"Database Size:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: [\n                                                        healthData.database.size,\n                                                        \" MB\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                    children: \"Recent System Logs\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3 max-h-64 overflow-y-auto\",\n                                    children: healthData.recentLogs.map((log)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3 p-3 bg-gray-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: getLogLevelBadge(log.level)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900 truncate\",\n                                                            children: log.message\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                            lineNumber: 368,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 mt-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: log.source\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                                    lineNumber: 372,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                log.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-400\",\n                                                                    children: [\n                                                                        \"• \",\n                                                                        log.category\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                                    lineNumber: 374,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-400\",\n                                                                    children: [\n                                                                        \"• \",\n                                                                        new Date(log.createdAt).toLocaleTimeString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                                    lineNumber: 376,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                            lineNumber: 371,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, log.id, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                lineNumber: 319,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n        lineNumber: 195,\n        columnNumber: 5\n    }, this);\n}\n_s(SystemHealthPage, \"F3I0p5jaID9HHzOMQkGJZ7rVEKw=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession\n    ];\n});\n_c = SystemHealthPage;\nvar _c;\n$RefreshReg$(_c, \"SystemHealthPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/super-admin/system-health/page.tsx\n"));

/***/ })

});