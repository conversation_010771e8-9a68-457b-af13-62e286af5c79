'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  Building2,
  LayoutDashboard,
  Users,
  UserPlus,
  FileText,
  Receipt,
  FileCheck,
  Package,
  BarChart3,
  Crown,
  Settings,
  ChevronLeft,
  ChevronRight,
  Menu,
  X,
  Shield,
  CreditCard
} from 'lucide-react'

interface SidebarProps {
  user?: {
    name?: string | null
    email?: string | null
    image?: string | null
    role?: string
    company?: {
      name?: string
    }
  }
  collapsed?: boolean
  onToggle?: () => void
  className?: string
}

const menuItems = [
  {
    title: 'Main',
    items: [
      {
        title: 'Dashboard',
        href: '/dashboard',
        icon: LayoutDashboard,
      },
      {
        title: 'Customers',
        href: '/dashboard/customers',
        icon: Users,
      },
      {
        title: 'Leads',
        href: '/dashboard/leads',
        icon: UserPlus,
      },
    ],
  },
  {
    title: 'Documents',
    items: [
      {
        title: 'Quotations',
        href: '/dashboard/quotations',
        icon: FileText,
      },
      {
        title: 'Invoices',
        href: '/dashboard/invoices',
        icon: Receipt,
      },
      {
        title: 'Contracts',
        href: '/dashboard/contracts',
        icon: FileCheck,
      },
      {
        title: 'Payments',
        href: '/dashboard/payments',
        icon: CreditCard,
      },
    ],
  },
  {
    title: 'Inventory',
    items: [
      {
        title: 'Items',
        href: '/dashboard/items',
        icon: Package,
      },
    ],
  },
  {
    title: 'Analytics',
    items: [
      {
        title: 'Reports',
        href: '/dashboard/reports',
        icon: BarChart3,
      },
    ],
  },
  {
    title: 'Account',
    items: [
      {
        title: 'Subscription',
        href: '/dashboard/subscription',
        icon: Crown,
        badge: 'PRO',
        badgeVariant: 'secondary' as const,
      },
      {
        title: 'Settings',
        href: '/dashboard/settings',
        icon: Settings,
      },
    ],
  },
]

const superAdminItems = [
  {
    title: 'Super Admin',
    items: [
      {
        title: 'Admin Panel',
        href: '/super-admin',
        icon: Shield,
        badge: 'ADMIN',
        badgeVariant: 'destructive' as const,
      },
    ],
  },
]

export function Sidebar({ user, collapsed = false, onToggle, className }: SidebarProps) {
  const pathname = usePathname()

  // Combine menu items with super admin items if user is super admin
  const allMenuItems = user?.role === 'SUPER_ADMIN'
    ? [...menuItems, ...superAdminItems]
    : menuItems

  return (
    <div
      className={cn(
        'flex h-full flex-col bg-gray-900 text-white transition-all duration-300',
        collapsed ? 'w-16' : 'w-64',
        className
      )}
    >
      {/* Header */}
      <div className="flex h-16 items-center justify-between px-4 border-b border-gray-800">
        {!collapsed && (
          <div className="flex items-center space-x-2">
            <Building2 className="h-6 w-6 text-blue-400" />
            <span className="font-semibold">Business SaaS</span>
          </div>
        )}
        {onToggle && (
          <Button
            variant="ghost"
            size="icon"
            onClick={onToggle}
            className="text-gray-400 hover:text-white hover:bg-gray-800"
          >
            {collapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
          </Button>
        )}
      </div>

      {/* User Profile */}
      {user && (
        <div className="p-4 border-b border-gray-800">
          <div className="flex items-center space-x-3">
            <Avatar className="h-8 w-8">
              <AvatarImage src={user.image || ''} alt={user.name || ''} />
              <AvatarFallback className="bg-blue-600 text-white">
                {user.name?.charAt(0)?.toUpperCase() || user.email?.charAt(0)?.toUpperCase() || 'U'}
              </AvatarFallback>
            </Avatar>
            {!collapsed && (
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-white truncate">
                  {user.name || user.email}
                </p>
                <p className="text-xs text-gray-400 truncate">
                  {user.company?.name || user.role || 'User'}
                </p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Navigation */}
      <nav className="flex-1 overflow-y-auto py-4">
        {allMenuItems.map((group) => (
          <div key={group.title} className="mb-6">
            {!collapsed && (
              <h3 className="px-4 mb-2 text-xs font-semibold text-gray-400 uppercase tracking-wider">
                {group.title}
              </h3>
            )}
            <ul className="space-y-1 px-2">
              {group.items.map((item) => {
                const isActive = pathname === item.href
                const Icon = item.icon

                return (
                  <li key={item.href}>
                    <Link
                      href={item.href}
                      className={cn(
                        'flex items-center px-2 py-2 text-sm rounded-md transition-colors',
                        isActive
                          ? 'bg-blue-600 text-white'
                          : 'text-gray-300 hover:bg-gray-800 hover:text-white'
                      )}
                    >
                      <Icon className={cn('h-5 w-5', collapsed ? 'mx-auto' : 'mr-3')} />
                      {!collapsed && (
                        <>
                          <span className="flex-1">{item.title}</span>
                          {item.badge && (
                            <Badge variant={item.badgeVariant || 'default'} className="ml-2">
                              {item.badge}
                            </Badge>
                          )}
                        </>
                      )}
                    </Link>
                  </li>
                )
              })}
            </ul>
          </div>
        ))}
      </nav>
    </div>
  )
}
