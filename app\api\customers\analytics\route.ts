import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '30' // days
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - parseInt(period))

    // Get comprehensive customer analytics
    const [
      totalCustomers,
      customersByStatus,
      customersByIndustry,
      customerGrowth,
      topCustomersByRevenue,
      customerLifetimeValue,
      customerEngagement,
      recentCustomers,
      customerRetention,
      customerSegmentation
    ] = await Promise.all([
      // Total customers
      prisma.customer.count({
        where: {
          companyId: session.user.companyId,
          createdAt: { gte: startDate }
        }
      }),

      // Customers by status
      prisma.customer.groupBy({
        by: ['status'],
        where: {
          companyId: session.user.companyId,
          createdAt: { gte: startDate }
        },
        _count: {
          id: true
        }
      }),

      // Customers by industry
      prisma.customer.groupBy({
        by: ['industry'],
        where: {
          companyId: session.user.companyId,
          industry: { not: null },
          createdAt: { gte: startDate }
        },
        _count: {
          id: true
        }
      }),

      // Customer growth trend (simplified approach)
      Promise.resolve([
        { date: new Date().toISOString().split('T')[0], customer_count: await prisma.customer.count({ where: { companyId: session.user.companyId } }) }
      ]),

      // Top customers by revenue
      prisma.customer.findMany({
        where: {
          companyId: session.user.companyId
        },
        select: {
          id: true,
          name: true,
          company: true,
          email: true,
          status: true,
          invoices: {
            where: { status: 'PAID' },
            select: { total: true }
          },
          quotations: {
            where: { status: 'ACCEPTED' },
            select: { total: true }
          }
        },
        take: 10
      }),

      // Customer lifetime value calculation
      prisma.customer.findMany({
        where: {
          companyId: session.user.companyId,
          invoices: {
            some: { status: 'PAID' }
          }
        },
        select: {
          id: true,
          name: true,
          createdAt: true,
          invoices: {
            where: { status: 'PAID' },
            select: { total: true, paidAt: true }
          }
        }
      }),

      // Customer engagement metrics
      Promise.all([
        // Customers with recent activities
        prisma.customer.count({
          where: {
            companyId: session.user.companyId,
            activities: {
              some: {
                createdAt: {
                  gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
                }
              }
            }
          }
        }),
        // Customers with recent quotations
        prisma.customer.count({
          where: {
            companyId: session.user.companyId,
            quotations: {
              some: {
                createdAt: {
                  gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
                }
              }
            }
          }
        }),
        // Customers with recent invoices
        prisma.customer.count({
          where: {
            companyId: session.user.companyId,
            invoices: {
              some: {
                createdAt: {
                  gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
                }
              }
            }
          }
        })
      ]),

      // Recent customers
      prisma.customer.findMany({
        where: {
          companyId: session.user.companyId,
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
          }
        },
        orderBy: { createdAt: 'desc' },
        take: 10,
        include: {
          createdBy: {
            select: { name: true, email: true }
          }
        }
      }),

      // Customer retention analysis
      Promise.all([
        // Customers created in the last 90 days
        prisma.customer.count({
          where: {
            companyId: session.user.companyId,
            createdAt: {
              gte: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000)
            }
          }
        }),
        // Customers with recent activity (engaged)
        prisma.customer.count({
          where: {
            companyId: session.user.companyId,
            createdAt: {
              gte: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000)
            },
            OR: [
              {
                activities: {
                  some: {
                    createdAt: {
                      gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
                    }
                  }
                }
              },
              {
                invoices: {
                  some: {
                    createdAt: {
                      gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
                    }
                  }
                }
              }
            ]
          }
        })
      ]),

      // Customer segmentation
      Promise.all([
        // High value customers (>$10k revenue)
        prisma.customer.count({
          where: {
            companyId: session.user.companyId,
            invoices: {
              some: {
                status: 'PAID',
                total: { gte: 10000 }
              }
            }
          }
        }),
        // Active customers (recent activity)
        prisma.customer.count({
          where: {
            companyId: session.user.companyId,
            status: 'ACTIVE',
            activities: {
              some: {
                createdAt: {
                  gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
                }
              }
            }
          }
        }),
        // Prospects (no invoices yet)
        prisma.customer.count({
          where: {
            companyId: session.user.companyId,
            status: 'PROSPECT',
            invoices: { none: {} }
          }
        })
      ])
    ])

    // Process top customers by revenue
    const topCustomersWithRevenue = topCustomersByRevenue.map(customer => {
      const totalRevenue = customer.invoices.reduce((sum, invoice) => sum + invoice.total, 0) +
                          customer.quotations.reduce((sum, quotation) => sum + quotation.total, 0)
      return {
        ...customer,
        totalRevenue,
        invoices: undefined,
        quotations: undefined
      }
    }).sort((a, b) => b.totalRevenue - a.totalRevenue)

    // Calculate customer lifetime value metrics
    const clvMetrics = customerLifetimeValue.map(customer => {
      const totalRevenue = customer.invoices.reduce((sum, invoice) => sum + invoice.total, 0)
      const customerAge = Math.max(1, Math.floor((Date.now() - new Date(customer.createdAt).getTime()) / (1000 * 60 * 60 * 24)))
      const avgMonthlyRevenue = totalRevenue / Math.max(1, customerAge / 30)
      
      return {
        id: customer.id,
        name: customer.name,
        totalRevenue,
        customerAge,
        avgMonthlyRevenue,
        clv: avgMonthlyRevenue * 12 // Estimated annual value
      }
    })

    const avgCLV = clvMetrics.length > 0 
      ? clvMetrics.reduce((sum, customer) => sum + customer.clv, 0) / clvMetrics.length 
      : 0

    // Calculate retention rate
    const [newCustomers, retainedCustomers] = customerRetention
    const retentionRate = newCustomers > 0 ? (retainedCustomers / newCustomers) * 100 : 0

    // Process engagement metrics
    const [customersWithActivities, customersWithQuotations, customersWithInvoices] = customerEngagement

    // Process segmentation
    const [highValueCustomers, activeCustomers, prospectCustomers] = customerSegmentation

    // Calculate total revenue
    const totalRevenue = topCustomersWithRevenue.reduce((sum, customer) => sum + customer.totalRevenue, 0)

    return NextResponse.json({
      summary: {
        totalCustomers,
        totalRevenue,
        avgCLV: Math.round(avgCLV * 100) / 100,
        retentionRate: Math.round(retentionRate * 100) / 100,
        newCustomersThisWeek: recentCustomers.length,
        engagementRate: totalCustomers > 0 ? Math.round((customersWithActivities / totalCustomers) * 100) : 0
      },
      customersByStatus: customersByStatus.map(item => ({
        status: item.status,
        count: item._count.id
      })),
      customersByIndustry: customersByIndustry.map(item => ({
        industry: item.industry,
        count: item._count.id
      })),
      customerGrowth,
      topCustomers: topCustomersWithRevenue.slice(0, 10),
      engagement: {
        withActivities: customersWithActivities,
        withQuotations: customersWithQuotations,
        withInvoices: customersWithInvoices
      },
      recentCustomers,
      segmentation: {
        highValue: highValueCustomers,
        active: activeCustomers,
        prospects: prospectCustomers
      },
      clvMetrics: clvMetrics.slice(0, 10),
      period: parseInt(period)
    })

  } catch (error) {
    console.error('Error fetching customer analytics:', error)
    return NextResponse.json(
      { error: 'Failed to fetch customer analytics' },
      { status: 500 }
    )
  }
}
