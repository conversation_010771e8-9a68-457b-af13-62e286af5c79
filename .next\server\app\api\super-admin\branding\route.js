"use strict";(()=>{var e={};e.id=9721,e.ids=[9721],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},14361:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>h,originalPathname:()=>w,patchFetch:()=>b,requestAsyncStorage:()=>m,routeModule:()=>g,serverHooks:()=>f,staticGenerationAsyncStorage:()=>y,staticGenerationBailout:()=>I});var n={};t.r(n),t.d(n,{GET:()=>d,POST:()=>p});var s=t(95419),a=t(69108),o=t(99678),i=t(78070),l=t(81355),u=t(3205),c=t(9108);async function d(e){try{let e=await (0,l.getServerSession)(u.L);if(!e?.user||"SUPER_ADMIN"!==e.user.role)return i.Z.json({success:!1,error:"Unauthorized"},{status:401});let r=(await c._.brandingConfig.findMany().catch(()=>[])).reduce((e,r)=>{let t=r.value;if("boolean"===r.type)t="true"===t;else if("number"===r.type)t=parseInt(t);else if("json"===r.type)try{t=JSON.parse(t)}catch(e){t={}}return e[r.key]=t,e},{});return i.Z.json({success:!0,branding:r})}catch(e){return console.error("Error fetching branding config:",e),i.Z.json({success:!0,branding:{}})}}async function p(e){try{let r=await (0,l.getServerSession)(u.L);if(!r?.user||"SUPER_ADMIN"!==r.user.role)return i.Z.json({success:!1,error:"Unauthorized"},{status:401});let t=await e.json(),n=[];for(let[e,r]of Object.entries(t)){let t=String(r),s="string";"boolean"==typeof r?(s="boolean",t=r.toString()):"number"==typeof r?(s="number",t=r.toString()):"object"==typeof r&&null!==r&&(s="json",t=JSON.stringify(r));try{n.push(c._.brandingConfig.upsert({where:{key:e},update:{value:t,type:s,updatedAt:new Date},create:{key:e,value:t,type:s,description:`Branding configuration for ${e}`}}))}catch(r){console.error(`Error updating branding config for ${e}:`,r)}}try{await Promise.all(n)}catch(e){console.error("Error saving branding config:",e)}try{await c._.auditLog.create({data:{action:"UPDATE_BRANDING_CONFIG",entityType:"BRANDING_CONFIG",entityId:"branding",userId:r.user.id,details:{updatedKeys:Object.keys(t),timestamp:new Date().toISOString()}}})}catch(e){console.error("Error creating audit log:",e)}return i.Z.json({success:!0,message:"Branding configuration updated successfully"})}catch(e){return console.error("Error updating branding config:",e),i.Z.json({success:!1,error:"Internal server error"},{status:500})}}let g=new s.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/super-admin/branding/route",pathname:"/api/super-admin/branding",filename:"route",bundlePath:"app/api/super-admin/branding/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\super-admin\\branding\\route.ts",nextConfigOutput:"",userland:n}),{requestAsyncStorage:m,staticGenerationAsyncStorage:y,serverHooks:f,headerHooks:h,staticGenerationBailout:I}=g,w="/api/super-admin/branding/route";function b(){return(0,o.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:y})}},3205:(e,r,t)=>{t.d(r,{L:()=>u});var n=t(86485),s=t(10375),a=t(50694),o=t(6521),i=t.n(o),l=t(9108);let u={providers:[(0,n.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let r=await l._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),t=r?.companyId;if(!t&&r){let e=await l._.company.findFirst({where:{ownerId:r.id},select:{id:!0}});(t=e?.id)&&await l._.user.update({where:{id:r.id},data:{companyId:t}})}if(!r)return console.log("User not found:",e.email),null;if(!r.password)return console.log("User has no password set:",e.email),null;if(!await i().compare(e.password,r.password))return console.log("Invalid password for user:",e.email),null;return await l._.user.update({where:{id:r.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",r.email),{id:r.id,email:r.email,name:r.name,role:r.role,companyId:t}}catch(e){return console.error("Authentication error:",e),null}}}),(0,s.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,a.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(console.log("JWT callback - user data:",{id:r.id,email:r.email,role:r.role,companyId:r.companyId}),e.role=r.role,e.companyId=r.companyId),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role,e.user.companyId=r.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,r,t)=>{t.d(r,{_:()=>s});let n=require("@prisma/client"),s=globalThis.prisma??new n.PrismaClient}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[1638,6206,6521,2455,4520],()=>t(14361));module.exports=n})();