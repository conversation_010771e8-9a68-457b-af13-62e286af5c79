(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7316],{72894:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},92457:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},28203:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},13008:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6141:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},56224:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},41298:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},35817:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},83284:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("FileSignature",[["path",{d:"M20 19.5v.5a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h8.5L18 5.5",key:"kd5d3"}],["path",{d:"M8 18h1",key:"13wk12"}],["path",{d:"M18.42 9.61a2.1 2.1 0 1 1 2.97 2.97L16.95 17 13 18l.99-3.95 4.43-4.44Z",key:"johvi5"}]])},76637:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]])},46232:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("Repeat",[["path",{d:"m17 2 4 4-4 4",key:"nntrym"}],["path",{d:"M3 11v-1a4 4 0 0 1 4-4h14",key:"84bu3i"}],["path",{d:"m7 22-4-4 4-4",key:"1wqhfi"}],["path",{d:"M21 13v1a4 4 0 0 1-4 4H3",key:"1rx37r"}]])},76020:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]])},49036:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},67972:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},25750:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},66165:function(e,s,t){Promise.resolve().then(t.bind(t,42849))},42849:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return G}});var a=t(57437),r=t(2265),l=t(82749),n=t(27815),c=t(85754),i=t(31478),d=t(25462),x=t(80382),o=t(45509),m=t(92457),h=t(64280),u=t(76637),j=t(13008),g=t(41298),y=t(83284),p=t(66654);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let N=(0,t(62898).Z)("Timer",[["line",{x1:"10",x2:"14",y1:"2",y2:"2",key:"14vaq8"}],["line",{x1:"12",x2:"15",y1:"14",y2:"11",key:"17fdiu"}],["circle",{cx:"12",cy:"14",r:"8",key:"1e1u0o"}]]);var f=t(6141),v=t(46232),b=t(28203),w=t(25750),C=t(72894),E=t(5925);function Z(){let[e,s]=(0,r.useState)(null),[t,l]=(0,r.useState)(!0),[d,x]=(0,r.useState)("30"),Z=async()=>{try{l(!0);let e=await fetch("/api/contracts/analytics?period=".concat(d));if(!e.ok)throw Error("Failed to fetch analytics");let t=await e.json();s(t)}catch(e){E.toast.error("Failed to load contract analytics"),console.error("Error fetching analytics:",e)}finally{l(!1)}};(0,r.useEffect)(()=>{Z()},[d]);let k=e=>{switch(e){case"DRAFT":default:return"bg-gray-100 text-gray-800";case"REVIEW":return"bg-yellow-100 text-yellow-800";case"SENT":return"bg-blue-100 text-blue-800";case"SIGNED":return"bg-green-100 text-green-800";case"ACTIVE":return"bg-emerald-100 text-emerald-800";case"COMPLETED":return"bg-purple-100 text-purple-800";case"CANCELLED":return"bg-red-100 text-red-800";case"EXPIRED":return"bg-orange-100 text-orange-800"}},S=e=>{switch(e){case"SERVICE":return"bg-blue-100 text-blue-800";case"PRODUCT":return"bg-green-100 text-green-800";case"SUBSCRIPTION":return"bg-purple-100 text-purple-800";case"MAINTENANCE":return"bg-orange-100 text-orange-800";case"CONSULTING":return"bg-indigo-100 text-indigo-800";default:return"bg-gray-100 text-gray-800"}},D=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e);return t?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):e?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Contract Analytics"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(o.Ph,{value:d,onValueChange:x,children:[(0,a.jsx)(o.i4,{className:"w-32",children:(0,a.jsx)(o.ki,{})}),(0,a.jsxs)(o.Bw,{children:[(0,a.jsx)(o.Ql,{value:"7",children:"Last 7 days"}),(0,a.jsx)(o.Ql,{value:"30",children:"Last 30 days"}),(0,a.jsx)(o.Ql,{value:"90",children:"Last 90 days"}),(0,a.jsx)(o.Ql,{value:"365",children:"Last year"})]})]}),(0,a.jsxs)(c.z,{variant:"outline",onClick:Z,size:"sm",children:[(0,a.jsx)(h.Z,{className:"h-4 w-4 mr-2"}),"Refresh"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsx)(n.Zb,{children:(0,a.jsx)(n.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-blue-100 rounded-full",children:(0,a.jsx)(u.Z,{className:"h-6 w-6 text-blue-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Total Contracts"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.totalContracts})]})]})})}),(0,a.jsx)(n.Zb,{children:(0,a.jsx)(n.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-green-100 rounded-full",children:(0,a.jsx)(j.Z,{className:"h-6 w-6 text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Active Contracts"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.activeContracts})]})]})})}),(0,a.jsx)(n.Zb,{children:(0,a.jsx)(n.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-purple-100 rounded-full",children:(0,a.jsx)(g.Z,{className:"h-6 w-6 text-purple-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Total Value"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:D(e.summary.totalValue)})]})]})})}),(0,a.jsx)(n.Zb,{children:(0,a.jsx)(n.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-orange-100 rounded-full",children:(0,a.jsx)(y.Z,{className:"h-6 w-6 text-orange-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Pending Signatures"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.pendingSignatures})]})]})})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsx)(n.Zb,{children:(0,a.jsx)(n.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-indigo-100 rounded-full",children:(0,a.jsx)(p.Z,{className:"h-6 w-6 text-indigo-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Signature Rate"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[e.summary.signatureRate.toFixed(1),"%"]})]})]})})}),(0,a.jsx)(n.Zb,{children:(0,a.jsx)(n.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-pink-100 rounded-full",children:(0,a.jsx)(N,{className:"h-6 w-6 text-pink-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Avg Time to Sign"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[e.summary.avgSignatureDays.toFixed(1),"d"]})]})]})})}),(0,a.jsx)(n.Zb,{children:(0,a.jsx)(n.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-teal-100 rounded-full",children:(0,a.jsx)(f.Z,{className:"h-6 w-6 text-teal-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Avg Duration"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[e.summary.avgDurationDays,"d"]})]})]})})}),(0,a.jsx)(n.Zb,{children:(0,a.jsx)(n.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-emerald-100 rounded-full",children:(0,a.jsx)(v.Z,{className:"h-6 w-6 text-emerald-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Auto-Renewal"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.autoRenewalContracts})]})]})})})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsx)(n.Ol,{children:(0,a.jsxs)(n.ll,{className:"flex items-center",children:[(0,a.jsx)(g.Z,{className:"h-5 w-5 mr-2"}),"Contract Value Overview"]})}),(0,a.jsx)(n.aY,{children:(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-green-600",children:D(e.summary.activeValue)}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Active Contract Value"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:D(e.summary.averageValue)}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Average Contract Value"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-purple-600",children:D(e.renewalAnalysis.autoRenewalValue)}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Auto-Renewal Value"})]})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)(n.Zb,{children:[(0,a.jsx)(n.Ol,{children:(0,a.jsx)(n.ll,{children:"Contracts by Status"})}),(0,a.jsx)(n.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:e.contractsByStatus.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsx)(i.C,{className:k(e.status),children:e.status})}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("span",{className:"font-semibold",children:e.count}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:D(e.value)})]})]},e.status))})})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsx)(n.Ol,{children:(0,a.jsx)(n.ll,{children:"Contracts by Type"})}),(0,a.jsx)(n.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:e.contractsByType.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsx)(i.C,{className:S(e.type),children:e.type})}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("span",{className:"font-semibold",children:e.count}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:D(e.value)})]})]},e.type))})})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsx)(n.Ol,{children:(0,a.jsxs)(n.ll,{className:"flex items-center",children:[(0,a.jsx)(b.Z,{className:"h-5 w-5 mr-2"}),"Renewal & Expiration Analysis"]})}),(0,a.jsx)(n.aY,{children:(0,a.jsxs)("div",{className:"grid grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-orange-600",children:e.renewalAnalysis.expiring30Days}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Expiring in 30 Days"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:e.renewalAnalysis.expiring90Days}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Expiring in 90 Days"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-green-600",children:e.summary.autoRenewalContracts}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Auto-Renewal Contracts"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:e.renewalAnalysis.avgDurationDays}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Avg Duration (Days)"})]})]})})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsx)(n.Ol,{children:(0,a.jsxs)(n.ll,{className:"flex items-center",children:[(0,a.jsx)(w.Z,{className:"h-5 w-5 mr-2"}),"Top Customers by Contract Value"]})}),(0,a.jsx)(n.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:e.customerContracts.slice(0,5).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center",children:(0,a.jsxs)("span",{className:"text-xs font-semibold text-blue-600",children:["#",s+1]})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:e.customer.name}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:e.customer.company})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"font-semibold text-green-600",children:D(e.totalValue)}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[e.contractCount," contracts"]})]})]},e.customer.id))})})]}),e.expiringContracts.length>0&&(0,a.jsxs)(n.Zb,{children:[(0,a.jsx)(n.Ol,{children:(0,a.jsxs)(n.ll,{className:"flex items-center",children:[(0,a.jsx)(C.Z,{className:"h-5 w-5 mr-2 text-orange-600"}),"Contracts Expiring Soon"]})}),(0,a.jsx)(n.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:e.expiringContracts.slice(0,5).map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-orange-50 rounded-lg border border-orange-200",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:e.contractNumber}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:e.customer.name}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,a.jsxs)("p",{className:"text-sm text-orange-600",children:["Expires in ",e.daysUntilExpiry," days"]}),e.autoRenewal&&(0,a.jsx)(i.C,{variant:"outline",className:"text-xs",children:"Auto-Renewal"})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"font-semibold text-green-600",children:D(e.value)}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:new Date(e.endDate).toLocaleDateString()})]})]},e.id))})})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsx)(n.Ol,{children:(0,a.jsxs)(n.ll,{className:"flex items-center",children:[(0,a.jsx)(b.Z,{className:"h-5 w-5 mr-2"}),"Recent Contracts (Last 7 Days)"]})}),(0,a.jsx)(n.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:0===e.recentContracts.length?(0,a.jsx)("p",{className:"text-gray-500 text-center py-4",children:"No recent contracts"}):e.recentContracts.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:e.contractNumber}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:e.title}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[e.customer.name," • ",new Date(e.createdAt).toLocaleDateString()]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"font-semibold text-green-600",children:D(e.value)}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(i.C,{className:k(e.status),variant:"outline",children:e.status}),(0,a.jsx)(i.C,{className:S(e.type),variant:"outline",children:e.type})]})]})]},e.id))})})]})]}):(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)(m.Z,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,a.jsx)("p",{children:"Failed to load analytics data"})]})}var k=t(45179),S=t(49842),D=t(23444),R=t(42706),T=t(47934),A=t(49036),V=t(67972),I=t(45367);function L(e){let{open:s,contract:t,onClose:l,onSuccess:n}=e,[d,x]=(0,r.useState)([]),[m,h]=(0,r.useState)(!1),[u,j]=(0,r.useState)(!1),[g,p]=(0,r.useState)({signerName:"",signerEmail:"",signerRole:"",signatureType:"ELECTRONIC",notes:""}),N=async()=>{if(t)try{h(!0);let e=await fetch("/api/contracts/".concat(t.id,"/signatures"));if(!e.ok)throw Error("Failed to fetch signatures");let s=await e.json();x(s.signatures)}catch(e){E.toast.error("Failed to load signatures"),console.error("Error fetching signatures:",e)}finally{h(!1)}};(0,r.useEffect)(()=>{s&&t&&(N(),p(e=>({...e,signerName:t.customer.name,signerEmail:"",signerRole:"Client"})))},[s,t]);let f=(e,s)=>{p(t=>({...t,[e]:s}))},v=async e=>{if(e.preventDefault(),t){if(!g.signerName||!g.signerEmail){E.toast.error("Signer name and email are required");return}if(d.find(e=>e.signerEmail.toLowerCase()===g.signerEmail.toLowerCase())){E.toast.error("This email has already signed the contract");return}j(!0);try{let e=await fetch("/api/contracts/".concat(t.id,"/signatures"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({signerName:g.signerName,signerEmail:g.signerEmail,signerRole:g.signerRole||void 0,signatureType:g.signatureType,notes:g.notes||void 0})});if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to record signature")}E.toast.success("Signature recorded successfully!"),p({signerName:"",signerEmail:"",signerRole:"",signatureType:"ELECTRONIC",notes:""}),await N(),n()}catch(e){E.toast.error(e instanceof Error?e.message:"Failed to record signature")}finally{j(!1)}}},w=async e=>{if(t&&confirm("Are you sure you want to delete this signature?"))try{let s=await fetch("/api/contracts/".concat(t.id,"/signatures?signatureId=").concat(e),{method:"DELETE"});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to delete signature")}E.toast.success("Signature deleted successfully!"),await N(),n()}catch(e){E.toast.error(e instanceof Error?e.message:"Failed to delete signature")}},C=()=>{p({signerName:"",signerEmail:"",signerRole:"",signatureType:"ELECTRONIC",notes:""}),x([]),l()};if(!t)return null;let Z=["SENT","REVIEW"].includes(t.status);return(0,a.jsx)(R.Vq,{open:s,onOpenChange:C,children:(0,a.jsxs)(R.cZ,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsx)(R.fK,{children:(0,a.jsxs)(R.$N,{className:"flex items-center",children:[(0,a.jsx)(y.Z,{className:"h-5 w-5 mr-2"}),"Signature Management"]})}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(y.Z,{className:"h-5 w-5 text-blue-600"}),(0,a.jsx)("span",{className:"font-semibold",children:t.contractNumber}),(0,a.jsx)(i.C,{className:(e=>{switch(e){case"DRAFT":default:return"bg-gray-100 text-gray-800";case"REVIEW":return"bg-yellow-100 text-yellow-800";case"SENT":return"bg-blue-100 text-blue-800";case"SIGNED":return"bg-green-100 text-green-800";case"ACTIVE":return"bg-emerald-100 text-emerald-800";case"COMPLETED":return"bg-purple-100 text-purple-800";case"CANCELLED":return"bg-red-100 text-red-800";case"EXPIRED":return"bg-orange-100 text-orange-800"}})(t.status),children:t.status})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(A.Z,{className:"h-4 w-4 text-gray-500"}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["Signature ",t.signatureRequired?"Required":"Optional"]})]})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,a.jsx)("p",{className:"font-medium",children:t.title}),(0,a.jsxs)("div",{className:"flex items-center mt-1",children:[(0,a.jsx)(V.Z,{className:"h-4 w-4 mr-1"}),(0,a.jsx)("span",{children:t.customer.name}),t.customer.company&&(0,a.jsxs)("span",{className:"ml-1",children:["(",t.customer.company,")"]})]})]})]}),Z&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Record New Signature"}),(0,a.jsxs)("form",{onSubmit:v,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(S._,{htmlFor:"signerName",children:"Signer Name *"}),(0,a.jsx)(k.I,{id:"signerName",value:g.signerName,onChange:e=>f("signerName",e.target.value),placeholder:"Full name of signer",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(S._,{htmlFor:"signerEmail",children:"Signer Email *"}),(0,a.jsx)(k.I,{id:"signerEmail",type:"email",value:g.signerEmail,onChange:e=>f("signerEmail",e.target.value),placeholder:"<EMAIL>",required:!0})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(S._,{htmlFor:"signerRole",children:"Role (optional)"}),(0,a.jsx)(k.I,{id:"signerRole",value:g.signerRole,onChange:e=>f("signerRole",e.target.value),placeholder:"e.g., CEO, Manager, Client"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(S._,{htmlFor:"signatureType",children:"Signature Type"}),(0,a.jsxs)(o.Ph,{value:g.signatureType,onValueChange:e=>f("signatureType",e),children:[(0,a.jsx)(o.i4,{children:(0,a.jsx)(o.ki,{})}),(0,a.jsxs)(o.Bw,{children:[(0,a.jsx)(o.Ql,{value:"ELECTRONIC",children:"Electronic"}),(0,a.jsx)(o.Ql,{value:"DIGITAL",children:"Digital"}),(0,a.jsx)(o.Ql,{value:"WET_SIGNATURE",children:"Wet Signature"})]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(S._,{htmlFor:"notes",children:"Notes (optional)"}),(0,a.jsx)(D.g,{id:"notes",value:g.notes,onChange:e=>f("notes",e.target.value),placeholder:"Additional notes about this signature...",rows:3})]}),(0,a.jsx)(c.z,{type:"submit",disabled:u,children:u?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Recording..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(y.Z,{className:"h-4 w-4 mr-2"}),"Record Signature"]})})]})]}),!Z&&(0,a.jsx)("div",{className:"p-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:(0,a.jsx)("p",{className:"text-yellow-800",children:'Signatures can only be added when the contract is in "Sent" or "Review" status.'})}),(0,a.jsx)(T.Z,{}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Signature History"}),m?(0,a.jsx)("div",{className:"flex items-center justify-center py-4",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"})}):0===d.length?(0,a.jsx)("p",{className:"text-gray-500 text-center py-4",children:"No signatures recorded"}):(0,a.jsx)("div",{className:"space-y-3",children:d.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-green-50 rounded-lg border border-green-200",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)(y.Z,{className:"h-4 w-4 text-green-600"}),(0,a.jsx)("span",{className:"font-semibold text-green-800",children:"Signed"}),(0,a.jsx)(i.C,{variant:"outline",children:e.signatureType})]}),(0,a.jsxs)("div",{className:"text-sm space-y-1",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Name:"})," ",e.signerName]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Email:"})," ",e.signerEmail]}),e.signerRole&&(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Role:"})," ",e.signerRole]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(b.Z,{className:"h-3 w-3"}),(0,a.jsxs)("span",{children:[(0,a.jsx)("strong",{children:"Signed:"})," ",new Date(e.signedAt).toLocaleString()]})]}),e.notes&&(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Notes:"})," ",e.notes]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 mt-2",children:["IP: ",e.ipAddress||"Unknown"," • Recorded by ",e.signedBy.name||e.signedBy.email," on"," ",new Date(e.createdAt).toLocaleDateString()]})]})]}),(0,a.jsx)(c.z,{variant:"outline",size:"sm",onClick:()=>w(e.id),className:"text-red-600 hover:text-red-700 ml-4",disabled:"ACTIVE"===t.status,children:(0,a.jsx)(I.Z,{className:"h-4 w-4"})})]},e.id))})]}),(0,a.jsx)("div",{className:"flex justify-end pt-4 border-t",children:(0,a.jsx)(c.z,{variant:"outline",onClick:C,children:"Close"})})]})]})})}var O=t(17472),F=t(99670),M=t(49617),P=t(56224),U=t(76020),Y=t(35817),z=t(9883),q=t(93930),H=t(61396),_=t.n(H);function G(){let{data:e}=(0,l.useSession)(),[s,t]=(0,r.useState)([]),[o,h]=(0,r.useState)(!0),[u,p]=(0,r.useState)(!1),[N,v]=(0,r.useState)(null),[w,k]=(0,r.useState)(!1),[S,D]=(0,r.useState)(!1),[R,T]=(0,r.useState)(null),[A,H]=(0,r.useState)({total:0,draft:0,active:0,signed:0,expired:0,totalValue:0}),G=async()=>{try{let e=await fetch("/api/contracts");if(!e.ok)throw Error("Failed to fetch contracts");let s=await e.json();t(s.contracts);let a=s.contracts.length,r=s.contracts.filter(e=>"DRAFT"===e.status).length,l=s.contracts.filter(e=>"ACTIVE"===e.status).length,n=s.contracts.filter(e=>"SIGNED"===e.status).length,c=s.contracts.filter(e=>"EXPIRED"===e.status).length,i=s.contracts.reduce((e,s)=>e+(s.value||0),0);H({total:a,draft:r,active:l,signed:n,expired:c,totalValue:i})}catch(e){E.toast.error("Failed to load contracts"),console.error("Error fetching contracts:",e)}finally{h(!1)}};(0,r.useEffect)(()=>{G()},[]);let X=async e=>{if(confirm('Are you sure you want to delete contract "'.concat(e.contractNumber,'"?')))try{let s=await fetch("/api/contracts/".concat(e.id),{method:"DELETE"});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to delete contract")}E.toast.success("Contract deleted successfully"),G()}catch(e){E.toast.error(e instanceof Error?e.message:"Failed to delete contract")}},B=e=>{v(e),p(!0)},K=async e=>{try{let s=await fetch("/api/contracts/".concat(e,"/pdf"));if(!s.ok)throw Error("Failed to generate PDF");let t=await s.blob(),a=window.URL.createObjectURL(t),r=document.createElement("a");r.href=a,r.download="contract-".concat(e,".html"),document.body.appendChild(r),r.click(),window.URL.revokeObjectURL(a),document.body.removeChild(r),E.toast.success("PDF downloaded successfully")}catch(e){E.toast.error("Failed to download PDF"),console.error("Error downloading PDF:",e)}},W=e=>{T(e),D(!0)},Q=e=>{if(!e)return(0,a.jsx)(i.C,{variant:"secondary",children:"Unknown"});switch(e){case"DRAFT":return(0,a.jsx)(i.C,{variant:"secondary",children:"Draft"});case"REVIEW":return(0,a.jsx)(i.C,{variant:"warning",children:"Review"});case"SENT":return(0,a.jsx)(i.C,{variant:"info",children:"Sent"});case"SIGNED":return(0,a.jsx)(i.C,{variant:"success",children:"Signed"});case"ACTIVE":return(0,a.jsx)(i.C,{variant:"success",children:"Active"});case"COMPLETED":return(0,a.jsx)(i.C,{variant:"success",children:"Completed"});case"CANCELLED":return(0,a.jsx)(i.C,{variant:"destructive",children:"Cancelled"});case"EXPIRED":return(0,a.jsx)(i.C,{variant:"destructive",children:"Expired"});default:return(0,a.jsx)(i.C,{variant:"secondary",children:e})}},$=e=>{let s={SERVICE:"bg-blue-100 text-blue-800",PRODUCT:"bg-green-100 text-green-800",SUBSCRIPTION:"bg-purple-100 text-purple-800",MAINTENANCE:"bg-orange-100 text-orange-800",CONSULTING:"bg-indigo-100 text-indigo-800",OTHER:"bg-gray-100 text-gray-800"};return(0,a.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(s[e]||s.OTHER),children:e})},J=e=>{switch(e){case"URGENT":return(0,a.jsx)(i.C,{variant:"destructive",children:"Urgent"});case"HIGH":return(0,a.jsx)(i.C,{variant:"warning",children:"High"});case"MEDIUM":return(0,a.jsx)(i.C,{variant:"info",children:"Medium"});case"LOW":return(0,a.jsx)(i.C,{variant:"secondary",children:"Low"});default:return(0,a.jsx)(i.C,{variant:"secondary",children:e})}};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Contracts"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"Create and manage your contracts"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(c.z,{variant:"outline",onClick:()=>k(!w),children:[(0,a.jsx)(m.Z,{className:"h-4 w-4 mr-2"}),"Analytics"]}),(0,a.jsxs)(c.z,{onClick:()=>p(!0),children:[(0,a.jsx)(z.Z,{className:"h-4 w-4 mr-2"}),"New Contract"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4",children:[(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ll,{className:"text-sm font-medium",children:"Total Contracts"}),(0,a.jsx)(y.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:A.total}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"All contracts"})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ll,{className:"text-sm font-medium",children:"Draft"}),(0,a.jsx)(f.Z,{className:"h-4 w-4 text-gray-600"})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:A.draft}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Draft contracts"})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ll,{className:"text-sm font-medium",children:"Active"}),(0,a.jsx)(j.Z,{className:"h-4 w-4 text-green-600"})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:A.active}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Active contracts"})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ll,{className:"text-sm font-medium",children:"Signed"}),(0,a.jsx)(y.Z,{className:"h-4 w-4 text-blue-600"})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:A.signed}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Signed contracts"})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ll,{className:"text-sm font-medium",children:"Expired"}),(0,a.jsx)(C.Z,{className:"h-4 w-4 text-red-600"})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:A.expired}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Expired contracts"})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ll,{className:"text-sm font-medium",children:"Total Value"}),(0,a.jsx)(g.Z,{className:"h-4 w-4 text-green-600"})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:["$",A.totalValue.toLocaleString()]}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Total contract value"})]})]})]}),w&&(0,a.jsx)(Z,{}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsx)(n.Ol,{children:(0,a.jsx)(n.ll,{children:"Contract Management"})}),(0,a.jsx)(n.aY,{children:o?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,a.jsx)(d.w,{columns:[{accessorKey:"contractNumber",header:"Contract",cell:e=>{let{row:s}=e,t=s.original;return(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(y.Z,{className:"h-4 w-4 text-purple-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:t.contractNumber}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:t.title})]})]})}},{accessorKey:"customer",header:"Customer",cell:e=>{let{row:s}=e,t=s.original.customer;return(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:t.name}),t.company&&(0,a.jsx)("div",{className:"text-sm text-gray-500",children:t.company})]})}},{accessorKey:"type",header:"Type",cell:e=>{let{row:s}=e;return $(s.getValue("type"))}},{accessorKey:"status",header:"Status",cell:e=>{let{row:s}=e;return Q(s.getValue("status"))}},{accessorKey:"priority",header:"Priority",cell:e=>{let{row:s}=e;return J(s.getValue("priority"))}},{accessorKey:"value",header:"Value",cell:e=>{let{row:s}=e,t=s.getValue("value"),r=s.original.currency;return t?(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(g.Z,{className:"h-3 w-3 text-green-600"}),(0,a.jsxs)("span",{className:"font-medium",children:[r," ",t.toLocaleString()]})]}):(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"-"})}},{accessorKey:"endDate",header:"End Date",cell:e=>{let{row:s}=e,t=s.getValue("endDate");if(!t)return(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"-"});let r=new Date(t),l=r<new Date(new Date().getTime()+2592e6);return(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(b.Z,{className:"h-3 w-3 ".concat(l?"text-red-400":"text-gray-400")}),(0,a.jsx)("span",{className:"text-sm ".concat(l?"text-red-600":""),children:r.toLocaleDateString()})]})}},{accessorKey:"assignedTo",header:"Assigned To",cell:e=>{let{row:s}=e,t=s.original.assignedTo;return t?(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(V.Z,{className:"h-3 w-3 text-gray-400"}),(0,a.jsx)("span",{className:"text-sm",children:t.name||"Unknown"})]}):(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"Unassigned"})}},{id:"actions",cell:e=>{let{row:s}=e,t=s.original;return(0,a.jsxs)(q.h_,{children:[(0,a.jsx)(q.$F,{asChild:!0,children:(0,a.jsx)(c.z,{variant:"ghost",className:"h-8 w-8 p-0",children:(0,a.jsx)(O.Z,{className:"h-4 w-4"})})}),(0,a.jsxs)(q.AW,{align:"end",children:[(0,a.jsx)(q.Ju,{children:"Actions"}),(0,a.jsx)(q.Xi,{asChild:!0,children:(0,a.jsxs)(_(),{href:"/dashboard/contracts/".concat(t.id),children:[(0,a.jsx)(F.Z,{className:"mr-2 h-4 w-4"}),"View Details"]})}),(0,a.jsxs)(q.Xi,{onClick:()=>B(t),children:[(0,a.jsx)(M.Z,{className:"mr-2 h-4 w-4"}),"Edit"]}),(0,a.jsxs)(q.Xi,{children:[(0,a.jsx)(P.Z,{className:"mr-2 h-4 w-4"}),"Duplicate"]}),(0,a.jsx)(q.VD,{}),(0,a.jsxs)(q.Xi,{children:[(0,a.jsx)(U.Z,{className:"mr-2 h-4 w-4"}),"Send for Signature"]}),(0,a.jsxs)(q.Xi,{onClick:()=>K(t.id),children:[(0,a.jsx)(Y.Z,{className:"mr-2 h-4 w-4"}),"Download PDF"]}),(0,a.jsxs)(q.Xi,{onClick:()=>W(t),children:[(0,a.jsx)(y.Z,{className:"mr-2 h-4 w-4"}),"Manage Signatures"]}),(0,a.jsx)(q.VD,{}),(0,a.jsxs)(q.Xi,{onClick:()=>X(t),className:"text-red-600",disabled:"SIGNED"===t.status||"ACTIVE"===t.status||t._count.signatures>0,children:[(0,a.jsx)(I.Z,{className:"mr-2 h-4 w-4"}),"Delete"]})]})]})}}],data:s,searchPlaceholder:"Search contracts..."})})]}),(0,a.jsx)(x.R,{isOpen:u,onClose:()=>{p(!1),v(null)},onSuccess:G,contract:N,mode:N?"edit":"create"}),(0,a.jsx)(L,{open:S,contract:R,onClose:()=>{D(!1),T(null)},onSuccess:()=>{D(!1),T(null),G()}})]})}},47934:function(e,s,t){"use strict";t.d(s,{Z:function(){return c}});var a=t(57437),r=t(2265),l=t(26823),n=t(1657);let c=r.forwardRef((e,s)=>{let{className:t,orientation:r="horizontal",decorative:c=!0,...i}=e;return(0,a.jsx)(l.f,{ref:s,decorative:c,orientation:r,className:(0,n.cn)("shrink-0 bg-border","horizontal"===r?"h-[1px] w-full":"h-full w-[1px]",t),...i})});c.displayName=l.f.displayName},23444:function(e,s,t){"use strict";t.d(s,{g:function(){return n}});var a=t(57437),r=t(2265),l=t(1657);let n=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...r})});n.displayName="Textarea"},26823:function(e,s,t){"use strict";t.d(s,{f:function(){return d}});var a=t(2265),r=t(9381),l=t(57437),n="horizontal",c=["horizontal","vertical"],i=a.forwardRef((e,s)=>{let{decorative:t,orientation:a=n,...i}=e,d=c.includes(a)?a:n;return(0,l.jsx)(r.WV.div,{"data-orientation":d,...t?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...i,ref:s})});i.displayName="Separator";var d=i}},function(e){e.O(0,[6723,9502,2749,1706,4138,1396,4997,2881,2012,5385,528,382,2971,4938,1744],function(){return e(e.s=66165)}),_N_E=e.O()}]);