"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/landing/landing-page-content.tsx":
/*!*****************************************************!*\
  !*** ./components/landing/landing-page-content.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LandingPageContent: function() { return /* binding */ LandingPageContent; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/quote.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,FileText,Globe,Mail,Menu,Phone,Quote,Shield,Star,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_6__);\n/* __next_internal_client_entry_do_not_use__ LandingPageContent auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Default fallback content\nconst defaultContent = {\n    hero: {\n        enabled: true,\n        title: \"Build Your SaaS Business\",\n        subtitle: \"The Complete Platform\",\n        description: \"Everything you need to launch, grow, and scale your SaaS business. From customer management to billing, we've got you covered.\",\n        primaryCTA: {\n            text: \"Start Free Trial\",\n            link: \"/auth/signup\"\n        },\n        secondaryCTA: {\n            text: \"Watch Demo\",\n            link: \"/demo\"\n        },\n        backgroundImage: \"\",\n        backgroundVideo: \"\"\n    },\n    features: {\n        enabled: true,\n        title: \"Everything You Need\",\n        subtitle: \"Powerful Features\",\n        items: [\n            {\n                id: \"1\",\n                title: \"Customer Management\",\n                description: \"Manage your customers, track interactions, and build lasting relationships.\",\n                icon: \"users\",\n                image: \"\"\n            },\n            {\n                id: \"2\",\n                title: \"Subscription Billing\",\n                description: \"Automated billing, invoicing, and payment processing for recurring revenue.\",\n                icon: \"credit-card\",\n                image: \"\"\n            },\n            {\n                id: \"3\",\n                title: \"Analytics & Reports\",\n                description: \"Comprehensive analytics to track your business performance and growth.\",\n                icon: \"bar-chart\",\n                image: \"\"\n            },\n            {\n                id: \"4\",\n                title: \"Multi-Tenant Architecture\",\n                description: \"Secure data isolation with company-based access control and team management.\",\n                icon: \"building\",\n                image: \"\"\n            },\n            {\n                id: \"5\",\n                title: \"Enterprise Security\",\n                description: \"Role-based access control with audit logs and data encryption.\",\n                icon: \"shield\",\n                image: \"\"\n            },\n            {\n                id: \"6\",\n                title: \"Global Ready\",\n                description: \"Multi-currency support and localization for worldwide businesses.\",\n                icon: \"globe\",\n                image: \"\"\n            }\n        ]\n    },\n    pricing: {\n        enabled: true,\n        title: \"Simple, Transparent Pricing\",\n        subtitle: \"Choose the plan that fits your needs\",\n        showPricingTable: true,\n        customMessage: \"\"\n    },\n    testimonials: {\n        enabled: true,\n        title: \"What Our Customers Say\",\n        subtitle: \"Trusted by thousands of businesses\",\n        items: [\n            {\n                id: \"1\",\n                name: \"John Smith\",\n                role: \"CEO\",\n                company: \"TechCorp\",\n                content: \"This platform has transformed how we manage our SaaS business. The automation features alone have saved us countless hours.\",\n                avatar: \"\",\n                rating: 5\n            },\n            {\n                id: \"2\",\n                name: \"Sarah Johnson\",\n                role: \"Founder\",\n                company: \"StartupXYZ\",\n                content: \"The best investment we've made for our business. The customer management features are incredibly powerful.\",\n                avatar: \"\",\n                rating: 5\n            },\n            {\n                id: \"3\",\n                name: \"Mike Chen\",\n                role: \"CTO\",\n                company: \"InnovateLab\",\n                content: \"Excellent platform with great support. The analytics help us make data-driven decisions every day.\",\n                avatar: \"\",\n                rating: 5\n            }\n        ]\n    },\n    faq: {\n        enabled: true,\n        title: \"Frequently Asked Questions\",\n        subtitle: \"Everything you need to know\",\n        items: [\n            {\n                id: \"1\",\n                question: \"How do I get started?\",\n                answer: \"Simply sign up for a free trial and follow our onboarding guide to set up your account. Our team is here to help you every step of the way.\"\n            },\n            {\n                id: \"2\",\n                question: \"Can I cancel anytime?\",\n                answer: \"Yes, you can cancel your subscription at any time. No long-term contracts or cancellation fees. Your data will remain accessible during the notice period.\"\n            },\n            {\n                id: \"3\",\n                question: \"Is my data secure?\",\n                answer: \"Absolutely. We use enterprise-grade security measures including encryption, regular backups, and compliance with industry standards like SOC 2 and GDPR.\"\n            },\n            {\n                id: \"4\",\n                question: \"Do you offer customer support?\",\n                answer: \"Yes, we provide 24/7 customer support via email, chat, and phone. Our premium plans also include dedicated account managers.\"\n            },\n            {\n                id: \"5\",\n                question: \"Can I integrate with other tools?\",\n                answer: \"Yes, we offer integrations with popular tools like Slack, Zapier, QuickBooks, and many more. We also provide a robust API for custom integrations.\"\n            }\n        ]\n    },\n    cta: {\n        enabled: true,\n        title: \"Ready to Get Started?\",\n        description: \"Join thousands of businesses already using our platform to grow their SaaS.\",\n        buttonText: \"Start Your Free Trial\",\n        buttonLink: \"/auth/signup\",\n        backgroundImage: \"\"\n    },\n    footer: {\n        enabled: true,\n        companyDescription: \"The complete SaaS platform for modern businesses.\",\n        links: [\n            {\n                id: \"1\",\n                title: \"Product\",\n                items: [\n                    {\n                        id: \"1\",\n                        text: \"Features\",\n                        link: \"/features\"\n                    },\n                    {\n                        id: \"2\",\n                        text: \"Pricing\",\n                        link: \"/pricing\"\n                    },\n                    {\n                        id: \"3\",\n                        text: \"Security\",\n                        link: \"/security\"\n                    },\n                    {\n                        id: \"4\",\n                        text: \"Integrations\",\n                        link: \"/integrations\"\n                    }\n                ]\n            },\n            {\n                id: \"2\",\n                title: \"Company\",\n                items: [\n                    {\n                        id: \"1\",\n                        text: \"About\",\n                        link: \"/about\"\n                    },\n                    {\n                        id: \"2\",\n                        text: \"Blog\",\n                        link: \"/blog\"\n                    },\n                    {\n                        id: \"3\",\n                        text: \"Careers\",\n                        link: \"/careers\"\n                    },\n                    {\n                        id: \"4\",\n                        text: \"Contact\",\n                        link: \"/contact\"\n                    }\n                ]\n            },\n            {\n                id: \"3\",\n                title: \"Support\",\n                items: [\n                    {\n                        id: \"1\",\n                        text: \"Help Center\",\n                        link: \"/help\"\n                    },\n                    {\n                        id: \"2\",\n                        text: \"Documentation\",\n                        link: \"/docs\"\n                    },\n                    {\n                        id: \"3\",\n                        text: \"API Reference\",\n                        link: \"/api\"\n                    },\n                    {\n                        id: \"4\",\n                        text: \"Status\",\n                        link: \"/status\"\n                    }\n                ]\n            }\n        ],\n        socialLinks: {\n            twitter: \"https://twitter.com/yourcompany\",\n            linkedin: \"https://linkedin.com/company/yourcompany\",\n            facebook: \"https://facebook.com/yourcompany\",\n            instagram: \"https://instagram.com/yourcompany\"\n        },\n        copyrightText: \"\\xa9 2024 Your Company. All rights reserved.\"\n    },\n    seo: {\n        title: \"SaaS Platform - Build Your Business\",\n        description: \"The complete SaaS platform for modern businesses. Customer management, billing, analytics, and more.\",\n        keywords: \"saas, platform, business, customer management, billing, analytics\",\n        ogImage: \"\"\n    }\n};\nconst getIconComponent = (iconName)=>{\n    const icons = {\n        users: _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        \"credit-card\": _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        \"bar-chart\": _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        building: _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        shield: _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        globe: _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        zap: _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        \"file-text\": _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n    };\n    return icons[iconName] || _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n};\nconst formatStorage = (bytes)=>{\n    const gb = bytes / (1024 * 1024 * 1024);\n    return gb >= 1 ? \"\".concat(gb, \"GB\") : \"\".concat(Math.round(gb * 1024), \"MB\");\n};\nconst getFeatureList = (plan)=>{\n    const features = [];\n    // Add usage limits\n    features.push(\"Up to \".concat(plan.maxUsers, \" users\"));\n    features.push(\"\".concat(plan.maxCompanies, \" \").concat(plan.maxCompanies === 1 ? \"company\" : \"companies\"));\n    features.push(\"\".concat(plan.maxCustomers, \" customers\"));\n    features.push(\"\".concat(plan.maxQuotations, \" quotations/month\"));\n    features.push(\"\".concat(plan.maxInvoices, \" invoices/month\"));\n    features.push(\"\".concat(formatStorage(plan.maxStorage), \" storage\"));\n    // Add feature flags\n    if (plan.features.basicReporting) features.push(\"Basic reporting\");\n    if (plan.features.emailSupport) features.push(\"Email support\");\n    if (plan.features.mobileApp) features.push(\"Mobile app access\");\n    if (plan.features.advancedAnalytics) features.push(\"Advanced analytics\");\n    if (plan.features.customBranding) features.push(\"Custom branding\");\n    if (plan.features.apiAccess) features.push(\"API access\");\n    if (plan.features.prioritySupport) features.push(\"Priority support\");\n    if (plan.features.customIntegrations) features.push(\"Custom integrations\");\n    if (plan.features.advancedSecurity) features.push(\"Advanced security\");\n    if (plan.features.dedicatedManager) features.push(\"Dedicated account manager\");\n    return features;\n};\nfunction LandingPageContent() {\n    var _content_hero, _content_features, _content_pricing, _content_testimonials, _content_faq, _content_cta, _content_footer;\n    _s();\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultContent);\n    const [plans, setPlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [openFAQ, setOpenFAQ] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isYearly, setIsYearly] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchData = async ()=>{\n            try {\n                // Fetch CMS content\n                const cmsResponse = await fetch(\"/api/super-admin/cms\");\n                const cmsData = await cmsResponse.json();\n                if (cmsData.success && cmsData.content) {\n                    // Merge with default content to ensure all sections exist\n                    setContent({\n                        ...defaultContent,\n                        ...cmsData.content\n                    });\n                }\n                // Fetch pricing plans\n                const plansResponse = await fetch(\"/api/pricing-plans?publicOnly=true\");\n                const plansData = await plansResponse.json();\n                if (plansData.success) {\n                    // Sort plans by sortOrder and filter active public plans\n                    const activePlans = plansData.data.filter((plan)=>plan.isActive && plan.isPublic).sort((a, b)=>a.sortOrder - b.sortOrder);\n                    setPlans(activePlans);\n                }\n            } catch (error) {\n                console.error(\"Error fetching data:\", error);\n            // Use default content on error\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchData();\n    }, []);\n    const getPrice = (plan)=>{\n        if (isYearly && plan.yearlyPrice) {\n            return plan.yearlyPrice / 12 // Show monthly equivalent\n            ;\n        }\n        return plan.monthlyPrice;\n    };\n    const getYearlyDiscount = (plan)=>{\n        if (!plan.yearlyPrice || !plan.monthlyPrice) return 0;\n        const yearlyMonthly = plan.yearlyPrice / 12;\n        const discount = (plan.monthlyPrice - yearlyMonthly) / plan.monthlyPrice * 100;\n        return Math.round(discount);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 469,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n            lineNumber: 468,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"border-b bg-white/95 backdrop-blur-sm sticky top-0 z-50 shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-8 w-8 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: \"SaaS Platform\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"hidden md:flex items-center space-x-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#features\",\n                                            className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                            children: \"Features\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#pricing\",\n                                            className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                            children: \"Pricing\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#testimonials\",\n                                            className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                            children: \"Testimonials\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#faq\",\n                                            className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                            children: \"FAQ\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 497,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/contact\",\n                                            className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                            children: \"Contact\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 487,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/auth/signin\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"ghost\",\n                                                className: \"text-gray-600 hover:text-gray-900\",\n                                                children: \"Sign In\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/auth/signup\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                className: \"bg-blue-600 hover:bg-blue-700\",\n                                                children: \"Get Started Free\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 506,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"md:hidden p-2\",\n                                    onClick: ()=>setMobileMenuOpen(!mobileMenuOpen),\n                                    children: mobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-6 w-6 text-gray-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-6 w-6 text-gray-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 527,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 479,\n                            columnNumber: 11\n                        }, this),\n                        mobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden mt-4 pb-4 border-t\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex flex-col space-y-4 pt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#features\",\n                                        className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"Features\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#pricing\",\n                                        className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"Pricing\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 543,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#testimonials\",\n                                        className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"Testimonials\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 550,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#faq\",\n                                        className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"FAQ\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 557,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                        href: \"/contact\",\n                                        className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"Contact\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 564,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col space-y-2 pt-4 border-t\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                href: \"/auth/signin\",\n                                                onClick: ()=>setMobileMenuOpen(false),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"ghost\",\n                                                    className: \"w-full justify-start\",\n                                                    children: \"Sign In\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 573,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 572,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                href: \"/auth/signup\",\n                                                onClick: ()=>setMobileMenuOpen(false),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    className: \"w-full bg-blue-600 hover:bg-blue-700\",\n                                                    children: \"Get Started Free\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 578,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 577,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 571,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                lineNumber: 535,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 534,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 478,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 477,\n                columnNumber: 7\n            }, this),\n            ((_content_hero = content.hero) === null || _content_hero === void 0 ? void 0 : _content_hero.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4 relative overflow-hidden\",\n                children: [\n                    content.hero.backgroundImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 z-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_6___default()), {\n                            src: content.hero.backgroundImage,\n                            alt: \"Hero Background\",\n                            fill: true,\n                            className: \"object-cover opacity-20\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 594,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                        lineNumber: 593,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto text-center relative z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto\",\n                            children: [\n                                content.hero.subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                    className: \"mb-4 text-sm px-4 py-2\",\n                                    children: content.hero.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 605,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-5xl md:text-6xl font-bold text-gray-900 mb-6\",\n                                    children: content.hero.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 609,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n                                    children: content.hero.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 612,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-4 justify-center mb-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: content.hero.primaryCTA.link,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                size: \"lg\",\n                                                className: \"text-lg px-8 py-3\",\n                                                children: [\n                                                    content.hero.primaryCTA.text,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"ml-2 h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 619,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 617,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 616,\n                                            columnNumber: 17\n                                        }, this),\n                                        content.hero.secondaryCTA.text && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: content.hero.secondaryCTA.link,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                size: \"lg\",\n                                                variant: \"outline\",\n                                                className: \"text-lg px-8 py-3\",\n                                                children: content.hero.secondaryCTA.text\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 623,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 615,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 603,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                        lineNumber: 602,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 591,\n                columnNumber: 9\n            }, this),\n            ((_content_features = content.features) === null || _content_features === void 0 ? void 0 : _content_features.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"features\",\n                className: \"py-20 px-4 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                    children: content.features.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 640,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                                    children: content.features.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 643,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 639,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: content.features.items.map((feature)=>{\n                                const IconComponent = getIconComponent(feature.icon);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"border-0 shadow-lg hover:shadow-xl transition-shadow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                        className: \"h-6 w-6 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 655,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 654,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-xl\",\n                                                    children: feature.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 657,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 653,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                className: \"text-gray-600\",\n                                                children: feature.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 660,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 659,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, feature.id, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 652,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 648,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 638,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 637,\n                columnNumber: 9\n            }, this),\n            ((_content_pricing = content.pricing) === null || _content_pricing === void 0 ? void 0 : _content_pricing.enabled) && plans.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"pricing\",\n                className: \"py-20 px-4 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                    children: content.pricing.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 677,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 max-w-2xl mx-auto mb-8\",\n                                    children: content.pricing.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 680,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-4 mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm \".concat(!isYearly ? \"text-gray-900 font-medium\" : \"text-gray-500\"),\n                                            children: \"Monthly\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 686,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Switch, {\n                                            checked: isYearly,\n                                            onCheckedChange: setIsYearly\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 689,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm \".concat(isYearly ? \"text-gray-900 font-medium\" : \"text-gray-500\"),\n                                            children: \"Yearly\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 693,\n                                            columnNumber: 17\n                                        }, this),\n                                        plans.some((plan)=>getYearlyDiscount(plan) > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            className: \"ml-2 bg-green-100 text-green-800\",\n                                            children: [\n                                                \"Save up to \",\n                                                Math.max(...plans.map(getYearlyDiscount)),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 697,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 685,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 676,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto\",\n                            children: plans.map((plan, index)=>{\n                                const features = getFeatureList(plan);\n                                const price = getPrice(plan);\n                                const discount = getYearlyDiscount(plan);\n                                const isPopular = index === 1 // Middle plan is popular\n                                ;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"relative \".concat(isPopular ? \"border-blue-500 shadow-xl scale-105\" : \"border-gray-200 shadow-lg\", \" bg-white\"),\n                                    children: [\n                                        isPopular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -top-4 left-1/2 transform -translate-x-1/2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                className: \"bg-blue-500 text-white px-4 py-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 717,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    \"Most Popular\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 716,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 715,\n                                            columnNumber: 23\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            className: \"text-center pb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: plan.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 724,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                    className: \"text-gray-600 mt-2\",\n                                                    children: plan.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 725,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-baseline justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-5xl font-bold text-gray-900\",\n                                                                    children: [\n                                                                        \"$\",\n                                                                        price.toFixed(0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                    lineNumber: 731,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-500 ml-1\",\n                                                                    children: \"/month\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                    lineNumber: 734,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 730,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        isYearly && discount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-green-600 mt-2\",\n                                                            children: [\n                                                                \"Save \",\n                                                                discount,\n                                                                \"% with yearly billing\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 737,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        plan.trialDays > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-blue-600 mt-2\",\n                                                            children: [\n                                                                plan.trialDays,\n                                                                \"-day free trial\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 742,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 729,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 723,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        features.slice(0, 8).map((feature, featureIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-green-500 flex-shrink-0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                        lineNumber: 753,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-700 text-sm\",\n                                                                        children: feature\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                        lineNumber: 754,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, featureIndex, true, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                lineNumber: 752,\n                                                                columnNumber: 27\n                                                            }, this)),\n                                                        features.length > 8 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500 italic\",\n                                                            children: [\n                                                                \"+\",\n                                                                features.length - 8,\n                                                                \" more features\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 758,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 750,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                    href: \"/auth/signup\",\n                                                    className: \"block\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        className: \"w-full \".concat(isPopular ? \"bg-blue-600 hover:bg-blue-700\" : \"\"),\n                                                        variant: isPopular ? \"default\" : \"outline\",\n                                                        size: \"lg\",\n                                                        children: [\n                                                            \"Get Started\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"ml-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                lineNumber: 771,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 765,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 764,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 749,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, plan.id, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 713,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 705,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mt-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"max-w-2xl mx-auto border-gray-200 shadow-lg bg-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                            children: \"Need something custom?\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 784,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-6\",\n                                            children: \"Contact our sales team for enterprise pricing, custom features, and dedicated support.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 787,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    size: \"lg\",\n                                                    variant: \"outline\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 792,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Contact Sales\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 791,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    size: \"lg\",\n                                                    variant: \"ghost\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 796,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Schedule Demo\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 795,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 790,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 783,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                lineNumber: 782,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 781,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 675,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 674,\n                columnNumber: 9\n            }, this),\n            ((_content_testimonials = content.testimonials) === null || _content_testimonials === void 0 ? void 0 : _content_testimonials.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"testimonials\",\n                className: \"py-20 px-4 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                    children: content.testimonials.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 812,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                                    children: content.testimonials.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 815,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 811,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: content.testimonials.items.map((testimonial)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"border-0 shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    ...Array(testimonial.rating)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-5 w-5 text-yellow-400 fill-current\"\n                                                    }, i, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 826,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 824,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"h-8 w-8 text-gray-300 mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 829,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-6 italic\",\n                                                children: [\n                                                    '\"',\n                                                    testimonial.content,\n                                                    '\"'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 830,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    testimonial.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                                        src: testimonial.avatar,\n                                                        alt: testimonial.name,\n                                                        width: 48,\n                                                        height: 48,\n                                                        className: \"rounded-full mr-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 835,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-gray-200 rounded-full mr-4 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-6 w-6 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 844,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 843,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-semibold text-gray-900\",\n                                                                children: testimonial.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                lineNumber: 848,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: [\n                                                                    testimonial.role,\n                                                                    \", \",\n                                                                    testimonial.company\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                lineNumber: 849,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 847,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 833,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 823,\n                                        columnNumber: 19\n                                    }, this)\n                                }, testimonial.id, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 822,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 820,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 810,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 809,\n                columnNumber: 9\n            }, this),\n            ((_content_faq = content.faq) === null || _content_faq === void 0 ? void 0 : _content_faq.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"faq\",\n                className: \"py-20 px-4 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto max-w-4xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                    children: content.faq.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 865,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600\",\n                                    children: content.faq.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 868,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 864,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: content.faq.items.map((faq)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"border shadow-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"p-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"w-full p-6 text-left flex items-center justify-between hover:bg-gray-50\",\n                                                onClick: ()=>setOpenFAQ(openFAQ === faq.id ? null : faq.id),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold text-gray-900\",\n                                                        children: faq.question\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 881,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    openFAQ === faq.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 883,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 885,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 877,\n                                                columnNumber: 21\n                                            }, this),\n                                            openFAQ === faq.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-6 pb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: faq.answer\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 890,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 889,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 876,\n                                        columnNumber: 19\n                                    }, this)\n                                }, faq.id, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 875,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 873,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 863,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 862,\n                columnNumber: 9\n            }, this),\n            ((_content_cta = content.cta) === null || _content_cta === void 0 ? void 0 : _content_cta.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4 bg-blue-600 relative overflow-hidden\",\n                children: [\n                    content.cta.backgroundImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 z-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_6___default()), {\n                            src: content.cta.backgroundImage,\n                            alt: \"CTA Background\",\n                            fill: true,\n                            className: \"object-cover opacity-20\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 906,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                        lineNumber: 905,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto text-center relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl font-bold text-white mb-6\",\n                                children: content.cta.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                lineNumber: 915,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-blue-100 mb-8 max-w-2xl mx-auto\",\n                                children: content.cta.description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                lineNumber: 918,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                href: content.cta.buttonLink,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    size: \"lg\",\n                                    variant: \"secondary\",\n                                    className: \"text-lg px-8 py-3\",\n                                    children: [\n                                        content.cta.buttonText,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"ml-2 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 924,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 922,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                lineNumber: 921,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                        lineNumber: 914,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 903,\n                columnNumber: 9\n            }, this),\n            ((_content_footer = content.footer) === null || _content_footer === void 0 ? void 0 : _content_footer.enabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-gray-900 text-white py-12 px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-4 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_FileText_Globe_Mail_Menu_Phone_Quote_Shield_Star_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-6 w-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 938,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-lg font-bold\",\n                                                    children: \"SaaS Platform\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 939,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 937,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400\",\n                                            children: content.footer.companyDescription\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 941,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 936,\n                                    columnNumber: 15\n                                }, this),\n                                content.footer.links.map((linkGroup)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-4\",\n                                                children: linkGroup.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 947,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-2 text-gray-400\",\n                                                children: linkGroup.items.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                            href: link.link,\n                                                            className: \"hover:text-white\",\n                                                            children: link.text\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 951,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, link.id, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 950,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 948,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, linkGroup.id, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 946,\n                                        columnNumber: 17\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 935,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: content.footer.copyrightText\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                lineNumber: 961,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 960,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 934,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 933,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n        lineNumber: 475,\n        columnNumber: 5\n    }, this);\n}\n_s(LandingPageContent, \"Ob77mu/u4HMnkCjOlnHJhwEopZQ=\");\n_c = LandingPageContent;\nvar _c;\n$RefreshReg$(_c, \"LandingPageContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/landing/landing-page-content.tsx\n"));

/***/ })

});