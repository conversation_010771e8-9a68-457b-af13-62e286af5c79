"use strict";(()=>{var e={};e.id=2345,e.ids=[2345],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},4161:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>v,originalPathname:()=>f,patchFetch:()=>y,requestAsyncStorage:()=>g,routeModule:()=>u,serverHooks:()=>_,staticGenerationAsyncStorage:()=>m,staticGenerationBailout:()=>b});var r={};a.r(r),a.d(r,{GET:()=>p});var i=a(95419),o=a(69108),s=a(99678),n=a(78070),l=a(81355),d=a(3205),c=a(9108);async function p(e,{params:t}){try{let e=await (0,l.getServerSession)(d.L);if(!e?.user?.id||!e?.user?.companyId)return n.Z.json({error:"Unauthorized"},{status:401});let a=await c._.contract.findFirst({where:{id:t.id,companyId:e.user.companyId},include:{customer:{select:{id:!0,name:!0,email:!0,phone:!0,company:!0,address:!0,city:!0,state:!0,country:!0,postalCode:!0}},quotation:{select:{id:!0,quotationNumber:!0,title:!0,total:!0}},invoice:{select:{id:!0,invoiceNumber:!0,total:!0,status:!0}},createdBy:{select:{name:!0,email:!0}},assignedTo:{select:{name:!0,email:!0}},company:{select:{name:!0,email:!0,phone:!0,address:!0,city:!0,state:!0,country:!0,postalCode:!0,website:!0,logo:!0}},signatures:{orderBy:{createdAt:"desc"},include:{signedBy:{select:{name:!0,email:!0}}}}}});if(!a)return n.Z.json({error:"Contract not found"},{status:404});let r=function(e){let{contract:t,customer:a,company:r,signatures:i}=e;return`
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contract ${t.contractNumber}</title>
    <style>
        body {
            font-family: 'Times New Roman', serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #333;
            padding-bottom: 20px;
        }
        .contract-title {
            font-size: 28px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 2px;
        }
        .contract-number {
            font-size: 18px;
            color: #666;
            margin-bottom: 10px;
        }
        .contract-date {
            font-size: 14px;
            color: #666;
        }
        .parties-section {
            margin: 40px 0;
            padding: 20px;
            background-color: #f9f9f9;
            border-left: 4px solid #333;
        }
        .party {
            margin-bottom: 30px;
        }
        .party-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            text-transform: uppercase;
        }
        .party-details {
            margin-left: 20px;
            line-height: 1.8;
        }
        .contract-body {
            margin: 40px 0;
            text-align: justify;
        }
        .section-title {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin: 30px 0 15px 0;
            border-bottom: 2px solid #333;
            padding-bottom: 5px;
        }
        .contract-details {
            margin: 30px 0;
            padding: 20px;
            background-color: #f5f5f5;
            border-radius: 5px;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #ddd;
        }
        .detail-row:last-child {
            border-bottom: none;
        }
        .detail-label {
            font-weight: bold;
            color: #555;
        }
        .detail-value {
            color: #333;
        }
        .terms-section {
            margin: 40px 0;
            padding: 20px;
            background-color: #fff;
            border: 2px solid #333;
        }
        .signatures-section {
            margin-top: 60px;
            padding: 30px 0;
            border-top: 2px solid #333;
        }
        .signature-block {
            margin: 40px 0;
            padding: 20px;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
        }
        .signature-line {
            border-bottom: 2px solid #333;
            height: 60px;
            margin: 20px 0;
            position: relative;
        }
        .signature-info {
            margin-top: 10px;
            font-size: 12px;
            color: #666;
        }
        .signed-signature {
            background-color: #e8f5e8;
            border-color: #4caf50;
        }
        .signed-indicator {
            color: #4caf50;
            font-weight: bold;
            font-size: 14px;
        }
        .status-badge {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-draft { background-color: #f3f4f6; color: #374151; }
        .status-review { background-color: #fef3c7; color: #d97706; }
        .status-sent { background-color: #dbeafe; color: #1d4ed8; }
        .status-signed { background-color: #d1fae5; color: #065f46; }
        .status-active { background-color: #dcfce7; color: #166534; }
        .status-completed { background-color: #e0e7ff; color: #3730a3; }
        .status-cancelled { background-color: #fee2e2; color: #dc2626; }
        .status-expired { background-color: #fed7aa; color: #ea580c; }
        .footer {
            margin-top: 60px;
            text-align: center;
            color: #666;
            font-size: 12px;
            border-top: 1px solid #ccc;
            padding-top: 20px;
        }
        .watermark {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            font-size: 72px;
            color: rgba(255, 0, 0, 0.1);
            z-index: -1;
            pointer-events: none;
        }
        @media print {
            body { margin: 0; padding: 20px; }
            .watermark { display: none; }
        }
    </style>
</head>
<body>
    ${"DRAFT"===t.status?'<div class="watermark">DRAFT</div>':""}
    ${"CANCELLED"===t.status?'<div class="watermark">CANCELLED</div>':""}

    <div class="header">
        <div class="contract-title">Service Agreement</div>
        <div class="contract-number">Contract No: ${t.contractNumber}</div>
        <div class="contract-date">Date: ${new Date(t.createdAt).toLocaleDateString()}</div>
        <div style="margin-top: 10px;">
            <span class="status-badge status-${t.status.toLowerCase()}">${t.status}</span>
        </div>
    </div>

    <div class="parties-section">
        <div class="party">
            <div class="party-title">Service Provider:</div>
            <div class="party-details">
                <strong>${r.name}</strong><br>
                ${r.address||""}<br>
                ${r.city||""}, ${r.state||""} ${r.postalCode||""}<br>
                ${r.country||""}<br>
                Email: ${r.email||""}<br>
                Phone: ${r.phone||""}<br>
                ${r.website?`Website: ${r.website}`:""}
            </div>
        </div>

        <div class="party">
            <div class="party-title">Client:</div>
            <div class="party-details">
                <strong>${a.name}</strong><br>
                ${a.company?`${a.company}<br>`:""}
                ${a.address?`${a.address}<br>`:""}
                ${a.city||a.state||a.postalCode?`${a.city||""}, ${a.state||""} ${a.postalCode||""}<br>`:""}
                ${a.country?`${a.country}<br>`:""}
                ${a.email?`Email: ${a.email}<br>`:""}
                ${a.phone?`Phone: ${a.phone}`:""}
            </div>
        </div>
    </div>

    <div class="contract-details">
        <div class="section-title">Contract Details</div>
        <div class="detail-row">
            <span class="detail-label">Contract Title:</span>
            <span class="detail-value">${t.title}</span>
        </div>
        <div class="detail-row">
            <span class="detail-label">Contract Type:</span>
            <span class="detail-value">${t.type}</span>
        </div>
        ${t.value?`
        <div class="detail-row">
            <span class="detail-label">Contract Value:</span>
            <span class="detail-value">${new Intl.NumberFormat("en-US",{style:"currency",currency:t.currency}).format(Number(t.value))}</span>
        </div>
        `:""}
        ${t.startDate?`
        <div class="detail-row">
            <span class="detail-label">Start Date:</span>
            <span class="detail-value">${new Date(t.startDate).toLocaleDateString()}</span>
        </div>
        `:""}
        ${t.endDate?`
        <div class="detail-row">
            <span class="detail-label">End Date:</span>
            <span class="detail-value">${new Date(t.endDate).toLocaleDateString()}</span>
        </div>
        `:""}
        ${t.autoRenewal?`
        <div class="detail-row">
            <span class="detail-label">Auto Renewal:</span>
            <span class="detail-value">Yes (${t.renewalPeriod||12} months)</span>
        </div>
        `:""}
        <div class="detail-row">
            <span class="detail-label">Priority:</span>
            <span class="detail-value">${t.priority}</span>
        </div>
    </div>

    <div class="contract-body">
        <div class="section-title">Scope of Work</div>
        <p>${t.description||"The scope of work will be defined in accordance with the terms and conditions outlined in this agreement."}</p>

        ${t.terms?`
        <div class="section-title">Terms and Conditions</div>
        <div class="terms-section">
            <p>${t.terms}</p>
        </div>
        `:""}

        ${t.conditions?`
        <div class="section-title">Additional Conditions</div>
        <p>${t.conditions}</p>
        `:""}

        <div class="section-title">General Terms</div>
        <p>1. <strong>Payment Terms:</strong> Payment shall be made according to the terms specified in related invoices and quotations.</p>
        <p>2. <strong>Confidentiality:</strong> Both parties agree to maintain confidentiality of all proprietary information shared during the course of this agreement.</p>
        <p>3. <strong>Termination:</strong> Either party may terminate this agreement with 30 days written notice.</p>
        <p>4. <strong>Governing Law:</strong> This agreement shall be governed by the laws of the jurisdiction where the service provider is located.</p>
        <p>5. <strong>Dispute Resolution:</strong> Any disputes arising from this agreement shall be resolved through mediation and, if necessary, binding arbitration.</p>
    </div>

    <div class="signatures-section">
        <div class="section-title">Signatures</div>
        
        ${i.length>0?`
        <p><strong>This contract has been electronically signed by the following parties:</strong></p>
        ${i.map(e=>`
            <div class="signature-block signed-signature">
                <div class="signed-indicator">✓ ELECTRONICALLY SIGNED</div>
                <div style="margin: 15px 0;">
                    <strong>Name:</strong> ${e.signerName}<br>
                    <strong>Email:</strong> ${e.signerEmail}<br>
                    ${e.signerRole?`<strong>Role:</strong> ${e.signerRole}<br>`:""}
                    <strong>Signed:</strong> ${new Date(e.signedAt).toLocaleString()}<br>
                    <strong>IP Address:</strong> ${e.ipAddress}<br>
                    ${e.notes?`<strong>Notes:</strong> ${e.notes}`:""}
                </div>
            </div>
        `).join("")}
        `:`
        <p><strong>Signature Required:</strong> ${t.signatureRequired?"Yes":"No"}</p>
        
        <div class="signature-block">
            <div><strong>Service Provider Signature:</strong></div>
            <div class="signature-line"></div>
            <div class="signature-info">
                Name: ___________________________ Date: _______________<br>
                Title: ___________________________ 
            </div>
        </div>

        <div class="signature-block">
            <div><strong>Client Signature:</strong></div>
            <div class="signature-line"></div>
            <div class="signature-info">
                Name: ___________________________ Date: _______________<br>
                Title: ___________________________
            </div>
        </div>
        `}
    </div>

    ${t.internalNotes?`
    <div style="margin-top: 40px; padding: 20px; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px;">
        <div class="section-title" style="color: #856404;">Internal Notes</div>
        <p style="color: #856404;">${t.internalNotes}</p>
    </div>
    `:""}

    <div class="footer">
        <p>This contract was generated on ${new Date().toLocaleDateString()} by ${t.createdBy.name||t.createdBy.email}</p>
        <p>Contract ID: ${t.id} | Company: ${r.name}</p>
        ${t.quotation?`<p>Related Quotation: ${t.quotation.quotationNumber}</p>`:""}
        ${t.invoice?`<p>Related Invoice: ${t.invoice.invoiceNumber}</p>`:""}
    </div>
</body>
</html>
  `}({contract:a,customer:a.customer,company:a.company,signatures:a.signatures});return new Response(r,{headers:{"Content-Type":"text/html","Content-Disposition":`inline; filename="contract-${a.contractNumber}.html"`}})}catch(e){return console.error("Error generating contract PDF:",e),n.Z.json({error:"Failed to generate PDF"},{status:500})}}let u=new i.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/contracts/[id]/pdf/route",pathname:"/api/contracts/[id]/pdf",filename:"route",bundlePath:"app/api/contracts/[id]/pdf/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\contracts\\[id]\\pdf\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:g,staticGenerationAsyncStorage:m,serverHooks:_,headerHooks:v,staticGenerationBailout:b}=u,f="/api/contracts/[id]/pdf/route";function y(){return(0,s.patchFetch)({serverHooks:_,staticGenerationAsyncStorage:m})}},3205:(e,t,a)=>{a.d(t,{L:()=>d});var r=a(86485),i=a(10375),o=a(50694),s=a(6521),n=a.n(s),l=a(9108);let d={providers:[(0,r.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await l._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),a=t?.companyId;if(!a&&t){let e=await l._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(a=e?.id)&&await l._.user.update({where:{id:t.id},data:{companyId:a}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await n().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await l._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:a}}catch(e){return console.error("Authentication error:",e),null}}}),(0,i.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,o.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,a)=>{a.d(t,{_:()=>i});let r=require("@prisma/client"),i=globalThis.prisma??new r.PrismaClient}};var t=require("../../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[1638,6206,6521,2455,4520],()=>a(4161));module.exports=r})();