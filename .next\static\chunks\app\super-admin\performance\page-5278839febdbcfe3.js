(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8654],{55458:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(62898).Z)("Cpu",[["rect",{x:"4",y:"4",width:"16",height:"16",rx:"2",key:"1vbyd7"}],["rect",{x:"9",y:"9",width:"6",height:"6",key:"o3kz5p"}],["path",{d:"M15 2v2",key:"13l42r"}],["path",{d:"M15 20v2",key:"15mkzm"}],["path",{d:"M2 15h2",key:"1gxd5l"}],["path",{d:"M2 9h2",key:"1bbxkp"}],["path",{d:"M20 15h2",key:"19e6y8"}],["path",{d:"M20 9h2",key:"19tzq7"}],["path",{d:"M9 2v2",key:"165o2o"}],["path",{d:"M9 20v2",key:"i2bqo8"}]])},97332:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(62898).Z)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},28956:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(62898).Z)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},26714:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(62898).Z)("Server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]])},74522:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(62898).Z)("TrendingDown",[["polyline",{points:"22 17 13.5 8.5 8.5 13.5 2 7",key:"1r2t7k"}],["polyline",{points:"16 17 22 17 22 11",key:"11uiuu"}]])},85790:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(62898).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},49037:function(e,s,a){Promise.resolve().then(a.bind(a,69346))},69346:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return Z}});var t=a(57437),n=a(2265),i=a(27815),l=a(31478),c=a(85754),r=a(10500),d=a(40110),x=a(19160),m=a(13008),h=a(72894),u=a(90998),o=a(85790),j=a(74522),N=a(64280),y=a(92457),f=a(26714),p=a(55458),v=a(469);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let g=(0,a(62898).Z)("Wifi",[["path",{d:"M5 13a10 10 0 0 1 14 0",key:"6v8j51"}],["path",{d:"M8.5 16.5a5 5 0 0 1 7 0",key:"sej527"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["line",{x1:"12",x2:"12.01",y1:"20",y2:"20",key:"of4bc4"}]]);var w=a(97332),k=a(28956);function Z(){let[e,s]=(0,n.useState)(!0),[a,Z]=(0,n.useState)(new Date),[b]=(0,n.useState)([{id:"1",name:"Response Time",value:245,unit:"ms",status:"good",trend:"down",change:-12},{id:"2",name:"Throughput",value:1250,unit:"req/min",status:"good",trend:"up",change:8},{id:"3",name:"Error Rate",value:.12,unit:"%",status:"good",trend:"down",change:-.05},{id:"4",name:"Database Query Time",value:89,unit:"ms",status:"warning",trend:"up",change:15}]),[S]=(0,n.useState)([{name:"CPU Usage",usage:45,limit:100,status:"good"},{name:"Memory Usage",usage:68,limit:100,status:"warning"},{name:"Disk Usage",usage:32,limit:100,status:"good"},{name:"Network I/O",usage:23,limit:100,status:"good"}]);(0,n.useEffect)(()=>{let e=setTimeout(()=>{s(!1)},1e3);return()=>clearTimeout(e)},[]);let M=e=>{switch(e){case"good":return(0,t.jsx)(m.Z,{className:"h-4 w-4 text-green-500"});case"warning":return(0,t.jsx)(h.Z,{className:"h-4 w-4 text-yellow-500"});case"critical":return(0,t.jsx)(h.Z,{className:"h-4 w-4 text-red-500"});default:return(0,t.jsx)(u.Z,{className:"h-4 w-4 text-gray-500"})}},z=e=>{switch(e){case"up":return(0,t.jsx)(o.Z,{className:"h-4 w-4 text-green-500"});case"down":return(0,t.jsx)(j.Z,{className:"h-4 w-4 text-red-500"});default:return(0,t.jsx)(u.Z,{className:"h-4 w-4 text-gray-500"})}};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Performance Monitor"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Real-time system performance metrics and monitoring"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(l.C,{variant:"outline",className:"text-xs",children:["Last updated: ",a.toLocaleTimeString()]}),(0,t.jsxs)(c.z,{onClick:()=>{s(!0),Z(new Date),setTimeout(()=>s(!1),1e3)},disabled:e,size:"sm",children:[(0,t.jsx)(N.Z,{className:"h-4 w-4 mr-2 ".concat(e?"animate-spin":"")}),"Refresh"]})]})]}),(0,t.jsx)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:b.map(e=>(0,t.jsxs)(i.Zb,{children:[(0,t.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(i.ll,{className:"text-sm font-medium",children:e.name}),M(e.status)]}),(0,t.jsxs)(i.aY,{children:[(0,t.jsxs)("div",{className:"text-2xl font-bold",children:[e.value,e.unit]}),(0,t.jsxs)("div",{className:"flex items-center text-xs text-muted-foreground",children:[z(e.trend),(0,t.jsxs)("span",{className:"ml-1",children:[e.change>0?"+":"",e.change,"% from last hour"]})]})]})]},e.id))}),(0,t.jsxs)(d.mQ,{defaultValue:"overview",className:"space-y-4",children:[(0,t.jsxs)(d.dr,{children:[(0,t.jsx)(d.SP,{value:"overview",children:"Overview"}),(0,t.jsx)(d.SP,{value:"resources",children:"System Resources"}),(0,t.jsx)(d.SP,{value:"database",children:"Database"}),(0,t.jsx)(d.SP,{value:"network",children:"Network"})]}),(0,t.jsx)(d.nU,{value:"overview",className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,t.jsxs)(i.Zb,{children:[(0,t.jsxs)(i.Ol,{children:[(0,t.jsxs)(i.ll,{className:"flex items-center",children:[(0,t.jsx)(y.Z,{className:"h-5 w-5 mr-2"}),"Performance Trends"]}),(0,t.jsx)(i.SZ,{children:"Key metrics over the last 24 hours"})]}),(0,t.jsx)(i.aY,{children:(0,t.jsx)("div",{className:"space-y-4",children:b.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[M(e.status),(0,t.jsx)("span",{className:"text-sm font-medium",children:e.name})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)("span",{className:"text-sm",children:[e.value,e.unit]}),z(e.trend)]})]},e.id))})})]}),(0,t.jsxs)(i.Zb,{children:[(0,t.jsxs)(i.Ol,{children:[(0,t.jsxs)(i.ll,{className:"flex items-center",children:[(0,t.jsx)(f.Z,{className:"h-5 w-5 mr-2"}),"System Health"]}),(0,t.jsx)(i.SZ,{children:"Current system resource utilization"})]}),(0,t.jsx)(i.aY,{children:(0,t.jsx)("div",{className:"space-y-4",children:S.map(e=>(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,t.jsx)("span",{className:"font-medium",children:e.name}),(0,t.jsxs)("span",{children:[e.usage,"%"]})]}),(0,t.jsx)(r.E,{value:e.usage,className:"h-2"})]},e.name))})})]})]})}),(0,t.jsx)(d.nU,{value:"resources",className:"space-y-4",children:(0,t.jsxs)(i.Zb,{children:[(0,t.jsxs)(i.Ol,{children:[(0,t.jsx)(i.ll,{children:"System Resources"}),(0,t.jsx)(i.SZ,{children:"Detailed view of system resource utilization"})]}),(0,t.jsx)(i.aY,{children:(0,t.jsxs)(x.iA,{children:[(0,t.jsx)(x.xD,{children:(0,t.jsxs)(x.SC,{children:[(0,t.jsx)(x.ss,{children:"Resource"}),(0,t.jsx)(x.ss,{children:"Usage"}),(0,t.jsx)(x.ss,{children:"Status"}),(0,t.jsx)(x.ss,{children:"Trend"})]})}),(0,t.jsx)(x.RM,{children:S.map(e=>(0,t.jsxs)(x.SC,{children:[(0,t.jsx)(x.pj,{className:"font-medium",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:["CPU Usage"===e.name&&(0,t.jsx)(p.Z,{className:"h-4 w-4"}),"Memory Usage"===e.name&&(0,t.jsx)(f.Z,{className:"h-4 w-4"}),"Disk Usage"===e.name&&(0,t.jsx)(v.Z,{className:"h-4 w-4"}),"Network I/O"===e.name&&(0,t.jsx)(g,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:e.name})]})}),(0,t.jsx)(x.pj,{children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(r.E,{value:e.usage,className:"w-20 h-2"}),(0,t.jsxs)("span",{className:"text-sm",children:[e.usage,"%"]})]})}),(0,t.jsx)(x.pj,{children:(0,t.jsx)(l.C,{variant:"good"===e.status?"default":"destructive",className:"capitalize",children:e.status})}),(0,t.jsx)(x.pj,{children:(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(o.Z,{className:"h-4 w-4 text-green-500"}),(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"Stable"})]})})]},e.name))})]})})]})}),(0,t.jsx)(d.nU,{value:"database",className:"space-y-4",children:(0,t.jsxs)(i.Zb,{children:[(0,t.jsxs)(i.Ol,{children:[(0,t.jsxs)(i.ll,{className:"flex items-center",children:[(0,t.jsx)(w.Z,{className:"h-5 w-5 mr-2"}),"Database Performance"]}),(0,t.jsx)(i.SZ,{children:"Database query performance and connection metrics"})]}),(0,t.jsx)(i.aY,{children:(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-3",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("div",{className:"text-sm font-medium",children:"Query Performance"}),(0,t.jsx)("div",{className:"text-2xl font-bold",children:"89ms"}),(0,t.jsx)("div",{className:"text-xs text-muted-foreground",children:"Average query time"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("div",{className:"text-sm font-medium",children:"Active Connections"}),(0,t.jsx)("div",{className:"text-2xl font-bold",children:"24"}),(0,t.jsx)("div",{className:"text-xs text-muted-foreground",children:"Current connections"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("div",{className:"text-sm font-medium",children:"Cache Hit Rate"}),(0,t.jsx)("div",{className:"text-2xl font-bold",children:"94.2%"}),(0,t.jsx)("div",{className:"text-xs text-muted-foreground",children:"Query cache efficiency"})]})]})})]})}),(0,t.jsx)(d.nU,{value:"network",className:"space-y-4",children:(0,t.jsxs)(i.Zb,{children:[(0,t.jsxs)(i.Ol,{children:[(0,t.jsxs)(i.ll,{className:"flex items-center",children:[(0,t.jsx)(k.Z,{className:"h-5 w-5 mr-2"}),"Network Performance"]}),(0,t.jsx)(i.SZ,{children:"Network latency and bandwidth utilization"})]}),(0,t.jsx)(i.aY,{children:(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-3",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("div",{className:"text-sm font-medium",children:"Latency"}),(0,t.jsx)("div",{className:"text-2xl font-bold",children:"12ms"}),(0,t.jsx)("div",{className:"text-xs text-muted-foreground",children:"Average response time"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("div",{className:"text-sm font-medium",children:"Bandwidth Usage"}),(0,t.jsx)("div",{className:"text-2xl font-bold",children:"23%"}),(0,t.jsx)("div",{className:"text-xs text-muted-foreground",children:"Current utilization"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("div",{className:"text-sm font-medium",children:"Packet Loss"}),(0,t.jsx)("div",{className:"text-2xl font-bold",children:"0.01%"}),(0,t.jsx)("div",{className:"text-xs text-muted-foreground",children:"Network reliability"})]})]})})]})})]})]})}}},function(e){e.O(0,[6723,9502,4522,9187,2971,4938,1744],function(){return e(e.s=49037)}),_N_E=e.O()}]);