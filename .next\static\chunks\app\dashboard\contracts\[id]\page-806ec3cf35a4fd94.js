(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5724],{70780:function(e,s,t){Promise.resolve().then(t.bind(t,20189))},20189:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return R}});var a=t(57437),r=t(2265),n=t(24033),i=t(27815),c=t(85754),l=t(31478),d=t(80382),o=t(13008),x=t(72894),m=t(6141),u=t(83284),h=t(73067),f=t(56224),j=t(76020),p=t(35817),N=t(49617),g=t(45367),v=t(49036),y=t(33673),b=t(67972),w=t(76637),C=t(28203),D=t(46232),E=t(5925),Z=t(61396),S=t.n(Z);function R(){let e=(0,n.useParams)(),s=(0,n.useRouter)(),[t,Z]=(0,r.useState)(null),[R,T]=(0,r.useState)(!0),[k,z]=(0,r.useState)(!1),I=async()=>{try{let t=await fetch("/api/contracts/".concat(e.id));if(!t.ok){if(404===t.status){E.toast.error("Contract not found"),s.push("/dashboard/contracts");return}throw Error("Failed to fetch contract")}let a=await t.json();Z(a)}catch(e){E.toast.error("Failed to load contract details"),console.error("Error fetching contract:",e)}finally{T(!1)}};(0,r.useEffect)(()=>{e.id&&I()},[e.id]);let O=async()=>{if(t&&confirm('Are you sure you want to delete contract "'.concat(t.contractNumber,'"?')))try{let e=await fetch("/api/contracts/".concat(t.id),{method:"DELETE"});if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to delete contract")}E.toast.success("Contract deleted successfully"),s.push("/dashboard/contracts")}catch(e){E.toast.error(e instanceof Error?e.message:"Failed to delete contract")}},A=e=>{switch(e){case"DRAFT":return(0,a.jsx)(l.C,{variant:"secondary",children:"Draft"});case"REVIEW":return(0,a.jsx)(l.C,{variant:"warning",children:"Review"});case"SENT":return(0,a.jsx)(l.C,{variant:"info",children:"Sent"});case"SIGNED":return(0,a.jsx)(l.C,{variant:"success",children:"Signed"});case"ACTIVE":return(0,a.jsx)(l.C,{variant:"success",children:"Active"});case"COMPLETED":return(0,a.jsx)(l.C,{variant:"success",children:"Completed"});case"CANCELLED":return(0,a.jsx)(l.C,{variant:"destructive",children:"Cancelled"});case"EXPIRED":return(0,a.jsx)(l.C,{variant:"destructive",children:"Expired"});default:return(0,a.jsx)(l.C,{variant:"secondary",children:e})}},L=e=>{switch(e){case"URGENT":return(0,a.jsx)(l.C,{variant:"destructive",children:"Urgent"});case"HIGH":return(0,a.jsx)(l.C,{variant:"warning",children:"High"});case"MEDIUM":return(0,a.jsx)(l.C,{variant:"info",children:"Medium"});case"LOW":return(0,a.jsx)(l.C,{variant:"secondary",children:"Low"});default:return(0,a.jsx)(l.C,{variant:"secondary",children:e})}},V=e=>{let s={SERVICE:"bg-blue-100 text-blue-800",PRODUCT:"bg-green-100 text-green-800",SUBSCRIPTION:"bg-purple-100 text-purple-800",MAINTENANCE:"bg-orange-100 text-orange-800",CONSULTING:"bg-indigo-100 text-indigo-800",OTHER:"bg-gray-100 text-gray-800"};return(0,a.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(s[e]||s.OTHER),children:e})};if(R)return(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})});if(!t)return(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("p",{className:"text-gray-500",children:"Contract not found"}),(0,a.jsx)(c.z,{asChild:!0,className:"mt-4",children:(0,a.jsx)(S(),{href:"/dashboard/contracts",children:"Back to Contracts"})})]});let P=t.endDate&&new Date(t.endDate)<new Date,F=t.endDate&&new Date(t.endDate)<new Date(Date.now()+2592e6);return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(c.z,{variant:"ghost",size:"sm",asChild:!0,children:(0,a.jsxs)(S(),{href:"/dashboard/contracts",children:[(0,a.jsx)(h.Z,{className:"h-4 w-4 mr-2"}),"Back to Contracts"]})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(e=>{switch(e){case"SIGNED":case"ACTIVE":case"COMPLETED":return(0,a.jsx)(o.Z,{className:"h-5 w-5 text-green-600"});case"EXPIRED":case"CANCELLED":return(0,a.jsx)(x.Z,{className:"h-5 w-5 text-red-600"});case"DRAFT":return(0,a.jsx)(m.Z,{className:"h-5 w-5 text-gray-600"});default:return(0,a.jsx)(u.Z,{className:"h-5 w-5 text-blue-600"})}})(t.status),(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:t.contractNumber})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[A(t.status),L(t.priority),V(t.type),P&&(0,a.jsx)(l.C,{variant:"destructive",children:"Expired"}),F&&!P&&(0,a.jsx)(l.C,{variant:"warning",children:"Expiring Soon"})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(c.z,{variant:"outline",children:[(0,a.jsx)(f.Z,{className:"h-4 w-4 mr-2"}),"Duplicate"]}),(0,a.jsxs)(c.z,{variant:"outline",children:[(0,a.jsx)(j.Z,{className:"h-4 w-4 mr-2"}),"Send for Signature"]}),(0,a.jsxs)(c.z,{variant:"outline",children:[(0,a.jsx)(p.Z,{className:"h-4 w-4 mr-2"}),"PDF"]}),(0,a.jsxs)(c.z,{variant:"outline",children:[(0,a.jsx)(u.Z,{className:"h-4 w-4 mr-2"}),"Signatures"]}),(0,a.jsxs)(c.z,{variant:"outline",onClick:()=>z(!0),children:[(0,a.jsx)(N.Z,{className:"h-4 w-4 mr-2"}),"Edit"]}),(0,a.jsxs)(c.z,{variant:"destructive",onClick:O,disabled:"SIGNED"===t.status||"ACTIVE"===t.status||t._count.signatures>0,children:[(0,a.jsx)(g.Z,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,a.jsxs)(i.Zb,{children:[(0,a.jsx)(i.Ol,{children:(0,a.jsxs)(i.ll,{className:"flex items-center",children:[(0,a.jsx)(u.Z,{className:"h-5 w-5 mr-2"}),"Contract Overview"]})}),(0,a.jsxs)(i.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:t.title}),t.description&&(0,a.jsx)("p",{className:"text-gray-600 mt-2",children:t.description})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Contract Number"}),(0,a.jsx)("p",{className:"font-medium",children:t.contractNumber})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Type"}),(0,a.jsx)("div",{className:"mt-1",children:V(t.type)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Status"}),(0,a.jsx)("div",{className:"mt-1",children:A(t.status)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Priority"}),(0,a.jsx)("div",{className:"mt-1",children:L(t.priority)})]}),t.value&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Contract Value"}),(0,a.jsxs)("p",{className:"font-medium text-lg text-green-600",children:[t.currency," ",t.value.toLocaleString()]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Signature Required"}),(0,a.jsx)("div",{className:"flex items-center space-x-2 mt-1",children:t.signatureRequired?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(v.Z,{className:"h-4 w-4 text-green-600"}),(0,a.jsx)("span",{className:"text-green-600",children:"Yes"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(v.Z,{className:"h-4 w-4 text-gray-400"}),(0,a.jsx)("span",{className:"text-gray-600",children:"No"})]})})]})]}),t.tags&&t.tags.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 mb-2",children:"Tags"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:t.tags.map(e=>(0,a.jsxs)(l.C,{variant:"outline",className:"flex items-center space-x-1",children:[(0,a.jsx)(y.Z,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:e})]},e))})]})]})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsx)(i.Ol,{children:(0,a.jsxs)(i.ll,{className:"flex items-center",children:[(0,a.jsx)(b.Z,{className:"h-5 w-5 mr-2"}),"Customer Information"]})}),(0,a.jsxs)(i.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Customer Name"}),(0,a.jsx)("p",{className:"font-medium",children:t.customer.name})]}),t.customer.company&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Company"}),(0,a.jsx)("p",{className:"font-medium",children:t.customer.company})]}),t.customer.email&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Email"}),(0,a.jsx)("p",{className:"font-medium",children:t.customer.email})]}),t.customer.phone&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Phone"}),(0,a.jsx)("p",{className:"font-medium",children:t.customer.phone})]})]}),(t.customer.address||t.customer.city)&&(0,a.jsxs)("div",{className:"pt-4 border-t",children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 mb-2",children:"Address"}),(0,a.jsxs)("div",{className:"text-gray-900",children:[t.customer.address&&(0,a.jsx)("p",{children:t.customer.address}),(0,a.jsx)("p",{children:[t.customer.city,t.customer.state,t.customer.postalCode].filter(Boolean).join(", ")}),t.customer.country&&(0,a.jsx)("p",{children:t.customer.country})]})]})]})]}),(t.quotation||t.invoice)&&(0,a.jsxs)(i.Zb,{children:[(0,a.jsx)(i.Ol,{children:(0,a.jsxs)(i.ll,{className:"flex items-center",children:[(0,a.jsx)(w.Z,{className:"h-5 w-5 mr-2"}),"Related Documents"]})}),(0,a.jsxs)(i.aY,{className:"space-y-4",children:[t.quotation&&(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"font-medium",children:["Quotation: ",t.quotation.quotationNumber]}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:t.quotation.title}),(0,a.jsxs)("p",{className:"text-sm text-green-600 font-medium",children:["Total: $",t.quotation.total.toLocaleString()]})]}),(0,a.jsx)(c.z,{variant:"outline",size:"sm",asChild:!0,children:(0,a.jsxs)(S(),{href:"/dashboard/quotations/".concat(t.quotation.id),children:[(0,a.jsx)(Eye,{className:"h-4 w-4 mr-2"}),"View"]})})]}),t.invoice&&(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"font-medium",children:["Invoice: ",t.invoice.invoiceNumber]}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Status: ",t.invoice.status]}),(0,a.jsxs)("p",{className:"text-sm text-green-600 font-medium",children:["Total: $",t.invoice.total.toLocaleString()]})]}),(0,a.jsx)(c.z,{variant:"outline",size:"sm",asChild:!0,children:(0,a.jsxs)(S(),{href:"/dashboard/invoices/".concat(t.invoice.id),children:[(0,a.jsx)(Eye,{className:"h-4 w-4 mr-2"}),"View"]})})]})]})]}),(t.terms||t.conditions)&&(0,a.jsxs)(i.Zb,{children:[(0,a.jsx)(i.Ol,{children:(0,a.jsx)(i.ll,{children:"Terms & Conditions"})}),(0,a.jsxs)(i.aY,{className:"space-y-4",children:[t.terms&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 mb-2",children:"Terms & Conditions"}),(0,a.jsx)("p",{className:"text-gray-900 whitespace-pre-wrap",children:t.terms})]}),t.conditions&&(0,a.jsxs)("div",{className:"pt-4 border-t",children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 mb-2",children:"Additional Conditions"}),(0,a.jsx)("p",{className:"text-gray-900 whitespace-pre-wrap",children:t.conditions})]})]})]}),t.notes&&(0,a.jsxs)(i.Zb,{children:[(0,a.jsx)(i.Ol,{children:(0,a.jsx)(i.ll,{children:"Internal Notes"})}),(0,a.jsx)(i.aY,{children:(0,a.jsx)("p",{className:"text-gray-900 whitespace-pre-wrap",children:t.notes})})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(i.Zb,{children:[(0,a.jsx)(i.Ol,{children:(0,a.jsx)(i.ll,{children:"Contract Details"})}),(0,a.jsxs)(i.aY,{className:"space-y-4",children:[t.startDate&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(C.Z,{className:"h-4 w-4 text-gray-400"}),(0,a.jsx)("span",{className:"text-sm",children:"Start Date"})]}),(0,a.jsx)("span",{className:"font-medium text-sm",children:new Date(t.startDate).toLocaleDateString()})]}),t.endDate&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(C.Z,{className:"h-4 w-4 text-gray-400"}),(0,a.jsx)("span",{className:"text-sm",children:"End Date"})]}),(0,a.jsx)("span",{className:"font-medium text-sm ".concat(P||F?"text-red-600":""),children:new Date(t.endDate).toLocaleDateString()})]}),t.renewalDate&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(D.Z,{className:"h-4 w-4 text-gray-400"}),(0,a.jsx)("span",{className:"text-sm",children:"Renewal Date"})]}),(0,a.jsx)("span",{className:"font-medium text-sm",children:new Date(t.renewalDate).toLocaleDateString()})]}),t.autoRenewal&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(D.Z,{className:"h-4 w-4 text-green-600"}),(0,a.jsx)("span",{className:"text-sm",children:"Auto Renewal"})]}),(0,a.jsx)("span",{className:"font-medium text-sm text-green-600",children:t.renewalPeriod?"Every ".concat(t.renewalPeriod," months"):"Enabled"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(b.Z,{className:"h-4 w-4 text-gray-400"}),(0,a.jsx)("span",{className:"text-sm",children:"Created By"})]}),(0,a.jsx)("span",{className:"font-medium text-sm",children:t.createdBy.name||"Unknown"})]}),t.assignedTo&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(b.Z,{className:"h-4 w-4 text-gray-400"}),(0,a.jsx)("span",{className:"text-sm",children:"Assigned To"})]}),(0,a.jsx)("span",{className:"font-medium text-sm",children:t.assignedTo.name||"Unknown"})]})]})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsx)(i.Ol,{children:(0,a.jsx)(i.ll,{children:"Activity Summary"})}),(0,a.jsxs)(i.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm",children:"Total Activities"}),(0,a.jsx)("span",{className:"font-medium",children:t._count.activities})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm",children:"Signatures"}),(0,a.jsx)("span",{className:"font-medium",children:t._count.signatures})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm",children:"Documents"}),(0,a.jsx)("span",{className:"font-medium",children:t._count.documents})]})]})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsx)(i.Ol,{children:(0,a.jsx)(i.ll,{children:"Quick Actions"})}),(0,a.jsxs)(i.aY,{className:"space-y-2",children:[(0,a.jsxs)(c.z,{variant:"outline",className:"w-full justify-start",children:[(0,a.jsx)(j.Z,{className:"h-4 w-4 mr-2"}),"Send for Signature"]}),(0,a.jsxs)(c.z,{variant:"outline",className:"w-full justify-start",children:[(0,a.jsx)(p.Z,{className:"h-4 w-4 mr-2"}),"Download PDF"]}),(0,a.jsxs)(c.z,{variant:"outline",className:"w-full justify-start",children:[(0,a.jsx)(u.Z,{className:"h-4 w-4 mr-2"}),"Manage Signatures"]}),(0,a.jsxs)(c.z,{variant:"outline",className:"w-full justify-start",children:[(0,a.jsx)(f.Z,{className:"h-4 w-4 mr-2"}),"Duplicate Contract"]})]})]})]})]}),(0,a.jsx)(d.R,{isOpen:k,onClose:()=>z(!1),onSuccess:I,contract:t,mode:"edit"})]})}},31478:function(e,s,t){"use strict";t.d(s,{C:function(){return c}});var a=t(57437);t(2265);var r=t(96061),n=t(1657);let i=(0,r.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600",info:"border-transparent bg-blue-500 text-white hover:bg-blue-600"}},defaultVariants:{variant:"default"}});function c(e){let{className:s,variant:t,...r}=e;return(0,a.jsx)("div",{className:(0,n.cn)(i({variant:t}),s),...r})}},85754:function(e,s,t){"use strict";t.d(s,{z:function(){return d}});var a=t(57437),r=t(2265),n=t(67256),i=t(96061),c=t(1657);let l=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=r.forwardRef((e,s)=>{let{className:t,variant:r,size:i,asChild:d=!1,...o}=e,x=d?n.g7:"button";return(0,a.jsx)(x,{className:(0,c.cn)(l({variant:r,size:i,className:t})),ref:s,...o})});d.displayName="Button"},27815:function(e,s,t){"use strict";t.d(s,{Ol:function(){return c},SZ:function(){return d},Zb:function(){return i},aY:function(){return o},eW:function(){return x},ll:function(){return l}});var a=t(57437),r=t(2265),n=t(1657);let i=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...r})});i.displayName="Card";let c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",t),...r})});c.displayName="CardHeader";let l=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("h3",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",t),...r})});l.displayName="CardTitle";let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",t),...r})});d.displayName="CardDescription";let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("p-6 pt-0",t),...r})});o.displayName="CardContent";let x=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",t),...r})});x.displayName="CardFooter"},42706:function(e,s,t){"use strict";t.d(s,{$N:function(){return f},Be:function(){return j},Vq:function(){return l},cN:function(){return h},cZ:function(){return m},fK:function(){return u},hg:function(){return d},t9:function(){return x}});var a=t(57437),r=t(2265),n=t(28712),i=t(82549),c=t(1657);let l=n.fC,d=n.xz,o=n.h_;n.x8;let x=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(n.aV,{ref:s,className:(0,c.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...r})});x.displayName=n.aV.displayName;let m=r.forwardRef((e,s)=>{let{className:t,children:r,...l}=e;return(0,a.jsxs)(o,{children:[(0,a.jsx)(x,{}),(0,a.jsxs)(n.VY,{ref:s,className:(0,c.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",t),...l,children:[r,(0,a.jsxs)(n.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(i.Z,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});m.displayName=n.VY.displayName;let u=e=>{let{className:s,...t}=e;return(0,a.jsx)("div",{className:(0,c.cn)("flex flex-col space-y-1.5 text-center sm:text-left",s),...t})};u.displayName="DialogHeader";let h=e=>{let{className:s,...t}=e;return(0,a.jsx)("div",{className:(0,c.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",s),...t})};h.displayName="DialogFooter";let f=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(n.Dx,{ref:s,className:(0,c.cn)("text-lg font-semibold leading-none tracking-tight",t),...r})});f.displayName=n.Dx.displayName;let j=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(n.dk,{ref:s,className:(0,c.cn)("text-sm text-muted-foreground",t),...r})});j.displayName=n.dk.displayName},45179:function(e,s,t){"use strict";t.d(s,{I:function(){return i}});var a=t(57437),r=t(2265),n=t(1657);let i=r.forwardRef((e,s)=>{let{className:t,type:r,...i}=e;return(0,a.jsx)("input",{type:r,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...i})});i.displayName="Input"},49842:function(e,s,t){"use strict";t.d(s,{_:function(){return d}});var a=t(57437),r=t(2265),n=t(36743),i=t(96061),c=t(1657);let l=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(n.f,{ref:s,className:(0,c.cn)(l(),t),...r})});d.displayName=n.f.displayName},1657:function(e,s,t){"use strict";t.d(s,{cn:function(){return n}});var a=t(57042),r=t(74769);function n(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,r.m6)((0,a.W)(s))}}},function(e){e.O(0,[6723,1706,1396,2881,7481,382,2971,4938,1744],function(){return e(e.s=70780)}),_N_E=e.O()}]);