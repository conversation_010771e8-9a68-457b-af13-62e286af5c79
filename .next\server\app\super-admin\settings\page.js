(()=>{var e={};e.id=3229,e.ids=[3229],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},44066:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,originalPathname:()=>u,pages:()=>o,routeModule:()=>m,tree:()=>c});var a=t(50482),r=t(69108),i=t(62563),l=t.n(i),n=t(68300),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let c=["",{children:["super-admin",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,29593)),"C:\\proj\\nextjs-saas\\app\\super-admin\\settings\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,11285)),"C:\\proj\\nextjs-saas\\app\\super-admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,59504)),"C:\\proj\\nextjs-saas\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\proj\\nextjs-saas\\app\\super-admin\\settings\\page.tsx"],u="/super-admin/settings/page",x={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/super-admin/settings/page",pathname:"/super-admin/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},70808:(e,s,t)=>{Promise.resolve().then(t.bind(t,99786))},99786:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>A});var a=t(95344),r=t(3729),i=t(47674),l=t(22254),n=t(61351),d=t(16212),c=t(92549),o=t(93601),u=t(69436),x=t(71809),m=t(1586),p=t(81036),f=t(17470),h=t(16802),j=t(25757),g=t(23485),b=t(99046),y=t(71206),v=t(33037),N=t(70009),w=t(34826),k=t(13746),C=t(33733),P=t(31498),S=t(51838),Z=t(30304),E=t(28765),R=t(53148),_=t(1222);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let M=(0,t(69224).Z)("Unlock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 9.9-1",key:"1mm8w8"}]]);var T=t(47958),V=t(38271);function A(){let{data:e,status:s}=(0,i.useSession)(),[t,A]=(0,r.useState)({}),[D,O]=(0,r.useState)([]),[z,I]=(0,r.useState)(!0),[q,F]=(0,r.useState)(!1),[$,Q]=(0,r.useState)(""),[U,B]=(0,r.useState)("all"),[G,J]=(0,r.useState)({}),[L,W]=(0,r.useState)({key:"",value:"",category:"",description:"",isPublic:!1,isEditable:!0}),[Y,K]=(0,r.useState)(!1);if("loading"===s)return a.jsx("div",{className:"min-h-screen flex items-center justify-center",children:a.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})});"unauthenticated"===s&&(0,l.redirect)("/auth/signin"),e?.user?.role!=="SUPER_ADMIN"&&(0,l.redirect)("/dashboard");let H=async()=>{try{I(!0);let e=new URLSearchParams({...U&&"all"!==U&&{category:U},...$&&{search:$}}),s=await fetch(`/api/super-admin/settings?${e}`);if(!s.ok)throw Error("Failed to fetch settings");let t=await s.json();A(t.settings),O(t.categories)}catch(e){console.error("Error fetching settings:",e)}finally{I(!1)}};(0,r.useEffect)(()=>{H()},[U,$]);let X=(e,s,t)=>{J(a=>({...a,[e]:{...a[e],[s]:t}}))},ee=async()=>{try{F(!0);let e=Object.entries(G).map(([e,s])=>({id:e,...s}));if(!(await fetch("/api/super-admin/settings",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({settings:e})})).ok)throw Error("Failed to save settings");J({}),await H()}catch(e){console.error("Error saving settings:",e)}finally{F(!1)}},es=async()=>{try{if(!(await fetch("/api/super-admin/settings",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(L)})).ok)throw Error("Failed to create setting");W({key:"",value:"",category:"",description:"",isPublic:!1,isEditable:!0}),K(!1),await H()}catch(e){console.error("Error creating setting:",e)}},et=async e=>{if(confirm("Are you sure you want to delete this setting?"))try{if(!(await fetch(`/api/super-admin/settings?id=${e}`,{method:"DELETE"})).ok)throw Error("Failed to delete setting");await H()}catch(e){console.error("Error deleting setting:",e)}},ea=e=>({security:g.Z,database:b.Z,email:y.Z,notifications:v.Z,ui:N.Z,api:w.Z,general:k.Z})[e.toLowerCase()]||k.Z,er=(e,s=!1)=>"boolean"==typeof e?e?"true":"false":"object"==typeof e?s?JSON.stringify(e,null,2):JSON.stringify(e):String(e),ei=(e,s)=>{if("boolean"==typeof s)return"true"===e;if("number"==typeof s)return Number(e);if("object"==typeof s)try{return JSON.parse(e)}catch{}return e},el=Object.keys(G).length>0;return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(k.Z,{className:"h-8 w-8 text-blue-600"}),a.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"System Settings"})]}),a.jsx("p",{className:"text-gray-500 mt-1",children:"Configure global application settings"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(d.z,{variant:"outline",onClick:H,disabled:z,children:[a.jsx(C.Z,{className:`h-4 w-4 mr-2 ${z?"animate-spin":""}`}),"Refresh"]}),el&&(0,a.jsxs)(d.z,{onClick:ee,disabled:q,children:[a.jsx(P.Z,{className:`h-4 w-4 mr-2 ${q?"animate-spin":""}`}),"Save Changes"]}),(0,a.jsxs)(h.Vq,{open:Y,onOpenChange:K,children:[a.jsx(h.hg,{asChild:!0,children:(0,a.jsxs)(d.z,{children:[a.jsx(S.Z,{className:"h-4 w-4 mr-2"}),"Add Setting"]})}),(0,a.jsxs)(h.cZ,{children:[(0,a.jsxs)(h.fK,{children:[a.jsx(h.$N,{children:"Create New Setting"}),a.jsx(h.Be,{children:"Add a new system setting to configure application behavior."})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[a.jsx(m._,{htmlFor:"key",children:"Key"}),a.jsx(c.I,{id:"key",value:L.key,onChange:e=>W(s=>({...s,key:e.target.value})),placeholder:"e.g., app.max_users_per_company"})]}),(0,a.jsxs)("div",{children:[a.jsx(m._,{htmlFor:"category",children:"Category"}),(0,a.jsxs)(f.Ph,{value:L.category,onValueChange:e=>W(s=>({...s,category:e})),children:[a.jsx(f.i4,{children:a.jsx(f.ki,{placeholder:"Select category"})}),(0,a.jsxs)(f.Bw,{children:[a.jsx(f.Ql,{value:"general",children:"General"}),a.jsx(f.Ql,{value:"security",children:"Security"}),a.jsx(f.Ql,{value:"database",children:"Database"}),a.jsx(f.Ql,{value:"email",children:"Email"}),a.jsx(f.Ql,{value:"notifications",children:"Notifications"}),a.jsx(f.Ql,{value:"ui",children:"UI/UX"}),a.jsx(f.Ql,{value:"api",children:"API"})]})]})]}),(0,a.jsxs)("div",{children:[a.jsx(m._,{htmlFor:"value",children:"Value"}),a.jsx(o.g,{id:"value",value:L.value,onChange:e=>W(s=>({...s,value:e.target.value})),placeholder:"Setting value (JSON for objects)"})]}),(0,a.jsxs)("div",{children:[a.jsx(m._,{htmlFor:"description",children:"Description"}),a.jsx(o.g,{id:"description",value:L.description,onChange:e=>W(s=>({...s,description:e.target.value})),placeholder:"Describe what this setting does"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(x.r,{id:"isPublic",checked:L.isPublic,onCheckedChange:e=>W(s=>({...s,isPublic:e}))}),a.jsx(m._,{htmlFor:"isPublic",children:"Public"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(x.r,{id:"isEditable",checked:L.isEditable,onCheckedChange:e=>W(s=>({...s,isEditable:e}))}),a.jsx(m._,{htmlFor:"isEditable",children:"Editable"})]})]})]}),(0,a.jsxs)(h.cN,{children:[a.jsx(d.z,{variant:"outline",onClick:()=>K(!1),children:"Cancel"}),a.jsx(d.z,{onClick:es,children:"Create Setting"})]})]})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Total Settings"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:Object.values(t).flat().length})]}),a.jsx(k.Z,{className:"h-8 w-8 text-blue-600"})]})})}),a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Categories"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:D.length})]}),a.jsx(b.Z,{className:"h-8 w-8 text-green-600"})]})})}),a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Public Settings"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:Object.values(t).flat().filter(e=>e.isPublic).length})]}),a.jsx(Z.Z,{className:"h-8 w-8 text-purple-600"})]})})})]}),a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[a.jsx("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[a.jsx(E.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),a.jsx(c.I,{placeholder:"Search settings...",value:$,onChange:e=>Q(e.target.value),className:"pl-10"})]})}),(0,a.jsxs)(f.Ph,{value:U,onValueChange:B,children:[a.jsx(f.i4,{className:"w-full sm:w-48",children:a.jsx(f.ki,{placeholder:"All Categories"})}),(0,a.jsxs)(f.Bw,{children:[a.jsx(f.Ql,{value:"all",children:"All Categories"}),D.map(e=>(0,a.jsxs)(f.Ql,{value:e.name,children:[e.name," (",e.count,")"]},e.name))]})]})]})})}),(0,a.jsxs)(j.mQ,{value:U,onValueChange:B,children:[(0,a.jsxs)(j.dr,{className:"grid w-full grid-cols-4 lg:grid-cols-8",children:[a.jsx(j.SP,{value:"all",children:"All"}),D.slice(0,7).map(e=>{let s=ea(e.name);return(0,a.jsxs)(j.SP,{value:e.name,className:"flex items-center space-x-1",children:[a.jsx(s,{className:"h-4 w-4"}),a.jsx("span",{className:"hidden sm:inline",children:e.name})]},e.name)})]}),a.jsx(j.nU,{value:U,className:"space-y-6",children:z?a.jsx("div",{className:"flex items-center justify-center py-8",children:a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):Object.entries(t).map(([e,s])=>(0,a.jsxs)(n.Zb,{children:[a.jsx(n.Ol,{children:(0,a.jsxs)(n.ll,{className:"flex items-center space-x-2",children:[(()=>{let s=ea(e);return a.jsx(s,{className:"h-5 w-5"})})(),a.jsx("span",{children:e}),a.jsx(u.C,{variant:"secondary",children:s.length})]})}),a.jsx(n.aY,{children:a.jsx("div",{className:"overflow-x-auto",children:(0,a.jsxs)(p.iA,{children:[a.jsx(p.xD,{children:(0,a.jsxs)(p.SC,{children:[a.jsx(p.ss,{children:"Key"}),a.jsx(p.ss,{children:"Value"}),a.jsx(p.ss,{children:"Description"}),a.jsx(p.ss,{children:"Flags"}),a.jsx(p.ss,{children:"Actions"})]})}),a.jsx(p.RM,{children:s.map(e=>{let s=G[e.id],t=s?.value!==void 0?s.value:e.value;return(0,a.jsxs)(p.SC,{children:[a.jsx(p.pj,{children:a.jsx("code",{className:"text-sm bg-gray-100 px-2 py-1 rounded",children:e.key})}),a.jsx(p.pj,{children:e.isEditable?a.jsx(o.g,{value:er(t,!0),onChange:s=>X(e.id,"value",ei(s.target.value,e.value)),className:"min-h-[60px] font-mono text-sm"}):a.jsx("code",{className:"text-sm bg-gray-50 px-2 py-1 rounded block max-w-xs overflow-hidden",children:er(e.value)})}),a.jsx(p.pj,{children:e.isEditable?a.jsx(c.I,{value:s?.description!==void 0?s.description:e.description||"",onChange:s=>X(e.id,"description",s.target.value),placeholder:"Add description..."}):a.jsx("span",{className:"text-sm text-gray-600",children:e.description||"No description"})}),a.jsx(p.pj,{children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[e.isPublic?a.jsx(R.Z,{className:"h-4 w-4 text-green-600"}):a.jsx(_.Z,{className:"h-4 w-4 text-gray-400"}),a.jsx("span",{className:"text-xs",children:e.isPublic?"Public":"Private"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[e.isEditable?a.jsx(M,{className:"h-4 w-4 text-blue-600"}):a.jsx(T.Z,{className:"h-4 w-4 text-gray-400"}),a.jsx("span",{className:"text-xs",children:e.isEditable?"Editable":"Locked"})]})]})}),a.jsx(p.pj,{children:e.isEditable&&a.jsx(d.z,{variant:"ghost",size:"sm",onClick:()=>et(e.id),children:a.jsx(V.Z,{className:"h-4 w-4"})})})]},e.id)})})]})})})]},e))})]})]})}},92549:(e,s,t)=>{"use strict";t.d(s,{I:()=>l});var a=t(95344),r=t(3729),i=t(91626);let l=r.forwardRef(({className:e,type:s,...t},r)=>a.jsx("input",{type:s,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...t}));l.displayName="Input"},1586:(e,s,t)=>{"use strict";t.d(s,{_:()=>c});var a=t(95344),r=t(3729),i=t(14217),l=t(49247),n=t(91626);let d=(0,l.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=r.forwardRef(({className:e,...s},t)=>a.jsx(i.f,{ref:t,className:(0,n.cn)(d(),e),...s}));c.displayName=i.f.displayName},17470:(e,s,t)=>{"use strict";t.d(s,{Bw:()=>f,Ph:()=>o,Ql:()=>h,i4:()=>x,ki:()=>u});var a=t(95344),r=t(3729),i=t(1146),l=t(25390),n=t(12704),d=t(62312),c=t(91626);let o=i.fC;i.ZA;let u=i.B4,x=r.forwardRef(({className:e,children:s,...t},r)=>(0,a.jsxs)(i.xz,{ref:r,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...t,children:[s,a.jsx(i.JO,{asChild:!0,children:a.jsx(l.Z,{className:"h-4 w-4 opacity-50"})})]}));x.displayName=i.xz.displayName;let m=r.forwardRef(({className:e,...s},t)=>a.jsx(i.u_,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:a.jsx(n.Z,{className:"h-4 w-4"})}));m.displayName=i.u_.displayName;let p=r.forwardRef(({className:e,...s},t)=>a.jsx(i.$G,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:a.jsx(l.Z,{className:"h-4 w-4"})}));p.displayName=i.$G.displayName;let f=r.forwardRef(({className:e,children:s,position:t="popper",...r},l)=>a.jsx(i.h_,{children:(0,a.jsxs)(i.VY,{ref:l,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:t,...r,children:[a.jsx(m,{}),a.jsx(i.l_,{className:(0,c.cn)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),a.jsx(p,{})]})}));f.displayName=i.VY.displayName,r.forwardRef(({className:e,...s},t)=>a.jsx(i.__,{ref:t,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...s})).displayName=i.__.displayName;let h=r.forwardRef(({className:e,children:s,...t},r)=>(0,a.jsxs)(i.ck,{ref:r,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[a.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:a.jsx(i.wU,{children:a.jsx(d.Z,{className:"h-4 w-4"})})}),a.jsx(i.eT,{children:s})]}));h.displayName=i.ck.displayName,r.forwardRef(({className:e,...s},t)=>a.jsx(i.Z0,{ref:t,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",e),...s})).displayName=i.Z0.displayName},71809:(e,s,t)=>{"use strict";t.d(s,{r:()=>w});var a=t(95344),r=t(3729),i=t(85222),l=t(31405),n=t(98462),d=t(33183),c=t(92062),o=t(63085),u=t(62409),x="Switch",[m,p]=(0,n.b)(x),[f,h]=m(x),j=r.forwardRef((e,s)=>{let{__scopeSwitch:t,name:n,checked:c,defaultChecked:o,required:m,disabled:p,value:h="on",onCheckedChange:j,form:g,...b}=e,[N,w]=r.useState(null),k=(0,l.e)(s,e=>w(e)),C=r.useRef(!1),P=!N||g||!!N.closest("form"),[S,Z]=(0,d.T)({prop:c,defaultProp:o??!1,onChange:j,caller:x});return(0,a.jsxs)(f,{scope:t,checked:S,disabled:p,children:[(0,a.jsx)(u.WV.button,{type:"button",role:"switch","aria-checked":S,"aria-required":m,"data-state":v(S),"data-disabled":p?"":void 0,disabled:p,value:h,...b,ref:k,onClick:(0,i.M)(e.onClick,e=>{Z(e=>!e),P&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),P&&(0,a.jsx)(y,{control:N,bubbles:!C.current,name:n,value:h,checked:S,required:m,disabled:p,form:g,style:{transform:"translateX(-100%)"}})]})});j.displayName=x;var g="SwitchThumb",b=r.forwardRef((e,s)=>{let{__scopeSwitch:t,...r}=e,i=h(g,t);return(0,a.jsx)(u.WV.span,{"data-state":v(i.checked),"data-disabled":i.disabled?"":void 0,...r,ref:s})});b.displayName=g;var y=r.forwardRef(({__scopeSwitch:e,control:s,checked:t,bubbles:i=!0,...n},d)=>{let u=r.useRef(null),x=(0,l.e)(u,d),m=(0,c.D)(t),p=(0,o.t)(s);return r.useEffect(()=>{let e=u.current;if(!e)return;let s=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(m!==t&&s){let a=new Event("click",{bubbles:i});s.call(e,t),e.dispatchEvent(a)}},[m,t,i]),(0,a.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:t,...n,tabIndex:-1,ref:x,style:{...n.style,...p,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function v(e){return e?"checked":"unchecked"}y.displayName="SwitchBubbleInput";var N=t(91626);let w=r.forwardRef(({className:e,...s},t)=>a.jsx(j,{className:(0,N.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...s,ref:t,children:a.jsx(b,{className:(0,N.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));w.displayName=j.displayName},81036:(e,s,t)=>{"use strict";t.d(s,{RM:()=>d,SC:()=>c,iA:()=>l,pj:()=>u,ss:()=>o,xD:()=>n});var a=t(95344),r=t(3729),i=t(91626);let l=r.forwardRef(({className:e,...s},t)=>a.jsx("div",{className:"relative w-full overflow-auto",children:a.jsx("table",{ref:t,className:(0,i.cn)("w-full caption-bottom text-sm",e),...s})}));l.displayName="Table";let n=r.forwardRef(({className:e,...s},t)=>a.jsx("thead",{ref:t,className:(0,i.cn)("[&_tr]:border-b",e),...s}));n.displayName="TableHeader";let d=r.forwardRef(({className:e,...s},t)=>a.jsx("tbody",{ref:t,className:(0,i.cn)("[&_tr:last-child]:border-0",e),...s}));d.displayName="TableBody",r.forwardRef(({className:e,...s},t)=>a.jsx("tfoot",{ref:t,className:(0,i.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...s})).displayName="TableFooter";let c=r.forwardRef(({className:e,...s},t)=>a.jsx("tr",{ref:t,className:(0,i.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...s}));c.displayName="TableRow";let o=r.forwardRef(({className:e,...s},t)=>a.jsx("th",{ref:t,className:(0,i.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...s}));o.displayName="TableHead";let u=r.forwardRef(({className:e,...s},t)=>a.jsx("td",{ref:t,className:(0,i.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...s}));u.displayName="TableCell",r.forwardRef(({className:e,...s},t)=>a.jsx("caption",{ref:t,className:(0,i.cn)("mt-4 text-sm text-muted-foreground",e),...s})).displayName="TableCaption"},25757:(e,s,t)=>{"use strict";t.d(s,{mQ:()=>E,nU:()=>M,dr:()=>R,SP:()=>_});var a=t(95344),r=t(3729),i=t(85222),l=t(98462),n=t(34504),d=t(43234),c=t(62409),o=t(3975),u=t(33183),x=t(99048),m="Tabs",[p,f]=(0,l.b)(m,[n.Pc]),h=(0,n.Pc)(),[j,g]=p(m),b=r.forwardRef((e,s)=>{let{__scopeTabs:t,value:r,onValueChange:i,defaultValue:l,orientation:n="horizontal",dir:d,activationMode:p="automatic",...f}=e,h=(0,o.gm)(d),[g,b]=(0,u.T)({prop:r,onChange:i,defaultProp:l??"",caller:m});return(0,a.jsx)(j,{scope:t,baseId:(0,x.M)(),value:g,onValueChange:b,orientation:n,dir:h,activationMode:p,children:(0,a.jsx)(c.WV.div,{dir:h,"data-orientation":n,...f,ref:s})})});b.displayName=m;var y="TabsList",v=r.forwardRef((e,s)=>{let{__scopeTabs:t,loop:r=!0,...i}=e,l=g(y,t),d=h(t);return(0,a.jsx)(n.fC,{asChild:!0,...d,orientation:l.orientation,dir:l.dir,loop:r,children:(0,a.jsx)(c.WV.div,{role:"tablist","aria-orientation":l.orientation,...i,ref:s})})});v.displayName=y;var N="TabsTrigger",w=r.forwardRef((e,s)=>{let{__scopeTabs:t,value:r,disabled:l=!1,...d}=e,o=g(N,t),u=h(t),x=P(o.baseId,r),m=S(o.baseId,r),p=r===o.value;return(0,a.jsx)(n.ck,{asChild:!0,...u,focusable:!l,active:p,children:(0,a.jsx)(c.WV.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":m,"data-state":p?"active":"inactive","data-disabled":l?"":void 0,disabled:l,id:x,...d,ref:s,onMouseDown:(0,i.M)(e.onMouseDown,e=>{l||0!==e.button||!1!==e.ctrlKey?e.preventDefault():o.onValueChange(r)}),onKeyDown:(0,i.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&o.onValueChange(r)}),onFocus:(0,i.M)(e.onFocus,()=>{let e="manual"!==o.activationMode;p||l||!e||o.onValueChange(r)})})})});w.displayName=N;var k="TabsContent",C=r.forwardRef((e,s)=>{let{__scopeTabs:t,value:i,forceMount:l,children:n,...o}=e,u=g(k,t),x=P(u.baseId,i),m=S(u.baseId,i),p=i===u.value,f=r.useRef(p);return r.useEffect(()=>{let e=requestAnimationFrame(()=>f.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,a.jsx)(d.z,{present:l||p,children:({present:t})=>(0,a.jsx)(c.WV.div,{"data-state":p?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":x,hidden:!t,id:m,tabIndex:0,...o,ref:s,style:{...e.style,animationDuration:f.current?"0s":void 0},children:t&&n})})});function P(e,s){return`${e}-trigger-${s}`}function S(e,s){return`${e}-content-${s}`}C.displayName=k;var Z=t(91626);let E=b,R=r.forwardRef(({className:e,...s},t)=>a.jsx(v,{ref:t,className:(0,Z.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s}));R.displayName=v.displayName;let _=r.forwardRef(({className:e,...s},t)=>a.jsx(w,{ref:t,className:(0,Z.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s}));_.displayName=w.displayName;let M=r.forwardRef(({className:e,...s},t)=>a.jsx(C,{ref:t,className:(0,Z.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));M.displayName=C.displayName},93601:(e,s,t)=>{"use strict";t.d(s,{g:()=>l});var a=t(95344),r=t(3729),i=t(91626);let l=r.forwardRef(({className:e,...s},t)=>a.jsx("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:t,...s}));l.displayName="Textarea"},1222:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},53148:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},51838:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},31498:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},38271:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},29593:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>i,__esModule:()=>r,default:()=>l});let a=(0,t(86843).createProxy)(String.raw`C:\proj\nextjs-saas\app\super-admin\settings\page.tsx`),{__esModule:r,$$typeof:i}=a,l=a.default},14217:(e,s,t)=>{"use strict";t.d(s,{f:()=>n});var a=t(3729),r=t(62409),i=t(95344),l=a.forwardRef((e,s)=>(0,i.jsx)(r.WV.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));l.displayName="Label";var n=l}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[1638,7948,6671,4626,7792,2506,8830,1729,2125,3965],()=>t(44066));module.exports=a})();