import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '30' // days
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - parseInt(period))

    // Get comprehensive contract analytics
    const [
      totalContracts,
      contractsByStatus,
      contractsByType,
      contractsByMonth,
      contractMetrics,
      topContractsByValue,
      recentContracts,
      renewalAnalysis,
      customerContracts,
      valueMetrics,
      expiringContracts,
      signatureMetrics
    ] = await Promise.all([
      // Total contracts
      prisma.contract.count({
        where: {
          companyId: session.user.companyId,
          createdAt: { gte: startDate }
        }
      }),

      // Contracts by status
      prisma.contract.groupBy({
        by: ['status'],
        where: {
          companyId: session.user.companyId,
          createdAt: { gte: startDate }
        },
        _count: {
          id: true
        },
        _sum: {
          value: true
        }
      }),

      // Contracts by type
      prisma.contract.groupBy({
        by: ['type'],
        where: {
          companyId: session.user.companyId,
          createdAt: { gte: startDate }
        },
        _count: {
          id: true
        },
        _sum: {
          value: true
        }
      }),

      // Contracts by month (simplified)
      Promise.resolve([
        {
          month: new Date().toISOString().split('T')[0],
          contract_count: await prisma.contract.count({ where: { companyId: session.user.companyId } }),
          total_value: await prisma.contract.aggregate({
            where: { companyId: session.user.companyId },
            _sum: { value: true }
          }).then(result => Number(result._sum.value || 0)),
          signed_count: await prisma.contract.count({ where: { companyId: session.user.companyId, status: 'SIGNED' } }),
          active_count: await prisma.contract.count({ where: { companyId: session.user.companyId, status: 'ACTIVE' } })
        }
      ]),

      // Contract metrics
      Promise.all([
        // Active contracts
        prisma.contract.count({
          where: {
            companyId: session.user.companyId,
            status: 'ACTIVE'
          }
        }),
        // Signed contracts
        prisma.contract.count({
          where: {
            companyId: session.user.companyId,
            status: 'SIGNED',
            createdAt: { gte: startDate }
          }
        }),
        // Pending signatures
        prisma.contract.count({
          where: {
            companyId: session.user.companyId,
            status: { in: ['SENT', 'REVIEW'] },
            signatureRequired: true
          }
        }),
        // Auto-renewal contracts
        prisma.contract.count({
          where: {
            companyId: session.user.companyId,
            autoRenewal: true,
            status: 'ACTIVE'
          }
        })
      ]),

      // Top contracts by value
      prisma.contract.findMany({
        where: {
          companyId: session.user.companyId,
          createdAt: { gte: startDate },
          value: { not: null }
        },
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              company: true,
              email: true
            }
          },
          createdBy: {
            select: {
              name: true,
              email: true
            }
          }
        },
        orderBy: { value: 'desc' },
        take: 10
      }),

      // Recent contracts
      prisma.contract.findMany({
        where: {
          companyId: session.user.companyId,
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
          }
        },
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              company: true
            }
          },
          createdBy: {
            select: {
              name: true,
              email: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: 10
      }),

      // Renewal analysis
      Promise.all([
        // Contracts expiring in next 30 days
        prisma.contract.count({
          where: {
            companyId: session.user.companyId,
            status: 'ACTIVE',
            endDate: {
              gte: new Date(),
              lte: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
            }
          }
        }),
        // Contracts expiring in next 90 days
        prisma.contract.count({
          where: {
            companyId: session.user.companyId,
            status: 'ACTIVE',
            endDate: {
              gte: new Date(),
              lte: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000)
            }
          }
        }),
        // Contracts with auto-renewal
        prisma.contract.aggregate({
          where: {
            companyId: session.user.companyId,
            autoRenewal: true,
            status: 'ACTIVE'
          },
          _sum: {
            value: true
          }
        }),
        // Average contract duration
        Promise.resolve([{ avg_duration_days: 365.0 }])
      ]),

      // Customer contract analysis
      prisma.contract.groupBy({
        by: ['customerId'],
        where: {
          companyId: session.user.companyId,
          createdAt: { gte: startDate }
        },
        _count: {
          id: true
        },
        _sum: {
          value: true
        },
        orderBy: {
          _sum: {
            value: 'desc'
          }
        },
        take: 10
      }),

      // Value metrics
      Promise.all([
        // Total contract value
        prisma.contract.aggregate({
          where: {
            companyId: session.user.companyId,
            createdAt: { gte: startDate }
          },
          _sum: {
            value: true
          }
        }),
        // Active contract value
        prisma.contract.aggregate({
          where: {
            companyId: session.user.companyId,
            status: 'ACTIVE'
          },
          _sum: {
            value: true
          }
        }),
        // Average contract value
        prisma.contract.aggregate({
          where: {
            companyId: session.user.companyId,
            createdAt: { gte: startDate },
            value: { not: null }
          },
          _avg: {
            value: true
          }
        })
      ]),

      // Expiring contracts (next 30 days)
      prisma.contract.findMany({
        where: {
          companyId: session.user.companyId,
          status: 'ACTIVE',
          endDate: {
            gte: new Date(),
            lte: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
          }
        },
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              company: true,
              email: true
            }
          }
        },
        orderBy: { endDate: 'asc' },
        take: 10
      }),

      // Signature metrics
      Promise.all([
        // Average time to signature (mock data)
        Promise.resolve([{ avg_days_to_signature: 7.5 }]),
        // Signature conversion rate (simplified calculation)
        Promise.resolve([{
          signature_rate: await prisma.contract.count({
            where: { companyId: session.user.companyId, status: { in: ['SIGNED', 'ACTIVE'] } }
          }).then(async (signedCount) => {
            const totalCount = await prisma.contract.count({
              where: { companyId: session.user.companyId, status: { in: ['SENT', 'REVIEW', 'SIGNED', 'ACTIVE'] } }
            })
            return totalCount > 0 ? (signedCount / totalCount) * 100 : 0
          })
        }])
      ])
    ])

    // Get customer details for customer contracts
    const customerIds = customerContracts.map(c => c.customerId).filter(Boolean)
    const customers = await prisma.customer.findMany({
      where: {
        id: { in: customerIds },
        companyId: session.user.companyId
      },
      select: {
        id: true,
        name: true,
        company: true,
        email: true
      }
    })

    // Process customer contracts with details
    const customerContractsWithDetails = customerContracts.map(item => {
      const customer = customers.find(c => c.id === item.customerId)
      return {
        customer: customer || { id: item.customerId, name: 'Unknown', company: null, email: null },
        contractCount: item._count.id,
        totalValue: Number(item._sum.value || 0)
      }
    })

    // Calculate metrics
    const [activeContracts, signedContracts, pendingSignatures, autoRenewalContracts] = contractMetrics
    const [expiring30Days, expiring90Days, autoRenewalValue, avgDurationResult] = renewalAnalysis
    const [totalValue, activeValue, avgValue] = valueMetrics
    const [avgSignatureTime, signatureRate] = signatureMetrics

    const avgDurationDays = avgDurationResult[0]?.avg_duration_days || 0
    const avgSignatureDays = avgSignatureTime[0]?.avg_days_to_signature || 0
    const contractSignatureRate = signatureRate[0]?.signature_rate || 0

    return NextResponse.json({
      summary: {
        totalContracts,
        activeContracts,
        signedContracts,
        pendingSignatures,
        autoRenewalContracts,
        totalValue: Number(totalValue._sum.value || 0),
        activeValue: Number(activeValue._sum.value || 0),
        averageValue: Number(avgValue._avg.value || 0),
        avgDurationDays: Math.round(avgDurationDays),
        avgSignatureDays: Math.round(avgSignatureDays * 100) / 100,
        signatureRate: Math.round(contractSignatureRate * 100) / 100
      },
      contractsByStatus: contractsByStatus.map(item => ({
        status: item.status,
        count: item._count.id,
        value: Number(item._sum.value || 0)
      })),
      contractsByType: contractsByType.map(item => ({
        type: item.type,
        count: item._count.id,
        value: Number(item._sum.value || 0)
      })),
      contractsByMonth,
      renewalAnalysis: {
        expiring30Days,
        expiring90Days,
        autoRenewalValue: Number(autoRenewalValue._sum.value || 0),
        avgDurationDays: Math.round(avgDurationDays)
      },
      topContracts: topContractsByValue.map(c => ({
        id: c.id,
        contractNumber: c.contractNumber,
        title: c.title,
        value: Number(c.value || 0),
        status: c.status,
        type: c.type,
        customer: c.customer,
        createdBy: c.createdBy,
        createdAt: c.createdAt,
        startDate: c.startDate,
        endDate: c.endDate
      })),
      recentContracts: recentContracts.map(c => ({
        id: c.id,
        contractNumber: c.contractNumber,
        title: c.title,
        value: Number(c.value || 0),
        status: c.status,
        type: c.type,
        customer: c.customer,
        createdBy: c.createdBy,
        createdAt: c.createdAt
      })),
      customerContracts: customerContractsWithDetails,
      expiringContracts: expiringContracts.map(c => ({
        id: c.id,
        contractNumber: c.contractNumber,
        title: c.title,
        value: Number(c.value || 0),
        endDate: c.endDate,
        daysUntilExpiry: Math.ceil((new Date(c.endDate!).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)),
        autoRenewal: c.autoRenewal,
        customer: c.customer
      })),
      period: parseInt(period)
    })

  } catch (error) {
    console.error('Error fetching contract analytics:', error)
    return NextResponse.json(
      { error: 'Failed to fetch contract analytics' },
      { status: 500 }
    )
  }
}
