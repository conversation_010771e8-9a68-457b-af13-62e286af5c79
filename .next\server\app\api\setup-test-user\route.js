"use strict";(()=>{var e={};e.id=3469,e.ids=[3469],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6113:e=>{e.exports=require("crypto")},47277:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>h,originalPathname:()=>x,patchFetch:()=>C,requestAsyncStorage:()=>l,routeModule:()=>c,serverHooks:()=>y,staticGenerationAsyncStorage:()=>w,staticGenerationBailout:()=>T});var s={};a.r(s),a.d(s,{POST:()=>d});var r=a(95419),n=a(69108),i=a(99678),o=a(78070),u=a(9108),m=a(6521),p=a.n(m);async function d(e){try{let e=await u._.user.findUnique({where:{email:"<EMAIL>"}});if(e)return o.Z.json({message:"Test user already exists",user:{email:e.email,name:e.name,role:e.role}});let t=await u._.company.create({data:{name:"Test Company",email:"<EMAIL>",phone:"******-0123",address:"123 Test Street",city:"Test City",state:"Test State",country:"Test Country",postalCode:"12345",website:"https://test.com",industry:"Technology",size:"SMALL",status:"ACTIVE",subscriptionStatus:"ACTIVE",subscriptionPlan:"PROFESSIONAL",subscriptionStartDate:new Date,subscriptionEndDate:new Date(Date.now()+31536e6),trialEndDate:new Date(Date.now()+2592e6),maxUsers:10,maxCustomers:1e3,maxQuotations:1e3,maxInvoices:1e3,maxContracts:1e3}}),a=await p().hash("password123",12),s=await u._.user.create({data:{email:"<EMAIL>",password:a,name:"Test Admin",role:"ADMIN",status:"ACTIVE",emailVerified:new Date,companyId:t.id,loginCount:0}});return o.Z.json({message:"Test user created successfully",user:{email:s.email,name:s.name,role:s.role,companyId:s.companyId},company:{id:t.id,name:t.name},credentials:{email:"<EMAIL>",password:"password123"}})}catch(e){return console.error("Error creating test user:",e),o.Z.json({error:"Failed to create test user",details:e},{status:500})}}let c=new r.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/setup-test-user/route",pathname:"/api/setup-test-user",filename:"route",bundlePath:"app/api/setup-test-user/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\setup-test-user\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:l,staticGenerationAsyncStorage:w,serverHooks:y,headerHooks:h,staticGenerationBailout:T}=c,x="/api/setup-test-user/route";function C(){return(0,i.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:w})}},9108:(e,t,a)=>{a.d(t,{_:()=>r});let s=require("@prisma/client"),r=globalThis.prisma??new s.PrismaClient}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[1638,6206,6521],()=>a(47277));module.exports=s})();