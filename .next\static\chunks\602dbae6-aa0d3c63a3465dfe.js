"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3338],{38122:function(e,t,r){r.d(t,{Z5:function(){return n$},_l:function(){return n3},bK:function(){return lR}});var n=r(2265),l=r(54887),i=r(64483),a=r(93046),o=r(68725),d=r(73530),s=r(13428);let u=/[ \t]{2,}/g,c=/^[ \t]*/gm,p=e=>e.replace(u," ").replace(c,"").trim();function f(e,t){}function g(){}function m(e,t,r){let n=t.map(t=>{var n;let l=(n=t.options,{...r,...n});return e.addEventListener(t.eventName,t.fn,l),function(){e.removeEventListener(t.eventName,t.fn,l)}});return function(){n.forEach(e=>{e()})}}f.bind(null,"warn"),f.bind(null,"error");class b extends Error{}function h(e,t){throw new b("Invariant failed")}b.prototype.toString=function(){return this.message};class y extends n.Component{constructor(...e){super(...e),this.callbacks=null,this.unbind=g,this.onWindowError=e=>{let t=this.getCallbacks();t.isDragging()&&t.tryAbort(),e.error instanceof b&&e.preventDefault()},this.getCallbacks=()=>{if(!this.callbacks)throw Error("Unable to find AppCallbacks in <ErrorBoundary/>");return this.callbacks},this.setCallbacks=e=>{this.callbacks=e}}componentDidMount(){this.unbind=m(window,[{eventName:"error",fn:this.onWindowError}])}componentDidCatch(e){if(e instanceof b){this.setState({});return}throw e}componentWillUnmount(){this.unbind()}render(){return this.props.children(this.setCallbacks)}}let I=e=>e+1,v=(e,t)=>{let r=e.droppableId===t.droppableId,n=I(e.index),l=I(t.index);return r?`
      You have moved the item from position ${n}
      to position ${l}
    `:`
    You have moved the item from position ${n}
    in list ${e.droppableId}
    to list ${t.droppableId}
    in position ${l}
  `},x=(e,t,r)=>t.droppableId===r.droppableId?`
      The item ${e}
      has been combined with ${r.draggableId}`:`
      The item ${e}
      in list ${t.droppableId}
      has been combined with ${r.draggableId}
      in list ${r.droppableId}
    `,D=e=>`
  The item has returned to its starting position
  of ${I(e.index)}
`,E={dragHandleUsageInstructions:`
  Press space bar to start a drag.
  When dragging you can use the arrow keys to move the item around and escape to cancel.
  Some screen readers may require you to be in focus mode or to use your pass through key
`,onDragStart:e=>`
  You have lifted an item in position ${I(e.source.index)}
`,onDragUpdate:e=>{let t=e.destination;if(t)return v(e.source,t);let r=e.combine;return r?x(e.draggableId,e.source,r):"You are over an area that cannot be dropped on"},onDragEnd:e=>{if("CANCEL"===e.reason)return`
      Movement cancelled.
      ${D(e.source)}
    `;let t=e.destination,r=e.combine;return t?`
      You have dropped the item.
      ${v(e.source,t)}
    `:r?`
      You have dropped the item.
      ${x(e.draggableId,e.source,r)}
    `:`
    The item has been dropped while not over a drop area.
    ${D(e.source)}
  `}};function A(e,t){if(e.length!==t.length)return!1;for(let l=0;l<e.length;l++){var r,n;if(!((r=e[l])===(n=t[l])||Number.isNaN(r)&&Number.isNaN(n)))return!1}return!0}function N(e,t){let r=(0,n.useState)(()=>({inputs:t,result:e()}))[0],l=(0,n.useRef)(!0),i=(0,n.useRef)(r),a=l.current||t&&i.current.inputs&&A(t,i.current.inputs)?i.current:{inputs:t,result:e()};return(0,n.useEffect)(()=>{l.current=!1,i.current=a},[a]),a.result}function R(e,t){return N(()=>e,t)}let C={x:0,y:0},P=(e,t)=>({x:e.x+t.x,y:e.y+t.y}),w=(e,t)=>({x:e.x-t.x,y:e.y-t.y}),O=(e,t)=>e.x===t.x&&e.y===t.y,S=e=>({x:0!==e.x?-e.x:0,y:0!==e.y?-e.y:0}),B=(e,t,r=0)=>"x"===e?{x:t,y:r}:{x:r,y:t},G=(e,t)=>Math.sqrt((t.x-e.x)**2+(t.y-e.y)**2),L=(e,t)=>Math.min(...t.map(t=>G(e,t))),T=e=>t=>({x:e(t.x),y:e(t.y)});var _=(e,t)=>{let r=(0,o.Dz)({top:Math.max(t.top,e.top),right:Math.min(t.right,e.right),bottom:Math.min(t.bottom,e.bottom),left:Math.max(t.left,e.left)});return r.width<=0||r.height<=0?null:r};let M=(e,t)=>({top:e.top+t.y,left:e.left+t.x,bottom:e.bottom+t.y,right:e.right+t.x}),F=e=>[{x:e.left,y:e.top},{x:e.right,y:e.top},{x:e.left,y:e.bottom},{x:e.right,y:e.bottom}],k=(e,t)=>t?M(e,t.scroll.diff.displacement):e,$=(e,t,r)=>r&&r.increasedBy?{...e,[t.end]:e[t.end]+r.increasedBy[t.line]}:e,W=(e,t)=>t&&t.shouldClipSubject?_(t.pageMarginBox,e):(0,o.Dz)(e);var U=({page:e,withPlaceholder:t,axis:r,frame:n})=>{let l=W($(k(e.marginBox,n),r,t),n);return{page:e,withPlaceholder:t,active:l}},H=(e,t)=>{e.frame||h();let r=e.frame,n=w(t,r.scroll.initial),l=S(n),i={...r,scroll:{initial:r.scroll.initial,current:t,diff:{value:n,displacement:l},max:r.scroll.max}},a=U({page:e.subject.page,withPlaceholder:e.subject.withPlaceholder,axis:e.axis,frame:i});return{...e,frame:i,subject:a}};function V(e,t=A){let r=null;function n(...l){if(r&&r.lastThis===this&&t(l,r.lastArgs))return r.lastResult;let i=e.apply(this,l);return r={lastResult:i,lastArgs:l,lastThis:this},i}return n.clear=function(){r=null},n}let j=V(e=>e.reduce((e,t)=>(e[t.descriptor.id]=t,e),{})),z=V(e=>e.reduce((e,t)=>(e[t.descriptor.id]=t,e),{})),q=V(e=>Object.values(e)),Y=V(e=>Object.values(e));var J=V((e,t)=>Y(t).filter(t=>e===t.descriptor.droppableId).sort((e,t)=>e.descriptor.index-t.descriptor.index));function Z(e){return e.at&&"REORDER"===e.at.type?e.at.destination:null}function X(e){return e.at&&"COMBINE"===e.at.type?e.at.combine:null}var K=V((e,t)=>t.filter(t=>t.descriptor.id!==e.descriptor.id)),Q=({isMovingForward:e,draggable:t,destination:r,insideDestination:n,previousImpact:l})=>{if(!r.isCombineEnabled||!Z(l))return null;function i(e){let t={type:"COMBINE",combine:{draggableId:e,droppableId:r.descriptor.id}};return{...l,at:t}}let a=l.displaced.all,o=a.length?a[0]:null;if(e)return o?i(o):null;let d=K(t,n);if(!o)return d.length?i(d[d.length-1].descriptor.id):null;let s=d.findIndex(e=>e.descriptor.id===o);-1!==s||h();let u=s-1;return u<0?null:i(d[u].descriptor.id)},ee=(e,t)=>e.descriptor.droppableId===t.descriptor.id;let et={point:C,value:0},er={invisible:{},visible:{},all:[]},en={displaced:er,displacedBy:et,at:null};var el=(e,t)=>r=>e<=r&&r<=t,ei=e=>{let t=el(e.top,e.bottom),r=el(e.left,e.right);return n=>{if(t(n.top)&&t(n.bottom)&&r(n.left)&&r(n.right))return!0;let l=t(n.top)||t(n.bottom),i=r(n.left)||r(n.right);if(l&&i)return!0;let a=n.top<e.top&&n.bottom>e.bottom,o=n.left<e.left&&n.right>e.right;return!!a&&!!o||a&&i||o&&l}},ea=e=>{let t=el(e.top,e.bottom),r=el(e.left,e.right);return e=>t(e.top)&&t(e.bottom)&&r(e.left)&&r(e.right)};let eo={direction:"vertical",line:"y",crossAxisLine:"x",start:"top",end:"bottom",size:"height",crossAxisStart:"left",crossAxisEnd:"right",crossAxisSize:"width"},ed={direction:"horizontal",line:"x",crossAxisLine:"y",start:"left",end:"right",size:"width",crossAxisStart:"top",crossAxisEnd:"bottom",crossAxisSize:"height"};var es=e=>t=>{let r=el(t.top,t.bottom),n=el(t.left,t.right);return t=>e===eo?r(t.top)&&r(t.bottom):n(t.left)&&n(t.right)};let eu=(e,t)=>M(e,t.frame?t.frame.scroll.diff.displacement:C),ec=(e,t,r)=>!!t.subject.active&&r(t.subject.active)(e),ep=(e,t,r)=>r(t)(e),ef=({target:e,destination:t,viewport:r,withDroppableDisplacement:n,isVisibleThroughFrameFn:l})=>{let i=n?eu(e,t):e;return ec(i,t,l)&&ep(i,r,l)},eg=e=>ef({...e,isVisibleThroughFrameFn:ei}),em=e=>ef({...e,isVisibleThroughFrameFn:ea}),eb=e=>ef({...e,isVisibleThroughFrameFn:es(e.destination.axis)}),eh=(e,t,r)=>{if("boolean"==typeof r)return r;if(!t)return!0;let{invisible:n,visible:l}=t;if(n[e])return!1;let i=l[e];return!i||i.shouldAnimate};function ey({afterDragging:e,destination:t,displacedBy:r,viewport:n,forceShouldAnimate:l,last:i}){return e.reduce(function(e,a){let d=function(e,t){let r=e.page.marginBox,n={top:t.point.y,right:0,bottom:0,left:t.point.x};return(0,o.Dz)((0,o.jn)(r,n))}(a,r),s=a.descriptor.id;if(e.all.push(s),!eg({target:d,destination:t,viewport:n,withDroppableDisplacement:!0}))return e.invisible[a.descriptor.id]=!0,e;let u=eh(s,i,l);return e.visible[s]={draggableId:s,shouldAnimate:u},e},{all:[],visible:{},invisible:{}})}function eI({insideDestination:e,inHomeList:t,displacedBy:r,destination:n}){let l=function(e,t){if(!e.length)return 0;let r=e[e.length-1].descriptor.index;return t.inHomeList?r:r+1}(e,{inHomeList:t});return{displaced:er,displacedBy:r,at:{type:"REORDER",destination:{droppableId:n.descriptor.id,index:l}}}}function ev({draggable:e,insideDestination:t,destination:r,viewport:n,displacedBy:l,last:i,index:a,forceShouldAnimate:o}){let d=ee(e,r);if(null==a)return eI({insideDestination:t,inHomeList:d,displacedBy:l,destination:r});let s=t.find(e=>e.descriptor.index===a);if(!s)return eI({insideDestination:t,inHomeList:d,displacedBy:l,destination:r});let u=K(e,t),c=t.indexOf(s);return{displaced:ey({afterDragging:u.slice(c),destination:r,displacedBy:l,last:i,viewport:n.frame,forceShouldAnimate:o}),displacedBy:l,at:{type:"REORDER",destination:{droppableId:r.descriptor.id,index:a}}}}function ex(e,t){return!!t.effected[e]}var eD=({isMovingForward:e,destination:t,draggables:r,combine:n,afterCritical:l})=>{if(!t.isCombineEnabled)return null;let i=n.draggableId,a=r[i].descriptor.index;return ex(i,l)?e?a:a-1:e?a+1:a},eE=({isMovingForward:e,isInHomeList:t,insideDestination:r,location:n})=>{if(!r.length)return null;let l=n.index,i=e?l+1:l-1,a=r[0].descriptor.index,o=r[r.length-1].descriptor.index;return i<a||i>(t?o:o+1)?null:i},eA=({isMovingForward:e,isInHomeList:t,draggable:r,draggables:n,destination:l,insideDestination:i,previousImpact:a,viewport:o,afterCritical:d})=>{let s=a.at;if(s||h(),"REORDER"===s.type){let n=eE({isMovingForward:e,isInHomeList:t,location:s.destination,insideDestination:i});return null==n?null:ev({draggable:r,insideDestination:i,destination:l,viewport:o,last:a.displaced,displacedBy:a.displacedBy,index:n})}let u=eD({isMovingForward:e,destination:l,displaced:a.displaced,draggables:n,combine:s.combine,afterCritical:d});return null==u?null:ev({draggable:r,insideDestination:i,destination:l,viewport:o,last:a.displaced,displacedBy:a.displacedBy,index:u})},eN=({displaced:e,afterCritical:t,combineWith:r,displacedBy:n})=>{let l=!!(e.visible[r]||e.invisible[r]);return ex(r,t)?l?C:S(n.point):l?n.point:C},eR=({afterCritical:e,impact:t,draggables:r})=>{let n=X(t);n||h();let l=n.draggableId;return P(r[l].page.borderBox.center,eN({displaced:t.displaced,afterCritical:e,combineWith:l,displacedBy:t.displacedBy}))};let eC=(e,t)=>t.margin[e.start]+t.borderBox[e.size]/2,eP=(e,t)=>t.margin[e.end]+t.borderBox[e.size]/2,ew=(e,t,r)=>t[e.crossAxisStart]+r.margin[e.crossAxisStart]+r.borderBox[e.crossAxisSize]/2,eO=({axis:e,moveRelativeTo:t,isMoving:r})=>B(e.line,t.marginBox[e.end]+eC(e,r),ew(e,t.marginBox,r)),eS=({axis:e,moveRelativeTo:t,isMoving:r})=>B(e.line,t.marginBox[e.start]-eP(e,r),ew(e,t.marginBox,r)),eB=({axis:e,moveInto:t,isMoving:r})=>B(e.line,t.contentBox[e.start]+eC(e,r),ew(e,t.contentBox,r));var eG=({impact:e,draggable:t,draggables:r,droppable:n,afterCritical:l})=>{let i=J(n.descriptor.id,r),a=t.page,d=n.axis;if(!i.length)return eB({axis:d,moveInto:n.page,isMoving:a});let{displaced:s,displacedBy:u}=e,c=s.all[0];if(c){let e=r[c];return ex(c,l)?eS({axis:d,moveRelativeTo:e.page,isMoving:a}):eS({axis:d,moveRelativeTo:(0,o.cv)(e.page,u.point),isMoving:a})}let p=i[i.length-1];return p.descriptor.id===t.descriptor.id?a.borderBox.center:ex(p.descriptor.id,l)?eO({axis:d,moveRelativeTo:(0,o.cv)(p.page,S(l.displacedBy.point)),isMoving:a}):eO({axis:d,moveRelativeTo:p.page,isMoving:a})},eL=(e,t)=>{let r=e.frame;return r?P(t,r.scroll.diff.displacement):t};let eT=({impact:e,draggable:t,droppable:r,draggables:n,afterCritical:l})=>{let i=t.page.borderBox.center,a=e.at;return r&&a?"REORDER"===a.type?eG({impact:e,draggable:t,draggables:n,droppable:r,afterCritical:l}):eR({impact:e,draggables:n,afterCritical:l}):i};var e_=e=>{let t=eT(e),r=e.droppable;return r?eL(r,t):t},eM=(e,t)=>{let r=w(t,e.scroll.initial),n=S(r);return{frame:(0,o.Dz)({top:t.y,bottom:t.y+e.frame.height,left:t.x,right:t.x+e.frame.width}),scroll:{initial:e.scroll.initial,max:e.scroll.max,current:t,diff:{value:r,displacement:n}}}};function eF(e,t){return e.map(e=>t[e])}var ek=({impact:e,viewport:t,destination:r,draggables:n,maxScrollChange:l})=>{let i=eM(t,P(t.scroll.current,l)),a=r.frame?H(r,P(r.frame.scroll.current,l)):r,o=e.displaced,d=ey({afterDragging:eF(o.all,n),destination:r,displacedBy:e.displacedBy,viewport:i.frame,last:o,forceShouldAnimate:!1}),s=ey({afterDragging:eF(o.all,n),destination:a,displacedBy:e.displacedBy,viewport:t.frame,last:o,forceShouldAnimate:!1}),u={},c={},p=[o,d,s];return o.all.forEach(e=>{let t=function(e,t){for(let r=0;r<t.length;r++){let n=t[r].visible[e];if(n)return n}return null}(e,p);if(t){c[e]=t;return}u[e]=!0}),{...e,displaced:{all:o.all,invisible:u,visible:c}}},e$=(e,t)=>P(e.scroll.diff.displacement,t),eW=({pageBorderBoxCenter:e,draggable:t,viewport:r})=>{let n=w(e$(r,e),t.page.borderBox.center);return P(t.client.borderBox.center,n)},eU=({draggable:e,destination:t,newPageBorderBoxCenter:r,viewport:n,withDroppableDisplacement:l,onlyOnMainAxis:i=!1})=>{let a=w(r,e.page.borderBox.center),o={target:M(e.page.borderBox,a),destination:t,withDroppableDisplacement:l,viewport:n};return i?eb(o):em(o)},eH=({isMovingForward:e,draggable:t,destination:r,draggables:n,previousImpact:l,viewport:i,previousPageBorderBoxCenter:a,previousClientSelection:o,afterCritical:d})=>{if(!r.isEnabled)return null;let s=J(r.descriptor.id,n),u=ee(t,r),c=Q({isMovingForward:e,draggable:t,destination:r,insideDestination:s,previousImpact:l})||eA({isMovingForward:e,isInHomeList:u,draggable:t,draggables:n,destination:r,insideDestination:s,previousImpact:l,viewport:i,afterCritical:d});if(!c)return null;let p=e_({impact:c,draggable:t,droppable:r,draggables:n,afterCritical:d});if(eU({draggable:t,destination:r,newPageBorderBoxCenter:p,viewport:i.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0}))return{clientSelection:eW({pageBorderBoxCenter:p,draggable:t,viewport:i}),impact:c,scrollJumpRequest:null};let f=w(p,a);return{clientSelection:o,impact:ek({impact:c,viewport:i,destination:r,draggables:n,maxScrollChange:f}),scrollJumpRequest:f}};let eV=e=>{let t=e.subject.active;return t||h(),t};var ej=({isMovingForward:e,pageBorderBoxCenter:t,source:r,droppables:n,viewport:l})=>{let i=r.subject.active;if(!i)return null;let a=r.axis,o=el(i[a.start],i[a.end]),d=q(n).filter(e=>e!==r).filter(e=>e.isEnabled).filter(e=>!!e.subject.active).filter(e=>ei(l.frame)(eV(e))).filter(t=>{let r=eV(t);return e?i[a.crossAxisEnd]<r[a.crossAxisEnd]:r[a.crossAxisStart]<i[a.crossAxisStart]}).filter(e=>{let t=eV(e),r=el(t[a.start],t[a.end]);return o(t[a.start])||o(t[a.end])||r(i[a.start])||r(i[a.end])}).sort((t,r)=>{let n=eV(t)[a.crossAxisStart],l=eV(r)[a.crossAxisStart];return e?n-l:l-n}).filter((e,t,r)=>eV(e)[a.crossAxisStart]===eV(r[0])[a.crossAxisStart]);if(!d.length)return null;if(1===d.length)return d[0];let s=d.filter(e=>el(eV(e)[a.start],eV(e)[a.end])(t[a.line]));return 1===s.length?s[0]:s.length>1?s.sort((e,t)=>eV(e)[a.start]-eV(t)[a.start])[0]:d.sort((e,r)=>{let n=L(t,F(eV(e))),l=L(t,F(eV(r)));return n!==l?n-l:eV(e)[a.start]-eV(r)[a.start]})[0]};let ez=(e,t)=>{let r=e.page.borderBox.center;return ex(e.descriptor.id,t)?w(r,t.displacedBy.point):r},eq=(e,t)=>{let r=e.page.borderBox;return ex(e.descriptor.id,t)?M(r,S(t.displacedBy.point)):r};var eY=({pageBorderBoxCenter:e,viewport:t,destination:r,insideDestination:n,afterCritical:l})=>n.filter(e=>em({target:eq(e,l),destination:r,viewport:t.frame,withDroppableDisplacement:!0})).sort((t,n)=>{let i=G(e,eL(r,ez(t,l))),a=G(e,eL(r,ez(n,l)));return i<a?-1:a<i?1:t.descriptor.index-n.descriptor.index})[0]||null,eJ=V(function(e,t){let r=t[e.line];return{value:r,point:B(e.line,r)}});let eZ=(e,t,r)=>{let n=e.axis;if("virtual"===e.descriptor.mode)return B(n.line,t[n.line]);let l=e.subject.page.contentBox[n.size],i=J(e.descriptor.id,r).reduce((e,t)=>e+t.client.marginBox[n.size],0)+t[n.line]-l;return i<=0?null:B(n.line,i)},eX=(e,t)=>({...e,scroll:{...e.scroll,max:t}}),eK=(e,t,r)=>{let n=e.frame;ee(t,e)&&h(),e.subject.withPlaceholder&&h();let l=eJ(e.axis,t.displaceBy).point,i=eZ(e,l,r),a={placeholderSize:l,increasedBy:i,oldFrameMaxScroll:e.frame?e.frame.scroll.max:null};if(!n){let t=U({page:e.subject.page,withPlaceholder:a,axis:e.axis,frame:e.frame});return{...e,subject:t}}let o=i?P(n.scroll.max,i):n.scroll.max,d=eX(n,o),s=U({page:e.subject.page,withPlaceholder:a,axis:e.axis,frame:d});return{...e,subject:s,frame:d}},eQ=e=>{let t=e.subject.withPlaceholder;t||h();let r=e.frame;if(!r){let t=U({page:e.subject.page,axis:e.axis,frame:null,withPlaceholder:null});return{...e,subject:t}}let n=t.oldFrameMaxScroll;n||h();let l=eX(r,n),i=U({page:e.subject.page,axis:e.axis,frame:l,withPlaceholder:null});return{...e,subject:i,frame:l}};var e0=({previousPageBorderBoxCenter:e,moveRelativeTo:t,insideDestination:r,draggable:n,draggables:l,destination:i,viewport:a,afterCritical:o})=>{if(!t){if(r.length)return null;let e={displaced:er,displacedBy:et,at:{type:"REORDER",destination:{droppableId:i.descriptor.id,index:0}}},t=e_({impact:e,draggable:n,droppable:i,draggables:l,afterCritical:o}),d=ee(n,i)?i:eK(i,n,l);return eU({draggable:n,destination:d,newPageBorderBoxCenter:t,viewport:a.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0})?e:null}let d=e[i.axis.line]<=t.page.borderBox.center[i.axis.line],s=(()=>{let e=t.descriptor.index;return t.descriptor.id===n.descriptor.id||d?e:e+1})(),u=eJ(i.axis,n.displaceBy);return ev({draggable:n,insideDestination:r,destination:i,viewport:a,displacedBy:u,last:er,index:s})},e1=({isMovingForward:e,previousPageBorderBoxCenter:t,draggable:r,isOver:n,draggables:l,droppables:i,viewport:a,afterCritical:o})=>{let d=ej({isMovingForward:e,pageBorderBoxCenter:t,source:n,droppables:i,viewport:a});if(!d)return null;let s=J(d.descriptor.id,l),u=eY({pageBorderBoxCenter:t,viewport:a,destination:d,insideDestination:s,afterCritical:o}),c=e0({previousPageBorderBoxCenter:t,destination:d,draggable:r,draggables:l,moveRelativeTo:u,insideDestination:s,viewport:a,afterCritical:o});return c?{clientSelection:eW({pageBorderBoxCenter:e_({impact:c,draggable:r,droppable:d,draggables:l,afterCritical:o}),draggable:r,viewport:a}),impact:c,scrollJumpRequest:null}:null},e2=e=>{let t=e.at;return t?"REORDER"===t.type?t.destination.droppableId:t.combine.droppableId:null};let e3=(e,t)=>{let r=e2(e);return r?t[r]:null};var e5=({state:e,type:t})=>{let r=e3(e.impact,e.dimensions.droppables),n=!!r,l=e.dimensions.droppables[e.critical.droppable.id],i=r||l,a=i.axis.direction,o="vertical"===a&&("MOVE_UP"===t||"MOVE_DOWN"===t)||"horizontal"===a&&("MOVE_LEFT"===t||"MOVE_RIGHT"===t);if(o&&!n)return null;let d="MOVE_DOWN"===t||"MOVE_RIGHT"===t,s=e.dimensions.draggables[e.critical.draggable.id],u=e.current.page.borderBoxCenter,{draggables:c,droppables:p}=e.dimensions;return o?eH({isMovingForward:d,previousPageBorderBoxCenter:u,draggable:s,destination:i,draggables:c,viewport:e.viewport,previousClientSelection:e.current.client.selection,previousImpact:e.impact,afterCritical:e.afterCritical}):e1({isMovingForward:d,previousPageBorderBoxCenter:u,draggable:s,isOver:i,draggables:c,droppables:p,viewport:e.viewport,afterCritical:e.afterCritical})};function e4(e){return"DRAGGING"===e.phase||"COLLECTING"===e.phase}function e7(e){let t=el(e.top,e.bottom),r=el(e.left,e.right);return function(e){return t(e.y)&&r(e.x)}}let e8=(e,t)=>(0,o.Dz)(M(e,t));var e6=(e,t)=>{let r=e.frame;return r?e8(t,r.scroll.diff.value):t};function e9({displaced:e,id:t}){return!!(e.visible[t]||e.invisible[t])}var te=({pageBorderBoxWithDroppableScroll:e,draggable:t,destination:r,insideDestination:n,last:l,viewport:i,afterCritical:a})=>{let o=r.axis,d=eJ(r.axis,t.displaceBy),s=d.value,u=e[o.start],c=e[o.end],p=K(t,n).find(e=>{let t=e.descriptor.id,r=e.page.borderBox.center[o.line],n=ex(t,a),i=e9({displaced:l,id:t});return n?i?c<=r:u<r-s:i?c<=r+s:u<r})||null,f=function({draggable:e,closest:t,inHomeList:r}){return t?r&&t.descriptor.index>e.descriptor.index?t.descriptor.index-1:t.descriptor.index:null}({draggable:t,closest:p,inHomeList:ee(t,r)});return ev({draggable:t,insideDestination:n,destination:r,viewport:i,last:l,displacedBy:d,index:f})},tt=({draggable:e,pageBorderBoxWithDroppableScroll:t,previousImpact:r,destination:n,insideDestination:l,afterCritical:i})=>{if(!n.isCombineEnabled)return null;let a=n.axis,o=eJ(n.axis,e.displaceBy),d=o.value,s=t[a.start],u=t[a.end],c=K(e,l).find(e=>{let t=e.descriptor.id,n=e.page.borderBox,l=n[a.size]/4,o=ex(t,i),c=e9({displaced:r.displaced,id:t});return o?c?u>n[a.start]+l&&u<n[a.end]-l:s>n[a.start]-d+l&&s<n[a.end]-d-l:c?u>n[a.start]+d+l&&u<n[a.end]+d-l:s>n[a.start]+l&&s<n[a.end]-l});return c?{displacedBy:o,displaced:r.displaced,at:{type:"COMBINE",combine:{draggableId:c.descriptor.id,droppableId:n.descriptor.id}}}:null},tr=({pageOffset:e,draggable:t,draggables:r,droppables:n,previousImpact:l,viewport:i,afterCritical:a})=>{let o=e8(t.page.borderBox,e),d=function({pageBorderBox:e,draggable:t,droppables:r}){let n=q(r).filter(t=>{if(!t.isEnabled)return!1;let r=t.subject.active;if(!r||!(e.left<r.right)||!(e.right>r.left)||!(e.top<r.bottom)||!(e.bottom>r.top))return!1;if(e7(r)(e.center))return!0;let n=t.axis,l=r.center[n.crossAxisLine],i=e[n.crossAxisStart],a=e[n.crossAxisEnd],o=el(r[n.crossAxisStart],r[n.crossAxisEnd]),d=o(i),s=o(a);return!d&&!s||(d?i<l:a>l)});return n.length?1===n.length?n[0].descriptor.id:function({pageBorderBox:e,draggable:t,candidates:r}){let n=t.page.borderBox.center,l=r.map(t=>{let r=t.axis,l=B(t.axis.line,e.center[r.line],t.page.borderBox.center[r.crossAxisLine]);return{id:t.descriptor.id,distance:G(n,l)}}).sort((e,t)=>t.distance-e.distance);return l[0]?l[0].id:null}({pageBorderBox:e,draggable:t,candidates:n}):null}({pageBorderBox:o,draggable:t,droppables:n});if(!d)return en;let s=n[d],u=J(s.descriptor.id,r),c=e6(s,o);return tt({pageBorderBoxWithDroppableScroll:c,draggable:t,previousImpact:l,destination:s,insideDestination:u,afterCritical:a})||te({pageBorderBoxWithDroppableScroll:c,draggable:t,destination:s,insideDestination:u,last:l.displaced,viewport:i,afterCritical:a})},tn=(e,t)=>({...e,[t.descriptor.id]:t});let tl=({previousImpact:e,impact:t,droppables:r})=>{let n=e2(e),l=e2(t);if(!n||n===l)return r;let i=r[n];return i.subject.withPlaceholder?tn(r,eQ(i)):r};var ti=({draggable:e,draggables:t,droppables:r,previousImpact:n,impact:l})=>{let i=tl({previousImpact:n,impact:l,droppables:r}),a=e2(l);if(!a)return i;let o=r[a];return ee(e,o)||o.subject.withPlaceholder?i:tn(i,eK(o,e,t))},ta=({state:e,clientSelection:t,dimensions:r,viewport:n,impact:l,scrollJumpRequest:i})=>{let a=n||e.viewport,o=r||e.dimensions,d=t||e.current.client.selection,s=w(d,e.initial.client.selection),u={offset:s,selection:d,borderBoxCenter:P(e.initial.client.borderBoxCenter,s)},c={selection:P(u.selection,a.scroll.current),borderBoxCenter:P(u.borderBoxCenter,a.scroll.current),offset:P(u.offset,a.scroll.diff.value)},p={client:u,page:c};if("COLLECTING"===e.phase)return{...e,dimensions:o,viewport:a,current:p};let f=o.draggables[e.critical.draggable.id],g=l||tr({pageOffset:c.offset,draggable:f,draggables:o.draggables,droppables:o.droppables,previousImpact:e.impact,viewport:a,afterCritical:e.afterCritical}),m=ti({draggable:f,impact:g,previousImpact:e.impact,draggables:o.draggables,droppables:o.droppables});return{...e,current:p,dimensions:{draggables:o.draggables,droppables:m},impact:g,viewport:a,scrollJumpRequest:i||null,forceShouldAnimate:!i&&null}},to=({impact:e,viewport:t,draggables:r,destination:n,forceShouldAnimate:l})=>{let i=e.displaced,a=ey({afterDragging:i.all.map(e=>r[e]),destination:n,displacedBy:e.displacedBy,viewport:t.frame,forceShouldAnimate:l,last:i});return{...e,displaced:a}},td=({impact:e,draggable:t,droppable:r,draggables:n,viewport:l,afterCritical:i})=>eW({pageBorderBoxCenter:e_({impact:e,draggable:t,draggables:n,droppable:r,afterCritical:i}),draggable:t,viewport:l}),ts=({state:e,dimensions:t,viewport:r})=>{"SNAP"!==e.movementMode&&h();let n=e.impact,l=r||e.viewport,i=t||e.dimensions,{draggables:a,droppables:o}=i,d=a[e.critical.draggable.id],s=e2(n);s||h();let u=o[s],c=to({impact:n,viewport:l,destination:u,draggables:a}),p=td({impact:c,draggable:d,droppable:u,draggables:a,viewport:l,afterCritical:e.afterCritical});return ta({impact:c,clientSelection:p,state:e,dimensions:i,viewport:l})},tu=e=>({index:e.index,droppableId:e.droppableId}),tc=({draggable:e,home:t,draggables:r,viewport:n})=>{let l=eJ(t.axis,e.displaceBy),i=J(t.descriptor.id,r),a=i.indexOf(e);-1!==a||h();let o=i.slice(a+1),d=o.reduce((e,t)=>(e[t.descriptor.id]=!0,e),{}),s={inVirtualList:"virtual"===t.descriptor.mode,displacedBy:l,effected:d};return{impact:{displaced:ey({afterDragging:o,destination:t,displacedBy:l,last:null,viewport:n.frame,forceShouldAnimate:!1}),displacedBy:l,at:{type:"REORDER",destination:tu(e.descriptor)}},afterCritical:s}},tp=(e,t)=>({draggables:e.draggables,droppables:tn(e.droppables,t)});let tf=e=>{},tg=e=>{};var tm=({draggable:e,offset:t,initialWindowScroll:r})=>{let n=(0,o.cv)(e.client,t),l=(0,o.oc)(n,r);return{...e,placeholder:{...e.placeholder,client:n},client:n,page:l}},tb=e=>{let t=e.frame;return t||h(),t},th=({additions:e,updatedDroppables:t,viewport:r})=>{let n=r.scroll.diff.value;return e.map(e=>{let l=P(n,tb(t[e.descriptor.droppableId]).scroll.diff.value);return tm({draggable:e,offset:l,initialWindowScroll:r.scroll.initial})})},ty=({state:e,published:t})=>{tf();let r=t.modified.map(t=>H(e.dimensions.droppables[t.droppableId],t.scroll)),n={...e.dimensions.droppables,...j(r)},l=z(th({additions:t.additions,updatedDroppables:n,viewport:e.viewport})),i={...e.dimensions.draggables,...l};t.removals.forEach(e=>{delete i[e]});let a={droppables:n,draggables:i},o=e2(e.impact),d=o?a.droppables[o]:null,{impact:s,afterCritical:u}=tc({draggable:a.draggables[e.critical.draggable.id],home:a.droppables[e.critical.droppable.id],draggables:i,viewport:e.viewport}),c=d&&d.isCombineEnabled?e.impact:s,p=tr({pageOffset:e.current.page.offset,draggable:a.draggables[e.critical.draggable.id],draggables:a.draggables,droppables:a.droppables,previousImpact:c,viewport:e.viewport,afterCritical:u});tg();let f={...e,phase:"DRAGGING",impact:p,onLiftImpact:s,dimensions:a,afterCritical:u,forceShouldAnimate:!1};return"COLLECTING"===e.phase?f:{...f,phase:"DROP_PENDING",reason:e.reason,isWaiting:!1}};let tI=e=>"SNAP"===e.movementMode,tv=(e,t,r)=>{let n=tp(e.dimensions,t);return!tI(e)||r?ta({state:e,dimensions:n}):ts({state:e,dimensions:n})};function tx(e){return e.isDragging&&"SNAP"===e.movementMode?{...e,scrollJumpRequest:null}:e}let tD={phase:"IDLE",completed:null,shouldFlush:!1};var tE=(e=tD,t)=>{if("FLUSH"===t.type)return{...tD,shouldFlush:!0};if("INITIAL_PUBLISH"===t.type){"IDLE"!==e.phase&&h();let{critical:r,clientSelection:n,viewport:l,dimensions:i,movementMode:a}=t.payload,o=i.draggables[r.draggable.id],d=i.droppables[r.droppable.id],s={selection:n,borderBoxCenter:o.client.borderBox.center,offset:C},u={client:s,page:{selection:P(s.selection,l.scroll.initial),borderBoxCenter:P(s.selection,l.scroll.initial),offset:P(s.selection,l.scroll.diff.value)}},c=q(i.droppables).every(e=>!e.isFixedOnPage),{impact:p,afterCritical:f}=tc({draggable:o,home:d,draggables:i.draggables,viewport:l});return{phase:"DRAGGING",isDragging:!0,critical:r,movementMode:a,dimensions:i,initial:u,current:u,isWindowScrollAllowed:c,impact:p,afterCritical:f,onLiftImpact:p,viewport:l,scrollJumpRequest:null,forceShouldAnimate:null}}if("COLLECTION_STARTING"===t.type)return"COLLECTING"===e.phase||"DROP_PENDING"===e.phase?e:("DRAGGING"!==e.phase&&h(),{...e,phase:"COLLECTING"});if("PUBLISH_WHILE_DRAGGING"===t.type)return"COLLECTING"===e.phase||"DROP_PENDING"===e.phase||h(),ty({state:e,published:t.payload});if("MOVE"===t.type){if("DROP_PENDING"===e.phase)return e;e4(e)||h();let{client:r}=t.payload;return O(r,e.current.client.selection)?e:ta({state:e,clientSelection:r,impact:tI(e)?e.impact:null})}if("UPDATE_DROPPABLE_SCROLL"===t.type){if("DROP_PENDING"===e.phase||"COLLECTING"===e.phase)return tx(e);e4(e)||h();let{id:r,newScroll:n}=t.payload,l=e.dimensions.droppables[r];return l?tv(e,H(l,n),!1):e}if("UPDATE_DROPPABLE_IS_ENABLED"===t.type){if("DROP_PENDING"===e.phase)return e;e4(e)||h();let{id:r,isEnabled:n}=t.payload,l=e.dimensions.droppables[r];return l||h(),l.isEnabled!==n||h(),tv(e,{...l,isEnabled:n},!0)}if("UPDATE_DROPPABLE_IS_COMBINE_ENABLED"===t.type){if("DROP_PENDING"===e.phase)return e;e4(e)||h();let{id:r,isCombineEnabled:n}=t.payload,l=e.dimensions.droppables[r];return l||h(),l.isCombineEnabled!==n||h(),tv(e,{...l,isCombineEnabled:n},!0)}if("MOVE_BY_WINDOW_SCROLL"===t.type){if("DROP_PENDING"===e.phase||"DROP_ANIMATING"===e.phase)return e;e4(e)||h(),e.isWindowScrollAllowed||h();let r=t.payload.newScroll;if(O(e.viewport.scroll.current,r))return tx(e);let n=eM(e.viewport,r);return tI(e)?ts({state:e,viewport:n}):ta({state:e,viewport:n})}if("UPDATE_VIEWPORT_MAX_SCROLL"===t.type){if(!e4(e))return e;let r=t.payload.maxScroll;if(O(r,e.viewport.scroll.max))return e;let n={...e.viewport,scroll:{...e.viewport.scroll,max:r}};return{...e,viewport:n}}if("MOVE_UP"===t.type||"MOVE_DOWN"===t.type||"MOVE_LEFT"===t.type||"MOVE_RIGHT"===t.type){if("COLLECTING"===e.phase||"DROP_PENDING"===e.phase)return e;"DRAGGING"!==e.phase&&h();let r=e5({state:e,type:t.type});return r?ta({state:e,impact:r.impact,clientSelection:r.clientSelection,scrollJumpRequest:r.scrollJumpRequest}):e}if("DROP_PENDING"===t.type){let r=t.payload.reason;return"COLLECTING"!==e.phase&&h(),{...e,phase:"DROP_PENDING",isWaiting:!0,reason:r}}if("DROP_ANIMATE"===t.type){let{completed:r,dropDuration:n,newHomeClientOffset:l}=t.payload;return"DRAGGING"===e.phase||"DROP_PENDING"===e.phase||h(),{phase:"DROP_ANIMATING",completed:r,dropDuration:n,newHomeClientOffset:l,dimensions:e.dimensions}}if("DROP_COMPLETE"===t.type){let{completed:e}=t.payload;return{phase:"IDLE",completed:e,shouldFlush:!1}}return e};function tA(e,t){return e instanceof Object&&"type"in e&&e.type===t}let tN=e=>({type:"BEFORE_INITIAL_CAPTURE",payload:e}),tR=e=>({type:"LIFT",payload:e}),tC=e=>({type:"INITIAL_PUBLISH",payload:e}),tP=e=>({type:"PUBLISH_WHILE_DRAGGING",payload:e}),tw=()=>({type:"COLLECTION_STARTING",payload:null}),tO=e=>({type:"UPDATE_DROPPABLE_SCROLL",payload:e}),tS=e=>({type:"UPDATE_DROPPABLE_IS_ENABLED",payload:e}),tB=e=>({type:"UPDATE_DROPPABLE_IS_COMBINE_ENABLED",payload:e}),tG=e=>({type:"MOVE",payload:e}),tL=e=>({type:"MOVE_BY_WINDOW_SCROLL",payload:e}),tT=()=>({type:"MOVE_UP",payload:null}),t_=()=>({type:"MOVE_DOWN",payload:null}),tM=()=>({type:"MOVE_RIGHT",payload:null}),tF=()=>({type:"MOVE_LEFT",payload:null}),tk=()=>({type:"FLUSH",payload:null}),t$=e=>({type:"DROP_ANIMATE",payload:e}),tW=e=>({type:"DROP_COMPLETE",payload:e}),tU=e=>({type:"DROP",payload:e}),tH=e=>({type:"DROP_PENDING",payload:e}),tV=()=>({type:"DROP_ANIMATION_FINISHED",payload:null});var tj=e=>({getState:t,dispatch:r})=>n=>l=>{if(!tA(l,"LIFT")){n(l);return}let{id:i,clientSelection:a,movementMode:o}=l.payload,d=t();"DROP_ANIMATING"===d.phase&&r(tW({completed:d.completed})),"IDLE"!==t().phase&&h(),r(tk()),r(tN({draggableId:i,movementMode:o}));let{critical:s,dimensions:u,viewport:c}=e.startPublishing({draggableId:i,scrollOptions:{shouldPublishImmediately:"SNAP"===o}});r(tC({critical:s,dimensions:u,clientSelection:a,movementMode:o,viewport:c}))},tz=e=>()=>t=>r=>{tA(r,"INITIAL_PUBLISH")&&e.dragging(),tA(r,"DROP_ANIMATE")&&e.dropping(r.payload.completed.result.reason),(tA(r,"FLUSH")||tA(r,"DROP_COMPLETE"))&&e.resting(),t(r)};let tq={outOfTheWay:"cubic-bezier(0.2, 0, 0, 1)",drop:"cubic-bezier(.2,1,.1,1)"},tY={opacity:{drop:0,combining:.7},scale:{drop:.75}},tJ={outOfTheWay:.2,minDropTime:.33,maxDropTime:.55},tZ=`${tJ.outOfTheWay}s ${tq.outOfTheWay}`,tX={fluid:`opacity ${tZ}`,snap:`transform ${tZ}, opacity ${tZ}`,drop:e=>{let t=`${e}s ${tq.drop}`;return`transform ${t}, opacity ${t}`},outOfTheWay:`transform ${tZ}`,placeholder:`height ${tZ}, width ${tZ}, margin ${tZ}`},tK=e=>O(e,C)?void 0:`translate(${e.x}px, ${e.y}px)`,tQ={moveTo:tK,drop:(e,t)=>{let r=tK(e);return r?t?`${r} scale(${tY.scale.drop})`:r:void 0}},{minDropTime:t0,maxDropTime:t1}=tJ,t2=t1-t0;var t3=({current:e,destination:t,reason:r})=>{let n=G(e,t);if(n<=0)return t0;if(n>=1500)return t1;let l=t0+n/1500*t2;return Number(("CANCEL"===r?.6*l:l).toFixed(2))},t5=({impact:e,draggable:t,dimensions:r,viewport:n,afterCritical:l})=>{let{draggables:i,droppables:a}=r,o=e2(e),d=o?a[o]:null,s=a[t.descriptor.droppableId];return w(td({impact:e,draggable:t,draggables:i,afterCritical:l,droppable:d||s,viewport:n}),t.client.borderBox.center)},t4=({draggables:e,reason:t,lastImpact:r,home:n,viewport:l,onLiftImpact:i})=>r.at&&"DROP"===t?"REORDER"===r.at.type?{impact:r,didDropInsideDroppable:!0}:{impact:{...r,displaced:er},didDropInsideDroppable:!0}:{impact:to({draggables:e,impact:i,destination:n,viewport:l,forceShouldAnimate:!0}),didDropInsideDroppable:!1};let t7=({getState:e,dispatch:t})=>r=>n=>{if(!tA(n,"DROP")){r(n);return}let l=e(),i=n.payload.reason;if("COLLECTING"===l.phase){t(tH({reason:i}));return}if("IDLE"===l.phase)return;"DROP_PENDING"===l.phase&&l.isWaiting&&h(),"DRAGGING"===l.phase||"DROP_PENDING"===l.phase||h();let a=l.critical,o=l.dimensions,d=o.draggables[l.critical.draggable.id],{impact:s,didDropInsideDroppable:u}=t4({reason:i,lastImpact:l.impact,afterCritical:l.afterCritical,onLiftImpact:l.onLiftImpact,home:l.dimensions.droppables[l.critical.droppable.id],viewport:l.viewport,draggables:l.dimensions.draggables}),c=u?Z(s):null,p=u?X(s):null,f={index:a.draggable.index,droppableId:a.droppable.id},g={draggableId:d.descriptor.id,type:d.descriptor.type,source:f,reason:i,mode:l.movementMode,destination:c,combine:p},m=t5({impact:s,draggable:d,dimensions:o,viewport:l.viewport,afterCritical:l.afterCritical}),b={critical:l.critical,afterCritical:l.afterCritical,result:g,impact:s};if(!(!O(l.current.client.offset,m)||g.combine)){t(tW({completed:b}));return}let y=t3({current:l.current.client.offset,destination:m,reason:i});t(t$({newHomeClientOffset:m,dropDuration:y,completed:b}))};var t8=()=>({x:window.pageXOffset,y:window.pageYOffset});let t6=e=>tA(e,"DROP_COMPLETE")||tA(e,"DROP_ANIMATE")||tA(e,"FLUSH"),t9=e=>{let t=function({onWindowScroll:e}){let t=(0,d.Z)(function(){e(t8())}),r={eventName:"scroll",options:{passive:!0,capture:!1},fn:e=>{(e.target===window||e.target===window.document)&&t()}},n=g;function l(){return n!==g}return{start:function(){l()&&h(),n=m(window,[r])},stop:function(){l()||h(),t.cancel(),n(),n=g},isActive:l}}({onWindowScroll:t=>{e.dispatch(tL({newScroll:t}))}});return e=>r=>{!t.isActive()&&tA(r,"INITIAL_PUBLISH")&&t.start(),t.isActive()&&t6(r)&&t.stop(),e(r)}};var re=e=>{let t=!1,r=!1,n=setTimeout(()=>{r=!0}),l=l=>{t||r||(t=!0,e(l),clearTimeout(n))};return l.wasCalled=()=>t,l},rt=()=>{let e=[],t=t=>{let r=e.findIndex(e=>e.timerId===t);-1!==r||h();let[n]=e.splice(r,1);n.callback()};return{add:r=>{let n=setTimeout(()=>t(n));e.push({timerId:n,callback:r})},flush:()=>{if(!e.length)return;let t=[...e];e.length=0,t.forEach(e=>{clearTimeout(e.timerId),e.callback()})}}};let rr=(e,t)=>null==e&&null==t||null!=e&&null!=t&&e.droppableId===t.droppableId&&e.index===t.index,rn=(e,t)=>null==e&&null==t||null!=e&&null!=t&&e.draggableId===t.draggableId&&e.droppableId===t.droppableId,rl=(e,t)=>{if(e===t)return!0;let r=e.draggable.id===t.draggable.id&&e.draggable.droppableId===t.draggable.droppableId&&e.draggable.type===t.draggable.type&&e.draggable.index===t.draggable.index,n=e.droppable.id===t.droppable.id&&e.droppable.type===t.droppable.type;return r&&n},ri=(e,t)=>{tf(),t(),tg()},ra=(e,t)=>({draggableId:e.draggable.id,type:e.droppable.type,source:{droppableId:e.droppable.id,index:e.draggable.index},mode:t});function ro(e,t,r,n){if(!e){r(n(t));return}let l=re(r);e(t,{announce:l}),l.wasCalled()||r(n(t))}var rd=(e,t)=>{let r=rt(),n=null,l=r=>{n||h(),n=null,ri("onDragEnd",()=>ro(e().onDragEnd,r,t,E.onDragEnd))};return{beforeCapture:(t,r)=>{n&&h(),ri("onBeforeCapture",()=>{let n=e().onBeforeCapture;n&&n({draggableId:t,mode:r})})},beforeStart:(t,r)=>{n&&h(),ri("onBeforeDragStart",()=>{let n=e().onBeforeDragStart;n&&n(ra(t,r))})},start:(l,i)=>{n&&h();let a=ra(l,i);n={mode:i,lastCritical:l,lastLocation:a.source,lastCombine:null},r.add(()=>{ri("onDragStart",()=>ro(e().onDragStart,a,t,E.onDragStart))})},update:(l,i)=>{let a=Z(i),o=X(i);n||h();let d=!rl(l,n.lastCritical);d&&(n.lastCritical=l);let s=!rr(n.lastLocation,a);s&&(n.lastLocation=a);let u=!rn(n.lastCombine,o);if(u&&(n.lastCombine=o),!d&&!s&&!u)return;let c={...ra(l,n.mode),combine:o,destination:a};r.add(()=>{ri("onDragUpdate",()=>ro(e().onDragUpdate,c,t,E.onDragUpdate))})},flush:()=>{n||h(),r.flush()},drop:l,abort:()=>{n&&l({...ra(n.lastCritical,n.mode),combine:null,destination:null,reason:"CANCEL"})}}},rs=(e,t)=>{let r=rd(e,t);return e=>t=>n=>{if(tA(n,"BEFORE_INITIAL_CAPTURE")){r.beforeCapture(n.payload.draggableId,n.payload.movementMode);return}if(tA(n,"INITIAL_PUBLISH")){let e=n.payload.critical;r.beforeStart(e,n.payload.movementMode),t(n),r.start(e,n.payload.movementMode);return}if(tA(n,"DROP_COMPLETE")){let e=n.payload.completed.result;r.flush(),t(n),r.drop(e);return}if(t(n),tA(n,"FLUSH")){r.abort();return}let l=e.getState();"DRAGGING"===l.phase&&r.update(l.critical,l.impact)}};let ru=e=>t=>r=>{if(!tA(r,"DROP_ANIMATION_FINISHED")){t(r);return}let n=e.getState();"DROP_ANIMATING"!==n.phase&&h(),e.dispatch(tW({completed:n.completed}))},rc=e=>{let t=null,r=null;return n=>l=>{if((tA(l,"FLUSH")||tA(l,"DROP_COMPLETE")||tA(l,"DROP_ANIMATION_FINISHED"))&&(r&&(cancelAnimationFrame(r),r=null),t&&(t(),t=null)),n(l),!tA(l,"DROP_ANIMATE"))return;let i={eventName:"scroll",options:{capture:!0,passive:!1,once:!0},fn:function(){"DROP_ANIMATING"===e.getState().phase&&e.dispatch(tV())}};r=requestAnimationFrame(()=>{r=null,t=m(window,[i])})}};var rp=e=>()=>t=>r=>{(tA(r,"DROP_COMPLETE")||tA(r,"FLUSH")||tA(r,"DROP_ANIMATE"))&&e.stopPublishing(),t(r)},rf=e=>{let t=!1;return()=>r=>n=>{if(tA(n,"INITIAL_PUBLISH")){t=!0,e.tryRecordFocus(n.payload.critical.draggable.id),r(n),e.tryRestoreFocusRecorded();return}if(r(n),t){if(tA(n,"FLUSH")){t=!1,e.tryRestoreFocusRecorded();return}if(tA(n,"DROP_COMPLETE")){t=!1;let r=n.payload.completed.result;r.combine&&e.tryShiftRecord(r.draggableId,r.combine.draggableId),e.tryRestoreFocusRecorded()}}}};let rg=e=>tA(e,"DROP_COMPLETE")||tA(e,"DROP_ANIMATE")||tA(e,"FLUSH");var rm=e=>t=>r=>n=>{if(rg(n)){e.stop(),r(n);return}if(tA(n,"INITIAL_PUBLISH")){r(n);let l=t.getState();"DRAGGING"!==l.phase&&h(),e.start(l);return}r(n),e.scroll(t.getState())};let rb=e=>t=>r=>{if(t(r),!tA(r,"PUBLISH_WHILE_DRAGGING"))return;let n=e.getState();"DROP_PENDING"!==n.phase||n.isWaiting||e.dispatch(tU({reason:n.reason}))},rh=i.qC;var ry=({dimensionMarshal:e,focusMarshal:t,styleMarshal:r,getResponders:n,announce:l,autoScroller:a})=>(0,i.MT)(tE,rh((0,i.md)(tz(r),rp(e),tj(e),t7,ru,rc,rb,rm(a),t9,rf(t),rs(n,l))));let rI=()=>({additions:{},removals:{},modified:{}});var rv=({scrollHeight:e,scrollWidth:t,height:r,width:n})=>{let l=w({x:t,y:e},{x:n,y:r});return{x:Math.max(0,l.x),y:Math.max(0,l.y)}},rx=()=>{let e=document.documentElement;return e||h(),e},rD=()=>{let e=rx();return rv({scrollHeight:e.scrollHeight,scrollWidth:e.scrollWidth,width:e.clientWidth,height:e.clientHeight})},rE=()=>{let e=t8(),t=rD(),r=e.y,n=e.x,l=rx(),i=l.clientWidth,a=l.clientHeight;return{frame:(0,o.Dz)({top:r,left:n,right:n+i,bottom:r+a}),scroll:{initial:e,current:e,max:t,diff:{value:C,displacement:C}}}},rA=({critical:e,scrollOptions:t,registry:r})=>{tf();let n=rE(),l=n.scroll.current,i=e.droppable,a=r.droppable.getAllByType(i.type).map(e=>e.callbacks.getDimensionAndWatchScroll(l,t)),o={draggables:z(r.draggable.getAllByType(e.draggable.type).map(e=>e.getDimension(l))),droppables:j(a)};return tg(),{dimensions:o,critical:e,viewport:n}};function rN(e,t,r){return r.descriptor.id!==t.id&&r.descriptor.type===t.type&&"virtual"===e.droppable.getById(r.descriptor.droppableId).descriptor.mode}var rR=(e,t)=>{let r=null,n=function({registry:e,callbacks:t}){let r=rI(),n=null,l=()=>{n||(t.collectionStarting(),n=requestAnimationFrame(()=>{n=null,tf();let{additions:l,removals:i,modified:a}=r,o=Object.keys(l).map(t=>e.draggable.getById(t).getDimension(C)).sort((e,t)=>e.descriptor.index-t.descriptor.index),d=Object.keys(a).map(t=>{let r=e.droppable.getById(t).callbacks.getScrollWhileDragging();return{droppableId:t,scroll:r}}),s={additions:o,removals:Object.keys(i),modified:d};r=rI(),tg(),t.publish(s)}))};return{add:e=>{let t=e.descriptor.id;r.additions[t]=e,r.modified[e.descriptor.droppableId]=!0,r.removals[t]&&delete r.removals[t],l()},remove:e=>{let t=e.descriptor;r.removals[t.id]=!0,r.modified[t.droppableId]=!0,r.additions[t.id]&&delete r.additions[t.id],l()},stop:()=>{n&&(cancelAnimationFrame(n),n=null,r=rI())}}}({callbacks:{publish:t.publishWhileDragging,collectionStarting:t.collectionStarting},registry:e}),l=t=>{r||h();let l=r.critical.draggable;"ADDITION"===t.type&&rN(e,l,t.value)&&n.add(t.value),"REMOVAL"===t.type&&rN(e,l,t.value)&&n.remove(t.value)};return{updateDroppableIsEnabled:(n,l)=>{e.droppable.exists(n)||h(),r&&t.updateDroppableIsEnabled({id:n,isEnabled:l})},updateDroppableIsCombineEnabled:(n,l)=>{r&&(e.droppable.exists(n)||h(),t.updateDroppableIsCombineEnabled({id:n,isCombineEnabled:l}))},scrollDroppable:(t,n)=>{r&&e.droppable.getById(t).callbacks.scroll(n)},updateDroppableScroll:(n,l)=>{r&&(e.droppable.exists(n)||h(),t.updateDroppableScroll({id:n,newScroll:l}))},startPublishing:t=>{r&&h();let n=e.draggable.getById(t.draggableId),i=e.droppable.getById(n.descriptor.droppableId),a={draggable:n.descriptor,droppable:i.descriptor};return r={critical:a,unsubscribe:e.subscribe(l)},rA({critical:a,registry:e,scrollOptions:t.scrollOptions})},stopPublishing:()=>{if(!r)return;n.stop();let t=r.critical.droppable;e.droppable.getAllByType(t.type).forEach(e=>e.callbacks.dragStopped()),r.unsubscribe(),r=null}}},rC=(e,t)=>"IDLE"===e.phase||"DROP_ANIMATING"===e.phase&&e.completed.result.draggableId!==t&&"DROP"===e.completed.result.reason,rP=e=>{window.scrollBy(e.x,e.y)};let rw=V(e=>q(e).filter(e=>!!e.isEnabled&&!!e.frame)),rO=(e,t)=>rw(t).find(t=>(t.frame||h(),e7(t.frame.pageMarginBox)(e)))||null;var rS=({center:e,destination:t,droppables:r})=>{if(t){let e=r[t];return e.frame?e:null}return rO(e,r)};let rB={startFromPercentage:.25,maxScrollAtPercentage:.05,maxPixelScroll:28,ease:e=>e**2,durationDampening:{stopDampeningAt:1200,accelerateAt:360},disabled:!1};var rG=(e,t,r=()=>rB)=>{let n=r();return{startScrollingFrom:e[t.size]*n.startFromPercentage,maxScrollValueAt:e[t.size]*n.maxScrollAtPercentage}},rL=({startOfRange:e,endOfRange:t,current:r})=>{let n=t-e;return 0===n?0:(r-e)/n},rT=(e,t,r=()=>rB)=>{let n=r();if(e>t.startScrollingFrom)return 0;if(e<=t.maxScrollValueAt)return n.maxPixelScroll;if(e===t.startScrollingFrom)return 1;let l=rL({startOfRange:t.maxScrollValueAt,endOfRange:t.startScrollingFrom,current:e});return Math.ceil(n.maxPixelScroll*n.ease(1-l))},r_=(e,t,r)=>{let n=r(),l=n.durationDampening.accelerateAt,i=n.durationDampening.stopDampeningAt,a=Date.now()-t;if(a>=i)return e;if(a<l)return 1;let o=rL({startOfRange:l,endOfRange:i,current:a});return Math.ceil(e*n.ease(o))},rM=({distanceToEdge:e,thresholds:t,dragStartTime:r,shouldUseTimeDampening:n,getAutoScrollerOptions:l})=>{let i=rT(e,t,l);return 0===i?0:n?Math.max(r_(i,r,l),1):i},rF=({container:e,distanceToEdges:t,dragStartTime:r,axis:n,shouldUseTimeDampening:l,getAutoScrollerOptions:i})=>{let a=rG(e,n,i);return t[n.end]<t[n.start]?rM({distanceToEdge:t[n.end],thresholds:a,dragStartTime:r,shouldUseTimeDampening:l,getAutoScrollerOptions:i}):-1*rM({distanceToEdge:t[n.start],thresholds:a,dragStartTime:r,shouldUseTimeDampening:l,getAutoScrollerOptions:i})},rk=({container:e,subject:t,proposedScroll:r})=>{let n=t.height>e.height,l=t.width>e.width;return l||n?l&&n?null:{x:l?0:r.x,y:n?0:r.y}:r};let r$=T(e=>0===e?0:e);var rW=({dragStartTime:e,container:t,subject:r,center:n,shouldUseTimeDampening:l,getAutoScrollerOptions:i})=>{let a={top:n.y-t.top,right:t.right-n.x,bottom:t.bottom-n.y,left:n.x-t.left},o=rF({container:t,distanceToEdges:a,dragStartTime:e,axis:eo,shouldUseTimeDampening:l,getAutoScrollerOptions:i}),d=r$({x:rF({container:t,distanceToEdges:a,dragStartTime:e,axis:ed,shouldUseTimeDampening:l,getAutoScrollerOptions:i}),y:o});if(O(d,C))return null;let s=rk({container:t,subject:r,proposedScroll:d});return s?O(s,C)?null:s:null};let rU=T(e=>0===e?0:e>0?1:-1),rH=(()=>{let e=(e,t)=>e<0?e:e>t?e-t:0;return({current:t,max:r,change:n})=>{let l=P(t,n),i={x:e(l.x,r.x),y:e(l.y,r.y)};return O(i,C)?null:i}})(),rV=({max:e,current:t,change:r})=>{let n={x:Math.max(t.x,e.x),y:Math.max(t.y,e.y)},l=rU(r),i=rH({max:n,current:t,change:l});return!i||0!==l.x&&0===i.x||0!==l.y&&0===i.y},rj=(e,t)=>rV({current:e.scroll.current,max:e.scroll.max,change:t}),rz=(e,t)=>{if(!rj(e,t))return null;let r=e.scroll.max;return rH({current:e.scroll.current,max:r,change:t})},rq=(e,t)=>{let r=e.frame;return!!r&&rV({current:r.scroll.current,max:r.scroll.max,change:t})},rY=(e,t)=>{let r=e.frame;return r&&rq(e,t)?rH({current:r.scroll.current,max:r.scroll.max,change:t}):null};var rJ=({viewport:e,subject:t,center:r,dragStartTime:n,shouldUseTimeDampening:l,getAutoScrollerOptions:i})=>{let a=rW({dragStartTime:n,container:e.frame,subject:t,center:r,shouldUseTimeDampening:l,getAutoScrollerOptions:i});return a&&rj(e,a)?a:null},rZ=({droppable:e,subject:t,center:r,dragStartTime:n,shouldUseTimeDampening:l,getAutoScrollerOptions:i})=>{let a=e.frame;if(!a)return null;let o=rW({dragStartTime:n,container:a.pageMarginBox,subject:t,center:r,shouldUseTimeDampening:l,getAutoScrollerOptions:i});return o&&rq(e,o)?o:null},rX=({state:e,dragStartTime:t,shouldUseTimeDampening:r,scrollWindow:n,scrollDroppable:l,getAutoScrollerOptions:i})=>{let a=e.current.page.borderBoxCenter,o=e.dimensions.draggables[e.critical.draggable.id].page.marginBox;if(e.isWindowScrollAllowed){let l=rJ({dragStartTime:t,viewport:e.viewport,subject:o,center:a,shouldUseTimeDampening:r,getAutoScrollerOptions:i});if(l){n(l);return}}let d=rS({center:a,destination:e2(e.impact),droppables:e.dimensions.droppables});if(!d)return;let s=rZ({dragStartTime:t,droppable:d,subject:o,center:a,shouldUseTimeDampening:r,getAutoScrollerOptions:i});s&&l(d.descriptor.id,s)},rK=({scrollWindow:e,scrollDroppable:t,getAutoScrollerOptions:r=()=>rB})=>{let n=(0,d.Z)(e),l=(0,d.Z)(t),i=null,a=e=>{i||h();let{shouldUseTimeDampening:t,dragStartTime:a}=i;rX({state:e,scrollWindow:n,scrollDroppable:l,dragStartTime:a,shouldUseTimeDampening:t,getAutoScrollerOptions:r})};return{start:e=>{tf(),i&&h();let t=Date.now(),n=!1,l=()=>{n=!0};rX({state:e,dragStartTime:0,shouldUseTimeDampening:!1,scrollWindow:l,scrollDroppable:l,getAutoScrollerOptions:r}),i={dragStartTime:t,shouldUseTimeDampening:n},tg(),n&&a(e)},stop:()=>{i&&(n.cancel(),l.cancel(),i=null)},scroll:a}},rQ=({move:e,scrollDroppable:t,scrollWindow:r})=>{let n=(t,r)=>{e({client:P(t.current.client.selection,r)})},l=(e,r)=>{if(!rq(e,r))return r;let n=rY(e,r);if(!n)return t(e.descriptor.id,r),null;let l=w(r,n);return t(e.descriptor.id,l),w(r,l)},i=(e,t,n)=>{if(!e||!rj(t,n))return n;let l=rz(t,n);if(!l)return r(n),null;let i=w(n,l);return r(i),w(n,i)};return e=>{let t=e.scrollJumpRequest;if(!t)return;let r=e2(e.impact);r||h();let a=l(e.dimensions.droppables[r],t);if(!a)return;let o=e.viewport,d=i(e.isWindowScrollAllowed,o,a);d&&n(e,d)}},r0=({scrollDroppable:e,scrollWindow:t,move:r,getAutoScrollerOptions:n})=>{let l=rK({scrollWindow:t,scrollDroppable:e,getAutoScrollerOptions:n}),i=rQ({move:r,scrollWindow:t,scrollDroppable:e});return{scroll:e=>{if(!n().disabled&&"DRAGGING"===e.phase){if("FLUID"===e.movementMode){l.scroll(e);return}e.scrollJumpRequest&&i(e)}},start:l.start,stop:l.stop}};let r1="data-rfd",r2=(()=>{let e=`${r1}-drag-handle`;return{base:e,draggableId:`${e}-draggable-id`,contextId:`${e}-context-id`}})(),r3=(()=>{let e=`${r1}-draggable`;return{base:e,contextId:`${e}-context-id`,id:`${e}-id`}})(),r5=(()=>{let e=`${r1}-droppable`;return{base:e,contextId:`${e}-context-id`,id:`${e}-id`}})(),r4={contextId:`${r1}-scroll-container-context-id`},r7=e=>t=>`[${t}="${e}"]`,r8=(e,t)=>e.map(e=>{let r=e.styles[t];return r?`${e.selector} { ${r} }`:""}).join(" ");var r6=e=>{let t=r7(e),r=(()=>{let e=`
      cursor: -webkit-grab;
      cursor: grab;
    `;return{selector:t(r2.contextId),styles:{always:`
          -webkit-touch-callout: none;
          -webkit-tap-highlight-color: rgba(0,0,0,0);
          touch-action: manipulation;
        `,resting:e,dragging:"pointer-events: none;",dropAnimating:e}}})(),n=[(()=>{let e=`
      transition: ${tX.outOfTheWay};
    `;return{selector:t(r3.contextId),styles:{dragging:e,dropAnimating:e,userCancel:e}}})(),r,{selector:t(r5.contextId),styles:{always:"overflow-anchor: none;"}},{selector:"body",styles:{dragging:`
        cursor: grabbing;
        cursor: -webkit-grabbing;
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        overflow-anchor: none;
      `}}];return{always:r8(n,"always"),resting:r8(n,"resting"),dragging:r8(n,"dragging"),dropAnimating:r8(n,"dropAnimating"),userCancel:r8(n,"userCancel")}};let r9="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?n.useLayoutEffect:n.useEffect,ne=()=>{let e=document.querySelector("head");return e||h(),e},nt=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.type="text/css",t};function nr(e,t){return Array.from(e.querySelectorAll(t))}var nn=e=>e&&e.ownerDocument&&e.ownerDocument.defaultView?e.ownerDocument.defaultView:window;function nl(e){return e instanceof nn(e).HTMLElement}function ni(e,t){let r=`[${r2.contextId}="${e}"]`,n=nr(document,r);if(!n.length)return null;let l=n.find(e=>e.getAttribute(r2.draggableId)===t);return l&&nl(l)?l:null}function na(){let e={draggables:{},droppables:{}},t=[];function r(e){t.length&&t.forEach(t=>t(e))}function n(t){return e.draggables[t]||null}function l(t){return e.droppables[t]||null}return{draggable:{register:t=>{e.draggables[t.descriptor.id]=t,r({type:"ADDITION",value:t})},update:(t,r)=>{let n=e.draggables[r.descriptor.id];n&&n.uniqueId===t.uniqueId&&(delete e.draggables[r.descriptor.id],e.draggables[t.descriptor.id]=t)},unregister:t=>{let l=t.descriptor.id,i=n(l);i&&t.uniqueId===i.uniqueId&&(delete e.draggables[l],e.droppables[t.descriptor.droppableId]&&r({type:"REMOVAL",value:t}))},getById:function(e){let t=n(e);return t||h(),t},findById:n,exists:e=>!!n(e),getAllByType:t=>Object.values(e.draggables).filter(e=>e.descriptor.type===t)},droppable:{register:t=>{e.droppables[t.descriptor.id]=t},unregister:t=>{let r=l(t.descriptor.id);r&&t.uniqueId===r.uniqueId&&delete e.droppables[t.descriptor.id]},getById:function(e){let t=l(e);return t||h(),t},findById:l,exists:e=>!!l(e),getAllByType:t=>Object.values(e.droppables).filter(e=>e.descriptor.type===t)},subscribe:function(e){return t.push(e),function(){let r=t.indexOf(e);-1!==r&&t.splice(r,1)}},clean:function(){e.draggables={},e.droppables={},t.length=0}}}var no=n.createContext(null),nd=()=>{let e=document.body;return e||h(),e};let ns={position:"absolute",width:"1px",height:"1px",margin:"-1px",border:"0",padding:"0",overflow:"hidden",clip:"rect(0 0 0 0)","clip-path":"inset(100%)"},nu=e=>`rfd-announcement-${e}`,nc={separator:"::"};function np(e,t=nc){let r=n.useId();return N(()=>`${e}${t.separator}${r}`,[t.separator,e,r])}var nf=n.createContext(null);let ng=/(\d+)\.(\d+)\.(\d+)/,nm=e=>{let t=ng.exec(e);return null!=t||h(),{major:Number(t[1]),minor:Number(t[2]),patch:Number(t[3]),raw:e}},nb=(e,t)=>t.major>e.major||!(t.major<e.major)&&(t.minor>e.minor||!(t.minor<e.minor)&&t.patch>=e.patch);var nh=(e,t)=>{if(nb(nm(e),nm(t)))return},ny=e=>{let t=e.doctype;t&&(t.name.toLowerCase(),t.publicId)};function nI(e,t){}function nv(e){let t=(0,n.useRef)(e);return(0,n.useEffect)(()=>{t.current=e}),t}function nx(e){return"IDLE"!==e.phase&&"DROP_ANIMATING"!==e.phase&&e.isDragging}let nD={13:!0,9:!0};var nE=e=>{nD[e.keyCode]&&e.preventDefault()};let nA=(()=>{let e="visibilitychange";return"undefined"==typeof document?e:[e,`ms${e}`,`webkit${e}`,`moz${e}`,`o${e}`].find(e=>`on${e}` in document)||e})(),nN={type:"IDLE"};function nR(){}let nC={34:!0,33:!0,36:!0,35:!0},nP={type:"IDLE"},nw=["input","button","textarea","select","option","optgroup","video","audio"];var nO=e=>(0,o.Dz)(e.getBoundingClientRect()).center;let nS=(()=>{let e="matches";return"undefined"==typeof document?e:[e,"msMatchesSelector","webkitMatchesSelector"].find(e=>e in Element.prototype)||e})();function nB(e){e.preventDefault()}function nG({expected:e,phase:t,isLockActive:r,shouldWarn:n}){return!!r()&&e===t}function nL({lockAPI:e,store:t,registry:r,draggableId:n}){if(e.isClaimed())return!1;let l=r.draggable.findById(n);return!!(l&&l.options.isEnabled&&rC(t.getState(),n))}let nT=[function(e){let t=(0,n.useRef)(nN),r=(0,n.useRef)(g),l=N(()=>({eventName:"mousedown",fn:function(t){if(t.defaultPrevented||0!==t.button||t.ctrlKey||t.metaKey||t.shiftKey||t.altKey)return;let n=e.findClosestDraggableId(t);if(!n)return;let l=e.tryGetLock(n,o,{sourceEvent:t});if(!l)return;t.preventDefault();let i={x:t.clientX,y:t.clientY};r.current(),u(l,i)}}),[e]),i=N(()=>({eventName:"webkitmouseforcewillbegin",fn:t=>{if(t.defaultPrevented)return;let r=e.findClosestDraggableId(t);if(!r)return;let n=e.findOptionsForDraggable(r);n&&!n.shouldRespectForcePress&&e.canGetLock(r)&&t.preventDefault()}}),[e]),a=R(function(){r.current=m(window,[i,l],{passive:!1,capture:!0})},[i,l]),o=R(()=>{"IDLE"!==t.current.type&&(t.current=nN,r.current(),a())},[a]),d=R(()=>{let e=t.current;o(),"DRAGGING"===e.type&&e.actions.cancel({shouldBlockNextClick:!0}),"PENDING"===e.type&&e.actions.abort()},[o]),s=R(function(){let e=function({cancel:e,completed:t,getPhase:r,setPhase:n}){return[{eventName:"mousemove",fn:e=>{var t;let{button:l,clientX:i,clientY:a}=e;if(0!==l)return;let o={x:i,y:a},d=r();if("DRAGGING"===d.type){e.preventDefault(),d.actions.move(o);return}"PENDING"!==d.type&&h(),t=d.point,(Math.abs(o.x-t.x)>=5||Math.abs(o.y-t.y)>=5)&&(e.preventDefault(),n({type:"DRAGGING",actions:d.actions.fluidLift(o)}))}},{eventName:"mouseup",fn:n=>{let l=r();if("DRAGGING"!==l.type){e();return}n.preventDefault(),l.actions.drop({shouldBlockNextClick:!0}),t()}},{eventName:"mousedown",fn:t=>{"DRAGGING"===r().type&&t.preventDefault(),e()}},{eventName:"keydown",fn:t=>{if("PENDING"===r().type){e();return}if(27===t.keyCode){t.preventDefault(),e();return}nE(t)}},{eventName:"resize",fn:e},{eventName:"scroll",options:{passive:!0,capture:!1},fn:()=>{"PENDING"===r().type&&e()}},{eventName:"webkitmouseforcedown",fn:t=>{let n=r();if("IDLE"!==n.type||h(),n.actions.shouldRespectForcePress()){e();return}t.preventDefault()}},{eventName:nA,fn:e}]}({cancel:d,completed:o,getPhase:()=>t.current,setPhase:e=>{t.current=e}});r.current=m(window,e,{capture:!0,passive:!1})},[d,o]),u=R(function(e,r){"IDLE"!==t.current.type&&h(),t.current={type:"PENDING",point:r,actions:e},s()},[s]);r9(function(){return a(),function(){r.current()}},[a])},function(e){let t=(0,n.useRef)(nR),r=N(()=>({eventName:"keydown",fn:function(r){if(r.defaultPrevented||32!==r.keyCode)return;let n=e.findClosestDraggableId(r);if(!n)return;let i=e.tryGetLock(n,d,{sourceEvent:r});if(!i)return;r.preventDefault();let a=!0,o=i.snapLift();function d(){a||h(),a=!1,t.current(),l()}t.current(),t.current=m(window,function(e,t){function r(){t(),e.cancel()}return[{eventName:"keydown",fn:n=>{if(27===n.keyCode){n.preventDefault(),r();return}if(32===n.keyCode){n.preventDefault(),t(),e.drop();return}if(40===n.keyCode){n.preventDefault(),e.moveDown();return}if(38===n.keyCode){n.preventDefault(),e.moveUp();return}if(39===n.keyCode){n.preventDefault(),e.moveRight();return}if(37===n.keyCode){n.preventDefault(),e.moveLeft();return}if(nC[n.keyCode]){n.preventDefault();return}nE(n)}},{eventName:"mousedown",fn:r},{eventName:"mouseup",fn:r},{eventName:"click",fn:r},{eventName:"touchstart",fn:r},{eventName:"resize",fn:r},{eventName:"wheel",fn:r,options:{passive:!0}},{eventName:nA,fn:r}]}(o,d),{capture:!0,passive:!1})}}),[e]),l=R(function(){t.current=m(window,[r],{passive:!1,capture:!0})},[r]);r9(function(){return l(),function(){t.current()}},[l])},function(e){let t=(0,n.useRef)(nP),r=(0,n.useRef)(g),l=R(function(){return t.current},[]),i=R(function(e){t.current=e},[]),a=N(()=>({eventName:"touchstart",fn:function(t){if(t.defaultPrevented)return;let n=e.findClosestDraggableId(t);if(!n)return;let l=e.tryGetLock(n,d,{sourceEvent:t});if(!l)return;let{clientX:i,clientY:a}=t.touches[0];r.current(),p(l,{x:i,y:a})}}),[e]),o=R(function(){r.current=m(window,[a],{capture:!0,passive:!1})},[a]),d=R(()=>{let e=t.current;"IDLE"!==e.type&&("PENDING"===e.type&&clearTimeout(e.longPressTimerId),i(nP),r.current(),o())},[o,i]),s=R(()=>{let e=t.current;d(),"DRAGGING"===e.type&&e.actions.cancel({shouldBlockNextClick:!0}),"PENDING"===e.type&&e.actions.abort()},[d]),u=R(function(){let e={capture:!0,passive:!1},t={cancel:s,completed:d,getPhase:l},n=m(window,function({cancel:e,completed:t,getPhase:r}){return[{eventName:"touchmove",options:{capture:!1},fn:t=>{let n=r();if("DRAGGING"!==n.type){e();return}n.hasMoved=!0;let{clientX:l,clientY:i}=t.touches[0];t.preventDefault(),n.actions.move({x:l,y:i})}},{eventName:"touchend",fn:n=>{let l=r();if("DRAGGING"!==l.type){e();return}n.preventDefault(),l.actions.drop({shouldBlockNextClick:!0}),t()}},{eventName:"touchcancel",fn:t=>{if("DRAGGING"!==r().type){e();return}t.preventDefault(),e()}},{eventName:"touchforcechange",fn:t=>{let n=r();"IDLE"!==n.type||h();let l=t.touches[0];if(!l||!(l.force>=.15))return;let i=n.actions.shouldRespectForcePress();if("PENDING"===n.type){i&&e();return}if(i){if(n.hasMoved){t.preventDefault();return}e();return}t.preventDefault()}},{eventName:nA,fn:e}]}(t),e),i=m(window,function({cancel:e,getPhase:t}){return[{eventName:"orientationchange",fn:e},{eventName:"resize",fn:e},{eventName:"contextmenu",fn:e=>{e.preventDefault()}},{eventName:"keydown",fn:r=>{if("DRAGGING"!==t().type){e();return}27===r.keyCode&&r.preventDefault(),e()}},{eventName:nA,fn:e}]}(t),e);r.current=function(){n(),i()}},[s,l,d]),c=R(function(){let e=l();"PENDING"!==e.type&&h(),i({type:"DRAGGING",actions:e.actions.fluidLift(e.point),hasMoved:!1})},[l,i]),p=R(function(e,t){"IDLE"!==l().type&&h(),i({type:"PENDING",point:t,actions:e,longPressTimerId:setTimeout(c,120)}),u()},[u,l,i,c]);r9(function(){return o(),function(){r.current();let e=l();"PENDING"===e.type&&(clearTimeout(e.longPressTimerId),i(nP))}},[l,o,i]),r9(function(){return m(window,[{eventName:"touchmove",fn:()=>{},options:{capture:!1,passive:!1}}])},[])}],n_=e=>({onBeforeCapture:t=>{(0,l.flushSync)(()=>{e.onBeforeCapture&&e.onBeforeCapture(t)})},onBeforeDragStart:e.onBeforeDragStart,onDragStart:e.onDragStart,onDragEnd:e.onDragEnd,onDragUpdate:e.onDragUpdate}),nM=e=>({...rB,...e.autoScrollerOptions,durationDampening:{...rB.durationDampening,...e.autoScrollerOptions}});function nF(e){return e.current||h(),e.current}function nk(e){let{contextId:t,setCallbacks:r,sensors:l,nonce:o,dragHandleUsageInstructions:u}=e,c=(0,n.useRef)(null),p=nv(e),f=R(()=>n_(p.current),[p]),b=R(()=>nM(p.current),[p]),y=function(e){let t=N(()=>nu(e),[e]),r=(0,n.useRef)(null);return(0,n.useEffect)(function(){let e=document.createElement("div");return r.current=e,e.id=t,e.setAttribute("aria-live","assertive"),e.setAttribute("aria-atomic","true"),(0,s.Z)(e.style,ns),nd().appendChild(e),function(){setTimeout(function(){let t=nd();t.contains(e)&&t.removeChild(e),e===r.current&&(r.current=null)})}},[t]),R(e=>{let t=r.current;if(t){t.textContent=e;return}},[])}(t),I=function({contextId:e,text:t}){let r=np("hidden-text",{separator:"-"}),l=N(()=>(function({contextId:e,uniqueId:t}){return`rfd-hidden-text-${e}-${t}`})({contextId:e,uniqueId:r}),[r,e]);return(0,n.useEffect)(function(){let e=document.createElement("div");return e.id=l,e.textContent=t,e.style.display="none",nd().appendChild(e),function(){let t=nd();t.contains(e)&&t.removeChild(e)}},[l,t]),l}({contextId:t,text:u}),v=function(e,t){let r=N(()=>r6(e),[e]),l=(0,n.useRef)(null),i=(0,n.useRef)(null),a=R(V(e=>{let t=i.current;t||h(),t.textContent=e}),[]),o=R(e=>{let t=l.current;t||h(),t.textContent=e},[]);r9(()=>{(l.current||i.current)&&h();let n=nt(t),d=nt(t);return l.current=n,i.current=d,n.setAttribute(`${r1}-always`,e),d.setAttribute(`${r1}-dynamic`,e),ne().appendChild(n),ne().appendChild(d),o(r.always),a(r.resting),()=>{let e=e=>{let t=e.current;t||h(),ne().removeChild(t),e.current=null};e(l),e(i)}},[t,o,a,r.always,r.resting,e]);let d=R(()=>a(r.dragging),[a,r.dragging]),s=R(e=>{if("DROP"===e){a(r.dropAnimating);return}a(r.userCancel)},[a,r.dropAnimating,r.userCancel]),u=R(()=>{i.current&&a(r.resting)},[a,r.resting]);return N(()=>({dragging:d,dropping:s,resting:u}),[d,s,u])}(t,o),x=R(e=>{nF(c).dispatch(e)},[]),D=N(()=>(0,i.DE)({publishWhileDragging:tP,updateDroppableScroll:tO,updateDroppableIsEnabled:tS,updateDroppableIsCombineEnabled:tB,collectionStarting:tw},x),[x]),E=function(){let e=N(na,[]);return(0,n.useEffect)(()=>function(){e.clean()},[e]),e}(),A=N(()=>rR(E,D),[E,D]),C=N(()=>r0({scrollWindow:rP,scrollDroppable:A.scrollDroppable,getAutoScrollerOptions:b,...(0,i.DE)({move:tG},x)}),[A.scrollDroppable,x,b]),P=function(e){let t=(0,n.useRef)({}),r=(0,n.useRef)(null),l=(0,n.useRef)(null),i=(0,n.useRef)(!1),a=R(function(e,r){let n={id:e,focus:r};return t.current[e]=n,function(){let r=t.current;r[e]!==n&&delete r[e]}},[]),o=R(function(t){let r=ni(e,t);r&&r!==document.activeElement&&r.focus()},[e]),d=R(function(e,t){r.current===e&&(r.current=t)},[]),s=R(function(){!l.current&&i.current&&(l.current=requestAnimationFrame(()=>{l.current=null;let e=r.current;e&&o(e)}))},[o]),u=R(function(e){r.current=null;let t=document.activeElement;t&&t.getAttribute(r2.draggableId)===e&&(r.current=e)},[]);return r9(()=>(i.current=!0,function(){i.current=!1;let e=l.current;e&&cancelAnimationFrame(e)}),[]),N(()=>({register:a,tryRecordFocus:u,tryRestoreFocusRecorded:s,tryShiftRecord:d}),[a,u,s,d])}(t),w=N(()=>ry({announce:y,autoScroller:C,dimensionMarshal:A,focusMarshal:P,getResponders:f,styleMarshal:v}),[y,C,A,P,f,v]);c.current=w;let O=R(()=>{let e=nF(c);"IDLE"!==e.getState().phase&&e.dispatch(tk())},[]),S=R(()=>{let e=nF(c).getState();return"DROP_ANIMATING"===e.phase||"IDLE"!==e.phase&&e.isDragging},[]);r(N(()=>({isDragging:S,tryAbort:O}),[S,O]));let B=R(e=>rC(nF(c).getState(),e),[]),G=R(()=>e4(nF(c).getState()),[]),L=N(()=>({marshal:A,focus:P,contextId:t,canLift:B,isMovementAllowed:G,dragHandleUsageInstructionsId:I,registry:E}),[t,A,I,P,B,G,E]);return!function({contextId:e,store:t,registry:r,customSensors:l,enableDefaultSensors:i}){let a=[...i?nT:[],...l||[]],o=(0,n.useState)(()=>(function(){let e=null;function t(){e||h(),e=null}return{isClaimed:function(){return!!e},isActive:function(t){return t===e},claim:function(t){e&&h();let r={abandon:t};return e=r,r},release:t,tryAbandon:function(){e&&(e.abandon(),t())}}})())[0],s=R(function(e,t){nx(e)&&!nx(t)&&o.tryAbandon()},[o]);r9(function(){let e=t.getState();return t.subscribe(()=>{let r=t.getState();s(e,r),e=r})},[o,t,s]),r9(()=>o.tryAbandon,[o.tryAbandon]);let u=R(e=>nL({lockAPI:o,registry:r,store:t,draggableId:e}),[o,r,t]),c=R((n,l,i)=>(function({lockAPI:e,contextId:t,store:r,registry:n,draggableId:l,forceSensorStop:i,sourceEvent:a}){if(!nL({lockAPI:e,store:r,registry:n,draggableId:l}))return null;let o=n.draggable.getById(l),s=function(e,t){let r=`[${r3.contextId}="${e}"]`,n=nr(document,r).find(e=>e.getAttribute(r3.id)===t);return n&&nl(n)?n:null}(t,o.descriptor.id);if(!s||a&&!o.options.canDragInteractiveElements&&function(e,t){let r=t.target;return!!nl(r)&&function e(t,r){if(null==r)return!1;if(nw.includes(r.tagName.toLowerCase()))return!0;let n=r.getAttribute("contenteditable");return"true"===n||""===n||r!==t&&e(t,r.parentElement)}(e,r)}(s,a))return null;let u=e.claim(i||g),c="PRE_DRAG";function p(){return o.options.shouldRespectForcePress}function f(){return e.isActive(u)}let b=(function(e,t){nG({expected:e,phase:c,isLockActive:f,shouldWarn:!0})&&r.dispatch(t())}).bind(null,"DRAGGING");function y(t){function n(){e.release(),c="COMPLETED"}function l(e,l={shouldBlockNextClick:!1}){t.cleanup(),l.shouldBlockNextClick&&setTimeout(m(window,[{eventName:"click",fn:nB,options:{once:!0,passive:!1,capture:!0}}])),n(),r.dispatch(tU({reason:e}))}return"PRE_DRAG"!==c&&(n(),h()),r.dispatch(tR(t.liftActionArgs)),c="DRAGGING",{isActive:()=>nG({expected:"DRAGGING",phase:c,isLockActive:f,shouldWarn:!1}),shouldRespectForcePress:p,drop:e=>l("DROP",e),cancel:e=>l("CANCEL",e),...t.actions}}return{isActive:()=>nG({expected:"PRE_DRAG",phase:c,isLockActive:f,shouldWarn:!1}),shouldRespectForcePress:p,fluidLift:function(e){let t=(0,d.Z)(e=>{b(()=>tG({client:e}))});return{...y({liftActionArgs:{id:l,clientSelection:e,movementMode:"FLUID"},cleanup:()=>t.cancel(),actions:{move:t}}),move:t}},snapLift:function(){return y({liftActionArgs:{id:l,clientSelection:nO(s),movementMode:"SNAP"},cleanup:g,actions:{moveUp:()=>b(tT),moveRight:()=>b(tM),moveDown:()=>b(t_),moveLeft:()=>b(tF)}})},abort:function(){nG({expected:"PRE_DRAG",phase:c,isLockActive:f,shouldWarn:!0})&&e.release()}}})({lockAPI:o,registry:r,contextId:e,store:t,draggableId:n,forceSensorStop:l||null,sourceEvent:i&&i.sourceEvent?i.sourceEvent:null}),[e,o,r,t]),p=R(t=>(function(e,t){let r=function(e,t){let r=t.target;if(!(r instanceof nn(r).Element))return null;let n=`[${r2.contextId}="${e}"]`,l=r.closest?r.closest(n):function e(t,r){return null==t?null:t[nS](r)?t:e(t.parentElement,r)}(r,n);return l&&nl(l)?l:null}(e,t);return r?r.getAttribute(r2.draggableId):null})(e,t),[e]),f=R(e=>{let t=r.draggable.findById(e);return t?t.options:null},[r.draggable]),b=R(function(){o.isClaimed()&&(o.tryAbandon(),"IDLE"!==t.getState().phase&&t.dispatch(tk()))},[o,t]),y=R(()=>o.isClaimed(),[o]),I=N(()=>({canGetLock:u,tryGetLock:c,findClosestDraggableId:p,findOptionsForDraggable:f,tryReleaseLock:b,isLockClaimed:y}),[u,c,p,f,b,y]);for(let e=0;e<a.length;e++)a[e](I)}({contextId:t,store:w,registry:E,customSensors:l||null,enableDefaultSensors:!1!==e.enableDefaultSensors}),(0,n.useEffect)(()=>O,[O]),n.createElement(nf.Provider,{value:L},n.createElement(a.zt,{context:no,store:w},e.children))}function n$(e){let t=n.useId(),r=e.dragHandleUsageInstructions||E.dragHandleUsageInstructions;return n.createElement(y,null,l=>n.createElement(nk,{nonce:e.nonce,contextId:t,setCallbacks:l,dragHandleUsageInstructions:r,enableDefaultSensors:e.enableDefaultSensors,sensors:e.sensors,onBeforeCapture:e.onBeforeCapture,onBeforeDragStart:e.onBeforeDragStart,onDragStart:e.onDragStart,onDragUpdate:e.onDragUpdate,onDragEnd:e.onDragEnd,autoScrollerOptions:e.autoScrollerOptions},e.children))}let nW={dragging:5e3,dropAnimating:4500},nU=(e,t)=>t?tX.drop(t.duration):e?tX.snap:tX.fluid,nH=(e,t)=>{if(e)return t?tY.opacity.drop:tY.opacity.combining},nV=e=>null!=e.forceShouldAnimate?e.forceShouldAnimate:"SNAP"===e.mode;var nj=n.createContext(null);function nz(e){e&&nl(e)||h()}function nq(e){let t=(0,n.useContext)(e);return t||h(),t}function nY(e){e.preventDefault()}var nJ=(e,t)=>e===t,nZ=e=>{let{combine:t,destination:r}=e;return r?r.droppableId:t?t.droppableId:null};let nX=e=>e.combine?e.combine.draggableId:null,nK=e=>e.at&&"COMBINE"===e.at.type?e.at.combine.draggableId:null;function nQ(e=null){return{isDragging:!1,isDropAnimating:!1,isClone:!1,dropAnimation:null,mode:null,draggingOver:null,combineTargetFor:e,combineWith:null}}let n0={mapped:{type:"SECONDARY",offset:C,combineTargetFor:null,shouldAnimateDisplacement:!0,snapshot:nQ(null)}},n1=(0,a.$j)(()=>{let e=function(){let e=V((e,t)=>({x:e,y:t})),t=V((e,t,r=null,n=null,l=null)=>({isDragging:!0,isClone:t,isDropAnimating:!!l,dropAnimation:l,mode:e,draggingOver:r,combineWith:n,combineTargetFor:null})),r=V((e,r,n,l,i=null,a=null,o=null)=>({mapped:{type:"DRAGGING",dropping:null,draggingOver:i,combineWith:a,mode:r,offset:e,dimension:n,forceShouldAnimate:o,snapshot:t(r,l,i,a,null)}}));return(n,l)=>{if(nx(n)){if(n.critical.draggable.id!==l.draggableId)return null;let t=n.current.client.offset,i=n.dimensions.draggables[l.draggableId],a=e2(n.impact),o=nK(n.impact),d=n.forceShouldAnimate;return r(e(t.x,t.y),n.movementMode,i,l.isClone,a,o,d)}if("DROP_ANIMATING"===n.phase){let e=n.completed;if(e.result.draggableId!==l.draggableId)return null;let r=l.isClone,i=n.dimensions.draggables[l.draggableId],a=e.result,o=a.mode,d=nZ(a),s=nX(a),u={duration:n.dropDuration,curve:tq.drop,moveTo:n.newHomeClientOffset,opacity:s?tY.opacity.drop:null,scale:s?tY.scale.drop:null};return{mapped:{type:"DRAGGING",offset:n.newHomeClientOffset,dimension:i,dropping:u,draggingOver:d,combineWith:s,mode:o,forceShouldAnimate:null,snapshot:t(o,r,d,s,u)}}}return null}}(),t=function(){let e=V((e,t)=>({x:e,y:t})),t=V(nQ),r=V((e,r=null,n)=>({mapped:{type:"SECONDARY",offset:e,combineTargetFor:r,shouldAnimateDisplacement:n,snapshot:t(r)}})),n=e=>e?r(C,e,!0):null,l=(t,l,i,a)=>{let o=i.displaced.visible[t],d=!!(a.inVirtualList&&a.effected[t]),s=X(i),u=s&&s.draggableId===t?l:null;if(!o){if(!d)return n(u);if(i.displaced.invisible[t])return null;let l=S(a.displacedBy.point);return r(e(l.x,l.y),u,!0)}if(d)return n(u);let c=i.displacedBy.point;return r(e(c.x,c.y),u,o.shouldAnimate)};return(e,t)=>{if(nx(e))return e.critical.draggable.id===t.draggableId?null:l(t.draggableId,e.critical.draggable.id,e.impact,e.afterCritical);if("DROP_ANIMATING"===e.phase){let r=e.completed;return r.result.draggableId===t.draggableId?null:l(t.draggableId,r.result.draggableId,r.impact,r.afterCritical)}return null}}();return(r,n)=>e(r,n)||t(r,n)||n0},{dropAnimationFinished:tV},null,{context:no,areStatePropsEqual:nJ})(e=>{let t=(0,n.useRef)(null),r=R((e=null)=>{t.current=e},[]),i=R(()=>t.current,[]),{contextId:a,dragHandleUsageInstructionsId:d,registry:s}=nq(nf),{type:u,droppableId:c}=nq(nj),p=N(()=>({id:e.draggableId,index:e.index,type:u,droppableId:c}),[e.draggableId,e.index,u,c]),{children:f,draggableId:g,isEnabled:m,shouldRespectForcePress:b,canDragInteractiveElements:y,isClone:I,mapped:v,dropAnimationFinished:x}=e;()=>{let t=e.draggableId;t||h(!1),"string"!=typeof t&&h(!1),Number.isInteger(e.index)||h(!1),"DRAGGING"!==e.mapped.type&&(nz(i()),e.isEnabled&&(ni(a,t)||h(!1)))},I||function(e){let t=np("draggable"),{descriptor:r,registry:l,getDraggableRef:i,canDragInteractiveElements:a,shouldRespectForcePress:d,isEnabled:s}=e,u=N(()=>({canDragInteractiveElements:a,shouldRespectForcePress:d,isEnabled:s}),[a,s,d]),c=R(e=>{let t=i();return t||h(),function(e,t,r=C){let n=window.getComputedStyle(t),l=t.getBoundingClientRect(),i=(0,o.Oq)(l,n),a=(0,o.oc)(i,r);return{descriptor:e,placeholder:{client:i,tagName:t.tagName.toLowerCase(),display:n.display},displaceBy:{x:i.marginBox.width,y:i.marginBox.height},client:i,page:a}}(r,t,e)},[r,i]),p=N(()=>({uniqueId:t,descriptor:r,options:u,getDimension:c}),[r,c,u,t]),f=(0,n.useRef)(p),g=(0,n.useRef)(!0);r9(()=>(l.draggable.register(f.current),()=>l.draggable.unregister(f.current)),[l.draggable]),r9(()=>{if(g.current){g.current=!1;return}let e=f.current;f.current=p,l.draggable.update(p,e)},[p,l.draggable])}(N(()=>({descriptor:p,registry:s,getDraggableRef:i,canDragInteractiveElements:y,shouldRespectForcePress:b,isEnabled:m}),[p,s,i,y,b,m]));let D=N(()=>m?{tabIndex:0,role:"button","aria-describedby":d,"data-rfd-drag-handle-draggable-id":g,"data-rfd-drag-handle-context-id":a,draggable:!1,onDragStart:nY}:null,[a,d,g,m]),E=R(e=>{"DRAGGING"===v.type&&v.dropping&&"transform"===e.propertyName&&(0,l.flushSync)(x)},[x,v]),A=N(()=>({innerRef:r,draggableProps:{"data-rfd-draggable-context-id":a,"data-rfd-draggable-id":g,style:"DRAGGING"===v.type?function(e){let t=e.dimension.client,{offset:r,combineWith:n,dropping:l}=e,i=!!n,a=nV(e),o=!!l,d=o?tQ.drop(r,i):tQ.moveTo(r);return{position:"fixed",top:t.marginBox.top,left:t.marginBox.left,boxSizing:"border-box",width:t.borderBox.width,height:t.borderBox.height,transition:nU(a,l),transform:d,opacity:nH(i,o),zIndex:o?nW.dropAnimating:nW.dragging,pointerEvents:"none"}}(v):{transform:tQ.moveTo(v.offset),transition:v.shouldAnimateDisplacement?void 0:"none"},onTransitionEnd:"DRAGGING"===v.type&&v.dropping?E:void 0},dragHandleProps:D}),[a,D,g,v,E,r]),P=N(()=>({draggableId:p.id,type:p.type,source:{index:p.index,droppableId:p.droppableId}}),[p.droppableId,p.id,p.index,p.type]);return n.createElement(n.Fragment,null,f(A,v.snapshot,P))});function n2(e){return nq(nj).isUsingCloneFor!==e.draggableId||e.isClone?n.createElement(n1,e):null}function n3(e){let t="boolean"!=typeof e.isDragDisabled||!e.isDragDisabled,r=!!e.disableInteractiveElementBlocking,l=!!e.shouldRespectForcePress;return n.createElement(n2,(0,s.Z)({},e,{isClone:!1,isEnabled:t,canDragInteractiveElements:r,shouldRespectForcePress:l}))}let n5=e=>t=>e===t,n4=n5("scroll"),n7=n5("auto");n5("visible");let n8=(e,t)=>t(e.overflowX)||t(e.overflowY),n6=e=>{let t=window.getComputedStyle(e),r={overflowX:t.overflowX,overflowY:t.overflowY};return n8(r,n4)||n8(r,n7)},n9=()=>!1,le=e=>null==e?null:e===document.body?n9()?e:null:e===document.documentElement?null:n6(e)?e:le(e.parentElement);var lt=e=>({x:e.scrollLeft,y:e.scrollTop});let lr=e=>!!e&&("fixed"===window.getComputedStyle(e).position||lr(e.parentElement));var ln=e=>({closestScrollable:le(e),isFixedOnPage:lr(e)}),ll=({descriptor:e,isEnabled:t,isCombineEnabled:r,isFixedOnPage:n,direction:l,client:i,page:a,closest:o})=>{let d=(()=>{if(!o)return null;let{scrollSize:e,client:t}=o,r=rv({scrollHeight:e.scrollHeight,scrollWidth:e.scrollWidth,height:t.paddingBox.height,width:t.paddingBox.width});return{pageMarginBox:o.page.marginBox,frameClient:t,scrollSize:e,shouldClipSubject:o.shouldClipSubject,scroll:{initial:o.scroll,current:o.scroll,max:r,diff:{value:C,displacement:C}}}})(),s="vertical"===l?eo:ed,u=U({page:a,withPlaceholder:null,axis:s,frame:d});return{descriptor:e,isCombineEnabled:r,isFixedOnPage:n,axis:s,isEnabled:t,client:i,page:a,frame:d,subject:u}};let li=(e,t)=>{let r=(0,o.iz)(e);if(!t||e!==t)return r;let n=r.paddingBox.top-t.scrollTop,l=r.paddingBox.left-t.scrollLeft,i=n+t.scrollHeight,a=l+t.scrollWidth,d=(0,o.jn)({top:n,right:a,bottom:i,left:l},r.border);return(0,o.dO)({borderBox:d,margin:r.margin,border:r.border,padding:r.padding})};var la=({ref:e,descriptor:t,env:r,windowScroll:n,direction:l,isDropDisabled:i,isCombineEnabled:a,shouldClipSubject:d})=>{let s=r.closestScrollable,u=li(e,s),c=(0,o.oc)(u,n),p=(()=>{if(!s)return null;let e=(0,o.iz)(s),t={scrollHeight:s.scrollHeight,scrollWidth:s.scrollWidth};return{client:e,page:(0,o.oc)(e,n),scroll:lt(s),scrollSize:t,shouldClipSubject:d}})();return ll({descriptor:t,isEnabled:!i,isCombineEnabled:a,isFixedOnPage:r.isFixedOnPage,direction:l,client:u,page:c,closest:p})};let lo={passive:!1},ld={passive:!0};var ls=e=>e.shouldPublishImmediately?lo:ld;let lu=e=>e&&e.env.closestScrollable||null;function lc(){}let lp={width:0,height:0,margin:{top:0,right:0,bottom:0,left:0}},lf=({isAnimatingOpenOnMount:e,placeholder:t,animate:r})=>e||"close"===r?lp:{height:t.client.borderBox.height,width:t.client.borderBox.width,margin:t.client.margin},lg=({isAnimatingOpenOnMount:e,placeholder:t,animate:r})=>{let n=lf({isAnimatingOpenOnMount:e,placeholder:t,animate:r});return{display:t.display,boxSizing:"border-box",width:n.width,height:n.height,marginTop:n.margin.top,marginRight:n.margin.right,marginBottom:n.margin.bottom,marginLeft:n.margin.left,flexShrink:"0",flexGrow:"0",pointerEvents:"none",transition:"none"!==r?tX.placeholder:null}};var lm=n.memo(e=>{let t=(0,n.useRef)(null),r=R(()=>{t.current&&(clearTimeout(t.current),t.current=null)},[]),{animate:l,onTransitionEnd:i,onClose:a,contextId:o}=e,[d,s]=(0,n.useState)("open"===e.animate);(0,n.useEffect)(()=>d?"open"!==l?(r(),s(!1),lc):t.current?lc:(t.current=setTimeout(()=>{t.current=null,s(!1)}),r):lc,[l,d,r]);let u=R(e=>{"height"===e.propertyName&&(i(),"close"===l&&a())},[l,a,i]),c=lg({isAnimatingOpenOnMount:d,animate:e.animate,placeholder:e.placeholder});return n.createElement(e.placeholder.tagName,{style:c,"data-rfd-placeholder-context-id":o,onTransitionEnd:u,ref:e.innerRef})});function lb(e){return"boolean"==typeof e}function lh(e,t){t.forEach(t=>t(e))}let ly=[function({props:e}){e.droppableId||h(),"string"!=typeof e.droppableId&&h()},function({props:e}){lb(e.isDropDisabled)||h(),lb(e.isCombineEnabled)||h(),lb(e.ignoreContainerClipping)||h()},function({getDroppableRef:e}){nz(e())}],lI=[function({props:e,getPlaceholderRef:t}){if(!e.placeholder||t())return}],lv=[function({props:e}){e.renderClone||h()},function({getPlaceholderRef:e}){e()&&h()}];class lx extends n.PureComponent{constructor(...e){super(...e),this.state={isVisible:!!this.props.on,data:this.props.on,animate:this.props.shouldAnimate&&this.props.on?"open":"none"},this.onClose=()=>{"close"===this.state.animate&&this.setState({isVisible:!1})}}static getDerivedStateFromProps(e,t){return e.shouldAnimate?e.on?{isVisible:!0,data:e.on,animate:"open"}:t.isVisible?{isVisible:!0,data:t.data,animate:"close"}:{isVisible:!1,animate:"close",data:null}:{isVisible:!!e.on,data:e.on,animate:"none"}}render(){if(!this.state.isVisible)return null;let e={onClose:this.onClose,data:this.state.data,animate:this.state.animate};return this.props.children(e)}}let lD={mode:"standard",type:"DEFAULT",direction:"vertical",isDropDisabled:!1,isCombineEnabled:!1,ignoreContainerClipping:!1,renderClone:null,getContainerForClone:function(){return document.body||h(),document.body}},lE=e=>{let t,r={...e};for(t in lD)void 0===e[t]&&(r={...r,[t]:lD[t]});return r},lA=(e,t)=>e===t.droppable.type,lN=(e,t)=>t.draggables[e.draggable.id],lR=(0,a.$j)(()=>{let e={placeholder:null,shouldAnimatePlaceholder:!0,snapshot:{isDraggingOver:!1,draggingOverWith:null,draggingFromThisWith:null,isUsingPlaceholder:!1},useClone:null},t={...e,shouldAnimatePlaceholder:!1},r=V(e=>({draggableId:e.id,type:e.type,source:{index:e.index,droppableId:e.droppableId}})),n=V((n,l,i,a,o,d)=>{let s=o.descriptor.id;if(o.descriptor.droppableId===n){let e=d?{render:d,dragging:r(o.descriptor)}:null;return{placeholder:o.placeholder,shouldAnimatePlaceholder:!1,snapshot:{isDraggingOver:i,draggingOverWith:i?s:null,draggingFromThisWith:s,isUsingPlaceholder:!0},useClone:e}}return l?a?{placeholder:o.placeholder,shouldAnimatePlaceholder:!0,snapshot:{isDraggingOver:i,draggingOverWith:s,draggingFromThisWith:null,isUsingPlaceholder:!0},useClone:null}:e:t});return(r,l)=>{let i=lE(l),a=i.droppableId,o=i.type,d=!i.isDropDisabled,s=i.renderClone;if(nx(r)){let e=r.critical;if(!lA(o,e))return t;let l=lN(e,r.dimensions),i=e2(r.impact)===a;return n(a,d,i,i,l,s)}if("DROP_ANIMATING"===r.phase){let e=r.completed;if(!lA(o,e.critical))return t;let l=lN(e.critical,r.dimensions);return n(a,d,nZ(e.result)===a,e2(e.impact)===a,l,s)}if("IDLE"===r.phase&&r.completed&&!r.shouldFlush){let n=r.completed;if(!lA(o,n.critical))return t;let l=e2(n.impact)===a,i=!!(n.impact.at&&"COMBINE"===n.impact.at.type),d=n.critical.droppable.id===a;if(l)return i?e:t;if(d)return e}return t}},{updateViewportMaxScroll:e=>({type:"UPDATE_VIEWPORT_MAX_SCROLL",payload:e})},(e,t,r)=>({...lE(r),...e,...t}),{context:no,areStatePropsEqual:nJ})(e=>{var t;let r=(0,n.useContext)(nf);r||h();let{contextId:i,isMovementAllowed:a}=r,o=(0,n.useRef)(null),s=(0,n.useRef)(null),{children:u,droppableId:c,type:p,mode:f,direction:g,ignoreContainerClipping:m,isDropDisabled:b,isCombineEnabled:y,snapshot:I,useClone:v,updateViewportMaxScroll:x,getContainerForClone:D}=e,E=R(()=>o.current,[]),A=R((e=null)=>{o.current=e},[]),P=R(()=>s.current,[]),w=R((e=null)=>{s.current=e},[]);t={props:e,getDroppableRef:E,getPlaceholderRef:P},()=>{lh(t,ly),"standard"===t.props.mode&&lh(t,lI),"virtual"===t.props.mode&&lh(t,lv)};let O=R(()=>{a()&&x({maxScroll:rD()})},[a,x]);!function(e){let t=(0,n.useRef)(null),r=nq(nf),l=np("droppable"),{registry:i,marshal:a}=r,o=nv(e),s=N(()=>({id:e.droppableId,type:e.type,mode:e.mode}),[e.droppableId,e.mode,e.type]),u=(0,n.useRef)(s),c=N(()=>V((e,r)=>{t.current||h(),a.updateDroppableScroll(s.id,{x:e,y:r})}),[s.id,a]),p=R(()=>{let e=t.current;return e&&e.env.closestScrollable?lt(e.env.closestScrollable):C},[]),f=R(()=>{let e=p();c(e.x,e.y)},[p,c]),g=N(()=>(0,d.Z)(f),[f]),m=R(()=>{let e=t.current,r=lu(e);if(e&&r||h(),e.scrollOptions.shouldPublishImmediately){f();return}g()},[g,f]),b=R((e,n)=>{t.current&&h();let l=o.current,i=l.getDroppableRef();i||h();let a=ln(i),d={ref:i,descriptor:s,env:a,scrollOptions:n};t.current=d;let u=la({ref:i,descriptor:s,env:a,windowScroll:e,direction:l.direction,isDropDisabled:l.isDropDisabled,isCombineEnabled:l.isCombineEnabled,shouldClipSubject:!l.ignoreContainerClipping}),c=a.closestScrollable;return c&&(c.setAttribute(r4.contextId,r.contextId),c.addEventListener("scroll",m,ls(d.scrollOptions))),u},[r.contextId,s,m,o]),y=R(()=>{let e=t.current,r=lu(e);return e&&r||h(),lt(r)},[]),I=R(()=>{let e=t.current;e||h();let r=lu(e);t.current=null,r&&(g.cancel(),r.removeAttribute(r4.contextId),r.removeEventListener("scroll",m,ls(e.scrollOptions)))},[m,g]),v=R(e=>{let r=t.current;r||h();let n=lu(r);n||h(),n.scrollTop+=e.y,n.scrollLeft+=e.x},[]),x=N(()=>({getDimensionAndWatchScroll:b,getScrollWhileDragging:y,dragStopped:I,scroll:v}),[I,b,y,v]),D=N(()=>({uniqueId:l,descriptor:s,callbacks:x}),[x,s,l]);r9(()=>(u.current=D.descriptor,i.droppable.register(D),()=>{t.current&&I(),i.droppable.unregister(D)}),[x,s,I,D,a,i.droppable]),r9(()=>{t.current&&a.updateDroppableIsEnabled(u.current.id,!e.isDropDisabled)},[e.isDropDisabled,a]),r9(()=>{t.current&&a.updateDroppableIsCombineEnabled(u.current.id,e.isCombineEnabled)},[e.isCombineEnabled,a])}({droppableId:c,type:p,mode:f,direction:g,isDropDisabled:b,isCombineEnabled:y,ignoreContainerClipping:m,getDroppableRef:E});let S=N(()=>n.createElement(lx,{on:e.placeholder,shouldAnimate:e.shouldAnimatePlaceholder},({onClose:e,data:t,animate:r})=>n.createElement(lm,{placeholder:t,onClose:e,innerRef:w,animate:r,contextId:i,onTransitionEnd:O})),[i,O,e.placeholder,e.shouldAnimatePlaceholder,w]),B=N(()=>({innerRef:A,placeholder:S,droppableProps:{"data-rfd-droppable-id":c,"data-rfd-droppable-context-id":i}}),[i,c,S,A]),G=v?v.dragging.draggableId:null,L=N(()=>({droppableId:c,type:p,isUsingCloneFor:G}),[c,G,p]);return n.createElement(nj.Provider,{value:L},u(B,I),function(){if(!v)return null;let{dragging:e,render:t}=v,r=n.createElement(n2,{draggableId:e.draggableId,index:e.source.index,isClone:!0,isEnabled:!0,shouldRespectForcePress:!1,canDragInteractiveElements:!0},(r,n)=>t(r,n,e));return l.createPortal(r,D())}())})}}]);