'use client'

import { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  FileText,
  DollarSign,
  TrendingUp,
  Clock,
  Target,
  Users,
  Calendar,
  RefreshCw,
  BarChart3,
  CheckCircle,
  AlertTriangle,
  FileSignature,
  Repeat,
  Timer
} from 'lucide-react'
import { toast } from 'react-hot-toast'

interface ContractAnalytics {
  summary: {
    totalContracts: number
    activeContracts: number
    signedContracts: number
    pendingSignatures: number
    autoRenewalContracts: number
    totalValue: number
    activeValue: number
    averageValue: number
    avgDurationDays: number
    avgSignatureDays: number
    signatureRate: number
  }
  contractsByStatus: Array<{
    status: string
    count: number
    value: number
  }>
  contractsByType: Array<{
    type: string
    count: number
    value: number
  }>
  contractsByMonth: Array<{
    month: string
    contract_count: number
    total_value: number
    signed_count: number
    active_count: number
  }>
  renewalAnalysis: {
    expiring30Days: number
    expiring90Days: number
    autoRenewalValue: number
    avgDurationDays: number
  }
  topContracts: Array<{
    id: string
    contractNumber: string
    title: string
    value: number
    status: string
    type: string
    customer: {
      id: string
      name: string
      company: string | null
      email: string | null
    }
    createdBy: {
      name: string | null
      email: string | null
    }
    createdAt: string
    startDate: string | null
    endDate: string | null
  }>
  recentContracts: Array<{
    id: string
    contractNumber: string
    title: string
    value: number
    status: string
    type: string
    customer: {
      id: string
      name: string
      company: string | null
    }
    createdBy: {
      name: string | null
      email: string | null
    }
    createdAt: string
  }>
  customerContracts: Array<{
    customer: {
      id: string
      name: string
      company: string | null
      email: string | null
    }
    contractCount: number
    totalValue: number
  }>
  expiringContracts: Array<{
    id: string
    contractNumber: string
    title: string
    value: number
    endDate: string
    daysUntilExpiry: number
    autoRenewal: boolean
    customer: {
      id: string
      name: string
      company: string | null
      email: string | null
    }
  }>
  period: number
}

export function ContractAnalytics() {
  const [analytics, setAnalytics] = useState<ContractAnalytics | null>(null)
  const [loading, setLoading] = useState(true)
  const [period, setPeriod] = useState('30')

  const fetchAnalytics = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/contracts/analytics?period=${period}`)
      if (!response.ok) {
        throw new Error('Failed to fetch analytics')
      }

      const data = await response.json()
      setAnalytics(data)
    } catch (error) {
      toast.error('Failed to load contract analytics')
      console.error('Error fetching analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchAnalytics()
  }, [period])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DRAFT':
        return 'bg-gray-100 text-gray-800'
      case 'REVIEW':
        return 'bg-yellow-100 text-yellow-800'
      case 'SENT':
        return 'bg-blue-100 text-blue-800'
      case 'SIGNED':
        return 'bg-green-100 text-green-800'
      case 'ACTIVE':
        return 'bg-emerald-100 text-emerald-800'
      case 'COMPLETED':
        return 'bg-purple-100 text-purple-800'
      case 'CANCELLED':
        return 'bg-red-100 text-red-800'
      case 'EXPIRED':
        return 'bg-orange-100 text-orange-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'SERVICE':
        return 'bg-blue-100 text-blue-800'
      case 'PRODUCT':
        return 'bg-green-100 text-green-800'
      case 'SUBSCRIPTION':
        return 'bg-purple-100 text-purple-800'
      case 'MAINTENANCE':
        return 'bg-orange-100 text-orange-800'
      case 'CONSULTING':
        return 'bg-indigo-100 text-indigo-800'
      case 'OTHER':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!analytics) {
    return (
      <div className="text-center py-8 text-gray-500">
        <BarChart3 className="h-12 w-12 mx-auto mb-4 text-gray-300" />
        <p>Failed to load analytics data</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Contract Analytics</h3>
        <div className="flex items-center space-x-2">
          <Select value={period} onValueChange={setPeriod}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">Last 7 days</SelectItem>
              <SelectItem value="30">Last 30 days</SelectItem>
              <SelectItem value="90">Last 90 days</SelectItem>
              <SelectItem value="365">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={fetchAnalytics} size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-blue-100 rounded-full">
                <FileText className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Total Contracts</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.summary.totalContracts}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-green-100 rounded-full">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Active Contracts</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.summary.activeContracts}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-purple-100 rounded-full">
                <DollarSign className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Total Value</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(analytics.summary.totalValue)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-orange-100 rounded-full">
                <FileSignature className="h-6 w-6 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Pending Signatures</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.summary.pendingSignatures}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-indigo-100 rounded-full">
                <Target className="h-6 w-6 text-indigo-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Signature Rate</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analytics.summary.signatureRate.toFixed(1)}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-pink-100 rounded-full">
                <Timer className="h-6 w-6 text-pink-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Avg Time to Sign</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analytics.summary.avgSignatureDays.toFixed(1)}d
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-teal-100 rounded-full">
                <Clock className="h-6 w-6 text-teal-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Avg Duration</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analytics.summary.avgDurationDays}d
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-emerald-100 rounded-full">
                <Repeat className="h-6 w-6 text-emerald-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Auto-Renewal</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.summary.autoRenewalContracts}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Value Metrics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <DollarSign className="h-5 w-5 mr-2" />
            Contract Value Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">
                {formatCurrency(analytics.summary.activeValue)}
              </p>
              <p className="text-sm text-gray-500">Active Contract Value</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600">
                {formatCurrency(analytics.summary.averageValue)}
              </p>
              <p className="text-sm text-gray-500">Average Contract Value</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-purple-600">
                {formatCurrency(analytics.renewalAnalysis.autoRenewalValue)}
              </p>
              <p className="text-sm text-gray-500">Auto-Renewal Value</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Charts and Breakdowns */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Contracts by Status */}
        <Card>
          <CardHeader>
            <CardTitle>Contracts by Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.contractsByStatus.map((item) => (
                <div key={item.status} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Badge className={getStatusColor(item.status)}>
                      {item.status}
                    </Badge>
                  </div>
                  <div className="text-right">
                    <span className="font-semibold">{item.count}</span>
                    <p className="text-sm text-gray-500">
                      {formatCurrency(item.value)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Contracts by Type */}
        <Card>
          <CardHeader>
            <CardTitle>Contracts by Type</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.contractsByType.map((item) => (
                <div key={item.type} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Badge className={getTypeColor(item.type)}>
                      {item.type}
                    </Badge>
                  </div>
                  <div className="text-right">
                    <span className="font-semibold">{item.count}</span>
                    <p className="text-sm text-gray-500">
                      {formatCurrency(item.value)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Renewal Analysis */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="h-5 w-5 mr-2" />
            Renewal & Expiration Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-orange-600">
                {analytics.renewalAnalysis.expiring30Days}
              </p>
              <p className="text-sm text-gray-500">Expiring in 30 Days</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-yellow-600">
                {analytics.renewalAnalysis.expiring90Days}
              </p>
              <p className="text-sm text-gray-500">Expiring in 90 Days</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">
                {analytics.summary.autoRenewalContracts}
              </p>
              <p className="text-sm text-gray-500">Auto-Renewal Contracts</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600">
                {analytics.renewalAnalysis.avgDurationDays}
              </p>
              <p className="text-sm text-gray-500">Avg Duration (Days)</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Top Customers */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Users className="h-5 w-5 mr-2" />
            Top Customers by Contract Value
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {analytics.customerContracts.slice(0, 5).map((item, index) => (
              <div key={item.customer.id} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-xs font-semibold text-blue-600">#{index + 1}</span>
                  </div>
                  <div>
                    <p className="font-medium">{item.customer.name}</p>
                    <p className="text-sm text-gray-500">{item.customer.company}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-green-600">
                    {formatCurrency(item.totalValue)}
                  </p>
                  <p className="text-sm text-gray-500">
                    {item.contractCount} contracts
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Expiring Contracts */}
      {analytics.expiringContracts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2 text-orange-600" />
              Contracts Expiring Soon
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.expiringContracts.slice(0, 5).map((contract) => (
                <div key={contract.id} className="flex items-center justify-between p-3 bg-orange-50 rounded-lg border border-orange-200">
                  <div>
                    <p className="font-medium">{contract.contractNumber}</p>
                    <p className="text-sm text-gray-600">{contract.title}</p>
                    <p className="text-sm text-gray-500">{contract.customer.name}</p>
                    <div className="flex items-center space-x-2 mt-1">
                      <p className="text-sm text-orange-600">
                        Expires in {contract.daysUntilExpiry} days
                      </p>
                      {contract.autoRenewal && (
                        <Badge variant="outline" className="text-xs">
                          Auto-Renewal
                        </Badge>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-green-600">
                      {formatCurrency(contract.value)}
                    </p>
                    <p className="text-sm text-gray-500">
                      {new Date(contract.endDate).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recent Contracts */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="h-5 w-5 mr-2" />
            Recent Contracts (Last 7 Days)
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {analytics.recentContracts.length === 0 ? (
              <p className="text-gray-500 text-center py-4">No recent contracts</p>
            ) : (
              analytics.recentContracts.map((contract) => (
                <div key={contract.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium">{contract.contractNumber}</p>
                    <p className="text-sm text-gray-600">{contract.title}</p>
                    <p className="text-sm text-gray-500">
                      {contract.customer.name} • {new Date(contract.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-green-600">
                      {formatCurrency(contract.value)}
                    </p>
                    <div className="flex items-center space-x-2">
                      <Badge className={getStatusColor(contract.status)} variant="outline">
                        {contract.status}
                      </Badge>
                      <Badge className={getTypeColor(contract.type)} variant="outline">
                        {contract.type}
                      </Badge>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
