"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/quotations/page",{

/***/ "(app-pages-browser)/./components/quotations/quotation-form.tsx":
/*!**************************************************!*\
  !*** ./components/quotations/quotation-form.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuotationForm: function() { return /* binding */ QuotationForm; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/v3/types.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calculator_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ QuotationForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst quotationItemSchema = zod__WEBPACK_IMPORTED_MODULE_8__.object({\n    description: zod__WEBPACK_IMPORTED_MODULE_8__.string().min(1, \"Description is required\"),\n    quantity: zod__WEBPACK_IMPORTED_MODULE_8__.number().min(1, \"Quantity must be at least 1\"),\n    unitPrice: zod__WEBPACK_IMPORTED_MODULE_8__.number().min(0, \"Unit price must be positive\"),\n    discount: zod__WEBPACK_IMPORTED_MODULE_8__.number().min(0).max(100).default(0),\n    taxRate: zod__WEBPACK_IMPORTED_MODULE_8__.number().min(0).max(100).default(0)\n});\nconst quotationSchema = zod__WEBPACK_IMPORTED_MODULE_8__.object({\n    title: zod__WEBPACK_IMPORTED_MODULE_8__.string().min(1, \"Title is required\"),\n    description: zod__WEBPACK_IMPORTED_MODULE_8__.string().optional(),\n    customerId: zod__WEBPACK_IMPORTED_MODULE_8__.string().min(1, \"Customer is required\"),\n    leadId: zod__WEBPACK_IMPORTED_MODULE_8__.string().optional(),\n    status: zod__WEBPACK_IMPORTED_MODULE_8__[\"enum\"]([\n        \"DRAFT\",\n        \"SENT\",\n        \"VIEWED\",\n        \"ACCEPTED\",\n        \"REJECTED\",\n        \"EXPIRED\"\n    ]).default(\"DRAFT\"),\n    validUntil: zod__WEBPACK_IMPORTED_MODULE_8__.string().optional(),\n    terms: zod__WEBPACK_IMPORTED_MODULE_8__.string().optional(),\n    notes: zod__WEBPACK_IMPORTED_MODULE_8__.string().optional(),\n    paymentTerms: zod__WEBPACK_IMPORTED_MODULE_8__.string().optional(),\n    paymentDueDays: zod__WEBPACK_IMPORTED_MODULE_8__.number().min(0).default(30),\n    items: zod__WEBPACK_IMPORTED_MODULE_8__.array(quotationItemSchema).min(1, \"At least one item is required\"),\n    taxRate: zod__WEBPACK_IMPORTED_MODULE_8__.number().min(0).max(100).default(0),\n    discountType: zod__WEBPACK_IMPORTED_MODULE_8__[\"enum\"]([\n        \"PERCENTAGE\",\n        \"FIXED\"\n    ]).default(\"PERCENTAGE\"),\n    discountValue: zod__WEBPACK_IMPORTED_MODULE_8__.number().min(0).default(0)\n});\nfunction QuotationForm(param) {\n    let { isOpen, onClose, onSuccess, quotation, mode, preselectedCustomerId, preselectedLeadId } = param;\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [customers, setCustomers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [leads, setLeads] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const { register, handleSubmit, formState: { errors }, reset, watch, control, setValue } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_9__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(quotationSchema),\n        defaultValues: quotation ? {\n            title: quotation.title,\n            description: quotation.description || \"\",\n            customerId: quotation.customerId || preselectedCustomerId || \"\",\n            leadId: quotation.leadId || preselectedLeadId || \"\",\n            status: quotation.status || \"DRAFT\",\n            validUntil: quotation.validUntil ? new Date(quotation.validUntil).toISOString().split(\"T\")[0] : \"\",\n            terms: quotation.terms || \"\",\n            notes: quotation.notes || \"\",\n            items: quotation.items || [\n                {\n                    description: \"\",\n                    quantity: 1,\n                    unitPrice: 0,\n                    discount: 0,\n                    taxRate: 0\n                }\n            ],\n            taxRate: quotation.taxRate || 0,\n            discountType: quotation.discountType || \"PERCENTAGE\",\n            discountValue: quotation.discountValue || 0\n        } : {\n            status: \"DRAFT\",\n            customerId: preselectedCustomerId || \"\",\n            leadId: preselectedLeadId || \"\",\n            items: [\n                {\n                    description: \"\",\n                    quantity: 1,\n                    unitPrice: 0,\n                    discount: 0,\n                    taxRate: 0\n                }\n            ],\n            taxRate: 0,\n            discountType: \"PERCENTAGE\",\n            discountValue: 0\n        }\n    });\n    const { fields, append, remove } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_9__.useFieldArray)({\n        control,\n        name: \"items\"\n    });\n    const watchedItems = watch(\"items\");\n    const watchedTaxRate = watch(\"taxRate\");\n    const watchedDiscountType = watch(\"discountType\");\n    const watchedDiscountValue = watch(\"discountValue\");\n    // Fetch customers and leads\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchData = async ()=>{\n            try {\n                const [customersRes, leadsRes] = await Promise.all([\n                    fetch(\"/api/customers?limit=100\"),\n                    fetch(\"/api/leads?limit=100\")\n                ]);\n                if (customersRes.ok) {\n                    const customersData = await customersRes.json();\n                    setCustomers(customersData.customers);\n                }\n                if (leadsRes.ok) {\n                    const leadsData = await leadsRes.json();\n                    setLeads(leadsData.leads);\n                }\n            } catch (error) {\n                console.error(\"Error fetching data:\", error);\n            }\n        };\n        if (isOpen) {\n            fetchData();\n        }\n    }, [\n        isOpen\n    ]);\n    // Calculate totals\n    const calculateTotals = ()=>{\n        const subtotal = watchedItems.reduce((sum, item)=>{\n            const itemTotal = (item.quantity || 0) * (item.unitPrice || 0);\n            const discountAmount = itemTotal * (item.discount || 0) / 100;\n            const afterDiscount = itemTotal - discountAmount;\n            const taxAmount = afterDiscount * (item.taxRate || 0) / 100;\n            return sum + afterDiscount + taxAmount;\n        }, 0);\n        let total = subtotal;\n        if (watchedDiscountType === \"PERCENTAGE\") {\n            total = subtotal - subtotal * (watchedDiscountValue || 0) / 100;\n        } else {\n            total = subtotal - (watchedDiscountValue || 0);\n        }\n        const finalTaxAmount = total * (watchedTaxRate || 0) / 100;\n        const finalTotal = total + finalTaxAmount;\n        return {\n            subtotal: Math.round(subtotal * 100) / 100,\n            total: Math.round(finalTotal * 100) / 100,\n            taxAmount: Math.round(finalTaxAmount * 100) / 100,\n            discountAmount: watchedDiscountType === \"PERCENTAGE\" ? Math.round(subtotal * (watchedDiscountValue || 0) / 100 * 100) / 100 : watchedDiscountValue || 0\n        };\n    };\n    const totals = calculateTotals();\n    const addItem = ()=>{\n        append({\n            description: \"\",\n            quantity: 1,\n            unitPrice: 0,\n            discount: 0,\n            taxRate: 0\n        });\n    };\n    const removeItem = (index)=>{\n        if (fields.length > 1) {\n            remove(index);\n        }\n    };\n    const onSubmit = async (data)=>{\n        setIsLoading(true);\n        try {\n            const url = mode === \"create\" ? \"/api/quotations\" : \"/api/quotations/\".concat(quotation.id);\n            const method = mode === \"create\" ? \"POST\" : \"PUT\";\n            const response = await fetch(url, {\n                method,\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(data)\n            });\n            if (!response.ok) {\n                const error = await response.json();\n                throw new Error(error.error || \"Failed to save quotation\");\n            }\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Quotation \".concat(mode === \"create\" ? \"created\" : \"updated\", \" successfully\"));\n            reset();\n            onSuccess();\n            onClose();\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_7__.toast.error(error instanceof Error ? error.message : \"An error occurred\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleClose = ()=>{\n        reset();\n        onClose();\n    };\n    const statusOptions = [\n        {\n            value: \"DRAFT\",\n            label: \"Draft\"\n        },\n        {\n            value: \"SENT\",\n            label: \"Sent\"\n        },\n        {\n            value: \"VIEWED\",\n            label: \"Viewed\"\n        },\n        {\n            value: \"ACCEPTED\",\n            label: \"Accepted\"\n        },\n        {\n            value: \"REJECTED\",\n            label: \"Rejected\"\n        },\n        {\n            value: \"EXPIRED\",\n            label: \"Expired\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.Dialog, {\n        open: isOpen,\n        onOpenChange: handleClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogContent, {\n            className: \"max-w-6xl max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogTitle, {\n                            children: mode === \"create\" ? \"Create New Quotation\" : \"Edit Quotation\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogDescription, {\n                            children: mode === \"create\" ? \"Create a new quotation with items, pricing, and terms.\" : \"Update the quotation information and items.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit(onSubmit),\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            htmlFor: \"title\",\n                                            children: \"Title *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            id: \"title\",\n                                            ...register(\"title\"),\n                                            placeholder: \"Quotation title\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 15\n                                        }, this),\n                                        errors.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-600 mt-1\",\n                                            children: errors.title.message\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            htmlFor: \"status\",\n                                            children: \"Status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            id: \"status\",\n                                            ...register(\"status\"),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: statusOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: option.value,\n                                                    children: option.label\n                                                }, option.value, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            htmlFor: \"customerId\",\n                                            children: \"Customer *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            id: \"customerId\",\n                                            ...register(\"customerId\"),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select a customer\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 17\n                                                }, this),\n                                                customers.map((customer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: customer.id,\n                                                        children: [\n                                                            customer.name,\n                                                            \" \",\n                                                            customer.company && \"(\".concat(customer.company, \")\")\n                                                        ]\n                                                    }, customer.id, true, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 15\n                                        }, this),\n                                        errors.customerId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-600 mt-1\",\n                                            children: errors.customerId.message\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            htmlFor: \"leadId\",\n                                            children: \"Related Lead\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            id: \"leadId\",\n                                            ...register(\"leadId\"),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select a lead (optional)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 17\n                                                }, this),\n                                                leads.map((lead)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: lead.id,\n                                                        children: lead.name\n                                                    }, lead.id, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            htmlFor: \"validUntil\",\n                                            children: \"Valid Until\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            id: \"validUntil\",\n                                            type: \"date\",\n                                            ...register(\"validUntil\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                    htmlFor: \"description\",\n                                    children: \"Description\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    id: \"description\",\n                                    ...register(\"description\"),\n                                    rows: 3,\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                    placeholder: \"Brief description of the quotation...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium\",\n                                            children: \"Items\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            type: \"button\",\n                                            onClick: addItem,\n                                            size: \"sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Add Item\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: fields.map((field, index)=>{\n                                        var _errors_items_index, _errors_items, _errors_items_index_description, _errors_items_index1;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-12 gap-2 items-end p-4 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            htmlFor: \"items.\".concat(index, \".description\"),\n                                                            children: \"Description *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            ...register(\"items.\".concat(index, \".description\")),\n                                                            placeholder: \"Item description\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        ((_errors_items = errors.items) === null || _errors_items === void 0 ? void 0 : (_errors_items_index = _errors_items[index]) === null || _errors_items_index === void 0 ? void 0 : _errors_items_index.description) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-red-600 mt-1\",\n                                                            children: (_errors_items_index1 = errors.items[index]) === null || _errors_items_index1 === void 0 ? void 0 : (_errors_items_index_description = _errors_items_index1.description) === null || _errors_items_index_description === void 0 ? void 0 : _errors_items_index_description.message\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 348,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            htmlFor: \"items.\".concat(index, \".quantity\"),\n                                                            children: \"Qty *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 355,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            type: \"number\",\n                                                            min: \"1\",\n                                                            ...register(\"items.\".concat(index, \".quantity\"), {\n                                                                valueAsNumber: true\n                                                            }),\n                                                            placeholder: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 356,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                    lineNumber: 354,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            htmlFor: \"items.\".concat(index, \".unitPrice\"),\n                                                            children: \"Unit Price *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            type: \"number\",\n                                                            min: \"0\",\n                                                            step: \"0.01\",\n                                                            ...register(\"items.\".concat(index, \".unitPrice\"), {\n                                                                valueAsNumber: true\n                                                            }),\n                                                            placeholder: \"0.00\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            htmlFor: \"items.\".concat(index, \".discount\"),\n                                                            children: \"Disc %\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            type: \"number\",\n                                                            min: \"0\",\n                                                            max: \"100\",\n                                                            ...register(\"items.\".concat(index, \".discount\"), {\n                                                                valueAsNumber: true\n                                                            }),\n                                                            placeholder: \"0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            htmlFor: \"items.\".concat(index, \".taxRate\"),\n                                                            children: \"Tax %\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            type: \"number\",\n                                                            min: \"0\",\n                                                            max: \"100\",\n                                                            ...register(\"items.\".concat(index, \".taxRate\"), {\n                                                                valueAsNumber: true\n                                                            }),\n                                                            placeholder: \"0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            children: \"Total\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 398,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"px-3 py-2 bg-gray-50 rounded-md text-sm\",\n                                                            children: [\n                                                                \"$\",\n                                                                (()=>{\n                                                                    const item = watchedItems[index];\n                                                                    if (!item) return \"0.00\";\n                                                                    const itemTotal = (item.quantity || 0) * (item.unitPrice || 0);\n                                                                    const discountAmount = itemTotal * (item.discount || 0) / 100;\n                                                                    const afterDiscount = itemTotal - discountAmount;\n                                                                    const taxAmount = afterDiscount * (item.taxRate || 0) / 100;\n                                                                    return (afterDiscount + taxAmount).toFixed(2);\n                                                                })()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        type: \"button\",\n                                                        variant: \"destructive\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>removeItem(index),\n                                                        disabled: fields.length === 1,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 420,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, field.id, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 17\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium\",\n                                            children: \"Settings\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            htmlFor: \"discountType\",\n                                                            children: \"Discount Type\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            id: \"discountType\",\n                                                            ...register(\"discountType\"),\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"PERCENTAGE\",\n                                                                    children: \"Percentage\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                                    lineNumber: 441,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"FIXED\",\n                                                                    children: \"Fixed Amount\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                                    lineNumber: 442,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            htmlFor: \"discountValue\",\n                                                            children: [\n                                                                \"Discount \",\n                                                                watchedDiscountType === \"PERCENTAGE\" ? \"%\" : \"$\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 447,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            type: \"number\",\n                                                            min: \"0\",\n                                                            step: \"0.01\",\n                                                            ...register(\"discountValue\", {\n                                                                valueAsNumber: true\n                                                            }),\n                                                            placeholder: \"0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 450,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"taxRate\",\n                                                    children: \"Overall Tax Rate (%)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    max: \"100\",\n                                                    step: \"0.01\",\n                                                    ...register(\"taxRate\", {\n                                                        valueAsNumber: true\n                                                    }),\n                                                    placeholder: \"0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                    lineNumber: 462,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                    lineNumber: 430,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Totals\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 p-4 bg-gray-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Subtotal:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 481,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"$\",\n                                                                totals.subtotal.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 17\n                                                }, this),\n                                                totals.discountAmount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-red-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Discount:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 487,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"-$\",\n                                                                totals.discountAmount.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 488,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 19\n                                                }, this),\n                                                totals.taxAmount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Tax:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 494,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"$\",\n                                                                totals.taxAmount.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 495,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                    lineNumber: 493,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between font-bold text-lg border-t pt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Total:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 500,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"$\",\n                                                                totals.total.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                            lineNumber: 501,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 479,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                    lineNumber: 473,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                            lineNumber: 429,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            htmlFor: \"paymentTerms\",\n                                            children: \"Payment Terms\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 510,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            id: \"paymentTerms\",\n                                            ...register(\"paymentTerms\"),\n                                            rows: 3,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"Net 30, 2/10 Net 30, Payment due upon receipt, etc...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 511,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                    lineNumber: 509,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            htmlFor: \"paymentDueDays\",\n                                            children: \"Payment Due (Days)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            id: \"paymentDueDays\",\n                                            type: \"number\",\n                                            min: \"0\",\n                                            ...register(\"paymentDueDays\", {\n                                                valueAsNumber: true\n                                            }),\n                                            placeholder: \"30\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 522,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500 mt-1\",\n                                            children: \"Number of days after invoice date\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 529,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                            lineNumber: 508,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            htmlFor: \"terms\",\n                                            children: \"Terms & Conditions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 536,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            id: \"terms\",\n                                            ...register(\"terms\"),\n                                            rows: 4,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"General terms, delivery conditions, warranties, etc...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            htmlFor: \"notes\",\n                                            children: \"Internal Notes\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 547,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            id: \"notes\",\n                                            ...register(\"notes\"),\n                                            rows: 4,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"Internal notes (not visible to customer)...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                            lineNumber: 548,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                    lineNumber: 546,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                            lineNumber: 534,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    type: \"button\",\n                                    variant: \"outline\",\n                                    onClick: handleClose,\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                    lineNumber: 559,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    type: \"submit\",\n                                    disabled: isLoading,\n                                    children: isLoading ? \"Saving...\" : mode === \"create\" ? \"Create Quotation\" : \"Update Quotation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                                    lineNumber: 562,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                            lineNumber: 558,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n            lineNumber: 229,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\quotations\\\\quotation-form.tsx\",\n        lineNumber: 228,\n        columnNumber: 5\n    }, this);\n}\n_s(QuotationForm, \"1Ky/yd/swgE2r/wOn58sNmnXBqg=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_9__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_9__.useFieldArray\n    ];\n});\n_c = QuotationForm;\nvar _c;\n$RefreshReg$(_c, \"QuotationForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvcXVvdGF0aW9ucy9xdW90YXRpb24tZm9ybS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFMkM7QUFDYTtBQUNIO0FBQzlCO0FBQ3dCO0FBQ0Y7QUFDQTtBQVFkO0FBQ3dCO0FBQ2hCO0FBRXZDLE1BQU1tQixzQkFBc0JkLHVDQUFRLENBQUM7SUFDbkNnQixhQUFhaEIsdUNBQVEsR0FBR2tCLEdBQUcsQ0FBQyxHQUFHO0lBQy9CQyxVQUFVbkIsdUNBQVEsR0FBR2tCLEdBQUcsQ0FBQyxHQUFHO0lBQzVCRyxXQUFXckIsdUNBQVEsR0FBR2tCLEdBQUcsQ0FBQyxHQUFHO0lBQzdCSSxVQUFVdEIsdUNBQVEsR0FBR2tCLEdBQUcsQ0FBQyxHQUFHSyxHQUFHLENBQUMsS0FBS0MsT0FBTyxDQUFDO0lBQzdDQyxTQUFTekIsdUNBQVEsR0FBR2tCLEdBQUcsQ0FBQyxHQUFHSyxHQUFHLENBQUMsS0FBS0MsT0FBTyxDQUFDO0FBQzlDO0FBRUEsTUFBTUUsa0JBQWtCMUIsdUNBQVEsQ0FBQztJQUMvQjJCLE9BQU8zQix1Q0FBUSxHQUFHa0IsR0FBRyxDQUFDLEdBQUc7SUFDekJGLGFBQWFoQix1Q0FBUSxHQUFHNEIsUUFBUTtJQUNoQ0MsWUFBWTdCLHVDQUFRLEdBQUdrQixHQUFHLENBQUMsR0FBRztJQUM5QlksUUFBUTlCLHVDQUFRLEdBQUc0QixRQUFRO0lBQzNCRyxRQUFRL0Isd0NBQU0sQ0FBQztRQUFDO1FBQVM7UUFBUTtRQUFVO1FBQVk7UUFBWTtLQUFVLEVBQUV3QixPQUFPLENBQUM7SUFDdkZTLFlBQVlqQyx1Q0FBUSxHQUFHNEIsUUFBUTtJQUMvQk0sT0FBT2xDLHVDQUFRLEdBQUc0QixRQUFRO0lBQzFCTyxPQUFPbkMsdUNBQVEsR0FBRzRCLFFBQVE7SUFDMUJRLGNBQWNwQyx1Q0FBUSxHQUFHNEIsUUFBUTtJQUNqQ1MsZ0JBQWdCckMsdUNBQVEsR0FBR2tCLEdBQUcsQ0FBQyxHQUFHTSxPQUFPLENBQUM7SUFDMUNjLE9BQU90QyxzQ0FBTyxDQUFDYyxxQkFBcUJJLEdBQUcsQ0FBQyxHQUFHO0lBQzNDTyxTQUFTekIsdUNBQVEsR0FBR2tCLEdBQUcsQ0FBQyxHQUFHSyxHQUFHLENBQUMsS0FBS0MsT0FBTyxDQUFDO0lBQzVDZ0IsY0FBY3hDLHdDQUFNLENBQUM7UUFBQztRQUFjO0tBQVEsRUFBRXdCLE9BQU8sQ0FBQztJQUN0RGlCLGVBQWV6Qyx1Q0FBUSxHQUFHa0IsR0FBRyxDQUFDLEdBQUdNLE9BQU8sQ0FBQztBQUMzQztBQWNPLFNBQVNrQixjQUFjLEtBUVQ7UUFSUyxFQUM1QkMsTUFBTSxFQUNOQyxPQUFPLEVBQ1BDLFNBQVMsRUFDVEMsU0FBUyxFQUNUQyxJQUFJLEVBQ0pDLHFCQUFxQixFQUNyQkMsaUJBQWlCLEVBQ0UsR0FSUzs7SUFTNUIsTUFBTSxDQUFDQyxXQUFXQyxhQUFhLEdBQUd4RCwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUN5RCxXQUFXQyxhQUFhLEdBQUcxRCwrQ0FBUUEsQ0FBUSxFQUFFO0lBQ3BELE1BQU0sQ0FBQzJELE9BQU9DLFNBQVMsR0FBRzVELCtDQUFRQSxDQUFRLEVBQUU7SUFFNUMsTUFBTSxFQUNKNkQsUUFBUSxFQUNSQyxZQUFZLEVBQ1pDLFdBQVcsRUFBRUMsTUFBTSxFQUFFLEVBQ3JCQyxLQUFLLEVBQ0xDLEtBQUssRUFDTEMsT0FBTyxFQUNQQyxRQUFRLEVBQ1QsR0FBR2xFLHdEQUFPQSxDQUFvQjtRQUM3Qm1FLFVBQVVqRSxvRUFBV0EsQ0FBQzJCO1FBQ3RCdUMsZUFBZW5CLFlBQVk7WUFDekJuQixPQUFPbUIsVUFBVW5CLEtBQUs7WUFDdEJYLGFBQWE4QixVQUFVOUIsV0FBVyxJQUFJO1lBQ3RDYSxZQUFZaUIsVUFBVWpCLFVBQVUsSUFBSW1CLHlCQUF5QjtZQUM3RGxCLFFBQVFnQixVQUFVaEIsTUFBTSxJQUFJbUIscUJBQXFCO1lBQ2pEbEIsUUFBUWUsVUFBVWYsTUFBTSxJQUFJO1lBQzVCRSxZQUFZYSxVQUFVYixVQUFVLEdBQUcsSUFBSWlDLEtBQUtwQixVQUFVYixVQUFVLEVBQUVrQyxXQUFXLEdBQUdDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRSxHQUFHO1lBQ2hHbEMsT0FBT1ksVUFBVVosS0FBSyxJQUFJO1lBQzFCQyxPQUFPVyxVQUFVWCxLQUFLLElBQUk7WUFDMUJHLE9BQU9RLFVBQVVSLEtBQUssSUFBSTtnQkFBQztvQkFBRXRCLGFBQWE7b0JBQUlHLFVBQVU7b0JBQUdFLFdBQVc7b0JBQUdDLFVBQVU7b0JBQUdHLFNBQVM7Z0JBQUU7YUFBRTtZQUNuR0EsU0FBU3FCLFVBQVVyQixPQUFPLElBQUk7WUFDOUJlLGNBQWNNLFVBQVVOLFlBQVksSUFBSTtZQUN4Q0MsZUFBZUssVUFBVUwsYUFBYSxJQUFJO1FBQzVDLElBQUk7WUFDRlYsUUFBUTtZQUNSRixZQUFZbUIseUJBQXlCO1lBQ3JDbEIsUUFBUW1CLHFCQUFxQjtZQUM3QlgsT0FBTztnQkFBQztvQkFBRXRCLGFBQWE7b0JBQUlHLFVBQVU7b0JBQUdFLFdBQVc7b0JBQUdDLFVBQVU7b0JBQUdHLFNBQVM7Z0JBQUU7YUFBRTtZQUNoRkEsU0FBUztZQUNUZSxjQUFjO1lBQ2RDLGVBQWU7UUFDakI7SUFDRjtJQUVBLE1BQU0sRUFBRTRCLE1BQU0sRUFBRUMsTUFBTSxFQUFFQyxNQUFNLEVBQUUsR0FBR3pFLDhEQUFhQSxDQUFDO1FBQy9DZ0U7UUFDQVUsTUFBTTtJQUNSO0lBRUEsTUFBTUMsZUFBZVosTUFBTTtJQUMzQixNQUFNYSxpQkFBaUJiLE1BQU07SUFDN0IsTUFBTWMsc0JBQXNCZCxNQUFNO0lBQ2xDLE1BQU1lLHVCQUF1QmYsTUFBTTtJQUVuQyw0QkFBNEI7SUFDNUJqRSxnREFBU0EsQ0FBQztRQUNSLE1BQU1pRixZQUFZO1lBQ2hCLElBQUk7Z0JBQ0YsTUFBTSxDQUFDQyxjQUFjQyxTQUFTLEdBQUcsTUFBTUMsUUFBUUMsR0FBRyxDQUFDO29CQUNqREMsTUFBTTtvQkFDTkEsTUFBTTtpQkFDUDtnQkFFRCxJQUFJSixhQUFhSyxFQUFFLEVBQUU7b0JBQ25CLE1BQU1DLGdCQUFnQixNQUFNTixhQUFhTyxJQUFJO29CQUM3Q2hDLGFBQWErQixjQUFjaEMsU0FBUztnQkFDdEM7Z0JBRUEsSUFBSTJCLFNBQVNJLEVBQUUsRUFBRTtvQkFDZixNQUFNRyxZQUFZLE1BQU1QLFNBQVNNLElBQUk7b0JBQ3JDOUIsU0FBUytCLFVBQVVoQyxLQUFLO2dCQUMxQjtZQUNGLEVBQUUsT0FBT2lDLE9BQU87Z0JBQ2RDLFFBQVFELEtBQUssQ0FBQyx3QkFBd0JBO1lBQ3hDO1FBQ0Y7UUFFQSxJQUFJNUMsUUFBUTtZQUNWa0M7UUFDRjtJQUNGLEdBQUc7UUFBQ2xDO0tBQU87SUFFWCxtQkFBbUI7SUFDbkIsTUFBTThDLGtCQUFrQjtRQUN0QixNQUFNQyxXQUFXakIsYUFBYWtCLE1BQU0sQ0FBQyxDQUFDQyxLQUFLQztZQUN6QyxNQUFNQyxZQUFZLENBQUNELEtBQUsxRSxRQUFRLElBQUksS0FBTTBFLENBQUFBLEtBQUt4RSxTQUFTLElBQUk7WUFDNUQsTUFBTTBFLGlCQUFpQixZQUFjRixDQUFBQSxLQUFLdkUsUUFBUSxJQUFJLEtBQU07WUFDNUQsTUFBTTBFLGdCQUFnQkYsWUFBWUM7WUFDbEMsTUFBTUUsWUFBWSxnQkFBa0JKLENBQUFBLEtBQUtwRSxPQUFPLElBQUksS0FBTTtZQUMxRCxPQUFPbUUsTUFBTUksZ0JBQWdCQztRQUMvQixHQUFHO1FBRUgsSUFBSUMsUUFBUVI7UUFDWixJQUFJZix3QkFBd0IsY0FBYztZQUN4Q3VCLFFBQVFSLFdBQVcsV0FBYWQsQ0FBQUEsd0JBQXdCLEtBQU07UUFDaEUsT0FBTztZQUNMc0IsUUFBUVIsV0FBWWQsQ0FBQUEsd0JBQXdCO1FBQzlDO1FBRUEsTUFBTXVCLGlCQUFpQixRQUFVekIsQ0FBQUEsa0JBQWtCLEtBQU07UUFDekQsTUFBTTBCLGFBQWFGLFFBQVFDO1FBRTNCLE9BQU87WUFDTFQsVUFBVVcsS0FBS0MsS0FBSyxDQUFDWixXQUFXLE9BQU87WUFDdkNRLE9BQU9HLEtBQUtDLEtBQUssQ0FBQ0YsYUFBYSxPQUFPO1lBQ3RDSCxXQUFXSSxLQUFLQyxLQUFLLENBQUNILGlCQUFpQixPQUFPO1lBQzlDSixnQkFBZ0JwQix3QkFBd0IsZUFDcEMwQixLQUFLQyxLQUFLLENBQUMsV0FBYTFCLENBQUFBLHdCQUF3QixLQUFLLE1BQU8sT0FBTyxNQUNsRUEsd0JBQXdCO1FBQy9CO0lBQ0Y7SUFFQSxNQUFNMkIsU0FBU2Q7SUFFZixNQUFNZSxVQUFVO1FBQ2RsQyxPQUFPO1lBQUV0RCxhQUFhO1lBQUlHLFVBQVU7WUFBR0UsV0FBVztZQUFHQyxVQUFVO1lBQUdHLFNBQVM7UUFBRTtJQUMvRTtJQUVBLE1BQU1nRixhQUFhLENBQUNDO1FBQ2xCLElBQUlyQyxPQUFPc0MsTUFBTSxHQUFHLEdBQUc7WUFDckJwQyxPQUFPbUM7UUFDVDtJQUNGO0lBRUEsTUFBTUUsV0FBVyxPQUFPQztRQUN0QjFELGFBQWE7UUFDYixJQUFJO1lBQ0YsTUFBTTJELE1BQU0vRCxTQUFTLFdBQVcsb0JBQW9CLG1CQUFnQyxPQUFiRCxVQUFVaUUsRUFBRTtZQUNuRixNQUFNQyxTQUFTakUsU0FBUyxXQUFXLFNBQVM7WUFFNUMsTUFBTWtFLFdBQVcsTUFBTS9CLE1BQU00QixLQUFLO2dCQUNoQ0U7Z0JBQ0FFLFNBQVM7b0JBQUUsZ0JBQWdCO2dCQUFtQjtnQkFDOUNDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQ1I7WUFDdkI7WUFFQSxJQUFJLENBQUNJLFNBQVM5QixFQUFFLEVBQUU7Z0JBQ2hCLE1BQU1JLFFBQVEsTUFBTTBCLFNBQVM1QixJQUFJO2dCQUNqQyxNQUFNLElBQUlpQyxNQUFNL0IsTUFBTUEsS0FBSyxJQUFJO1lBQ2pDO1lBRUExRSxrREFBS0EsQ0FBQzBHLE9BQU8sQ0FBQyxhQUF1RCxPQUExQ3hFLFNBQVMsV0FBVyxZQUFZLFdBQVU7WUFDckVhO1lBQ0FmO1lBQ0FEO1FBQ0YsRUFBRSxPQUFPMkMsT0FBTztZQUNkMUUsa0RBQUtBLENBQUMwRSxLQUFLLENBQUNBLGlCQUFpQitCLFFBQVEvQixNQUFNaUMsT0FBTyxHQUFHO1FBQ3ZELFNBQVU7WUFDUnJFLGFBQWE7UUFDZjtJQUNGO0lBRUEsTUFBTXNFLGNBQWM7UUFDbEI3RDtRQUNBaEI7SUFDRjtJQUVBLE1BQU04RSxnQkFBZ0I7UUFDcEI7WUFBRUMsT0FBTztZQUFTQyxPQUFPO1FBQVE7UUFDakM7WUFBRUQsT0FBTztZQUFRQyxPQUFPO1FBQU87UUFDL0I7WUFBRUQsT0FBTztZQUFVQyxPQUFPO1FBQVM7UUFDbkM7WUFBRUQsT0FBTztZQUFZQyxPQUFPO1FBQVc7UUFDdkM7WUFBRUQsT0FBTztZQUFZQyxPQUFPO1FBQVc7UUFDdkM7WUFBRUQsT0FBTztZQUFXQyxPQUFPO1FBQVU7S0FDdEM7SUFFRCxxQkFDRSw4REFBQ3hILHlEQUFNQTtRQUFDeUgsTUFBTWxGO1FBQVFtRixjQUFjTDtrQkFDbEMsNEVBQUNwSCxnRUFBYUE7WUFBQzBILFdBQVU7OzhCQUN2Qiw4REFBQ3ZILCtEQUFZQTs7c0NBQ1gsOERBQUNDLDhEQUFXQTtzQ0FDVHNDLFNBQVMsV0FBVyx5QkFBeUI7Ozs7OztzQ0FFaEQsOERBQUN6QyxvRUFBaUJBO3NDQUNmeUMsU0FBUyxXQUNOLDJEQUNBOzs7Ozs7Ozs7Ozs7OEJBS1IsOERBQUNpRjtvQkFBS3BCLFVBQVVuRCxhQUFhbUQ7b0JBQVdtQixXQUFVOztzQ0FFaEQsOERBQUNFOzRCQUFJRixXQUFVOzs4Q0FDYiw4REFBQ0U7O3NEQUNDLDhEQUFDOUgsdURBQUtBOzRDQUFDK0gsU0FBUTtzREFBUTs7Ozs7O3NEQUN2Qiw4REFBQ2hJLHVEQUFLQTs0Q0FDSjZHLElBQUc7NENBQ0YsR0FBR3ZELFNBQVMsUUFBUTs0Q0FDckIyRSxhQUFZOzs7Ozs7d0NBRWJ4RSxPQUFPaEMsS0FBSyxrQkFDWCw4REFBQ3lHOzRDQUFFTCxXQUFVO3NEQUE2QnBFLE9BQU9oQyxLQUFLLENBQUM2RixPQUFPOzs7Ozs7Ozs7Ozs7OENBSWxFLDhEQUFDUzs7c0RBQ0MsOERBQUM5SCx1REFBS0E7NENBQUMrSCxTQUFRO3NEQUFTOzs7Ozs7c0RBQ3hCLDhEQUFDRzs0Q0FDQ3RCLElBQUc7NENBQ0YsR0FBR3ZELFNBQVMsU0FBUzs0Q0FDdEJ1RSxXQUFVO3NEQUVUTCxjQUFjWSxHQUFHLENBQUMsQ0FBQ0MsdUJBQ2xCLDhEQUFDQTtvREFBMEJaLE9BQU9ZLE9BQU9aLEtBQUs7OERBQzNDWSxPQUFPWCxLQUFLO21EQURGVyxPQUFPWixLQUFLOzs7Ozs7Ozs7Ozs7Ozs7OzhDQU8vQiw4REFBQ007O3NEQUNDLDhEQUFDOUgsdURBQUtBOzRDQUFDK0gsU0FBUTtzREFBYTs7Ozs7O3NEQUM1Qiw4REFBQ0c7NENBQ0N0QixJQUFHOzRDQUNGLEdBQUd2RCxTQUFTLGFBQWE7NENBQzFCdUUsV0FBVTs7OERBRVYsOERBQUNRO29EQUFPWixPQUFNOzhEQUFHOzs7Ozs7Z0RBQ2hCdkUsVUFBVWtGLEdBQUcsQ0FBQyxDQUFDRSx5QkFDZCw4REFBQ0Q7d0RBQXlCWixPQUFPYSxTQUFTekIsRUFBRTs7NERBQ3pDeUIsU0FBU2hFLElBQUk7NERBQUM7NERBQUVnRSxTQUFTQyxPQUFPLElBQUksSUFBcUIsT0FBakJELFNBQVNDLE9BQU8sRUFBQzs7dURBRC9DRCxTQUFTekIsRUFBRTs7Ozs7Ozs7Ozs7d0NBSzNCcEQsT0FBTzlCLFVBQVUsa0JBQ2hCLDhEQUFDdUc7NENBQUVMLFdBQVU7c0RBQTZCcEUsT0FBTzlCLFVBQVUsQ0FBQzJGLE9BQU87Ozs7Ozs7Ozs7Ozs4Q0FJdkUsOERBQUNTOztzREFDQyw4REFBQzlILHVEQUFLQTs0Q0FBQytILFNBQVE7c0RBQVM7Ozs7OztzREFDeEIsOERBQUNHOzRDQUNDdEIsSUFBRzs0Q0FDRixHQUFHdkQsU0FBUyxTQUFTOzRDQUN0QnVFLFdBQVU7OzhEQUVWLDhEQUFDUTtvREFBT1osT0FBTTs4REFBRzs7Ozs7O2dEQUNoQnJFLE1BQU1nRixHQUFHLENBQUMsQ0FBQ0kscUJBQ1YsOERBQUNIO3dEQUFxQlosT0FBT2UsS0FBSzNCLEVBQUU7a0VBQ2pDMkIsS0FBS2xFLElBQUk7dURBRENrRSxLQUFLM0IsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBTzFCLDhEQUFDa0I7O3NEQUNDLDhEQUFDOUgsdURBQUtBOzRDQUFDK0gsU0FBUTtzREFBYTs7Ozs7O3NEQUM1Qiw4REFBQ2hJLHVEQUFLQTs0Q0FDSjZHLElBQUc7NENBQ0g0QixNQUFLOzRDQUNKLEdBQUduRixTQUFTLGFBQWE7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FLaEMsOERBQUN5RTs7OENBQ0MsOERBQUM5SCx1REFBS0E7b0NBQUMrSCxTQUFROzhDQUFjOzs7Ozs7OENBQzdCLDhEQUFDVTtvQ0FDQzdCLElBQUc7b0NBQ0YsR0FBR3ZELFNBQVMsY0FBYztvQ0FDM0JxRixNQUFNO29DQUNOZCxXQUFVO29DQUNWSSxhQUFZOzs7Ozs7Ozs7Ozs7c0NBS2hCLDhEQUFDRjs0QkFBSUYsV0FBVTs7OENBQ2IsOERBQUNFO29DQUFJRixXQUFVOztzREFDYiw4REFBQ2U7NENBQUdmLFdBQVU7c0RBQXNCOzs7Ozs7c0RBQ3BDLDhEQUFDOUgseURBQU1BOzRDQUFDMEksTUFBSzs0Q0FBU0ksU0FBU3ZDOzRDQUFTd0MsTUFBSzs7OERBQzNDLDhEQUFDdEksbUdBQUlBO29EQUFDcUgsV0FBVTs7Ozs7O2dEQUFpQjs7Ozs7Ozs7Ozs7Ozs4Q0FLckMsOERBQUNFO29DQUFJRixXQUFVOzhDQUNaMUQsT0FBT2lFLEdBQUcsQ0FBQyxDQUFDVyxPQUFPdkM7NENBUWIvQyxxQkFBQUEsZUFFSUEsaUNBQUFBOzZEQVRULDhEQUFDc0U7NENBQW1CRixXQUFVOzs4REFDNUIsOERBQUNFO29EQUFJRixXQUFVOztzRUFDYiw4REFBQzVILHVEQUFLQTs0REFBQytILFNBQVMsU0FBZSxPQUFOeEIsT0FBTTtzRUFBZTs7Ozs7O3NFQUM5Qyw4REFBQ3hHLHVEQUFLQTs0REFDSCxHQUFHc0QsU0FBUyxTQUFlLE9BQU5rRCxPQUFNLGdCQUFjOzREQUMxQ3lCLGFBQVk7Ozs7Ozt3REFFYnhFLEVBQUFBLGdCQUFBQSxPQUFPckIsS0FBSyxjQUFacUIscUNBQUFBLHNCQUFBQSxhQUFjLENBQUMrQyxNQUFNLGNBQXJCL0MsMENBQUFBLG9CQUF1QjNDLFdBQVcsbUJBQ2pDLDhEQUFDb0g7NERBQUVMLFdBQVU7dUVBQ1ZwRSx1QkFBQUEsT0FBT3JCLEtBQUssQ0FBQ29FLE1BQU0sY0FBbkIvQyw0Q0FBQUEsa0NBQUFBLHFCQUFxQjNDLFdBQVcsY0FBaEMyQyxzREFBQUEsZ0NBQWtDNkQsT0FBTzs7Ozs7Ozs7Ozs7OzhEQUtoRCw4REFBQ1M7b0RBQUlGLFdBQVU7O3NFQUNiLDhEQUFDNUgsdURBQUtBOzREQUFDK0gsU0FBUyxTQUFlLE9BQU54QixPQUFNO3NFQUFZOzs7Ozs7c0VBQzNDLDhEQUFDeEcsdURBQUtBOzREQUNKeUksTUFBSzs0REFDTHpILEtBQUk7NERBQ0gsR0FBR3NDLFNBQVMsU0FBZSxPQUFOa0QsT0FBTSxjQUFZO2dFQUFFd0MsZUFBZTs0REFBSyxFQUFFOzREQUNoRWYsYUFBWTs7Ozs7Ozs7Ozs7OzhEQUloQiw4REFBQ0Y7b0RBQUlGLFdBQVU7O3NFQUNiLDhEQUFDNUgsdURBQUtBOzREQUFDK0gsU0FBUyxTQUFlLE9BQU54QixPQUFNO3NFQUFhOzs7Ozs7c0VBQzVDLDhEQUFDeEcsdURBQUtBOzREQUNKeUksTUFBSzs0REFDTHpILEtBQUk7NERBQ0ppSSxNQUFLOzREQUNKLEdBQUczRixTQUFTLFNBQWUsT0FBTmtELE9BQU0sZUFBYTtnRUFBRXdDLGVBQWU7NERBQUssRUFBRTs0REFDakVmLGFBQVk7Ozs7Ozs7Ozs7Ozs4REFJaEIsOERBQUNGO29EQUFJRixXQUFVOztzRUFDYiw4REFBQzVILHVEQUFLQTs0REFBQytILFNBQVMsU0FBZSxPQUFOeEIsT0FBTTtzRUFBWTs7Ozs7O3NFQUMzQyw4REFBQ3hHLHVEQUFLQTs0REFDSnlJLE1BQUs7NERBQ0x6SCxLQUFJOzREQUNKSyxLQUFJOzREQUNILEdBQUdpQyxTQUFTLFNBQWUsT0FBTmtELE9BQU0sY0FBWTtnRUFBRXdDLGVBQWU7NERBQUssRUFBRTs0REFDaEVmLGFBQVk7Ozs7Ozs7Ozs7Ozs4REFJaEIsOERBQUNGO29EQUFJRixXQUFVOztzRUFDYiw4REFBQzVILHVEQUFLQTs0REFBQytILFNBQVMsU0FBZSxPQUFOeEIsT0FBTTtzRUFBVzs7Ozs7O3NFQUMxQyw4REFBQ3hHLHVEQUFLQTs0REFDSnlJLE1BQUs7NERBQ0x6SCxLQUFJOzREQUNKSyxLQUFJOzREQUNILEdBQUdpQyxTQUFTLFNBQWUsT0FBTmtELE9BQU0sYUFBVztnRUFBRXdDLGVBQWU7NERBQUssRUFBRTs0REFDL0RmLGFBQVk7Ozs7Ozs7Ozs7Ozs4REFJaEIsOERBQUNGO29EQUFJRixXQUFVOztzRUFDYiw4REFBQzVILHVEQUFLQTtzRUFBQzs7Ozs7O3NFQUNQLDhEQUFDOEg7NERBQUlGLFdBQVU7O2dFQUEwQztnRUFDcEQ7b0VBQ0QsTUFBTWxDLE9BQU9wQixZQUFZLENBQUNpQyxNQUFNO29FQUNoQyxJQUFJLENBQUNiLE1BQU0sT0FBTztvRUFDbEIsTUFBTUMsWUFBWSxDQUFDRCxLQUFLMUUsUUFBUSxJQUFJLEtBQU0wRSxDQUFBQSxLQUFLeEUsU0FBUyxJQUFJO29FQUM1RCxNQUFNMEUsaUJBQWlCLFlBQWNGLENBQUFBLEtBQUt2RSxRQUFRLElBQUksS0FBTTtvRUFDNUQsTUFBTTBFLGdCQUFnQkYsWUFBWUM7b0VBQ2xDLE1BQU1FLFlBQVksZ0JBQWtCSixDQUFBQSxLQUFLcEUsT0FBTyxJQUFJLEtBQU07b0VBQzFELE9BQU8sQ0FBQ3VFLGdCQUFnQkMsU0FBUSxFQUFHbUQsT0FBTyxDQUFDO2dFQUM3Qzs7Ozs7Ozs7Ozs7Ozs4REFJSiw4REFBQ25CO29EQUFJRixXQUFVOzhEQUNiLDRFQUFDOUgseURBQU1BO3dEQUNMMEksTUFBSzt3REFDTFUsU0FBUTt3REFDUkwsTUFBSzt3REFDTEQsU0FBUyxJQUFNdEMsV0FBV0M7d0RBQzFCNEMsVUFBVWpGLE9BQU9zQyxNQUFNLEtBQUs7a0VBRTVCLDRFQUFDaEcsbUdBQU1BOzREQUFDb0gsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7MkNBaEZka0IsTUFBTWxDLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQXlGeEIsOERBQUNrQjs0QkFBSUYsV0FBVTs7OENBQ2IsOERBQUNFO29DQUFJRixXQUFVOztzREFDYiw4REFBQ2U7NENBQUdmLFdBQVU7c0RBQXNCOzs7Ozs7c0RBRXBDLDhEQUFDRTs0Q0FBSUYsV0FBVTs7OERBQ2IsOERBQUNFOztzRUFDQyw4REFBQzlILHVEQUFLQTs0REFBQytILFNBQVE7c0VBQWU7Ozs7OztzRUFDOUIsOERBQUNHOzREQUNDdEIsSUFBRzs0REFDRixHQUFHdkQsU0FBUyxlQUFlOzREQUM1QnVFLFdBQVU7OzhFQUVWLDhEQUFDUTtvRUFBT1osT0FBTTs4RUFBYTs7Ozs7OzhFQUMzQiw4REFBQ1k7b0VBQU9aLE9BQU07OEVBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFJMUIsOERBQUNNOztzRUFDQyw4REFBQzlILHVEQUFLQTs0REFBQytILFNBQVE7O2dFQUFnQjtnRUFDbkJ2RCx3QkFBd0IsZUFBZSxNQUFNOzs7Ozs7O3NFQUV6RCw4REFBQ3pFLHVEQUFLQTs0REFDSnlJLE1BQUs7NERBQ0x6SCxLQUFJOzREQUNKaUksTUFBSzs0REFDSixHQUFHM0YsU0FBUyxpQkFBaUI7Z0VBQUUwRixlQUFlOzREQUFLLEVBQUU7NERBQ3REZixhQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBS2xCLDhEQUFDRjs7OERBQ0MsOERBQUM5SCx1REFBS0E7b0RBQUMrSCxTQUFROzhEQUFVOzs7Ozs7OERBQ3pCLDhEQUFDaEksdURBQUtBO29EQUNKeUksTUFBSztvREFDTHpILEtBQUk7b0RBQ0pLLEtBQUk7b0RBQ0o0SCxNQUFLO29EQUNKLEdBQUczRixTQUFTLFdBQVc7d0RBQUUwRixlQUFlO29EQUFLLEVBQUU7b0RBQ2hEZixhQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBS2xCLDhEQUFDRjtvQ0FBSUYsV0FBVTs7c0RBQ2IsOERBQUNlOzRDQUFHZixXQUFVOzs4REFDWiw4REFBQ25ILG1HQUFVQTtvREFBQ21ILFdBQVU7Ozs7OztnREFBaUI7Ozs7Ozs7c0RBSXpDLDhEQUFDRTs0Q0FBSUYsV0FBVTs7OERBQ2IsOERBQUNFO29EQUFJRixXQUFVOztzRUFDYiw4REFBQ3dCO3NFQUFLOzs7Ozs7c0VBQ04sOERBQUNBOztnRUFBSztnRUFBRWhELE9BQU9iLFFBQVEsQ0FBQzBELE9BQU8sQ0FBQzs7Ozs7Ozs7Ozs7OztnREFHakM3QyxPQUFPUixjQUFjLEdBQUcsbUJBQ3ZCLDhEQUFDa0M7b0RBQUlGLFdBQVU7O3NFQUNiLDhEQUFDd0I7c0VBQUs7Ozs7OztzRUFDTiw4REFBQ0E7O2dFQUFLO2dFQUFHaEQsT0FBT1IsY0FBYyxDQUFDcUQsT0FBTyxDQUFDOzs7Ozs7Ozs7Ozs7O2dEQUkxQzdDLE9BQU9OLFNBQVMsR0FBRyxtQkFDbEIsOERBQUNnQztvREFBSUYsV0FBVTs7c0VBQ2IsOERBQUN3QjtzRUFBSzs7Ozs7O3NFQUNOLDhEQUFDQTs7Z0VBQUs7Z0VBQUVoRCxPQUFPTixTQUFTLENBQUNtRCxPQUFPLENBQUM7Ozs7Ozs7Ozs7Ozs7OERBSXJDLDhEQUFDbkI7b0RBQUlGLFdBQVU7O3NFQUNiLDhEQUFDd0I7c0VBQUs7Ozs7OztzRUFDTiw4REFBQ0E7O2dFQUFLO2dFQUFFaEQsT0FBT0wsS0FBSyxDQUFDa0QsT0FBTyxDQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU9yQyw4REFBQ25COzRCQUFJRixXQUFVOzs4Q0FDYiw4REFBQ0U7O3NEQUNDLDhEQUFDOUgsdURBQUtBOzRDQUFDK0gsU0FBUTtzREFBZTs7Ozs7O3NEQUM5Qiw4REFBQ1U7NENBQ0M3QixJQUFHOzRDQUNGLEdBQUd2RCxTQUFTLGVBQWU7NENBQzVCcUYsTUFBTTs0Q0FDTmQsV0FBVTs0Q0FDVkksYUFBWTs7Ozs7Ozs7Ozs7OzhDQUloQiw4REFBQ0Y7O3NEQUNDLDhEQUFDOUgsdURBQUtBOzRDQUFDK0gsU0FBUTtzREFBaUI7Ozs7OztzREFDaEMsOERBQUNoSSx1REFBS0E7NENBQ0o2RyxJQUFHOzRDQUNINEIsTUFBSzs0Q0FDTHpILEtBQUk7NENBQ0gsR0FBR3NDLFNBQVMsa0JBQWtCO2dEQUFFMEYsZUFBZTs0Q0FBSyxFQUFFOzRDQUN2RGYsYUFBWTs7Ozs7O3NEQUVkLDhEQUFDQzs0Q0FBRUwsV0FBVTtzREFBNkI7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FLOUMsOERBQUNFOzRCQUFJRixXQUFVOzs4Q0FDYiw4REFBQ0U7O3NEQUNDLDhEQUFDOUgsdURBQUtBOzRDQUFDK0gsU0FBUTtzREFBUTs7Ozs7O3NEQUN2Qiw4REFBQ1U7NENBQ0M3QixJQUFHOzRDQUNGLEdBQUd2RCxTQUFTLFFBQVE7NENBQ3JCcUYsTUFBTTs0Q0FDTmQsV0FBVTs0Q0FDVkksYUFBWTs7Ozs7Ozs7Ozs7OzhDQUloQiw4REFBQ0Y7O3NEQUNDLDhEQUFDOUgsdURBQUtBOzRDQUFDK0gsU0FBUTtzREFBUTs7Ozs7O3NEQUN2Qiw4REFBQ1U7NENBQ0M3QixJQUFHOzRDQUNGLEdBQUd2RCxTQUFTLFFBQVE7NENBQ3JCcUYsTUFBTTs0Q0FDTmQsV0FBVTs0Q0FDVkksYUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUtsQiw4REFBQzVILCtEQUFZQTs7OENBQ1gsOERBQUNOLHlEQUFNQTtvQ0FBQzBJLE1BQUs7b0NBQVNVLFNBQVE7b0NBQVVOLFNBQVN0Qjs4Q0FBYTs7Ozs7OzhDQUc5RCw4REFBQ3hILHlEQUFNQTtvQ0FBQzBJLE1BQUs7b0NBQVNXLFVBQVVwRzs4Q0FDN0JBLFlBQVksY0FBY0gsU0FBUyxXQUFXLHFCQUFxQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPbEY7R0FoZ0JnQkw7O1FBcUJWN0Msb0RBQU9BO1FBMEJ3QkMsMERBQWFBOzs7S0EvQ2xDNEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vY29tcG9uZW50cy9xdW90YXRpb25zL3F1b3RhdGlvbi1mb3JtLnRzeD84ZDhmIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyB1c2VGb3JtLCB1c2VGaWVsZEFycmF5IH0gZnJvbSAncmVhY3QtaG9vay1mb3JtJ1xuaW1wb3J0IHsgem9kUmVzb2x2ZXIgfSBmcm9tICdAaG9va2Zvcm0vcmVzb2x2ZXJzL3pvZCdcbmltcG9ydCB7IHogfSBmcm9tICd6b2QnXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnV0dG9uJ1xuaW1wb3J0IHsgSW5wdXQgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvaW5wdXQnXG5pbXBvcnQgeyBMYWJlbCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9sYWJlbCdcbmltcG9ydCB7XG4gIERpYWxvZyxcbiAgRGlhbG9nQ29udGVudCxcbiAgRGlhbG9nRGVzY3JpcHRpb24sXG4gIERpYWxvZ0Zvb3RlcixcbiAgRGlhbG9nSGVhZGVyLFxuICBEaWFsb2dUaXRsZSxcbn0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2RpYWxvZydcbmltcG9ydCB7IFBsdXMsIFRyYXNoMiwgQ2FsY3VsYXRvciB9IGZyb20gJ2x1Y2lkZS1yZWFjdCdcbmltcG9ydCB7IHRvYXN0IH0gZnJvbSAncmVhY3QtaG90LXRvYXN0J1xuXG5jb25zdCBxdW90YXRpb25JdGVtU2NoZW1hID0gei5vYmplY3Qoe1xuICBkZXNjcmlwdGlvbjogei5zdHJpbmcoKS5taW4oMSwgJ0Rlc2NyaXB0aW9uIGlzIHJlcXVpcmVkJyksXG4gIHF1YW50aXR5OiB6Lm51bWJlcigpLm1pbigxLCAnUXVhbnRpdHkgbXVzdCBiZSBhdCBsZWFzdCAxJyksXG4gIHVuaXRQcmljZTogei5udW1iZXIoKS5taW4oMCwgJ1VuaXQgcHJpY2UgbXVzdCBiZSBwb3NpdGl2ZScpLFxuICBkaXNjb3VudDogei5udW1iZXIoKS5taW4oMCkubWF4KDEwMCkuZGVmYXVsdCgwKSxcbiAgdGF4UmF0ZTogei5udW1iZXIoKS5taW4oMCkubWF4KDEwMCkuZGVmYXVsdCgwKVxufSlcblxuY29uc3QgcXVvdGF0aW9uU2NoZW1hID0gei5vYmplY3Qoe1xuICB0aXRsZTogei5zdHJpbmcoKS5taW4oMSwgJ1RpdGxlIGlzIHJlcXVpcmVkJyksXG4gIGRlc2NyaXB0aW9uOiB6LnN0cmluZygpLm9wdGlvbmFsKCksXG4gIGN1c3RvbWVySWQ6IHouc3RyaW5nKCkubWluKDEsICdDdXN0b21lciBpcyByZXF1aXJlZCcpLFxuICBsZWFkSWQ6IHouc3RyaW5nKCkub3B0aW9uYWwoKSxcbiAgc3RhdHVzOiB6LmVudW0oWydEUkFGVCcsICdTRU5UJywgJ1ZJRVdFRCcsICdBQ0NFUFRFRCcsICdSRUpFQ1RFRCcsICdFWFBJUkVEJ10pLmRlZmF1bHQoJ0RSQUZUJyksXG4gIHZhbGlkVW50aWw6IHouc3RyaW5nKCkub3B0aW9uYWwoKSxcbiAgdGVybXM6IHouc3RyaW5nKCkub3B0aW9uYWwoKSxcbiAgbm90ZXM6IHouc3RyaW5nKCkub3B0aW9uYWwoKSxcbiAgcGF5bWVudFRlcm1zOiB6LnN0cmluZygpLm9wdGlvbmFsKCksXG4gIHBheW1lbnREdWVEYXlzOiB6Lm51bWJlcigpLm1pbigwKS5kZWZhdWx0KDMwKSxcbiAgaXRlbXM6IHouYXJyYXkocXVvdGF0aW9uSXRlbVNjaGVtYSkubWluKDEsICdBdCBsZWFzdCBvbmUgaXRlbSBpcyByZXF1aXJlZCcpLFxuICB0YXhSYXRlOiB6Lm51bWJlcigpLm1pbigwKS5tYXgoMTAwKS5kZWZhdWx0KDApLFxuICBkaXNjb3VudFR5cGU6IHouZW51bShbJ1BFUkNFTlRBR0UnLCAnRklYRUQnXSkuZGVmYXVsdCgnUEVSQ0VOVEFHRScpLFxuICBkaXNjb3VudFZhbHVlOiB6Lm51bWJlcigpLm1pbigwKS5kZWZhdWx0KDApXG59KVxuXG50eXBlIFF1b3RhdGlvbkZvcm1EYXRhID0gei5pbmZlcjx0eXBlb2YgcXVvdGF0aW9uU2NoZW1hPlxuXG5pbnRlcmZhY2UgUXVvdGF0aW9uRm9ybVByb3BzIHtcbiAgaXNPcGVuOiBib29sZWFuXG4gIG9uQ2xvc2U6ICgpID0+IHZvaWRcbiAgb25TdWNjZXNzOiAoKSA9PiB2b2lkXG4gIHF1b3RhdGlvbj86IGFueVxuICBtb2RlOiAnY3JlYXRlJyB8ICdlZGl0J1xuICBwcmVzZWxlY3RlZEN1c3RvbWVySWQ/OiBzdHJpbmdcbiAgcHJlc2VsZWN0ZWRMZWFkSWQ/OiBzdHJpbmdcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFF1b3RhdGlvbkZvcm0oeyBcbiAgaXNPcGVuLCBcbiAgb25DbG9zZSwgXG4gIG9uU3VjY2VzcywgXG4gIHF1b3RhdGlvbiwgXG4gIG1vZGUsIFxuICBwcmVzZWxlY3RlZEN1c3RvbWVySWQsXG4gIHByZXNlbGVjdGVkTGVhZElkIFxufTogUXVvdGF0aW9uRm9ybVByb3BzKSB7XG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW2N1c3RvbWVycywgc2V0Q3VzdG9tZXJzXSA9IHVzZVN0YXRlPGFueVtdPihbXSlcbiAgY29uc3QgW2xlYWRzLCBzZXRMZWFkc10gPSB1c2VTdGF0ZTxhbnlbXT4oW10pXG5cbiAgY29uc3Qge1xuICAgIHJlZ2lzdGVyLFxuICAgIGhhbmRsZVN1Ym1pdCxcbiAgICBmb3JtU3RhdGU6IHsgZXJyb3JzIH0sXG4gICAgcmVzZXQsXG4gICAgd2F0Y2gsXG4gICAgY29udHJvbCxcbiAgICBzZXRWYWx1ZVxuICB9ID0gdXNlRm9ybTxRdW90YXRpb25Gb3JtRGF0YT4oe1xuICAgIHJlc29sdmVyOiB6b2RSZXNvbHZlcihxdW90YXRpb25TY2hlbWEpLFxuICAgIGRlZmF1bHRWYWx1ZXM6IHF1b3RhdGlvbiA/IHtcbiAgICAgIHRpdGxlOiBxdW90YXRpb24udGl0bGUsXG4gICAgICBkZXNjcmlwdGlvbjogcXVvdGF0aW9uLmRlc2NyaXB0aW9uIHx8ICcnLFxuICAgICAgY3VzdG9tZXJJZDogcXVvdGF0aW9uLmN1c3RvbWVySWQgfHwgcHJlc2VsZWN0ZWRDdXN0b21lcklkIHx8ICcnLFxuICAgICAgbGVhZElkOiBxdW90YXRpb24ubGVhZElkIHx8IHByZXNlbGVjdGVkTGVhZElkIHx8ICcnLFxuICAgICAgc3RhdHVzOiBxdW90YXRpb24uc3RhdHVzIHx8ICdEUkFGVCcsXG4gICAgICB2YWxpZFVudGlsOiBxdW90YXRpb24udmFsaWRVbnRpbCA/IG5ldyBEYXRlKHF1b3RhdGlvbi52YWxpZFVudGlsKS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF0gOiAnJyxcbiAgICAgIHRlcm1zOiBxdW90YXRpb24udGVybXMgfHwgJycsXG4gICAgICBub3RlczogcXVvdGF0aW9uLm5vdGVzIHx8ICcnLFxuICAgICAgaXRlbXM6IHF1b3RhdGlvbi5pdGVtcyB8fCBbeyBkZXNjcmlwdGlvbjogJycsIHF1YW50aXR5OiAxLCB1bml0UHJpY2U6IDAsIGRpc2NvdW50OiAwLCB0YXhSYXRlOiAwIH1dLFxuICAgICAgdGF4UmF0ZTogcXVvdGF0aW9uLnRheFJhdGUgfHwgMCxcbiAgICAgIGRpc2NvdW50VHlwZTogcXVvdGF0aW9uLmRpc2NvdW50VHlwZSB8fCAnUEVSQ0VOVEFHRScsXG4gICAgICBkaXNjb3VudFZhbHVlOiBxdW90YXRpb24uZGlzY291bnRWYWx1ZSB8fCAwXG4gICAgfSA6IHtcbiAgICAgIHN0YXR1czogJ0RSQUZUJyxcbiAgICAgIGN1c3RvbWVySWQ6IHByZXNlbGVjdGVkQ3VzdG9tZXJJZCB8fCAnJyxcbiAgICAgIGxlYWRJZDogcHJlc2VsZWN0ZWRMZWFkSWQgfHwgJycsXG4gICAgICBpdGVtczogW3sgZGVzY3JpcHRpb246ICcnLCBxdWFudGl0eTogMSwgdW5pdFByaWNlOiAwLCBkaXNjb3VudDogMCwgdGF4UmF0ZTogMCB9XSxcbiAgICAgIHRheFJhdGU6IDAsXG4gICAgICBkaXNjb3VudFR5cGU6ICdQRVJDRU5UQUdFJyxcbiAgICAgIGRpc2NvdW50VmFsdWU6IDBcbiAgICB9XG4gIH0pXG5cbiAgY29uc3QgeyBmaWVsZHMsIGFwcGVuZCwgcmVtb3ZlIH0gPSB1c2VGaWVsZEFycmF5KHtcbiAgICBjb250cm9sLFxuICAgIG5hbWU6ICdpdGVtcydcbiAgfSlcblxuICBjb25zdCB3YXRjaGVkSXRlbXMgPSB3YXRjaCgnaXRlbXMnKVxuICBjb25zdCB3YXRjaGVkVGF4UmF0ZSA9IHdhdGNoKCd0YXhSYXRlJylcbiAgY29uc3Qgd2F0Y2hlZERpc2NvdW50VHlwZSA9IHdhdGNoKCdkaXNjb3VudFR5cGUnKVxuICBjb25zdCB3YXRjaGVkRGlzY291bnRWYWx1ZSA9IHdhdGNoKCdkaXNjb3VudFZhbHVlJylcblxuICAvLyBGZXRjaCBjdXN0b21lcnMgYW5kIGxlYWRzXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgZmV0Y2hEYXRhID0gYXN5bmMgKCkgPT4ge1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgW2N1c3RvbWVyc1JlcywgbGVhZHNSZXNdID0gYXdhaXQgUHJvbWlzZS5hbGwoW1xuICAgICAgICAgIGZldGNoKCcvYXBpL2N1c3RvbWVycz9saW1pdD0xMDAnKSxcbiAgICAgICAgICBmZXRjaCgnL2FwaS9sZWFkcz9saW1pdD0xMDAnKVxuICAgICAgICBdKVxuXG4gICAgICAgIGlmIChjdXN0b21lcnNSZXMub2spIHtcbiAgICAgICAgICBjb25zdCBjdXN0b21lcnNEYXRhID0gYXdhaXQgY3VzdG9tZXJzUmVzLmpzb24oKVxuICAgICAgICAgIHNldEN1c3RvbWVycyhjdXN0b21lcnNEYXRhLmN1c3RvbWVycylcbiAgICAgICAgfVxuXG4gICAgICAgIGlmIChsZWFkc1Jlcy5vaykge1xuICAgICAgICAgIGNvbnN0IGxlYWRzRGF0YSA9IGF3YWl0IGxlYWRzUmVzLmpzb24oKVxuICAgICAgICAgIHNldExlYWRzKGxlYWRzRGF0YS5sZWFkcylcbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgZGF0YTonLCBlcnJvcilcbiAgICAgIH1cbiAgICB9XG5cbiAgICBpZiAoaXNPcGVuKSB7XG4gICAgICBmZXRjaERhdGEoKVxuICAgIH1cbiAgfSwgW2lzT3Blbl0pXG5cbiAgLy8gQ2FsY3VsYXRlIHRvdGFsc1xuICBjb25zdCBjYWxjdWxhdGVUb3RhbHMgPSAoKSA9PiB7XG4gICAgY29uc3Qgc3VidG90YWwgPSB3YXRjaGVkSXRlbXMucmVkdWNlKChzdW0sIGl0ZW0pID0+IHtcbiAgICAgIGNvbnN0IGl0ZW1Ub3RhbCA9IChpdGVtLnF1YW50aXR5IHx8IDApICogKGl0ZW0udW5pdFByaWNlIHx8IDApXG4gICAgICBjb25zdCBkaXNjb3VudEFtb3VudCA9IChpdGVtVG90YWwgKiAoaXRlbS5kaXNjb3VudCB8fCAwKSkgLyAxMDBcbiAgICAgIGNvbnN0IGFmdGVyRGlzY291bnQgPSBpdGVtVG90YWwgLSBkaXNjb3VudEFtb3VudFxuICAgICAgY29uc3QgdGF4QW1vdW50ID0gKGFmdGVyRGlzY291bnQgKiAoaXRlbS50YXhSYXRlIHx8IDApKSAvIDEwMFxuICAgICAgcmV0dXJuIHN1bSArIGFmdGVyRGlzY291bnQgKyB0YXhBbW91bnRcbiAgICB9LCAwKVxuXG4gICAgbGV0IHRvdGFsID0gc3VidG90YWxcbiAgICBpZiAod2F0Y2hlZERpc2NvdW50VHlwZSA9PT0gJ1BFUkNFTlRBR0UnKSB7XG4gICAgICB0b3RhbCA9IHN1YnRvdGFsIC0gKHN1YnRvdGFsICogKHdhdGNoZWREaXNjb3VudFZhbHVlIHx8IDApKSAvIDEwMFxuICAgIH0gZWxzZSB7XG4gICAgICB0b3RhbCA9IHN1YnRvdGFsIC0gKHdhdGNoZWREaXNjb3VudFZhbHVlIHx8IDApXG4gICAgfVxuXG4gICAgY29uc3QgZmluYWxUYXhBbW91bnQgPSAodG90YWwgKiAod2F0Y2hlZFRheFJhdGUgfHwgMCkpIC8gMTAwXG4gICAgY29uc3QgZmluYWxUb3RhbCA9IHRvdGFsICsgZmluYWxUYXhBbW91bnRcblxuICAgIHJldHVybiB7XG4gICAgICBzdWJ0b3RhbDogTWF0aC5yb3VuZChzdWJ0b3RhbCAqIDEwMCkgLyAxMDAsXG4gICAgICB0b3RhbDogTWF0aC5yb3VuZChmaW5hbFRvdGFsICogMTAwKSAvIDEwMCxcbiAgICAgIHRheEFtb3VudDogTWF0aC5yb3VuZChmaW5hbFRheEFtb3VudCAqIDEwMCkgLyAxMDAsXG4gICAgICBkaXNjb3VudEFtb3VudDogd2F0Y2hlZERpc2NvdW50VHlwZSA9PT0gJ1BFUkNFTlRBR0UnIFxuICAgICAgICA/IE1hdGgucm91bmQoKHN1YnRvdGFsICogKHdhdGNoZWREaXNjb3VudFZhbHVlIHx8IDApIC8gMTAwKSAqIDEwMCkgLyAxMDBcbiAgICAgICAgOiAod2F0Y2hlZERpc2NvdW50VmFsdWUgfHwgMClcbiAgICB9XG4gIH1cblxuICBjb25zdCB0b3RhbHMgPSBjYWxjdWxhdGVUb3RhbHMoKVxuXG4gIGNvbnN0IGFkZEl0ZW0gPSAoKSA9PiB7XG4gICAgYXBwZW5kKHsgZGVzY3JpcHRpb246ICcnLCBxdWFudGl0eTogMSwgdW5pdFByaWNlOiAwLCBkaXNjb3VudDogMCwgdGF4UmF0ZTogMCB9KVxuICB9XG5cbiAgY29uc3QgcmVtb3ZlSXRlbSA9IChpbmRleDogbnVtYmVyKSA9PiB7XG4gICAgaWYgKGZpZWxkcy5sZW5ndGggPiAxKSB7XG4gICAgICByZW1vdmUoaW5kZXgpXG4gICAgfVxuICB9XG5cbiAgY29uc3Qgb25TdWJtaXQgPSBhc3luYyAoZGF0YTogUXVvdGF0aW9uRm9ybURhdGEpID0+IHtcbiAgICBzZXRJc0xvYWRpbmcodHJ1ZSlcbiAgICB0cnkge1xuICAgICAgY29uc3QgdXJsID0gbW9kZSA9PT0gJ2NyZWF0ZScgPyAnL2FwaS9xdW90YXRpb25zJyA6IGAvYXBpL3F1b3RhdGlvbnMvJHtxdW90YXRpb24uaWR9YFxuICAgICAgY29uc3QgbWV0aG9kID0gbW9kZSA9PT0gJ2NyZWF0ZScgPyAnUE9TVCcgOiAnUFVUJ1xuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKHVybCwge1xuICAgICAgICBtZXRob2QsXG4gICAgICAgIGhlYWRlcnM6IHsgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShkYXRhKVxuICAgICAgfSlcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICBjb25zdCBlcnJvciA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKVxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3IuZXJyb3IgfHwgJ0ZhaWxlZCB0byBzYXZlIHF1b3RhdGlvbicpXG4gICAgICB9XG5cbiAgICAgIHRvYXN0LnN1Y2Nlc3MoYFF1b3RhdGlvbiAke21vZGUgPT09ICdjcmVhdGUnID8gJ2NyZWF0ZWQnIDogJ3VwZGF0ZWQnfSBzdWNjZXNzZnVsbHlgKVxuICAgICAgcmVzZXQoKVxuICAgICAgb25TdWNjZXNzKClcbiAgICAgIG9uQ2xvc2UoKVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICB0b2FzdC5lcnJvcihlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICdBbiBlcnJvciBvY2N1cnJlZCcpXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzTG9hZGluZyhmYWxzZSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBoYW5kbGVDbG9zZSA9ICgpID0+IHtcbiAgICByZXNldCgpXG4gICAgb25DbG9zZSgpXG4gIH1cblxuICBjb25zdCBzdGF0dXNPcHRpb25zID0gW1xuICAgIHsgdmFsdWU6ICdEUkFGVCcsIGxhYmVsOiAnRHJhZnQnIH0sXG4gICAgeyB2YWx1ZTogJ1NFTlQnLCBsYWJlbDogJ1NlbnQnIH0sXG4gICAgeyB2YWx1ZTogJ1ZJRVdFRCcsIGxhYmVsOiAnVmlld2VkJyB9LFxuICAgIHsgdmFsdWU6ICdBQ0NFUFRFRCcsIGxhYmVsOiAnQWNjZXB0ZWQnIH0sXG4gICAgeyB2YWx1ZTogJ1JFSkVDVEVEJywgbGFiZWw6ICdSZWplY3RlZCcgfSxcbiAgICB7IHZhbHVlOiAnRVhQSVJFRCcsIGxhYmVsOiAnRXhwaXJlZCcgfVxuICBdXG5cbiAgcmV0dXJuIChcbiAgICA8RGlhbG9nIG9wZW49e2lzT3Blbn0gb25PcGVuQ2hhbmdlPXtoYW5kbGVDbG9zZX0+XG4gICAgICA8RGlhbG9nQ29udGVudCBjbGFzc05hbWU9XCJtYXgtdy02eGwgbWF4LWgtWzkwdmhdIG92ZXJmbG93LXktYXV0b1wiPlxuICAgICAgICA8RGlhbG9nSGVhZGVyPlxuICAgICAgICAgIDxEaWFsb2dUaXRsZT5cbiAgICAgICAgICAgIHttb2RlID09PSAnY3JlYXRlJyA/ICdDcmVhdGUgTmV3IFF1b3RhdGlvbicgOiAnRWRpdCBRdW90YXRpb24nfVxuICAgICAgICAgIDwvRGlhbG9nVGl0bGU+XG4gICAgICAgICAgPERpYWxvZ0Rlc2NyaXB0aW9uPlxuICAgICAgICAgICAge21vZGUgPT09ICdjcmVhdGUnIFxuICAgICAgICAgICAgICA/ICdDcmVhdGUgYSBuZXcgcXVvdGF0aW9uIHdpdGggaXRlbXMsIHByaWNpbmcsIGFuZCB0ZXJtcy4nXG4gICAgICAgICAgICAgIDogJ1VwZGF0ZSB0aGUgcXVvdGF0aW9uIGluZm9ybWF0aW9uIGFuZCBpdGVtcy4nXG4gICAgICAgICAgICB9XG4gICAgICAgICAgPC9EaWFsb2dEZXNjcmlwdGlvbj5cbiAgICAgICAgPC9EaWFsb2dIZWFkZXI+XG5cbiAgICAgICAgPGZvcm0gb25TdWJtaXQ9e2hhbmRsZVN1Ym1pdChvblN1Ym1pdCl9IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICAgIHsvKiBCYXNpYyBJbmZvcm1hdGlvbiAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwidGl0bGVcIj5UaXRsZSAqPC9MYWJlbD5cbiAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgaWQ9XCJ0aXRsZVwiXG4gICAgICAgICAgICAgICAgey4uLnJlZ2lzdGVyKCd0aXRsZScpfVxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiUXVvdGF0aW9uIHRpdGxlXCJcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAge2Vycm9ycy50aXRsZSAmJiAoXG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXJlZC02MDAgbXQtMVwiPntlcnJvcnMudGl0bGUubWVzc2FnZX08L3A+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJzdGF0dXNcIj5TdGF0dXM8L0xhYmVsPlxuICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgaWQ9XCJzdGF0dXNcIlxuICAgICAgICAgICAgICAgIHsuLi5yZWdpc3Rlcignc3RhdHVzJyl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHtzdGF0dXNPcHRpb25zLm1hcCgob3B0aW9uKSA9PiAoXG4gICAgICAgICAgICAgICAgICA8b3B0aW9uIGtleT17b3B0aW9uLnZhbHVlfSB2YWx1ZT17b3B0aW9uLnZhbHVlfT5cbiAgICAgICAgICAgICAgICAgICAge29wdGlvbi5sYWJlbH1cbiAgICAgICAgICAgICAgICAgIDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImN1c3RvbWVySWRcIj5DdXN0b21lciAqPC9MYWJlbD5cbiAgICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICAgIGlkPVwiY3VzdG9tZXJJZFwiXG4gICAgICAgICAgICAgICAgey4uLnJlZ2lzdGVyKCdjdXN0b21lcklkJyl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJcIj5TZWxlY3QgYSBjdXN0b21lcjwvb3B0aW9uPlxuICAgICAgICAgICAgICAgIHtjdXN0b21lcnMubWFwKChjdXN0b21lcikgPT4gKFxuICAgICAgICAgICAgICAgICAgPG9wdGlvbiBrZXk9e2N1c3RvbWVyLmlkfSB2YWx1ZT17Y3VzdG9tZXIuaWR9PlxuICAgICAgICAgICAgICAgICAgICB7Y3VzdG9tZXIubmFtZX0ge2N1c3RvbWVyLmNvbXBhbnkgJiYgYCgke2N1c3RvbWVyLmNvbXBhbnl9KWB9XG4gICAgICAgICAgICAgICAgICA8L29wdGlvbj5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICAgIHtlcnJvcnMuY3VzdG9tZXJJZCAmJiAoXG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXJlZC02MDAgbXQtMVwiPntlcnJvcnMuY3VzdG9tZXJJZC5tZXNzYWdlfTwvcD5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImxlYWRJZFwiPlJlbGF0ZWQgTGVhZDwvTGFiZWw+XG4gICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICBpZD1cImxlYWRJZFwiXG4gICAgICAgICAgICAgICAgey4uLnJlZ2lzdGVyKCdsZWFkSWQnKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtMyBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDBcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlwiPlNlbGVjdCBhIGxlYWQgKG9wdGlvbmFsKTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgIHtsZWFkcy5tYXAoKGxlYWQpID0+IChcbiAgICAgICAgICAgICAgICAgIDxvcHRpb24ga2V5PXtsZWFkLmlkfSB2YWx1ZT17bGVhZC5pZH0+XG4gICAgICAgICAgICAgICAgICAgIHtsZWFkLm5hbWV9XG4gICAgICAgICAgICAgICAgICA8L29wdGlvbj5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJ2YWxpZFVudGlsXCI+VmFsaWQgVW50aWw8L0xhYmVsPlxuICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICBpZD1cInZhbGlkVW50aWxcIlxuICAgICAgICAgICAgICAgIHR5cGU9XCJkYXRlXCJcbiAgICAgICAgICAgICAgICB7Li4ucmVnaXN0ZXIoJ3ZhbGlkVW50aWwnKX1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiZGVzY3JpcHRpb25cIj5EZXNjcmlwdGlvbjwvTGFiZWw+XG4gICAgICAgICAgICA8dGV4dGFyZWFcbiAgICAgICAgICAgICAgaWQ9XCJkZXNjcmlwdGlvblwiXG4gICAgICAgICAgICAgIHsuLi5yZWdpc3RlcignZGVzY3JpcHRpb24nKX1cbiAgICAgICAgICAgICAgcm93cz17M31cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwXCJcbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJCcmllZiBkZXNjcmlwdGlvbiBvZiB0aGUgcXVvdGF0aW9uLi4uXCJcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogSXRlbXMgU2VjdGlvbiAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW1cIj5JdGVtczwvaDM+XG4gICAgICAgICAgICAgIDxCdXR0b24gdHlwZT1cImJ1dHRvblwiIG9uQ2xpY2s9e2FkZEl0ZW19IHNpemU9XCJzbVwiPlxuICAgICAgICAgICAgICAgIDxQbHVzIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgQWRkIEl0ZW1cbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAge2ZpZWxkcy5tYXAoKGZpZWxkLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgIDxkaXYga2V5PXtmaWVsZC5pZH0gY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMTIgZ2FwLTIgaXRlbXMtZW5kIHAtNCBib3JkZXIgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb2wtc3Bhbi00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPXtgaXRlbXMuJHtpbmRleH0uZGVzY3JpcHRpb25gfT5EZXNjcmlwdGlvbiAqPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgey4uLnJlZ2lzdGVyKGBpdGVtcy4ke2luZGV4fS5kZXNjcmlwdGlvbmApfVxuICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiSXRlbSBkZXNjcmlwdGlvblwiXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIHtlcnJvcnMuaXRlbXM/LltpbmRleF0/LmRlc2NyaXB0aW9uICYmIChcbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtcmVkLTYwMCBtdC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7ZXJyb3JzLml0ZW1zW2luZGV4XT8uZGVzY3JpcHRpb24/Lm1lc3NhZ2V9XG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29sLXNwYW4tMlwiPlxuICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj17YGl0ZW1zLiR7aW5kZXh9LnF1YW50aXR5YH0+UXR5ICo8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcbiAgICAgICAgICAgICAgICAgICAgICBtaW49XCIxXCJcbiAgICAgICAgICAgICAgICAgICAgICB7Li4ucmVnaXN0ZXIoYGl0ZW1zLiR7aW5kZXh9LnF1YW50aXR5YCwgeyB2YWx1ZUFzTnVtYmVyOiB0cnVlIH0pfVxuICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiMVwiXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb2wtc3Bhbi0yXCI+XG4gICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPXtgaXRlbXMuJHtpbmRleH0udW5pdFByaWNlYH0+VW5pdCBQcmljZSAqPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXG4gICAgICAgICAgICAgICAgICAgICAgbWluPVwiMFwiXG4gICAgICAgICAgICAgICAgICAgICAgc3RlcD1cIjAuMDFcIlxuICAgICAgICAgICAgICAgICAgICAgIHsuLi5yZWdpc3RlcihgaXRlbXMuJHtpbmRleH0udW5pdFByaWNlYCwgeyB2YWx1ZUFzTnVtYmVyOiB0cnVlIH0pfVxuICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiMC4wMFwiXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb2wtc3Bhbi0xXCI+XG4gICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPXtgaXRlbXMuJHtpbmRleH0uZGlzY291bnRgfT5EaXNjICU8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcbiAgICAgICAgICAgICAgICAgICAgICBtaW49XCIwXCJcbiAgICAgICAgICAgICAgICAgICAgICBtYXg9XCIxMDBcIlxuICAgICAgICAgICAgICAgICAgICAgIHsuLi5yZWdpc3RlcihgaXRlbXMuJHtpbmRleH0uZGlzY291bnRgLCB7IHZhbHVlQXNOdW1iZXI6IHRydWUgfSl9XG4gICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCIwXCJcbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbC1zcGFuLTFcIj5cbiAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9e2BpdGVtcy4ke2luZGV4fS50YXhSYXRlYH0+VGF4ICU8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcbiAgICAgICAgICAgICAgICAgICAgICBtaW49XCIwXCJcbiAgICAgICAgICAgICAgICAgICAgICBtYXg9XCIxMDBcIlxuICAgICAgICAgICAgICAgICAgICAgIHsuLi5yZWdpc3RlcihgaXRlbXMuJHtpbmRleH0udGF4UmF0ZWAsIHsgdmFsdWVBc051bWJlcjogdHJ1ZSB9KX1cbiAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIjBcIlxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29sLXNwYW4tMVwiPlxuICAgICAgICAgICAgICAgICAgICA8TGFiZWw+VG90YWw8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB4LTMgcHktMiBiZy1ncmF5LTUwIHJvdW5kZWQtbWQgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgICAgICR7KCgpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGl0ZW0gPSB3YXRjaGVkSXRlbXNbaW5kZXhdXG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoIWl0ZW0pIHJldHVybiAnMC4wMCdcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGl0ZW1Ub3RhbCA9IChpdGVtLnF1YW50aXR5IHx8IDApICogKGl0ZW0udW5pdFByaWNlIHx8IDApXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBkaXNjb3VudEFtb3VudCA9IChpdGVtVG90YWwgKiAoaXRlbS5kaXNjb3VudCB8fCAwKSkgLyAxMDBcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGFmdGVyRGlzY291bnQgPSBpdGVtVG90YWwgLSBkaXNjb3VudEFtb3VudFxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgdGF4QW1vdW50ID0gKGFmdGVyRGlzY291bnQgKiAoaXRlbS50YXhSYXRlIHx8IDApKSAvIDEwMFxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChhZnRlckRpc2NvdW50ICsgdGF4QW1vdW50KS50b0ZpeGVkKDIpXG4gICAgICAgICAgICAgICAgICAgICAgfSkoKX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb2wtc3Bhbi0xXCI+XG4gICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiZGVzdHJ1Y3RpdmVcIlxuICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gcmVtb3ZlSXRlbShpbmRleCl9XG4gICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2ZpZWxkcy5sZW5ndGggPT09IDF9XG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8VHJhc2gyIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIFRvdGFscyBhbmQgU2V0dGluZ3MgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC02XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bVwiPlNldHRpbmdzPC9oMz5cbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImRpc2NvdW50VHlwZVwiPkRpc2NvdW50IFR5cGU8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICAgICAgICBpZD1cImRpc2NvdW50VHlwZVwiXG4gICAgICAgICAgICAgICAgICAgIHsuLi5yZWdpc3RlcignZGlzY291bnRUeXBlJyl9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJQRVJDRU5UQUdFXCI+UGVyY2VudGFnZTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiRklYRURcIj5GaXhlZCBBbW91bnQ8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiZGlzY291bnRWYWx1ZVwiPlxuICAgICAgICAgICAgICAgICAgICBEaXNjb3VudCB7d2F0Y2hlZERpc2NvdW50VHlwZSA9PT0gJ1BFUkNFTlRBR0UnID8gJyUnIDogJyQnfVxuICAgICAgICAgICAgICAgICAgPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcbiAgICAgICAgICAgICAgICAgICAgbWluPVwiMFwiXG4gICAgICAgICAgICAgICAgICAgIHN0ZXA9XCIwLjAxXCJcbiAgICAgICAgICAgICAgICAgICAgey4uLnJlZ2lzdGVyKCdkaXNjb3VudFZhbHVlJywgeyB2YWx1ZUFzTnVtYmVyOiB0cnVlIH0pfVxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIjBcIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInRheFJhdGVcIj5PdmVyYWxsIFRheCBSYXRlICglKTwvTGFiZWw+XG4gICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcbiAgICAgICAgICAgICAgICAgIG1pbj1cIjBcIlxuICAgICAgICAgICAgICAgICAgbWF4PVwiMTAwXCJcbiAgICAgICAgICAgICAgICAgIHN0ZXA9XCIwLjAxXCJcbiAgICAgICAgICAgICAgICAgIHsuLi5yZWdpc3RlcigndGF4UmF0ZScsIHsgdmFsdWVBc051bWJlcjogdHJ1ZSB9KX1cbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiMFwiXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8Q2FsY3VsYXRvciBjbGFzc05hbWU9XCJoLTUgdy01IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgIFRvdGFsc1xuICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTIgcC00IGJnLWdyYXktNTAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuPlN1YnRvdGFsOjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuPiR7dG90YWxzLnN1YnRvdGFsLnRvRml4ZWQoMil9PC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIHt0b3RhbHMuZGlzY291bnRBbW91bnQgPiAwICYmIChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gdGV4dC1yZWQtNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuPkRpc2NvdW50Ojwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4+LSR7dG90YWxzLmRpc2NvdW50QW1vdW50LnRvRml4ZWQoMil9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICB7dG90YWxzLnRheEFtb3VudCA+IDAgJiYgKFxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICA8c3Bhbj5UYXg6PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8c3Bhbj4ke3RvdGFscy50YXhBbW91bnQudG9GaXhlZCgyKX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gZm9udC1ib2xkIHRleHQtbGcgYm9yZGVyLXQgcHQtMlwiPlxuICAgICAgICAgICAgICAgICAgPHNwYW4+VG90YWw6PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPHNwYW4+JHt0b3RhbHMudG90YWwudG9GaXhlZCgyKX08L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogUGF5bWVudCBUZXJtcyAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwicGF5bWVudFRlcm1zXCI+UGF5bWVudCBUZXJtczwvTGFiZWw+XG4gICAgICAgICAgICAgIDx0ZXh0YXJlYVxuICAgICAgICAgICAgICAgIGlkPVwicGF5bWVudFRlcm1zXCJcbiAgICAgICAgICAgICAgICB7Li4ucmVnaXN0ZXIoJ3BheW1lbnRUZXJtcycpfVxuICAgICAgICAgICAgICAgIHJvd3M9ezN9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwXCJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIk5ldCAzMCwgMi8xMCBOZXQgMzAsIFBheW1lbnQgZHVlIHVwb24gcmVjZWlwdCwgZXRjLi4uXCJcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInBheW1lbnREdWVEYXlzXCI+UGF5bWVudCBEdWUgKERheXMpPC9MYWJlbD5cbiAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgaWQ9XCJwYXltZW50RHVlRGF5c1wiXG4gICAgICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXG4gICAgICAgICAgICAgICAgbWluPVwiMFwiXG4gICAgICAgICAgICAgICAgey4uLnJlZ2lzdGVyKCdwYXltZW50RHVlRGF5cycsIHsgdmFsdWVBc051bWJlcjogdHJ1ZSB9KX1cbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIjMwXCJcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwIG10LTFcIj5OdW1iZXIgb2YgZGF5cyBhZnRlciBpbnZvaWNlIGRhdGU8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBUZXJtcyBhbmQgTm90ZXMgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC00XCI+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInRlcm1zXCI+VGVybXMgJiBDb25kaXRpb25zPC9MYWJlbD5cbiAgICAgICAgICAgICAgPHRleHRhcmVhXG4gICAgICAgICAgICAgICAgaWQ9XCJ0ZXJtc1wiXG4gICAgICAgICAgICAgICAgey4uLnJlZ2lzdGVyKCd0ZXJtcycpfVxuICAgICAgICAgICAgICAgIHJvd3M9ezR9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwXCJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkdlbmVyYWwgdGVybXMsIGRlbGl2ZXJ5IGNvbmRpdGlvbnMsIHdhcnJhbnRpZXMsIGV0Yy4uLlwiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJub3Rlc1wiPkludGVybmFsIE5vdGVzPC9MYWJlbD5cbiAgICAgICAgICAgICAgPHRleHRhcmVhXG4gICAgICAgICAgICAgICAgaWQ9XCJub3Rlc1wiXG4gICAgICAgICAgICAgICAgey4uLnJlZ2lzdGVyKCdub3RlcycpfVxuICAgICAgICAgICAgICAgIHJvd3M9ezR9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwXCJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkludGVybmFsIG5vdGVzIChub3QgdmlzaWJsZSB0byBjdXN0b21lcikuLi5cIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8RGlhbG9nRm9vdGVyPlxuICAgICAgICAgICAgPEJ1dHRvbiB0eXBlPVwiYnV0dG9uXCIgdmFyaWFudD1cIm91dGxpbmVcIiBvbkNsaWNrPXtoYW5kbGVDbG9zZX0+XG4gICAgICAgICAgICAgIENhbmNlbFxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8QnV0dG9uIHR5cGU9XCJzdWJtaXRcIiBkaXNhYmxlZD17aXNMb2FkaW5nfT5cbiAgICAgICAgICAgICAge2lzTG9hZGluZyA/ICdTYXZpbmcuLi4nIDogbW9kZSA9PT0gJ2NyZWF0ZScgPyAnQ3JlYXRlIFF1b3RhdGlvbicgOiAnVXBkYXRlIFF1b3RhdGlvbid9XG4gICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICA8L0RpYWxvZ0Zvb3Rlcj5cbiAgICAgICAgPC9mb3JtPlxuICAgICAgPC9EaWFsb2dDb250ZW50PlxuICAgIDwvRGlhbG9nPlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VGb3JtIiwidXNlRmllbGRBcnJheSIsInpvZFJlc29sdmVyIiwieiIsIkJ1dHRvbiIsIklucHV0IiwiTGFiZWwiLCJEaWFsb2ciLCJEaWFsb2dDb250ZW50IiwiRGlhbG9nRGVzY3JpcHRpb24iLCJEaWFsb2dGb290ZXIiLCJEaWFsb2dIZWFkZXIiLCJEaWFsb2dUaXRsZSIsIlBsdXMiLCJUcmFzaDIiLCJDYWxjdWxhdG9yIiwidG9hc3QiLCJxdW90YXRpb25JdGVtU2NoZW1hIiwib2JqZWN0IiwiZGVzY3JpcHRpb24iLCJzdHJpbmciLCJtaW4iLCJxdWFudGl0eSIsIm51bWJlciIsInVuaXRQcmljZSIsImRpc2NvdW50IiwibWF4IiwiZGVmYXVsdCIsInRheFJhdGUiLCJxdW90YXRpb25TY2hlbWEiLCJ0aXRsZSIsIm9wdGlvbmFsIiwiY3VzdG9tZXJJZCIsImxlYWRJZCIsInN0YXR1cyIsImVudW0iLCJ2YWxpZFVudGlsIiwidGVybXMiLCJub3RlcyIsInBheW1lbnRUZXJtcyIsInBheW1lbnREdWVEYXlzIiwiaXRlbXMiLCJhcnJheSIsImRpc2NvdW50VHlwZSIsImRpc2NvdW50VmFsdWUiLCJRdW90YXRpb25Gb3JtIiwiaXNPcGVuIiwib25DbG9zZSIsIm9uU3VjY2VzcyIsInF1b3RhdGlvbiIsIm1vZGUiLCJwcmVzZWxlY3RlZEN1c3RvbWVySWQiLCJwcmVzZWxlY3RlZExlYWRJZCIsImlzTG9hZGluZyIsInNldElzTG9hZGluZyIsImN1c3RvbWVycyIsInNldEN1c3RvbWVycyIsImxlYWRzIiwic2V0TGVhZHMiLCJyZWdpc3RlciIsImhhbmRsZVN1Ym1pdCIsImZvcm1TdGF0ZSIsImVycm9ycyIsInJlc2V0Iiwid2F0Y2giLCJjb250cm9sIiwic2V0VmFsdWUiLCJyZXNvbHZlciIsImRlZmF1bHRWYWx1ZXMiLCJEYXRlIiwidG9JU09TdHJpbmciLCJzcGxpdCIsImZpZWxkcyIsImFwcGVuZCIsInJlbW92ZSIsIm5hbWUiLCJ3YXRjaGVkSXRlbXMiLCJ3YXRjaGVkVGF4UmF0ZSIsIndhdGNoZWREaXNjb3VudFR5cGUiLCJ3YXRjaGVkRGlzY291bnRWYWx1ZSIsImZldGNoRGF0YSIsImN1c3RvbWVyc1JlcyIsImxlYWRzUmVzIiwiUHJvbWlzZSIsImFsbCIsImZldGNoIiwib2siLCJjdXN0b21lcnNEYXRhIiwianNvbiIsImxlYWRzRGF0YSIsImVycm9yIiwiY29uc29sZSIsImNhbGN1bGF0ZVRvdGFscyIsInN1YnRvdGFsIiwicmVkdWNlIiwic3VtIiwiaXRlbSIsIml0ZW1Ub3RhbCIsImRpc2NvdW50QW1vdW50IiwiYWZ0ZXJEaXNjb3VudCIsInRheEFtb3VudCIsInRvdGFsIiwiZmluYWxUYXhBbW91bnQiLCJmaW5hbFRvdGFsIiwiTWF0aCIsInJvdW5kIiwidG90YWxzIiwiYWRkSXRlbSIsInJlbW92ZUl0ZW0iLCJpbmRleCIsImxlbmd0aCIsIm9uU3VibWl0IiwiZGF0YSIsInVybCIsImlkIiwibWV0aG9kIiwicmVzcG9uc2UiLCJoZWFkZXJzIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJFcnJvciIsInN1Y2Nlc3MiLCJtZXNzYWdlIiwiaGFuZGxlQ2xvc2UiLCJzdGF0dXNPcHRpb25zIiwidmFsdWUiLCJsYWJlbCIsIm9wZW4iLCJvbk9wZW5DaGFuZ2UiLCJjbGFzc05hbWUiLCJmb3JtIiwiZGl2IiwiaHRtbEZvciIsInBsYWNlaG9sZGVyIiwicCIsInNlbGVjdCIsIm1hcCIsIm9wdGlvbiIsImN1c3RvbWVyIiwiY29tcGFueSIsImxlYWQiLCJ0eXBlIiwidGV4dGFyZWEiLCJyb3dzIiwiaDMiLCJvbkNsaWNrIiwic2l6ZSIsImZpZWxkIiwidmFsdWVBc051bWJlciIsInN0ZXAiLCJ0b0ZpeGVkIiwidmFyaWFudCIsImRpc2FibGVkIiwic3BhbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/quotations/quotation-form.tsx\n"));

/***/ })

});