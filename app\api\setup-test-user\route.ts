import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'

export async function POST(request: NextRequest) {
  try {
    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })

    if (existingUser) {
      return NextResponse.json({ 
        message: 'Test user already exists',
        user: {
          email: existingUser.email,
          name: existingUser.name,
          role: existingUser.role
        }
      })
    }

    // Create test company first
    const company = await prisma.company.create({
      data: {
        name: 'Test Company',
        email: '<EMAIL>',
        phone: '******-0123',
        address: '123 Test Street',
        city: 'Test City',
        state: 'Test State',
        country: 'Test Country',
        postalCode: '12345',
        website: 'https://test.com',
        industry: 'Technology',
        size: 'SMALL',
        status: 'ACTIVE',
        subscriptionStatus: 'ACTIVE',
        subscriptionPlan: 'PROFESSIONAL',
        subscriptionStartDate: new Date(),
        subscriptionEndDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
        trialEndDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        maxUsers: 10,
        maxCustomers: 1000,
        maxQuotations: 1000,
        maxInvoices: 1000,
        maxContracts: 1000
      }
    })

    // Hash password
    const hashedPassword = await bcrypt.hash('password123', 12)

    // Create test user
    const user = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Test Admin',
        role: 'ADMIN',
        status: 'ACTIVE',
        emailVerified: new Date(),
        companyId: company.id,
        loginCount: 0
      }
    })

    return NextResponse.json({
      message: 'Test user created successfully',
      user: {
        email: user.email,
        name: user.name,
        role: user.role,
        companyId: user.companyId
      },
      company: {
        id: company.id,
        name: company.name
      },
      credentials: {
        email: '<EMAIL>',
        password: 'password123'
      }
    })
  } catch (error) {
    console.error('Error creating test user:', error)
    return NextResponse.json(
      { error: 'Failed to create test user', details: error },
      { status: 500 }
    )
  }
}
