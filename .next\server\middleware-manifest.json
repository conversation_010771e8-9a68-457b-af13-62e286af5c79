{"sortedMiddleware": ["/"], "middleware": {"/": {"files": ["prerender-manifest.js", "server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/dashboard(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/dashboard/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/admin(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/admin/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/super-admin(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/super-admin/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api\\/customers(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/api/customers/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api\\/leads(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/api/leads/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api\\/quotations(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/api/quotations/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api\\/invoices(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/api/invoices/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api\\/contracts(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/api/contracts/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api\\/activities(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/api/activities/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api\\/users(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/api/users/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api\\/super-admin(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/api/super-admin/:path*"}], "wasm": [], "assets": []}}, "functions": {}, "version": 2}