"use strict";(()=>{var e={};e.id=9185,e.ids=[9185],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},29958:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>h,originalPathname:()=>I,patchFetch:()=>b,requestAsyncStorage:()=>m,routeModule:()=>g,serverHooks:()=>f,staticGenerationAsyncStorage:()=>y,staticGenerationBailout:()=>w});var o={};t.r(o),t.d(o,{GET:()=>p,POST:()=>d});var s=t(95419),a=t(69108),n=t(99678),i=t(78070),l=t(81355),c=t(3205),u=t(9108);async function p(e){try{let e=await (0,l.getServerSession)(c.L);if(!e?.user||"SUPER_ADMIN"!==e.user.role)return i.Z.json({success:!1,error:"Unauthorized"},{status:401});let r=[];try{r=await u._.globalConfig.findMany()}catch(e){return console.error("Error fetching global config:",e),i.Z.json({success:!0,config:{}})}let t=r.reduce((e,r)=>{let t=r.value;if("boolean"===r.type)t="true"===t;else if("number"===r.type)t=parseInt(t);else if("json"===r.type)try{t=JSON.parse(t)}catch(e){t={}}return e[r.key]=t,e},{});return i.Z.json({success:!0,config:t})}catch(e){return console.error("Error fetching global config:",e),i.Z.json({success:!1,error:"Internal server error"},{status:500})}}async function d(e){try{let r=await (0,l.getServerSession)(c.L);if(!r?.user||"SUPER_ADMIN"!==r.user.role)return i.Z.json({success:!1,error:"Unauthorized"},{status:401});let t=await e.json(),o=[];for(let[e,r]of Object.entries(t)){let t=String(r),s="string";"boolean"==typeof r?(s="boolean",t=r.toString()):"number"==typeof r?(s="number",t=r.toString()):"object"==typeof r&&null!==r&&(s="json",t=JSON.stringify(r));try{o.push(u._.globalConfig.upsert({where:{key:e},update:{value:t,type:s,updatedAt:new Date},create:{key:e,value:t,type:s,description:`Configuration for ${e}`}}))}catch(r){console.error(`Error updating config for ${e}:`,r)}}try{await Promise.all(o)}catch(e){console.error("Error saving global config:",e)}try{await u._.auditLog.create({data:{action:"UPDATE_GLOBAL_CONFIG",entityType:"GLOBAL_CONFIG",entityId:"global",details:{updatedKeys:Object.keys(t),timestamp:new Date().toISOString()}}})}catch(e){console.error("Error creating audit log:",e)}return i.Z.json({success:!0,message:"Global configuration updated successfully"})}catch(e){return console.error("Error updating global config:",e),i.Z.json({success:!1,error:"Internal server error"},{status:500})}}let g=new s.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/super-admin/global-config/route",pathname:"/api/super-admin/global-config",filename:"route",bundlePath:"app/api/super-admin/global-config/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\super-admin\\global-config\\route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:m,staticGenerationAsyncStorage:y,serverHooks:f,headerHooks:h,staticGenerationBailout:w}=g,I="/api/super-admin/global-config/route";function b(){return(0,n.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:y})}},3205:(e,r,t)=>{t.d(r,{L:()=>c});var o=t(86485),s=t(10375),a=t(50694),n=t(6521),i=t.n(n),l=t(9108);let c={providers:[(0,o.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let r=await l._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),t=r?.companyId;if(!t&&r){let e=await l._.company.findFirst({where:{ownerId:r.id},select:{id:!0}});(t=e?.id)&&await l._.user.update({where:{id:r.id},data:{companyId:t}})}if(!r)return console.log("User not found:",e.email),null;if(!r.password)return console.log("User has no password set:",e.email),null;if(!await i().compare(e.password,r.password))return console.log("Invalid password for user:",e.email),null;return await l._.user.update({where:{id:r.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",r.email),{id:r.id,email:r.email,name:r.name,role:r.role,companyId:t}}catch(e){return console.error("Authentication error:",e),null}}}),(0,s.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,a.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(console.log("JWT callback - user data:",{id:r.id,email:r.email,role:r.role,companyId:r.companyId}),e.role=r.role,e.companyId=r.companyId),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role,e.user.companyId=r.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,r,t)=>{t.d(r,{_:()=>s});let o=require("@prisma/client"),s=globalThis.prisma??new o.PrismaClient}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[1638,6206,6521,2455,4520],()=>t(29958));module.exports=o})();