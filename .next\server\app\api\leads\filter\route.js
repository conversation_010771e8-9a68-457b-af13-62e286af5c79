"use strict";(()=>{var e={};e.id=7943,e.ids=[7943],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},18710:(e,t,i)=>{i.r(t),i.d(t,{headerHooks:()=>A,originalPathname:()=>I,patchFetch:()=>O,requestAsyncStorage:()=>h,routeModule:()=>y,serverHooks:()=>x,staticGenerationAsyncStorage:()=>f,staticGenerationBailout:()=>w});var a={};i.r(a),i.d(a,{POST:()=>v});var o=i(95419),r=i(69108),n=i(99678),s=i(78070),l=i(81355),c=i(3205),d=i(9108),u=i(25252),p=i(52178);let m=u.Ry({search:u.Z_().optional(),status:u.IX(u.Z_()).optional(),priority:u.IX(u.Z_()).optional(),source:u.IX(u.Z_()).optional(),industry:u.IX(u.Z_()).optional(),companySize:u.IX(u.Z_()).optional(),scoreMin:u.Rx().min(0).max(100).optional(),scoreMax:u.Rx().min(0).max(100).optional(),temperature:u.IX(u.Km(["HOT","WARM","COLD"])).optional(),createdAfter:u.Z_().optional(),createdBefore:u.Z_().optional(),lastActivityAfter:u.Z_().optional(),lastActivityBefore:u.Z_().optional(),convertedAfter:u.Z_().optional(),convertedBefore:u.Z_().optional(),budgetMin:u.Rx().optional(),budgetMax:u.Rx().optional(),hasBudget:u.O7().optional(),hasActivities:u.O7().optional(),activityCountMin:u.Rx().optional(),activityCountMax:u.Rx().optional(),hasRecentActivity:u.O7().optional(),assignedTo:u.IX(u.Z_()).optional(),unassigned:u.O7().optional(),isQualified:u.O7().optional(),hasPhone:u.O7().optional(),hasEmail:u.O7().optional(),hasWebsite:u.O7().optional(),hasTimeline:u.O7().optional(),isConverted:u.O7().optional(),conversionType:u.IX(u.Z_()).optional(),page:u.Rx().min(1).default(1),limit:u.Rx().min(1).max(100).default(20),sortBy:u.Km(["createdAt","updatedAt","score","lastName","companyName","lastActivity"]).default("createdAt"),sortOrder:u.Km(["asc","desc"]).default("desc"),export:u.O7().default(!1),exportFormat:u.Km(["csv","xlsx"]).default("csv")});async function v(e){try{var t,i;let a=await (0,l.getServerSession)(c.L);if(!a?.user?.id||!a?.user?.companyId)return s.Z.json({error:"Unauthorized"},{status:401});let o=await e.json(),r=m.parse(o),n={companyId:a.user.companyId};if(r.search){let e=r.search.toLowerCase();n.OR=[{firstName:{contains:e,mode:"insensitive"}},{lastName:{contains:e,mode:"insensitive"}},{email:{contains:e,mode:"insensitive"}},{phone:{contains:e,mode:"insensitive"}},{companyName:{contains:e,mode:"insensitive"}},{title:{contains:e,mode:"insensitive"}},{website:{contains:e,mode:"insensitive"}}]}if(r.status&&r.status.length>0&&(n.status={in:r.status}),r.priority&&r.priority.length>0&&(n.priority={in:r.priority}),r.source&&r.source.length>0&&(n.source={in:r.source}),r.industry&&r.industry.length>0&&(n.industry={in:r.industry}),r.companySize&&r.companySize.length>0&&(n.companySize={in:r.companySize}),(void 0!==r.scoreMin||void 0!==r.scoreMax)&&(n.score={},void 0!==r.scoreMin&&(n.score.gte=r.scoreMin),void 0!==r.scoreMax&&(n.score.lte=r.scoreMax)),r.temperature&&r.temperature.length>0){let e=[];r.temperature.includes("HOT")&&e.push({score:{gte:70}}),r.temperature.includes("WARM")&&e.push({score:{gte:40,lt:70}}),r.temperature.includes("COLD")&&e.push({score:{lt:40}}),e.length>0&&(n.OR=n.OR?[...n.OR,...e]:e)}(r.createdAfter||r.createdBefore)&&(n.createdAt={},r.createdAfter&&(n.createdAt.gte=new Date(r.createdAfter)),r.createdBefore&&(n.createdAt.lte=new Date(r.createdBefore))),(r.convertedAfter||r.convertedBefore)&&(n.convertedAt={},r.convertedAfter&&(n.convertedAt.gte=new Date(r.convertedAfter)),r.convertedBefore&&(n.convertedAt.lte=new Date(r.convertedBefore))),(void 0!==r.budgetMin||void 0!==r.budgetMax)&&(n.budget={},void 0!==r.budgetMin&&(n.budget.gte=r.budgetMin),void 0!==r.budgetMax&&(n.budget.lte=r.budgetMax)),void 0!==r.hasBudget&&(r.hasBudget?n.budget={gt:0}:n.OR=[{budget:null},{budget:0}]),r.assignedTo&&r.assignedTo.length>0&&(n.assignedToId={in:r.assignedTo}),r.unassigned&&(n.assignedToId=null),void 0!==r.hasPhone&&(r.hasPhone?n.phone={not:null}:n.phone=null),void 0!==r.hasEmail&&(r.hasEmail?n.email={not:null}:n.email=null),void 0!==r.hasWebsite&&(r.hasWebsite?n.website={not:null}:n.website=null),void 0!==r.hasTimeline&&(r.hasTimeline?n.timeline={not:null}:n.timeline=null),void 0!==r.isConverted&&(r.isConverted?n.status="CONVERTED":n.status={not:"CONVERTED"}),r.conversionType&&r.conversionType.length>0&&(n.conversion={conversionType:{in:r.conversionType}}),void 0!==r.hasActivities||void 0!==r.activityCountMin||void 0!==r.activityCountMax||void 0!==r.hasRecentActivity||r.lastActivityAfter||r.lastActivityBefore;let u={};"lastActivity"===r.sortBy?u.activities={_count:r.sortOrder}:u[r.sortBy]=r.sortOrder;let[p,v]=await Promise.all([d._.lead.findMany({where:n,include:{assignedTo:{select:{id:!0,name:!0,email:!0}},activities:{select:{id:!0,createdAt:!0},orderBy:{createdAt:"desc"},take:1},conversion:{select:{id:!0,conversionType:!0,conversionValue:!0,createdAt:!0}},_count:{select:{activities:!0,leadNotes:!0}}},orderBy:u,skip:r.export?0:(r.page-1)*r.limit,take:r.export?void 0:r.limit}),d._.lead.count({where:n})]),y=p;if(void 0!==r.hasActivities&&(y=y.filter(e=>r.hasActivities?e._count.activities>0:0===e._count.activities)),void 0!==r.activityCountMin&&(y=y.filter(e=>e._count.activities>=r.activityCountMin)),void 0!==r.activityCountMax&&(y=y.filter(e=>e._count.activities<=r.activityCountMax)),void 0!==r.hasRecentActivity){let e=new Date;e.setDate(e.getDate()-7),y=y.filter(t=>{let i=t.activities.length>0&&new Date(t.activities[0].createdAt)>=e;return r.hasRecentActivity?i:!i})}if(r.lastActivityAfter){let e=new Date(r.lastActivityAfter);y=y.filter(t=>t.activities.length>0&&new Date(t.activities[0].createdAt)>=e)}if(r.lastActivityBefore){let e=new Date(r.lastActivityBefore);y=y.filter(t=>t.activities.length>0&&new Date(t.activities[0].createdAt)<=e)}if(r.export)return g(y,r.exportFormat);let h={totalResults:y.length,statusBreakdown:function(e){let t={};return e.forEach(e=>{t[e.status]=(t[e.status]||0)+1}),t}(y),temperatureBreakdown:function(e){let t={HOT:0,WARM:0,COLD:0};return e.forEach(e=>{e.score>=70?t.HOT++:e.score>=40?t.WARM++:t.COLD++}),t}(y),averageScore:(t=y,0===t.length?0:Math.round(t.reduce((e,t)=>e+t.score,0)/t.length)),conversionRate:(i=y,0===i.length?0:Math.round(i.filter(e=>"CONVERTED"===e.status).length/i.length*100))};return s.Z.json({leads:y,pagination:{page:r.page,limit:r.limit,total:y.length,pages:Math.ceil(y.length/r.limit)},filters:r,summary:h})}catch(e){if(e instanceof p.jm)return s.Z.json({error:"Invalid filter parameters",details:e.errors},{status:400});return console.error("Error filtering leads:",e),s.Z.json({error:"Failed to filter leads"},{status:500})}}async function g(e,t){return s.Z.json({data:e,format:t,message:"Export functionality to be implemented"})}let y=new o.AppRouteRouteModule({definition:{kind:r.x.APP_ROUTE,page:"/api/leads/filter/route",pathname:"/api/leads/filter",filename:"route",bundlePath:"app/api/leads/filter/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\leads\\filter\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:h,staticGenerationAsyncStorage:f,serverHooks:x,headerHooks:A,staticGenerationBailout:w}=y,I="/api/leads/filter/route";function O(){return(0,n.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:f})}},3205:(e,t,i)=>{i.d(t,{L:()=>c});var a=i(86485),o=i(10375),r=i(50694),n=i(6521),s=i.n(n),l=i(9108);let c={providers:[(0,a.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await l._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),i=t?.companyId;if(!i&&t){let e=await l._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(i=e?.id)&&await l._.user.update({where:{id:t.id},data:{companyId:i}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await s().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await l._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:i}}catch(e){return console.error("Authentication error:",e),null}}}),(0,o.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,r.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,i)=>{i.d(t,{_:()=>o});let a=require("@prisma/client"),o=globalThis.prisma??new a.PrismaClient}};var t=require("../../../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),a=t.X(0,[1638,6206,6521,2455,4520,5252],()=>i(18710));module.exports=a})();