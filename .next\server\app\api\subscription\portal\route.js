"use strict";(()=>{var e={};e.id=3343,e.ids=[3343],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},32081:e=>{e.exports=require("child_process")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},22251:(e,r,s)=>{s.r(r),s.d(r,{headerHooks:()=>h,originalPathname:()=>_,patchFetch:()=>E,requestAsyncStorage:()=>y,routeModule:()=>m,serverHooks:()=>w,staticGenerationAsyncStorage:()=>I,staticGenerationBailout:()=>g});var t={};s.r(t),s.d(t,{POST:()=>d});var a=s(95419),o=s(69108),i=s(99678),n=s(78070),l=s(81355),c=s(3205),u=s(9108),p=s(15922);async function d(e){try{let e=await (0,l.getServerSession)(c.L);if(!e?.user?.companyId)return n.Z.json({success:!1,error:"Unauthorized"},{status:401});let r=await u._.subscription.findFirst({where:{companyId:e.user.companyId,status:{in:["ACTIVE","TRIALING","PAST_DUE"]}}});if(!r||!r.stripeCustomerId)return n.Z.json({success:!1,error:"No active Stripe subscription found"},{status:404});let s=await (0,p.FL)({customerId:r.stripeCustomerId,returnUrl:"http://localhost:3000/subscription"});return n.Z.json({success:!0,data:{url:s.url}})}catch(e){return console.error("Error creating billing portal session:",e),n.Z.json({success:!1,error:"Failed to create billing portal session"},{status:500})}}let m=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/subscription/portal/route",pathname:"/api/subscription/portal",filename:"route",bundlePath:"app/api/subscription/portal/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\subscription\\portal\\route.ts",nextConfigOutput:"",userland:t}),{requestAsyncStorage:y,staticGenerationAsyncStorage:I,serverHooks:w,headerHooks:h,staticGenerationBailout:g}=m,_="/api/subscription/portal/route";function E(){return(0,i.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:I})}},3205:(e,r,s)=>{s.d(r,{L:()=>c});var t=s(86485),a=s(10375),o=s(50694),i=s(6521),n=s.n(i),l=s(9108);let c={providers:[(0,t.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let r=await l._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),s=r?.companyId;if(!s&&r){let e=await l._.company.findFirst({where:{ownerId:r.id},select:{id:!0}});(s=e?.id)&&await l._.user.update({where:{id:r.id},data:{companyId:s}})}if(!r)return console.log("User not found:",e.email),null;if(!r.password)return console.log("User has no password set:",e.email),null;if(!await n().compare(e.password,r.password))return console.log("Invalid password for user:",e.email),null;return await l._.user.update({where:{id:r.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",r.email),{id:r.id,email:r.email,name:r.name,role:r.role,companyId:s}}catch(e){return console.error("Authentication error:",e),null}}}),(0,a.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,o.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(console.log("JWT callback - user data:",{id:r.id,email:r.email,role:r.role,companyId:r.companyId}),e.role=r.role,e.companyId=r.companyId),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role,e.user.companyId=r.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,r,s)=>{s.d(r,{_:()=>a});let t=require("@prisma/client"),a=globalThis.prisma??new t.PrismaClient},15922:(e,r,s)=>{s.d(r,{$t:()=>c,Ag:()=>a,FL:()=>l,R:()=>n,Sh:()=>i,hT:()=>o});var t=s(91211);if(!process.env.STRIPE_SECRET_KEY)throw Error("STRIPE_SECRET_KEY is not set in environment variables");let a=new t.Z(process.env.STRIPE_SECRET_KEY,{apiVersion:"2024-06-20",typescript:!0}),o=async e=>(await a.customers.list({email:e,limit:1})).data[0]||null,i=async e=>await a.customers.create({email:e.email,name:e.name,metadata:{companyId:e.companyId}}),n=async e=>{let r={customer:e.customerId,payment_method_types:["card"],line_items:[{price:e.priceId,quantity:1}],mode:"subscription",success_url:e.successUrl,cancel_url:e.cancelUrl,allow_promotion_codes:!0};return e.trialPeriodDays&&e.trialPeriodDays>0&&(r.subscription_data={trial_period_days:e.trialPeriodDays}),await a.checkout.sessions.create(r)},l=async e=>await a.billingPortal.sessions.create({customer:e.customerId,return_url:e.returnUrl}),c=(e,r,s)=>a.webhooks.constructEvent(e,r,s)}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[1638,6206,6521,2455,4520,1211],()=>s(22251));module.exports=t})();