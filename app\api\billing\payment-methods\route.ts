import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { stripe } from '@/lib/stripe'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get current subscription
    const subscription = await prisma.subscription.findFirst({
      where: {
        companyId: session.user.companyId,
        status: { in: ['ACTIVE', 'TRIALING', 'PAST_DUE', 'CANCELED'] }
      }
    })

    if (!subscription || !subscription.stripeCustomerId) {
      return NextResponse.json({
        success: true,
        data: {
          paymentMethods: [],
          defaultPaymentMethod: null
        }
      })
    }

    // Get customer from Stripe
    const customer = await stripe.customers.retrieve(subscription.stripeCustomerId)
    
    if (customer.deleted) {
      return NextResponse.json(
        { success: false, error: 'Customer not found' },
        { status: 404 }
      )
    }

    // Get payment methods
    const paymentMethods = await stripe.paymentMethods.list({
      customer: subscription.stripeCustomerId,
      type: 'card'
    })

    const formattedPaymentMethods = paymentMethods.data.map(pm => ({
      id: pm.id,
      type: pm.type,
      card: pm.card ? {
        brand: pm.card.brand,
        last4: pm.card.last4,
        expMonth: pm.card.exp_month,
        expYear: pm.card.exp_year,
        funding: pm.card.funding,
        country: pm.card.country
      } : null,
      created: new Date(pm.created * 1000),
      isDefault: pm.id === customer.invoice_settings.default_payment_method
    }))

    return NextResponse.json({
      success: true,
      data: {
        paymentMethods: formattedPaymentMethods,
        defaultPaymentMethod: customer.invoice_settings.default_payment_method
      }
    })

  } catch (error) {
    console.error('Error fetching payment methods:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch payment methods' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { action, paymentMethodId } = body

    // Get current subscription
    const subscription = await prisma.subscription.findFirst({
      where: {
        companyId: session.user.companyId,
        status: { in: ['ACTIVE', 'TRIALING', 'PAST_DUE', 'CANCELED'] }
      }
    })

    if (!subscription || !subscription.stripeCustomerId) {
      return NextResponse.json(
        { success: false, error: 'No subscription found' },
        { status: 404 }
      )
    }

    switch (action) {
      case 'set_default':
        // Set default payment method
        await stripe.customers.update(subscription.stripeCustomerId, {
          invoice_settings: {
            default_payment_method: paymentMethodId
          }
        })

        return NextResponse.json({
          success: true,
          message: 'Default payment method updated'
        })

      case 'delete':
        // Delete payment method
        await stripe.paymentMethods.detach(paymentMethodId)

        return NextResponse.json({
          success: true,
          message: 'Payment method deleted'
        })

      case 'create_setup_intent':
        // Create setup intent for adding new payment method
        const setupIntent = await stripe.setupIntents.create({
          customer: subscription.stripeCustomerId,
          payment_method_types: ['card'],
          usage: 'off_session'
        })

        return NextResponse.json({
          success: true,
          data: {
            clientSecret: setupIntent.client_secret,
            setupIntentId: setupIntent.id
          }
        })

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('Error handling payment method action:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to handle payment method action' },
      { status: 500 }
    )
  }
}
