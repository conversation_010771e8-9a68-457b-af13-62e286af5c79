'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { QuotationForm } from '@/components/quotations/quotation-form'
import { 
  ArrowLeft, 
  Edit, 
  Trash2, 
  Send, 
  Download, 
  Copy,
  Mail,
  Phone,
  Building2,
  Calendar,
  User,
  FileText,
  DollarSign,
  Calculator,
  Activity,
  Plus
} from 'lucide-react'
import { toast } from 'react-hot-toast'
import Link from 'next/link'

interface QuotationItem {
  id: string
  description: string
  quantity: number
  unitPrice: number
  discount: number
  taxRate: number
}

interface Quotation {
  id: string
  quotationNumber: string
  title: string
  description: string | null
  status: 'DRAFT' | 'SENT' | 'VIEWED' | 'ACCEPTED' | 'REJECTED' | 'EXPIRED'
  validUntil: string | null
  terms: string | null
  notes: string | null
  taxRate: number
  discountType: 'PERCENTAGE' | 'FIXED'
  discountValue: number
  subtotal: number
  total: number
  taxAmount: number
  discountAmount: number
  createdAt: string
  updatedAt: string
  customer: {
    id: string
    name: string
    email: string | null
    company: string | null
    phone: string | null
    address: string | null
    city: string | null
    state: string | null
    country: string | null
    postalCode: string | null
  }
  lead: {
    id: string
    title: string
    status: string
  } | null
  createdBy: {
    name: string | null
    email: string | null
  }
  items: QuotationItem[]
  activities: any[]
  _count: {
    activities: number
  }
}

export default function QuotationDetailPage() {
  const params = useParams()
  const router = useRouter()
  const [quotation, setQuotation] = useState<Quotation | null>(null)
  const [loading, setLoading] = useState(true)
  const [showEditForm, setShowEditForm] = useState(false)

  const fetchQuotation = async () => {
    try {
      const response = await fetch(`/api/quotations/${params.id}`)
      if (!response.ok) {
        if (response.status === 404) {
          toast.error('Quotation not found')
          router.push('/dashboard/quotations')
          return
        }
        throw new Error('Failed to fetch quotation')
      }
      
      const data = await response.json()
      setQuotation(data)
    } catch (error) {
      toast.error('Failed to load quotation details')
      console.error('Error fetching quotation:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (params.id) {
      fetchQuotation()
    }
  }, [params.id])

  const handleDelete = async () => {
    if (!quotation || !confirm(`Are you sure you want to delete quotation "${quotation.quotationNumber}"?`)) return

    try {
      const response = await fetch(`/api/quotations/${quotation.id}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to delete quotation')
      }

      toast.success('Quotation deleted successfully')
      router.push('/dashboard/quotations')
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to delete quotation')
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'DRAFT':
        return <Badge variant="secondary">Draft</Badge>
      case 'SENT':
        return <Badge variant="info">Sent</Badge>
      case 'VIEWED':
        return <Badge variant="warning">Viewed</Badge>
      case 'ACCEPTED':
        return <Badge variant="success">Accepted</Badge>
      case 'REJECTED':
        return <Badge variant="destructive">Rejected</Badge>
      case 'EXPIRED':
        return <Badge variant="secondary">Expired</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!quotation) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Quotation not found</p>
        <Button asChild className="mt-4">
          <Link href="/dashboard/quotations">Back to Quotations</Link>
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/dashboard/quotations">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Quotations
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{quotation.quotationNumber}</h1>
            <div className="flex items-center space-x-2 mt-1">
              {getStatusBadge(quotation.status)}
              <span className="text-gray-500">• {quotation.title}</span>
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline">
            <Copy className="h-4 w-4 mr-2" />
            Duplicate
          </Button>
          <Button variant="outline">
            <Send className="h-4 w-4 mr-2" />
            Send
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            PDF
          </Button>
          <Button variant="outline" onClick={() => setShowEditForm(true)}>
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
          <Button variant="destructive" onClick={handleDelete}>
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Customer Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="h-5 w-5 mr-2" />
                Customer Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-500">Customer Name</p>
                  <p className="font-medium">{quotation.customer.name}</p>
                </div>
                
                {quotation.customer.company && (
                  <div>
                    <p className="text-sm text-gray-500">Company</p>
                    <p className="font-medium">{quotation.customer.company}</p>
                  </div>
                )}
                
                {quotation.customer.email && (
                  <div>
                    <p className="text-sm text-gray-500">Email</p>
                    <p className="font-medium">{quotation.customer.email}</p>
                  </div>
                )}
                
                {quotation.customer.phone && (
                  <div>
                    <p className="text-sm text-gray-500">Phone</p>
                    <p className="font-medium">{quotation.customer.phone}</p>
                  </div>
                )}
              </div>

              {/* Address */}
              {(quotation.customer.address || quotation.customer.city) && (
                <div className="pt-4 border-t">
                  <p className="text-sm text-gray-500 mb-2">Address</p>
                  <div className="text-gray-900">
                    {quotation.customer.address && <p>{quotation.customer.address}</p>}
                    <p>
                      {[quotation.customer.city, quotation.customer.state, quotation.customer.postalCode].filter(Boolean).join(', ')}
                    </p>
                    {quotation.customer.country && <p>{quotation.customer.country}</p>}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Items */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="h-5 w-5 mr-2" />
                Items
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-2">Description</th>
                      <th className="text-right py-2">Qty</th>
                      <th className="text-right py-2">Unit Price</th>
                      <th className="text-right py-2">Discount</th>
                      <th className="text-right py-2">Tax</th>
                      <th className="text-right py-2">Total</th>
                    </tr>
                  </thead>
                  <tbody>
                    {quotation.items && Array.isArray(quotation.items) ? quotation.items.map((item, index) => {
                      const itemTotal = item.quantity * item.unitPrice
                      const discountAmount = (itemTotal * item.discount) / 100
                      const afterDiscount = itemTotal - discountAmount
                      const taxAmount = (afterDiscount * item.taxRate) / 100
                      const finalTotal = afterDiscount + taxAmount

                      return (
                        <tr key={index} className="border-b">
                          <td className="py-3">{item.description}</td>
                          <td className="text-right py-3">{item.quantity}</td>
                          <td className="text-right py-3">${item.unitPrice.toFixed(2)}</td>
                          <td className="text-right py-3">{item.discount}%</td>
                          <td className="text-right py-3">{item.taxRate}%</td>
                          <td className="text-right py-3 font-medium">${finalTotal.toFixed(2)}</td>
                        </tr>
                      )
                    }) : (
                      <tr>
                        <td colSpan={6} className="text-center py-4 text-gray-500">
                          No items found
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>

          {/* Terms and Notes */}
          {(quotation.terms || quotation.notes) && (
            <Card>
              <CardHeader>
                <CardTitle>Additional Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {quotation.terms && (
                  <div>
                    <p className="text-sm text-gray-500 mb-2">Terms & Conditions</p>
                    <p className="text-gray-900 whitespace-pre-wrap">{quotation.terms}</p>
                  </div>
                )}
                
                {quotation.notes && (
                  <div className="pt-4 border-t">
                    <p className="text-sm text-gray-500 mb-2">Internal Notes</p>
                    <p className="text-gray-900 whitespace-pre-wrap">{quotation.notes}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Activity Timeline */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center">
                  <Activity className="h-5 w-5 mr-2" />
                  Activity Timeline
                </CardTitle>
                <Button variant="outline" size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Note
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {quotation.activities && Array.isArray(quotation.activities) && quotation.activities.length > 0 ? (
                <div className="space-y-4">
                  {quotation.activities.map((activity) => (
                    <div key={activity.id} className="flex items-start space-x-3 pb-4 border-b last:border-b-0">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <Activity className="h-4 w-4 text-blue-600" />
                      </div>
                      <div className="flex-1">
                        <p className="font-medium">{activity.title}</p>
                        <p className="text-sm text-gray-600">{activity.description}</p>
                        <p className="text-xs text-gray-500 mt-1">
                          {new Date(activity.createdAt).toLocaleDateString()} by {activity.createdBy.name}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-4">No activity recorded yet</p>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calculator className="h-5 w-5 mr-2" />
                Summary
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Subtotal:</span>
                  <span>${quotation.subtotal.toFixed(2)}</span>
                </div>
                
                {quotation.discountAmount > 0 && (
                  <div className="flex justify-between text-red-600">
                    <span>Discount:</span>
                    <span>-${quotation.discountAmount.toFixed(2)}</span>
                  </div>
                )}
                
                {quotation.taxAmount > 0 && (
                  <div className="flex justify-between">
                    <span>Tax:</span>
                    <span>${quotation.taxAmount.toFixed(2)}</span>
                  </div>
                )}
                
                <div className="flex justify-between font-bold text-lg border-t pt-2">
                  <span>Total:</span>
                  <span>${quotation.total.toFixed(2)}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Details */}
          <Card>
            <CardHeader>
              <CardTitle>Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4 text-gray-400" />
                  <span className="text-sm">Created</span>
                </div>
                <span className="font-medium text-sm">
                  {new Date(quotation.createdAt).toLocaleDateString()}
                </span>
              </div>
              
              {quotation.validUntil && (
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-gray-400" />
                    <span className="text-sm">Valid Until</span>
                  </div>
                  <span className="font-medium text-sm">
                    {new Date(quotation.validUntil).toLocaleDateString()}
                  </span>
                </div>
              )}
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <User className="h-4 w-4 text-gray-400" />
                  <span className="text-sm">Created By</span>
                </div>
                <span className="font-medium text-sm">
                  {quotation.createdBy.name || 'Unknown'}
                </span>
              </div>

              {quotation.lead && (
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <FileText className="h-4 w-4 text-gray-400" />
                    <span className="text-sm">Related Lead</span>
                  </div>
                  <Link 
                    href={`/dashboard/leads/${quotation.lead.id}`}
                    className="font-medium text-sm text-blue-600 hover:underline"
                  >
                    {quotation.lead.name}
                  </Link>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button variant="outline" className="w-full justify-start">
                <Send className="h-4 w-4 mr-2" />
                Send to Customer
              </Button>
              
              <Button variant="outline" className="w-full justify-start">
                <Download className="h-4 w-4 mr-2" />
                Download PDF
              </Button>
              
              <Button variant="outline" className="w-full justify-start" asChild>
                <Link href={`/dashboard/invoices/new?quotationId=${quotation.id}`}>
                  <DollarSign className="h-4 w-4 mr-2" />
                  Convert to Invoice
                </Link>
              </Button>
              
              <Button variant="outline" className="w-full justify-start">
                <Copy className="h-4 w-4 mr-2" />
                Duplicate Quotation
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Edit Form Modal */}
      <QuotationForm
        isOpen={showEditForm}
        onClose={() => setShowEditForm(false)}
        onSuccess={fetchQuotation}
        quotation={quotation}
        mode="edit"
      />
    </div>
  )
}
