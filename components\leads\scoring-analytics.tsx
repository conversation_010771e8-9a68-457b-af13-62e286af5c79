'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  BarChart3,
  <PERSON><PERSON>dingUp,
  Users,
  Target,
  RefreshCw,
  Thermometer,
  AlertTriangle,
  CheckCircle,
  Activity
} from 'lucide-react'
import { toast } from 'react-hot-toast'
import { LeadTemperatureCompact } from './lead-temperature'

interface ScoringAnalytics {
  summary: {
    totalLeads: number
    averageScore: number
    recentLeads: number
    recentAverageScore: number
  }
  temperatureDistribution: {
    HOT: number
    WARM: number
    COLD: number
  }
  scoreRanges: {
    '0-20': number
    '20-40': number
    '40-60': number
    '60-80': number
    '80-100': number
  }
  statusAnalysis: Record<string, {
    count: number
    averageScore: number
  }>
  topPerformers: Array<{
    id: string
    score: number
    status: string
  }>
  bottomPerformers: Array<{
    id: string
    score: number
    status: string
  }>
  recommendations: Array<{
    type: string
    priority: string
    message: string
    action: string
  }>
}

export function ScoringAnalytics() {
  const [analytics, setAnalytics] = useState<ScoringAnalytics | null>(null)
  const [loading, setLoading] = useState(true)
  const [recalculating, setRecalculating] = useState(false)

  const fetchAnalytics = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/leads/scoring/bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'analyze' }),
      })

      if (!response.ok) {
        throw new Error('Failed to fetch analytics')
      }

      const data = await response.json()
      setAnalytics(data)
    } catch (error) {
      toast.error('Failed to load scoring analytics')
      console.error('Error fetching analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleRecalculateAll = async () => {
    try {
      setRecalculating(true)
      const response = await fetch('/api/leads/scoring/bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'recalculate' }),
      })

      if (!response.ok) {
        throw new Error('Failed to recalculate scores')
      }

      const result = await response.json()
      toast.success(`Recalculated ${result.updatedCount} lead scores`)
      fetchAnalytics() // Refresh analytics
    } catch (error) {
      toast.error('Failed to recalculate scores')
      console.error('Error recalculating scores:', error)
    } finally {
      setRecalculating(false)
    }
  }

  useEffect(() => {
    fetchAnalytics()
  }, [])

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT':
        return 'text-red-600 bg-red-100'
      case 'HIGH':
        return 'text-orange-600 bg-orange-100'
      case 'MEDIUM':
        return 'text-yellow-600 bg-yellow-100'
      case 'LOW':
        return 'text-green-600 bg-green-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!analytics) {
    return (
      <div className="text-center py-8 text-gray-500">
        <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-gray-300" />
        <p>Failed to load analytics data</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">Lead Scoring Analytics</h2>
        <Button onClick={handleRecalculateAll} disabled={recalculating}>
          <RefreshCw className={`h-4 w-4 mr-2 ${recalculating ? 'animate-spin' : ''}`} />
          {recalculating ? 'Recalculating...' : 'Recalculate All Scores'}
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-blue-100 rounded-full">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Total Leads</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.summary.totalLeads}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-green-100 rounded-full">
                <Target className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Average Score</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.summary.averageScore}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-purple-100 rounded-full">
                <Activity className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Recent Leads (30d)</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.summary.recentLeads}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-orange-100 rounded-full">
                <TrendingUp className="h-6 w-6 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Recent Avg Score</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.summary.recentAverageScore}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Temperature Distribution */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Thermometer className="h-5 w-5 mr-2" />
            Lead Temperature Distribution
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-3xl font-bold text-red-600">{analytics.temperatureDistribution.HOT}</div>
              <div className="text-sm text-gray-500">Hot Leads</div>
              <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                <div
                  className="bg-red-600 h-2 rounded-full"
                  style={{ 
                    width: `${(analytics.temperatureDistribution.HOT / analytics.summary.totalLeads) * 100}%` 
                  }}
                ></div>
              </div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-orange-600">{analytics.temperatureDistribution.WARM}</div>
              <div className="text-sm text-gray-500">Warm Leads</div>
              <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                <div
                  className="bg-orange-600 h-2 rounded-full"
                  style={{ 
                    width: `${(analytics.temperatureDistribution.WARM / analytics.summary.totalLeads) * 100}%` 
                  }}
                ></div>
              </div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600">{analytics.temperatureDistribution.COLD}</div>
              <div className="text-sm text-gray-500">Cold Leads</div>
              <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                <div
                  className="bg-blue-600 h-2 rounded-full"
                  style={{ 
                    width: `${(analytics.temperatureDistribution.COLD / analytics.summary.totalLeads) * 100}%` 
                  }}
                ></div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Score Ranges */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <BarChart3 className="h-5 w-5 mr-2" />
            Score Distribution
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Object.entries(analytics.scoreRanges).map(([range, count]) => (
              <div key={range} className="flex items-center justify-between">
                <span className="text-sm font-medium">{range} points</span>
                <div className="flex items-center space-x-2">
                  <div className="w-32 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full"
                      style={{ width: `${(count / analytics.summary.totalLeads) * 100}%` }}
                    ></div>
                  </div>
                  <span className="text-sm text-gray-500 w-8">{count}</span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Top and Bottom Performers */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
              Top Performers
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {analytics.topPerformers.slice(0, 5).map((lead, index) => (
                <div key={lead.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium">#{index + 1}</span>
                    <LeadTemperatureCompact score={lead.score} />
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline" className="text-xs">
                      {lead.status}
                    </Badge>
                    <span className="text-sm font-bold">{lead.score}</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2 text-red-600" />
              Needs Attention
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {analytics.bottomPerformers.slice(0, 5).map((lead, index) => (
                <div key={lead.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium">#{index + 1}</span>
                    <LeadTemperatureCompact score={lead.score} />
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline" className="text-xs">
                      {lead.status}
                    </Badge>
                    <span className="text-sm font-bold">{lead.score}</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recommendations */}
      {analytics.recommendations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Recommendations</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.recommendations.map((rec, index) => (
                <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                  <Badge className={`${getPriorityColor(rec.priority)} text-xs`}>
                    {rec.priority}
                  </Badge>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">{rec.message}</p>
                    <p className="text-xs text-gray-500 mt-1">Recommended action: {rec.action}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
