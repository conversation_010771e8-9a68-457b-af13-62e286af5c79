// Simple test script to verify payment API functionality
// This can be run in the browser console on the payments page

async function testPaymentAPI() {
  console.log('🧪 Testing Payment API...');
  
  try {
    // Test GET /api/payments
    console.log('📥 Testing GET /api/payments...');
    const response = await fetch('/api/payments');
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ GET /api/payments - SUCCESS');
      console.log(`📊 Found ${data.payments?.length || 0} payments`);
      console.log(`💰 Total amount: $${data.summary?.totalAmount || 0}`);
      console.log(`📈 Completed payments: ${data.summary?.completedCount || 0}`);
    } else {
      console.log('❌ GET /api/payments - FAILED');
      console.log('Error:', data);
    }
    
    // Test POST /api/payments
    console.log('\n📤 Testing POST /api/payments...');
    const testPayment = {
      amount: 999.99,
      paymentMethod: 'CREDIT_CARD',
      reference: 'TEST-' + Date.now(),
      description: 'Test payment from API test',
      notes: 'This is a test payment created by the API test script'
    };
    
    const createResponse = await fetch('/api/payments', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testPayment)
    });
    
    const createData = await createResponse.json();
    
    if (createResponse.ok) {
      console.log('✅ POST /api/payments - SUCCESS');
      console.log('💳 Payment created:', createData.payment);
    } else {
      console.log('❌ POST /api/payments - FAILED');
      console.log('Error:', createData);
    }
    
    console.log('\n🎉 Payment API test completed!');
    
  } catch (error) {
    console.log('❌ API Test failed with error:', error);
  }
}

// Instructions for use:
console.log('🔧 Payment API Test Script Loaded');
console.log('📋 To run the test, execute: testPaymentAPI()');
console.log('🌐 Make sure you are on the payments page and logged in');
