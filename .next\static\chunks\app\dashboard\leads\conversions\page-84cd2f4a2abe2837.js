(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8115],{92457:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62898).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},28203:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62898).Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},6141:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62898).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},41298:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62898).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},30525:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62898).Z)("PieChart",[["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}],["path",{d:"M22 12A10 10 0 0 0 12 2v10z",key:"1rfc4y"}]])},64280:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62898).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},66654:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62898).Z)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},25750:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62898).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},95489:function(e,t,s){Promise.resolve().then(s.bind(s,52781))},52781:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return b}});var r=s(57437),a=s(2265),n=s(27815),i=s(85754),l=s(31478),o=s(45509),c=s(92457),d=s(64280),u=s(25750),m=s(66654),f=s(41298),p=s(6141),x=s(30525);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let h=(0,s(62898).Z)("Award",[["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}],["path",{d:"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11",key:"em7aur"}]]);var y=s(28203),g=s(5925);function v(){let[e,t]=(0,a.useState)(null),[s,v]=(0,a.useState)(!0),[b,j]=(0,a.useState)("30"),N=async()=>{try{v(!0);let e=await fetch("/api/leads/conversions/analytics?period=".concat(b));if(!e.ok)throw Error("Failed to fetch analytics");let s=await e.json();t(s)}catch(e){g.toast.error("Failed to load conversion analytics"),console.error("Error fetching analytics:",e)}finally{v(!1)}};(0,a.useEffect)(()=>{N()},[b]);let w=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e),k=e=>"".concat(e.toFixed(1),"%");return s?(0,r.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):e?(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Conversion Analytics"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)(o.Ph,{value:b,onValueChange:j,children:[(0,r.jsx)(o.i4,{className:"w-32",children:(0,r.jsx)(o.ki,{})}),(0,r.jsxs)(o.Bw,{children:[(0,r.jsx)(o.Ql,{value:"7",children:"Last 7 days"}),(0,r.jsx)(o.Ql,{value:"30",children:"Last 30 days"}),(0,r.jsx)(o.Ql,{value:"90",children:"Last 90 days"}),(0,r.jsx)(o.Ql,{value:"365",children:"Last year"})]})]}),(0,r.jsxs)(i.z,{variant:"outline",onClick:N,size:"sm",children:[(0,r.jsx)(d.Z,{className:"h-4 w-4 mr-2"}),"Refresh"]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,r.jsx)(n.Zb,{children:(0,r.jsx)(n.aY,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"p-3 bg-blue-100 rounded-full",children:(0,r.jsx)(u.Z,{className:"h-6 w-6 text-blue-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Total Leads"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.totalLeads})]})]})})}),(0,r.jsx)(n.Zb,{children:(0,r.jsx)(n.aY,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"p-3 bg-green-100 rounded-full",children:(0,r.jsx)(m.Z,{className:"h-6 w-6 text-green-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Converted"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.convertedLeads}),(0,r.jsxs)("p",{className:"text-xs text-green-600",children:[k(e.summary.conversionRate)," rate"]})]})]})})}),(0,r.jsx)(n.Zb,{children:(0,r.jsx)(n.aY,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"p-3 bg-purple-100 rounded-full",children:(0,r.jsx)(f.Z,{className:"h-6 w-6 text-purple-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Total Value"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:w(e.summary.totalConversionValue)}),(0,r.jsxs)("p",{className:"text-xs text-purple-600",children:[w(e.summary.averageConversionValue)," avg"]})]})]})})}),(0,r.jsx)(n.Zb,{children:(0,r.jsx)(n.aY,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"p-3 bg-orange-100 rounded-full",children:(0,r.jsx)(p.Z,{className:"h-6 w-6 text-orange-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Avg Time"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[Math.round(e.summary.averageConversionTime),"d"]}),(0,r.jsx)("p",{className:"text-xs text-orange-600",children:"to convert"})]})]})})})]}),(0,r.jsxs)(n.Zb,{children:[(0,r.jsx)(n.Ol,{children:(0,r.jsxs)(n.ll,{className:"flex items-center",children:[(0,r.jsx)(c.Z,{className:"h-5 w-5 mr-2"}),"Conversion Funnel"]})}),(0,r.jsx)(n.aY,{children:(0,r.jsx)("div",{className:"space-y-4",children:e.conversionFunnel.map((e,t)=>(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:e.stage}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-sm text-gray-500",children:e.count}),(0,r.jsx)("span",{className:"text-sm font-medium",children:k(e.percentage)})]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3",children:(0,r.jsx)("div",{className:"h-3 rounded-full ".concat(0===t?"bg-blue-600":1===t?"bg-yellow-600":2===t?"bg-orange-600":"bg-green-600"),style:{width:"".concat(e.percentage,"%")}})})]},e.stage))})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)(n.Zb,{children:[(0,r.jsx)(n.Ol,{children:(0,r.jsxs)(n.ll,{className:"flex items-center",children:[(0,r.jsx)(x.Z,{className:"h-5 w-5 mr-2"}),"Conversions by Type"]})}),(0,r.jsx)(n.aY,{children:(0,r.jsx)("div",{className:"space-y-3",children:e.conversionsByType.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:e.type}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:[e.count," conversions"]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("p",{className:"font-bold",children:w(e.totalValue)}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:[w(e.averageValue)," avg"]})]})]},e.type))})})]}),(0,r.jsxs)(n.Zb,{children:[(0,r.jsx)(n.Ol,{children:(0,r.jsx)(n.ll,{children:"Lead Source Performance"})}),(0,r.jsx)(n.aY,{children:(0,r.jsx)("div",{className:"space-y-3",children:e.sourceAnalysis.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:e.source||"Unknown"}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:[e.totalLeads," leads"]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("p",{className:"font-bold",children:[e.convertedLeads," converted"]}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:k(e.conversionRate)})]})]},e.source))})})]})]}),(0,r.jsxs)(n.Zb,{children:[(0,r.jsx)(n.Ol,{children:(0,r.jsxs)(n.ll,{className:"flex items-center",children:[(0,r.jsx)(h,{className:"h-5 w-5 mr-2"}),"Top Performing Sales Reps"]})}),(0,r.jsx)(n.aY,{children:(0,r.jsx)("div",{className:"space-y-3",children:e.topPerformers.slice(0,5).map((e,t)=>{var s,a;return(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full",children:(0,r.jsxs)("span",{className:"text-sm font-bold text-blue-600",children:["#",t+1]})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:(null===(s=e.salesRep)||void 0===s?void 0:s.name)||"Unknown"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:null===(a=e.salesRep)||void 0===a?void 0:a.email})]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("p",{className:"font-bold",children:[e._count.id," conversions"]}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:[w(e._sum.conversionValue||0)," total"]})]})]},e.salesRepId)})})})]}),(0,r.jsxs)(n.Zb,{children:[(0,r.jsx)(n.Ol,{children:(0,r.jsxs)(n.ll,{className:"flex items-center",children:[(0,r.jsx)(y.Z,{className:"h-5 w-5 mr-2"}),"Recent Conversions"]})}),(0,r.jsx)(n.aY,{children:(0,r.jsx)("div",{className:"space-y-3",children:e.recentConversions.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,r.jsxs)("p",{className:"font-medium",children:[e.lead.firstName," ",e.lead.lastName]}),(0,r.jsx)(l.C,{variant:"outline",className:"text-xs",children:e.conversionType})]}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:[e.lead.companyName," → ",e.customer.name]}),(0,r.jsx)("p",{className:"text-xs text-gray-400",children:new Date(e.createdAt).toLocaleDateString()})]}),(0,r.jsx)("div",{className:"text-right",children:(0,r.jsx)("p",{className:"font-bold text-green-600",children:w(e.conversionValue||0)})})]},e.id))})})]})]}):(0,r.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,r.jsx)(c.Z,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,r.jsx)("p",{children:"Failed to load analytics data"})]})}function b(){return(0,r.jsx)("div",{className:"container mx-auto py-6",children:(0,r.jsx)(v,{})})}},31478:function(e,t,s){"use strict";s.d(t,{C:function(){return l}});var r=s(57437);s(2265);var a=s(96061),n=s(1657);let i=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600",info:"border-transparent bg-blue-500 text-white hover:bg-blue-600"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:s,...a}=e;return(0,r.jsx)("div",{className:(0,n.cn)(i({variant:s}),t),...a})}},85754:function(e,t,s){"use strict";s.d(t,{z:function(){return c}});var r=s(57437),a=s(2265),n=s(67256),i=s(96061),l=s(1657);let o=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,t)=>{let{className:s,variant:a,size:i,asChild:c=!1,...d}=e,u=c?n.g7:"button";return(0,r.jsx)(u,{className:(0,l.cn)(o({variant:a,size:i,className:s})),ref:t,...d})});c.displayName="Button"},27815:function(e,t,s){"use strict";s.d(t,{Ol:function(){return l},SZ:function(){return c},Zb:function(){return i},aY:function(){return d},eW:function(){return u},ll:function(){return o}});var r=s(57437),a=s(2265),n=s(1657);let i=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...a})});i.displayName="Card";let l=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",s),...a})});l.displayName="CardHeader";let o=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",s),...a})});o.displayName="CardTitle";let c=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",s),...a})});c.displayName="CardDescription";let d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",s),...a})});d.displayName="CardContent";let u=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",s),...a})});u.displayName="CardFooter"},45509:function(e,t,s){"use strict";s.d(t,{Bw:function(){return x},Ph:function(){return d},Ql:function(){return h},i4:function(){return m},ki:function(){return u}});var r=s(57437),a=s(2265),n=s(99530),i=s(83523),l=s(9224),o=s(62442),c=s(1657);let d=n.fC;n.ZA;let u=n.B4,m=a.forwardRef((e,t)=>{let{className:s,children:a,...l}=e;return(0,r.jsxs)(n.xz,{ref:t,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",s),...l,children:[a,(0,r.jsx)(n.JO,{asChild:!0,children:(0,r.jsx)(i.Z,{className:"h-4 w-4 opacity-50"})})]})});m.displayName=n.xz.displayName;let f=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(n.u_,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",s),...a,children:(0,r.jsx)(l.Z,{className:"h-4 w-4"})})});f.displayName=n.u_.displayName;let p=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(n.$G,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",s),...a,children:(0,r.jsx)(i.Z,{className:"h-4 w-4"})})});p.displayName=n.$G.displayName;let x=a.forwardRef((e,t)=>{let{className:s,children:a,position:i="popper",...l}=e;return(0,r.jsx)(n.h_,{children:(0,r.jsxs)(n.VY,{ref:t,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:i,...l,children:[(0,r.jsx)(f,{}),(0,r.jsx)(n.l_,{className:(0,c.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),(0,r.jsx)(p,{})]})})});x.displayName=n.VY.displayName,a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(n.__,{ref:t,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",s),...a})}).displayName=n.__.displayName;let h=a.forwardRef((e,t)=>{let{className:s,children:a,...i}=e;return(0,r.jsxs)(n.ck,{ref:t,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...i,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(n.wU,{children:(0,r.jsx)(o.Z,{className:"h-4 w-4"})})}),(0,r.jsx)(n.eT,{children:a})]})});h.displayName=n.ck.displayName,a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(n.Z0,{ref:t,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",s),...a})}).displayName=n.Z0.displayName},1657:function(e,t,s){"use strict";s.d(t,{cn:function(){return n}});var r=s(57042),a=s(74769);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.m6)((0,r.W)(t))}},5925:function(e,t,s){"use strict";let r,a;s.r(t),s.d(t,{CheckmarkIcon:function(){return W},ErrorIcon:function(){return B},LoaderIcon:function(){return q},ToastBar:function(){return el},ToastIcon:function(){return et},Toaster:function(){return eu},default:function(){return em},resolveValue:function(){return k},toast:function(){return D},useToaster:function(){return I},useToasterStore:function(){return T}});var n,i=s(2265);let l={data:""},o=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||l,c=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,d=/\/\*[^]*?\*\/|  +/g,u=/\n+/g,m=(e,t)=>{let s="",r="",a="";for(let n in e){let i=e[n];"@"==n[0]?"i"==n[1]?s=n+" "+i+";":r+="f"==n[1]?m(i,n):n+"{"+m(i,"k"==n[1]?"":t)+"}":"object"==typeof i?r+=m(i,t?t.replace(/([^,])+/g,e=>n.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)):n):null!=i&&(n=/^--/.test(n)?n:n.replace(/[A-Z]/g,"-$&").toLowerCase(),a+=m.p?m.p(n,i):n+":"+i+";")}return s+(t&&a?t+"{"+a+"}":a)+r},f={},p=e=>{if("object"==typeof e){let t="";for(let s in e)t+=s+p(e[s]);return t}return e},x=(e,t,s,r,a)=>{var n;let i=p(e),l=f[i]||(f[i]=(e=>{let t=0,s=11;for(;t<e.length;)s=101*s+e.charCodeAt(t++)>>>0;return"go"+s})(i));if(!f[l]){let t=i!==e?e:(e=>{let t,s,r=[{}];for(;t=c.exec(e.replace(d,""));)t[4]?r.shift():t[3]?(s=t[3].replace(u," ").trim(),r.unshift(r[0][s]=r[0][s]||{})):r[0][t[1]]=t[2].replace(u," ").trim();return r[0]})(e);f[l]=m(a?{["@keyframes "+l]:t}:t,s?"":"."+l)}let o=s&&f.g?f.g:null;return s&&(f.g=f[l]),n=f[l],o?t.data=t.data.replace(o,n):-1===t.data.indexOf(n)&&(t.data=r?n+t.data:t.data+n),l},h=(e,t,s)=>e.reduce((e,r,a)=>{let n=t[a];if(n&&n.call){let e=n(s),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;n=t?"."+t:e&&"object"==typeof e?e.props?"":m(e,""):!1===e?"":e}return e+r+(null==n?"":n)},"");function y(e){let t=this||{},s=e.call?e(t.p):e;return x(s.unshift?s.raw?h(s,[].slice.call(arguments,1),t.p):s.reduce((e,s)=>Object.assign(e,s&&s.call?s(t.p):s),{}):s,o(t.target),t.g,t.o,t.k)}y.bind({g:1});let g,v,b,j=y.bind({k:1});function N(e,t){let s=this||{};return function(){let r=arguments;function a(n,i){let l=Object.assign({},n),o=l.className||a.className;s.p=Object.assign({theme:v&&v()},l),s.o=/ *go\d+/.test(o),l.className=y.apply(s,r)+(o?" "+o:""),t&&(l.ref=i);let c=e;return e[0]&&(c=l.as||e,delete l.as),b&&c[0]&&b(l),g(c,l)}return t?t(a):a}}var w=e=>"function"==typeof e,k=(e,t)=>w(e)?e(t):e,Z=(r=0,()=>(++r).toString()),C=()=>{if(void 0===a&&"u">typeof window){let e=matchMedia("(prefers-reduced-motion: reduce)");a=!e||e.matches}return a},E=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case 2:let{toast:s}=t;return E(e,{type:e.toasts.find(e=>e.id===s.id)?1:0,toast:s});case 3:let{toastId:r}=t;return{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,dismissed:!0,visible:!1}:e)};case 4:return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let a=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(e=>({...e,pauseDuration:e.pauseDuration+a}))}}},R=[],z={toasts:[],pausedAt:void 0},M=e=>{z=E(z,e),R.forEach(e=>{e(z)})},O={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},T=(e={})=>{let[t,s]=(0,i.useState)(z),r=(0,i.useRef)(z);(0,i.useEffect)(()=>(r.current!==z&&s(z),R.push(s),()=>{let e=R.indexOf(s);e>-1&&R.splice(e,1)}),[]);let a=t.toasts.map(t=>{var s,r,a;return{...e,...e[t.type],...t,removeDelay:t.removeDelay||(null==(s=e[t.type])?void 0:s.removeDelay)||(null==e?void 0:e.removeDelay),duration:t.duration||(null==(r=e[t.type])?void 0:r.duration)||(null==e?void 0:e.duration)||O[t.type],style:{...e.style,...null==(a=e[t.type])?void 0:a.style,...t.style}}});return{...t,toasts:a}},$=(e,t="blank",s)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...s,id:(null==s?void 0:s.id)||Z()}),_=e=>(t,s)=>{let r=$(t,e,s);return M({type:2,toast:r}),r.id},D=(e,t)=>_("blank")(e,t);D.error=_("error"),D.success=_("success"),D.loading=_("loading"),D.custom=_("custom"),D.dismiss=e=>{M({type:3,toastId:e})},D.remove=e=>M({type:4,toastId:e}),D.promise=(e,t,s)=>{let r=D.loading(t.loading,{...s,...null==s?void 0:s.loading});return"function"==typeof e&&(e=e()),e.then(e=>{let a=t.success?k(t.success,e):void 0;return a?D.success(a,{id:r,...s,...null==s?void 0:s.success}):D.dismiss(r),e}).catch(e=>{let a=t.error?k(t.error,e):void 0;a?D.error(a,{id:r,...s,...null==s?void 0:s.error}):D.dismiss(r)}),e};var A=(e,t)=>{M({type:1,toast:{id:e,height:t}})},L=()=>{M({type:5,time:Date.now()})},S=new Map,P=1e3,V=(e,t=P)=>{if(S.has(e))return;let s=setTimeout(()=>{S.delete(e),M({type:4,toastId:e})},t);S.set(e,s)},I=e=>{let{toasts:t,pausedAt:s}=T(e);(0,i.useEffect)(()=>{if(s)return;let e=Date.now(),r=t.map(t=>{if(t.duration===1/0)return;let s=(t.duration||0)+t.pauseDuration-(e-t.createdAt);if(s<0){t.visible&&D.dismiss(t.id);return}return setTimeout(()=>D.dismiss(t.id),s)});return()=>{r.forEach(e=>e&&clearTimeout(e))}},[t,s]);let r=(0,i.useCallback)(()=>{s&&M({type:6,time:Date.now()})},[s]),a=(0,i.useCallback)((e,s)=>{let{reverseOrder:r=!1,gutter:a=8,defaultPosition:n}=s||{},i=t.filter(t=>(t.position||n)===(e.position||n)&&t.height),l=i.findIndex(t=>t.id===e.id),o=i.filter((e,t)=>t<l&&e.visible).length;return i.filter(e=>e.visible).slice(...r?[o+1]:[0,o]).reduce((e,t)=>e+(t.height||0)+a,0)},[t]);return(0,i.useEffect)(()=>{t.forEach(e=>{if(e.dismissed)V(e.id,e.removeDelay);else{let t=S.get(e.id);t&&(clearTimeout(t),S.delete(e.id))}})},[t]),{toasts:t,handlers:{updateHeight:A,startPause:L,endPause:r,calculateOffset:a}}},F=j`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,Y=j`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,H=j`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,B=N("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${F} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${Y} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${H} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,U=j`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,q=N("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${U} 1s linear infinite;
`,Q=j`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,G=j`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,W=N("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Q} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${G} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,J=N("div")`
  position: absolute;
`,K=N("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,X=j`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,ee=N("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${X} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,et=({toast:e})=>{let{icon:t,type:s,iconTheme:r}=e;return void 0!==t?"string"==typeof t?i.createElement(ee,null,t):t:"blank"===s?null:i.createElement(K,null,i.createElement(q,{...r}),"loading"!==s&&i.createElement(J,null,"error"===s?i.createElement(B,{...r}):i.createElement(W,{...r})))},es=e=>`
0% {transform: translate3d(0,${-200*e}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,er=e=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${-150*e}%,-1px) scale(.6); opacity:0;}
`,ea=N("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,en=N("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,ei=(e,t)=>{let s=e.includes("top")?1:-1,[r,a]=C()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[es(s),er(s)];return{animation:t?`${j(r)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${j(a)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},el=i.memo(({toast:e,position:t,style:s,children:r})=>{let a=e.height?ei(e.position||t||"top-center",e.visible):{opacity:0},n=i.createElement(et,{toast:e}),l=i.createElement(en,{...e.ariaProps},k(e.message,e));return i.createElement(ea,{className:e.className,style:{...a,...s,...e.style}},"function"==typeof r?r({icon:n,message:l}):i.createElement(i.Fragment,null,n,l))});n=i.createElement,m.p=void 0,g=n,v=void 0,b=void 0;var eo=({id:e,className:t,style:s,onHeightUpdate:r,children:a})=>{let n=i.useCallback(t=>{if(t){let s=()=>{r(e,t.getBoundingClientRect().height)};s(),new MutationObserver(s).observe(t,{subtree:!0,childList:!0,characterData:!0})}},[e,r]);return i.createElement("div",{ref:n,className:t,style:s},a)},ec=(e,t)=>{let s=e.includes("top"),r=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:C()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(s?1:-1)}px)`,...s?{top:0}:{bottom:0},...r}},ed=y`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,eu=({reverseOrder:e,position:t="top-center",toastOptions:s,gutter:r,children:a,containerStyle:n,containerClassName:l})=>{let{toasts:o,handlers:c}=I(s);return i.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...n},className:l,onMouseEnter:c.startPause,onMouseLeave:c.endPause},o.map(s=>{let n=s.position||t,l=ec(n,c.calculateOffset(s,{reverseOrder:e,gutter:r,defaultPosition:t}));return i.createElement(eo,{id:s.id,key:s.id,onHeightUpdate:c.updateHeight,className:s.visible?ed:"",style:l},"custom"===s.type?k(s.message,s):a?a(s):i.createElement(el,{toast:s,position:n}))}))},em=D}},function(e){e.O(0,[6723,9502,1706,4138,4997,2971,4938,1744],function(){return e(e.s=95489)}),_N_E=e.O()}]);