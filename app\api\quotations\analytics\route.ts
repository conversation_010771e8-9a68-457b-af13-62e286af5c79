import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '30' // days
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - parseInt(period))

    // Get comprehensive quotation analytics
    const [
      totalQuotations,
      quotationsByStatus,
      quotationsByMonth,
      conversionRates,
      averageQuotationValue,
      topQuotationsByValue,
      recentQuotations,
      quotationTrends,
      customerQuotations,
      revenueForecast
    ] = await Promise.all([
      // Total quotations
      prisma.quotation.count({
        where: {
          companyId: session.user.companyId,
          createdAt: { gte: startDate }
        }
      }),

      // Quotations by status
      prisma.quotation.groupBy({
        by: ['status'],
        where: {
          companyId: session.user.companyId,
          createdAt: { gte: startDate }
        },
        _count: {
          id: true
        },
        _sum: {
          total: true
        }
      }),

      // Quotations by month (last 12 months)
      prisma.$queryRaw`
        SELECT 
          DATE_TRUNC('month', "createdAt") as month,
          COUNT(*)::int as quotation_count,
          SUM("total")::float as total_value
        FROM "Quotation" 
        WHERE "companyId" = ${session.user.companyId}
          AND "createdAt" >= ${new Date(Date.now() - 365 * 24 * 60 * 60 * 1000)}
        GROUP BY DATE_TRUNC('month', "createdAt")
        ORDER BY month DESC
        LIMIT 12
      `,

      // Conversion rates
      Promise.all([
        // Total sent quotations
        prisma.quotation.count({
          where: {
            companyId: session.user.companyId,
            status: { in: ['SENT', 'VIEWED', 'ACCEPTED', 'REJECTED'] },
            createdAt: { gte: startDate }
          }
        }),
        // Accepted quotations
        prisma.quotation.count({
          where: {
            companyId: session.user.companyId,
            status: 'ACCEPTED',
            createdAt: { gte: startDate }
          }
        }),
        // Viewed quotations
        prisma.quotation.count({
          where: {
            companyId: session.user.companyId,
            status: { in: ['VIEWED', 'ACCEPTED', 'REJECTED'] },
            createdAt: { gte: startDate }
          }
        })
      ]),

      // Average quotation value
      prisma.quotation.aggregate({
        where: {
          companyId: session.user.companyId,
          createdAt: { gte: startDate }
        },
        _avg: {
          total: true
        }
      }),

      // Top quotations by value
      prisma.quotation.findMany({
        where: {
          companyId: session.user.companyId,
          createdAt: { gte: startDate }
        },
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              company: true,
              email: true
            }
          },
          createdBy: {
            select: {
              name: true,
              email: true
            }
          }
        },
        orderBy: { total: 'desc' },
        take: 10
      }),

      // Recent quotations
      prisma.quotation.findMany({
        where: {
          companyId: session.user.companyId,
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
          }
        },
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              company: true
            }
          },
          createdBy: {
            select: {
              name: true,
              email: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: 10
      }),

      // Quotation trends (last 30 days)
      prisma.$queryRaw`
        SELECT 
          DATE("createdAt") as date,
          COUNT(*)::int as quotations_created,
          COUNT(CASE WHEN status = 'ACCEPTED' THEN 1 END)::int as quotations_accepted,
          SUM("total")::float as total_value
        FROM "Quotation" 
        WHERE "companyId" = ${session.user.companyId}
          AND "createdAt" >= ${new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)}
        GROUP BY DATE("createdAt")
        ORDER BY date DESC
        LIMIT 30
      `,

      // Customer quotation analysis
      prisma.quotation.groupBy({
        by: ['customerId'],
        where: {
          companyId: session.user.companyId,
          createdAt: { gte: startDate }
        },
        _count: {
          id: true
        },
        _sum: {
          total: true
        },
        orderBy: {
          _sum: {
            total: 'desc'
          }
        },
        take: 10
      }),

      // Revenue forecast (pending + accepted quotations)
      Promise.all([
        // Pending quotations (potential revenue)
        prisma.quotation.aggregate({
          where: {
            companyId: session.user.companyId,
            status: { in: ['DRAFT', 'SENT', 'VIEWED'] },
            validUntil: { gte: new Date() }
          },
          _sum: {
            total: true
          }
        }),
        // Accepted quotations (confirmed revenue)
        prisma.quotation.aggregate({
          where: {
            companyId: session.user.companyId,
            status: 'ACCEPTED',
            createdAt: { gte: startDate }
          },
          _sum: {
            total: true
          }
        }),
        // Expired quotations
        prisma.quotation.count({
          where: {
            companyId: session.user.companyId,
            status: { in: ['DRAFT', 'SENT', 'VIEWED'] },
            validUntil: { lt: new Date() }
          }
        })
      ])
    ])

    // Get customer details for customer quotations
    const customerIds = customerQuotations.map(q => q.customerId).filter(Boolean)
    const customers = await prisma.customer.findMany({
      where: {
        id: { in: customerIds },
        companyId: session.user.companyId
      },
      select: {
        id: true,
        name: true,
        company: true,
        email: true
      }
    })

    // Process customer quotations with details
    const customerQuotationsWithDetails = customerQuotations.map(item => {
      const customer = customers.find(c => c.id === item.customerId)
      return {
        customer: customer || { id: item.customerId, name: 'Unknown', company: null, email: null },
        quotationCount: item._count.id,
        totalValue: Number(item._sum.total || 0)
      }
    })

    // Calculate conversion rates
    const [totalSent, totalAccepted, totalViewed] = conversionRates
    const acceptanceRate = totalSent > 0 ? (totalAccepted / totalSent) * 100 : 0
    const viewRate = totalSent > 0 ? (totalViewed / totalSent) * 100 : 0

    // Calculate revenue metrics
    const [pendingRevenue, confirmedRevenue, expiredQuotations] = revenueForecast
    const totalPendingValue = Number(pendingRevenue._sum.total || 0)
    const totalConfirmedValue = Number(confirmedRevenue._sum.total || 0)

    // Calculate total value by status
    const totalValueByStatus = quotationsByStatus.reduce((acc, item) => {
      acc[item.status] = Number(item._sum.total || 0)
      return acc
    }, {} as Record<string, number>)

    // Calculate average time to acceptance
    const acceptedQuotations = await prisma.quotation.findMany({
      where: {
        companyId: session.user.companyId,
        status: 'ACCEPTED',
        acceptedAt: { not: null },
        createdAt: { gte: startDate }
      },
      select: {
        createdAt: true,
        acceptedAt: true
      }
    })

    const avgTimeToAcceptance = acceptedQuotations.length > 0
      ? acceptedQuotations.reduce((sum, q) => {
          const timeDiff = new Date(q.acceptedAt!).getTime() - new Date(q.createdAt).getTime()
          return sum + timeDiff
        }, 0) / acceptedQuotations.length / (1000 * 60 * 60 * 24) // Convert to days
      : 0

    return NextResponse.json({
      summary: {
        totalQuotations,
        totalValue: quotationsByStatus.reduce((sum, item) => sum + Number(item._sum.total || 0), 0),
        averageValue: Number(averageQuotationValue._avg.total || 0),
        acceptanceRate: Math.round(acceptanceRate * 100) / 100,
        viewRate: Math.round(viewRate * 100) / 100,
        avgTimeToAcceptance: Math.round(avgTimeToAcceptance * 100) / 100,
        pendingRevenue: totalPendingValue,
        confirmedRevenue: totalConfirmedValue,
        expiredQuotations
      },
      quotationsByStatus: quotationsByStatus.map(item => ({
        status: item.status,
        count: item._count.id,
        value: Number(item._sum.total || 0)
      })),
      quotationsByMonth,
      quotationTrends,
      topQuotations: topQuotationsByValue.map(q => ({
        id: q.id,
        quotationNumber: q.quotationNumber,
        title: q.title,
        total: Number(q.total),
        status: q.status,
        customer: q.customer,
        createdBy: q.createdBy,
        createdAt: q.createdAt
      })),
      recentQuotations: recentQuotations.map(q => ({
        id: q.id,
        quotationNumber: q.quotationNumber,
        title: q.title,
        total: Number(q.total),
        status: q.status,
        customer: q.customer,
        createdBy: q.createdBy,
        createdAt: q.createdAt
      })),
      customerQuotations: customerQuotationsWithDetails,
      revenueForecast: {
        pending: totalPendingValue,
        confirmed: totalConfirmedValue,
        potential: totalPendingValue + totalConfirmedValue
      },
      period: parseInt(period)
    })

  } catch (error) {
    console.error('Error fetching quotation analytics:', error)
    return NextResponse.json(
      { error: 'Failed to fetch quotation analytics' },
      { status: 500 }
    )
  }
}
