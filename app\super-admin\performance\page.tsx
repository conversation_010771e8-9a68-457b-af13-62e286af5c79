'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Zap,
  Server,
  Database,
  Globe,
  Clock,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Activity,
  BarChart3,
  Cpu,
  HardDrive,
  Wifi
} from 'lucide-react'

interface PerformanceMetric {
  id: string
  name: string
  value: number
  unit: string
  status: 'good' | 'warning' | 'critical'
  trend: 'up' | 'down' | 'stable'
  change: number
}

interface SystemResource {
  name: string
  usage: number
  limit: number
  status: 'good' | 'warning' | 'critical'
}

export default function SuperAdminPerformancePage() {
  const [isLoading, setIsLoading] = useState(true)
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date())

  // Mock data - in production, this would come from APIs
  const [performanceMetrics] = useState<PerformanceMetric[]>([
    {
      id: '1',
      name: 'Response Time',
      value: 245,
      unit: 'ms',
      status: 'good',
      trend: 'down',
      change: -12
    },
    {
      id: '2',
      name: 'Throughput',
      value: 1250,
      unit: 'req/min',
      status: 'good',
      trend: 'up',
      change: 8
    },
    {
      id: '3',
      name: 'Error Rate',
      value: 0.12,
      unit: '%',
      status: 'good',
      trend: 'down',
      change: -0.05
    },
    {
      id: '4',
      name: 'Database Query Time',
      value: 89,
      unit: 'ms',
      status: 'warning',
      trend: 'up',
      change: 15
    }
  ])

  const [systemResources] = useState<SystemResource[]>([
    { name: 'CPU Usage', usage: 45, limit: 100, status: 'good' },
    { name: 'Memory Usage', usage: 68, limit: 100, status: 'warning' },
    { name: 'Disk Usage', usage: 32, limit: 100, status: 'good' },
    { name: 'Network I/O', usage: 23, limit: 100, status: 'good' }
  ])

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  const refreshData = () => {
    setIsLoading(true)
    setLastUpdated(new Date())
    setTimeout(() => setIsLoading(false), 1000)
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'good':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case 'critical':
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      default:
        return <Activity className="h-4 w-4 text-gray-500" />
    }
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-500" />
      case 'down':
        return <TrendingDown className="h-4 w-4 text-red-500" />
      default:
        return <Activity className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good':
        return 'bg-green-500'
      case 'warning':
        return 'bg-yellow-500'
      case 'critical':
        return 'bg-red-500'
      default:
        return 'bg-gray-500'
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Performance Monitor</h1>
          <p className="text-muted-foreground">
            Real-time system performance metrics and monitoring
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="outline" className="text-xs">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </Badge>
          <Button onClick={refreshData} disabled={isLoading} size="sm">
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Performance Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {performanceMetrics.map((metric) => (
          <Card key={metric.id}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{metric.name}</CardTitle>
              {getStatusIcon(metric.status)}
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {metric.value}{metric.unit}
              </div>
              <div className="flex items-center text-xs text-muted-foreground">
                {getTrendIcon(metric.trend)}
                <span className="ml-1">
                  {metric.change > 0 ? '+' : ''}{metric.change}% from last hour
                </span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="resources">System Resources</TabsTrigger>
          <TabsTrigger value="database">Database</TabsTrigger>
          <TabsTrigger value="network">Network</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart3 className="h-5 w-5 mr-2" />
                  Performance Trends
                </CardTitle>
                <CardDescription>
                  Key metrics over the last 24 hours
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {performanceMetrics.map((metric) => (
                    <div key={metric.id} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(metric.status)}
                        <span className="text-sm font-medium">{metric.name}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm">{metric.value}{metric.unit}</span>
                        {getTrendIcon(metric.trend)}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Server className="h-5 w-5 mr-2" />
                  System Health
                </CardTitle>
                <CardDescription>
                  Current system resource utilization
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {systemResources.map((resource) => (
                    <div key={resource.name} className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="font-medium">{resource.name}</span>
                        <span>{resource.usage}%</span>
                      </div>
                      <Progress 
                        value={resource.usage} 
                        className="h-2"
                      />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="resources" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>System Resources</CardTitle>
              <CardDescription>
                Detailed view of system resource utilization
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Resource</TableHead>
                    <TableHead>Usage</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Trend</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {systemResources.map((resource) => (
                    <TableRow key={resource.name}>
                      <TableCell className="font-medium">
                        <div className="flex items-center space-x-2">
                          {resource.name === 'CPU Usage' && <Cpu className="h-4 w-4" />}
                          {resource.name === 'Memory Usage' && <Server className="h-4 w-4" />}
                          {resource.name === 'Disk Usage' && <HardDrive className="h-4 w-4" />}
                          {resource.name === 'Network I/O' && <Wifi className="h-4 w-4" />}
                          <span>{resource.name}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Progress value={resource.usage} className="w-20 h-2" />
                          <span className="text-sm">{resource.usage}%</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge 
                          variant={resource.status === 'good' ? 'default' : 'destructive'}
                          className="capitalize"
                        >
                          {resource.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <TrendingUp className="h-4 w-4 text-green-500" />
                          <span className="text-sm text-muted-foreground">Stable</span>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="database" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Database className="h-5 w-5 mr-2" />
                Database Performance
              </CardTitle>
              <CardDescription>
                Database query performance and connection metrics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <div className="text-sm font-medium">Query Performance</div>
                  <div className="text-2xl font-bold">89ms</div>
                  <div className="text-xs text-muted-foreground">Average query time</div>
                </div>
                <div className="space-y-2">
                  <div className="text-sm font-medium">Active Connections</div>
                  <div className="text-2xl font-bold">24</div>
                  <div className="text-xs text-muted-foreground">Current connections</div>
                </div>
                <div className="space-y-2">
                  <div className="text-sm font-medium">Cache Hit Rate</div>
                  <div className="text-2xl font-bold">94.2%</div>
                  <div className="text-xs text-muted-foreground">Query cache efficiency</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="network" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Globe className="h-5 w-5 mr-2" />
                Network Performance
              </CardTitle>
              <CardDescription>
                Network latency and bandwidth utilization
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <div className="text-sm font-medium">Latency</div>
                  <div className="text-2xl font-bold">12ms</div>
                  <div className="text-xs text-muted-foreground">Average response time</div>
                </div>
                <div className="space-y-2">
                  <div className="text-sm font-medium">Bandwidth Usage</div>
                  <div className="text-2xl font-bold">23%</div>
                  <div className="text-xs text-muted-foreground">Current utilization</div>
                </div>
                <div className="space-y-2">
                  <div className="text-sm font-medium">Packet Loss</div>
                  <div className="text-2xl font-bold">0.01%</div>
                  <div className="text-xs text-muted-foreground">Network reliability</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
