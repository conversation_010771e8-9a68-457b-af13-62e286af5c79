'use client'

import { useState, useEffect } from 'react'
import { useForm, useFieldArray } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Plus, Trash2, Calculator } from 'lucide-react'
import { toast } from 'react-hot-toast'

const quotationItemSchema = z.object({
  description: z.string().min(1, 'Description is required'),
  quantity: z.number().min(1, 'Quantity must be at least 1'),
  unitPrice: z.number().min(0, 'Unit price must be positive'),
  discount: z.number().min(0).max(100).default(0),
  taxRate: z.number().min(0).max(100).default(0)
})

const quotationSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  customerId: z.string().min(1, 'Customer is required'),
  leadId: z.string().optional(),
  status: z.enum(['DRAFT', 'SENT', 'VIEWED', 'ACCEPTED', 'REJECTED', 'EXPIRED']).default('DRAFT'),
  validUntil: z.string().optional(),
  terms: z.string().optional(),
  notes: z.string().optional(),
  paymentTerms: z.string().optional(),
  paymentDueDays: z.number().min(0).default(30),
  items: z.array(quotationItemSchema).min(1, 'At least one item is required'),
  taxRate: z.number().min(0).max(100).default(0),
  discountType: z.enum(['PERCENTAGE', 'FIXED']).default('PERCENTAGE'),
  discountValue: z.number().min(0).default(0)
})

type QuotationFormData = z.infer<typeof quotationSchema>

interface QuotationFormProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  quotation?: any
  mode: 'create' | 'edit'
  preselectedCustomerId?: string
  preselectedLeadId?: string
}

export function QuotationForm({ 
  isOpen, 
  onClose, 
  onSuccess, 
  quotation, 
  mode, 
  preselectedCustomerId,
  preselectedLeadId 
}: QuotationFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [customers, setCustomers] = useState<any[]>([])
  const [leads, setLeads] = useState<any[]>([])

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    control,
    setValue
  } = useForm<QuotationFormData>({
    resolver: zodResolver(quotationSchema),
    defaultValues: quotation ? {
      title: quotation.title,
      description: quotation.description || '',
      customerId: quotation.customerId || preselectedCustomerId || '',
      leadId: quotation.leadId || preselectedLeadId || '',
      status: quotation.status || 'DRAFT',
      validUntil: quotation.validUntil ? new Date(quotation.validUntil).toISOString().split('T')[0] : '',
      terms: quotation.terms || '',
      notes: quotation.notes || '',
      items: quotation.items || [{ description: '', quantity: 1, unitPrice: 0, discount: 0, taxRate: 0 }],
      taxRate: quotation.taxRate || 0,
      discountType: quotation.discountType || 'PERCENTAGE',
      discountValue: quotation.discountValue || 0
    } : {
      status: 'DRAFT',
      customerId: preselectedCustomerId || '',
      leadId: preselectedLeadId || '',
      items: [{ description: '', quantity: 1, unitPrice: 0, discount: 0, taxRate: 0 }],
      taxRate: 0,
      discountType: 'PERCENTAGE',
      discountValue: 0
    }
  })

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'items'
  })

  const watchedItems = watch('items')
  const watchedTaxRate = watch('taxRate')
  const watchedDiscountType = watch('discountType')
  const watchedDiscountValue = watch('discountValue')

  // Fetch customers and leads
  useEffect(() => {
    const fetchData = async () => {
      try {
        const [customersRes, leadsRes] = await Promise.all([
          fetch('/api/customers?limit=100'),
          fetch('/api/leads?limit=100')
        ])

        if (customersRes.ok) {
          const customersData = await customersRes.json()
          setCustomers(customersData.customers)
        }

        if (leadsRes.ok) {
          const leadsData = await leadsRes.json()
          setLeads(leadsData.leads)
        }
      } catch (error) {
        console.error('Error fetching data:', error)
      }
    }

    if (isOpen) {
      fetchData()
    }
  }, [isOpen])

  // Calculate totals
  const calculateTotals = () => {
    const subtotal = watchedItems.reduce((sum, item) => {
      const itemTotal = (item.quantity || 0) * (item.unitPrice || 0)
      const discountAmount = (itemTotal * (item.discount || 0)) / 100
      const afterDiscount = itemTotal - discountAmount
      const taxAmount = (afterDiscount * (item.taxRate || 0)) / 100
      return sum + afterDiscount + taxAmount
    }, 0)

    let total = subtotal
    if (watchedDiscountType === 'PERCENTAGE') {
      total = subtotal - (subtotal * (watchedDiscountValue || 0)) / 100
    } else {
      total = subtotal - (watchedDiscountValue || 0)
    }

    const finalTaxAmount = (total * (watchedTaxRate || 0)) / 100
    const finalTotal = total + finalTaxAmount

    return {
      subtotal: Math.round(subtotal * 100) / 100,
      total: Math.round(finalTotal * 100) / 100,
      taxAmount: Math.round(finalTaxAmount * 100) / 100,
      discountAmount: watchedDiscountType === 'PERCENTAGE' 
        ? Math.round((subtotal * (watchedDiscountValue || 0) / 100) * 100) / 100
        : (watchedDiscountValue || 0)
    }
  }

  const totals = calculateTotals()

  const addItem = () => {
    append({ description: '', quantity: 1, unitPrice: 0, discount: 0, taxRate: 0 })
  }

  const removeItem = (index: number) => {
    if (fields.length > 1) {
      remove(index)
    }
  }

  const onSubmit = async (data: QuotationFormData) => {
    setIsLoading(true)
    try {
      const url = mode === 'create' ? '/api/quotations' : `/api/quotations/${quotation.id}`
      const method = mode === 'create' ? 'POST' : 'PUT'

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to save quotation')
      }

      toast.success(`Quotation ${mode === 'create' ? 'created' : 'updated'} successfully`)
      reset()
      onSuccess()
      onClose()
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  const handleClose = () => {
    reset()
    onClose()
  }

  const statusOptions = [
    { value: 'DRAFT', label: 'Draft' },
    { value: 'SENT', label: 'Sent' },
    { value: 'VIEWED', label: 'Viewed' },
    { value: 'ACCEPTED', label: 'Accepted' },
    { value: 'REJECTED', label: 'Rejected' },
    { value: 'EXPIRED', label: 'Expired' }
  ]

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {mode === 'create' ? 'Create New Quotation' : 'Edit Quotation'}
          </DialogTitle>
          <DialogDescription>
            {mode === 'create' 
              ? 'Create a new quotation with items, pricing, and terms.'
              : 'Update the quotation information and items.'
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="title">Title *</Label>
              <Input
                id="title"
                {...register('title')}
                placeholder="Quotation title"
              />
              {errors.title && (
                <p className="text-sm text-red-600 mt-1">{errors.title.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="status">Status</Label>
              <select
                id="status"
                {...register('status')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {statusOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <Label htmlFor="customerId">Customer *</Label>
              <select
                id="customerId"
                {...register('customerId')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select a customer</option>
                {customers.map((customer) => (
                  <option key={customer.id} value={customer.id}>
                    {customer.name} {customer.company && `(${customer.company})`}
                  </option>
                ))}
              </select>
              {errors.customerId && (
                <p className="text-sm text-red-600 mt-1">{errors.customerId.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="leadId">Related Lead</Label>
              <select
                id="leadId"
                {...register('leadId')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select a lead (optional)</option>
                {leads.map((lead) => (
                  <option key={lead.id} value={lead.id}>
                    {lead.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <Label htmlFor="validUntil">Valid Until</Label>
              <Input
                id="validUntil"
                type="date"
                {...register('validUntil')}
              />
            </div>
          </div>

          <div>
            <Label htmlFor="description">Description</Label>
            <textarea
              id="description"
              {...register('description')}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Brief description of the quotation..."
            />
          </div>

          {/* Items Section */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">Items</h3>
              <Button type="button" onClick={addItem} size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Item
              </Button>
            </div>

            <div className="space-y-4">
              {fields.map((field, index) => (
                <div key={field.id} className="grid grid-cols-12 gap-2 items-end p-4 border rounded-lg">
                  <div className="col-span-4">
                    <Label htmlFor={`items.${index}.description`}>Description *</Label>
                    <Input
                      {...register(`items.${index}.description`)}
                      placeholder="Item description"
                    />
                    {errors.items?.[index]?.description && (
                      <p className="text-sm text-red-600 mt-1">
                        {errors.items[index]?.description?.message}
                      </p>
                    )}
                  </div>

                  <div className="col-span-2">
                    <Label htmlFor={`items.${index}.quantity`}>Qty *</Label>
                    <Input
                      type="number"
                      min="1"
                      {...register(`items.${index}.quantity`, { valueAsNumber: true })}
                      placeholder="1"
                    />
                  </div>

                  <div className="col-span-2">
                    <Label htmlFor={`items.${index}.unitPrice`}>Unit Price *</Label>
                    <Input
                      type="number"
                      min="0"
                      step="0.01"
                      {...register(`items.${index}.unitPrice`, { valueAsNumber: true })}
                      placeholder="0.00"
                    />
                  </div>

                  <div className="col-span-1">
                    <Label htmlFor={`items.${index}.discount`}>Disc %</Label>
                    <Input
                      type="number"
                      min="0"
                      max="100"
                      {...register(`items.${index}.discount`, { valueAsNumber: true })}
                      placeholder="0"
                    />
                  </div>

                  <div className="col-span-1">
                    <Label htmlFor={`items.${index}.taxRate`}>Tax %</Label>
                    <Input
                      type="number"
                      min="0"
                      max="100"
                      {...register(`items.${index}.taxRate`, { valueAsNumber: true })}
                      placeholder="0"
                    />
                  </div>

                  <div className="col-span-1">
                    <Label>Total</Label>
                    <div className="px-3 py-2 bg-gray-50 rounded-md text-sm">
                      ${(() => {
                        const item = watchedItems[index]
                        if (!item) return '0.00'
                        const itemTotal = (item.quantity || 0) * (item.unitPrice || 0)
                        const discountAmount = (itemTotal * (item.discount || 0)) / 100
                        const afterDiscount = itemTotal - discountAmount
                        const taxAmount = (afterDiscount * (item.taxRate || 0)) / 100
                        return (afterDiscount + taxAmount).toFixed(2)
                      })()}
                    </div>
                  </div>

                  <div className="col-span-1">
                    <Button
                      type="button"
                      variant="destructive"
                      size="sm"
                      onClick={() => removeItem(index)}
                      disabled={fields.length === 1}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Totals and Settings */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Settings</h3>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="discountType">Discount Type</Label>
                  <select
                    id="discountType"
                    {...register('discountType')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="PERCENTAGE">Percentage</option>
                    <option value="FIXED">Fixed Amount</option>
                  </select>
                </div>

                <div>
                  <Label htmlFor="discountValue">
                    Discount {watchedDiscountType === 'PERCENTAGE' ? '%' : '$'}
                  </Label>
                  <Input
                    type="number"
                    min="0"
                    step="0.01"
                    {...register('discountValue', { valueAsNumber: true })}
                    placeholder="0"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="taxRate">Overall Tax Rate (%)</Label>
                <Input
                  type="number"
                  min="0"
                  max="100"
                  step="0.01"
                  {...register('taxRate', { valueAsNumber: true })}
                  placeholder="0"
                />
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-medium flex items-center">
                <Calculator className="h-5 w-5 mr-2" />
                Totals
              </h3>
              
              <div className="space-y-2 p-4 bg-gray-50 rounded-lg">
                <div className="flex justify-between">
                  <span>Subtotal:</span>
                  <span>${totals.subtotal.toFixed(2)}</span>
                </div>
                
                {totals.discountAmount > 0 && (
                  <div className="flex justify-between text-red-600">
                    <span>Discount:</span>
                    <span>-${totals.discountAmount.toFixed(2)}</span>
                  </div>
                )}
                
                {totals.taxAmount > 0 && (
                  <div className="flex justify-between">
                    <span>Tax:</span>
                    <span>${totals.taxAmount.toFixed(2)}</span>
                  </div>
                )}
                
                <div className="flex justify-between font-bold text-lg border-t pt-2">
                  <span>Total:</span>
                  <span>${totals.total.toFixed(2)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Payment Terms */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="paymentTerms">Payment Terms</Label>
              <textarea
                id="paymentTerms"
                {...register('paymentTerms')}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Net 30, 2/10 Net 30, Payment due upon receipt, etc..."
              />
            </div>

            <div>
              <Label htmlFor="paymentDueDays">Payment Due (Days)</Label>
              <Input
                id="paymentDueDays"
                type="number"
                min="0"
                {...register('paymentDueDays', { valueAsNumber: true })}
                placeholder="30"
              />
              <p className="text-sm text-gray-500 mt-1">Number of days after invoice date</p>
            </div>
          </div>

          {/* Terms and Notes */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="terms">Terms & Conditions</Label>
              <textarea
                id="terms"
                {...register('terms')}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="General terms, delivery conditions, warranties, etc..."
              />
            </div>

            <div>
              <Label htmlFor="notes">Internal Notes</Label>
              <textarea
                id="notes"
                {...register('notes')}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Internal notes (not visible to customer)..."
              />
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Saving...' : mode === 'create' ? 'Create Quotation' : 'Update Quotation'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
