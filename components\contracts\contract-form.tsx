'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { X } from 'lucide-react'
import { toast } from 'react-hot-toast'

const contractSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  customerId: z.string().min(1, 'Customer is required'),
  quotationId: z.string().optional(),
  invoiceId: z.string().optional(),
  type: z.enum(['SERVICE', 'PRODUCT', 'SUBSCRIPTION', 'MAINTENANCE', 'CONSULTING', 'OTHER']).default('SERVICE'),
  status: z.enum(['DRAFT', 'REVIEW', 'SENT', 'SIGNED', 'ACTIVE', 'COMPLETED', 'CANCELLED', 'EXPIRED']).default('DRAFT'),
  value: z.number().min(0, 'Contract value must be positive').optional(),
  currency: z.string().default('USD'),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  renewalDate: z.string().optional(),
  autoRenewal: z.boolean().default(false),
  renewalPeriod: z.number().optional(),
  terms: z.string().optional(),
  conditions: z.string().optional(),
  notes: z.string().optional(),
  templateId: z.string().optional(),
  signatureRequired: z.boolean().default(true),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).default('MEDIUM'),
  tags: z.array(z.string()).optional().default([]),
  assignedToId: z.string().optional()
})

type ContractFormData = z.infer<typeof contractSchema>

interface ContractFormProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  contract?: any
  mode: 'create' | 'edit'
  preselectedCustomerId?: string
  preselectedQuotationId?: string
  preselectedInvoiceId?: string
}

export function ContractForm({ 
  isOpen, 
  onClose, 
  onSuccess, 
  contract, 
  mode, 
  preselectedCustomerId,
  preselectedQuotationId,
  preselectedInvoiceId 
}: ContractFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [customers, setCustomers] = useState<any[]>([])
  const [quotations, setQuotations] = useState<any[]>([])
  const [invoices, setInvoices] = useState<any[]>([])
  const [users, setUsers] = useState<any[]>([])
  const [templates, setTemplates] = useState<any[]>([])
  const [tagInput, setTagInput] = useState('')

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue,
    getValues
  } = useForm<ContractFormData>({
    resolver: zodResolver(contractSchema),
    defaultValues: contract ? {
      title: contract.title,
      description: contract.description || '',
      customerId: contract.customerId || preselectedCustomerId || '',
      quotationId: contract.quotationId || preselectedQuotationId || '',
      invoiceId: contract.invoiceId || preselectedInvoiceId || '',
      type: contract.type || 'SERVICE',
      status: contract.status || 'DRAFT',
      value: contract.value || undefined,
      currency: contract.currency || 'USD',
      startDate: contract.startDate ? new Date(contract.startDate).toISOString().split('T')[0] : '',
      endDate: contract.endDate ? new Date(contract.endDate).toISOString().split('T')[0] : '',
      renewalDate: contract.renewalDate ? new Date(contract.renewalDate).toISOString().split('T')[0] : '',
      autoRenewal: contract.autoRenewal || false,
      renewalPeriod: contract.renewalPeriod || undefined,
      terms: contract.terms || '',
      conditions: contract.conditions || '',
      notes: contract.notes || '',
      templateId: contract.templateId || '',
      signatureRequired: contract.signatureRequired !== false,
      priority: contract.priority || 'MEDIUM',
      tags: contract.tags || [],
      assignedToId: contract.assignedToId || ''
    } : {
      type: 'SERVICE',
      status: 'DRAFT',
      currency: 'USD',
      autoRenewal: false,
      signatureRequired: true,
      priority: 'MEDIUM',
      tags: [],
      customerId: preselectedCustomerId || '',
      quotationId: preselectedQuotationId || '',
      invoiceId: preselectedInvoiceId || ''
    }
  })

  const watchedTags = watch('tags')

  // Fetch data
  useEffect(() => {
    const fetchData = async () => {
      try {
        const [customersRes, quotationsRes, invoicesRes] = await Promise.all([
          fetch('/api/customers?limit=100'),
          fetch('/api/quotations?limit=100'),
          fetch('/api/invoices?limit=100')
        ])

        if (customersRes.ok) {
          const customersData = await customersRes.json()
          setCustomers(customersData.customers || [])
        }

        if (quotationsRes.ok) {
          const quotationsData = await quotationsRes.json()
          setQuotations(quotationsData.quotations || [])
        }

        if (invoicesRes.ok) {
          const invoicesData = await invoicesRes.json()
          setInvoices(invoicesData.invoices || [])
        }
      } catch (error) {
        console.error('Error fetching data:', error)
      }
    }

    if (isOpen) {
      fetchData()
    }
  }, [isOpen])

  const addTag = () => {
    if (tagInput.trim() && !watchedTags?.includes(tagInput.trim())) {
      const newTags = [...(watchedTags || []), tagInput.trim()]
      setValue('tags', newTags)
      setTagInput('')
    }
  }

  const removeTag = (tagToRemove: string) => {
    const newTags = (watchedTags || []).filter(tag => tag !== tagToRemove)
    setValue('tags', newTags)
  }

  const onSubmit = async (data: ContractFormData) => {
    setIsLoading(true)
    try {
      const url = mode === 'create' ? '/api/contracts' : `/api/contracts/${contract.id}`
      const method = mode === 'create' ? 'POST' : 'PUT'

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to save contract')
      }

      toast.success(`Contract ${mode === 'create' ? 'created' : 'updated'} successfully`)
      reset()
      onSuccess()
      onClose()
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  const handleClose = () => {
    reset()
    onClose()
  }

  const contractTypes = [
    { value: 'SERVICE', label: 'Service Agreement' },
    { value: 'PRODUCT', label: 'Product Sale' },
    { value: 'SUBSCRIPTION', label: 'Subscription' },
    { value: 'MAINTENANCE', label: 'Maintenance' },
    { value: 'CONSULTING', label: 'Consulting' },
    { value: 'OTHER', label: 'Other' }
  ]

  const statusOptions = [
    { value: 'DRAFT', label: 'Draft' },
    { value: 'REVIEW', label: 'Under Review' },
    { value: 'SENT', label: 'Sent to Customer' },
    { value: 'SIGNED', label: 'Signed' },
    { value: 'ACTIVE', label: 'Active' },
    { value: 'COMPLETED', label: 'Completed' },
    { value: 'CANCELLED', label: 'Cancelled' },
    { value: 'EXPIRED', label: 'Expired' }
  ]

  const priorityOptions = [
    { value: 'LOW', label: 'Low' },
    { value: 'MEDIUM', label: 'Medium' },
    { value: 'HIGH', label: 'High' },
    { value: 'URGENT', label: 'Urgent' }
  ]

  const currencyOptions = [
    { value: 'USD', label: 'USD ($)' },
    { value: 'EUR', label: 'EUR (€)' },
    { value: 'GBP', label: 'GBP (£)' },
    { value: 'CAD', label: 'CAD (C$)' },
    { value: 'AUD', label: 'AUD (A$)' }
  ]

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {mode === 'create' ? 'Create New Contract' : 'Edit Contract'}
          </DialogTitle>
          <DialogDescription>
            {mode === 'create' 
              ? 'Create a new contract with terms, conditions, and signature requirements.'
              : 'Update the contract information and settings.'
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="md:col-span-2">
              <Label htmlFor="title">Contract Title *</Label>
              <Input
                id="title"
                {...register('title')}
                placeholder="Contract title"
              />
              {errors.title && (
                <p className="text-sm text-red-600 mt-1">{errors.title.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="customerId">Customer *</Label>
              <select
                id="customerId"
                {...register('customerId')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select a customer</option>
                {customers.map((customer) => (
                  <option key={customer.id} value={customer.id}>
                    {customer.name} {customer.company && `(${customer.company})`}
                  </option>
                ))}
              </select>
              {errors.customerId && (
                <p className="text-sm text-red-600 mt-1">{errors.customerId.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="type">Contract Type</Label>
              <select
                id="type"
                {...register('type')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {contractTypes.map((type) => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <Label htmlFor="status">Status</Label>
              <select
                id="status"
                {...register('status')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {statusOptions.map((status) => (
                  <option key={status.value} value={status.value}>
                    {status.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <Label htmlFor="priority">Priority</Label>
              <select
                id="priority"
                {...register('priority')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {priorityOptions.map((priority) => (
                  <option key={priority.value} value={priority.value}>
                    {priority.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <Label htmlFor="quotationId">Related Quotation</Label>
              <select
                id="quotationId"
                {...register('quotationId')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select a quotation (optional)</option>
                {quotations.map((quotation) => (
                  <option key={quotation.id} value={quotation.id}>
                    {quotation.quotationNumber} - {quotation.title}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <Label htmlFor="invoiceId">Related Invoice</Label>
              <select
                id="invoiceId"
                {...register('invoiceId')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select an invoice (optional)</option>
                {invoices.map((invoice) => (
                  <option key={invoice.id} value={invoice.id}>
                    {invoice.invoiceNumber}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <Label htmlFor="assignedToId">Assigned To</Label>
              <select
                id="assignedToId"
                {...register('assignedToId')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select a user (optional)</option>
                {users.map((user) => (
                  <option key={user.id} value={user.id}>
                    {user.name || user.email}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div>
            <Label htmlFor="description">Description</Label>
            <textarea
              id="description"
              {...register('description')}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Brief description of the contract..."
            />
          </div>

          {/* Financial Information */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="value">Contract Value</Label>
              <Input
                id="value"
                type="number"
                min="0"
                step="0.01"
                {...register('value', { valueAsNumber: true })}
                placeholder="0.00"
              />
            </div>

            <div>
              <Label htmlFor="currency">Currency</Label>
              <select
                id="currency"
                {...register('currency')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {currencyOptions.map((currency) => (
                  <option key={currency.value} value={currency.value}>
                    {currency.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <Label htmlFor="renewalPeriod">Renewal Period (months)</Label>
              <Input
                id="renewalPeriod"
                type="number"
                min="1"
                {...register('renewalPeriod', { valueAsNumber: true })}
                placeholder="12"
              />
            </div>
          </div>

          {/* Dates */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="startDate">Start Date</Label>
              <Input
                id="startDate"
                type="date"
                {...register('startDate')}
              />
            </div>

            <div>
              <Label htmlFor="endDate">End Date</Label>
              <Input
                id="endDate"
                type="date"
                {...register('endDate')}
              />
            </div>

            <div>
              <Label htmlFor="renewalDate">Renewal Date</Label>
              <Input
                id="renewalDate"
                type="date"
                {...register('renewalDate')}
              />
            </div>
          </div>

          {/* Settings */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="autoRenewal"
                {...register('autoRenewal')}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <Label htmlFor="autoRenewal">Auto Renewal</Label>
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="signatureRequired"
                {...register('signatureRequired')}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <Label htmlFor="signatureRequired">Signature Required</Label>
            </div>
          </div>

          {/* Tags */}
          <div>
            <Label htmlFor="tags">Tags</Label>
            <div className="space-y-2">
              <div className="flex space-x-2">
                <Input
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  placeholder="Add a tag..."
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault()
                      addTag()
                    }
                  }}
                />
                <Button type="button" onClick={addTag} size="sm">
                  Add
                </Button>
              </div>
              {watchedTags && watchedTags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {watchedTags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="flex items-center space-x-1">
                      <span>{tag}</span>
                      <button
                        type="button"
                        onClick={() => removeTag(tag)}
                        className="ml-1 hover:text-red-600"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Terms and Conditions */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="terms">Terms & Conditions</Label>
              <textarea
                id="terms"
                {...register('terms')}
                rows={6}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Contract terms and conditions..."
              />
            </div>

            <div>
              <Label htmlFor="conditions">Additional Conditions</Label>
              <textarea
                id="conditions"
                {...register('conditions')}
                rows={6}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Additional conditions and clauses..."
              />
            </div>
          </div>

          <div>
            <Label htmlFor="notes">Internal Notes</Label>
            <textarea
              id="notes"
              {...register('notes')}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Internal notes (not visible to customer)..."
            />
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Saving...' : mode === 'create' ? 'Create Contract' : 'Update Contract'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
