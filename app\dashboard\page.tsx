'use client'

import { useSession } from 'next-auth/react'
import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
// import { Skeleton } from '@/components/ui/skeleton'
import {
  Users,
  FileText,
  Receipt,
  DollarSign,
  UserPlus,
  Activity,
  TrendingUp,
  ArrowUpRight,
  ArrowDownRight,
  Plus,
  Calendar,
  Clock,
  AlertCircle,
  CheckCircle,
  FileCheck,
  Target,
  BarChart3
} from 'lucide-react'

interface DashboardData {
  stats: {
    customers: { current: number; change: number; total: number }
    leads: { current: number; change: number; total: number }
    quotations: { current: number; change: number; total: number }
    invoices: { current: number; change: number; total: number }
    contracts: { current: number; total: number }
    tasks: { current: number; total: number }
    revenue: { total: number; lastMonth: number; pending: number; change: number }
  }
  recentActivities: any[]
  leadsByStatus: any[]
  invoicesByStatus: any[]
  topCustomers: any[]
  upcomingTasks: any[]
  upcomingRenewals: any[]
}

export default function DashboardPage() {
  const { data: session, status } = useSession()
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true)
        setError(null)
        const response = await fetch('/api/dashboard')
        if (!response.ok) {
          throw new Error('Failed to fetch dashboard data')
        }
        const data = await response.json()
        setDashboardData(data)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred')
      } finally {
        setLoading(false)
      }
    }

    if (status === 'loading') {
      return // Don't do anything while session is loading
    }

    if (session?.user?.companyId) {
      fetchDashboardData()
    } else if (session && !session.user?.companyId) {
      setLoading(false)
      setError('No company associated with user')
    } else if (status === 'unauthenticated') {
      setLoading(false)
      setError('Please log in to view dashboard')
    }
  }, [session?.user?.companyId, status])

  if (status === 'loading' || loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Loading Dashboard</h3>
          <p className="text-gray-500">Please wait while we fetch your data...</p>
        </div>
      </div>
    )
  }

  if (error || !dashboardData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Dashboard</h3>
          <p className="text-gray-500">{error || 'Failed to load dashboard data'}</p>
          <Button
            onClick={() => window.location.reload()}
            className="mt-4"
          >
            Retry
          </Button>
        </div>
      </div>
    )
  }

  const stats = [
    {
      title: 'Total Customers',
      value: dashboardData.stats.customers.total.toLocaleString(),
      change: `${dashboardData.stats.customers.change >= 0 ? '+' : ''}${dashboardData.stats.customers.change.toFixed(1)}%`,
      changeType: dashboardData.stats.customers.change >= 0 ? 'increase' as const : 'decrease' as const,
      icon: Users,
      color: 'text-blue-600',
      subtitle: `${dashboardData.stats.customers.current} this month`
    },
    {
      title: 'Active Leads',
      value: dashboardData.stats.leads.current.toLocaleString(),
      change: `${dashboardData.stats.leads.change >= 0 ? '+' : ''}${dashboardData.stats.leads.change.toFixed(1)}%`,
      changeType: dashboardData.stats.leads.change >= 0 ? 'increase' as const : 'decrease' as const,
      icon: UserPlus,
      color: 'text-green-600',
      subtitle: `${dashboardData.stats.leads.total} total leads`
    },
    {
      title: 'Quotations',
      value: dashboardData.stats.quotations.current.toLocaleString(),
      change: `${dashboardData.stats.quotations.change >= 0 ? '+' : ''}${dashboardData.stats.quotations.change.toFixed(1)}%`,
      changeType: dashboardData.stats.quotations.change >= 0 ? 'increase' as const : 'decrease' as const,
      icon: FileText,
      color: 'text-yellow-600',
      subtitle: `${dashboardData.stats.quotations.total} total`
    },
    {
      title: 'Revenue',
      value: `$${dashboardData.stats.revenue.total.toLocaleString()}`,
      change: `${dashboardData.stats.revenue.change >= 0 ? '+' : ''}${dashboardData.stats.revenue.change.toFixed(1)}%`,
      changeType: dashboardData.stats.revenue.change >= 0 ? 'increase' as const : 'decrease' as const,
      icon: DollarSign,
      color: 'text-purple-600',
      subtitle: `$${dashboardData.stats.revenue.pending.toLocaleString()} pending`
    },
    {
      title: 'Active Contracts',
      value: dashboardData.stats.contracts.current.toLocaleString(),
      change: '',
      changeType: 'neutral' as const,
      icon: FileCheck,
      color: 'text-indigo-600',
      subtitle: `${dashboardData.stats.contracts.total} total`
    },
    {
      title: 'Open Tasks',
      value: dashboardData.stats.tasks.current.toLocaleString(),
      change: '',
      changeType: 'neutral' as const,
      icon: Target,
      color: 'text-orange-600',
      subtitle: `${dashboardData.stats.tasks.total} total tasks`
    }
  ]

  // Process recent activities
  const processedActivities = dashboardData.recentActivities.map((activity: any) => {
    let message = activity.description
    let icon = Activity
    let status = 'info'

    // Customize based on activity type
    switch (activity.type) {
      case 'NOTE':
        icon = FileText
        status = 'info'
        break
      case 'CALL':
        icon = Users
        status = 'success'
        break
      case 'EMAIL':
        icon = Receipt
        status = 'info'
        break
      case 'MEETING':
        icon = Calendar
        status = 'warning'
        break
      case 'STATUS_CHANGE':
        icon = TrendingUp
        status = 'success'
        break
      case 'PAYMENT_RECEIVED':
        icon = DollarSign
        status = 'success'
        break
      case 'CONTRACT_SIGNED':
        icon = FileCheck
        status = 'success'
        break
      default:
        icon = Activity
        status = 'info'
    }

    return {
      id: activity.id,
      type: activity.type,
      message: activity.title,
      description: activity.description,
      time: new Date(activity.createdAt).toLocaleString(),
      icon,
      status,
      user: activity.createdBy?.name || 'System'
    }
  })

  const quickActions = [
    {
      title: 'Add Lead',
      description: 'Track a new business lead',
      icon: UserPlus,
      href: '/dashboard/leads/new',
      color: 'text-green-600'
    },
    {
      title: 'Add Customer',
      description: 'Create a new customer profile',
      icon: Users,
      href: '/dashboard/customers/new',
      color: 'text-blue-600'
    },
    {
      title: 'Create Quotation',
      description: 'Generate a new quotation',
      icon: FileText,
      href: '/dashboard/quotations/new',
      color: 'text-yellow-600'
    },
    {
      title: 'Create Invoice',
      description: 'Generate a new invoice',
      icon: Receipt,
      href: '/dashboard/invoices/new',
      color: 'text-purple-600'
    },
    {
      title: 'New Contract',
      description: 'Create a new contract',
      icon: FileCheck,
      href: '/dashboard/contracts/new',
      color: 'text-indigo-600'
    },
    {
      title: 'View Reports',
      description: 'Analyze your business data',
      icon: BarChart3,
      href: '/dashboard/reports',
      color: 'text-orange-600'
    }
  ]

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 text-white">
        <h1 className="text-2xl font-bold mb-2">
          Welcome back, {session?.user?.name || session?.user?.email || 'User'}!
        </h1>
        <p className="text-blue-100">
          Here's what's happening with your business today.
        </p>
        <div className="mt-4 flex items-center space-x-4 text-sm">
          <span className="flex items-center">
            <Badge variant="secondary" className="mr-2">
              {session?.user?.role || 'USER'}
            </Badge>
            Role
          </span>
          <span className="flex items-center">
            <Badge variant="outline" className="mr-2 text-white border-white">
              {dashboardData.stats.customers.total} Customers
            </Badge>
            <Badge variant="outline" className="mr-2 text-white border-white">
              {dashboardData.stats.leads.current} Active Leads
            </Badge>
          </span>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        {stats.map((stat) => (
          <Card key={stat.title} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                {stat.title}
              </CardTitle>
              <stat.icon className={`h-5 w-5 ${stat.color}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
              {stat.change && (
                <div className="flex items-center mt-1">
                  {stat.changeType === 'increase' ? (
                    <ArrowUpRight className="h-4 w-4 text-green-600 mr-1" />
                  ) : stat.changeType === 'decrease' ? (
                    <ArrowDownRight className="h-4 w-4 text-red-600 mr-1" />
                  ) : null}
                  <span className={`text-sm font-medium ${
                    stat.changeType === 'increase' ? 'text-green-600' :
                    stat.changeType === 'decrease' ? 'text-red-600' : 'text-gray-500'
                  }`}>
                    {stat.change}
                  </span>
                  {stat.changeType !== 'neutral' && (
                    <span className="text-sm text-gray-500 ml-1">from last month</span>
                  )}
                </div>
              )}
              {stat.subtitle && (
                <p className="text-xs text-gray-500 mt-1">{stat.subtitle}</p>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Plus className="h-5 w-5 mr-2" />
              Quick Actions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-3">
              {quickActions.map((action) => (
                <Button
                  key={action.title}
                  variant="outline"
                  className="h-auto p-3 flex flex-col items-center space-y-2 hover:bg-gray-50"
                  asChild
                >
                  <a href={action.href}>
                    <action.icon className={`h-5 w-5 ${action.color}`} />
                    <div className="text-center">
                      <div className="font-medium text-xs">{action.title}</div>
                      <div className="text-xs text-gray-500">{action.description}</div>
                    </div>
                  </a>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Activity className="h-5 w-5 mr-2" />
              Recent Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4 max-h-80 overflow-y-auto">
              {processedActivities.length > 0 ? (
                processedActivities.map((activity: any) => (
                  <div key={activity.id} className="flex items-start space-x-3">
                    <div className={`p-2 rounded-full ${
                      activity.status === 'success' ? 'bg-green-100' :
                      activity.status === 'warning' ? 'bg-yellow-100' :
                      'bg-blue-100'
                    }`}>
                      <activity.icon className={`h-4 w-4 ${
                        activity.status === 'success' ? 'text-green-600' :
                        activity.status === 'warning' ? 'text-yellow-600' :
                        'text-blue-600'
                      }`} />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-gray-900">{activity.message}</p>
                      <p className="text-xs text-gray-500">{activity.description}</p>
                      <div className="flex items-center mt-1 text-xs text-gray-400">
                        <span>{activity.user}</span>
                        <span className="mx-1">•</span>
                        <span>{activity.time}</span>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Activity className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>No recent activity</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Additional Sections */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Upcoming Tasks */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Clock className="h-5 w-5 mr-2" />
              Upcoming Tasks
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {dashboardData.upcomingTasks.length > 0 ? (
                dashboardData.upcomingTasks.map((task: any) => (
                  <div key={task.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                    <div className={`p-2 rounded-full ${
                      task.priority === 'HIGH' ? 'bg-red-100' :
                      task.priority === 'MEDIUM' ? 'bg-yellow-100' :
                      'bg-green-100'
                    }`}>
                      <Target className={`h-4 w-4 ${
                        task.priority === 'HIGH' ? 'text-red-600' :
                        task.priority === 'MEDIUM' ? 'text-yellow-600' :
                        'text-green-600'
                      }`} />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900">{task.title}</p>
                      <p className="text-xs text-gray-500">
                        Due: {new Date(task.dueDate).toLocaleDateString()}
                      </p>
                      {task.assignedTo && (
                        <p className="text-xs text-gray-400">
                          Assigned to: {task.assignedTo.name}
                        </p>
                      )}
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <CheckCircle className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>No upcoming tasks</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Contract Renewals */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              Upcoming Renewals
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {dashboardData.upcomingRenewals.length > 0 ? (
                dashboardData.upcomingRenewals.map((contract: any) => (
                  <div key={contract.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                    <div className="p-2 rounded-full bg-orange-100">
                      <FileCheck className="h-4 w-4 text-orange-600" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900">{contract.title}</p>
                      <p className="text-xs text-gray-500">
                        Customer: {contract.customer.name}
                      </p>
                      <p className="text-xs text-gray-500">
                        Renewal: {new Date(contract.renewalDate).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <FileCheck className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>No upcoming renewals</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}


