import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '30' // days
    const detailed = searchParams.get('detailed') === 'true'

    // Get current subscription and limits
    const subscription = await prisma.subscription.findFirst({
      where: {
        companyId: session.user.companyId,
        status: { in: ['ACTIVE', 'TRIALING', 'PAST_DUE'] }
      },
      include: {
        pricingPlan: true
      }
    })

    if (!subscription) {
      return NextResponse.json(
        { success: false, error: 'No active subscription found' },
        { status: 404 }
      )
    }

    const startDate = new Date()
    startDate.setDate(startDate.getDate() - parseInt(period))

    // Calculate current usage
    const [
      totalUsers,
      totalCustomers,
      totalQuotations,
      totalInvoices,
      totalContracts,
      recentQuotations,
      recentInvoices,
      recentContracts,
      storageUsed
    ] = await Promise.all([
      // Total counts
      prisma.user.count({
        where: { companyId: session.user.companyId }
      }),
      prisma.customer.count({
        where: { companyId: session.user.companyId }
      }),
      prisma.quotation.count({
        where: { companyId: session.user.companyId }
      }),
      prisma.invoice.count({
        where: { companyId: session.user.companyId }
      }),
      prisma.contract.count({
        where: { companyId: session.user.companyId }
      }),
      
      // Recent activity (for trend analysis)
      prisma.quotation.count({
        where: { 
          companyId: session.user.companyId,
          createdAt: { gte: startDate }
        }
      }),
      prisma.invoice.count({
        where: { 
          companyId: session.user.companyId,
          createdAt: { gte: startDate }
        }
      }),
      prisma.contract.count({
        where: { 
          companyId: session.user.companyId,
          createdAt: { gte: startDate }
        }
      }),
      
      // Mock storage calculation - in real app, calculate actual file sizes
      calculateStorageUsage(session.user.companyId)
    ])

    const limits = {
      users: subscription.pricingPlan.maxUsers,
      customers: subscription.pricingPlan.maxCustomers,
      quotations: subscription.pricingPlan.maxQuotations,
      invoices: subscription.pricingPlan.maxInvoices,
      contracts: subscription.pricingPlan.maxContracts,
      storage: Number(subscription.pricingPlan.maxStorage)
    }

    const usage = {
      users: {
        current: totalUsers,
        limit: limits.users,
        percentage: Math.round((totalUsers / limits.users) * 100),
        status: getUsageStatus(totalUsers, limits.users)
      },
      customers: {
        current: totalCustomers,
        limit: limits.customers,
        percentage: Math.round((totalCustomers / limits.customers) * 100),
        status: getUsageStatus(totalCustomers, limits.customers)
      },
      quotations: {
        current: totalQuotations,
        limit: limits.quotations,
        percentage: Math.round((totalQuotations / limits.quotations) * 100),
        status: getUsageStatus(totalQuotations, limits.quotations),
        recent: recentQuotations
      },
      invoices: {
        current: totalInvoices,
        limit: limits.invoices,
        percentage: Math.round((totalInvoices / limits.invoices) * 100),
        status: getUsageStatus(totalInvoices, limits.invoices),
        recent: recentInvoices
      },
      contracts: {
        current: totalContracts,
        limit: limits.contracts,
        percentage: Math.round((totalContracts / limits.contracts) * 100),
        status: getUsageStatus(totalContracts, limits.contracts),
        recent: recentContracts
      },
      storage: {
        current: storageUsed,
        limit: limits.storage,
        percentage: Math.round((storageUsed / limits.storage) * 100),
        status: getUsageStatus(storageUsed, limits.storage),
        currentFormatted: formatBytes(storageUsed),
        limitFormatted: formatBytes(limits.storage)
      }
    }

    let detailedUsage = null
    if (detailed) {
      detailedUsage = await getDetailedUsage(session.user.companyId, startDate)
    }

    return NextResponse.json({
      success: true,
      data: {
        usage,
        subscription: {
          planName: subscription.pricingPlan.name,
          status: subscription.status,
          billingCycle: subscription.billingCycle,
          currentPeriodEnd: subscription.currentPeriodEnd
        },
        period: parseInt(period),
        detailed: detailedUsage
      }
    })

  } catch (error) {
    console.error('Error fetching usage data:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch usage data' },
      { status: 500 }
    )
  }
}

async function calculateStorageUsage(companyId: string): Promise<number> {
  // Mock storage calculation
  // In a real application, you would:
  // 1. Calculate file sizes from uploaded documents
  // 2. Include database storage estimates
  // 3. Cache results for performance
  
  const [quotationCount, invoiceCount, contractCount] = await Promise.all([
    prisma.quotation.count({ where: { companyId } }),
    prisma.invoice.count({ where: { companyId } }),
    prisma.contract.count({ where: { companyId } })
  ])

  // Estimate: 50KB per quotation, 75KB per invoice, 100KB per contract
  const estimatedBytes = (quotationCount * 50000) + (invoiceCount * 75000) + (contractCount * 100000)
  
  // Add some randomness for demo purposes
  return estimatedBytes + Math.floor(Math.random() * 10000000)
}

async function getDetailedUsage(companyId: string, startDate: Date) {
  // Get daily usage trends for the period
  const dailyQuotations = await prisma.quotation.groupBy({
    by: ['createdAt'],
    where: {
      companyId,
      createdAt: { gte: startDate }
    },
    _count: true
  })

  const dailyInvoices = await prisma.invoice.groupBy({
    by: ['createdAt'],
    where: {
      companyId,
      createdAt: { gte: startDate }
    },
    _count: true
  })

  return {
    dailyQuotations: processDailyData(dailyQuotations),
    dailyInvoices: processDailyData(dailyInvoices)
  }
}

function processDailyData(data: any[]) {
  // Group by date and sum counts
  const grouped = data.reduce((acc, item) => {
    const date = item.createdAt.toISOString().split('T')[0]
    acc[date] = (acc[date] || 0) + item._count
    return acc
  }, {})

  return Object.entries(grouped).map(([date, count]) => ({
    date,
    count
  }))
}

function getUsageStatus(current: number, limit: number): 'normal' | 'warning' | 'critical' | 'exceeded' {
  const percentage = (current / limit) * 100
  
  if (current >= limit) return 'exceeded'
  if (percentage >= 90) return 'critical'
  if (percentage >= 75) return 'warning'
  return 'normal'
}

function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes'

  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// Export utility functions for use in other APIs
export { getUsageStatus, formatBytes }
