(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4565],{51078:function(e,s,a){Promise.resolve().then(a.bind(a,45614))},45614:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return I}});var t=a(57437),r=a(2265),n=a(82749),i=a(24033),l=a(27815),c=a(85754),o=a(31478),d=a(86443),u=a(19160),m=a(42706),x=a(41298),f=a(64280),p=a(9883),h=a(13008),g=a(99670),j=a(55340),y=a(25750),b=a(97332),v=a(49617),N=a(45367),w=a(5925),C=a(49842),P=a(45179),k=a(23444),S=a(54900),F=a(45509);function R(e){let{formData:s,setFormData:a,onSubmit:r,onCancel:n,submitLabel:i}=e,l=e=>1073741824*(parseFloat(e)||1);return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(C._,{htmlFor:"name",children:"Plan Name"}),(0,t.jsx)(P.I,{id:"name",value:s.name,onChange:e=>a(s=>({...s,name:e.target.value})),placeholder:"e.g., Pro Plan"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(C._,{htmlFor:"currency",children:"Currency"}),(0,t.jsxs)(F.Ph,{value:s.currency,onValueChange:e=>a(s=>({...s,currency:e})),children:[(0,t.jsx)(F.i4,{children:(0,t.jsx)(F.ki,{})}),(0,t.jsxs)(F.Bw,{children:[(0,t.jsx)(F.Ql,{value:"USD",children:"USD"}),(0,t.jsx)(F.Ql,{value:"EUR",children:"EUR"}),(0,t.jsx)(F.Ql,{value:"GBP",children:"GBP"})]})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(C._,{htmlFor:"description",children:"Description"}),(0,t.jsx)(k.g,{id:"description",value:s.description,onChange:e=>a(s=>({...s,description:e.target.value})),placeholder:"Brief description of the plan",rows:3})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(C._,{htmlFor:"monthlyPrice",children:"Monthly Price"}),(0,t.jsx)(P.I,{id:"monthlyPrice",type:"number",value:s.monthlyPrice,onChange:e=>a(s=>({...s,monthlyPrice:parseFloat(e.target.value)||0})),placeholder:"0.00"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(C._,{htmlFor:"yearlyPrice",children:"Yearly Price"}),(0,t.jsx)(P.I,{id:"yearlyPrice",type:"number",value:s.yearlyPrice,onChange:e=>a(s=>({...s,yearlyPrice:parseFloat(e.target.value)||0})),placeholder:"0.00"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(C._,{htmlFor:"trialDays",children:"Trial Days"}),(0,t.jsx)(P.I,{id:"trialDays",type:"number",value:s.trialDays,onChange:e=>a(s=>({...s,trialDays:parseInt(e.target.value)||0})),placeholder:"0"})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium",children:"Usage Limits"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(C._,{htmlFor:"maxUsers",children:"Max Users"}),(0,t.jsx)(P.I,{id:"maxUsers",type:"number",value:s.maxUsers,onChange:e=>a(s=>({...s,maxUsers:parseInt(e.target.value)||1}))})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(C._,{htmlFor:"maxCompanies",children:"Max Companies"}),(0,t.jsx)(P.I,{id:"maxCompanies",type:"number",value:s.maxCompanies,onChange:e=>a(s=>({...s,maxCompanies:parseInt(e.target.value)||1}))})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(C._,{htmlFor:"maxCustomers",children:"Max Customers"}),(0,t.jsx)(P.I,{id:"maxCustomers",type:"number",value:s.maxCustomers,onChange:e=>a(s=>({...s,maxCustomers:parseInt(e.target.value)||10}))})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(C._,{htmlFor:"maxQuotations",children:"Max Quotations"}),(0,t.jsx)(P.I,{id:"maxQuotations",type:"number",value:s.maxQuotations,onChange:e=>a(s=>({...s,maxQuotations:parseInt(e.target.value)||5}))})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(C._,{htmlFor:"maxInvoices",children:"Max Invoices"}),(0,t.jsx)(P.I,{id:"maxInvoices",type:"number",value:s.maxInvoices,onChange:e=>a(s=>({...s,maxInvoices:parseInt(e.target.value)||5}))})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(C._,{htmlFor:"maxContracts",children:"Max Contracts"}),(0,t.jsx)(P.I,{id:"maxContracts",type:"number",value:s.maxContracts,onChange:e=>a(s=>({...s,maxContracts:parseInt(e.target.value)||1}))})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(C._,{htmlFor:"maxStorage",children:"Max Storage (GB)"}),(0,t.jsx)(P.I,{id:"maxStorage",type:"number",value:(s.maxStorage/1073741824).toString(),onChange:e=>a(s=>({...s,maxStorage:l(e.target.value)})),placeholder:"1"})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium",children:"Features"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:Object.entries(s.features).map(e=>{let[s,r]=e;return(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(S.X,{id:s,checked:r,onCheckedChange:e=>a(a=>({...a,features:{...a.features,[s]:e}}))}),(0,t.jsx)(C._,{htmlFor:s,className:"text-sm",children:s.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase())})]},s)})})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium",children:"Settings"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(S.X,{id:"isActive",checked:s.isActive,onCheckedChange:e=>a(s=>({...s,isActive:e}))}),(0,t.jsx)(C._,{htmlFor:"isActive",children:"Active"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(S.X,{id:"isPublic",checked:s.isPublic,onCheckedChange:e=>a(s=>({...s,isPublic:e}))}),(0,t.jsx)(C._,{htmlFor:"isPublic",children:"Public"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(C._,{htmlFor:"sortOrder",children:"Sort Order"}),(0,t.jsx)(P.I,{id:"sortOrder",type:"number",value:s.sortOrder,onChange:e=>a(s=>({...s,sortOrder:parseInt(e.target.value)||0}))})]})]})]}),(0,t.jsxs)(m.cN,{children:[(0,t.jsx)(c.z,{variant:"outline",onClick:n,children:"Cancel"}),(0,t.jsx)(c.z,{onClick:r,disabled:!s.name.trim(),children:i})]})]})}function I(){var e;let{data:s,status:a}=(0,n.useSession)(),[C,P]=(0,r.useState)([]),[k,S]=(0,r.useState)(!0),[F,I]=(0,r.useState)(!1),[_,D]=(0,r.useState)(!1),[A,Z]=(0,r.useState)(null),[U,T]=(0,r.useState)({name:"",description:"",monthlyPrice:0,yearlyPrice:0,currency:"USD",maxUsers:1,maxCompanies:1,maxCustomers:10,maxQuotations:5,maxInvoices:5,maxContracts:1,maxStorage:1073741824,isActive:!0,isPublic:!0,trialDays:0,sortOrder:0,features:{basicReporting:!0,emailSupport:!0,mobileApp:!1,advancedAnalytics:!1,customBranding:!1,apiAccess:!1,prioritySupport:!1,customIntegrations:!1,advancedSecurity:!1,dedicatedManager:!1}});if("loading"===a)return(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})});"unauthenticated"===a&&(0,i.redirect)("/auth/signin"),(null==s?void 0:null===(e=s.user)||void 0===e?void 0:e.role)!=="SUPER_ADMIN"&&(0,i.redirect)("/dashboard");let z=async()=>{try{S(!0);let e=await fetch("/api/pricing-plans?includeInactive=true");if(!e.ok)throw Error("HTTP error! status: ".concat(e.status));let s=await e.json();console.log("Fetched plans data:",s),s.success&&s.data?(P(s.data),w.toast.success("Loaded ".concat(s.data.length," pricing plans"))):(console.error("API response error:",s),w.toast.error(s.error||"Failed to load pricing plans"))}catch(e){console.error("Error fetching plans:",e),w.toast.error("Failed to load pricing plans")}finally{S(!1)}};(0,r.useEffect)(()=>{z()},[]);let O=async(e,s)=>{try{let a=await fetch("/api/pricing-plans/".concat(e),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({isActive:s})}),t=await a.json();t.success?(w.toast.success("Plan ".concat(s?"activated":"deactivated"," successfully")),z()):w.toast.error(t.error||"Failed to update plan")}catch(e){console.error("Error updating plan:",e),w.toast.error("Failed to update plan")}},E=async()=>{try{let e=await fetch("/api/pricing-plans",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(U)}),s=await e.json();s.success?(w.toast.success("Plan created successfully"),I(!1),V(),z()):w.toast.error(s.error||"Failed to create plan")}catch(e){console.error("Error creating plan:",e),w.toast.error("Failed to create plan")}},M=e=>{Z(e),T({name:e.name,description:e.description,monthlyPrice:e.monthlyPrice,yearlyPrice:e.yearlyPrice||0,currency:e.currency,maxUsers:e.maxUsers,maxCompanies:e.maxCompanies,maxCustomers:e.maxCustomers,maxQuotations:e.maxQuotations,maxInvoices:e.maxInvoices,maxContracts:e.maxContracts,maxStorage:e.maxStorage,isActive:e.isActive,isPublic:e.isPublic,trialDays:e.trialDays,sortOrder:e.sortOrder,features:e.features}),D(!0)},B=async()=>{if(A)try{let e=await fetch("/api/pricing-plans/".concat(A.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(U)}),s=await e.json();s.success?(w.toast.success("Plan updated successfully"),D(!1),Z(null),V(),z()):w.toast.error(s.error||"Failed to update plan")}catch(e){console.error("Error updating plan:",e),w.toast.error("Failed to update plan")}},Q=async e=>{if(confirm("Are you sure you want to delete this pricing plan? This action cannot be undone."))try{let s=await fetch("/api/pricing-plans/".concat(e),{method:"DELETE"}),a=await s.json();a.success?(w.toast.success("Plan deleted successfully"),z()):w.toast.error(a.error||"Failed to delete plan")}catch(e){console.error("Error deleting plan:",e),w.toast.error("Failed to delete plan")}},V=()=>{T({name:"",description:"",monthlyPrice:0,yearlyPrice:0,currency:"USD",maxUsers:1,maxCompanies:1,maxCustomers:10,maxQuotations:5,maxInvoices:5,maxContracts:1,maxStorage:1073741824,isActive:!0,isPublic:!0,trialDays:0,sortOrder:0,features:{basicReporting:!0,emailSupport:!0,mobileApp:!1,advancedAnalytics:!1,customBranding:!1,apiAccess:!1,prioritySupport:!1,customIntegrations:!1,advancedSecurity:!1,dedicatedManager:!1}})},Y=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:0}).format(e),L=e=>Object.values(e).filter(Boolean).length;return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(x.Z,{className:"h-8 w-8 text-blue-600"}),(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Pricing Plans Management"})]}),(0,t.jsx)("p",{className:"text-gray-500 mt-1",children:"Manage subscription plans, pricing, and features"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(c.z,{variant:"outline",onClick:z,disabled:k,children:[(0,t.jsx)(f.Z,{className:"h-4 w-4 mr-2 ".concat(k?"animate-spin":"")}),"Refresh"]}),(0,t.jsxs)(c.z,{onClick:()=>I(!0),children:[(0,t.jsx)(p.Z,{className:"h-4 w-4 mr-2"}),"Add Plan"]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsx)(l.Zb,{children:(0,t.jsx)(l.aY,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Total Plans"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:C.length})]}),(0,t.jsx)(x.Z,{className:"h-8 w-8 text-blue-600"})]})})}),(0,t.jsx)(l.Zb,{children:(0,t.jsx)(l.aY,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Active Plans"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-green-600",children:C.filter(e=>e.isActive).length})]}),(0,t.jsx)(h.Z,{className:"h-8 w-8 text-green-600"})]})})}),(0,t.jsx)(l.Zb,{children:(0,t.jsx)(l.aY,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Public Plans"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-purple-600",children:C.filter(e=>e.isPublic).length})]}),(0,t.jsx)(g.Z,{className:"h-8 w-8 text-purple-600"})]})})})]}),(0,t.jsxs)(l.Zb,{children:[(0,t.jsxs)(l.Ol,{children:[(0,t.jsxs)(l.ll,{children:["Pricing Plans (",C.length,")"]}),(0,t.jsx)(l.SZ,{children:"Manage your subscription plans, pricing, and features"})]}),(0,t.jsx)(l.aY,{children:k?(0,t.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)(u.iA,{children:[(0,t.jsx)(u.xD,{children:(0,t.jsxs)(u.SC,{children:[(0,t.jsx)(u.ss,{children:"Plan"}),(0,t.jsx)(u.ss,{children:"Pricing"}),(0,t.jsx)(u.ss,{children:"Limits"}),(0,t.jsx)(u.ss,{children:"Features"}),(0,t.jsx)(u.ss,{children:"Status"}),(0,t.jsx)(u.ss,{children:"Actions"})]})}),(0,t.jsx)(u.RM,{children:C.map(e=>(0,t.jsxs)(u.SC,{children:[(0,t.jsx)(u.pj,{children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold",children:e.name.charAt(0)}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("p",{className:"font-medium text-gray-900",children:e.name}),"pro"===e.name.toLowerCase()&&(0,t.jsx)(j.Z,{className:"h-4 w-4 text-yellow-500"})]}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:e.description})]})]})}),(0,t.jsx)(u.pj,{children:(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsxs)("p",{className:"font-medium",children:[Y(e.monthlyPrice),"/month"]}),e.yearlyPrice&&(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:[Y(e.yearlyPrice),"/year (",e.yearlyDiscount,"% off)"]}),e.trialDays>0&&(0,t.jsxs)(o.C,{variant:"secondary",className:"text-xs mt-1",children:[e.trialDays,"-day trial"]})]})}),(0,t.jsx)(u.pj,{children:(0,t.jsxs)("div",{className:"text-sm space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(y.Z,{className:"h-3 w-3 text-gray-400"}),(0,t.jsxs)("span",{children:[e.maxUsers," users"]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(b.Z,{className:"h-3 w-3 text-gray-400"}),(0,t.jsx)("span",{children:e.formattedStorage})]}),(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:[e.maxCustomers," customers, ",e.maxQuotations," quotes"]})]})}),(0,t.jsx)(u.pj,{children:(0,t.jsxs)(o.C,{variant:"outline",children:[L(e.features)," features"]})}),(0,t.jsx)(u.pj,{children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(d.r,{checked:e.isActive,onCheckedChange:s=>O(e.id,s)}),(0,t.jsx)("span",{className:"text-sm",children:e.isActive?"Active":"Inactive"})]}),e.isPublic&&(0,t.jsx)(o.C,{variant:"secondary",className:"text-xs",children:"Public"})]})}),(0,t.jsx)(u.pj,{children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(c.z,{variant:"ghost",size:"sm",onClick:()=>M(e),title:"Edit plan",children:(0,t.jsx)(v.Z,{className:"h-4 w-4"})}),(0,t.jsx)(c.z,{variant:"ghost",size:"sm",onClick:()=>Q(e.id),title:"Delete plan",children:(0,t.jsx)(N.Z,{className:"h-4 w-4"})})]})})]},e.id))})]})})})]}),(0,t.jsx)(m.Vq,{open:F,onOpenChange:I,children:(0,t.jsxs)(m.cZ,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)(m.fK,{children:[(0,t.jsx)(m.$N,{children:"Create New Pricing Plan"}),(0,t.jsx)(m.Be,{children:"Add a new subscription plan with custom pricing and features."})]}),(0,t.jsx)(R,{formData:U,setFormData:T,onSubmit:E,onCancel:()=>{I(!1),V()},submitLabel:"Create Plan"})]})}),(0,t.jsx)(m.Vq,{open:_,onOpenChange:D,children:(0,t.jsxs)(m.cZ,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)(m.fK,{children:[(0,t.jsx)(m.$N,{children:"Edit Pricing Plan"}),(0,t.jsx)(m.Be,{children:"Modify the pricing plan details and features."})]}),(0,t.jsx)(R,{formData:U,setFormData:T,onSubmit:B,onCancel:()=>{D(!1),Z(null),V()},submitLabel:"Update Plan"})]})})]})}},31478:function(e,s,a){"use strict";a.d(s,{C:function(){return l}});var t=a(57437);a(2265);var r=a(96061),n=a(1657);let i=(0,r.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600",info:"border-transparent bg-blue-500 text-white hover:bg-blue-600"}},defaultVariants:{variant:"default"}});function l(e){let{className:s,variant:a,...r}=e;return(0,t.jsx)("div",{className:(0,n.cn)(i({variant:a}),s),...r})}},85754:function(e,s,a){"use strict";a.d(s,{z:function(){return o}});var t=a(57437),r=a(2265),n=a(67256),i=a(96061),l=a(1657);let c=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=r.forwardRef((e,s)=>{let{className:a,variant:r,size:i,asChild:o=!1,...d}=e,u=o?n.g7:"button";return(0,t.jsx)(u,{className:(0,l.cn)(c({variant:r,size:i,className:a})),ref:s,...d})});o.displayName="Button"},27815:function(e,s,a){"use strict";a.d(s,{Ol:function(){return l},SZ:function(){return o},Zb:function(){return i},aY:function(){return d},eW:function(){return u},ll:function(){return c}});var t=a(57437),r=a(2265),n=a(1657);let i=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{ref:s,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...r})});i.displayName="Card";let l=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",a),...r})});l.displayName="CardHeader";let c=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("h3",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",a),...r})});c.displayName="CardTitle";let o=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",a),...r})});o.displayName="CardDescription";let d=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{ref:s,className:(0,n.cn)("p-6 pt-0",a),...r})});d.displayName="CardContent";let u=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",a),...r})});u.displayName="CardFooter"},54900:function(e,s,a){"use strict";a.d(s,{X:function(){return c}});var t=a(57437),r=a(2265),n=a(66062),i=a(62442),l=a(1657);let c=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(n.fC,{ref:s,className:(0,l.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",a),...r,children:(0,t.jsx)(n.z$,{className:(0,l.cn)("flex items-center justify-center text-current"),children:(0,t.jsx)(i.Z,{className:"h-4 w-4"})})})});c.displayName=n.fC.displayName},42706:function(e,s,a){"use strict";a.d(s,{$N:function(){return p},Be:function(){return h},Vq:function(){return c},cN:function(){return f},cZ:function(){return m},fK:function(){return x},hg:function(){return o},t9:function(){return u}});var t=a(57437),r=a(2265),n=a(28712),i=a(82549),l=a(1657);let c=n.fC,o=n.xz,d=n.h_;n.x8;let u=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(n.aV,{ref:s,className:(0,l.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...r})});u.displayName=n.aV.displayName;let m=r.forwardRef((e,s)=>{let{className:a,children:r,...c}=e;return(0,t.jsxs)(d,{children:[(0,t.jsx)(u,{}),(0,t.jsxs)(n.VY,{ref:s,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...c,children:[r,(0,t.jsxs)(n.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,t.jsx)(i.Z,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});m.displayName=n.VY.displayName;let x=e=>{let{className:s,...a}=e;return(0,t.jsx)("div",{className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",s),...a})};x.displayName="DialogHeader";let f=e=>{let{className:s,...a}=e;return(0,t.jsx)("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",s),...a})};f.displayName="DialogFooter";let p=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(n.Dx,{ref:s,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",a),...r})});p.displayName=n.Dx.displayName;let h=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(n.dk,{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",a),...r})});h.displayName=n.dk.displayName},45179:function(e,s,a){"use strict";a.d(s,{I:function(){return i}});var t=a(57437),r=a(2265),n=a(1657);let i=r.forwardRef((e,s)=>{let{className:a,type:r,...i}=e;return(0,t.jsx)("input",{type:r,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:s,...i})});i.displayName="Input"},49842:function(e,s,a){"use strict";a.d(s,{_:function(){return o}});var t=a(57437),r=a(2265),n=a(36743),i=a(96061),l=a(1657);let c=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(n.f,{ref:s,className:(0,l.cn)(c(),a),...r})});o.displayName=n.f.displayName},45509:function(e,s,a){"use strict";a.d(s,{Bw:function(){return p},Ph:function(){return d},Ql:function(){return h},i4:function(){return m},ki:function(){return u}});var t=a(57437),r=a(2265),n=a(99530),i=a(83523),l=a(9224),c=a(62442),o=a(1657);let d=n.fC;n.ZA;let u=n.B4,m=r.forwardRef((e,s)=>{let{className:a,children:r,...l}=e;return(0,t.jsxs)(n.xz,{ref:s,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...l,children:[r,(0,t.jsx)(n.JO,{asChild:!0,children:(0,t.jsx)(i.Z,{className:"h-4 w-4 opacity-50"})})]})});m.displayName=n.xz.displayName;let x=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(n.u_,{ref:s,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",a),...r,children:(0,t.jsx)(l.Z,{className:"h-4 w-4"})})});x.displayName=n.u_.displayName;let f=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(n.$G,{ref:s,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",a),...r,children:(0,t.jsx)(i.Z,{className:"h-4 w-4"})})});f.displayName=n.$G.displayName;let p=r.forwardRef((e,s)=>{let{className:a,children:r,position:i="popper",...l}=e;return(0,t.jsx)(n.h_,{children:(0,t.jsxs)(n.VY,{ref:s,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:i,...l,children:[(0,t.jsx)(x,{}),(0,t.jsx)(n.l_,{className:(0,o.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),(0,t.jsx)(f,{})]})})});p.displayName=n.VY.displayName,r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(n.__,{ref:s,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...r})}).displayName=n.__.displayName;let h=r.forwardRef((e,s)=>{let{className:a,children:r,...i}=e;return(0,t.jsxs)(n.ck,{ref:s,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...i,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(n.wU,{children:(0,t.jsx)(c.Z,{className:"h-4 w-4"})})}),(0,t.jsx)(n.eT,{children:r})]})});h.displayName=n.ck.displayName,r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(n.Z0,{ref:s,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",a),...r})}).displayName=n.Z0.displayName},86443:function(e,s,a){"use strict";a.d(s,{r:function(){return l}});var t=a(57437),r=a(2265),n=a(92376),i=a(1657);let l=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(n.fC,{className:(0,i.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",a),...r,ref:s,children:(0,t.jsx)(n.bU,{className:(0,i.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});l.displayName=n.fC.displayName},19160:function(e,s,a){"use strict";a.d(s,{RM:function(){return c},SC:function(){return o},iA:function(){return i},pj:function(){return u},ss:function(){return d},xD:function(){return l}});var t=a(57437),r=a(2265),n=a(1657);let i=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{className:"relative w-full overflow-auto",children:(0,t.jsx)("table",{ref:s,className:(0,n.cn)("w-full caption-bottom text-sm",a),...r})})});i.displayName="Table";let l=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("thead",{ref:s,className:(0,n.cn)("[&_tr]:border-b",a),...r})});l.displayName="TableHeader";let c=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("tbody",{ref:s,className:(0,n.cn)("[&_tr:last-child]:border-0",a),...r})});c.displayName="TableBody",r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("tfoot",{ref:s,className:(0,n.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",a),...r})}).displayName="TableFooter";let o=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("tr",{ref:s,className:(0,n.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",a),...r})});o.displayName="TableRow";let d=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("th",{ref:s,className:(0,n.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",a),...r})});d.displayName="TableHead";let u=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("td",{ref:s,className:(0,n.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",a),...r})});u.displayName="TableCell",r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("caption",{ref:s,className:(0,n.cn)("mt-4 text-sm text-muted-foreground",a),...r})}).displayName="TableCaption"},23444:function(e,s,a){"use strict";a.d(s,{g:function(){return i}});var t=a(57437),r=a(2265),n=a(1657);let i=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:s,...r})});i.displayName="Textarea"},1657:function(e,s,a){"use strict";a.d(s,{cn:function(){return n}});var t=a(57042),r=a(74769);function n(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,r.m6)((0,t.W)(s))}}},function(e){e.O(0,[6723,9502,2749,1706,4138,4997,9440,2971,4938,1744],function(){return e(e.s=51078)}),_N_E=e.O()}]);