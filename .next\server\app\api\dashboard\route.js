"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/dashboard/route";
exports.ids = ["app/api/dashboard/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Froute&page=%2Fapi%2Fdashboard%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Froute&page=%2Fapi%2Fdashboard%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_proj_nextjs_saas_app_api_dashboard_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/dashboard/route.ts */ \"(rsc)/./app/api/dashboard/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/dashboard/route\",\n        pathname: \"/api/dashboard\",\n        filename: \"route\",\n        bundlePath: \"app/api/dashboard/route\"\n    },\n    resolvedPagePath: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\api\\\\dashboard\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_proj_nextjs_saas_app_api_dashboard_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/dashboard/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Froute&page=%2Fapi%2Fdashboard%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/dashboard/route.ts":
/*!************************************!*\
  !*** ./app/api/dashboard/route.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id || !session?.user?.companyId) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const companyId = session.user.companyId;\n        // Get date ranges for comparison\n        const now = new Date();\n        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);\n        const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);\n        const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);\n        // Parallel queries for better performance\n        const [// Current month stats\n        customersCount, leadsCount, quotationsCount, invoicesCount, contractsCount, tasksCount, // Last month stats for comparison\n        lastMonthCustomers, lastMonthLeads, lastMonthQuotations, lastMonthInvoices, // Revenue calculations\n        totalRevenue, lastMonthRevenue, pendingRevenue, // Recent activities\n        recentActivities, // Lead pipeline\n        leadsByStatus, // Invoice status breakdown\n        invoicesByStatus, // Top customers by revenue\n        topCustomers, // Upcoming tasks\n        upcomingTasks, // Contract renewals\n        upcomingRenewals] = await Promise.all([\n            // Current month counts\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.customer.count({\n                where: {\n                    companyId,\n                    createdAt: {\n                        gte: startOfMonth\n                    }\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.lead.count({\n                where: {\n                    companyId,\n                    status: {\n                        not: \"CLOSED_LOST\"\n                    }\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.quotation.count({\n                where: {\n                    companyId,\n                    createdAt: {\n                        gte: startOfMonth\n                    }\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.invoice.count({\n                where: {\n                    companyId,\n                    createdAt: {\n                        gte: startOfMonth\n                    }\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.contract.count({\n                where: {\n                    companyId,\n                    status: \"ACTIVE\"\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.task.count({\n                where: {\n                    companyId,\n                    status: {\n                        in: [\n                            \"TODO\",\n                            \"IN_PROGRESS\"\n                        ]\n                    }\n                }\n            }),\n            // Last month counts\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.customer.count({\n                where: {\n                    companyId,\n                    createdAt: {\n                        gte: startOfLastMonth,\n                        lte: endOfLastMonth\n                    }\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.lead.count({\n                where: {\n                    companyId,\n                    createdAt: {\n                        gte: startOfLastMonth,\n                        lte: endOfLastMonth\n                    }\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.quotation.count({\n                where: {\n                    companyId,\n                    createdAt: {\n                        gte: startOfLastMonth,\n                        lte: endOfLastMonth\n                    }\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.invoice.count({\n                where: {\n                    companyId,\n                    createdAt: {\n                        gte: startOfLastMonth,\n                        lte: endOfLastMonth\n                    }\n                }\n            }),\n            // Revenue calculations\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.invoice.aggregate({\n                where: {\n                    companyId,\n                    status: \"PAID\"\n                },\n                _sum: {\n                    total: true\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.invoice.aggregate({\n                where: {\n                    companyId,\n                    status: \"PAID\",\n                    paidAt: {\n                        gte: startOfLastMonth,\n                        lte: endOfLastMonth\n                    }\n                },\n                _sum: {\n                    total: true\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.invoice.aggregate({\n                where: {\n                    companyId,\n                    status: {\n                        in: [\n                            \"SENT\",\n                            \"VIEWED\"\n                        ]\n                    }\n                },\n                _sum: {\n                    total: true\n                }\n            }),\n            // Recent activities\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.activity.findMany({\n                where: {\n                    companyId\n                },\n                orderBy: {\n                    createdAt: \"desc\"\n                },\n                take: 10,\n                include: {\n                    createdBy: {\n                        select: {\n                            name: true,\n                            email: true\n                        }\n                    },\n                    lead: {\n                        select: {\n                            firstName: true,\n                            lastName: true\n                        }\n                    },\n                    customer: {\n                        select: {\n                            name: true\n                        }\n                    },\n                    quotation: {\n                        select: {\n                            quotationNumber: true,\n                            title: true\n                        }\n                    },\n                    invoice: {\n                        select: {\n                            invoiceNumber: true,\n                            title: true\n                        }\n                    },\n                    contract: {\n                        select: {\n                            contractNumber: true,\n                            title: true\n                        }\n                    }\n                }\n            }),\n            // Lead pipeline\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.lead.groupBy({\n                by: [\n                    \"status\"\n                ],\n                where: {\n                    companyId\n                },\n                _count: {\n                    status: true\n                }\n            }),\n            // Invoice status breakdown\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.invoice.groupBy({\n                by: [\n                    \"status\"\n                ],\n                where: {\n                    companyId\n                },\n                _count: {\n                    status: true\n                },\n                _sum: {\n                    total: true\n                }\n            }),\n            // Top customers by revenue\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.customer.findMany({\n                where: {\n                    companyId\n                },\n                select: {\n                    id: true,\n                    name: true,\n                    companyName: true,\n                    invoices: {\n                        where: {\n                            status: \"PAID\"\n                        },\n                        select: {\n                            total: true\n                        }\n                    }\n                },\n                take: 5\n            }),\n            // Upcoming tasks\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.task.findMany({\n                where: {\n                    companyId,\n                    status: {\n                        in: [\n                            \"TODO\",\n                            \"IN_PROGRESS\"\n                        ]\n                    },\n                    dueDate: {\n                        gte: now,\n                        lte: new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000) // Next 7 days\n                    }\n                },\n                orderBy: {\n                    dueDate: \"asc\"\n                },\n                take: 5,\n                include: {\n                    assignedTo: {\n                        select: {\n                            name: true,\n                            email: true\n                        }\n                    },\n                    customer: {\n                        select: {\n                            name: true\n                        }\n                    }\n                }\n            }),\n            // Upcoming contract renewals\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.contract.findMany({\n                where: {\n                    companyId,\n                    status: \"ACTIVE\",\n                    renewalDate: {\n                        gte: now,\n                        lte: new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000) // Next 30 days\n                    }\n                },\n                orderBy: {\n                    renewalDate: \"asc\"\n                },\n                take: 5,\n                include: {\n                    customer: {\n                        select: {\n                            name: true\n                        }\n                    }\n                }\n            })\n        ]);\n        // Calculate percentage changes\n        const calculateChange = (current, previous)=>{\n            if (previous === 0) return current > 0 ? 100 : 0;\n            return (current - previous) / previous * 100;\n        };\n        // Process top customers with revenue\n        const processedTopCustomers = topCustomers.map((customer)=>({\n                ...customer,\n                totalRevenue: customer.invoices.reduce((sum, invoice)=>sum + Number(invoice.total), 0)\n            })).sort((a, b)=>b.totalRevenue - a.totalRevenue);\n        const stats = {\n            customers: {\n                current: customersCount,\n                change: calculateChange(customersCount, lastMonthCustomers),\n                total: await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.customer.count({\n                    where: {\n                        companyId\n                    }\n                })\n            },\n            leads: {\n                current: leadsCount,\n                change: calculateChange(leadsCount, lastMonthLeads),\n                total: await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.lead.count({\n                    where: {\n                        companyId\n                    }\n                })\n            },\n            quotations: {\n                current: quotationsCount,\n                change: calculateChange(quotationsCount, lastMonthQuotations),\n                total: await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.quotation.count({\n                    where: {\n                        companyId\n                    }\n                })\n            },\n            invoices: {\n                current: invoicesCount,\n                change: calculateChange(invoicesCount, lastMonthInvoices),\n                total: await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.invoice.count({\n                    where: {\n                        companyId\n                    }\n                })\n            },\n            contracts: {\n                current: contractsCount,\n                total: await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.contract.count({\n                    where: {\n                        companyId\n                    }\n                })\n            },\n            tasks: {\n                current: tasksCount,\n                total: await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.task.count({\n                    where: {\n                        companyId\n                    }\n                })\n            },\n            revenue: {\n                total: Number(totalRevenue._sum.total || 0),\n                lastMonth: Number(lastMonthRevenue._sum.total || 0),\n                pending: Number(pendingRevenue._sum.total || 0),\n                change: calculateChange(Number(totalRevenue._sum.total || 0), Number(lastMonthRevenue._sum.total || 0))\n            }\n        };\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            stats,\n            recentActivities,\n            leadsByStatus,\n            invoicesByStatus,\n            topCustomers: processedTopCustomers,\n            upcomingTasks,\n            upcomingRenewals\n        });\n    } catch (error) {\n        console.error(\"Error fetching dashboard data:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to fetch dashboard data\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/dashboard/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/github */ \"(rsc)/./node_modules/next-auth/providers/github.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n\nconst authOptions = {\n    // Using JWT strategy instead of database adapter for now\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                try {\n                    if (!credentials?.email || !credentials?.password) {\n                        console.log(\"Missing credentials\");\n                        return null;\n                    }\n                    console.log(\"Attempting to authenticate user:\", credentials.email);\n                    const user = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.findUnique({\n                        where: {\n                            email: credentials.email\n                        },\n                        select: {\n                            id: true,\n                            email: true,\n                            name: true,\n                            password: true,\n                            role: true,\n                            companyId: true\n                        }\n                    });\n                    // Get the company ID - either as member or owner\n                    let finalCompanyId = user?.companyId;\n                    if (!finalCompanyId && user) {\n                        const ownedCompany = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.company.findFirst({\n                            where: {\n                                ownerId: user.id\n                            },\n                            select: {\n                                id: true\n                            }\n                        });\n                        finalCompanyId = ownedCompany?.id;\n                        // If user is company owner, update their companyId for future queries\n                        if (finalCompanyId) {\n                            await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                                where: {\n                                    id: user.id\n                                },\n                                data: {\n                                    companyId: finalCompanyId\n                                }\n                            });\n                        }\n                    }\n                    if (!user) {\n                        console.log(\"User not found:\", credentials.email);\n                        return null;\n                    }\n                    if (!user.password) {\n                        console.log(\"User has no password set:\", credentials.email);\n                        return null;\n                    }\n                    const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_3___default().compare(credentials.password, user.password);\n                    if (!isPasswordValid) {\n                        console.log(\"Invalid password for user:\", credentials.email);\n                        return null;\n                    }\n                    // Update last login\n                    await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                        where: {\n                            id: user.id\n                        },\n                        data: {\n                            lastLoginAt: new Date(),\n                            loginCount: {\n                                increment: 1\n                            }\n                        }\n                    });\n                    console.log(\"User authenticated successfully:\", user.email);\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name,\n                        role: user.role,\n                        companyId: finalCompanyId\n                    };\n                } catch (error) {\n                    console.error(\"Authentication error:\", error);\n                    return null;\n                }\n            }\n        }),\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            clientId: process.env.GITHUB_CLIENT_ID,\n            clientSecret: process.env.GITHUB_CLIENT_SECRET\n        })\n    ],\n    session: {\n        strategy: \"jwt\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                console.log(\"JWT callback - user data:\", {\n                    id: user.id,\n                    email: user.email,\n                    role: user.role,\n                    companyId: user.companyId\n                });\n                token.role = user.role;\n                token.companyId = user.companyId;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n                session.user.companyId = token.companyId;\n                // Ensure no company object is ever set on the session\n                if (session.user.company) {\n                    delete session.user.company;\n                }\n                console.log(\"Session callback - final session:\", {\n                    id: session.user.id,\n                    email: session.user.email,\n                    role: session.user.role,\n                    companyId: session.user.companyId\n                });\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        signUp: \"/auth/signup\",\n        error: \"/auth/error\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zYWFzLWFwcC8uL2xpYi9wcmlzbWEudHM/OTgyMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/lru-cache","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Froute&page=%2Fapi%2Fdashboard%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();