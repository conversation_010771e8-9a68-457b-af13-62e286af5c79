"use strict";(()=>{var e={};e.id=3707,e.ids=[3707],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},81679:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>w,originalPathname:()=>y,patchFetch:()=>I,requestAsyncStorage:()=>p,routeModule:()=>m,serverHooks:()=>g,staticGenerationAsyncStorage:()=>h,staticGenerationBailout:()=>_});var r={};a.r(r),a.d(r,{GET:()=>d});var o=a(95419),s=a(69108),n=a(99678),i=a(78070),u=a(81355),c=a(3205),l=a(9108);async function d(e){try{let e=await (0,u.getServerSession)(c.L);if(!e?.user?.id||!e?.user?.companyId)return i.Z.json({error:"Unauthorized"},{status:401});let t=e.user.companyId,a=new Date,r=new Date(a.getFullYear(),a.getMonth(),1),o=new Date(a.getFullYear(),a.getMonth()-1,1),s=new Date(a.getFullYear(),a.getMonth(),0),[n,d,m,p,h,g,w,_,y,I,v,b,f,q,x,E,A,T,D]=await Promise.all([l._.customer.count({where:{companyId:t,createdAt:{gte:r}}}),l._.lead.count({where:{companyId:t,status:{not:"CLOSED_LOST"}}}),l._.quotation.count({where:{companyId:t,createdAt:{gte:r}}}),l._.invoice.count({where:{companyId:t,createdAt:{gte:r}}}),l._.contract.count({where:{companyId:t,status:"ACTIVE"}}),l._.task.count({where:{companyId:t,status:{in:["TODO","IN_PROGRESS"]}}}),l._.customer.count({where:{companyId:t,createdAt:{gte:o,lte:s}}}),l._.lead.count({where:{companyId:t,createdAt:{gte:o,lte:s}}}),l._.quotation.count({where:{companyId:t,createdAt:{gte:o,lte:s}}}),l._.invoice.count({where:{companyId:t,createdAt:{gte:o,lte:s}}}),l._.invoice.aggregate({where:{companyId:t,status:"PAID"},_sum:{total:!0}}),l._.invoice.aggregate({where:{companyId:t,status:"PAID",paidAt:{gte:o,lte:s}},_sum:{total:!0}}),l._.invoice.aggregate({where:{companyId:t,status:{in:["SENT","VIEWED"]}},_sum:{total:!0}}),l._.activity.findMany({where:{companyId:t},orderBy:{createdAt:"desc"},take:10,include:{createdBy:{select:{name:!0,email:!0}},lead:{select:{firstName:!0,lastName:!0}},customer:{select:{name:!0}},quotation:{select:{quotationNumber:!0,title:!0}},invoice:{select:{invoiceNumber:!0,title:!0}},contract:{select:{contractNumber:!0,title:!0}}}}),l._.lead.groupBy({by:["status"],where:{companyId:t},_count:{status:!0}}),l._.invoice.groupBy({by:["status"],where:{companyId:t},_count:{status:!0},_sum:{total:!0}}),l._.customer.findMany({where:{companyId:t},select:{id:!0,name:!0,companyName:!0,invoices:{where:{status:"PAID"},select:{total:!0}}},take:5}),l._.task.findMany({where:{companyId:t,status:{in:["TODO","IN_PROGRESS"]},dueDate:{gte:a,lte:new Date(a.getTime()+6048e5)}},orderBy:{dueDate:"asc"},take:5,include:{assignedTo:{select:{name:!0,email:!0}},customer:{select:{name:!0}}}}),l._.contract.findMany({where:{companyId:t,status:"ACTIVE",renewalDate:{gte:a,lte:new Date(a.getTime()+2592e6)}},orderBy:{renewalDate:"asc"},take:5,include:{customer:{select:{name:!0}}}})]),N=(e,t)=>0===t?e>0?100:0:(e-t)/t*100,S=A.map(e=>({...e,totalRevenue:e.invoices.reduce((e,t)=>e+Number(t.total),0)})).sort((e,t)=>t.totalRevenue-e.totalRevenue),k={customers:{current:n,change:N(n,w),total:await l._.customer.count({where:{companyId:t}})},leads:{current:d,change:N(d,_),total:await l._.lead.count({where:{companyId:t}})},quotations:{current:m,change:N(m,y),total:await l._.quotation.count({where:{companyId:t}})},invoices:{current:p,change:N(p,I),total:await l._.invoice.count({where:{companyId:t}})},contracts:{current:h,total:await l._.contract.count({where:{companyId:t}})},tasks:{current:g,total:await l._.task.count({where:{companyId:t}})},revenue:{total:Number(v._sum.total||0),lastMonth:Number(b._sum.total||0),pending:Number(f._sum.total||0),change:N(Number(v._sum.total||0),Number(b._sum.total||0))}};return i.Z.json({stats:k,recentActivities:q,leadsByStatus:x,invoicesByStatus:E,topCustomers:S,upcomingTasks:T,upcomingRenewals:D})}catch(e){return console.error("Error fetching dashboard data:",e),i.Z.json({error:"Failed to fetch dashboard data"},{status:500})}}let m=new o.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/dashboard/route",pathname:"/api/dashboard",filename:"route",bundlePath:"app/api/dashboard/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\dashboard\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:p,staticGenerationAsyncStorage:h,serverHooks:g,headerHooks:w,staticGenerationBailout:_}=m,y="/api/dashboard/route";function I(){return(0,n.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:h})}},3205:(e,t,a)=>{a.d(t,{L:()=>c});var r=a(86485),o=a(10375),s=a(50694),n=a(6521),i=a.n(n),u=a(9108);let c={providers:[(0,r.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await u._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),a=t?.companyId;if(!a&&t){let e=await u._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(a=e?.id)&&await u._.user.update({where:{id:t.id},data:{companyId:a}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await i().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await u._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:a}}catch(e){return console.error("Authentication error:",e),null}}}),(0,o.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,s.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,a)=>{a.d(t,{_:()=>o});let r=require("@prisma/client"),o=globalThis.prisma??new r.PrismaClient}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[1638,6206,6521,2455,4520],()=>a(81679));module.exports=r})();