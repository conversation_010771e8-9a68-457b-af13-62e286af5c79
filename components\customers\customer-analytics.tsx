'use client'

import { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Users,
  DollarSign,
  TrendingUp,
  UserCheck,
  Activity,
  Target,
  Star,
  RefreshCw,
  BarChart3
} from 'lucide-react'
import { toast } from 'react-hot-toast'

interface CustomerAnalytics {
  summary: {
    totalCustomers: number
    totalRevenue: number
    avgCLV: number
    retentionRate: number
    newCustomersThisWeek: number
    engagementRate: number
  }
  customersByStatus: Array<{
    status: string
    count: number
  }>
  customersByIndustry: Array<{
    industry: string
    count: number
  }>
  customerGrowth: Array<{
    date: string
    customer_count: number
  }>
  topCustomers: Array<{
    id: string
    name: string
    company: string | null
    email: string | null
    status: string
    totalRevenue: number
  }>
  engagement: {
    withActivities: number
    withQuotations: number
    withInvoices: number
  }
  recentCustomers: Array<{
    id: string
    name: string
    company: string | null
    email: string | null
    status: string
    createdAt: string
    createdBy: {
      name: string | null
      email: string | null
    }
  }>
  segmentation: {
    highValue: number
    active: number
    prospects: number
  }
  clvMetrics: Array<{
    id: string
    name: string
    totalRevenue: number
    customerAge: number
    avgMonthlyRevenue: number
    clv: number
  }>
  period: number
}

export function CustomerAnalytics() {
  const [analytics, setAnalytics] = useState<CustomerAnalytics | null>(null)
  const [loading, setLoading] = useState(true)
  const [period, setPeriod] = useState('30')

  const fetchAnalytics = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/customers/analytics?period=${period}`)
      if (!response.ok) {
        throw new Error('Failed to fetch analytics')
      }

      const data = await response.json()
      setAnalytics(data)
    } catch (error) {
      toast.error('Failed to load customer analytics')
      console.error('Error fetching analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchAnalytics()
  }, [period])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800'
      case 'INACTIVE':
        return 'bg-gray-100 text-gray-800'
      case 'PROSPECT':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!analytics) {
    return (
      <div className="text-center py-8 text-gray-500">
        <BarChart3 className="h-12 w-12 mx-auto mb-4 text-gray-300" />
        <p>Failed to load analytics data</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Customer Analytics</h3>
        <div className="flex items-center space-x-2">
          <Select value={period} onValueChange={setPeriod}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">Last 7 days</SelectItem>
              <SelectItem value="30">Last 30 days</SelectItem>
              <SelectItem value="90">Last 90 days</SelectItem>
              <SelectItem value="365">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={fetchAnalytics} size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-blue-100 rounded-full">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Total Customers</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.summary.totalCustomers}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-green-100 rounded-full">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Total Revenue</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(analytics.summary.totalRevenue)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-purple-100 rounded-full">
                <TrendingUp className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Avg CLV</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(analytics.summary.avgCLV)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-orange-100 rounded-full">
                <UserCheck className="h-6 w-6 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Retention Rate</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analytics.summary.retentionRate.toFixed(1)}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-indigo-100 rounded-full">
                <Activity className="h-6 w-6 text-indigo-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Engagement Rate</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analytics.summary.engagementRate}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-pink-100 rounded-full">
                <Star className="h-6 w-6 text-pink-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">New This Week</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analytics.summary.newCustomersThisWeek}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts and Breakdowns */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Customers by Status */}
        <Card>
          <CardHeader>
            <CardTitle>Customers by Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.customersByStatus.map((item) => (
                <div key={item.status} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Badge className={getStatusColor(item.status)}>
                      {item.status}
                    </Badge>
                  </div>
                  <span className="font-semibold">{item.count}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Customer Segmentation */}
        <Card>
          <CardHeader>
            <CardTitle>Customer Segmentation</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">High Value Customers</span>
                <span className="font-semibold">{analytics.segmentation.highValue}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Active Customers</span>
                <span className="font-semibold">{analytics.segmentation.active}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Prospects</span>
                <span className="font-semibold">{analytics.segmentation.prospects}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Customers by Industry */}
        <Card>
          <CardHeader>
            <CardTitle>Customers by Industry</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.customersByIndustry.slice(0, 5).map((item) => (
                <div key={item.industry} className="flex items-center justify-between">
                  <span className="text-sm">{item.industry}</span>
                  <span className="font-semibold">{item.count}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Engagement Metrics */}
        <Card>
          <CardHeader>
            <CardTitle>Customer Engagement</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">With Recent Activities</span>
                <span className="font-semibold">{analytics.engagement.withActivities}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">With Recent Quotations</span>
                <span className="font-semibold">{analytics.engagement.withQuotations}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">With Recent Invoices</span>
                <span className="font-semibold">{analytics.engagement.withInvoices}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Customers */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Target className="h-5 w-5 mr-2" />
            Top Customers by Revenue
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {analytics.topCustomers.length === 0 ? (
              <p className="text-gray-500 text-center py-4">No customer data available</p>
            ) : (
              analytics.topCustomers.map((customer, index) => (
                <div key={customer.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-sm font-semibold text-blue-600">#{index + 1}</span>
                    </div>
                    <div>
                      <p className="font-medium">{customer.name}</p>
                      <p className="text-sm text-gray-500">
                        {customer.company && `${customer.company} • `}
                        {customer.email}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-green-600">
                      {formatCurrency(customer.totalRevenue)}
                    </p>
                    <Badge className={getStatusColor(customer.status)} variant="outline">
                      {customer.status}
                    </Badge>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>

      {/* Recent Customers */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Customers (Last 7 Days)</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {analytics.recentCustomers.length === 0 ? (
              <p className="text-gray-500 text-center py-4">No new customers this week</p>
            ) : (
              analytics.recentCustomers.map((customer) => (
                <div key={customer.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium">{customer.name}</p>
                    <p className="text-sm text-gray-500">
                      {customer.company && `${customer.company} • `}
                      Added by {customer.createdBy.name || customer.createdBy.email}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-500">
                      {new Date(customer.createdAt).toLocaleDateString()}
                    </p>
                    <Badge className={getStatusColor(customer.status)} variant="outline">
                      {customer.status}
                    </Badge>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>

      {/* Customer Lifetime Value */}
      <Card>
        <CardHeader>
          <CardTitle>Customer Lifetime Value Analysis</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {analytics.clvMetrics.length === 0 ? (
              <p className="text-gray-500 text-center py-4">No CLV data available</p>
            ) : (
              analytics.clvMetrics.map((customer) => (
                <div key={customer.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium">{customer.name}</p>
                    <p className="text-sm text-gray-500">
                      Customer for {customer.customerAge} days
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-purple-600">
                      CLV: {formatCurrency(customer.clv)}
                    </p>
                    <p className="text-sm text-gray-500">
                      Total: {formatCurrency(customer.totalRevenue)}
                    </p>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
