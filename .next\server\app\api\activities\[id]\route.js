"use strict";(()=>{var e={};e.id=4804,e.ids=[4804],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},28478:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>x,originalPathname:()=>E,patchFetch:()=>q,requestAsyncStorage:()=>h,routeModule:()=>v,serverHooks:()=>I,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>j});var i={};r.r(i),r.d(i,{DELETE:()=>f,GET:()=>y,PUT:()=>w});var a=r(95419),s=r(69108),o=r(99678),n=r(78070),l=r(81355),c=r(3205),d=r(9108),u=r(25252),p=r(52178);let m=u.Ry({type:u.Km(["NOTE","CALL","EMAIL","MEETING","TASK"]).optional(),title:u.Z_().min(1,"Title is required").optional(),description:u.Z_().optional().nullable()});async function y(e,{params:t}){try{let e=await (0,l.getServerSession)(c.L);if(!e?.user?.id||!e?.user?.companyId)return n.Z.json({error:"Unauthorized"},{status:401});let r=await d._.activity.findFirst({where:{id:t.id,companyId:e.user.companyId},include:{lead:{select:{id:!0,firstName:!0,lastName:!0,companyName:!0}},createdBy:{select:{id:!0,name:!0,email:!0}}}});if(!r)return n.Z.json({error:"Activity not found"},{status:404});return n.Z.json({activity:r})}catch(e){return console.error("Error fetching activity:",e),n.Z.json({error:"Failed to fetch activity"},{status:500})}}async function w(e,{params:t}){try{let r=await (0,l.getServerSession)(c.L);if(!r?.user?.id||!r?.user?.companyId)return n.Z.json({error:"Unauthorized"},{status:401});let i=await e.json(),a=m.parse(i);if(!await d._.activity.findFirst({where:{id:t.id,companyId:r.user.companyId}}))return n.Z.json({error:"Activity not found"},{status:404});let s={...a};s.updatedAt=new Date;let o=await d._.activity.update({where:{id:t.id},data:s,include:{lead:{select:{id:!0,firstName:!0,lastName:!0,companyName:!0}},createdBy:{select:{id:!0,name:!0,email:!0}}}});return n.Z.json({activity:o})}catch(e){if(e instanceof p.jm)return n.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("Error updating activity:",e),n.Z.json({error:"Failed to update activity"},{status:500})}}async function f(e,{params:t}){try{let e=await (0,l.getServerSession)(c.L);if(!e?.user?.id||!e?.user?.companyId)return n.Z.json({error:"Unauthorized"},{status:401});if(!await d._.activity.findFirst({where:{id:t.id,companyId:e.user.companyId}}))return n.Z.json({error:"Activity not found"},{status:404});return await d._.activity.delete({where:{id:t.id}}),n.Z.json({message:"Activity deleted successfully"})}catch(e){return console.error("Error deleting activity:",e),n.Z.json({error:"Failed to delete activity"},{status:500})}}let v=new a.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/activities/[id]/route",pathname:"/api/activities/[id]",filename:"route",bundlePath:"app/api/activities/[id]/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\activities\\[id]\\route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:h,staticGenerationAsyncStorage:g,serverHooks:I,headerHooks:x,staticGenerationBailout:j}=v,E="/api/activities/[id]/route";function q(){return(0,o.patchFetch)({serverHooks:I,staticGenerationAsyncStorage:g})}},3205:(e,t,r)=>{r.d(t,{L:()=>c});var i=r(86485),a=r(10375),s=r(50694),o=r(6521),n=r.n(o),l=r(9108);let c={providers:[(0,i.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await l._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),r=t?.companyId;if(!r&&t){let e=await l._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(r=e?.id)&&await l._.user.update({where:{id:t.id},data:{companyId:r}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await n().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await l._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:r}}catch(e){return console.error("Authentication error:",e),null}}}),(0,a.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,s.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,r)=>{r.d(t,{_:()=>a});let i=require("@prisma/client"),a=globalThis.prisma??new i.PrismaClient}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[1638,6206,6521,2455,4520,5252],()=>r(28478));module.exports=i})();