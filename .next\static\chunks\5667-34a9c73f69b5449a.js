(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5667],{72894:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},98253:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Building2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},13008:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6141:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},49168:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Crown",[["path",{d:"m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7zm3 16h14",key:"zkxr6b"}]])},41298:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},99670:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},28956:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},17472:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("MoreHorizontal",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},49617:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},9883:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},64280:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},41827:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},85790:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},82104:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},82549:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},24033:function(e,t,n){e.exports=n(15313)},99808:function(e,t,n){"use strict";/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=n(2265),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=r.useState,u=r.useEffect,a=r.useLayoutEffect,l=r.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!o(e,n)}catch(e){return!0}}var s="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=i({inst:{value:n,getSnapshot:t}}),o=r[0].inst,s=r[1];return a(function(){o.value=n,o.getSnapshot=t,c(o)&&s({inst:o})},[e,n,t]),u(function(){return c(o)&&s({inst:o}),e(function(){c(o)&&s({inst:o})})},[e]),l(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:s},26272:function(e,t,n){"use strict";e.exports=n(99808)},61465:function(e,t,n){"use strict";n.d(t,{NY:function(){return b},Ee:function(){return N},fC:function(){return x}});var r=n(2265),o=n(56989),i=n(16459),u=n(51030),a=n(9381),l=n(26272);function c(){return()=>{}}var s=n(57437),d="Avatar",[f,p]=(0,o.b)(d),[y,m]=f(d),h=r.forwardRef((e,t)=>{let{__scopeAvatar:n,...o}=e,[i,u]=r.useState("idle");return(0,s.jsx)(y,{scope:n,imageLoadingStatus:i,onImageLoadingStatusChange:u,children:(0,s.jsx)(a.WV.span,{...o,ref:t})})});h.displayName=d;var g="AvatarImage",v=r.forwardRef((e,t)=>{let{__scopeAvatar:n,src:o,onLoadingStatusChange:d=()=>{},...f}=e,p=m(g,n),y=function(e,{referrerPolicy:t,crossOrigin:n}){let o=(0,l.useSyncExternalStore)(c,()=>!0,()=>!1),i=r.useRef(null),a=o?(i.current||(i.current=new window.Image),i.current):null,[s,d]=r.useState(()=>w(a,e));return(0,u.b)(()=>{d(w(a,e))},[a,e]),(0,u.b)(()=>{let e=e=>()=>{d(e)};if(!a)return;let r=e("loaded"),o=e("error");return a.addEventListener("load",r),a.addEventListener("error",o),t&&(a.referrerPolicy=t),"string"==typeof n&&(a.crossOrigin=n),()=>{a.removeEventListener("load",r),a.removeEventListener("error",o)}},[a,n,t]),s}(o,f),h=(0,i.W)(e=>{d(e),p.onImageLoadingStatusChange(e)});return(0,u.b)(()=>{"idle"!==y&&h(y)},[y,h]),"loaded"===y?(0,s.jsx)(a.WV.img,{...f,ref:t,src:o}):null});v.displayName=g;var k="AvatarFallback",M=r.forwardRef((e,t)=>{let{__scopeAvatar:n,delayMs:o,...i}=e,u=m(k,n),[l,c]=r.useState(void 0===o);return r.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>c(!0),o);return()=>window.clearTimeout(e)}},[o]),l&&"loaded"!==u.imageLoadingStatus?(0,s.jsx)(a.WV.span,{...i,ref:t}):null});function w(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}M.displayName=k;var x=h,N=v,b=M},28712:function(e,t,n){"use strict";n.d(t,{Dx:function(){return er},VY:function(){return en},aV:function(){return et},dk:function(){return eo},fC:function(){return J},h_:function(){return ee},x8:function(){return ei},xz:function(){return Q}});var r=n(2265),o=n(85744),i=n(42210),u=n(56989),a=n(20966),l=n(73763),c=n(79249),s=n(52759),d=n(52730),f=n(85606),p=n(9381),y=n(31244),m=n(73386),h=n(85859),g=n(67256),v=n(57437),k="Dialog",[M,w]=(0,u.b)(k),[x,N]=M(k),b=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:i,onOpenChange:u,modal:c=!0}=e,s=r.useRef(null),d=r.useRef(null),[f,p]=(0,l.T)({prop:o,defaultProp:i??!1,onChange:u,caller:k});return(0,v.jsx)(x,{scope:t,triggerRef:s,contentRef:d,contentId:(0,a.M)(),titleId:(0,a.M)(),descriptionId:(0,a.M)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:c,children:n})};b.displayName=k;var E="DialogTrigger",Z=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,u=N(E,n),a=(0,i.e)(t,u.triggerRef);return(0,v.jsx)(p.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":u.open,"aria-controls":u.contentId,"data-state":$(u.open),...r,ref:a,onClick:(0,o.M)(e.onClick,u.onOpenToggle)})});Z.displayName=E;var D="DialogPortal",[R,j]=M(D,{forceMount:void 0}),O=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:i}=e,u=N(D,t);return(0,v.jsx)(R,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,v.jsx)(f.z,{present:n||u.open,children:(0,v.jsx)(d.h,{asChild:!0,container:i,children:e})}))})};O.displayName=D;var C="DialogOverlay",I=r.forwardRef((e,t)=>{let n=j(C,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=N(C,e.__scopeDialog);return i.modal?(0,v.jsx)(f.z,{present:r||i.open,children:(0,v.jsx)(T,{...o,ref:t})}):null});I.displayName=C;var S=(0,g.Z8)("DialogOverlay.RemoveScroll"),T=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=N(C,n);return(0,v.jsx)(m.Z,{as:S,allowPinchZoom:!0,shards:[o.contentRef],children:(0,v.jsx)(p.WV.div,{"data-state":$(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),A="DialogContent",_=r.forwardRef((e,t)=>{let n=j(A,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=N(A,e.__scopeDialog);return(0,v.jsx)(f.z,{present:r||i.open,children:i.modal?(0,v.jsx)(F,{...o,ref:t}):(0,v.jsx)(L,{...o,ref:t})})});_.displayName=A;var F=r.forwardRef((e,t)=>{let n=N(A,e.__scopeDialog),u=r.useRef(null),a=(0,i.e)(t,n.contentRef,u);return r.useEffect(()=>{let e=u.current;if(e)return(0,h.Ry)(e)},[]),(0,v.jsx)(P,{...e,ref:a,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{e.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:(0,o.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault())})}),L=r.forwardRef((e,t)=>{let n=N(A,e.__scopeDialog),o=r.useRef(!1),i=r.useRef(!1);return(0,v.jsx)(P,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(o.current||n.triggerRef.current?.focus(),t.preventDefault()),o.current=!1,i.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(i.current=!0));let r=t.target;n.triggerRef.current?.contains(r)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),P=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:u,onCloseAutoFocus:a,...l}=e,d=N(A,n),f=r.useRef(null),p=(0,i.e)(t,f);return(0,y.EW)(),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(s.M,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:u,onUnmountAutoFocus:a,children:(0,v.jsx)(c.XB,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":$(d.open),...l,ref:p,onDismiss:()=>d.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(G,{titleId:d.titleId}),(0,v.jsx)(K,{contentRef:f,descriptionId:d.descriptionId})]})]})}),W="DialogTitle",z=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=N(W,n);return(0,v.jsx)(p.WV.h2,{id:o.titleId,...r,ref:t})});z.displayName=W;var V="DialogDescription",U=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=N(V,n);return(0,v.jsx)(p.WV.p,{id:o.descriptionId,...r,ref:t})});U.displayName=V;var q="DialogClose",H=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=N(q,n);return(0,v.jsx)(p.WV.button,{type:"button",...r,ref:t,onClick:(0,o.M)(e.onClick,()=>i.onOpenChange(!1))})});function $(e){return e?"open":"closed"}H.displayName=q;var B="DialogTitleWarning",[X,Y]=(0,u.k)(B,{contentName:A,titleName:W,docsSlug:"dialog"}),G=({titleId:e})=>{let t=Y(B),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return r.useEffect(()=>{e&&!document.getElementById(e)&&console.error(n)},[n,e]),null},K=({contentRef:e,descriptionId:t})=>{let n=Y("DialogDescriptionWarning"),o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${n.contentName}}.`;return r.useEffect(()=>{let n=e.current?.getAttribute("aria-describedby");t&&n&&!document.getElementById(t)&&console.warn(o)},[o,e,t]),null},J=b,Q=Z,ee=O,et=I,en=_,er=z,eo=U,ei=H},85606:function(e,t,n){"use strict";n.d(t,{z:function(){return u}});var r=n(2265),o=n(42210),i=n(51030),u=e=>{let t,n;let{present:u,children:l}=e,c=function(e){var t,n;let[o,u]=r.useState(),l=r.useRef(null),c=r.useRef(e),s=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>n[e][t]??e,t));return r.useEffect(()=>{let e=a(l.current);s.current="mounted"===d?e:"none"},[d]),(0,i.b)(()=>{let t=l.current,n=c.current;if(n!==e){let r=s.current,o=a(t);e?f("MOUNT"):"none"===o||t?.display==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),c.current=e}},[e,f]),(0,i.b)(()=>{if(o){let e;let t=o.ownerDocument.defaultView??window,n=n=>{let r=a(l.current).includes(n.animationName);if(n.target===o&&r&&(f("ANIMATION_END"),!c.current)){let n=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=n)})}},r=e=>{e.target===o&&(s.current=a(l.current))};return o.addEventListener("animationstart",r),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",r),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{l.current=e?getComputedStyle(e):null,u(e)},[])}}(u),s="function"==typeof l?l({present:c.isPresent}):r.Children.only(l),d=(0,o.e)(c.ref,(t=Object.getOwnPropertyDescriptor(s.props,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning?s.ref:(t=Object.getOwnPropertyDescriptor(s,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning?s.props.ref:s.props.ref||s.ref);return"function"==typeof l||c.isPresent?r.cloneElement(s,{ref:d}):null};function a(e){return e?.animationName||"none"}u.displayName="Presence"}}]);