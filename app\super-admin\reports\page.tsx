'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { redirect } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  <PERSON>bs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs'
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Users,
  Building2,
  DollarSign,
  Activity,
  Download,
  RefreshCw,
  Calendar,
  Globe,
  PieChart,
  LineChart,
  Target,
  Zap,
  Clock,
  CheckCircle,
  AlertTriangle
} from 'lucide-react'

interface ReportData {
  overview: {
    totalUsers: number
    totalCompanies: number
    totalRevenue: number
    totalActivities: number
    activeUsers: number
    activeCompanies: number
    userGrowthRate: number
    conversionRate: number
  }
  timeSeries: {
    userGrowth: Array<{ date: string; value: number }>
    companyGrowth: Array<{ date: string; value: number }>
    revenueGrowth: Array<{ date: string; value: number }>
    activityGrowth: Array<{ date: string; value: number }>
  }
  distribution: {
    geographic: Array<{ country: string; count: number }>
    industry: Array<{ industry: string; count: number }>
    roles: Array<{ role: string; count: number }>
    plans: Array<{ plan: string; count: number; revenue: number }>
  }
  topCompanies: Array<{
    id: string
    name: string
    industry?: string
    subscription?: any
    metrics: {
      users: number
      customers: number
      quotations: number
      invoices: number
      activities: number
    }
  }>
  recentActivities: Array<{
    id: string
    type: string
    title: string
    company?: string
    user?: string
    createdAt: Date
  }>
  funnel: {
    totalCompanies: number
    activeCompanies: number
    paidSubscriptions: number
    revenueGenerating: number
  }
  performance: {
    userEngagement: {
      dailyActiveUsers: number
      weeklyActiveUsers: number
      monthlyActiveUsers: number
    }
    systemHealth: {
      errorRate: number
      avgResponseTime: number
      uptime: number
    }
  }
  period: number
  generatedAt: string
}

export default function AdvancedReportsPage() {
  const { data: session, status } = useSession()
  const [reportData, setReportData] = useState<ReportData | null>(null)
  const [loading, setLoading] = useState(true)
  const [period, setPeriod] = useState('30')
  const [reportType, setReportType] = useState('overview')

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (status === 'unauthenticated') {
    redirect('/auth/signin')
  }

  if (session?.user?.role !== 'SUPER_ADMIN') {
    redirect('/dashboard')
  }

  const fetchReportData = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        period,
        type: reportType
      })

      const response = await fetch(`/api/super-admin/reports?${params}`)
      if (!response.ok) throw new Error('Failed to fetch report data')

      const data = await response.json()
      setReportData(data)
    } catch (error) {
      console.error('Error fetching report data:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchReportData()
  }, [period, reportType])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num)
  }

  const formatPercentage = (num: number) => {
    return `${num >= 0 ? '+' : ''}${num.toFixed(1)}%`
  }

  const getGrowthIcon = (rate: number) => {
    return rate >= 0 ? TrendingUp : TrendingDown
  }

  const getGrowthColor = (rate: number) => {
    return rate >= 0 ? 'text-green-600' : 'text-red-600'
  }

  if (!reportData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <div className="flex items-center space-x-3">
            <BarChart3 className="h-8 w-8 text-blue-600" />
            <h1 className="text-3xl font-bold text-gray-900">Advanced Analytics</h1>
          </div>
          <p className="text-gray-500 mt-1">Comprehensive reports and business intelligence</p>
          <div className="flex items-center space-x-2 mt-2 text-sm text-gray-500">
            <Clock className="h-4 w-4" />
            <span>Last updated: {new Date(reportData.generatedAt).toLocaleString()}</span>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={period} onValueChange={setPeriod}>
            <SelectTrigger className="w-36">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">Last 7 days</SelectItem>
              <SelectItem value="30">Last 30 days</SelectItem>
              <SelectItem value="90">Last 90 days</SelectItem>
              <SelectItem value="365">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={fetchReportData} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Users</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatNumber(reportData.overview.totalUsers)}
                </p>
              </div>
              <Users className="h-8 w-8 text-blue-600" />
            </div>
            <div className="mt-4 flex items-center space-x-2">
              {(() => {
                const GrowthIcon = getGrowthIcon(reportData.overview.userGrowthRate)
                return (
                  <>
                    <GrowthIcon className={`h-4 w-4 ${getGrowthColor(reportData.overview.userGrowthRate)}`} />
                    <span className={`text-sm font-medium ${getGrowthColor(reportData.overview.userGrowthRate)}`}>
                      {formatPercentage(reportData.overview.userGrowthRate)}
                    </span>
                    <span className="text-sm text-gray-500">vs last period</span>
                  </>
                )
              })()}
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Companies</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatNumber(reportData.overview.totalCompanies)}
                </p>
              </div>
              <Building2 className="h-8 w-8 text-green-600" />
            </div>
            <div className="mt-4">
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Active:</span>
                <span className="font-medium text-green-600">
                  {reportData.overview.activeCompanies} ({((reportData.overview.activeCompanies / reportData.overview.totalCompanies) * 100).toFixed(1)}%)
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Revenue</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(reportData.overview.totalRevenue)}
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-purple-600" />
            </div>
            <div className="mt-4">
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Avg/Company:</span>
                <span className="font-medium text-purple-600">
                  {formatCurrency(reportData.overview.totalRevenue / Math.max(reportData.overview.activeCompanies, 1))}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">System Activities</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatNumber(reportData.overview.totalActivities)}
                </p>
              </div>
              <Activity className="h-8 w-8 text-orange-600" />
            </div>
            <div className="mt-4">
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Avg/Day:</span>
                <span className="font-medium text-orange-600">
                  {Math.round(reportData.overview.totalActivities / reportData.period)}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Conversion Funnel */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Target className="h-5 w-5" />
            <span>Conversion Funnel</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="w-full bg-blue-100 rounded-lg p-6 mb-3">
                <Building2 className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                <p className="text-2xl font-bold text-blue-600">{reportData.funnel.totalCompanies}</p>
                <p className="text-sm text-gray-600">Total Companies</p>
              </div>
              <p className="text-xs text-gray-500">100%</p>
            </div>
            
            <div className="text-center">
              <div className="w-full bg-green-100 rounded-lg p-6 mb-3">
                <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
                <p className="text-2xl font-bold text-green-600">{reportData.funnel.activeCompanies}</p>
                <p className="text-sm text-gray-600">Active Companies</p>
              </div>
              <p className="text-xs text-gray-500">
                {((reportData.funnel.activeCompanies / reportData.funnel.totalCompanies) * 100).toFixed(1)}%
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-full bg-purple-100 rounded-lg p-6 mb-3">
                <DollarSign className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                <p className="text-2xl font-bold text-purple-600">{reportData.funnel.paidSubscriptions}</p>
                <p className="text-sm text-gray-600">Paid Subscriptions</p>
              </div>
              <p className="text-xs text-gray-500">
                {((reportData.funnel.paidSubscriptions / reportData.funnel.totalCompanies) * 100).toFixed(1)}%
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-full bg-orange-100 rounded-lg p-6 mb-3">
                <Zap className="h-8 w-8 text-orange-600 mx-auto mb-2" />
                <p className="text-2xl font-bold text-orange-600">{reportData.funnel.revenueGenerating}</p>
                <p className="text-sm text-gray-600">Revenue Generating</p>
              </div>
              <p className="text-xs text-gray-500">
                {((reportData.funnel.revenueGenerating / reportData.funnel.totalCompanies) * 100).toFixed(1)}%
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Users className="h-5 w-5" />
              <span>User Engagement</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-600">Daily Active Users</span>
                <span className="text-lg font-bold text-blue-600">
                  {formatNumber(reportData.performance.userEngagement.dailyActiveUsers)}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-600">Weekly Active Users</span>
                <span className="text-lg font-bold text-green-600">
                  {formatNumber(reportData.performance.userEngagement.weeklyActiveUsers)}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-600">Monthly Active Users</span>
                <span className="text-lg font-bold text-purple-600">
                  {formatNumber(reportData.performance.userEngagement.monthlyActiveUsers)}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Activity className="h-5 w-5" />
              <span>System Health</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-600">Error Rate</span>
                <div className="flex items-center space-x-2">
                  <span className="text-lg font-bold text-red-600">
                    {reportData.performance.systemHealth.errorRate.toFixed(2)}%
                  </span>
                  {reportData.performance.systemHealth.errorRate < 1 ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <AlertTriangle className="h-4 w-4 text-red-500" />
                  )}
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-600">Avg Response Time</span>
                <span className="text-lg font-bold text-blue-600">
                  {reportData.performance.systemHealth.avgResponseTime}ms
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-600">System Uptime</span>
                <div className="flex items-center space-x-2">
                  <span className="text-lg font-bold text-green-600">
                    {reportData.performance.systemHealth.uptime}%
                  </span>
                  <CheckCircle className="h-4 w-4 text-green-500" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Distribution Charts */}
      <Tabs defaultValue="geographic" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="geographic">Geographic</TabsTrigger>
          <TabsTrigger value="industry">Industry</TabsTrigger>
          <TabsTrigger value="roles">User Roles</TabsTrigger>
          <TabsTrigger value="plans">Subscription Plans</TabsTrigger>
        </TabsList>

        <TabsContent value="geographic">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Globe className="h-5 w-5" />
                <span>Geographic Distribution</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {reportData.distribution.geographic.slice(0, 10).map((item, index) => (
                  <div key={item.country} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className={`w-3 h-3 rounded-full ${
                        index === 0 ? 'bg-blue-500' :
                        index === 1 ? 'bg-green-500' :
                        index === 2 ? 'bg-purple-500' :
                        index === 3 ? 'bg-orange-500' : 'bg-gray-500'
                      }`} />
                      <span className="text-sm font-medium">{item.country}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-600">{item.count}</span>
                      <span className="text-xs text-gray-400">
                        ({((item.count / reportData.overview.totalCompanies) * 100).toFixed(1)}%)
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="industry">
          <Card>
            <CardHeader>
              <CardTitle>Industry Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {reportData.distribution.industry.slice(0, 10).map((item, index) => (
                  <div key={item.industry} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className={`w-3 h-3 rounded-full ${
                        index === 0 ? 'bg-blue-500' :
                        index === 1 ? 'bg-green-500' :
                        index === 2 ? 'bg-purple-500' :
                        index === 3 ? 'bg-orange-500' : 'bg-gray-500'
                      }`} />
                      <span className="text-sm font-medium">{item.industry}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-600">{item.count}</span>
                      <span className="text-xs text-gray-400">
                        ({((item.count / reportData.overview.totalCompanies) * 100).toFixed(1)}%)
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="roles">
          <Card>
            <CardHeader>
              <CardTitle>User Role Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {reportData.distribution.roles.map((item, index) => (
                  <div key={item.role} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className={`w-3 h-3 rounded-full ${
                        index === 0 ? 'bg-red-500' :
                        index === 1 ? 'bg-purple-500' :
                        index === 2 ? 'bg-blue-500' : 'bg-gray-500'
                      }`} />
                      <span className="text-sm font-medium">{item.role}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-600">{item.count}</span>
                      <span className="text-xs text-gray-400">
                        ({((item.count / reportData.overview.totalUsers) * 100).toFixed(1)}%)
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="plans">
          <Card>
            <CardHeader>
              <CardTitle>Subscription Plan Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {reportData.distribution.plans.map((item, index) => (
                  <div key={item.plan} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className={`w-3 h-3 rounded-full ${
                        index === 0 ? 'bg-purple-500' :
                        index === 1 ? 'bg-blue-500' :
                        index === 2 ? 'bg-green-500' : 'bg-orange-500'
                      }`} />
                      <span className="text-sm font-medium">{item.plan}</span>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <p className="text-sm text-gray-600">{item.count} subscriptions</p>
                        <p className="text-xs text-gray-400">{formatCurrency(item.revenue)} revenue</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Top Companies */}
      <Card>
        <CardHeader>
          <CardTitle>Top Performing Companies</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {reportData.topCompanies.slice(0, 10).map((company, index) => (
              <div key={company.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                    {index + 1}
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{company.name}</p>
                    <p className="text-sm text-gray-500">{company.industry}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-6 text-sm">
                  <div className="text-center">
                    <p className="font-medium text-gray-900">{company.metrics.users}</p>
                    <p className="text-xs text-gray-500">Users</p>
                  </div>
                  <div className="text-center">
                    <p className="font-medium text-gray-900">{company.metrics.customers}</p>
                    <p className="text-xs text-gray-500">Customers</p>
                  </div>
                  <div className="text-center">
                    <p className="font-medium text-gray-900">{company.metrics.activities}</p>
                    <p className="text-xs text-gray-500">Activities</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
