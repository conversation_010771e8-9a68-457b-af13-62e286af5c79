"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6950],{56950:function(e,s,i){i.d(s,{p:function(){return p}});var l=i(57437),t=i(2265),a=i(61865),r=i(37570),d=i(92160),n=i(85754),o=i(45179),c=i(49842),m=i(23444),h=i(45509),x=i(42706),u=i(5925);let j=d.Ry({firstName:d.Z_().min(1,"First name is required"),lastName:d.Z_().min(1,"Last name is required"),email:d.Z_().email("Invalid email address"),phone:d.Z_().optional(),companyName:d.Z_().optional(),title:d.Z_().optional(),website:d.Z_().url().optional().or(d.i0("")),source:d.Km(["WEBSITE","REFERRAL","SOCIAL_MEDIA","EMAIL_CAMPAIGN","COLD_CALL","TRADE_SHOW","PARTNER","OTHER"]).default("OTHER"),status:d.Km(["NEW","CONTACTED","QUALIFIED","PROPOSAL","NEGOTIATION","CLOSED_WON","CLOSED_LOST"]).default("NEW"),priority:d.Km(["LOW","MEDIUM","HIGH","URGENT"]).default("MEDIUM"),address:d.Z_().optional(),city:d.Z_().optional(),state:d.Z_().optional(),country:d.Z_().optional(),postalCode:d.Z_().optional(),industry:d.Z_().optional(),companySize:d.Z_().optional(),budget:d.Rx().min(0).optional(),timeline:d.Z_().optional(),description:d.Z_().optional()});function p(e){let{isOpen:s,onClose:i,onSuccess:d,lead:p,mode:N}=e,[v,y]=(0,t.useState)(!1),{register:g,handleSubmit:f,formState:{errors:_},reset:E,setValue:I,watch:C}=(0,a.cI)({resolver:(0,r.F)(j),defaultValues:{source:"OTHER",status:"NEW",priority:"MEDIUM"}});(0,t.useEffect)(()=>{p&&"edit"===N?E({firstName:p.firstName,lastName:p.lastName,email:p.email,phone:p.phone||"",companyName:p.companyName||"",title:p.title||"",website:p.website||"",source:p.source,status:p.status,priority:p.priority,address:p.address||"",city:p.city||"",state:p.state||"",country:p.country||"",postalCode:p.postalCode||"",industry:p.industry||"",companySize:p.companySize||"",budget:p.budget||void 0,timeline:p.timeline||"",description:p.description||""}):E({source:"OTHER",status:"NEW",priority:"MEDIUM"})},[p,N,E]);let b=async e=>{y(!0);try{let s="edit"===N?"/api/leads/".concat(null==p?void 0:p.id):"/api/leads",l=await fetch(s,{method:"edit"===N?"PUT":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!l.ok){let e=await l.json();throw Error(e.error||"Failed to save lead")}u.toast.success("Lead ".concat("edit"===N?"updated":"created"," successfully")),d(),i()}catch(e){u.toast.error(e instanceof Error?e.message:"An error occurred")}finally{y(!1)}},S=()=>{E(),i()};return(0,l.jsx)(x.Vq,{open:s,onOpenChange:S,children:(0,l.jsxs)(x.cZ,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,l.jsx)(x.fK,{children:(0,l.jsx)(x.$N,{children:"edit"===N?"Edit Lead":"Create New Lead"})}),(0,l.jsxs)("form",{onSubmit:f(b),className:"space-y-6",children:[(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h3",{className:"text-lg font-medium",children:"Personal Information"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)(c._,{htmlFor:"firstName",children:"First Name *"}),(0,l.jsx)(o.I,{id:"firstName",...g("firstName"),className:_.firstName?"border-red-500":""}),_.firstName&&(0,l.jsx)("p",{className:"text-red-500 text-sm mt-1",children:_.firstName.message})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(c._,{htmlFor:"lastName",children:"Last Name *"}),(0,l.jsx)(o.I,{id:"lastName",...g("lastName"),className:_.lastName?"border-red-500":""}),_.lastName&&(0,l.jsx)("p",{className:"text-red-500 text-sm mt-1",children:_.lastName.message})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(c._,{htmlFor:"email",children:"Email *"}),(0,l.jsx)(o.I,{id:"email",type:"email",...g("email"),className:_.email?"border-red-500":""}),_.email&&(0,l.jsx)("p",{className:"text-red-500 text-sm mt-1",children:_.email.message})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(c._,{htmlFor:"phone",children:"Phone"}),(0,l.jsx)(o.I,{id:"phone",...g("phone")})]})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h3",{className:"text-lg font-medium",children:"Company Information"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)(c._,{htmlFor:"companyName",children:"Company Name"}),(0,l.jsx)(o.I,{id:"companyName",...g("companyName")})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(c._,{htmlFor:"title",children:"Job Title"}),(0,l.jsx)(o.I,{id:"title",...g("title")})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(c._,{htmlFor:"website",children:"Website"}),(0,l.jsx)(o.I,{id:"website",...g("website"),placeholder:"https://example.com"}),_.website&&(0,l.jsx)("p",{className:"text-red-500 text-sm mt-1",children:_.website.message})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(c._,{htmlFor:"industry",children:"Industry"}),(0,l.jsx)(o.I,{id:"industry",...g("industry")})]})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h3",{className:"text-lg font-medium",children:"Lead Details"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)(c._,{htmlFor:"source",children:"Source *"}),(0,l.jsxs)(h.Ph,{onValueChange:e=>I("source",e),children:[(0,l.jsx)(h.i4,{children:(0,l.jsx)(h.ki,{placeholder:"Select source"})}),(0,l.jsxs)(h.Bw,{children:[(0,l.jsx)(h.Ql,{value:"WEBSITE",children:"Website"}),(0,l.jsx)(h.Ql,{value:"REFERRAL",children:"Referral"}),(0,l.jsx)(h.Ql,{value:"SOCIAL_MEDIA",children:"Social Media"}),(0,l.jsx)(h.Ql,{value:"EMAIL_CAMPAIGN",children:"Email Campaign"}),(0,l.jsx)(h.Ql,{value:"COLD_CALL",children:"Cold Call"}),(0,l.jsx)(h.Ql,{value:"TRADE_SHOW",children:"Trade Show"}),(0,l.jsx)(h.Ql,{value:"PARTNER",children:"Partner"}),(0,l.jsx)(h.Ql,{value:"OTHER",children:"Other"})]})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(c._,{htmlFor:"status",children:"Status *"}),(0,l.jsxs)(h.Ph,{onValueChange:e=>I("status",e),children:[(0,l.jsx)(h.i4,{children:(0,l.jsx)(h.ki,{placeholder:"Select status"})}),(0,l.jsxs)(h.Bw,{children:[(0,l.jsx)(h.Ql,{value:"NEW",children:"New"}),(0,l.jsx)(h.Ql,{value:"CONTACTED",children:"Contacted"}),(0,l.jsx)(h.Ql,{value:"QUALIFIED",children:"Qualified"}),(0,l.jsx)(h.Ql,{value:"PROPOSAL",children:"Proposal"}),(0,l.jsx)(h.Ql,{value:"NEGOTIATION",children:"Negotiation"}),(0,l.jsx)(h.Ql,{value:"CLOSED_WON",children:"Closed Won"}),(0,l.jsx)(h.Ql,{value:"CLOSED_LOST",children:"Closed Lost"})]})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(c._,{htmlFor:"priority",children:"Priority *"}),(0,l.jsxs)(h.Ph,{onValueChange:e=>I("priority",e),children:[(0,l.jsx)(h.i4,{children:(0,l.jsx)(h.ki,{placeholder:"Select priority"})}),(0,l.jsxs)(h.Bw,{children:[(0,l.jsx)(h.Ql,{value:"LOW",children:"Low"}),(0,l.jsx)(h.Ql,{value:"MEDIUM",children:"Medium"}),(0,l.jsx)(h.Ql,{value:"HIGH",children:"High"}),(0,l.jsx)(h.Ql,{value:"URGENT",children:"Urgent"})]})]})]})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h3",{className:"text-lg font-medium",children:"Business Information"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)(c._,{htmlFor:"companySize",children:"Company Size"}),(0,l.jsx)(o.I,{id:"companySize",...g("companySize"),placeholder:"e.g., 1-10, 11-50, 51-200"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(c._,{htmlFor:"budget",children:"Budget ($)"}),(0,l.jsx)(o.I,{id:"budget",type:"number",min:"0",step:"0.01",...g("budget",{valueAsNumber:!0}),placeholder:"0.00"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(c._,{htmlFor:"timeline",children:"Timeline"}),(0,l.jsx)(o.I,{id:"timeline",...g("timeline"),placeholder:"e.g., Q1 2024, 3-6 months"})]})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h3",{className:"text-lg font-medium",children:"Address Information"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)(c._,{htmlFor:"address",children:"Address"}),(0,l.jsx)(o.I,{id:"address",...g("address"),placeholder:"Street address"})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)(c._,{htmlFor:"city",children:"City"}),(0,l.jsx)(o.I,{id:"city",...g("city")})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(c._,{htmlFor:"state",children:"State/Province"}),(0,l.jsx)(o.I,{id:"state",...g("state")})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(c._,{htmlFor:"postalCode",children:"Postal Code"}),(0,l.jsx)(o.I,{id:"postalCode",...g("postalCode")})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(c._,{htmlFor:"country",children:"Country"}),(0,l.jsx)(o.I,{id:"country",...g("country")})]})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(c._,{htmlFor:"description",children:"Description"}),(0,l.jsx)(m.g,{id:"description",...g("description"),rows:3,placeholder:"Additional notes about this lead..."})]}),(0,l.jsxs)("div",{className:"flex justify-end space-x-4 pt-4",children:[(0,l.jsx)(n.z,{type:"button",variant:"outline",onClick:S,children:"Cancel"}),(0,l.jsx)(n.z,{type:"submit",disabled:v,children:v?"Saving...":"edit"===N?"Update Lead":"Create Lead"})]})]})]})})}},23444:function(e,s,i){i.d(s,{g:function(){return r}});var l=i(57437),t=i(2265),a=i(1657);let r=t.forwardRef((e,s)=>{let{className:i,...t}=e;return(0,l.jsx)("textarea",{className:(0,a.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",i),ref:s,...t})});r.displayName="Textarea"}}]);