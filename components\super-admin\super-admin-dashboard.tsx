'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Building2,
  Users,
  Activity,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Shield,
  Server,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  XCircle,
  BarChart3,
  PieChart,
  Globe,
  Zap
} from 'lucide-react'
import { toast } from 'react-hot-toast'

interface SuperAdminAnalytics {
  summary: {
    totalCompanies: number
    totalUsers: number
    totalQuotations: number
    totalInvoices: number
    totalContracts: number
    totalItems: number
    activeCompanies: number
    activeUsers: number
    newCompanies: number
    newUsers: number
    totalActivities: number
    totalRevenue: number
    avgRevenuePerCompany: number
    avgDocsPerCompany: number
  }
  companies: {
    total: number
    active: number
    new: number
    bySize: Array<{ size: string; count: number }>
    byIndustry: Array<{ industry: string; count: number }>
    topPerforming: Array<{
      id: string
      name: string
      industry: string
      size: string
      userCount: number
      quotationCount: number
      invoiceCount: number
      contractCount: number
      totalRevenue: number
    }>
  }
  users: {
    total: number
    active: number
    new: number
    suspended: number
    byRole: Array<{ role: string; count: number }>
    avgLoginCount: number
    totalLogins: number
  }
  activities: {
    total: number
    byType: Array<{ type: string; count: number }>
    dailyTrend: Array<{ date: string; count: number }>
    recent: Array<{
      id: string
      type: string
      title: string
      description: string
      company: string
      createdBy: string
      createdAt: string
    }>
  }
  performance: {
    newQuotations: number
    newInvoices: number
    newContracts: number
    avgDocsPerCompany: number
  }
  security: {
    suspendedUsers: number
    suspendedCompanies: number
    securityActivities: number
    errorCount: number
  }
  growth: {
    companies: Array<{ month: string; count: number }>
    users: Array<{ month: string; count: number }>
  }
  systemHealth: {
    totalCompanies: number
    totalUsers: number
    totalActivities: number
    errorRate: number
    uptime: number
  }
  featureAdoption: {
    quotations: { companies: number; adoptionRate: number }
    invoices: { companies: number; adoptionRate: number }
    contracts: { companies: number; adoptionRate: number }
    items: { companies: number; adoptionRate: number }
  }
  period: number
}

export function SuperAdminDashboard() {
  const [analytics, setAnalytics] = useState<SuperAdminAnalytics | null>(null)
  const [loading, setLoading] = useState(true)
  const [period, setPeriod] = useState('30')

  const fetchAnalytics = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/super-admin/analytics?period=${period}`)
      if (!response.ok) {
        throw new Error('Failed to fetch analytics')
      }

      const data = await response.json()
      setAnalytics(data)
    } catch (error) {
      toast.error('Failed to load super admin analytics')
      console.error('Error fetching analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchAnalytics()
  }, [period])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num)
  }

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'suspended':
        return 'bg-red-100 text-red-800'
      case 'inactive':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-blue-100 text-blue-800'
    }
  }

  const getHealthScore = () => {
    if (!analytics) return 0
    const { systemHealth } = analytics
    
    // Calculate health score based on various factors
    const uptimeScore = systemHealth.uptime
    const errorScore = Math.max(0, 100 - systemHealth.errorRate * 10)
    const activityScore = systemHealth.totalActivities > 1000 ? 100 : (systemHealth.totalActivities / 1000) * 100
    
    return Math.round((uptimeScore + errorScore + activityScore) / 3)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!analytics) {
    return (
      <div className="text-center py-8 text-gray-500">
        <BarChart3 className="h-12 w-12 mx-auto mb-4 text-gray-300" />
        <p>Failed to load analytics data</p>
      </div>
    )
  }

  const healthScore = getHealthScore()

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Super Admin Dashboard</h1>
          <p className="text-gray-500">System overview and management</p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={period} onValueChange={setPeriod}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">Last 7 days</SelectItem>
              <SelectItem value="30">Last 30 days</SelectItem>
              <SelectItem value="90">Last 90 days</SelectItem>
              <SelectItem value="365">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={fetchAnalytics} size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* System Health Alert */}
      <Card className={`border-l-4 ${healthScore >= 80 ? 'border-l-green-500' : healthScore >= 60 ? 'border-l-yellow-500' : 'border-l-red-500'}`}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className={`p-2 rounded-full ${healthScore >= 80 ? 'bg-green-100' : healthScore >= 60 ? 'bg-yellow-100' : 'bg-red-100'}`}>
                {healthScore >= 80 ? (
                  <CheckCircle className="h-5 w-5 text-green-600" />
                ) : healthScore >= 60 ? (
                  <AlertTriangle className="h-5 w-5 text-yellow-600" />
                ) : (
                  <XCircle className="h-5 w-5 text-red-600" />
                )}
              </div>
              <div>
                <h3 className="font-semibold">System Health Score</h3>
                <p className="text-sm text-gray-500">
                  {healthScore >= 80 ? 'All systems operational' : 
                   healthScore >= 60 ? 'Some issues detected' : 'Critical issues require attention'}
                </p>
              </div>
            </div>
            <div className="text-right">
              <div className={`text-2xl font-bold ${healthScore >= 80 ? 'text-green-600' : healthScore >= 60 ? 'text-yellow-600' : 'text-red-600'}`}>
                {healthScore}%
              </div>
              <div className="text-sm text-gray-500">
                Uptime: {analytics.systemHealth.uptime}%
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-blue-100 rounded-full">
                <Building2 className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Total Companies</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.summary.totalCompanies}</p>
                <p className="text-xs text-green-600">+{analytics.summary.newCompanies} this period</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-green-100 rounded-full">
                <Users className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Total Users</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.summary.totalUsers}</p>
                <p className="text-xs text-green-600">+{analytics.summary.newUsers} this period</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-purple-100 rounded-full">
                <DollarSign className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Total Revenue</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(analytics.summary.totalRevenue)}</p>
                <p className="text-xs text-gray-500">Avg: {formatCurrency(analytics.summary.avgRevenuePerCompany)}/company</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-orange-100 rounded-full">
                <Activity className="h-6 w-6 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">System Activity</p>
                <p className="text-2xl font-bold text-gray-900">{formatNumber(analytics.summary.totalActivities)}</p>
                <p className="text-xs text-gray-500">Last {period} days</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Company Analytics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Building2 className="h-5 w-5 mr-2" />
              Company Distribution
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">By Size</h4>
                <div className="space-y-2">
                  {analytics.companies.bySize.map((item) => (
                    <div key={item.size} className="flex items-center justify-between">
                      <span className="text-sm capitalize">{item.size.toLowerCase()}</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-20 bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full" 
                            style={{ width: `${(item.count / analytics.companies.total) * 100}%` }}
                          ></div>
                        </div>
                        <span className="text-sm font-medium w-8">{item.count}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              
              <div>
                <h4 className="font-medium mb-2">Top Industries</h4>
                <div className="space-y-2">
                  {analytics.companies.byIndustry.slice(0, 5).map((item) => (
                    <div key={item.industry} className="flex items-center justify-between">
                      <span className="text-sm">{item.industry || 'Other'}</span>
                      <Badge variant="outline">{item.count}</Badge>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              User Analytics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{analytics.users.active}</div>
                  <p className="text-sm text-gray-500">Active Users</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{analytics.users.suspended}</div>
                  <p className="text-sm text-gray-500">Suspended</p>
                </div>
              </div>
              
              <div>
                <h4 className="font-medium mb-2">By Role</h4>
                <div className="space-y-2">
                  {analytics.users.byRole.map((item) => (
                    <div key={item.role} className="flex items-center justify-between">
                      <span className="text-sm capitalize">{item.role.toLowerCase().replace('_', ' ')}</span>
                      <Badge variant="outline">{item.count}</Badge>
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="pt-2 border-t">
                <div className="flex justify-between text-sm">
                  <span>Avg Logins/User:</span>
                  <span className="font-medium">{analytics.users.avgLoginCount.toFixed(1)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Total Logins:</span>
                  <span className="font-medium">{formatNumber(analytics.users.totalLogins)}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Feature Adoption */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Zap className="h-5 w-5 mr-2" />
            Feature Adoption Rates
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="mb-2">
                <Progress value={analytics.featureAdoption.quotations.adoptionRate} className="h-2" />
              </div>
              <div className="text-lg font-bold text-blue-600">
                {formatPercentage(analytics.featureAdoption.quotations.adoptionRate)}
              </div>
              <p className="text-sm text-gray-500">Quotations</p>
              <p className="text-xs text-gray-400">{analytics.featureAdoption.quotations.companies} companies</p>
            </div>
            
            <div className="text-center">
              <div className="mb-2">
                <Progress value={analytics.featureAdoption.invoices.adoptionRate} className="h-2" />
              </div>
              <div className="text-lg font-bold text-green-600">
                {formatPercentage(analytics.featureAdoption.invoices.adoptionRate)}
              </div>
              <p className="text-sm text-gray-500">Invoices</p>
              <p className="text-xs text-gray-400">{analytics.featureAdoption.invoices.companies} companies</p>
            </div>
            
            <div className="text-center">
              <div className="mb-2">
                <Progress value={analytics.featureAdoption.contracts.adoptionRate} className="h-2" />
              </div>
              <div className="text-lg font-bold text-purple-600">
                {formatPercentage(analytics.featureAdoption.contracts.adoptionRate)}
              </div>
              <p className="text-sm text-gray-500">Contracts</p>
              <p className="text-xs text-gray-400">{analytics.featureAdoption.contracts.companies} companies</p>
            </div>
            
            <div className="text-center">
              <div className="mb-2">
                <Progress value={analytics.featureAdoption.items.adoptionRate} className="h-2" />
              </div>
              <div className="text-lg font-bold text-orange-600">
                {formatPercentage(analytics.featureAdoption.items.adoptionRate)}
              </div>
              <p className="text-sm text-gray-500">Items</p>
              <p className="text-xs text-gray-400">{analytics.featureAdoption.items.companies} companies</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Top Performing Companies */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <TrendingUp className="h-5 w-5 mr-2" />
            Top Performing Companies
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {analytics.companies.topPerforming.slice(0, 5).map((company, index) => (
              <div key={company.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-xs font-semibold text-blue-600">#{index + 1}</span>
                  </div>
                  <div>
                    <p className="font-medium">{company.name}</p>
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline" className="text-xs">{company.industry}</Badge>
                      <Badge variant="outline" className="text-xs">{company.size}</Badge>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-green-600">
                    {formatCurrency(company.totalRevenue)}
                  </p>
                  <p className="text-sm text-gray-500">
                    {company.userCount} users • {company.quotationCount + company.invoiceCount + company.contractCount} docs
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent System Activities */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Activity className="h-5 w-5 mr-2" />
            Recent System Activities
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {analytics.activities.recent.slice(0, 10).map((activity) => (
              <div key={activity.id} className="flex items-center justify-between p-3 border-l-2 border-blue-200 bg-blue-50">
                <div>
                  <p className="font-medium">{activity.title}</p>
                  <p className="text-sm text-gray-600">{activity.description}</p>
                  <div className="flex items-center space-x-2 mt-1">
                    <Badge variant="outline" className="text-xs">{activity.type}</Badge>
                    {activity.company && (
                      <span className="text-xs text-gray-500">{activity.company}</span>
                    )}
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-500">
                    {new Date(activity.createdAt).toLocaleDateString()}
                  </p>
                  <p className="text-xs text-gray-400">{activity.createdBy}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
