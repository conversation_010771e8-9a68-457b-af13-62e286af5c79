import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '30' // days
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - parseInt(period))

    // Get comprehensive task analytics
    const [
      totalTasks,
      tasksByStatus,
      tasksByPriority,
      tasksByAssignee,
      overdueTasks,
      completedTasks,
      tasksByType,
      tasksByCategory,
      taskCompletionTrend,
      averageCompletionTime,
      productivityMetrics
    ] = await Promise.all([
      // Total tasks
      prisma.task.count({
        where: {
          companyId: session.user.companyId,
          createdAt: { gte: startDate }
        }
      }),

      // Tasks by status
      prisma.task.groupBy({
        by: ['status'],
        where: {
          companyId: session.user.companyId,
          createdAt: { gte: startDate }
        },
        _count: {
          id: true
        }
      }),

      // Tasks by priority
      prisma.task.groupBy({
        by: ['priority'],
        where: {
          companyId: session.user.companyId,
          createdAt: { gte: startDate }
        },
        _count: {
          id: true
        }
      }),

      // Tasks by assignee
      prisma.task.groupBy({
        by: ['assignedToId'],
        where: {
          companyId: session.user.companyId,
          createdAt: { gte: startDate }
        },
        _count: {
          id: true
        }
      }),

      // Overdue tasks
      prisma.task.count({
        where: {
          companyId: session.user.companyId,
          status: { not: 'DONE' },
          dueDate: { lt: new Date() }
        }
      }),

      // Completed tasks in period
      prisma.task.count({
        where: {
          companyId: session.user.companyId,
          status: 'DONE',
          completedAt: { gte: startDate }
        }
      }),

      // Tasks by type
      prisma.task.groupBy({
        by: ['type'],
        where: {
          companyId: session.user.companyId,
          createdAt: { gte: startDate }
        },
        _count: {
          id: true
        }
      }),

      // Tasks by category
      prisma.task.groupBy({
        by: ['category'],
        where: {
          companyId: session.user.companyId,
          createdAt: { gte: startDate },
          category: { not: null }
        },
        _count: {
          id: true
        }
      }),

      // Task completion trend (last 7 days)
      prisma.$queryRaw`
        SELECT 
          DATE(completed_at) as date,
          COUNT(*)::int as completed_count
        FROM "Task" 
        WHERE "companyId" = ${session.user.companyId}
          AND status = 'DONE'
          AND completed_at >= ${new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)}
        GROUP BY DATE(completed_at)
        ORDER BY date DESC
        LIMIT 7
      `,

      // Average completion time
      prisma.task.findMany({
        where: {
          companyId: session.user.companyId,
          status: 'DONE',
          completedAt: { gte: startDate },
          createdAt: { not: null },
          completedAt: { not: null }
        },
        select: {
          createdAt: true,
          completedAt: true
        }
      }),

      // Productivity metrics
      Promise.all([
        // Tasks created today
        prisma.task.count({
          where: {
            companyId: session.user.companyId,
            createdAt: {
              gte: new Date(new Date().setHours(0, 0, 0, 0))
            }
          }
        }),
        // Tasks completed today
        prisma.task.count({
          where: {
            companyId: session.user.companyId,
            status: 'DONE',
            completedAt: {
              gte: new Date(new Date().setHours(0, 0, 0, 0))
            }
          }
        }),
        // Tasks due today
        prisma.task.count({
          where: {
            companyId: session.user.companyId,
            status: { not: 'DONE' },
            dueDate: {
              gte: new Date(new Date().setHours(0, 0, 0, 0)),
              lt: new Date(new Date().setHours(23, 59, 59, 999))
            }
          }
        })
      ])
    ])

    // Get user details for assignee data
    const assigneeIds = tasksByAssignee.map(t => t.assignedToId).filter(Boolean)
    const users = await prisma.user.findMany({
      where: {
        id: { in: assigneeIds },
        companyId: session.user.companyId
      },
      select: {
        id: true,
        name: true,
        email: true
      }
    })

    // Process assignee data
    const tasksByAssigneeWithDetails = tasksByAssignee.map(item => {
      const user = users.find(u => u.id === item.assignedToId)
      return {
        assignee: user || { id: item.assignedToId, name: 'Unknown', email: '' },
        count: item._count.id
      }
    })

    // Calculate average completion time
    let avgCompletionTime = 0
    if (averageCompletionTime.length > 0) {
      const totalTime = averageCompletionTime.reduce((sum, task) => {
        if (task.createdAt && task.completedAt) {
          const timeDiff = new Date(task.completedAt).getTime() - new Date(task.createdAt).getTime()
          return sum + timeDiff
        }
        return sum
      }, 0)
      avgCompletionTime = totalTime / averageCompletionTime.length / (1000 * 60 * 60 * 24) // Convert to days
    }

    // Calculate completion rate
    const completionRate = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0

    // Get upcoming tasks (next 7 days)
    const upcomingTasks = await prisma.task.findMany({
      where: {
        companyId: session.user.companyId,
        status: { not: 'DONE' },
        dueDate: {
          gte: new Date(),
          lte: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
        }
      },
      include: {
        assignedTo: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        lead: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            companyName: true
          }
        },
        customer: {
          select: {
            id: true,
            name: true,
            company: true
          }
        }
      },
      orderBy: { dueDate: 'asc' },
      take: 10
    })

    // Get high priority tasks
    const highPriorityTasks = await prisma.task.findMany({
      where: {
        companyId: session.user.companyId,
        status: { not: 'DONE' },
        priority: { in: ['HIGH', 'URGENT', 'CRITICAL'] }
      },
      include: {
        assignedTo: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: [
        { priority: 'desc' },
        { dueDate: 'asc' }
      ],
      take: 10
    })

    const [tasksCreatedToday, tasksCompletedToday, tasksDueToday] = productivityMetrics

    return NextResponse.json({
      summary: {
        totalTasks,
        completedTasks,
        overdueTasks,
        completionRate: Math.round(completionRate * 100) / 100,
        averageCompletionTime: Math.round(avgCompletionTime * 100) / 100,
        tasksCreatedToday,
        tasksCompletedToday,
        tasksDueToday
      },
      tasksByStatus: tasksByStatus.map(item => ({
        status: item.status,
        count: item._count.id
      })),
      tasksByPriority: tasksByPriority.map(item => ({
        priority: item.priority,
        count: item._count.id
      })),
      tasksByAssignee: tasksByAssigneeWithDetails,
      tasksByType: tasksByType.map(item => ({
        type: item.type,
        count: item._count.id
      })),
      tasksByCategory: tasksByCategory.map(item => ({
        category: item.category,
        count: item._count.id
      })),
      taskCompletionTrend,
      upcomingTasks,
      highPriorityTasks,
      period: parseInt(period)
    })

  } catch (error) {
    console.error('Error fetching task analytics:', error)
    return NextResponse.json(
      { error: 'Failed to fetch task analytics' },
      { status: 500 }
    )
  }
}
