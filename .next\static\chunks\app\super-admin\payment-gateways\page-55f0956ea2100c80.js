(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6715],{50240:function(e,t,r){Promise.resolve().then(r.bind(r,53208))},53208:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return S}});var s=r(57437),a=r(2265),n=r(82749),i=r(24033),l=r(27815),o=r(85754),c=r(45179),d=r(49842),u=r(86443),f=r(40110),p=r(31478),m=r(13008),h=r(82104),x=r(72894),g=r(94286),b=r(64280),v=r(92295),y=r(71738),j=r(77216),N=r(99670),w=r(49036),k=r(5925);function S(){var e;let{data:t,status:r}=(0,n.useSession)(),[S,C]=(0,a.useState)([]),[_,P]=(0,a.useState)({stripe:{enabled:!1,live:!1,publishableKey:"",secretKey:"",webhookSecret:"",supportedMethods:["card","apple_pay","google_pay"]},paypal:{enabled:!1,live:!1,clientId:"",clientSecret:"",webhookId:"",supportedMethods:["paypal","venmo"]},razorpay:{enabled:!1,live:!1,keyId:"",keySecret:"",webhookSecret:"",supportedMethods:["card","netbanking","upi","wallet"]},square:{enabled:!1,live:!1,applicationId:"",accessToken:"",webhookSignatureKey:"",supportedMethods:["card","apple_pay","google_pay"]}}),[Z,R]=(0,a.useState)(!0),[z,K]=(0,a.useState)(!1),[E,I]=(0,a.useState)({});if("loading"===r)return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})});"unauthenticated"===r&&(0,i.redirect)("/auth/signin"),(null==t?void 0:null===(e=t.user)||void 0===e?void 0:e.role)!=="SUPER_ADMIN"&&(0,i.redirect)("/dashboard");let F=async()=>{try{R(!0);let e=await fetch("/api/super-admin/payment-gateways"),t=await e.json();t.success&&(C(t.gateways),P({..._,...t.config}))}catch(e){console.error("Error fetching payment gateways:",e),k.toast.error("Failed to load payment gateways")}finally{R(!1)}},O=async()=>{try{K(!0);let e=await fetch("/api/super-admin/payment-gateways",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(_)}),t=await e.json();t.success?(k.toast.success("Payment gateway configuration saved successfully"),F()):k.toast.error(t.error||"Failed to save configuration")}catch(e){console.error("Error saving config:",e),k.toast.error("Failed to save configuration")}finally{K(!1)}},W=async e=>{try{let t=await fetch("/api/super-admin/payment-gateways/test",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({provider:e})}),r=await t.json();r.success?k.toast.success("".concat(e," connection test successful")):k.toast.error(r.error||"".concat(e," connection test failed"))}catch(e){console.error("Error testing connection:",e),k.toast.error("Connection test failed")}};(0,a.useEffect)(()=>{F()},[]);let T=(e,t,r)=>{P(s=>({...s,[e]:{...s[e],[t]:r}}))},V=e=>{I(t=>({...t,[e]:!t[e]}))},M=e=>{switch(e){case"active":return(0,s.jsx)(m.Z,{className:"h-5 w-5 text-green-500"});case"error":return(0,s.jsx)(h.Z,{className:"h-5 w-5 text-red-500"});default:return(0,s.jsx)(x.Z,{className:"h-5 w-5 text-yellow-500"})}};return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(g.Z,{className:"h-8 w-8 text-green-600"}),(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Payment Gateways"})]}),(0,s.jsx)("p",{className:"text-gray-500 mt-1",children:"Configure payment processors and methods"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsxs)(o.z,{variant:"outline",onClick:F,disabled:Z,children:[(0,s.jsx)(b.Z,{className:"h-4 w-4 mr-2 ".concat(Z?"animate-spin":"")}),"Refresh"]}),(0,s.jsxs)(o.z,{onClick:O,disabled:z,children:[(0,s.jsx)(v.Z,{className:"h-4 w-4 mr-2 ".concat(z?"animate-spin":"")}),"Save Configuration"]})]})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:Object.entries(_).map(e=>{let[t,r]=e;return(0,s.jsx)(l.Zb,{children:(0,s.jsx)(l.aY,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-500 capitalize",children:t}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[M(r.enabled?"active":"inactive"),(0,s.jsx)("span",{className:"text-sm font-medium",children:r.enabled?"Enabled":"Disabled"})]}),r.enabled&&(0,s.jsx)(p.C,{variant:r.live?"default":"secondary",className:"mt-2",children:r.live?"Live":"Test"})]}),(0,s.jsx)("div",{className:"text-right",children:(0,s.jsx)(y.Z,{className:"h-8 w-8 text-gray-400"})})]})})},t)})}),(0,s.jsxs)(f.mQ,{defaultValue:"stripe",className:"space-y-6",children:[(0,s.jsxs)(f.dr,{className:"grid w-full grid-cols-4",children:[(0,s.jsx)(f.SP,{value:"stripe",children:"Stripe"}),(0,s.jsx)(f.SP,{value:"paypal",children:"PayPal"}),(0,s.jsx)(f.SP,{value:"razorpay",children:"Razorpay"}),(0,s.jsx)(f.SP,{value:"square",children:"Square"})]}),(0,s.jsx)(f.nU,{value:"stripe",children:(0,s.jsxs)(l.Zb,{children:[(0,s.jsxs)(l.Ol,{children:[(0,s.jsxs)(l.ll,{className:"flex items-center",children:[(0,s.jsx)(y.Z,{className:"h-5 w-5 mr-2"}),"Stripe Configuration"]}),(0,s.jsx)(l.SZ,{children:"Configure Stripe payment processing"})]}),(0,s.jsxs)(l.aY,{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(d._,{htmlFor:"stripeEnabled",className:"text-base font-medium",children:"Enable Stripe"}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Accept credit cards, Apple Pay, Google Pay, and more"})]}),(0,s.jsx)(u.r,{id:"stripeEnabled",checked:_.stripe.enabled,onCheckedChange:e=>T("stripe","enabled",e)})]}),_.stripe.enabled&&(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(d._,{htmlFor:"stripeLive",className:"text-base font-medium",children:"Live Mode"}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Use live API keys for production payments"})]}),(0,s.jsx)(u.r,{id:"stripeLive",checked:_.stripe.live,onCheckedChange:e=>T("stripe","live",e)})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(d._,{htmlFor:"stripePublishableKey",children:"Publishable Key"}),(0,s.jsx)(c.I,{id:"stripePublishableKey",value:_.stripe.publishableKey,onChange:e=>T("stripe","publishableKey",e.target.value),placeholder:_.stripe.live?"pk_live_...":"pk_test_..."})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(d._,{htmlFor:"stripeSecretKey",children:"Secret Key"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(c.I,{id:"stripeSecretKey",type:E.stripeSecret?"text":"password",value:_.stripe.secretKey,onChange:e=>T("stripe","secretKey",e.target.value),placeholder:_.stripe.live?"sk_live_...":"sk_test_..."}),(0,s.jsx)(o.z,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>V("stripeSecret"),children:E.stripeSecret?(0,s.jsx)(j.Z,{className:"h-4 w-4"}):(0,s.jsx)(N.Z,{className:"h-4 w-4"})})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(d._,{htmlFor:"stripeWebhookSecret",children:"Webhook Endpoint Secret"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(c.I,{id:"stripeWebhookSecret",type:E.stripeWebhook?"text":"password",value:_.stripe.webhookSecret,onChange:e=>T("stripe","webhookSecret",e.target.value),placeholder:"whsec_..."}),(0,s.jsx)(o.z,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>V("stripeWebhook"),children:E.stripeWebhook?(0,s.jsx)(j.Z,{className:"h-4 w-4"}):(0,s.jsx)(N.Z,{className:"h-4 w-4"})})]}),(0,s.jsxs)("p",{className:"text-xs text-gray-500",children:["Webhook URL: ",window.location.origin,"/api/webhooks/stripe"]})]}),(0,s.jsx)("div",{className:"flex items-center space-x-2",children:(0,s.jsxs)(o.z,{variant:"outline",onClick:()=>W("stripe"),disabled:!_.stripe.secretKey,children:[(0,s.jsx)(w.Z,{className:"h-4 w-4 mr-2"}),"Test Connection"]})})]})]})]})})]})]})}},31478:function(e,t,r){"use strict";r.d(t,{C:function(){return l}});var s=r(57437);r(2265);var a=r(96061),n=r(1657);let i=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600",info:"border-transparent bg-blue-500 text-white hover:bg-blue-600"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:r,...a}=e;return(0,s.jsx)("div",{className:(0,n.cn)(i({variant:r}),t),...a})}},85754:function(e,t,r){"use strict";r.d(t,{z:function(){return c}});var s=r(57437),a=r(2265),n=r(67256),i=r(96061),l=r(1657);let o=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,t)=>{let{className:r,variant:a,size:i,asChild:c=!1,...d}=e,u=c?n.g7:"button";return(0,s.jsx)(u,{className:(0,l.cn)(o({variant:a,size:i,className:r})),ref:t,...d})});c.displayName="Button"},27815:function(e,t,r){"use strict";r.d(t,{Ol:function(){return l},SZ:function(){return c},Zb:function(){return i},aY:function(){return d},eW:function(){return u},ll:function(){return o}});var s=r(57437),a=r(2265),n=r(1657);let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});i.displayName="Card";let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...a})});l.displayName="CardHeader";let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})});o.displayName="CardTitle";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",r),...a})});c.displayName="CardDescription";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",r),...a})});d.displayName="CardContent";let u=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",r),...a})});u.displayName="CardFooter"},45179:function(e,t,r){"use strict";r.d(t,{I:function(){return i}});var s=r(57437),a=r(2265),n=r(1657);let i=a.forwardRef((e,t)=>{let{className:r,type:a,...i}=e;return(0,s.jsx)("input",{type:a,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...i})});i.displayName="Input"},49842:function(e,t,r){"use strict";r.d(t,{_:function(){return c}});var s=r(57437),a=r(2265),n=r(36743),i=r(96061),l=r(1657);let o=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(n.f,{ref:t,className:(0,l.cn)(o(),r),...a})});c.displayName=n.f.displayName},86443:function(e,t,r){"use strict";r.d(t,{r:function(){return l}});var s=r(57437),a=r(2265),n=r(92376),i=r(1657);let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(n.fC,{className:(0,i.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",r),...a,ref:t,children:(0,s.jsx)(n.bU,{className:(0,i.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});l.displayName=n.fC.displayName},40110:function(e,t,r){"use strict";r.d(t,{SP:function(){return c},dr:function(){return o},mQ:function(){return l},nU:function(){return d}});var s=r(57437),a=r(2265),n=r(34522),i=r(1657);let l=n.fC,o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(n.aV,{ref:t,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",r),...a})});o.displayName=n.aV.displayName;let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(n.xz,{ref:t,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",r),...a})});c.displayName=n.xz.displayName;let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(n.VY,{ref:t,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",r),...a})});d.displayName=n.VY.displayName},1657:function(e,t,r){"use strict";r.d(t,{cn:function(){return n}});var s=r(57042),a=r(74769);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.m6)((0,s.W)(t))}}},function(e){e.O(0,[6723,9502,2749,4522,4192,2971,4938,1744],function(){return e(e.s=50240)}),_N_E=e.O()}]);