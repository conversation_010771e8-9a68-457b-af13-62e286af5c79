'use client'

import { useEffect } from 'react'
import { X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Sidebar } from './sidebar'

interface MobileSidebarProps {
  isOpen: boolean
  onClose: () => void
  user?: {
    name?: string | null
    email?: string | null
    image?: string | null
    role?: string
    company?: {
      name?: string
    }
  }
}

export function MobileSidebar({ isOpen, onClose, user }: MobileSidebarProps) {
  // Close on escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = 'unset'
    }
  }, [isOpen, onClose])

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 md:hidden">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-gray-900/80 transition-opacity"
        onClick={onClose}
      />

      {/* Sidebar */}
      <div className="fixed inset-y-0 left-0 flex w-full max-w-xs">
        <div className="relative flex w-full flex-col">
          {/* Close button */}
          <div className="absolute right-0 top-0 -mr-12 pt-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="text-white hover:text-white hover:bg-gray-600"
            >
              <X className="h-6 w-6" />
            </Button>
          </div>

          {/* Sidebar content */}
          <Sidebar user={user} collapsed={false} />
        </div>
      </div>
    </div>
  )
}
