"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/global-config/branding/route";
exports.ids = ["app/api/global-config/branding/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fglobal-config%2Fbranding%2Froute&page=%2Fapi%2Fglobal-config%2Fbranding%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fglobal-config%2Fbranding%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fglobal-config%2Fbranding%2Froute&page=%2Fapi%2Fglobal-config%2Fbranding%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fglobal-config%2Fbranding%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_proj_nextjs_saas_app_api_global_config_branding_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/global-config/branding/route.ts */ \"(rsc)/./app/api/global-config/branding/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/global-config/branding/route\",\n        pathname: \"/api/global-config/branding\",\n        filename: \"route\",\n        bundlePath: \"app/api/global-config/branding/route\"\n    },\n    resolvedPagePath: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\api\\\\global-config\\\\branding\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_proj_nextjs_saas_app_api_global_config_branding_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/global-config/branding/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fglobal-config%2Fbranding%2Froute&page=%2Fapi%2Fglobal-config%2Fbranding%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fglobal-config%2Fbranding%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/global-config/branding/route.ts":
/*!*************************************************!*\
  !*** ./app/api/global-config/branding/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\nasync function GET(request) {\n    try {\n        // Public endpoint for fetching branding configuration\n        let branding = {};\n        try {\n            const configs = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.globalConfig.findMany();\n            if (configs && configs.length > 0) {\n                // Convert array to object for easier access\n                const configObject = configs.reduce((acc, config)=>{\n                    let value = config.value;\n                    // Parse JSON values\n                    if (config.type === \"boolean\") {\n                        value = value === \"true\";\n                    } else if (config.type === \"number\") {\n                        value = parseInt(value);\n                    } else if (config.type === \"json\") {\n                        try {\n                            value = JSON.parse(value);\n                        } catch (e) {\n                            value = {};\n                        }\n                    }\n                    acc[config.key] = value;\n                    return acc;\n                }, {});\n                // Extract branding-related configuration\n                branding = {\n                    appName: configObject.appName || \"SaaS Platform\",\n                    logoUrl: configObject.logoUrl || \"\",\n                    faviconUrl: configObject.faviconUrl || \"\",\n                    primaryColor: configObject.primaryColor || \"#3b82f6\",\n                    secondaryColor: configObject.secondaryColor || \"#64748b\",\n                    accentColor: configObject.accentColor || \"#10b981\",\n                    backgroundColor: configObject.backgroundColor || \"#ffffff\",\n                    textColor: configObject.textColor || \"#1f2937\",\n                    theme: configObject.theme || \"light\",\n                    fontFamily: configObject.fontFamily || \"Inter, sans-serif\",\n                    customCss: configObject.customCss || \"\"\n                };\n            } else {\n                // Return default branding if no configuration exists\n                branding = {\n                    appName: \"SaaS Platform\",\n                    logoUrl: \"\",\n                    faviconUrl: \"\",\n                    primaryColor: \"#3b82f6\",\n                    secondaryColor: \"#64748b\",\n                    accentColor: \"#10b981\",\n                    backgroundColor: \"#ffffff\",\n                    textColor: \"#1f2937\",\n                    theme: \"light\",\n                    fontFamily: \"Inter, sans-serif\",\n                    customCss: \"\"\n                };\n            }\n        } catch (error) {\n            console.error(\"Error fetching branding configuration:\", error);\n            // Return default branding on error\n            branding = {\n                appName: \"SaaS Platform\",\n                logoUrl: \"\",\n                faviconUrl: \"\",\n                primaryColor: \"#3b82f6\",\n                secondaryColor: \"#64748b\",\n                accentColor: \"#10b981\",\n                backgroundColor: \"#ffffff\",\n                textColor: \"#1f2937\",\n                theme: \"light\",\n                fontFamily: \"Inter, sans-serif\",\n                customCss: \"\"\n            };\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            branding\n        });\n    } catch (error) {\n        console.error(\"Error in branding API:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/global-config/branding/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zYWFzLWFwcC8uL2xpYi9wcmlzbWEudHM/OTgyMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fglobal-config%2Fbranding%2Froute&page=%2Fapi%2Fglobal-config%2Fbranding%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fglobal-config%2Fbranding%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();