"use strict";(()=>{var r={};r.id=7775,r.ids=[7775],r.modules={30517:r=>{r.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},40949:(r,o,e)=>{e.r(o),e.d(o,{headerHooks:()=>d,originalPathname:()=>b,patchFetch:()=>C,requestAsyncStorage:()=>u,routeModule:()=>f,serverHooks:()=>g,staticGenerationAsyncStorage:()=>p,staticGenerationBailout:()=>m});var a={};e.r(a),e.d(a,{GET:()=>c});var t=e(95419),n=e(69108),l=e(99678),i=e(78070),s=e(9108);async function c(r){try{let r={};try{let o=await s._.globalConfig.findMany();if(o&&o.length>0){let e=o.reduce((r,o)=>{let e=o.value;if("boolean"===o.type)e="true"===e;else if("number"===o.type)e=parseInt(e);else if("json"===o.type)try{e=JSON.parse(e)}catch(r){e={}}return r[o.key]=e,r},{});r={appName:e.appName||"SaaS Platform",logoUrl:e.logoUrl||"",faviconUrl:e.faviconUrl||"",primaryColor:e.primaryColor||"#3b82f6",secondaryColor:e.secondaryColor||"#64748b",accentColor:e.accentColor||"#10b981",backgroundColor:e.backgroundColor||"#ffffff",textColor:e.textColor||"#1f2937",theme:e.theme||"light",fontFamily:e.fontFamily||"Inter, sans-serif",customCss:e.customCss||""}}else r={appName:"SaaS Platform",logoUrl:"",faviconUrl:"",primaryColor:"#3b82f6",secondaryColor:"#64748b",accentColor:"#10b981",backgroundColor:"#ffffff",textColor:"#1f2937",theme:"light",fontFamily:"Inter, sans-serif",customCss:""}}catch(o){console.error("Error fetching branding configuration:",o),r={appName:"SaaS Platform",logoUrl:"",faviconUrl:"",primaryColor:"#3b82f6",secondaryColor:"#64748b",accentColor:"#10b981",backgroundColor:"#ffffff",textColor:"#1f2937",theme:"light",fontFamily:"Inter, sans-serif",customCss:""}}return i.Z.json({success:!0,branding:r})}catch(r){return console.error("Error in branding API:",r),i.Z.json({success:!1,error:"Internal server error"},{status:500})}}let f=new t.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/global-config/branding/route",pathname:"/api/global-config/branding",filename:"route",bundlePath:"app/api/global-config/branding/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\global-config\\branding\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:u,staticGenerationAsyncStorage:p,serverHooks:g,headerHooks:d,staticGenerationBailout:m}=f,b="/api/global-config/branding/route";function C(){return(0,l.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:p})}},9108:(r,o,e)=>{e.d(o,{_:()=>t});let a=require("@prisma/client"),t=globalThis.prisma??new a.PrismaClient}};var o=require("../../../../webpack-runtime.js");o.C(r);var e=r=>o(o.s=r),a=o.X(0,[1638,6206],()=>e(40949));module.exports=a})();