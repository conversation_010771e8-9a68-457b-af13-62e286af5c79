{"/_not-found": "app/_not-found.js", "/api/accounts/analytics/route": "app/api/accounts/analytics/route.js", "/api/accounts/route": "app/api/accounts/route.js", "/api/auth/forgot-password/route": "app/api/auth/forgot-password/route.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/auth/resend-verification/route": "app/api/auth/resend-verification/route.js", "/api/auth/reset-password/route": "app/api/auth/reset-password/route.js", "/api/auth/signup/route": "app/api/auth/signup/route.js", "/api/activities/[id]/route": "app/api/activities/[id]/route.js", "/api/auth/verify-email/route": "app/api/auth/verify-email/route.js", "/api/auth/verify-reset-token/route": "app/api/auth/verify-reset-token/route.js", "/api/billing/invoices/route": "app/api/billing/invoices/route.js", "/api/billing/payment-methods/route": "app/api/billing/payment-methods/route.js", "/api/contracts/[id]/pdf/route": "app/api/contracts/[id]/pdf/route.js", "/api/cms/route": "app/api/cms/route.js", "/api/contracts/[id]/signatures/route": "app/api/contracts/[id]/signatures/route.js", "/api/contracts/[id]/route": "app/api/contracts/[id]/route.js", "/api/contracts/analytics/route": "app/api/contracts/analytics/route.js", "/api/contracts/route": "app/api/contracts/route.js", "/api/customers/[id]/route": "app/api/customers/[id]/route.js", "/api/customers/analytics/route": "app/api/customers/analytics/route.js", "/api/customers/export/route": "app/api/customers/export/route.js", "/api/customers/import/route": "app/api/customers/import/route.js", "/api/customers/segments/route": "app/api/customers/segments/route.js", "/api/customers/route": "app/api/customers/route.js", "/api/global-config/branding/route": "app/api/global-config/branding/route.js", "/api/dashboard/route": "app/api/dashboard/route.js", "/api/invoices/[id]/payments/route": "app/api/invoices/[id]/payments/route.js", "/api/invoices/[id]/route": "app/api/invoices/[id]/route.js", "/api/invoices/route": "app/api/invoices/route.js", "/api/invoices/[id]/pdf/route": "app/api/invoices/[id]/pdf/route.js", "/api/items/[id]/route": "app/api/items/[id]/route.js", "/api/items/analytics/route": "app/api/items/analytics/route.js", "/api/lead-notes/[id]/route": "app/api/lead-notes/[id]/route.js", "/api/invoices/analytics/route": "app/api/invoices/analytics/route.js", "/api/leads/[id]/notes/route": "app/api/leads/[id]/notes/route.js", "/api/leads/[id]/activities/route": "app/api/leads/[id]/activities/route.js", "/api/leads/[id]/convert/route": "app/api/leads/[id]/convert/route.js", "/api/leads/[id]/route": "app/api/leads/[id]/route.js", "/api/leads/[id]/score/route": "app/api/leads/[id]/score/route.js", "/api/leads/conversions/analytics/route": "app/api/leads/conversions/analytics/route.js", "/api/leads/filter/route": "app/api/leads/filter/route.js", "/api/leads/saved-filters/route": "app/api/leads/saved-filters/route.js", "/api/leads/route": "app/api/leads/route.js", "/api/leads/scoring/bulk/route": "app/api/leads/scoring/bulk/route.js", "/api/pricing-plans/seed/route": "app/api/pricing-plans/seed/route.js", "/api/payments/route": "app/api/payments/route.js", "/api/pricing-plans/[id]/route": "app/api/pricing-plans/[id]/route.js", "/api/quotations/[id]/route": "app/api/quotations/[id]/route.js", "/api/pricing-plans/route": "app/api/pricing-plans/route.js", "/api/quotations/[id]/pdf/route": "app/api/quotations/[id]/pdf/route.js", "/api/quotations/[id]/send/route": "app/api/quotations/[id]/send/route.js", "/api/quotations/analytics/route": "app/api/quotations/analytics/route.js", "/api/quotations/route": "app/api/quotations/route.js", "/api/settings/company/route": "app/api/settings/company/route.js", "/api/settings/analytics/route": "app/api/settings/analytics/route.js", "/api/settings/system/route": "app/api/settings/system/route.js", "/api/setup-test-user/route": "app/api/setup-test-user/route.js", "/api/subscription/portal/route": "app/api/subscription/portal/route.js", "/api/settings/user/route": "app/api/settings/user/route.js", "/api/subscription/route": "app/api/subscription/route.js", "/api/super-admin/analytics/route": "app/api/super-admin/analytics/route.js", "/api/super-admin/audit-logs/route": "app/api/super-admin/audit-logs/route.js", "/api/super-admin/cms/route": "app/api/super-admin/cms/route.js", "/api/super-admin/branding/route": "app/api/super-admin/branding/route.js", "/api/super-admin/companies/[id]/route": "app/api/super-admin/companies/[id]/route.js", "/api/super-admin/companies/route": "app/api/super-admin/companies/route.js", "/api/super-admin/payment-gateways/route": "app/api/super-admin/payment-gateways/route.js", "/api/super-admin/reports/route": "app/api/super-admin/reports/route.js", "/api/super-admin/payment-methods/route": "app/api/super-admin/payment-methods/route.js", "/api/super-admin/global-config/route": "app/api/super-admin/global-config/route.js", "/api/super-admin/settings/route": "app/api/super-admin/settings/route.js", "/api/super-admin/subscriptions/route": "app/api/super-admin/subscriptions/route.js", "/api/super-admin/users/[id]/route": "app/api/super-admin/users/[id]/route.js", "/api/super-admin/system-health/route": "app/api/super-admin/system-health/route.js", "/api/super-admin/users/route": "app/api/super-admin/users/route.js", "/api/tasks/[id]/route": "app/api/tasks/[id]/route.js", "/api/tasks/analytics/route": "app/api/tasks/analytics/route.js", "/api/tasks/route": "app/api/tasks/route.js", "/api/usage/alerts/route": "app/api/usage/alerts/route.js", "/api/test-auth/route": "app/api/test-auth/route.js", "/api/webhooks/stripe/route": "app/api/webhooks/stripe/route.js", "/api/usage/route": "app/api/usage/route.js", "/auth/forgot-password/page": "app/auth/forgot-password/page.js", "/auth/reset-password/page": "app/auth/reset-password/page.js", "/auth/signin/page": "app/auth/signin/page.js", "/auth/signup/page": "app/auth/signup/page.js", "/auth/verify-email/page": "app/auth/verify-email/page.js", "/billing/page": "app/billing/page.js", "/page": "app/page.js", "/subscription/page": "app/subscription/page.js", "/pricing/page": "app/pricing/page.js", "/subscription/checkout/page": "app/subscription/checkout/page.js", "/test-styles/page": "app/test-styles/page.js", "/api/items/route": "app/api/items/route.js", "/dashboard/contracts/[id]/page": "app/dashboard/contracts/[id]/page.js", "/dashboard/contracts/page": "app/dashboard/contracts/page.js", "/dashboard/customers/page": "app/dashboard/customers/page.js", "/dashboard/customers/[id]/page": "app/dashboard/customers/[id]/page.js", "/dashboard/invoices/[id]/page": "app/dashboard/invoices/[id]/page.js", "/dashboard/items/page": "app/dashboard/items/page.js", "/dashboard/invoices/page": "app/dashboard/invoices/page.js", "/dashboard/leads/conversions/page": "app/dashboard/leads/conversions/page.js", "/dashboard/leads/[id]/page": "app/dashboard/leads/[id]/page.js", "/dashboard/leads/scoring/page": "app/dashboard/leads/scoring/page.js", "/dashboard/leads/new/page": "app/dashboard/leads/new/page.js", "/dashboard/leads/page": "app/dashboard/leads/page.js", "/dashboard/leads/pipeline/page": "app/dashboard/leads/pipeline/page.js", "/dashboard/page": "app/dashboard/page.js", "/dashboard/payments/page": "app/dashboard/payments/page.js", "/dashboard/quotations/page": "app/dashboard/quotations/page.js", "/super-admin/alerts/page": "app/super-admin/alerts/page.js", "/dashboard/payments/new/page": "app/dashboard/payments/new/page.js", "/dashboard/quotations/[id]/page": "app/dashboard/quotations/[id]/page.js", "/dashboard/settings/page": "app/dashboard/settings/page.js", "/super-admin/audit-logs/page": "app/super-admin/audit-logs/page.js", "/super-admin/cms/page": "app/super-admin/cms/page.js", "/super-admin/branding/page": "app/super-admin/branding/page.js", "/super-admin/page": "app/super-admin/page.js", "/super-admin/companies/page": "app/super-admin/companies/page.js", "/super-admin/database/page": "app/super-admin/database/page.js", "/super-admin/global-config/page": "app/super-admin/global-config/page.js", "/dashboard/tasks/[id]/page": "app/dashboard/tasks/[id]/page.js", "/dashboard/tasks/page": "app/dashboard/tasks/page.js", "/super-admin/payment-methods/page": "app/super-admin/payment-methods/page.js", "/super-admin/payment-gateways/page": "app/super-admin/payment-gateways/page.js", "/super-admin/pricing-plans/page": "app/super-admin/pricing-plans/page.js", "/super-admin/performance/page": "app/super-admin/performance/page.js", "/super-admin/settings/page": "app/super-admin/settings/page.js", "/super-admin/security/page": "app/super-admin/security/page.js", "/super-admin/system-health/page": "app/super-admin/system-health/page.js", "/super-admin/subscriptions/page": "app/super-admin/subscriptions/page.js", "/super-admin/reports/page": "app/super-admin/reports/page.js", "/super-admin/users/page": "app/super-admin/users/page.js"}