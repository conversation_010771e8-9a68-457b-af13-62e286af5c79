{"/api/global-config/branding/route": "app/api/global-config/branding/route.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/dashboard/route": "app/api/dashboard/route.js", "/api/contracts/route": "app/api/contracts/route.js", "/api/quotations/route": "app/api/quotations/route.js", "/api/customers/route": "app/api/customers/route.js", "/api/invoices/route": "app/api/invoices/route.js", "/dashboard/customers/page": "app/dashboard/customers/page.js", "/dashboard/quotations/page": "app/dashboard/quotations/page.js", "/dashboard/invoices/page": "app/dashboard/invoices/page.js", "/dashboard/contracts/page": "app/dashboard/contracts/page.js", "/dashboard/quotations/[id]/page": "app/dashboard/quotations/[id]/page.js", "/dashboard/page": "app/dashboard/page.js"}