import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Validation schema for creating tasks
const createTaskSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  status: z.enum(['TODO', 'IN_PROGRESS', 'REVIEW', 'DONE', 'CANCELLED']).default('TODO'),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT', 'CRITICAL']).default('MEDIUM'),
  dueDate: z.string().optional(),
  startDate: z.string().optional(),
  assignedToId: z.string().optional(),
  leadId: z.string().optional(),
  customerId: z.string().optional(),
  quotationId: z.string().optional(),
  invoiceId: z.string().optional(),
  contractId: z.string().optional(),
  tags: z.array(z.string()).default([]),
  customFields: z.record(z.any()).default({}),
  type: z.string().default('GENERAL'), // GENERAL, ONBOARDING, FOLLOW_UP, REVIEW, etc.
  category: z.string().optional(),
  estimatedHours: z.number().optional(),
  actualHours: z.number().optional()
})

// GET /api/tasks - Get all tasks with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    
    // Filtering parameters
    const status = searchParams.get('status')
    const priority = searchParams.get('priority')
    const assignedToId = searchParams.get('assignedToId')
    const leadId = searchParams.get('leadId')
    const customerId = searchParams.get('customerId')
    const dueAfter = searchParams.get('dueAfter')
    const dueBefore = searchParams.get('dueBefore')
    const search = searchParams.get('search')
    const type = searchParams.get('type')
    const category = searchParams.get('category')
    
    // Pagination parameters
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    // Build where clause
    const where: any = {
      companyId: session.user.companyId
    }

    if (status) {
      where.status = status
    }

    if (priority) {
      where.priority = priority
    }

    if (assignedToId) {
      where.assignedToId = assignedToId
    }

    if (leadId) {
      where.leadId = leadId
    }

    if (customerId) {
      where.customerId = customerId
    }

    if (type) {
      where.type = type
    }

    if (category) {
      where.category = category
    }

    if (dueAfter || dueBefore) {
      where.dueDate = {}
      if (dueAfter) where.dueDate.gte = new Date(dueAfter)
      if (dueBefore) where.dueDate.lte = new Date(dueBefore)
    }

    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ]
    }

    // Get tasks with pagination
    const [tasks, total] = await Promise.all([
      prisma.task.findMany({
        where,
        include: {
          assignedTo: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          createdBy: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          lead: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              companyName: true
            }
          },
          customer: {
            select: {
              id: true,
              name: true,
              email: true,
              company: true
            }
          },
          quotation: {
            select: {
              id: true,
              title: true,
              total: true
            }
          },
          invoice: {
            select: {
              id: true,
              invoiceNumber: true,
              total: true
            }
          },
          contract: {
            select: {
              id: true,
              title: true,
              status: true
            }
          }
        },
        orderBy: {
          [sortBy]: sortOrder
        },
        skip: (page - 1) * limit,
        take: limit
      }),
      prisma.task.count({ where })
    ])

    // Get task statistics
    const stats = await prisma.task.groupBy({
      by: ['status'],
      where: {
        companyId: session.user.companyId
      },
      _count: {
        id: true
      }
    })

    const taskStats = {
      total,
      byStatus: stats.reduce((acc, stat) => {
        acc[stat.status] = stat._count.id
        return acc
      }, {} as Record<string, number>)
    }

    return NextResponse.json({
      tasks,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      stats: taskStats
    })

  } catch (error) {
    console.error('Error fetching tasks:', error)
    return NextResponse.json(
      { error: 'Failed to fetch tasks' },
      { status: 500 }
    )
  }
}

// POST /api/tasks - Create a new task
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = createTaskSchema.parse(body)

    // Validate related entities exist and belong to company
    if (validatedData.assignedToId) {
      const assignedUser = await prisma.user.findFirst({
        where: {
          id: validatedData.assignedToId,
          companyId: session.user.companyId
        }
      })
      if (!assignedUser) {
        return NextResponse.json(
          { error: 'Assigned user not found or not in your company' },
          { status: 400 }
        )
      }
    }

    if (validatedData.leadId) {
      const lead = await prisma.lead.findFirst({
        where: {
          id: validatedData.leadId,
          companyId: session.user.companyId
        }
      })
      if (!lead) {
        return NextResponse.json(
          { error: 'Lead not found or not in your company' },
          { status: 400 }
        )
      }
    }

    if (validatedData.customerId) {
      const customer = await prisma.customer.findFirst({
        where: {
          id: validatedData.customerId,
          companyId: session.user.companyId
        }
      })
      if (!customer) {
        return NextResponse.json(
          { error: 'Customer not found or not in your company' },
          { status: 400 }
        )
      }
    }

    // Create the task
    const task = await prisma.task.create({
      data: {
        ...validatedData,
        dueDate: validatedData.dueDate ? new Date(validatedData.dueDate) : null,
        startDate: validatedData.startDate ? new Date(validatedData.startDate) : null,
        companyId: session.user.companyId,
        createdById: session.user.id,
        assignedToId: validatedData.assignedToId || session.user.id // Default to creator if no assignee
      },
      include: {
        assignedTo: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        lead: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            companyName: true
          }
        },
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
            company: true
          }
        }
      }
    })

    return NextResponse.json({ task }, { status: 201 })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating task:', error)
    return NextResponse.json(
      { error: 'Failed to create task' },
      { status: 500 }
    )
  }
}
