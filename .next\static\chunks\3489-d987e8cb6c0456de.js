(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3489],{92457:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},3021:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},98253:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Building2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},81291:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},71738:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},49168:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Crown",[["path",{d:"m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7zm3 16h14",key:"zkxr6b"}]])},76637:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]])},25119:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},65883:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},68004:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},41827:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},29409:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},49036:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},67972:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},25750:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},24033:function(e,t,n){e.exports=n(15313)},99808:function(e,t,n){"use strict";/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=n(2265),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},u=r.useState,i=r.useEffect,a=r.useLayoutEffect,c=r.useDebugValue;function s(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!o(e,n)}catch(e){return!0}}var l="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=u({inst:{value:n,getSnapshot:t}}),o=r[0].inst,l=r[1];return a(function(){o.value=n,o.getSnapshot=t,s(o)&&l({inst:o})},[e,n,t]),i(function(){return s(o)&&l({inst:o}),e(function(){s(o)&&l({inst:o})})},[e]),c(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:l},26272:function(e,t,n){"use strict";e.exports=n(99808)},61465:function(e,t,n){"use strict";n.d(t,{NY:function(){return E},Ee:function(){return b},fC:function(){return M}});var r=n(2265),o=n(56989),u=n(16459),i=n(51030),a=n(9381),c=n(26272);function s(){return()=>{}}var l=n(57437),d="Avatar",[f,y]=(0,o.b)(d),[p,h]=f(d),v=r.forwardRef((e,t)=>{let{__scopeAvatar:n,...o}=e,[u,i]=r.useState("idle");return(0,l.jsx)(p,{scope:n,imageLoadingStatus:u,onImageLoadingStatusChange:i,children:(0,l.jsx)(a.WV.span,{...o,ref:t})})});v.displayName=d;var m="AvatarImage",k=r.forwardRef((e,t)=>{let{__scopeAvatar:n,src:o,onLoadingStatusChange:d=()=>{},...f}=e,y=h(m,n),p=function(e,{referrerPolicy:t,crossOrigin:n}){let o=(0,c.useSyncExternalStore)(s,()=>!0,()=>!1),u=r.useRef(null),a=o?(u.current||(u.current=new window.Image),u.current):null,[l,d]=r.useState(()=>x(a,e));return(0,i.b)(()=>{d(x(a,e))},[a,e]),(0,i.b)(()=>{let e=e=>()=>{d(e)};if(!a)return;let r=e("loaded"),o=e("error");return a.addEventListener("load",r),a.addEventListener("error",o),t&&(a.referrerPolicy=t),"string"==typeof n&&(a.crossOrigin=n),()=>{a.removeEventListener("load",r),a.removeEventListener("error",o)}},[a,n,t]),l}(o,f),v=(0,u.W)(e=>{d(e),y.onImageLoadingStatusChange(e)});return(0,i.b)(()=>{"idle"!==p&&v(p)},[p,v]),"loaded"===p?(0,l.jsx)(a.WV.img,{...f,ref:t,src:o}):null});k.displayName=m;var g="AvatarFallback",w=r.forwardRef((e,t)=>{let{__scopeAvatar:n,delayMs:o,...u}=e,i=h(g,n),[c,s]=r.useState(void 0===o);return r.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>s(!0),o);return()=>window.clearTimeout(e)}},[o]),c&&"loaded"!==i.imageLoadingStatus?(0,l.jsx)(a.WV.span,{...u,ref:t}):null});function x(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}w.displayName=g;var M=v,b=k,E=w},85606:function(e,t,n){"use strict";n.d(t,{z:function(){return i}});var r=n(2265),o=n(42210),u=n(51030),i=e=>{let t,n;let{present:i,children:c}=e,s=function(e){var t,n;let[o,i]=r.useState(),c=r.useRef(null),s=r.useRef(e),l=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>n[e][t]??e,t));return r.useEffect(()=>{let e=a(c.current);l.current="mounted"===d?e:"none"},[d]),(0,u.b)(()=>{let t=c.current,n=s.current;if(n!==e){let r=l.current,o=a(t);e?f("MOUNT"):"none"===o||t?.display==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),s.current=e}},[e,f]),(0,u.b)(()=>{if(o){let e;let t=o.ownerDocument.defaultView??window,n=n=>{let r=a(c.current).includes(n.animationName);if(n.target===o&&r&&(f("ANIMATION_END"),!s.current)){let n=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=n)})}},r=e=>{e.target===o&&(l.current=a(c.current))};return o.addEventListener("animationstart",r),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",r),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{c.current=e?getComputedStyle(e):null,i(e)},[])}}(i),l="function"==typeof c?c({present:s.isPresent}):r.Children.only(c),d=(0,o.e)(s.ref,(t=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning?l.ref:(t=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning?l.props.ref:l.props.ref||l.ref);return"function"==typeof c||s.isPresent?r.cloneElement(l,{ref:d}):null};function a(e){return e?.animationName||"none"}i.displayName="Presence"},44356:function(e,t,n){"use strict";n.d(t,{Pc:function(){return x},ck:function(){return R},fC:function(){return A}});var r=n(2265),o=n(85744),u=n(27733),i=n(42210),a=n(56989),c=n(20966),s=n(9381),l=n(16459),d=n(73763),f=n(65400),y=n(57437),p="rovingFocusGroup.onEntryFocus",h={bubbles:!1,cancelable:!0},v="RovingFocusGroup",[m,k,g]=(0,u.B)(v),[w,x]=(0,a.b)(v,[g]),[M,b]=w(v),E=r.forwardRef((e,t)=>(0,y.jsx)(m.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,y.jsx)(m.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,y.jsx)(S,{...e,ref:t})})}));E.displayName=v;var S=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:u,loop:a=!1,dir:c,currentTabStopId:m,defaultCurrentTabStopId:g,onCurrentTabStopIdChange:w,onEntryFocus:x,preventScrollOnEntryFocus:b=!1,...E}=e,S=r.useRef(null),N=(0,i.e)(t,S),Z=(0,f.gm)(c),[T,A]=(0,d.T)({prop:m,defaultProp:g??null,onChange:w,caller:v}),[R,L]=r.useState(!1),O=(0,l.W)(x),F=k(n),j=r.useRef(!1),[C,D]=r.useState(0);return r.useEffect(()=>{let e=S.current;if(e)return e.addEventListener(p,O),()=>e.removeEventListener(p,O)},[O]),(0,y.jsx)(M,{scope:n,orientation:u,dir:Z,loop:a,currentTabStopId:T,onItemFocus:r.useCallback(e=>A(e),[A]),onItemShiftTab:r.useCallback(()=>L(!0),[]),onFocusableItemAdd:r.useCallback(()=>D(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>D(e=>e-1),[]),children:(0,y.jsx)(s.WV.div,{tabIndex:R||0===C?-1:0,"data-orientation":u,...E,ref:N,style:{outline:"none",...e.style},onMouseDown:(0,o.M)(e.onMouseDown,()=>{j.current=!0}),onFocus:(0,o.M)(e.onFocus,e=>{let t=!j.current;if(e.target===e.currentTarget&&t&&!R){let t=new CustomEvent(p,h);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=F().filter(e=>e.focusable);I([e.find(e=>e.active),e.find(e=>e.id===T),...e].filter(Boolean).map(e=>e.ref.current),b)}}j.current=!1}),onBlur:(0,o.M)(e.onBlur,()=>L(!1))})})}),N="RovingFocusGroupItem",Z=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:u=!0,active:i=!1,tabStopId:a,children:l,...d}=e,f=(0,c.M)(),p=a||f,h=b(N,n),v=h.currentTabStopId===p,g=k(n),{onFocusableItemAdd:w,onFocusableItemRemove:x,currentTabStopId:M}=h;return r.useEffect(()=>{if(u)return w(),()=>x()},[u,w,x]),(0,y.jsx)(m.ItemSlot,{scope:n,id:p,focusable:u,active:i,children:(0,y.jsx)(s.WV.span,{tabIndex:v?0:-1,"data-orientation":h.orientation,...d,ref:t,onMouseDown:(0,o.M)(e.onMouseDown,e=>{u?h.onItemFocus(p):e.preventDefault()}),onFocus:(0,o.M)(e.onFocus,()=>h.onItemFocus(p)),onKeyDown:(0,o.M)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){h.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return T[o]}(e,h.orientation,h.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let o=g().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)o.reverse();else if("prev"===t||"next"===t){var n,r;"prev"===t&&o.reverse();let u=o.indexOf(e.currentTarget);o=h.loop?(n=o,r=u+1,n.map((e,t)=>n[(r+t)%n.length])):o.slice(u+1)}setTimeout(()=>I(o))}}),children:"function"==typeof l?l({isCurrentTabStop:v,hasTabStop:null!=M}):l})})});Z.displayName=N;var T={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function I(e,t=!1){let n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var A=E,R=Z}}]);