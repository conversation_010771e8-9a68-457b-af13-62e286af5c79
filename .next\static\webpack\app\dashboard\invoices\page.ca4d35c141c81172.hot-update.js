"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/invoices/page",{

/***/ "(app-pages-browser)/./app/dashboard/invoices/page.tsx":
/*!*****************************************!*\
  !*** ./app/dashboard/invoices/page.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ InvoicesPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_data_table__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/data-table */ \"(app-pages-browser)/./components/ui/data-table.tsx\");\n/* harmony import */ var _components_invoices_invoice_form__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/invoices/invoice-form */ \"(app-pages-browser)/./components/invoices/invoice-form.tsx\");\n/* harmony import */ var _components_invoices_invoice_analytics__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/invoices/invoice-analytics */ \"(app-pages-browser)/./components/invoices/invoice-analytics.tsx\");\n/* harmony import */ var _components_invoices_payment_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/invoices/payment-modal */ \"(app-pages-browser)/./components/invoices/payment-modal.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_Clock_Copy_CreditCard_DollarSign_Download_Edit_Eye_FileText_MoreHorizontal_Plus_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CheckCircle,Clock,Copy,CreditCard,DollarSign,Download,Edit,Eye,FileText,MoreHorizontal,Plus,Send,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_Clock_Copy_CreditCard_DollarSign_Download_Edit_Eye_FileText_MoreHorizontal_Plus_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CheckCircle,Clock,Copy,CreditCard,DollarSign,Download,Edit,Eye,FileText,MoreHorizontal,Plus,Send,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_Clock_Copy_CreditCard_DollarSign_Download_Edit_Eye_FileText_MoreHorizontal_Plus_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CheckCircle,Clock,Copy,CreditCard,DollarSign,Download,Edit,Eye,FileText,MoreHorizontal,Plus,Send,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_Clock_Copy_CreditCard_DollarSign_Download_Edit_Eye_FileText_MoreHorizontal_Plus_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CheckCircle,Clock,Copy,CreditCard,DollarSign,Download,Edit,Eye,FileText,MoreHorizontal,Plus,Send,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_Clock_Copy_CreditCard_DollarSign_Download_Edit_Eye_FileText_MoreHorizontal_Plus_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CheckCircle,Clock,Copy,CreditCard,DollarSign,Download,Edit,Eye,FileText,MoreHorizontal,Plus,Send,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/more-horizontal.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_Clock_Copy_CreditCard_DollarSign_Download_Edit_Eye_FileText_MoreHorizontal_Plus_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CheckCircle,Clock,Copy,CreditCard,DollarSign,Download,Edit,Eye,FileText,MoreHorizontal,Plus,Send,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_Clock_Copy_CreditCard_DollarSign_Download_Edit_Eye_FileText_MoreHorizontal_Plus_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CheckCircle,Clock,Copy,CreditCard,DollarSign,Download,Edit,Eye,FileText,MoreHorizontal,Plus,Send,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_Clock_Copy_CreditCard_DollarSign_Download_Edit_Eye_FileText_MoreHorizontal_Plus_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CheckCircle,Clock,Copy,CreditCard,DollarSign,Download,Edit,Eye,FileText,MoreHorizontal,Plus,Send,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_Clock_Copy_CreditCard_DollarSign_Download_Edit_Eye_FileText_MoreHorizontal_Plus_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CheckCircle,Clock,Copy,CreditCard,DollarSign,Download,Edit,Eye,FileText,MoreHorizontal,Plus,Send,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_Clock_Copy_CreditCard_DollarSign_Download_Edit_Eye_FileText_MoreHorizontal_Plus_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CheckCircle,Clock,Copy,CreditCard,DollarSign,Download,Edit,Eye,FileText,MoreHorizontal,Plus,Send,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_Clock_Copy_CreditCard_DollarSign_Download_Edit_Eye_FileText_MoreHorizontal_Plus_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CheckCircle,Clock,Copy,CreditCard,DollarSign,Download,Edit,Eye,FileText,MoreHorizontal,Plus,Send,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_Clock_Copy_CreditCard_DollarSign_Download_Edit_Eye_FileText_MoreHorizontal_Plus_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CheckCircle,Clock,Copy,CreditCard,DollarSign,Download,Edit,Eye,FileText,MoreHorizontal,Plus,Send,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_Clock_Copy_CreditCard_DollarSign_Download_Edit_Eye_FileText_MoreHorizontal_Plus_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CheckCircle,Clock,Copy,CreditCard,DollarSign,Download,Edit,Eye,FileText,MoreHorizontal,Plus,Send,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_Clock_Copy_CreditCard_DollarSign_Download_Edit_Eye_FileText_MoreHorizontal_Plus_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CheckCircle,Clock,Copy,CreditCard,DollarSign,Download,Edit,Eye,FileText,MoreHorizontal,Plus,Send,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_Clock_Copy_CreditCard_DollarSign_Download_Edit_Eye_FileText_MoreHorizontal_Plus_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CheckCircle,Clock,Copy,CreditCard,DollarSign,Download,Edit,Eye,FileText,MoreHorizontal,Plus,Send,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_Clock_Copy_CreditCard_DollarSign_Download_Edit_Eye_FileText_MoreHorizontal_Plus_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CheckCircle,Clock,Copy,CreditCard,DollarSign,Download,Edit,Eye,FileText,MoreHorizontal,Plus,Send,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_Clock_Copy_CreditCard_DollarSign_Download_Edit_Eye_FileText_MoreHorizontal_Plus_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CheckCircle,Clock,Copy,CreditCard,DollarSign,Download,Edit,Eye,FileText,MoreHorizontal,Plus,Send,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_12__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction InvoicesPage() {\n    _s();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [invoices, setInvoices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showForm, setShowForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingInvoice, setEditingInvoice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showAnalytics, setShowAnalytics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPaymentModal, setShowPaymentModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [paymentInvoice, setPaymentInvoice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        total: 0,\n        draft: 0,\n        sent: 0,\n        paid: 0,\n        overdue: 0,\n        totalValue: 0,\n        totalPaid: 0\n    });\n    const fetchInvoices = async ()=>{\n        try {\n            const response = await fetch(\"/api/invoices\");\n            if (!response.ok) throw new Error(\"Failed to fetch invoices\");\n            const data = await response.json();\n            setInvoices(data.invoices);\n            // Calculate stats\n            const total = data.invoices.length;\n            const draft = data.invoices.filter((i)=>i.status === \"DRAFT\").length;\n            const sent = data.invoices.filter((i)=>i.status === \"SENT\").length;\n            const paid = data.invoices.filter((i)=>i.status === \"PAID\").length;\n            const overdue = data.invoices.filter((i)=>i.status === \"OVERDUE\").length;\n            const totalValue = data.invoices.reduce((sum, i)=>sum + (i.total || 0), 0);\n            const totalPaid = data.invoices.filter((i)=>i.status === \"PAID\").reduce((sum, i)=>sum + (i.total || 0), 0);\n            setStats({\n                total,\n                draft,\n                sent,\n                paid,\n                overdue,\n                totalValue,\n                totalPaid\n            });\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_11__.toast.error(\"Failed to load invoices\");\n            console.error(\"Error fetching invoices:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchInvoices();\n    }, []);\n    const handleDelete = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (invoice)=>{\n        if (!confirm('Are you sure you want to delete invoice \"'.concat(invoice.invoiceNumber, '\"?'))) return;\n        try {\n            const response = await fetch(\"/api/invoices/\".concat(invoice.id), {\n                method: \"DELETE\"\n            });\n            if (!response.ok) {\n                const error = await response.json();\n                throw new Error(error.error || \"Failed to delete invoice\");\n            }\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_11__.toast.success(\"Invoice deleted successfully\");\n            fetchInvoices();\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_11__.toast.error(error instanceof Error ? error.message : \"Failed to delete invoice\");\n        }\n    }, [\n        fetchInvoices\n    ]);\n    const handleEdit = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((invoice)=>{\n        setEditingInvoice(invoice);\n        setShowForm(true);\n    }, []);\n    const handleFormClose = ()=>{\n        setShowForm(false);\n        setEditingInvoice(null);\n    };\n    const handleDownloadPDF = async (invoiceId)=>{\n        try {\n            const response = await fetch(\"/api/invoices/\".concat(invoiceId, \"/pdf\"));\n            if (!response.ok) {\n                throw new Error(\"Failed to generate PDF\");\n            }\n            const blob = await response.blob();\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement(\"a\");\n            a.href = url;\n            a.download = \"invoice-\".concat(invoiceId, \".html\");\n            document.body.appendChild(a);\n            a.click();\n            window.URL.revokeObjectURL(url);\n            document.body.removeChild(a);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_11__.toast.success(\"PDF downloaded successfully\");\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_11__.toast.error(\"Failed to download PDF\");\n            console.error(\"Error downloading PDF:\", error);\n        }\n    };\n    const handleRecordPayment = (invoice)=>{\n        setPaymentInvoice(invoice);\n        setShowPaymentModal(true);\n    };\n    const getStatusBadge = (status)=>{\n        if (!status) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n            variant: \"secondary\",\n            children: \"Unknown\"\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n            lineNumber: 185,\n            columnNumber: 25\n        }, this);\n        switch(status){\n            case \"DRAFT\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"secondary\",\n                    children: \"Draft\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 16\n                }, this);\n            case \"SENT\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"info\",\n                    children: \"Sent\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                    lineNumber: 191,\n                    columnNumber: 16\n                }, this);\n            case \"VIEWED\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"warning\",\n                    children: \"Viewed\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                    lineNumber: 193,\n                    columnNumber: 16\n                }, this);\n            case \"PAID\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"success\",\n                    children: \"Paid\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 16\n                }, this);\n            case \"OVERDUE\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"destructive\",\n                    children: \"Overdue\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 16\n                }, this);\n            case \"CANCELLED\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"secondary\",\n                    children: \"Cancelled\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"secondary\",\n                    children: status\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const columns = [\n        {\n            accessorKey: \"invoiceNumber\",\n            header: \"Invoice\",\n            cell: (param)=>{\n                let { row } = param;\n                const invoice = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_Clock_Copy_CreditCard_DollarSign_Download_Edit_Eye_FileText_MoreHorizontal_Plus_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-4 w-4 text-green-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-medium\",\n                                    children: invoice.invoiceNumber\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: invoice.quotation ? \"From \".concat(invoice.quotation.quotationNumber) : \"Direct Invoice\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            accessorKey: \"customer\",\n            header: \"Customer\",\n            cell: (param)=>{\n                let { row } = param;\n                const customer = row.original.customer;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-medium\",\n                            children: customer.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 13\n                        }, this),\n                        customer.company && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500\",\n                            children: customer.company\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            accessorKey: \"status\",\n            header: \"Status\",\n            cell: (param)=>{\n                let { row } = param;\n                return getStatusBadge(row.getValue(\"status\"));\n            }\n        },\n        {\n            accessorKey: \"total\",\n            header: \"Amount\",\n            cell: (param)=>{\n                let { row } = param;\n                const total = row.getValue(\"total\");\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_Clock_Copy_CreditCard_DollarSign_Download_Edit_Eye_FileText_MoreHorizontal_Plus_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            className: \"h-3 w-3 text-green-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-medium\",\n                            children: [\n                                \"$\",\n                                total.toLocaleString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            accessorKey: \"issueDate\",\n            header: \"Issue Date\",\n            cell: (param)=>{\n                let { row } = param;\n                const date = row.getValue(\"issueDate\");\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_Clock_Copy_CreditCard_DollarSign_Download_Edit_Eye_FileText_MoreHorizontal_Plus_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            className: \"h-3 w-3 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm\",\n                            children: new Date(date).toLocaleDateString()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                    lineNumber: 265,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            accessorKey: \"dueDate\",\n            header: \"Due Date\",\n            cell: (param)=>{\n                let { row } = param;\n                const date = row.getValue(\"dueDate\");\n                const dueDate = new Date(date);\n                const today = new Date();\n                const isOverdue = dueDate < today && row.original.status !== \"PAID\";\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_Clock_Copy_CreditCard_DollarSign_Download_Edit_Eye_FileText_MoreHorizontal_Plus_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            className: \"h-3 w-3 \".concat(isOverdue ? \"text-red-400\" : \"text-gray-400\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm \".concat(isOverdue ? \"text-red-600\" : \"\"),\n                            children: dueDate.toLocaleDateString()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                    lineNumber: 282,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            accessorKey: \"createdBy\",\n            header: \"Created By\",\n            cell: (param)=>{\n                let { row } = param;\n                const createdBy = row.original.createdBy;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_Clock_Copy_CreditCard_DollarSign_Download_Edit_Eye_FileText_MoreHorizontal_Plus_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            className: \"h-3 w-3 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm\",\n                            children: createdBy.name || \"Unknown\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                    lineNumber: 297,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            id: \"actions\",\n            cell: (param)=>{\n                let { row } = param;\n                const invoice = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_10__.DropdownMenu, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_10__.DropdownMenuTrigger, {\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                className: \"h-8 w-8 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_Clock_Copy_CreditCard_DollarSign_Download_Edit_Eye_FileText_MoreHorizontal_Plus_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_10__.DropdownMenuContent, {\n                            align: \"end\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_10__.DropdownMenuLabel, {\n                                    children: \"Actions\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_10__.DropdownMenuItem, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_12___default()), {\n                                        href: \"/dashboard/invoices/\".concat(invoice.id),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_Clock_Copy_CreditCard_DollarSign_Download_Edit_Eye_FileText_MoreHorizontal_Plus_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"View Details\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                    lineNumber: 317,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_10__.DropdownMenuItem, {\n                                    onClick: ()=>handleEdit(invoice),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_Clock_Copy_CreditCard_DollarSign_Download_Edit_Eye_FileText_MoreHorizontal_Plus_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Edit\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_10__.DropdownMenuItem, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_Clock_Copy_CreditCard_DollarSign_Download_Edit_Eye_FileText_MoreHorizontal_Plus_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Duplicate\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_10__.DropdownMenuSeparator, {}, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_10__.DropdownMenuItem, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_Clock_Copy_CreditCard_DollarSign_Download_Edit_Eye_FileText_MoreHorizontal_Plus_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Send to Customer\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_10__.DropdownMenuItem, {\n                                    onClick: ()=>handleDownloadPDF(invoice.id),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_Clock_Copy_CreditCard_DollarSign_Download_Edit_Eye_FileText_MoreHorizontal_Plus_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Download PDF\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_10__.DropdownMenuItem, {\n                                    onClick: ()=>handleRecordPayment(invoice),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_Clock_Copy_CreditCard_DollarSign_Download_Edit_Eye_FileText_MoreHorizontal_Plus_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Record Payment\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_10__.DropdownMenuSeparator, {}, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_10__.DropdownMenuItem, {\n                                    onClick: ()=>handleDelete(invoice),\n                                    className: \"text-red-600\",\n                                    disabled: invoice.status === \"PAID\" || invoice._count.payments > 0,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_Clock_Copy_CreditCard_DollarSign_Download_Edit_Eye_FileText_MoreHorizontal_Plus_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Delete\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                    lineNumber: 309,\n                    columnNumber: 11\n                }, this);\n            }\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"Invoices\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Create and manage your invoices\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                lineNumber: 366,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                        lineNumber: 364,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>setShowAnalytics(!showAnalytics),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_Clock_Copy_CreditCard_DollarSign_Download_Edit_Eye_FileText_MoreHorizontal_Plus_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Analytics\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>setShowForm(true),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_Clock_Copy_CreditCard_DollarSign_Download_Edit_Eye_FileText_MoreHorizontal_Plus_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"New Invoice\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                        lineNumber: 368,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                lineNumber: 363,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Total Invoices\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_Clock_Copy_CreditCard_DollarSign_Download_Edit_Eye_FileText_MoreHorizontal_Plus_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: stats.total\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: \"All invoices\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Draft\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_Clock_Copy_CreditCard_DollarSign_Download_Edit_Eye_FileText_MoreHorizontal_Plus_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                        className: \"h-4 w-4 text-gray-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: stats.draft\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: \"Draft invoices\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                        lineNumber: 393,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Sent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_Clock_Copy_CreditCard_DollarSign_Download_Edit_Eye_FileText_MoreHorizontal_Plus_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-4 w-4 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: stats.sent\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: \"Sent to customers\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                        lineNumber: 404,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Paid\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_Clock_Copy_CreditCard_DollarSign_Download_Edit_Eye_FileText_MoreHorizontal_Plus_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                        className: \"h-4 w-4 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: stats.paid\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: \"Paid invoices\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                        lineNumber: 415,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Overdue\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_Clock_Copy_CreditCard_DollarSign_Download_Edit_Eye_FileText_MoreHorizontal_Plus_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                        className: \"h-4 w-4 text-red-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                lineNumber: 427,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: stats.overdue\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: \"Overdue invoices\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                lineNumber: 431,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                        lineNumber: 426,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Total Value\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_Clock_Copy_CreditCard_DollarSign_Download_Edit_Eye_FileText_MoreHorizontal_Plus_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: [\n                                            \"$\",\n                                            stats.totalValue.toLocaleString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: \"Total invoice value\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                        lineNumber: 437,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Total Paid\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_Clock_Copy_CreditCard_DollarSign_Download_Edit_Eye_FileText_MoreHorizontal_Plus_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"h-4 w-4 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: [\n                                            \"$\",\n                                            stats.totalPaid.toLocaleString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: \"Total payments received\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                lineNumber: 453,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                        lineNumber: 448,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                lineNumber: 381,\n                columnNumber: 7\n            }, this),\n            showAnalytics && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_invoices_invoice_analytics__WEBPACK_IMPORTED_MODULE_8__.InvoiceAnalytics, {}, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                lineNumber: 462,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            children: \"Invoice Management\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                            lineNumber: 468,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                        lineNumber: 467,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center py-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                                lineNumber: 473,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                            lineNumber: 472,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_data_table__WEBPACK_IMPORTED_MODULE_6__.DataTable, {\n                            columns: columns,\n                            data: invoices,\n                            searchPlaceholder: \"Search invoices...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                            lineNumber: 476,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                        lineNumber: 470,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                lineNumber: 466,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_invoices_invoice_form__WEBPACK_IMPORTED_MODULE_7__.InvoiceForm, {\n                isOpen: showForm,\n                onClose: handleFormClose,\n                onSuccess: fetchInvoices,\n                invoice: editingInvoice,\n                mode: editingInvoice ? \"edit\" : \"create\"\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                lineNumber: 486,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_invoices_payment_modal__WEBPACK_IMPORTED_MODULE_9__.PaymentModal, {\n                open: showPaymentModal,\n                invoice: paymentInvoice,\n                onClose: ()=>{\n                    setShowPaymentModal(false);\n                    setPaymentInvoice(null);\n                },\n                onSuccess: ()=>{\n                    setShowPaymentModal(false);\n                    setPaymentInvoice(null);\n                    fetchInvoices();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n                lineNumber: 495,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\invoices\\\\page.tsx\",\n        lineNumber: 361,\n        columnNumber: 5\n    }, this);\n}\n_s(InvoicesPage, \"mzIm0WA1dflwbsTaYClWEx1NzcY=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession\n    ];\n});\n_c = InvoicesPage;\nvar _c;\n$RefreshReg$(_c, \"InvoicesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/invoices/page.tsx\n"));

/***/ })

});