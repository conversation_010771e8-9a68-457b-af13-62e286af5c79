import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '30' // days
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - parseInt(period))

    // Get comprehensive invoice analytics
    const [
      totalInvoices,
      invoicesByStatus,
      invoicesByMonth,
      paymentMetrics,
      averageInvoiceValue,
      topInvoicesByValue,
      recentInvoices,
      agingReport,
      customerInvoices,
      revenueMetrics,
      overdueInvoices,
      collectionMetrics
    ] = await Promise.all([
      // Total invoices
      prisma.invoice.count({
        where: {
          companyId: session.user.companyId,
          createdAt: { gte: startDate }
        }
      }),

      // Invoices by status
      prisma.invoice.groupBy({
        by: ['status'],
        where: {
          companyId: session.user.companyId,
          createdAt: { gte: startDate }
        },
        _count: {
          id: true
        },
        _sum: {
          total: true
        }
      }),

      // Invoices by month (last 12 months)
      prisma.$queryRaw`
        SELECT 
          DATE_TRUNC('month', "createdAt") as month,
          COUNT(*)::int as invoice_count,
          SUM("total")::float as total_value,
          SUM(CASE WHEN status = 'PAID' THEN "total" ELSE 0 END)::float as paid_value,
          SUM("paidAmount")::float as collected_amount
        FROM "Invoice" 
        WHERE "companyId" = ${session.user.companyId}
          AND "createdAt" >= ${new Date(Date.now() - 365 * 24 * 60 * 60 * 1000)}
        GROUP BY DATE_TRUNC('month', "createdAt")
        ORDER BY month DESC
        LIMIT 12
      `,

      // Payment metrics
      Promise.all([
        // Total invoiced amount
        prisma.invoice.aggregate({
          where: {
            companyId: session.user.companyId,
            createdAt: { gte: startDate }
          },
          _sum: {
            total: true,
            paidAmount: true
          }
        }),
        // Paid invoices
        prisma.invoice.count({
          where: {
            companyId: session.user.companyId,
            status: 'PAID',
            createdAt: { gte: startDate }
          }
        }),
        // Outstanding amount
        prisma.invoice.aggregate({
          where: {
            companyId: session.user.companyId,
            status: { in: ['SENT', 'OVERDUE'] },
            createdAt: { gte: startDate }
          },
          _sum: {
            total: true,
            paidAmount: true
          }
        })
      ]),

      // Average invoice value
      prisma.invoice.aggregate({
        where: {
          companyId: session.user.companyId,
          createdAt: { gte: startDate }
        },
        _avg: {
          total: true
        }
      }),

      // Top invoices by value
      prisma.invoice.findMany({
        where: {
          companyId: session.user.companyId,
          createdAt: { gte: startDate }
        },
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              company: true,
              email: true
            }
          },
          createdBy: {
            select: {
              name: true,
              email: true
            }
          }
        },
        orderBy: { total: 'desc' },
        take: 10
      }),

      // Recent invoices
      prisma.invoice.findMany({
        where: {
          companyId: session.user.companyId,
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
          }
        },
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              company: true
            }
          },
          createdBy: {
            select: {
              name: true,
              email: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: 10
      }),

      // Aging report (invoices by age)
      Promise.all([
        // Current (0-30 days)
        prisma.invoice.aggregate({
          where: {
            companyId: session.user.companyId,
            status: { in: ['SENT', 'OVERDUE'] },
            dueDate: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
            }
          },
          _count: { id: true },
          _sum: { total: true, paidAmount: true }
        }),
        // 31-60 days
        prisma.invoice.aggregate({
          where: {
            companyId: session.user.companyId,
            status: { in: ['SENT', 'OVERDUE'] },
            dueDate: {
              gte: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000),
              lt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
            }
          },
          _count: { id: true },
          _sum: { total: true, paidAmount: true }
        }),
        // 61-90 days
        prisma.invoice.aggregate({
          where: {
            companyId: session.user.companyId,
            status: { in: ['SENT', 'OVERDUE'] },
            dueDate: {
              gte: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000),
              lt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000)
            }
          },
          _count: { id: true },
          _sum: { total: true, paidAmount: true }
        }),
        // 90+ days
        prisma.invoice.aggregate({
          where: {
            companyId: session.user.companyId,
            status: { in: ['SENT', 'OVERDUE'] },
            dueDate: {
              lt: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000)
            }
          },
          _count: { id: true },
          _sum: { total: true, paidAmount: true }
        })
      ]),

      // Customer invoice analysis
      prisma.invoice.groupBy({
        by: ['customerId'],
        where: {
          companyId: session.user.companyId,
          createdAt: { gte: startDate }
        },
        _count: {
          id: true
        },
        _sum: {
          total: true,
          paidAmount: true
        },
        orderBy: {
          _sum: {
            total: 'desc'
          }
        },
        take: 10
      }),

      // Revenue metrics
      Promise.all([
        // Total revenue (paid invoices)
        prisma.invoice.aggregate({
          where: {
            companyId: session.user.companyId,
            status: 'PAID',
            createdAt: { gte: startDate }
          },
          _sum: {
            total: true
          }
        }),
        // Pending revenue (unpaid invoices)
        prisma.invoice.aggregate({
          where: {
            companyId: session.user.companyId,
            status: { in: ['SENT', 'OVERDUE'] }
          },
          _sum: {
            total: true,
            paidAmount: true
          }
        }),
        // This month's revenue
        prisma.invoice.aggregate({
          where: {
            companyId: session.user.companyId,
            status: 'PAID',
            paidAt: {
              gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
            }
          },
          _sum: {
            total: true
          }
        })
      ]),

      // Overdue invoices
      prisma.invoice.findMany({
        where: {
          companyId: session.user.companyId,
          status: 'OVERDUE',
          dueDate: { lt: new Date() }
        },
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              company: true,
              email: true
            }
          }
        },
        orderBy: { dueDate: 'asc' },
        take: 10
      }),

      // Collection metrics
      Promise.all([
        // Average days to payment
        prisma.$queryRaw`
          SELECT AVG(EXTRACT(DAY FROM ("paidAt" - "issueDate")))::float as avg_days_to_payment
          FROM "Invoice"
          WHERE "companyId" = ${session.user.companyId}
            AND status = 'PAID'
            AND "paidAt" IS NOT NULL
            AND "createdAt" >= ${startDate}
        `,
        // Collection rate
        prisma.$queryRaw`
          SELECT 
            COUNT(CASE WHEN status = 'PAID' THEN 1 END)::float / COUNT(*)::float * 100 as collection_rate
          FROM "Invoice"
          WHERE "companyId" = ${session.user.companyId}
            AND status IN ('PAID', 'OVERDUE', 'SENT')
            AND "createdAt" >= ${startDate}
        `
      ])
    ])

    // Get customer details for customer invoices
    const customerIds = customerInvoices.map(i => i.customerId).filter(Boolean)
    const customers = await prisma.customer.findMany({
      where: {
        id: { in: customerIds },
        companyId: session.user.companyId
      },
      select: {
        id: true,
        name: true,
        company: true,
        email: true
      }
    })

    // Process customer invoices with details
    const customerInvoicesWithDetails = customerInvoices.map(item => {
      const customer = customers.find(c => c.id === item.customerId)
      return {
        customer: customer || { id: item.customerId, name: 'Unknown', company: null, email: null },
        invoiceCount: item._count.id,
        totalValue: Number(item._sum.total || 0),
        paidAmount: Number(item._sum.paidAmount || 0),
        outstandingAmount: Number(item._sum.total || 0) - Number(item._sum.paidAmount || 0)
      }
    })

    // Calculate payment metrics
    const [totalInvoiced, paidInvoicesCount, outstandingAmounts] = paymentMetrics
    const totalInvoicedAmount = Number(totalInvoiced._sum.total || 0)
    const totalPaidAmount = Number(totalInvoiced._sum.paidAmount || 0)
    const outstandingTotal = Number(outstandingAmounts._sum.total || 0)
    const outstandingPaid = Number(outstandingAmounts._sum.paidAmount || 0)
    const outstandingAmount = outstandingTotal - outstandingPaid

    // Calculate aging report
    const [current, days31to60, days61to90, days90plus] = agingReport
    const agingData = [
      {
        period: '0-30 days',
        count: current._count.id,
        amount: Number(current._sum.total || 0) - Number(current._sum.paidAmount || 0)
      },
      {
        period: '31-60 days',
        count: days31to60._count.id,
        amount: Number(days31to60._sum.total || 0) - Number(days31to60._sum.paidAmount || 0)
      },
      {
        period: '61-90 days',
        count: days61to90._count.id,
        amount: Number(days61to90._sum.total || 0) - Number(days61to90._sum.paidAmount || 0)
      },
      {
        period: '90+ days',
        count: days90plus._count.id,
        amount: Number(days90plus._sum.total || 0) - Number(days90plus._sum.paidAmount || 0)
      }
    ]

    // Calculate revenue metrics
    const [totalRevenue, pendingRevenue, thisMonthRevenue] = revenueMetrics
    const totalRevenueAmount = Number(totalRevenue._sum.total || 0)
    const pendingRevenueAmount = Number(pendingRevenue._sum.total || 0) - Number(pendingRevenue._sum.paidAmount || 0)
    const thisMonthRevenueAmount = Number(thisMonthRevenue._sum.total || 0)

    // Calculate collection metrics
    const [avgDaysResult, collectionRateResult] = collectionMetrics
    const avgDaysToPayment = avgDaysResult[0]?.avg_days_to_payment || 0
    const collectionRate = collectionRateResult[0]?.collection_rate || 0

    return NextResponse.json({
      summary: {
        totalInvoices,
        totalInvoicedAmount,
        totalPaidAmount,
        outstandingAmount,
        averageValue: Number(averageInvoiceValue._avg.total || 0),
        paidInvoicesCount,
        collectionRate: Math.round(collectionRate * 100) / 100,
        avgDaysToPayment: Math.round(avgDaysToPayment * 100) / 100
      },
      invoicesByStatus: invoicesByStatus.map(item => ({
        status: item.status,
        count: item._count.id,
        value: Number(item._sum.total || 0)
      })),
      invoicesByMonth,
      agingReport: agingData,
      topInvoices: topInvoicesByValue.map(i => ({
        id: i.id,
        invoiceNumber: i.invoiceNumber,
        title: i.title,
        total: Number(i.total),
        paidAmount: Number(i.paidAmount),
        status: i.status,
        customer: i.customer,
        createdBy: i.createdBy,
        createdAt: i.createdAt,
        dueDate: i.dueDate
      })),
      recentInvoices: recentInvoices.map(i => ({
        id: i.id,
        invoiceNumber: i.invoiceNumber,
        title: i.title,
        total: Number(i.total),
        paidAmount: Number(i.paidAmount),
        status: i.status,
        customer: i.customer,
        createdBy: i.createdBy,
        createdAt: i.createdAt,
        dueDate: i.dueDate
      })),
      customerInvoices: customerInvoicesWithDetails,
      revenueMetrics: {
        totalRevenue: totalRevenueAmount,
        pendingRevenue: pendingRevenueAmount,
        thisMonthRevenue: thisMonthRevenueAmount,
        projectedRevenue: totalRevenueAmount + pendingRevenueAmount
      },
      overdueInvoices: overdueInvoices.map(i => ({
        id: i.id,
        invoiceNumber: i.invoiceNumber,
        total: Number(i.total),
        paidAmount: Number(i.paidAmount),
        dueDate: i.dueDate,
        daysOverdue: Math.floor((new Date().getTime() - new Date(i.dueDate!).getTime()) / (1000 * 60 * 60 * 24)),
        customer: i.customer
      })),
      period: parseInt(period)
    })

  } catch (error) {
    console.error('Error fetching invoice analytics:', error)
    return NextResponse.json(
      { error: 'Failed to fetch invoice analytics' },
      { status: 500 }
    )
  }
}
