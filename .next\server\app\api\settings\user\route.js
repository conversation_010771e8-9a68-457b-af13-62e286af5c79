"use strict";(()=>{var e={};e.id=3177,e.ids=[3177],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},87846:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>x,originalPathname:()=>_,patchFetch:()=>b,requestAsyncStorage:()=>w,routeModule:()=>y,serverHooks:()=>v,staticGenerationAsyncStorage:()=>I,staticGenerationBailout:()=>j});var r={};a.r(r),a.d(r,{GET:()=>g,PATCH:()=>h,PUT:()=>f});var s=a(95419),i=a(69108),n=a(99678),o=a(78070),u=a(81355),d=a(3205),l=a(9108),c=a(25252),p=a(52178);let m=c.Ry({theme:c.Z_().default("light"),language:c.Z_().default("en"),timezone:c.Z_().default("UTC"),dateFormat:c.Z_().default("MM/dd/yyyy"),timeFormat:c.Z_().default("12"),dashboardLayout:c.Yj().optional(),defaultView:c.Z_().default("dashboard"),itemsPerPage:c.Rx().min(10).max(100).default(25),emailNotifications:c.O7().default(!0),pushNotifications:c.O7().default(!0),smsNotifications:c.O7().default(!1),notificationTypes:c.Yj().optional(),profileVisibility:c.Z_().default("company"),showEmail:c.O7().default(!1),showPhone:c.O7().default(!1),fontSize:c.Z_().default("medium"),highContrast:c.O7().default(!1),reducedMotion:c.O7().default(!1),autoSave:c.O7().default(!0),confirmActions:c.O7().default(!0),shortcuts:c.Yj().optional(),customPreferences:c.Yj().optional()});async function g(e){try{let e=await (0,u.getServerSession)(d.L);if(!e?.user?.id)return o.Z.json({error:"Unauthorized"},{status:401});let t=await l._.userSettings.findUnique({where:{userId:e.user.id},include:{user:{select:{id:!0,email:!0,name:!0,firstName:!0,lastName:!0,avatar:!0,phone:!0,title:!0,department:!0,bio:!0,timezone:!0,language:!0}}}});if(!t){let t=await l._.userSettings.create({data:{userId:e.user.id,theme:"light",language:"en",timezone:"UTC",dateFormat:"MM/dd/yyyy",timeFormat:"12",defaultView:"dashboard",itemsPerPage:25,emailNotifications:!0,pushNotifications:!0,smsNotifications:!1,profileVisibility:"company",showEmail:!1,showPhone:!1,fontSize:"medium",highContrast:!1,reducedMotion:!1,autoSave:!0,confirmActions:!0,dashboardLayout:{widgets:[{id:"overview",position:{x:0,y:0,w:12,h:4},enabled:!0},{id:"recent-activities",position:{x:0,y:4,w:6,h:6},enabled:!0},{id:"quick-stats",position:{x:6,y:4,w:6,h:6},enabled:!0},{id:"tasks",position:{x:0,y:10,w:12,h:6},enabled:!0}]},notificationTypes:{newQuotation:!0,quotationApproved:!0,quotationRejected:!0,invoiceCreated:!0,invoicePaid:!0,invoiceOverdue:!0,contractSigned:!0,contractExpiring:!0,taskAssigned:!0,taskDue:!0,taskCompleted:!0,teamUpdates:!0,systemUpdates:!1,marketingEmails:!1},shortcuts:{newQuotation:"ctrl+shift+q",newInvoice:"ctrl+shift+i",newContract:"ctrl+shift+c",newTask:"ctrl+shift+t",search:"ctrl+k",dashboard:"ctrl+shift+d"}},include:{user:{select:{id:!0,email:!0,name:!0,firstName:!0,lastName:!0,avatar:!0,phone:!0,title:!0,department:!0,bio:!0,timezone:!0,language:!0}}}});return o.Z.json({settings:t})}return o.Z.json({settings:t})}catch(e){return console.error("Error fetching user settings:",e),o.Z.json({error:"Failed to fetch user settings"},{status:500})}}async function f(e){try{let t=await (0,u.getServerSession)(d.L);if(!t?.user?.id)return o.Z.json({error:"Unauthorized"},{status:401});let a=await e.json(),r=m.parse(a),s=await l._.$transaction(async e=>{let a=await e.userSettings.upsert({where:{userId:t.user.id},update:{...r,updatedAt:new Date},create:{...r,userId:t.user.id},include:{user:{select:{id:!0,email:!0,name:!0,firstName:!0,lastName:!0,avatar:!0,phone:!0,title:!0,department:!0,bio:!0,timezone:!0,language:!0}}}});return(r.timezone||r.language)&&await e.user.update({where:{id:t.user.id},data:{...r.timezone&&{timezone:r.timezone},...r.language&&{language:r.language}}}),t.user.companyId&&await e.activity.create({data:{type:"SETTINGS",title:"User Settings Updated",description:"User preferences were updated",companyId:t.user.companyId,createdById:t.user.id}}),a});return o.Z.json({settings:s,message:"User settings updated successfully"})}catch(e){if(e instanceof p.jm)return o.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("Error updating user settings:",e),o.Z.json({error:"Failed to update user settings"},{status:500})}}async function h(e){try{let t=await (0,u.getServerSession)(d.L);if(!t?.user?.id)return o.Z.json({error:"Unauthorized"},{status:401});let a=await e.json(),r=await l._.userSettings.findUnique({where:{userId:t.user.id}});if(!r)return o.Z.json({error:"User settings not found"},{status:404});let s={...r,...a,updatedAt:new Date};delete s.id,delete s.userId,delete s.createdAt;let i=m.parse(s),n=await l._.userSettings.update({where:{userId:t.user.id},data:i,include:{user:{select:{id:!0,email:!0,name:!0,firstName:!0,lastName:!0,avatar:!0,phone:!0,title:!0,department:!0,bio:!0,timezone:!0,language:!0}}}});return o.Z.json({settings:n,message:"User settings updated successfully"})}catch(e){if(e instanceof p.jm)return o.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("Error updating user settings:",e),o.Z.json({error:"Failed to update user settings"},{status:500})}}let y=new s.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/settings/user/route",pathname:"/api/settings/user",filename:"route",bundlePath:"app/api/settings/user/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\settings\\user\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:w,staticGenerationAsyncStorage:I,serverHooks:v,headerHooks:x,staticGenerationBailout:j}=y,_="/api/settings/user/route";function b(){return(0,n.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:I})}},3205:(e,t,a)=>{a.d(t,{L:()=>d});var r=a(86485),s=a(10375),i=a(50694),n=a(6521),o=a.n(n),u=a(9108);let d={providers:[(0,r.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await u._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),a=t?.companyId;if(!a&&t){let e=await u._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(a=e?.id)&&await u._.user.update({where:{id:t.id},data:{companyId:a}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await o().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await u._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:a}}catch(e){return console.error("Authentication error:",e),null}}}),(0,s.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,i.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,a)=>{a.d(t,{_:()=>s});let r=require("@prisma/client"),s=globalThis.prisma??new r.PrismaClient}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[1638,6206,6521,2455,4520,5252],()=>a(87846));module.exports=r})();