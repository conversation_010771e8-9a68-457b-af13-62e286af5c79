'use client'

import { useRouter } from 'next/navigation'
import { PaymentForm } from '@/components/payments/payment-form'
import { Button } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'

export default function NewPaymentPage() {
  const router = useRouter()

  const handleSuccess = () => {
    router.push('/dashboard/payments')
  }

  const handleCancel = () => {
    router.push('/dashboard/payments')
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/dashboard/payments">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Payments
            </Link>
          </Button>
          <h1 className="text-3xl font-bold">Record New Payment</h1>
        </div>
      </div>

      {/* Payment Form */}
      <PaymentForm
        onSuccess={handleSuccess}
        onCancel={handleCancel}
      />
    </div>
  )
}
