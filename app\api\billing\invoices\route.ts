import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { stripe } from '@/lib/stripe'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '10')
    const offset = parseInt(searchParams.get('offset') || '0')

    // Get current subscription
    const subscription = await prisma.subscription.findFirst({
      where: {
        companyId: session.user.companyId,
        status: { in: ['ACTIVE', 'TRIALING', 'PAST_DUE', 'CANCELED'] }
      },
      include: {
        pricingPlan: true
      }
    })

    if (!subscription || !subscription.stripeCustomerId) {
      return NextResponse.json({
        success: true,
        data: {
          invoices: [],
          hasMore: false,
          total: 0
        }
      })
    }

    // Fetch invoices from Stripe
    const stripeInvoices = await stripe.invoices.list({
      customer: subscription.stripeCustomerId,
      limit: limit,
      starting_after: offset > 0 ? undefined : undefined, // Implement pagination properly
      expand: ['data.payment_intent']
    })

    const invoices = stripeInvoices.data.map(invoice => ({
      id: invoice.id,
      number: invoice.number,
      status: invoice.status,
      amount: invoice.total,
      currency: invoice.currency,
      created: new Date(invoice.created * 1000),
      dueDate: invoice.due_date ? new Date(invoice.due_date * 1000) : null,
      paidAt: invoice.status_transitions.paid_at 
        ? new Date(invoice.status_transitions.paid_at * 1000) 
        : null,
      description: invoice.description,
      hostedInvoiceUrl: invoice.hosted_invoice_url,
      invoicePdf: invoice.invoice_pdf,
      periodStart: new Date(invoice.period_start * 1000),
      periodEnd: new Date(invoice.period_end * 1000),
      subtotal: invoice.subtotal,
      tax: invoice.tax || 0,
      discount: invoice.discount?.amount || 0,
      lines: invoice.lines.data.map(line => ({
        id: line.id,
        description: line.description,
        amount: line.amount,
        quantity: line.quantity,
        period: {
          start: new Date(line.period.start * 1000),
          end: new Date(line.period.end * 1000)
        }
      })),
      paymentMethod: invoice.payment_intent?.payment_method_types || [],
      attemptCount: invoice.attempt_count
    }))

    return NextResponse.json({
      success: true,
      data: {
        invoices,
        hasMore: stripeInvoices.has_more,
        total: invoices.length,
        subscription: {
          planName: subscription.pricingPlan.name,
          status: subscription.status,
          billingCycle: subscription.billingCycle
        }
      }
    })

  } catch (error) {
    console.error('Error fetching billing invoices:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch billing invoices' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { action, invoiceId } = body

    // Get current subscription
    const subscription = await prisma.subscription.findFirst({
      where: {
        companyId: session.user.companyId,
        status: { in: ['ACTIVE', 'TRIALING', 'PAST_DUE', 'CANCELED'] }
      }
    })

    if (!subscription || !subscription.stripeCustomerId) {
      return NextResponse.json(
        { success: false, error: 'No subscription found' },
        { status: 404 }
      )
    }

    switch (action) {
      case 'retry_payment':
        // Retry payment for failed invoice
        const invoice = await stripe.invoices.retrieve(invoiceId)
        if (invoice.status === 'open') {
          const paymentIntent = await stripe.invoices.pay(invoiceId)
          return NextResponse.json({
            success: true,
            data: { paymentIntent }
          })
        } else {
          return NextResponse.json(
            { success: false, error: 'Invoice is not payable' },
            { status: 400 }
          )
        }

      case 'download_pdf':
        // Get PDF URL for invoice
        const invoiceForPdf = await stripe.invoices.retrieve(invoiceId)
        return NextResponse.json({
          success: true,
          data: { 
            pdfUrl: invoiceForPdf.invoice_pdf,
            hostedUrl: invoiceForPdf.hosted_invoice_url
          }
        })

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('Error handling invoice action:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to handle invoice action' },
      { status: 500 }
    )
  }
}
