"use strict";(()=>{var e={};e.id=9636,e.ids=[9636],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},47665:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>_,originalPathname:()=>b,patchFetch:()=>x,requestAsyncStorage:()=>h,routeModule:()=>I,serverHooks:()=>f,staticGenerationAsyncStorage:()=>w,staticGenerationBailout:()=>v});var a={};r.r(a),r.d(a,{GET:()=>y,POST:()=>g});var o=r(95419),s=r(69108),n=r(99678),i=r(78070),l=r(81355),u=r(3205),c=r(9108),d=r(25252),p=r(52178);let m=d.Ry({name:d.Z_().min(1,"Name is required"),email:d.Z_().email("Invalid email").optional().nullable().or(d.i0("")),phone:d.Z_().optional().nullable().or(d.i0("")),company:d.Z_().optional().nullable().or(d.i0("")),address:d.Z_().optional().nullable().or(d.i0("")),city:d.Z_().optional().nullable().or(d.i0("")),state:d.Z_().optional().nullable().or(d.i0("")),country:d.Z_().optional().nullable().or(d.i0("")),postalCode:d.Z_().optional().nullable().or(d.i0("")),industry:d.Z_().optional().nullable().or(d.i0("")),website:d.Z_().url("Invalid website URL").optional().nullable().or(d.i0("")),notes:d.Z_().optional().nullable().or(d.i0("")),tags:d.IX(d.Z_()).optional().default([]),status:d.Km(["ACTIVE","INACTIVE","PROSPECT"]).default("ACTIVE")});async function y(e){try{let t=await (0,l.getServerSession)(u.L);if(!t?.user?.id)return i.Z.json({error:"Unauthorized"},{status:401});let{searchParams:r}=new URL(e.url),a=parseInt(r.get("page")||"1"),o=parseInt(r.get("limit")||"10"),s=r.get("search")||"",n=r.get("status")||"",d=r.get("sortBy")||"createdAt",p=r.get("sortOrder")||"desc",m=(a-1)*o,y={companyId:t.user.companyId||void 0};s&&(y.OR=[{name:{contains:s,mode:"insensitive"}},{email:{contains:s,mode:"insensitive"}},{companyName:{contains:s,mode:"insensitive"}},{phone:{contains:s,mode:"insensitive"}}]),n&&(y.status=n);let[g,I]=await Promise.all([c._.customer.findMany({where:y,skip:m,take:o,orderBy:{[d]:p},include:{createdBy:{select:{name:!0,email:!0}},_count:{select:{leads:!0,quotations:!0,invoices:!0,activities:!0}}}}),c._.customer.count({where:y})]);return i.Z.json({customers:g,pagination:{page:a,limit:o,total:I,pages:Math.ceil(I/o)}})}catch(e){return console.error("Error fetching customers:",e),i.Z.json({error:"Failed to fetch customers"},{status:500})}}async function g(e){try{let t=await (0,l.getServerSession)(u.L);if(!t?.user?.id)return i.Z.json({error:"Unauthorized"},{status:401});let r=await e.json();console.log("Customer creation request body:",JSON.stringify(r,null,2)),console.log("About to transform data...");let a=m.parse(r);console.log("Validated customer data:",JSON.stringify(a,null,2));let o={};if(Object.entries(a).forEach(([e,t])=>{"company"===e?o.companyName=""===t?null:t:o[e]=""===t?null:t}),console.log("Transformed data:",JSON.stringify(o,null,2)),o.email&&await c._.customer.findFirst({where:{email:o.email,companyId:t.user.companyId||void 0}}))return i.Z.json({error:"Customer with this email already exists"},{status:400});let s=await c._.customer.create({data:{...o,tags:o.tags||[],companyId:t.user.companyId,createdById:t.user.id},include:{createdBy:{select:{name:!0,email:!0}},_count:{select:{leads:!0,quotations:!0,invoices:!0,activities:!0}}}});return await c._.activity.create({data:{type:"NOTE",title:"Customer Created",description:`Customer "${s.name}" was created`,customerId:s.id,companyId:t.user.companyId,createdById:t.user.id}}),i.Z.json(s,{status:201})}catch(e){if(e instanceof p.jm)return console.error("Customer creation validation error:",e.errors),i.Z.json({error:"Validation failed",details:e.errors,message:e.errors.map(e=>`${e.path.join(".")}: ${e.message}`).join(", ")},{status:400});return console.error("Error creating customer:",e),i.Z.json({error:"Failed to create customer"},{status:500})}}let I=new o.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/customers/route",pathname:"/api/customers",filename:"route",bundlePath:"app/api/customers/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\customers\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:h,staticGenerationAsyncStorage:w,serverHooks:f,headerHooks:_,staticGenerationBailout:v}=I,b="/api/customers/route";function x(){return(0,n.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:w})}},3205:(e,t,r)=>{r.d(t,{L:()=>u});var a=r(86485),o=r(10375),s=r(50694),n=r(6521),i=r.n(n),l=r(9108);let u={providers:[(0,a.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await l._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),r=t?.companyId;if(!r&&t){let e=await l._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(r=e?.id)&&await l._.user.update({where:{id:t.id},data:{companyId:r}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await i().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await l._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:r}}catch(e){return console.error("Authentication error:",e),null}}}),(0,o.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,s.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,r)=>{r.d(t,{_:()=>o});let a=require("@prisma/client"),o=globalThis.prisma??new a.PrismaClient}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,6206,6521,2455,4520,5252],()=>r(47665));module.exports=a})();