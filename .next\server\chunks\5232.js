"use strict";exports.id=5232,exports.ids=[5232],exports.modules={10763:(e,a,t)=>{t.d(a,{w:()=>x});var s=t(95344),l=t(3729),d=t(90491),r=t(60563),o=t(28765),i=t(25390),n=t(16212),c=t(20886),m=t(92549),f=t(81036);function x({columns:e,data:a,searchKey:t,searchPlaceholder:x="Search...",onSearch:p}){let[u,g]=l.useState([]),[h,N]=l.useState([]),[b,j]=l.useState({}),[y,w]=l.useState({}),[v,C]=l.useState(""),R=(0,d.b7)({data:a,columns:e,onSortingChange:g,onColumnFiltersChange:N,getCoreRowModel:(0,r.sC)(),getPaginationRowModel:(0,r.G_)(),getSortedRowModel:(0,r.tj)(),getFilteredRowModel:(0,r.vL)(),onColumnVisibilityChange:j,onRowSelectionChange:w,onGlobalFilterChange:C,globalFilterFn:"includesString",state:{sorting:u,columnFilters:h,columnVisibility:b,rowSelection:y,globalFilter:v}}),k=e=>{C(e),p?.(e)};return(0,s.jsxs)("div",{className:"w-full",children:[(0,s.jsxs)("div",{className:"flex items-center py-4",children:[(0,s.jsxs)("div",{className:"relative flex-1 max-w-sm",children:[s.jsx(o.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),s.jsx(m.I,{placeholder:x,value:v,onChange:e=>k(e.target.value),className:"pl-10"})]}),(0,s.jsxs)(c.h_,{children:[s.jsx(c.$F,{asChild:!0,children:(0,s.jsxs)(n.z,{variant:"outline",className:"ml-auto",children:["Columns ",s.jsx(i.Z,{className:"ml-2 h-4 w-4"})]})}),s.jsx(c.AW,{align:"end",children:R.getAllColumns().filter(e=>e.getCanHide()).map(e=>s.jsx(c.bO,{className:"capitalize",checked:e.getIsVisible(),onCheckedChange:a=>e.toggleVisibility(!!a),children:e.id},e.id))})]})]}),s.jsx("div",{className:"rounded-md border",children:(0,s.jsxs)(f.iA,{children:[s.jsx(f.xD,{children:R.getHeaderGroups().map(e=>s.jsx(f.SC,{children:e.headers.map(e=>s.jsx(f.ss,{children:e.isPlaceholder?null:(0,d.ie)(e.column.columnDef.header,e.getContext())},e.id))},e.id))}),s.jsx(f.RM,{children:R.getRowModel().rows?.length?R.getRowModel().rows.map(e=>s.jsx(f.SC,{"data-state":e.getIsSelected()&&"selected",children:e.getVisibleCells().map(e=>s.jsx(f.pj,{children:(0,d.ie)(e.column.columnDef.cell,e.getContext())},e.id))},e.id)):s.jsx(f.SC,{children:s.jsx(f.pj,{colSpan:e.length,className:"h-24 text-center",children:"No results."})})})]})}),(0,s.jsxs)("div",{className:"flex items-center justify-end space-x-2 py-4",children:[(0,s.jsxs)("div",{className:"flex-1 text-sm text-muted-foreground",children:[R.getFilteredSelectedRowModel().rows.length," of"," ",R.getFilteredRowModel().rows.length," row(s) selected."]}),(0,s.jsxs)("div",{className:"space-x-2",children:[s.jsx(n.z,{variant:"outline",size:"sm",onClick:()=>R.previousPage(),disabled:!R.getCanPreviousPage(),children:"Previous"}),s.jsx(n.z,{variant:"outline",size:"sm",onClick:()=>R.nextPage(),disabled:!R.getCanNextPage(),children:"Next"})]})]})]})}},16802:(e,a,t)=>{t.d(a,{$N:()=>u,Be:()=>g,Vq:()=>i,cN:()=>p,cZ:()=>f,fK:()=>x,hg:()=>n,t9:()=>m});var s=t(95344),l=t(3729),d=t(88794),r=t(14513),o=t(91626);let i=d.fC,n=d.xz,c=d.h_;d.x8;let m=l.forwardRef(({className:e,...a},t)=>s.jsx(d.aV,{ref:t,className:(0,o.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...a}));m.displayName=d.aV.displayName;let f=l.forwardRef(({className:e,children:a,...t},l)=>(0,s.jsxs)(c,{children:[s.jsx(m,{}),(0,s.jsxs)(d.VY,{ref:l,className:(0,o.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...t,children:[a,(0,s.jsxs)(d.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[s.jsx(r.Z,{className:"h-4 w-4"}),s.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));f.displayName=d.VY.displayName;let x=({className:e,...a})=>s.jsx("div",{className:(0,o.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...a});x.displayName="DialogHeader";let p=({className:e,...a})=>s.jsx("div",{className:(0,o.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...a});p.displayName="DialogFooter";let u=l.forwardRef(({className:e,...a},t)=>s.jsx(d.Dx,{ref:t,className:(0,o.cn)("text-lg font-semibold leading-none tracking-tight",e),...a}));u.displayName=d.Dx.displayName;let g=l.forwardRef(({className:e,...a},t)=>s.jsx(d.dk,{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",e),...a}));g.displayName=d.dk.displayName},92549:(e,a,t)=>{t.d(a,{I:()=>r});var s=t(95344),l=t(3729),d=t(91626);let r=l.forwardRef(({className:e,type:a,...t},l)=>s.jsx("input",{type:a,className:(0,d.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:l,...t}));r.displayName="Input"},1586:(e,a,t)=>{t.d(a,{_:()=>n});var s=t(95344),l=t(3729),d=t(14217),r=t(49247),o=t(91626);let i=(0,r.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),n=l.forwardRef(({className:e,...a},t)=>s.jsx(d.f,{ref:t,className:(0,o.cn)(i(),e),...a}));n.displayName=d.f.displayName},17470:(e,a,t)=>{t.d(a,{Bw:()=>u,Ph:()=>c,Ql:()=>g,i4:()=>f,ki:()=>m});var s=t(95344),l=t(3729),d=t(1146),r=t(25390),o=t(12704),i=t(62312),n=t(91626);let c=d.fC;d.ZA;let m=d.B4,f=l.forwardRef(({className:e,children:a,...t},l)=>(0,s.jsxs)(d.xz,{ref:l,className:(0,n.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...t,children:[a,s.jsx(d.JO,{asChild:!0,children:s.jsx(r.Z,{className:"h-4 w-4 opacity-50"})})]}));f.displayName=d.xz.displayName;let x=l.forwardRef(({className:e,...a},t)=>s.jsx(d.u_,{ref:t,className:(0,n.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:s.jsx(o.Z,{className:"h-4 w-4"})}));x.displayName=d.u_.displayName;let p=l.forwardRef(({className:e,...a},t)=>s.jsx(d.$G,{ref:t,className:(0,n.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:s.jsx(r.Z,{className:"h-4 w-4"})}));p.displayName=d.$G.displayName;let u=l.forwardRef(({className:e,children:a,position:t="popper",...l},r)=>s.jsx(d.h_,{children:(0,s.jsxs)(d.VY,{ref:r,className:(0,n.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:t,...l,children:[s.jsx(x,{}),s.jsx(d.l_,{className:(0,n.cn)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),s.jsx(p,{})]})}));u.displayName=d.VY.displayName,l.forwardRef(({className:e,...a},t)=>s.jsx(d.__,{ref:t,className:(0,n.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...a})).displayName=d.__.displayName;let g=l.forwardRef(({className:e,children:a,...t},l)=>(0,s.jsxs)(d.ck,{ref:l,className:(0,n.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[s.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:s.jsx(d.wU,{children:s.jsx(i.Z,{className:"h-4 w-4"})})}),s.jsx(d.eT,{children:a})]}));g.displayName=d.ck.displayName,l.forwardRef(({className:e,...a},t)=>s.jsx(d.Z0,{ref:t,className:(0,n.cn)("-mx-1 my-1 h-px bg-muted",e),...a})).displayName=d.Z0.displayName},81036:(e,a,t)=>{t.d(a,{RM:()=>i,SC:()=>n,iA:()=>r,pj:()=>m,ss:()=>c,xD:()=>o});var s=t(95344),l=t(3729),d=t(91626);let r=l.forwardRef(({className:e,...a},t)=>s.jsx("div",{className:"relative w-full overflow-auto",children:s.jsx("table",{ref:t,className:(0,d.cn)("w-full caption-bottom text-sm",e),...a})}));r.displayName="Table";let o=l.forwardRef(({className:e,...a},t)=>s.jsx("thead",{ref:t,className:(0,d.cn)("[&_tr]:border-b",e),...a}));o.displayName="TableHeader";let i=l.forwardRef(({className:e,...a},t)=>s.jsx("tbody",{ref:t,className:(0,d.cn)("[&_tr:last-child]:border-0",e),...a}));i.displayName="TableBody",l.forwardRef(({className:e,...a},t)=>s.jsx("tfoot",{ref:t,className:(0,d.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...a})).displayName="TableFooter";let n=l.forwardRef(({className:e,...a},t)=>s.jsx("tr",{ref:t,className:(0,d.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...a}));n.displayName="TableRow";let c=l.forwardRef(({className:e,...a},t)=>s.jsx("th",{ref:t,className:(0,d.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...a}));c.displayName="TableHead";let m=l.forwardRef(({className:e,...a},t)=>s.jsx("td",{ref:t,className:(0,d.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...a}));m.displayName="TableCell",l.forwardRef(({className:e,...a},t)=>s.jsx("caption",{ref:t,className:(0,d.cn)("mt-4 text-sm text-muted-foreground",e),...a})).displayName="TableCaption"}};