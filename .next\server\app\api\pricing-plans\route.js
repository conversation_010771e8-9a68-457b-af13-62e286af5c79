"use strict";(()=>{var e={};e.id=5997,e.ids=[5997],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},58707:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>x,originalPathname:()=>w,patchFetch:()=>f,requestAsyncStorage:()=>y,routeModule:()=>m,serverHooks:()=>h,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>P});var a={};t.r(a),t.d(a,{GET:()=>p,POST:()=>d});var s=t(95419),i=t(69108),n=t(99678),o=t(78070),l=t(81355),c=t(3205),u=t(9108);async function p(e){try{let{searchParams:r}=new URL(e.url),t="true"===r.get("includeInactive"),a="true"===r.get("publicOnly"),s={};t||(s.isActive=!0),a&&(s.isPublic=!0);let i=(await u._.pricingPlan.findMany({where:s,orderBy:{sortOrder:"asc"},select:{id:!0,name:!0,description:!0,monthlyPrice:!0,yearlyPrice:!0,currency:!0,maxUsers:!0,maxCompanies:!0,maxCustomers:!0,maxQuotations:!0,maxInvoices:!0,maxContracts:!0,maxStorage:!0,features:!0,isActive:!0,isPublic:!0,trialDays:!0,stripeProductId:!0,stripePriceId:!0,stripeYearlyPriceId:!0,createdAt:!0,updatedAt:!0}})).map(e=>({...e,monthlyPrice:Number(e.monthlyPrice),yearlyPrice:e.yearlyPrice?Number(e.yearlyPrice):null,maxStorage:Number(e.maxStorage),yearlyDiscount:e.yearlyPrice&&e.monthlyPrice?Math.round((1-Number(e.yearlyPrice)/12/Number(e.monthlyPrice))*100):0,formattedStorage:function(e){if(0===e)return"0 Bytes";let r=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,r)).toFixed(2))+" "+["Bytes","KB","MB","GB","TB"][r]}(Number(e.maxStorage)),isPopular:"pro"===e.name.toLowerCase(),features:e.features||{}}));return o.Z.json({success:!0,data:i})}catch(e){return console.error("Error fetching pricing plans:",e),o.Z.json({success:!1,error:"Failed to fetch pricing plans"},{status:500})}}async function d(e){try{let r=await (0,l.getServerSession)(c.L);if(!r?.user||"SUPER_ADMIN"!==r.user.role)return o.Z.json({success:!1,error:"Unauthorized"},{status:401});let{name:t,description:a,monthlyPrice:s,yearlyPrice:i,currency:n="USD",maxUsers:p,maxCompanies:d,maxCustomers:m,maxQuotations:y,maxInvoices:g,maxContracts:h,maxStorage:x,features:P,isActive:w=!0,isPublic:f=!0,trialDays:I=14,stripeProductId:b,stripePriceId:q,stripeYearlyPriceId:v}=await e.json();if(!t||!s||!p)return o.Z.json({success:!1,error:"Missing required fields"},{status:400});let S=await u._.pricingPlan.findFirst({orderBy:{sortOrder:"desc"},select:{sortOrder:!0}}),_=(S?.sortOrder||0)+1,j=await u._.pricingPlan.create({data:{name:t,description:a,monthlyPrice:s,yearlyPrice:i,currency:n,maxUsers:p,maxCompanies:d,maxCustomers:m,maxQuotations:y,maxInvoices:g,maxContracts:h,maxStorage:BigInt(x),features:P,isActive:w,isPublic:f,trialDays:I,sortOrder:_,stripeProductId:b,stripePriceId:q,stripeYearlyPriceId:v}});return o.Z.json({success:!0,data:{...j,monthlyPrice:Number(j.monthlyPrice),yearlyPrice:j.yearlyPrice?Number(j.yearlyPrice):null,maxStorage:Number(j.maxStorage)}})}catch(e){return console.error("Error creating pricing plan:",e),o.Z.json({success:!1,error:"Failed to create pricing plan"},{status:500})}}let m=new s.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/pricing-plans/route",pathname:"/api/pricing-plans",filename:"route",bundlePath:"app/api/pricing-plans/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\pricing-plans\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:y,staticGenerationAsyncStorage:g,serverHooks:h,headerHooks:x,staticGenerationBailout:P}=m,w="/api/pricing-plans/route";function f(){return(0,n.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:g})}},3205:(e,r,t)=>{t.d(r,{L:()=>c});var a=t(86485),s=t(10375),i=t(50694),n=t(6521),o=t.n(n),l=t(9108);let c={providers:[(0,a.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let r=await l._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),t=r?.companyId;if(!t&&r){let e=await l._.company.findFirst({where:{ownerId:r.id},select:{id:!0}});(t=e?.id)&&await l._.user.update({where:{id:r.id},data:{companyId:t}})}if(!r)return console.log("User not found:",e.email),null;if(!r.password)return console.log("User has no password set:",e.email),null;if(!await o().compare(e.password,r.password))return console.log("Invalid password for user:",e.email),null;return await l._.user.update({where:{id:r.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",r.email),{id:r.id,email:r.email,name:r.name,role:r.role,companyId:t}}catch(e){return console.error("Authentication error:",e),null}}}),(0,s.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,i.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(console.log("JWT callback - user data:",{id:r.id,email:r.email,role:r.role,companyId:r.companyId}),e.role=r.role,e.companyId=r.companyId),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role,e.user.companyId=r.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,r,t)=>{t.d(r,{_:()=>s});let a=require("@prisma/client"),s=globalThis.prisma??new a.PrismaClient}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[1638,6206,6521,2455,4520],()=>t(58707));module.exports=a})();