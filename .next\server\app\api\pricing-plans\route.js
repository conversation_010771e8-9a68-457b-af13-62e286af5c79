"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/pricing-plans/route";
exports.ids = ["app/api/pricing-plans/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fpricing-plans%2Froute&page=%2Fapi%2Fpricing-plans%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpricing-plans%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fpricing-plans%2Froute&page=%2Fapi%2Fpricing-plans%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpricing-plans%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_proj_nextjs_saas_app_api_pricing_plans_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/pricing-plans/route.ts */ \"(rsc)/./app/api/pricing-plans/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/pricing-plans/route\",\n        pathname: \"/api/pricing-plans\",\n        filename: \"route\",\n        bundlePath: \"app/api/pricing-plans/route\"\n    },\n    resolvedPagePath: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\api\\\\pricing-plans\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_proj_nextjs_saas_app_api_pricing_plans_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/pricing-plans/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fpricing-plans%2Froute&page=%2Fapi%2Fpricing-plans%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpricing-plans%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/pricing-plans/route.ts":
/*!****************************************!*\
  !*** ./app/api/pricing-plans/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const includeInactive = searchParams.get(\"includeInactive\") === \"true\";\n        const publicOnly = searchParams.get(\"publicOnly\") === \"true\";\n        // Build where clause\n        const where = {};\n        if (!includeInactive) {\n            where.isActive = true;\n        }\n        if (publicOnly) {\n            where.isPublic = true;\n        }\n        const plans = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.pricingPlan.findMany({\n            where,\n            orderBy: {\n                sortOrder: \"asc\"\n            },\n            select: {\n                id: true,\n                name: true,\n                description: true,\n                monthlyPrice: true,\n                yearlyPrice: true,\n                currency: true,\n                maxUsers: true,\n                maxCompanies: true,\n                maxCustomers: true,\n                maxQuotations: true,\n                maxInvoices: true,\n                maxContracts: true,\n                maxStorage: true,\n                features: true,\n                isActive: true,\n                isPublic: true,\n                trialDays: true,\n                stripeProductId: true,\n                stripePriceId: true,\n                stripeYearlyPriceId: true,\n                createdAt: true,\n                updatedAt: true\n            }\n        });\n        // Format the response with calculated values\n        const formattedPlans = plans.map((plan)=>({\n                ...plan,\n                monthlyPrice: Number(plan.monthlyPrice),\n                yearlyPrice: plan.yearlyPrice ? Number(plan.yearlyPrice) : null,\n                maxStorage: Number(plan.maxStorage),\n                yearlyDiscount: plan.yearlyPrice && plan.monthlyPrice ? Math.round((1 - Number(plan.yearlyPrice) / 12 / Number(plan.monthlyPrice)) * 100) : 0,\n                formattedStorage: formatBytes(Number(plan.maxStorage)),\n                isPopular: plan.name.toLowerCase() === \"pro\",\n                features: plan.features || {}\n            }));\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            data: formattedPlans\n        });\n    } catch (error) {\n        console.error(\"Error fetching pricing plans:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            error: \"Failed to fetch pricing plans\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user || session.user.role !== \"SUPER_ADMIN\") {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { name, description, monthlyPrice, yearlyPrice, currency = \"USD\", maxUsers, maxCompanies, maxCustomers, maxQuotations, maxInvoices, maxContracts, maxStorage, features, isActive = true, isPublic = true, trialDays = 14, stripeProductId, stripePriceId, stripeYearlyPriceId } = body;\n        // Validation\n        if (!name || !monthlyPrice || !maxUsers) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"Missing required fields\"\n            }, {\n                status: 400\n            });\n        }\n        // Get the next sort order\n        const lastPlan = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.pricingPlan.findFirst({\n            orderBy: {\n                sortOrder: \"desc\"\n            },\n            select: {\n                sortOrder: true\n            }\n        });\n        const sortOrder = (lastPlan?.sortOrder || 0) + 1;\n        const plan = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.pricingPlan.create({\n            data: {\n                name,\n                description,\n                monthlyPrice,\n                yearlyPrice,\n                currency,\n                maxUsers,\n                maxCompanies,\n                maxCustomers,\n                maxQuotations,\n                maxInvoices,\n                maxContracts,\n                maxStorage: BigInt(maxStorage),\n                features,\n                isActive,\n                isPublic,\n                trialDays,\n                sortOrder,\n                stripeProductId,\n                stripePriceId,\n                stripeYearlyPriceId\n            }\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            data: {\n                ...plan,\n                monthlyPrice: Number(plan.monthlyPrice),\n                yearlyPrice: plan.yearlyPrice ? Number(plan.yearlyPrice) : null,\n                maxStorage: Number(plan.maxStorage)\n            }\n        });\n    } catch (error) {\n        console.error(\"Error creating pricing plan:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            error: \"Failed to create pricing plan\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Helper function to format bytes\nfunction formatBytes(bytes) {\n    if (bytes === 0) return \"0 Bytes\";\n    const k = 1024;\n    const sizes = [\n        \"Bytes\",\n        \"KB\",\n        \"MB\",\n        \"GB\",\n        \"TB\"\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/pricing-plans/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/github */ \"(rsc)/./node_modules/next-auth/providers/github.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n\nconst authOptions = {\n    // Using JWT strategy instead of database adapter for now\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                try {\n                    if (!credentials?.email || !credentials?.password) {\n                        console.log(\"Missing credentials\");\n                        return null;\n                    }\n                    console.log(\"Attempting to authenticate user:\", credentials.email);\n                    const user = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.findUnique({\n                        where: {\n                            email: credentials.email\n                        },\n                        select: {\n                            id: true,\n                            email: true,\n                            name: true,\n                            password: true,\n                            role: true,\n                            companyId: true\n                        }\n                    });\n                    // Get the company ID - either as member or owner\n                    let finalCompanyId = user?.companyId;\n                    if (!finalCompanyId && user) {\n                        const ownedCompany = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.company.findFirst({\n                            where: {\n                                ownerId: user.id\n                            },\n                            select: {\n                                id: true\n                            }\n                        });\n                        finalCompanyId = ownedCompany?.id;\n                        // If user is company owner, update their companyId for future queries\n                        if (finalCompanyId) {\n                            await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                                where: {\n                                    id: user.id\n                                },\n                                data: {\n                                    companyId: finalCompanyId\n                                }\n                            });\n                        }\n                    }\n                    if (!user) {\n                        console.log(\"User not found:\", credentials.email);\n                        return null;\n                    }\n                    if (!user.password) {\n                        console.log(\"User has no password set:\", credentials.email);\n                        return null;\n                    }\n                    const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_3___default().compare(credentials.password, user.password);\n                    if (!isPasswordValid) {\n                        console.log(\"Invalid password for user:\", credentials.email);\n                        return null;\n                    }\n                    // Update last login\n                    await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                        where: {\n                            id: user.id\n                        },\n                        data: {\n                            lastLoginAt: new Date(),\n                            loginCount: {\n                                increment: 1\n                            }\n                        }\n                    });\n                    console.log(\"User authenticated successfully:\", user.email);\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name,\n                        role: user.role,\n                        companyId: finalCompanyId\n                    };\n                } catch (error) {\n                    console.error(\"Authentication error:\", error);\n                    return null;\n                }\n            }\n        }),\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            clientId: process.env.GITHUB_CLIENT_ID,\n            clientSecret: process.env.GITHUB_CLIENT_SECRET\n        })\n    ],\n    session: {\n        strategy: \"jwt\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                console.log(\"JWT callback - user data:\", {\n                    id: user.id,\n                    email: user.email,\n                    role: user.role,\n                    companyId: user.companyId\n                });\n                token.role = user.role;\n                token.companyId = user.companyId;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n                session.user.companyId = token.companyId;\n                // Ensure no company object is ever set on the session\n                if (session.user.company) {\n                    delete session.user.company;\n                }\n                console.log(\"Session callback - final session:\", {\n                    id: session.user.id,\n                    email: session.user.email,\n                    role: session.user.role,\n                    companyId: session.user.companyId\n                });\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        signUp: \"/auth/signup\",\n        error: \"/auth/error\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zYWFzLWFwcC8uL2xpYi9wcmlzbWEudHM/OTgyMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/lru-cache","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fpricing-plans%2Froute&page=%2Fapi%2Fpricing-plans%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpricing-plans%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();