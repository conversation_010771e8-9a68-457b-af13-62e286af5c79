'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { redirect } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { 
  Banknote, 
  Save, 
  RefreshCw, 
  Eye,
  EyeOff,
  CheckCircle,
  XCircle,
  AlertTriangle,
  CreditCard,
  Smartphone,
  Globe,
  Shield
} from 'lucide-react'
import { toast } from 'react-hot-toast'

interface PaymentGateway {
  id: string
  name: string
  provider: string
  isEnabled: boolean
  isLive: boolean
  config: Record<string, any>
  supportedMethods: string[]
  supportedCurrencies: string[]
  fees: {
    percentage: number
    fixed: number
    currency: string
  }
  status: 'active' | 'inactive' | 'error'
  lastSync: string
}

interface PaymentGatewayConfig {
  stripe: {
    enabled: boolean
    live: boolean
    publishableKey: string
    secretKey: string
    webhookSecret: string
    supportedMethods: string[]
  }
  paypal: {
    enabled: boolean
    live: boolean
    clientId: string
    clientSecret: string
    webhookId: string
    supportedMethods: string[]
  }
  razorpay: {
    enabled: boolean
    live: boolean
    keyId: string
    keySecret: string
    webhookSecret: string
    supportedMethods: string[]
  }
  square: {
    enabled: boolean
    live: boolean
    applicationId: string
    accessToken: string
    webhookSignatureKey: string
    supportedMethods: string[]
  }
}

export default function PaymentGatewaysPage() {
  const { data: session, status } = useSession()
  const [gateways, setGateways] = useState<PaymentGateway[]>([])
  const [config, setConfig] = useState<PaymentGatewayConfig>({
    stripe: {
      enabled: false,
      live: false,
      publishableKey: '',
      secretKey: '',
      webhookSecret: '',
      supportedMethods: ['card', 'apple_pay', 'google_pay']
    },
    paypal: {
      enabled: false,
      live: false,
      clientId: '',
      clientSecret: '',
      webhookId: '',
      supportedMethods: ['paypal', 'venmo']
    },
    razorpay: {
      enabled: false,
      live: false,
      keyId: '',
      keySecret: '',
      webhookSecret: '',
      supportedMethods: ['card', 'netbanking', 'upi', 'wallet']
    },
    square: {
      enabled: false,
      live: false,
      applicationId: '',
      accessToken: '',
      webhookSignatureKey: '',
      supportedMethods: ['card', 'apple_pay', 'google_pay']
    }
  })
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [showSecrets, setShowSecrets] = useState<Record<string, boolean>>({})

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (status === 'unauthenticated') {
    redirect('/auth/signin')
  }

  if (session?.user?.role !== 'SUPER_ADMIN') {
    redirect('/dashboard')
  }

  const fetchGateways = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/super-admin/payment-gateways')
      const data = await response.json()
      
      if (data.success) {
        setGateways(data.gateways)
        setConfig({ ...config, ...data.config })
      }
    } catch (error) {
      console.error('Error fetching payment gateways:', error)
      toast.error('Failed to load payment gateways')
    } finally {
      setLoading(false)
    }
  }

  const saveConfig = async () => {
    try {
      setSaving(true)
      const response = await fetch('/api/super-admin/payment-gateways', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
      })

      const data = await response.json()
      
      if (data.success) {
        toast.success('Payment gateway configuration saved successfully')
        fetchGateways()
      } else {
        toast.error(data.error || 'Failed to save configuration')
      }
    } catch (error) {
      console.error('Error saving config:', error)
      toast.error('Failed to save configuration')
    } finally {
      setSaving(false)
    }
  }

  const testConnection = async (provider: string) => {
    try {
      const response = await fetch(`/api/super-admin/payment-gateways/test`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ provider })
      })

      const data = await response.json()
      
      if (data.success) {
        toast.success(`${provider} connection test successful`)
      } else {
        toast.error(data.error || `${provider} connection test failed`)
      }
    } catch (error) {
      console.error('Error testing connection:', error)
      toast.error('Connection test failed')
    }
  }

  useEffect(() => {
    fetchGateways()
  }, [])

  const updateConfig = (provider: keyof PaymentGatewayConfig, key: string, value: any) => {
    setConfig(prev => ({
      ...prev,
      [provider]: {
        ...prev[provider],
        [key]: value
      }
    }))
  }

  const toggleSecret = (key: string) => {
    setShowSecrets(prev => ({ ...prev, [key]: !prev[key] }))
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />
      default:
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <div className="flex items-center space-x-3">
            <Banknote className="h-8 w-8 text-green-600" />
            <h1 className="text-3xl font-bold text-gray-900">Payment Gateways</h1>
          </div>
          <p className="text-gray-500 mt-1">Configure payment processors and methods</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={fetchGateways} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button onClick={saveConfig} disabled={saving}>
            <Save className={`h-4 w-4 mr-2 ${saving ? 'animate-spin' : ''}`} />
            Save Configuration
          </Button>
        </div>
      </div>

      {/* Gateway Status Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {Object.entries(config).map(([provider, settings]) => (
          <Card key={provider}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-500 capitalize">{provider}</p>
                  <div className="flex items-center space-x-2 mt-1">
                    {getStatusIcon(settings.enabled ? 'active' : 'inactive')}
                    <span className="text-sm font-medium">
                      {settings.enabled ? 'Enabled' : 'Disabled'}
                    </span>
                  </div>
                  {settings.enabled && (
                    <Badge variant={settings.live ? 'default' : 'secondary'} className="mt-2">
                      {settings.live ? 'Live' : 'Test'}
                    </Badge>
                  )}
                </div>
                <div className="text-right">
                  <CreditCard className="h-8 w-8 text-gray-400" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Gateway Configuration */}
      <Tabs defaultValue="stripe" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="stripe">Stripe</TabsTrigger>
          <TabsTrigger value="paypal">PayPal</TabsTrigger>
          <TabsTrigger value="razorpay">Razorpay</TabsTrigger>
          <TabsTrigger value="square">Square</TabsTrigger>
        </TabsList>

        {/* Stripe Configuration */}
        <TabsContent value="stripe">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <CreditCard className="h-5 w-5 mr-2" />
                Stripe Configuration
              </CardTitle>
              <CardDescription>
                Configure Stripe payment processing
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <Label htmlFor="stripeEnabled" className="text-base font-medium">
                    Enable Stripe
                  </Label>
                  <p className="text-sm text-gray-500">
                    Accept credit cards, Apple Pay, Google Pay, and more
                  </p>
                </div>
                <Switch
                  id="stripeEnabled"
                  checked={config.stripe.enabled}
                  onCheckedChange={(checked) => updateConfig('stripe', 'enabled', checked)}
                />
              </div>

              {config.stripe.enabled && (
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <Label htmlFor="stripeLive" className="text-base font-medium">
                        Live Mode
                      </Label>
                      <p className="text-sm text-gray-500">
                        Use live API keys for production payments
                      </p>
                    </div>
                    <Switch
                      id="stripeLive"
                      checked={config.stripe.live}
                      onCheckedChange={(checked) => updateConfig('stripe', 'live', checked)}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="stripePublishableKey">Publishable Key</Label>
                      <Input
                        id="stripePublishableKey"
                        value={config.stripe.publishableKey}
                        onChange={(e) => updateConfig('stripe', 'publishableKey', e.target.value)}
                        placeholder={config.stripe.live ? "pk_live_..." : "pk_test_..."}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="stripeSecretKey">Secret Key</Label>
                      <div className="relative">
                        <Input
                          id="stripeSecretKey"
                          type={showSecrets.stripeSecret ? 'text' : 'password'}
                          value={config.stripe.secretKey}
                          onChange={(e) => updateConfig('stripe', 'secretKey', e.target.value)}
                          placeholder={config.stripe.live ? "sk_live_..." : "sk_test_..."}
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                          onClick={() => toggleSecret('stripeSecret')}
                        >
                          {showSecrets.stripeSecret ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </Button>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="stripeWebhookSecret">Webhook Endpoint Secret</Label>
                    <div className="relative">
                      <Input
                        id="stripeWebhookSecret"
                        type={showSecrets.stripeWebhook ? 'text' : 'password'}
                        value={config.stripe.webhookSecret}
                        onChange={(e) => updateConfig('stripe', 'webhookSecret', e.target.value)}
                        placeholder="whsec_..."
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => toggleSecret('stripeWebhook')}
                      >
                        {showSecrets.stripeWebhook ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </Button>
                    </div>
                    <p className="text-xs text-gray-500">
                      Webhook URL: {window.location.origin}/api/webhooks/stripe
                    </p>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      onClick={() => testConnection('stripe')}
                      disabled={!config.stripe.secretKey}
                    >
                      <Shield className="h-4 w-4 mr-2" />
                      Test Connection
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
