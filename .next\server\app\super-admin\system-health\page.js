(()=>{var e={};e.id=9299,e.ids=[9299],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},40409:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>o,pages:()=>x,routeModule:()=>h,tree:()=>d});var a=t(50482),r=t(69108),l=t(62563),n=t.n(l),i=t(68300),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);t.d(s,c);let d=["",{children:["super-admin",{children:["system-health",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,86106)),"C:\\proj\\nextjs-saas\\app\\super-admin\\system-health\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,11285)),"C:\\proj\\nextjs-saas\\app\\super-admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,59504)),"C:\\proj\\nextjs-saas\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],x=["C:\\proj\\nextjs-saas\\app\\super-admin\\system-health\\page.tsx"],o="/super-admin/system-health/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/super-admin/system-health/page",pathname:"/super-admin/system-health",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},18992:(e,s,t)=>{Promise.resolve().then(t.bind(t,82089))},82089:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>N});var a=t(95344),r=t(3729),l=t(47674),n=t(22254),i=t(61351),c=t(16212),d=t(69436),x=t(76461),o=t(7060),m=t(45961),h=t(73229),p=t(88534),u=t(25545),y=t(33733),j=t(73577);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let g=(0,t(69224).Z)("MemoryStick",[["path",{d:"M6 19v-3",key:"1nvgqn"}],["path",{d:"M10 19v-3",key:"iu8nkm"}],["path",{d:"M14 19v-3",key:"kcehxu"}],["path",{d:"M18 19v-3",key:"1vh91z"}],["path",{d:"M8 11V9",key:"63erz4"}],["path",{d:"M16 11V9",key:"fru6f3"}],["path",{d:"M12 11V9",key:"ha00sb"}],["path",{d:"M2 15h20",key:"16ne18"}],["path",{d:"M2 7a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v1.1a2 2 0 0 0 0 3.837V17a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-5.1a2 2 0 0 0 0-3.837Z",key:"lhddv3"}]]);var f=t(99046),v=t(75203);function N(){let{data:e,status:s}=(0,l.useSession)(),[t,N]=(0,r.useState)(null),[b,w]=(0,r.useState)(!0),[k,M]=(0,r.useState)(null);if("loading"===s)return a.jsx("div",{className:"min-h-screen flex items-center justify-center",children:a.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})});"unauthenticated"===s&&(0,n.redirect)("/auth/signin"),e?.user?.role!=="SUPER_ADMIN"&&(0,n.redirect)("/dashboard");let P=async()=>{try{w(!0);let e=await fetch("/api/super-admin/system-health");if(!e.ok)throw Error("Failed to fetch system health");let s=await e.json();N(s),M(new Date)}catch(e){console.error("Error fetching system health:",e)}finally{w(!1)}};(0,r.useEffect)(()=>{P();let e=setInterval(P,3e4);return()=>clearInterval(e)},[]);let Z=e=>{let s={healthy:{variant:"default",icon:o.Z,color:"text-green-600"},warning:{variant:"secondary",icon:m.Z,color:"text-yellow-600"},critical:{variant:"destructive",icon:h.Z,color:"text-red-600"},unhealthy:{variant:"destructive",icon:h.Z,color:"text-red-600"}},t=s[e]||s.unhealthy,r=t.icon;return(0,a.jsxs)(d.C,{variant:t.variant,className:"flex items-center space-x-1",children:[a.jsx(r,{className:"h-3 w-3"}),a.jsx("span",{children:e.toUpperCase()})]})},A=e=>{if(0===e)return"0 Bytes";let s=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,s)).toFixed(2))+" "+["Bytes","KB","MB","GB","TB"][s]},C=e=>a.jsx(d.C,{variant:{ERROR:"destructive",WARN:"secondary",INFO:"default",DEBUG:"outline"}[e]||"default",children:e});return t?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(p.Z,{className:"h-8 w-8 text-blue-600"}),a.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"System Health"}),Z(t.status)]}),a.jsx("p",{className:"text-gray-500 mt-1",children:"Real-time system monitoring and diagnostics"}),k&&(0,a.jsxs)("div",{className:"flex items-center space-x-1 mt-2 text-sm text-gray-500",children:[a.jsx(u.Z,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:["Last updated: ",k.toLocaleTimeString()]})]})]}),(0,a.jsxs)(c.z,{onClick:P,disabled:b,children:[a.jsx(y.Z,{className:`h-4 w-4 mr-2 ${b?"animate-spin":""}`}),"Refresh"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[a.jsx(i.Zb,{children:(0,a.jsxs)(i.aY,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-500",children:"CPU Usage"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[t?.metrics?.memoryUsage?t.metrics.memoryUsage.toFixed(1):"0.0","%"]})]}),a.jsx(j.Z,{className:"h-8 w-8 text-blue-600"})]}),a.jsx(x.E,{value:t?.metrics?.memoryUsage||0,className:"mt-3"})]})}),a.jsx(i.Zb,{children:(0,a.jsxs)(i.aY,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Memory Usage"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[t?.system?.memory?.usagePercent?t.system.memory.usagePercent.toFixed(1):"0.0","%"]})]}),a.jsx(g,{className:"h-8 w-8 text-green-600"})]}),a.jsx(x.E,{value:t?.system?.memory?.usagePercent||0,className:"mt-3"}),a.jsx("p",{className:"text-xs text-gray-500 mt-2",children:t?.system?.memory?`${A(t.system.memory.used)} / ${A(t.system.memory.total)}`:"N/A"})]})}),a.jsx(i.Zb,{children:(0,a.jsxs)(i.aY,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Database Response"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[t?.database?.responseTime||0,"ms"]})]}),a.jsx(f.Z,{className:"h-8 w-8 text-purple-600"})]}),a.jsx("div",{className:"mt-3",children:Z(t?.database?.status||"unknown")})]})}),a.jsx(i.Zb,{children:(0,a.jsxs)(i.aY,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-500",children:"System Uptime"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:t?.system?.uptime?(e=>{let s=Math.floor(e/86400),t=Math.floor(e%86400/3600),a=Math.floor(e%3600/60);return s>0?`${s}d ${t}h ${a}m`:t>0?`${t}h ${a}m`:`${a}m`})(t.system.uptime):"N/A"})]}),a.jsx(v.Z,{className:"h-8 w-8 text-orange-600"})]}),a.jsx("p",{className:"text-xs text-gray-500 mt-3",children:t?.system?`${t.system.platform} ${t.system.arch}`:"N/A"})]})})]}),(0,a.jsxs)(i.Zb,{children:[a.jsx(i.Ol,{children:(0,a.jsxs)(i.ll,{className:"flex items-center space-x-2",children:[a.jsx(f.Z,{className:"h-5 w-5"}),a.jsx("span",{children:"Database Metrics"})]})}),a.jsx(i.aY,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("p",{className:"text-2xl font-bold text-blue-600",children:t?.database?.totalUsers||0}),a.jsx("p",{className:"text-sm text-gray-500",children:"Total Users"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("p",{className:"text-2xl font-bold text-green-600",children:t?.database?.totalCompanies||0}),a.jsx("p",{className:"text-sm text-gray-500",children:"Total Companies"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("p",{className:"text-2xl font-bold text-purple-600",children:t?.database?.totalActivities||0}),a.jsx("p",{className:"text-sm text-gray-500",children:"Total Activities"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("p",{className:"text-2xl font-bold text-red-600",children:t?.database?.recentErrors||0}),a.jsx("p",{className:"text-sm text-gray-500",children:"Recent Errors (24h)"})]})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(i.Zb,{children:[a.jsx(i.Ol,{children:a.jsx(i.ll,{children:"System Information"})}),a.jsx(i.aY,{children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-500",children:"Platform:"}),a.jsx("span",{className:"font-medium",children:t?.system?.platform||"N/A"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-500",children:"Architecture:"}),a.jsx("span",{className:"font-medium",children:t?.system?.arch||"N/A"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-500",children:"Node.js Version:"}),a.jsx("span",{className:"font-medium",children:t?.system?.nodeVersion||"N/A"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-500",children:"CPU Cores:"}),a.jsx("span",{className:"font-medium",children:t?.system?.cpuCount||0})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-500",children:"Load Average:"}),a.jsx("span",{className:"font-medium",children:t?.system?.loadAverage?t.system.loadAverage.map(e=>e.toFixed(2)).join(", "):"N/A"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-500",children:"Database Size:"}),(0,a.jsxs)("span",{className:"font-medium",children:[t?.database?.size||0," MB"]})]})]})})]}),(0,a.jsxs)(i.Zb,{children:[a.jsx(i.Ol,{children:a.jsx(i.ll,{children:"Recent System Logs"})}),a.jsx(i.aY,{children:a.jsx("div",{className:"space-y-3 max-h-64 overflow-y-auto",children:t?.recentLogs?.length>0?t.recentLogs.map(e=>(0,a.jsxs)("div",{className:"flex items-start space-x-3 p-3 bg-gray-50 rounded-lg",children:[a.jsx("div",{className:"flex-shrink-0",children:C(e.level)}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[a.jsx("p",{className:"text-sm font-medium text-gray-900 truncate",children:e.message}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[a.jsx("span",{className:"text-xs text-gray-500",children:e.source}),e.category&&(0,a.jsxs)("span",{className:"text-xs text-gray-400",children:["• ",e.category]}),(0,a.jsxs)("span",{className:"text-xs text-gray-400",children:["• ",new Date(e.createdAt).toLocaleTimeString()]})]})]})]},e.id)):a.jsx("div",{className:"text-center py-8 text-gray-500",children:a.jsx("p",{children:"No recent logs available"})})})})]})]})]}):a.jsx("div",{className:"min-h-screen flex items-center justify-center",children:a.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})})}},76461:(e,s,t)=>{"use strict";t.d(s,{E:()=>N});var a=t(95344),r=t(3729),l=t(98462),n=t(62409),i="Progress",[c,d]=(0,l.b)(i),[x,o]=c(i),m=r.forwardRef((e,s)=>{var t,r;let{__scopeProgress:l,value:i=null,max:c,getValueLabel:d=u,...o}=e;(c||0===c)&&!g(c)&&console.error((t=`${c}`,`Invalid prop \`max\` of value \`${t}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let m=g(c)?c:100;null===i||f(i,m)||console.error((r=`${i}`,`Invalid prop \`value\` of value \`${r}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let h=f(i,m)?i:null,p=j(h)?d(h,m):void 0;return(0,a.jsx)(x,{scope:l,value:h,max:m,children:(0,a.jsx)(n.WV.div,{"aria-valuemax":m,"aria-valuemin":0,"aria-valuenow":j(h)?h:void 0,"aria-valuetext":p,role:"progressbar","data-state":y(h,m),"data-value":h??void 0,"data-max":m,...o,ref:s})})});m.displayName=i;var h="ProgressIndicator",p=r.forwardRef((e,s)=>{let{__scopeProgress:t,...r}=e,l=o(h,t);return(0,a.jsx)(n.WV.div,{"data-state":y(l.value,l.max),"data-value":l.value??void 0,"data-max":l.max,...r,ref:s})});function u(e,s){return`${Math.round(e/s*100)}%`}function y(e,s){return null==e?"indeterminate":e===s?"complete":"loading"}function j(e){return"number"==typeof e}function g(e){return j(e)&&!isNaN(e)&&e>0}function f(e,s){return j(e)&&!isNaN(e)&&e<=s&&e>=0}p.displayName=h;var v=t(91626);let N=r.forwardRef(({className:e,value:s,...t},r)=>a.jsx(m,{ref:r,className:(0,v.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...t,children:a.jsx(p,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(s||0)}%)`}})}));N.displayName=m.displayName},73577:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Cpu",[["rect",{x:"4",y:"4",width:"16",height:"16",rx:"2",key:"1vbyd7"}],["rect",{x:"9",y:"9",width:"6",height:"6",key:"o3kz5p"}],["path",{d:"M15 2v2",key:"13l42r"}],["path",{d:"M15 20v2",key:"15mkzm"}],["path",{d:"M2 15h2",key:"1gxd5l"}],["path",{d:"M2 9h2",key:"1bbxkp"}],["path",{d:"M20 15h2",key:"19e6y8"}],["path",{d:"M20 9h2",key:"19tzq7"}],["path",{d:"M9 2v2",key:"165o2o"}],["path",{d:"M9 20v2",key:"i2bqo8"}]])},75203:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]])},73229:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},86106:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>l,__esModule:()=>r,default:()=>n});let a=(0,t(86843).createProxy)(String.raw`C:\proj\nextjs-saas\app\super-admin\system-health\page.tsx`),{__esModule:r,$$typeof:l}=a,n=a.default}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[1638,7948,6671,4626,7792,2506,1729,2125,3965],()=>t(40409));module.exports=a})();