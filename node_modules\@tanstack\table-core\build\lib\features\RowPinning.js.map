{"version": 3, "file": "RowPinning.js", "sources": ["../../../src/features/RowPinning.ts"], "sourcesContent": ["import {\n  OnChangeFn,\n  Updater,\n  Table,\n  Row,\n  RowData,\n  TableFeature,\n} from '../types'\nimport { getMemoOptions, makeStateUpdater, memo } from '../utils'\n\nexport type RowPinningPosition = false | 'top' | 'bottom'\n\nexport interface RowPinningState {\n  bottom?: string[]\n  top?: string[]\n}\n\nexport interface RowPinningTableState {\n  rowPinning: RowPinningState\n}\n\nexport interface RowPinningOptions<TData extends RowData> {\n  /**\n   * Enables/disables row pinning for the table. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#enablerowpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  enableRowPinning?: boolean | ((row: Row<TData>) => boolean)\n  /**\n   * When `false`, pinned rows will not be visible if they are filtered or paginated out of the table. When `true`, pinned rows will always be visible regardless of filtering or pagination. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#keeppinnedrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  keepPinnedRows?: boolean\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.rowPinning` changes. This overrides the default internal state management, so you will also need to supply `state.rowPinning` from your own managed state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#onrowpinningchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/onrowpinningchange)\n   */\n  onRowPinningChange?: OnChangeFn<RowPinningState>\n}\n\nexport interface RowPinningDefaultOptions {\n  onRowPinningChange: OnChangeFn<RowPinningState>\n}\n\nexport interface RowPinningRow {\n  /**\n   * Returns whether or not the row can be pinned.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#getcanpin-1)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getCanPin: () => boolean\n  /**\n   * Returns the pinned position of the row. (`'top'`, `'bottom'` or `false`)\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#getispinned-1)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getIsPinned: () => RowPinningPosition\n  /**\n   * Returns the numeric pinned index of the row within a pinned row group.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#getpinnedindex-1)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getPinnedIndex: () => number\n  /**\n   * Pins a row to the `'top'` or `'bottom'`, or unpins the row to the center if `false` is passed.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#pin-1)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  pin: (\n    position: RowPinningPosition,\n    includeLeafRows?: boolean,\n    includeParentRows?: boolean\n  ) => void\n}\n\nexport interface RowPinningInstance<TData extends RowData> {\n  _getPinnedRows: (\n    visiblePinnedRows: Array<Row<TData>>,\n    pinnedRowIds: Array<string> | undefined,\n    position: 'top' | 'bottom'\n  ) => Row<TData>[]\n  /**\n   * Returns all bottom pinned rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#getbottomrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getBottomRows: () => Row<TData>[]\n  /**\n   * Returns all rows that are not pinned to the top or bottom.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#getcenterrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getCenterRows: () => Row<TData>[]\n  /**\n   * Returns whether or not any rows are pinned. Optionally specify to only check for pinned rows in either the `top` or `bottom` position.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#getissomerowspinned)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getIsSomeRowsPinned: (position?: RowPinningPosition) => boolean\n  /**\n   * Returns all top pinned rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#gettoprows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getTopRows: () => Row<TData>[]\n  /**\n   * Resets the **rowPinning** state to `initialState.rowPinning`, or `true` can be passed to force a default blank state reset to `{ top: [], bottom: [], }`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#resetrowpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  resetRowPinning: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.rowPinning` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#setrowpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  setRowPinning: (updater: Updater<RowPinningState>) => void\n}\n\n//\n\nconst getDefaultRowPinningState = (): RowPinningState => ({\n  top: [],\n  bottom: [],\n})\n\nexport const RowPinning: TableFeature = {\n  getInitialState: (state): RowPinningTableState => {\n    return {\n      rowPinning: getDefaultRowPinningState(),\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): RowPinningDefaultOptions => {\n    return {\n      onRowPinningChange: makeStateUpdater('rowPinning', table),\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row.pin = (position, includeLeafRows, includeParentRows) => {\n      const leafRowIds = includeLeafRows\n        ? row.getLeafRows().map(({ id }) => id)\n        : []\n      const parentRowIds = includeParentRows\n        ? row.getParentRows().map(({ id }) => id)\n        : []\n      const rowIds = new Set([...parentRowIds, row.id, ...leafRowIds])\n\n      table.setRowPinning(old => {\n        if (position === 'bottom') {\n          return {\n            top: (old?.top ?? []).filter(d => !rowIds?.has(d)),\n            bottom: [\n              ...(old?.bottom ?? []).filter(d => !rowIds?.has(d)),\n              ...Array.from(rowIds),\n            ],\n          }\n        }\n\n        if (position === 'top') {\n          return {\n            top: [\n              ...(old?.top ?? []).filter(d => !rowIds?.has(d)),\n              ...Array.from(rowIds),\n            ],\n            bottom: (old?.bottom ?? []).filter(d => !rowIds?.has(d)),\n          }\n        }\n\n        return {\n          top: (old?.top ?? []).filter(d => !rowIds?.has(d)),\n          bottom: (old?.bottom ?? []).filter(d => !rowIds?.has(d)),\n        }\n      })\n    }\n    row.getCanPin = () => {\n      const { enableRowPinning, enablePinning } = table.options\n      if (typeof enableRowPinning === 'function') {\n        return enableRowPinning(row)\n      }\n      return enableRowPinning ?? enablePinning ?? true\n    }\n    row.getIsPinned = () => {\n      const rowIds = [row.id]\n\n      const { top, bottom } = table.getState().rowPinning\n\n      const isTop = rowIds.some(d => top?.includes(d))\n      const isBottom = rowIds.some(d => bottom?.includes(d))\n\n      return isTop ? 'top' : isBottom ? 'bottom' : false\n    }\n    row.getPinnedIndex = () => {\n      const position = row.getIsPinned()\n      if (!position) return -1\n\n      const visiblePinnedRowIds = (\n        position === 'top' ? table.getTopRows() : table.getBottomRows()\n      )?.map(({ id }) => id)\n\n      return visiblePinnedRowIds?.indexOf(row.id) ?? -1\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setRowPinning = updater => table.options.onRowPinningChange?.(updater)\n\n    table.resetRowPinning = defaultState =>\n      table.setRowPinning(\n        defaultState\n          ? getDefaultRowPinningState()\n          : table.initialState?.rowPinning ?? getDefaultRowPinningState()\n      )\n\n    table.getIsSomeRowsPinned = position => {\n      const pinningState = table.getState().rowPinning\n\n      if (!position) {\n        return Boolean(pinningState.top?.length || pinningState.bottom?.length)\n      }\n      return Boolean(pinningState[position]?.length)\n    }\n\n    table._getPinnedRows = (visibleRows, pinnedRowIds, position) => {\n      const rows =\n        table.options.keepPinnedRows ?? true\n          ? //get all rows that are pinned even if they would not be otherwise visible\n            //account for expanded parent rows, but not pagination or filtering\n            (pinnedRowIds ?? []).map(rowId => {\n              const row = table.getRow(rowId, true)\n              return row.getIsAllParentsExpanded() ? row : null\n            })\n          : //else get only visible rows that are pinned\n            (pinnedRowIds ?? []).map(\n              rowId => visibleRows.find(row => row.id === rowId)!\n            )\n\n      return rows.filter(Boolean).map(d => ({ ...d, position })) as Row<TData>[]\n    }\n\n    table.getTopRows = memo(\n      () => [table.getRowModel().rows, table.getState().rowPinning.top],\n      (allRows, topPinnedRowIds) =>\n        table._getPinnedRows(allRows, topPinnedRowIds, 'top'),\n      getMemoOptions(table.options, 'debugRows', 'getTopRows')\n    )\n\n    table.getBottomRows = memo(\n      () => [table.getRowModel().rows, table.getState().rowPinning.bottom],\n      (allRows, bottomPinnedRowIds) =>\n        table._getPinnedRows(allRows, bottomPinnedRowIds, 'bottom'),\n      getMemoOptions(table.options, 'debugRows', 'getBottomRows')\n    )\n\n    table.getCenterRows = memo(\n      () => [\n        table.getRowModel().rows,\n        table.getState().rowPinning.top,\n        table.getState().rowPinning.bottom,\n      ],\n      (allRows, top, bottom) => {\n        const topAndBottom = new Set([...(top ?? []), ...(bottom ?? [])])\n        return allRows.filter(d => !topAndBottom.has(d.id))\n      },\n      getMemoOptions(table.options, 'debugRows', 'getCenterRows')\n    )\n  },\n}\n"], "names": ["getDefaultRowPinningState", "top", "bottom", "RowPinning", "getInitialState", "state", "rowPinning", "getDefaultOptions", "table", "onRowPinningChange", "makeStateUpdater", "createRow", "row", "pin", "position", "includeLeafRows", "includeParentRows", "leafRowIds", "getLeafRows", "map", "_ref", "id", "parentRowIds", "getParentRows", "_ref2", "rowIds", "Set", "setRowPinning", "old", "_old$top3", "_old$bottom3", "_old$top", "_old$bottom", "filter", "d", "has", "Array", "from", "_old$top2", "_old$bottom2", "getCanPin", "_ref3", "enableRowPinning", "enablePinning", "options", "getIsPinned", "getState", "isTop", "some", "includes", "isBottom", "getPinnedIndex", "_ref4", "_visiblePinnedRowIds$", "visiblePinnedRowIds", "getTopRows", "getBottomRows", "_ref5", "indexOf", "createTable", "updater", "resetRowPinning", "defaultState", "_table$initialState$r", "_table$initialState", "initialState", "getIsSomeRowsPinned", "_pinningState$positio", "pinningState", "_pinningState$top", "_pinningState$bottom", "Boolean", "length", "_getPinnedRows", "visibleRows", "pinnedRowIds", "_table$options$keepPi", "rows", "keepPinnedRows", "rowId", "getRow", "getIsAllParentsExpanded", "find", "memo", "getRowModel", "allRows", "topPinnedRowIds", "getMemoOptions", "bottomPinnedRowIds", "getCenterRows", "topAndBottom"], "mappings": ";;;;;;;;;;;;;;AAyHA;;AAEA,MAAMA,yBAAyB,GAAGA,OAAwB;AACxDC,EAAAA,GAAG,EAAE,EAAE;AACPC,EAAAA,MAAM,EAAE,EAAA;AACV,CAAC,CAAC,CAAA;AAEK,MAAMC,UAAwB,GAAG;EACtCC,eAAe,EAAGC,KAAK,IAA2B;IAChD,OAAO;MACLC,UAAU,EAAEN,yBAAyB,EAAE;MACvC,GAAGK,KAAAA;KACJ,CAAA;GACF;EAEDE,iBAAiB,EACfC,KAAmB,IACU;IAC7B,OAAO;AACLC,MAAAA,kBAAkB,EAAEC,sBAAgB,CAAC,YAAY,EAAEF,KAAK,CAAA;KACzD,CAAA;GACF;AAEDG,EAAAA,SAAS,EAAEA,CACTC,GAAe,EACfJ,KAAmB,KACV;IACTI,GAAG,CAACC,GAAG,GAAG,CAACC,QAAQ,EAAEC,eAAe,EAAEC,iBAAiB,KAAK;AAC1D,MAAA,MAAMC,UAAU,GAAGF,eAAe,GAC9BH,GAAG,CAACM,WAAW,EAAE,CAACC,GAAG,CAACC,IAAA,IAAA;QAAA,IAAC;AAAEC,UAAAA,EAAAA;AAAG,SAAC,GAAAD,IAAA,CAAA;AAAA,QAAA,OAAKC,EAAE,CAAA;AAAA,OAAA,CAAC,GACrC,EAAE,CAAA;AACN,MAAA,MAAMC,YAAY,GAAGN,iBAAiB,GAClCJ,GAAG,CAACW,aAAa,EAAE,CAACJ,GAAG,CAACK,KAAA,IAAA;QAAA,IAAC;AAAEH,UAAAA,EAAAA;AAAG,SAAC,GAAAG,KAAA,CAAA;AAAA,QAAA,OAAKH,EAAE,CAAA;AAAA,OAAA,CAAC,GACvC,EAAE,CAAA;AACN,MAAA,MAAMI,MAAM,GAAG,IAAIC,GAAG,CAAC,CAAC,GAAGJ,YAAY,EAAEV,GAAG,CAACS,EAAE,EAAE,GAAGJ,UAAU,CAAC,CAAC,CAAA;AAEhET,MAAAA,KAAK,CAACmB,aAAa,CAACC,GAAG,IAAI;QAAA,IAAAC,SAAA,EAAAC,YAAA,CAAA;QACzB,IAAIhB,QAAQ,KAAK,QAAQ,EAAE;UAAA,IAAAiB,QAAA,EAAAC,WAAA,CAAA;UACzB,OAAO;YACL/B,GAAG,EAAE,CAAA8B,CAAAA,QAAA,GAACH,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAE3B,GAAG,KAAA8B,IAAAA,GAAAA,QAAA,GAAI,EAAE,EAAEE,MAAM,CAACC,CAAC,IAAI,EAACT,MAAM,IAAA,IAAA,IAANA,MAAM,CAAEU,GAAG,CAACD,CAAC,CAAC,CAAC,CAAA;AAClDhC,YAAAA,MAAM,EAAE,CACN,GAAG,CAAA8B,CAAAA,WAAA,GAACJ,GAAG,IAAA,IAAA,GAAA,KAAA,CAAA,GAAHA,GAAG,CAAE1B,MAAM,KAAA8B,IAAAA,GAAAA,WAAA,GAAI,EAAE,EAAEC,MAAM,CAACC,CAAC,IAAI,EAACT,MAAM,IAAA,IAAA,IAANA,MAAM,CAAEU,GAAG,CAACD,CAAC,CAAC,CAAA,CAAC,EACnD,GAAGE,KAAK,CAACC,IAAI,CAACZ,MAAM,CAAC,CAAA;WAExB,CAAA;AACH,SAAA;QAEA,IAAIX,QAAQ,KAAK,KAAK,EAAE;UAAA,IAAAwB,SAAA,EAAAC,YAAA,CAAA;UACtB,OAAO;AACLtC,YAAAA,GAAG,EAAE,CACH,GAAG,CAAAqC,CAAAA,SAAA,GAACV,GAAG,IAAA,IAAA,GAAA,KAAA,CAAA,GAAHA,GAAG,CAAE3B,GAAG,KAAAqC,IAAAA,GAAAA,SAAA,GAAI,EAAE,EAAEL,MAAM,CAACC,CAAC,IAAI,EAACT,MAAM,IAANA,IAAAA,IAAAA,MAAM,CAAEU,GAAG,CAACD,CAAC,CAAC,CAAC,CAAA,EAChD,GAAGE,KAAK,CAACC,IAAI,CAACZ,MAAM,CAAC,CACtB;YACDvB,MAAM,EAAE,CAAAqC,CAAAA,YAAA,GAACX,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAE1B,MAAM,KAAAqC,IAAAA,GAAAA,YAAA,GAAI,EAAE,EAAEN,MAAM,CAACC,CAAC,IAAI,EAACT,MAAM,IAANA,IAAAA,IAAAA,MAAM,CAAEU,GAAG,CAACD,CAAC,CAAC,CAAA,CAAA;WACxD,CAAA;AACH,SAAA;QAEA,OAAO;UACLjC,GAAG,EAAE,CAAA4B,CAAAA,SAAA,GAACD,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAE3B,GAAG,KAAA4B,IAAAA,GAAAA,SAAA,GAAI,EAAE,EAAEI,MAAM,CAACC,CAAC,IAAI,EAACT,MAAM,IAAA,IAAA,IAANA,MAAM,CAAEU,GAAG,CAACD,CAAC,CAAC,CAAC,CAAA;UAClDhC,MAAM,EAAE,CAAA4B,CAAAA,YAAA,GAACF,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAE1B,MAAM,KAAA4B,IAAAA,GAAAA,YAAA,GAAI,EAAE,EAAEG,MAAM,CAACC,CAAC,IAAI,EAACT,MAAM,IAANA,IAAAA,IAAAA,MAAM,CAAEU,GAAG,CAACD,CAAC,CAAC,CAAA,CAAA;SACxD,CAAA;AACH,OAAC,CAAC,CAAA;KACH,CAAA;IACDtB,GAAG,CAAC4B,SAAS,GAAG,MAAM;AAAA,MAAA,IAAAC,KAAA,CAAA;MACpB,MAAM;QAAEC,gBAAgB;AAAEC,QAAAA,aAAAA;OAAe,GAAGnC,KAAK,CAACoC,OAAO,CAAA;AACzD,MAAA,IAAI,OAAOF,gBAAgB,KAAK,UAAU,EAAE;QAC1C,OAAOA,gBAAgB,CAAC9B,GAAG,CAAC,CAAA;AAC9B,OAAA;MACA,OAAA6B,CAAAA,KAAA,GAAOC,gBAAgB,IAAhBA,IAAAA,GAAAA,gBAAgB,GAAIC,aAAa,KAAA,IAAA,GAAAF,KAAA,GAAI,IAAI,CAAA;KACjD,CAAA;IACD7B,GAAG,CAACiC,WAAW,GAAG,MAAM;AACtB,MAAA,MAAMpB,MAAM,GAAG,CAACb,GAAG,CAACS,EAAE,CAAC,CAAA;MAEvB,MAAM;QAAEpB,GAAG;AAAEC,QAAAA,MAAAA;AAAO,OAAC,GAAGM,KAAK,CAACsC,QAAQ,EAAE,CAACxC,UAAU,CAAA;AAEnD,MAAA,MAAMyC,KAAK,GAAGtB,MAAM,CAACuB,IAAI,CAACd,CAAC,IAAIjC,GAAG,IAAA,IAAA,GAAA,KAAA,CAAA,GAAHA,GAAG,CAAEgD,QAAQ,CAACf,CAAC,CAAC,CAAC,CAAA;AAChD,MAAA,MAAMgB,QAAQ,GAAGzB,MAAM,CAACuB,IAAI,CAACd,CAAC,IAAIhC,MAAM,IAAA,IAAA,GAAA,KAAA,CAAA,GAANA,MAAM,CAAE+C,QAAQ,CAACf,CAAC,CAAC,CAAC,CAAA;MAEtD,OAAOa,KAAK,GAAG,KAAK,GAAGG,QAAQ,GAAG,QAAQ,GAAG,KAAK,CAAA;KACnD,CAAA;IACDtC,GAAG,CAACuC,cAAc,GAAG,MAAM;MAAA,IAAAC,KAAA,EAAAC,qBAAA,CAAA;AACzB,MAAA,MAAMvC,QAAQ,GAAGF,GAAG,CAACiC,WAAW,EAAE,CAAA;AAClC,MAAA,IAAI,CAAC/B,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAA;MAExB,MAAMwC,mBAAmB,GAAAF,CAAAA,KAAA,GACvBtC,QAAQ,KAAK,KAAK,GAAGN,KAAK,CAAC+C,UAAU,EAAE,GAAG/C,KAAK,CAACgD,aAAa,EAAE,qBADrCJ,KAAA,CAEzBjC,GAAG,CAACsC,KAAA,IAAA;QAAA,IAAC;AAAEpC,UAAAA,EAAAA;AAAG,SAAC,GAAAoC,KAAA,CAAA;AAAA,QAAA,OAAKpC,EAAE,CAAA;OAAC,CAAA,CAAA;AAEtB,MAAA,OAAA,CAAAgC,qBAAA,GAAOC,mBAAmB,IAAnBA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,mBAAmB,CAAEI,OAAO,CAAC9C,GAAG,CAACS,EAAE,CAAC,KAAA,IAAA,GAAAgC,qBAAA,GAAI,CAAC,CAAC,CAAA;KAClD,CAAA;GACF;EAEDM,WAAW,EAA0BnD,KAAmB,IAAW;AACjEA,IAAAA,KAAK,CAACmB,aAAa,GAAGiC,OAAO,IAAIpD,KAAK,CAACoC,OAAO,CAACnC,kBAAkB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAhCD,KAAK,CAACoC,OAAO,CAACnC,kBAAkB,CAAGmD,OAAO,CAAC,CAAA;IAE5EpD,KAAK,CAACqD,eAAe,GAAGC,YAAY,IAAA;MAAA,IAAAC,qBAAA,EAAAC,mBAAA,CAAA;MAAA,OAClCxD,KAAK,CAACmB,aAAa,CACjBmC,YAAY,GACR9D,yBAAyB,EAAE,GAAA+D,CAAAA,qBAAA,GAAAC,CAAAA,mBAAA,GAC3BxD,KAAK,CAACyD,YAAY,KAAlBD,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,mBAAA,CAAoB1D,UAAU,KAAAyD,IAAAA,GAAAA,qBAAA,GAAI/D,yBAAyB,EACjE,CAAC,CAAA;AAAA,KAAA,CAAA;AAEHQ,IAAAA,KAAK,CAAC0D,mBAAmB,GAAGpD,QAAQ,IAAI;AAAA,MAAA,IAAAqD,qBAAA,CAAA;MACtC,MAAMC,YAAY,GAAG5D,KAAK,CAACsC,QAAQ,EAAE,CAACxC,UAAU,CAAA;MAEhD,IAAI,CAACQ,QAAQ,EAAE;QAAA,IAAAuD,iBAAA,EAAAC,oBAAA,CAAA;QACb,OAAOC,OAAO,CAAC,CAAAF,CAAAA,iBAAA,GAAAD,YAAY,CAACnE,GAAG,KAAA,IAAA,GAAA,KAAA,CAAA,GAAhBoE,iBAAA,CAAkBG,MAAM,MAAAF,CAAAA,oBAAA,GAAIF,YAAY,CAAClE,MAAM,KAAnBoE,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,oBAAA,CAAqBE,MAAM,CAAC,CAAA,CAAA;AACzE,OAAA;AACA,MAAA,OAAOD,OAAO,CAAA,CAAAJ,qBAAA,GAACC,YAAY,CAACtD,QAAQ,CAAC,KAAtBqD,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,qBAAA,CAAwBK,MAAM,CAAC,CAAA;KAC/C,CAAA;IAEDhE,KAAK,CAACiE,cAAc,GAAG,CAACC,WAAW,EAAEC,YAAY,EAAE7D,QAAQ,KAAK;AAAA,MAAA,IAAA8D,qBAAA,CAAA;AAC9D,MAAA,MAAMC,IAAI,GACR,CAAAD,CAAAA,qBAAA,GAAApE,KAAK,CAACoC,OAAO,CAACkC,cAAc,KAAAF,IAAAA,GAAAA,qBAAA,GAAI,IAAI;AAChC;AACA;MACA,CAACD,YAAY,WAAZA,YAAY,GAAI,EAAE,EAAExD,GAAG,CAAC4D,KAAK,IAAI;QAChC,MAAMnE,GAAG,GAAGJ,KAAK,CAACwE,MAAM,CAACD,KAAK,EAAE,IAAI,CAAC,CAAA;QACrC,OAAOnE,GAAG,CAACqE,uBAAuB,EAAE,GAAGrE,GAAG,GAAG,IAAI,CAAA;AACnD,OAAC,CAAC;AACF;MACA,CAAC+D,YAAY,WAAZA,YAAY,GAAI,EAAE,EAAExD,GAAG,CACtB4D,KAAK,IAAIL,WAAW,CAACQ,IAAI,CAACtE,GAAG,IAAIA,GAAG,CAACS,EAAE,KAAK0D,KAAK,CACnD,CAAC,CAAA;MAEP,OAAOF,IAAI,CAAC5C,MAAM,CAACsC,OAAO,CAAC,CAACpD,GAAG,CAACe,CAAC,KAAK;AAAE,QAAA,GAAGA,CAAC;AAAEpB,QAAAA,QAAAA;AAAS,OAAC,CAAC,CAAC,CAAA;KAC3D,CAAA;IAEDN,KAAK,CAAC+C,UAAU,GAAG4B,UAAI,CACrB,MAAM,CAAC3E,KAAK,CAAC4E,WAAW,EAAE,CAACP,IAAI,EAAErE,KAAK,CAACsC,QAAQ,EAAE,CAACxC,UAAU,CAACL,GAAG,CAAC,EACjE,CAACoF,OAAO,EAAEC,eAAe,KACvB9E,KAAK,CAACiE,cAAc,CAACY,OAAO,EAAEC,eAAe,EAAE,KAAK,CAAC,EACvDC,oBAAc,CAAC/E,KAAK,CAACoC,OAAO,EAAE,WAAW,EAAE,YAAY,CACzD,CAAC,CAAA;IAEDpC,KAAK,CAACgD,aAAa,GAAG2B,UAAI,CACxB,MAAM,CAAC3E,KAAK,CAAC4E,WAAW,EAAE,CAACP,IAAI,EAAErE,KAAK,CAACsC,QAAQ,EAAE,CAACxC,UAAU,CAACJ,MAAM,CAAC,EACpE,CAACmF,OAAO,EAAEG,kBAAkB,KAC1BhF,KAAK,CAACiE,cAAc,CAACY,OAAO,EAAEG,kBAAkB,EAAE,QAAQ,CAAC,EAC7DD,oBAAc,CAAC/E,KAAK,CAACoC,OAAO,EAAE,WAAW,EAAE,eAAe,CAC5D,CAAC,CAAA;AAEDpC,IAAAA,KAAK,CAACiF,aAAa,GAAGN,UAAI,CACxB,MAAM,CACJ3E,KAAK,CAAC4E,WAAW,EAAE,CAACP,IAAI,EACxBrE,KAAK,CAACsC,QAAQ,EAAE,CAACxC,UAAU,CAACL,GAAG,EAC/BO,KAAK,CAACsC,QAAQ,EAAE,CAACxC,UAAU,CAACJ,MAAM,CACnC,EACD,CAACmF,OAAO,EAAEpF,GAAG,EAAEC,MAAM,KAAK;MACxB,MAAMwF,YAAY,GAAG,IAAIhE,GAAG,CAAC,CAAC,IAAIzB,GAAG,IAAA,IAAA,GAAHA,GAAG,GAAI,EAAE,GAAG,IAAIC,MAAM,IAAA,IAAA,GAANA,MAAM,GAAI,EAAE,EAAE,CAAC,CAAA;AACjE,MAAA,OAAOmF,OAAO,CAACpD,MAAM,CAACC,CAAC,IAAI,CAACwD,YAAY,CAACvD,GAAG,CAACD,CAAC,CAACb,EAAE,CAAC,CAAC,CAAA;KACpD,EACDkE,oBAAc,CAAC/E,KAAK,CAACoC,OAAO,EAAE,WAAW,EAAE,eAAe,CAC5D,CAAC,CAAA;AACH,GAAA;AACF;;;;"}