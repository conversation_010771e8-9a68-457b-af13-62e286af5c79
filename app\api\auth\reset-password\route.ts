import { NextRequest, NextResponse } from 'next/server'
import bcrypt from 'bcryptjs'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const resetPasswordSchema = z.object({
  token: z.string().min(1, 'Token is required'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one uppercase letter, one lowercase letter, and one number')
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { token, password } = resetPasswordSchema.parse(body)

    // Find password reset record with this token
    const passwordReset = await prisma.passwordReset.findFirst({
      where: {
        token,
        expiresAt: {
          gt: new Date() // Token must not be expired
        },
        usedAt: null // Token must not have been used
      },
      include: {
        user: true
      }
    })

    if (!passwordReset) {
      return NextResponse.json(
        { error: 'Invalid or expired reset token' },
        { status: 400 }
      )
    }

    // Hash the new password
    const hashedPassword = await bcrypt.hash(password, 12)

    // Update user password and mark reset token as used
    await prisma.$transaction(async (tx) => {
      // Update user password
      await tx.user.update({
        where: { id: passwordReset.userId },
        data: {
          password: hashedPassword,
          passwordChangedAt: new Date()
        }
      })

      // Mark reset token as used
      await tx.passwordReset.update({
        where: { id: passwordReset.id },
        data: {
          usedAt: new Date()
        }
      })

      // Invalidate all other reset tokens for this user
      await tx.passwordReset.updateMany({
        where: {
          userId: passwordReset.userId,
          id: { not: passwordReset.id },
          usedAt: null
        },
        data: {
          usedAt: new Date() // Mark as used to invalidate
        }
      })
    })

    // Log the password reset event
    console.log(`Password reset completed for user: ${passwordReset.user.email}`)

    return NextResponse.json({
      message: 'Password reset successfully'
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Reset password error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
