(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9299],{90998:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62898).Z)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]])},72894:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62898).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},13008:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62898).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6141:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62898).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},55458:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62898).Z)("Cpu",[["rect",{x:"4",y:"4",width:"16",height:"16",rx:"2",key:"1vbyd7"}],["rect",{x:"9",y:"9",width:"6",height:"6",key:"o3kz5p"}],["path",{d:"M15 2v2",key:"13l42r"}],["path",{d:"M15 20v2",key:"15mkzm"}],["path",{d:"M2 15h2",key:"1gxd5l"}],["path",{d:"M2 9h2",key:"1bbxkp"}],["path",{d:"M20 15h2",key:"19e6y8"}],["path",{d:"M20 9h2",key:"19tzq7"}],["path",{d:"M9 2v2",key:"165o2o"}],["path",{d:"M9 20v2",key:"i2bqo8"}]])},97332:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62898).Z)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},64280:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62898).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},26714:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62898).Z)("Server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]])},82104:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62898).Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},8319:function(e,t,s){Promise.resolve().then(s.bind(s,60884))},60884:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return N}});var r=s(57437),a=s(2265),n=s(82749),l=s(24033),i=s(27815),c=s(85754),d=s(31478),o=s(10500),u=s(13008),m=s(72894),x=s(82104),h=s(90998),f=s(6141),v=s(64280),p=s(55458);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let y=(0,s(62898).Z)("MemoryStick",[["path",{d:"M6 19v-3",key:"1nvgqn"}],["path",{d:"M10 19v-3",key:"iu8nkm"}],["path",{d:"M14 19v-3",key:"kcehxu"}],["path",{d:"M18 19v-3",key:"1vh91z"}],["path",{d:"M8 11V9",key:"63erz4"}],["path",{d:"M16 11V9",key:"fru6f3"}],["path",{d:"M12 11V9",key:"ha00sb"}],["path",{d:"M2 15h20",key:"16ne18"}],["path",{d:"M2 7a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v1.1a2 2 0 0 0 0 3.837V17a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-5.1a2 2 0 0 0 0-3.837Z",key:"lhddv3"}]]);var g=s(97332),j=s(26714);function N(){var e,t,s,N,b,w,k,M,Z,C,A,R,z,P,S,E,$,V,U,_,D,L;let{data:O,status:T}=(0,n.useSession)(),[B,q]=(0,a.useState)(null),[F,I]=(0,a.useState)(!0),[Y,H]=(0,a.useState)(null);if("loading"===T)return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})});"unauthenticated"===T&&(0,l.redirect)("/auth/signin"),(null==O?void 0:null===(e=O.user)||void 0===e?void 0:e.role)!=="SUPER_ADMIN"&&(0,l.redirect)("/dashboard");let W=async()=>{try{I(!0);let e=await fetch("/api/super-admin/system-health");if(!e.ok)throw Error("Failed to fetch system health");let t=await e.json();q(t),H(new Date)}catch(e){console.error("Error fetching system health:",e)}finally{I(!1)}};(0,a.useEffect)(()=>{W();let e=setInterval(W,3e4);return()=>clearInterval(e)},[]);let G=e=>{let t={healthy:{variant:"default",icon:u.Z,color:"text-green-600"},warning:{variant:"secondary",icon:m.Z,color:"text-yellow-600"},critical:{variant:"destructive",icon:x.Z,color:"text-red-600"},unhealthy:{variant:"destructive",icon:x.Z,color:"text-red-600"}},s=t[e]||t.unhealthy,a=s.icon;return(0,r.jsxs)(d.C,{variant:s.variant,className:"flex items-center space-x-1",children:[(0,r.jsx)(a,{className:"h-3 w-3"}),(0,r.jsx)("span",{children:e.toUpperCase()})]})},X=e=>{if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB","TB"][t]},K=e=>(0,r.jsx)(d.C,{variant:{ERROR:"destructive",WARN:"secondary",INFO:"default",DEBUG:"outline"}[e]||"default",children:e});return B?(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(h.Z,{className:"h-8 w-8 text-blue-600"}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"System Health"}),G(B.status)]}),(0,r.jsx)("p",{className:"text-gray-500 mt-1",children:"Real-time system monitoring and diagnostics"}),Y&&(0,r.jsxs)("div",{className:"flex items-center space-x-1 mt-2 text-sm text-gray-500",children:[(0,r.jsx)(f.Z,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:["Last updated: ",Y.toLocaleTimeString()]})]})]}),(0,r.jsxs)(c.z,{onClick:W,disabled:F,children:[(0,r.jsx)(v.Z,{className:"h-4 w-4 mr-2 ".concat(F?"animate-spin":"")}),"Refresh"]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,r.jsx)(i.Zb,{children:(0,r.jsxs)(i.aY,{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"CPU Usage"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[(null==B?void 0:null===(t=B.metrics)||void 0===t?void 0:t.memoryUsage)?B.metrics.memoryUsage.toFixed(1):"0.0","%"]})]}),(0,r.jsx)(p.Z,{className:"h-8 w-8 text-blue-600"})]}),(0,r.jsx)(o.E,{value:(null==B?void 0:null===(s=B.metrics)||void 0===s?void 0:s.memoryUsage)||0,className:"mt-3"})]})}),(0,r.jsx)(i.Zb,{children:(0,r.jsxs)(i.aY,{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Memory Usage"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[(null==B?void 0:null===(b=B.system)||void 0===b?void 0:null===(N=b.memory)||void 0===N?void 0:N.usagePercent)?B.system.memory.usagePercent.toFixed(1):"0.0","%"]})]}),(0,r.jsx)(y,{className:"h-8 w-8 text-green-600"})]}),(0,r.jsx)(o.E,{value:(null==B?void 0:null===(k=B.system)||void 0===k?void 0:null===(w=k.memory)||void 0===w?void 0:w.usagePercent)||0,className:"mt-3"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:(null==B?void 0:null===(M=B.system)||void 0===M?void 0:M.memory)?"".concat(X(B.system.memory.used)," / ").concat(X(B.system.memory.total)):"N/A"})]})}),(0,r.jsx)(i.Zb,{children:(0,r.jsxs)(i.aY,{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Database Response"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[(null==B?void 0:null===(Z=B.database)||void 0===Z?void 0:Z.responseTime)||0,"ms"]})]}),(0,r.jsx)(g.Z,{className:"h-8 w-8 text-purple-600"})]}),(0,r.jsx)("div",{className:"mt-3",children:G((null==B?void 0:null===(C=B.database)||void 0===C?void 0:C.status)||"unknown")})]})}),(0,r.jsx)(i.Zb,{children:(0,r.jsxs)(i.aY,{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"System Uptime"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==B?void 0:null===(A=B.system)||void 0===A?void 0:A.uptime)?(e=>{let t=Math.floor(e/86400),s=Math.floor(e%86400/3600),r=Math.floor(e%3600/60);return t>0?"".concat(t,"d ").concat(s,"h ").concat(r,"m"):s>0?"".concat(s,"h ").concat(r,"m"):"".concat(r,"m")})(B.system.uptime):"N/A"})]}),(0,r.jsx)(j.Z,{className:"h-8 w-8 text-orange-600"})]}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-3",children:(null==B?void 0:B.system)?"".concat(B.system.platform," ").concat(B.system.arch):"N/A"})]})})]}),(0,r.jsxs)(i.Zb,{children:[(0,r.jsx)(i.Ol,{children:(0,r.jsxs)(i.ll,{className:"flex items-center space-x-2",children:[(0,r.jsx)(g.Z,{className:"h-5 w-5"}),(0,r.jsx)("span",{children:"Database Metrics"})]})}),(0,r.jsx)(i.aY,{children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:(null==B?void 0:null===(R=B.database)||void 0===R?void 0:R.totalUsers)||0}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Total Users"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-2xl font-bold text-green-600",children:(null==B?void 0:null===(z=B.database)||void 0===z?void 0:z.totalCompanies)||0}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Total Companies"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-2xl font-bold text-purple-600",children:(null==B?void 0:null===(P=B.database)||void 0===P?void 0:P.totalActivities)||0}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Total Activities"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-2xl font-bold text-red-600",children:(null==B?void 0:null===(S=B.database)||void 0===S?void 0:S.recentErrors)||0}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Recent Errors (24h)"})]})]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)(i.Zb,{children:[(0,r.jsx)(i.Ol,{children:(0,r.jsx)(i.ll,{children:"System Information"})}),(0,r.jsx)(i.aY,{children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-500",children:"Platform:"}),(0,r.jsx)("span",{className:"font-medium",children:(null==B?void 0:null===(E=B.system)||void 0===E?void 0:E.platform)||"N/A"})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-500",children:"Architecture:"}),(0,r.jsx)("span",{className:"font-medium",children:(null==B?void 0:null===($=B.system)||void 0===$?void 0:$.arch)||"N/A"})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-500",children:"Node.js Version:"}),(0,r.jsx)("span",{className:"font-medium",children:(null==B?void 0:null===(V=B.system)||void 0===V?void 0:V.nodeVersion)||"N/A"})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-500",children:"CPU Cores:"}),(0,r.jsx)("span",{className:"font-medium",children:(null==B?void 0:null===(U=B.system)||void 0===U?void 0:U.cpuCount)||0})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-500",children:"Load Average:"}),(0,r.jsx)("span",{className:"font-medium",children:(null==B?void 0:null===(_=B.system)||void 0===_?void 0:_.loadAverage)?B.system.loadAverage.map(e=>e.toFixed(2)).join(", "):"N/A"})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-500",children:"Database Size:"}),(0,r.jsxs)("span",{className:"font-medium",children:[(null==B?void 0:null===(D=B.database)||void 0===D?void 0:D.size)||0," MB"]})]})]})})]}),(0,r.jsxs)(i.Zb,{children:[(0,r.jsx)(i.Ol,{children:(0,r.jsx)(i.ll,{children:"Recent System Logs"})}),(0,r.jsx)(i.aY,{children:(0,r.jsx)("div",{className:"space-y-3 max-h-64 overflow-y-auto",children:(null==B?void 0:null===(L=B.recentLogs)||void 0===L?void 0:L.length)>0?B.recentLogs.map(e=>(0,r.jsxs)("div",{className:"flex items-start space-x-3 p-3 bg-gray-50 rounded-lg",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:K(e.level)}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",children:e.message}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,r.jsx)("span",{className:"text-xs text-gray-500",children:e.source}),e.category&&(0,r.jsxs)("span",{className:"text-xs text-gray-400",children:["• ",e.category]}),(0,r.jsxs)("span",{className:"text-xs text-gray-400",children:["• ",new Date(e.createdAt).toLocaleTimeString()]})]})]})]},e.id)):(0,r.jsx)("div",{className:"text-center py-8 text-gray-500",children:(0,r.jsx)("p",{children:"No recent logs available"})})})})]})]})]}):(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})})}},31478:function(e,t,s){"use strict";s.d(t,{C:function(){return i}});var r=s(57437);s(2265);var a=s(96061),n=s(1657);let l=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600",info:"border-transparent bg-blue-500 text-white hover:bg-blue-600"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:s,...a}=e;return(0,r.jsx)("div",{className:(0,n.cn)(l({variant:s}),t),...a})}},85754:function(e,t,s){"use strict";s.d(t,{z:function(){return d}});var r=s(57437),a=s(2265),n=s(67256),l=s(96061),i=s(1657);let c=(0,l.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,t)=>{let{className:s,variant:a,size:l,asChild:d=!1,...o}=e,u=d?n.g7:"button";return(0,r.jsx)(u,{className:(0,i.cn)(c({variant:a,size:l,className:s})),ref:t,...o})});d.displayName="Button"},27815:function(e,t,s){"use strict";s.d(t,{Ol:function(){return i},SZ:function(){return d},Zb:function(){return l},aY:function(){return o},eW:function(){return u},ll:function(){return c}});var r=s(57437),a=s(2265),n=s(1657);let l=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...a})});l.displayName="Card";let i=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",s),...a})});i.displayName="CardHeader";let c=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",s),...a})});c.displayName="CardTitle";let d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",s),...a})});d.displayName="CardDescription";let o=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",s),...a})});o.displayName="CardContent";let u=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",s),...a})});u.displayName="CardFooter"},10500:function(e,t,s){"use strict";s.d(t,{E:function(){return i}});var r=s(57437),a=s(2265),n=s(23177),l=s(1657);let i=a.forwardRef((e,t)=>{let{className:s,value:a,...i}=e;return(0,r.jsx)(n.fC,{ref:t,className:(0,l.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",s),...i,children:(0,r.jsx)(n.z$,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(a||0),"%)")}})})});i.displayName=n.fC.displayName},1657:function(e,t,s){"use strict";s.d(t,{cn:function(){return n}});var r=s(57042),a=s(74769);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.m6)((0,r.W)(t))}},24033:function(e,t,s){e.exports=s(15313)},56989:function(e,t,s){"use strict";s.d(t,{b:function(){return l},k:function(){return n}});var r=s(2265),a=s(57437);function n(e,t){let s=r.createContext(t),n=e=>{let{children:t,...n}=e,l=r.useMemo(()=>n,Object.values(n));return(0,a.jsx)(s.Provider,{value:l,children:t})};return n.displayName=e+"Provider",[n,function(a){let n=r.useContext(s);if(n)return n;if(void 0!==t)return t;throw Error(`\`${a}\` must be used within \`${e}\``)}]}function l(e,t=[]){let s=[],n=()=>{let t=s.map(e=>r.createContext(e));return function(s){let a=s?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...s,[e]:a}}),[s,a])}};return n.scopeName=e,[function(t,n){let l=r.createContext(n),i=s.length;s=[...s,n];let c=t=>{let{scope:s,children:n,...c}=t,d=s?.[e]?.[i]||l,o=r.useMemo(()=>c,Object.values(c));return(0,a.jsx)(d.Provider,{value:o,children:n})};return c.displayName=t+"Provider",[c,function(s,a){let c=a?.[e]?.[i]||l,d=r.useContext(c);if(d)return d;if(void 0!==n)return n;throw Error(`\`${s}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let s=()=>{let s=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let a=s.reduce((t,{useScope:s,scopeName:r})=>{let a=s(e)[`__scope${r}`];return{...t,...a}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:a}),[a])}};return s.scopeName=t.scopeName,s}(n,...t)]}},9381:function(e,t,s){"use strict";s.d(t,{WV:function(){return i},jH:function(){return c}});var r=s(2265),a=s(54887),n=s(67256),l=s(57437),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let s=(0,n.Z8)(`Primitive.${t}`),a=r.forwardRef((e,r)=>{let{asChild:a,...n}=e,i=a?s:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(i,{...n,ref:r})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{});function c(e,t){e&&a.flushSync(()=>e.dispatchEvent(t))}},23177:function(e,t,s){"use strict";s.d(t,{fC:function(){return j},z$:function(){return N}});var r=s(2265),a=s(56989),n=s(9381),l=s(57437),i="Progress",[c,d]=(0,a.b)(i),[o,u]=c(i),m=r.forwardRef((e,t)=>{var s,r;let{__scopeProgress:a,value:i=null,max:c,getValueLabel:d=f,...u}=e;(c||0===c)&&!y(c)&&console.error((s=`${c}`,`Invalid prop \`max\` of value \`${s}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let m=y(c)?c:100;null===i||g(i,m)||console.error((r=`${i}`,`Invalid prop \`value\` of value \`${r}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let x=g(i,m)?i:null,h=p(x)?d(x,m):void 0;return(0,l.jsx)(o,{scope:a,value:x,max:m,children:(0,l.jsx)(n.WV.div,{"aria-valuemax":m,"aria-valuemin":0,"aria-valuenow":p(x)?x:void 0,"aria-valuetext":h,role:"progressbar","data-state":v(x,m),"data-value":x??void 0,"data-max":m,...u,ref:t})})});m.displayName=i;var x="ProgressIndicator",h=r.forwardRef((e,t)=>{let{__scopeProgress:s,...r}=e,a=u(x,s);return(0,l.jsx)(n.WV.div,{"data-state":v(a.value,a.max),"data-value":a.value??void 0,"data-max":a.max,...r,ref:t})});function f(e,t){return`${Math.round(e/t*100)}%`}function v(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function p(e){return"number"==typeof e}function y(e){return p(e)&&!isNaN(e)&&e>0}function g(e,t){return p(e)&&!isNaN(e)&&e<=t&&e>=0}h.displayName=x;var j=m,N=h}},function(e){e.O(0,[6723,2749,2971,4938,1744],function(){return e(e.s=8319)}),_N_E=e.O()}]);