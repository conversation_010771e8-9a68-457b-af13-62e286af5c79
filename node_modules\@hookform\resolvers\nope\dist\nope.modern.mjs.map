{"version": 3, "file": "nope.modern.mjs", "sources": ["../src/nope.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport type { ShapeErrors } from 'nope-validator/lib/cjs/types';\nimport type { FieldError, FieldErrors } from 'react-hook-form';\nimport type { Resolver } from './types';\n\nconst parseErrors = (\n  errors: ShapeErrors,\n  parsedErrors: FieldErrors = {},\n  path = '',\n) => {\n  return Object.keys(errors).reduce((acc, key) => {\n    const _path = path ? `${path}.${key}` : key;\n    const error = errors[key];\n\n    if (typeof error === 'string') {\n      acc[_path] = {\n        message: error,\n      } as FieldError;\n    } else {\n      parseErrors(error, acc, _path);\n    }\n\n    return acc;\n  }, parsedErrors);\n};\n\n/**\n * Creates a resolver for react-hook-form using Nope schema validation\n * @param {NopeSchema} schema - The Nope schema to validate against\n * @param {NopeSchemaOptions} [schemaOptions] - Optional Nope validation options\n * @param {Object} resolverOptions - Additional resolver configuration\n * @param {string} [resolverOptions.mode='async'] - Validation mode\n * @returns {Resolver<NopeSchema>} A resolver function compatible with react-hook-form\n * @example\n * const schema = nope.object({\n *   name: nope.string().required(),\n *   age: nope.number().required()\n * });\n *\n * useForm({\n *   resolver: nopeResolver(schema)\n * });\n */\nexport const nopeResolver: Resolver =\n  (\n    schema,\n    schemaOptions = {\n      abortEarly: false,\n    },\n  ) =>\n  (values, context, options) => {\n    const result = schema.validate(values, context, schemaOptions) as\n      | ShapeErrors\n      | undefined;\n\n    if (result) {\n      return { values: {}, errors: toNestErrors(parseErrors(result), options) };\n    }\n\n    options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n    return { values, errors: {} };\n  };\n"], "names": ["parseErrors", "errors", "parsedErrors", "path", "Object", "keys", "reduce", "acc", "key", "_path", "error", "message", "nopeResolver", "schema", "schemaOptions", "abort<PERSON><PERSON><PERSON>", "values", "context", "options", "result", "validate", "toNestErrors", "shouldUseNativeValidation", "validateFieldsNatively"], "mappings": "+EAKA,MAAMA,EAAcA,CAClBC,EACAC,EAA4B,CAAA,EAC5BC,EAAO,KAEAC,OAAOC,KAAKJ,GAAQK,OAAO,CAACC,EAAKC,KACtC,MAAMC,EAAQN,EAAO,GAAGA,KAAQK,IAAQA,EAClCE,EAAQT,EAAOO,GAUrB,MARqB,iBAAVE,EACTH,EAAIE,GAAS,CACXE,QAASD,GAGXV,EAAYU,EAAOH,EAAKE,GAGnBF,GACNL,GAoBQU,EACXA,CACEC,EACAC,EAAgB,CACdC,YAAY,KAGhB,CAACC,EAAQC,EAASC,KAChB,MAAMC,EAASN,EAAOO,SAASJ,EAAQC,EAASH,GAIhD,OAAIK,EACK,CAAEH,OAAQ,CAAE,EAAEf,OAAQoB,EAAarB,EAAYmB,GAASD,KAGjEA,EAAQI,2BAA6BC,EAAuB,GAAIL,GAEzD,CAAEF,SAAQf,OAAQ"}