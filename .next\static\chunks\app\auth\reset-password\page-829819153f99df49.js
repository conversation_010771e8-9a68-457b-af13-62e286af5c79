(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2048],{11981:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(62898).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},73067:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(62898).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},98253:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(62898).Z)("Building2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},13008:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(62898).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},77216:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(62898).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},99670:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(62898).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},5589:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(62898).Z)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},8148:function(e,t,r){Promise.resolve().then(r.bind(r,50993))},50993:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return v}});var s=r(57437),a=r(2265),n=r(24033),i=r(61396),o=r.n(i),l=r(85754),c=r(45179),d=r(49842),u=r(27815),f=r(98253),m=r(13008),p=r(11981),h=r(73067),x=r(5589),y=r(77216),g=r(99670),b=r(5925);function v(){let e;let[t,r]=(0,a.useState)(""),[i,v]=(0,a.useState)(""),[w,j]=(0,a.useState)(!1),[N,k]=(0,a.useState)(!1),[Z,S]=(0,a.useState)(!1),[C,E]=(0,a.useState)(!1),[z,P]=(0,a.useState)(""),[O,I]=(0,a.useState)(null),M=(0,n.useRouter)(),R=(0,n.useSearchParams)().get("token");(0,a.useEffect)(()=>{if(!R){P("Invalid reset link"),I(!1);return}$(R)},[R]);let $=async e=>{try{let t=await fetch("/api/auth/verify-reset-token",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({token:e})});if(t.ok)I(!0);else{let e=await t.json();P(e.error||"Invalid or expired reset link"),I(!1)}}catch(e){console.error("Token verification error:",e),P("Failed to verify reset link"),I(!1)}},D=e=>e.length<8?"Password must be at least 8 characters long":/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(e)?null:"Password must contain at least one uppercase letter, one lowercase letter, and one number",T=async e=>{e.preventDefault(),P("");let r=D(t);if(r){P(r);return}if(t!==i){P("Passwords do not match");return}S(!0);try{let e=await fetch("/api/auth/reset-password",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({token:R,password:t})}),r=await e.json();e.ok?(E(!0),b.toast.success("Password reset successfully!"),setTimeout(()=>{M.push("/auth/signin?reset=success")},3e3)):P(r.error||"Failed to reset password")}catch(e){console.error("Reset password error:",e),P("An error occurred. Please try again.")}finally{S(!1)}},A=(e=0,t.length>=8&&e++,/[a-z]/.test(t)&&e++,/[A-Z]/.test(t)&&e++,/\d/.test(t)&&e++,/[^a-zA-Z\d]/.test(t)&&e++,e),_=(e=>{switch(e){case 0:case 1:return{label:"Very Weak",color:"text-red-600"};case 2:return{label:"Weak",color:"text-orange-600"};case 3:return{label:"Fair",color:"text-yellow-600"};case 4:return{label:"Good",color:"text-blue-600"};case 5:return{label:"Strong",color:"text-green-600"};default:return{label:"",color:""}}})(A);return null===O?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 p-4",children:(0,s.jsxs)("div",{className:"w-full max-w-md",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center mb-8",children:[(0,s.jsx)(f.Z,{className:"h-8 w-8 text-blue-600 mr-2"}),(0,s.jsx)("span",{className:"text-2xl font-bold text-gray-900",children:"Business SaaS"})]}),(0,s.jsx)(u.Zb,{children:(0,s.jsx)(u.aY,{className:"flex items-center justify-center py-8",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Verifying reset link..."})]})})})]})}):C?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 p-4",children:(0,s.jsxs)("div",{className:"w-full max-w-md",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center mb-8",children:[(0,s.jsx)(f.Z,{className:"h-8 w-8 text-blue-600 mr-2"}),(0,s.jsx)("span",{className:"text-2xl font-bold text-gray-900",children:"Business SaaS"})]}),(0,s.jsxs)(u.Zb,{children:[(0,s.jsxs)(u.Ol,{className:"text-center",children:[(0,s.jsx)("div",{className:"flex justify-center mb-4",children:(0,s.jsx)(m.Z,{className:"h-12 w-12 text-green-600"})}),(0,s.jsx)(u.ll,{className:"text-2xl",children:"Password reset successful!"}),(0,s.jsx)(u.SZ,{children:"Your password has been updated. You can now sign in with your new password."})]}),(0,s.jsxs)(u.aY,{className:"space-y-4",children:[(0,s.jsx)(l.z,{asChild:!0,className:"w-full",children:(0,s.jsx)(o(),{href:"/auth/signin",children:"Continue to Sign In"})}),(0,s.jsx)("p",{className:"text-sm text-center text-gray-500",children:"Redirecting automatically in 3 seconds..."})]})]})]})}):O?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 p-4",children:(0,s.jsxs)("div",{className:"w-full max-w-md",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center mb-8",children:[(0,s.jsx)(f.Z,{className:"h-8 w-8 text-blue-600 mr-2"}),(0,s.jsx)("span",{className:"text-2xl font-bold text-gray-900",children:"Business SaaS"})]}),(0,s.jsxs)(u.Zb,{children:[(0,s.jsxs)(u.Ol,{className:"space-y-1",children:[(0,s.jsx)(u.ll,{className:"text-2xl text-center",children:"Reset your password"}),(0,s.jsx)(u.SZ,{className:"text-center",children:"Enter your new password below"})]}),(0,s.jsxs)(u.aY,{children:[(0,s.jsxs)("form",{onSubmit:T,className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(d._,{htmlFor:"password",children:"New Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(x.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,s.jsx)(c.I,{id:"password",type:w?"text":"password",placeholder:"Enter your new password",value:t,onChange:e=>r(e.target.value),className:"pl-10 pr-10",required:!0}),(0,s.jsx)(l.z,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>j(!w),children:w?(0,s.jsx)(y.Z,{className:"h-4 w-4"}):(0,s.jsx)(g.Z,{className:"h-4 w-4"})})]}),t&&(0,s.jsx)("div",{className:"space-y-2",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"flex-1 bg-gray-200 rounded-full h-2",children:(0,s.jsx)("div",{className:"h-2 rounded-full transition-all duration-300 ".concat(A<=1?"bg-red-500":2===A?"bg-orange-500":3===A?"bg-yellow-500":4===A?"bg-blue-500":"bg-green-500"),style:{width:"".concat(A/5*100,"%")}})}),(0,s.jsx)("span",{className:"text-xs font-medium ".concat(_.color),children:_.label})]})})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(d._,{htmlFor:"confirmPassword",children:"Confirm New Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(x.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,s.jsx)(c.I,{id:"confirmPassword",type:N?"text":"password",placeholder:"Confirm your new password",value:i,onChange:e=>v(e.target.value),className:"pl-10 pr-10",required:!0}),(0,s.jsx)(l.z,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>k(!N),children:N?(0,s.jsx)(y.Z,{className:"h-4 w-4"}):(0,s.jsx)(g.Z,{className:"h-4 w-4"})})]})]}),z&&(0,s.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-red-600 bg-red-50 p-3 rounded-md",children:[(0,s.jsx)(p.Z,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:z})]}),(0,s.jsx)(l.z,{type:"submit",className:"w-full",disabled:Z,children:Z?"Resetting Password...":"Reset Password"})]}),(0,s.jsx)("div",{className:"mt-6 text-center",children:(0,s.jsxs)(o(),{href:"/auth/signin",className:"inline-flex items-center text-sm text-blue-600 hover:underline",children:[(0,s.jsx)(h.Z,{className:"h-4 w-4 mr-1"}),"Back to Sign In"]})})]})]})]})}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 p-4",children:(0,s.jsxs)("div",{className:"w-full max-w-md",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center mb-8",children:[(0,s.jsx)(f.Z,{className:"h-8 w-8 text-blue-600 mr-2"}),(0,s.jsx)("span",{className:"text-2xl font-bold text-gray-900",children:"Business SaaS"})]}),(0,s.jsxs)(u.Zb,{children:[(0,s.jsxs)(u.Ol,{className:"text-center",children:[(0,s.jsx)("div",{className:"flex justify-center mb-4",children:(0,s.jsx)(p.Z,{className:"h-12 w-12 text-red-600"})}),(0,s.jsx)(u.ll,{className:"text-2xl",children:"Invalid reset link"}),(0,s.jsx)(u.SZ,{children:"This password reset link is invalid or has expired."})]}),(0,s.jsxs)(u.aY,{className:"space-y-4",children:[z&&(0,s.jsx)("div",{className:"p-4 bg-red-50 border border-red-200 rounded-lg text-red-800 text-center",children:z}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(l.z,{asChild:!0,className:"w-full",children:(0,s.jsx)(o(),{href:"/auth/forgot-password",children:"Request New Reset Link"})}),(0,s.jsx)(l.z,{variant:"outline",asChild:!0,className:"w-full",children:(0,s.jsxs)(o(),{href:"/auth/signin",children:[(0,s.jsx)(h.Z,{className:"h-4 w-4 mr-2"}),"Back to Sign In"]})})]})]})]})]})})}},85754:function(e,t,r){"use strict";r.d(t,{z:function(){return c}});var s=r(57437),a=r(2265),n=r(67256),i=r(96061),o=r(1657);let l=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,t)=>{let{className:r,variant:a,size:i,asChild:c=!1,...d}=e,u=c?n.g7:"button";return(0,s.jsx)(u,{className:(0,o.cn)(l({variant:a,size:i,className:r})),ref:t,...d})});c.displayName="Button"},27815:function(e,t,r){"use strict";r.d(t,{Ol:function(){return o},SZ:function(){return c},Zb:function(){return i},aY:function(){return d},eW:function(){return u},ll:function(){return l}});var s=r(57437),a=r(2265),n=r(1657);let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});i.displayName="Card";let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...a})});o.displayName="CardHeader";let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})});l.displayName="CardTitle";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",r),...a})});c.displayName="CardDescription";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",r),...a})});d.displayName="CardContent";let u=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",r),...a})});u.displayName="CardFooter"},45179:function(e,t,r){"use strict";r.d(t,{I:function(){return i}});var s=r(57437),a=r(2265),n=r(1657);let i=a.forwardRef((e,t)=>{let{className:r,type:a,...i}=e;return(0,s.jsx)("input",{type:a,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...i})});i.displayName="Input"},49842:function(e,t,r){"use strict";r.d(t,{_:function(){return c}});var s=r(57437),a=r(2265),n=r(36743),i=r(96061),o=r(1657);let l=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(n.f,{ref:t,className:(0,o.cn)(l(),r),...a})});c.displayName=n.f.displayName},1657:function(e,t,r){"use strict";r.d(t,{cn:function(){return n}});var s=r(57042),a=r(74769);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.m6)((0,s.W)(t))}},24033:function(e,t,r){e.exports=r(15313)},36743:function(e,t,r){"use strict";r.d(t,{f:function(){return o}});var s=r(2265),a=r(9381),n=r(57437),i=s.forwardRef((e,t)=>(0,n.jsx)(a.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var o=i},9381:function(e,t,r){"use strict";r.d(t,{WV:function(){return o},jH:function(){return l}});var s=r(2265),a=r(54887),n=r(67256),i=r(57437),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,n.Z8)(`Primitive.${t}`),a=s.forwardRef((e,s)=>{let{asChild:a,...n}=e,o=a?r:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(o,{...n,ref:s})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{});function l(e,t){e&&a.flushSync(()=>e.dispatchEvent(t))}},5925:function(e,t,r){"use strict";let s,a;r.r(t),r.d(t,{CheckmarkIcon:function(){return G},ErrorIcon:function(){return V},LoaderIcon:function(){return W},ToastBar:function(){return eo},ToastIcon:function(){return et},Toaster:function(){return eu},default:function(){return ef},resolveValue:function(){return k},toast:function(){return $},useToaster:function(){return B},useToasterStore:function(){return I}});var n,i=r(2265);let o={data:""},l=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||o,c=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,d=/\/\*[^]*?\*\/|  +/g,u=/\n+/g,f=(e,t)=>{let r="",s="",a="";for(let n in e){let i=e[n];"@"==n[0]?"i"==n[1]?r=n+" "+i+";":s+="f"==n[1]?f(i,n):n+"{"+f(i,"k"==n[1]?"":t)+"}":"object"==typeof i?s+=f(i,t?t.replace(/([^,])+/g,e=>n.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)):n):null!=i&&(n=/^--/.test(n)?n:n.replace(/[A-Z]/g,"-$&").toLowerCase(),a+=f.p?f.p(n,i):n+":"+i+";")}return r+(t&&a?t+"{"+a+"}":a)+s},m={},p=e=>{if("object"==typeof e){let t="";for(let r in e)t+=r+p(e[r]);return t}return e},h=(e,t,r,s,a)=>{var n;let i=p(e),o=m[i]||(m[i]=(e=>{let t=0,r=11;for(;t<e.length;)r=101*r+e.charCodeAt(t++)>>>0;return"go"+r})(i));if(!m[o]){let t=i!==e?e:(e=>{let t,r,s=[{}];for(;t=c.exec(e.replace(d,""));)t[4]?s.shift():t[3]?(r=t[3].replace(u," ").trim(),s.unshift(s[0][r]=s[0][r]||{})):s[0][t[1]]=t[2].replace(u," ").trim();return s[0]})(e);m[o]=f(a?{["@keyframes "+o]:t}:t,r?"":"."+o)}let l=r&&m.g?m.g:null;return r&&(m.g=m[o]),n=m[o],l?t.data=t.data.replace(l,n):-1===t.data.indexOf(n)&&(t.data=s?n+t.data:t.data+n),o},x=(e,t,r)=>e.reduce((e,s,a)=>{let n=t[a];if(n&&n.call){let e=n(r),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;n=t?"."+t:e&&"object"==typeof e?e.props?"":f(e,""):!1===e?"":e}return e+s+(null==n?"":n)},"");function y(e){let t=this||{},r=e.call?e(t.p):e;return h(r.unshift?r.raw?x(r,[].slice.call(arguments,1),t.p):r.reduce((e,r)=>Object.assign(e,r&&r.call?r(t.p):r),{}):r,l(t.target),t.g,t.o,t.k)}y.bind({g:1});let g,b,v,w=y.bind({k:1});function j(e,t){let r=this||{};return function(){let s=arguments;function a(n,i){let o=Object.assign({},n),l=o.className||a.className;r.p=Object.assign({theme:b&&b()},o),r.o=/ *go\d+/.test(l),o.className=y.apply(r,s)+(l?" "+l:""),t&&(o.ref=i);let c=e;return e[0]&&(c=o.as||e,delete o.as),v&&c[0]&&v(o),g(c,o)}return t?t(a):a}}var N=e=>"function"==typeof e,k=(e,t)=>N(e)?e(t):e,Z=(s=0,()=>(++s).toString()),S=()=>{if(void 0===a&&"u">typeof window){let e=matchMedia("(prefers-reduced-motion: reduce)");a=!e||e.matches}return a},C=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case 2:let{toast:r}=t;return C(e,{type:e.toasts.find(e=>e.id===r.id)?1:0,toast:r});case 3:let{toastId:s}=t;return{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,dismissed:!0,visible:!1}:e)};case 4:return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let a=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(e=>({...e,pauseDuration:e.pauseDuration+a}))}}},E=[],z={toasts:[],pausedAt:void 0},P=e=>{z=C(z,e),E.forEach(e=>{e(z)})},O={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},I=(e={})=>{let[t,r]=(0,i.useState)(z),s=(0,i.useRef)(z);(0,i.useEffect)(()=>(s.current!==z&&r(z),E.push(r),()=>{let e=E.indexOf(r);e>-1&&E.splice(e,1)}),[]);let a=t.toasts.map(t=>{var r,s,a;return{...e,...e[t.type],...t,removeDelay:t.removeDelay||(null==(r=e[t.type])?void 0:r.removeDelay)||(null==e?void 0:e.removeDelay),duration:t.duration||(null==(s=e[t.type])?void 0:s.duration)||(null==e?void 0:e.duration)||O[t.type],style:{...e.style,...null==(a=e[t.type])?void 0:a.style,...t.style}}});return{...t,toasts:a}},M=(e,t="blank",r)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...r,id:(null==r?void 0:r.id)||Z()}),R=e=>(t,r)=>{let s=M(t,e,r);return P({type:2,toast:s}),s.id},$=(e,t)=>R("blank")(e,t);$.error=R("error"),$.success=R("success"),$.loading=R("loading"),$.custom=R("custom"),$.dismiss=e=>{P({type:3,toastId:e})},$.remove=e=>P({type:4,toastId:e}),$.promise=(e,t,r)=>{let s=$.loading(t.loading,{...r,...null==r?void 0:r.loading});return"function"==typeof e&&(e=e()),e.then(e=>{let a=t.success?k(t.success,e):void 0;return a?$.success(a,{id:s,...r,...null==r?void 0:r.success}):$.dismiss(s),e}).catch(e=>{let a=t.error?k(t.error,e):void 0;a?$.error(a,{id:s,...r,...null==r?void 0:r.error}):$.dismiss(s)}),e};var D=(e,t)=>{P({type:1,toast:{id:e,height:t}})},T=()=>{P({type:5,time:Date.now()})},A=new Map,_=1e3,F=(e,t=_)=>{if(A.has(e))return;let r=setTimeout(()=>{A.delete(e),P({type:4,toastId:e})},t);A.set(e,r)},B=e=>{let{toasts:t,pausedAt:r}=I(e);(0,i.useEffect)(()=>{if(r)return;let e=Date.now(),s=t.map(t=>{if(t.duration===1/0)return;let r=(t.duration||0)+t.pauseDuration-(e-t.createdAt);if(r<0){t.visible&&$.dismiss(t.id);return}return setTimeout(()=>$.dismiss(t.id),r)});return()=>{s.forEach(e=>e&&clearTimeout(e))}},[t,r]);let s=(0,i.useCallback)(()=>{r&&P({type:6,time:Date.now()})},[r]),a=(0,i.useCallback)((e,r)=>{let{reverseOrder:s=!1,gutter:a=8,defaultPosition:n}=r||{},i=t.filter(t=>(t.position||n)===(e.position||n)&&t.height),o=i.findIndex(t=>t.id===e.id),l=i.filter((e,t)=>t<o&&e.visible).length;return i.filter(e=>e.visible).slice(...s?[l+1]:[0,l]).reduce((e,t)=>e+(t.height||0)+a,0)},[t]);return(0,i.useEffect)(()=>{t.forEach(e=>{if(e.dismissed)F(e.id,e.removeDelay);else{let t=A.get(e.id);t&&(clearTimeout(t),A.delete(e.id))}})},[t]),{toasts:t,handlers:{updateHeight:D,startPause:T,endPause:s,calculateOffset:a}}},L=w`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,q=w`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,H=w`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,V=j("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${L} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${q} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${H} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,Y=w`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,W=j("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${Y} 1s linear infinite;
`,J=w`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,U=w`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,G=j("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${J} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${U} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,K=j("div")`
  position: absolute;
`,Q=j("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,X=w`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,ee=j("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${X} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,et=({toast:e})=>{let{icon:t,type:r,iconTheme:s}=e;return void 0!==t?"string"==typeof t?i.createElement(ee,null,t):t:"blank"===r?null:i.createElement(Q,null,i.createElement(W,{...s}),"loading"!==r&&i.createElement(K,null,"error"===r?i.createElement(V,{...s}):i.createElement(G,{...s})))},er=e=>`
0% {transform: translate3d(0,${-200*e}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,es=e=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${-150*e}%,-1px) scale(.6); opacity:0;}
`,ea=j("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,en=j("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,ei=(e,t)=>{let r=e.includes("top")?1:-1,[s,a]=S()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[er(r),es(r)];return{animation:t?`${w(s)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${w(a)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},eo=i.memo(({toast:e,position:t,style:r,children:s})=>{let a=e.height?ei(e.position||t||"top-center",e.visible):{opacity:0},n=i.createElement(et,{toast:e}),o=i.createElement(en,{...e.ariaProps},k(e.message,e));return i.createElement(ea,{className:e.className,style:{...a,...r,...e.style}},"function"==typeof s?s({icon:n,message:o}):i.createElement(i.Fragment,null,n,o))});n=i.createElement,f.p=void 0,g=n,b=void 0,v=void 0;var el=({id:e,className:t,style:r,onHeightUpdate:s,children:a})=>{let n=i.useCallback(t=>{if(t){let r=()=>{s(e,t.getBoundingClientRect().height)};r(),new MutationObserver(r).observe(t,{subtree:!0,childList:!0,characterData:!0})}},[e,s]);return i.createElement("div",{ref:n,className:t,style:r},a)},ec=(e,t)=>{let r=e.includes("top"),s=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:S()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(r?1:-1)}px)`,...r?{top:0}:{bottom:0},...s}},ed=y`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,eu=({reverseOrder:e,position:t="top-center",toastOptions:r,gutter:s,children:a,containerStyle:n,containerClassName:o})=>{let{toasts:l,handlers:c}=B(r);return i.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...n},className:o,onMouseEnter:c.startPause,onMouseLeave:c.endPause},l.map(r=>{let n=r.position||t,o=ec(n,c.calculateOffset(r,{reverseOrder:e,gutter:s,defaultPosition:t}));return i.createElement(el,{id:r.id,key:r.id,onHeightUpdate:c.updateHeight,className:r.visible?ed:"",style:o},"custom"===r.type?k(r.message,r):a?a(r):i.createElement(eo,{toast:r,position:n}))}))},ef=$}},function(e){e.O(0,[6723,1396,2971,4938,1744],function(){return e(e.s=8148)}),_N_E=e.O()}]);