"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4138],{62442:function(t,e,n){n.d(e,{Z:function(){return r}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(62898).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},83995:function(t,e,n){n.d(e,{ee:function(){return et},Eh:function(){return en},VY:function(){return ee},fC:function(){return t6},D7:function(){return tq}});var r=n(2265);let i=["top","right","bottom","left"],o=Math.min,l=Math.max,a=Math.round,f=Math.floor,s=t=>({x:t,y:t}),u={left:"right",right:"left",bottom:"top",top:"bottom"},c={start:"end",end:"start"};function d(t,e){return"function"==typeof t?t(e):t}function p(t){return t.split("-")[0]}function h(t){return t.split("-")[1]}function m(t){return"x"===t?"y":"x"}function g(t){return"y"===t?"height":"width"}let y=new Set(["top","bottom"]);function w(t){return y.has(p(t))?"y":"x"}function x(t){return t.replace(/start|end/g,t=>c[t])}let v=["left","right"],b=["right","left"],A=["top","bottom"],R=["bottom","top"];function S(t){return t.replace(/left|right|bottom|top/g,t=>u[t])}function L(t){return"number"!=typeof t?{top:0,right:0,bottom:0,left:0,...t}:{top:t,right:t,bottom:t,left:t}}function T(t){let{x:e,y:n,width:r,height:i}=t;return{width:r,height:i,top:n,left:e,right:e+r,bottom:n+i,x:e,y:n}}function E(t,e,n){let r,{reference:i,floating:o}=t,l=w(e),a=m(w(e)),f=g(a),s=p(e),u="y"===l,c=i.x+i.width/2-o.width/2,d=i.y+i.height/2-o.height/2,y=i[f]/2-o[f]/2;switch(s){case"top":r={x:c,y:i.y-o.height};break;case"bottom":r={x:c,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:d};break;case"left":r={x:i.x-o.width,y:d};break;default:r={x:i.x,y:i.y}}switch(h(e)){case"start":r[a]-=y*(n&&u?-1:1);break;case"end":r[a]+=y*(n&&u?-1:1)}return r}let C=async(t,e,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:l}=n,a=o.filter(Boolean),f=await (null==l.isRTL?void 0:l.isRTL(e)),s=await l.getElementRects({reference:t,floating:e,strategy:i}),{x:u,y:c}=E(s,r,f),d=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:o,fn:m}=a[n],{x:g,y:y,data:w,reset:x}=await m({x:u,y:c,initialPlacement:r,placement:d,strategy:i,middlewareData:p,rects:s,platform:l,elements:{reference:t,floating:e}});u=null!=g?g:u,c=null!=y?y:c,p={...p,[o]:{...p[o],...w}},x&&h<=50&&(h++,"object"==typeof x&&(x.placement&&(d=x.placement),x.rects&&(s=!0===x.rects?await l.getElementRects({reference:t,floating:e,strategy:i}):x.rects),{x:u,y:c}=E(s,d,f)),n=-1)}return{x:u,y:c,placement:d,strategy:i,middlewareData:p}};async function O(t,e){var n;void 0===e&&(e={});let{x:r,y:i,platform:o,rects:l,elements:a,strategy:f}=t,{boundary:s="clippingAncestors",rootBoundary:u="viewport",elementContext:c="floating",altBoundary:p=!1,padding:h=0}=d(e,t),m=L(h),g=a[p?"floating"===c?"reference":"floating":c],y=T(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(g)))||n?g:g.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(a.floating)),boundary:s,rootBoundary:u,strategy:f})),w="floating"===c?{x:r,y:i,width:l.floating.width,height:l.floating.height}:l.reference,x=await (null==o.getOffsetParent?void 0:o.getOffsetParent(a.floating)),v=await (null==o.isElement?void 0:o.isElement(x))&&await (null==o.getScale?void 0:o.getScale(x))||{x:1,y:1},b=T(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:w,offsetParent:x,strategy:f}):w);return{top:(y.top-b.top+m.top)/v.y,bottom:(b.bottom-y.bottom+m.bottom)/v.y,left:(y.left-b.left+m.left)/v.x,right:(b.right-y.right+m.right)/v.x}}function P(t,e){return{top:t.top-e.height,right:t.right-e.width,bottom:t.bottom-e.height,left:t.left-e.width}}function k(t){return i.some(e=>t[e]>=0)}let H=new Set(["left","top"]);async function D(t,e){let{placement:n,platform:r,elements:i}=t,o=await (null==r.isRTL?void 0:r.isRTL(i.floating)),l=p(n),a=h(n),f="y"===w(n),s=H.has(l)?-1:1,u=o&&f?-1:1,c=d(e,t),{mainAxis:m,crossAxis:g,alignmentAxis:y}="number"==typeof c?{mainAxis:c,crossAxis:0,alignmentAxis:null}:{mainAxis:c.mainAxis||0,crossAxis:c.crossAxis||0,alignmentAxis:c.alignmentAxis};return a&&"number"==typeof y&&(g="end"===a?-1*y:y),f?{x:g*u,y:m*s}:{x:m*s,y:g*u}}function W(){return"undefined"!=typeof window}function F(t){return $(t)?(t.nodeName||"").toLowerCase():"#document"}function j(t){var e;return(null==t||null==(e=t.ownerDocument)?void 0:e.defaultView)||window}function M(t){var e;return null==(e=($(t)?t.ownerDocument:t.document)||window.document)?void 0:e.documentElement}function $(t){return!!W()&&(t instanceof Node||t instanceof j(t).Node)}function V(t){return!!W()&&(t instanceof Element||t instanceof j(t).Element)}function z(t){return!!W()&&(t instanceof HTMLElement||t instanceof j(t).HTMLElement)}function N(t){return!!W()&&"undefined"!=typeof ShadowRoot&&(t instanceof ShadowRoot||t instanceof j(t).ShadowRoot)}let B=new Set(["inline","contents"]);function _(t){let{overflow:e,overflowX:n,overflowY:r,display:i}=tt(t);return/auto|scroll|overlay|hidden|clip/.test(e+r+n)&&!B.has(i)}let Y=new Set(["table","td","th"]),I=[":popover-open",":modal"];function X(t){return I.some(e=>{try{return t.matches(e)}catch(t){return!1}})}let q=["transform","translate","scale","rotate","perspective"],Z=["transform","translate","scale","rotate","perspective","filter"],G=["paint","layout","strict","content"];function J(t){let e=K(),n=V(t)?tt(t):t;return q.some(t=>!!n[t]&&"none"!==n[t])||!!n.containerType&&"normal"!==n.containerType||!e&&!!n.backdropFilter&&"none"!==n.backdropFilter||!e&&!!n.filter&&"none"!==n.filter||Z.some(t=>(n.willChange||"").includes(t))||G.some(t=>(n.contain||"").includes(t))}function K(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let Q=new Set(["html","body","#document"]);function U(t){return Q.has(F(t))}function tt(t){return j(t).getComputedStyle(t)}function te(t){return V(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function tn(t){if("html"===F(t))return t;let e=t.assignedSlot||t.parentNode||N(t)&&t.host||M(t);return N(e)?e.host:e}function tr(t,e,n){var r;void 0===e&&(e=[]),void 0===n&&(n=!0);let i=function t(e){let n=tn(e);return U(n)?e.ownerDocument?e.ownerDocument.body:e.body:z(n)&&_(n)?n:t(n)}(t),o=i===(null==(r=t.ownerDocument)?void 0:r.body),l=j(i);if(o){let t=ti(l);return e.concat(l,l.visualViewport||[],_(i)?i:[],t&&n?tr(t):[])}return e.concat(i,tr(i,[],n))}function ti(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function to(t){let e=tt(t),n=parseFloat(e.width)||0,r=parseFloat(e.height)||0,i=z(t),o=i?t.offsetWidth:n,l=i?t.offsetHeight:r,f=a(n)!==o||a(r)!==l;return f&&(n=o,r=l),{width:n,height:r,$:f}}function tl(t){return V(t)?t:t.contextElement}function ta(t){let e=tl(t);if(!z(e))return s(1);let n=e.getBoundingClientRect(),{width:r,height:i,$:o}=to(e),l=(o?a(n.width):n.width)/r,f=(o?a(n.height):n.height)/i;return l&&Number.isFinite(l)||(l=1),f&&Number.isFinite(f)||(f=1),{x:l,y:f}}let tf=s(0);function ts(t){let e=j(t);return K()&&e.visualViewport?{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}:tf}function tu(t,e,n,r){var i;void 0===e&&(e=!1),void 0===n&&(n=!1);let o=t.getBoundingClientRect(),l=tl(t),a=s(1);e&&(r?V(r)&&(a=ta(r)):a=ta(t));let f=(void 0===(i=n)&&(i=!1),r&&(!i||r===j(l))&&i)?ts(l):s(0),u=(o.left+f.x)/a.x,c=(o.top+f.y)/a.y,d=o.width/a.x,p=o.height/a.y;if(l){let t=j(l),e=r&&V(r)?j(r):r,n=t,i=ti(n);for(;i&&r&&e!==n;){let t=ta(i),e=i.getBoundingClientRect(),r=tt(i),o=e.left+(i.clientLeft+parseFloat(r.paddingLeft))*t.x,l=e.top+(i.clientTop+parseFloat(r.paddingTop))*t.y;u*=t.x,c*=t.y,d*=t.x,p*=t.y,u+=o,c+=l,i=ti(n=j(i))}}return T({width:d,height:p,x:u,y:c})}function tc(t,e){let n=te(t).scrollLeft;return e?e.left+n:tu(M(t)).left+n}function td(t,e,n){void 0===n&&(n=!1);let r=t.getBoundingClientRect();return{x:r.left+e.scrollLeft-(n?0:tc(t,r)),y:r.top+e.scrollTop}}let tp=new Set(["absolute","fixed"]);function th(t,e,n){let r;if("viewport"===e)r=function(t,e){let n=j(t),r=M(t),i=n.visualViewport,o=r.clientWidth,l=r.clientHeight,a=0,f=0;if(i){o=i.width,l=i.height;let t=K();(!t||t&&"fixed"===e)&&(a=i.offsetLeft,f=i.offsetTop)}return{width:o,height:l,x:a,y:f}}(t,n);else if("document"===e)r=function(t){let e=M(t),n=te(t),r=t.ownerDocument.body,i=l(e.scrollWidth,e.clientWidth,r.scrollWidth,r.clientWidth),o=l(e.scrollHeight,e.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+tc(t),f=-n.scrollTop;return"rtl"===tt(r).direction&&(a+=l(e.clientWidth,r.clientWidth)-i),{width:i,height:o,x:a,y:f}}(M(t));else if(V(e))r=function(t,e){let n=tu(t,!0,"fixed"===e),r=n.top+t.clientTop,i=n.left+t.clientLeft,o=z(t)?ta(t):s(1),l=t.clientWidth*o.x;return{width:l,height:t.clientHeight*o.y,x:i*o.x,y:r*o.y}}(e,n);else{let n=ts(t);r={x:e.x-n.x,y:e.y-n.y,width:e.width,height:e.height}}return T(r)}function tm(t){return"static"===tt(t).position}function tg(t,e){if(!z(t)||"fixed"===tt(t).position)return null;if(e)return e(t);let n=t.offsetParent;return M(t)===n&&(n=n.ownerDocument.body),n}function ty(t,e){var n;let r=j(t);if(X(t))return r;if(!z(t)){let e=tn(t);for(;e&&!U(e);){if(V(e)&&!tm(e))return e;e=tn(e)}return r}let i=tg(t,e);for(;i&&(n=i,Y.has(F(n)))&&tm(i);)i=tg(i,e);return i&&U(i)&&tm(i)&&!J(i)?r:i||function(t){let e=tn(t);for(;z(e)&&!U(e);){if(J(e))return e;if(X(e))break;e=tn(e)}return null}(t)||r}let tw=async function(t){let e=this.getOffsetParent||ty,n=this.getDimensions,r=await n(t.floating);return{reference:function(t,e,n){let r=z(e),i=M(e),o="fixed"===n,l=tu(t,!0,o,e),a={scrollLeft:0,scrollTop:0},f=s(0);if(r||!r&&!o){if(("body"!==F(e)||_(i))&&(a=te(e)),r){let t=tu(e,!0,o,e);f.x=t.x+e.clientLeft,f.y=t.y+e.clientTop}else i&&(f.x=tc(i))}o&&!r&&i&&(f.x=tc(i));let u=!i||r||o?s(0):td(i,a);return{x:l.left+a.scrollLeft-f.x-u.x,y:l.top+a.scrollTop-f.y-u.y,width:l.width,height:l.height}}(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},tx={convertOffsetParentRelativeRectToViewportRelativeRect:function(t){let{elements:e,rect:n,offsetParent:r,strategy:i}=t,o="fixed"===i,l=M(r),a=!!e&&X(e.floating);if(r===l||a&&o)return n;let f={scrollLeft:0,scrollTop:0},u=s(1),c=s(0),d=z(r);if((d||!d&&!o)&&(("body"!==F(r)||_(l))&&(f=te(r)),z(r))){let t=tu(r);u=ta(r),c.x=t.x+r.clientLeft,c.y=t.y+r.clientTop}let p=!l||d||o?s(0):td(l,f,!0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-f.scrollLeft*u.x+c.x+p.x,y:n.y*u.y-f.scrollTop*u.y+c.y+p.y}},getDocumentElement:M,getClippingRect:function(t){let{element:e,boundary:n,rootBoundary:r,strategy:i}=t,a=[..."clippingAncestors"===n?X(e)?[]:function(t,e){let n=e.get(t);if(n)return n;let r=tr(t,[],!1).filter(t=>V(t)&&"body"!==F(t)),i=null,o="fixed"===tt(t).position,l=o?tn(t):t;for(;V(l)&&!U(l);){let e=tt(l),n=J(l);n||"fixed"!==e.position||(i=null),(o?!n&&!i:!n&&"static"===e.position&&!!i&&tp.has(i.position)||_(l)&&!n&&function t(e,n){let r=tn(e);return!(r===n||!V(r)||U(r))&&("fixed"===tt(r).position||t(r,n))}(t,l))?r=r.filter(t=>t!==l):i=e,l=tn(l)}return e.set(t,r),r}(e,this._c):[].concat(n),r],f=a[0],s=a.reduce((t,n)=>{let r=th(e,n,i);return t.top=l(r.top,t.top),t.right=o(r.right,t.right),t.bottom=o(r.bottom,t.bottom),t.left=l(r.left,t.left),t},th(e,f,i));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:ty,getElementRects:tw,getClientRects:function(t){return Array.from(t.getClientRects())},getDimensions:function(t){let{width:e,height:n}=to(t);return{width:e,height:n}},getScale:ta,isElement:V,isRTL:function(t){return"rtl"===tt(t).direction}};function tv(t,e){return t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height}let tb=t=>({name:"arrow",options:t,async fn(e){let{x:n,y:r,placement:i,rects:a,platform:f,elements:s,middlewareData:u}=e,{element:c,padding:p=0}=d(t,e)||{};if(null==c)return{};let y=L(p),x={x:n,y:r},v=m(w(i)),b=g(v),A=await f.getDimensions(c),R="y"===v,S=R?"clientHeight":"clientWidth",T=a.reference[b]+a.reference[v]-x[v]-a.floating[b],E=x[v]-a.reference[v],C=await (null==f.getOffsetParent?void 0:f.getOffsetParent(c)),O=C?C[S]:0;O&&await (null==f.isElement?void 0:f.isElement(C))||(O=s.floating[S]||a.floating[b]);let P=O/2-A[b]/2-1,k=o(y[R?"top":"left"],P),H=o(y[R?"bottom":"right"],P),D=O-A[b]-H,W=O/2-A[b]/2+(T/2-E/2),F=l(k,o(W,D)),j=!u.arrow&&null!=h(i)&&W!==F&&a.reference[b]/2-(W<k?k:H)-A[b]/2<0,M=j?W<k?W-k:W-D:0;return{[v]:x[v]+M,data:{[v]:F,centerOffset:W-F-M,...j&&{alignmentOffset:M}},reset:j}}}),tA=(t,e,n)=>{let r=new Map,i={platform:tx,...n},o={...i.platform,_c:r};return C(t,e,{...i,platform:o})};var tR=n(54887),tS="undefined"!=typeof document?r.useLayoutEffect:function(){};function tL(t,e){let n,r,i;if(t===e)return!0;if(typeof t!=typeof e)return!1;if("function"==typeof t&&t.toString()===e.toString())return!0;if(t&&e&&"object"==typeof t){if(Array.isArray(t)){if((n=t.length)!==e.length)return!1;for(r=n;0!=r--;)if(!tL(t[r],e[r]))return!1;return!0}if((n=(i=Object.keys(t)).length)!==Object.keys(e).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(e,i[r]))return!1;for(r=n;0!=r--;){let n=i[r];if(("_owner"!==n||!t.$$typeof)&&!tL(t[n],e[n]))return!1}return!0}return t!=t&&e!=e}function tT(t){return"undefined"==typeof window?1:(t.ownerDocument.defaultView||window).devicePixelRatio||1}function tE(t,e){let n=tT(t);return Math.round(e*n)/n}function tC(t){let e=r.useRef(t);return tS(()=>{e.current=t}),e}let tO=t=>({name:"arrow",options:t,fn(e){let{element:n,padding:r}="function"==typeof t?t(e):t;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?tb({element:n.current,padding:r}).fn(e):{}:n?tb({element:n,padding:r}).fn(e):{}}}),tP=(t,e)=>{var n;return{...(void 0===(n=t)&&(n=0),{name:"offset",options:n,async fn(t){var e,r;let{x:i,y:o,placement:l,middlewareData:a}=t,f=await D(t,n);return l===(null==(e=a.offset)?void 0:e.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:i+f.x,y:o+f.y,data:{...f,placement:l}}}}),options:[t,e]}},tk=(t,e)=>{var n;return{...(void 0===(n=t)&&(n={}),{name:"shift",options:n,async fn(t){let{x:e,y:r,placement:i}=t,{mainAxis:a=!0,crossAxis:f=!1,limiter:s={fn:t=>{let{x:e,y:n}=t;return{x:e,y:n}}},...u}=d(n,t),c={x:e,y:r},h=await O(t,u),g=w(p(i)),y=m(g),x=c[y],v=c[g];if(a){let t="y"===y?"top":"left",e="y"===y?"bottom":"right",n=x+h[t],r=x-h[e];x=l(n,o(x,r))}if(f){let t="y"===g?"top":"left",e="y"===g?"bottom":"right",n=v+h[t],r=v-h[e];v=l(n,o(v,r))}let b=s.fn({...t,[y]:x,[g]:v});return{...b,data:{x:b.x-e,y:b.y-r,enabled:{[y]:a,[g]:f}}}}}),options:[t,e]}},tH=(t,e)=>{var n;return{...(void 0===(n=t)&&(n={}),{options:n,fn(t){let{x:e,y:r,placement:i,rects:o,middlewareData:l}=t,{offset:a=0,mainAxis:f=!0,crossAxis:s=!0}=d(n,t),u={x:e,y:r},c=w(i),h=m(c),g=u[h],y=u[c],x=d(a,t),v="number"==typeof x?{mainAxis:x,crossAxis:0}:{mainAxis:0,crossAxis:0,...x};if(f){let t="y"===h?"height":"width",e=o.reference[h]-o.floating[t]+v.mainAxis,n=o.reference[h]+o.reference[t]-v.mainAxis;g<e?g=e:g>n&&(g=n)}if(s){var b,A;let t="y"===h?"width":"height",e=H.has(p(i)),n=o.reference[c]-o.floating[t]+(e&&(null==(b=l.offset)?void 0:b[c])||0)+(e?0:v.crossAxis),r=o.reference[c]+o.reference[t]+(e?0:(null==(A=l.offset)?void 0:A[c])||0)-(e?v.crossAxis:0);y<n?y=n:y>r&&(y=r)}return{[h]:g,[c]:y}}}),options:[t,e]}},tD=(t,e)=>{var n;return{...(void 0===(n=t)&&(n={}),{name:"flip",options:n,async fn(t){var e,r,i,o,l;let{placement:a,middlewareData:f,rects:s,initialPlacement:u,platform:c,elements:y}=t,{mainAxis:L=!0,crossAxis:T=!0,fallbackPlacements:E,fallbackStrategy:C="bestFit",fallbackAxisSideDirection:P="none",flipAlignment:k=!0,...H}=d(n,t);if(null!=(e=f.arrow)&&e.alignmentOffset)return{};let D=p(a),W=w(u),F=p(u)===u,j=await (null==c.isRTL?void 0:c.isRTL(y.floating)),M=E||(F||!k?[S(u)]:function(t){let e=S(t);return[x(t),e,x(e)]}(u)),$="none"!==P;!E&&$&&M.push(...function(t,e,n,r){let i=h(t),o=function(t,e,n){switch(t){case"top":case"bottom":if(n)return e?b:v;return e?v:b;case"left":case"right":return e?A:R;default:return[]}}(p(t),"start"===n,r);return i&&(o=o.map(t=>t+"-"+i),e&&(o=o.concat(o.map(x)))),o}(u,k,P,j));let V=[u,...M],z=await O(t,H),N=[],B=(null==(r=f.flip)?void 0:r.overflows)||[];if(L&&N.push(z[D]),T){let t=function(t,e,n){void 0===n&&(n=!1);let r=h(t),i=m(w(t)),o=g(i),l="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return e.reference[o]>e.floating[o]&&(l=S(l)),[l,S(l)]}(a,s,j);N.push(z[t[0]],z[t[1]])}if(B=[...B,{placement:a,overflows:N}],!N.every(t=>t<=0)){let t=((null==(i=f.flip)?void 0:i.index)||0)+1,e=V[t];if(e&&(!("alignment"===T&&W!==w(e))||B.every(t=>t.overflows[0]>0&&w(t.placement)===W)))return{data:{index:t,overflows:B},reset:{placement:e}};let n=null==(o=B.filter(t=>t.overflows[0]<=0).sort((t,e)=>t.overflows[1]-e.overflows[1])[0])?void 0:o.placement;if(!n)switch(C){case"bestFit":{let t=null==(l=B.filter(t=>{if($){let e=w(t.placement);return e===W||"y"===e}return!0}).map(t=>[t.placement,t.overflows.filter(t=>t>0).reduce((t,e)=>t+e,0)]).sort((t,e)=>t[1]-e[1])[0])?void 0:l[0];t&&(n=t);break}case"initialPlacement":n=u}if(a!==n)return{reset:{placement:n}}}return{}}}),options:[t,e]}},tW=(t,e)=>{var n;return{...(void 0===(n=t)&&(n={}),{name:"size",options:n,async fn(t){var e,r;let i,a;let{placement:f,rects:s,platform:u,elements:c}=t,{apply:m=()=>{},...g}=d(n,t),y=await O(t,g),x=p(f),v=h(f),b="y"===w(f),{width:A,height:R}=s.floating;"top"===x||"bottom"===x?(i=x,a=v===(await (null==u.isRTL?void 0:u.isRTL(c.floating))?"start":"end")?"left":"right"):(a=x,i="end"===v?"top":"bottom");let S=R-y.top-y.bottom,L=A-y.left-y.right,T=o(R-y[i],S),E=o(A-y[a],L),C=!t.middlewareData.shift,P=T,k=E;if(null!=(e=t.middlewareData.shift)&&e.enabled.x&&(k=L),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(P=S),C&&!v){let t=l(y.left,0),e=l(y.right,0),n=l(y.top,0),r=l(y.bottom,0);b?k=A-2*(0!==t||0!==e?t+e:l(y.left,y.right)):P=R-2*(0!==n||0!==r?n+r:l(y.top,y.bottom))}await m({...t,availableWidth:k,availableHeight:P});let H=await u.getDimensions(c.floating);return A!==H.width||R!==H.height?{reset:{rects:!0}}:{}}}),options:[t,e]}},tF=(t,e)=>{var n;return{...(void 0===(n=t)&&(n={}),{name:"hide",options:n,async fn(t){let{rects:e}=t,{strategy:r="referenceHidden",...i}=d(n,t);switch(r){case"referenceHidden":{let n=P(await O(t,{...i,elementContext:"reference"}),e.reference);return{data:{referenceHiddenOffsets:n,referenceHidden:k(n)}}}case"escaped":{let n=P(await O(t,{...i,altBoundary:!0}),e.floating);return{data:{escapedOffsets:n,escaped:k(n)}}}default:return{}}}}),options:[t,e]}},tj=(t,e)=>({...tO(t),options:[t,e]});var tM=n(9381),t$=n(57437),tV=r.forwardRef((t,e)=>{let{children:n,width:r=10,height:i=5,...o}=t;return(0,t$.jsx)(tM.WV.svg,{...o,ref:e,width:r,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:t.asChild?n:(0,t$.jsx)("polygon",{points:"0,0 30,0 15,10"})})});tV.displayName="Arrow";var tz=n(42210),tN=n(56989),tB=n(16459),t_=n(51030),tY=n(94977),tI="Popper",[tX,tq]=(0,tN.b)(tI),[tZ,tG]=tX(tI),tJ=t=>{let{__scopePopper:e,children:n}=t,[i,o]=r.useState(null);return(0,t$.jsx)(tZ,{scope:e,anchor:i,onAnchorChange:o,children:n})};tJ.displayName=tI;var tK="PopperAnchor",tQ=r.forwardRef((t,e)=>{let{__scopePopper:n,virtualRef:i,...o}=t,l=tG(tK,n),a=r.useRef(null),f=(0,tz.e)(e,a);return r.useEffect(()=>{l.onAnchorChange(i?.current||a.current)}),i?null:(0,t$.jsx)(tM.WV.div,{...o,ref:f})});tQ.displayName=tK;var tU="PopperContent",[t0,t1]=tX(tU),t2=r.forwardRef((t,e)=>{let{__scopePopper:n,side:i="bottom",sideOffset:a=0,align:s="center",alignOffset:u=0,arrowPadding:c=0,avoidCollisions:d=!0,collisionBoundary:p=[],collisionPadding:h=0,sticky:m="partial",hideWhenDetached:g=!1,updatePositionStrategy:y="optimized",onPlaced:w,...x}=t,v=tG(tU,n),[b,A]=r.useState(null),R=(0,tz.e)(e,t=>A(t)),[S,L]=r.useState(null),T=(0,tY.t)(S),E=T?.width??0,C=T?.height??0,O="number"==typeof h?h:{top:0,right:0,bottom:0,left:0,...h},P=Array.isArray(p)?p:[p],k=P.length>0,H={padding:O,boundary:P.filter(t3),altBoundary:k},{refs:D,floatingStyles:W,placement:F,isPositioned:j,middlewareData:$}=function(t){void 0===t&&(t={});let{placement:e="bottom",strategy:n="absolute",middleware:i=[],platform:o,elements:{reference:l,floating:a}={},transform:f=!0,whileElementsMounted:s,open:u}=t,[c,d]=r.useState({x:0,y:0,strategy:n,placement:e,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(i);tL(p,i)||h(i);let[m,g]=r.useState(null),[y,w]=r.useState(null),x=r.useCallback(t=>{t!==R.current&&(R.current=t,g(t))},[]),v=r.useCallback(t=>{t!==S.current&&(S.current=t,w(t))},[]),b=l||m,A=a||y,R=r.useRef(null),S=r.useRef(null),L=r.useRef(c),T=null!=s,E=tC(s),C=tC(o),O=tC(u),P=r.useCallback(()=>{if(!R.current||!S.current)return;let t={placement:e,strategy:n,middleware:p};C.current&&(t.platform=C.current),tA(R.current,S.current,t).then(t=>{let e={...t,isPositioned:!1!==O.current};k.current&&!tL(L.current,e)&&(L.current=e,tR.flushSync(()=>{d(e)}))})},[p,e,n,C,O]);tS(()=>{!1===u&&L.current.isPositioned&&(L.current.isPositioned=!1,d(t=>({...t,isPositioned:!1})))},[u]);let k=r.useRef(!1);tS(()=>(k.current=!0,()=>{k.current=!1}),[]),tS(()=>{if(b&&(R.current=b),A&&(S.current=A),b&&A){if(E.current)return E.current(b,A,P);P()}},[b,A,P,E,T]);let H=r.useMemo(()=>({reference:R,floating:S,setReference:x,setFloating:v}),[x,v]),D=r.useMemo(()=>({reference:b,floating:A}),[b,A]),W=r.useMemo(()=>{let t={position:n,left:0,top:0};if(!D.floating)return t;let e=tE(D.floating,c.x),r=tE(D.floating,c.y);return f?{...t,transform:"translate("+e+"px, "+r+"px)",...tT(D.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:e,top:r}},[n,f,D.floating,c.x,c.y]);return r.useMemo(()=>({...c,update:P,refs:H,elements:D,floatingStyles:W}),[c,P,H,D,W])}({strategy:"fixed",placement:i+("center"!==s?"-"+s:""),whileElementsMounted:(...t)=>(function(t,e,n,r){let i;void 0===r&&(r={});let{ancestorScroll:a=!0,ancestorResize:s=!0,elementResize:u="function"==typeof ResizeObserver,layoutShift:c="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=tl(t),h=a||s?[...p?tr(p):[],...tr(e)]:[];h.forEach(t=>{a&&t.addEventListener("scroll",n,{passive:!0}),s&&t.addEventListener("resize",n)});let m=p&&c?function(t,e){let n,r=null,i=M(t);function a(){var t;clearTimeout(n),null==(t=r)||t.disconnect(),r=null}return!function s(u,c){void 0===u&&(u=!1),void 0===c&&(c=1),a();let d=t.getBoundingClientRect(),{left:p,top:h,width:m,height:g}=d;if(u||e(),!m||!g)return;let y=f(h),w=f(i.clientWidth-(p+m)),x={rootMargin:-y+"px "+-w+"px "+-f(i.clientHeight-(h+g))+"px "+-f(p)+"px",threshold:l(0,o(1,c))||1},v=!0;function b(e){let r=e[0].intersectionRatio;if(r!==c){if(!v)return s();r?s(!1,r):n=setTimeout(()=>{s(!1,1e-7)},1e3)}1!==r||tv(d,t.getBoundingClientRect())||s(),v=!1}try{r=new IntersectionObserver(b,{...x,root:i.ownerDocument})}catch(t){r=new IntersectionObserver(b,x)}r.observe(t)}(!0),a}(p,n):null,g=-1,y=null;u&&(y=new ResizeObserver(t=>{let[r]=t;r&&r.target===p&&y&&(y.unobserve(e),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var t;null==(t=y)||t.observe(e)})),n()}),p&&!d&&y.observe(p),y.observe(e));let w=d?tu(t):null;return d&&function e(){let r=tu(t);w&&!tv(w,r)&&n(),w=r,i=requestAnimationFrame(e)}(),n(),()=>{var t;h.forEach(t=>{a&&t.removeEventListener("scroll",n),s&&t.removeEventListener("resize",n)}),null==m||m(),null==(t=y)||t.disconnect(),y=null,d&&cancelAnimationFrame(i)}})(...t,{animationFrame:"always"===y}),elements:{reference:v.anchor},middleware:[tP({mainAxis:a+C,alignmentAxis:u}),d&&tk({mainAxis:!0,crossAxis:!1,limiter:"partial"===m?tH():void 0,...H}),d&&tD({...H}),tW({...H,apply:({elements:t,rects:e,availableWidth:n,availableHeight:r})=>{let{width:i,height:o}=e.reference,l=t.floating.style;l.setProperty("--radix-popper-available-width",`${n}px`),l.setProperty("--radix-popper-available-height",`${r}px`),l.setProperty("--radix-popper-anchor-width",`${i}px`),l.setProperty("--radix-popper-anchor-height",`${o}px`)}}),S&&tj({element:S,padding:c}),t4({arrowWidth:E,arrowHeight:C}),g&&tF({strategy:"referenceHidden",...H})]}),[V,z]=t8(F),N=(0,tB.W)(w);(0,t_.b)(()=>{j&&N?.()},[j,N]);let B=$.arrow?.x,_=$.arrow?.y,Y=$.arrow?.centerOffset!==0,[I,X]=r.useState();return(0,t_.b)(()=>{b&&X(window.getComputedStyle(b).zIndex)},[b]),(0,t$.jsx)("div",{ref:D.setFloating,"data-radix-popper-content-wrapper":"",style:{...W,transform:j?W.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:I,"--radix-popper-transform-origin":[$.transformOrigin?.x,$.transformOrigin?.y].join(" "),...$.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:t.dir,children:(0,t$.jsx)(t0,{scope:n,placedSide:V,onArrowChange:L,arrowX:B,arrowY:_,shouldHideArrow:Y,children:(0,t$.jsx)(tM.WV.div,{"data-side":V,"data-align":z,...x,ref:R,style:{...x.style,animation:j?void 0:"none"}})})})});t2.displayName=tU;var t5="PopperArrow",t9={top:"bottom",right:"left",bottom:"top",left:"right"},t7=r.forwardRef(function(t,e){let{__scopePopper:n,...r}=t,i=t1(t5,n),o=t9[i.placedSide];return(0,t$.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,t$.jsx)(tV,{...r,ref:e,style:{...r.style,display:"block"}})})});function t3(t){return null!==t}t7.displayName=t5;var t4=t=>({name:"transformOrigin",options:t,fn(e){let{placement:n,rects:r,middlewareData:i}=e,o=i.arrow?.centerOffset!==0,l=o?0:t.arrowWidth,a=o?0:t.arrowHeight,[f,s]=t8(n),u={start:"0%",center:"50%",end:"100%"}[s],c=(i.arrow?.x??0)+l/2,d=(i.arrow?.y??0)+a/2,p="",h="";return"bottom"===f?(p=o?u:`${c}px`,h=`${-a}px`):"top"===f?(p=o?u:`${c}px`,h=`${r.floating.height+a}px`):"right"===f?(p=`${-a}px`,h=o?u:`${d}px`):"left"===f&&(p=`${r.floating.width+a}px`,h=o?u:`${d}px`),{data:{x:p,y:h}}}});function t8(t){let[e,n="center"]=t.split("-");return[e,n]}var t6=tJ,et=tQ,ee=t2,en=t7},94977:function(t,e,n){n.d(e,{t:function(){return o}});var r=n(2265),i=n(51030);function o(t){let[e,n]=r.useState(void 0);return(0,i.b)(()=>{if(t){n({width:t.offsetWidth,height:t.offsetHeight});let e=new ResizeObserver(e=>{let r,i;if(!Array.isArray(e)||!e.length)return;let o=e[0];if("borderBoxSize"in o){let t=o.borderBoxSize,e=Array.isArray(t)?t[0]:t;r=e.inlineSize,i=e.blockSize}else r=t.offsetWidth,i=t.offsetHeight;n({width:r,height:i})});return e.observe(t,{box:"border-box"}),()=>e.unobserve(t)}n(void 0)},[t]),e}}}]);