(()=>{var e={};e.id=1930,e.ids=[1930],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},8296:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c});var r=s(50482),a=s(69108),n=s(62563),i=s.n(n),l=s(68300),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c=["",{children:["subscription",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,62047)),"C:\\proj\\nextjs-saas\\app\\subscription\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,59504)),"C:\\proj\\nextjs-saas\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\proj\\nextjs-saas\\app\\subscription\\page.tsx"],u="/subscription/page",m={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/subscription/page",pathname:"/subscription",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},30327:(e,t,s)=>{Promise.resolve().then(s.bind(s,55696))},64588:(e,t,s)=>{Promise.resolve().then(s.bind(s,56189)),Promise.resolve().then(s.bind(s,44669))},19634:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2583,23)),Promise.resolve().then(s.t.bind(s,26840,23)),Promise.resolve().then(s.t.bind(s,38771,23)),Promise.resolve().then(s.t.bind(s,13225,23)),Promise.resolve().then(s.t.bind(s,9295,23)),Promise.resolve().then(s.t.bind(s,43982,23))},55696:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>w});var r={};s.r(r);var a=s(95344),n=s(3729),i=s(47674),l=s(22254),o=s(61351),c=s(16212),d=s(69436),u=s(76461),m=s(25757),x=s(85674),p=s(55794),g=s(25545),h=s(89895),f=s(99046),y=s(46064),b=s(37121),v=s(74243),j=s(45961),N=s(34755);function w(){let{data:e}=(0,i.useSession)(),t=(0,l.useRouter)(),[s,w]=(0,n.useState)(null),[C,P]=(0,n.useState)(!0);(0,n.useEffect)(()=>{e?.user&&k()},[e]);let k=async()=>{try{let e=await fetch("/api/subscription"),t=await e.json();t.success?w(t.data):N.Am.error("Failed to load subscription data")}catch(e){console.error("Error fetching subscription:",e),N.Am.error("Failed to load subscription data")}finally{P(!1)}},S=async()=>{try{let e=await fetch("/api/subscription/portal",{method:"POST"}),t=await e.json();t.success?window.location.href=t.data.url:N.Am.error(t.error||"Failed to open billing portal")}catch(e){console.error("Error opening billing portal:",e),N.Am.error("Failed to open billing portal")}},Z=e=>e>=90?"bg-red-500":e>=75?"bg-yellow-500":"bg-green-500";if(C)return a.jsx("div",{className:"container mx-auto p-6",children:a.jsx("div",{className:"flex items-center justify-center h-64",children:a.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})})});if(!s?.hasActiveSubscription)return a.jsx("div",{className:"container mx-auto p-6",children:(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx(x.Z,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),a.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"No Active Subscription"}),a.jsx("p",{className:"text-gray-600 mb-6",children:"You don't have an active subscription. Choose a plan to get started."}),a.jsx(c.z,{onClick:()=>t.push("/pricing"),children:"View Pricing Plans"})]})});let{subscription:M,usage:R}=s;return(0,a.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Subscription"}),a.jsx("p",{className:"text-gray-600",children:"Manage your subscription and view usage"})]}),a.jsx(c.z,{variant:"outline",onClick:()=>t.push("/pricing"),children:"Change Plan"})]}),(0,a.jsxs)(m.mQ,{defaultValue:"overview",className:"space-y-6",children:[(0,a.jsxs)(m.dr,{children:[a.jsx(m.SP,{value:"overview",children:"Overview"}),a.jsx(m.SP,{value:"usage",children:"Usage"}),a.jsx(m.SP,{value:"billing",children:"Billing"})]}),(0,a.jsxs)(m.nU,{value:"overview",className:"space-y-6",children:[(0,a.jsxs)(o.Zb,{children:[a.jsx(o.Ol,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)(o.ll,{className:"flex items-center gap-2",children:[a.jsx(x.Z,{className:"h-5 w-5"}),"Current Plan"]}),a.jsx(o.SZ,{children:"Your active subscription details"})]}),a.jsx(d.C,{className:(e=>{switch(e){case"ACTIVE":return"bg-green-100 text-green-800";case"TRIALING":return"bg-blue-100 text-blue-800";case"PAST_DUE":return"bg-yellow-100 text-yellow-800";case"CANCELED":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}})(M.status),children:M.status})]})}),a.jsx(o.aY,{className:"space-y-4",children:(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-xl font-semibold",children:M.pricingPlan.name}),a.jsx("p",{className:"text-gray-600",children:M.pricingPlan.description}),(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsxs)("span",{className:"text-2xl font-bold",children:["$","YEARLY"===M.billingCycle?(M.pricingPlan.yearlyPrice/12).toFixed(0):M.pricingPlan.monthlyPrice]}),a.jsx("span",{className:"text-gray-500",children:"/month"}),"YEARLY"===M.billingCycle&&a.jsx(d.C,{variant:"secondary",className:"ml-2",children:"Billed Yearly"})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"flex items-center text-sm text-gray-600",children:[a.jsx(p.Z,{className:"h-4 w-4 mr-2"}),"TRIALING"===M.status?"Trial ends":"Next billing"]}),a.jsx("span",{className:"font-medium",children:M.daysUntilRenewal>0?`${M.daysUntilRenewal} days`:"Today"})]}),"TRIALING"===M.status&&a.jsx("div",{className:"p-3 bg-blue-50 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center text-blue-700",children:[a.jsx(g.Z,{className:"h-4 w-4 mr-2"}),a.jsx("span",{className:"text-sm font-medium",children:"Free trial active"})]})})]})]})})]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-3 gap-6",children:[a.jsx(o.Zb,{children:(0,a.jsxs)(o.aY,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-600",children:"Users"}),(0,a.jsxs)("p",{className:"text-2xl font-bold",children:[R.users.current,"/",R.users.limit]})]}),a.jsx(h.Z,{className:"h-8 w-8 text-blue-500"})]}),a.jsx(u.E,{value:R.users.percentage,className:"mt-3",style:{"--progress-background":Z(R.users.percentage)}})]})}),a.jsx(o.Zb,{children:(0,a.jsxs)(o.aY,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-600",children:"Storage"}),a.jsx("p",{className:"text-2xl font-bold",children:R.storage.currentFormatted}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:["of ",R.storage.limitFormatted]})]}),a.jsx(f.Z,{className:"h-8 w-8 text-green-500"})]}),a.jsx(u.E,{value:R.storage.percentage,className:"mt-3",style:{"--progress-background":Z(R.storage.percentage)}})]})}),a.jsx(o.Zb,{children:(0,a.jsxs)(o.aY,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-600",children:"Customers"}),(0,a.jsxs)("p",{className:"text-2xl font-bold",children:[R.customers.current,"/",R.customers.limit]})]}),a.jsx(y.Z,{className:"h-8 w-8 text-purple-500"})]}),a.jsx(u.E,{value:R.customers.percentage,className:"mt-3",style:{"--progress-background":Z(R.customers.percentage)}})]})})]})]}),a.jsx(m.nU,{value:"usage",className:"space-y-6",children:(0,a.jsxs)(o.Zb,{children:[(0,a.jsxs)(o.Ol,{children:[a.jsx(o.ll,{children:"Usage Details"}),a.jsx(o.SZ,{children:"Monitor your usage across all plan limits"})]}),a.jsx(o.aY,{children:a.jsx("div",{className:"space-y-6",children:[{key:"users",label:"Users",icon:h.Z},{key:"customers",label:"Customers",icon:y.Z},{key:"quotations",label:"Quotations",icon:b.Z},{key:"invoices",label:"Invoices",icon:v.Z},{key:"contracts",label:"Contracts",icon:r.FileContract},{key:"storage",label:"Storage",icon:f.Z}].map(({key:e,label:t,icon:s})=>{let r=R[e],n=r.percentage>=80;return(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(s,{className:"h-4 w-4 text-gray-500"}),a.jsx("span",{className:"font-medium",children:t}),n&&a.jsx(j.Z,{className:"h-4 w-4 text-yellow-500"})]}),a.jsx("span",{className:"text-sm text-gray-600",children:"storage"===e?`${r.currentFormatted} / ${r.limitFormatted}`:`${r.current} / ${r.limit}`})]}),a.jsx(u.E,{value:r.percentage,className:`h-2 ${n?"bg-yellow-100":""}`,style:{"--progress-background":Z(r.percentage)}}),(0,a.jsxs)("div",{className:"flex justify-between text-xs text-gray-500",children:[(0,a.jsxs)("span",{children:[r.percentage,"% used"]}),n&&a.jsx("span",{className:"text-yellow-600 font-medium",children:"Approaching limit"})]})]},e)})})})]})}),a.jsx(m.nU,{value:"billing",className:"space-y-6",children:(0,a.jsxs)(o.Zb,{children:[(0,a.jsxs)(o.Ol,{children:[a.jsx(o.ll,{children:"Billing Information"}),a.jsx(o.SZ,{children:"Manage your billing details and payment methods"})]}),a.jsx(o.aY,{children:M.stripeCustomerId?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-medium",children:"Payment Methods"}),a.jsx("p",{className:"text-sm text-gray-600",children:"Manage your payment methods and billing information"})]}),(0,a.jsxs)(c.z,{onClick:S,children:[a.jsx(x.Z,{className:"h-4 w-4 mr-2"}),"Manage Billing"]})]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"p-4 border rounded-lg",children:[a.jsx("h4",{className:"font-medium mb-2",children:"Current Plan"}),(0,a.jsxs)("p",{className:"text-2xl font-bold",children:["$","YEARLY"===M.billingCycle?(M.pricingPlan.yearlyPrice/12).toFixed(0):M.pricingPlan.monthlyPrice]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["per month ","YEARLY"===M.billingCycle&&"(billed annually)"]})]}),(0,a.jsxs)("div",{className:"p-4 border rounded-lg",children:[a.jsx("h4",{className:"font-medium mb-2",children:"Next Payment"}),a.jsx("p",{className:"text-lg font-semibold",children:M.daysUntilRenewal>0?`${M.daysUntilRenewal} days`:"Today"}),a.jsx("p",{className:"text-sm text-gray-600",children:new Date(M.currentPeriodEnd).toLocaleDateString()})]})]})]}):(0,a.jsxs)("div",{className:"text-center py-8",children:[a.jsx(x.Z,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Payment Method Required"}),a.jsx("p",{className:"text-gray-600 mb-4",children:"You're currently on a free plan that doesn't require payment."}),a.jsx(c.z,{variant:"outline",onClick:()=>t.push("/pricing"),children:"Upgrade Plan"})]})})]})})]})]})}},56189:(e,t,s)=>{"use strict";s.r(t),s.d(t,{Providers:()=>d});var r=s(95344),a=s(47674),n=s(6256),i=s(19115),l=s(26274),o=s(3729),c=s(66091);function d({children:e}){let[t]=(0,o.useState)(()=>new i.S({defaultOptions:{queries:{staleTime:6e4,retry:1}}}));return r.jsx(a.SessionProvider,{children:r.jsx(l.aH,{client:t,children:r.jsx(c.lY,{children:r.jsx(n.f,{attribute:"class",defaultTheme:"light",enableSystem:!0,disableTransitionOnChange:!0,children:e})})})})}},69436:(e,t,s)=>{"use strict";s.d(t,{C:()=>l});var r=s(95344);s(3729);var a=s(49247),n=s(91626);let i=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600",info:"border-transparent bg-blue-500 text-white hover:bg-blue-600"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,...s}){return r.jsx("div",{className:(0,n.cn)(i({variant:t}),e),...s})}},16212:(e,t,s)=>{"use strict";s.d(t,{z:()=>c});var r=s(95344),a=s(3729),n=s(32751),i=s(49247),l=s(91626);let o=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef(({className:e,variant:t,size:s,asChild:a=!1,...i},c)=>{let d=a?n.g7:"button";return r.jsx(d,{className:(0,l.cn)(o({variant:t,size:s,className:e})),ref:c,...i})});c.displayName="Button"},61351:(e,t,s)=>{"use strict";s.d(t,{Ol:()=>l,SZ:()=>c,Zb:()=>i,aY:()=>d,eW:()=>u,ll:()=>o});var r=s(95344),a=s(3729),n=s(91626);let i=a.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));i.displayName="Card";let l=a.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...t}));l.displayName="CardHeader";let o=a.forwardRef(({className:e,...t},s)=>r.jsx("h3",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));o.displayName="CardTitle";let c=a.forwardRef(({className:e,...t},s)=>r.jsx("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",e),...t}));c.displayName="CardDescription";let d=a.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,n.cn)("p-6 pt-0",e),...t}));d.displayName="CardContent";let u=a.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",e),...t}));u.displayName="CardFooter"},76461:(e,t,s)=>{"use strict";s.d(t,{E:()=>j});var r=s(95344),a=s(3729),n=s(98462),i=s(62409),l="Progress",[o,c]=(0,n.b)(l),[d,u]=o(l),m=a.forwardRef((e,t)=>{var s,a;let{__scopeProgress:n,value:l=null,max:o,getValueLabel:c=g,...u}=e;(o||0===o)&&!y(o)&&console.error((s=`${o}`,`Invalid prop \`max\` of value \`${s}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let m=y(o)?o:100;null===l||b(l,m)||console.error((a=`${l}`,`Invalid prop \`value\` of value \`${a}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let x=b(l,m)?l:null,p=f(x)?c(x,m):void 0;return(0,r.jsx)(d,{scope:n,value:x,max:m,children:(0,r.jsx)(i.WV.div,{"aria-valuemax":m,"aria-valuemin":0,"aria-valuenow":f(x)?x:void 0,"aria-valuetext":p,role:"progressbar","data-state":h(x,m),"data-value":x??void 0,"data-max":m,...u,ref:t})})});m.displayName=l;var x="ProgressIndicator",p=a.forwardRef((e,t)=>{let{__scopeProgress:s,...a}=e,n=u(x,s);return(0,r.jsx)(i.WV.div,{"data-state":h(n.value,n.max),"data-value":n.value??void 0,"data-max":n.max,...a,ref:t})});function g(e,t){return`${Math.round(e/t*100)}%`}function h(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function f(e){return"number"==typeof e}function y(e){return f(e)&&!isNaN(e)&&e>0}function b(e,t){return f(e)&&!isNaN(e)&&e<=t&&e>=0}p.displayName=x;var v=s(91626);let j=a.forwardRef(({className:e,value:t,...s},a)=>r.jsx(m,{ref:a,className:(0,v.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...s,children:r.jsx(p,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})}));j.displayName=m.displayName},25757:(e,t,s)=>{"use strict";s.d(t,{mQ:()=>M,nU:()=>T,dr:()=>R,SP:()=>E});var r=s(95344),a=s(3729),n=s(85222),i=s(98462),l=s(34504),o=s(43234),c=s(62409),d=s(3975),u=s(33183),m=s(99048),x="Tabs",[p,g]=(0,i.b)(x,[l.Pc]),h=(0,l.Pc)(),[f,y]=p(x),b=a.forwardRef((e,t)=>{let{__scopeTabs:s,value:a,onValueChange:n,defaultValue:i,orientation:l="horizontal",dir:o,activationMode:p="automatic",...g}=e,h=(0,d.gm)(o),[y,b]=(0,u.T)({prop:a,onChange:n,defaultProp:i??"",caller:x});return(0,r.jsx)(f,{scope:s,baseId:(0,m.M)(),value:y,onValueChange:b,orientation:l,dir:h,activationMode:p,children:(0,r.jsx)(c.WV.div,{dir:h,"data-orientation":l,...g,ref:t})})});b.displayName=x;var v="TabsList",j=a.forwardRef((e,t)=>{let{__scopeTabs:s,loop:a=!0,...n}=e,i=y(v,s),o=h(s);return(0,r.jsx)(l.fC,{asChild:!0,...o,orientation:i.orientation,dir:i.dir,loop:a,children:(0,r.jsx)(c.WV.div,{role:"tablist","aria-orientation":i.orientation,...n,ref:t})})});j.displayName=v;var N="TabsTrigger",w=a.forwardRef((e,t)=>{let{__scopeTabs:s,value:a,disabled:i=!1,...o}=e,d=y(N,s),u=h(s),m=k(d.baseId,a),x=S(d.baseId,a),p=a===d.value;return(0,r.jsx)(l.ck,{asChild:!0,...u,focusable:!i,active:p,children:(0,r.jsx)(c.WV.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":x,"data-state":p?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:m,...o,ref:t,onMouseDown:(0,n.M)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(a)}),onKeyDown:(0,n.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(a)}),onFocus:(0,n.M)(e.onFocus,()=>{let e="manual"!==d.activationMode;p||i||!e||d.onValueChange(a)})})})});w.displayName=N;var C="TabsContent",P=a.forwardRef((e,t)=>{let{__scopeTabs:s,value:n,forceMount:i,children:l,...d}=e,u=y(C,s),m=k(u.baseId,n),x=S(u.baseId,n),p=n===u.value,g=a.useRef(p);return a.useEffect(()=>{let e=requestAnimationFrame(()=>g.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,r.jsx)(o.z,{present:i||p,children:({present:s})=>(0,r.jsx)(c.WV.div,{"data-state":p?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":m,hidden:!s,id:x,tabIndex:0,...d,ref:t,style:{...e.style,animationDuration:g.current?"0s":void 0},children:s&&l})})});function k(e,t){return`${e}-trigger-${t}`}function S(e,t){return`${e}-content-${t}`}P.displayName=C;var Z=s(91626);let M=b,R=a.forwardRef(({className:e,...t},s)=>r.jsx(j,{ref:s,className:(0,Z.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));R.displayName=j.displayName;let E=a.forwardRef(({className:e,...t},s)=>r.jsx(w,{ref:s,className:(0,Z.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));E.displayName=w.displayName;let T=a.forwardRef(({className:e,...t},s)=>r.jsx(P,{ref:s,className:(0,Z.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));T.displayName=P.displayName},66091:(e,t,s)=>{"use strict";s.d(t,{TC:()=>o,lY:()=>l});var r=s(95344),a=s(3729);let n={appName:"SaaS Platform",logoUrl:"",faviconUrl:"",primaryColor:"#3b82f6",secondaryColor:"#64748b",accentColor:"#10b981",backgroundColor:"#ffffff",textColor:"#1f2937",theme:"light",fontFamily:"Inter, sans-serif",customCss:""},i=(0,a.createContext)(void 0);function l({children:e}){let[t,s]=(0,a.useState)(n),[l,o]=(0,a.useState)(!0);(0,a.useEffect)(()=>{c()},[]);let c=async()=>{try{let e=await fetch("/api/global-config/branding"),t=await e.json();t.success&&t.branding?(s({...n,...t.branding}),d({...n,...t.branding})):d(n)}catch(e){console.error("Error fetching branding config:",e),d(n)}finally{o(!1)}},d=e=>{let t=document.documentElement;if(t.style.setProperty("--primary-color",e.primaryColor),t.style.setProperty("--secondary-color",e.secondaryColor),t.style.setProperty("--accent-color",e.accentColor),t.style.setProperty("--background-color",e.backgroundColor),t.style.setProperty("--text-color",e.textColor),t.style.setProperty("--font-family",e.fontFamily),document.body.className=document.body.className.replace(/theme-\w+/g,""),document.body.classList.add(`theme-${e.theme}`),document.title=e.appName,e.faviconUrl){let t=document.querySelector('link[rel="icon"]');t||((t=document.createElement("link")).rel="icon",document.head.appendChild(t)),t.href=e.faviconUrl}let s=document.getElementById("custom-branding-css");e.customCss?(s||((s=document.createElement("style")).id="custom-branding-css",document.head.appendChild(s)),s.textContent=e.customCss):s&&s.remove();let r=document.querySelector('meta[name="theme-color"]');r||((r=document.createElement("meta")).name="theme-color",document.head.appendChild(r)),r.content=e.primaryColor};return r.jsx(i.Provider,{value:{branding:t,updateBranding:e=>{let r={...t,...e};s(r),d(r)},loading:l},children:e})}function o(){let e=(0,a.useContext)(i);if(void 0===e)throw Error("useBranding must be used within a BrandingProvider");return e}},91626:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n});var r=s(56815),a=s(79377);function n(...e){return(0,a.m6)((0,r.W)(e))}},45961:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(69224).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},55794:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(69224).Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},25545:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(69224).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},99046:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(69224).Z)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},37121:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(69224).Z)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]])},74243:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(69224).Z)("Receipt",[["path",{d:"M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1-2-1Z",key:"wqdwcb"}],["path",{d:"M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8",key:"1h4pet"}],["path",{d:"M12 17V7",key:"pyj7ub"}]])},46064:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(69224).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},89895:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(69224).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},22254:(e,t,s)=>{e.exports=s(14767)},59504:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>x,metadata:()=>m});var r=s(25036),a=s(80265),n=s.n(a),i=s(86843);let l=(0,i.createProxy)(String.raw`C:\proj\nextjs-saas\components\providers.tsx`),{__esModule:o,$$typeof:c}=l;l.default;let d=(0,i.createProxy)(String.raw`C:\proj\nextjs-saas\components\providers.tsx#Providers`);var u=s(69636);s(67272);let m={title:{default:"Business SaaS - Complete Business Management Solution",template:"%s | Business SaaS"},description:"Complete SaaS solution for business management including CRM, invoicing, quotations, and more.",keywords:["SaaS","Business Management","CRM","Invoicing","Quotations"],authors:[{name:"Business SaaS Team"}],creator:"Business SaaS",openGraph:{type:"website",locale:"en_US",url:process.env.NEXT_PUBLIC_APP_URL,title:"Business SaaS - Complete Business Management Solution",description:"Complete SaaS solution for business management including CRM, invoicing, quotations, and more.",siteName:"Business SaaS"},twitter:{card:"summary_large_image",title:"Business SaaS - Complete Business Management Solution",description:"Complete SaaS solution for business management including CRM, invoicing, quotations, and more.",creator:"@businesssaas"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function x({children:e}){return r.jsx("html",{lang:"en",suppressHydrationWarning:!0,children:r.jsx("body",{className:n().className,children:(0,r.jsxs)(d,{children:[e,r.jsx(u.x7,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#4ade80",secondary:"#fff"}},error:{duration:4e3,iconTheme:{primary:"#ef4444",secondary:"#fff"}}}})]})})})}},62047:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>n,__esModule:()=>a,default:()=>i});let r=(0,s(86843).createProxy)(String.raw`C:\proj\nextjs-saas\app\subscription\page.tsx`),{__esModule:a,$$typeof:n}=r,i=r.default},67272:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[1638,7948,6671,7792,4755],()=>s(8296));module.exports=r})();