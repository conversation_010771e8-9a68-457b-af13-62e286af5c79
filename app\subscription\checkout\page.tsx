'use client'

import { useState, useEffect, Suspense } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Label } from '@/components/ui/label'
import { 
  CreditCard, 
  Check, 
  Shield, 
  Clock,
  ArrowLeft,
  Users,
  Database,
  FileText
} from 'lucide-react'
import { toast } from 'sonner'

interface PricingPlan {
  id: string
  name: string
  description: string
  monthlyPrice: number
  yearlyPrice: number | null
  currency: string
  maxUsers: number
  maxStorage: number
  formattedStorage: string
  features: Record<string, boolean>
  trialDays: number
  yearlyDiscount: number
}

function CheckoutContent() {
  const { data: session } = useSession()
  const router = useRouter()
  const searchParams = useSearchParams()
  const [plan, setPlan] = useState<PricingPlan | null>(null)
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly')
  const [loading, setLoading] = useState(true)
  const [processing, setProcessing] = useState(false)

  const planId = searchParams.get('plan')
  const initialBilling = searchParams.get('billing') as 'monthly' | 'yearly' || 'monthly'

  useEffect(() => {
    setBillingCycle(initialBilling)
  }, [initialBilling])

  useEffect(() => {
    if (planId) {
      fetchPlan()
    } else {
      router.push('/pricing')
    }
  }, [planId])

  const fetchPlan = async () => {
    try {
      const response = await fetch(`/api/pricing-plans/${planId}`)
      const data = await response.json()
      
      if (data.success) {
        setPlan(data.data)
      } else {
        toast.error('Plan not found')
        router.push('/pricing')
      }
    } catch (error) {
      console.error('Error fetching plan:', error)
      toast.error('Failed to load plan details')
      router.push('/pricing')
    } finally {
      setLoading(false)
    }
  }

  const handleSubscribe = async () => {
    if (!plan || !session?.user) return

    setProcessing(true)
    try {
      const response = await fetch('/api/subscription', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          planId: plan.id,
          billingCycle: billingCycle.toUpperCase(),
          useStripe: plan.monthlyPrice > 0 // Only use Stripe for paid plans
        })
      })

      const data = await response.json()

      if (data.success) {
        if (data.data.checkoutUrl) {
          // Redirect to Stripe checkout for paid plans
          window.location.href = data.data.checkoutUrl
        } else {
          // Direct subscription creation for free plans
          toast.success('Subscription created successfully!')
          router.push('/subscription')
        }
      } else {
        toast.error(data.error || 'Failed to create subscription')
      }
    } catch (error) {
      console.error('Error creating subscription:', error)
      toast.error('Failed to create subscription')
    } finally {
      setProcessing(false)
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    )
  }

  if (!plan) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Plan not found</h2>
          <Button onClick={() => router.push('/pricing')}>
            Back to Pricing
          </Button>
        </div>
      </div>
    )
  }

  const price = billingCycle === 'yearly' && plan.yearlyPrice 
    ? plan.yearlyPrice / 12 
    : plan.monthlyPrice
  const totalPrice = billingCycle === 'yearly' && plan.yearlyPrice 
    ? plan.yearlyPrice 
    : plan.monthlyPrice
  const hasDiscount = billingCycle === 'yearly' && plan.yearlyDiscount > 0

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-8">
        <Button 
          variant="ghost" 
          size="sm"
          onClick={() => router.back()}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Complete Your Subscription</h1>
          <p className="text-gray-600">Review your plan and start your subscription</p>
        </div>
      </div>

      <div className="grid lg:grid-cols-2 gap-8">
        {/* Plan Details */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                {plan.name}
                {plan.name.toLowerCase() === 'pro' && (
                  <Badge>Most Popular</Badge>
                )}
              </CardTitle>
              <CardDescription>{plan.description}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Billing Cycle Selection */}
              <div>
                <Label className="text-base font-medium">Billing Cycle</Label>
                <RadioGroup 
                  value={billingCycle} 
                  onValueChange={(value) => setBillingCycle(value as 'monthly' | 'yearly')}
                  className="mt-2"
                >
                  <div className="flex items-center space-x-2 p-3 border rounded-lg">
                    <RadioGroupItem value="monthly" id="monthly" />
                    <Label htmlFor="monthly" className="flex-1 cursor-pointer">
                      <div className="flex justify-between items-center">
                        <span>Monthly</span>
                        <span className="font-medium">${plan.monthlyPrice}/month</span>
                      </div>
                    </Label>
                  </div>
                  
                  {plan.yearlyPrice && (
                    <div className="flex items-center space-x-2 p-3 border rounded-lg">
                      <RadioGroupItem value="yearly" id="yearly" />
                      <Label htmlFor="yearly" className="flex-1 cursor-pointer">
                        <div className="flex justify-between items-center">
                          <div className="flex items-center gap-2">
                            <span>Yearly</span>
                            {hasDiscount && (
                              <Badge variant="secondary" className="text-xs">
                                {plan.yearlyDiscount}% off
                              </Badge>
                            )}
                          </div>
                          <div className="text-right">
                            <div className="font-medium">${(plan.yearlyPrice / 12).toFixed(0)}/month</div>
                            <div className="text-xs text-gray-500">
                              ${plan.yearlyPrice}/year
                            </div>
                          </div>
                        </div>
                      </Label>
                    </div>
                  )}
                </RadioGroup>
              </div>

              <Separator />

              {/* Plan Features */}
              <div>
                <h4 className="font-medium mb-3">What's included:</h4>
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span className="flex items-center">
                      <Users className="h-4 w-4 mr-2 text-gray-400" />
                      Users
                    </span>
                    <span className="font-medium">{plan.maxUsers}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="flex items-center">
                      <Database className="h-4 w-4 mr-2 text-gray-400" />
                      Storage
                    </span>
                    <span className="font-medium">{plan.formattedStorage}</span>
                  </div>
                  
                  {Object.entries(plan.features).map(([feature, enabled]) => (
                    enabled && (
                      <div key={feature} className="flex items-center text-sm">
                        <Check className="h-4 w-4 mr-2 text-green-500" />
                        <span>{feature.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}</span>
                      </div>
                    )
                  ))}
                </div>
              </div>

              {plan.trialDays > 0 && (
                <div className="p-4 bg-blue-50 rounded-lg">
                  <div className="flex items-center text-blue-700">
                    <Clock className="h-4 w-4 mr-2" />
                    <span className="text-sm font-medium">
                      {plan.trialDays}-day free trial included
                    </span>
                  </div>
                  <p className="text-xs text-blue-600 mt-1">
                    You won't be charged until your trial ends
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Order Summary */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Order Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between">
                <span>{plan.name} Plan</span>
                <span>${price.toFixed(0)}/month</span>
              </div>
              
              {billingCycle === 'yearly' && (
                <>
                  <div className="flex justify-between text-sm text-gray-600">
                    <span>Billing cycle</span>
                    <span>Annual</span>
                  </div>
                  {hasDiscount && (
                    <div className="flex justify-between text-sm text-green-600">
                      <span>Annual discount</span>
                      <span>-{plan.yearlyDiscount}%</span>
                    </div>
                  )}
                </>
              )}
              
              <Separator />
              
              <div className="flex justify-between font-medium text-lg">
                <span>Total {billingCycle === 'yearly' ? 'per year' : 'per month'}</span>
                <span>${totalPrice}</span>
              </div>

              {plan.trialDays > 0 && (
                <div className="text-sm text-gray-600">
                  <p>Free for {plan.trialDays} days, then ${totalPrice} {billingCycle === 'yearly' ? 'per year' : 'per month'}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Security Notice */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center text-sm text-gray-600">
                <Shield className="h-4 w-4 mr-2 text-green-500" />
                <span>Secure checkout powered by industry-standard encryption</span>
              </div>
            </CardContent>
          </Card>

          {/* Subscribe Button */}
          <Button 
            className="w-full" 
            size="lg"
            onClick={handleSubscribe}
            disabled={processing}
          >
            {processing ? (
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Processing...
              </div>
            ) : (
              <div className="flex items-center">
                <CreditCard className="h-4 w-4 mr-2" />
                {plan.trialDays > 0 ? 'Start Free Trial' : 'Subscribe Now'}
              </div>
            )}
          </Button>

          <p className="text-xs text-gray-500 text-center">
            By subscribing, you agree to our Terms of Service and Privacy Policy.
            You can cancel anytime.
          </p>
        </div>
      </div>
    </div>
  )
}

export default function CheckoutPage() {
  return (
    <Suspense fallback={
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    }>
      <CheckoutContent />
    </Suspense>
  )
}
