import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const accountSchema = z.object({
  name: z.string().min(1, 'Account name is required'),
  accountNumber: z.string().optional(),
  accountType: z.enum(['ASSET', 'LIABILITY', 'EQUITY', 'REVENUE', 'EXPENSE']),
  accountSubType: z.string().optional(),
  description: z.string().optional(),
  balance: z.number().default(0),
  currency: z.string().default('USD'),
  isActive: z.boolean().default(true),
  bankName: z.string().optional(),
  routingNumber: z.string().optional(),
  accountNumberMask: z.string().optional(),
  parentAccountId: z.string().optional(),
  normalBalance: z.enum(['DEBIT', 'CREDIT']).default('DEBIT'),
  taxReportingCode: z.string().optional(),
  isSystemAccount: z.boolean().default(false)
})

// GET /api/accounts - Get all financial accounts
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const search = searchParams.get('search') || ''
    const accountType = searchParams.get('accountType') || ''
    const status = searchParams.get('status') || ''

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {
      companyId: session.user.companyId
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { accountNumber: { contains: search, mode: 'insensitive' } },
        { accountSubType: { contains: search, mode: 'insensitive' } }
      ]
    }

    if (accountType) {
      where.accountType = accountType
    }

    if (status === 'active') {
      where.isActive = true
    } else if (status === 'inactive') {
      where.isActive = false
    }

    const [accounts, total] = await Promise.all([
      prisma.financialAccount.findMany({
        where,
        include: {
          parentAccount: {
            select: {
              id: true,
              name: true,
              accountType: true
            }
          },
          childAccounts: {
            select: {
              id: true,
              name: true,
              accountType: true,
              balance: true
            }
          },
          createdBy: {
            select: {
              name: true,
              email: true
            }
          },
          _count: {
            select: {
              accountTransactions: true,
              budgets: true
            }
          }
        },
        orderBy: [
          { accountType: 'asc' },
          { name: 'asc' }
        ],
        skip,
        take: limit
      }),
      prisma.financialAccount.count({ where })
    ])

    // Get recent transactions for each account
    const accountsWithTransactions = await Promise.all(
      accounts.map(async (account) => {
        const recentTransactions = await prisma.accountTransaction.findMany({
          where: {
            OR: [
              { debitAccountId: account.id },
              { creditAccountId: account.id }
            ]
          },
          select: {
            id: true,
            transactionNumber: true,
            description: true,
            amount: true,
            transactionDate: true,
            transactionType: true,
            status: true
          },
          orderBy: { transactionDate: 'desc' },
          take: 5
        })

        return {
          id: account.id,
          name: account.name,
          accountNumber: account.accountNumber,
          accountType: account.accountType,
          accountSubType: account.accountSubType,
          description: account.description,
          balance: Number(account.balance),
          currency: account.currency,
          isActive: account.isActive,
          bankName: account.bankName,
          routingNumber: account.routingNumber,
          accountNumberMask: account.accountNumberMask,
          parentAccount: account.parentAccount,
          childAccounts: account.childAccounts.map(child => ({
            ...child,
            balance: Number(child.balance)
          })),
          normalBalance: account.normalBalance,
          taxReportingCode: account.taxReportingCode,
          isSystemAccount: account.isSystemAccount,
          createdBy: account.createdBy,
          createdAt: account.createdAt,
          updatedAt: account.updatedAt,
          transactionCount: account._count.accountTransactions,
          budgetCount: account._count.budgets,
          recentTransactions: recentTransactions.map(tx => ({
            ...tx,
            amount: Number(tx.amount)
          }))
        }
      })
    )

    return NextResponse.json({
      accounts: accountsWithTransactions,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Error fetching accounts:', error)
    return NextResponse.json(
      { error: 'Failed to fetch accounts' },
      { status: 500 }
    )
  }
}

// POST /api/accounts - Create new financial account
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = accountSchema.parse(body)

    // Check for duplicate account number if provided
    if (validatedData.accountNumber) {
      const existingAccount = await prisma.financialAccount.findFirst({
        where: {
          accountNumber: validatedData.accountNumber,
          companyId: session.user.companyId
        }
      })

      if (existingAccount) {
        return NextResponse.json(
          { error: 'An account with this account number already exists' },
          { status: 400 }
        )
      }
    }

    // Validate parent account if provided
    if (validatedData.parentAccountId) {
      const parentAccount = await prisma.financialAccount.findFirst({
        where: {
          id: validatedData.parentAccountId,
          companyId: session.user.companyId
        }
      })

      if (!parentAccount) {
        return NextResponse.json(
          { error: 'Parent account not found' },
          { status: 400 }
        )
      }

      // Ensure parent account is of compatible type
      if (parentAccount.accountType !== validatedData.accountType) {
        return NextResponse.json(
          { error: 'Parent account must be of the same account type' },
          { status: 400 }
        )
      }
    }

    // Set default normal balance based on account type
    if (!validatedData.normalBalance) {
      switch (validatedData.accountType) {
        case 'ASSET':
        case 'EXPENSE':
          validatedData.normalBalance = 'DEBIT'
          break
        case 'LIABILITY':
        case 'EQUITY':
        case 'REVENUE':
          validatedData.normalBalance = 'CREDIT'
          break
      }
    }

    const account = await prisma.$transaction(async (tx) => {
      // Create the account
      const newAccount = await tx.financialAccount.create({
        data: {
          ...validatedData,
          companyId: session.user.companyId!,
          createdById: session.user.id
        },
        include: {
          parentAccount: {
            select: {
              id: true,
              name: true,
              accountType: true
            }
          },
          createdBy: {
            select: {
              name: true,
              email: true
            }
          }
        }
      })

      // Log activity
      await tx.activity.create({
        data: {
          type: 'ACCOUNT',
          title: 'Financial Account Created',
          description: `Financial account "${validatedData.name}" was created`,
          companyId: session.user.companyId!,
          createdById: session.user.id
        }
      })

      return newAccount
    })

    return NextResponse.json({
      account: {
        id: account.id,
        name: account.name,
        accountNumber: account.accountNumber,
        accountType: account.accountType,
        accountSubType: account.accountSubType,
        description: account.description,
        balance: Number(account.balance),
        currency: account.currency,
        isActive: account.isActive,
        bankName: account.bankName,
        routingNumber: account.routingNumber,
        accountNumberMask: account.accountNumberMask,
        parentAccount: account.parentAccount,
        normalBalance: account.normalBalance,
        taxReportingCode: account.taxReportingCode,
        isSystemAccount: account.isSystemAccount,
        createdBy: account.createdBy,
        createdAt: account.createdAt,
        updatedAt: account.updatedAt,
        transactionCount: 0,
        budgetCount: 0,
        recentTransactions: []
      },
      message: 'Financial account created successfully'
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating account:', error)
    return NextResponse.json(
      { error: 'Failed to create account' },
      { status: 500 }
    )
  }
}
