(()=>{var e={};e.id=4565,e.ids=[4565],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},59975:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>o,routeModule:()=>p,tree:()=>d});var t=a(50482),r=a(69108),i=a(62563),n=a.n(i),l=a(68300),c={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);a.d(s,c);let d=["",{children:["super-admin",{children:["pricing-plans",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,36343)),"C:\\proj\\nextjs-saas\\app\\super-admin\\pricing-plans\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,11285)),"C:\\proj\\nextjs-saas\\app\\super-admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,59504)),"C:\\proj\\nextjs-saas\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,69361,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\proj\\nextjs-saas\\app\\super-admin\\pricing-plans\\page.tsx"],m="/super-admin/pricing-plans/page",x={require:a,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/super-admin/pricing-plans/page",pathname:"/super-admin/pricing-plans",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},80398:(e,s,a)=>{Promise.resolve().then(a.bind(a,15616))},15616:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>R});var t=a(95344),r=a(3729),i=a(47674),n=a(22254),l=a(61351),c=a(16212),d=a(69436),o=a(71809),m=a(81036),x=a(16802),p=a(48411),u=a(33733),h=a(51838),f=a(7060),j=a(53148),y=a(76755),g=a(89895),b=a(99046),v=a(75695),N=a(38271),w=a(44669),C=a(1586),P=a(92549),k=a(93601),S=a(84332),_=a(17470);function I({formData:e,setFormData:s,onSubmit:a,onCancel:r,submitLabel:i}){let n=e=>1073741824*(parseFloat(e)||1);return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(C._,{htmlFor:"name",children:"Plan Name"}),t.jsx(P.I,{id:"name",value:e.name,onChange:e=>s(s=>({...s,name:e.target.value})),placeholder:"e.g., Pro Plan"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(C._,{htmlFor:"currency",children:"Currency"}),(0,t.jsxs)(_.Ph,{value:e.currency,onValueChange:e=>s(s=>({...s,currency:e})),children:[t.jsx(_.i4,{children:t.jsx(_.ki,{})}),(0,t.jsxs)(_.Bw,{children:[t.jsx(_.Ql,{value:"USD",children:"USD"}),t.jsx(_.Ql,{value:"EUR",children:"EUR"}),t.jsx(_.Ql,{value:"GBP",children:"GBP"})]})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(C._,{htmlFor:"description",children:"Description"}),t.jsx(k.g,{id:"description",value:e.description,onChange:e=>s(s=>({...s,description:e.target.value})),placeholder:"Brief description of the plan",rows:3})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(C._,{htmlFor:"monthlyPrice",children:"Monthly Price"}),t.jsx(P.I,{id:"monthlyPrice",type:"number",value:e.monthlyPrice,onChange:e=>s(s=>({...s,monthlyPrice:parseFloat(e.target.value)||0})),placeholder:"0.00"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(C._,{htmlFor:"yearlyPrice",children:"Yearly Price"}),t.jsx(P.I,{id:"yearlyPrice",type:"number",value:e.yearlyPrice,onChange:e=>s(s=>({...s,yearlyPrice:parseFloat(e.target.value)||0})),placeholder:"0.00"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(C._,{htmlFor:"trialDays",children:"Trial Days"}),t.jsx(P.I,{id:"trialDays",type:"number",value:e.trialDays,onChange:e=>s(s=>({...s,trialDays:parseInt(e.target.value)||0})),placeholder:"0"})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[t.jsx("h3",{className:"text-lg font-medium",children:"Usage Limits"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(C._,{htmlFor:"maxUsers",children:"Max Users"}),t.jsx(P.I,{id:"maxUsers",type:"number",value:e.maxUsers,onChange:e=>s(s=>({...s,maxUsers:parseInt(e.target.value)||1}))})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(C._,{htmlFor:"maxCompanies",children:"Max Companies"}),t.jsx(P.I,{id:"maxCompanies",type:"number",value:e.maxCompanies,onChange:e=>s(s=>({...s,maxCompanies:parseInt(e.target.value)||1}))})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(C._,{htmlFor:"maxCustomers",children:"Max Customers"}),t.jsx(P.I,{id:"maxCustomers",type:"number",value:e.maxCustomers,onChange:e=>s(s=>({...s,maxCustomers:parseInt(e.target.value)||10}))})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(C._,{htmlFor:"maxQuotations",children:"Max Quotations"}),t.jsx(P.I,{id:"maxQuotations",type:"number",value:e.maxQuotations,onChange:e=>s(s=>({...s,maxQuotations:parseInt(e.target.value)||5}))})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(C._,{htmlFor:"maxInvoices",children:"Max Invoices"}),t.jsx(P.I,{id:"maxInvoices",type:"number",value:e.maxInvoices,onChange:e=>s(s=>({...s,maxInvoices:parseInt(e.target.value)||5}))})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(C._,{htmlFor:"maxContracts",children:"Max Contracts"}),t.jsx(P.I,{id:"maxContracts",type:"number",value:e.maxContracts,onChange:e=>s(s=>({...s,maxContracts:parseInt(e.target.value)||1}))})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(C._,{htmlFor:"maxStorage",children:"Max Storage (GB)"}),t.jsx(P.I,{id:"maxStorage",type:"number",value:(e.maxStorage/1073741824).toString(),onChange:e=>s(s=>({...s,maxStorage:n(e.target.value)})),placeholder:"1"})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[t.jsx("h3",{className:"text-lg font-medium",children:"Features"}),t.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:Object.entries(e.features).map(([e,a])=>(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(S.X,{id:e,checked:a,onCheckedChange:a=>s(s=>({...s,features:{...s.features,[e]:a}}))}),t.jsx(C._,{htmlFor:e,className:"text-sm",children:e.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase())})]},e))})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[t.jsx("h3",{className:"text-lg font-medium",children:"Settings"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(S.X,{id:"isActive",checked:e.isActive,onCheckedChange:e=>s(s=>({...s,isActive:e}))}),t.jsx(C._,{htmlFor:"isActive",children:"Active"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(S.X,{id:"isPublic",checked:e.isPublic,onCheckedChange:e=>s(s=>({...s,isPublic:e}))}),t.jsx(C._,{htmlFor:"isPublic",children:"Public"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(C._,{htmlFor:"sortOrder",children:"Sort Order"}),t.jsx(P.I,{id:"sortOrder",type:"number",value:e.sortOrder,onChange:e=>s(s=>({...s,sortOrder:parseInt(e.target.value)||0}))})]})]})]}),(0,t.jsxs)(x.cN,{children:[t.jsx(c.z,{variant:"outline",onClick:r,children:"Cancel"}),t.jsx(c.z,{onClick:a,disabled:!e.name.trim(),children:i})]})]})}function R(){let{data:e,status:s}=(0,i.useSession)(),[a,C]=(0,r.useState)([]),[P,k]=(0,r.useState)(!0),[S,_]=(0,r.useState)(!1),[R,F]=(0,r.useState)(!1),[Z,D]=(0,r.useState)(null),[M,A]=(0,r.useState)({name:"",description:"",monthlyPrice:0,yearlyPrice:0,currency:"USD",maxUsers:1,maxCompanies:1,maxCustomers:10,maxQuotations:5,maxInvoices:5,maxContracts:1,maxStorage:1073741824,isActive:!0,isPublic:!0,trialDays:0,sortOrder:0,features:{basicReporting:!0,emailSupport:!0,mobileApp:!1,advancedAnalytics:!1,customBranding:!1,apiAccess:!1,prioritySupport:!1,customIntegrations:!1,advancedSecurity:!1,dedicatedManager:!1}});if("loading"===s)return t.jsx("div",{className:"min-h-screen flex items-center justify-center",children:t.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})});"unauthenticated"===s&&(0,n.redirect)("/auth/signin"),e?.user?.role!=="SUPER_ADMIN"&&(0,n.redirect)("/dashboard");let E=async()=>{try{k(!0);let e=await fetch("/api/pricing-plans?includeInactive=true");if(!e.ok)throw Error(`HTTP error! status: ${e.status}`);let s=await e.json();console.log("Fetched plans data:",s),s.success&&s.data?(C(s.data),w.toast.success(`Loaded ${s.data.length} pricing plans`)):(console.error("API response error:",s),w.toast.error(s.error||"Failed to load pricing plans"))}catch(e){console.error("Error fetching plans:",e),w.toast.error("Failed to load pricing plans")}finally{k(!1)}};(0,r.useEffect)(()=>{E()},[]);let T=async(e,s)=>{try{let a=await fetch(`/api/pricing-plans/${e}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({isActive:s})}),t=await a.json();t.success?(w.toast.success(`Plan ${s?"activated":"deactivated"} successfully`),E()):w.toast.error(t.error||"Failed to update plan")}catch(e){console.error("Error updating plan:",e),w.toast.error("Failed to update plan")}},U=async()=>{try{let e=await fetch("/api/pricing-plans",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(M)}),s=await e.json();s.success?(w.toast.success("Plan created successfully"),_(!1),B(),E()):w.toast.error(s.error||"Failed to create plan")}catch(e){console.error("Error creating plan:",e),w.toast.error("Failed to create plan")}},O=e=>{D(e),A({name:e.name,description:e.description,monthlyPrice:e.monthlyPrice,yearlyPrice:e.yearlyPrice||0,currency:e.currency,maxUsers:e.maxUsers,maxCompanies:e.maxCompanies,maxCustomers:e.maxCustomers,maxQuotations:e.maxQuotations,maxInvoices:e.maxInvoices,maxContracts:e.maxContracts,maxStorage:e.maxStorage,isActive:e.isActive,isPublic:e.isPublic,trialDays:e.trialDays,sortOrder:e.sortOrder,features:e.features}),F(!0)},q=async()=>{if(Z)try{let e=await fetch(`/api/pricing-plans/${Z.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(M)}),s=await e.json();s.success?(w.toast.success("Plan updated successfully"),F(!1),D(null),B(),E()):w.toast.error(s.error||"Failed to update plan")}catch(e){console.error("Error updating plan:",e),w.toast.error("Failed to update plan")}},z=async e=>{if(confirm("Are you sure you want to delete this pricing plan? This action cannot be undone."))try{let s=await fetch(`/api/pricing-plans/${e}`,{method:"DELETE"}),a=await s.json();a.success?(w.toast.success("Plan deleted successfully"),E()):w.toast.error(a.error||"Failed to delete plan")}catch(e){console.error("Error deleting plan:",e),w.toast.error("Failed to delete plan")}},B=()=>{A({name:"",description:"",monthlyPrice:0,yearlyPrice:0,currency:"USD",maxUsers:1,maxCompanies:1,maxCustomers:10,maxQuotations:5,maxInvoices:5,maxContracts:1,maxStorage:1073741824,isActive:!0,isPublic:!0,trialDays:0,sortOrder:0,features:{basicReporting:!0,emailSupport:!0,mobileApp:!1,advancedAnalytics:!1,customBranding:!1,apiAccess:!1,prioritySupport:!1,customIntegrations:!1,advancedSecurity:!1,dedicatedManager:!1}})},Q=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:0}).format(e),$=e=>Object.values(e).filter(Boolean).length;return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[t.jsx(p.Z,{className:"h-8 w-8 text-blue-600"}),t.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Pricing Plans Management"})]}),t.jsx("p",{className:"text-gray-500 mt-1",children:"Manage subscription plans, pricing, and features"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(c.z,{variant:"outline",onClick:E,disabled:P,children:[t.jsx(u.Z,{className:`h-4 w-4 mr-2 ${P?"animate-spin":""}`}),"Refresh"]}),(0,t.jsxs)(c.z,{onClick:()=>_(!0),children:[t.jsx(h.Z,{className:"h-4 w-4 mr-2"}),"Add Plan"]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[t.jsx(l.Zb,{children:t.jsx(l.aY,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm text-gray-500",children:"Total Plans"}),t.jsx("p",{className:"text-2xl font-bold text-gray-900",children:a.length})]}),t.jsx(p.Z,{className:"h-8 w-8 text-blue-600"})]})})}),t.jsx(l.Zb,{children:t.jsx(l.aY,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm text-gray-500",children:"Active Plans"}),t.jsx("p",{className:"text-2xl font-bold text-green-600",children:a.filter(e=>e.isActive).length})]}),t.jsx(f.Z,{className:"h-8 w-8 text-green-600"})]})})}),t.jsx(l.Zb,{children:t.jsx(l.aY,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm text-gray-500",children:"Public Plans"}),t.jsx("p",{className:"text-2xl font-bold text-purple-600",children:a.filter(e=>e.isPublic).length})]}),t.jsx(j.Z,{className:"h-8 w-8 text-purple-600"})]})})})]}),(0,t.jsxs)(l.Zb,{children:[(0,t.jsxs)(l.Ol,{children:[(0,t.jsxs)(l.ll,{children:["Pricing Plans (",a.length,")"]}),t.jsx(l.SZ,{children:"Manage your subscription plans, pricing, and features"})]}),t.jsx(l.aY,{children:P?t.jsx("div",{className:"flex items-center justify-center py-8",children:t.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):t.jsx("div",{className:"overflow-x-auto",children:(0,t.jsxs)(m.iA,{children:[t.jsx(m.xD,{children:(0,t.jsxs)(m.SC,{children:[t.jsx(m.ss,{children:"Plan"}),t.jsx(m.ss,{children:"Pricing"}),t.jsx(m.ss,{children:"Limits"}),t.jsx(m.ss,{children:"Features"}),t.jsx(m.ss,{children:"Status"}),t.jsx(m.ss,{children:"Actions"})]})}),t.jsx(m.RM,{children:a.map(e=>(0,t.jsxs)(m.SC,{children:[t.jsx(m.pj,{children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[t.jsx("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold",children:e.name.charAt(0)}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx("p",{className:"font-medium text-gray-900",children:e.name}),"pro"===e.name.toLowerCase()&&t.jsx(y.Z,{className:"h-4 w-4 text-yellow-500"})]}),t.jsx("p",{className:"text-sm text-gray-500",children:e.description})]})]})}),t.jsx(m.pj,{children:(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsxs)("p",{className:"font-medium",children:[Q(e.monthlyPrice),"/month"]}),e.yearlyPrice&&(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:[Q(e.yearlyPrice),"/year (",e.yearlyDiscount,"% off)"]}),e.trialDays>0&&(0,t.jsxs)(d.C,{variant:"secondary",className:"text-xs mt-1",children:[e.trialDays,"-day trial"]})]})}),t.jsx(m.pj,{children:(0,t.jsxs)("div",{className:"text-sm space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[t.jsx(g.Z,{className:"h-3 w-3 text-gray-400"}),(0,t.jsxs)("span",{children:[e.maxUsers," users"]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[t.jsx(b.Z,{className:"h-3 w-3 text-gray-400"}),t.jsx("span",{children:e.formattedStorage})]}),(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:[e.maxCustomers," customers, ",e.maxQuotations," quotes"]})]})}),t.jsx(m.pj,{children:(0,t.jsxs)(d.C,{variant:"outline",children:[$(e.features)," features"]})}),t.jsx(m.pj,{children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(o.r,{checked:e.isActive,onCheckedChange:s=>T(e.id,s)}),t.jsx("span",{className:"text-sm",children:e.isActive?"Active":"Inactive"})]}),e.isPublic&&t.jsx(d.C,{variant:"secondary",className:"text-xs",children:"Public"})]})}),t.jsx(m.pj,{children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(c.z,{variant:"ghost",size:"sm",onClick:()=>O(e),title:"Edit plan",children:t.jsx(v.Z,{className:"h-4 w-4"})}),t.jsx(c.z,{variant:"ghost",size:"sm",onClick:()=>z(e.id),title:"Delete plan",children:t.jsx(N.Z,{className:"h-4 w-4"})})]})})]},e.id))})]})})})]}),t.jsx(x.Vq,{open:S,onOpenChange:_,children:(0,t.jsxs)(x.cZ,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)(x.fK,{children:[t.jsx(x.$N,{children:"Create New Pricing Plan"}),t.jsx(x.Be,{children:"Add a new subscription plan with custom pricing and features."})]}),t.jsx(I,{formData:M,setFormData:A,onSubmit:U,onCancel:()=>{_(!1),B()},submitLabel:"Create Plan"})]})}),t.jsx(x.Vq,{open:R,onOpenChange:F,children:(0,t.jsxs)(x.cZ,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)(x.fK,{children:[t.jsx(x.$N,{children:"Edit Pricing Plan"}),t.jsx(x.Be,{children:"Modify the pricing plan details and features."})]}),t.jsx(I,{formData:M,setFormData:A,onSubmit:q,onCancel:()=>{F(!1),D(null),B()},submitLabel:"Update Plan"})]})})]})}},84332:(e,s,a)=>{"use strict";a.d(s,{X:()=>R});var t=a(95344),r=a(3729),i=a(31405),n=a(98462),l=a(85222),c=a(33183),d=a(92062),o=a(63085),m=a(43234),x=a(62409),p="Checkbox",[u,h]=(0,n.b)(p),[f,j]=u(p);function y(e){let{__scopeCheckbox:s,checked:a,children:i,defaultChecked:n,disabled:l,form:d,name:o,onCheckedChange:m,required:x,value:u="on",internal_do_not_use_render:h}=e,[j,y]=(0,c.T)({prop:a,defaultProp:n??!1,onChange:m,caller:p}),[g,b]=r.useState(null),[v,N]=r.useState(null),w=r.useRef(!1),C=!g||!!d||!!g.closest("form"),P={checked:j,disabled:l,setChecked:y,control:g,setControl:b,name:o,form:d,value:u,hasConsumerStoppedPropagationRef:w,required:x,defaultChecked:!k(n)&&n,isFormControl:C,bubbleInput:v,setBubbleInput:N};return(0,t.jsx)(f,{scope:s,...P,children:"function"==typeof h?h(P):i})}var g="CheckboxTrigger",b=r.forwardRef(({__scopeCheckbox:e,onKeyDown:s,onClick:a,...n},c)=>{let{control:d,value:o,disabled:m,checked:p,required:u,setControl:h,setChecked:f,hasConsumerStoppedPropagationRef:y,isFormControl:b,bubbleInput:v}=j(g,e),N=(0,i.e)(c,h),w=r.useRef(p);return r.useEffect(()=>{let e=d?.form;if(e){let s=()=>f(w.current);return e.addEventListener("reset",s),()=>e.removeEventListener("reset",s)}},[d,f]),(0,t.jsx)(x.WV.button,{type:"button",role:"checkbox","aria-checked":k(p)?"mixed":p,"aria-required":u,"data-state":S(p),"data-disabled":m?"":void 0,disabled:m,value:o,...n,ref:N,onKeyDown:(0,l.M)(s,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,l.M)(a,e=>{f(e=>!!k(e)||!e),v&&b&&(y.current=e.isPropagationStopped(),y.current||e.stopPropagation())})})});b.displayName=g;var v=r.forwardRef((e,s)=>{let{__scopeCheckbox:a,name:r,checked:i,defaultChecked:n,required:l,disabled:c,value:d,onCheckedChange:o,form:m,...x}=e;return(0,t.jsx)(y,{__scopeCheckbox:a,checked:i,defaultChecked:n,disabled:c,required:l,onCheckedChange:o,name:r,form:m,value:d,internal_do_not_use_render:({isFormControl:e})=>(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(b,{...x,ref:s,__scopeCheckbox:a}),e&&(0,t.jsx)(P,{__scopeCheckbox:a})]})})});v.displayName=p;var N="CheckboxIndicator",w=r.forwardRef((e,s)=>{let{__scopeCheckbox:a,forceMount:r,...i}=e,n=j(N,a);return(0,t.jsx)(m.z,{present:r||k(n.checked)||!0===n.checked,children:(0,t.jsx)(x.WV.span,{"data-state":S(n.checked),"data-disabled":n.disabled?"":void 0,...i,ref:s,style:{pointerEvents:"none",...e.style}})})});w.displayName=N;var C="CheckboxBubbleInput",P=r.forwardRef(({__scopeCheckbox:e,...s},a)=>{let{control:n,hasConsumerStoppedPropagationRef:l,checked:c,defaultChecked:m,required:p,disabled:u,name:h,value:f,form:y,bubbleInput:g,setBubbleInput:b}=j(C,e),v=(0,i.e)(a,b),N=(0,d.D)(c),w=(0,o.t)(n);r.useEffect(()=>{if(!g)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,s=!l.current;if(N!==c&&e){let a=new Event("click",{bubbles:s});g.indeterminate=k(c),e.call(g,!k(c)&&c),g.dispatchEvent(a)}},[g,N,c,l]);let P=r.useRef(!k(c)&&c);return(0,t.jsx)(x.WV.input,{type:"checkbox","aria-hidden":!0,defaultChecked:m??P.current,required:p,disabled:u,name:h,value:f,form:y,...s,tabIndex:-1,ref:v,style:{...s.style,...w,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function k(e){return"indeterminate"===e}function S(e){return k(e)?"indeterminate":e?"checked":"unchecked"}P.displayName=C;var _=a(62312),I=a(91626);let R=r.forwardRef(({className:e,...s},a)=>t.jsx(v,{ref:a,className:(0,I.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...s,children:t.jsx(w,{className:(0,I.cn)("flex items-center justify-center text-current"),children:t.jsx(_.Z,{className:"h-4 w-4"})})}));R.displayName=v.displayName},92549:(e,s,a)=>{"use strict";a.d(s,{I:()=>n});var t=a(95344),r=a(3729),i=a(91626);let n=r.forwardRef(({className:e,type:s,...a},r)=>t.jsx("input",{type:s,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...a}));n.displayName="Input"},1586:(e,s,a)=>{"use strict";a.d(s,{_:()=>d});var t=a(95344),r=a(3729),i=a(14217),n=a(49247),l=a(91626);let c=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef(({className:e,...s},a)=>t.jsx(i.f,{ref:a,className:(0,l.cn)(c(),e),...s}));d.displayName=i.f.displayName},17470:(e,s,a)=>{"use strict";a.d(s,{Bw:()=>h,Ph:()=>o,Ql:()=>f,i4:()=>x,ki:()=>m});var t=a(95344),r=a(3729),i=a(1146),n=a(25390),l=a(12704),c=a(62312),d=a(91626);let o=i.fC;i.ZA;let m=i.B4,x=r.forwardRef(({className:e,children:s,...a},r)=>(0,t.jsxs)(i.xz,{ref:r,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...a,children:[s,t.jsx(i.JO,{asChild:!0,children:t.jsx(n.Z,{className:"h-4 w-4 opacity-50"})})]}));x.displayName=i.xz.displayName;let p=r.forwardRef(({className:e,...s},a)=>t.jsx(i.u_,{ref:a,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:t.jsx(l.Z,{className:"h-4 w-4"})}));p.displayName=i.u_.displayName;let u=r.forwardRef(({className:e,...s},a)=>t.jsx(i.$G,{ref:a,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:t.jsx(n.Z,{className:"h-4 w-4"})}));u.displayName=i.$G.displayName;let h=r.forwardRef(({className:e,children:s,position:a="popper",...r},n)=>t.jsx(i.h_,{children:(0,t.jsxs)(i.VY,{ref:n,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...r,children:[t.jsx(p,{}),t.jsx(i.l_,{className:(0,d.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),t.jsx(u,{})]})}));h.displayName=i.VY.displayName,r.forwardRef(({className:e,...s},a)=>t.jsx(i.__,{ref:a,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...s})).displayName=i.__.displayName;let f=r.forwardRef(({className:e,children:s,...a},r)=>(0,t.jsxs)(i.ck,{ref:r,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...a,children:[t.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:t.jsx(i.wU,{children:t.jsx(c.Z,{className:"h-4 w-4"})})}),t.jsx(i.eT,{children:s})]}));f.displayName=i.ck.displayName,r.forwardRef(({className:e,...s},a)=>t.jsx(i.Z0,{ref:a,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...s})).displayName=i.Z0.displayName},71809:(e,s,a)=>{"use strict";a.d(s,{r:()=>w});var t=a(95344),r=a(3729),i=a(85222),n=a(31405),l=a(98462),c=a(33183),d=a(92062),o=a(63085),m=a(62409),x="Switch",[p,u]=(0,l.b)(x),[h,f]=p(x),j=r.forwardRef((e,s)=>{let{__scopeSwitch:a,name:l,checked:d,defaultChecked:o,required:p,disabled:u,value:f="on",onCheckedChange:j,form:y,...g}=e,[N,w]=r.useState(null),C=(0,n.e)(s,e=>w(e)),P=r.useRef(!1),k=!N||y||!!N.closest("form"),[S,_]=(0,c.T)({prop:d,defaultProp:o??!1,onChange:j,caller:x});return(0,t.jsxs)(h,{scope:a,checked:S,disabled:u,children:[(0,t.jsx)(m.WV.button,{type:"button",role:"switch","aria-checked":S,"aria-required":p,"data-state":v(S),"data-disabled":u?"":void 0,disabled:u,value:f,...g,ref:C,onClick:(0,i.M)(e.onClick,e=>{_(e=>!e),k&&(P.current=e.isPropagationStopped(),P.current||e.stopPropagation())})}),k&&(0,t.jsx)(b,{control:N,bubbles:!P.current,name:l,value:f,checked:S,required:p,disabled:u,form:y,style:{transform:"translateX(-100%)"}})]})});j.displayName=x;var y="SwitchThumb",g=r.forwardRef((e,s)=>{let{__scopeSwitch:a,...r}=e,i=f(y,a);return(0,t.jsx)(m.WV.span,{"data-state":v(i.checked),"data-disabled":i.disabled?"":void 0,...r,ref:s})});g.displayName=y;var b=r.forwardRef(({__scopeSwitch:e,control:s,checked:a,bubbles:i=!0,...l},c)=>{let m=r.useRef(null),x=(0,n.e)(m,c),p=(0,d.D)(a),u=(0,o.t)(s);return r.useEffect(()=>{let e=m.current;if(!e)return;let s=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(p!==a&&s){let t=new Event("click",{bubbles:i});s.call(e,a),e.dispatchEvent(t)}},[p,a,i]),(0,t.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:a,...l,tabIndex:-1,ref:x,style:{...l.style,...u,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function v(e){return e?"checked":"unchecked"}b.displayName="SwitchBubbleInput";var N=a(91626);let w=r.forwardRef(({className:e,...s},a)=>t.jsx(j,{className:(0,N.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...s,ref:a,children:t.jsx(g,{className:(0,N.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));w.displayName=j.displayName},81036:(e,s,a)=>{"use strict";a.d(s,{RM:()=>c,SC:()=>d,iA:()=>n,pj:()=>m,ss:()=>o,xD:()=>l});var t=a(95344),r=a(3729),i=a(91626);let n=r.forwardRef(({className:e,...s},a)=>t.jsx("div",{className:"relative w-full overflow-auto",children:t.jsx("table",{ref:a,className:(0,i.cn)("w-full caption-bottom text-sm",e),...s})}));n.displayName="Table";let l=r.forwardRef(({className:e,...s},a)=>t.jsx("thead",{ref:a,className:(0,i.cn)("[&_tr]:border-b",e),...s}));l.displayName="TableHeader";let c=r.forwardRef(({className:e,...s},a)=>t.jsx("tbody",{ref:a,className:(0,i.cn)("[&_tr:last-child]:border-0",e),...s}));c.displayName="TableBody",r.forwardRef(({className:e,...s},a)=>t.jsx("tfoot",{ref:a,className:(0,i.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...s})).displayName="TableFooter";let d=r.forwardRef(({className:e,...s},a)=>t.jsx("tr",{ref:a,className:(0,i.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...s}));d.displayName="TableRow";let o=r.forwardRef(({className:e,...s},a)=>t.jsx("th",{ref:a,className:(0,i.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...s}));o.displayName="TableHead";let m=r.forwardRef(({className:e,...s},a)=>t.jsx("td",{ref:a,className:(0,i.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...s}));m.displayName="TableCell",r.forwardRef(({className:e,...s},a)=>t.jsx("caption",{ref:a,className:(0,i.cn)("mt-4 text-sm text-muted-foreground",e),...s})).displayName="TableCaption"},93601:(e,s,a)=>{"use strict";a.d(s,{g:()=>n});var t=a(95344),r=a(3729),i=a(91626);let n=r.forwardRef(({className:e,...s},a)=>t.jsx("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...s}));n.displayName="Textarea"},53148:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},75695:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},51838:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},76755:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},38271:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},36343:(e,s,a)=>{"use strict";a.r(s),a.d(s,{$$typeof:()=>i,__esModule:()=>r,default:()=>n});let t=(0,a(86843).createProxy)(String.raw`C:\proj\nextjs-saas\app\super-admin\pricing-plans\page.tsx`),{__esModule:r,$$typeof:i}=t,n=t.default},14217:(e,s,a)=>{"use strict";a.d(s,{f:()=>l});var t=a(3729),r=a(62409),i=a(95344),n=t.forwardRef((e,s)=>(0,i.jsx)(r.WV.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));n.displayName="Label";var l=n}};var s=require("../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[1638,7948,6671,4626,7792,2506,8830,1729,2125,3965],()=>a(59975));module.exports=t})();