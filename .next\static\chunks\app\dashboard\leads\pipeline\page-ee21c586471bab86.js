(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8131],{64334:function(e,t,s){Promise.resolve().then(s.bind(s,19005))},19005:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return E}});var r=s(57437),a=s(2265),l=s(82749),n=s(38122),i=s(27815),d=s(85754),c=s(31478),o=s(45179),u=s(45509),x=s(9883),f=s(85790),m=s(41827),h=s(99670),p=s(1295),g=s(12741),b=s(66654),j=s(41298),N=s(67972),v=s(5925),y=s(61396),w=s.n(y);let C=[{id:"new",title:"New Leads",status:"NEW",color:"bg-gray-100 border-gray-300"},{id:"contacted",title:"Contacted",status:"CONTACTED",color:"bg-blue-100 border-blue-300"},{id:"qualified",title:"Qualified",status:"QUALIFIED",color:"bg-green-100 border-green-300"},{id:"proposal",title:"Proposal",status:"PROPOSAL",color:"bg-yellow-100 border-yellow-300"},{id:"negotiation",title:"Negotiation",status:"NEGOTIATION",color:"bg-orange-100 border-orange-300"},{id:"won",title:"Closed Won",status:"CLOSED_WON",color:"bg-green-100 border-green-500"},{id:"lost",title:"Closed Lost",status:"CLOSED_LOST",color:"bg-red-100 border-red-300"}];function E(){var e;let{data:t}=(0,l.useSession)(),[s,h]=(0,a.useState)([]),[p,g]=(0,a.useState)(!0),[b,j]=(0,a.useState)(""),[N,y]=(0,a.useState)("all"),[E,I]=(0,a.useState)("all"),[L,A]=(0,a.useState)("all"),S=async()=>{try{g(!0);let e=new URLSearchParams({limit:"1000",...b&&{search:b},..."all"!==N&&{source:N},..."all"!==E&&{priority:E},..."all"!==L&&{assignedTo:L}}),t=await fetch("/api/leads?".concat(e));if(!t.ok)throw Error("Failed to fetch leads");let s=await t.json(),r=C.map(e=>({...e,leads:s.leads.filter(t=>t.status===e.status),count:s.leads.filter(t=>t.status===e.status).length}));h(r)}catch(e){v.toast.error("Failed to load pipeline data"),console.error("Error fetching leads:",e)}finally{g(!1)}};(0,a.useEffect)(()=>{var e;(null==t?void 0:null===(e=t.user)||void 0===e?void 0:e.companyId)&&S()},[null==t?void 0:null===(e=t.user)||void 0===e?void 0:e.companyId,b,N,E,L]);let O=async e=>{let{destination:t,source:r,draggableId:a}=e;if(!t||t.droppableId===r.droppableId&&t.index===r.index)return;let l=s.find(e=>e.id===r.droppableId),n=s.find(e=>e.id===t.droppableId);if(!l||!n)return;let i=l.leads.find(e=>e.id===a);if(i)try{if(!(await fetch("/api/leads/".concat(a),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:n.status})})).ok)throw Error("Failed to update lead status");let e=s.map(e=>{if(e.id===r.droppableId)return{...e,leads:e.leads.filter(e=>e.id!==a),count:e.count-1};if(e.id===t.droppableId){let s={...i,status:n.status},r=[...e.leads];return r.splice(t.index,0,s),{...e,leads:r,count:e.count+1}}return e});h(e),v.toast.success("Lead moved to ".concat(n.title))}catch(e){v.toast.error("Failed to update lead status"),console.error("Error updating lead:",e)}};return p?(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Loading Pipeline"}),(0,r.jsx)("p",{className:"text-gray-500",children:"Please wait while we fetch your leads..."})]})}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Lead Pipeline"}),(0,r.jsx)("p",{className:"text-gray-500",children:"Drag and drop leads through your sales pipeline"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(d.z,{asChild:!0,children:(0,r.jsxs)(w(),{href:"/dashboard/leads/new",children:[(0,r.jsx)(x.Z,{className:"h-4 w-4 mr-2"}),"Add Lead"]})}),(0,r.jsx)(d.z,{variant:"outline",asChild:!0,children:(0,r.jsxs)(w(),{href:"/dashboard/leads",children:[(0,r.jsx)(f.Z,{className:"h-4 w-4 mr-2"}),"List View"]})})]})]}),(0,r.jsx)(i.Zb,{children:(0,r.jsx)(i.aY,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex flex-wrap items-center gap-4",children:[(0,r.jsx)("div",{className:"flex-1 min-w-64",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(m.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,r.jsx)(o.I,{placeholder:"Search leads...",value:b,onChange:e=>j(e.target.value),className:"pl-10"})]})}),(0,r.jsxs)(u.Ph,{value:N,onValueChange:y,children:[(0,r.jsx)(u.i4,{className:"w-40",children:(0,r.jsx)(u.ki,{placeholder:"Source"})}),(0,r.jsxs)(u.Bw,{children:[(0,r.jsx)(u.Ql,{value:"all",children:"All Sources"}),(0,r.jsx)(u.Ql,{value:"WEBSITE",children:"Website"}),(0,r.jsx)(u.Ql,{value:"REFERRAL",children:"Referral"}),(0,r.jsx)(u.Ql,{value:"SOCIAL_MEDIA",children:"Social Media"}),(0,r.jsx)(u.Ql,{value:"EMAIL_CAMPAIGN",children:"Email Campaign"}),(0,r.jsx)(u.Ql,{value:"COLD_CALL",children:"Cold Call"}),(0,r.jsx)(u.Ql,{value:"TRADE_SHOW",children:"Trade Show"}),(0,r.jsx)(u.Ql,{value:"PARTNER",children:"Partner"}),(0,r.jsx)(u.Ql,{value:"OTHER",children:"Other"})]})]}),(0,r.jsxs)(u.Ph,{value:E,onValueChange:I,children:[(0,r.jsx)(u.i4,{className:"w-40",children:(0,r.jsx)(u.ki,{placeholder:"Priority"})}),(0,r.jsxs)(u.Bw,{children:[(0,r.jsx)(u.Ql,{value:"all",children:"All Priorities"}),(0,r.jsx)(u.Ql,{value:"LOW",children:"Low"}),(0,r.jsx)(u.Ql,{value:"MEDIUM",children:"Medium"}),(0,r.jsx)(u.Ql,{value:"HIGH",children:"High"}),(0,r.jsx)(u.Ql,{value:"URGENT",children:"Urgent"})]})]})]})})}),(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4",children:s.map(e=>(0,r.jsx)(i.Zb,{className:"".concat(e.color," border-2"),children:(0,r.jsxs)(i.aY,{className:"p-4 text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:e.count}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:e.title})]})},e.id))}),(0,r.jsx)(n.Z5,{onDragEnd:O,children:(0,r.jsx)("div",{className:"flex space-x-4 overflow-x-auto pb-4",children:s.map(e=>(0,r.jsx)("div",{className:"flex-shrink-0 w-80",children:(0,r.jsxs)(i.Zb,{className:"".concat(e.color," border-2"),children:[(0,r.jsx)(i.Ol,{className:"pb-3",children:(0,r.jsxs)(i.ll,{className:"flex items-center justify-between text-sm font-medium",children:[(0,r.jsx)("span",{children:e.title}),(0,r.jsx)(c.C,{variant:"secondary",className:"text-xs",children:e.count})]})}),(0,r.jsx)(n.bK,{droppableId:e.id,children:(t,s)=>(0,r.jsxs)("div",{ref:t.innerRef,...t.droppableProps,className:"min-h-96 p-2 space-y-2 ".concat(s.isDraggingOver?"bg-gray-50":""),children:[e.leads.map((e,t)=>(0,r.jsx)(n._l,{draggableId:e.id,index:t,children:(t,s)=>(0,r.jsx)("div",{ref:t.innerRef,...t.draggableProps,...t.dragHandleProps,className:"".concat(s.isDragging?"rotate-2 shadow-lg":""),children:(0,r.jsx)(R,{lead:e})})},e.id)),t.placeholder,0===e.leads.length&&(0,r.jsxs)("div",{className:"text-center py-8 text-gray-400",children:[(0,r.jsx)(f.Z,{className:"h-8 w-8 mx-auto mb-2"}),(0,r.jsx)("p",{className:"text-sm",children:"No leads in this stage"})]})]})})]})},e.id))})})]})}function R(e){let{lead:t}=e;return(0,r.jsx)(i.Zb,{className:"bg-white hover:shadow-md transition-shadow cursor-pointer",children:(0,r.jsx)(i.aY,{className:"p-4",children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("h3",{className:"font-medium text-sm text-gray-900 truncate",children:[t.firstName," ",t.lastName]}),t.companyName&&(0,r.jsx)("p",{className:"text-xs text-gray-500 truncate",children:t.companyName}),t.title&&(0,r.jsx)("p",{className:"text-xs text-gray-400 truncate",children:t.title})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(e=>{switch(e){case"LOW":return(0,r.jsx)(c.C,{variant:"secondary",className:"text-xs",children:"Low"});case"MEDIUM":return(0,r.jsx)(c.C,{className:"bg-blue-100 text-blue-800 text-xs",children:"Medium"});case"HIGH":return(0,r.jsx)(c.C,{className:"bg-orange-100 text-orange-800 text-xs",children:"High"});case"URGENT":return(0,r.jsx)(c.C,{variant:"destructive",className:"text-xs",children:"Urgent"});default:return(0,r.jsx)(c.C,{variant:"secondary",className:"text-xs",children:e})}})(t.priority),(0,r.jsx)(d.z,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0",asChild:!0,children:(0,r.jsx)(w(),{href:"/dashboard/leads/".concat(t.id),children:(0,r.jsx)(h.Z,{className:"h-3 w-3"})})})]})]}),(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-xs text-gray-600",children:[(0,r.jsx)(p.Z,{className:"h-3 w-3"}),(0,r.jsx)("span",{className:"truncate",children:t.email})]}),t.phone&&(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-xs text-gray-600",children:[(0,r.jsx)(g.Z,{className:"h-3 w-3"}),(0,r.jsx)("span",{children:t.phone})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-xs",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(b.Z,{className:"h-3 w-3 text-blue-600"}),(0,r.jsxs)("span",{className:"text-gray-600",children:["Score: ",t.score,"/100"]})]}),t.budget&&(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(j.Z,{className:"h-3 w-3 text-green-600"}),(0,r.jsxs)("span",{className:"text-gray-600",children:["$",t.budget.toLocaleString()]})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between pt-2 border-t border-gray-100",children:[(0,r.jsx)("div",{className:"text-xs ".concat((e=>{switch(e){case"WEBSITE":return"text-blue-600";case"REFERRAL":return"text-green-600";case"SOCIAL_MEDIA":return"text-purple-600";case"EMAIL_CAMPAIGN":return"text-orange-600";case"COLD_CALL":return"text-red-600";case"TRADE_SHOW":return"text-indigo-600";case"PARTNER":return"text-pink-600";default:return"text-gray-600"}})(t.source)),children:t.source.replace("_"," ")}),(0,r.jsx)("div",{className:"text-xs text-gray-400",children:new Date(t.createdAt).toLocaleDateString()})]}),t.assignedTo&&(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-xs text-gray-500",children:[(0,r.jsx)(N.Z,{className:"h-3 w-3"}),(0,r.jsx)("span",{className:"truncate",children:t.assignedTo.name})]})]})})})}},31478:function(e,t,s){"use strict";s.d(t,{C:function(){return i}});var r=s(57437);s(2265);var a=s(96061),l=s(1657);let n=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600",info:"border-transparent bg-blue-500 text-white hover:bg-blue-600"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:s,...a}=e;return(0,r.jsx)("div",{className:(0,l.cn)(n({variant:s}),t),...a})}},85754:function(e,t,s){"use strict";s.d(t,{z:function(){return c}});var r=s(57437),a=s(2265),l=s(67256),n=s(96061),i=s(1657);let d=(0,n.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,t)=>{let{className:s,variant:a,size:n,asChild:c=!1,...o}=e,u=c?l.g7:"button";return(0,r.jsx)(u,{className:(0,i.cn)(d({variant:a,size:n,className:s})),ref:t,...o})});c.displayName="Button"},27815:function(e,t,s){"use strict";s.d(t,{Ol:function(){return i},SZ:function(){return c},Zb:function(){return n},aY:function(){return o},eW:function(){return u},ll:function(){return d}});var r=s(57437),a=s(2265),l=s(1657);let n=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...a})});n.displayName="Card";let i=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",s),...a})});i.displayName="CardHeader";let d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("h3",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",s),...a})});d.displayName="CardTitle";let c=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("p",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",s),...a})});c.displayName="CardDescription";let o=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",s),...a})});o.displayName="CardContent";let u=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",s),...a})});u.displayName="CardFooter"},45179:function(e,t,s){"use strict";s.d(t,{I:function(){return n}});var r=s(57437),a=s(2265),l=s(1657);let n=a.forwardRef((e,t)=>{let{className:s,type:a,...n}=e;return(0,r.jsx)("input",{type:a,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:t,...n})});n.displayName="Input"},45509:function(e,t,s){"use strict";s.d(t,{Bw:function(){return h},Ph:function(){return o},Ql:function(){return p},i4:function(){return x},ki:function(){return u}});var r=s(57437),a=s(2265),l=s(99530),n=s(83523),i=s(9224),d=s(62442),c=s(1657);let o=l.fC;l.ZA;let u=l.B4,x=a.forwardRef((e,t)=>{let{className:s,children:a,...i}=e;return(0,r.jsxs)(l.xz,{ref:t,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",s),...i,children:[a,(0,r.jsx)(l.JO,{asChild:!0,children:(0,r.jsx)(n.Z,{className:"h-4 w-4 opacity-50"})})]})});x.displayName=l.xz.displayName;let f=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(l.u_,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",s),...a,children:(0,r.jsx)(i.Z,{className:"h-4 w-4"})})});f.displayName=l.u_.displayName;let m=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(l.$G,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",s),...a,children:(0,r.jsx)(n.Z,{className:"h-4 w-4"})})});m.displayName=l.$G.displayName;let h=a.forwardRef((e,t)=>{let{className:s,children:a,position:n="popper",...i}=e;return(0,r.jsx)(l.h_,{children:(0,r.jsxs)(l.VY,{ref:t,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:n,...i,children:[(0,r.jsx)(f,{}),(0,r.jsx)(l.l_,{className:(0,c.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),(0,r.jsx)(m,{})]})})});h.displayName=l.VY.displayName,a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(l.__,{ref:t,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",s),...a})}).displayName=l.__.displayName;let p=a.forwardRef((e,t)=>{let{className:s,children:a,...n}=e;return(0,r.jsxs)(l.ck,{ref:t,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...n,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(l.wU,{children:(0,r.jsx)(d.Z,{className:"h-4 w-4"})})}),(0,r.jsx)(l.eT,{children:a})]})});p.displayName=l.ck.displayName,a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(l.Z0,{ref:t,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",s),...a})}).displayName=l.Z0.displayName},1657:function(e,t,s){"use strict";s.d(t,{cn:function(){return l}});var r=s(57042),a=s(74769);function l(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.m6)((0,r.W)(t))}}},function(e){e.O(0,[3338,6723,9502,2749,1706,4138,1396,4997,6404,2971,4938,1744],function(){return e(e.s=64334)}),_N_E=e.O()}]);