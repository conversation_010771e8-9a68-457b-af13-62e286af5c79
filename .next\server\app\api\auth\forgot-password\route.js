"use strict";(()=>{var e={};e.id=9118,e.ids=[9118],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6113:e=>{e.exports=require("crypto")},49530:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>v,originalPathname:()=>y,patchFetch:()=>j,requestAsyncStorage:()=>h,routeModule:()=>w,serverHooks:()=>g,staticGenerationAsyncStorage:()=>f,staticGenerationBailout:()=>x});var a={};r.r(a),r.d(a,{POST:()=>m});var s=r(95419),o=r(69108),n=r(99678),i=r(78070),u=r(9108),l=r(11896),d=r(25252),p=r(52178);let c=d.Ry({email:d.Z_().email("Invalid email address")});async function m(e){try{let t=await e.json(),{email:r}=c.parse(t),a=await u._.user.findUnique({where:{email:r}});if(a){if(await u._.passwordReset.findFirst({where:{userId:a.id,createdAt:{gt:new Date(Date.now()-3e5)}}}))return i.Z.json({message:"If an account with that email exists, we have sent a password reset link."});let e=(0,l.uE)(),t=new Date(Date.now()+36e5);await u._.passwordReset.create({data:{userId:a.id,token:e,expiresAt:t}});try{await (0,l.LS)(a.email,a.name||`${a.firstName} ${a.lastName}`,e)}catch(e){console.error("Failed to send password reset email:",e)}console.log(`Password reset requested for: ${a.email}`)}return i.Z.json({message:"If an account with that email exists, we have sent a password reset link."})}catch(e){if(e instanceof p.jm)return i.Z.json({error:"Invalid email address",details:e.errors},{status:400});return console.error("Forgot password error:",e),i.Z.json({error:"Internal server error"},{status:500})}}let w=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/auth/forgot-password/route",pathname:"/api/auth/forgot-password",filename:"route",bundlePath:"app/api/auth/forgot-password/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\auth\\forgot-password\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:h,staticGenerationAsyncStorage:f,serverHooks:g,headerHooks:v,staticGenerationBailout:x}=w,y="/api/auth/forgot-password/route";function j(){return(0,n.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:f})}},11896:(e,t,r)=>{r.d(t,{LS:()=>l,cl:()=>n,uE:()=>i,zk:()=>u});var a=r(6113),s=r.n(a);let o={from:process.env.EMAIL_FROM||"<EMAIL>",baseUrl:"http://localhost:3000"};function n(){return s().randomBytes(32).toString("hex")}function i(){return s().randomBytes(32).toString("hex")}async function u(e,t,r){o.baseUrl,o.from,console.log("\uD83D\uDCE7 Email would be sent to:",e)}async function l(e,t,r){o.baseUrl,o.from,console.log("\uD83D\uDCE7 Password reset email would be sent to:",e)}},9108:(e,t,r)=>{r.d(t,{_:()=>s});let a=require("@prisma/client"),s=globalThis.prisma??new a.PrismaClient}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,6206,5252],()=>r(49530));module.exports=a})();