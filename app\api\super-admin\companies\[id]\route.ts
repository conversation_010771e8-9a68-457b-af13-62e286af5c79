import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const companyUpdateSchema = z.object({
  name: z.string().optional(),
  email: z.string().email().optional(),
  phone: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  country: z.string().optional(),
  postalCode: z.string().optional(),
  website: z.string().url().optional().or(z.literal('')),
  industry: z.string().optional(),
  size: z.enum(['STARTUP', 'SMALL', 'MEDIUM', 'LARGE', 'ENTERPRISE']).optional(),
  businessType: z.string().optional(),
  status: z.enum(['ACTIVE', 'SUSPENDED', 'INACTIVE']).optional(),
  taxId: z.string().optional(),
  registrationNumber: z.string().optional()
})

// GET /api/super-admin/companies/[id] - Get company details
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || session?.user?.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Super admin access required' }, { status: 403 })
    }

    const companyId = params.id

    const company = await prisma.company.findUnique({
      where: { id: companyId },
      include: {
        owner: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
            status: true,
            lastLoginAt: true,
            loginCount: true,
            createdAt: true
          }
        },
        users: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
            status: true,
            lastLoginAt: true,
            loginCount: true,
            createdAt: true
          },
          orderBy: { createdAt: 'desc' }
        },
        customers: {
          select: {
            id: true,
            name: true,
            email: true,
            status: true,
            createdAt: true
          },
          orderBy: { createdAt: 'desc' },
          take: 10
        },
        quotations: {
          select: {
            id: true,
            quotationNumber: true,
            status: true,
            totalAmount: true,
            createdAt: true
          },
          orderBy: { createdAt: 'desc' },
          take: 10
        },
        invoices: {
          select: {
            id: true,
            invoiceNumber: true,
            status: true,
            totalAmount: true,
            createdAt: true
          },
          orderBy: { createdAt: 'desc' },
          take: 10
        },
        contracts: {
          select: {
            id: true,
            contractNumber: true,
            status: true,
            value: true,
            createdAt: true
          },
          orderBy: { createdAt: 'desc' },
          take: 10
        },
        items: {
          select: {
            id: true,
            name: true,
            sku: true,
            unitPrice: true,
            stockQuantity: true,
            createdAt: true
          },
          orderBy: { createdAt: 'desc' },
          take: 10
        },
        activities: {
          select: {
            id: true,
            type: true,
            title: true,
            description: true,
            createdAt: true,
            createdBy: {
              select: {
                name: true,
                email: true
              }
            }
          },
          orderBy: { createdAt: 'desc' },
          take: 20
        },
        _count: {
          select: {
            users: true,
            customers: true,
            quotations: true,
            invoices: true,
            contracts: true,
            items: true,
            activities: true
          }
        }
      }
    })

    if (!company) {
      return NextResponse.json({ error: 'Company not found' }, { status: 404 })
    }

    // Get additional analytics
    const [
      revenueMetrics,
      activityMetrics,
      userMetrics,
      documentMetrics
    ] = await Promise.all([
      // Revenue metrics
      Promise.all([
        prisma.invoice.aggregate({
          where: {
            companyId,
            status: 'PAID'
          },
          _sum: { totalAmount: true },
          _count: true
        }),
        prisma.invoice.aggregate({
          where: {
            companyId,
            status: 'PENDING'
          },
          _sum: { totalAmount: true },
          _count: true
        }),
        prisma.quotation.aggregate({
          where: {
            companyId,
            status: 'APPROVED'
          },
          _sum: { totalAmount: true },
          _count: true
        })
      ]),

      // Activity metrics
      Promise.all([
        prisma.activity.count({
          where: {
            companyId,
            createdAt: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
            }
          }
        }),
        prisma.activity.groupBy({
          by: ['type'],
          where: { companyId },
          _count: { id: true },
          orderBy: { _count: { id: 'desc' } }
        })
      ]),

      // User metrics
      Promise.all([
        prisma.user.count({
          where: {
            companyId,
            status: 'ACTIVE'
          }
        }),
        prisma.user.count({
          where: {
            companyId,
            lastLoginAt: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
            }
          }
        })
      ]),

      // Document metrics
      Promise.all([
        prisma.quotation.count({
          where: {
            companyId,
            createdAt: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
            }
          }
        }),
        prisma.invoice.count({
          where: {
            companyId,
            createdAt: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
            }
          }
        }),
        prisma.contract.count({
          where: {
            companyId,
            createdAt: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
            }
          }
        })
      ])
    ])

    const [paidInvoices, pendingInvoices, approvedQuotations] = revenueMetrics
    const [recentActivityCount, activitiesByType] = activityMetrics
    const [activeUsers, recentActiveUsers] = userMetrics
    const [recentQuotations, recentInvoices, recentContracts] = documentMetrics

    return NextResponse.json({
      company: {
        ...company,
        items: company.items.map(item => ({
          ...item,
          unitPrice: Number(item.unitPrice),
          stockQuantity: Number(item.stockQuantity)
        })),
        quotations: company.quotations.map(q => ({
          ...q,
          totalAmount: Number(q.totalAmount)
        })),
        invoices: company.invoices.map(i => ({
          ...i,
          totalAmount: Number(i.totalAmount)
        })),
        contracts: company.contracts.map(c => ({
          ...c,
          value: Number(c.value)
        }))
      },
      analytics: {
        revenue: {
          paid: {
            amount: Number(paidInvoices._sum.totalAmount || 0),
            count: paidInvoices._count
          },
          pending: {
            amount: Number(pendingInvoices._sum.totalAmount || 0),
            count: pendingInvoices._count
          },
          approved: {
            amount: Number(approvedQuotations._sum.totalAmount || 0),
            count: approvedQuotations._count
          }
        },
        activity: {
          recent: recentActivityCount,
          byType: activitiesByType.map(item => ({
            type: item.type,
            count: item._count.id
          }))
        },
        users: {
          active: activeUsers,
          recentlyActive: recentActiveUsers
        },
        documents: {
          recentQuotations,
          recentInvoices,
          recentContracts
        }
      }
    })

  } catch (error) {
    console.error('Error fetching company details:', error)
    return NextResponse.json(
      { error: 'Failed to fetch company details' },
      { status: 500 }
    )
  }
}

// PUT /api/super-admin/companies/[id] - Update company
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || session?.user?.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Super admin access required' }, { status: 403 })
    }

    const companyId = params.id
    const body = await request.json()
    const validatedData = companyUpdateSchema.parse(body)

    // Check if company exists
    const existingCompany = await prisma.company.findUnique({
      where: { id: companyId }
    })

    if (!existingCompany) {
      return NextResponse.json({ error: 'Company not found' }, { status: 404 })
    }

    const company = await prisma.$transaction(async (tx) => {
      // Update company
      const updatedCompany = await tx.company.update({
        where: { id: companyId },
        data: validatedData,
        include: {
          owner: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true
            }
          },
          _count: {
            select: {
              users: true,
              customers: true,
              quotations: true,
              invoices: true,
              contracts: true
            }
          }
        }
      })

      // Log admin action
      await tx.activity.create({
        data: {
          type: 'ADMIN',
          title: 'Company Updated by Super Admin',
          description: `Company "${updatedCompany.name}" was updated by super admin`,
          companyId,
          createdById: session.user.id
        }
      })

      return updatedCompany
    })

    return NextResponse.json({
      company,
      message: 'Company updated successfully'
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating company:', error)
    return NextResponse.json(
      { error: 'Failed to update company' },
      { status: 500 }
    )
  }
}

// DELETE /api/super-admin/companies/[id] - Delete company
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || session?.user?.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Super admin access required' }, { status: 403 })
    }

    const companyId = params.id

    // Check if company exists
    const existingCompany = await prisma.company.findUnique({
      where: { id: companyId },
      select: { name: true }
    })

    if (!existingCompany) {
      return NextResponse.json({ error: 'Company not found' }, { status: 404 })
    }

    await prisma.$transaction(async (tx) => {
      // Delete company (cascade will handle related records)
      await tx.company.delete({
        where: { id: companyId }
      })

      // Log admin action (create in system activity log if needed)
      // Note: Can't log to company activities since company is deleted
    })

    return NextResponse.json({
      message: 'Company deleted successfully'
    })

  } catch (error) {
    console.error('Error deleting company:', error)
    return NextResponse.json(
      { error: 'Failed to delete company' },
      { status: 500 }
    )
  }
}
