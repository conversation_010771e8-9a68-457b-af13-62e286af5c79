{"version": 3, "file": "RowExpanding.js", "sources": ["../../../src/features/RowExpanding.ts"], "sourcesContent": ["import { RowModel } from '..'\nimport {\n  OnChangeFn,\n  Table,\n  Row,\n  Updater,\n  RowData,\n  TableFeature,\n} from '../types'\nimport { makeStateUpdater } from '../utils'\n\nexport type ExpandedStateList = Record<string, boolean>\nexport type ExpandedState = true | Record<string, boolean>\nexport interface ExpandedTableState {\n  expanded: ExpandedState\n}\n\nexport interface ExpandedRow {\n  /**\n   * Returns whether the row can be expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getcanexpand)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getCanExpand: () => boolean\n  /**\n   * Returns whether all parent rows of the row are expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getisallparentsexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getIsAllParentsExpanded: () => boolean\n  /**\n   * Returns whether the row is expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getisexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getIsExpanded: () => boolean\n  /**\n   * Returns a function that can be used to toggle the expanded state of the row. This function can be used to bind to an event handler to a button.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#gettoggleexpandedhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getToggleExpandedHandler: () => () => void\n  /**\n   * Toggles the expanded state (or sets it if `expanded` is provided) for the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#toggleexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  toggleExpanded: (expanded?: boolean) => void\n}\n\nexport interface ExpandedOptions<TData extends RowData> {\n  /**\n   * Enable this setting to automatically reset the expanded state of the table when expanding state changes.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#autoresetexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  autoResetExpanded?: boolean\n  /**\n   * Enable/disable expanding for all rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#enableexpanding)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  enableExpanding?: boolean\n  /**\n   * This function is responsible for returning the expanded row model. If this function is not provided, the table will not expand rows. You can use the default exported `getExpandedRowModel` function to get the expanded row model or implement your own.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getexpandedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getExpandedRowModel?: (table: Table<any>) => () => RowModel<any>\n  /**\n   * If provided, allows you to override the default behavior of determining whether a row is currently expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getisrowexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getIsRowExpanded?: (row: Row<TData>) => boolean\n  /**\n   * If provided, allows you to override the default behavior of determining whether a row can be expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getrowcanexpand)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getRowCanExpand?: (row: Row<TData>) => boolean\n  /**\n   * Enables manual row expansion. If this is set to `true`, `getExpandedRowModel` will not be used to expand rows and you would be expected to perform the expansion in your own data model. This is useful if you are doing server-side expansion.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#manualexpanding)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  manualExpanding?: boolean\n  /**\n   * This function is called when the `expanded` table state changes. If a function is provided, you will be responsible for managing this state on your own. To pass the managed state back to the table, use the `tableOptions.state.expanded` option.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#onexpandedchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  onExpandedChange?: OnChangeFn<ExpandedState>\n  /**\n   * If `true` expanded rows will be paginated along with the rest of the table (which means expanded rows may span multiple pages). If `false` expanded rows will not be considered for pagination (which means expanded rows will always render on their parents page. This also means more rows will be rendered than the set page size)\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#paginateexpandedrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  paginateExpandedRows?: boolean\n}\n\nexport interface ExpandedInstance<TData extends RowData> {\n  _autoResetExpanded: () => void\n  _getExpandedRowModel?: () => RowModel<TData>\n  /**\n   * Returns whether there are any rows that can be expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getcansomerowsexpand)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getCanSomeRowsExpand: () => boolean\n  /**\n   * Returns the maximum depth of the expanded rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getexpandeddepth)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getExpandedDepth: () => number\n  /**\n   * Returns the row model after expansion has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getexpandedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getExpandedRowModel: () => RowModel<TData>\n  /**\n   * Returns whether all rows are currently expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getisallrowsexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getIsAllRowsExpanded: () => boolean\n  /**\n   * Returns whether there are any rows that are currently expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getissomerowsexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getIsSomeRowsExpanded: () => boolean\n  /**\n   * Returns the row model before expansion has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getpreexpandedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getPreExpandedRowModel: () => RowModel<TData>\n  /**\n   * Returns a handler that can be used to toggle the expanded state of all rows. This handler is meant to be used with an `input[type=checkbox]` element.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#gettoggleallrowsexpandedhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getToggleAllRowsExpandedHandler: () => (event: unknown) => void\n  /**\n   * Resets the expanded state of the table to the initial state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#resetexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  resetExpanded: (defaultState?: boolean) => void\n  /**\n   * Updates the expanded state of the table via an update function or value.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#setexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  setExpanded: (updater: Updater<ExpandedState>) => void\n  /**\n   * Toggles the expanded state for all rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#toggleallrowsexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  toggleAllRowsExpanded: (expanded?: boolean) => void\n}\n\n//\n\nexport const RowExpanding: TableFeature = {\n  getInitialState: (state): ExpandedTableState => {\n    return {\n      expanded: {},\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ExpandedOptions<TData> => {\n    return {\n      onExpandedChange: makeStateUpdater('expanded', table),\n      paginateExpandedRows: true,\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    let registered = false\n    let queued = false\n\n    table._autoResetExpanded = () => {\n      if (!registered) {\n        table._queue(() => {\n          registered = true\n        })\n        return\n      }\n\n      if (\n        table.options.autoResetAll ??\n        table.options.autoResetExpanded ??\n        !table.options.manualExpanding\n      ) {\n        if (queued) return\n        queued = true\n        table._queue(() => {\n          table.resetExpanded()\n          queued = false\n        })\n      }\n    }\n    table.setExpanded = updater => table.options.onExpandedChange?.(updater)\n    table.toggleAllRowsExpanded = expanded => {\n      if (expanded ?? !table.getIsAllRowsExpanded()) {\n        table.setExpanded(true)\n      } else {\n        table.setExpanded({})\n      }\n    }\n    table.resetExpanded = defaultState => {\n      table.setExpanded(defaultState ? {} : table.initialState?.expanded ?? {})\n    }\n    table.getCanSomeRowsExpand = () => {\n      return table\n        .getPrePaginationRowModel()\n        .flatRows.some(row => row.getCanExpand())\n    }\n    table.getToggleAllRowsExpandedHandler = () => {\n      return (e: unknown) => {\n        ;(e as any).persist?.()\n        table.toggleAllRowsExpanded()\n      }\n    }\n    table.getIsSomeRowsExpanded = () => {\n      const expanded = table.getState().expanded\n      return expanded === true || Object.values(expanded).some(Boolean)\n    }\n    table.getIsAllRowsExpanded = () => {\n      const expanded = table.getState().expanded\n\n      // If expanded is true, save some cycles and return true\n      if (typeof expanded === 'boolean') {\n        return expanded === true\n      }\n\n      if (!Object.keys(expanded).length) {\n        return false\n      }\n\n      // If any row is not expanded, return false\n      if (table.getRowModel().flatRows.some(row => !row.getIsExpanded())) {\n        return false\n      }\n\n      // They must all be expanded :shrug:\n      return true\n    }\n    table.getExpandedDepth = () => {\n      let maxDepth = 0\n\n      const rowIds =\n        table.getState().expanded === true\n          ? Object.keys(table.getRowModel().rowsById)\n          : Object.keys(table.getState().expanded)\n\n      rowIds.forEach(id => {\n        const splitId = id.split('.')\n        maxDepth = Math.max(maxDepth, splitId.length)\n      })\n\n      return maxDepth\n    }\n    table.getPreExpandedRowModel = () => table.getSortedRowModel()\n    table.getExpandedRowModel = () => {\n      if (!table._getExpandedRowModel && table.options.getExpandedRowModel) {\n        table._getExpandedRowModel = table.options.getExpandedRowModel(table)\n      }\n\n      if (table.options.manualExpanding || !table._getExpandedRowModel) {\n        return table.getPreExpandedRowModel()\n      }\n\n      return table._getExpandedRowModel()\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row.toggleExpanded = expanded => {\n      table.setExpanded(old => {\n        const exists = old === true ? true : !!old?.[row.id]\n\n        let oldExpanded: ExpandedStateList = {}\n\n        if (old === true) {\n          Object.keys(table.getRowModel().rowsById).forEach(rowId => {\n            oldExpanded[rowId] = true\n          })\n        } else {\n          oldExpanded = old\n        }\n\n        expanded = expanded ?? !exists\n\n        if (!exists && expanded) {\n          return {\n            ...oldExpanded,\n            [row.id]: true,\n          }\n        }\n\n        if (exists && !expanded) {\n          const { [row.id]: _, ...rest } = oldExpanded\n          return rest\n        }\n\n        return old\n      })\n    }\n    row.getIsExpanded = () => {\n      const expanded = table.getState().expanded\n\n      return !!(\n        table.options.getIsRowExpanded?.(row) ??\n        (expanded === true || expanded?.[row.id])\n      )\n    }\n    row.getCanExpand = () => {\n      return (\n        table.options.getRowCanExpand?.(row) ??\n        ((table.options.enableExpanding ?? true) && !!row.subRows?.length)\n      )\n    }\n    row.getIsAllParentsExpanded = () => {\n      let isFullyExpanded = true\n      let currentRow = row\n\n      while (isFullyExpanded && currentRow.parentId) {\n        currentRow = table.getRow(currentRow.parentId, true)\n        isFullyExpanded = currentRow.getIsExpanded()\n      }\n\n      return isFullyExpanded\n    }\n    row.getToggleExpandedHandler = () => {\n      const canExpand = row.getCanExpand()\n\n      return () => {\n        if (!canExpand) return\n        row.toggleExpanded()\n      }\n    }\n  },\n}\n"], "names": ["RowExpanding", "getInitialState", "state", "expanded", "getDefaultOptions", "table", "onExpandedChange", "makeStateUpdater", "paginateExpandedRows", "createTable", "registered", "queued", "_autoResetExpanded", "_ref", "_table$options$autoRe", "_queue", "options", "autoResetAll", "autoResetExpanded", "manualExpanding", "resetExpanded", "setExpanded", "updater", "toggleAllRowsExpanded", "getIsAllRowsExpanded", "defaultState", "_table$initialState$e", "_table$initialState", "initialState", "getCanSomeRowsExpand", "getPrePaginationRowModel", "flatRows", "some", "row", "getCanExpand", "getToggleAllRowsExpandedHandler", "e", "persist", "getIsSomeRowsExpanded", "getState", "Object", "values", "Boolean", "keys", "length", "getRowModel", "getIsExpanded", "getExpandedDepth", "max<PERSON><PERSON><PERSON>", "rowIds", "rowsById", "for<PERSON>ach", "id", "splitId", "split", "Math", "max", "getPreExpandedRowModel", "getSortedRowModel", "getExpandedRowModel", "_getExpandedRowModel", "createRow", "toggleExpanded", "old", "_expanded", "exists", "oldExpanded", "rowId", "_", "rest", "_table$options$getIsR", "getIsRowExpanded", "_table$options$getRow", "_table$options$enable", "_row$subRows", "getRowCanExpand", "enableExpanding", "subRows", "getIsAllParentsExpanded", "isFullyExpanded", "currentRow", "parentId", "getRow", "getToggleExpandedHandler", "canExpand"], "mappings": ";;;;;;;;;;;;;;AAsKA;;AAEO,MAAMA,YAA0B,GAAG;EACxCC,eAAe,EAAGC,KAAK,IAAyB;IAC9C,OAAO;MACLC,QAAQ,EAAE,EAAE;MACZ,GAAGD,KAAAA;KACJ,CAAA;GACF;EAEDE,iBAAiB,EACfC,KAAmB,IACQ;IAC3B,OAAO;AACLC,MAAAA,gBAAgB,EAAEC,sBAAgB,CAAC,UAAU,EAAEF,KAAK,CAAC;AACrDG,MAAAA,oBAAoB,EAAE,IAAA;KACvB,CAAA;GACF;EAEDC,WAAW,EAA0BJ,KAAmB,IAAW;IACjE,IAAIK,UAAU,GAAG,KAAK,CAAA;IACtB,IAAIC,MAAM,GAAG,KAAK,CAAA;IAElBN,KAAK,CAACO,kBAAkB,GAAG,MAAM;MAAA,IAAAC,IAAA,EAAAC,qBAAA,CAAA;MAC/B,IAAI,CAACJ,UAAU,EAAE;QACfL,KAAK,CAACU,MAAM,CAAC,MAAM;AACjBL,UAAAA,UAAU,GAAG,IAAI,CAAA;AACnB,SAAC,CAAC,CAAA;AACF,QAAA,OAAA;AACF,OAAA;MAEA,IAAAG,CAAAA,IAAA,GAAAC,CAAAA,qBAAA,GACET,KAAK,CAACW,OAAO,CAACC,YAAY,KAAAH,IAAAA,GAAAA,qBAAA,GAC1BT,KAAK,CAACW,OAAO,CAACE,iBAAiB,KAAA,IAAA,GAAAL,IAAA,GAC/B,CAACR,KAAK,CAACW,OAAO,CAACG,eAAe,EAC9B;AACA,QAAA,IAAIR,MAAM,EAAE,OAAA;AACZA,QAAAA,MAAM,GAAG,IAAI,CAAA;QACbN,KAAK,CAACU,MAAM,CAAC,MAAM;UACjBV,KAAK,CAACe,aAAa,EAAE,CAAA;AACrBT,UAAAA,MAAM,GAAG,KAAK,CAAA;AAChB,SAAC,CAAC,CAAA;AACJ,OAAA;KACD,CAAA;AACDN,IAAAA,KAAK,CAACgB,WAAW,GAAGC,OAAO,IAAIjB,KAAK,CAACW,OAAO,CAACV,gBAAgB,IAAA,IAAA,GAAA,KAAA,CAAA,GAA9BD,KAAK,CAACW,OAAO,CAACV,gBAAgB,CAAGgB,OAAO,CAAC,CAAA;AACxEjB,IAAAA,KAAK,CAACkB,qBAAqB,GAAGpB,QAAQ,IAAI;MACxC,IAAIA,QAAQ,IAARA,IAAAA,GAAAA,QAAQ,GAAI,CAACE,KAAK,CAACmB,oBAAoB,EAAE,EAAE;AAC7CnB,QAAAA,KAAK,CAACgB,WAAW,CAAC,IAAI,CAAC,CAAA;AACzB,OAAC,MAAM;AACLhB,QAAAA,KAAK,CAACgB,WAAW,CAAC,EAAE,CAAC,CAAA;AACvB,OAAA;KACD,CAAA;AACDhB,IAAAA,KAAK,CAACe,aAAa,GAAGK,YAAY,IAAI;MAAA,IAAAC,qBAAA,EAAAC,mBAAA,CAAA;MACpCtB,KAAK,CAACgB,WAAW,CAACI,YAAY,GAAG,EAAE,GAAA,CAAAC,qBAAA,GAAA,CAAAC,mBAAA,GAAGtB,KAAK,CAACuB,YAAY,KAAlBD,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,mBAAA,CAAoBxB,QAAQ,YAAAuB,qBAAA,GAAI,EAAE,CAAC,CAAA;KAC1E,CAAA;IACDrB,KAAK,CAACwB,oBAAoB,GAAG,MAAM;AACjC,MAAA,OAAOxB,KAAK,CACTyB,wBAAwB,EAAE,CAC1BC,QAAQ,CAACC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,YAAY,EAAE,CAAC,CAAA;KAC5C,CAAA;IACD7B,KAAK,CAAC8B,+BAA+B,GAAG,MAAM;AAC5C,MAAA,OAAQC,CAAU,IAAK;AACnBA,QAAAA,CAAC,CAASC,OAAO,IAAA,IAAA,IAAjBD,CAAC,CAASC,OAAO,EAAI,CAAA;QACvBhC,KAAK,CAACkB,qBAAqB,EAAE,CAAA;OAC9B,CAAA;KACF,CAAA;IACDlB,KAAK,CAACiC,qBAAqB,GAAG,MAAM;MAClC,MAAMnC,QAAQ,GAAGE,KAAK,CAACkC,QAAQ,EAAE,CAACpC,QAAQ,CAAA;AAC1C,MAAA,OAAOA,QAAQ,KAAK,IAAI,IAAIqC,MAAM,CAACC,MAAM,CAACtC,QAAQ,CAAC,CAAC6B,IAAI,CAACU,OAAO,CAAC,CAAA;KAClE,CAAA;IACDrC,KAAK,CAACmB,oBAAoB,GAAG,MAAM;MACjC,MAAMrB,QAAQ,GAAGE,KAAK,CAACkC,QAAQ,EAAE,CAACpC,QAAQ,CAAA;;AAE1C;AACA,MAAA,IAAI,OAAOA,QAAQ,KAAK,SAAS,EAAE;QACjC,OAAOA,QAAQ,KAAK,IAAI,CAAA;AAC1B,OAAA;MAEA,IAAI,CAACqC,MAAM,CAACG,IAAI,CAACxC,QAAQ,CAAC,CAACyC,MAAM,EAAE;AACjC,QAAA,OAAO,KAAK,CAAA;AACd,OAAA;;AAEA;AACA,MAAA,IAAIvC,KAAK,CAACwC,WAAW,EAAE,CAACd,QAAQ,CAACC,IAAI,CAACC,GAAG,IAAI,CAACA,GAAG,CAACa,aAAa,EAAE,CAAC,EAAE;AAClE,QAAA,OAAO,KAAK,CAAA;AACd,OAAA;;AAEA;AACA,MAAA,OAAO,IAAI,CAAA;KACZ,CAAA;IACDzC,KAAK,CAAC0C,gBAAgB,GAAG,MAAM;MAC7B,IAAIC,QAAQ,GAAG,CAAC,CAAA;AAEhB,MAAA,MAAMC,MAAM,GACV5C,KAAK,CAACkC,QAAQ,EAAE,CAACpC,QAAQ,KAAK,IAAI,GAC9BqC,MAAM,CAACG,IAAI,CAACtC,KAAK,CAACwC,WAAW,EAAE,CAACK,QAAQ,CAAC,GACzCV,MAAM,CAACG,IAAI,CAACtC,KAAK,CAACkC,QAAQ,EAAE,CAACpC,QAAQ,CAAC,CAAA;AAE5C8C,MAAAA,MAAM,CAACE,OAAO,CAACC,EAAE,IAAI;AACnB,QAAA,MAAMC,OAAO,GAAGD,EAAE,CAACE,KAAK,CAAC,GAAG,CAAC,CAAA;QAC7BN,QAAQ,GAAGO,IAAI,CAACC,GAAG,CAACR,QAAQ,EAAEK,OAAO,CAACT,MAAM,CAAC,CAAA;AAC/C,OAAC,CAAC,CAAA;AAEF,MAAA,OAAOI,QAAQ,CAAA;KAChB,CAAA;IACD3C,KAAK,CAACoD,sBAAsB,GAAG,MAAMpD,KAAK,CAACqD,iBAAiB,EAAE,CAAA;IAC9DrD,KAAK,CAACsD,mBAAmB,GAAG,MAAM;MAChC,IAAI,CAACtD,KAAK,CAACuD,oBAAoB,IAAIvD,KAAK,CAACW,OAAO,CAAC2C,mBAAmB,EAAE;QACpEtD,KAAK,CAACuD,oBAAoB,GAAGvD,KAAK,CAACW,OAAO,CAAC2C,mBAAmB,CAACtD,KAAK,CAAC,CAAA;AACvE,OAAA;MAEA,IAAIA,KAAK,CAACW,OAAO,CAACG,eAAe,IAAI,CAACd,KAAK,CAACuD,oBAAoB,EAAE;AAChE,QAAA,OAAOvD,KAAK,CAACoD,sBAAsB,EAAE,CAAA;AACvC,OAAA;AAEA,MAAA,OAAOpD,KAAK,CAACuD,oBAAoB,EAAE,CAAA;KACpC,CAAA;GACF;AAEDC,EAAAA,SAAS,EAAEA,CACT5B,GAAe,EACf5B,KAAmB,KACV;AACT4B,IAAAA,GAAG,CAAC6B,cAAc,GAAG3D,QAAQ,IAAI;AAC/BE,MAAAA,KAAK,CAACgB,WAAW,CAAC0C,GAAG,IAAI;AAAA,QAAA,IAAAC,SAAA,CAAA;AACvB,QAAA,MAAMC,MAAM,GAAGF,GAAG,KAAK,IAAI,GAAG,IAAI,GAAG,CAAC,EAACA,GAAG,IAAHA,IAAAA,IAAAA,GAAG,CAAG9B,GAAG,CAACmB,EAAE,CAAC,CAAA,CAAA;QAEpD,IAAIc,WAA8B,GAAG,EAAE,CAAA;QAEvC,IAAIH,GAAG,KAAK,IAAI,EAAE;AAChBvB,UAAAA,MAAM,CAACG,IAAI,CAACtC,KAAK,CAACwC,WAAW,EAAE,CAACK,QAAQ,CAAC,CAACC,OAAO,CAACgB,KAAK,IAAI;AACzDD,YAAAA,WAAW,CAACC,KAAK,CAAC,GAAG,IAAI,CAAA;AAC3B,WAAC,CAAC,CAAA;AACJ,SAAC,MAAM;AACLD,UAAAA,WAAW,GAAGH,GAAG,CAAA;AACnB,SAAA;QAEA5D,QAAQ,GAAA,CAAA6D,SAAA,GAAG7D,QAAQ,YAAA6D,SAAA,GAAI,CAACC,MAAM,CAAA;AAE9B,QAAA,IAAI,CAACA,MAAM,IAAI9D,QAAQ,EAAE;UACvB,OAAO;AACL,YAAA,GAAG+D,WAAW;YACd,CAACjC,GAAG,CAACmB,EAAE,GAAG,IAAA;WACX,CAAA;AACH,SAAA;AAEA,QAAA,IAAIa,MAAM,IAAI,CAAC9D,QAAQ,EAAE;UACvB,MAAM;AAAE,YAAA,CAAC8B,GAAG,CAACmB,EAAE,GAAGgB,CAAC;YAAE,GAAGC,IAAAA;AAAK,WAAC,GAAGH,WAAW,CAAA;AAC5C,UAAA,OAAOG,IAAI,CAAA;AACb,SAAA;AAEA,QAAA,OAAON,GAAG,CAAA;AACZ,OAAC,CAAC,CAAA;KACH,CAAA;IACD9B,GAAG,CAACa,aAAa,GAAG,MAAM;AAAA,MAAA,IAAAwB,qBAAA,CAAA;MACxB,MAAMnE,QAAQ,GAAGE,KAAK,CAACkC,QAAQ,EAAE,CAACpC,QAAQ,CAAA;AAE1C,MAAA,OAAO,CAAC,EAAA,CAAAmE,qBAAA,GACNjE,KAAK,CAACW,OAAO,CAACuD,gBAAgB,IAA9BlE,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,KAAK,CAACW,OAAO,CAACuD,gBAAgB,CAAGtC,GAAG,CAAC,KAAAqC,IAAAA,GAAAA,qBAAA,GACpCnE,QAAQ,KAAK,IAAI,KAAIA,QAAQ,IAAA,IAAA,GAAA,KAAA,CAAA,GAARA,QAAQ,CAAG8B,GAAG,CAACmB,EAAE,CAAC,CACzC,CAAA,CAAA;KACF,CAAA;IACDnB,GAAG,CAACC,YAAY,GAAG,MAAM;AAAA,MAAA,IAAAsC,qBAAA,EAAAC,qBAAA,EAAAC,YAAA,CAAA;AACvB,MAAA,OAAA,CAAAF,qBAAA,GACEnE,KAAK,CAACW,OAAO,CAAC2D,eAAe,IAA7BtE,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,KAAK,CAACW,OAAO,CAAC2D,eAAe,CAAG1C,GAAG,CAAC,KAAAuC,IAAAA,GAAAA,qBAAA,GACnC,CAAAC,CAAAA,qBAAA,GAACpE,KAAK,CAACW,OAAO,CAAC4D,eAAe,KAAAH,IAAAA,GAAAA,qBAAA,GAAI,IAAI,KAAK,CAAC,EAAAC,CAAAA,YAAA,GAACzC,GAAG,CAAC4C,OAAO,KAAXH,IAAAA,IAAAA,YAAA,CAAa9B,MAAM,CAAA,CAAA;KAEpE,CAAA;IACDX,GAAG,CAAC6C,uBAAuB,GAAG,MAAM;MAClC,IAAIC,eAAe,GAAG,IAAI,CAAA;MAC1B,IAAIC,UAAU,GAAG/C,GAAG,CAAA;AAEpB,MAAA,OAAO8C,eAAe,IAAIC,UAAU,CAACC,QAAQ,EAAE;QAC7CD,UAAU,GAAG3E,KAAK,CAAC6E,MAAM,CAACF,UAAU,CAACC,QAAQ,EAAE,IAAI,CAAC,CAAA;AACpDF,QAAAA,eAAe,GAAGC,UAAU,CAAClC,aAAa,EAAE,CAAA;AAC9C,OAAA;AAEA,MAAA,OAAOiC,eAAe,CAAA;KACvB,CAAA;IACD9C,GAAG,CAACkD,wBAAwB,GAAG,MAAM;AACnC,MAAA,MAAMC,SAAS,GAAGnD,GAAG,CAACC,YAAY,EAAE,CAAA;AAEpC,MAAA,OAAO,MAAM;QACX,IAAI,CAACkD,SAAS,EAAE,OAAA;QAChBnD,GAAG,CAAC6B,cAAc,EAAE,CAAA;OACrB,CAAA;KACF,CAAA;AACH,GAAA;AACF;;;;"}