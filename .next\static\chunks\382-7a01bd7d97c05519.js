"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[382],{80382:function(e,t,r){r.d(t,{R:function(){return v}});var a=r(57437),l=r(2265),s=r(61865),i=r(37570),n=r(92160),o=r(85754),d=r(45179),c=r(49842),u=r(42706),m=r(31478),p=r(82549),h=r(5925);let x=n.Ry({title:n.Z_().min(1,"Title is required"),description:n.Z_().optional(),customerId:n.Z_().min(1,"Customer is required"),quotationId:n.Z_().optional(),invoiceId:n.Z_().optional(),type:n.Km(["SERVICE","PRODUCT","SUBSCRIPTION","MAINTENANC<PERSON>","CONSULTING","OTHER"]).default("SERVICE"),status:n.Km(["DRAFT","REVIEW","SENT","SIGNED","ACTIVE","COMPLETED","CANCELLED","EXPIRED"]).default("DRAFT"),value:n.Rx().min(0,"Contract value must be positive").optional(),currency:n.Z_().default("USD"),startDate:n.Z_().optional(),endDate:n.Z_().optional(),renewalDate:n.Z_().optional(),autoRenewal:n.O7().default(!1),renewalPeriod:n.Rx().optional(),terms:n.Z_().optional(),conditions:n.Z_().optional(),notes:n.Z_().optional(),templateId:n.Z_().optional(),signatureRequired:n.O7().default(!0),priority:n.Km(["LOW","MEDIUM","HIGH","URGENT"]).default("MEDIUM"),tags:n.IX(n.Z_()).optional().default([]),assignedToId:n.Z_().optional()});function v(e){let{isOpen:t,onClose:r,onSuccess:n,contract:v,mode:g,preselectedCustomerId:j,preselectedQuotationId:f,preselectedInvoiceId:b}=e,[y,I]=(0,l.useState)(!1),[N,w]=(0,l.useState)([]),[D,C]=(0,l.useState)([]),[E,S]=(0,l.useState)([]),[R,T]=(0,l.useState)([]),[_,A]=(0,l.useState)([]),[F,U]=(0,l.useState)(""),{register:q,handleSubmit:P,formState:{errors:O},reset:Z,watch:M,setValue:k,getValues:V}=(0,s.cI)({resolver:(0,i.F)(x),defaultValues:v?{title:v.title,description:v.description||"",customerId:v.customerId||j||"",quotationId:v.quotationId||f||"",invoiceId:v.invoiceId||b||"",type:v.type||"SERVICE",status:v.status||"DRAFT",value:v.value||void 0,currency:v.currency||"USD",startDate:v.startDate?new Date(v.startDate).toISOString().split("T")[0]:"",endDate:v.endDate?new Date(v.endDate).toISOString().split("T")[0]:"",renewalDate:v.renewalDate?new Date(v.renewalDate).toISOString().split("T")[0]:"",autoRenewal:v.autoRenewal||!1,renewalPeriod:v.renewalPeriod||void 0,terms:v.terms||"",conditions:v.conditions||"",notes:v.notes||"",templateId:v.templateId||"",signatureRequired:!1!==v.signatureRequired,priority:v.priority||"MEDIUM",tags:v.tags||[],assignedToId:v.assignedToId||""}:{type:"SERVICE",status:"DRAFT",currency:"USD",autoRenewal:!1,signatureRequired:!0,priority:"MEDIUM",tags:[],customerId:j||"",quotationId:f||"",invoiceId:b||""}}),L=M("tags");(0,l.useEffect)(()=>{let e=async()=>{try{let[e,t,r]=await Promise.all([fetch("/api/customers?limit=100"),fetch("/api/quotations?limit=100"),fetch("/api/invoices?limit=100")]);if(e.ok){let t=await e.json();w(t.customers||[])}if(t.ok){let e=await t.json();C(e.quotations||[])}if(r.ok){let e=await r.json();S(e.invoices||[])}}catch(e){console.error("Error fetching data:",e)}};t&&e()},[t]);let G=()=>{!F.trim()||(null==L?void 0:L.includes(F.trim()))||(k("tags",[...L||[],F.trim()]),U(""))},H=e=>{k("tags",(L||[]).filter(t=>t!==e))},B=async e=>{I(!0);try{let t="create"===g?"/api/contracts":"/api/contracts/".concat(v.id),a=await fetch(t,{method:"create"===g?"POST":"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!a.ok){let e=await a.json();throw Error(e.error||"Failed to save contract")}h.toast.success("Contract ".concat("create"===g?"created":"updated"," successfully")),Z(),n(),r()}catch(e){h.toast.error(e instanceof Error?e.message:"An error occurred")}finally{I(!1)}},K=()=>{Z(),r()};return(0,a.jsx)(u.Vq,{open:t,onOpenChange:K,children:(0,a.jsxs)(u.cZ,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)(u.fK,{children:[(0,a.jsx)(u.$N,{children:"create"===g?"Create New Contract":"Edit Contract"}),(0,a.jsx)(u.Be,{children:"create"===g?"Create a new contract with terms, conditions, and signature requirements.":"Update the contract information and settings."})]}),(0,a.jsxs)("form",{onSubmit:P(B),className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)(c._,{htmlFor:"title",children:"Contract Title *"}),(0,a.jsx)(d.I,{id:"title",...q("title"),placeholder:"Contract title"}),O.title&&(0,a.jsx)("p",{className:"text-sm text-red-600 mt-1",children:O.title.message})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"customerId",children:"Customer *"}),(0,a.jsxs)("select",{id:"customerId",...q("customerId"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"",children:"Select a customer"}),N.map(e=>(0,a.jsxs)("option",{value:e.id,children:[e.name," ",e.company&&"(".concat(e.company,")")]},e.id))]}),O.customerId&&(0,a.jsx)("p",{className:"text-sm text-red-600 mt-1",children:O.customerId.message})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"type",children:"Contract Type"}),(0,a.jsx)("select",{id:"type",...q("type"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[{value:"SERVICE",label:"Service Agreement"},{value:"PRODUCT",label:"Product Sale"},{value:"SUBSCRIPTION",label:"Subscription"},{value:"MAINTENANCE",label:"Maintenance"},{value:"CONSULTING",label:"Consulting"},{value:"OTHER",label:"Other"}].map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"status",children:"Status"}),(0,a.jsx)("select",{id:"status",...q("status"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[{value:"DRAFT",label:"Draft"},{value:"REVIEW",label:"Under Review"},{value:"SENT",label:"Sent to Customer"},{value:"SIGNED",label:"Signed"},{value:"ACTIVE",label:"Active"},{value:"COMPLETED",label:"Completed"},{value:"CANCELLED",label:"Cancelled"},{value:"EXPIRED",label:"Expired"}].map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"priority",children:"Priority"}),(0,a.jsx)("select",{id:"priority",...q("priority"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[{value:"LOW",label:"Low"},{value:"MEDIUM",label:"Medium"},{value:"HIGH",label:"High"},{value:"URGENT",label:"Urgent"}].map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"quotationId",children:"Related Quotation"}),(0,a.jsxs)("select",{id:"quotationId",...q("quotationId"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"",children:"Select a quotation (optional)"}),D.map(e=>(0,a.jsxs)("option",{value:e.id,children:[e.quotationNumber," - ",e.title]},e.id))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"invoiceId",children:"Related Invoice"}),(0,a.jsxs)("select",{id:"invoiceId",...q("invoiceId"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"",children:"Select an invoice (optional)"}),E.map(e=>(0,a.jsx)("option",{value:e.id,children:e.invoiceNumber},e.id))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"assignedToId",children:"Assigned To"}),(0,a.jsxs)("select",{id:"assignedToId",...q("assignedToId"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"",children:"Select a user (optional)"}),R.map(e=>(0,a.jsx)("option",{value:e.id,children:e.name||e.email},e.id))]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"description",children:"Description"}),(0,a.jsx)("textarea",{id:"description",...q("description"),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Brief description of the contract..."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"value",children:"Contract Value"}),(0,a.jsx)(d.I,{id:"value",type:"number",min:"0",step:"0.01",...q("value",{valueAsNumber:!0}),placeholder:"0.00"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"currency",children:"Currency"}),(0,a.jsx)("select",{id:"currency",...q("currency"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[{value:"USD",label:"USD ($)"},{value:"EUR",label:"EUR (€)"},{value:"GBP",label:"GBP (\xa3)"},{value:"CAD",label:"CAD (C$)"},{value:"AUD",label:"AUD (A$)"}].map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"renewalPeriod",children:"Renewal Period (months)"}),(0,a.jsx)(d.I,{id:"renewalPeriod",type:"number",min:"1",...q("renewalPeriod",{valueAsNumber:!0}),placeholder:"12"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"startDate",children:"Start Date"}),(0,a.jsx)(d.I,{id:"startDate",type:"date",...q("startDate")})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"endDate",children:"End Date"}),(0,a.jsx)(d.I,{id:"endDate",type:"date",...q("endDate")})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"renewalDate",children:"Renewal Date"}),(0,a.jsx)(d.I,{id:"renewalDate",type:"date",...q("renewalDate")})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",id:"autoRenewal",...q("autoRenewal"),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,a.jsx)(c._,{htmlFor:"autoRenewal",children:"Auto Renewal"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",id:"signatureRequired",...q("signatureRequired"),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,a.jsx)(c._,{htmlFor:"signatureRequired",children:"Signature Required"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"tags",children:"Tags"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(d.I,{value:F,onChange:e=>U(e.target.value),placeholder:"Add a tag...",onKeyPress:e=>{"Enter"===e.key&&(e.preventDefault(),G())}}),(0,a.jsx)(o.z,{type:"button",onClick:G,size:"sm",children:"Add"})]}),L&&L.length>0&&(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:L.map(e=>(0,a.jsxs)(m.C,{variant:"secondary",className:"flex items-center space-x-1",children:[(0,a.jsx)("span",{children:e}),(0,a.jsx)("button",{type:"button",onClick:()=>H(e),className:"ml-1 hover:text-red-600",children:(0,a.jsx)(p.Z,{className:"h-3 w-3"})})]},e))})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"terms",children:"Terms & Conditions"}),(0,a.jsx)("textarea",{id:"terms",...q("terms"),rows:6,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Contract terms and conditions..."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"conditions",children:"Additional Conditions"}),(0,a.jsx)("textarea",{id:"conditions",...q("conditions"),rows:6,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Additional conditions and clauses..."})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"notes",children:"Internal Notes"}),(0,a.jsx)("textarea",{id:"notes",...q("notes"),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Internal notes (not visible to customer)..."})]}),(0,a.jsxs)(u.cN,{children:[(0,a.jsx)(o.z,{type:"button",variant:"outline",onClick:K,children:"Cancel"}),(0,a.jsx)(o.z,{type:"submit",disabled:y,children:y?"Saving...":"create"===g?"Create Contract":"Update Contract"})]})]})]})})}}}]);