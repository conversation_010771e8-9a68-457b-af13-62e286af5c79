"use strict";(()=>{var e={};e.id=1663,e.ids=[1663],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},35271:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>E,originalPathname:()=>x,patchFetch:()=>_,requestAsyncStorage:()=>g,routeModule:()=>I,serverHooks:()=>h,staticGenerationAsyncStorage:()=>f,staticGenerationBailout:()=>v});var a={};r.r(a),r.d(a,{GET:()=>y,POST:()=>w});var i=r(95419),s=r(69108),o=r(99678),n=r(78070),l=r(81355),d=r(3205),u=r(9108),c=r(25252),p=r(52178);let m=c.Ry({type:c.Km(["NOTE","CALL","EMAIL","MEETING","TASK"]),title:c.Z_().min(1,"Title is required"),description:c.Z_().optional().nullable(),status:c.Km(["PENDING","COMPLETED","CANCELLED"]).default("COMPLETED"),priority:c.Km(["LOW","MEDIUM","HIGH","URGENT"]).default("MEDIUM"),scheduledAt:c.Z_().optional().nullable(),completedAt:c.Z_().optional().nullable(),duration:c.Rx().optional().nullable(),outcome:c.Z_().optional().nullable(),followUpRequired:c.O7().default(!1),followUpDate:c.Z_().optional().nullable(),tags:c.IX(c.Z_()).default([])});async function y(e,{params:t}){try{let r=await (0,l.getServerSession)(d.L);if(!r?.user?.id||!r?.user?.companyId)return n.Z.json({error:"Unauthorized"},{status:401});let{searchParams:a}=new URL(e.url),i=parseInt(a.get("page")||"1"),s=parseInt(a.get("limit")||"20"),o=a.get("type"),c=a.get("status");if(!await u._.lead.findFirst({where:{id:t.id,companyId:r.user.companyId}}))return n.Z.json({error:"Lead not found"},{status:404});let p={leadId:t.id};o&&(p.type=o),c&&(p.status=c);let[m,y]=await Promise.all([u._.activity.findMany({where:p,include:{createdBy:{select:{id:!0,name:!0,email:!0}},assignedTo:{select:{id:!0,name:!0,email:!0}}},orderBy:{createdAt:"desc"},skip:(i-1)*s,take:s}),u._.activity.count({where:p})]);return n.Z.json({activities:m,pagination:{page:i,limit:s,total:y,pages:Math.ceil(y/s)}})}catch(e){return console.error("Error fetching activities:",e),n.Z.json({error:"Failed to fetch activities"},{status:500})}}async function w(e,{params:t}){try{let r=await (0,l.getServerSession)(d.L);if(!r?.user?.id||!r?.user?.companyId)return n.Z.json({error:"Unauthorized"},{status:401});let a=await e.json(),i=m.parse(a);if(!await u._.lead.findFirst({where:{id:t.id,companyId:r.user.companyId}}))return n.Z.json({error:"Lead not found"},{status:404});let s={...i,leadId:t.id,companyId:r.user.companyId,createdById:r.user.id};i.scheduledAt&&(s.scheduledAt=new Date(i.scheduledAt)),i.completedAt&&(s.completedAt=new Date(i.completedAt)),i.followUpDate&&(s.followUpDate=new Date(i.followUpDate)),"COMPLETED"!==i.status||i.completedAt||(s.completedAt=new Date);let o=await u._.activity.create({data:s,include:{createdBy:{select:{id:!0,name:!0,email:!0}},assignedTo:{select:{id:!0,name:!0,email:!0}}}});return n.Z.json({activity:o},{status:201})}catch(e){if(e instanceof p.jm)return n.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("Error creating activity:",e),n.Z.json({error:"Failed to create activity"},{status:500})}}let I=new i.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/leads/[id]/activities/route",pathname:"/api/leads/[id]/activities",filename:"route",bundlePath:"app/api/leads/[id]/activities/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\leads\\[id]\\activities\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:g,staticGenerationAsyncStorage:f,serverHooks:h,headerHooks:E,staticGenerationBailout:v}=I,x="/api/leads/[id]/activities/route";function _(){return(0,o.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:f})}},3205:(e,t,r)=>{r.d(t,{L:()=>d});var a=r(86485),i=r(10375),s=r(50694),o=r(6521),n=r.n(o),l=r(9108);let d={providers:[(0,a.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await l._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),r=t?.companyId;if(!r&&t){let e=await l._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(r=e?.id)&&await l._.user.update({where:{id:t.id},data:{companyId:r}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await n().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await l._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:r}}catch(e){return console.error("Authentication error:",e),null}}}),(0,i.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,s.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,r)=>{r.d(t,{_:()=>i});let a=require("@prisma/client"),i=globalThis.prisma??new a.PrismaClient}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,6206,6521,2455,4520,5252],()=>r(35271));module.exports=a})();