(()=>{var e={};e.id=2456,e.ids=[2456],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},59627:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>u,tree:()=>c});var a=t(50482),l=t(69108),r=t(62563),i=t.n(r),n=t(68300),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let c=["",{children:["super-admin",{children:["audit-logs",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,80726)),"C:\\proj\\nextjs-saas\\app\\super-admin\\audit-logs\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,11285)),"C:\\proj\\nextjs-saas\\app\\super-admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,59504)),"C:\\proj\\nextjs-saas\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\proj\\nextjs-saas\\app\\super-admin\\audit-logs\\page.tsx"],x="/super-admin/audit-logs/page",m={require:t,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:l.x.APP_PAGE,page:"/super-admin/audit-logs/page",pathname:"/super-admin/audit-logs",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},52903:(e,s,t)=>{Promise.resolve().then(t.bind(t,53390))},53390:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>C});var a=t(95344),l=t(3729),r=t(47674),i=t(22254),n=t(61351),d=t(16212),c=t(92549),o=t(69436),x=t(82885),m=t(81036),u=t(17470),h=t(16802),p=t(18822),j=t(88534),f=t(45961);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let g=(0,t(69224).Z)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);var y=t(37121),N=t(33733),v=t(96885),b=t(23485),w=t(28765),A=t(53148);function C(){let{data:e,status:s}=(0,r.useSession)(),[t,C]=(0,l.useState)([]),[T,R]=(0,l.useState)(null),[S,Z]=(0,l.useState)(!0),[k,I]=(0,l.useState)(""),[E,_]=(0,l.useState)("all"),[P,L]=(0,l.useState)("all"),[M,q]=(0,l.useState)("all"),[O,z]=(0,l.useState)(1),[D,H]=(0,l.useState)(1),[U,Q]=(0,l.useState)(null);if("loading"===s)return a.jsx("div",{className:"min-h-screen flex items-center justify-center",children:a.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})});"unauthenticated"===s&&(0,i.redirect)("/auth/signin"),e?.user?.role!=="SUPER_ADMIN"&&(0,i.redirect)("/dashboard");let V=async()=>{try{Z(!0);let e=new URLSearchParams({page:O.toString(),limit:"50",...k&&{search:k},...E&&"all"!==E&&{action:E},...P&&"all"!==P&&{severity:P},...M&&"all"!==M&&{entityType:M}}),s=await fetch(`/api/super-admin/audit-logs?${e}`);if(!s.ok)throw Error("Failed to fetch audit logs");let t=await s.json();C(t.auditLogs),R(t.stats),H(t.pagination.pages)}catch(e){console.error("Error fetching audit logs:",e)}finally{Z(!1)}};(0,l.useEffect)(()=>{V()},[O,k,E,P,M]);let G=e=>a.jsx(o.C,{variant:{LOW:"outline",INFO:"default",MEDIUM:"secondary",HIGH:"destructive",CRITICAL:"destructive"}[e]||"default",children:a.jsx("span",{className:{LOW:"text-gray-600",INFO:"text-blue-600",MEDIUM:"text-yellow-600",HIGH:"text-orange-600",CRITICAL:"text-red-600"}[e]||"text-gray-600",children:e})}),Y=e=>new Date(e).toLocaleString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",second:"2-digit"}),$=e=>e.includes("LOGIN")||e.includes("AUTH")?p.Z:e.includes("CREATE")||e.includes("UPDATE")||e.includes("DELETE")?j.Z:e.includes("SECURITY")||e.includes("ALERT")?f.Z:g;return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(y.Z,{className:"h-8 w-8 text-blue-600"}),a.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Audit Logs"})]}),a.jsx("p",{className:"text-gray-500 mt-1",children:"Track all system activities and changes"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(d.z,{variant:"outline",onClick:V,disabled:S,children:[a.jsx(N.Z,{className:`h-4 w-4 mr-2 ${S?"animate-spin":""}`}),"Refresh"]}),(0,a.jsxs)(d.z,{variant:"outline",children:[a.jsx(v.Z,{className:"h-4 w-4 mr-2"}),"Export"]})]})]}),T&&(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Total Logs"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:T.total.toLocaleString()})]}),a.jsx(y.Z,{className:"h-8 w-8 text-blue-600"})]})})}),a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Last 24 Hours"}),a.jsx("p",{className:"text-2xl font-bold text-green-600",children:T.last24Hours})]}),a.jsx(j.Z,{className:"h-8 w-8 text-green-600"})]})})}),a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Critical Events"}),a.jsx("p",{className:"text-2xl font-bold text-red-600",children:T.bySeverity.find(e=>"CRITICAL"===e.severity)?.count||0})]}),a.jsx(f.Z,{className:"h-8 w-8 text-red-600"})]})})}),a.jsx(n.Zb,{children:(0,a.jsxs)(n.aY,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Top Action"}),a.jsx("p",{className:"text-lg font-bold text-purple-600",children:T.byAction[0]?.action||"N/A"})]}),a.jsx(b.Z,{className:"h-8 w-8 text-purple-600"})]}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[T.byAction[0]?.count||0," occurrences"]})]})})]}),a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4",children:[a.jsx("div",{className:"lg:col-span-2",children:(0,a.jsxs)("div",{className:"relative",children:[a.jsx(w.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),a.jsx(c.I,{placeholder:"Search logs...",value:k,onChange:e=>I(e.target.value),className:"pl-10"})]})}),(0,a.jsxs)(u.Ph,{value:E,onValueChange:_,children:[a.jsx(u.i4,{children:a.jsx(u.ki,{placeholder:"All Actions"})}),(0,a.jsxs)(u.Bw,{children:[a.jsx(u.Ql,{value:"all",children:"All Actions"}),T?.byAction.slice(0,10).map(e=>a.jsxs(u.Ql,{value:e.action,children:[e.action," (",e.count,")"]},e.action))]})]}),(0,a.jsxs)(u.Ph,{value:P,onValueChange:L,children:[a.jsx(u.i4,{children:a.jsx(u.ki,{placeholder:"All Severities"})}),(0,a.jsxs)(u.Bw,{children:[a.jsx(u.Ql,{value:"all",children:"All Severities"}),a.jsx(u.Ql,{value:"LOW",children:"Low"}),a.jsx(u.Ql,{value:"INFO",children:"Info"}),a.jsx(u.Ql,{value:"MEDIUM",children:"Medium"}),a.jsx(u.Ql,{value:"HIGH",children:"High"}),a.jsx(u.Ql,{value:"CRITICAL",children:"Critical"})]})]}),(0,a.jsxs)(u.Ph,{value:M,onValueChange:q,children:[a.jsx(u.i4,{children:a.jsx(u.ki,{placeholder:"All Entities"})}),(0,a.jsxs)(u.Bw,{children:[a.jsx(u.Ql,{value:"all",children:"All Entities"}),T?.byEntityType.slice(0,10).map(e=>a.jsxs(u.Ql,{value:e.entityType,children:[e.entityType," (",e.count,")"]},e.entityType))]})]})]})})}),(0,a.jsxs)(n.Zb,{children:[a.jsx(n.Ol,{children:(0,a.jsxs)(n.ll,{children:["Audit Logs (",t.length,")"]})}),a.jsx(n.aY,{children:S?a.jsx("div",{className:"flex items-center justify-center py-8",children:a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):a.jsx("div",{className:"overflow-x-auto",children:(0,a.jsxs)(m.iA,{children:[a.jsx(m.xD,{children:(0,a.jsxs)(m.SC,{children:[a.jsx(m.ss,{children:"Action"}),a.jsx(m.ss,{children:"User"}),a.jsx(m.ss,{children:"Entity"}),a.jsx(m.ss,{children:"Severity"}),a.jsx(m.ss,{children:"IP Address"}),a.jsx(m.ss,{children:"Timestamp"}),a.jsx(m.ss,{children:"Actions"})]})}),a.jsx(m.RM,{children:t.map(e=>{let s=$(e.action);return(0,a.jsxs)(m.SC,{children:[a.jsx(m.pj,{children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(s,{className:"h-4 w-4 text-gray-500"}),a.jsx("span",{className:"font-medium",children:e.action})]})}),a.jsx(m.pj,{children:e.user?(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(x.qE,{className:"h-6 w-6",children:[a.jsx(x.F$,{src:e.user.avatar,alt:e.user.name}),a.jsx(x.Q5,{children:e.user.name?.charAt(0)?.toUpperCase()||"U"})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium text-sm",children:e.user.name}),a.jsx("p",{className:"text-xs text-gray-500",children:e.userRole})]})]}):(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium text-sm",children:e.userEmail||"System"}),a.jsx("p",{className:"text-xs text-gray-500",children:e.userRole||"N/A"})]})}),a.jsx(m.pj,{children:(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium text-sm",children:e.entityType}),e.entityId&&(0,a.jsxs)("p",{className:"text-xs text-gray-500 font-mono",children:[e.entityId.slice(0,8),"..."]})]})}),a.jsx(m.pj,{children:G(e.severity)}),a.jsx(m.pj,{children:a.jsx("span",{className:"font-mono text-sm",children:e.ipAddress||"N/A"})}),a.jsx(m.pj,{children:a.jsx("div",{className:"text-sm",children:a.jsx("p",{children:Y(e.createdAt)})})}),a.jsx(m.pj,{children:(0,a.jsxs)(h.Vq,{children:[a.jsx(h.hg,{asChild:!0,children:a.jsx(d.z,{variant:"ghost",size:"sm",onClick:()=>Q(e),children:a.jsx(A.Z,{className:"h-4 w-4"})})}),(0,a.jsxs)(h.cZ,{className:"max-w-2xl",children:[(0,a.jsxs)(h.fK,{children:[a.jsx(h.$N,{children:"Audit Log Details"}),a.jsx(h.Be,{children:"Detailed information about this audit log entry"})]}),U&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Action"}),a.jsx("p",{className:"text-sm",children:U.action})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Severity"}),a.jsx("div",{className:"mt-1",children:G(U.severity)})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Entity Type"}),a.jsx("p",{className:"text-sm",children:U.entityType})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Entity ID"}),a.jsx("p",{className:"text-sm font-mono",children:U.entityId||"N/A"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"text-sm font-medium text-gray-500",children:"IP Address"}),a.jsx("p",{className:"text-sm font-mono",children:U.ipAddress||"N/A"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Timestamp"}),a.jsx("p",{className:"text-sm",children:Y(U.createdAt)})]})]}),U.oldValues&&(0,a.jsxs)("div",{children:[a.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Old Values"}),a.jsx("pre",{className:"text-xs bg-gray-100 p-3 rounded mt-1 overflow-auto",children:JSON.stringify(U.oldValues,null,2)})]}),U.newValues&&(0,a.jsxs)("div",{children:[a.jsx("label",{className:"text-sm font-medium text-gray-500",children:"New Values"}),a.jsx("pre",{className:"text-xs bg-gray-100 p-3 rounded mt-1 overflow-auto",children:JSON.stringify(U.newValues,null,2)})]}),U.metadata&&(0,a.jsxs)("div",{children:[a.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Metadata"}),a.jsx("pre",{className:"text-xs bg-gray-100 p-3 rounded mt-1 overflow-auto",children:JSON.stringify(U.metadata,null,2)})]})]})]})]})})]},e.id)})})]})})})]}),D>1&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Page ",O," of ",D]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(d.z,{variant:"outline",onClick:()=>z(O-1),disabled:1===O,children:"Previous"}),a.jsx(d.z,{variant:"outline",onClick:()=>z(O+1),disabled:O===D,children:"Next"})]})]})]})}},92549:(e,s,t)=>{"use strict";t.d(s,{I:()=>i});var a=t(95344),l=t(3729),r=t(91626);let i=l.forwardRef(({className:e,type:s,...t},l)=>a.jsx("input",{type:s,className:(0,r.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:l,...t}));i.displayName="Input"},17470:(e,s,t)=>{"use strict";t.d(s,{Bw:()=>p,Ph:()=>o,Ql:()=>j,i4:()=>m,ki:()=>x});var a=t(95344),l=t(3729),r=t(1146),i=t(25390),n=t(12704),d=t(62312),c=t(91626);let o=r.fC;r.ZA;let x=r.B4,m=l.forwardRef(({className:e,children:s,...t},l)=>(0,a.jsxs)(r.xz,{ref:l,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...t,children:[s,a.jsx(r.JO,{asChild:!0,children:a.jsx(i.Z,{className:"h-4 w-4 opacity-50"})})]}));m.displayName=r.xz.displayName;let u=l.forwardRef(({className:e,...s},t)=>a.jsx(r.u_,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:a.jsx(n.Z,{className:"h-4 w-4"})}));u.displayName=r.u_.displayName;let h=l.forwardRef(({className:e,...s},t)=>a.jsx(r.$G,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:a.jsx(i.Z,{className:"h-4 w-4"})}));h.displayName=r.$G.displayName;let p=l.forwardRef(({className:e,children:s,position:t="popper",...l},i)=>a.jsx(r.h_,{children:(0,a.jsxs)(r.VY,{ref:i,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:t,...l,children:[a.jsx(u,{}),a.jsx(r.l_,{className:(0,c.cn)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),a.jsx(h,{})]})}));p.displayName=r.VY.displayName,l.forwardRef(({className:e,...s},t)=>a.jsx(r.__,{ref:t,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...s})).displayName=r.__.displayName;let j=l.forwardRef(({className:e,children:s,...t},l)=>(0,a.jsxs)(r.ck,{ref:l,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[a.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:a.jsx(r.wU,{children:a.jsx(d.Z,{className:"h-4 w-4"})})}),a.jsx(r.eT,{children:s})]}));j.displayName=r.ck.displayName,l.forwardRef(({className:e,...s},t)=>a.jsx(r.Z0,{ref:t,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",e),...s})).displayName=r.Z0.displayName},81036:(e,s,t)=>{"use strict";t.d(s,{RM:()=>d,SC:()=>c,iA:()=>i,pj:()=>x,ss:()=>o,xD:()=>n});var a=t(95344),l=t(3729),r=t(91626);let i=l.forwardRef(({className:e,...s},t)=>a.jsx("div",{className:"relative w-full overflow-auto",children:a.jsx("table",{ref:t,className:(0,r.cn)("w-full caption-bottom text-sm",e),...s})}));i.displayName="Table";let n=l.forwardRef(({className:e,...s},t)=>a.jsx("thead",{ref:t,className:(0,r.cn)("[&_tr]:border-b",e),...s}));n.displayName="TableHeader";let d=l.forwardRef(({className:e,...s},t)=>a.jsx("tbody",{ref:t,className:(0,r.cn)("[&_tr:last-child]:border-0",e),...s}));d.displayName="TableBody",l.forwardRef(({className:e,...s},t)=>a.jsx("tfoot",{ref:t,className:(0,r.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...s})).displayName="TableFooter";let c=l.forwardRef(({className:e,...s},t)=>a.jsx("tr",{ref:t,className:(0,r.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...s}));c.displayName="TableRow";let o=l.forwardRef(({className:e,...s},t)=>a.jsx("th",{ref:t,className:(0,r.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...s}));o.displayName="TableHead";let x=l.forwardRef(({className:e,...s},t)=>a.jsx("td",{ref:t,className:(0,r.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...s}));x.displayName="TableCell",l.forwardRef(({className:e,...s},t)=>a.jsx("caption",{ref:t,className:(0,r.cn)("mt-4 text-sm text-muted-foreground",e),...s})).displayName="TableCaption"},96885:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},53148:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},80726:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>r,__esModule:()=>l,default:()=>i});let a=(0,t(86843).createProxy)(String.raw`C:\proj\nextjs-saas\app\super-admin\audit-logs\page.tsx`),{__esModule:l,$$typeof:r}=a,i=a.default}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[1638,7948,6671,4626,7792,2506,8830,1729,2125,3965],()=>t(59627));module.exports=a})();