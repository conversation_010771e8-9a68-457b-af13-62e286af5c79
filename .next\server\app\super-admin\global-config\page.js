(()=>{var e={};e.id=4228,e.ids=[4228],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},85637:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>n.a,__next_app__:()=>h,originalPathname:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c});var r=a(50482),l=a(69108),i=a(62563),n=a.n(i),t=a(68300),o={};for(let e in t)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>t[e]);a.d(s,o);let c=["",{children:["super-admin",{children:["global-config",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,45128)),"C:\\proj\\nextjs-saas\\app\\super-admin\\global-config\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,11285)),"C:\\proj\\nextjs-saas\\app\\super-admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,59504)),"C:\\proj\\nextjs-saas\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,69361,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\proj\\nextjs-saas\\app\\super-admin\\global-config\\page.tsx"],m="/super-admin/global-config/page",h={require:a,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:l.x.APP_PAGE,page:"/super-admin/global-config/page",pathname:"/super-admin/global-config",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},83069:(e,s,a)=>{Promise.resolve().then(a.bind(a,62393))},62393:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>S});var r=a(95344),l=a(3729),i=a(47674),n=a(22254),t=a(61351),o=a(16212),c=a(92549),d=a(1586),m=a(93601),h=a(71809),u=a(17470),p=a(25757),x=a(30304),g=a(33733),j=a(31498),f=a(78638),v=a(13746),y=a(23485),b=a(47958),N=a(71206),C=a(1222),w=a(53148),k=a(89895),P=a(44669);function S(){let{data:e,status:s}=(0,i.useSession)(),[a,S]=(0,l.useState)({appName:"SaaS Platform",appDescription:"Modern SaaS application for business management",appUrl:"https://yourapp.com",supportEmail:"<EMAIL>",companyName:"Your Company",companyAddress:"123 Business St, City, Country",companyPhone:"+****************",logoUrl:"",faviconUrl:"",primaryColor:"#3b82f6",secondaryColor:"#64748b",accentColor:"#10b981",backgroundColor:"#ffffff",textColor:"#1f2937",theme:"light",fontFamily:"Inter, sans-serif",customCss:"",timezone:"UTC",dateFormat:"MM/DD/YYYY",currency:"USD",language:"en",enableRegistration:!0,enableTrials:!0,enableMultiTenant:!0,enableApiAccess:!0,enableWebhooks:!0,sessionTimeout:24,passwordMinLength:8,requireTwoFactor:!1,allowSocialLogin:!0,emailProvider:"smtp",smtpHost:"",smtpPort:587,smtpUsername:"",smtpPassword:"",smtpSecure:!0,maxUsersPerCompany:100,maxCompaniesPerUser:5,defaultStorageLimit:5,maintenanceMode:!1,maintenanceMessage:"System is under maintenance. Please check back later."}),[M,F]=(0,l.useState)(!0),[_,U]=(0,l.useState)(!1),[A,T]=(0,l.useState)(!1);(0,l.useEffect)(()=>{D()},[]);let D=async()=>{try{let e=await fetch("/api/super-admin/global-config"),s=await e.json();s.success&&s.config&&S(e=>({...e,...s.config}))}catch(e){console.error("Error fetching config:",e),P.toast.error("Failed to load configuration")}finally{F(!1)}};if("loading"===s)return r.jsx("div",{className:"min-h-screen flex items-center justify-center",children:r.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})});"unauthenticated"===s&&(0,n.redirect)("/auth/signin"),e?.user?.role!=="SUPER_ADMIN"&&(0,n.redirect)("/dashboard"),(0,l.useEffect)(()=>{D()},[]);let I=(e,s)=>{S(a=>({...a,[e]:s}))},Y=async()=>{U(!0);try{let e=await fetch("/api/super-admin/global-config",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)}),s=await e.json();s.success?(P.toast.success("Configuration saved successfully!"),window.location.reload()):P.toast.error(s.error||"Failed to save configuration")}catch(e){console.error("Error saving config:",e),P.toast.error("Failed to save configuration")}finally{U(!1)}};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx(x.Z,{className:"h-8 w-8 text-blue-600"}),r.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Global Configuration"})]}),r.jsx("p",{className:"text-gray-500 mt-1",children:"Manage system-wide settings and configuration"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)(o.z,{variant:"outline",onClick:D,disabled:M,children:[r.jsx(g.Z,{className:`h-4 w-4 mr-2 ${M?"animate-spin":""}`}),"Refresh"]}),(0,r.jsxs)(o.z,{onClick:Y,disabled:_,children:[r.jsx(j.Z,{className:`h-4 w-4 mr-2 ${_?"animate-spin":""}`}),"Save Changes"]})]})]}),(0,r.jsxs)(p.mQ,{defaultValue:"application",className:"space-y-6",children:[(0,r.jsxs)(p.dr,{className:"grid w-full grid-cols-7",children:[r.jsx(p.SP,{value:"application",children:"Application"}),r.jsx(p.SP,{value:"branding",children:"Branding"}),r.jsx(p.SP,{value:"system",children:"System"}),r.jsx(p.SP,{value:"features",children:"Features"}),r.jsx(p.SP,{value:"security",children:"Security"}),r.jsx(p.SP,{value:"email",children:"Email"}),r.jsx(p.SP,{value:"limits",children:"Limits"})]}),r.jsx(p.nU,{value:"application",children:(0,r.jsxs)(t.Zb,{children:[(0,r.jsxs)(t.Ol,{children:[(0,r.jsxs)(t.ll,{className:"flex items-center",children:[r.jsx(f.Z,{className:"h-5 w-5 mr-2"}),"Application Settings"]}),r.jsx(t.SZ,{children:"Basic application information and company details"})]}),(0,r.jsxs)(t.aY,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"appName",children:"Application Name"}),r.jsx(c.I,{id:"appName",value:a.appName,onChange:e=>I("appName",e.target.value),placeholder:"Your SaaS App"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"appUrl",children:"Application URL"}),r.jsx(c.I,{id:"appUrl",value:a.appUrl,onChange:e=>I("appUrl",e.target.value),placeholder:"https://yourapp.com"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"appDescription",children:"Application Description"}),r.jsx(m.g,{id:"appDescription",value:a.appDescription,onChange:e=>I("appDescription",e.target.value),placeholder:"Brief description of your application",rows:3})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"companyName",children:"Company Name"}),r.jsx(c.I,{id:"companyName",value:a.companyName,onChange:e=>I("companyName",e.target.value),placeholder:"Your Company Inc."})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"supportEmail",children:"Support Email"}),r.jsx(c.I,{id:"supportEmail",type:"email",value:a.supportEmail,onChange:e=>I("supportEmail",e.target.value),placeholder:"<EMAIL>"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"companyPhone",children:"Company Phone"}),r.jsx(c.I,{id:"companyPhone",value:a.companyPhone,onChange:e=>I("companyPhone",e.target.value),placeholder:"+****************"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"companyAddress",children:"Company Address"}),r.jsx(c.I,{id:"companyAddress",value:a.companyAddress,onChange:e=>I("companyAddress",e.target.value),placeholder:"123 Business St, City, Country"})]})]})]})]})}),r.jsx(p.nU,{value:"branding",children:(0,r.jsxs)(t.Zb,{children:[(0,r.jsxs)(t.Ol,{children:[(0,r.jsxs)(t.ll,{className:"flex items-center",children:[r.jsx(f.Z,{className:"h-5 w-5 mr-2"}),"Branding & Appearance"]}),r.jsx(t.SZ,{children:"Customize your application's visual identity and branding"})]}),(0,r.jsxs)(t.aY,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"logoUrl",children:"Logo URL"}),r.jsx(c.I,{id:"logoUrl",value:a.logoUrl,onChange:e=>I("logoUrl",e.target.value),placeholder:"https://example.com/logo.png"}),r.jsx("p",{className:"text-xs text-gray-500",children:"Recommended: 200x50px PNG or SVG"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"faviconUrl",children:"Favicon URL"}),r.jsx(c.I,{id:"faviconUrl",value:a.faviconUrl,onChange:e=>I("faviconUrl",e.target.value),placeholder:"https://example.com/favicon.ico"}),r.jsx("p",{className:"text-xs text-gray-500",children:"Recommended: 32x32px ICO or PNG"})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx("h3",{className:"text-lg font-medium",children:"Color Scheme"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"primaryColor",children:"Primary Color"}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[r.jsx(c.I,{id:"primaryColor",type:"color",value:a.primaryColor,onChange:e=>I("primaryColor",e.target.value),className:"w-16 h-10 p-1 border rounded"}),r.jsx(c.I,{value:a.primaryColor,onChange:e=>I("primaryColor",e.target.value),placeholder:"#3b82f6",className:"flex-1"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"secondaryColor",children:"Secondary Color"}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[r.jsx(c.I,{id:"secondaryColor",type:"color",value:a.secondaryColor,onChange:e=>I("secondaryColor",e.target.value),className:"w-16 h-10 p-1 border rounded"}),r.jsx(c.I,{value:a.secondaryColor,onChange:e=>I("secondaryColor",e.target.value),placeholder:"#64748b",className:"flex-1"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"accentColor",children:"Accent Color"}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[r.jsx(c.I,{id:"accentColor",type:"color",value:a.accentColor,onChange:e=>I("accentColor",e.target.value),className:"w-16 h-10 p-1 border rounded"}),r.jsx(c.I,{value:a.accentColor,onChange:e=>I("accentColor",e.target.value),placeholder:"#10b981",className:"flex-1"})]})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"theme",children:"Theme"}),(0,r.jsxs)(u.Ph,{value:a.theme,onValueChange:e=>I("theme",e),children:[r.jsx(u.i4,{children:r.jsx(u.ki,{placeholder:"Select theme"})}),(0,r.jsxs)(u.Bw,{children:[r.jsx(u.Ql,{value:"light",children:"Light"}),r.jsx(u.Ql,{value:"dark",children:"Dark"}),r.jsx(u.Ql,{value:"auto",children:"Auto (System)"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"fontFamily",children:"Font Family"}),(0,r.jsxs)(u.Ph,{value:a.fontFamily,onValueChange:e=>I("fontFamily",e),children:[r.jsx(u.i4,{children:r.jsx(u.ki,{placeholder:"Select font"})}),(0,r.jsxs)(u.Bw,{children:[r.jsx(u.Ql,{value:"Inter, sans-serif",children:"Inter"}),r.jsx(u.Ql,{value:"Roboto, sans-serif",children:"Roboto"}),r.jsx(u.Ql,{value:"Open Sans, sans-serif",children:"Open Sans"}),r.jsx(u.Ql,{value:"Lato, sans-serif",children:"Lato"}),r.jsx(u.Ql,{value:"Poppins, sans-serif",children:"Poppins"}),r.jsx(u.Ql,{value:"Montserrat, sans-serif",children:"Montserrat"})]})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"customCss",children:"Custom CSS"}),r.jsx(m.g,{id:"customCss",value:a.customCss,onChange:e=>I("customCss",e.target.value),placeholder:"/* Add your custom CSS here */ .custom-class { color: #333; }",rows:6,className:"font-mono text-sm"}),r.jsx("p",{className:"text-xs text-gray-500",children:"Add custom CSS to override default styles. Use with caution."})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{children:"Preview"}),(0,r.jsxs)("div",{className:"p-4 border rounded-lg",style:{backgroundColor:a.backgroundColor,color:a.textColor,fontFamily:a.fontFamily},children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[a.logoUrl?r.jsx("img",{src:a.logoUrl,alt:"Logo",className:"h-8"}):r.jsx("div",{className:"w-8 h-8 rounded",style:{backgroundColor:a.primaryColor}}),r.jsx("span",{className:"font-bold",children:a.appName})]}),r.jsx("button",{className:"px-4 py-2 rounded text-white text-sm",style:{backgroundColor:a.primaryColor},children:"Primary Button"}),r.jsx("button",{className:"px-4 py-2 rounded text-white text-sm ml-2",style:{backgroundColor:a.accentColor},children:"Accent Button"})]})]})]})]})}),r.jsx(p.nU,{value:"system",children:(0,r.jsxs)(t.Zb,{children:[(0,r.jsxs)(t.Ol,{children:[(0,r.jsxs)(t.ll,{className:"flex items-center",children:[r.jsx(v.Z,{className:"h-5 w-5 mr-2"}),"System Settings"]}),r.jsx(t.SZ,{children:"Configure system-wide preferences and defaults"})]}),(0,r.jsxs)(t.aY,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"timezone",children:"Default Timezone"}),(0,r.jsxs)(u.Ph,{value:a.timezone,onValueChange:e=>I("timezone",e),children:[r.jsx(u.i4,{children:r.jsx(u.ki,{})}),(0,r.jsxs)(u.Bw,{children:[r.jsx(u.Ql,{value:"UTC",children:"UTC"}),r.jsx(u.Ql,{value:"America/New_York",children:"Eastern Time"}),r.jsx(u.Ql,{value:"America/Chicago",children:"Central Time"}),r.jsx(u.Ql,{value:"America/Denver",children:"Mountain Time"}),r.jsx(u.Ql,{value:"America/Los_Angeles",children:"Pacific Time"}),r.jsx(u.Ql,{value:"Europe/London",children:"London"}),r.jsx(u.Ql,{value:"Europe/Paris",children:"Paris"}),r.jsx(u.Ql,{value:"Asia/Tokyo",children:"Tokyo"}),r.jsx(u.Ql,{value:"Asia/Shanghai",children:"Shanghai"}),r.jsx(u.Ql,{value:"Asia/Kolkata",children:"India"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"dateFormat",children:"Date Format"}),(0,r.jsxs)(u.Ph,{value:a.dateFormat,onValueChange:e=>I("dateFormat",e),children:[r.jsx(u.i4,{children:r.jsx(u.ki,{})}),(0,r.jsxs)(u.Bw,{children:[r.jsx(u.Ql,{value:"MM/DD/YYYY",children:"MM/DD/YYYY"}),r.jsx(u.Ql,{value:"DD/MM/YYYY",children:"DD/MM/YYYY"}),r.jsx(u.Ql,{value:"YYYY-MM-DD",children:"YYYY-MM-DD"}),r.jsx(u.Ql,{value:"DD-MM-YYYY",children:"DD-MM-YYYY"})]})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"currency",children:"Default Currency"}),(0,r.jsxs)(u.Ph,{value:a.currency,onValueChange:e=>I("currency",e),children:[r.jsx(u.i4,{children:r.jsx(u.ki,{})}),(0,r.jsxs)(u.Bw,{children:[r.jsx(u.Ql,{value:"USD",children:"USD - US Dollar"}),r.jsx(u.Ql,{value:"EUR",children:"EUR - Euro"}),r.jsx(u.Ql,{value:"GBP",children:"GBP - British Pound"}),r.jsx(u.Ql,{value:"JPY",children:"JPY - Japanese Yen"}),r.jsx(u.Ql,{value:"INR",children:"INR - Indian Rupee"}),r.jsx(u.Ql,{value:"CAD",children:"CAD - Canadian Dollar"}),r.jsx(u.Ql,{value:"AUD",children:"AUD - Australian Dollar"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"language",children:"Default Language"}),(0,r.jsxs)(u.Ph,{value:a.language,onValueChange:e=>I("language",e),children:[r.jsx(u.i4,{children:r.jsx(u.ki,{})}),(0,r.jsxs)(u.Bw,{children:[r.jsx(u.Ql,{value:"en",children:"English"}),r.jsx(u.Ql,{value:"es",children:"Spanish"}),r.jsx(u.Ql,{value:"fr",children:"French"}),r.jsx(u.Ql,{value:"de",children:"German"}),r.jsx(u.Ql,{value:"it",children:"Italian"}),r.jsx(u.Ql,{value:"pt",children:"Portuguese"}),r.jsx(u.Ql,{value:"ja",children:"Japanese"}),r.jsx(u.Ql,{value:"ko",children:"Korean"}),r.jsx(u.Ql,{value:"zh",children:"Chinese"}),r.jsx(u.Ql,{value:"hi",children:"Hindi"})]})]})]})]}),(0,r.jsxs)("div",{className:"space-y-4 p-4 border rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx(d._,{htmlFor:"maintenanceMode",className:"text-base font-medium",children:"Maintenance Mode"}),r.jsx("p",{className:"text-sm text-gray-500",children:"Enable to put the application in maintenance mode"})]}),r.jsx(h.r,{id:"maintenanceMode",checked:a.maintenanceMode,onCheckedChange:e=>I("maintenanceMode",e)})]}),a.maintenanceMode&&(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"maintenanceMessage",children:"Maintenance Message"}),r.jsx(m.g,{id:"maintenanceMessage",value:a.maintenanceMessage,onChange:e=>I("maintenanceMessage",e.target.value),placeholder:"System is under maintenance...",rows:2})]})]})]})]})}),r.jsx(p.nU,{value:"features",children:(0,r.jsxs)(t.Zb,{children:[(0,r.jsxs)(t.Ol,{children:[(0,r.jsxs)(t.ll,{className:"flex items-center",children:[r.jsx(y.Z,{className:"h-5 w-5 mr-2"}),"Feature Flags"]}),r.jsx(t.SZ,{children:"Enable or disable application features"})]}),r.jsx(t.aY,{className:"space-y-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,r.jsxs)("div",{children:[r.jsx(d._,{htmlFor:"enableRegistration",className:"text-base font-medium",children:"User Registration"}),r.jsx("p",{className:"text-sm text-gray-500",children:"Allow new users to register accounts"})]}),r.jsx(h.r,{id:"enableRegistration",checked:a.enableRegistration,onCheckedChange:e=>I("enableRegistration",e)})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,r.jsxs)("div",{children:[r.jsx(d._,{htmlFor:"enableTrials",className:"text-base font-medium",children:"Free Trials"}),r.jsx("p",{className:"text-sm text-gray-500",children:"Enable free trial periods for new users"})]}),r.jsx(h.r,{id:"enableTrials",checked:a.enableTrials,onCheckedChange:e=>I("enableTrials",e)})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,r.jsxs)("div",{children:[r.jsx(d._,{htmlFor:"enableMultiTenant",className:"text-base font-medium",children:"Multi-Tenant"}),r.jsx("p",{className:"text-sm text-gray-500",children:"Allow users to manage multiple companies"})]}),r.jsx(h.r,{id:"enableMultiTenant",checked:a.enableMultiTenant,onCheckedChange:e=>I("enableMultiTenant",e)})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,r.jsxs)("div",{children:[r.jsx(d._,{htmlFor:"enableApiAccess",className:"text-base font-medium",children:"API Access"}),r.jsx("p",{className:"text-sm text-gray-500",children:"Enable REST API access for integrations"})]}),r.jsx(h.r,{id:"enableApiAccess",checked:a.enableApiAccess,onCheckedChange:e=>I("enableApiAccess",e)})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,r.jsxs)("div",{children:[r.jsx(d._,{htmlFor:"enableWebhooks",className:"text-base font-medium",children:"Webhooks"}),r.jsx("p",{className:"text-sm text-gray-500",children:"Enable webhook notifications for events"})]}),r.jsx(h.r,{id:"enableWebhooks",checked:a.enableWebhooks,onCheckedChange:e=>I("enableWebhooks",e)})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,r.jsxs)("div",{children:[r.jsx(d._,{htmlFor:"allowSocialLogin",className:"text-base font-medium",children:"Social Login"}),r.jsx("p",{className:"text-sm text-gray-500",children:"Allow login with Google, GitHub, etc."})]}),r.jsx(h.r,{id:"allowSocialLogin",checked:a.allowSocialLogin,onCheckedChange:e=>I("allowSocialLogin",e)})]})]})]})})]})}),r.jsx(p.nU,{value:"security",children:(0,r.jsxs)(t.Zb,{children:[(0,r.jsxs)(t.Ol,{children:[(0,r.jsxs)(t.ll,{className:"flex items-center",children:[r.jsx(b.Z,{className:"h-5 w-5 mr-2"}),"Security Settings"]}),r.jsx(t.SZ,{children:"Configure security policies and authentication settings"})]}),(0,r.jsxs)(t.aY,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"sessionTimeout",children:"Session Timeout (hours)"}),r.jsx(c.I,{id:"sessionTimeout",type:"number",value:a.sessionTimeout,onChange:e=>I("sessionTimeout",parseInt(e.target.value)||24),min:"1",max:"168"}),r.jsx("p",{className:"text-xs text-gray-500",children:"Maximum session duration before auto-logout"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"passwordMinLength",children:"Minimum Password Length"}),r.jsx(c.I,{id:"passwordMinLength",type:"number",value:a.passwordMinLength,onChange:e=>I("passwordMinLength",parseInt(e.target.value)||8),min:"6",max:"32"}),r.jsx("p",{className:"text-xs text-gray-500",children:"Minimum characters required for passwords"})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,r.jsxs)("div",{children:[r.jsx(d._,{htmlFor:"requireTwoFactor",className:"text-base font-medium",children:"Require Two-Factor Authentication"}),r.jsx("p",{className:"text-sm text-gray-500",children:"Force all users to enable 2FA for enhanced security"})]}),r.jsx(h.r,{id:"requireTwoFactor",checked:a.requireTwoFactor,onCheckedChange:e=>I("requireTwoFactor",e)})]})]})]})}),r.jsx(p.nU,{value:"email",children:(0,r.jsxs)(t.Zb,{children:[(0,r.jsxs)(t.Ol,{children:[(0,r.jsxs)(t.ll,{className:"flex items-center",children:[r.jsx(N.Z,{className:"h-5 w-5 mr-2"}),"Email Configuration"]}),r.jsx(t.SZ,{children:"Configure SMTP settings for transactional emails"})]}),(0,r.jsxs)(t.aY,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"emailProvider",children:"Email Provider"}),(0,r.jsxs)(u.Ph,{value:a.emailProvider,onValueChange:e=>I("emailProvider",e),children:[r.jsx(u.i4,{children:r.jsx(u.ki,{})}),(0,r.jsxs)(u.Bw,{children:[r.jsx(u.Ql,{value:"smtp",children:"Custom SMTP"}),r.jsx(u.Ql,{value:"sendgrid",children:"SendGrid"}),r.jsx(u.Ql,{value:"mailgun",children:"Mailgun"}),r.jsx(u.Ql,{value:"ses",children:"Amazon SES"}),r.jsx(u.Ql,{value:"postmark",children:"Postmark"})]})]})]}),"smtp"===a.emailProvider&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"smtpHost",children:"SMTP Host"}),r.jsx(c.I,{id:"smtpHost",value:a.smtpHost,onChange:e=>I("smtpHost",e.target.value),placeholder:"smtp.gmail.com"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"smtpPort",children:"SMTP Port"}),r.jsx(c.I,{id:"smtpPort",type:"number",value:a.smtpPort,onChange:e=>I("smtpPort",parseInt(e.target.value)||587),placeholder:"587"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"smtpUsername",children:"SMTP Username"}),r.jsx(c.I,{id:"smtpUsername",value:a.smtpUsername,onChange:e=>I("smtpUsername",e.target.value),placeholder:"<EMAIL>"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"smtpPassword",children:"SMTP Password"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx(c.I,{id:"smtpPassword",type:A?"text":"password",value:a.smtpPassword,onChange:e=>I("smtpPassword",e.target.value),placeholder:"your-app-password"}),r.jsx(o.z,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>T(!A),children:A?r.jsx(C.Z,{className:"h-4 w-4"}):r.jsx(w.Z,{className:"h-4 w-4"})})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,r.jsxs)("div",{children:[r.jsx(d._,{htmlFor:"smtpSecure",className:"text-base font-medium",children:"Use TLS/SSL"}),r.jsx("p",{className:"text-sm text-gray-500",children:"Enable secure connection for SMTP"})]}),r.jsx(h.r,{id:"smtpSecure",checked:a.smtpSecure,onCheckedChange:e=>I("smtpSecure",e)})]})]})]})]})}),r.jsx(p.nU,{value:"limits",children:(0,r.jsxs)(t.Zb,{children:[(0,r.jsxs)(t.Ol,{children:[(0,r.jsxs)(t.ll,{className:"flex items-center",children:[r.jsx(k.Z,{className:"h-5 w-5 mr-2"}),"System Limits"]}),r.jsx(t.SZ,{children:"Configure default limits and quotas"})]}),r.jsx(t.aY,{className:"space-y-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"maxUsersPerCompany",children:"Max Users per Company"}),r.jsx(c.I,{id:"maxUsersPerCompany",type:"number",value:a.maxUsersPerCompany,onChange:e=>I("maxUsersPerCompany",parseInt(e.target.value)||100),min:"1"}),r.jsx("p",{className:"text-xs text-gray-500",children:"Default limit for new companies"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"maxCompaniesPerUser",children:"Max Companies per User"}),r.jsx(c.I,{id:"maxCompaniesPerUser",type:"number",value:a.maxCompaniesPerUser,onChange:e=>I("maxCompaniesPerUser",parseInt(e.target.value)||5),min:"1"}),r.jsx("p",{className:"text-xs text-gray-500",children:"How many companies a user can join"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(d._,{htmlFor:"defaultStorageLimit",children:"Default Storage Limit (GB)"}),r.jsx(c.I,{id:"defaultStorageLimit",type:"number",value:a.defaultStorageLimit,onChange:e=>I("defaultStorageLimit",parseInt(e.target.value)||5),min:"1"}),r.jsx("p",{className:"text-xs text-gray-500",children:"Default storage quota for new companies"})]})]})})]})})]})]})}},92549:(e,s,a)=>{"use strict";a.d(s,{I:()=>n});var r=a(95344),l=a(3729),i=a(91626);let n=l.forwardRef(({className:e,type:s,...a},l)=>r.jsx("input",{type:s,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:l,...a}));n.displayName="Input"},1586:(e,s,a)=>{"use strict";a.d(s,{_:()=>c});var r=a(95344),l=a(3729),i=a(14217),n=a(49247),t=a(91626);let o=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=l.forwardRef(({className:e,...s},a)=>r.jsx(i.f,{ref:a,className:(0,t.cn)(o(),e),...s}));c.displayName=i.f.displayName},17470:(e,s,a)=>{"use strict";a.d(s,{Bw:()=>x,Ph:()=>d,Ql:()=>g,i4:()=>h,ki:()=>m});var r=a(95344),l=a(3729),i=a(1146),n=a(25390),t=a(12704),o=a(62312),c=a(91626);let d=i.fC;i.ZA;let m=i.B4,h=l.forwardRef(({className:e,children:s,...a},l)=>(0,r.jsxs)(i.xz,{ref:l,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...a,children:[s,r.jsx(i.JO,{asChild:!0,children:r.jsx(n.Z,{className:"h-4 w-4 opacity-50"})})]}));h.displayName=i.xz.displayName;let u=l.forwardRef(({className:e,...s},a)=>r.jsx(i.u_,{ref:a,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:r.jsx(t.Z,{className:"h-4 w-4"})}));u.displayName=i.u_.displayName;let p=l.forwardRef(({className:e,...s},a)=>r.jsx(i.$G,{ref:a,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:r.jsx(n.Z,{className:"h-4 w-4"})}));p.displayName=i.$G.displayName;let x=l.forwardRef(({className:e,children:s,position:a="popper",...l},n)=>r.jsx(i.h_,{children:(0,r.jsxs)(i.VY,{ref:n,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...l,children:[r.jsx(u,{}),r.jsx(i.l_,{className:(0,c.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),r.jsx(p,{})]})}));x.displayName=i.VY.displayName,l.forwardRef(({className:e,...s},a)=>r.jsx(i.__,{ref:a,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...s})).displayName=i.__.displayName;let g=l.forwardRef(({className:e,children:s,...a},l)=>(0,r.jsxs)(i.ck,{ref:l,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...a,children:[r.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:r.jsx(i.wU,{children:r.jsx(o.Z,{className:"h-4 w-4"})})}),r.jsx(i.eT,{children:s})]}));g.displayName=i.ck.displayName,l.forwardRef(({className:e,...s},a)=>r.jsx(i.Z0,{ref:a,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",e),...s})).displayName=i.Z0.displayName},71809:(e,s,a)=>{"use strict";a.d(s,{r:()=>C});var r=a(95344),l=a(3729),i=a(85222),n=a(31405),t=a(98462),o=a(33183),c=a(92062),d=a(63085),m=a(62409),h="Switch",[u,p]=(0,t.b)(h),[x,g]=u(h),j=l.forwardRef((e,s)=>{let{__scopeSwitch:a,name:t,checked:c,defaultChecked:d,required:u,disabled:p,value:g="on",onCheckedChange:j,form:f,...v}=e,[N,C]=l.useState(null),w=(0,n.e)(s,e=>C(e)),k=l.useRef(!1),P=!N||f||!!N.closest("form"),[S,M]=(0,o.T)({prop:c,defaultProp:d??!1,onChange:j,caller:h});return(0,r.jsxs)(x,{scope:a,checked:S,disabled:p,children:[(0,r.jsx)(m.WV.button,{type:"button",role:"switch","aria-checked":S,"aria-required":u,"data-state":b(S),"data-disabled":p?"":void 0,disabled:p,value:g,...v,ref:w,onClick:(0,i.M)(e.onClick,e=>{M(e=>!e),P&&(k.current=e.isPropagationStopped(),k.current||e.stopPropagation())})}),P&&(0,r.jsx)(y,{control:N,bubbles:!k.current,name:t,value:g,checked:S,required:u,disabled:p,form:f,style:{transform:"translateX(-100%)"}})]})});j.displayName=h;var f="SwitchThumb",v=l.forwardRef((e,s)=>{let{__scopeSwitch:a,...l}=e,i=g(f,a);return(0,r.jsx)(m.WV.span,{"data-state":b(i.checked),"data-disabled":i.disabled?"":void 0,...l,ref:s})});v.displayName=f;var y=l.forwardRef(({__scopeSwitch:e,control:s,checked:a,bubbles:i=!0,...t},o)=>{let m=l.useRef(null),h=(0,n.e)(m,o),u=(0,c.D)(a),p=(0,d.t)(s);return l.useEffect(()=>{let e=m.current;if(!e)return;let s=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(u!==a&&s){let r=new Event("click",{bubbles:i});s.call(e,a),e.dispatchEvent(r)}},[u,a,i]),(0,r.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:a,...t,tabIndex:-1,ref:h,style:{...t.style,...p,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function b(e){return e?"checked":"unchecked"}y.displayName="SwitchBubbleInput";var N=a(91626);let C=l.forwardRef(({className:e,...s},a)=>r.jsx(j,{className:(0,N.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...s,ref:a,children:r.jsx(v,{className:(0,N.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));C.displayName=j.displayName},25757:(e,s,a)=>{"use strict";a.d(s,{mQ:()=>F,nU:()=>A,dr:()=>_,SP:()=>U});var r=a(95344),l=a(3729),i=a(85222),n=a(98462),t=a(34504),o=a(43234),c=a(62409),d=a(3975),m=a(33183),h=a(99048),u="Tabs",[p,x]=(0,n.b)(u,[t.Pc]),g=(0,t.Pc)(),[j,f]=p(u),v=l.forwardRef((e,s)=>{let{__scopeTabs:a,value:l,onValueChange:i,defaultValue:n,orientation:t="horizontal",dir:o,activationMode:p="automatic",...x}=e,g=(0,d.gm)(o),[f,v]=(0,m.T)({prop:l,onChange:i,defaultProp:n??"",caller:u});return(0,r.jsx)(j,{scope:a,baseId:(0,h.M)(),value:f,onValueChange:v,orientation:t,dir:g,activationMode:p,children:(0,r.jsx)(c.WV.div,{dir:g,"data-orientation":t,...x,ref:s})})});v.displayName=u;var y="TabsList",b=l.forwardRef((e,s)=>{let{__scopeTabs:a,loop:l=!0,...i}=e,n=f(y,a),o=g(a);return(0,r.jsx)(t.fC,{asChild:!0,...o,orientation:n.orientation,dir:n.dir,loop:l,children:(0,r.jsx)(c.WV.div,{role:"tablist","aria-orientation":n.orientation,...i,ref:s})})});b.displayName=y;var N="TabsTrigger",C=l.forwardRef((e,s)=>{let{__scopeTabs:a,value:l,disabled:n=!1,...o}=e,d=f(N,a),m=g(a),h=P(d.baseId,l),u=S(d.baseId,l),p=l===d.value;return(0,r.jsx)(t.ck,{asChild:!0,...m,focusable:!n,active:p,children:(0,r.jsx)(c.WV.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":u,"data-state":p?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:h,...o,ref:s,onMouseDown:(0,i.M)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(l)}),onKeyDown:(0,i.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(l)}),onFocus:(0,i.M)(e.onFocus,()=>{let e="manual"!==d.activationMode;p||n||!e||d.onValueChange(l)})})})});C.displayName=N;var w="TabsContent",k=l.forwardRef((e,s)=>{let{__scopeTabs:a,value:i,forceMount:n,children:t,...d}=e,m=f(w,a),h=P(m.baseId,i),u=S(m.baseId,i),p=i===m.value,x=l.useRef(p);return l.useEffect(()=>{let e=requestAnimationFrame(()=>x.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,r.jsx)(o.z,{present:n||p,children:({present:a})=>(0,r.jsx)(c.WV.div,{"data-state":p?"active":"inactive","data-orientation":m.orientation,role:"tabpanel","aria-labelledby":h,hidden:!a,id:u,tabIndex:0,...d,ref:s,style:{...e.style,animationDuration:x.current?"0s":void 0},children:a&&t})})});function P(e,s){return`${e}-trigger-${s}`}function S(e,s){return`${e}-content-${s}`}k.displayName=w;var M=a(91626);let F=v,_=l.forwardRef(({className:e,...s},a)=>r.jsx(b,{ref:a,className:(0,M.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s}));_.displayName=b.displayName;let U=l.forwardRef(({className:e,...s},a)=>r.jsx(C,{ref:a,className:(0,M.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s}));U.displayName=C.displayName;let A=l.forwardRef(({className:e,...s},a)=>r.jsx(k,{ref:a,className:(0,M.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));A.displayName=k.displayName},93601:(e,s,a)=>{"use strict";a.d(s,{g:()=>n});var r=a(95344),l=a(3729),i=a(91626);let n=l.forwardRef(({className:e,...s},a)=>r.jsx("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...s}));n.displayName="Textarea"},78638:(e,s,a)=>{"use strict";a.d(s,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(69224).Z)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},1222:(e,s,a)=>{"use strict";a.d(s,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(69224).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},53148:(e,s,a)=>{"use strict";a.d(s,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(69224).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},31498:(e,s,a)=>{"use strict";a.d(s,{Z:()=>r});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(69224).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},45128:(e,s,a)=>{"use strict";a.r(s),a.d(s,{$$typeof:()=>i,__esModule:()=>l,default:()=>n});let r=(0,a(86843).createProxy)(String.raw`C:\proj\nextjs-saas\app\super-admin\global-config\page.tsx`),{__esModule:l,$$typeof:i}=r,n=r.default},14217:(e,s,a)=>{"use strict";a.d(s,{f:()=>t});var r=a(3729),l=a(62409),i=a(95344),n=r.forwardRef((e,s)=>(0,i.jsx)(l.WV.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));n.displayName="Label";var t=n}};var s=require("../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),r=s.X(0,[1638,7948,6671,4626,7792,2506,8830,1729,2125,3965],()=>a(85637));module.exports=r})();