"use strict";(()=>{var e={};e.id=7836,e.ids=[7836],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},38600:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>w,originalPathname:()=>v,patchFetch:()=>x,requestAsyncStorage:()=>y,routeModule:()=>m,serverHooks:()=>h,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>I});var s={};r.r(s),r.d(s,{GET:()=>d,POST:()=>p});var o=r(95419),n=r(69108),a=r(99678),i=r(78070),c=r(81355),u=r(3205),l=r(9108);async function d(e){try{let t=await (0,c.getServerSession)(u.L);if(!t?.user?.id||!t?.user?.companyId)return i.Z.json({error:"Unauthorized"},{status:401});let{searchParams:r}=new URL(e.url),s=r.get("format")||"csv",o=r.get("status"),n=r.get("industry"),a="true"===r.get("includeStats"),d={companyId:t.user.companyId};o&&(d.status=o),n&&(d.industry=n);let p=(await l._.customer.findMany({where:d,include:{createdBy:{select:{name:!0,email:!0}},...a&&{_count:{select:{leads:!0,quotations:!0,invoices:!0,activities:!0,contracts:!0}},invoices:{where:{status:"PAID"},select:{total:!0}}}},orderBy:{createdAt:"desc"}})).map(e=>{let t={id:e.id,name:e.name,email:e.email||"",phone:e.phone||"",company:e.company||"",address:e.address||"",city:e.city||"",state:e.state||"",country:e.country||"",postalCode:e.postalCode||"",industry:e.industry||"",website:e.website||"",notes:e.notes||"",status:e.status,tags:Array.isArray(e.tags)?e.tags.join(";"):"",createdAt:e.createdAt.toISOString(),updatedAt:e.updatedAt.toISOString(),createdBy:e.createdBy.name||e.createdBy.email||""};if(a&&"_count"in e){let r=e.invoices?.reduce((e,t)=>e+t.total,0)||0;return{...t,totalLeads:e._count.leads,totalQuotations:e._count.quotations,totalInvoices:e._count.invoices,totalContracts:e._count.contracts,totalActivities:e._count.activities,totalRevenue:r}}return t});if("json"===s)return i.Z.json({customers:p,exportedAt:new Date().toISOString(),totalCount:p.length,filters:{status:o,industry:n,includeStats:a}});if(0===p.length)return new Response("No customers found for export",{status:404,headers:{"Content-Type":"text/plain"}});let m=Object.keys(p[0]),y=m.join(","),g=p.map(e=>m.map(t=>{let r=e[t];return"string"==typeof r&&(r.includes(",")||r.includes('"')||r.includes("\n"))?`"${r.replace(/"/g,'""')}"`:r}).join(",")),h=[y,...g].join("\n"),w=new Date().toISOString().split("T")[0],I=`customers-export-${w}.csv`;return new Response(h,{headers:{"Content-Type":"text/csv","Content-Disposition":`attachment; filename="${I}"`}})}catch(e){return console.error("Error exporting customers:",e),i.Z.json({error:"Failed to export customers"},{status:500})}}async function p(e){try{let t=await (0,c.getServerSession)(u.L);if(!t?.user?.id||!t?.user?.companyId)return i.Z.json({error:"Unauthorized"},{status:401});let{customerIds:r,format:s="csv",includeStats:o=!1}=await e.json();if(!Array.isArray(r)||0===r.length)return i.Z.json({error:"Customer IDs are required"},{status:400});let n=await l._.customer.findMany({where:{id:{in:r},companyId:t.user.companyId},include:{createdBy:{select:{name:!0,email:!0}},...o&&{_count:{select:{leads:!0,quotations:!0,invoices:!0,activities:!0,contracts:!0}},invoices:{where:{status:"PAID"},select:{total:!0}}}},orderBy:{name:"asc"}});if(0===n.length)return i.Z.json({error:"No customers found with provided IDs"},{status:404});let a=n.map(e=>{let t={id:e.id,name:e.name,email:e.email||"",phone:e.phone||"",company:e.company||"",address:e.address||"",city:e.city||"",state:e.state||"",country:e.country||"",postalCode:e.postalCode||"",industry:e.industry||"",website:e.website||"",notes:e.notes||"",status:e.status,tags:Array.isArray(e.tags)?e.tags.join(";"):"",createdAt:e.createdAt.toISOString(),updatedAt:e.updatedAt.toISOString(),createdBy:e.createdBy.name||e.createdBy.email||""};if(o&&"_count"in e){let r=e.invoices?.reduce((e,t)=>e+t.total,0)||0;return{...t,totalLeads:e._count.leads,totalQuotations:e._count.quotations,totalInvoices:e._count.invoices,totalContracts:e._count.contracts,totalActivities:e._count.activities,totalRevenue:r}}return t});if("json"===s)return i.Z.json({customers:a,exportedAt:new Date().toISOString(),totalCount:a.length,selectedIds:r});let d=Object.keys(a[0]),p=d.join(","),m=a.map(e=>d.map(t=>{let r=e[t];return"string"==typeof r&&(r.includes(",")||r.includes('"')||r.includes("\n"))?`"${r.replace(/"/g,'""')}"`:r}).join(",")),y=[p,...m].join("\n"),g=new Date().toISOString().split("T")[0],h=`customers-selected-export-${g}.csv`;return new Response(y,{headers:{"Content-Type":"text/csv","Content-Disposition":`attachment; filename="${h}"`}})}catch(e){return console.error("Error exporting selected customers:",e),i.Z.json({error:"Failed to export selected customers"},{status:500})}}let m=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/customers/export/route",pathname:"/api/customers/export",filename:"route",bundlePath:"app/api/customers/export/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\customers\\export\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:y,staticGenerationAsyncStorage:g,serverHooks:h,headerHooks:w,staticGenerationBailout:I}=m,v="/api/customers/export/route";function x(){return(0,a.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:g})}},3205:(e,t,r)=>{r.d(t,{L:()=>u});var s=r(86485),o=r(10375),n=r(50694),a=r(6521),i=r.n(a),c=r(9108);let u={providers:[(0,s.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await c._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),r=t?.companyId;if(!r&&t){let e=await c._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(r=e?.id)&&await c._.user.update({where:{id:t.id},data:{companyId:r}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await i().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await c._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:r}}catch(e){return console.error("Authentication error:",e),null}}}),(0,o.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,n.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,r)=>{r.d(t,{_:()=>o});let s=require("@prisma/client"),o=globalThis.prisma??new s.PrismaClient}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1638,6206,6521,2455,4520],()=>r(38600));module.exports=s})();