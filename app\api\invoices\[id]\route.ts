import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Validation schema for invoice items
const invoiceItemSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  quantity: z.number().min(1, 'Quantity must be at least 1'),
  unitPrice: z.number().min(0, 'Unit price must be positive'),
  itemId: z.string().optional()
})

// Validation schema for invoice update
const invoiceUpdateSchema = z.object({
  title: z.string().optional(),
  customerId: z.string().min(1, 'Customer is required').optional(),
  quotationId: z.string().optional().nullable(),
  status: z.enum(['DRAFT', 'SENT', 'PAID', 'OVERDUE', 'CANCELLED']).optional(),
  dueDate: z.string().optional(),
  terms: z.string().optional().nullable(),
  notes: z.string().optional().nullable(),
  items: z.array(invoiceItemSchema).min(1, 'At least one item is required').optional(),
  taxRate: z.number().min(0).max(100).optional()
})

// GET /api/invoices/[id] - Get single invoice
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const invoice = await prisma.invoice.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId || undefined
      },
      include: {
        customer: {
          select: { 
            id: true, 
            name: true, 
            email: true, 
            company: true, 
            phone: true, 
            address: true, 
            city: true, 
            state: true, 
            country: true, 
            postalCode: true 
          }
        },
        quotation: {
          select: { id: true, quotationNumber: true, title: true }
        },
        createdBy: {
          select: { name: true, email: true }
        },
        items: true,
        transactions: {
          orderBy: { createdAt: 'desc' }
        }
      }
    })

    if (!invoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 })
    }

    // Calculate payment totals
    const totalPaid = invoice.transactions.reduce((sum, transaction) => {
      return transaction.type === 'PAYMENT' ? sum + Number(transaction.amount) : sum
    }, 0)

    const amountDue = Number(invoice.total) - totalPaid

    const invoiceWithTotals = {
      ...invoice,
      subtotal: Number(invoice.subtotal),
      total: Number(invoice.total),
      taxAmount: Number(invoice.taxAmount),
      totalPaid: Math.round(totalPaid * 100) / 100,
      amountDue: Math.round(amountDue * 100) / 100
    }

    return NextResponse.json(invoiceWithTotals)
  } catch (error) {
    console.error('Error fetching invoice:', error)
    return NextResponse.json(
      { error: 'Failed to fetch invoice' },
      { status: 500 }
    )
  }
}

// PUT /api/invoices/[id] - Update invoice
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = invoiceUpdateSchema.parse(body)

    // Check if invoice exists and belongs to user's company
    const existingInvoice = await prisma.invoice.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId || undefined
      },
      include: {
        items: true
      }
    })

    if (!existingInvoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 })
    }

    // Check if invoice can be edited (not paid or cancelled)
    if (existingInvoice.status === 'PAID' || existingInvoice.status === 'CANCELLED') {
      return NextResponse.json(
        { error: 'Cannot edit paid or cancelled invoices' },
        { status: 400 }
      )
    }

    // Prepare update data
    const updateData: any = { ...validatedData }

    if (validatedData.dueDate) {
      updateData.dueDate = new Date(validatedData.dueDate)
    }

    // Recalculate totals if items are updated
    if (validatedData.items) {
      const subtotal = validatedData.items.reduce((sum, item) => {
        return sum + (item.quantity * item.unitPrice)
      }, 0)

      const taxAmount = (subtotal * (validatedData.taxRate || 0)) / 100
      const total = subtotal + taxAmount

      updateData.subtotal = subtotal
      updateData.taxAmount = taxAmount
      updateData.total = total
    }

    // Update invoice with items in a transaction
    const invoice = await prisma.$transaction(async (tx) => {
      // Update invoice
      const updatedInvoice = await tx.invoice.update({
        where: { id: params.id },
        data: updateData,
        include: {
          customer: {
            select: { id: true, name: true, email: true, company: true }
          },
          quotation: {
            select: { id: true, quotationNumber: true, title: true }
          },
          createdBy: {
            select: { name: true, email: true }
          },
          items: {
            orderBy: { createdAt: 'asc' }
          }
        }
      })

      // Update items if provided
      if (validatedData.items) {
        // Delete existing items
        await tx.invoiceItem.deleteMany({
          where: { invoiceId: params.id }
        })

        // Create new items
        await tx.invoiceItem.createMany({
          data: validatedData.items.map(item => ({
            name: item.name,
            description: item.description,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            total: item.quantity * item.unitPrice,
            itemId: item.itemId,
            invoiceId: params.id
          }))
        })

        // Fetch updated invoice with new items
        const finalInvoice = await tx.invoice.findUnique({
          where: { id: params.id },
          include: {
            customer: {
              select: { id: true, name: true, email: true, company: true }
            },
            quotation: {
              select: { id: true, quotationNumber: true, title: true }
            },
            createdBy: {
              select: { name: true, email: true }
            },
            items: true
          }
        })

        return finalInvoice
      }

      return updatedInvoice
    })

    return NextResponse.json(invoice)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating invoice:', error)
    return NextResponse.json(
      { error: 'Failed to update invoice' },
      { status: 500 }
    )
  }
}

// DELETE /api/invoices/[id] - Delete invoice
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if invoice exists and belongs to user's company
    const invoice = await prisma.invoice.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId || undefined
      },
      include: {
        transactions: true
      }
    })

    if (!invoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 })
    }

    // Check if invoice has payments
    const hasPayments = invoice.transactions.some(t => t.type === 'PAYMENT')
    if (hasPayments) {
      return NextResponse.json(
        {
          error: 'Cannot delete invoice with existing payments'
        },
        { status: 400 }
      )
    }

    // Check if invoice is paid
    if (invoice.status === 'PAID') {
      return NextResponse.json(
        { error: 'Cannot delete paid invoices' },
        { status: 400 }
      )
    }

    await prisma.invoice.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ message: 'Invoice deleted successfully' })
  } catch (error) {
    console.error('Error deleting invoice:', error)
    return NextResponse.json(
      { error: 'Failed to delete invoice' },
      { status: 500 }
    )
  }
}
