'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Check, Star, Zap, Shield, Users, Database, BarChart3, Headphones } from 'lucide-react'
import { toast } from 'sonner'

interface PricingPlan {
  id: string
  name: string
  description: string
  monthlyPrice: number
  yearlyPrice: number | null
  currency: string
  maxUsers: number
  maxCompanies: number
  maxCustomers: number
  maxQuotations: number
  maxInvoices: number
  maxContracts: number
  maxStorage: number
  formattedStorage: string
  features: Record<string, boolean>
  isActive: boolean
  isPublic: boolean
  trialDays: number
  yearlyDiscount: number
  isPopular?: boolean
}

export default function PricingPage() {
  const { data: session } = useSession()
  const router = useRouter()
  const [plans, setPlans] = useState<PricingPlan[]>([])
  const [loading, setLoading] = useState(true)
  const [isYearly, setIsYearly] = useState(false)

  useEffect(() => {
    fetchPlans()
  }, [])

  const fetchPlans = async () => {
    try {
      const response = await fetch('/api/pricing-plans?publicOnly=true')
      const data = await response.json()
      
      if (data.success) {
        setPlans(data.data)
      } else {
        toast.error('Failed to load pricing plans')
      }
    } catch (error) {
      console.error('Error fetching plans:', error)
      toast.error('Failed to load pricing plans')
    } finally {
      setLoading(false)
    }
  }

  const handleSelectPlan = (plan: PricingPlan) => {
    if (!session) {
      router.push('/auth/signin?callbackUrl=/pricing')
      return
    }

    if (plan.monthlyPrice === 0) {
      // Free plan - redirect to dashboard
      router.push('/dashboard')
    } else {
      // Paid plan - redirect to subscription flow
      router.push(`/subscription/checkout?plan=${plan.id}&billing=${isYearly ? 'yearly' : 'monthly'}`)
    }
  }

  const getFeatureIcon = (feature: string) => {
    switch (feature) {
      case 'basicReporting':
      case 'advancedAnalytics':
        return <BarChart3 className="h-4 w-4" />
      case 'prioritySupport':
      case 'emailSupport':
        return <Headphones className="h-4 w-4" />
      case 'advancedSecurity':
        return <Shield className="h-4 w-4" />
      case 'apiAccess':
        return <Zap className="h-4 w-4" />
      default:
        return <Check className="h-4 w-4" />
    }
  }

  const getFeatureLabel = (feature: string) => {
    const labels: Record<string, string> = {
      basicReporting: 'Basic Reporting',
      emailSupport: 'Email Support',
      mobileApp: 'Mobile App Access',
      advancedAnalytics: 'Advanced Analytics',
      customBranding: 'Custom Branding',
      apiAccess: 'API Access',
      prioritySupport: 'Priority Support',
      customIntegrations: 'Custom Integrations',
      advancedSecurity: 'Advanced Security',
      dedicatedManager: 'Dedicated Account Manager'
    }
    return labels[feature] || feature
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading pricing plans...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-16">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Choose Your Perfect Plan
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Start with our free plan and upgrade as your business grows. 
            All plans include our core features with varying limits.
          </p>
          
          {/* Billing Toggle */}
          <div className="flex items-center justify-center space-x-4 mb-8">
            <span className={`text-sm font-medium ${!isYearly ? 'text-blue-600' : 'text-gray-500'}`}>
              Monthly
            </span>
            <Switch
              checked={isYearly}
              onCheckedChange={setIsYearly}
              className="data-[state=checked]:bg-blue-600"
            />
            <span className={`text-sm font-medium ${isYearly ? 'text-blue-600' : 'text-gray-500'}`}>
              Yearly
            </span>
            {plans.some(p => p.yearlyDiscount > 0) && (
              <Badge variant="secondary" className="ml-2">
                Save up to {Math.max(...plans.map(p => p.yearlyDiscount))}%
              </Badge>
            )}
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {plans.map((plan) => {
            const price = isYearly && plan.yearlyPrice !== null ? plan.yearlyPrice / 12 : plan.monthlyPrice
            const originalPrice = plan.monthlyPrice
            const hasDiscount = isYearly && plan.yearlyPrice !== null && plan.yearlyDiscount > 0

            return (
              <Card 
                key={plan.id} 
                className={`relative ${plan.isPopular ? 'ring-2 ring-blue-500 shadow-lg scale-105' : 'shadow-md'} hover:shadow-xl transition-all duration-300`}
              >
                {plan.isPopular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-blue-600 text-white px-4 py-1">
                      <Star className="h-3 w-3 mr-1" />
                      Most Popular
                    </Badge>
                  </div>
                )}

                <CardHeader className="text-center pb-4">
                  <CardTitle className="text-2xl font-bold">{plan.name}</CardTitle>
                  <CardDescription className="text-gray-600">
                    {plan.description}
                  </CardDescription>
                  
                  <div className="mt-4">
                    <div className="flex items-baseline justify-center">
                      <span className="text-4xl font-bold text-gray-900">
                        ${price.toFixed(0)}
                      </span>
                      <span className="text-gray-500 ml-1">/month</span>
                    </div>
                    
                    {hasDiscount && (
                      <div className="flex items-center justify-center mt-2">
                        <span className="text-sm text-gray-500 line-through mr-2">
                          ${originalPrice}/month
                        </span>
                        <Badge variant="secondary" className="text-xs">
                          {plan.yearlyDiscount}% off
                        </Badge>
                      </div>
                    )}
                    
                    {isYearly && plan.yearlyPrice !== null && (
                      <p className="text-sm text-gray-500 mt-1">
                        Billed annually (${plan.yearlyPrice}/year)
                      </p>
                    )}
                  </div>
                </CardHeader>

                <CardContent className="space-y-4">
                  {/* Limits */}
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center justify-between">
                      <span className="flex items-center">
                        <Users className="h-4 w-4 mr-2 text-gray-400" />
                        Users
                      </span>
                      <span className="font-medium">{plan.maxUsers}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="flex items-center">
                        <Database className="h-4 w-4 mr-2 text-gray-400" />
                        Storage
                      </span>
                      <span className="font-medium">{plan.formattedStorage}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Customers</span>
                      <span className="font-medium">{plan.maxCustomers.toLocaleString()}</span>
                    </div>
                  </div>

                  <hr className="my-4" />

                  {/* Features */}
                  <div className="space-y-2">
                    {Object.entries(plan.features).map(([feature, enabled]) => (
                      enabled && (
                        <div key={feature} className="flex items-center text-sm">
                          <div className="text-green-500 mr-2">
                            {getFeatureIcon(feature)}
                          </div>
                          <span>{getFeatureLabel(feature)}</span>
                        </div>
                      )
                    ))}
                  </div>

                  {plan.trialDays > 0 && (
                    <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                      <p className="text-sm text-blue-700 font-medium">
                        {plan.trialDays}-day free trial
                      </p>
                    </div>
                  )}
                </CardContent>

                <CardFooter>
                  <Button 
                    className={`w-full ${plan.isPopular ? 'bg-blue-600 hover:bg-blue-700' : ''}`}
                    variant={plan.isPopular ? 'default' : 'outline'}
                    onClick={() => handleSelectPlan(plan)}
                  >
                    {plan.monthlyPrice === 0 ? 'Get Started Free' : 'Start Free Trial'}
                  </Button>
                </CardFooter>
              </Card>
            )
          })}
        </div>

        {/* FAQ or Additional Info */}
        <div className="text-center mt-16">
          <p className="text-gray-600">
            Need a custom plan? {' '}
            <a href="/contact" className="text-blue-600 hover:underline">
              Contact our sales team
            </a>
          </p>
        </div>
      </div>
    </div>
  )
}
