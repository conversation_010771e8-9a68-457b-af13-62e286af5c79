'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  UserCheck,
  TrendingUp,
  DollarSign,
  Calendar,
  CheckCircle,
  AlertCircle,
  Target,
  ArrowRight,
  FileText,
  Clock,
  User
} from 'lucide-react'
import { toast } from 'react-hot-toast'

interface ConversionReadiness {
  score: number
  maxScore: number
  percentage: number
  level: 'HIGH' | 'MEDIUM' | 'LOW'
  factors: Array<{
    factor: string
    points: number
    maxPoints: number
    status: 'GOOD' | 'FAIR' | 'POOR'
  }>
  recommendations: Array<{
    type: string
    priority: string
    message: string
    action: string
  }>
}

interface ConversionData {
  lead: any
  conversionReadiness: ConversionReadiness
  isConverted: boolean
  conversion?: any
  customer?: any
}

interface LeadConversionProps {
  leadId: string
  onConversionComplete?: () => void
}

export function LeadConversion({ leadId, onConversionComplete }: LeadConversionProps) {
  const [data, setData] = useState<ConversionData | null>(null)
  const [loading, setLoading] = useState(true)
  const [showConversionForm, setShowConversionForm] = useState(false)
  const [converting, setConverting] = useState(false)

  // Form state
  const [formData, setFormData] = useState({
    customerData: {
      name: '',
      email: '',
      phone: '',
      company: '',
      address: '',
      city: '',
      state: '',
      zipCode: '',
      country: '',
      website: '',
      industry: '',
      companySize: '',
      notes: ''
    },
    conversionData: {
      conversionType: 'DIRECT',
      conversionReason: '',
      conversionValue: '',
      conversionDate: new Date().toISOString().slice(0, 16),
      salesRepId: '',
      conversionNotes: '',
      followUpRequired: false,
      followUpDate: ''
    },
    createQuotation: false,
    quotationData: {
      title: '',
      description: '',
      validUntil: '',
      items: []
    }
  })

  const fetchConversionData = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/leads/${leadId}/convert`)
      if (!response.ok) {
        throw new Error('Failed to fetch conversion data')
      }

      const conversionData = await response.json()
      setData(conversionData)

      // Pre-populate form with lead data
      if (conversionData.lead) {
        setFormData(prev => ({
          ...prev,
          customerData: {
            ...prev.customerData,
            name: `${conversionData.lead.firstName} ${conversionData.lead.lastName}`.trim(),
            email: conversionData.lead.email || '',
            phone: conversionData.lead.phone || '',
            company: conversionData.lead.companyName || '',
            website: conversionData.lead.website || '',
            industry: conversionData.lead.industry || '',
            companySize: conversionData.lead.companySize || ''
          }
        }))
      }
    } catch (error) {
      toast.error('Failed to load conversion data')
      console.error('Error fetching conversion data:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (leadId) {
      fetchConversionData()
    }
  }, [leadId])

  const handleConvert = async () => {
    try {
      setConverting(true)

      const submitData = {
        customerData: formData.customerData,
        conversionData: {
          ...formData.conversionData,
          conversionValue: formData.conversionData.conversionValue 
            ? parseFloat(formData.conversionData.conversionValue) 
            : undefined
        },
        createQuotation: formData.createQuotation,
        quotationData: formData.createQuotation ? formData.quotationData : undefined
      }

      const response = await fetch(`/api/leads/${leadId}/convert`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to convert lead')
      }

      const result = await response.json()
      toast.success('Lead converted successfully!')
      setShowConversionForm(false)
      fetchConversionData()
      onConversionComplete?.()
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to convert lead')
    } finally {
      setConverting(false)
    }
  }

  const getReadinessColor = (level: string) => {
    switch (level) {
      case 'HIGH':
        return 'text-green-600 bg-green-100'
      case 'MEDIUM':
        return 'text-yellow-600 bg-yellow-100'
      case 'LOW':
        return 'text-red-600 bg-red-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getFactorStatusColor = (status: string) => {
    switch (status) {
      case 'GOOD':
        return 'text-green-600'
      case 'FAIR':
        return 'text-yellow-600'
      case 'POOR':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT':
        return 'text-red-600 bg-red-100'
      case 'HIGH':
        return 'text-orange-600 bg-orange-100'
      case 'MEDIUM':
        return 'text-yellow-600 bg-yellow-100'
      case 'LOW':
        return 'text-green-600 bg-green-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!data) {
    return (
      <div className="text-center py-8 text-gray-500">
        <AlertCircle className="h-12 w-12 mx-auto mb-4 text-gray-300" />
        <p>Failed to load conversion data</p>
      </div>
    )
  }

  if (data.isConverted) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-green-600">
            <CheckCircle className="h-5 w-5 mr-2" />
            Lead Successfully Converted
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium text-gray-500">Customer</Label>
                <p className="text-lg font-semibold">{data.customer?.name}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-500">Conversion Date</Label>
                <p className="text-lg font-semibold">
                  {data.conversion?.conversionDate 
                    ? new Date(data.conversion.conversionDate).toLocaleDateString()
                    : 'N/A'
                  }
                </p>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-500">Conversion Type</Label>
                <Badge className="mt-1">{data.conversion?.conversionType}</Badge>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-500">Conversion Value</Label>
                <p className="text-lg font-semibold">
                  {data.conversion?.conversionValue 
                    ? `$${data.conversion.conversionValue.toLocaleString()}`
                    : 'Not specified'
                  }
                </p>
              </div>
            </div>
            
            {data.conversion?.conversionNotes && (
              <div>
                <Label className="text-sm font-medium text-gray-500">Notes</Label>
                <p className="text-sm text-gray-700 mt-1">{data.conversion.conversionNotes}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Conversion Readiness */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <Target className="h-5 w-5 mr-2" />
              Conversion Readiness
            </div>
            <Badge className={`${getReadinessColor(data.conversionReadiness.level)} text-sm`}>
              {data.conversionReadiness.level} ({data.conversionReadiness.percentage}%)
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Progress Bar */}
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div
                className={`h-3 rounded-full ${
                  data.conversionReadiness.level === 'HIGH' 
                    ? 'bg-green-600' 
                    : data.conversionReadiness.level === 'MEDIUM' 
                    ? 'bg-yellow-600' 
                    : 'bg-red-600'
                }`}
                style={{ width: `${data.conversionReadiness.percentage}%` }}
              ></div>
            </div>

            {/* Readiness Factors */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {data.conversionReadiness.factors.map((factor, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="text-sm font-medium">{factor.factor}</p>
                    <p className={`text-xs ${getFactorStatusColor(factor.status)}`}>
                      {factor.status}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-bold">{factor.points}/{factor.maxPoints}</p>
                    <div className="w-16 bg-gray-200 rounded-full h-1 mt-1">
                      <div
                        className="bg-blue-600 h-1 rounded-full"
                        style={{ width: `${(factor.points / factor.maxPoints) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recommendations */}
      {data.conversionReadiness.recommendations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Conversion Recommendations</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {data.conversionReadiness.recommendations.map((rec, index) => (
                <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                  <Badge className={`${getPriorityColor(rec.priority)} text-xs`}>
                    {rec.priority}
                  </Badge>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">{rec.message}</p>
                    <p className="text-xs text-gray-500 mt-1">Action: {rec.action}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Conversion Action */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <UserCheck className="h-5 w-5 mr-2" />
            Convert Lead to Customer
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">
                Ready to convert this lead to a customer? This will create a new customer record 
                and update the lead status.
              </p>
              <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                <div className="flex items-center space-x-1">
                  <User className="h-3 w-3" />
                  <span>Customer record will be created</span>
                </div>
                <div className="flex items-center space-x-1">
                  <ArrowRight className="h-3 w-3" />
                  <span>Lead status will be updated</span>
                </div>
                <div className="flex items-center space-x-1">
                  <FileText className="h-3 w-3" />
                  <span>Optional quotation can be generated</span>
                </div>
              </div>
            </div>
            <Button 
              onClick={() => setShowConversionForm(true)}
              className="flex items-center space-x-2"
              disabled={data.conversionReadiness.level === 'LOW'}
            >
              <UserCheck className="h-4 w-4" />
              <span>Convert Lead</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Conversion Form Dialog */}
      <Dialog open={showConversionForm} onOpenChange={setShowConversionForm}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Convert Lead to Customer</DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            {/* Customer Information */}
            <div>
              <h3 className="text-lg font-medium mb-4">Customer Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Full Name *</Label>
                  <Input
                    id="name"
                    value={formData.customerData.name}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      customerData: { ...prev.customerData, name: e.target.value }
                    }))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="email">Email *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.customerData.email}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      customerData: { ...prev.customerData, email: e.target.value }
                    }))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="phone">Phone</Label>
                  <Input
                    id="phone"
                    value={formData.customerData.phone}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      customerData: { ...prev.customerData, phone: e.target.value }
                    }))}
                  />
                </div>
                <div>
                  <Label htmlFor="company">Company</Label>
                  <Input
                    id="company"
                    value={formData.customerData.company}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      customerData: { ...prev.customerData, company: e.target.value }
                    }))}
                  />
                </div>
              </div>
            </div>

            {/* Conversion Details */}
            <div>
              <h3 className="text-lg font-medium mb-4">Conversion Details</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="conversionType">Conversion Type</Label>
                  <Select 
                    value={formData.conversionData.conversionType}
                    onValueChange={(value) => setFormData(prev => ({
                      ...prev,
                      conversionData: { ...prev.conversionData, conversionType: value }
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="DIRECT">Direct Sale</SelectItem>
                      <SelectItem value="QUOTATION">Through Quotation</SelectItem>
                      <SelectItem value="PROPOSAL">Through Proposal</SelectItem>
                      <SelectItem value="TRIAL">After Trial</SelectItem>
                      <SelectItem value="DEMO">After Demo</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="conversionValue">Conversion Value ($)</Label>
                  <Input
                    id="conversionValue"
                    type="number"
                    step="0.01"
                    value={formData.conversionData.conversionValue}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      conversionData: { ...prev.conversionData, conversionValue: e.target.value }
                    }))}
                  />
                </div>
                <div>
                  <Label htmlFor="conversionDate">Conversion Date</Label>
                  <Input
                    id="conversionDate"
                    type="datetime-local"
                    value={formData.conversionData.conversionDate}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      conversionData: { ...prev.conversionData, conversionDate: e.target.value }
                    }))}
                  />
                </div>
              </div>
              
              <div className="mt-4">
                <Label htmlFor="conversionNotes">Conversion Notes</Label>
                <Textarea
                  id="conversionNotes"
                  value={formData.conversionData.conversionNotes}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    conversionData: { ...prev.conversionData, conversionNotes: e.target.value }
                  }))}
                  rows={3}
                />
              </div>
            </div>

            {/* Follow-up */}
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <Checkbox
                  id="followUpRequired"
                  checked={formData.conversionData.followUpRequired}
                  onCheckedChange={(checked) => setFormData(prev => ({
                    ...prev,
                    conversionData: { ...prev.conversionData, followUpRequired: !!checked }
                  }))}
                />
                <Label htmlFor="followUpRequired">Schedule follow-up</Label>
              </div>
              
              {formData.conversionData.followUpRequired && (
                <div>
                  <Label htmlFor="followUpDate">Follow-up Date</Label>
                  <Input
                    id="followUpDate"
                    type="datetime-local"
                    value={formData.conversionData.followUpDate}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      conversionData: { ...prev.conversionData, followUpDate: e.target.value }
                    }))}
                  />
                </div>
              )}
            </div>

            {/* Create Quotation */}
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <Checkbox
                  id="createQuotation"
                  checked={formData.createQuotation}
                  onCheckedChange={(checked) => setFormData(prev => ({
                    ...prev,
                    createQuotation: !!checked
                  }))}
                />
                <Label htmlFor="createQuotation">Create quotation for this customer</Label>
              </div>
              
              {formData.createQuotation && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="quotationTitle">Quotation Title</Label>
                    <Input
                      id="quotationTitle"
                      value={formData.quotationData.title}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        quotationData: { ...prev.quotationData, title: e.target.value }
                      }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="validUntil">Valid Until</Label>
                    <Input
                      id="validUntil"
                      type="date"
                      value={formData.quotationData.validUntil}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        quotationData: { ...prev.quotationData, validUntil: e.target.value }
                      }))}
                    />
                  </div>
                </div>
              )}
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-2 pt-4 border-t">
              <Button variant="outline" onClick={() => setShowConversionForm(false)}>
                Cancel
              </Button>
              <Button
                onClick={handleConvert}
                disabled={converting || !formData.customerData.name || !formData.customerData.email}
              >
                {converting ? 'Converting...' : 'Convert Lead'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
