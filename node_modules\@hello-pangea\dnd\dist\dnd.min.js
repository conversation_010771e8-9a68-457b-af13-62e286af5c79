!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react"),require("react-dom")):"function"==typeof define&&define.amd?define(["exports","react","react-dom"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).ReactBeautifulDnd={},e.<PERSON>,e.ReactDOM)}(this,(function(e,t,r){"use strict";function n(e){var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var o=n(t);function i(){}function a(e,t,r){const n=t.map((t=>{const n=(o=r,i=t.options,{...o,...i});var o,i;return e.addEventListener(t.eventName,t.fn,n),function(){e.removeEventListener(t.eventName,t.fn,n)}}));return function(){n.forEach((e=>{e()}))}}const s="Invariant failed";class l extends Error{}function c(e,t){throw new l(s)}l.prototype.toString=function(){return this.message};class d extends t.Component{constructor(...e){super(...e),this.callbacks=null,this.unbind=i,this.onWindowError=e=>{const t=this.getCallbacks();t.isDragging()&&t.tryAbort();e.error instanceof l&&e.preventDefault()},this.getCallbacks=()=>{if(!this.callbacks)throw new Error("Unable to find AppCallbacks in <ErrorBoundary/>");return this.callbacks},this.setCallbacks=e=>{this.callbacks=e}}componentDidMount(){this.unbind=a(window,[{eventName:"error",fn:this.onWindowError}])}componentDidCatch(e){if(!(e instanceof l))throw e;this.setState({})}componentWillUnmount(){this.unbind()}render(){return this.props.children(this.setCallbacks)}}const u=e=>e+1,p=(e,t)=>{const r=e.droppableId===t.droppableId,n=u(e.index),o=u(t.index);return r?`\n      You have moved the item from position ${n}\n      to position ${o}\n    `:`\n    You have moved the item from position ${n}\n    in list ${e.droppableId}\n    to list ${t.droppableId}\n    in position ${o}\n  `},g=(e,t,r)=>t.droppableId===r.droppableId?`\n      The item ${e}\n      has been combined with ${r.draggableId}`:`\n      The item ${e}\n      in list ${t.droppableId}\n      has been combined with ${r.draggableId}\n      in list ${r.droppableId}\n    `,f=e=>`\n  The item has returned to its starting position\n  of ${u(e.index)}\n`,m={dragHandleUsageInstructions:"\n  Press space bar to start a drag.\n  When dragging you can use the arrow keys to move the item around and escape to cancel.\n  Some screen readers may require you to be in focus mode or to use your pass through key\n",onDragStart:e=>`\n  You have lifted an item in position ${u(e.source.index)}\n`,onDragUpdate:e=>{const t=e.destination;if(t)return p(e.source,t);const r=e.combine;return r?g(e.draggableId,e.source,r):"You are over an area that cannot be dropped on"},onDragEnd:e=>{if("CANCEL"===e.reason)return`\n      Movement cancelled.\n      ${f(e.source)}\n    `;const t=e.destination,r=e.combine;return t?`\n      You have dropped the item.\n      ${p(e.source,t)}\n    `:r?`\n      You have dropped the item.\n      ${g(e.draggableId,e.source,r)}\n    `:`\n    The item has been dropped while not over a drop area.\n    ${f(e.source)}\n  `}};function b(e){return`Minified Redux error #${e}; visit https://redux.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}var h=(()=>"function"==typeof Symbol&&Symbol.observable||"@@observable")(),v=()=>Math.random().toString(36).substring(7).split("").join("."),y={INIT:`@@redux/INIT${v()}`,REPLACE:`@@redux/REPLACE${v()}`};function x(e,t,r){if("function"!=typeof e)throw new Error(b(2));if("function"==typeof t&&"function"==typeof r||"function"==typeof r&&"function"==typeof arguments[3])throw new Error(b(0));if("function"==typeof t&&void 0===r&&(r=t,t=void 0),void 0!==r){if("function"!=typeof r)throw new Error(b(1));return r(x)(e,t)}let n=e,o=t,i=new Map,a=i,s=0,l=!1;function c(){a===i&&(a=new Map,i.forEach(((e,t)=>{a.set(t,e)})))}function d(){if(l)throw new Error(b(3));return o}function u(e){if("function"!=typeof e)throw new Error(b(4));if(l)throw new Error(b(5));let t=!0;c();const r=s++;return a.set(r,e),function(){if(t){if(l)throw new Error(b(6));t=!1,c(),a.delete(r),i=null}}}function p(e){if(!function(e){if("object"!=typeof e||null===e)return!1;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t||null===Object.getPrototypeOf(e)}(e))throw new Error(b(7));if(void 0===e.type)throw new Error(b(8));if("string"!=typeof e.type)throw new Error(b(17));if(l)throw new Error(b(9));try{l=!0,o=n(o,e)}finally{l=!1}return(i=a).forEach((e=>{e()})),e}p({type:y.INIT});return{dispatch:p,subscribe:u,getState:d,replaceReducer:function(e){if("function"!=typeof e)throw new Error(b(10));n=e,p({type:y.REPLACE})},[h]:function(){const e=u;return{subscribe(t){if("object"!=typeof t||null===t)throw new Error(b(11));function r(){const e=t;e.next&&e.next(d())}r();return{unsubscribe:e(r)}},[h](){return this}}}}}function I(e,t){return function(...r){return t(e.apply(this,r))}}function D(e,t){if("function"==typeof e)return I(e,t);if("object"!=typeof e||null===e)throw new Error(b(16));const r={};for(const n in e){const o=e[n];"function"==typeof o&&(r[n]=I(o,t))}return r}function w(...e){return 0===e.length?e=>e:1===e.length?e[0]:e.reduce(((e,t)=>(...r)=>e(t(...r))))}var E,S={};
/**
   * @license React
   * use-sync-external-store-with-selector.production.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   */!function(){if(E)return S;E=1;var e=t,r="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},n=e.useSyncExternalStore,o=e.useRef,i=e.useEffect,a=e.useMemo,s=e.useDebugValue;S.useSyncExternalStoreWithSelector=function(e,t,l,c,d){var u=o(null);if(null===u.current){var p={hasValue:!1,value:null};u.current=p}else p=u.current;u=a((function(){function e(e){if(!i){if(i=!0,n=e,e=c(e),void 0!==d&&p.hasValue){var t=p.value;if(d(t,e))return o=t}return o=e}if(t=o,r(n,e))return t;var a=c(e);return void 0!==d&&d(t,a)?(n=e,t):(n=e,o=a)}var n,o,i=!1,a=void 0===l?null:l;return[function(){return e(t())},null===a?void 0:function(){return e(a())}]}),[t,l,c,d]);var g=n(e,u[0],u[1]);return i((function(){p.hasValue=!0,p.value=g}),[g]),s(g),g}}();var C=o.version.startsWith("19"),A=Symbol.for(C?"react.transitional.element":"react.element"),O=Symbol.for("react.portal"),P=Symbol.for("react.fragment"),R=Symbol.for("react.strict_mode"),B=Symbol.for("react.profiler"),N=Symbol.for("react.consumer"),T=Symbol.for("react.context"),L=Symbol.for("react.forward_ref"),M=Symbol.for("react.suspense"),G=Symbol.for("react.suspense_list"),_=Symbol.for("react.memo"),F=Symbol.for("react.lazy"),k=L,W=_;function $(e){return function(e){if("object"==typeof e&&null!==e){const{$$typeof:t}=e;switch(t){case A:switch(e=e.type){case P:case B:case R:case M:case G:return e;default:switch(e=e&&e.$$typeof){case T:case L:case F:case _:case N:return e;default:return t}}case O:return t}}}(e)===_}function j(e,t,r,n,{areStatesEqual:o,areOwnPropsEqual:i,areStatePropsEqual:a}){let s,l,c,d,u,p=!1;function g(p,g){const f=!i(g,l),m=!o(p,s,g,l);return s=p,l=g,f&&m?(c=e(s,l),t.dependsOnOwnProps&&(d=t(n,l)),u=r(c,d,l),u):f?(e.dependsOnOwnProps&&(c=e(s,l)),t.dependsOnOwnProps&&(d=t(n,l)),u=r(c,d,l),u):m?function(){const t=e(s,l),n=!a(t,c);return c=t,n&&(u=r(c,d,l)),u}():u}return function(o,i){return p?g(o,i):(s=o,l=i,c=e(s,l),d=t(n,l),u=r(c,d,l),p=!0,u)}}function U(e){return function(t){const r=e(t);function n(){return r}return n.dependsOnOwnProps=!1,n}}function H(e){return e.dependsOnOwnProps?Boolean(e.dependsOnOwnProps):1!==e.length}function V(e,t){return function(t,{displayName:r}){const n=function(e,t){return n.dependsOnOwnProps?n.mapToProps(e,t):n.mapToProps(e,void 0)};return n.dependsOnOwnProps=!0,n.mapToProps=function(t,r){n.mapToProps=e,n.dependsOnOwnProps=H(e);let o=n(t,r);return"function"==typeof o&&(n.mapToProps=o,n.dependsOnOwnProps=H(o),o=n(t,r)),o},n}}function q(e,t){return(r,n)=>{throw new Error(`Invalid value of type ${typeof e} for ${t} argument when connecting component ${n.wrappedComponentName}.`)}}function z(e,t,r){return{...r,...e,...t}}var Y={notify(){},get:()=>[]};function J(e,t){let r,n=Y,o=0,i=!1;function a(){c.onStateChange&&c.onStateChange()}function s(){o++,r||(r=t?t.addNestedSub(a):e.subscribe(a),n=function(){let e=null,t=null;return{clear(){e=null,t=null},notify(){(()=>{let t=e;for(;t;)t.callback(),t=t.next})()},get(){const t=[];let r=e;for(;r;)t.push(r),r=r.next;return t},subscribe(r){let n=!0;const o=t={callback:r,next:null,prev:t};return o.prev?o.prev.next=o:e=o,function(){n&&null!==e&&(n=!1,o.next?o.next.prev=o.prev:t=o.prev,o.prev?o.prev.next=o.next:e=o.next)}}}}())}function l(){o--,r&&0===o&&(r(),r=void 0,n.clear(),n=Y)}const c={addNestedSub:function(e){s();const t=n.subscribe(e);let r=!1;return()=>{r||(r=!0,t(),l())}},notifyNestedSubs:function(){n.notify()},handleChangeWrapper:a,isSubscribed:function(){return i},trySubscribe:function(){i||(i=!0,s())},tryUnsubscribe:function(){i&&(i=!1,l())},getListeners:()=>n};return c}var X=(()=>!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement))(),K=(()=>"undefined"!=typeof navigator&&"ReactNative"===navigator.product)(),Q=(()=>X||K?o.useLayoutEffect:o.useEffect)();function Z(e,t){return e===t?0!==e||0!==t||1/e==1/t:e!=e&&t!=t}function ee(e,t){if(Z(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;const r=Object.keys(e),n=Object.keys(t);if(r.length!==n.length)return!1;for(let n=0;n<r.length;n++)if(!Object.prototype.hasOwnProperty.call(t,r[n])||!Z(e[r[n]],t[r[n]]))return!1;return!0}var te={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},re={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},ne={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},oe={[k]:{$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},[W]:ne};function ie(e){return $(e)?ne:oe[e.$$typeof]||te}var ae=Object.defineProperty,se=Object.getOwnPropertyNames,le=Object.getOwnPropertySymbols,ce=Object.getOwnPropertyDescriptor,de=Object.getPrototypeOf,ue=Object.prototype;function pe(e,t){if("string"!=typeof t){if(ue){const r=de(t);r&&r!==ue&&pe(e,r)}let r=se(t);le&&(r=r.concat(le(t)));const n=ie(e),o=ie(t);for(let i=0;i<r.length;++i){const a=r[i];if(!(re[a]||o&&o[a]||n&&n[a])){const r=ce(t,a);try{ae(e,a,r)}catch(e){}}}}return e}var ge=Symbol.for("react-redux-context"),fe="undefined"!=typeof globalThis?globalThis:{};function me(){if(!o.createContext)return{};const e=fe[ge]??=new Map;let t=e.get(o.createContext);return t||(t=o.createContext(null),e.set(o.createContext,t)),t}var be=me(),he=[null,null];function ve(e,t,r,n,o,i){e.current=n,r.current=!1,o.current&&(o.current=null,i())}function ye(e,t){return e===t}var xe=function(e,t,r,{pure:n,areStatesEqual:i=ye,areOwnPropsEqual:a=ee,areStatePropsEqual:s=ee,areMergedPropsEqual:l=ee,forwardRef:c=!1,context:d=be}={}){const u=d,p=function(e){return e?"function"==typeof e?V(e):q(e,"mapStateToProps"):U((()=>({})))}(e),g=function(e){return e&&"object"==typeof e?U((t=>function(e,t){const r={};for(const n in e){const o=e[n];"function"==typeof o&&(r[n]=(...e)=>t(o(...e)))}return r}(e,t))):e?"function"==typeof e?V(e):q(e,"mapDispatchToProps"):U((e=>({dispatch:e})))}(t),f=function(e){return e?"function"==typeof e?function(e){return function(t,{displayName:r,areMergedPropsEqual:n}){let o,i=!1;return function(t,r,a){const s=e(t,r,a);return i?n(s,o)||(o=s):(i=!0,o=s),o}}}(e):q(e,"mergeProps"):()=>z}(r),m=Boolean(e);return e=>{const t=e.displayName||e.name||"Component",r=`Connect(${t})`,n={shouldHandleStateChanges:m,displayName:r,wrappedComponentName:t,WrappedComponent:e,initMapStateToProps:p,initMapDispatchToProps:g,initMergeProps:f,areStatesEqual:i,areStatePropsEqual:s,areOwnPropsEqual:a,areMergedPropsEqual:l};function d(t){const[r,i,a]=o.useMemo((()=>{const{reactReduxForwardedRef:e,...r}=t;return[t.context,e,r]}),[t]),s=o.useMemo((()=>u),[r,u]),l=o.useContext(s),c=Boolean(t.store)&&Boolean(t.store.getState)&&Boolean(t.store.dispatch),d=Boolean(l)&&Boolean(l.store),p=c?t.store:l.store,g=d?l.getServerState:p.getState,f=o.useMemo((()=>function(e,{initMapStateToProps:t,initMapDispatchToProps:r,initMergeProps:n,...o}){return j(t(e,o),r(e,o),n(e,o),e,o)}(p.dispatch,n)),[p]),[b,h]=o.useMemo((()=>{if(!m)return he;const e=J(p,c?void 0:l.subscription),t=e.notifyNestedSubs.bind(e);return[e,t]}),[p,c,l]),v=o.useMemo((()=>c?l:{...l,subscription:b}),[c,l,b]),y=o.useRef(void 0),x=o.useRef(a),I=o.useRef(void 0),D=o.useRef(!1),w=o.useRef(!1),E=o.useRef(void 0);Q((()=>(w.current=!0,()=>{w.current=!1})),[]);const S=o.useMemo((()=>()=>I.current&&a===x.current?I.current:f(p.getState(),a)),[p,a]),C=o.useMemo((()=>e=>b?function(e,t,r,n,o,i,a,s,l,c,d){if(!e)return()=>{};let u=!1,p=null;const g=()=>{if(u||!s.current)return;const e=t.getState();let r,g;try{r=n(e,o.current)}catch(e){g=e,p=e}g||(p=null),r===i.current?a.current||c():(i.current=r,l.current=r,a.current=!0,d())};return r.onStateChange=g,r.trySubscribe(),g(),()=>{if(u=!0,r.tryUnsubscribe(),r.onStateChange=null,p)throw p}}(m,p,b,f,x,y,D,w,I,h,e):()=>{}),[b]);var A,O,P;let R;A=ve,O=[x,y,D,a,I,h],Q((()=>A(...O)),P);try{R=o.useSyncExternalStore(C,S,g?()=>f(g(),a):S)}catch(e){throw E.current&&(e.message+=`\nThe error may be correlated with this previous error:\n${E.current.stack}\n\n`),e}Q((()=>{E.current=void 0,I.current=void 0,y.current=R}));const B=o.useMemo((()=>o.createElement(e,{...R,ref:i})),[i,e,R]);return o.useMemo((()=>m?o.createElement(s.Provider,{value:v},B):B),[s,B,v])}const b=o.memo(d);if(b.WrappedComponent=e,b.displayName=d.displayName=r,c){const t=o.forwardRef((function(e,t){return o.createElement(b,{...e,reactReduxForwardedRef:t})}));return t.displayName=r,t.WrappedComponent=e,pe(t,e)}return pe(b,e)}};var Ie=function(e){const{children:t,context:r,serverState:n,store:i}=e,a=o.useMemo((()=>{const e=J(i);return{store:i,subscription:e,getServerState:n?()=>n:void 0}}),[i,n]),s=o.useMemo((()=>i.getState()),[i]);Q((()=>{const{subscription:e}=a;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),s!==i.getState()&&e.notifyNestedSubs(),()=>{e.tryUnsubscribe(),e.onStateChange=void 0}}),[a,s]);const l=r||be;return o.createElement(l.Provider,{value:a},t)};function De(e,t){if(e.length!==t.length)return!1;for(let o=0;o<e.length;o++)if(r=e[o],n=t[o],!(r===n||Number.isNaN(r)&&Number.isNaN(n)))return!1;var r,n;return!0}function we(e,r){const n=t.useState((()=>({inputs:r,result:e()})))[0],o=t.useRef(!0),i=t.useRef(n),a=o.current||Boolean(r&&i.current.inputs&&De(r,i.current.inputs))?i.current:{inputs:r,result:e()};return t.useEffect((()=>{o.current=!1,i.current=a}),[a]),a.result}function Ee(e,t){return we((()=>e),t)}const Se={x:0,y:0},Ce=(e,t)=>({x:e.x+t.x,y:e.y+t.y}),Ae=(e,t)=>({x:e.x-t.x,y:e.y-t.y}),Oe=(e,t)=>e.x===t.x&&e.y===t.y,Pe=e=>({x:0!==e.x?-e.x:0,y:0!==e.y?-e.y:0}),Re=(e,t,r=0)=>"x"===e?{x:t,y:r}:{x:r,y:t},Be=(e,t)=>Math.sqrt((t.x-e.x)**2+(t.y-e.y)**2),Ne=(e,t)=>Math.min(...t.map((t=>Be(e,t)))),Te=e=>t=>({x:e(t.x),y:e(t.y)});var Le=function(e){var t=e.top,r=e.right,n=e.bottom,o=e.left;return{top:t,right:r,bottom:n,left:o,width:r-o,height:n-t,x:o,y:t,center:{x:(r+o)/2,y:(n+t)/2}}},Me=function(e,t){return{top:e.top-t.top,left:e.left-t.left,bottom:e.bottom+t.bottom,right:e.right+t.right}},Ge=function(e,t){return{top:e.top+t.top,left:e.left+t.left,bottom:e.bottom-t.bottom,right:e.right-t.right}},_e={top:0,right:0,bottom:0,left:0},Fe=function(e){var t=e.borderBox,r=e.margin,n=void 0===r?_e:r,o=e.border,i=void 0===o?_e:o,a=e.padding,s=void 0===a?_e:a,l=Le(Me(t,n)),c=Le(Ge(t,i)),d=Le(Ge(c,s));return{marginBox:l,borderBox:Le(t),paddingBox:c,contentBox:d,margin:n,border:i,padding:s}},ke=function(e){var t=e.slice(0,-2);if("px"!==e.slice(-2))return 0;var r=Number(t);return isNaN(r)&&function(e,t){throw new Error("Invariant failed")}(),r},We=function(e,t){var r,n,o=e.borderBox,i=e.border,a=e.margin,s=e.padding,l=(n=t,{top:(r=o).top+n.y,left:r.left+n.x,bottom:r.bottom+n.y,right:r.right+n.x});return Fe({borderBox:l,border:i,margin:a,padding:s})},$e=function(e,t){return void 0===t&&(t={x:window.pageXOffset,y:window.pageYOffset}),We(e,t)},je=function(e,t){var r={top:ke(t.marginTop),right:ke(t.marginRight),bottom:ke(t.marginBottom),left:ke(t.marginLeft)},n={top:ke(t.paddingTop),right:ke(t.paddingRight),bottom:ke(t.paddingBottom),left:ke(t.paddingLeft)},o={top:ke(t.borderTopWidth),right:ke(t.borderRightWidth),bottom:ke(t.borderBottomWidth),left:ke(t.borderLeftWidth)};return Fe({borderBox:e,margin:r,padding:n,border:o})},Ue=function(e){var t=e.getBoundingClientRect(),r=window.getComputedStyle(e);return je(t,r)};const He=(e,t)=>({top:e.top+t.y,left:e.left+t.x,bottom:e.bottom+t.y,right:e.right+t.x}),Ve=e=>[{x:e.left,y:e.top},{x:e.right,y:e.top},{x:e.left,y:e.bottom},{x:e.right,y:e.bottom}],qe=(e,t)=>t&&t.shouldClipSubject?((e,t)=>{const r=Le({top:Math.max(t.top,e.top),right:Math.min(t.right,e.right),bottom:Math.min(t.bottom,e.bottom),left:Math.max(t.left,e.left)});return r.width<=0||r.height<=0?null:r})(t.pageMarginBox,e):Le(e);var ze=({page:e,withPlaceholder:t,axis:r,frame:n})=>{const o=((e,t)=>t?He(e,t.scroll.diff.displacement):e)(e.marginBox,n),i=((e,t,r)=>r&&r.increasedBy?{...e,[t.end]:e[t.end]+r.increasedBy[t.line]}:e)(o,r,t);return{page:e,withPlaceholder:t,active:qe(i,n)}},Ye=(e,t)=>{e.frame||c();const r=e.frame,n=Ae(t,r.scroll.initial),o=Pe(n),i={...r,scroll:{initial:r.scroll.initial,current:t,diff:{value:n,displacement:o},max:r.scroll.max}},a=ze({page:e.subject.page,withPlaceholder:e.subject.withPlaceholder,axis:e.axis,frame:i});return{...e,frame:i,subject:a}};function Je(e,t=De){let r=null;function n(...n){if(r&&r.lastThis===this&&t(n,r.lastArgs))return r.lastResult;const o=e.apply(this,n);return r={lastResult:o,lastArgs:n,lastThis:this},o}return n.clear=function(){r=null},n}const Xe=Je((e=>e.reduce(((e,t)=>(e[t.descriptor.id]=t,e)),{}))),Ke=Je((e=>e.reduce(((e,t)=>(e[t.descriptor.id]=t,e)),{}))),Qe=Je((e=>Object.values(e))),Ze=Je((e=>Object.values(e)));var et=Je(((e,t)=>{const r=Ze(t).filter((t=>e===t.descriptor.droppableId)).sort(((e,t)=>e.descriptor.index-t.descriptor.index));return r}));function tt(e){return e.at&&"REORDER"===e.at.type?e.at.destination:null}function rt(e){return e.at&&"COMBINE"===e.at.type?e.at.combine:null}var nt=Je(((e,t)=>t.filter((t=>t.descriptor.id!==e.descriptor.id)))),ot=(e,t)=>e.descriptor.droppableId===t.descriptor.id;const it={point:Se,value:0},at={invisible:{},visible:{},all:[]},st={displaced:at,displacedBy:it,at:null};var lt=(e,t)=>r=>e<=r&&r<=t,ct=e=>{const t=lt(e.top,e.bottom),r=lt(e.left,e.right);return n=>{if(t(n.top)&&t(n.bottom)&&r(n.left)&&r(n.right))return!0;const o=t(n.top)||t(n.bottom),i=r(n.left)||r(n.right);if(o&&i)return!0;const a=n.top<e.top&&n.bottom>e.bottom,s=n.left<e.left&&n.right>e.right;if(a&&s)return!0;return a&&i||s&&o}},dt=e=>{const t=lt(e.top,e.bottom),r=lt(e.left,e.right);return e=>t(e.top)&&t(e.bottom)&&r(e.left)&&r(e.right)};const ut={direction:"vertical",line:"y",crossAxisLine:"x",start:"top",end:"bottom",size:"height",crossAxisStart:"left",crossAxisEnd:"right",crossAxisSize:"width"},pt={direction:"horizontal",line:"x",crossAxisLine:"y",start:"left",end:"right",size:"width",crossAxisStart:"top",crossAxisEnd:"bottom",crossAxisSize:"height"};const gt=({target:e,destination:t,viewport:r,withDroppableDisplacement:n,isVisibleThroughFrameFn:o})=>{const i=n?((e,t)=>{const r=t.frame?t.frame.scroll.diff.displacement:Se;return He(e,r)})(e,t):e;return((e,t,r)=>!!t.subject.active&&r(t.subject.active)(e))(i,t,o)&&((e,t,r)=>r(t)(e))(i,r,o)},ft=e=>gt({...e,isVisibleThroughFrameFn:ct}),mt=e=>gt({...e,isVisibleThroughFrameFn:dt}),bt=(e,t,r)=>{if("boolean"==typeof r)return r;if(!t)return!0;const{invisible:n,visible:o}=t;if(n[e])return!1;const i=o[e];return!i||i.shouldAnimate};function ht({afterDragging:e,destination:t,displacedBy:r,viewport:n,forceShouldAnimate:o,last:i}){return e.reduce((function(e,a){const s=function(e,t){const r=e.page.marginBox,n={top:t.point.y,right:0,bottom:0,left:t.point.x};return Le(Me(r,n))}(a,r),l=a.descriptor.id;e.all.push(l);if(!ft({target:s,destination:t,viewport:n,withDroppableDisplacement:!0}))return e.invisible[a.descriptor.id]=!0,e;const c={draggableId:l,shouldAnimate:bt(l,i,o)};return e.visible[l]=c,e}),{all:[],visible:{},invisible:{}})}function vt({insideDestination:e,inHomeList:t,displacedBy:r,destination:n}){const o=function(e,t){if(!e.length)return 0;const r=e[e.length-1].descriptor.index;return t.inHomeList?r:r+1}(e,{inHomeList:t});return{displaced:at,displacedBy:r,at:{type:"REORDER",destination:{droppableId:n.descriptor.id,index:o}}}}function yt({draggable:e,insideDestination:t,destination:r,viewport:n,displacedBy:o,last:i,index:a,forceShouldAnimate:s}){const l=ot(e,r);if(null==a)return vt({insideDestination:t,inHomeList:l,displacedBy:o,destination:r});const c=t.find((e=>e.descriptor.index===a));if(!c)return vt({insideDestination:t,inHomeList:l,displacedBy:o,destination:r});const d=nt(e,t),u=t.indexOf(c);return{displaced:ht({afterDragging:d.slice(u),destination:r,displacedBy:o,last:i,viewport:n.frame,forceShouldAnimate:s}),displacedBy:o,at:{type:"REORDER",destination:{droppableId:r.descriptor.id,index:a}}}}function xt(e,t){return Boolean(t.effected[e])}var It=({isMovingForward:e,isInHomeList:t,draggable:r,draggables:n,destination:o,insideDestination:i,previousImpact:a,viewport:s,afterCritical:l})=>{const d=a.at;if(d||c(),"REORDER"===d.type){const n=(({isMovingForward:e,isInHomeList:t,insideDestination:r,location:n})=>{if(!r.length)return null;const o=n.index,i=e?o+1:o-1,a=r[0].descriptor.index,s=r[r.length-1].descriptor.index;return i<a||i>(t?s:s+1)?null:i})({isMovingForward:e,isInHomeList:t,location:d.destination,insideDestination:i});return null==n?null:yt({draggable:r,insideDestination:i,destination:o,viewport:s,last:a.displaced,displacedBy:a.displacedBy,index:n})}const u=(({isMovingForward:e,destination:t,draggables:r,combine:n,afterCritical:o})=>{if(!t.isCombineEnabled)return null;const i=n.draggableId,a=r[i].descriptor.index;return xt(i,o)?e?a:a-1:e?a+1:a})({isMovingForward:e,destination:o,displaced:a.displaced,draggables:n,combine:d.combine,afterCritical:l});return null==u?null:yt({draggable:r,insideDestination:i,destination:o,viewport:s,last:a.displaced,displacedBy:a.displacedBy,index:u})},Dt=({afterCritical:e,impact:t,draggables:r})=>{const n=rt(t);n||c();const o=n.draggableId,i=r[o].page.borderBox.center,a=(({displaced:e,afterCritical:t,combineWith:r,displacedBy:n})=>{const o=Boolean(e.visible[r]||e.invisible[r]);return xt(r,t)?o?Se:Pe(n.point):o?n.point:Se})({displaced:t.displaced,afterCritical:e,combineWith:o,displacedBy:t.displacedBy});return Ce(i,a)};const wt=(e,t)=>t.margin[e.start]+t.borderBox[e.size]/2,Et=(e,t,r)=>t[e.crossAxisStart]+r.margin[e.crossAxisStart]+r.borderBox[e.crossAxisSize]/2,St=({axis:e,moveRelativeTo:t,isMoving:r})=>Re(e.line,t.marginBox[e.end]+wt(e,r),Et(e,t.marginBox,r)),Ct=({axis:e,moveRelativeTo:t,isMoving:r})=>Re(e.line,t.marginBox[e.start]-((e,t)=>t.margin[e.end]+t.borderBox[e.size]/2)(e,r),Et(e,t.marginBox,r));var At=({impact:e,draggable:t,draggables:r,droppable:n,afterCritical:o})=>{const i=et(n.descriptor.id,r),a=t.page,s=n.axis;if(!i.length)return(({axis:e,moveInto:t,isMoving:r})=>Re(e.line,t.contentBox[e.start]+wt(e,r),Et(e,t.contentBox,r)))({axis:s,moveInto:n.page,isMoving:a});const{displaced:l,displacedBy:c}=e,d=l.all[0];if(d){const e=r[d];if(xt(d,o))return Ct({axis:s,moveRelativeTo:e.page,isMoving:a});const t=We(e.page,c.point);return Ct({axis:s,moveRelativeTo:t,isMoving:a})}const u=i[i.length-1];if(u.descriptor.id===t.descriptor.id)return a.borderBox.center;if(xt(u.descriptor.id,o)){const e=We(u.page,Pe(o.displacedBy.point));return St({axis:s,moveRelativeTo:e,isMoving:a})}return St({axis:s,moveRelativeTo:u.page,isMoving:a})},Ot=(e,t)=>{const r=e.frame;return r?Ce(t,r.scroll.diff.displacement):t};var Pt=e=>{const t=(({impact:e,draggable:t,droppable:r,draggables:n,afterCritical:o})=>{const i=t.page.borderBox.center,a=e.at;return r&&a?"REORDER"===a.type?At({impact:e,draggable:t,draggables:n,droppable:r,afterCritical:o}):Dt({impact:e,draggables:n,afterCritical:o}):i})(e),r=e.droppable;return r?Ot(r,t):t},Rt=(e,t)=>{const r=Ae(t,e.scroll.initial),n=Pe(r);return{frame:Le({top:t.y,bottom:t.y+e.frame.height,left:t.x,right:t.x+e.frame.width}),scroll:{initial:e.scroll.initial,max:e.scroll.max,current:t,diff:{value:r,displacement:n}}}};function Bt(e,t){return e.map((e=>t[e]))}var Nt=({pageBorderBoxCenter:e,draggable:t,viewport:r})=>{const n=((e,t)=>Ce(e.scroll.diff.displacement,t))(r,e),o=Ae(n,t.page.borderBox.center);return Ce(t.client.borderBox.center,o)},Tt=({draggable:e,destination:t,newPageBorderBoxCenter:r,viewport:n,withDroppableDisplacement:o,onlyOnMainAxis:i=!1})=>{const a=Ae(r,e.page.borderBox.center),s={target:He(e.page.borderBox,a),destination:t,withDroppableDisplacement:o,viewport:n};return i?(e=>{return gt({...e,isVisibleThroughFrameFn:(t=e.destination.axis,e=>{const r=lt(e.top,e.bottom),n=lt(e.left,e.right);return e=>t===ut?r(e.top)&&r(e.bottom):n(e.left)&&n(e.right)})});var t})(s):mt(s)},Lt=({isMovingForward:e,draggable:t,destination:r,draggables:n,previousImpact:o,viewport:i,previousPageBorderBoxCenter:a,previousClientSelection:s,afterCritical:l})=>{if(!r.isEnabled)return null;const d=et(r.descriptor.id,n),u=ot(t,r),p=(({isMovingForward:e,draggable:t,destination:r,insideDestination:n,previousImpact:o})=>{if(!r.isCombineEnabled)return null;if(!tt(o))return null;function i(e){const t={type:"COMBINE",combine:{draggableId:e,droppableId:r.descriptor.id}};return{...o,at:t}}const a=o.displaced.all,s=a.length?a[0]:null;if(e)return s?i(s):null;const l=nt(t,n);if(!s)return l.length?i(l[l.length-1].descriptor.id):null;const d=l.findIndex((e=>e.descriptor.id===s));-1===d&&c();const u=d-1;return u<0?null:i(l[u].descriptor.id)})({isMovingForward:e,draggable:t,destination:r,insideDestination:d,previousImpact:o})||It({isMovingForward:e,isInHomeList:u,draggable:t,draggables:n,destination:r,insideDestination:d,previousImpact:o,viewport:i,afterCritical:l});if(!p)return null;const g=Pt({impact:p,draggable:t,droppable:r,draggables:n,afterCritical:l});if(Tt({draggable:t,destination:r,newPageBorderBoxCenter:g,viewport:i.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0})){return{clientSelection:Nt({pageBorderBoxCenter:g,draggable:t,viewport:i}),impact:p,scrollJumpRequest:null}}const f=Ae(g,a),m=(({impact:e,viewport:t,destination:r,draggables:n,maxScrollChange:o})=>{const i=Rt(t,Ce(t.scroll.current,o)),a=r.frame?Ye(r,Ce(r.frame.scroll.current,o)):r,s=e.displaced,l=ht({afterDragging:Bt(s.all,n),destination:r,displacedBy:e.displacedBy,viewport:i.frame,last:s,forceShouldAnimate:!1}),c=ht({afterDragging:Bt(s.all,n),destination:a,displacedBy:e.displacedBy,viewport:t.frame,last:s,forceShouldAnimate:!1}),d={},u={},p=[s,l,c];return s.all.forEach((e=>{const t=function(e,t){for(let r=0;r<t.length;r++){const n=t[r].visible[e];if(n)return n}return null}(e,p);t?u[e]=t:d[e]=!0})),{...e,displaced:{all:s.all,invisible:d,visible:u}}})({impact:p,viewport:i,destination:r,draggables:n,maxScrollChange:f});return{clientSelection:s,impact:m,scrollJumpRequest:f}};const Mt=e=>{const t=e.subject.active;return t||c(),t};const Gt=(e,t)=>{const r=e.page.borderBox.center;return xt(e.descriptor.id,t)?Ae(r,t.displacedBy.point):r},_t=(e,t)=>{const r=e.page.borderBox;return xt(e.descriptor.id,t)?He(r,Pe(t.displacedBy.point)):r};var Ft=Je((function(e,t){const r=t[e.line];return{value:r,point:Re(e.line,r)}}));const kt=(e,t)=>({...e,scroll:{...e.scroll,max:t}}),Wt=(e,t,r)=>{const n=e.frame;ot(t,e)&&c(),e.subject.withPlaceholder&&c();const o=Ft(e.axis,t.displaceBy).point,i=((e,t,r)=>{const n=e.axis;if("virtual"===e.descriptor.mode)return Re(n.line,t[n.line]);const o=e.subject.page.contentBox[n.size],i=et(e.descriptor.id,r).reduce(((e,t)=>e+t.client.marginBox[n.size]),0)+t[n.line]-o;return i<=0?null:Re(n.line,i)})(e,o,r),a={placeholderSize:o,increasedBy:i,oldFrameMaxScroll:e.frame?e.frame.scroll.max:null};if(!n){const t=ze({page:e.subject.page,withPlaceholder:a,axis:e.axis,frame:e.frame});return{...e,subject:t}}const s=i?Ce(n.scroll.max,i):n.scroll.max,l=kt(n,s),d=ze({page:e.subject.page,withPlaceholder:a,axis:e.axis,frame:l});return{...e,subject:d,frame:l}};var $t=({isMovingForward:e,previousPageBorderBoxCenter:t,draggable:r,isOver:n,draggables:o,droppables:i,viewport:a,afterCritical:s})=>{const l=(({isMovingForward:e,pageBorderBoxCenter:t,source:r,droppables:n,viewport:o})=>{const i=r.subject.active;if(!i)return null;const a=r.axis,s=lt(i[a.start],i[a.end]),l=Qe(n).filter((e=>e!==r)).filter((e=>e.isEnabled)).filter((e=>Boolean(e.subject.active))).filter((e=>ct(o.frame)(Mt(e)))).filter((t=>{const r=Mt(t);return e?i[a.crossAxisEnd]<r[a.crossAxisEnd]:r[a.crossAxisStart]<i[a.crossAxisStart]})).filter((e=>{const t=Mt(e),r=lt(t[a.start],t[a.end]);return s(t[a.start])||s(t[a.end])||r(i[a.start])||r(i[a.end])})).sort(((t,r)=>{const n=Mt(t)[a.crossAxisStart],o=Mt(r)[a.crossAxisStart];return e?n-o:o-n})).filter(((e,t,r)=>Mt(e)[a.crossAxisStart]===Mt(r[0])[a.crossAxisStart]));if(!l.length)return null;if(1===l.length)return l[0];const c=l.filter((e=>lt(Mt(e)[a.start],Mt(e)[a.end])(t[a.line])));return 1===c.length?c[0]:c.length>1?c.sort(((e,t)=>Mt(e)[a.start]-Mt(t)[a.start]))[0]:l.sort(((e,r)=>{const n=Ne(t,Ve(Mt(e))),o=Ne(t,Ve(Mt(r)));return n!==o?n-o:Mt(e)[a.start]-Mt(r)[a.start]}))[0]})({isMovingForward:e,pageBorderBoxCenter:t,source:n,droppables:i,viewport:a});if(!l)return null;const c=et(l.descriptor.id,o),d=(({pageBorderBoxCenter:e,viewport:t,destination:r,insideDestination:n,afterCritical:o})=>{const i=n.filter((e=>mt({target:_t(e,o),destination:r,viewport:t.frame,withDroppableDisplacement:!0}))).sort(((t,n)=>{const i=Be(e,Ot(r,Gt(t,o))),a=Be(e,Ot(r,Gt(n,o)));return i<a?-1:a<i?1:t.descriptor.index-n.descriptor.index}));return i[0]||null})({pageBorderBoxCenter:t,viewport:a,destination:l,insideDestination:c,afterCritical:s}),u=(({previousPageBorderBoxCenter:e,moveRelativeTo:t,insideDestination:r,draggable:n,draggables:o,destination:i,viewport:a,afterCritical:s})=>{if(!t){if(r.length)return null;const e={displaced:at,displacedBy:it,at:{type:"REORDER",destination:{droppableId:i.descriptor.id,index:0}}},t=Pt({impact:e,draggable:n,droppable:i,draggables:o,afterCritical:s}),l=ot(n,i)?i:Wt(i,n,o);return Tt({draggable:n,destination:l,newPageBorderBoxCenter:t,viewport:a.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0})?e:null}const l=Boolean(e[i.axis.line]<=t.page.borderBox.center[i.axis.line]),c=(()=>{const e=t.descriptor.index;return t.descriptor.id===n.descriptor.id||l?e:e+1})(),d=Ft(i.axis,n.displaceBy);return yt({draggable:n,insideDestination:r,destination:i,viewport:a,displacedBy:d,last:at,index:c})})({previousPageBorderBoxCenter:t,destination:l,draggable:r,draggables:o,moveRelativeTo:d,insideDestination:c,viewport:a,afterCritical:s});if(!u)return null;const p=Pt({impact:u,draggable:r,droppable:l,draggables:o,afterCritical:s});return{clientSelection:Nt({pageBorderBoxCenter:p,draggable:r,viewport:a}),impact:u,scrollJumpRequest:null}},jt=e=>{const t=e.at;return t?"REORDER"===t.type?t.destination.droppableId:t.combine.droppableId:null};var Ut=({state:e,type:t})=>{const r=((e,t)=>{const r=jt(e);return r?t[r]:null})(e.impact,e.dimensions.droppables),n=Boolean(r),o=e.dimensions.droppables[e.critical.droppable.id],i=r||o,a=i.axis.direction,s="vertical"===a&&("MOVE_UP"===t||"MOVE_DOWN"===t)||"horizontal"===a&&("MOVE_LEFT"===t||"MOVE_RIGHT"===t);if(s&&!n)return null;const l="MOVE_DOWN"===t||"MOVE_RIGHT"===t,c=e.dimensions.draggables[e.critical.draggable.id],d=e.current.page.borderBoxCenter,{draggables:u,droppables:p}=e.dimensions;return s?Lt({isMovingForward:l,previousPageBorderBoxCenter:d,draggable:c,destination:i,draggables:u,viewport:e.viewport,previousClientSelection:e.current.client.selection,previousImpact:e.impact,afterCritical:e.afterCritical}):$t({isMovingForward:l,previousPageBorderBoxCenter:d,draggable:c,isOver:i,draggables:u,droppables:p,viewport:e.viewport,afterCritical:e.afterCritical})};function Ht(e){return"DRAGGING"===e.phase||"COLLECTING"===e.phase}function Vt(e){const t=lt(e.top,e.bottom),r=lt(e.left,e.right);return function(e){return t(e.y)&&r(e.x)}}function qt({pageBorderBox:e,draggable:t,droppables:r}){const n=Qe(r).filter((t=>{if(!t.isEnabled)return!1;const r=t.subject.active;if(!r)return!1;if(o=r,!((n=e).left<o.right&&n.right>o.left&&n.top<o.bottom&&n.bottom>o.top))return!1;var n,o;if(Vt(r)(e.center))return!0;const i=t.axis,a=r.center[i.crossAxisLine],s=e[i.crossAxisStart],l=e[i.crossAxisEnd],c=lt(r[i.crossAxisStart],r[i.crossAxisEnd]),d=c(s),u=c(l);return!d&&!u||(d?s<a:l>a)}));return n.length?1===n.length?n[0].descriptor.id:function({pageBorderBox:e,draggable:t,candidates:r}){const n=t.page.borderBox.center,o=r.map((t=>{const r=t.axis,o=Re(t.axis.line,e.center[r.line],t.page.borderBox.center[r.crossAxisLine]);return{id:t.descriptor.id,distance:Be(n,o)}})).sort(((e,t)=>t.distance-e.distance));return o[0]?o[0].id:null}({pageBorderBox:e,draggable:t,candidates:n}):null}const zt=(e,t)=>Le(He(e,t));function Yt({displaced:e,id:t}){return Boolean(e.visible[t]||e.invisible[t])}var Jt=({pageOffset:e,draggable:t,draggables:r,droppables:n,previousImpact:o,viewport:i,afterCritical:a})=>{const s=zt(t.page.borderBox,e),l=qt({pageBorderBox:s,draggable:t,droppables:n});if(!l)return st;const c=n[l],d=et(c.descriptor.id,r),u=((e,t)=>{const r=e.frame;return r?zt(t,r.scroll.diff.value):t})(c,s);return(({draggable:e,pageBorderBoxWithDroppableScroll:t,previousImpact:r,destination:n,insideDestination:o,afterCritical:i})=>{if(!n.isCombineEnabled)return null;const a=n.axis,s=Ft(n.axis,e.displaceBy),l=s.value,c=t[a.start],d=t[a.end],u=nt(e,o).find((e=>{const t=e.descriptor.id,n=e.page.borderBox,o=n[a.size]/4,s=xt(t,i),u=Yt({displaced:r.displaced,id:t});return s?u?d>n[a.start]+o&&d<n[a.end]-o:c>n[a.start]-l+o&&c<n[a.end]-l-o:u?d>n[a.start]+l+o&&d<n[a.end]+l-o:c>n[a.start]+o&&c<n[a.end]-o}));return u?{displacedBy:s,displaced:r.displaced,at:{type:"COMBINE",combine:{draggableId:u.descriptor.id,droppableId:n.descriptor.id}}}:null})({pageBorderBoxWithDroppableScroll:u,draggable:t,previousImpact:o,destination:c,insideDestination:d,afterCritical:a})||(({pageBorderBoxWithDroppableScroll:e,draggable:t,destination:r,insideDestination:n,last:o,viewport:i,afterCritical:a})=>{const s=r.axis,l=Ft(r.axis,t.displaceBy),c=l.value,d=e[s.start],u=e[s.end],p=function({draggable:e,closest:t,inHomeList:r}){return t?r&&t.descriptor.index>e.descriptor.index?t.descriptor.index-1:t.descriptor.index:null}({draggable:t,closest:nt(t,n).find((e=>{const t=e.descriptor.id,r=e.page.borderBox.center[s.line],n=xt(t,a),i=Yt({displaced:o,id:t});return n?i?u<=r:d<r-c:i?u<=r+c:d<r}))||null,inHomeList:ot(t,r)});return yt({draggable:t,insideDestination:n,destination:r,viewport:i,last:o,displacedBy:l,index:p})})({pageBorderBoxWithDroppableScroll:u,draggable:t,destination:c,insideDestination:d,last:o.displaced,viewport:i,afterCritical:a})},Xt=(e,t)=>({...e,[t.descriptor.id]:t});const Kt=({previousImpact:e,impact:t,droppables:r})=>{const n=jt(e),o=jt(t);if(!n)return r;if(n===o)return r;const i=r[n];if(!i.subject.withPlaceholder)return r;const a=(e=>{const t=e.subject.withPlaceholder;t||c();const r=e.frame;if(!r){const t=ze({page:e.subject.page,axis:e.axis,frame:null,withPlaceholder:null});return{...e,subject:t}}const n=t.oldFrameMaxScroll;n||c();const o=kt(r,n),i=ze({page:e.subject.page,axis:e.axis,frame:o,withPlaceholder:null});return{...e,subject:i,frame:o}})(i);return Xt(r,a)};var Qt=({state:e,clientSelection:t,dimensions:r,viewport:n,impact:o,scrollJumpRequest:i})=>{const a=n||e.viewport,s=r||e.dimensions,l=t||e.current.client.selection,c=Ae(l,e.initial.client.selection),d={offset:c,selection:l,borderBoxCenter:Ce(e.initial.client.borderBoxCenter,c)},u={selection:Ce(d.selection,a.scroll.current),borderBoxCenter:Ce(d.borderBoxCenter,a.scroll.current),offset:Ce(d.offset,a.scroll.diff.value)},p={client:d,page:u};if("COLLECTING"===e.phase)return{...e,dimensions:s,viewport:a,current:p};const g=s.draggables[e.critical.draggable.id],f=o||Jt({pageOffset:u.offset,draggable:g,draggables:s.draggables,droppables:s.droppables,previousImpact:e.impact,viewport:a,afterCritical:e.afterCritical}),m=(({draggable:e,draggables:t,droppables:r,previousImpact:n,impact:o})=>{const i=Kt({previousImpact:n,impact:o,droppables:r}),a=jt(o);if(!a)return i;const s=r[a];if(ot(e,s))return i;if(s.subject.withPlaceholder)return i;const l=Wt(s,e,t);return Xt(i,l)})({draggable:g,impact:f,previousImpact:e.impact,draggables:s.draggables,droppables:s.droppables});return{...e,current:p,dimensions:{draggables:s.draggables,droppables:m},impact:f,viewport:a,scrollJumpRequest:i||null,forceShouldAnimate:!i&&null}};var Zt=({impact:e,viewport:t,draggables:r,destination:n,forceShouldAnimate:o})=>{const i=e.displaced,a=function(e,t){return e.map((e=>t[e]))}(i.all,r),s=ht({afterDragging:a,destination:n,displacedBy:e.displacedBy,viewport:t.frame,forceShouldAnimate:o,last:i});return{...e,displaced:s}},er=({impact:e,draggable:t,droppable:r,draggables:n,viewport:o,afterCritical:i})=>{const a=Pt({impact:e,draggable:t,draggables:n,droppable:r,afterCritical:i});return Nt({pageBorderBoxCenter:a,draggable:t,viewport:o})},tr=({state:e,dimensions:t,viewport:r})=>{"SNAP"!==e.movementMode&&c();const n=e.impact,o=r||e.viewport,i=t||e.dimensions,{draggables:a,droppables:s}=i,l=a[e.critical.draggable.id],d=jt(n);d||c();const u=s[d],p=Zt({impact:n,viewport:o,destination:u,draggables:a}),g=er({impact:p,draggable:l,droppable:u,draggables:a,viewport:o,afterCritical:e.afterCritical});return Qt({impact:p,clientSelection:g,state:e,dimensions:i,viewport:o})},rr=({draggable:e,home:t,draggables:r,viewport:n})=>{const o=Ft(t.axis,e.displaceBy),i=et(t.descriptor.id,r),a=i.indexOf(e);-1===a&&c();const s=i.slice(a+1),l=s.reduce(((e,t)=>(e[t.descriptor.id]=!0,e)),{}),d={inVirtualList:"virtual"===t.descriptor.mode,displacedBy:o,effected:l};var u;return{impact:{displaced:ht({afterDragging:s,destination:t,displacedBy:o,last:null,viewport:n.frame,forceShouldAnimate:!1}),displacedBy:o,at:{type:"REORDER",destination:(u=e.descriptor,{index:u.index,droppableId:u.droppableId})}},afterCritical:d}},nr=({additions:e,updatedDroppables:t,viewport:r})=>{const n=r.scroll.diff.value;return e.map((e=>{const o=e.descriptor.droppableId,i=(e=>{const t=e.frame;return t||c(),t})(t[o]),a=i.scroll.diff.value,s=(({draggable:e,offset:t,initialWindowScroll:r})=>{const n=We(e.client,t),o=$e(n,r);return{...e,placeholder:{...e.placeholder,client:n},client:n,page:o}})({draggable:e,offset:Ce(n,a),initialWindowScroll:r.scroll.initial});return s}))};const or=e=>"SNAP"===e.movementMode,ir=(e,t,r)=>{const n=((e,t)=>({draggables:e.draggables,droppables:Xt(e.droppables,t)}))(e.dimensions,t);return!or(e)||r?Qt({state:e,dimensions:n}):tr({state:e,dimensions:n})};function ar(e){return e.isDragging&&"SNAP"===e.movementMode?{...e,scrollJumpRequest:null}:e}const sr={phase:"IDLE",completed:null,shouldFlush:!1};var lr=(e=sr,t)=>{if("FLUSH"===t.type)return{...sr,shouldFlush:!0};if("INITIAL_PUBLISH"===t.type){"IDLE"!==e.phase&&c();const{critical:r,clientSelection:n,viewport:o,dimensions:i,movementMode:a}=t.payload,s=i.draggables[r.draggable.id],l=i.droppables[r.droppable.id],d={selection:n,borderBoxCenter:s.client.borderBox.center,offset:Se},u={client:d,page:{selection:Ce(d.selection,o.scroll.initial),borderBoxCenter:Ce(d.selection,o.scroll.initial),offset:Ce(d.selection,o.scroll.diff.value)}},p=Qe(i.droppables).every((e=>!e.isFixedOnPage)),{impact:g,afterCritical:f}=rr({draggable:s,home:l,draggables:i.draggables,viewport:o});return{phase:"DRAGGING",isDragging:!0,critical:r,movementMode:a,dimensions:i,initial:u,current:u,isWindowScrollAllowed:p,impact:g,afterCritical:f,onLiftImpact:g,viewport:o,scrollJumpRequest:null,forceShouldAnimate:null}}if("COLLECTION_STARTING"===t.type){if("COLLECTING"===e.phase||"DROP_PENDING"===e.phase)return e;"DRAGGING"!==e.phase&&c();return{...e,phase:"COLLECTING"}}if("PUBLISH_WHILE_DRAGGING"===t.type)return"COLLECTING"!==e.phase&&"DROP_PENDING"!==e.phase&&c(),(({state:e,published:t})=>{const r=t.modified.map((t=>{const r=e.dimensions.droppables[t.droppableId];return Ye(r,t.scroll)})),n={...e.dimensions.droppables,...Xe(r)},o=Ke(nr({additions:t.additions,updatedDroppables:n,viewport:e.viewport})),i={...e.dimensions.draggables,...o};t.removals.forEach((e=>{delete i[e]}));const a={droppables:n,draggables:i},s=jt(e.impact),l=s?a.droppables[s]:null,c=a.draggables[e.critical.draggable.id],d=a.droppables[e.critical.droppable.id],{impact:u,afterCritical:p}=rr({draggable:c,home:d,draggables:i,viewport:e.viewport}),g=l&&l.isCombineEnabled?e.impact:u,f=Jt({pageOffset:e.current.page.offset,draggable:a.draggables[e.critical.draggable.id],draggables:a.draggables,droppables:a.droppables,previousImpact:g,viewport:e.viewport,afterCritical:p}),m={...e,phase:"DRAGGING",impact:f,onLiftImpact:u,dimensions:a,afterCritical:p,forceShouldAnimate:!1};return"COLLECTING"===e.phase?m:{...m,phase:"DROP_PENDING",reason:e.reason,isWaiting:!1}})({state:e,published:t.payload});if("MOVE"===t.type){if("DROP_PENDING"===e.phase)return e;Ht(e)||c();const{client:r}=t.payload;return Oe(r,e.current.client.selection)?e:Qt({state:e,clientSelection:r,impact:or(e)?e.impact:null})}if("UPDATE_DROPPABLE_SCROLL"===t.type){if("DROP_PENDING"===e.phase)return ar(e);if("COLLECTING"===e.phase)return ar(e);Ht(e)||c();const{id:r,newScroll:n}=t.payload,o=e.dimensions.droppables[r];if(!o)return e;const i=Ye(o,n);return ir(e,i,!1)}if("UPDATE_DROPPABLE_IS_ENABLED"===t.type){if("DROP_PENDING"===e.phase)return e;Ht(e)||c();const{id:r,isEnabled:n}=t.payload,o=e.dimensions.droppables[r];o||c(),o.isEnabled===n&&c();const i={...o,isEnabled:n};return ir(e,i,!0)}if("UPDATE_DROPPABLE_IS_COMBINE_ENABLED"===t.type){if("DROP_PENDING"===e.phase)return e;Ht(e)||c();const{id:r,isCombineEnabled:n}=t.payload,o=e.dimensions.droppables[r];o||c(),o.isCombineEnabled===n&&c();const i={...o,isCombineEnabled:n};return ir(e,i,!0)}if("MOVE_BY_WINDOW_SCROLL"===t.type){if("DROP_PENDING"===e.phase||"DROP_ANIMATING"===e.phase)return e;Ht(e)||c(),e.isWindowScrollAllowed||c();const r=t.payload.newScroll;if(Oe(e.viewport.scroll.current,r))return ar(e);const n=Rt(e.viewport,r);return or(e)?tr({state:e,viewport:n}):Qt({state:e,viewport:n})}if("UPDATE_VIEWPORT_MAX_SCROLL"===t.type){if(!Ht(e))return e;const r=t.payload.maxScroll;if(Oe(r,e.viewport.scroll.max))return e;const n={...e.viewport,scroll:{...e.viewport.scroll,max:r}};return{...e,viewport:n}}if("MOVE_UP"===t.type||"MOVE_DOWN"===t.type||"MOVE_LEFT"===t.type||"MOVE_RIGHT"===t.type){if("COLLECTING"===e.phase||"DROP_PENDING"===e.phase)return e;"DRAGGING"!==e.phase&&c();const r=Ut({state:e,type:t.type});return r?Qt({state:e,impact:r.impact,clientSelection:r.clientSelection,scrollJumpRequest:r.scrollJumpRequest}):e}if("DROP_PENDING"===t.type){const r=t.payload.reason;"COLLECTING"!==e.phase&&c();return{...e,phase:"DROP_PENDING",isWaiting:!0,reason:r}}if("DROP_ANIMATE"===t.type){const{completed:r,dropDuration:n,newHomeClientOffset:o}=t.payload;"DRAGGING"!==e.phase&&"DROP_PENDING"!==e.phase&&c();return{phase:"DROP_ANIMATING",completed:r,dropDuration:n,newHomeClientOffset:o,dimensions:e.dimensions}}if("DROP_COMPLETE"===t.type){const{completed:e}=t.payload;return{phase:"IDLE",completed:e,shouldFlush:!1}}return e};function cr(e,t){return e instanceof Object&&"type"in e&&e.type===t}const dr=e=>({type:"LIFT",payload:e}),ur=e=>({type:"PUBLISH_WHILE_DRAGGING",payload:e}),pr=()=>({type:"COLLECTION_STARTING",payload:null}),gr=e=>({type:"UPDATE_DROPPABLE_SCROLL",payload:e}),fr=e=>({type:"UPDATE_DROPPABLE_IS_ENABLED",payload:e}),mr=e=>({type:"UPDATE_DROPPABLE_IS_COMBINE_ENABLED",payload:e}),br=e=>({type:"MOVE",payload:e}),hr=()=>({type:"MOVE_UP",payload:null}),vr=()=>({type:"MOVE_DOWN",payload:null}),yr=()=>({type:"MOVE_RIGHT",payload:null}),xr=()=>({type:"MOVE_LEFT",payload:null}),Ir=()=>({type:"FLUSH",payload:null}),Dr=e=>({type:"DROP_COMPLETE",payload:e}),wr=e=>({type:"DROP",payload:e}),Er=()=>({type:"DROP_ANIMATION_FINISHED",payload:null});const Sr="cubic-bezier(.2,1,.1,1)",Cr={drop:0,combining:.7},Ar={drop:.75},Or={outOfTheWay:.2,minDropTime:.33,maxDropTime:.55},Pr=`${Or.outOfTheWay}s ${"cubic-bezier(0.2, 0, 0, 1)"}`,Rr={fluid:`opacity ${Pr}`,snap:`transform ${Pr}, opacity ${Pr}`,drop:e=>{const t=`${e}s ${Sr}`;return`transform ${t}, opacity ${t}`},outOfTheWay:`transform ${Pr}`,placeholder:`height ${Pr}, width ${Pr}, margin ${Pr}`},Br=e=>Oe(e,Se)?void 0:`translate(${e.x}px, ${e.y}px)`,Nr={moveTo:Br,drop:(e,t)=>{const r=Br(e);if(r)return t?`${r} scale(${Ar.drop})`:r}},{minDropTime:Tr,maxDropTime:Lr}=Or,Mr=Lr-Tr;const Gr=({getState:e,dispatch:t})=>r=>n=>{if(!cr(n,"DROP"))return void r(n);const o=e(),i=n.payload.reason;if("COLLECTING"===o.phase)return void t((e=>({type:"DROP_PENDING",payload:e}))({reason:i}));if("IDLE"===o.phase)return;"DROP_PENDING"===o.phase&&o.isWaiting&&c(),"DRAGGING"!==o.phase&&"DROP_PENDING"!==o.phase&&c();const a=o.critical,s=o.dimensions,l=s.draggables[o.critical.draggable.id],{impact:d,didDropInsideDroppable:u}=(({draggables:e,reason:t,lastImpact:r,home:n,viewport:o,onLiftImpact:i})=>{if(!r.at||"DROP"!==t)return{impact:Zt({draggables:e,impact:i,destination:n,viewport:o,forceShouldAnimate:!0}),didDropInsideDroppable:!1};return"REORDER"===r.at.type?{impact:r,didDropInsideDroppable:!0}:{impact:{...r,displaced:at},didDropInsideDroppable:!0}})({reason:i,lastImpact:o.impact,afterCritical:o.afterCritical,onLiftImpact:o.onLiftImpact,home:o.dimensions.droppables[o.critical.droppable.id],viewport:o.viewport,draggables:o.dimensions.draggables}),p=u?tt(d):null,g=u?rt(d):null,f={index:a.draggable.index,droppableId:a.droppable.id},m={draggableId:l.descriptor.id,type:l.descriptor.type,source:f,reason:i,mode:o.movementMode,destination:p,combine:g},b=(({impact:e,draggable:t,dimensions:r,viewport:n,afterCritical:o})=>{const{draggables:i,droppables:a}=r,s=jt(e),l=s?a[s]:null,c=a[t.descriptor.droppableId],d=er({impact:e,draggable:t,draggables:i,afterCritical:o,droppable:l||c,viewport:n});return Ae(d,t.client.borderBox.center)})({impact:d,draggable:l,dimensions:s,viewport:o.viewport,afterCritical:o.afterCritical}),h={critical:o.critical,afterCritical:o.afterCritical,result:m,impact:d};if(!(!Oe(o.current.client.offset,b)||Boolean(m.combine)))return void t(Dr({completed:h}));const v=(({current:e,destination:t,reason:r})=>{const n=Be(e,t);if(n<=0)return Tr;if(n>=1500)return Lr;const o=Tr+Mr*(n/1500);return Number(("CANCEL"===r?.6*o:o).toFixed(2))})({current:o.current.client.offset,destination:b,reason:i});t((e=>({type:"DROP_ANIMATE",payload:e}))({newHomeClientOffset:b,dropDuration:v,completed:h}))};var _r=function(e){var t=[],r=null,n=function(){for(var n=arguments.length,o=new Array(n),i=0;i<n;i++)o[i]=arguments[i];t=o,r||(r=requestAnimationFrame((function(){r=null,e.apply(void 0,t)})))};return n.cancel=function(){r&&(cancelAnimationFrame(r),r=null)},n},Fr=()=>({x:window.pageXOffset,y:window.pageYOffset});function kr({onWindowScroll:e}){const t=_r((function(){e(Fr())})),r=function(e){return{eventName:"scroll",options:{passive:!0,capture:!1},fn:t=>{t.target!==window&&t.target!==window.document||e()}}}(t);let n=i;function o(){return n!==i}return{start:function(){o()&&c(),n=a(window,[r])},stop:function(){o()||c(),t.cancel(),n(),n=i},isActive:o}}const Wr=e=>{const t=kr({onWindowScroll:t=>{e.dispatch({type:"MOVE_BY_WINDOW_SCROLL",payload:{newScroll:t}})}});return e=>r=>{!t.isActive()&&cr(r,"INITIAL_PUBLISH")&&t.start(),t.isActive()&&(e=>cr(e,"DROP_COMPLETE")||cr(e,"DROP_ANIMATE")||cr(e,"FLUSH"))(r)&&t.stop(),e(r)}};var $r=()=>{const e=[];return{add:t=>{const r=setTimeout((()=>(t=>{const r=e.findIndex((e=>e.timerId===t));-1===r&&c();const[n]=e.splice(r,1);n.callback()})(r))),n={timerId:r,callback:t};e.push(n)},flush:()=>{if(!e.length)return;const t=[...e];e.length=0,t.forEach((e=>{clearTimeout(e.timerId),e.callback()}))}}};const jr=(e,t)=>{t()},Ur=(e,t)=>({draggableId:e.draggable.id,type:e.droppable.type,source:{droppableId:e.droppable.id,index:e.draggable.index},mode:t});function Hr(e,t,r,n){if(!e)return void r(n(t));const o=(e=>{let t=!1,r=!1;const n=setTimeout((()=>{r=!0})),o=o=>{t||r||(t=!0,e(o),clearTimeout(n))};return o.wasCalled=()=>t,o})(r);e(t,{announce:o}),o.wasCalled()||r(n(t))}var Vr=(e,t)=>{const r=((e,t)=>{const r=$r();let n=null;const o=r=>{n||c(),n=null,jr(0,(()=>Hr(e().onDragEnd,r,t,m.onDragEnd)))};return{beforeCapture:(t,r)=>{n&&c(),jr(0,(()=>{const n=e().onBeforeCapture;n&&n({draggableId:t,mode:r})}))},beforeStart:(t,r)=>{n&&c(),jr(0,(()=>{const n=e().onBeforeDragStart;n&&n(Ur(t,r))}))},start:(o,i)=>{n&&c();const a=Ur(o,i);n={mode:i,lastCritical:o,lastLocation:a.source,lastCombine:null},r.add((()=>{jr(0,(()=>Hr(e().onDragStart,a,t,m.onDragStart)))}))},update:(o,i)=>{const a=tt(i),s=rt(i);n||c();const l=!((e,t)=>{if(e===t)return!0;const r=e.draggable.id===t.draggable.id&&e.draggable.droppableId===t.draggable.droppableId&&e.draggable.type===t.draggable.type&&e.draggable.index===t.draggable.index,n=e.droppable.id===t.droppable.id&&e.droppable.type===t.droppable.type;return r&&n})(o,n.lastCritical);l&&(n.lastCritical=o);const d=(p=a,!(null==(u=n.lastLocation)&&null==p||null!=u&&null!=p&&u.droppableId===p.droppableId&&u.index===p.index));var u,p;d&&(n.lastLocation=a);const g=!((e,t)=>null==e&&null==t||null!=e&&null!=t&&e.draggableId===t.draggableId&&e.droppableId===t.droppableId)(n.lastCombine,s);if(g&&(n.lastCombine=s),!l&&!d&&!g)return;const f={...Ur(o,n.mode),combine:s,destination:a};r.add((()=>{jr(0,(()=>Hr(e().onDragUpdate,f,t,m.onDragUpdate)))}))},flush:()=>{n||c(),r.flush()},drop:o,abort:()=>{if(!n)return;const e={...Ur(n.lastCritical,n.mode),combine:null,destination:null,reason:"CANCEL"};o(e)}}})(e,t);return e=>t=>n=>{if(cr(n,"BEFORE_INITIAL_CAPTURE"))return void r.beforeCapture(n.payload.draggableId,n.payload.movementMode);if(cr(n,"INITIAL_PUBLISH")){const e=n.payload.critical;return r.beforeStart(e,n.payload.movementMode),t(n),void r.start(e,n.payload.movementMode)}if(cr(n,"DROP_COMPLETE")){const e=n.payload.completed.result;return r.flush(),t(n),void r.drop(e)}if(t(n),cr(n,"FLUSH"))return void r.abort();const o=e.getState();"DRAGGING"===o.phase&&r.update(o.critical,o.impact)}};const qr=e=>t=>r=>{if(!cr(r,"DROP_ANIMATION_FINISHED"))return void t(r);const n=e.getState();"DROP_ANIMATING"!==n.phase&&c(),e.dispatch(Dr({completed:n.completed}))},zr=e=>{let t=null,r=null;return n=>o=>{if((cr(o,"FLUSH")||cr(o,"DROP_COMPLETE")||cr(o,"DROP_ANIMATION_FINISHED"))&&(r&&(cancelAnimationFrame(r),r=null),t&&(t(),t=null)),n(o),!cr(o,"DROP_ANIMATE"))return;const i={eventName:"scroll",options:{capture:!0,passive:!1,once:!0},fn:function(){"DROP_ANIMATING"===e.getState().phase&&e.dispatch({type:"DROP_ANIMATION_FINISHED",payload:null})}};r=requestAnimationFrame((()=>{r=null,t=a(window,[i])}))}};var Yr=e=>t=>r=>n=>{if((e=>cr(e,"DROP_COMPLETE")||cr(e,"DROP_ANIMATE")||cr(e,"FLUSH"))(n))return e.stop(),void r(n);if(cr(n,"INITIAL_PUBLISH")){r(n);const o=t.getState();return"DRAGGING"!==o.phase&&c(),void e.start(o)}r(n),e.scroll(t.getState())};const Jr=e=>t=>r=>{if(t(r),!cr(r,"PUBLISH_WHILE_DRAGGING"))return;const n=e.getState();"DROP_PENDING"===n.phase&&(n.isWaiting||e.dispatch(wr({reason:n.reason})))},Xr=w;var Kr=({dimensionMarshal:e,focusMarshal:t,styleMarshal:r,getResponders:n,announce:o,autoScroller:i})=>{return x(lr,Xr(function(...e){return t=>(r,n)=>{const o=t(r,n);let i=()=>{throw new Error(b(15))};const a={getState:o.getState,dispatch:(e,...t)=>i(e,...t)},s=e.map((e=>e(a)));return i=w(...s)(o.dispatch),{...o,dispatch:i}}}((a=r,()=>e=>t=>{cr(t,"INITIAL_PUBLISH")&&a.dragging(),cr(t,"DROP_ANIMATE")&&a.dropping(t.payload.completed.result.reason),(cr(t,"FLUSH")||cr(t,"DROP_COMPLETE"))&&a.resting(),e(t)}),(e=>()=>t=>r=>{(cr(r,"DROP_COMPLETE")||cr(r,"FLUSH")||cr(r,"DROP_ANIMATE"))&&e.stopPublishing(),t(r)})(e),(e=>({getState:t,dispatch:r})=>n=>o=>{if(!cr(o,"LIFT"))return void n(o);const{id:i,clientSelection:a,movementMode:s}=o.payload,l=t();"DROP_ANIMATING"===l.phase&&r(Dr({completed:l.completed})),"IDLE"!==t().phase&&c(),r(Ir()),r({type:"BEFORE_INITIAL_CAPTURE",payload:{draggableId:i,movementMode:s}});const d={draggableId:i,scrollOptions:{shouldPublishImmediately:"SNAP"===s}},{critical:u,dimensions:p,viewport:g}=e.startPublishing(d);r({type:"INITIAL_PUBLISH",payload:{critical:u,dimensions:p,clientSelection:a,movementMode:s,viewport:g}})})(e),Gr,qr,zr,Jr,Yr(i),Wr,(e=>{let t=!1;return()=>r=>n=>{if(cr(n,"INITIAL_PUBLISH"))return t=!0,e.tryRecordFocus(n.payload.critical.draggable.id),r(n),void e.tryRestoreFocusRecorded();if(r(n),t){if(cr(n,"FLUSH"))return t=!1,void e.tryRestoreFocusRecorded();if(cr(n,"DROP_COMPLETE")){t=!1;const r=n.payload.completed.result;r.combine&&e.tryShiftRecord(r.draggableId,r.combine.draggableId),e.tryRestoreFocusRecorded()}}}})(t),Vr(n,o))));var a};var Qr=({scrollHeight:e,scrollWidth:t,height:r,width:n})=>{const o=Ae({x:t,y:e},{x:n,y:r});return{x:Math.max(0,o.x),y:Math.max(0,o.y)}},Zr=()=>{const e=document.documentElement;return e||c(),e},en=()=>{const e=Zr();return Qr({scrollHeight:e.scrollHeight,scrollWidth:e.scrollWidth,width:e.clientWidth,height:e.clientHeight})},tn=({critical:e,scrollOptions:t,registry:r})=>{const n=(()=>{const e=Fr(),t=en(),r=e.y,n=e.x,o=Zr(),i=o.clientWidth,a=o.clientHeight;return{frame:Le({top:r,left:n,right:n+i,bottom:r+a}),scroll:{initial:e,current:e,max:t,diff:{value:Se,displacement:Se}}}})(),o=n.scroll.current,i=e.droppable,a=r.droppable.getAllByType(i.type).map((e=>e.callbacks.getDimensionAndWatchScroll(o,t))),s=r.draggable.getAllByType(e.draggable.type).map((e=>e.getDimension(o)));return{dimensions:{draggables:Ke(s),droppables:Xe(a)},critical:e,viewport:n}};function rn(e,t,r){if(r.descriptor.id===t.id)return!1;if(r.descriptor.type!==t.type)return!1;return"virtual"===e.droppable.getById(r.descriptor.droppableId).descriptor.mode}var nn=(e,t)=>{let r=null;const n=function({registry:e,callbacks:t}){let r={additions:{},removals:{},modified:{}},n=null;const o=()=>{n||(t.collectionStarting(),n=requestAnimationFrame((()=>{n=null;const{additions:o,removals:i,modified:a}=r,s=Object.keys(o).map((t=>e.draggable.getById(t).getDimension(Se))).sort(((e,t)=>e.descriptor.index-t.descriptor.index)),l=Object.keys(a).map((t=>({droppableId:t,scroll:e.droppable.getById(t).callbacks.getScrollWhileDragging()}))),c={additions:s,removals:Object.keys(i),modified:l};r={additions:{},removals:{},modified:{}},t.publish(c)})))};return{add:e=>{const t=e.descriptor.id;r.additions[t]=e,r.modified[e.descriptor.droppableId]=!0,r.removals[t]&&delete r.removals[t],o()},remove:e=>{const t=e.descriptor;r.removals[t.id]=!0,r.modified[t.droppableId]=!0,r.additions[t.id]&&delete r.additions[t.id],o()},stop:()=>{n&&(cancelAnimationFrame(n),n=null,r={additions:{},removals:{},modified:{}})}}}({callbacks:{publish:t.publishWhileDragging,collectionStarting:t.collectionStarting},registry:e}),o=t=>{r||c();const o=r.critical.draggable;"ADDITION"===t.type&&rn(e,o,t.value)&&n.add(t.value),"REMOVAL"===t.type&&rn(e,o,t.value)&&n.remove(t.value)},i={updateDroppableIsEnabled:(n,o)=>{e.droppable.exists(n)||c(),r&&t.updateDroppableIsEnabled({id:n,isEnabled:o})},updateDroppableIsCombineEnabled:(n,o)=>{r&&(e.droppable.exists(n)||c(),t.updateDroppableIsCombineEnabled({id:n,isCombineEnabled:o}))},scrollDroppable:(t,n)=>{r&&e.droppable.getById(t).callbacks.scroll(n)},updateDroppableScroll:(n,o)=>{r&&(e.droppable.exists(n)||c(),t.updateDroppableScroll({id:n,newScroll:o}))},startPublishing:t=>{r&&c();const n=e.draggable.getById(t.draggableId),i=e.droppable.getById(n.descriptor.droppableId),a={draggable:n.descriptor,droppable:i.descriptor},s=e.subscribe(o);return r={critical:a,unsubscribe:s},tn({critical:a,registry:e,scrollOptions:t.scrollOptions})},stopPublishing:()=>{if(!r)return;n.stop();const t=r.critical.droppable;e.droppable.getAllByType(t.type).forEach((e=>e.callbacks.dragStopped())),r.unsubscribe(),r=null}};return i},on=(e,t)=>"IDLE"===e.phase||"DROP_ANIMATING"===e.phase&&(e.completed.result.draggableId!==t&&"DROP"===e.completed.result.reason),an=e=>{window.scrollBy(e.x,e.y)};const sn=Je((e=>Qe(e).filter((e=>!!e.isEnabled&&!!e.frame))));var ln=({center:e,destination:t,droppables:r})=>{if(t){const e=r[t];return e.frame?e:null}const n=((e,t)=>{const r=sn(t).find((t=>(t.frame||c(),Vt(t.frame.pageMarginBox)(e))))||null;return r})(e,r);return n};const cn={startFromPercentage:.25,maxScrollAtPercentage:.05,maxPixelScroll:28,ease:e=>e**2,durationDampening:{stopDampeningAt:1200,accelerateAt:360},disabled:!1};var dn=({startOfRange:e,endOfRange:t,current:r})=>{const n=t-e;if(0===n)return 0;return(r-e)/n},un=({distanceToEdge:e,thresholds:t,dragStartTime:r,shouldUseTimeDampening:n,getAutoScrollerOptions:o})=>{const i=((e,t,r=(()=>cn))=>{const n=r();if(e>t.startScrollingFrom)return 0;if(e<=t.maxScrollValueAt)return n.maxPixelScroll;if(e===t.startScrollingFrom)return 1;const o=1-dn({startOfRange:t.maxScrollValueAt,endOfRange:t.startScrollingFrom,current:e}),i=n.maxPixelScroll*n.ease(o);return Math.ceil(i)})(e,t,o);return 0===i?0:n?Math.max(((e,t,r)=>{const n=r(),o=n.durationDampening.accelerateAt,i=n.durationDampening.stopDampeningAt,a=t,s=i,l=Date.now()-a;if(l>=i)return e;if(l<o)return 1;const c=dn({startOfRange:o,endOfRange:s,current:l}),d=e*n.ease(c);return Math.ceil(d)})(i,r,o),1):i},pn=({container:e,distanceToEdges:t,dragStartTime:r,axis:n,shouldUseTimeDampening:o,getAutoScrollerOptions:i})=>{const a=((e,t,r=(()=>cn))=>{const n=r();return{startScrollingFrom:e[t.size]*n.startFromPercentage,maxScrollValueAt:e[t.size]*n.maxScrollAtPercentage}})(e,n,i);return t[n.end]<t[n.start]?un({distanceToEdge:t[n.end],thresholds:a,dragStartTime:r,shouldUseTimeDampening:o,getAutoScrollerOptions:i}):-1*un({distanceToEdge:t[n.start],thresholds:a,dragStartTime:r,shouldUseTimeDampening:o,getAutoScrollerOptions:i})};const gn=Te((e=>0===e?0:e));var fn=({dragStartTime:e,container:t,subject:r,center:n,shouldUseTimeDampening:o,getAutoScrollerOptions:i})=>{const a={top:n.y-t.top,right:t.right-n.x,bottom:t.bottom-n.y,left:n.x-t.left},s=pn({container:t,distanceToEdges:a,dragStartTime:e,axis:ut,shouldUseTimeDampening:o,getAutoScrollerOptions:i}),l=pn({container:t,distanceToEdges:a,dragStartTime:e,axis:pt,shouldUseTimeDampening:o,getAutoScrollerOptions:i}),c=gn({x:l,y:s});if(Oe(c,Se))return null;const d=(({container:e,subject:t,proposedScroll:r})=>{const n=t.height>e.height,o=t.width>e.width;return o||n?o&&n?null:{x:o?0:r.x,y:n?0:r.y}:r})({container:t,subject:r,proposedScroll:c});return d?Oe(d,Se)?null:d:null};const mn=Te((e=>0===e?0:e>0?1:-1)),bn=(()=>{const e=(e,t)=>e<0?e:e>t?e-t:0;return({current:t,max:r,change:n})=>{const o=Ce(t,n),i={x:e(o.x,r.x),y:e(o.y,r.y)};return Oe(i,Se)?null:i}})(),hn=({max:e,current:t,change:r})=>{const n={x:Math.max(t.x,e.x),y:Math.max(t.y,e.y)},o=mn(r),i=bn({max:n,current:t,change:o});return!i||(0!==o.x&&0===i.x||0!==o.y&&0===i.y)},vn=(e,t)=>hn({current:e.scroll.current,max:e.scroll.max,change:t}),yn=(e,t)=>{const r=e.frame;return!!r&&hn({current:r.scroll.current,max:r.scroll.max,change:t})};var xn=({state:e,dragStartTime:t,shouldUseTimeDampening:r,scrollWindow:n,scrollDroppable:o,getAutoScrollerOptions:i})=>{const a=e.current.page.borderBoxCenter,s=e.dimensions.draggables[e.critical.draggable.id].page.marginBox;if(e.isWindowScrollAllowed){const o=(({viewport:e,subject:t,center:r,dragStartTime:n,shouldUseTimeDampening:o,getAutoScrollerOptions:i})=>{const a=fn({dragStartTime:n,container:e.frame,subject:t,center:r,shouldUseTimeDampening:o,getAutoScrollerOptions:i});return a&&vn(e,a)?a:null})({dragStartTime:t,viewport:e.viewport,subject:s,center:a,shouldUseTimeDampening:r,getAutoScrollerOptions:i});if(o)return void n(o)}const l=ln({center:a,destination:jt(e.impact),droppables:e.dimensions.droppables});if(!l)return;const c=(({droppable:e,subject:t,center:r,dragStartTime:n,shouldUseTimeDampening:o,getAutoScrollerOptions:i})=>{const a=e.frame;if(!a)return null;const s=fn({dragStartTime:n,container:a.pageMarginBox,subject:t,center:r,shouldUseTimeDampening:o,getAutoScrollerOptions:i});return s&&yn(e,s)?s:null})({dragStartTime:t,droppable:l,subject:s,center:a,shouldUseTimeDampening:r,getAutoScrollerOptions:i});c&&o(l.descriptor.id,c)},In=({scrollWindow:e,scrollDroppable:t,getAutoScrollerOptions:r=(()=>cn)})=>{const n=_r(e),o=_r(t);let i=null;const a=e=>{i||c();const{shouldUseTimeDampening:t,dragStartTime:a}=i;xn({state:e,scrollWindow:n,scrollDroppable:o,dragStartTime:a,shouldUseTimeDampening:t,getAutoScrollerOptions:r})};return{start:e=>{i&&c();const t=Date.now();let n=!1;const o=()=>{n=!0};xn({state:e,dragStartTime:0,shouldUseTimeDampening:!1,scrollWindow:o,scrollDroppable:o,getAutoScrollerOptions:r}),i={dragStartTime:t,shouldUseTimeDampening:n},n&&a(e)},stop:()=>{i&&(n.cancel(),o.cancel(),i=null)},scroll:a}},Dn=({move:e,scrollDroppable:t,scrollWindow:r})=>{const n=(e,r)=>{if(!yn(e,r))return r;const n=((e,t)=>{const r=e.frame;return r&&yn(e,t)?bn({current:r.scroll.current,max:r.scroll.max,change:t}):null})(e,r);if(!n)return t(e.descriptor.id,r),null;const o=Ae(r,n);t(e.descriptor.id,o);return Ae(r,o)},o=(e,t,n)=>{if(!e)return n;if(!vn(t,n))return n;const o=((e,t)=>{if(!vn(e,t))return null;const r=e.scroll.max,n=e.scroll.current;return bn({current:n,max:r,change:t})})(t,n);if(!o)return r(n),null;const i=Ae(n,o);r(i);return Ae(n,i)};return t=>{const r=t.scrollJumpRequest;if(!r)return;const i=jt(t.impact);i||c();const a=n(t.dimensions.droppables[i],r);if(!a)return;const s=t.viewport,l=o(t.isWindowScrollAllowed,s,a);l&&((t,r)=>{const n=Ce(t.current.client.selection,r);e({client:n})})(t,l)}},wn=({scrollDroppable:e,scrollWindow:t,move:r,getAutoScrollerOptions:n})=>{const o=In({scrollWindow:t,scrollDroppable:e,getAutoScrollerOptions:n}),i=Dn({move:r,scrollWindow:t,scrollDroppable:e});return{scroll:e=>{n().disabled||"DRAGGING"!==e.phase||("FLUID"!==e.movementMode?e.scrollJumpRequest&&i(e):o.scroll(e))},start:o.start,stop:o.stop}};const En="data-rfd",Sn=(()=>{const e=`${En}-drag-handle`;return{base:e,draggableId:`${e}-draggable-id`,contextId:`${e}-context-id`}})(),Cn=(()=>{const e=`${En}-draggable`;return{base:e,contextId:`${e}-context-id`,id:`${e}-id`}})(),An=(()=>{const e=`${En}-droppable`;return{base:e,contextId:`${e}-context-id`,id:`${e}-id`}})(),On={contextId:`${En}-scroll-container-context-id`},Pn=(e,t)=>e.map((e=>{const r=e.styles[t];return r?`${e.selector} { ${r} }`:""})).join(" ");var Rn=e=>{const t=(r=e,e=>`[${e}="${r}"]`);var r;const n=(()=>{const e="\n      cursor: -webkit-grab;\n      cursor: grab;\n    ";return{selector:t(Sn.contextId),styles:{always:"\n          -webkit-touch-callout: none;\n          -webkit-tap-highlight-color: rgba(0,0,0,0);\n          touch-action: manipulation;\n        ",resting:e,dragging:"pointer-events: none;",dropAnimating:e}}})(),o=[(()=>{const e=`\n      transition: ${Rr.outOfTheWay};\n    `;return{selector:t(Cn.contextId),styles:{dragging:e,dropAnimating:e,userCancel:e}}})(),n,{selector:t(An.contextId),styles:{always:"overflow-anchor: none;"}},{selector:"body",styles:{dragging:"\n        cursor: grabbing;\n        cursor: -webkit-grabbing;\n        user-select: none;\n        -webkit-user-select: none;\n        -moz-user-select: none;\n        -ms-user-select: none;\n        overflow-anchor: none;\n      "}}];return{always:Pn(o,"always"),resting:Pn(o,"resting"),dragging:Pn(o,"dragging"),dropAnimating:Pn(o,"dropAnimating"),userCancel:Pn(o,"userCancel")}};const Bn="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?t.useLayoutEffect:t.useEffect,Nn=()=>{const e=document.querySelector("head");return e||c(),e},Tn=e=>{const t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.type="text/css",t};function Ln(e,t){return Array.from(e.querySelectorAll(t))}var Mn=e=>e&&e.ownerDocument&&e.ownerDocument.defaultView?e.ownerDocument.defaultView:window;function Gn(e){return e instanceof Mn(e).HTMLElement}function _n(e){const r=t.useRef({}),n=t.useRef(null),o=t.useRef(null),i=t.useRef(!1),a=Ee((function(e,t){const n={id:e,focus:t};return r.current[e]=n,function(){const t=r.current;t[e]!==n&&delete t[e]}}),[]),s=Ee((function(t){const r=function(e,t){const r=`[${Sn.contextId}="${e}"]`,n=Ln(document,r);if(!n.length)return null;const o=n.find((e=>e.getAttribute(Sn.draggableId)===t));return o&&Gn(o)?o:null}(e,t);r&&r!==document.activeElement&&r.focus()}),[e]),l=Ee((function(e,t){n.current===e&&(n.current=t)}),[]),c=Ee((function(){o.current||i.current&&(o.current=requestAnimationFrame((()=>{o.current=null;const e=n.current;e&&s(e)})))}),[s]),d=Ee((function(e){n.current=null;const t=document.activeElement;t&&t.getAttribute(Sn.draggableId)===e&&(n.current=e)}),[]);Bn((()=>(i.current=!0,function(){i.current=!1;const e=o.current;e&&cancelAnimationFrame(e)})),[]);return we((()=>({register:a,tryRecordFocus:d,tryRestoreFocusRecorded:c,tryShiftRecord:l})),[a,d,c,l])}function Fn(){const e={draggables:{},droppables:{}},t=[];function r(e){t.length&&t.forEach((t=>t(e)))}function n(t){return e.draggables[t]||null}function o(t){return e.droppables[t]||null}return{draggable:{register:t=>{e.draggables[t.descriptor.id]=t,r({type:"ADDITION",value:t})},update:(t,r)=>{const n=e.draggables[r.descriptor.id];n&&n.uniqueId===t.uniqueId&&(delete e.draggables[r.descriptor.id],e.draggables[t.descriptor.id]=t)},unregister:t=>{const o=t.descriptor.id,i=n(o);i&&t.uniqueId===i.uniqueId&&(delete e.draggables[o],e.droppables[t.descriptor.droppableId]&&r({type:"REMOVAL",value:t}))},getById:function(e){const t=n(e);return t||c(),t},findById:n,exists:e=>Boolean(n(e)),getAllByType:t=>Object.values(e.draggables).filter((e=>e.descriptor.type===t))},droppable:{register:t=>{e.droppables[t.descriptor.id]=t},unregister:t=>{const r=o(t.descriptor.id);r&&t.uniqueId===r.uniqueId&&delete e.droppables[t.descriptor.id]},getById:function(e){const t=o(e);return t||c(),t},findById:o,exists:e=>Boolean(o(e)),getAllByType:t=>Object.values(e.droppables).filter((e=>e.descriptor.type===t))},subscribe:function(e){return t.push(e),function(){const r=t.indexOf(e);-1!==r&&t.splice(r,1)}},clean:function(){e.draggables={},e.droppables={},t.length=0}}}var kn=t.createContext(null);function Wn(){return Wn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Wn.apply(null,arguments)}var $n=()=>{const e=document.body;return e||c(),e};const jn={position:"absolute",width:"1px",height:"1px",margin:"-1px",border:"0",padding:"0",overflow:"hidden",clip:"rect(0 0 0 0)","clip-path":"inset(100%)"},Un=e=>`rfd-announcement-${e}`;const Hn={separator:"::"};function Vn(e,r=Hn){const n=t.useId();return we((()=>`${e}${r.separator}${n}`),[r.separator,e,n])}var qn=t.createContext(null);function zn(e){const r=t.useRef(e);return t.useEffect((()=>{r.current=e})),r}function Yn(e){return"IDLE"!==e.phase&&"DROP_ANIMATING"!==e.phase&&e.isDragging}const Jn=27,Xn=32,Kn=37,Qn=38,Zn=39,eo=40,to={13:!0,9:!0};var ro=e=>{to[e.keyCode]&&e.preventDefault()};const no=(()=>{const e="visibilitychange";if("undefined"==typeof document)return e;return[e,`ms${e}`,`webkit${e}`,`moz${e}`,`o${e}`].find((e=>`on${e}`in document))||e})(),oo=0,io=5;const ao={type:"IDLE"};function so({cancel:e,completed:t,getPhase:r,setPhase:n}){return[{eventName:"mousemove",fn:e=>{const{button:t,clientX:o,clientY:i}=e;if(t!==oo)return;const a={x:o,y:i},s=r();if("DRAGGING"===s.type)return e.preventDefault(),void s.actions.move(a);"PENDING"!==s.type&&c();const l=s.point;if(d=l,u=a,!(Math.abs(u.x-d.x)>=io||Math.abs(u.y-d.y)>=io))return;var d,u;e.preventDefault();const p=s.actions.fluidLift(a);n({type:"DRAGGING",actions:p})}},{eventName:"mouseup",fn:n=>{const o=r();"DRAGGING"===o.type?(n.preventDefault(),o.actions.drop({shouldBlockNextClick:!0}),t()):e()}},{eventName:"mousedown",fn:t=>{"DRAGGING"===r().type&&t.preventDefault(),e()}},{eventName:"keydown",fn:t=>{if("PENDING"!==r().type)return t.keyCode===Jn?(t.preventDefault(),void e()):void ro(t);e()}},{eventName:"resize",fn:e},{eventName:"scroll",options:{passive:!0,capture:!1},fn:()=>{"PENDING"===r().type&&e()}},{eventName:"webkitmouseforcedown",fn:t=>{const n=r();"IDLE"===n.type&&c(),n.actions.shouldRespectForcePress()?e():t.preventDefault()}},{eventName:no,fn:e}]}function lo(e){const r=t.useRef(ao),n=t.useRef(i),o=we((()=>({eventName:"mousedown",fn:function(t){if(t.defaultPrevented)return;if(t.button!==oo)return;if(t.ctrlKey||t.metaKey||t.shiftKey||t.altKey)return;const r=e.findClosestDraggableId(t);if(!r)return;const o=e.tryGetLock(r,d,{sourceEvent:t});if(!o)return;t.preventDefault();const i={x:t.clientX,y:t.clientY};n.current(),g(o,i)}})),[e]),s=we((()=>({eventName:"webkitmouseforcewillbegin",fn:t=>{if(t.defaultPrevented)return;const r=e.findClosestDraggableId(t);if(!r)return;const n=e.findOptionsForDraggable(r);n&&(n.shouldRespectForcePress||e.canGetLock(r)&&t.preventDefault())}})),[e]),l=Ee((function(){n.current=a(window,[s,o],{passive:!1,capture:!0})}),[s,o]),d=Ee((()=>{"IDLE"!==r.current.type&&(r.current=ao,n.current(),l())}),[l]),u=Ee((()=>{const e=r.current;d(),"DRAGGING"===e.type&&e.actions.cancel({shouldBlockNextClick:!0}),"PENDING"===e.type&&e.actions.abort()}),[d]),p=Ee((function(){const e=so({cancel:u,completed:d,getPhase:()=>r.current,setPhase:e=>{r.current=e}});n.current=a(window,e,{capture:!0,passive:!1})}),[u,d]),g=Ee((function(e,t){"IDLE"!==r.current.type&&c(),r.current={type:"PENDING",point:t,actions:e},p()}),[p]);Bn((function(){return l(),function(){n.current()}}),[l])}function co(){}const uo={34:!0,33:!0,36:!0,35:!0};function po(e,t){function r(){t(),e.cancel()}return[{eventName:"keydown",fn:n=>n.keyCode===Jn?(n.preventDefault(),void r()):n.keyCode===Xn?(n.preventDefault(),t(),void e.drop()):n.keyCode===eo?(n.preventDefault(),void e.moveDown()):n.keyCode===Qn?(n.preventDefault(),void e.moveUp()):n.keyCode===Zn?(n.preventDefault(),void e.moveRight()):n.keyCode===Kn?(n.preventDefault(),void e.moveLeft()):void(uo[n.keyCode]?n.preventDefault():ro(n))},{eventName:"mousedown",fn:r},{eventName:"mouseup",fn:r},{eventName:"click",fn:r},{eventName:"touchstart",fn:r},{eventName:"resize",fn:r},{eventName:"wheel",fn:r,options:{passive:!0}},{eventName:no,fn:r}]}function go(e){const r=t.useRef(co),n=we((()=>({eventName:"keydown",fn:function(t){if(t.defaultPrevented)return;if(t.keyCode!==Xn)return;const n=e.findClosestDraggableId(t);if(!n)return;const i=e.tryGetLock(n,d,{sourceEvent:t});if(!i)return;t.preventDefault();let s=!0;const l=i.snapLift();function d(){s||c(),s=!1,r.current(),o()}r.current(),r.current=a(window,po(l,d),{capture:!0,passive:!1})}})),[e]),o=Ee((function(){r.current=a(window,[n],{passive:!1,capture:!0})}),[n]);Bn((function(){return o(),function(){r.current()}}),[o])}const fo={type:"IDLE"},mo=.15;function bo(e){const r=t.useRef(fo),n=t.useRef(i),o=Ee((function(){return r.current}),[]),s=Ee((function(e){r.current=e}),[]),l=we((()=>({eventName:"touchstart",fn:function(t){if(t.defaultPrevented)return;const r=e.findClosestDraggableId(t);if(!r)return;const o=e.tryGetLock(r,u,{sourceEvent:t});if(!o)return;const i=t.touches[0],{clientX:a,clientY:s}=i,l={x:a,y:s};n.current(),m(o,l)}})),[e]),d=Ee((function(){n.current=a(window,[l],{capture:!0,passive:!1})}),[l]),u=Ee((()=>{const e=r.current;"IDLE"!==e.type&&("PENDING"===e.type&&clearTimeout(e.longPressTimerId),s(fo),n.current(),d())}),[d,s]),p=Ee((()=>{const e=r.current;u(),"DRAGGING"===e.type&&e.actions.cancel({shouldBlockNextClick:!0}),"PENDING"===e.type&&e.actions.abort()}),[u]),g=Ee((function(){const e={capture:!0,passive:!1},t={cancel:p,completed:u,getPhase:o},r=a(window,function({cancel:e,completed:t,getPhase:r}){return[{eventName:"touchmove",options:{capture:!1},fn:t=>{const n=r();if("DRAGGING"!==n.type)return void e();n.hasMoved=!0;const{clientX:o,clientY:i}=t.touches[0],a={x:o,y:i};t.preventDefault(),n.actions.move(a)}},{eventName:"touchend",fn:n=>{const o=r();"DRAGGING"===o.type?(n.preventDefault(),o.actions.drop({shouldBlockNextClick:!0}),t()):e()}},{eventName:"touchcancel",fn:t=>{"DRAGGING"===r().type?(t.preventDefault(),e()):e()}},{eventName:"touchforcechange",fn:t=>{const n=r();"IDLE"===n.type&&c();const o=t.touches[0];if(!o)return;if(!(o.force>=mo))return;const i=n.actions.shouldRespectForcePress();if("PENDING"!==n.type)return i?n.hasMoved?void t.preventDefault():void e():void t.preventDefault();i&&e()}},{eventName:no,fn:e}]}(t),e),i=a(window,function({cancel:e,getPhase:t}){return[{eventName:"orientationchange",fn:e},{eventName:"resize",fn:e},{eventName:"contextmenu",fn:e=>{e.preventDefault()}},{eventName:"keydown",fn:r=>{"DRAGGING"===t().type?(r.keyCode===Jn&&r.preventDefault(),e()):e()}},{eventName:no,fn:e}]}(t),e);n.current=function(){r(),i()}}),[p,o,u]),f=Ee((function(){const e=o();"PENDING"!==e.type&&c();const t=e.actions.fluidLift(e.point);s({type:"DRAGGING",actions:t,hasMoved:!1})}),[o,s]),m=Ee((function(e,t){"IDLE"!==o().type&&c();const r=setTimeout(f,120);s({type:"PENDING",point:t,actions:e,longPressTimerId:r}),g()}),[g,o,s,f]);Bn((function(){return d(),function(){n.current();const e=o();"PENDING"===e.type&&(clearTimeout(e.longPressTimerId),s(fo))}}),[o,d,s]),Bn((function(){return a(window,[{eventName:"touchmove",fn:()=>{},options:{capture:!1,passive:!1}}])}),[])}const ho=["input","button","textarea","select","option","optgroup","video","audio"];function vo(e,t){if(null==t)return!1;if(ho.includes(t.tagName.toLowerCase()))return!0;const r=t.getAttribute("contenteditable");return"true"===r||""===r||t!==e&&vo(e,t.parentElement)}function yo(e,t){const r=t.target;return!!Gn(r)&&vo(e,r)}var xo=e=>Le(e.getBoundingClientRect()).center;const Io=(()=>{const e="matches";if("undefined"==typeof document)return e;return[e,"msMatchesSelector","webkitMatchesSelector"].find((e=>e in Element.prototype))||e})();function Do(e,t){return null==e?null:e[Io](t)?e:Do(e.parentElement,t)}function wo(e,t){return e.closest?e.closest(t):Do(e,t)}function Eo(e,t){const r=t.target;if(!((n=r)instanceof Mn(n).Element))return null;var n;const o=function(e){return`[${Sn.contextId}="${e}"]`}(e),i=wo(r,o);return i&&Gn(i)?i:null}function So(e){e.preventDefault()}function Co({expected:e,phase:t,isLockActive:r,shouldWarn:n}){return!!r()&&e===t}function Ao({lockAPI:e,store:t,registry:r,draggableId:n}){if(e.isClaimed())return!1;const o=r.draggable.findById(n);return!!o&&(!!o.options.isEnabled&&!!on(t.getState(),n))}function Oo({lockAPI:e,contextId:t,store:r,registry:n,draggableId:o,forceSensorStop:s,sourceEvent:l}){if(!Ao({lockAPI:e,store:r,registry:n,draggableId:o}))return null;const d=n.draggable.getById(o),u=function(e,t){const r=`[${Cn.contextId}="${e}"]`,n=Ln(document,r).find((e=>e.getAttribute(Cn.id)===t));return n&&Gn(n)?n:null}(t,d.descriptor.id);if(!u)return null;if(l&&!d.options.canDragInteractiveElements&&yo(u,l))return null;const p=e.claim(s||i);let g="PRE_DRAG";function f(){return d.options.shouldRespectForcePress}function m(){return e.isActive(p)}const b=function(e,t){Co({expected:e,phase:g,isLockActive:m,shouldWarn:!0})&&r.dispatch(t())}.bind(null,"DRAGGING");function h(t){function n(){e.release(),g="COMPLETED"}function o(e,o={shouldBlockNextClick:!1}){if(t.cleanup(),o.shouldBlockNextClick){const e=a(window,[{eventName:"click",fn:So,options:{once:!0,passive:!1,capture:!0}}]);setTimeout(e)}n(),r.dispatch(wr({reason:e}))}return"PRE_DRAG"!==g&&(n(),c()),r.dispatch(dr(t.liftActionArgs)),g="DRAGGING",{isActive:()=>Co({expected:"DRAGGING",phase:g,isLockActive:m,shouldWarn:!1}),shouldRespectForcePress:f,drop:e=>o("DROP",e),cancel:e=>o("CANCEL",e),...t.actions}}return{isActive:()=>Co({expected:"PRE_DRAG",phase:g,isLockActive:m,shouldWarn:!1}),shouldRespectForcePress:f,fluidLift:function(e){const t=_r((e=>{b((()=>br({client:e})))}));return{...h({liftActionArgs:{id:o,clientSelection:e,movementMode:"FLUID"},cleanup:()=>t.cancel(),actions:{move:t}}),move:t}},snapLift:function(){const e={moveUp:()=>b(hr),moveRight:()=>b(yr),moveDown:()=>b(vr),moveLeft:()=>b(xr)};return h({liftActionArgs:{id:o,clientSelection:xo(u),movementMode:"SNAP"},cleanup:i,actions:e})},abort:function(){Co({expected:"PRE_DRAG",phase:g,isLockActive:m,shouldWarn:!0})&&e.release()}}}const Po=[lo,go,bo];function Ro({contextId:e,store:r,registry:n,customSensors:o,enableDefaultSensors:i}){const a=[...i?Po:[],...o||[]],s=t.useState((()=>function(){let e=null;function t(){e||c(),e=null}return{isClaimed:function(){return Boolean(e)},isActive:function(t){return t===e},claim:function(t){e&&c();const r={abandon:t};return e=r,r},release:t,tryAbandon:function(){e&&(e.abandon(),t())}}}()))[0],l=Ee((function(e,t){Yn(e)&&!Yn(t)&&s.tryAbandon()}),[s]);Bn((function(){let e=r.getState();return r.subscribe((()=>{const t=r.getState();l(e,t),e=t}))}),[s,r,l]),Bn((()=>s.tryAbandon),[s.tryAbandon]);const d=Ee((e=>Ao({lockAPI:s,registry:n,store:r,draggableId:e})),[s,n,r]),u=Ee(((t,o,i)=>Oo({lockAPI:s,registry:n,contextId:e,store:r,draggableId:t,forceSensorStop:o||null,sourceEvent:i&&i.sourceEvent?i.sourceEvent:null})),[e,s,n,r]),p=Ee((t=>function(e,t){const r=Eo(e,t);return r?r.getAttribute(Sn.draggableId):null}(e,t)),[e]),g=Ee((e=>{const t=n.draggable.findById(e);return t?t.options:null}),[n.draggable]),f=Ee((function(){s.isClaimed()&&(s.tryAbandon(),"IDLE"!==r.getState().phase&&r.dispatch(Ir()))}),[s,r]),m=Ee((()=>s.isClaimed()),[s]),b=we((()=>({canGetLock:d,tryGetLock:u,findClosestDraggableId:p,findOptionsForDraggable:g,tryReleaseLock:f,isLockClaimed:m})),[d,u,p,g,f,m]);for(let e=0;e<a.length;e++)a[e](b)}const Bo=e=>({onBeforeCapture:t=>{r.flushSync((()=>{e.onBeforeCapture&&e.onBeforeCapture(t)}))},onBeforeDragStart:e.onBeforeDragStart,onDragStart:e.onDragStart,onDragEnd:e.onDragEnd,onDragUpdate:e.onDragUpdate}),No=e=>({...cn,...e.autoScrollerOptions,durationDampening:{...cn.durationDampening,...e.autoScrollerOptions}});function To(e){return e.current||c(),e.current}function Lo(e){const{contextId:r,setCallbacks:n,sensors:o,nonce:i,dragHandleUsageInstructions:a}=e,s=t.useRef(null),l=zn(e),d=Ee((()=>Bo(l.current)),[l]),u=Ee((()=>No(l.current)),[l]),p=function(e){const r=we((()=>Un(e)),[e]),n=t.useRef(null);return t.useEffect((function(){const e=document.createElement("div");return n.current=e,e.id=r,e.setAttribute("aria-live","assertive"),e.setAttribute("aria-atomic","true"),Wn(e.style,jn),$n().appendChild(e),function(){setTimeout((function(){const t=$n();t.contains(e)&&t.removeChild(e),e===n.current&&(n.current=null)}))}}),[r]),Ee((e=>{const t=n.current;t&&(t.textContent=e)}),[])}(r),g=function({contextId:e,text:r}){const n=Vn("hidden-text",{separator:"-"}),o=we((()=>function({contextId:e,uniqueId:t}){return`rfd-hidden-text-${e}-${t}`}({contextId:e,uniqueId:n})),[n,e]);return t.useEffect((function(){const e=document.createElement("div");return e.id=o,e.textContent=r,e.style.display="none",$n().appendChild(e),function(){const t=$n();t.contains(e)&&t.removeChild(e)}}),[o,r]),o}({contextId:r,text:a}),f=function(e,r){const n=we((()=>Rn(e)),[e]),o=t.useRef(null),i=t.useRef(null),a=Ee(Je((e=>{const t=i.current;t||c(),t.textContent=e})),[]),s=Ee((e=>{const t=o.current;t||c(),t.textContent=e}),[]);Bn((()=>{(o.current||i.current)&&c();const t=Tn(r),l=Tn(r);return o.current=t,i.current=l,t.setAttribute(`${En}-always`,e),l.setAttribute(`${En}-dynamic`,e),Nn().appendChild(t),Nn().appendChild(l),s(n.always),a(n.resting),()=>{const e=e=>{const t=e.current;t||c(),Nn().removeChild(t),e.current=null};e(o),e(i)}}),[r,s,a,n.always,n.resting,e]);const l=Ee((()=>a(n.dragging)),[a,n.dragging]),d=Ee((e=>{a("DROP"!==e?n.userCancel:n.dropAnimating)}),[a,n.dropAnimating,n.userCancel]),u=Ee((()=>{i.current&&a(n.resting)}),[a,n.resting]);return we((()=>({dragging:l,dropping:d,resting:u})),[l,d,u])}(r,i),m=Ee((e=>{To(s).dispatch(e)}),[]),b=we((()=>D({publishWhileDragging:ur,updateDroppableScroll:gr,updateDroppableIsEnabled:fr,updateDroppableIsCombineEnabled:mr,collectionStarting:pr},m)),[m]),h=function(){const e=we(Fn,[]);return t.useEffect((()=>function(){e.clean()}),[e]),e}(),v=we((()=>nn(h,b)),[h,b]),y=we((()=>wn({scrollWindow:an,scrollDroppable:v.scrollDroppable,getAutoScrollerOptions:u,...D({move:br},m)})),[v.scrollDroppable,m,u]),x=_n(r),I=we((()=>Kr({announce:p,autoScroller:y,dimensionMarshal:v,focusMarshal:x,getResponders:d,styleMarshal:f})),[p,y,v,x,d,f]);s.current=I;const w=Ee((()=>{const e=To(s);"IDLE"!==e.getState().phase&&e.dispatch(Ir())}),[]),E=Ee((()=>{const e=To(s).getState();return"DROP_ANIMATING"===e.phase||"IDLE"!==e.phase&&e.isDragging}),[]);n(we((()=>({isDragging:E,tryAbort:w})),[E,w]));const S=Ee((e=>on(To(s).getState(),e)),[]),C=Ee((()=>Ht(To(s).getState())),[]),A=we((()=>({marshal:v,focus:x,contextId:r,canLift:S,isMovementAllowed:C,dragHandleUsageInstructionsId:g,registry:h})),[r,v,g,x,S,C,h]);return Ro({contextId:r,store:I,registry:h,customSensors:o||null,enableDefaultSensors:!1!==e.enableDefaultSensors}),t.useEffect((()=>w),[w]),t.createElement(qn.Provider,{value:A},t.createElement(Ie,{context:kn,store:I},e.children))}const Mo={dragging:5e3,dropAnimating:4500},Go=(e,t)=>t?Rr.drop(t.duration):e?Rr.snap:Rr.fluid,_o=(e,t)=>{if(e)return t?Cr.drop:Cr.combining},Fo=e=>null!=e.forceShouldAnimate?e.forceShouldAnimate:"SNAP"===e.mode;function ko(e){return"DRAGGING"===e.type?function(e){const t=e.dimension.client,{offset:r,combineWith:n,dropping:o}=e,i=Boolean(n),a=Fo(e),s=Boolean(o),l=s?Nr.drop(r,i):Nr.moveTo(r);return{position:"fixed",top:t.marginBox.top,left:t.marginBox.left,boxSizing:"border-box",width:t.borderBox.width,height:t.borderBox.height,transition:Go(a,o),transform:l,opacity:_o(i,s),zIndex:s?Mo.dropAnimating:Mo.dragging,pointerEvents:"none"}}(e):(t=e,{transform:Nr.moveTo(t.offset),transition:t.shouldAnimateDisplacement?void 0:"none"});var t}function Wo(e){const r=Vn("draggable"),{descriptor:n,registry:o,getDraggableRef:i,canDragInteractiveElements:a,shouldRespectForcePress:s,isEnabled:l}=e,d=we((()=>({canDragInteractiveElements:a,shouldRespectForcePress:s,isEnabled:l})),[a,l,s]),u=Ee((e=>{const t=i();return t||c(),function(e,t,r=Se){const n=window.getComputedStyle(t),o=t.getBoundingClientRect(),i=je(o,n),a=$e(i,r);return{descriptor:e,placeholder:{client:i,tagName:t.tagName.toLowerCase(),display:n.display},displaceBy:{x:i.marginBox.width,y:i.marginBox.height},client:i,page:a}}(n,t,e)}),[n,i]),p=we((()=>({uniqueId:r,descriptor:n,options:d,getDimension:u})),[n,u,d,r]),g=t.useRef(p),f=t.useRef(!0);Bn((()=>(o.draggable.register(g.current),()=>o.draggable.unregister(g.current))),[o.draggable]),Bn((()=>{if(f.current)return void(f.current=!1);const e=g.current;g.current=p,o.draggable.update(p,e)}),[p,o.draggable])}var $o=t.createContext(null);function jo(e){const r=t.useContext(e);return r||c(),r}function Uo(e){e.preventDefault()}var Ho=(e,t)=>e===t,Vo=e=>{const{combine:t,destination:r}=e;return r?r.droppableId:t?t.droppableId:null};function qo(e=null){return{isDragging:!1,isDropAnimating:!1,isClone:!1,dropAnimation:null,mode:null,draggingOver:null,combineTargetFor:e,combineWith:null}}const zo={mapped:{type:"SECONDARY",offset:Se,combineTargetFor:null,shouldAnimateDisplacement:!0,snapshot:qo(null)}};const Yo=xe((()=>{const e=function(){const e=Je(((e,t)=>({x:e,y:t}))),t=Je(((e,t,r=null,n=null,o=null)=>({isDragging:!0,isClone:t,isDropAnimating:Boolean(o),dropAnimation:o,mode:e,draggingOver:r,combineWith:n,combineTargetFor:null}))),r=Je(((e,r,n,o,i=null,a=null,s=null)=>({mapped:{type:"DRAGGING",dropping:null,draggingOver:i,combineWith:a,mode:r,offset:e,dimension:n,forceShouldAnimate:s,snapshot:t(r,o,i,a,null)}})));return(n,o)=>{if(Yn(n)){if(n.critical.draggable.id!==o.draggableId)return null;const t=n.current.client.offset,a=n.dimensions.draggables[o.draggableId],s=jt(n.impact),l=(i=n.impact).at&&"COMBINE"===i.at.type?i.at.combine.draggableId:null,c=n.forceShouldAnimate;return r(e(t.x,t.y),n.movementMode,a,o.isClone,s,l,c)}var i;if("DROP_ANIMATING"===n.phase){const e=n.completed;if(e.result.draggableId!==o.draggableId)return null;const r=o.isClone,i=n.dimensions.draggables[o.draggableId],a=e.result,s=a.mode,l=Vo(a),c=(e=>e.combine?e.combine.draggableId:null)(a),d={duration:n.dropDuration,curve:Sr,moveTo:n.newHomeClientOffset,opacity:c?Cr.drop:null,scale:c?Ar.drop:null};return{mapped:{type:"DRAGGING",offset:n.newHomeClientOffset,dimension:i,dropping:d,draggingOver:l,combineWith:c,mode:s,forceShouldAnimate:null,snapshot:t(s,r,l,c,d)}}}return null}}(),t=function(){const e=Je(((e,t)=>({x:e,y:t}))),t=Je(qo),r=Je(((e,r=null,n)=>({mapped:{type:"SECONDARY",offset:e,combineTargetFor:r,shouldAnimateDisplacement:n,snapshot:t(r)}}))),n=e=>e?r(Se,e,!0):null,o=(t,o,i,a)=>{const s=i.displaced.visible[t],l=Boolean(a.inVirtualList&&a.effected[t]),c=rt(i),d=c&&c.draggableId===t?o:null;if(!s){if(!l)return n(d);if(i.displaced.invisible[t])return null;const o=Pe(a.displacedBy.point),s=e(o.x,o.y);return r(s,d,!0)}if(l)return n(d);const u=i.displacedBy.point,p=e(u.x,u.y);return r(p,d,s.shouldAnimate)};return(e,t)=>{if(Yn(e))return e.critical.draggable.id===t.draggableId?null:o(t.draggableId,e.critical.draggable.id,e.impact,e.afterCritical);if("DROP_ANIMATING"===e.phase){const r=e.completed;return r.result.draggableId===t.draggableId?null:o(t.draggableId,r.result.draggableId,r.impact,r.afterCritical)}return null}}();return(r,n)=>e(r,n)||t(r,n)||zo}),{dropAnimationFinished:Er},null,{context:kn,areStatePropsEqual:Ho})((e=>{const n=t.useRef(null),o=Ee(((e=null)=>{n.current=e}),[]),i=Ee((()=>n.current),[]),{contextId:a,dragHandleUsageInstructionsId:s,registry:l}=jo(qn),{type:c,droppableId:d}=jo($o),u=we((()=>({id:e.draggableId,index:e.index,type:c,droppableId:d})),[e.draggableId,e.index,c,d]),{children:p,draggableId:g,isEnabled:f,shouldRespectForcePress:m,canDragInteractiveElements:b,isClone:h,mapped:v,dropAnimationFinished:y}=e;if(!h){Wo(we((()=>({descriptor:u,registry:l,getDraggableRef:i,canDragInteractiveElements:b,shouldRespectForcePress:m,isEnabled:f})),[u,l,i,b,m,f]))}const x=we((()=>f?{tabIndex:0,role:"button","aria-describedby":s,"data-rfd-drag-handle-draggable-id":g,"data-rfd-drag-handle-context-id":a,draggable:!1,onDragStart:Uo}:null),[a,s,g,f]),I=Ee((e=>{"DRAGGING"===v.type&&v.dropping&&"transform"===e.propertyName&&r.flushSync(y)}),[y,v]),D=we((()=>{const e=ko(v),t="DRAGGING"===v.type&&v.dropping?I:void 0;return{innerRef:o,draggableProps:{"data-rfd-draggable-context-id":a,"data-rfd-draggable-id":g,style:e,onTransitionEnd:t},dragHandleProps:x}}),[a,x,g,v,I,o]),w=we((()=>({draggableId:u.id,type:u.type,source:{index:u.index,droppableId:u.droppableId}})),[u.droppableId,u.id,u.index,u.type]);return t.createElement(t.Fragment,null,p(D,v.snapshot,w))}));function Jo(e){return jo($o).isUsingCloneFor!==e.draggableId||e.isClone?t.createElement(Yo,e):null}const Xo=e=>t=>e===t,Ko=Xo("scroll"),Qo=Xo("auto"),Zo=(e,t)=>t(e.overflowX)||t(e.overflowY),ei=e=>null==e||e===document.body||e===document.documentElement?null:(e=>{const t=window.getComputedStyle(e),r={overflowX:t.overflowX,overflowY:t.overflowY};return Zo(r,Ko)||Zo(r,Qo)})(e)?e:ei(e.parentElement);var ti=e=>({x:e.scrollLeft,y:e.scrollTop});const ri=e=>{if(!e)return!1;return"fixed"===window.getComputedStyle(e).position||ri(e.parentElement)};var ni=({ref:e,descriptor:t,env:r,windowScroll:n,direction:o,isDropDisabled:i,isCombineEnabled:a,shouldClipSubject:s})=>{const l=r.closestScrollable,c=((e,t)=>{const r=Ue(e);if(!t)return r;if(e!==t)return r;const n=r.paddingBox.top-t.scrollTop,o=r.paddingBox.left-t.scrollLeft,i=n+t.scrollHeight,a=o+t.scrollWidth,s=Me({top:n,right:a,bottom:i,left:o},r.border);return Fe({borderBox:s,margin:r.margin,border:r.border,padding:r.padding})})(e,l),d=$e(c,n),u=(()=>{if(!l)return null;const e=Ue(l),t={scrollHeight:l.scrollHeight,scrollWidth:l.scrollWidth};return{client:e,page:$e(e,n),scroll:ti(l),scrollSize:t,shouldClipSubject:s}})(),p=(({descriptor:e,isEnabled:t,isCombineEnabled:r,isFixedOnPage:n,direction:o,client:i,page:a,closest:s})=>{const l=(()=>{if(!s)return null;const{scrollSize:e,client:t}=s,r=Qr({scrollHeight:e.scrollHeight,scrollWidth:e.scrollWidth,height:t.paddingBox.height,width:t.paddingBox.width});return{pageMarginBox:s.page.marginBox,frameClient:t,scrollSize:e,shouldClipSubject:s.shouldClipSubject,scroll:{initial:s.scroll,current:s.scroll,max:r,diff:{value:Se,displacement:Se}}}})(),c="vertical"===o?ut:pt;return{descriptor:e,isCombineEnabled:r,isFixedOnPage:n,axis:c,isEnabled:t,client:i,page:a,frame:l,subject:ze({page:a,withPlaceholder:null,axis:c,frame:l})}})({descriptor:t,isEnabled:!i,isCombineEnabled:a,isFixedOnPage:r.isFixedOnPage,direction:o,client:c,page:d,closest:u});return p};const oi={passive:!1},ii={passive:!0};var ai=e=>e.shouldPublishImmediately?oi:ii;const si=e=>e&&e.env.closestScrollable||null;function li(e){const r=t.useRef(null),n=jo(qn),o=Vn("droppable"),{registry:i,marshal:a}=n,s=zn(e),l=we((()=>({id:e.droppableId,type:e.type,mode:e.mode})),[e.droppableId,e.mode,e.type]),d=t.useRef(l),u=we((()=>Je(((e,t)=>{r.current||c();const n={x:e,y:t};a.updateDroppableScroll(l.id,n)}))),[l.id,a]),p=Ee((()=>{const e=r.current;return e&&e.env.closestScrollable?ti(e.env.closestScrollable):Se}),[]),g=Ee((()=>{const e=p();u(e.x,e.y)}),[p,u]),f=we((()=>_r(g)),[g]),m=Ee((()=>{const e=r.current,t=si(e);e&&t||c();e.scrollOptions.shouldPublishImmediately?g():f()}),[f,g]),b=Ee(((e,t)=>{r.current&&c();const o=s.current,i=o.getDroppableRef();i||c();const a={closestScrollable:ei(d=i),isFixedOnPage:ri(d)};var d;const u={ref:i,descriptor:l,env:a,scrollOptions:t};r.current=u;const p=ni({ref:i,descriptor:l,env:a,windowScroll:e,direction:o.direction,isDropDisabled:o.isDropDisabled,isCombineEnabled:o.isCombineEnabled,shouldClipSubject:!o.ignoreContainerClipping}),g=a.closestScrollable;return g&&(g.setAttribute(On.contextId,n.contextId),g.addEventListener("scroll",m,ai(u.scrollOptions))),p}),[n.contextId,l,m,s]),h=Ee((()=>{const e=r.current,t=si(e);return e&&t||c(),ti(t)}),[]),v=Ee((()=>{const e=r.current;e||c();const t=si(e);r.current=null,t&&(f.cancel(),t.removeAttribute(On.contextId),t.removeEventListener("scroll",m,ai(e.scrollOptions)))}),[m,f]),y=Ee((e=>{const t=r.current;t||c();const n=si(t);n||c(),n.scrollTop+=e.y,n.scrollLeft+=e.x}),[]),x=we((()=>({getDimensionAndWatchScroll:b,getScrollWhileDragging:h,dragStopped:v,scroll:y})),[v,b,h,y]),I=we((()=>({uniqueId:o,descriptor:l,callbacks:x})),[x,l,o]);Bn((()=>(d.current=I.descriptor,i.droppable.register(I),()=>{r.current&&v(),i.droppable.unregister(I)})),[x,l,v,I,a,i.droppable]),Bn((()=>{r.current&&a.updateDroppableIsEnabled(d.current.id,!e.isDropDisabled)}),[e.isDropDisabled,a]),Bn((()=>{r.current&&a.updateDroppableIsCombineEnabled(d.current.id,e.isCombineEnabled)}),[e.isCombineEnabled,a])}function ci(){}const di={width:0,height:0,margin:{top:0,right:0,bottom:0,left:0}},ui=({isAnimatingOpenOnMount:e,placeholder:t,animate:r})=>{const n=(({isAnimatingOpenOnMount:e,placeholder:t,animate:r})=>e||"close"===r?di:{height:t.client.borderBox.height,width:t.client.borderBox.width,margin:t.client.margin})({isAnimatingOpenOnMount:e,placeholder:t,animate:r});return{display:t.display,boxSizing:"border-box",width:n.width,height:n.height,marginTop:n.margin.top,marginRight:n.margin.right,marginBottom:n.margin.bottom,marginLeft:n.margin.left,flexShrink:"0",flexGrow:"0",pointerEvents:"none",transition:"none"!==r?Rr.placeholder:null}};var pi=t.memo((e=>{const r=t.useRef(null),n=Ee((()=>{r.current&&(clearTimeout(r.current),r.current=null)}),[]),{animate:o,onTransitionEnd:i,onClose:a,contextId:s}=e,[l,c]=t.useState("open"===e.animate);t.useEffect((()=>l?"open"!==o?(n(),c(!1),ci):r.current?ci:(r.current=setTimeout((()=>{r.current=null,c(!1)})),n):ci),[o,l,n]);const d=Ee((e=>{"height"===e.propertyName&&(i(),"close"===o&&a())}),[o,a,i]),u=ui({isAnimatingOpenOnMount:l,animate:e.animate,placeholder:e.placeholder});return t.createElement(e.placeholder.tagName,{style:u,"data-rfd-placeholder-context-id":s,onTransitionEnd:d,ref:e.innerRef})}));class gi extends t.PureComponent{constructor(...e){super(...e),this.state={isVisible:Boolean(this.props.on),data:this.props.on,animate:this.props.shouldAnimate&&this.props.on?"open":"none"},this.onClose=()=>{"close"===this.state.animate&&this.setState({isVisible:!1})}}static getDerivedStateFromProps(e,t){return e.shouldAnimate?e.on?{isVisible:!0,data:e.on,animate:"open"}:t.isVisible?{isVisible:!0,data:t.data,animate:"close"}:{isVisible:!1,animate:"close",data:null}:{isVisible:Boolean(e.on),data:e.on,animate:"none"}}render(){if(!this.state.isVisible)return null;const e={onClose:this.onClose,data:this.state.data,animate:this.state.animate};return this.props.children(e)}}const fi={mode:"standard",type:"DEFAULT",direction:"vertical",isDropDisabled:!1,isCombineEnabled:!1,ignoreContainerClipping:!1,renderClone:null,getContainerForClone:function(){return document.body||c(),document.body}},mi=e=>{let t,r={...e};for(t in fi)void 0===e[t]&&(r={...r,[t]:fi[t]});return r},bi=(e,t)=>e===t.droppable.type,hi=(e,t)=>t.draggables[e.draggable.id],vi=xe((()=>{const e={placeholder:null,shouldAnimatePlaceholder:!0,snapshot:{isDraggingOver:!1,draggingOverWith:null,draggingFromThisWith:null,isUsingPlaceholder:!1},useClone:null},t={...e,shouldAnimatePlaceholder:!1},r=Je((e=>({draggableId:e.id,type:e.type,source:{index:e.index,droppableId:e.droppableId}}))),n=Je(((n,o,i,a,s,l)=>{const c=s.descriptor.id;if(s.descriptor.droppableId===n){const e=l?{render:l,dragging:r(s.descriptor)}:null,t={isDraggingOver:i,draggingOverWith:i?c:null,draggingFromThisWith:c,isUsingPlaceholder:!0};return{placeholder:s.placeholder,shouldAnimatePlaceholder:!1,snapshot:t,useClone:e}}if(!o)return t;if(!a)return e;const d={isDraggingOver:i,draggingOverWith:c,draggingFromThisWith:null,isUsingPlaceholder:!0};return{placeholder:s.placeholder,shouldAnimatePlaceholder:!0,snapshot:d,useClone:null}}));return(r,o)=>{const i=mi(o),a=i.droppableId,s=i.type,l=!i.isDropDisabled,c=i.renderClone;if(Yn(r)){const e=r.critical;if(!bi(s,e))return t;const o=hi(e,r.dimensions),i=jt(r.impact)===a;return n(a,l,i,i,o,c)}if("DROP_ANIMATING"===r.phase){const e=r.completed;if(!bi(s,e.critical))return t;const o=hi(e.critical,r.dimensions);return n(a,l,Vo(e.result)===a,jt(e.impact)===a,o,c)}if("IDLE"===r.phase&&r.completed&&!r.shouldFlush){const n=r.completed;if(!bi(s,n.critical))return t;const o=jt(n.impact)===a,i=Boolean(n.impact.at&&"COMBINE"===n.impact.at.type),l=n.critical.droppable.id===a;return o?i?e:t:l?e:t}return t}}),{updateViewportMaxScroll:e=>({type:"UPDATE_VIEWPORT_MAX_SCROLL",payload:e})},((e,t,r)=>({...mi(r),...e,...t})),{context:kn,areStatePropsEqual:Ho})((e=>{const n=t.useContext(qn);n||c();const{contextId:o,isMovementAllowed:i}=n,a=t.useRef(null),s=t.useRef(null),{children:l,droppableId:d,type:u,mode:p,direction:g,ignoreContainerClipping:f,isDropDisabled:m,isCombineEnabled:b,snapshot:h,useClone:v,updateViewportMaxScroll:y,getContainerForClone:x}=e,I=Ee((()=>a.current),[]),D=Ee(((e=null)=>{a.current=e}),[]);Ee((()=>s.current),[]);const w=Ee(((e=null)=>{s.current=e}),[]),E=Ee((()=>{i()&&y({maxScroll:en()})}),[i,y]);li({droppableId:d,type:u,mode:p,direction:g,isDropDisabled:m,isCombineEnabled:b,ignoreContainerClipping:f,getDroppableRef:I});const S=we((()=>t.createElement(gi,{on:e.placeholder,shouldAnimate:e.shouldAnimatePlaceholder},(({onClose:e,data:r,animate:n})=>t.createElement(pi,{placeholder:r,onClose:e,innerRef:w,animate:n,contextId:o,onTransitionEnd:E})))),[o,E,e.placeholder,e.shouldAnimatePlaceholder,w]),C=we((()=>({innerRef:D,placeholder:S,droppableProps:{"data-rfd-droppable-id":d,"data-rfd-droppable-context-id":o}})),[o,d,S,D]),A=v?v.dragging.draggableId:null,O=we((()=>({droppableId:d,type:u,isUsingCloneFor:A})),[d,A,u]);return t.createElement($o.Provider,{value:O},l(C,h),function(){if(!v)return null;const{dragging:e,render:n}=v,o=t.createElement(Jo,{draggableId:e.draggableId,index:e.source.index,isClone:!0,isEnabled:!0,shouldRespectForcePress:!1,canDragInteractiveElements:!0},((t,r)=>n(t,r,e)));return r.createPortal(o,x())}())}));e.DragDropContext=function(e){const r=t.useId(),n=e.dragHandleUsageInstructions||m.dragHandleUsageInstructions;return t.createElement(d,null,(o=>t.createElement(Lo,{nonce:e.nonce,contextId:r,setCallbacks:o,dragHandleUsageInstructions:n,enableDefaultSensors:e.enableDefaultSensors,sensors:e.sensors,onBeforeCapture:e.onBeforeCapture,onBeforeDragStart:e.onBeforeDragStart,onDragStart:e.onDragStart,onDragUpdate:e.onDragUpdate,onDragEnd:e.onDragEnd,autoScrollerOptions:e.autoScrollerOptions},e.children)))},e.Draggable=function(e){const r="boolean"!=typeof e.isDragDisabled||!e.isDragDisabled,n=Boolean(e.disableInteractiveElementBlocking),o=Boolean(e.shouldRespectForcePress);return t.createElement(Jo,Wn({},e,{isClone:!1,isEnabled:r,canDragInteractiveElements:n,shouldRespectForcePress:o}))},e.Droppable=vi,e.useKeyboardSensor=go,e.useMouseSensor=lo,e.useTouchSensor=bo}));
