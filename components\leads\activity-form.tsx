'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { toast } from 'react-hot-toast'

const activitySchema = z.object({
  type: z.enum(['NOTE', 'CALL', 'EMAIL', 'MEETING', 'TASK']),
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  status: z.enum(['PENDING', 'COMPLETED', 'CANCELLED']),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']),
  scheduledAt: z.string().optional(),
  duration: z.number().optional(),
  outcome: z.string().optional(),
  followUpRequired: z.boolean(),
  followUpDate: z.string().optional(),
  tags: z.string().optional()
})

type ActivityFormData = z.infer<typeof activitySchema>

interface ActivityFormProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  leadId: string
  activity?: any
  mode: 'create' | 'edit'
}

export function ActivityForm({
  isOpen,
  onClose,
  onSuccess,
  leadId,
  activity,
  mode
}: ActivityFormProps) {
  const [loading, setLoading] = useState(false)

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors }
  } = useForm<ActivityFormData>({
    resolver: zodResolver(activitySchema),
    defaultValues: {
      type: 'NOTE',
      status: 'COMPLETED',
      priority: 'MEDIUM',
      followUpRequired: false
    }
  })

  const watchFollowUpRequired = watch('followUpRequired')
  const watchType = watch('type')

  useEffect(() => {
    if (activity && mode === 'edit') {
      setValue('type', activity.type)
      setValue('title', activity.title)
      setValue('description', activity.description || '')
      setValue('status', activity.status)
      setValue('priority', activity.priority)
      setValue('scheduledAt', activity.scheduledAt ? new Date(activity.scheduledAt).toISOString().slice(0, 16) : '')
      setValue('duration', activity.duration || undefined)
      setValue('outcome', activity.outcome || '')
      setValue('followUpRequired', activity.followUpRequired)
      setValue('followUpDate', activity.followUpDate ? new Date(activity.followUpDate).toISOString().slice(0, 16) : '')
      setValue('tags', activity.tags.join(', '))
    } else {
      reset({
        type: 'NOTE',
        status: 'COMPLETED',
        priority: 'MEDIUM',
        followUpRequired: false
      })
    }
  }, [activity, mode, setValue, reset])

  const onSubmit = async (data: ActivityFormData) => {
    try {
      setLoading(true)

      // Prepare the data
      const submitData = {
        ...data,
        description: data.description || null,
        scheduledAt: data.scheduledAt || null,
        duration: data.duration || null,
        outcome: data.outcome || null,
        followUpDate: data.followUpRequired && data.followUpDate ? data.followUpDate : null,
        tags: data.tags ? data.tags.split(',').map(tag => tag.trim()).filter(Boolean) : []
      }

      const url = mode === 'edit' 
        ? `/api/activities/${activity.id}`
        : `/api/leads/${leadId}/activities`
      
      const method = mode === 'edit' ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || `Failed to ${mode} activity`)
      }

      toast.success(`Activity ${mode === 'edit' ? 'updated' : 'created'} successfully`)
      onSuccess()
    } catch (error) {
      toast.error(error instanceof Error ? error.message : `Failed to ${mode} activity`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {mode === 'edit' ? 'Edit Activity' : 'Add New Activity'}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          {/* Type and Status */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="type">Activity Type</Label>
              <Select onValueChange={(value) => setValue('type', value as any)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="NOTE">Note</SelectItem>
                  <SelectItem value="CALL">Call</SelectItem>
                  <SelectItem value="EMAIL">Email</SelectItem>
                  <SelectItem value="MEETING">Meeting</SelectItem>
                  <SelectItem value="TASK">Task</SelectItem>
                </SelectContent>
              </Select>
              {errors.type && (
                <p className="text-sm text-red-600 mt-1">{errors.type.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="status">Status</Label>
              <Select onValueChange={(value) => setValue('status', value as any)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="PENDING">Pending</SelectItem>
                  <SelectItem value="COMPLETED">Completed</SelectItem>
                  <SelectItem value="CANCELLED">Cancelled</SelectItem>
                </SelectContent>
              </Select>
              {errors.status && (
                <p className="text-sm text-red-600 mt-1">{errors.status.message}</p>
              )}
            </div>
          </div>

          {/* Title */}
          <div>
            <Label htmlFor="title">Title</Label>
            <Input
              id="title"
              {...register('title')}
              placeholder="Enter activity title"
            />
            {errors.title && (
              <p className="text-sm text-red-600 mt-1">{errors.title.message}</p>
            )}
          </div>

          {/* Description */}
          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              {...register('description')}
              placeholder="Enter activity description"
              rows={3}
            />
          </div>

          {/* Priority and Scheduled At */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="priority">Priority</Label>
              <Select onValueChange={(value) => setValue('priority', value as any)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="LOW">Low</SelectItem>
                  <SelectItem value="MEDIUM">Medium</SelectItem>
                  <SelectItem value="HIGH">High</SelectItem>
                  <SelectItem value="URGENT">Urgent</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="scheduledAt">Scheduled Date & Time</Label>
              <Input
                id="scheduledAt"
                type="datetime-local"
                {...register('scheduledAt')}
              />
            </div>
          </div>

          {/* Duration (for calls and meetings) */}
          {(watchType === 'CALL' || watchType === 'MEETING') && (
            <div>
              <Label htmlFor="duration">Duration (minutes)</Label>
              <Input
                id="duration"
                type="number"
                {...register('duration', { valueAsNumber: true })}
                placeholder="Enter duration in minutes"
              />
            </div>
          )}

          {/* Outcome */}
          <div>
            <Label htmlFor="outcome">Outcome</Label>
            <Textarea
              id="outcome"
              {...register('outcome')}
              placeholder="What was the outcome of this activity?"
              rows={2}
            />
          </div>

          {/* Follow-up */}
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="followUpRequired"
                onCheckedChange={(checked) => setValue('followUpRequired', !!checked)}
              />
              <Label htmlFor="followUpRequired">Follow-up required</Label>
            </div>

            {watchFollowUpRequired && (
              <div>
                <Label htmlFor="followUpDate">Follow-up Date & Time</Label>
                <Input
                  id="followUpDate"
                  type="datetime-local"
                  {...register('followUpDate')}
                />
              </div>
            )}
          </div>

          {/* Tags */}
          <div>
            <Label htmlFor="tags">Tags</Label>
            <Input
              id="tags"
              {...register('tags')}
              placeholder="Enter tags separated by commas"
            />
            <p className="text-sm text-gray-500 mt-1">
              Separate multiple tags with commas
            </p>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Saving...' : mode === 'edit' ? 'Update Activity' : 'Create Activity'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
