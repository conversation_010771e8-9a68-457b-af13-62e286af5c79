(()=>{var e={};e.id=4159,e.ids=[4159],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},91944:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>d});var a=t(50482),r=t(69108),i=t(62563),l=t.n(i),n=t(68300),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d=["",{children:["dashboard",{children:["leads",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,4878)),"C:\\proj\\nextjs-saas\\app\\dashboard\\leads\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,96339)),"C:\\proj\\nextjs-saas\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,59504)),"C:\\proj\\nextjs-saas\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\proj\\nextjs-saas\\app\\dashboard\\leads\\[id]\\page.tsx"],x="/dashboard/leads/[id]/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/leads/[id]/page",pathname:"/dashboard/leads/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},8789:(e,s,t)=>{Promise.resolve().then(t.bind(t,72083))},72083:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>ex});var a=t(95344),r=t(3729),i=t(22254),l=t(47674),n=t(61351),c=t(16212),d=t(69436),o=t(25757),x=t(68008),m=t(20886),h=t(37121),u=t(40626),p=t(71206),j=t(55794),v=t(2246),y=t(88534),f=t(7060),g=t(25545),N=t(73229),w=t(66138),b=t(51838),C=t(18822),k=t(36341),D=t(62093),Z=t(75695),S=t(38271),E=t(44669),T=t(60708),L=t(55697),A=t(12374),q=t(16802),M=t(92549),I=t(1586),U=t(93601),O=t(17470),P=t(84332);let R=A.Ry({type:A.Km(["NOTE","CALL","EMAIL","MEETING","TASK"]),title:A.Z_().min(1,"Title is required"),description:A.Z_().optional(),status:A.Km(["PENDING","COMPLETED","CANCELLED"]),priority:A.Km(["LOW","MEDIUM","HIGH","URGENT"]),scheduledAt:A.Z_().optional(),duration:A.Rx().optional(),outcome:A.Z_().optional(),followUpRequired:A.O7(),followUpDate:A.Z_().optional(),tags:A.Z_().optional()});function _({isOpen:e,onClose:s,onSuccess:t,leadId:i,activity:l,mode:n}){let[d,o]=(0,r.useState)(!1),{register:x,handleSubmit:m,setValue:h,watch:u,reset:p,formState:{errors:j}}=(0,T.cI)({resolver:(0,L.F)(R),defaultValues:{type:"NOTE",status:"COMPLETED",priority:"MEDIUM",followUpRequired:!1}}),v=u("followUpRequired"),y=u("type");(0,r.useEffect)(()=>{l&&"edit"===n?(h("type",l.type),h("title",l.title),h("description",l.description||""),h("status",l.status),h("priority",l.priority),h("scheduledAt",l.scheduledAt?new Date(l.scheduledAt).toISOString().slice(0,16):""),h("duration",l.duration||void 0),h("outcome",l.outcome||""),h("followUpRequired",l.followUpRequired),h("followUpDate",l.followUpDate?new Date(l.followUpDate).toISOString().slice(0,16):""),h("tags",l.tags.join(", "))):p({type:"NOTE",status:"COMPLETED",priority:"MEDIUM",followUpRequired:!1})},[l,n,h,p]);let f=async e=>{try{o(!0);let s={...e,description:e.description||null,scheduledAt:e.scheduledAt||null,duration:e.duration||null,outcome:e.outcome||null,followUpDate:e.followUpRequired&&e.followUpDate?e.followUpDate:null,tags:e.tags?e.tags.split(",").map(e=>e.trim()).filter(Boolean):[]},a="edit"===n?`/api/activities/${l.id}`:`/api/leads/${i}/activities`,r="edit"===n?"PUT":"POST",c=await fetch(a,{method:r,headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(!c.ok){let e=await c.json();throw Error(e.error||`Failed to ${n} activity`)}E.toast.success(`Activity ${"edit"===n?"updated":"created"} successfully`),t()}catch(e){E.toast.error(e instanceof Error?e.message:`Failed to ${n} activity`)}finally{o(!1)}};return a.jsx(q.Vq,{open:e,onOpenChange:s,children:(0,a.jsxs)(q.cZ,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[a.jsx(q.fK,{children:a.jsx(q.$N,{children:"edit"===n?"Edit Activity":"Add New Activity"})}),(0,a.jsxs)("form",{onSubmit:m(f),className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx(I._,{htmlFor:"type",children:"Activity Type"}),(0,a.jsxs)(O.Ph,{onValueChange:e=>h("type",e),children:[a.jsx(O.i4,{children:a.jsx(O.ki,{placeholder:"Select type"})}),(0,a.jsxs)(O.Bw,{children:[a.jsx(O.Ql,{value:"NOTE",children:"Note"}),a.jsx(O.Ql,{value:"CALL",children:"Call"}),a.jsx(O.Ql,{value:"EMAIL",children:"Email"}),a.jsx(O.Ql,{value:"MEETING",children:"Meeting"}),a.jsx(O.Ql,{value:"TASK",children:"Task"})]})]}),j.type&&a.jsx("p",{className:"text-sm text-red-600 mt-1",children:j.type.message})]}),(0,a.jsxs)("div",{children:[a.jsx(I._,{htmlFor:"status",children:"Status"}),(0,a.jsxs)(O.Ph,{onValueChange:e=>h("status",e),children:[a.jsx(O.i4,{children:a.jsx(O.ki,{placeholder:"Select status"})}),(0,a.jsxs)(O.Bw,{children:[a.jsx(O.Ql,{value:"PENDING",children:"Pending"}),a.jsx(O.Ql,{value:"COMPLETED",children:"Completed"}),a.jsx(O.Ql,{value:"CANCELLED",children:"Cancelled"})]})]}),j.status&&a.jsx("p",{className:"text-sm text-red-600 mt-1",children:j.status.message})]})]}),(0,a.jsxs)("div",{children:[a.jsx(I._,{htmlFor:"title",children:"Title"}),a.jsx(M.I,{id:"title",...x("title"),placeholder:"Enter activity title"}),j.title&&a.jsx("p",{className:"text-sm text-red-600 mt-1",children:j.title.message})]}),(0,a.jsxs)("div",{children:[a.jsx(I._,{htmlFor:"description",children:"Description"}),a.jsx(U.g,{id:"description",...x("description"),placeholder:"Enter activity description",rows:3})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx(I._,{htmlFor:"priority",children:"Priority"}),(0,a.jsxs)(O.Ph,{onValueChange:e=>h("priority",e),children:[a.jsx(O.i4,{children:a.jsx(O.ki,{placeholder:"Select priority"})}),(0,a.jsxs)(O.Bw,{children:[a.jsx(O.Ql,{value:"LOW",children:"Low"}),a.jsx(O.Ql,{value:"MEDIUM",children:"Medium"}),a.jsx(O.Ql,{value:"HIGH",children:"High"}),a.jsx(O.Ql,{value:"URGENT",children:"Urgent"})]})]})]}),(0,a.jsxs)("div",{children:[a.jsx(I._,{htmlFor:"scheduledAt",children:"Scheduled Date & Time"}),a.jsx(M.I,{id:"scheduledAt",type:"datetime-local",...x("scheduledAt")})]})]}),("CALL"===y||"MEETING"===y)&&(0,a.jsxs)("div",{children:[a.jsx(I._,{htmlFor:"duration",children:"Duration (minutes)"}),a.jsx(M.I,{id:"duration",type:"number",...x("duration",{valueAsNumber:!0}),placeholder:"Enter duration in minutes"})]}),(0,a.jsxs)("div",{children:[a.jsx(I._,{htmlFor:"outcome",children:"Outcome"}),a.jsx(U.g,{id:"outcome",...x("outcome"),placeholder:"What was the outcome of this activity?",rows:2})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(P.X,{id:"followUpRequired",onCheckedChange:e=>h("followUpRequired",!!e)}),a.jsx(I._,{htmlFor:"followUpRequired",children:"Follow-up required"})]}),v&&(0,a.jsxs)("div",{children:[a.jsx(I._,{htmlFor:"followUpDate",children:"Follow-up Date & Time"}),a.jsx(M.I,{id:"followUpDate",type:"datetime-local",...x("followUpDate")})]})]}),(0,a.jsxs)("div",{children:[a.jsx(I._,{htmlFor:"tags",children:"Tags"}),a.jsx(M.I,{id:"tags",...x("tags"),placeholder:"Enter tags separated by commas"}),a.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Separate multiple tags with commas"})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-2 pt-4",children:[a.jsx(c.z,{type:"button",variant:"outline",onClick:s,children:"Cancel"}),a.jsx(c.z,{type:"submit",disabled:d,children:d?"Saving...":"edit"===n?"Update Activity":"Create Activity"})]})]})]})})}function F({leadId:e}){let[s,t]=(0,r.useState)([]),[i,l]=(0,r.useState)(!0),[o,x]=(0,r.useState)(!1),[T,L]=(0,r.useState)(null),[A,q]=(0,r.useState)("all"),M=async()=>{try{l(!0);let s=new URLSearchParams({limit:"50"});"all"!==A&&s.append("type",A);let a=await fetch(`/api/leads/${e}/activities?${s}`);if(!a.ok)throw Error("Failed to fetch activities");let r=await a.json();t(r.activities)}catch(e){E.toast.error("Failed to load activities"),console.error("Error fetching activities:",e)}finally{l(!1)}};(0,r.useEffect)(()=>{e&&M()},[e,A]);let I=async e=>{if(confirm("Are you sure you want to delete this activity?"))try{if(!(await fetch(`/api/activities/${e}`,{method:"DELETE"})).ok)throw Error("Failed to delete activity");E.toast.success("Activity deleted successfully"),M()}catch(e){E.toast.error("Failed to delete activity"),console.error("Error deleting activity:",e)}},U=e=>{switch(e){case"NOTE":return a.jsx(h.Z,{className:"h-4 w-4"});case"CALL":return a.jsx(u.Z,{className:"h-4 w-4"});case"EMAIL":return a.jsx(p.Z,{className:"h-4 w-4"});case"MEETING":return a.jsx(j.Z,{className:"h-4 w-4"});case"TASK":return a.jsx(v.Z,{className:"h-4 w-4"});default:return a.jsx(y.Z,{className:"h-4 w-4"})}},O=e=>{switch(e){case"NOTE":return"text-blue-600 bg-blue-100";case"CALL":return"text-green-600 bg-green-100";case"EMAIL":return"text-purple-600 bg-purple-100";case"MEETING":return"text-orange-600 bg-orange-100";case"TASK":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}},P=e=>{switch(e){case"COMPLETED":return a.jsx(f.Z,{className:"h-4 w-4 text-green-600"});case"PENDING":return a.jsx(g.Z,{className:"h-4 w-4 text-yellow-600"});case"CANCELLED":return a.jsx(N.Z,{className:"h-4 w-4 text-red-600"});default:return a.jsx(w.Z,{className:"h-4 w-4 text-gray-600"})}},R=e=>{switch(e){case"LOW":return a.jsx(d.C,{variant:"secondary",className:"text-xs",children:"Low"});case"MEDIUM":return a.jsx(d.C,{className:"bg-blue-100 text-blue-800 text-xs",children:"Medium"});case"HIGH":return a.jsx(d.C,{className:"bg-orange-100 text-orange-800 text-xs",children:"High"});case"URGENT":return a.jsx(d.C,{variant:"destructive",className:"text-xs",children:"Urgent"});default:return a.jsx(d.C,{variant:"secondary",className:"text-xs",children:e})}};return i?a.jsx("div",{className:"flex items-center justify-center py-8",children:a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("h3",{className:"text-lg font-medium",children:"Activity Timeline"}),(0,a.jsxs)("select",{value:A,onChange:e=>q(e.target.value),className:"text-sm border rounded px-2 py-1",children:[a.jsx("option",{value:"all",children:"All Activities"}),a.jsx("option",{value:"NOTE",children:"Notes"}),a.jsx("option",{value:"CALL",children:"Calls"}),a.jsx("option",{value:"EMAIL",children:"Emails"}),a.jsx("option",{value:"MEETING",children:"Meetings"}),a.jsx("option",{value:"TASK",children:"Tasks"})]})]}),(0,a.jsxs)(c.z,{onClick:()=>x(!0),size:"sm",children:[a.jsx(b.Z,{className:"h-4 w-4 mr-2"}),"Add Activity"]})]}),a.jsx("div",{className:"space-y-4",children:0===s.length?a.jsx(n.Zb,{children:(0,a.jsxs)(n.aY,{className:"py-8 text-center text-gray-500",children:[a.jsx(y.Z,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),a.jsx("p",{children:"No activities found"}),a.jsx("p",{className:"text-sm",children:"Start by adding a note, call, or meeting"})]})}):s.map(e=>a.jsx(n.Zb,{className:"relative",children:a.jsx(n.aY,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[a.jsx("div",{className:`p-2 rounded-full ${O(e.type)}`,children:U(e.type)}),a.jsx("div",{className:"flex-1 min-w-0",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[a.jsx("h4",{className:"font-medium text-gray-900",children:e.title}),R(e.priority),P(e.status)]}),e.description&&a.jsx("p",{className:"text-sm text-gray-600 mb-2",children:e.description}),(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-4 text-xs text-gray-500 mb-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[a.jsx(C.Z,{className:"h-3 w-3"}),a.jsx("span",{children:e.createdBy.name||e.createdBy.email})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[a.jsx(g.Z,{className:"h-3 w-3"}),a.jsx("span",{children:new Date(e.createdAt).toLocaleString()})]}),e.scheduledAt&&(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[a.jsx(j.Z,{className:"h-3 w-3"}),(0,a.jsxs)("span",{children:["Scheduled: ",new Date(e.scheduledAt).toLocaleString()]})]}),e.duration&&(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[a.jsx(g.Z,{className:"h-3 w-3"}),(0,a.jsxs)("span",{children:[e.duration," minutes"]})]})]}),e.outcome&&a.jsx("div",{className:"bg-gray-50 rounded p-2 mb-2",children:(0,a.jsxs)("p",{className:"text-sm text-gray-700",children:[a.jsx("strong",{children:"Outcome:"})," ",e.outcome]})}),e.followUpRequired&&e.followUpDate&&a.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded p-2 mb-2",children:(0,a.jsxs)("p",{className:"text-sm text-yellow-800",children:[a.jsx("strong",{children:"Follow-up required:"})," ",new Date(e.followUpDate).toLocaleDateString()]})}),e.tags.length>0&&(0,a.jsxs)("div",{className:"flex items-center space-x-1 mb-2",children:[a.jsx(k.Z,{className:"h-3 w-3 text-gray-400"}),a.jsx("div",{className:"flex flex-wrap gap-1",children:e.tags.map((e,s)=>a.jsx(d.C,{variant:"outline",className:"text-xs",children:e},s))})]})]}),(0,a.jsxs)(m.h_,{children:[a.jsx(m.$F,{asChild:!0,children:a.jsx(c.z,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",children:a.jsx(D.Z,{className:"h-4 w-4"})})}),(0,a.jsxs)(m.AW,{align:"end",children:[(0,a.jsxs)(m.Xi,{onClick:()=>L(e),children:[a.jsx(Z.Z,{className:"h-4 w-4 mr-2"}),"Edit"]}),(0,a.jsxs)(m.Xi,{onClick:()=>I(e.id),className:"text-red-600",children:[a.jsx(S.Z,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]})]})})]})})},e.id))}),(o||T)&&a.jsx(_,{isOpen:o||!!T,onClose:()=>{x(!1),L(null)},onSuccess:()=>{x(!1),L(null),M()},leadId:e,activity:T,mode:T?"edit":"create"})]})}var $=t(69224);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let z=(0,$.Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]);var Q=t(14513),V=t(31498);function H({leadId:e}){let[s,t]=(0,r.useState)([]),[i,l]=(0,r.useState)(!0),[d,o]=(0,r.useState)(!1),[x,h]=(0,r.useState)(null),[u,p]=(0,r.useState)({title:"",content:"",isPrivate:!1}),[j,v]=(0,r.useState)(!1),y=async()=>{try{l(!0);let s=await fetch(`/api/leads/${e}/notes`);if(!s.ok)throw Error("Failed to fetch notes");let a=await s.json();t(a.notes)}catch(e){E.toast.error("Failed to load notes"),console.error("Error fetching notes:",e)}finally{l(!1)}};(0,r.useEffect)(()=>{e&&y()},[e]);let f=async s=>{if(s.preventDefault(),!u.title.trim()||!u.content.trim()){E.toast.error("Title and content are required");return}try{v(!0);let s=x?`/api/lead-notes/${x.id}`:`/api/leads/${e}/notes`,t=await fetch(s,{method:x?"PUT":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(u)});if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to save note")}E.toast.success(`Note ${x?"updated":"created"} successfully`),o(!1),h(null),p({title:"",content:"",isPrivate:!1}),y()}catch(e){E.toast.error(e instanceof Error?e.message:"Failed to save note")}finally{v(!1)}},N=e=>{h(e),p({title:e.title,content:e.content,isPrivate:e.isPrivate}),o(!0)},w=async e=>{if(confirm("Are you sure you want to delete this note?"))try{if(!(await fetch(`/api/lead-notes/${e}`,{method:"DELETE"})).ok)throw Error("Failed to delete note");E.toast.success("Note deleted successfully"),y()}catch(e){E.toast.error("Failed to delete note"),console.error("Error deleting note:",e)}},k=()=>{o(!1),h(null),p({title:"",content:"",isPrivate:!1})};return i?a.jsx("div",{className:"flex items-center justify-center py-8",children:a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("h3",{className:"text-lg font-medium",children:"Lead Notes"}),(0,a.jsxs)(c.z,{onClick:()=>o(!0),size:"sm",children:[a.jsx(b.Z,{className:"h-4 w-4 mr-2"}),"Add Note"]})]}),a.jsx("div",{className:"space-y-4",children:0===s.length?a.jsx(n.Zb,{children:(0,a.jsxs)(n.aY,{className:"py-8 text-center text-gray-500",children:[a.jsx(z,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),a.jsx("p",{children:"No notes found"}),a.jsx("p",{className:"text-sm",children:"Add your first note to get started"})]})}):s.map(e=>(0,a.jsxs)(n.Zb,{children:[a.jsx(n.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx(n.ll,{className:"text-base",children:e.title}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500 mt-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[a.jsx(C.Z,{className:"h-3 w-3"}),a.jsx("span",{children:e.createdBy.name||e.createdBy.email})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[a.jsx(g.Z,{className:"h-3 w-3"}),a.jsx("span",{children:new Date(e.createdAt).toLocaleString()})]}),e.isPrivate&&a.jsx("span",{className:"bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs",children:"Private"})]})]}),(0,a.jsxs)(m.h_,{children:[a.jsx(m.$F,{asChild:!0,children:a.jsx(c.z,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",children:a.jsx(D.Z,{className:"h-4 w-4"})})}),(0,a.jsxs)(m.AW,{align:"end",children:[(0,a.jsxs)(m.Xi,{onClick:()=>N(e),children:[a.jsx(Z.Z,{className:"h-4 w-4 mr-2"}),"Edit"]}),(0,a.jsxs)(m.Xi,{onClick:()=>w(e.id),className:"text-red-600",children:[a.jsx(S.Z,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]})]})}),(0,a.jsxs)(n.aY,{children:[a.jsx("div",{className:"whitespace-pre-wrap text-sm text-gray-700",children:e.content}),e.updatedAt!==e.createdAt&&(0,a.jsxs)("div",{className:"text-xs text-gray-400 mt-2",children:["Last updated: ",new Date(e.updatedAt).toLocaleString()]})]})]},e.id))}),a.jsx(q.Vq,{open:d,onOpenChange:k,children:(0,a.jsxs)(q.cZ,{className:"max-w-2xl",children:[a.jsx(q.fK,{children:a.jsx(q.$N,{children:x?"Edit Note":"Add New Note"})}),(0,a.jsxs)("form",{onSubmit:f,className:"space-y-4",children:[(0,a.jsxs)("div",{children:[a.jsx(I._,{htmlFor:"title",children:"Title"}),a.jsx(M.I,{id:"title",value:u.title,onChange:e=>p({...u,title:e.target.value}),placeholder:"Enter note title",required:!0})]}),(0,a.jsxs)("div",{children:[a.jsx(I._,{htmlFor:"content",children:"Content"}),a.jsx(U.g,{id:"content",value:u.content,onChange:e=>p({...u,content:e.target.value}),placeholder:"Enter note content",rows:6,required:!0})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("input",{type:"checkbox",id:"isPrivate",checked:u.isPrivate,onChange:e=>p({...u,isPrivate:e.target.checked}),className:"rounded"}),a.jsx(I._,{htmlFor:"isPrivate",className:"text-sm",children:"Private note (only visible to you)"})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-2 pt-4",children:[(0,a.jsxs)(c.z,{type:"button",variant:"outline",onClick:k,children:[a.jsx(Q.Z,{className:"h-4 w-4 mr-2"}),"Cancel"]}),(0,a.jsxs)(c.z,{type:"submit",disabled:j,children:[a.jsx(V.Z,{className:"h-4 w-4 mr-2"}),j?"Saving...":x?"Update Note":"Save Note"]})]})]})]})})]})}var G=t(17910),W=t(50340),Y=t(66985);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let B=(0,$.Z)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]),K=(0,$.Z)("Lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]]);var X=t(46064),J=t(77402);function ee({leadId:e}){let[s,t]=(0,r.useState)(null),[i,l]=(0,r.useState)(!0),[o,x]=(0,r.useState)(!1),[m,h]=(0,r.useState)(!1),[u,p]=(0,r.useState)(""),[j,v]=(0,r.useState)(""),[y,N]=(0,r.useState)(""),[b,C]=(0,r.useState)(!1),k=async()=>{try{l(!0);let s=await fetch(`/api/leads/${e}/score`);if(!s.ok)throw Error("Failed to fetch scoring data");let a=await s.json();t(a)}catch(e){E.toast.error("Failed to load scoring data"),console.error("Error fetching scoring data:",e)}finally{l(!1)}};(0,r.useEffect)(()=>{e&&k()},[e]);let D=async()=>{if(!u&&!y){E.toast.error("Please provide a score or qualification notes");return}try{C(!0);let s=await fetch(`/api/leads/${e}/score`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({manualScore:u?parseInt(u):void 0,reason:j,qualificationNotes:y})});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to update score")}E.toast.success("Lead score updated successfully"),x(!1),p(""),v(""),N(""),k()}catch(e){E.toast.error(e instanceof Error?e.message:"Failed to update score")}finally{C(!1)}},S=e=>{switch(e){case"URGENT":return"text-red-600 bg-red-100";case"HIGH":return"text-orange-600 bg-orange-100";case"MEDIUM":return"text-yellow-600 bg-yellow-100";case"LOW":return"text-green-600 bg-green-100";default:return"text-gray-600 bg-gray-100"}};return i?a.jsx("div",{className:"flex items-center justify-center py-8",children:a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):s?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-blue-100 rounded-full",children:a.jsx(G.Z,{className:"h-6 w-6 text-blue-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Current Score"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[s.currentScore,"/100"]})]})]})})}),a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-purple-100 rounded-full",children:a.jsx(W.Z,{className:"h-6 w-6 text-purple-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Automated Score"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[s.automatedScore,"/100"]})]})]})})}),a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-orange-100 rounded-full",children:a.jsx(Y.Z,{className:"h-6 w-6 text-orange-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Temperature"}),a.jsx(d.C,{className:`${(e=>{switch(e){case"HOT":return"text-red-600 bg-red-100";case"WARM":return"text-orange-600 bg-orange-100";case"COLD":return"text-blue-600 bg-blue-100";default:return"text-gray-600 bg-gray-100"}})(s.temperature)} text-sm`,children:s.temperature})]})]})})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(c.z,{onClick:()=>x(!0),variant:"outline",children:[a.jsx(Z.Z,{className:"h-4 w-4 mr-2"}),"Update Score"]}),(0,a.jsxs)(c.z,{onClick:()=>h(!0),variant:"outline",children:[a.jsx(B,{className:"h-4 w-4 mr-2"}),"Score History"]})]}),(0,a.jsxs)(n.Zb,{children:[a.jsx(n.Ol,{children:(0,a.jsxs)(n.ll,{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx(f.Z,{className:"h-5 w-5 mr-2"}),"Qualification Status"]}),a.jsx(d.C,{className:s.qualificationStatus.isQualified?"bg-green-100 text-green-800":"bg-red-100 text-red-800",children:s.qualificationStatus.isQualified?"QUALIFIED":"NOT QUALIFIED"})]})}),a.jsx(n.aY,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-sm font-medium",children:"Qualification Score"}),(0,a.jsxs)("span",{className:"text-lg font-bold text-gray-900",children:[s.qualificationStatus.qualificationScore,"%"]})]}),a.jsx("div",{className:"w-full bg-gray-200 rounded-full h-3",children:a.jsx("div",{className:`h-3 rounded-full ${s.qualificationStatus.qualificationScore>=60?"bg-green-600":s.qualificationStatus.qualificationScore>=40?"bg-yellow-600":"bg-red-600"}`,style:{width:`${s.qualificationStatus.qualificationScore}%`}})}),a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:Object.entries(s.qualificationStatus.criteria).map(([e,s])=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[s?a.jsx(f.Z,{className:"h-4 w-4 text-green-600"}):a.jsx(w.Z,{className:"h-4 w-4 text-red-600"}),a.jsx("span",{className:`text-sm ${s?"text-green-700":"text-red-700"}`,children:e.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase())})]},e))}),s.qualificationStatus.missingCriteria.length>0&&(0,a.jsxs)("div",{className:"mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:[a.jsx("p",{className:"text-sm font-medium text-yellow-800 mb-2",children:"Missing Qualification Criteria:"}),a.jsx("ul",{className:"text-sm text-yellow-700 space-y-1",children:s.qualificationStatus.missingCriteria.map(e=>(0,a.jsxs)("li",{className:"flex items-center space-x-2",children:[a.jsx(w.Z,{className:"h-3 w-3"}),a.jsx("span",{children:e.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase())})]},e))})]})]})})]}),(0,a.jsxs)(n.Zb,{children:[a.jsx(n.Ol,{children:a.jsx(n.ll,{children:"Score Breakdown"})}),a.jsx(n.aY,{children:a.jsx("div",{className:"space-y-4",children:s.scoreBreakdown.map((e,s)=>(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-sm font-medium",children:e.category}),(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[e.points,"/",e.maxPoints," points"]})]}),a.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:a.jsx("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:`${e.points/e.maxPoints*100}%`}})}),a.jsx("p",{className:"text-xs text-gray-500",children:e.description})]},s))})})]}),s.recommendations.length>0&&(0,a.jsxs)(n.Zb,{children:[a.jsx(n.Ol,{children:(0,a.jsxs)(n.ll,{className:"flex items-center",children:[a.jsx(K,{className:"h-5 w-5 mr-2"}),"Recommendations"]})}),a.jsx(n.aY,{children:a.jsx("div",{className:"space-y-3",children:s.recommendations.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-start space-x-3 p-3 bg-gray-50 rounded-lg",children:[a.jsx(d.C,{className:`${S(e.priority)} text-xs`,children:e.priority}),(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("p",{className:"text-sm font-medium text-gray-900",children:e.message}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["Action: ",e.action]})]})]},s))})})]}),a.jsx(q.Vq,{open:o,onOpenChange:x,children:(0,a.jsxs)(q.cZ,{className:"max-w-md",children:[a.jsx(q.fK,{children:a.jsx(q.$N,{children:"Update Lead Score"})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[a.jsx(I._,{htmlFor:"manualScore",children:"Manual Score (0-100)"}),a.jsx(M.I,{id:"manualScore",type:"number",min:"0",max:"100",value:u,onChange:e=>p(e.target.value),placeholder:"Enter score"})]}),(0,a.jsxs)("div",{children:[a.jsx(I._,{htmlFor:"reason",children:"Reason for Change"}),a.jsx(M.I,{id:"reason",value:j,onChange:e=>v(e.target.value),placeholder:"Why are you changing the score?"})]}),(0,a.jsxs)("div",{children:[a.jsx(I._,{htmlFor:"qualificationNotes",children:"Qualification Notes"}),a.jsx(U.g,{id:"qualificationNotes",value:y,onChange:e=>N(e.target.value),placeholder:"Add qualification notes...",rows:3})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-2",children:[a.jsx(c.z,{variant:"outline",onClick:()=>x(!1),children:"Cancel"}),a.jsx(c.z,{onClick:D,disabled:b,children:b?"Updating...":"Update Score"})]})]})]})}),a.jsx(q.Vq,{open:m,onOpenChange:h,children:(0,a.jsxs)(q.cZ,{className:"max-w-2xl max-h-[80vh] overflow-y-auto",children:[a.jsx(q.fK,{children:a.jsx(q.$N,{children:"Score History"})}),a.jsx("div",{className:"space-y-4",children:0===s.scoreHistory.length?a.jsx("p",{className:"text-center text-gray-500 py-8",children:"No score history available"}):s.scoreHistory.map(e=>(0,a.jsxs)("div",{className:"flex items-start space-x-3 p-3 border rounded-lg",children:[a.jsx("div",{className:"flex-shrink-0",children:e.newScore>e.previousScore?a.jsx(X.Z,{className:"h-5 w-5 text-green-600"}):e.newScore<e.previousScore?a.jsx(J.Z,{className:"h-5 w-5 text-red-600"}):a.jsx(G.Z,{className:"h-5 w-5 text-gray-600"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,a.jsxs)("span",{className:"font-medium",children:[e.previousScore," → ",e.newScore]}),a.jsx(d.C,{variant:e.isManual?"default":"secondary",className:"text-xs",children:e.isManual?"Manual":"Automatic"})]}),a.jsx("p",{className:"text-sm text-gray-600",children:e.changeReason}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-2 text-xs text-gray-500",children:[a.jsx(g.Z,{className:"h-3 w-3"}),a.jsx("span",{children:new Date(e.createdAt).toLocaleString()}),(0,a.jsxs)("span",{children:["by ",e.createdBy.name||e.createdBy.email]})]})]})]},e.id))})]})})]}):(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[a.jsx(w.Z,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),a.jsx("p",{children:"Failed to load scoring data"})]})}var es=t(27109),et=t(15366),ea=t(35299);function er({leadId:e,onConversionComplete:s}){let[t,i]=(0,r.useState)(null),[l,o]=(0,r.useState)(!0),[x,m]=(0,r.useState)(!1),[u,p]=(0,r.useState)(!1),[j,v]=(0,r.useState)({customerData:{name:"",email:"",phone:"",company:"",address:"",city:"",state:"",zipCode:"",country:"",website:"",industry:"",companySize:"",notes:""},conversionData:{conversionType:"DIRECT",conversionReason:"",conversionValue:"",conversionDate:new Date().toISOString().slice(0,16),salesRepId:"",conversionNotes:"",followUpRequired:!1,followUpDate:""},createQuotation:!1,quotationData:{title:"",description:"",validUntil:"",items:[]}}),y=async()=>{try{o(!0);let s=await fetch(`/api/leads/${e}/convert`);if(!s.ok)throw Error("Failed to fetch conversion data");let t=await s.json();i(t),t.lead&&v(e=>({...e,customerData:{...e.customerData,name:`${t.lead.firstName} ${t.lead.lastName}`.trim(),email:t.lead.email||"",phone:t.lead.phone||"",company:t.lead.companyName||"",website:t.lead.website||"",industry:t.lead.industry||"",companySize:t.lead.companySize||""}}))}catch(e){E.toast.error("Failed to load conversion data"),console.error("Error fetching conversion data:",e)}finally{o(!1)}};(0,r.useEffect)(()=>{e&&y()},[e]);let g=async()=>{try{p(!0);let t={customerData:j.customerData,conversionData:{...j.conversionData,conversionValue:j.conversionData.conversionValue?parseFloat(j.conversionData.conversionValue):void 0},createQuotation:j.createQuotation,quotationData:j.createQuotation?j.quotationData:void 0},a=await fetch(`/api/leads/${e}/convert`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!a.ok){let e=await a.json();throw Error(e.error||"Failed to convert lead")}await a.json(),E.toast.success("Lead converted successfully!"),m(!1),y(),s?.()}catch(e){E.toast.error(e instanceof Error?e.message:"Failed to convert lead")}finally{p(!1)}},N=e=>{switch(e){case"GOOD":return"text-green-600";case"FAIR":return"text-yellow-600";case"POOR":return"text-red-600";default:return"text-gray-600"}},b=e=>{switch(e){case"URGENT":return"text-red-600 bg-red-100";case"HIGH":return"text-orange-600 bg-orange-100";case"MEDIUM":return"text-yellow-600 bg-yellow-100";case"LOW":return"text-green-600 bg-green-100";default:return"text-gray-600 bg-gray-100"}};return l?a.jsx("div",{className:"flex items-center justify-center py-8",children:a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):t?t.isConverted?(0,a.jsxs)(n.Zb,{children:[a.jsx(n.Ol,{children:(0,a.jsxs)(n.ll,{className:"flex items-center text-green-600",children:[a.jsx(f.Z,{className:"h-5 w-5 mr-2"}),"Lead Successfully Converted"]})}),a.jsx(n.aY,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx(I._,{className:"text-sm font-medium text-gray-500",children:"Customer"}),a.jsx("p",{className:"text-lg font-semibold",children:t.customer?.name})]}),(0,a.jsxs)("div",{children:[a.jsx(I._,{className:"text-sm font-medium text-gray-500",children:"Conversion Date"}),a.jsx("p",{className:"text-lg font-semibold",children:t.conversion?.conversionDate?new Date(t.conversion.conversionDate).toLocaleDateString():"N/A"})]}),(0,a.jsxs)("div",{children:[a.jsx(I._,{className:"text-sm font-medium text-gray-500",children:"Conversion Type"}),a.jsx(d.C,{className:"mt-1",children:t.conversion?.conversionType})]}),(0,a.jsxs)("div",{children:[a.jsx(I._,{className:"text-sm font-medium text-gray-500",children:"Conversion Value"}),a.jsx("p",{className:"text-lg font-semibold",children:t.conversion?.conversionValue?`$${t.conversion.conversionValue.toLocaleString()}`:"Not specified"})]})]}),t.conversion?.conversionNotes&&(0,a.jsxs)("div",{children:[a.jsx(I._,{className:"text-sm font-medium text-gray-500",children:"Notes"}),a.jsx("p",{className:"text-sm text-gray-700 mt-1",children:t.conversion.conversionNotes})]})]})})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(n.Zb,{children:[a.jsx(n.Ol,{children:(0,a.jsxs)(n.ll,{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx(G.Z,{className:"h-5 w-5 mr-2"}),"Conversion Readiness"]}),(0,a.jsxs)(d.C,{className:`${(e=>{switch(e){case"HIGH":return"text-green-600 bg-green-100";case"MEDIUM":return"text-yellow-600 bg-yellow-100";case"LOW":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}})(t.conversionReadiness.level)} text-sm`,children:[t.conversionReadiness.level," (",t.conversionReadiness.percentage,"%)"]})]})}),a.jsx(n.aY,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("div",{className:"w-full bg-gray-200 rounded-full h-3",children:a.jsx("div",{className:`h-3 rounded-full ${"HIGH"===t.conversionReadiness.level?"bg-green-600":"MEDIUM"===t.conversionReadiness.level?"bg-yellow-600":"bg-red-600"}`,style:{width:`${t.conversionReadiness.percentage}%`}})}),a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:t.conversionReadiness.factors.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium",children:e.factor}),a.jsx("p",{className:`text-xs ${N(e.status)}`,children:e.status})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("p",{className:"text-sm font-bold",children:[e.points,"/",e.maxPoints]}),a.jsx("div",{className:"w-16 bg-gray-200 rounded-full h-1 mt-1",children:a.jsx("div",{className:"bg-blue-600 h-1 rounded-full",style:{width:`${e.points/e.maxPoints*100}%`}})})]})]},s))})]})})]}),t.conversionReadiness.recommendations.length>0&&(0,a.jsxs)(n.Zb,{children:[a.jsx(n.Ol,{children:a.jsx(n.ll,{children:"Conversion Recommendations"})}),a.jsx(n.aY,{children:a.jsx("div",{className:"space-y-3",children:t.conversionReadiness.recommendations.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-start space-x-3 p-3 bg-gray-50 rounded-lg",children:[a.jsx(d.C,{className:`${b(e.priority)} text-xs`,children:e.priority}),(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("p",{className:"text-sm font-medium text-gray-900",children:e.message}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["Action: ",e.action]})]})]},s))})})]}),(0,a.jsxs)(n.Zb,{children:[a.jsx(n.Ol,{children:(0,a.jsxs)(n.ll,{className:"flex items-center",children:[a.jsx(et.Z,{className:"h-5 w-5 mr-2"}),"Convert Lead to Customer"]})}),a.jsx(n.aY,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-600",children:"Ready to convert this lead to a customer? This will create a new customer record and update the lead status."}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 mt-2 text-xs text-gray-500",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[a.jsx(C.Z,{className:"h-3 w-3"}),a.jsx("span",{children:"Customer record will be created"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[a.jsx(ea.Z,{className:"h-3 w-3"}),a.jsx("span",{children:"Lead status will be updated"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[a.jsx(h.Z,{className:"h-3 w-3"}),a.jsx("span",{children:"Optional quotation can be generated"})]})]})]}),(0,a.jsxs)(c.z,{onClick:()=>m(!0),className:"flex items-center space-x-2",disabled:"LOW"===t.conversionReadiness.level,children:[a.jsx(et.Z,{className:"h-4 w-4"}),a.jsx("span",{children:"Convert Lead"})]})]})})]}),a.jsx(q.Vq,{open:x,onOpenChange:m,children:(0,a.jsxs)(q.cZ,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[a.jsx(q.fK,{children:a.jsx(q.$N,{children:"Convert Lead to Customer"})}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-medium mb-4",children:"Customer Information"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx(I._,{htmlFor:"name",children:"Full Name *"}),a.jsx(M.I,{id:"name",value:j.customerData.name,onChange:e=>v(s=>({...s,customerData:{...s.customerData,name:e.target.value}})),required:!0})]}),(0,a.jsxs)("div",{children:[a.jsx(I._,{htmlFor:"email",children:"Email *"}),a.jsx(M.I,{id:"email",type:"email",value:j.customerData.email,onChange:e=>v(s=>({...s,customerData:{...s.customerData,email:e.target.value}})),required:!0})]}),(0,a.jsxs)("div",{children:[a.jsx(I._,{htmlFor:"phone",children:"Phone"}),a.jsx(M.I,{id:"phone",value:j.customerData.phone,onChange:e=>v(s=>({...s,customerData:{...s.customerData,phone:e.target.value}}))})]}),(0,a.jsxs)("div",{children:[a.jsx(I._,{htmlFor:"company",children:"Company"}),a.jsx(M.I,{id:"company",value:j.customerData.company,onChange:e=>v(s=>({...s,customerData:{...s.customerData,company:e.target.value}}))})]})]})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-medium mb-4",children:"Conversion Details"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx(I._,{htmlFor:"conversionType",children:"Conversion Type"}),(0,a.jsxs)(O.Ph,{value:j.conversionData.conversionType,onValueChange:e=>v(s=>({...s,conversionData:{...s.conversionData,conversionType:e}})),children:[a.jsx(O.i4,{children:a.jsx(O.ki,{})}),(0,a.jsxs)(O.Bw,{children:[a.jsx(O.Ql,{value:"DIRECT",children:"Direct Sale"}),a.jsx(O.Ql,{value:"QUOTATION",children:"Through Quotation"}),a.jsx(O.Ql,{value:"PROPOSAL",children:"Through Proposal"}),a.jsx(O.Ql,{value:"TRIAL",children:"After Trial"}),a.jsx(O.Ql,{value:"DEMO",children:"After Demo"})]})]})]}),(0,a.jsxs)("div",{children:[a.jsx(I._,{htmlFor:"conversionValue",children:"Conversion Value ($)"}),a.jsx(M.I,{id:"conversionValue",type:"number",step:"0.01",value:j.conversionData.conversionValue,onChange:e=>v(s=>({...s,conversionData:{...s.conversionData,conversionValue:e.target.value}}))})]}),(0,a.jsxs)("div",{children:[a.jsx(I._,{htmlFor:"conversionDate",children:"Conversion Date"}),a.jsx(M.I,{id:"conversionDate",type:"datetime-local",value:j.conversionData.conversionDate,onChange:e=>v(s=>({...s,conversionData:{...s.conversionData,conversionDate:e.target.value}}))})]})]}),(0,a.jsxs)("div",{className:"mt-4",children:[a.jsx(I._,{htmlFor:"conversionNotes",children:"Conversion Notes"}),a.jsx(U.g,{id:"conversionNotes",value:j.conversionData.conversionNotes,onChange:e=>v(s=>({...s,conversionData:{...s.conversionData,conversionNotes:e.target.value}})),rows:3})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[a.jsx(P.X,{id:"followUpRequired",checked:j.conversionData.followUpRequired,onCheckedChange:e=>v(s=>({...s,conversionData:{...s.conversionData,followUpRequired:!!e}}))}),a.jsx(I._,{htmlFor:"followUpRequired",children:"Schedule follow-up"})]}),j.conversionData.followUpRequired&&(0,a.jsxs)("div",{children:[a.jsx(I._,{htmlFor:"followUpDate",children:"Follow-up Date"}),a.jsx(M.I,{id:"followUpDate",type:"datetime-local",value:j.conversionData.followUpDate,onChange:e=>v(s=>({...s,conversionData:{...s.conversionData,followUpDate:e.target.value}}))})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[a.jsx(P.X,{id:"createQuotation",checked:j.createQuotation,onCheckedChange:e=>v(s=>({...s,createQuotation:!!e}))}),a.jsx(I._,{htmlFor:"createQuotation",children:"Create quotation for this customer"})]}),j.createQuotation&&(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx(I._,{htmlFor:"quotationTitle",children:"Quotation Title"}),a.jsx(M.I,{id:"quotationTitle",value:j.quotationData.title,onChange:e=>v(s=>({...s,quotationData:{...s.quotationData,title:e.target.value}}))})]}),(0,a.jsxs)("div",{children:[a.jsx(I._,{htmlFor:"validUntil",children:"Valid Until"}),a.jsx(M.I,{id:"validUntil",type:"date",value:j.quotationData.validUntil,onChange:e=>v(s=>({...s,quotationData:{...s.quotationData,validUntil:e.target.value}}))})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-2 pt-4 border-t",children:[a.jsx(c.z,{variant:"outline",onClick:()=>m(!1),children:"Cancel"}),a.jsx(c.z,{onClick:g,disabled:u||!j.customerData.name||!j.customerData.email,children:u?"Converting...":"Convert Lead"})]})]})]})})]}):(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[a.jsx(w.Z,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),a.jsx("p",{children:"Failed to load conversion data"})]})}var ei=t(63024),el=t(48411),en=t(30304),ec=t(91700),ed=t(20783),eo=t.n(ed);function ex(){let e=(0,i.useParams)(),s=(0,i.useRouter)(),{data:t}=(0,l.useSession)(),[m,j]=(0,r.useState)(null),[v,y]=(0,r.useState)(!0),[g,N]=(0,r.useState)(!1),[b,k]=(0,r.useState)("overview"),D=async()=>{try{let t=await fetch(`/api/leads/${e.id}`);if(!t.ok){if(404===t.status){E.toast.error("Lead not found"),s.push("/dashboard/leads");return}throw Error("Failed to fetch lead")}let a=await t.json();j(a.lead)}catch(e){E.toast.error("Failed to load lead details"),console.error("Error fetching lead:",e)}finally{y(!1)}};(0,r.useEffect)(()=>{e.id&&D()},[e.id]);let T=async()=>{if(m&&confirm(`Are you sure you want to delete "${m.firstName} ${m.lastName}"?`))try{let e=await fetch(`/api/leads/${m.id}`,{method:"DELETE"});if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to delete lead")}E.toast.success("Lead deleted successfully"),s.push("/dashboard/leads")}catch(e){E.toast.error(e instanceof Error?e.message:"Failed to delete lead")}};return v?a.jsx("div",{className:"flex items-center justify-center h-64",children:a.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):m?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx(c.z,{variant:"ghost",size:"sm",asChild:!0,children:(0,a.jsxs)(eo(),{href:"/dashboard/leads",children:[a.jsx(ei.Z,{className:"h-4 w-4 mr-2"}),"Back to Leads"]})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-2xl font-bold text-gray-900",children:[m.firstName," ",m.lastName]}),a.jsx("p",{className:"text-gray-500",children:m.title&&m.companyName?`${m.title} at ${m.companyName}`:m.title||m.companyName||"Lead Details"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx(es.OT,{score:m.score}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(c.z,{variant:"outline",onClick:()=>N(!0),children:[a.jsx(Z.Z,{className:"h-4 w-4 mr-2"}),"Edit"]}),(0,a.jsxs)(c.z,{variant:"destructive",onClick:T,children:[a.jsx(S.Z,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(X.Z,{className:"h-5 w-5 text-blue-600"}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Status"}),(e=>{switch(e){case"NEW":return a.jsx(d.C,{variant:"secondary",children:"New"});case"CONTACTED":return a.jsx(d.C,{className:"bg-blue-100 text-blue-800",children:"Contacted"});case"QUALIFIED":return a.jsx(d.C,{className:"bg-green-100 text-green-800",children:"Qualified"});case"PROPOSAL":return a.jsx(d.C,{className:"bg-yellow-100 text-yellow-800",children:"Proposal"});case"NEGOTIATION":return a.jsx(d.C,{className:"bg-orange-100 text-orange-800",children:"Negotiation"});case"CLOSED_WON":return a.jsx(d.C,{className:"bg-green-100 text-green-800",children:"Closed Won"});case"CLOSED_LOST":return a.jsx(d.C,{variant:"destructive",children:"Closed Lost"});default:return a.jsx(d.C,{variant:"secondary",children:e})}})(m.status)]})]})})}),a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(w.Z,{className:"h-5 w-5 text-orange-600"}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Priority"}),(e=>{switch(e){case"LOW":return a.jsx(d.C,{variant:"secondary",className:"text-xs",children:"Low"});case"MEDIUM":return a.jsx(d.C,{className:"bg-blue-100 text-blue-800 text-xs",children:"Medium"});case"HIGH":return a.jsx(d.C,{className:"bg-orange-100 text-orange-800 text-xs",children:"High"});case"URGENT":return a.jsx(d.C,{variant:"destructive",className:"text-xs",children:"Urgent"});default:return a.jsx(d.C,{variant:"secondary",className:"text-xs",children:e})}})(m.priority)]})]})})}),a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(G.Z,{className:"h-5 w-5 text-green-600"}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Score"}),(0,a.jsxs)("p",{className:"font-semibold",children:[m.score,"/100"]})]})]})})}),a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(el.Z,{className:"h-5 w-5 text-purple-600"}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Budget"}),a.jsx("p",{className:"font-semibold",children:m.budget?`$${m.budget.toLocaleString()}`:"Not specified"})]})]})})})]}),(0,a.jsxs)(o.mQ,{value:b,onValueChange:k,children:[(0,a.jsxs)(o.dr,{children:[a.jsx(o.SP,{value:"overview",children:"Overview"}),(0,a.jsxs)(o.SP,{value:"activities",children:["Activities (",m._count.activities,")"]}),(0,a.jsxs)(o.SP,{value:"notes",children:["Notes (",m._count.leadNotes,")"]}),a.jsx(o.SP,{value:"scoring",children:"Scoring & Qualification"}),a.jsx(o.SP,{value:"conversion",children:"Conversion"}),(0,a.jsxs)(o.SP,{value:"tasks",children:["Tasks (",m._count.tasks,")"]}),(0,a.jsxs)(o.SP,{value:"documents",children:["Documents (",m._count.documents,")"]})]}),a.jsx(o.nU,{value:"overview",className:"space-y-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(n.Zb,{children:[a.jsx(n.Ol,{children:(0,a.jsxs)(n.ll,{className:"flex items-center",children:[a.jsx(C.Z,{className:"h-5 w-5 mr-2"}),"Contact Information"]})}),(0,a.jsxs)(n.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(p.Z,{className:"h-4 w-4 text-gray-400"}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Email"}),a.jsx("p",{className:"font-medium",children:m.email})]})]}),m.phone&&(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(u.Z,{className:"h-4 w-4 text-gray-400"}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Phone"}),a.jsx("p",{className:"font-medium",children:m.phone})]})]}),m.website&&(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(en.Z,{className:"h-4 w-4 text-gray-400"}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Website"}),a.jsx("a",{href:m.website,target:"_blank",rel:"noopener noreferrer",className:"font-medium text-blue-600 hover:underline",children:m.website})]})]})]})]}),(0,a.jsxs)(n.Zb,{children:[a.jsx(n.Ol,{children:(0,a.jsxs)(n.ll,{className:"flex items-center",children:[a.jsx(ec.Z,{className:"h-5 w-5 mr-2"}),"Company Information"]})}),(0,a.jsxs)(n.aY,{className:"space-y-4",children:[m.companyName&&(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Company"}),a.jsx("p",{className:"font-medium",children:m.companyName})]}),m.title&&(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Job Title"}),a.jsx("p",{className:"font-medium",children:m.title})]}),m.industry&&(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Industry"}),a.jsx("p",{className:"font-medium",children:m.industry})]}),m.companySize&&(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Company Size"}),a.jsx("p",{className:"font-medium",children:m.companySize})]})]})]})]})}),a.jsx(o.nU,{value:"activities",children:a.jsx(F,{leadId:m.id})}),a.jsx(o.nU,{value:"notes",children:a.jsx(H,{leadId:m.id})}),a.jsx(o.nU,{value:"scoring",children:a.jsx(ee,{leadId:m.id})}),a.jsx(o.nU,{value:"conversion",children:a.jsx(er,{leadId:m.id,onConversionComplete:()=>window.location.reload()})}),a.jsx(o.nU,{value:"tasks",children:(0,a.jsxs)(n.Zb,{children:[a.jsx(n.Ol,{children:a.jsx(n.ll,{children:"Related Tasks"})}),a.jsx(n.aY,{children:(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[a.jsx(f.Z,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),a.jsx("p",{children:"Task management will be implemented next"})]})})]})}),a.jsx(o.nU,{value:"documents",children:(0,a.jsxs)(n.Zb,{children:[a.jsx(n.Ol,{children:a.jsx(n.ll,{children:"Documents"})}),a.jsx(n.aY,{children:(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[a.jsx(h.Z,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),a.jsx("p",{children:"Document management will be implemented next"})]})})]})})]}),g&&a.jsx(x.p,{isOpen:g,onClose:()=>N(!1),onSuccess:()=>{N(!1),D()},lead:m,mode:"edit"})]}):a.jsx("div",{className:"flex items-center justify-center h-64",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx(w.Z,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Lead Not Found"}),a.jsx("p",{className:"text-gray-500 mb-4",children:"The lead you're looking for doesn't exist."}),a.jsx(c.z,{asChild:!0,children:a.jsx(eo(),{href:"/dashboard/leads",children:"Back to Leads"})})]})})}},27109:(e,s,t)=>{"use strict";t.d(s,{q9:()=>x,OT:()=>m});var a=t(95344),r=t(69436),i=t(69224);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,i.Z)("Flame",[["path",{d:"M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z",key:"96xj49"}]]),n=(0,i.Z)("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]),c=(0,i.Z)("Snowflake",[["line",{x1:"2",x2:"22",y1:"12",y2:"12",key:"1dnqot"}],["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"m20 16-4-4 4-4",key:"rquw4f"}],["path",{d:"m4 8 4 4-4 4",key:"12s3z9"}],["path",{d:"m16 4-4 4-4-4",key:"1tumq1"}],["path",{d:"m8 20 4-4 4 4",key:"9p200w"}]]);var d=t(66985);function o({score:e,size:s="md",showIcon:t=!0,showLabel:i=!0}){let o=e>=70?"HOT":e>=40?"WARM":"COLD",x=(e=>{switch(e){case"HOT":return{color:"text-red-600 bg-red-100 border-red-200",icon:l,label:"Hot Lead",description:"High priority, ready to convert"};case"WARM":return{color:"text-orange-600 bg-orange-100 border-orange-200",icon:n,label:"Warm Lead",description:"Good potential, needs nurturing"};case"COLD":return{color:"text-blue-600 bg-blue-100 border-blue-200",icon:c,label:"Cold Lead",description:"Low engagement, requires attention"};default:return{color:"text-gray-600 bg-gray-100 border-gray-200",icon:d.Z,label:"Unknown",description:"Temperature not determined"}}})(o),m=(e=>{switch(e){case"sm":return{badge:"text-xs px-2 py-1",icon:"h-3 w-3",text:"text-xs"};case"lg":return{badge:"text-base px-4 py-2",icon:"h-5 w-5",text:"text-base"};default:return{badge:"text-sm px-3 py-1",icon:"h-4 w-4",text:"text-sm"}}})(s),h=x.icon;return(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(r.C,{className:`${x.color} border ${m.badge} font-medium`,variant:"outline",children:(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[t&&a.jsx(h,{className:m.icon}),a.jsx("span",{children:o})]})}),i&&(0,a.jsxs)("div",{className:"flex flex-col",children:[a.jsx("span",{className:`${m.text} font-medium text-gray-900`,children:x.label}),"lg"===s&&a.jsx("span",{className:"text-xs text-gray-500",children:x.description})]})]})}function x({score:e}){return a.jsx(o,{score:e,size:"sm",showLabel:!1})}function m({score:e}){return a.jsx(o,{score:e,size:"lg",showIcon:!0,showLabel:!0})}},84332:(e,s,t)=>{"use strict";t.d(s,{X:()=>T});var a=t(95344),r=t(3729),i=t(31405),l=t(98462),n=t(85222),c=t(33183),d=t(92062),o=t(63085),x=t(43234),m=t(62409),h="Checkbox",[u,p]=(0,l.b)(h),[j,v]=u(h);function y(e){let{__scopeCheckbox:s,checked:t,children:i,defaultChecked:l,disabled:n,form:d,name:o,onCheckedChange:x,required:m,value:u="on",internal_do_not_use_render:p}=e,[v,y]=(0,c.T)({prop:t,defaultProp:l??!1,onChange:x,caller:h}),[f,g]=r.useState(null),[N,w]=r.useState(null),b=r.useRef(!1),C=!f||!!d||!!f.closest("form"),k={checked:v,disabled:n,setChecked:y,control:f,setControl:g,name:o,form:d,value:u,hasConsumerStoppedPropagationRef:b,required:m,defaultChecked:!D(l)&&l,isFormControl:C,bubbleInput:N,setBubbleInput:w};return(0,a.jsx)(j,{scope:s,...k,children:"function"==typeof p?p(k):i})}var f="CheckboxTrigger",g=r.forwardRef(({__scopeCheckbox:e,onKeyDown:s,onClick:t,...l},c)=>{let{control:d,value:o,disabled:x,checked:h,required:u,setControl:p,setChecked:j,hasConsumerStoppedPropagationRef:y,isFormControl:g,bubbleInput:N}=v(f,e),w=(0,i.e)(c,p),b=r.useRef(h);return r.useEffect(()=>{let e=d?.form;if(e){let s=()=>j(b.current);return e.addEventListener("reset",s),()=>e.removeEventListener("reset",s)}},[d,j]),(0,a.jsx)(m.WV.button,{type:"button",role:"checkbox","aria-checked":D(h)?"mixed":h,"aria-required":u,"data-state":Z(h),"data-disabled":x?"":void 0,disabled:x,value:o,...l,ref:w,onKeyDown:(0,n.M)(s,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,n.M)(t,e=>{j(e=>!!D(e)||!e),N&&g&&(y.current=e.isPropagationStopped(),y.current||e.stopPropagation())})})});g.displayName=f;var N=r.forwardRef((e,s)=>{let{__scopeCheckbox:t,name:r,checked:i,defaultChecked:l,required:n,disabled:c,value:d,onCheckedChange:o,form:x,...m}=e;return(0,a.jsx)(y,{__scopeCheckbox:t,checked:i,defaultChecked:l,disabled:c,required:n,onCheckedChange:o,name:r,form:x,value:d,internal_do_not_use_render:({isFormControl:e})=>(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g,{...m,ref:s,__scopeCheckbox:t}),e&&(0,a.jsx)(k,{__scopeCheckbox:t})]})})});N.displayName=h;var w="CheckboxIndicator",b=r.forwardRef((e,s)=>{let{__scopeCheckbox:t,forceMount:r,...i}=e,l=v(w,t);return(0,a.jsx)(x.z,{present:r||D(l.checked)||!0===l.checked,children:(0,a.jsx)(m.WV.span,{"data-state":Z(l.checked),"data-disabled":l.disabled?"":void 0,...i,ref:s,style:{pointerEvents:"none",...e.style}})})});b.displayName=w;var C="CheckboxBubbleInput",k=r.forwardRef(({__scopeCheckbox:e,...s},t)=>{let{control:l,hasConsumerStoppedPropagationRef:n,checked:c,defaultChecked:x,required:h,disabled:u,name:p,value:j,form:y,bubbleInput:f,setBubbleInput:g}=v(C,e),N=(0,i.e)(t,g),w=(0,d.D)(c),b=(0,o.t)(l);r.useEffect(()=>{if(!f)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,s=!n.current;if(w!==c&&e){let t=new Event("click",{bubbles:s});f.indeterminate=D(c),e.call(f,!D(c)&&c),f.dispatchEvent(t)}},[f,w,c,n]);let k=r.useRef(!D(c)&&c);return(0,a.jsx)(m.WV.input,{type:"checkbox","aria-hidden":!0,defaultChecked:x??k.current,required:h,disabled:u,name:p,value:j,form:y,...s,tabIndex:-1,ref:N,style:{...s.style,...b,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function D(e){return"indeterminate"===e}function Z(e){return D(e)?"indeterminate":e?"checked":"unchecked"}k.displayName=C;var S=t(62312),E=t(91626);let T=r.forwardRef(({className:e,...s},t)=>a.jsx(N,{ref:t,className:(0,E.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...s,children:a.jsx(b,{className:(0,E.cn)("flex items-center justify-center text-current"),children:a.jsx(S.Z,{className:"h-4 w-4"})})}));T.displayName=N.displayName},17470:(e,s,t)=>{"use strict";t.d(s,{Bw:()=>p,Ph:()=>o,Ql:()=>j,i4:()=>m,ki:()=>x});var a=t(95344),r=t(3729),i=t(1146),l=t(25390),n=t(12704),c=t(62312),d=t(91626);let o=i.fC;i.ZA;let x=i.B4,m=r.forwardRef(({className:e,children:s,...t},r)=>(0,a.jsxs)(i.xz,{ref:r,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...t,children:[s,a.jsx(i.JO,{asChild:!0,children:a.jsx(l.Z,{className:"h-4 w-4 opacity-50"})})]}));m.displayName=i.xz.displayName;let h=r.forwardRef(({className:e,...s},t)=>a.jsx(i.u_,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:a.jsx(n.Z,{className:"h-4 w-4"})}));h.displayName=i.u_.displayName;let u=r.forwardRef(({className:e,...s},t)=>a.jsx(i.$G,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:a.jsx(l.Z,{className:"h-4 w-4"})}));u.displayName=i.$G.displayName;let p=r.forwardRef(({className:e,children:s,position:t="popper",...r},l)=>a.jsx(i.h_,{children:(0,a.jsxs)(i.VY,{ref:l,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:t,...r,children:[a.jsx(h,{}),a.jsx(i.l_,{className:(0,d.cn)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),a.jsx(u,{})]})}));p.displayName=i.VY.displayName,r.forwardRef(({className:e,...s},t)=>a.jsx(i.__,{ref:t,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...s})).displayName=i.__.displayName;let j=r.forwardRef(({className:e,children:s,...t},r)=>(0,a.jsxs)(i.ck,{ref:r,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[a.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:a.jsx(i.wU,{children:a.jsx(c.Z,{className:"h-4 w-4"})})}),a.jsx(i.eT,{children:s})]}));j.displayName=i.ck.displayName,r.forwardRef(({className:e,...s},t)=>a.jsx(i.Z0,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...s})).displayName=i.Z0.displayName},25757:(e,s,t)=>{"use strict";t.d(s,{mQ:()=>E,nU:()=>A,dr:()=>T,SP:()=>L});var a=t(95344),r=t(3729),i=t(85222),l=t(98462),n=t(34504),c=t(43234),d=t(62409),o=t(3975),x=t(33183),m=t(99048),h="Tabs",[u,p]=(0,l.b)(h,[n.Pc]),j=(0,n.Pc)(),[v,y]=u(h),f=r.forwardRef((e,s)=>{let{__scopeTabs:t,value:r,onValueChange:i,defaultValue:l,orientation:n="horizontal",dir:c,activationMode:u="automatic",...p}=e,j=(0,o.gm)(c),[y,f]=(0,x.T)({prop:r,onChange:i,defaultProp:l??"",caller:h});return(0,a.jsx)(v,{scope:t,baseId:(0,m.M)(),value:y,onValueChange:f,orientation:n,dir:j,activationMode:u,children:(0,a.jsx)(d.WV.div,{dir:j,"data-orientation":n,...p,ref:s})})});f.displayName=h;var g="TabsList",N=r.forwardRef((e,s)=>{let{__scopeTabs:t,loop:r=!0,...i}=e,l=y(g,t),c=j(t);return(0,a.jsx)(n.fC,{asChild:!0,...c,orientation:l.orientation,dir:l.dir,loop:r,children:(0,a.jsx)(d.WV.div,{role:"tablist","aria-orientation":l.orientation,...i,ref:s})})});N.displayName=g;var w="TabsTrigger",b=r.forwardRef((e,s)=>{let{__scopeTabs:t,value:r,disabled:l=!1,...c}=e,o=y(w,t),x=j(t),m=D(o.baseId,r),h=Z(o.baseId,r),u=r===o.value;return(0,a.jsx)(n.ck,{asChild:!0,...x,focusable:!l,active:u,children:(0,a.jsx)(d.WV.button,{type:"button",role:"tab","aria-selected":u,"aria-controls":h,"data-state":u?"active":"inactive","data-disabled":l?"":void 0,disabled:l,id:m,...c,ref:s,onMouseDown:(0,i.M)(e.onMouseDown,e=>{l||0!==e.button||!1!==e.ctrlKey?e.preventDefault():o.onValueChange(r)}),onKeyDown:(0,i.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&o.onValueChange(r)}),onFocus:(0,i.M)(e.onFocus,()=>{let e="manual"!==o.activationMode;u||l||!e||o.onValueChange(r)})})})});b.displayName=w;var C="TabsContent",k=r.forwardRef((e,s)=>{let{__scopeTabs:t,value:i,forceMount:l,children:n,...o}=e,x=y(C,t),m=D(x.baseId,i),h=Z(x.baseId,i),u=i===x.value,p=r.useRef(u);return r.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,a.jsx)(c.z,{present:l||u,children:({present:t})=>(0,a.jsx)(d.WV.div,{"data-state":u?"active":"inactive","data-orientation":x.orientation,role:"tabpanel","aria-labelledby":m,hidden:!t,id:h,tabIndex:0,...o,ref:s,style:{...e.style,animationDuration:p.current?"0s":void 0},children:t&&n})})});function D(e,s){return`${e}-trigger-${s}`}function Z(e,s){return`${e}-content-${s}`}k.displayName=C;var S=t(91626);let E=f,T=r.forwardRef(({className:e,...s},t)=>a.jsx(N,{ref:t,className:(0,S.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s}));T.displayName=N.displayName;let L=r.forwardRef(({className:e,...s},t)=>a.jsx(b,{ref:t,className:(0,S.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s}));L.displayName=b.displayName;let A=r.forwardRef(({className:e,...s},t)=>a.jsx(k,{ref:t,className:(0,S.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));A.displayName=k.displayName},88534:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]])},66138:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},35299:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},7060:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},2246:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("CheckSquare",[["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}],["path",{d:"M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11",key:"1jnkn4"}]])},48411:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},30304:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},71206:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},62093:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("MoreHorizontal",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},40626:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},51838:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},31498:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},17910:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},66985:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Thermometer",[["path",{d:"M14 4v10.54a4 4 0 1 1-4 0V4a2 2 0 0 1 4 0Z",key:"17jzev"}]])},77402:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("TrendingDown",[["polyline",{points:"22 17 13.5 8.5 8.5 13.5 2 7",key:"1r2t7k"}],["polyline",{points:"16 17 22 17 22 11",key:"11uiuu"}]])},46064:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},15366:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("UserCheck",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]])},73229:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},4878:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>i,__esModule:()=>r,default:()=>l});let a=(0,t(86843).createProxy)(String.raw`C:\proj\nextjs-saas\app\dashboard\leads\[id]\page.tsx`),{__esModule:r,$$typeof:i}=a,l=a.default}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[1638,7948,6671,4626,7792,2506,8830,7150,2125,5045,8008,5803],()=>t(91944));module.exports=a})();