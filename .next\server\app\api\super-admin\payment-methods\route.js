"use strict";(()=>{var e={};e.id=8826,e.ids=[8826],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},50022:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>f,originalPathname:()=>I,patchFetch:()=>b,requestAsyncStorage:()=>y,routeModule:()=>m,serverHooks:()=>g,staticGenerationAsyncStorage:()=>h,staticGenerationBailout:()=>w});var a={};t.r(a),t.d(a,{GET:()=>u,POST:()=>p});var s=t(95419),n=t(69108),o=t(99678),i=t(78070),l=t(81355),d=t(3205),c=t(9108);async function u(e){try{let e=await (0,l.getServerSession)(d.L);if(!e?.user||"SUPER_ADMIN"!==e.user.role)return i.Z.json({success:!1,error:"Unauthorized"},{status:401});let r=[],t={};try{t=(r=await c._.paymentMethodConfig.findMany()).reduce((e,r)=>(e[r.method]={enabled:r.isEnabled,...r.config},e),{})}catch(e){console.error("Error fetching payment methods:",e),t={creditCard:{enabled:!0,supportedBrands:["visa","mastercard","amex","discover"],requireCVV:!0,require3DS:!1},debitCard:{enabled:!0,supportedBrands:["visa","mastercard"],requireCVV:!0,require3DS:!1},applePay:{enabled:!1,merchantId:""},googlePay:{enabled:!1,merchantId:""},paypal:{enabled:!1,clientId:""},netBanking:{enabled:!1,supportedBanks:[]},ach:{enabled:!1,verificationMethod:"instant"},wire:{enabled:!1,requireManualApproval:!0},upi:{enabled:!1,supportedApps:["gpay","phonepe","paytm"],qrCodeEnabled:!0},bitcoin:{enabled:!1,walletAddress:""},ethereum:{enabled:!1,walletAddress:""},klarna:{enabled:!1,clientId:""},afterpay:{enabled:!1,merchantId:""}}}return i.Z.json({success:!0,methods:r,config:t})}catch(e){return console.error("Error fetching payment methods:",e),i.Z.json({success:!1,error:"Internal server error"},{status:500})}}async function p(e){try{let r=await (0,l.getServerSession)(d.L);if(!r?.user||"SUPER_ADMIN"!==r.user.role)return i.Z.json({success:!1,error:"Unauthorized"},{status:401});let t=await e.json(),a=[];for(let[e,r]of Object.entries(t)){let{enabled:t,...s}=r;try{a.push(c._.paymentMethodConfig.upsert({where:{method:e},update:{isEnabled:t,config:s,updatedAt:new Date},create:{method:e,isEnabled:t,config:s}}))}catch(r){console.error(`Error updating payment method config for ${e}:`,r)}}try{await Promise.all(a)}catch(e){console.error("Error saving payment method config:",e)}try{await c._.auditLog.create({data:{action:"UPDATE_PAYMENT_METHODS",entityType:"PAYMENT_METHOD_CONFIG",entityId:"payment_methods",userId:r.user.id,details:{updatedMethods:Object.keys(t),timestamp:new Date().toISOString()}}})}catch(e){console.error("Error creating audit log:",e)}return i.Z.json({success:!0,message:"Payment method configuration updated successfully"})}catch(e){return console.error("Error updating payment method config:",e),i.Z.json({success:!1,error:"Internal server error"},{status:500})}}let m=new s.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/super-admin/payment-methods/route",pathname:"/api/super-admin/payment-methods",filename:"route",bundlePath:"app/api/super-admin/payment-methods/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\super-admin\\payment-methods\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:y,staticGenerationAsyncStorage:h,serverHooks:g,headerHooks:f,staticGenerationBailout:w}=m,I="/api/super-admin/payment-methods/route";function b(){return(0,o.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:h})}},3205:(e,r,t)=>{t.d(r,{L:()=>d});var a=t(86485),s=t(10375),n=t(50694),o=t(6521),i=t.n(o),l=t(9108);let d={providers:[(0,a.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let r=await l._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),t=r?.companyId;if(!t&&r){let e=await l._.company.findFirst({where:{ownerId:r.id},select:{id:!0}});(t=e?.id)&&await l._.user.update({where:{id:r.id},data:{companyId:t}})}if(!r)return console.log("User not found:",e.email),null;if(!r.password)return console.log("User has no password set:",e.email),null;if(!await i().compare(e.password,r.password))return console.log("Invalid password for user:",e.email),null;return await l._.user.update({where:{id:r.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",r.email),{id:r.id,email:r.email,name:r.name,role:r.role,companyId:t}}catch(e){return console.error("Authentication error:",e),null}}}),(0,s.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,n.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(console.log("JWT callback - user data:",{id:r.id,email:r.email,role:r.role,companyId:r.companyId}),e.role=r.role,e.companyId=r.companyId),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role,e.user.companyId=r.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,r,t)=>{t.d(r,{_:()=>s});let a=require("@prisma/client"),s=globalThis.prisma??new a.PrismaClient}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[1638,6206,6521,2455,4520],()=>t(50022));module.exports=a})();