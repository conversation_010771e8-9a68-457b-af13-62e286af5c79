import{toNestErrors as r,validateFieldsNatively as t}from"@hookform/resolvers";import{plainToClass as s}from"class-transformer";import{validateSync as e,validate as o}from"class-validator";function n(r,t,s={},e=""){return r.reduce((r,s)=>{const o=e?`${e}.${s.property}`:s.property;if(s.constraints){const e=Object.keys(s.constraints)[0];r[o]={type:e,message:s.constraints[e]};const n=r[o];t&&n&&Object.assign(n,{types:s.constraints})}return s.children&&s.children.length&&n(s.children,t,r,o),r},s)}function a(a,i={},c={}){return async(l,d,m)=>{const{transformer:u,validator:p}=i,f=s(a,l,u),h=await("sync"===c.mode?e:o)(f,p);return h.length?{values:{},errors:r(n(h,!m.shouldUseNativeValidation&&"all"===m.criteriaMode),m)}:(m.shouldUseNativeValidation&&t({},m),{values:c.raw?Object.assign({},l):f,errors:{}})}}export{a as classValidatorResolver};
//# sourceMappingURL=class-validator.modern.mjs.map
