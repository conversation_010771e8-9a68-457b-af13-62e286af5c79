'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { 
  CreditCard, 
  Download, 
  RefreshCw, 
  Plus,
  Calendar,
  DollarSign,
  FileText,
  AlertCircle,
  CheckCircle,
  Clock,
  ExternalLink
} from 'lucide-react'
import { toast } from 'sonner'

interface Invoice {
  id: string
  number: string
  status: string
  amount: number
  currency: string
  created: string
  dueDate: string | null
  paidAt: string | null
  description: string
  hostedInvoiceUrl: string
  invoicePdf: string
  periodStart: string
  periodEnd: string
}

interface PaymentMethod {
  id: string
  type: string
  card: {
    brand: string
    last4: string
    expMonth: number
    expYear: number
    funding: string
    country: string
  } | null
  created: string
  isDefault: boolean
}

export default function BillingPage() {
  const { data: session } = useSession()
  const [invoices, setInvoices] = useState<Invoice[]>([])
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([])
  const [loading, setLoading] = useState(true)
  const [actionLoading, setActionLoading] = useState<string | null>(null)

  useEffect(() => {
    if (session?.user) {
      fetchBillingData()
    }
  }, [session])

  const fetchBillingData = async () => {
    try {
      const [invoicesRes, paymentMethodsRes] = await Promise.all([
        fetch('/api/billing/invoices'),
        fetch('/api/billing/payment-methods')
      ])

      const [invoicesData, paymentMethodsData] = await Promise.all([
        invoicesRes.json(),
        paymentMethodsRes.json()
      ])

      if (invoicesData.success) {
        setInvoices(invoicesData.data.invoices)
      }

      if (paymentMethodsData.success) {
        setPaymentMethods(paymentMethodsData.data.paymentMethods)
      }
    } catch (error) {
      console.error('Error fetching billing data:', error)
      toast.error('Failed to load billing data')
    } finally {
      setLoading(false)
    }
  }

  const handleInvoiceAction = async (action: string, invoiceId: string) => {
    setActionLoading(invoiceId)
    try {
      const response = await fetch('/api/billing/invoices', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ action, invoiceId })
      })

      const data = await response.json()

      if (data.success) {
        if (action === 'download_pdf' && data.data.pdfUrl) {
          window.open(data.data.pdfUrl, '_blank')
        } else if (action === 'retry_payment') {
          toast.success('Payment retry initiated')
          fetchBillingData() // Refresh data
        }
      } else {
        toast.error(data.error || 'Action failed')
      }
    } catch (error) {
      console.error('Error handling invoice action:', error)
      toast.error('Failed to perform action')
    } finally {
      setActionLoading(null)
    }
  }

  const handlePaymentMethodAction = async (action: string, paymentMethodId?: string) => {
    setActionLoading(paymentMethodId || 'payment-method')
    try {
      const response = await fetch('/api/billing/payment-methods', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ action, paymentMethodId })
      })

      const data = await response.json()

      if (data.success) {
        if (action === 'create_setup_intent') {
          // In a real app, you would integrate with Stripe Elements here
          toast.info('Payment method setup would open here')
        } else {
          toast.success(data.message)
          fetchBillingData() // Refresh data
        }
      } else {
        toast.error(data.error || 'Action failed')
      }
    } catch (error) {
      console.error('Error handling payment method action:', error)
      toast.error('Failed to perform action')
    } finally {
      setActionLoading(null)
    }
  }

  const getInvoiceStatusIcon = (status: string) => {
    switch (status) {
      case 'paid':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'open':
        return <Clock className="h-4 w-4 text-yellow-500" />
      case 'void':
      case 'uncollectible':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      default:
        return <FileText className="h-4 w-4 text-gray-500" />
    }
  }

  const getInvoiceStatusBadge = (status: string) => {
    const variants: Record<string, any> = {
      paid: 'default',
      open: 'secondary',
      void: 'destructive',
      uncollectible: 'destructive'
    }
    return <Badge variant={variants[status] || 'outline'}>{status}</Badge>
  }

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase()
    }).format(amount / 100)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Billing</h1>
          <p className="text-gray-600">Manage your billing and payment information</p>
        </div>
        <Button onClick={fetchBillingData} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      <Tabs defaultValue="invoices" className="space-y-6">
        <TabsList>
          <TabsTrigger value="invoices">Invoices</TabsTrigger>
          <TabsTrigger value="payment-methods">Payment Methods</TabsTrigger>
        </TabsList>

        <TabsContent value="invoices" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Invoice History
              </CardTitle>
              <CardDescription>
                View and manage your billing invoices
              </CardDescription>
            </CardHeader>
            <CardContent>
              {invoices.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No invoices found
                  </h3>
                  <p className="text-gray-600">
                    Your billing invoices will appear here once you have an active subscription.
                  </p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Invoice</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Period</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {invoices.map((invoice) => (
                      <TableRow key={invoice.id}>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {getInvoiceStatusIcon(invoice.status)}
                            <span className="font-medium">{invoice.number || invoice.id.slice(-8)}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          {getInvoiceStatusBadge(invoice.status)}
                        </TableCell>
                        <TableCell className="font-medium">
                          {formatCurrency(invoice.amount, invoice.currency)}
                        </TableCell>
                        <TableCell>{formatDate(invoice.created)}</TableCell>
                        <TableCell className="text-sm text-gray-600">
                          {formatDate(invoice.periodStart)} - {formatDate(invoice.periodEnd)}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleInvoiceAction('download_pdf', invoice.id)}
                              disabled={actionLoading === invoice.id}
                            >
                              <Download className="h-3 w-3 mr-1" />
                              PDF
                            </Button>
                            {invoice.hostedInvoiceUrl && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => window.open(invoice.hostedInvoiceUrl, '_blank')}
                              >
                                <ExternalLink className="h-3 w-3 mr-1" />
                                View
                              </Button>
                            )}
                            {invoice.status === 'open' && (
                              <Button
                                size="sm"
                                onClick={() => handleInvoiceAction('retry_payment', invoice.id)}
                                disabled={actionLoading === invoice.id}
                              >
                                <RefreshCw className="h-3 w-3 mr-1" />
                                Retry
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="payment-methods" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <CreditCard className="h-5 w-5" />
                    Payment Methods
                  </CardTitle>
                  <CardDescription>
                    Manage your payment methods and billing preferences
                  </CardDescription>
                </div>
                <Button 
                  onClick={() => handlePaymentMethodAction('create_setup_intent')}
                  disabled={actionLoading === 'payment-method'}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Payment Method
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {paymentMethods.length === 0 ? (
                <div className="text-center py-8">
                  <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No payment methods
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Add a payment method to manage your subscription billing.
                  </p>
                  <Button 
                    onClick={() => handlePaymentMethodAction('create_setup_intent')}
                    disabled={actionLoading === 'payment-method'}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Payment Method
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {paymentMethods.map((pm) => (
                    <div key={pm.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <CreditCard className="h-8 w-8 text-gray-400" />
                        <div>
                          <div className="flex items-center gap-2">
                            <span className="font-medium">
                              {pm.card?.brand.toUpperCase()} •••• {pm.card?.last4}
                            </span>
                            {pm.isDefault && (
                              <Badge variant="secondary">Default</Badge>
                            )}
                          </div>
                          <p className="text-sm text-gray-600">
                            Expires {pm.card?.expMonth}/{pm.card?.expYear}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {!pm.isDefault && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handlePaymentMethodAction('set_default', pm.id)}
                            disabled={actionLoading === pm.id}
                          >
                            Set Default
                          </Button>
                        )}
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handlePaymentMethodAction('delete', pm.id)}
                          disabled={actionLoading === pm.id || pm.isDefault}
                        >
                          Remove
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
