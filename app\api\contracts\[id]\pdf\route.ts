import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET /api/contracts/[id]/pdf - Generate PDF for contract
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Fetch contract with all related data
    const contract = await prisma.contract.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId
      },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            company: true,
            address: true,
            city: true,
            state: true,
            country: true,
            postalCode: true
          }
        },
        quotation: {
          select: {
            id: true,
            quotationNumber: true,
            title: true,
            total: true
          }
        },
        invoice: {
          select: {
            id: true,
            invoiceNumber: true,
            total: true,
            status: true
          }
        },
        createdBy: {
          select: {
            name: true,
            email: true
          }
        },
        assignedTo: {
          select: {
            name: true,
            email: true
          }
        },
        company: {
          select: {
            name: true,
            email: true,
            phone: true,
            address: true,
            city: true,
            state: true,
            country: true,
            postalCode: true,
            website: true,
            logo: true
          }
        },
        signatures: {
          orderBy: { createdAt: 'desc' },
          include: {
            signedBy: {
              select: {
                name: true,
                email: true
              }
            }
          }
        }
      }
    })

    if (!contract) {
      return NextResponse.json({ error: 'Contract not found' }, { status: 404 })
    }

    // Generate HTML for PDF
    const html = generateContractHTML({
      contract,
      customer: contract.customer,
      company: contract.company,
      signatures: contract.signatures
    })

    // For now, return HTML (in production, you'd use a PDF library like Puppeteer)
    return new Response(html, {
      headers: {
        'Content-Type': 'text/html',
        'Content-Disposition': `inline; filename="contract-${contract.contractNumber}.html"`
      }
    })

  } catch (error) {
    console.error('Error generating contract PDF:', error)
    return NextResponse.json(
      { error: 'Failed to generate PDF' },
      { status: 500 }
    )
  }
}

function generateContractHTML(data: any) {
  const { contract, customer, company, signatures } = data

  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contract ${contract.contractNumber}</title>
    <style>
        body {
            font-family: 'Times New Roman', serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #333;
            padding-bottom: 20px;
        }
        .contract-title {
            font-size: 28px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 2px;
        }
        .contract-number {
            font-size: 18px;
            color: #666;
            margin-bottom: 10px;
        }
        .contract-date {
            font-size: 14px;
            color: #666;
        }
        .parties-section {
            margin: 40px 0;
            padding: 20px;
            background-color: #f9f9f9;
            border-left: 4px solid #333;
        }
        .party {
            margin-bottom: 30px;
        }
        .party-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            text-transform: uppercase;
        }
        .party-details {
            margin-left: 20px;
            line-height: 1.8;
        }
        .contract-body {
            margin: 40px 0;
            text-align: justify;
        }
        .section-title {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin: 30px 0 15px 0;
            border-bottom: 2px solid #333;
            padding-bottom: 5px;
        }
        .contract-details {
            margin: 30px 0;
            padding: 20px;
            background-color: #f5f5f5;
            border-radius: 5px;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #ddd;
        }
        .detail-row:last-child {
            border-bottom: none;
        }
        .detail-label {
            font-weight: bold;
            color: #555;
        }
        .detail-value {
            color: #333;
        }
        .terms-section {
            margin: 40px 0;
            padding: 20px;
            background-color: #fff;
            border: 2px solid #333;
        }
        .signatures-section {
            margin-top: 60px;
            padding: 30px 0;
            border-top: 2px solid #333;
        }
        .signature-block {
            margin: 40px 0;
            padding: 20px;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
        }
        .signature-line {
            border-bottom: 2px solid #333;
            height: 60px;
            margin: 20px 0;
            position: relative;
        }
        .signature-info {
            margin-top: 10px;
            font-size: 12px;
            color: #666;
        }
        .signed-signature {
            background-color: #e8f5e8;
            border-color: #4caf50;
        }
        .signed-indicator {
            color: #4caf50;
            font-weight: bold;
            font-size: 14px;
        }
        .status-badge {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-draft { background-color: #f3f4f6; color: #374151; }
        .status-review { background-color: #fef3c7; color: #d97706; }
        .status-sent { background-color: #dbeafe; color: #1d4ed8; }
        .status-signed { background-color: #d1fae5; color: #065f46; }
        .status-active { background-color: #dcfce7; color: #166534; }
        .status-completed { background-color: #e0e7ff; color: #3730a3; }
        .status-cancelled { background-color: #fee2e2; color: #dc2626; }
        .status-expired { background-color: #fed7aa; color: #ea580c; }
        .footer {
            margin-top: 60px;
            text-align: center;
            color: #666;
            font-size: 12px;
            border-top: 1px solid #ccc;
            padding-top: 20px;
        }
        .watermark {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            font-size: 72px;
            color: rgba(255, 0, 0, 0.1);
            z-index: -1;
            pointer-events: none;
        }
        @media print {
            body { margin: 0; padding: 20px; }
            .watermark { display: none; }
        }
    </style>
</head>
<body>
    ${contract.status === 'DRAFT' ? '<div class="watermark">DRAFT</div>' : ''}
    ${contract.status === 'CANCELLED' ? '<div class="watermark">CANCELLED</div>' : ''}

    <div class="header">
        <div class="contract-title">Service Agreement</div>
        <div class="contract-number">Contract No: ${contract.contractNumber}</div>
        <div class="contract-date">Date: ${new Date(contract.createdAt).toLocaleDateString()}</div>
        <div style="margin-top: 10px;">
            <span class="status-badge status-${contract.status.toLowerCase()}">${contract.status}</span>
        </div>
    </div>

    <div class="parties-section">
        <div class="party">
            <div class="party-title">Service Provider:</div>
            <div class="party-details">
                <strong>${company.name}</strong><br>
                ${company.address || ''}<br>
                ${company.city || ''}, ${company.state || ''} ${company.postalCode || ''}<br>
                ${company.country || ''}<br>
                Email: ${company.email || ''}<br>
                Phone: ${company.phone || ''}<br>
                ${company.website ? `Website: ${company.website}` : ''}
            </div>
        </div>

        <div class="party">
            <div class="party-title">Client:</div>
            <div class="party-details">
                <strong>${customer.name}</strong><br>
                ${customer.company ? `${customer.company}<br>` : ''}
                ${customer.address ? `${customer.address}<br>` : ''}
                ${customer.city || customer.state || customer.postalCode ? 
                  `${customer.city || ''}, ${customer.state || ''} ${customer.postalCode || ''}<br>` : ''}
                ${customer.country ? `${customer.country}<br>` : ''}
                ${customer.email ? `Email: ${customer.email}<br>` : ''}
                ${customer.phone ? `Phone: ${customer.phone}` : ''}
            </div>
        </div>
    </div>

    <div class="contract-details">
        <div class="section-title">Contract Details</div>
        <div class="detail-row">
            <span class="detail-label">Contract Title:</span>
            <span class="detail-value">${contract.title}</span>
        </div>
        <div class="detail-row">
            <span class="detail-label">Contract Type:</span>
            <span class="detail-value">${contract.type}</span>
        </div>
        ${contract.value ? `
        <div class="detail-row">
            <span class="detail-label">Contract Value:</span>
            <span class="detail-value">${new Intl.NumberFormat('en-US', { style: 'currency', currency: contract.currency }).format(Number(contract.value))}</span>
        </div>
        ` : ''}
        ${contract.startDate ? `
        <div class="detail-row">
            <span class="detail-label">Start Date:</span>
            <span class="detail-value">${new Date(contract.startDate).toLocaleDateString()}</span>
        </div>
        ` : ''}
        ${contract.endDate ? `
        <div class="detail-row">
            <span class="detail-label">End Date:</span>
            <span class="detail-value">${new Date(contract.endDate).toLocaleDateString()}</span>
        </div>
        ` : ''}
        ${contract.autoRenewal ? `
        <div class="detail-row">
            <span class="detail-label">Auto Renewal:</span>
            <span class="detail-value">Yes (${contract.renewalPeriod || 12} months)</span>
        </div>
        ` : ''}
        <div class="detail-row">
            <span class="detail-label">Priority:</span>
            <span class="detail-value">${contract.priority}</span>
        </div>
    </div>

    <div class="contract-body">
        <div class="section-title">Scope of Work</div>
        <p>${contract.description || 'The scope of work will be defined in accordance with the terms and conditions outlined in this agreement.'}</p>

        ${contract.terms ? `
        <div class="section-title">Terms and Conditions</div>
        <div class="terms-section">
            <p>${contract.terms}</p>
        </div>
        ` : ''}

        ${contract.conditions ? `
        <div class="section-title">Additional Conditions</div>
        <p>${contract.conditions}</p>
        ` : ''}

        <div class="section-title">General Terms</div>
        <p>1. <strong>Payment Terms:</strong> Payment shall be made according to the terms specified in related invoices and quotations.</p>
        <p>2. <strong>Confidentiality:</strong> Both parties agree to maintain confidentiality of all proprietary information shared during the course of this agreement.</p>
        <p>3. <strong>Termination:</strong> Either party may terminate this agreement with 30 days written notice.</p>
        <p>4. <strong>Governing Law:</strong> This agreement shall be governed by the laws of the jurisdiction where the service provider is located.</p>
        <p>5. <strong>Dispute Resolution:</strong> Any disputes arising from this agreement shall be resolved through mediation and, if necessary, binding arbitration.</p>
    </div>

    <div class="signatures-section">
        <div class="section-title">Signatures</div>
        
        ${signatures.length > 0 ? `
        <p><strong>This contract has been electronically signed by the following parties:</strong></p>
        ${signatures.map(signature => `
            <div class="signature-block signed-signature">
                <div class="signed-indicator">✓ ELECTRONICALLY SIGNED</div>
                <div style="margin: 15px 0;">
                    <strong>Name:</strong> ${signature.signerName}<br>
                    <strong>Email:</strong> ${signature.signerEmail}<br>
                    ${signature.signerRole ? `<strong>Role:</strong> ${signature.signerRole}<br>` : ''}
                    <strong>Signed:</strong> ${new Date(signature.signedAt).toLocaleString()}<br>
                    <strong>IP Address:</strong> ${signature.ipAddress}<br>
                    ${signature.notes ? `<strong>Notes:</strong> ${signature.notes}` : ''}
                </div>
            </div>
        `).join('')}
        ` : `
        <p><strong>Signature Required:</strong> ${contract.signatureRequired ? 'Yes' : 'No'}</p>
        
        <div class="signature-block">
            <div><strong>Service Provider Signature:</strong></div>
            <div class="signature-line"></div>
            <div class="signature-info">
                Name: ___________________________ Date: _______________<br>
                Title: ___________________________ 
            </div>
        </div>

        <div class="signature-block">
            <div><strong>Client Signature:</strong></div>
            <div class="signature-line"></div>
            <div class="signature-info">
                Name: ___________________________ Date: _______________<br>
                Title: ___________________________
            </div>
        </div>
        `}
    </div>

    ${contract.internalNotes ? `
    <div style="margin-top: 40px; padding: 20px; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px;">
        <div class="section-title" style="color: #856404;">Internal Notes</div>
        <p style="color: #856404;">${contract.internalNotes}</p>
    </div>
    ` : ''}

    <div class="footer">
        <p>This contract was generated on ${new Date().toLocaleDateString()} by ${contract.createdBy.name || contract.createdBy.email}</p>
        <p>Contract ID: ${contract.id} | Company: ${company.name}</p>
        ${contract.quotation ? `<p>Related Quotation: ${contract.quotation.quotationNumber}</p>` : ''}
        ${contract.invoice ? `<p>Related Invoice: ${contract.invoice.invoiceNumber}</p>` : ''}
    </div>
</body>
</html>
  `
}
