(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7702],{90998:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(62898).Z)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]])},11981:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(62898).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},92457:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(62898).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},28203:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(62898).Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},13008:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(62898).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6141:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(62898).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},41298:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(62898).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},97982:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(62898).Z)("FileCheck",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["path",{d:"m9 15 2 2 4-4",key:"1grp1n"}]])},76637:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(62898).Z)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]])},9883:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(62898).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},15713:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(62898).Z)("Receipt",[["path",{d:"M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1-2-1Z",key:"wqdwcb"}],["path",{d:"M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8",key:"1h4pet"}],["path",{d:"M12 17V7",key:"pyj7ub"}]])},66654:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(62898).Z)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},85790:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(62898).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},74527:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(62898).Z)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},25750:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(62898).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},38834:function(e,t,s){Promise.resolve().then(s.bind(s,63920))},63920:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return T}});var a=s(57437),r=s(82749),n=s(2265),c=s(27815),i=s(31478),l=s(85754),o=s(11981),d=s(25750),u=s(74527),x=s(76637),h=s(41298),m=s(97982),g=s(66654),y=s(90998),p=s(15713),f=s(28203),v=s(85790),b=s(92457),j=s(62898);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let N=(0,j.Z)("ArrowUpRight",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]]),w=(0,j.Z)("ArrowDownRight",[["path",{d:"m7 7 10 10",key:"1fmybs"}],["path",{d:"M17 7v10H7",key:"6fjiku"}]]);var k=s(9883),Z=s(6141),C=s(13008);function T(){var e,t,s,j;let{data:T,status:M}=(0,r.useSession)(),[A,L]=(0,n.useState)(null),[R,S]=(0,n.useState)(!0),[E,D]=(0,n.useState)(null);if((0,n.useEffect)(()=>{var e,t;let s=async()=>{try{S(!0),D(null);let e=await fetch("/api/dashboard");if(!e.ok)throw Error("Failed to fetch dashboard data");let t=await e.json();L(t)}catch(e){D(e instanceof Error?e.message:"An error occurred")}finally{S(!1)}};"loading"!==M&&((null==T?void 0:null===(e=T.user)||void 0===e?void 0:e.companyId)?s():!T||(null===(t=T.user)||void 0===t?void 0:t.companyId)?"unauthenticated"===M&&(S(!1),D("Please log in to view dashboard")):(S(!1),D("No company associated with user")))},[null==T?void 0:null===(e=T.user)||void 0===e?void 0:e.companyId,M]),"loading"===M||R)return(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Loading Dashboard"}),(0,a.jsx)("p",{className:"text-gray-500",children:"Please wait while we fetch your data..."})]})});if(E||!A)return(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(o.Z,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Error Loading Dashboard"}),(0,a.jsx)("p",{className:"text-gray-500",children:E||"Failed to load dashboard data"}),(0,a.jsx)(l.z,{onClick:()=>window.location.reload(),className:"mt-4",children:"Retry"})]})});let H=[{title:"Total Customers",value:A.stats.customers.total.toLocaleString(),change:"".concat(A.stats.customers.change>=0?"+":"").concat(A.stats.customers.change.toFixed(1),"%"),changeType:A.stats.customers.change>=0?"increase":"decrease",icon:d.Z,color:"text-blue-600",subtitle:"".concat(A.stats.customers.current," this month")},{title:"Active Leads",value:A.stats.leads.current.toLocaleString(),change:"".concat(A.stats.leads.change>=0?"+":"").concat(A.stats.leads.change.toFixed(1),"%"),changeType:A.stats.leads.change>=0?"increase":"decrease",icon:u.Z,color:"text-green-600",subtitle:"".concat(A.stats.leads.total," total leads")},{title:"Quotations",value:A.stats.quotations.current.toLocaleString(),change:"".concat(A.stats.quotations.change>=0?"+":"").concat(A.stats.quotations.change.toFixed(1),"%"),changeType:A.stats.quotations.change>=0?"increase":"decrease",icon:x.Z,color:"text-yellow-600",subtitle:"".concat(A.stats.quotations.total," total")},{title:"Revenue",value:"$".concat(A.stats.revenue.total.toLocaleString()),change:"".concat(A.stats.revenue.change>=0?"+":"").concat(A.stats.revenue.change.toFixed(1),"%"),changeType:A.stats.revenue.change>=0?"increase":"decrease",icon:h.Z,color:"text-purple-600",subtitle:"$".concat(A.stats.revenue.pending.toLocaleString()," pending")},{title:"Active Contracts",value:A.stats.contracts.current.toLocaleString(),change:"",changeType:"neutral",icon:m.Z,color:"text-indigo-600",subtitle:"".concat(A.stats.contracts.total," total")},{title:"Open Tasks",value:A.stats.tasks.current.toLocaleString(),change:"",changeType:"neutral",icon:g.Z,color:"text-orange-600",subtitle:"".concat(A.stats.tasks.total," total tasks")}],q=A.recentActivities.map(e=>{var t;e.description;let s=y.Z,a="info";switch(e.type){case"NOTE":s=x.Z,a="info";break;case"CALL":s=d.Z,a="success";break;case"EMAIL":s=p.Z,a="info";break;case"MEETING":s=f.Z,a="warning";break;case"STATUS_CHANGE":s=v.Z,a="success";break;case"PAYMENT_RECEIVED":s=h.Z,a="success";break;case"CONTRACT_SIGNED":s=m.Z,a="success";break;default:s=y.Z,a="info"}return{id:e.id,type:e.type,message:e.title,description:e.description,time:new Date(e.createdAt).toLocaleString(),icon:s,status:a,user:(null===(t=e.createdBy)||void 0===t?void 0:t.name)||"System"}}),z=[{title:"Add Lead",description:"Track a new business lead",icon:u.Z,href:"/dashboard/leads/new",color:"text-green-600"},{title:"Add Customer",description:"Create a new customer profile",icon:d.Z,href:"/dashboard/customers/new",color:"text-blue-600"},{title:"Create Quotation",description:"Generate a new quotation",icon:x.Z,href:"/dashboard/quotations/new",color:"text-yellow-600"},{title:"Create Invoice",description:"Generate a new invoice",icon:p.Z,href:"/dashboard/invoices/new",color:"text-purple-600"},{title:"New Contract",description:"Create a new contract",icon:m.Z,href:"/dashboard/contracts/new",color:"text-indigo-600"},{title:"View Reports",description:"Analyze your business data",icon:b.Z,href:"/dashboard/reports",color:"text-orange-600"}];return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 text-white",children:[(0,a.jsxs)("h1",{className:"text-2xl font-bold mb-2",children:["Welcome back, ",(null==T?void 0:null===(t=T.user)||void 0===t?void 0:t.name)||(null==T?void 0:null===(s=T.user)||void 0===s?void 0:s.email)||"User","!"]}),(0,a.jsx)("p",{className:"text-blue-100",children:"Here's what's happening with your business today."}),(0,a.jsxs)("div",{className:"mt-4 flex items-center space-x-4 text-sm",children:[(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(i.C,{variant:"secondary",className:"mr-2",children:(null==T?void 0:null===(j=T.user)||void 0===j?void 0:j.role)||"USER"}),"Role"]}),(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsxs)(i.C,{variant:"outline",className:"mr-2 text-white border-white",children:[A.stats.customers.total," Customers"]}),(0,a.jsxs)(i.C,{variant:"outline",className:"mr-2 text-white border-white",children:[A.stats.leads.current," Active Leads"]})]})]})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4",children:H.map(e=>(0,a.jsxs)(c.Zb,{className:"hover:shadow-md transition-shadow",children:[(0,a.jsxs)(c.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(c.ll,{className:"text-sm font-medium text-gray-600",children:e.title}),(0,a.jsx)(e.icon,{className:"h-5 w-5 ".concat(e.color)})]}),(0,a.jsxs)(c.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:e.value}),e.change&&(0,a.jsxs)("div",{className:"flex items-center mt-1",children:["increase"===e.changeType?(0,a.jsx)(N,{className:"h-4 w-4 text-green-600 mr-1"}):"decrease"===e.changeType?(0,a.jsx)(w,{className:"h-4 w-4 text-red-600 mr-1"}):null,(0,a.jsx)("span",{className:"text-sm font-medium ".concat("increase"===e.changeType?"text-green-600":"decrease"===e.changeType?"text-red-600":"text-gray-500"),children:e.change}),"neutral"!==e.changeType&&(0,a.jsx)("span",{className:"text-sm text-gray-500 ml-1",children:"from last month"})]}),e.subtitle&&(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:e.subtitle})]})]},e.title))}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)(c.Zb,{children:[(0,a.jsx)(c.Ol,{children:(0,a.jsxs)(c.ll,{className:"flex items-center",children:[(0,a.jsx)(k.Z,{className:"h-5 w-5 mr-2"}),"Quick Actions"]})}),(0,a.jsx)(c.aY,{children:(0,a.jsx)("div",{className:"grid grid-cols-2 gap-3",children:z.map(e=>(0,a.jsx)(l.z,{variant:"outline",className:"h-auto p-3 flex flex-col items-center space-y-2 hover:bg-gray-50",asChild:!0,children:(0,a.jsxs)("a",{href:e.href,children:[(0,a.jsx)(e.icon,{className:"h-5 w-5 ".concat(e.color)}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"font-medium text-xs",children:e.title}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:e.description})]})]})},e.title))})})]}),(0,a.jsxs)(c.Zb,{className:"lg:col-span-2",children:[(0,a.jsx)(c.Ol,{children:(0,a.jsxs)(c.ll,{className:"flex items-center",children:[(0,a.jsx)(y.Z,{className:"h-5 w-5 mr-2"}),"Recent Activity"]})}),(0,a.jsx)(c.aY,{children:(0,a.jsx)("div",{className:"space-y-4 max-h-80 overflow-y-auto",children:q.length>0?q.map(e=>(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"p-2 rounded-full ".concat("success"===e.status?"bg-green-100":"warning"===e.status?"bg-yellow-100":"bg-blue-100"),children:(0,a.jsx)(e.icon,{className:"h-4 w-4 ".concat("success"===e.status?"text-green-600":"warning"===e.status?"text-yellow-600":"text-blue-600")})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("p",{className:"text-sm text-gray-900",children:e.message}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center mt-1 text-xs text-gray-400",children:[(0,a.jsx)("span",{children:e.user}),(0,a.jsx)("span",{className:"mx-1",children:"•"}),(0,a.jsx)("span",{children:e.time})]})]})]},e.id)):(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)(y.Z,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,a.jsx)("p",{children:"No recent activity"})]})})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(c.Zb,{children:[(0,a.jsx)(c.Ol,{children:(0,a.jsxs)(c.ll,{className:"flex items-center",children:[(0,a.jsx)(Z.Z,{className:"h-5 w-5 mr-2"}),"Upcoming Tasks"]})}),(0,a.jsx)(c.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:A.upcomingTasks.length>0?A.upcomingTasks.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)("div",{className:"p-2 rounded-full ".concat("HIGH"===e.priority?"bg-red-100":"MEDIUM"===e.priority?"bg-yellow-100":"bg-green-100"),children:(0,a.jsx)(g.Z,{className:"h-4 w-4 ".concat("HIGH"===e.priority?"text-red-600":"MEDIUM"===e.priority?"text-yellow-600":"text-green-600")})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.title}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:["Due: ",new Date(e.dueDate).toLocaleDateString()]}),e.assignedTo&&(0,a.jsxs)("p",{className:"text-xs text-gray-400",children:["Assigned to: ",e.assignedTo.name]})]})]},e.id)):(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)(C.Z,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,a.jsx)("p",{children:"No upcoming tasks"})]})})})]}),(0,a.jsxs)(c.Zb,{children:[(0,a.jsx)(c.Ol,{children:(0,a.jsxs)(c.ll,{className:"flex items-center",children:[(0,a.jsx)(x.Z,{className:"h-5 w-5 mr-2"}),"Upcoming Renewals"]})}),(0,a.jsx)(c.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:A.upcomingRenewals.length>0?A.upcomingRenewals.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)("div",{className:"p-2 rounded-full bg-orange-100",children:(0,a.jsx)(m.Z,{className:"h-4 w-4 text-orange-600"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.title}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:["Customer: ",e.customer.name]}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:["Renewal: ",new Date(e.renewalDate).toLocaleDateString()]})]})]},e.id)):(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)(m.Z,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,a.jsx)("p",{children:"No upcoming renewals"})]})})})]})]})]})}},31478:function(e,t,s){"use strict";s.d(t,{C:function(){return i}});var a=s(57437);s(2265);var r=s(96061),n=s(1657);let c=(0,r.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600",info:"border-transparent bg-blue-500 text-white hover:bg-blue-600"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:s,...r}=e;return(0,a.jsx)("div",{className:(0,n.cn)(c({variant:s}),t),...r})}},85754:function(e,t,s){"use strict";s.d(t,{z:function(){return o}});var a=s(57437),r=s(2265),n=s(67256),c=s(96061),i=s(1657);let l=(0,c.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=r.forwardRef((e,t)=>{let{className:s,variant:r,size:c,asChild:o=!1,...d}=e,u=o?n.g7:"button";return(0,a.jsx)(u,{className:(0,i.cn)(l({variant:r,size:c,className:s})),ref:t,...d})});o.displayName="Button"},27815:function(e,t,s){"use strict";s.d(t,{Ol:function(){return i},SZ:function(){return o},Zb:function(){return c},aY:function(){return d},eW:function(){return u},ll:function(){return l}});var a=s(57437),r=s(2265),n=s(1657);let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...r})});c.displayName="Card";let i=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",s),...r})});i.displayName="CardHeader";let l=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",s),...r})});l.displayName="CardTitle";let o=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",s),...r})});o.displayName="CardDescription";let d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",s),...r})});d.displayName="CardContent";let u=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",s),...r})});u.displayName="CardFooter"},1657:function(e,t,s){"use strict";s.d(t,{cn:function(){return n}});var a=s(57042),r=s(74769);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.m6)((0,a.W)(t))}}},function(e){e.O(0,[6723,2749,2971,4938,1744],function(){return e(e.s=38834)}),_N_E=e.O()}]);