"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/quotations/[id]/pdf/route";
exports.ids = ["app/api/quotations/[id]/pdf/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fquotations%2F%5Bid%5D%2Fpdf%2Froute&page=%2Fapi%2Fquotations%2F%5Bid%5D%2Fpdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fquotations%2F%5Bid%5D%2Fpdf%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fquotations%2F%5Bid%5D%2Fpdf%2Froute&page=%2Fapi%2Fquotations%2F%5Bid%5D%2Fpdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fquotations%2F%5Bid%5D%2Fpdf%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_proj_nextjs_saas_app_api_quotations_id_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/quotations/[id]/pdf/route.ts */ \"(rsc)/./app/api/quotations/[id]/pdf/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/quotations/[id]/pdf/route\",\n        pathname: \"/api/quotations/[id]/pdf\",\n        filename: \"route\",\n        bundlePath: \"app/api/quotations/[id]/pdf/route\"\n    },\n    resolvedPagePath: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\api\\\\quotations\\\\[id]\\\\pdf\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_proj_nextjs_saas_app_api_quotations_id_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/quotations/[id]/pdf/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fquotations%2F%5Bid%5D%2Fpdf%2Froute&page=%2Fapi%2Fquotations%2F%5Bid%5D%2Fpdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fquotations%2F%5Bid%5D%2Fpdf%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/quotations/[id]/pdf/route.ts":
/*!**********************************************!*\
  !*** ./app/api/quotations/[id]/pdf/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n// GET /api/quotations/[id]/pdf - Generate PDF for quotation\nasync function GET(request, { params }) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id || !session?.user?.companyId) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        // Fetch quotation with all related data\n        const quotation = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.quotation.findFirst({\n            where: {\n                id: params.id,\n                companyId: session.user.companyId\n            },\n            include: {\n                customer: {\n                    select: {\n                        id: true,\n                        name: true,\n                        email: true,\n                        phone: true,\n                        company: true,\n                        address: true,\n                        city: true,\n                        state: true,\n                        country: true,\n                        postalCode: true\n                    }\n                },\n                items: {\n                    orderBy: {\n                        createdAt: \"asc\"\n                    }\n                },\n                createdBy: {\n                    select: {\n                        name: true,\n                        email: true\n                    }\n                },\n                company: {\n                    select: {\n                        name: true,\n                        email: true,\n                        phone: true,\n                        address: true,\n                        city: true,\n                        state: true,\n                        country: true,\n                        postalCode: true,\n                        website: true,\n                        logo: true\n                    }\n                }\n            }\n        });\n        if (!quotation) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Quotation not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Calculate totals\n        const itemTotals = quotation.items.map((item)=>{\n            const lineTotal = Number(item.quantity) * Number(item.unitPrice);\n            const discountAmount = lineTotal * Number(item.discount) / 100;\n            const subtotal = lineTotal - discountAmount;\n            const taxAmount = subtotal * Number(item.taxRate) / 100;\n            const total = subtotal + taxAmount;\n            return {\n                ...item,\n                lineTotal: Math.round(lineTotal * 100) / 100,\n                discountAmount: Math.round(discountAmount * 100) / 100,\n                subtotal: Math.round(subtotal * 100) / 100,\n                taxAmount: Math.round(taxAmount * 100) / 100,\n                total: Math.round(total * 100) / 100\n            };\n        });\n        const subtotal = itemTotals.reduce((sum, item)=>sum + item.subtotal, 0);\n        const totalDiscount = itemTotals.reduce((sum, item)=>sum + item.discountAmount, 0);\n        const totalTax = itemTotals.reduce((sum, item)=>sum + item.taxAmount, 0);\n        const grandTotal = subtotal + totalTax;\n        // Apply quotation-level discount\n        const quotationDiscountAmount = quotation.discountType === \"PERCENTAGE\" ? subtotal * Number(quotation.discountValue) / 100 : Number(quotation.discountValue);\n        const finalSubtotal = subtotal - quotationDiscountAmount;\n        const finalTax = finalSubtotal * Number(quotation.taxRate) / 100;\n        const finalTotal = finalSubtotal + finalTax;\n        // Generate HTML for PDF\n        const html = generateQuotationHTML({\n            quotation,\n            customer: quotation.customer,\n            company: quotation.company,\n            items: itemTotals,\n            totals: {\n                subtotal: Math.round(subtotal * 100) / 100,\n                totalDiscount: Math.round(totalDiscount * 100) / 100,\n                quotationDiscount: Math.round(quotationDiscountAmount * 100) / 100,\n                finalSubtotal: Math.round(finalSubtotal * 100) / 100,\n                totalTax: Math.round(finalTax * 100) / 100,\n                grandTotal: Math.round(finalTotal * 100) / 100\n            }\n        });\n        // For now, return HTML (in production, you'd use a PDF library like Puppeteer)\n        return new Response(html, {\n            headers: {\n                \"Content-Type\": \"text/html\",\n                \"Content-Disposition\": `inline; filename=\"quotation-${quotation.quotationNumber}.html\"`\n            }\n        });\n    } catch (error) {\n        console.error(\"Error generating quotation PDF:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to generate PDF\"\n        }, {\n            status: 500\n        });\n    }\n}\nfunction generateQuotationHTML(data) {\n    const { quotation, customer, company, items, totals } = data;\n    return `\n<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Quotation ${quotation.quotationNumber}</title>\n    <style>\n        body {\n            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n            line-height: 1.6;\n            color: #333;\n            max-width: 800px;\n            margin: 0 auto;\n            padding: 20px;\n        }\n        .header {\n            display: flex;\n            justify-content: space-between;\n            align-items: flex-start;\n            margin-bottom: 40px;\n            border-bottom: 2px solid #e5e7eb;\n            padding-bottom: 20px;\n        }\n        .company-info {\n            flex: 1;\n        }\n        .company-name {\n            font-size: 24px;\n            font-weight: bold;\n            color: #1f2937;\n            margin-bottom: 10px;\n        }\n        .quotation-info {\n            text-align: right;\n            flex: 1;\n        }\n        .quotation-title {\n            font-size: 28px;\n            font-weight: bold;\n            color: #3b82f6;\n            margin-bottom: 10px;\n        }\n        .quotation-number {\n            font-size: 18px;\n            color: #6b7280;\n            margin-bottom: 5px;\n        }\n        .customer-section {\n            margin: 30px 0;\n            padding: 20px;\n            background-color: #f9fafb;\n            border-radius: 8px;\n        }\n        .section-title {\n            font-size: 18px;\n            font-weight: bold;\n            color: #1f2937;\n            margin-bottom: 15px;\n            border-bottom: 1px solid #d1d5db;\n            padding-bottom: 5px;\n        }\n        .items-table {\n            width: 100%;\n            border-collapse: collapse;\n            margin: 30px 0;\n        }\n        .items-table th,\n        .items-table td {\n            padding: 12px;\n            text-align: left;\n            border-bottom: 1px solid #e5e7eb;\n        }\n        .items-table th {\n            background-color: #f3f4f6;\n            font-weight: bold;\n            color: #374151;\n        }\n        .items-table .number {\n            text-align: right;\n        }\n        .totals-section {\n            margin-top: 30px;\n            padding: 20px;\n            background-color: #f9fafb;\n            border-radius: 8px;\n        }\n        .totals-table {\n            width: 100%;\n            max-width: 400px;\n            margin-left: auto;\n        }\n        .totals-table td {\n            padding: 8px 12px;\n            border: none;\n        }\n        .totals-table .label {\n            text-align: right;\n            font-weight: 500;\n        }\n        .totals-table .amount {\n            text-align: right;\n            font-weight: bold;\n        }\n        .grand-total {\n            border-top: 2px solid #374151;\n            font-size: 18px;\n            color: #1f2937;\n        }\n        .terms-section {\n            margin-top: 40px;\n            padding: 20px;\n            background-color: #fef3c7;\n            border-radius: 8px;\n            border-left: 4px solid #f59e0b;\n        }\n        .notes-section {\n            margin-top: 20px;\n            padding: 20px;\n            background-color: #eff6ff;\n            border-radius: 8px;\n            border-left: 4px solid #3b82f6;\n        }\n        .footer {\n            margin-top: 40px;\n            text-align: center;\n            color: #6b7280;\n            font-size: 14px;\n            border-top: 1px solid #e5e7eb;\n            padding-top: 20px;\n        }\n        .status-badge {\n            display: inline-block;\n            padding: 4px 12px;\n            border-radius: 20px;\n            font-size: 12px;\n            font-weight: bold;\n            text-transform: uppercase;\n        }\n        .status-draft { background-color: #f3f4f6; color: #374151; }\n        .status-sent { background-color: #dbeafe; color: #1d4ed8; }\n        .status-viewed { background-color: #fef3c7; color: #d97706; }\n        .status-accepted { background-color: #d1fae5; color: #065f46; }\n        .status-rejected { background-color: #fee2e2; color: #dc2626; }\n        .status-expired { background-color: #fed7aa; color: #ea580c; }\n    </style>\n</head>\n<body>\n    <div class=\"header\">\n        <div class=\"company-info\">\n            <div class=\"company-name\">${company.name}</div>\n            <div>${company.address || \"\"}</div>\n            <div>${company.city || \"\"}, ${company.state || \"\"} ${company.postalCode || \"\"}</div>\n            <div>${company.country || \"\"}</div>\n            <div>Email: ${company.email || \"\"}</div>\n            <div>Phone: ${company.phone || \"\"}</div>\n            ${company.website ? `<div>Website: ${company.website}</div>` : \"\"}\n        </div>\n        <div class=\"quotation-info\">\n            <div class=\"quotation-title\">QUOTATION</div>\n            <div class=\"quotation-number\">${quotation.quotationNumber}</div>\n            <div>Date: ${new Date(quotation.createdAt).toLocaleDateString()}</div>\n            ${quotation.validUntil ? `<div>Valid Until: ${new Date(quotation.validUntil).toLocaleDateString()}</div>` : \"\"}\n            <div class=\"status-badge status-${quotation.status.toLowerCase()}\">${quotation.status}</div>\n        </div>\n    </div>\n\n    <div class=\"customer-section\">\n        <div class=\"section-title\">Bill To:</div>\n        <div><strong>${customer.name}</strong></div>\n        ${customer.company ? `<div>${customer.company}</div>` : \"\"}\n        ${customer.address ? `<div>${customer.address}</div>` : \"\"}\n        ${customer.city || customer.state || customer.postalCode ? `<div>${customer.city || \"\"}, ${customer.state || \"\"} ${customer.postalCode || \"\"}</div>` : \"\"}\n        ${customer.country ? `<div>${customer.country}</div>` : \"\"}\n        ${customer.email ? `<div>Email: ${customer.email}</div>` : \"\"}\n        ${customer.phone ? `<div>Phone: ${customer.phone}</div>` : \"\"}\n    </div>\n\n    ${quotation.title ? `\n    <div class=\"section-title\">Project: ${quotation.title}</div>\n    ` : \"\"}\n\n    ${quotation.description ? `\n    <div style=\"margin: 20px 0;\">\n        <div class=\"section-title\">Description:</div>\n        <p>${quotation.description}</p>\n    </div>\n    ` : \"\"}\n\n    <table class=\"items-table\">\n        <thead>\n            <tr>\n                <th>Description</th>\n                <th class=\"number\">Qty</th>\n                <th class=\"number\">Unit Price</th>\n                <th class=\"number\">Discount</th>\n                <th class=\"number\">Tax</th>\n                <th class=\"number\">Total</th>\n            </tr>\n        </thead>\n        <tbody>\n            ${items.map((item)=>`\n                <tr>\n                    <td>\n                        <strong>${item.description}</strong>\n                    </td>\n                    <td class=\"number\">${Number(item.quantity)}</td>\n                    <td class=\"number\">$${Number(item.unitPrice).toFixed(2)}</td>\n                    <td class=\"number\">${Number(item.discount)}%</td>\n                    <td class=\"number\">${Number(item.taxRate)}%</td>\n                    <td class=\"number\">$${item.total.toFixed(2)}</td>\n                </tr>\n            `).join(\"\")}\n        </tbody>\n    </table>\n\n    <div class=\"totals-section\">\n        <table class=\"totals-table\">\n            <tr>\n                <td class=\"label\">Subtotal:</td>\n                <td class=\"amount\">$${totals.subtotal.toFixed(2)}</td>\n            </tr>\n            ${totals.quotationDiscount > 0 ? `\n            <tr>\n                <td class=\"label\">Discount:</td>\n                <td class=\"amount\">-$${totals.quotationDiscount.toFixed(2)}</td>\n            </tr>\n            ` : \"\"}\n            <tr>\n                <td class=\"label\">Tax:</td>\n                <td class=\"amount\">$${totals.totalTax.toFixed(2)}</td>\n            </tr>\n            <tr class=\"grand-total\">\n                <td class=\"label\">Total:</td>\n                <td class=\"amount\">$${totals.grandTotal.toFixed(2)}</td>\n            </tr>\n        </table>\n    </div>\n\n    ${quotation.terms ? `\n    <div class=\"terms-section\">\n        <div class=\"section-title\">Terms & Conditions:</div>\n        <p>${quotation.terms}</p>\n    </div>\n    ` : \"\"}\n\n    ${quotation.notes ? `\n    <div class=\"notes-section\">\n        <div class=\"section-title\">Notes:</div>\n        <p>${quotation.notes}</p>\n    </div>\n    ` : \"\"}\n\n    <div class=\"footer\">\n        <p>Thank you for your business!</p>\n        <p>Generated on ${new Date().toLocaleDateString()} by ${quotation.createdBy.name || quotation.createdBy.email}</p>\n    </div>\n</body>\n</html>\n  `;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/quotations/[id]/pdf/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/github */ \"(rsc)/./node_modules/next-auth/providers/github.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n\nconst authOptions = {\n    // Using JWT strategy instead of database adapter for now\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                try {\n                    if (!credentials?.email || !credentials?.password) {\n                        console.log(\"Missing credentials\");\n                        return null;\n                    }\n                    console.log(\"Attempting to authenticate user:\", credentials.email);\n                    const user = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.findUnique({\n                        where: {\n                            email: credentials.email\n                        },\n                        select: {\n                            id: true,\n                            email: true,\n                            name: true,\n                            password: true,\n                            role: true,\n                            companyId: true\n                        }\n                    });\n                    // Get the company ID - either as member or owner\n                    let finalCompanyId = user?.companyId;\n                    if (!finalCompanyId && user) {\n                        const ownedCompany = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.company.findFirst({\n                            where: {\n                                ownerId: user.id\n                            },\n                            select: {\n                                id: true\n                            }\n                        });\n                        finalCompanyId = ownedCompany?.id;\n                        // If user is company owner, update their companyId for future queries\n                        if (finalCompanyId) {\n                            await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                                where: {\n                                    id: user.id\n                                },\n                                data: {\n                                    companyId: finalCompanyId\n                                }\n                            });\n                        }\n                    }\n                    if (!user) {\n                        console.log(\"User not found:\", credentials.email);\n                        return null;\n                    }\n                    if (!user.password) {\n                        console.log(\"User has no password set:\", credentials.email);\n                        return null;\n                    }\n                    const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_3___default().compare(credentials.password, user.password);\n                    if (!isPasswordValid) {\n                        console.log(\"Invalid password for user:\", credentials.email);\n                        return null;\n                    }\n                    // Update last login\n                    await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                        where: {\n                            id: user.id\n                        },\n                        data: {\n                            lastLoginAt: new Date(),\n                            loginCount: {\n                                increment: 1\n                            }\n                        }\n                    });\n                    console.log(\"User authenticated successfully:\", user.email);\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name,\n                        role: user.role,\n                        companyId: finalCompanyId\n                    };\n                } catch (error) {\n                    console.error(\"Authentication error:\", error);\n                    return null;\n                }\n            }\n        }),\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            clientId: process.env.GITHUB_CLIENT_ID,\n            clientSecret: process.env.GITHUB_CLIENT_SECRET\n        })\n    ],\n    session: {\n        strategy: \"jwt\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                console.log(\"JWT callback - user data:\", {\n                    id: user.id,\n                    email: user.email,\n                    role: user.role,\n                    companyId: user.companyId\n                });\n                token.role = user.role;\n                token.companyId = user.companyId;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n                session.user.companyId = token.companyId;\n                console.log(\"Session callback - final session:\", {\n                    id: session.user.id,\n                    email: session.user.email,\n                    role: session.user.role,\n                    companyId: session.user.companyId\n                });\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        signUp: \"/auth/signup\",\n        error: \"/auth/error\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zYWFzLWFwcC8uL2xpYi9wcmlzbWEudHM/OTgyMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/lru-cache","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fquotations%2F%5Bid%5D%2Fpdf%2Froute&page=%2Fapi%2Fquotations%2F%5Bid%5D%2Fpdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fquotations%2F%5Bid%5D%2Fpdf%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();