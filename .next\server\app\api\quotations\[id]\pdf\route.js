"use strict";(()=>{var t={};t.id=8361,t.ids=[8361],t.modules={30517:t=>{t.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:t=>{t.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:t=>{t.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:t=>{t.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:t=>{t.exports=require("assert")},14300:t=>{t.exports=require("buffer")},6113:t=>{t.exports=require("crypto")},82361:t=>{t.exports=require("events")},13685:t=>{t.exports=require("http")},95687:t=>{t.exports=require("https")},63477:t=>{t.exports=require("querystring")},57310:t=>{t.exports=require("url")},73837:t=>{t.exports=require("util")},59796:t=>{t.exports=require("zlib")},70324:(t,e,o)=>{o.r(e),o.d(e,{headerHooks:()=>x,originalPathname:()=>h,patchFetch:()=>v,requestAsyncStorage:()=>m,routeModule:()=>p,serverHooks:()=>g,staticGenerationAsyncStorage:()=>b,staticGenerationBailout:()=>f});var a={};o.r(a),o.d(a,{GET:()=>u});var i=o(95419),r=o(69108),s=o(99678),n=o(78070),d=o(81355),l=o(3205),c=o(9108);async function u(t,{params:e}){try{let t=await (0,d.getServerSession)(l.L);if(!t?.user?.id||!t?.user?.companyId)return n.Z.json({error:"Unauthorized"},{status:401});let o=await c._.quotation.findFirst({where:{id:e.id,companyId:t.user.companyId},include:{customer:{select:{id:!0,name:!0,email:!0,phone:!0,company:!0,address:!0,city:!0,state:!0,country:!0,postalCode:!0}},items:{orderBy:{createdAt:"asc"}},createdBy:{select:{name:!0,email:!0}},company:{select:{name:!0,email:!0,phone:!0,address:!0,city:!0,state:!0,country:!0,postalCode:!0,website:!0,logo:!0}}}});if(!o)return n.Z.json({error:"Quotation not found"},{status:404});let a=o.items.map(t=>{let e=Number(t.quantity)*Number(t.unitPrice),o=e*Number(t.discount)/100,a=e-o,i=a*Number(t.taxRate)/100;return{...t,lineTotal:Math.round(100*e)/100,discountAmount:Math.round(100*o)/100,subtotal:Math.round(100*a)/100,taxAmount:Math.round(100*i)/100,total:Math.round(100*(a+i))/100}}),i=a.reduce((t,e)=>t+e.subtotal,0),r=a.reduce((t,e)=>t+e.discountAmount,0);a.reduce((t,e)=>t+e.taxAmount,0);let s="PERCENTAGE"===o.discountType?i*Number(o.discountValue)/100:Number(o.discountValue),u=i-s,p=u*Number(o.taxRate)/100,m=function(t){let{quotation:e,customer:o,company:a,items:i,totals:r}=t;return`
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quotation ${e.quotationNumber}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 40px;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 20px;
        }
        .company-info {
            flex: 1;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 10px;
        }
        .quotation-info {
            text-align: right;
            flex: 1;
        }
        .quotation-title {
            font-size: 28px;
            font-weight: bold;
            color: #3b82f6;
            margin-bottom: 10px;
        }
        .quotation-number {
            font-size: 18px;
            color: #6b7280;
            margin-bottom: 5px;
        }
        .customer-section {
            margin: 30px 0;
            padding: 20px;
            background-color: #f9fafb;
            border-radius: 8px;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 15px;
            border-bottom: 1px solid #d1d5db;
            padding-bottom: 5px;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
        }
        .items-table th,
        .items-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        .items-table th {
            background-color: #f3f4f6;
            font-weight: bold;
            color: #374151;
        }
        .items-table .number {
            text-align: right;
        }
        .totals-section {
            margin-top: 30px;
            padding: 20px;
            background-color: #f9fafb;
            border-radius: 8px;
        }
        .totals-table {
            width: 100%;
            max-width: 400px;
            margin-left: auto;
        }
        .totals-table td {
            padding: 8px 12px;
            border: none;
        }
        .totals-table .label {
            text-align: right;
            font-weight: 500;
        }
        .totals-table .amount {
            text-align: right;
            font-weight: bold;
        }
        .grand-total {
            border-top: 2px solid #374151;
            font-size: 18px;
            color: #1f2937;
        }
        .terms-section {
            margin-top: 40px;
            padding: 20px;
            background-color: #fef3c7;
            border-radius: 8px;
            border-left: 4px solid #f59e0b;
        }
        .notes-section {
            margin-top: 20px;
            padding: 20px;
            background-color: #eff6ff;
            border-radius: 8px;
            border-left: 4px solid #3b82f6;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            color: #6b7280;
            font-size: 14px;
            border-top: 1px solid #e5e7eb;
            padding-top: 20px;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-draft { background-color: #f3f4f6; color: #374151; }
        .status-sent { background-color: #dbeafe; color: #1d4ed8; }
        .status-viewed { background-color: #fef3c7; color: #d97706; }
        .status-accepted { background-color: #d1fae5; color: #065f46; }
        .status-rejected { background-color: #fee2e2; color: #dc2626; }
        .status-expired { background-color: #fed7aa; color: #ea580c; }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-info">
            <div class="company-name">${a.name}</div>
            <div>${a.address||""}</div>
            <div>${a.city||""}, ${a.state||""} ${a.postalCode||""}</div>
            <div>${a.country||""}</div>
            <div>Email: ${a.email||""}</div>
            <div>Phone: ${a.phone||""}</div>
            ${a.website?`<div>Website: ${a.website}</div>`:""}
        </div>
        <div class="quotation-info">
            <div class="quotation-title">QUOTATION</div>
            <div class="quotation-number">${e.quotationNumber}</div>
            <div>Date: ${new Date(e.createdAt).toLocaleDateString()}</div>
            ${e.validUntil?`<div>Valid Until: ${new Date(e.validUntil).toLocaleDateString()}</div>`:""}
            <div class="status-badge status-${e.status.toLowerCase()}">${e.status}</div>
        </div>
    </div>

    <div class="customer-section">
        <div class="section-title">Bill To:</div>
        <div><strong>${o.name}</strong></div>
        ${o.company?`<div>${o.company}</div>`:""}
        ${o.address?`<div>${o.address}</div>`:""}
        ${o.city||o.state||o.postalCode?`<div>${o.city||""}, ${o.state||""} ${o.postalCode||""}</div>`:""}
        ${o.country?`<div>${o.country}</div>`:""}
        ${o.email?`<div>Email: ${o.email}</div>`:""}
        ${o.phone?`<div>Phone: ${o.phone}</div>`:""}
    </div>

    ${e.title?`
    <div class="section-title">Project: ${e.title}</div>
    `:""}

    ${e.description?`
    <div style="margin: 20px 0;">
        <div class="section-title">Description:</div>
        <p>${e.description}</p>
    </div>
    `:""}

    <table class="items-table">
        <thead>
            <tr>
                <th>Description</th>
                <th class="number">Qty</th>
                <th class="number">Unit Price</th>
                <th class="number">Discount</th>
                <th class="number">Tax</th>
                <th class="number">Total</th>
            </tr>
        </thead>
        <tbody>
            ${i.map(t=>`
                <tr>
                    <td>
                        <strong>${t.description}</strong>
                    </td>
                    <td class="number">${Number(t.quantity)}</td>
                    <td class="number">$${Number(t.unitPrice).toFixed(2)}</td>
                    <td class="number">${Number(t.discount)}%</td>
                    <td class="number">${Number(t.taxRate)}%</td>
                    <td class="number">$${t.total.toFixed(2)}</td>
                </tr>
            `).join("")}
        </tbody>
    </table>

    <div class="totals-section">
        <table class="totals-table">
            <tr>
                <td class="label">Subtotal:</td>
                <td class="amount">$${r.subtotal.toFixed(2)}</td>
            </tr>
            ${r.quotationDiscount>0?`
            <tr>
                <td class="label">Discount:</td>
                <td class="amount">-$${r.quotationDiscount.toFixed(2)}</td>
            </tr>
            `:""}
            <tr>
                <td class="label">Tax:</td>
                <td class="amount">$${r.totalTax.toFixed(2)}</td>
            </tr>
            <tr class="grand-total">
                <td class="label">Total:</td>
                <td class="amount">$${r.grandTotal.toFixed(2)}</td>
            </tr>
        </table>
    </div>

    ${e.terms?`
    <div class="terms-section">
        <div class="section-title">Terms & Conditions:</div>
        <p>${e.terms}</p>
    </div>
    `:""}

    ${e.notes?`
    <div class="notes-section">
        <div class="section-title">Notes:</div>
        <p>${e.notes}</p>
    </div>
    `:""}

    <div class="footer">
        <p>Thank you for your business!</p>
        <p>Generated on ${new Date().toLocaleDateString()} by ${e.createdBy.name||e.createdBy.email}</p>
    </div>
</body>
</html>
  `}({quotation:o,customer:o.customer,company:o.company,items:a,totals:{subtotal:Math.round(100*i)/100,totalDiscount:Math.round(100*r)/100,quotationDiscount:Math.round(100*s)/100,finalSubtotal:Math.round(100*u)/100,totalTax:Math.round(100*p)/100,grandTotal:Math.round(100*(u+p))/100}});return new Response(m,{headers:{"Content-Type":"text/html","Content-Disposition":`inline; filename="quotation-${o.quotationNumber}.html"`}})}catch(t){return console.error("Error generating quotation PDF:",t),n.Z.json({error:"Failed to generate PDF"},{status:500})}}let p=new i.AppRouteRouteModule({definition:{kind:r.x.APP_ROUTE,page:"/api/quotations/[id]/pdf/route",pathname:"/api/quotations/[id]/pdf",filename:"route",bundlePath:"app/api/quotations/[id]/pdf/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\quotations\\[id]\\pdf\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:m,staticGenerationAsyncStorage:b,serverHooks:g,headerHooks:x,staticGenerationBailout:f}=p,h="/api/quotations/[id]/pdf/route";function v(){return(0,s.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:b})}},3205:(t,e,o)=>{o.d(e,{L:()=>l});var a=o(86485),i=o(10375),r=o(50694),s=o(6521),n=o.n(s),d=o(9108);let l={providers:[(0,a.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(t){try{if(!t?.email||!t?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",t.email);let e=await d._.user.findUnique({where:{email:t.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),o=e?.companyId;if(!o&&e){let t=await d._.company.findFirst({where:{ownerId:e.id},select:{id:!0}});(o=t?.id)&&await d._.user.update({where:{id:e.id},data:{companyId:o}})}if(!e)return console.log("User not found:",t.email),null;if(!e.password)return console.log("User has no password set:",t.email),null;if(!await n().compare(t.password,e.password))return console.log("Invalid password for user:",t.email),null;return await d._.user.update({where:{id:e.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",e.email),{id:e.id,email:e.email,name:e.name,role:e.role,companyId:o}}catch(t){return console.error("Authentication error:",t),null}}}),(0,i.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,r.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:t,user:e})=>(e&&(console.log("JWT callback - user data:",{id:e.id,email:e.email,role:e.role,companyId:e.companyId}),t.role=e.role,t.companyId=e.companyId),t),session:async({session:t,token:e})=>(e&&(t.user.id=e.sub,t.user.role=e.role,t.user.companyId=e.companyId,t.user.company&&delete t.user.company,console.log("Session callback - final session:",{id:t.user.id,email:t.user.email,role:t.user.role,companyId:t.user.companyId})),t)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(t,e,o)=>{o.d(e,{_:()=>i});let a=require("@prisma/client"),i=globalThis.prisma??new a.PrismaClient}};var e=require("../../../../../webpack-runtime.js");e.C(t);var o=t=>e(e.s=t),a=e.X(0,[1638,6206,6521,2455,4520],()=>o(70324));module.exports=a})();