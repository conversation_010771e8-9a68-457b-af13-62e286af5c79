(()=>{var e={};e.id=1931,e.ids=[1931],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},86480:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>c});var s=r(50482),a=r(69108),n=r(62563),i=r.n(n),o=r(68300),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,74782)),"C:\\proj\\nextjs-saas\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,59504)),"C:\\proj\\nextjs-saas\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\proj\\nextjs-saas\\app\\page.tsx"],u="/page",m={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},32919:(e,t,r)=>{Promise.resolve().then(r.bind(r,54358))},64588:(e,t,r)=>{Promise.resolve().then(r.bind(r,56189)),Promise.resolve().then(r.bind(r,44669))},19634:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2583,23)),Promise.resolve().then(r.t.bind(r,26840,23)),Promise.resolve().then(r.t.bind(r,38771,23)),Promise.resolve().then(r.t.bind(r,13225,23)),Promise.resolve().then(r.t.bind(r,9295,23)),Promise.resolve().then(r.t.bind(r,43982,23))},54358:(e,t,r)=>{"use strict";r.r(t),r.d(t,{LandingPageContent:()=>U});var s=r(95344),a=r(3729),n=r(16212),i=r(61351),o=r(69436),l=r(71809),c=r(66091),d=r(41223),u=r.n(d),m=r(20783),h=r.n(m),p=r(89895),f=r(85674),x=r(50340),g=r(91700),y=r(23485),b=r(30304),v=r(79200),j=r(37121),w=r(14513),k=r(98200),N=r(35299),C=r(76755),S=r(62312),P=r(71206),M=r(40626),_=r(58968),z=r(12704),Z=r(25390),E=r(80508),A=r(69224);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let T=(0,A.Z)("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]),I=(0,A.Z)("Linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]]),O=(0,A.Z)("Facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]]),R=(0,A.Z)("Instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]]),q=(0,A.Z)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]]),B=(0,A.Z)("Youtube",[["path",{d:"M2.5 17a24.12 24.12 0 0 1 0-10 2 2 0 0 1 1.4-1.4 49.56 49.56 0 0 1 16.2 0A2 2 0 0 1 21.5 7a24.12 24.12 0 0 1 0 10 2 2 0 0 1-1.4 1.4 49.55 49.55 0 0 1-16.2 0A2 2 0 0 1 2.5 17",key:"1q2vi4"}],["path",{d:"m10 15 5-3-5-3z",key:"1jp15x"}]]),L={hero:{enabled:!0,title:"Build Your SaaS Business",subtitle:"The Complete Platform",description:"Everything you need to launch, grow, and scale your SaaS business. From customer management to billing, we've got you covered.",primaryCTA:{text:"Start Free Trial",link:"/auth/signup"},secondaryCTA:{text:"Watch Demo",link:"/demo"},backgroundImage:"",backgroundVideo:""},features:{enabled:!0,title:"Everything You Need",subtitle:"Powerful Features",items:[{id:"1",title:"Customer Management",description:"Manage your customers, track interactions, and build lasting relationships.",icon:"users",image:""},{id:"2",title:"Subscription Billing",description:"Automated billing, invoicing, and payment processing for recurring revenue.",icon:"credit-card",image:""},{id:"3",title:"Analytics & Reports",description:"Comprehensive analytics to track your business performance and growth.",icon:"bar-chart",image:""},{id:"4",title:"Multi-Tenant Architecture",description:"Secure data isolation with company-based access control and team management.",icon:"building",image:""},{id:"5",title:"Enterprise Security",description:"Role-based access control with audit logs and data encryption.",icon:"shield",image:""},{id:"6",title:"Global Ready",description:"Multi-currency support and localization for worldwide businesses.",icon:"globe",image:""}]},pricing:{enabled:!0,title:"Simple, Transparent Pricing",subtitle:"Choose the plan that fits your needs",showPricingTable:!0,customMessage:""},testimonials:{enabled:!0,title:"What Our Customers Say",subtitle:"Trusted by thousands of businesses",items:[{id:"1",name:"John Smith",role:"CEO",company:"TechCorp",content:"This platform has transformed how we manage our SaaS business. The automation features alone have saved us countless hours.",avatar:"",rating:5},{id:"2",name:"Sarah Johnson",role:"Founder",company:"StartupXYZ",content:"The best investment we've made for our business. The customer management features are incredibly powerful.",avatar:"",rating:5},{id:"3",name:"Mike Chen",role:"CTO",company:"InnovateLab",content:"Excellent platform with great support. The analytics help us make data-driven decisions every day.",avatar:"",rating:5}]},faq:{enabled:!0,title:"Frequently Asked Questions",subtitle:"Everything you need to know",items:[{id:"1",question:"How do I get started?",answer:"Simply sign up for a free trial and follow our onboarding guide to set up your account. Our team is here to help you every step of the way."},{id:"2",question:"Can I cancel anytime?",answer:"Yes, you can cancel your subscription at any time. No long-term contracts or cancellation fees. Your data will remain accessible during the notice period."},{id:"3",question:"Is my data secure?",answer:"Absolutely. We use enterprise-grade security measures including encryption, regular backups, and compliance with industry standards like SOC 2 and GDPR."},{id:"4",question:"Do you offer customer support?",answer:"Yes, we provide 24/7 customer support via email, chat, and phone. Our premium plans also include dedicated account managers."},{id:"5",question:"Can I integrate with other tools?",answer:"Yes, we offer integrations with popular tools like Slack, Zapier, QuickBooks, and many more. We also provide a robust API for custom integrations."}]},cta:{enabled:!0,title:"Ready to Get Started?",description:"Join thousands of businesses already using our platform to grow their SaaS.",buttonText:"Start Your Free Trial",buttonLink:"/auth/signup",backgroundImage:""},footer:{enabled:!0,companyDescription:"The complete SaaS platform for modern businesses.",links:[{id:"1",title:"Product",items:[{id:"1",text:"Features",link:"/features"},{id:"2",text:"Pricing",link:"/pricing"},{id:"3",text:"Security",link:"/security"},{id:"4",text:"Integrations",link:"/integrations"}]},{id:"2",title:"Company",items:[{id:"1",text:"About",link:"/about"},{id:"2",text:"Blog",link:"/blog"},{id:"3",text:"Careers",link:"/careers"},{id:"4",text:"Contact",link:"/contact"}]},{id:"3",title:"Support",items:[{id:"1",text:"Help Center",link:"/help"},{id:"2",text:"Documentation",link:"/docs"},{id:"3",text:"API Reference",link:"/api"},{id:"4",text:"Status",link:"/status"}]}],socialLinks:{twitter:"https://twitter.com/yourcompany",linkedin:"https://linkedin.com/company/yourcompany",facebook:"https://facebook.com/yourcompany",instagram:"https://instagram.com/yourcompany"},copyrightText:"\xa9 2024 Your Company. All rights reserved."},seo:{title:"SaaS Platform - Build Your Business",description:"The complete SaaS platform for modern businesses. Customer management, billing, analytics, and more.",keywords:"saas, platform, business, customer management, billing, analytics",ogImage:""}},$=e=>({users:p.Z,"credit-card":f.Z,"bar-chart":x.Z,building:g.Z,shield:y.Z,globe:b.Z,zap:v.Z,"file-text":j.Z})[e]||p.Z,F=e=>{let t=e/1073741824;return t>=1?`${t}GB`:`${Math.round(1024*t)}MB`},D=e=>{let t=[];return t.push(`Up to ${e.maxUsers} users`),t.push(`${e.maxCompanies} ${1===e.maxCompanies?"company":"companies"}`),t.push(`${e.maxCustomers} customers`),t.push(`${e.maxQuotations} quotations/month`),t.push(`${e.maxInvoices} invoices/month`),t.push(`${F(e.maxStorage)} storage`),e.features.basicReporting&&t.push("Basic reporting"),e.features.emailSupport&&t.push("Email support"),e.features.mobileApp&&t.push("Mobile app access"),e.features.advancedAnalytics&&t.push("Advanced analytics"),e.features.customBranding&&t.push("Custom branding"),e.features.apiAccess&&t.push("API access"),e.features.prioritySupport&&t.push("Priority support"),e.features.customIntegrations&&t.push("Custom integrations"),e.features.advancedSecurity&&t.push("Advanced security"),e.features.dedicatedManager&&t.push("Dedicated account manager"),t};function U(){let{branding:e}=(0,c.TC)(),[t,r]=(0,a.useState)(L),[d,m]=(0,a.useState)([]),[f,x]=(0,a.useState)(!0),[y,b]=(0,a.useState)(null),[v,j]=(0,a.useState)(!1),[A,F]=(0,a.useState)(!1),[U,V]=(0,a.useState)(!1);(0,a.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/cms"),t=await e.json();t.success&&t.content&&r({...L,...t.content});let s=await fetch("/api/pricing-plans?publicOnly=true"),a=await s.json();if(a.success){let e=a.data.filter(e=>e.isActive&&e.isPublic).sort((e,t)=>e.sortOrder-t.sortOrder);m(e)}}catch(e){console.error("Error fetching data:",e)}finally{x(!1)}})()},[]),(0,a.useEffect)(()=>{let e=()=>{V(window.scrollY>400)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]);let G=e=>v&&e.yearlyPrice?e.yearlyPrice/12:e.monthlyPrice,Y=e=>{if(!e.yearlyPrice||!e.monthlyPrice)return 0;let t=e.yearlyPrice/12;return Math.round((e.monthlyPrice-t)/e.monthlyPrice*100)};return f?s.jsx("div",{className:"min-h-screen flex items-center justify-center",children:s.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50",children:[s.jsx("header",{className:"border-b bg-white/95 backdrop-blur-sm sticky top-0 z-50 shadow-sm",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 py-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[e.logoUrl?s.jsx(u(),{src:e.logoUrl,alt:e.appName,width:32,height:32,className:"h-8 w-auto"}):s.jsx(g.Z,{className:"h-8 w-8",style:{color:e.primaryColor}}),s.jsx("span",{className:"text-2xl font-bold",style:{color:e.textColor,fontFamily:e.fontFamily},children:e.appName})]}),(0,s.jsxs)("nav",{className:"hidden md:flex items-center space-x-8",children:[s.jsx("a",{href:"#features",className:"text-gray-600 hover:text-gray-900 transition-colors",children:"Features"}),s.jsx("a",{href:"#pricing",className:"text-gray-600 hover:text-gray-900 transition-colors",children:"Pricing"}),s.jsx("a",{href:"#testimonials",className:"text-gray-600 hover:text-gray-900 transition-colors",children:"Testimonials"}),s.jsx("a",{href:"#faq",className:"text-gray-600 hover:text-gray-900 transition-colors",children:"FAQ"}),s.jsx(h(),{href:"/contact",className:"text-gray-600 hover:text-gray-900 transition-colors",children:"Contact"})]}),(0,s.jsxs)("div",{className:"hidden md:flex items-center space-x-4",children:[s.jsx(h(),{href:"/auth/signin",children:s.jsx(n.z,{variant:"ghost",className:"text-gray-600 hover:text-gray-900",children:"Sign In"})}),s.jsx(h(),{href:"/auth/signup",children:s.jsx(n.z,{className:"text-white",style:{backgroundColor:e.primaryColor,borderColor:e.primaryColor},children:"Get Started Free"})})]}),s.jsx("button",{className:"md:hidden p-2",onClick:()=>F(!A),children:A?s.jsx(w.Z,{className:"h-6 w-6 text-gray-600"}):s.jsx(k.Z,{className:"h-6 w-6 text-gray-600"})})]}),A&&s.jsx("div",{className:"md:hidden mt-4 pb-4 border-t",children:(0,s.jsxs)("nav",{className:"flex flex-col space-y-4 pt-4",children:[s.jsx("a",{href:"#features",className:"text-gray-600 hover:text-gray-900 transition-colors",onClick:()=>F(!1),children:"Features"}),s.jsx("a",{href:"#pricing",className:"text-gray-600 hover:text-gray-900 transition-colors",onClick:()=>F(!1),children:"Pricing"}),s.jsx("a",{href:"#testimonials",className:"text-gray-600 hover:text-gray-900 transition-colors",onClick:()=>F(!1),children:"Testimonials"}),s.jsx("a",{href:"#faq",className:"text-gray-600 hover:text-gray-900 transition-colors",onClick:()=>F(!1),children:"FAQ"}),s.jsx(h(),{href:"/contact",className:"text-gray-600 hover:text-gray-900 transition-colors",onClick:()=>F(!1),children:"Contact"}),(0,s.jsxs)("div",{className:"flex flex-col space-y-2 pt-4 border-t",children:[s.jsx(h(),{href:"/auth/signin",onClick:()=>F(!1),children:s.jsx(n.z,{variant:"ghost",className:"w-full justify-start",children:"Sign In"})}),s.jsx(h(),{href:"/auth/signup",onClick:()=>F(!1),children:s.jsx(n.z,{className:"w-full text-white",style:{backgroundColor:e.primaryColor,borderColor:e.primaryColor},children:"Get Started Free"})})]})]})})]})}),t.hero?.enabled&&(0,s.jsxs)("section",{className:"py-20 px-4 relative overflow-hidden",children:[t.hero.backgroundImage&&s.jsx("div",{className:"absolute inset-0 z-0",children:s.jsx(u(),{src:t.hero.backgroundImage,alt:"Hero Background",fill:!0,className:"object-cover opacity-20"})}),s.jsx("div",{className:"container mx-auto text-center relative z-10",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[t.hero.subtitle&&s.jsx(o.C,{className:"mb-4 text-sm px-4 py-2",children:t.hero.subtitle}),s.jsx("h1",{className:"text-5xl md:text-6xl font-bold text-gray-900 mb-6",children:t.hero.title}),s.jsx("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:t.hero.description}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center mb-12",children:[s.jsx(h(),{href:t.hero.primaryCTA.link,children:(0,s.jsxs)(n.z,{size:"lg",className:"text-lg px-8 py-3",children:[t.hero.primaryCTA.text,s.jsx(N.Z,{className:"ml-2 h-5 w-5"})]})}),t.hero.secondaryCTA.text&&s.jsx(h(),{href:t.hero.secondaryCTA.link,children:s.jsx(n.z,{size:"lg",variant:"outline",className:"text-lg px-8 py-3",children:t.hero.secondaryCTA.text})})]})]})})]}),t.features?.enabled&&s.jsx("section",{id:"features",className:"py-20 px-4 bg-white",children:(0,s.jsxs)("div",{className:"container mx-auto",children:[(0,s.jsxs)("div",{className:"text-center mb-16",children:[s.jsx("h2",{className:"text-4xl font-bold text-gray-900 mb-4",children:t.features.title}),s.jsx("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:t.features.subtitle})]}),s.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:t.features.items.map(e=>{let t=$(e.icon);return(0,s.jsxs)(i.Zb,{className:"border-0 shadow-lg hover:shadow-xl transition-shadow",children:[(0,s.jsxs)(i.Ol,{children:[s.jsx("div",{className:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4",children:s.jsx(t,{className:"h-6 w-6 text-blue-600"})}),s.jsx(i.ll,{className:"text-xl",children:e.title})]}),s.jsx(i.aY,{children:s.jsx(i.SZ,{className:"text-gray-600",children:e.description})})]},e.id)})})]})}),t.pricing?.enabled&&d.length>0&&s.jsx("section",{id:"pricing",className:"py-20 px-4 bg-gray-50",children:(0,s.jsxs)("div",{className:"container mx-auto",children:[(0,s.jsxs)("div",{className:"text-center mb-16",children:[s.jsx("h2",{className:"text-4xl font-bold text-gray-900 mb-4",children:t.pricing.title}),s.jsx("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto mb-8",children:t.pricing.subtitle}),(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-4 mb-8",children:[s.jsx("span",{className:`text-sm ${v?"text-gray-500":"text-gray-900 font-medium"}`,children:"Monthly"}),s.jsx(l.r,{checked:v,onCheckedChange:j}),s.jsx("span",{className:`text-sm ${v?"text-gray-900 font-medium":"text-gray-500"}`,children:"Yearly"}),d.some(e=>Y(e)>0)&&(0,s.jsxs)(o.C,{className:"ml-2 bg-green-100 text-green-800",children:["Save up to ",Math.max(...d.map(Y)),"%"]})]})]}),s.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto",children:d.map((e,t)=>{let r=D(e),a=G(e),l=Y(e),c=1===t;return(0,s.jsxs)(i.Zb,{className:`relative ${c?"border-blue-500 shadow-xl scale-105":"border-gray-200 shadow-lg"} bg-white`,children:[c&&s.jsx("div",{className:"absolute -top-4 left-1/2 transform -translate-x-1/2",children:(0,s.jsxs)(o.C,{className:"bg-blue-500 text-white px-4 py-1",children:[s.jsx(C.Z,{className:"h-4 w-4 mr-1"}),"Most Popular"]})}),(0,s.jsxs)(i.Ol,{className:"text-center pb-8",children:[s.jsx(i.ll,{className:"text-2xl font-bold",children:e.name}),s.jsx(i.SZ,{className:"text-gray-600 mt-2",children:e.description}),(0,s.jsxs)("div",{className:"mt-6",children:[(0,s.jsxs)("div",{className:"flex items-baseline justify-center",children:[(0,s.jsxs)("span",{className:"text-5xl font-bold text-gray-900",children:["$",a.toFixed(0)]}),s.jsx("span",{className:"text-gray-500 ml-1",children:"/month"})]}),v&&l>0&&(0,s.jsxs)("p",{className:"text-sm text-green-600 mt-2",children:["Save ",l,"% with yearly billing"]}),e.trialDays>0&&(0,s.jsxs)("p",{className:"text-sm text-blue-600 mt-2",children:[e.trialDays,"-day free trial"]})]})]}),(0,s.jsxs)(i.aY,{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-3",children:[r.slice(0,8).map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[s.jsx(S.Z,{className:"h-5 w-5 text-green-500 flex-shrink-0"}),s.jsx("span",{className:"text-gray-700 text-sm",children:e})]},t)),r.length>8&&(0,s.jsxs)("p",{className:"text-sm text-gray-500 italic",children:["+",r.length-8," more features"]})]}),s.jsx(h(),{href:"/auth/signup",className:"block",children:(0,s.jsxs)(n.z,{className:`w-full ${c?"bg-blue-600 hover:bg-blue-700":""}`,variant:c?"default":"outline",size:"lg",children:["Get Started",s.jsx(N.Z,{className:"ml-2 h-4 w-4"})]})})]})]},e.id)})}),s.jsx("div",{className:"text-center mt-16",children:s.jsx(i.Zb,{className:"max-w-2xl mx-auto border-gray-200 shadow-lg bg-white",children:(0,s.jsxs)(i.aY,{className:"p-8",children:[s.jsx("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Need something custom?"}),s.jsx("p",{className:"text-gray-600 mb-6",children:"Contact our sales team for enterprise pricing, custom features, and dedicated support."}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,s.jsxs)(n.z,{size:"lg",variant:"outline",children:[s.jsx(P.Z,{className:"h-4 w-4 mr-2"}),"Contact Sales"]}),(0,s.jsxs)(n.z,{size:"lg",variant:"ghost",children:[s.jsx(M.Z,{className:"h-4 w-4 mr-2"}),"Schedule Demo"]})]})]})})})]})}),t.testimonials?.enabled&&s.jsx("section",{id:"testimonials",className:"py-20 px-4 bg-white",children:(0,s.jsxs)("div",{className:"container mx-auto",children:[(0,s.jsxs)("div",{className:"text-center mb-16",children:[s.jsx("h2",{className:"text-4xl font-bold text-gray-900 mb-4",children:t.testimonials.title}),s.jsx("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:t.testimonials.subtitle})]}),s.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:t.testimonials.items.map(e=>s.jsx(i.Zb,{className:"border-0 shadow-lg",children:(0,s.jsxs)(i.aY,{className:"p-6",children:[s.jsx("div",{className:"flex items-center mb-4",children:[...Array(e.rating)].map((e,t)=>s.jsx(C.Z,{className:"h-5 w-5 text-yellow-400 fill-current"},t))}),s.jsx(_.Z,{className:"h-8 w-8 text-gray-300 mb-4"}),(0,s.jsxs)("p",{className:"text-gray-600 mb-6 italic",children:['"',e.content,'"']}),(0,s.jsxs)("div",{className:"flex items-center",children:[e.avatar?s.jsx(u(),{src:e.avatar,alt:e.name,width:48,height:48,className:"rounded-full mr-4"}):s.jsx("div",{className:"w-12 h-12 bg-gray-200 rounded-full mr-4 flex items-center justify-center",children:s.jsx(p.Z,{className:"h-6 w-6 text-gray-400"})}),(0,s.jsxs)("div",{children:[s.jsx("p",{className:"font-semibold text-gray-900",children:e.name}),(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:[e.role,", ",e.company]})]})]})]})},e.id))})]})}),t.faq?.enabled&&s.jsx("section",{id:"faq",className:"py-20 px-4 bg-gray-50",children:(0,s.jsxs)("div",{className:"container mx-auto max-w-4xl",children:[(0,s.jsxs)("div",{className:"text-center mb-16",children:[s.jsx("h2",{className:"text-4xl font-bold text-gray-900 mb-4",children:t.faq.title}),s.jsx("p",{className:"text-xl text-gray-600",children:t.faq.subtitle})]}),s.jsx("div",{className:"space-y-4",children:t.faq.items.map(e=>s.jsx(i.Zb,{className:"border shadow-sm",children:(0,s.jsxs)(i.aY,{className:"p-0",children:[(0,s.jsxs)("button",{className:"w-full p-6 text-left flex items-center justify-between hover:bg-gray-50",onClick:()=>b(y===e.id?null:e.id),children:[s.jsx("span",{className:"font-semibold text-gray-900",children:e.question}),y===e.id?s.jsx(z.Z,{className:"h-5 w-5 text-gray-500"}):s.jsx(Z.Z,{className:"h-5 w-5 text-gray-500"})]}),y===e.id&&s.jsx("div",{className:"px-6 pb-6",children:s.jsx("p",{className:"text-gray-600",children:e.answer})})]})},e.id))})]})}),t.cta?.enabled&&(0,s.jsxs)("section",{className:"py-20 px-4 bg-blue-600 relative overflow-hidden",children:[t.cta.backgroundImage&&s.jsx("div",{className:"absolute inset-0 z-0",children:s.jsx(u(),{src:t.cta.backgroundImage,alt:"CTA Background",fill:!0,className:"object-cover opacity-20"})}),(0,s.jsxs)("div",{className:"container mx-auto text-center relative z-10",children:[s.jsx("h2",{className:"text-4xl font-bold text-white mb-6",children:t.cta.title}),s.jsx("p",{className:"text-xl text-blue-100 mb-8 max-w-2xl mx-auto",children:t.cta.description}),s.jsx(h(),{href:t.cta.buttonLink,children:(0,s.jsxs)(n.z,{size:"lg",variant:"secondary",className:"text-lg px-8 py-3",children:[t.cta.buttonText,s.jsx(N.Z,{className:"ml-2 h-5 w-5"})]})})]})]}),t.footer?.enabled&&s.jsx("footer",{className:"bg-gray-900 text-white py-16 px-4",children:(0,s.jsxs)("div",{className:"container mx-auto",children:[(0,s.jsxs)("div",{className:"grid md:grid-cols-5 gap-8 mb-12",children:[(0,s.jsxs)("div",{className:"md:col-span-2",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[e.logoUrl?s.jsx(u(),{src:e.logoUrl,alt:e.appName,width:32,height:32,className:"h-8 w-auto"}):s.jsx(g.Z,{className:"h-8 w-8",style:{color:e.primaryColor}}),s.jsx("span",{className:"text-2xl font-bold",style:{fontFamily:e.fontFamily},children:e.appName})]}),s.jsx("p",{className:"text-gray-400 mb-6 leading-relaxed",children:t.footer.companyDescription}),(0,s.jsxs)("div",{className:"space-y-3 mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 text-gray-400",children:[s.jsx(P.Z,{className:"h-4 w-4"}),s.jsx("span",{children:"<EMAIL>"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3 text-gray-400",children:[s.jsx(M.Z,{className:"h-4 w-4"}),s.jsx("span",{children:"+1 (555) 123-4567"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3 text-gray-400",children:[s.jsx(E.Z,{className:"h-4 w-4"}),s.jsx("span",{children:"San Francisco, CA"})]})]}),(0,s.jsxs)("div",{className:"flex space-x-4",children:[t.footer.socialLinks.twitter&&s.jsx("a",{href:t.footer.socialLinks.twitter,className:"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-blue-600 transition-colors",target:"_blank",rel:"noopener noreferrer",children:s.jsx(T,{className:"h-5 w-5"})}),t.footer.socialLinks.linkedin&&s.jsx("a",{href:t.footer.socialLinks.linkedin,className:"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-blue-700 transition-colors",target:"_blank",rel:"noopener noreferrer",children:s.jsx(I,{className:"h-5 w-5"})}),t.footer.socialLinks.facebook&&s.jsx("a",{href:t.footer.socialLinks.facebook,className:"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-blue-800 transition-colors",target:"_blank",rel:"noopener noreferrer",children:s.jsx(O,{className:"h-5 w-5"})}),t.footer.socialLinks.instagram&&s.jsx("a",{href:t.footer.socialLinks.instagram,className:"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-pink-600 transition-colors",target:"_blank",rel:"noopener noreferrer",children:s.jsx(R,{className:"h-5 w-5"})}),s.jsx("a",{href:"https://github.com",className:"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 transition-colors",target:"_blank",rel:"noopener noreferrer",children:s.jsx(q,{className:"h-5 w-5"})}),s.jsx("a",{href:"https://youtube.com",className:"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-red-600 transition-colors",target:"_blank",rel:"noopener noreferrer",children:s.jsx(B,{className:"h-5 w-5"})})]})]}),t.footer.links.map(e=>(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"font-semibold mb-4 text-white",children:e.title}),s.jsx("ul",{className:"space-y-3",children:e.items.map(e=>s.jsx("li",{children:s.jsx(h(),{href:e.link,className:"text-gray-400 hover:text-white transition-colors",children:e.text})},e.id))})]},e.id))]}),s.jsx("div",{className:"border-t border-gray-800 pt-8 mb-8",children:(0,s.jsxs)("div",{className:"max-w-md",children:[s.jsx("h3",{className:"font-semibold mb-4 text-white",children:"Stay Updated"}),s.jsx("p",{className:"text-gray-400 mb-4",children:"Get the latest updates, tips, and insights delivered to your inbox."}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[s.jsx("input",{type:"email",placeholder:"Enter your email",className:"flex-1 px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"}),s.jsx(n.z,{className:"bg-blue-600 hover:bg-blue-700",children:"Subscribe"})]})]})}),(0,s.jsxs)("div",{className:"border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center",children:[s.jsx("p",{className:"text-gray-400 mb-4 md:mb-0",children:t.footer.copyrightText}),(0,s.jsxs)("div",{className:"flex space-x-6 text-gray-400 text-sm",children:[s.jsx(h(),{href:"/privacy",className:"hover:text-white transition-colors",children:"Privacy Policy"}),s.jsx(h(),{href:"/terms",className:"hover:text-white transition-colors",children:"Terms of Service"}),s.jsx(h(),{href:"/cookies",className:"hover:text-white transition-colors",children:"Cookie Policy"}),s.jsx(h(),{href:"/security",className:"hover:text-white transition-colors",children:"Security"})]})]})]})}),U&&s.jsx("button",{onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})},className:"fixed bottom-8 right-8 w-12 h-12 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg transition-all duration-300 z-50 flex items-center justify-center","aria-label":"Scroll to top",children:s.jsx(z.Z,{className:"h-6 w-6"})})]})}},56189:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Providers:()=>d});var s=r(95344),a=r(47674),n=r(6256),i=r(19115),o=r(26274),l=r(3729),c=r(66091);function d({children:e}){let[t]=(0,l.useState)(()=>new i.S({defaultOptions:{queries:{staleTime:6e4,retry:1}}}));return s.jsx(a.SessionProvider,{children:s.jsx(o.aH,{client:t,children:s.jsx(c.lY,{children:s.jsx(n.f,{attribute:"class",defaultTheme:"light",enableSystem:!0,disableTransitionOnChange:!0,children:e})})})})}},69436:(e,t,r)=>{"use strict";r.d(t,{C:()=>o});var s=r(95344);r(3729);var a=r(49247),n=r(91626);let i=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600",info:"border-transparent bg-blue-500 text-white hover:bg-blue-600"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,...r}){return s.jsx("div",{className:(0,n.cn)(i({variant:t}),e),...r})}},16212:(e,t,r)=>{"use strict";r.d(t,{z:()=>c});var s=r(95344),a=r(3729),n=r(32751),i=r(49247),o=r(91626);let l=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef(({className:e,variant:t,size:r,asChild:a=!1,...i},c)=>{let d=a?n.g7:"button";return s.jsx(d,{className:(0,o.cn)(l({variant:t,size:r,className:e})),ref:c,...i})});c.displayName="Button"},61351:(e,t,r)=>{"use strict";r.d(t,{Ol:()=>o,SZ:()=>c,Zb:()=>i,aY:()=>d,eW:()=>u,ll:()=>l});var s=r(95344),a=r(3729),n=r(91626);let i=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));i.displayName="Card";let o=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...t}));o.displayName="CardHeader";let l=a.forwardRef(({className:e,...t},r)=>s.jsx("h3",{ref:r,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let c=a.forwardRef(({className:e,...t},r)=>s.jsx("p",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",e),...t}));c.displayName="CardDescription";let d=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,n.cn)("p-6 pt-0",e),...t}));d.displayName="CardContent";let u=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",e),...t}));u.displayName="CardFooter"},71809:(e,t,r)=>{"use strict";r.d(t,{r:()=>k});var s=r(95344),a=r(3729),n=r(85222),i=r(31405),o=r(98462),l=r(33183),c=r(92062),d=r(63085),u=r(62409),m="Switch",[h,p]=(0,o.b)(m),[f,x]=h(m),g=a.forwardRef((e,t)=>{let{__scopeSwitch:r,name:o,checked:c,defaultChecked:d,required:h,disabled:p,value:x="on",onCheckedChange:g,form:y,...b}=e,[w,k]=a.useState(null),N=(0,i.e)(t,e=>k(e)),C=a.useRef(!1),S=!w||y||!!w.closest("form"),[P,M]=(0,l.T)({prop:c,defaultProp:d??!1,onChange:g,caller:m});return(0,s.jsxs)(f,{scope:r,checked:P,disabled:p,children:[(0,s.jsx)(u.WV.button,{type:"button",role:"switch","aria-checked":P,"aria-required":h,"data-state":j(P),"data-disabled":p?"":void 0,disabled:p,value:x,...b,ref:N,onClick:(0,n.M)(e.onClick,e=>{M(e=>!e),S&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),S&&(0,s.jsx)(v,{control:w,bubbles:!C.current,name:o,value:x,checked:P,required:h,disabled:p,form:y,style:{transform:"translateX(-100%)"}})]})});g.displayName=m;var y="SwitchThumb",b=a.forwardRef((e,t)=>{let{__scopeSwitch:r,...a}=e,n=x(y,r);return(0,s.jsx)(u.WV.span,{"data-state":j(n.checked),"data-disabled":n.disabled?"":void 0,...a,ref:t})});b.displayName=y;var v=a.forwardRef(({__scopeSwitch:e,control:t,checked:r,bubbles:n=!0,...o},l)=>{let u=a.useRef(null),m=(0,i.e)(u,l),h=(0,c.D)(r),p=(0,d.t)(t);return a.useEffect(()=>{let e=u.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(h!==r&&t){let s=new Event("click",{bubbles:n});t.call(e,r),e.dispatchEvent(s)}},[h,r,n]),(0,s.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:r,...o,tabIndex:-1,ref:m,style:{...o.style,...p,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function j(e){return e?"checked":"unchecked"}v.displayName="SwitchBubbleInput";var w=r(91626);let k=a.forwardRef(({className:e,...t},r)=>s.jsx(g,{className:(0,w.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...t,ref:r,children:s.jsx(b,{className:(0,w.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));k.displayName=g.displayName},66091:(e,t,r)=>{"use strict";r.d(t,{TC:()=>l,lY:()=>o});var s=r(95344),a=r(3729);let n={appName:"SaaS Platform",logoUrl:"",faviconUrl:"",primaryColor:"#3b82f6",secondaryColor:"#64748b",accentColor:"#10b981",backgroundColor:"#ffffff",textColor:"#1f2937",theme:"light",fontFamily:"Inter, sans-serif",customCss:""},i=(0,a.createContext)(void 0);function o({children:e}){let[t,r]=(0,a.useState)(n),[o,l]=(0,a.useState)(!0);(0,a.useEffect)(()=>{c()},[]);let c=async()=>{try{let e=await fetch("/api/global-config/branding"),t=await e.json();t.success&&t.branding?(r({...n,...t.branding}),d({...n,...t.branding})):d(n)}catch(e){console.error("Error fetching branding config:",e),d(n)}finally{l(!1)}},d=e=>{let t=document.documentElement;if(t.style.setProperty("--primary-color",e.primaryColor),t.style.setProperty("--secondary-color",e.secondaryColor),t.style.setProperty("--accent-color",e.accentColor),t.style.setProperty("--background-color",e.backgroundColor),t.style.setProperty("--text-color",e.textColor),t.style.setProperty("--font-family",e.fontFamily),document.body.className=document.body.className.replace(/theme-\w+/g,""),document.body.classList.add(`theme-${e.theme}`),document.title=e.appName,e.faviconUrl){let t=document.querySelector('link[rel="icon"]');t||((t=document.createElement("link")).rel="icon",document.head.appendChild(t)),t.href=e.faviconUrl}let r=document.getElementById("custom-branding-css");e.customCss?(r||((r=document.createElement("style")).id="custom-branding-css",document.head.appendChild(r)),r.textContent=e.customCss):r&&r.remove();let s=document.querySelector('meta[name="theme-color"]');s||((s=document.createElement("meta")).name="theme-color",document.head.appendChild(s)),s.content=e.primaryColor};return s.jsx(i.Provider,{value:{branding:t,updateBranding:e=>{let s={...t,...e};r(s),d(s)},loading:o},children:e})}function l(){let e=(0,a.useContext)(i);if(void 0===e)throw Error("useBranding must be used within a BrandingProvider");return e}},91626:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(56815),a=r(79377);function n(...e){return(0,a.m6)((0,s.W)(e))}},35299:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(69224).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},50340:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(69224).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},62312:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(69224).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},25390:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(69224).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},12704:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(69224).Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},85674:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(69224).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},37121:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(69224).Z)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]])},30304:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(69224).Z)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},71206:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(69224).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},80508:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(69224).Z)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},98200:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(69224).Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},40626:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(69224).Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},58968:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(69224).Z)("Quote",[["path",{d:"M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z",key:"4rm80e"}],["path",{d:"M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z",key:"10za9r"}]])},23485:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(69224).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},76755:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(69224).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},89895:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(69224).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},14513:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(69224).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},79200:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(69224).Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]])},31900:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return g}});let s=r(39694),a=r(17824)._(r(3729)),n=s._(r(81202)),i=s._(r(1758)),o=r(83855),l=r(73053),c=r(74187);r(70837);let d=r(66150),u=s._(r(74931)),m={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function h(e,t,r,s,a,n){let i=null==e?void 0:e.src;e&&e["data-loaded-src"]!==i&&(e["data-loaded-src"]=i,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&a(!0),null==r?void 0:r.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let s=!1,a=!1;r.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>s,isPropagationStopped:()=>a,persist:()=>{},preventDefault:()=>{s=!0,t.preventDefault()},stopPropagation:()=>{a=!0,t.stopPropagation()}})}(null==s?void 0:s.current)&&s.current(e)}}))}function p(e){let[t,r]=a.version.split(".",2),s=parseInt(t,10),n=parseInt(r,10);return s>18||18===s&&n>=3?{fetchPriority:e}:{fetchpriority:e}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let f=(0,a.forwardRef)((e,t)=>{let{src:r,srcSet:s,sizes:n,height:i,width:o,decoding:l,className:c,style:d,fetchPriority:u,placeholder:m,loading:f,unoptimized:x,fill:g,onLoadRef:y,onLoadingCompleteRef:b,setBlurComplete:v,setShowAltText:j,onLoad:w,onError:k,...N}=e;return a.default.createElement("img",{...N,...p(u),loading:f,width:o,height:i,decoding:l,"data-nimg":g?"fill":"1",className:c,style:d,sizes:n,srcSet:s,src:r,ref:(0,a.useCallback)(e=>{t&&("function"==typeof t?t(e):"object"==typeof t&&(t.current=e)),e&&(k&&(e.src=e.src),e.complete&&h(e,m,y,b,v,x))},[r,m,y,b,v,k,x,t]),onLoad:e=>{h(e.currentTarget,m,y,b,v,x)},onError:e=>{j(!0),"empty"!==m&&v(!0),k&&k(e)}})});function x(e){let{isAppRouter:t,imgAttributes:r}=e,s={as:"image",imageSrcSet:r.srcSet,imageSizes:r.sizes,crossOrigin:r.crossOrigin,referrerPolicy:r.referrerPolicy,...p(r.fetchPriority)};return t&&n.default.preload?(n.default.preload(r.src,s),null):a.default.createElement(i.default,null,a.default.createElement("link",{key:"__nimg-"+r.src+r.srcSet+r.sizes,rel:"preload",href:r.srcSet?void 0:r.src,...s}))}let g=(0,a.forwardRef)((e,t)=>{let r=(0,a.useContext)(d.RouterContext),s=(0,a.useContext)(c.ImageConfigContext),n=(0,a.useMemo)(()=>{let e=m||s||l.imageConfigDefault,t=[...e.deviceSizes,...e.imageSizes].sort((e,t)=>e-t),r=e.deviceSizes.sort((e,t)=>e-t);return{...e,allSizes:t,deviceSizes:r}},[s]),{onLoad:i,onLoadingComplete:h}=e,p=(0,a.useRef)(i);(0,a.useEffect)(()=>{p.current=i},[i]);let g=(0,a.useRef)(h);(0,a.useEffect)(()=>{g.current=h},[h]);let[y,b]=(0,a.useState)(!1),[v,j]=(0,a.useState)(!1),{props:w,meta:k}=(0,o.getImgProps)(e,{defaultLoader:u.default,imgConf:n,blurComplete:y,showAltText:v});return a.default.createElement(a.default.Fragment,null,a.default.createElement(f,{...w,unoptimized:k.unoptimized,placeholder:k.placeholder,fill:k.fill,onLoadRef:p,onLoadingCompleteRef:g,setBlurComplete:b,setShowAltText:j,ref:t}),k.priority?a.default.createElement(x,{isAppRouter:!r,imgAttributes:w}):null)});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7637:(e,t,r)=>{"use strict";e.exports=r(16372).vendored.contexts.AmpContext},32158:(e,t,r)=>{"use strict";e.exports=r(16372).vendored.contexts.HeadManagerContext},74187:(e,t,r)=>{"use strict";e.exports=r(16372).vendored.contexts.ImageConfigContext},13126:(e,t)=>{"use strict";function r(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:s=!1}=void 0===e?{}:e;return t||r&&s}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return r}})},83855:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return o}}),r(70837);let s=r(86358),a=r(73053);function n(e){return void 0!==e.default}function i(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function o(e,t){var r;let o,l,c,{src:d,sizes:u,unoptimized:m=!1,priority:h=!1,loading:p,className:f,quality:x,width:g,height:y,fill:b=!1,style:v,onLoad:j,onLoadingComplete:w,placeholder:k="empty",blurDataURL:N,fetchPriority:C,layout:S,objectFit:P,objectPosition:M,lazyBoundary:_,lazyRoot:z,...Z}=e,{imgConf:E,showAltText:A,blurComplete:T,defaultLoader:I}=t,O=E||a.imageConfigDefault;if("allSizes"in O)o=O;else{let e=[...O.deviceSizes,...O.imageSizes].sort((e,t)=>e-t),t=O.deviceSizes.sort((e,t)=>e-t);o={...O,allSizes:e,deviceSizes:t}}let R=Z.loader||I;delete Z.loader,delete Z.srcSet;let q="__next_img_default"in R;if(q){if("custom"===o.loader)throw Error('Image with src "'+d+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader')}else{let e=R;R=t=>{let{config:r,...s}=t;return e(s)}}if(S){"fill"===S&&(b=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[S];e&&(v={...v,...e});let t={responsive:"100vw",fill:"100vw"}[S];t&&!u&&(u=t)}let B="",L=i(g),$=i(y);if("object"==typeof(r=d)&&(n(r)||void 0!==r.src)){let e=n(d)?d.default:d;if(!e.src)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e));if(!e.height||!e.width)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e));if(l=e.blurWidth,c=e.blurHeight,N=N||e.blurDataURL,B=e.src,!b){if(L||$){if(L&&!$){let t=L/e.width;$=Math.round(e.height*t)}else if(!L&&$){let t=$/e.height;L=Math.round(e.width*t)}}else L=e.width,$=e.height}}let F=!h&&("lazy"===p||void 0===p);(!(d="string"==typeof d?d:B)||d.startsWith("data:")||d.startsWith("blob:"))&&(m=!0,F=!1),o.unoptimized&&(m=!0),q&&d.endsWith(".svg")&&!o.dangerouslyAllowSVG&&(m=!0),h&&(C="high");let D=i(x),U=Object.assign(b?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:P,objectPosition:M}:{},A?{}:{color:"transparent"},v),V=T||"empty"===k?null:"blur"===k?'url("data:image/svg+xml;charset=utf-8,'+(0,s.getImageBlurSvg)({widthInt:L,heightInt:$,blurWidth:l,blurHeight:c,blurDataURL:N||"",objectFit:U.objectFit})+'")':'url("'+k+'")',G=V?{backgroundSize:U.objectFit||"cover",backgroundPosition:U.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:V}:{},Y=function(e){let{config:t,src:r,unoptimized:s,width:a,quality:n,sizes:i,loader:o}=e;if(s)return{src:r,srcSet:void 0,sizes:void 0};let{widths:l,kind:c}=function(e,t,r){let{deviceSizes:s,allSizes:a}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let s;s=e.exec(r);s)t.push(parseInt(s[2]));if(t.length){let e=.01*Math.min(...t);return{widths:a.filter(t=>t>=s[0]*e),kind:"w"}}return{widths:a,kind:"w"}}return"number"!=typeof t?{widths:s,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>a.find(t=>t>=e)||a[a.length-1]))],kind:"x"}}(t,a,i),d=l.length-1;return{sizes:i||"w"!==c?i:"100vw",srcSet:l.map((e,s)=>o({config:t,src:r,quality:n,width:e})+" "+("w"===c?e:s+1)+c).join(", "),src:o({config:t,src:r,quality:n,width:l[d]})}}({config:o,src:d,unoptimized:m,width:L,quality:D,sizes:u,loader:R});return{props:{...Z,loading:F?"lazy":p,fetchPriority:C,width:L,height:$,decoding:"async",className:f,style:{...U,...G},sizes:Y.sizes,srcSet:Y.srcSet,src:Y.src},meta:{unoptimized:m,priority:h,placeholder:k,fill:b}}}},1758:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{defaultHead:function(){return c},default:function(){return h}});let s=r(39694),a=r(17824)._(r(3729)),n=s._(r(27984)),i=r(7637),o=r(32158),l=r(13126);function c(e){void 0===e&&(e=!1);let t=[a.default.createElement("meta",{charSet:"utf-8"})];return e||t.push(a.default.createElement("meta",{name:"viewport",content:"width=device-width"})),t}function d(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===a.default.Fragment?e.concat(a.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}r(70837);let u=["name","httpEquiv","charSet","itemProp"];function m(e,t){let{inAmpMode:r}=t;return e.reduce(d,[]).reverse().concat(c(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,s={};return a=>{let n=!0,i=!1;if(a.key&&"number"!=typeof a.key&&a.key.indexOf("$")>0){i=!0;let t=a.key.slice(a.key.indexOf("$")+1);e.has(t)?n=!1:e.add(t)}switch(a.type){case"title":case"base":t.has(a.type)?n=!1:t.add(a.type);break;case"meta":for(let e=0,t=u.length;e<t;e++){let t=u[e];if(a.props.hasOwnProperty(t)){if("charSet"===t)r.has(t)?n=!1:r.add(t);else{let e=a.props[t],r=s[t]||new Set;("name"!==t||!i)&&r.has(e)?n=!1:(r.add(e),s[t]=r)}}}}return n}}()).reverse().map((e,t)=>{let s=e.key||t;if(!r&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,a.default.cloneElement(e,t)}return a.default.cloneElement(e,{key:s})})}let h=function(e){let{children:t}=e,r=(0,a.useContext)(i.AmpStateContext),s=(0,a.useContext)(o.HeadManagerContext);return a.default.createElement(n.default,{reduceComponentsToState:m,headManager:s,inAmpMode:(0,l.isInAmpMode)(r)},t)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},86358:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:s,blurHeight:a,blurDataURL:n,objectFit:i}=e,o=s?40*s:t,l=a?40*a:r,c=o&&l?"viewBox='0 0 "+o+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+c+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(c?"none":"contain"===i?"xMidYMid":"cover"===i?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+n+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},73053:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return s}});let r=["default","imgix","cloudinary","akamai","custom"],s={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[],unoptimized:!1}},37412:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{unstable_getImgProps:function(){return l},default:function(){return c}});let s=r(39694),a=r(83855),n=r(70837),i=r(31900),o=s._(r(74931)),l=e=>{(0,n.warnOnce)("Warning: unstable_getImgProps() is experimental and may change or be removed at any time. Use at your own risk.");let{props:t}=(0,a.getImgProps)(e,{defaultLoader:o.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}},c=i.Image},74931:(e,t)=>{"use strict";function r(e){let{config:t,src:r,width:s,quality:a}=e;return t.path+"?url="+encodeURIComponent(r)+"&w="+s+"&q="+(a||75)}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}}),r.__next_img_default=!0;let s=r},27984:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}});let s=r(3729),a=()=>{},n=()=>{};function i(e){var t;let{headManager:r,reduceComponentsToState:i}=e;function o(){if(r&&r.mountedInstances){let t=s.Children.toArray(Array.from(r.mountedInstances).filter(Boolean));r.updateHead(i(t,e))}}return null==r||null==(t=r.mountedInstances)||t.add(e.children),o(),a(()=>{var t;return null==r||null==(t=r.mountedInstances)||t.add(e.children),()=>{var t;null==r||null==(t=r.mountedInstances)||t.delete(e.children)}}),a(()=>(r&&(r._pendingUpdate=o),()=>{r&&(r._pendingUpdate=o)})),n(()=>(r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null),()=>{r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null)})),null}},70837:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},41223:(e,t,r)=>{e.exports=r(37412)},59504:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h,metadata:()=>m});var s=r(25036),a=r(80265),n=r.n(a),i=r(86843);let o=(0,i.createProxy)(String.raw`C:\proj\nextjs-saas\components\providers.tsx`),{__esModule:l,$$typeof:c}=o;o.default;let d=(0,i.createProxy)(String.raw`C:\proj\nextjs-saas\components\providers.tsx#Providers`);var u=r(69636);r(67272);let m={title:{default:"Business SaaS - Complete Business Management Solution",template:"%s | Business SaaS"},description:"Complete SaaS solution for business management including CRM, invoicing, quotations, and more.",keywords:["SaaS","Business Management","CRM","Invoicing","Quotations"],authors:[{name:"Business SaaS Team"}],creator:"Business SaaS",openGraph:{type:"website",locale:"en_US",url:process.env.NEXT_PUBLIC_APP_URL,title:"Business SaaS - Complete Business Management Solution",description:"Complete SaaS solution for business management including CRM, invoicing, quotations, and more.",siteName:"Business SaaS"},twitter:{card:"summary_large_image",title:"Business SaaS - Complete Business Management Solution",description:"Complete SaaS solution for business management including CRM, invoicing, quotations, and more.",creator:"@businesssaas"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function h({children:e}){return s.jsx("html",{lang:"en",suppressHydrationWarning:!0,children:s.jsx("body",{className:n().className,children:(0,s.jsxs)(d,{children:[e,s.jsx(u.x7,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#4ade80",secondary:"#fff"}},error:{duration:4e3,iconTheme:{primary:"#ef4444",secondary:"#fff"}}}})]})})})}},74782:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var s=r(25036),a=r(40002),n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),o=(e,t)=>{let r=(0,a.forwardRef)(({color:r="currentColor",size:s=24,strokeWidth:o=2,absoluteStrokeWidth:l,className:c="",children:d,...u},m)=>(0,a.createElement)("svg",{ref:m,...n,width:s,height:s,stroke:r,strokeWidth:l?24*Number(o)/Number(s):o,className:["lucide",`lucide-${i(e)}`,c].join(" "),...u},[...t.map(([e,t])=>(0,a.createElement)(e,t)),...Array.isArray(d)?d:[d]]));return r.displayName=`${e}`,r};o("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),o("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]]),o("Building2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]]),o("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]),o("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),o("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]]);var l=r(86843);let c=(0,l.createProxy)(String.raw`C:\proj\nextjs-saas\components\landing\landing-page-content.tsx`),{__esModule:d,$$typeof:u}=c;c.default;let m=(0,l.createProxy)(String.raw`C:\proj\nextjs-saas\components\landing\landing-page-content.tsx#LandingPageContent`);function h(){return s.jsx(m,{})}},67272:()=>{},85222:(e,t,r)=>{"use strict";function s(e,t,{checkForDefaultPrevented:r=!0}={}){return function(s){if(e?.(s),!1===r||!s.defaultPrevented)return t?.(s)}}r.d(t,{M:()=>s})},98462:(e,t,r)=>{"use strict";r.d(t,{b:()=>i,k:()=>n});var s=r(3729),a=r(95344);function n(e,t){let r=s.createContext(t),n=e=>{let{children:t,...n}=e,i=s.useMemo(()=>n,Object.values(n));return(0,a.jsx)(r.Provider,{value:i,children:t})};return n.displayName=e+"Provider",[n,function(a){let n=s.useContext(r);if(n)return n;if(void 0!==t)return t;throw Error(`\`${a}\` must be used within \`${e}\``)}]}function i(e,t=[]){let r=[],n=()=>{let t=r.map(e=>s.createContext(e));return function(r){let a=r?.[e]||t;return s.useMemo(()=>({[`__scope${e}`]:{...r,[e]:a}}),[r,a])}};return n.scopeName=e,[function(t,n){let i=s.createContext(n),o=r.length;r=[...r,n];let l=t=>{let{scope:r,children:n,...l}=t,c=r?.[e]?.[o]||i,d=s.useMemo(()=>l,Object.values(l));return(0,a.jsx)(c.Provider,{value:d,children:n})};return l.displayName=t+"Provider",[l,function(r,a){let l=a?.[e]?.[o]||i,c=s.useContext(l);if(c)return c;if(void 0!==n)return n;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let a=r.reduce((t,{useScope:r,scopeName:s})=>{let a=r(e)[`__scope${s}`];return{...t,...a}},{});return s.useMemo(()=>({[`__scope${t.scopeName}`]:a}),[a])}};return r.scopeName=t.scopeName,r}(n,...t)]}},62409:(e,t,r)=>{"use strict";r.d(t,{WV:()=>o,jH:()=>l});var s=r(3729),a=r(81202),n=r(32751),i=r(95344),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,n.Z8)(`Primitive.${t}`),a=s.forwardRef((e,s)=>{let{asChild:a,...n}=e,o=a?r:t;return(0,i.jsx)(o,{...n,ref:s})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{});function l(e,t){e&&a.flushSync(()=>e.dispatchEvent(t))}},33183:(e,t,r)=>{"use strict";r.d(t,{T:()=>o});var s,a=r(3729),n=r(16069),i=(s||(s=r.t(a,2)))[" useInsertionEffect ".trim().toString()]||n.b;function o({prop:e,defaultProp:t,onChange:r=()=>{},caller:s}){let[n,o,l]=function({defaultProp:e,onChange:t}){let[r,s]=a.useState(e),n=a.useRef(r),o=a.useRef(t);return i(()=>{o.current=t},[t]),a.useEffect(()=>{n.current!==r&&(o.current?.(r),n.current=r)},[r,n]),[r,s,o]}({defaultProp:t,onChange:r}),c=void 0!==e,d=c?e:n;{let t=a.useRef(void 0!==e);a.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${s} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,s])}return[d,a.useCallback(t=>{if(c){let r="function"==typeof t?t(e):t;r!==e&&l.current?.(r)}else o(t)},[c,e,o,l])]}Symbol("RADIX:SYNC_STATE")},16069:(e,t,r)=>{"use strict";r.d(t,{b:()=>a});var s=r(3729),a=globalThis?.document?s.useLayoutEffect:()=>{}},92062:(e,t,r)=>{"use strict";r.d(t,{D:()=>a});var s=r(3729);function a(e){let t=s.useRef({value:e,previous:e});return s.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},63085:(e,t,r)=>{"use strict";r.d(t,{t:()=>n});var s=r(3729),a=r(16069);function n(e){let[t,r]=s.useState(void 0);return(0,a.b)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let s,a;if(!Array.isArray(t)||!t.length)return;let n=t[0];if("borderBoxSize"in n){let e=n.borderBoxSize,t=Array.isArray(e)?e[0]:e;s=t.inlineSize,a=t.blockSize}else s=e.offsetWidth,a=e.offsetHeight;r({width:s,height:a})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1638,7948,6671,4626],()=>r(86480));module.exports=s})();