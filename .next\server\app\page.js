/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\proj\\\\nextjs-saas\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cproj%5Cnextjs-saas%5Ccomponents%5Clanding%5Clanding-page-content.tsx&server=true!":
/*!******************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cproj%5Cnextjs-saas%5Ccomponents%5Clanding%5Clanding-page-content.tsx&server=true! ***!
  \******************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/landing/landing-page-content.tsx */ \"(ssr)/./components/landing/landing-page-content.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q3Byb2olNUNuZXh0anMtc2FhcyU1Q2NvbXBvbmVudHMlNUNsYW5kaW5nJTVDbGFuZGluZy1wYWdlLWNvbnRlbnQudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zYWFzLWFwcC8/YWExNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXHByb2pcXFxcbmV4dGpzLXNhYXNcXFxcY29tcG9uZW50c1xcXFxsYW5kaW5nXFxcXGxhbmRpbmctcGFnZS1jb250ZW50LnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cproj%5Cnextjs-saas%5Ccomponents%5Clanding%5Clanding-page-content.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cproj%5Cnextjs-saas%5Ccomponents%5Cproviders.tsx&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5Cproj%5Cnextjs-saas%5Capp%5Cglobals.css&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cproj%5Cnextjs-saas%5Ccomponents%5Cproviders.tsx&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5Cproj%5Cnextjs-saas%5Capp%5Cglobals.css&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/providers.tsx */ \"(ssr)/./components/providers.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q3Byb2olNUNuZXh0anMtc2FhcyU1Q2NvbXBvbmVudHMlNUNwcm92aWRlcnMudHN4Jm1vZHVsZXM9QyUzQSU1Q3Byb2olNUNuZXh0anMtc2FhcyU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNmb250JTVDZ29vZ2xlJTVDdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIySW50ZXIlMjIlMkMlMjJhcmd1bWVudHMlMjIlM0ElNUIlN0IlMjJzdWJzZXRzJTIyJTNBJTVCJTIybGF0aW4lMjIlNUQlN0QlNUQlMkMlMjJ2YXJpYWJsZU5hbWUlMjIlM0ElMjJpbnRlciUyMiU3RCZtb2R1bGVzPUMlM0ElNUNwcm9qJTVDbmV4dGpzLXNhYXMlNUNhcHAlNUNnbG9iYWxzLmNzcyZtb2R1bGVzPUMlM0ElNUNwcm9qJTVDbmV4dGpzLXNhYXMlNUNub2RlX21vZHVsZXMlNUNyZWFjdC1ob3QtdG9hc3QlNUNkaXN0JTVDaW5kZXgubWpzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnS0FBcUY7QUFDckYiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0anMtc2Fhcy1hcHAvP2Q0ZTUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxwcm9qXFxcXG5leHRqcy1zYWFzXFxcXGNvbXBvbmVudHNcXFxccHJvdmlkZXJzLnRzeFwiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxccHJvalxcXFxuZXh0anMtc2Fhc1xcXFxub2RlX21vZHVsZXNcXFxccmVhY3QtaG90LXRvYXN0XFxcXGRpc3RcXFxcaW5kZXgubWpzXCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cproj%5Cnextjs-saas%5Ccomponents%5Cproviders.tsx&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5Cproj%5Cnextjs-saas%5Capp%5Cglobals.css&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5Cproj%5Cnextjs-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./components/landing/landing-page-content.tsx":
/*!*****************************************************!*\
  !*** ./components/landing/landing-page-content.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LandingPageContent: () => (/* binding */ LandingPageContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/switch */ \"(ssr)/./components/ui/switch.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/quote.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/facebook.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/instagram.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Building2,Check,ChevronDown,ChevronUp,CreditCard,Facebook,FileText,Github,Globe,Instagram,Linkedin,Mail,MapPin,Menu,Phone,Quote,Shield,Star,Twitter,Users,X,Youtube,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/youtube.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_7__);\n/* __next_internal_client_entry_do_not_use__ LandingPageContent auto */ \n\n\n\n\n\n\n\n\n// Default fallback content\nconst defaultContent = {\n    hero: {\n        enabled: true,\n        title: \"Build Your SaaS Business\",\n        subtitle: \"The Complete Platform\",\n        description: \"Everything you need to launch, grow, and scale your SaaS business. From customer management to billing, we've got you covered.\",\n        primaryCTA: {\n            text: \"Start Free Trial\",\n            link: \"/auth/signup\"\n        },\n        secondaryCTA: {\n            text: \"Watch Demo\",\n            link: \"/demo\"\n        },\n        backgroundImage: \"\",\n        backgroundVideo: \"\"\n    },\n    features: {\n        enabled: true,\n        title: \"Everything You Need\",\n        subtitle: \"Powerful Features\",\n        items: [\n            {\n                id: \"1\",\n                title: \"Customer Management\",\n                description: \"Manage your customers, track interactions, and build lasting relationships.\",\n                icon: \"users\",\n                image: \"\"\n            },\n            {\n                id: \"2\",\n                title: \"Subscription Billing\",\n                description: \"Automated billing, invoicing, and payment processing for recurring revenue.\",\n                icon: \"credit-card\",\n                image: \"\"\n            },\n            {\n                id: \"3\",\n                title: \"Analytics & Reports\",\n                description: \"Comprehensive analytics to track your business performance and growth.\",\n                icon: \"bar-chart\",\n                image: \"\"\n            },\n            {\n                id: \"4\",\n                title: \"Multi-Tenant Architecture\",\n                description: \"Secure data isolation with company-based access control and team management.\",\n                icon: \"building\",\n                image: \"\"\n            },\n            {\n                id: \"5\",\n                title: \"Enterprise Security\",\n                description: \"Role-based access control with audit logs and data encryption.\",\n                icon: \"shield\",\n                image: \"\"\n            },\n            {\n                id: \"6\",\n                title: \"Global Ready\",\n                description: \"Multi-currency support and localization for worldwide businesses.\",\n                icon: \"globe\",\n                image: \"\"\n            }\n        ]\n    },\n    pricing: {\n        enabled: true,\n        title: \"Simple, Transparent Pricing\",\n        subtitle: \"Choose the plan that fits your needs\",\n        showPricingTable: true,\n        customMessage: \"\"\n    },\n    testimonials: {\n        enabled: true,\n        title: \"What Our Customers Say\",\n        subtitle: \"Trusted by thousands of businesses\",\n        items: [\n            {\n                id: \"1\",\n                name: \"John Smith\",\n                role: \"CEO\",\n                company: \"TechCorp\",\n                content: \"This platform has transformed how we manage our SaaS business. The automation features alone have saved us countless hours.\",\n                avatar: \"\",\n                rating: 5\n            },\n            {\n                id: \"2\",\n                name: \"Sarah Johnson\",\n                role: \"Founder\",\n                company: \"StartupXYZ\",\n                content: \"The best investment we've made for our business. The customer management features are incredibly powerful.\",\n                avatar: \"\",\n                rating: 5\n            },\n            {\n                id: \"3\",\n                name: \"Mike Chen\",\n                role: \"CTO\",\n                company: \"InnovateLab\",\n                content: \"Excellent platform with great support. The analytics help us make data-driven decisions every day.\",\n                avatar: \"\",\n                rating: 5\n            }\n        ]\n    },\n    faq: {\n        enabled: true,\n        title: \"Frequently Asked Questions\",\n        subtitle: \"Everything you need to know\",\n        items: [\n            {\n                id: \"1\",\n                question: \"How do I get started?\",\n                answer: \"Simply sign up for a free trial and follow our onboarding guide to set up your account. Our team is here to help you every step of the way.\"\n            },\n            {\n                id: \"2\",\n                question: \"Can I cancel anytime?\",\n                answer: \"Yes, you can cancel your subscription at any time. No long-term contracts or cancellation fees. Your data will remain accessible during the notice period.\"\n            },\n            {\n                id: \"3\",\n                question: \"Is my data secure?\",\n                answer: \"Absolutely. We use enterprise-grade security measures including encryption, regular backups, and compliance with industry standards like SOC 2 and GDPR.\"\n            },\n            {\n                id: \"4\",\n                question: \"Do you offer customer support?\",\n                answer: \"Yes, we provide 24/7 customer support via email, chat, and phone. Our premium plans also include dedicated account managers.\"\n            },\n            {\n                id: \"5\",\n                question: \"Can I integrate with other tools?\",\n                answer: \"Yes, we offer integrations with popular tools like Slack, Zapier, QuickBooks, and many more. We also provide a robust API for custom integrations.\"\n            }\n        ]\n    },\n    cta: {\n        enabled: true,\n        title: \"Ready to Get Started?\",\n        description: \"Join thousands of businesses already using our platform to grow their SaaS.\",\n        buttonText: \"Start Your Free Trial\",\n        buttonLink: \"/auth/signup\",\n        backgroundImage: \"\"\n    },\n    footer: {\n        enabled: true,\n        companyDescription: \"The complete SaaS platform for modern businesses.\",\n        links: [\n            {\n                id: \"1\",\n                title: \"Product\",\n                items: [\n                    {\n                        id: \"1\",\n                        text: \"Features\",\n                        link: \"/features\"\n                    },\n                    {\n                        id: \"2\",\n                        text: \"Pricing\",\n                        link: \"/pricing\"\n                    },\n                    {\n                        id: \"3\",\n                        text: \"Security\",\n                        link: \"/security\"\n                    },\n                    {\n                        id: \"4\",\n                        text: \"Integrations\",\n                        link: \"/integrations\"\n                    }\n                ]\n            },\n            {\n                id: \"2\",\n                title: \"Company\",\n                items: [\n                    {\n                        id: \"1\",\n                        text: \"About\",\n                        link: \"/about\"\n                    },\n                    {\n                        id: \"2\",\n                        text: \"Blog\",\n                        link: \"/blog\"\n                    },\n                    {\n                        id: \"3\",\n                        text: \"Careers\",\n                        link: \"/careers\"\n                    },\n                    {\n                        id: \"4\",\n                        text: \"Contact\",\n                        link: \"/contact\"\n                    }\n                ]\n            },\n            {\n                id: \"3\",\n                title: \"Support\",\n                items: [\n                    {\n                        id: \"1\",\n                        text: \"Help Center\",\n                        link: \"/help\"\n                    },\n                    {\n                        id: \"2\",\n                        text: \"Documentation\",\n                        link: \"/docs\"\n                    },\n                    {\n                        id: \"3\",\n                        text: \"API Reference\",\n                        link: \"/api\"\n                    },\n                    {\n                        id: \"4\",\n                        text: \"Status\",\n                        link: \"/status\"\n                    }\n                ]\n            }\n        ],\n        socialLinks: {\n            twitter: \"https://twitter.com/yourcompany\",\n            linkedin: \"https://linkedin.com/company/yourcompany\",\n            facebook: \"https://facebook.com/yourcompany\",\n            instagram: \"https://instagram.com/yourcompany\"\n        },\n        copyrightText: \"\\xa9 2024 Your Company. All rights reserved.\"\n    },\n    seo: {\n        title: \"SaaS Platform - Build Your Business\",\n        description: \"The complete SaaS platform for modern businesses. Customer management, billing, analytics, and more.\",\n        keywords: \"saas, platform, business, customer management, billing, analytics\",\n        ogImage: \"\"\n    }\n};\nconst getIconComponent = (iconName)=>{\n    const icons = {\n        users: _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        \"credit-card\": _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        \"bar-chart\": _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        building: _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        shield: _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        globe: _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        zap: _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        \"file-text\": _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n    };\n    return icons[iconName] || _barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"];\n};\nconst formatStorage = (bytes)=>{\n    const gb = bytes / (1024 * 1024 * 1024);\n    return gb >= 1 ? `${gb}GB` : `${Math.round(gb * 1024)}MB`;\n};\nconst getFeatureList = (plan)=>{\n    const features = [];\n    // Add usage limits\n    features.push(`Up to ${plan.maxUsers} users`);\n    features.push(`${plan.maxCompanies} ${plan.maxCompanies === 1 ? \"company\" : \"companies\"}`);\n    features.push(`${plan.maxCustomers} customers`);\n    features.push(`${plan.maxQuotations} quotations/month`);\n    features.push(`${plan.maxInvoices} invoices/month`);\n    features.push(`${formatStorage(plan.maxStorage)} storage`);\n    // Add feature flags\n    if (plan.features.basicReporting) features.push(\"Basic reporting\");\n    if (plan.features.emailSupport) features.push(\"Email support\");\n    if (plan.features.mobileApp) features.push(\"Mobile app access\");\n    if (plan.features.advancedAnalytics) features.push(\"Advanced analytics\");\n    if (plan.features.customBranding) features.push(\"Custom branding\");\n    if (plan.features.apiAccess) features.push(\"API access\");\n    if (plan.features.prioritySupport) features.push(\"Priority support\");\n    if (plan.features.customIntegrations) features.push(\"Custom integrations\");\n    if (plan.features.advancedSecurity) features.push(\"Advanced security\");\n    if (plan.features.dedicatedManager) features.push(\"Dedicated account manager\");\n    return features;\n};\nfunction LandingPageContent() {\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultContent);\n    const [plans, setPlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [openFAQ, setOpenFAQ] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isYearly, setIsYearly] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showScrollTop, setShowScrollTop] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchData = async ()=>{\n            try {\n                // Fetch CMS content\n                const cmsResponse = await fetch(\"/api/super-admin/cms\");\n                const cmsData = await cmsResponse.json();\n                if (cmsData.success && cmsData.content) {\n                    // Merge with default content to ensure all sections exist\n                    setContent({\n                        ...defaultContent,\n                        ...cmsData.content\n                    });\n                }\n                // Fetch pricing plans\n                const plansResponse = await fetch(\"/api/pricing-plans?publicOnly=true\");\n                const plansData = await plansResponse.json();\n                if (plansData.success) {\n                    // Sort plans by sortOrder and filter active public plans\n                    const activePlans = plansData.data.filter((plan)=>plan.isActive && plan.isPublic).sort((a, b)=>a.sortOrder - b.sortOrder);\n                    setPlans(activePlans);\n                }\n            } catch (error) {\n                console.error(\"Error fetching data:\", error);\n            // Use default content on error\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchData();\n    }, []);\n    // Scroll to top functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            setShowScrollTop(window.scrollY > 400);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    const scrollToTop = ()=>{\n        window.scrollTo({\n            top: 0,\n            behavior: \"smooth\"\n        });\n    };\n    const getPrice = (plan)=>{\n        if (isYearly && plan.yearlyPrice) {\n            return plan.yearlyPrice / 12 // Show monthly equivalent\n            ;\n        }\n        return plan.monthlyPrice;\n    };\n    const getYearlyDiscount = (plan)=>{\n        if (!plan.yearlyPrice || !plan.monthlyPrice) return 0;\n        const yearlyMonthly = plan.yearlyPrice / 12;\n        const discount = (plan.monthlyPrice - yearlyMonthly) / plan.monthlyPrice * 100;\n        return Math.round(discount);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 486,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n            lineNumber: 485,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"border-b bg-white/95 backdrop-blur-sm sticky top-0 z-50 shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-8 w-8 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: \"SaaS Platform\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 498,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"hidden md:flex items-center space-x-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#features\",\n                                            className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                            children: \"Features\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 505,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#pricing\",\n                                            className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                            children: \"Pricing\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#testimonials\",\n                                            className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                            children: \"Testimonials\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 511,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#faq\",\n                                            className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                            children: \"FAQ\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 514,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                            href: \"/contact\",\n                                            className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                            children: \"Contact\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 517,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                            href: \"/auth/signin\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"ghost\",\n                                                className: \"text-gray-600 hover:text-gray-900\",\n                                                children: \"Sign In\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 525,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 524,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                            href: \"/auth/signup\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                className: \"bg-blue-600 hover:bg-blue-700\",\n                                                children: \"Get Started Free\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 530,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 529,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 523,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"md:hidden p-2\",\n                                    onClick: ()=>setMobileMenuOpen(!mobileMenuOpen),\n                                    children: mobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-6 w-6 text-gray-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 542,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-6 w-6 text-gray-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 544,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 537,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 496,\n                            columnNumber: 11\n                        }, this),\n                        mobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden mt-4 pb-4 border-t\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex flex-col space-y-4 pt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#features\",\n                                        className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"Features\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 553,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#pricing\",\n                                        className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"Pricing\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 560,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#testimonials\",\n                                        className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"Testimonials\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 567,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#faq\",\n                                        className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"FAQ\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 574,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                        href: \"/contact\",\n                                        className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"Contact\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 581,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col space-y-2 pt-4 border-t\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                                href: \"/auth/signin\",\n                                                onClick: ()=>setMobileMenuOpen(false),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"ghost\",\n                                                    className: \"w-full justify-start\",\n                                                    children: \"Sign In\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 590,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 589,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                                href: \"/auth/signup\",\n                                                onClick: ()=>setMobileMenuOpen(false),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    className: \"w-full bg-blue-600 hover:bg-blue-700\",\n                                                    children: \"Get Started Free\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 595,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 594,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 588,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                lineNumber: 552,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 551,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 495,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 494,\n                columnNumber: 7\n            }, this),\n            content.hero?.enabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4 relative overflow-hidden\",\n                children: [\n                    content.hero.backgroundImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 z-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_7___default()), {\n                            src: content.hero.backgroundImage,\n                            alt: \"Hero Background\",\n                            fill: true,\n                            className: \"object-cover opacity-20\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 611,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                        lineNumber: 610,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto text-center relative z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto\",\n                            children: [\n                                content.hero.subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                    className: \"mb-4 text-sm px-4 py-2\",\n                                    children: content.hero.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 622,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-5xl md:text-6xl font-bold text-gray-900 mb-6\",\n                                    children: content.hero.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 626,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n                                    children: content.hero.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 629,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-4 justify-center mb-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                            href: content.hero.primaryCTA.link,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                size: \"lg\",\n                                                className: \"text-lg px-8 py-3\",\n                                                children: [\n                                                    content.hero.primaryCTA.text,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"ml-2 h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 636,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 634,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 633,\n                                            columnNumber: 17\n                                        }, this),\n                                        content.hero.secondaryCTA.text && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                            href: content.hero.secondaryCTA.link,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                size: \"lg\",\n                                                variant: \"outline\",\n                                                className: \"text-lg px-8 py-3\",\n                                                children: content.hero.secondaryCTA.text\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 641,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 640,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 632,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 620,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                        lineNumber: 619,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 608,\n                columnNumber: 9\n            }, this),\n            content.features?.enabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"features\",\n                className: \"py-20 px-4 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                    children: content.features.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 657,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                                    children: content.features.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 660,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 656,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: content.features.items.map((feature)=>{\n                                const IconComponent = getIconComponent(feature.icon);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"border-0 shadow-lg hover:shadow-xl transition-shadow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                        className: \"h-6 w-6 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 672,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 671,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-xl\",\n                                                    children: feature.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 674,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 670,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                className: \"text-gray-600\",\n                                                children: feature.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 677,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 676,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, feature.id, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 669,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 665,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 655,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 654,\n                columnNumber: 9\n            }, this),\n            content.pricing?.enabled && plans.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"pricing\",\n                className: \"py-20 px-4 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                    children: content.pricing.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 694,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 max-w-2xl mx-auto mb-8\",\n                                    children: content.pricing.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 697,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-4 mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: `text-sm ${!isYearly ? \"text-gray-900 font-medium\" : \"text-gray-500\"}`,\n                                            children: \"Monthly\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 703,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_5__.Switch, {\n                                            checked: isYearly,\n                                            onCheckedChange: setIsYearly\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 706,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: `text-sm ${isYearly ? \"text-gray-900 font-medium\" : \"text-gray-500\"}`,\n                                            children: \"Yearly\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 710,\n                                            columnNumber: 17\n                                        }, this),\n                                        plans.some((plan)=>getYearlyDiscount(plan) > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            className: \"ml-2 bg-green-100 text-green-800\",\n                                            children: [\n                                                \"Save up to \",\n                                                Math.max(...plans.map(getYearlyDiscount)),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 714,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 702,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 693,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto\",\n                            children: plans.map((plan, index)=>{\n                                const features = getFeatureList(plan);\n                                const price = getPrice(plan);\n                                const discount = getYearlyDiscount(plan);\n                                const isPopular = index === 1 // Middle plan is popular\n                                ;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: `relative ${isPopular ? \"border-blue-500 shadow-xl scale-105\" : \"border-gray-200 shadow-lg\"} bg-white`,\n                                    children: [\n                                        isPopular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -top-4 left-1/2 transform -translate-x-1/2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                className: \"bg-blue-500 text-white px-4 py-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 734,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    \"Most Popular\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 733,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 732,\n                                            columnNumber: 23\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            className: \"text-center pb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: plan.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 741,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                    className: \"text-gray-600 mt-2\",\n                                                    children: plan.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 742,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-baseline justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-5xl font-bold text-gray-900\",\n                                                                    children: [\n                                                                        \"$\",\n                                                                        price.toFixed(0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                    lineNumber: 748,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-500 ml-1\",\n                                                                    children: \"/month\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                    lineNumber: 751,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 747,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        isYearly && discount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-green-600 mt-2\",\n                                                            children: [\n                                                                \"Save \",\n                                                                discount,\n                                                                \"% with yearly billing\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 754,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        plan.trialDays > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-blue-600 mt-2\",\n                                                            children: [\n                                                                plan.trialDays,\n                                                                \"-day free trial\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 759,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 746,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 740,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        features.slice(0, 8).map((feature, featureIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-green-500 flex-shrink-0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                        lineNumber: 770,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-700 text-sm\",\n                                                                        children: feature\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                        lineNumber: 771,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, featureIndex, true, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                lineNumber: 769,\n                                                                columnNumber: 27\n                                                            }, this)),\n                                                        features.length > 8 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500 italic\",\n                                                            children: [\n                                                                \"+\",\n                                                                features.length - 8,\n                                                                \" more features\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 775,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 767,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                                    href: \"/auth/signup\",\n                                                    className: \"block\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        className: `w-full ${isPopular ? \"bg-blue-600 hover:bg-blue-700\" : \"\"}`,\n                                                        variant: isPopular ? \"default\" : \"outline\",\n                                                        size: \"lg\",\n                                                        children: [\n                                                            \"Get Started\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"ml-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                lineNumber: 788,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 782,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 781,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 766,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, plan.id, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 730,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 722,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mt-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"max-w-2xl mx-auto border-gray-200 shadow-lg bg-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                            children: \"Need something custom?\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 801,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-6\",\n                                            children: \"Contact our sales team for enterprise pricing, custom features, and dedicated support.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 804,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    size: \"lg\",\n                                                    variant: \"outline\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 809,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Contact Sales\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 808,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    size: \"lg\",\n                                                    variant: \"ghost\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 813,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Schedule Demo\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 812,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 807,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 800,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                lineNumber: 799,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 798,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 692,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 691,\n                columnNumber: 9\n            }, this),\n            content.testimonials?.enabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"testimonials\",\n                className: \"py-20 px-4 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                    children: content.testimonials.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 829,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                                    children: content.testimonials.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 832,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 828,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: content.testimonials.items.map((testimonial)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"border-0 shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    ...Array(testimonial.rating)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-5 w-5 text-yellow-400 fill-current\"\n                                                    }, i, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 843,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 841,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                className: \"h-8 w-8 text-gray-300 mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 846,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-6 italic\",\n                                                children: [\n                                                    '\"',\n                                                    testimonial.content,\n                                                    '\"'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 847,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    testimonial.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                        src: testimonial.avatar,\n                                                        alt: testimonial.name,\n                                                        width: 48,\n                                                        height: 48,\n                                                        className: \"rounded-full mr-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 852,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-gray-200 rounded-full mr-4 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-6 w-6 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 861,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 860,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-semibold text-gray-900\",\n                                                                children: testimonial.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                lineNumber: 865,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: [\n                                                                    testimonial.role,\n                                                                    \", \",\n                                                                    testimonial.company\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                                lineNumber: 866,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 864,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 850,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 840,\n                                        columnNumber: 19\n                                    }, this)\n                                }, testimonial.id, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 839,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 837,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 827,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 826,\n                columnNumber: 9\n            }, this),\n            content.faq?.enabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"faq\",\n                className: \"py-20 px-4 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto max-w-4xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                    children: content.faq.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 882,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600\",\n                                    children: content.faq.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 885,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 881,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: content.faq.items.map((faq)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"border shadow-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"p-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"w-full p-6 text-left flex items-center justify-between hover:bg-gray-50\",\n                                                onClick: ()=>setOpenFAQ(openFAQ === faq.id ? null : faq.id),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold text-gray-900\",\n                                                        children: faq.question\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 898,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    openFAQ === faq.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 900,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 902,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 894,\n                                                columnNumber: 21\n                                            }, this),\n                                            openFAQ === faq.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-6 pb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: faq.answer\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 907,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 906,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 893,\n                                        columnNumber: 19\n                                    }, this)\n                                }, faq.id, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 892,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 890,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 880,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 879,\n                columnNumber: 9\n            }, this),\n            content.cta?.enabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4 bg-blue-600 relative overflow-hidden\",\n                children: [\n                    content.cta.backgroundImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 z-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_7___default()), {\n                            src: content.cta.backgroundImage,\n                            alt: \"CTA Background\",\n                            fill: true,\n                            className: \"object-cover opacity-20\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 923,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                        lineNumber: 922,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto text-center relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl font-bold text-white mb-6\",\n                                children: content.cta.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                lineNumber: 932,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-blue-100 mb-8 max-w-2xl mx-auto\",\n                                children: content.cta.description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                lineNumber: 935,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                href: content.cta.buttonLink,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    size: \"lg\",\n                                    variant: \"secondary\",\n                                    className: \"text-lg px-8 py-3\",\n                                    children: [\n                                        content.cta.buttonText,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"ml-2 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 941,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 939,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                lineNumber: 938,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                        lineNumber: 931,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 920,\n                columnNumber: 9\n            }, this),\n            content.footer?.enabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-gray-900 text-white py-16 px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-5 gap-8 mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-8 w-8 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 956,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: \"SaaS Platform\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 957,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 955,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 mb-6 leading-relaxed\",\n                                            children: content.footer.companyDescription\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 959,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3 text-gray-400\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 966,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"<EMAIL>\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 967,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 965,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3 text-gray-400\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 970,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"+****************\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 971,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 969,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3 text-gray-400\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 974,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"San Francisco, CA\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 975,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 973,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 964,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-4\",\n                                            children: [\n                                                content.footer.socialLinks.twitter && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: content.footer.socialLinks.twitter,\n                                                    className: \"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-blue-600 transition-colors\",\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 988,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 982,\n                                                    columnNumber: 21\n                                                }, this),\n                                                content.footer.socialLinks.linkedin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: content.footer.socialLinks.linkedin,\n                                                    className: \"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-blue-700 transition-colors\",\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 998,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 992,\n                                                    columnNumber: 21\n                                                }, this),\n                                                content.footer.socialLinks.facebook && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: content.footer.socialLinks.facebook,\n                                                    className: \"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-blue-800 transition-colors\",\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 1008,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 1002,\n                                                    columnNumber: 21\n                                                }, this),\n                                                content.footer.socialLinks.instagram && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: content.footer.socialLinks.instagram,\n                                                    className: \"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-pink-600 transition-colors\",\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 1018,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 1012,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"https://github.com\",\n                                                    className: \"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 transition-colors\",\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 1027,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 1021,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"https://youtube.com\",\n                                                    className: \"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-red-600 transition-colors\",\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 1035,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                    lineNumber: 1029,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 980,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 954,\n                                    columnNumber: 15\n                                }, this),\n                                content.footer.links.map((linkGroup)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-4 text-white\",\n                                                children: linkGroup.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 1043,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-3\",\n                                                children: linkGroup.items.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                                            href: link.link,\n                                                            className: \"text-gray-400 hover:text-white transition-colors\",\n                                                            children: link.text\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                            lineNumber: 1047,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, link.id, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                        lineNumber: 1046,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 1044,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, linkGroup.id, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 1042,\n                                        columnNumber: 17\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 952,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-800 pt-8 mb-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold mb-4 text-white\",\n                                        children: \"Stay Updated\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 1063,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-4\",\n                                        children: \"Get the latest updates, tips, and insights delivered to your inbox.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 1064,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"email\",\n                                                placeholder: \"Enter your email\",\n                                                className: \"flex-1 px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 1068,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                className: \"bg-blue-600 hover:bg-blue-700\",\n                                                children: \"Subscribe\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                                lineNumber: 1073,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                        lineNumber: 1067,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                lineNumber: 1062,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 1061,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-4 md:mb-0\",\n                                    children: content.footer.copyrightText\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 1082,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-6 text-gray-400 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                            href: \"/privacy\",\n                                            className: \"hover:text-white transition-colors\",\n                                            children: \"Privacy Policy\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 1086,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                            href: \"/terms\",\n                                            className: \"hover:text-white transition-colors\",\n                                            children: \"Terms of Service\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 1089,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                            href: \"/cookies\",\n                                            className: \"hover:text-white transition-colors\",\n                                            children: \"Cookie Policy\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 1092,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                            href: \"/security\",\n                                            className: \"hover:text-white transition-colors\",\n                                            children: \"Security\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                            lineNumber: 1095,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                                    lineNumber: 1085,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                            lineNumber: 1081,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 951,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 950,\n                columnNumber: 9\n            }, this),\n            showScrollTop && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: scrollToTop,\n                className: \"fixed bottom-8 right-8 w-12 h-12 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg transition-all duration-300 z-50 flex items-center justify-center\",\n                \"aria-label\": \"Scroll to top\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Building2_Check_ChevronDown_ChevronUp_CreditCard_Facebook_FileText_Github_Globe_Instagram_Linkedin_Mail_MapPin_Menu_Phone_Quote_Shield_Star_Twitter_Users_X_Youtube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                    className: \"h-6 w-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                    lineNumber: 1111,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n                lineNumber: 1106,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\landing\\\\landing-page-content.tsx\",\n        lineNumber: 492,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/landing/landing-page-content.tsx\n");

/***/ }),

/***/ "(ssr)/./components/providers.tsx":
/*!**********************************!*\
  !*** ./components/providers.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.module.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\n\nfunction Providers({ children }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(()=>new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.QueryClient({\n            defaultOptions: {\n                queries: {\n                    staleTime: 60 * 1000,\n                    retry: 1\n                }\n            }\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.QueryClientProvider, {\n            client: queryClient,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: \"light\",\n                enableSystem: true,\n                disableTransitionOnChange: true,\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\providers.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\providers.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\providers.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/badge.tsx":
/*!*********************************!*\
  !*** ./components/ui/badge.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\",\n            success: \"border-transparent bg-green-500 text-white hover:bg-green-600\",\n            warning: \"border-transparent bg-yellow-500 text-white hover:bg-yellow-600\",\n            info: \"border-transparent bg-blue-500 text-white hover:bg-blue-600\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 32,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 47,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 59,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 67,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/switch.tsx":
/*!**********************************!*\
  !*** ./components/ui/switch.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Switch: () => (/* binding */ Switch)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_switch__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-switch */ \"(ssr)/./node_modules/@radix-ui/react-switch/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Switch auto */ \n\n\n\nconst Switch = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_switch__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\", className),\n        ...props,\n        ref: ref,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_switch__WEBPACK_IMPORTED_MODULE_3__.Thumb, {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0\")\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\ui\\\\switch.tsx\",\n            lineNumber: 20,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\ui\\\\switch.tsx\",\n        lineNumber: 12,\n        columnNumber: 3\n    }, undefined));\nSwitch.displayName = _radix_ui_react_switch__WEBPACK_IMPORTED_MODULE_3__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/switch.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculatePercentage: () => (/* binding */ calculatePercentage),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   generateContractNumber: () => (/* binding */ generateContractNumber),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   generateInvoiceNumber: () => (/* binding */ generateInvoiceNumber),\n/* harmony export */   generateQuotationNumber: () => (/* binding */ generateQuotationNumber),\n/* harmony export */   getInitials: () => (/* binding */ getInitials),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   isValidPhone: () => (/* binding */ isValidPhone),\n/* harmony export */   slugify: () => (/* binding */ slugify),\n/* harmony export */   truncate: () => (/* binding */ truncate)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatCurrency(amount, currency = \"USD\", locale = \"en-US\") {\n    return new Intl.NumberFormat(locale, {\n        style: \"currency\",\n        currency\n    }).format(amount);\n}\nfunction formatDate(date, options = {}) {\n    return new Intl.DateTimeFormat(\"en-US\", {\n        year: \"numeric\",\n        month: \"short\",\n        day: \"numeric\",\n        ...options\n    }).format(new Date(date));\n}\nfunction generateId(prefix) {\n    const timestamp = Date.now().toString(36);\n    const randomStr = Math.random().toString(36).substring(2, 8);\n    return prefix ? `${prefix}_${timestamp}_${randomStr}` : `${timestamp}_${randomStr}`;\n}\nfunction slugify(text) {\n    return text.toLowerCase().replace(/[^\\w ]+/g, \"\").replace(/ +/g, \"-\");\n}\nfunction truncate(text, length = 100) {\n    if (text.length <= length) return text;\n    return text.substring(0, length) + \"...\";\n}\nfunction getInitials(name) {\n    return name.split(\" \").map((word)=>word.charAt(0)).join(\"\").toUpperCase().substring(0, 2);\n}\nfunction calculatePercentage(value, total) {\n    if (total === 0) return 0;\n    return Math.round(value / total * 100);\n}\nfunction debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\nfunction isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\nfunction isValidPhone(phone) {\n    const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/;\n    return phoneRegex.test(phone.replace(/\\s/g, \"\"));\n}\nfunction generateInvoiceNumber() {\n    const year = new Date().getFullYear();\n    const month = String(new Date().getMonth() + 1).padStart(2, \"0\");\n    const random = Math.floor(Math.random() * 9999).toString().padStart(4, \"0\");\n    return `INV-${year}${month}-${random}`;\n}\nfunction generateQuotationNumber() {\n    const year = new Date().getFullYear();\n    const month = String(new Date().getMonth() + 1).padStart(2, \"0\");\n    const random = Math.floor(Math.random() * 9999).toString().padStart(4, \"0\");\n    return `QUO-${year}${month}-${random}`;\n}\nfunction generateContractNumber() {\n    const year = new Date().getFullYear();\n    const month = String(new Date().getMonth() + 1).padStart(2, \"0\");\n    const random = Math.floor(Math.random() * 9999).toString().padStart(4, \"0\");\n    return `CON-${year}${month}-${random}`;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8f869fe146ad\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0anMtc2Fhcy1hcHAvLi9hcHAvZ2xvYmFscy5jc3M/YzYxMCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjhmODY5ZmUxNDZhZFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/providers */ \"(rsc)/./components/providers.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\n\n\nconst metadata = {\n    title: {\n        default: \"Business SaaS - Complete Business Management Solution\",\n        template: \"%s | Business SaaS\"\n    },\n    description: \"Complete SaaS solution for business management including CRM, invoicing, quotations, and more.\",\n    keywords: [\n        \"SaaS\",\n        \"Business Management\",\n        \"CRM\",\n        \"Invoicing\",\n        \"Quotations\"\n    ],\n    authors: [\n        {\n            name: \"Business SaaS Team\"\n        }\n    ],\n    creator: \"Business SaaS\",\n    openGraph: {\n        type: \"website\",\n        locale: \"en_US\",\n        url: process.env.NEXT_PUBLIC_APP_URL,\n        title: \"Business SaaS - Complete Business Management Solution\",\n        description: \"Complete SaaS solution for business management including CRM, invoicing, quotations, and more.\",\n        siteName: \"Business SaaS\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Business SaaS - Complete Business Management Solution\",\n        description: \"Complete SaaS solution for business management including CRM, invoicing, quotations, and more.\",\n        creator: \"@businesssaas\"\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers__WEBPACK_IMPORTED_MODULE_1__.Providers, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n                        position: \"top-right\",\n                        toastOptions: {\n                            duration: 4000,\n                            style: {\n                                background: \"#363636\",\n                                color: \"#fff\"\n                            },\n                            success: {\n                                duration: 3000,\n                                iconTheme: {\n                                    primary: \"#4ade80\",\n                                    secondary: \"#fff\"\n                                }\n                            },\n                            error: {\n                                duration: 4000,\n                                iconTheme: {\n                                    primary: \"#ef4444\",\n                                    secondary: \"#fff\"\n                                }\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\layout.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\layout.tsx\",\n                lineNumber: 53,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\layout.tsx\",\n            lineNumber: 52,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\layout.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,CreditCard,FileText,Shield,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,CreditCard,FileText,Shield,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,CreditCard,FileText,Shield,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,CreditCard,FileText,Shield,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,CreditCard,FileText,Shield,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,CreditCard,FileText,Shield,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _components_landing_landing_page_content__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/landing/landing-page-content */ \"(rsc)/./components/landing/landing-page-content.tsx\");\n\n\n\nconst features = [\n    {\n        icon: _barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        title: \"Customer Management\",\n        description: \"Complete CRM with lead tracking, customer profiles, and activity management.\"\n    },\n    {\n        icon: _barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: \"Invoicing & Quotations\",\n        description: \"Professional invoices and quotations with automated calculations and templates.\"\n    },\n    {\n        icon: _barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Multi-Tenant Architecture\",\n        description: \"Secure data isolation with company-based access control and team management.\"\n    },\n    {\n        icon: _barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Subscription Management\",\n        description: \"Flexible pricing plans with trial management and automated billing.\"\n    },\n    {\n        icon: _barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"Analytics & Reports\",\n        description: \"Real-time dashboards with business insights and performance metrics.\"\n    },\n    {\n        icon: _barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: \"Enterprise Security\",\n        description: \"Role-based access control with audit logs and data encryption.\"\n    }\n];\nconst businessTypes = [\n    {\n        name: \"Retail Business\",\n        color: \"bg-blue-100 text-blue-800\"\n    },\n    {\n        name: \"Healthcare Services\",\n        color: \"bg-red-100 text-red-800\"\n    },\n    {\n        name: \"Consulting Services\",\n        color: \"bg-indigo-100 text-indigo-800\"\n    },\n    {\n        name: \"Manufacturing\",\n        color: \"bg-orange-100 text-orange-800\"\n    },\n    {\n        name: \"Education Services\",\n        color: \"bg-green-100 text-green-800\"\n    },\n    {\n        name: \"Jewellery Business\",\n        color: \"bg-purple-100 text-purple-800\"\n    }\n];\nconst benefits = [\n    \"Complete business management in one platform\",\n    \"Industry-specific templates and workflows\",\n    \"Real-time collaboration and team management\",\n    \"Automated invoicing and payment tracking\",\n    \"Advanced analytics and reporting\",\n    \"Mobile-responsive design\",\n    \"24/7 customer support\",\n    \"Regular updates and new features\"\n];\nfunction HomePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_landing_page_content__WEBPACK_IMPORTED_MODULE_1__.LandingPageContent, {}, void 0, false, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\page.tsx\",\n        lineNumber: 75,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./components/landing/landing-page-content.tsx":
/*!*****************************************************!*\
  !*** ./components/landing/landing-page-content.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LandingPageContent: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\proj\nextjs-saas\components\landing\landing-page-content.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\proj\nextjs-saas\components\landing\landing-page-content.tsx#LandingPageContent`);


/***/ }),

/***/ "(rsc)/./components/providers.tsx":
/*!**********************************!*\
  !*** ./components/providers.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\proj\nextjs-saas\components\providers.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\proj\nextjs-saas\components\providers.tsx#Providers`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@tanstack","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/react-hot-toast","vendor-chunks/next-themes","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/lucide-react","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/class-variance-authority"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();