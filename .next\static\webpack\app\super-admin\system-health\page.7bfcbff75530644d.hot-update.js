"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/super-admin/system-health/page",{

/***/ "(app-pages-browser)/./app/super-admin/system-health/page.tsx":
/*!************************************************!*\
  !*** ./app/super-admin/system-health/page.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SystemHealthPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./components/ui/progress.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Cpu,Database,MemoryStick,RefreshCw,Server,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Cpu,Database,MemoryStick,RefreshCw,Server,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Cpu,Database,MemoryStick,RefreshCw,Server,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Cpu,Database,MemoryStick,RefreshCw,Server,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Cpu,Database,MemoryStick,RefreshCw,Server,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Cpu,Database,MemoryStick,RefreshCw,Server,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Cpu,Database,MemoryStick,RefreshCw,Server,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Cpu,Database,MemoryStick,RefreshCw,Server,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/memory-stick.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Cpu,Database,MemoryStick,RefreshCw,Server,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Cpu,Database,MemoryStick,RefreshCw,Server,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction SystemHealthPage() {\n    var _session_user, _healthData_metrics, _healthData_metrics1, _healthData_system_memory, _healthData_system, _healthData_system_memory1, _healthData_system1, _healthData_system2, _healthData_database, _healthData_database1, _healthData_system3, _healthData_database2, _healthData_database3, _healthData_database4, _healthData_database5, _healthData_system4, _healthData_system5, _healthData_system6, _healthData_system7, _healthData_system8, _healthData_database6, _healthData_recentLogs;\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [healthData, setHealthData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastUpdated, setLastUpdated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                lineNumber: 97,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n            lineNumber: 96,\n            columnNumber: 7\n        }, this);\n    }\n    if (status === \"unauthenticated\") {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.redirect)(\"/auth/signin\");\n    }\n    if ((session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.role) !== \"SUPER_ADMIN\") {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.redirect)(\"/dashboard\");\n    }\n    const fetchHealthData = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/super-admin/system-health\");\n            if (!response.ok) throw new Error(\"Failed to fetch system health\");\n            const data = await response.json();\n            setHealthData(data);\n            setLastUpdated(new Date());\n        } catch (error) {\n            console.error(\"Error fetching system health:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchHealthData();\n        // Auto-refresh every 30 seconds\n        const interval = setInterval(fetchHealthData, 30000);\n        return ()=>clearInterval(interval);\n    }, []);\n    const getStatusBadge = (status)=>{\n        const config = {\n            healthy: {\n                variant: \"default\",\n                icon: _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                color: \"text-green-600\"\n            },\n            warning: {\n                variant: \"secondary\",\n                icon: _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                color: \"text-yellow-600\"\n            },\n            critical: {\n                variant: \"destructive\",\n                icon: _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                color: \"text-red-600\"\n            },\n            unhealthy: {\n                variant: \"destructive\",\n                icon: _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                color: \"text-red-600\"\n            }\n        };\n        const statusConfig = config[status] || config.unhealthy;\n        const Icon = statusConfig.icon;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n            variant: statusConfig.variant,\n            className: \"flex items-center space-x-1\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                    className: \"h-3 w-3\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: status.toUpperCase()\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n            lineNumber: 146,\n            columnNumber: 7\n        }, this);\n    };\n    const formatBytes = (bytes)=>{\n        if (bytes === 0) return \"0 Bytes\";\n        const k = 1024;\n        const sizes = [\n            \"Bytes\",\n            \"KB\",\n            \"MB\",\n            \"GB\",\n            \"TB\"\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n    };\n    const formatUptime = (seconds)=>{\n        const days = Math.floor(seconds / 86400);\n        const hours = Math.floor(seconds % 86400 / 3600);\n        const minutes = Math.floor(seconds % 3600 / 60);\n        if (days > 0) return \"\".concat(days, \"d \").concat(hours, \"h \").concat(minutes, \"m\");\n        if (hours > 0) return \"\".concat(hours, \"h \").concat(minutes, \"m\");\n        return \"\".concat(minutes, \"m\");\n    };\n    const getLogLevelBadge = (level)=>{\n        const variants = {\n            ERROR: \"destructive\",\n            WARN: \"secondary\",\n            INFO: \"default\",\n            DEBUG: \"outline\"\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n            variant: variants[level] || \"default\",\n            children: level\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n            lineNumber: 180,\n            columnNumber: 7\n        }, this);\n    };\n    if (!healthData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                lineNumber: 189,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n            lineNumber: 188,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row sm:items-center justify-between gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"System Health\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, this),\n                                    getStatusBadge(healthData.status)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 mt-1\",\n                                children: \"Real-time system monitoring and diagnostics\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, this),\n                            lastUpdated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1 mt-2 text-sm text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Last updated: \",\n                                            lastUpdated.toLocaleTimeString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        onClick: fetchHealthData,\n                        disabled: loading,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-4 w-4 mr-2 \".concat(loading ? \"animate-spin\" : \"\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, this),\n                            \"Refresh\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-500\",\n                                                    children: \"CPU Usage\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: [\n                                                        (healthData === null || healthData === void 0 ? void 0 : (_healthData_metrics = healthData.metrics) === null || _healthData_metrics === void 0 ? void 0 : _healthData_metrics.memoryUsage) ? healthData.metrics.memoryUsage.toFixed(1) : \"0.0\",\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-8 w-8 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_7__.Progress, {\n                                    value: (healthData === null || healthData === void 0 ? void 0 : (_healthData_metrics1 = healthData.metrics) === null || _healthData_metrics1 === void 0 ? void 0 : _healthData_metrics1.memoryUsage) || 0,\n                                    className: \"mt-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-500\",\n                                                    children: \"Memory Usage\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: [\n                                                        (healthData === null || healthData === void 0 ? void 0 : (_healthData_system = healthData.system) === null || _healthData_system === void 0 ? void 0 : (_healthData_system_memory = _healthData_system.memory) === null || _healthData_system_memory === void 0 ? void 0 : _healthData_system_memory.usagePercent) ? healthData.system.memory.usagePercent.toFixed(1) : \"0.0\",\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-8 w-8 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_7__.Progress, {\n                                    value: (healthData === null || healthData === void 0 ? void 0 : (_healthData_system1 = healthData.system) === null || _healthData_system1 === void 0 ? void 0 : (_healthData_system_memory1 = _healthData_system1.memory) === null || _healthData_system_memory1 === void 0 ? void 0 : _healthData_system_memory1.usagePercent) || 0,\n                                    className: \"mt-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-2\",\n                                    children: (healthData === null || healthData === void 0 ? void 0 : (_healthData_system2 = healthData.system) === null || _healthData_system2 === void 0 ? void 0 : _healthData_system2.memory) ? \"\".concat(formatBytes(healthData.system.memory.used), \" / \").concat(formatBytes(healthData.system.memory.total)) : \"N/A\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-500\",\n                                                    children: \"Database Response\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: [\n                                                        (healthData === null || healthData === void 0 ? void 0 : (_healthData_database = healthData.database) === null || _healthData_database === void 0 ? void 0 : _healthData_database.responseTime) || 0,\n                                                        \"ms\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-8 w-8 text-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-3\",\n                                    children: getStatusBadge((healthData === null || healthData === void 0 ? void 0 : (_healthData_database1 = healthData.database) === null || _healthData_database1 === void 0 ? void 0 : _healthData_database1.status) || \"unknown\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-500\",\n                                                    children: \"System Uptime\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: (healthData === null || healthData === void 0 ? void 0 : (_healthData_system3 = healthData.system) === null || _healthData_system3 === void 0 ? void 0 : _healthData_system3.uptime) ? formatUptime(healthData.system.uptime) : \"N/A\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-8 w-8 text-orange-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-3\",\n                                    children: (healthData === null || healthData === void 0 ? void 0 : healthData.system) ? \"\".concat(healthData.system.platform, \" \").concat(healthData.system.arch) : \"N/A\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Cpu_Database_MemoryStick_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Database Metrics\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-blue-600\",\n                                            children: (healthData === null || healthData === void 0 ? void 0 : (_healthData_database2 = healthData.database) === null || _healthData_database2 === void 0 ? void 0 : _healthData_database2.totalUsers) || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"Total Users\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-green-600\",\n                                            children: (healthData === null || healthData === void 0 ? void 0 : (_healthData_database3 = healthData.database) === null || _healthData_database3 === void 0 ? void 0 : _healthData_database3.totalCompanies) || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"Total Companies\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-purple-600\",\n                                            children: (healthData === null || healthData === void 0 ? void 0 : (_healthData_database4 = healthData.database) === null || _healthData_database4 === void 0 ? void 0 : _healthData_database4.totalActivities) || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"Total Activities\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-red-600\",\n                                            children: (healthData === null || healthData === void 0 ? void 0 : (_healthData_database5 = healthData.database) === null || _healthData_database5 === void 0 ? void 0 : _healthData_database5.recentErrors) || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"Recent Errors (24h)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                lineNumber: 289,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                    children: \"System Information\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-500\",\n                                                    children: \"Platform:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: (healthData === null || healthData === void 0 ? void 0 : (_healthData_system4 = healthData.system) === null || _healthData_system4 === void 0 ? void 0 : _healthData_system4.platform) || \"N/A\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-500\",\n                                                    children: \"Architecture:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: (healthData === null || healthData === void 0 ? void 0 : (_healthData_system5 = healthData.system) === null || _healthData_system5 === void 0 ? void 0 : _healthData_system5.arch) || \"N/A\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-500\",\n                                                    children: \"Node.js Version:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: (healthData === null || healthData === void 0 ? void 0 : (_healthData_system6 = healthData.system) === null || _healthData_system6 === void 0 ? void 0 : _healthData_system6.nodeVersion) || \"N/A\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-500\",\n                                                    children: \"CPU Cores:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: (healthData === null || healthData === void 0 ? void 0 : (_healthData_system7 = healthData.system) === null || _healthData_system7 === void 0 ? void 0 : _healthData_system7.cpuCount) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-500\",\n                                                    children: \"Load Average:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: (healthData === null || healthData === void 0 ? void 0 : (_healthData_system8 = healthData.system) === null || _healthData_system8 === void 0 ? void 0 : _healthData_system8.loadAverage) ? healthData.system.loadAverage.map((load)=>load.toFixed(2)).join(\", \") : \"N/A\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-500\",\n                                                    children: \"Database Size:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: [\n                                                        (healthData === null || healthData === void 0 ? void 0 : (_healthData_database6 = healthData.database) === null || _healthData_database6 === void 0 ? void 0 : _healthData_database6.size) || 0,\n                                                        \" MB\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                    children: \"Recent System Logs\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3 max-h-64 overflow-y-auto\",\n                                    children: (healthData === null || healthData === void 0 ? void 0 : (_healthData_recentLogs = healthData.recentLogs) === null || _healthData_recentLogs === void 0 ? void 0 : _healthData_recentLogs.length) > 0 ? healthData.recentLogs.map((log)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3 p-3 bg-gray-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: getLogLevelBadge(log.level)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900 truncate\",\n                                                            children: log.message\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                            lineNumber: 368,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 mt-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: log.source\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                                    lineNumber: 372,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                log.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-400\",\n                                                                    children: [\n                                                                        \"• \",\n                                                                        log.category\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                                    lineNumber: 374,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-400\",\n                                                                    children: [\n                                                                        \"• \",\n                                                                        new Date(log.createdAt).toLocaleTimeString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                                    lineNumber: 376,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                            lineNumber: 371,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, log.id, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 17\n                                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"No recent logs available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n                lineNumber: 319,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\super-admin\\\\system-health\\\\page.tsx\",\n        lineNumber: 195,\n        columnNumber: 5\n    }, this);\n}\n_s(SystemHealthPage, \"F3I0p5jaID9HHzOMQkGJZ7rVEKw=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession\n    ];\n});\n_c = SystemHealthPage;\nvar _c;\n$RefreshReg$(_c, \"SystemHealthPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/super-admin/system-health/page.tsx\n"));

/***/ })

});