'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ContractForm } from '@/components/contracts/contract-form'
import { 
  ArrowLeft, 
  Edit, 
  Trash2, 
  Send, 
  Download, 
  Copy,
  Mail,
  Phone,
  Building2,
  Calendar,
  User,
  FileText,
  DollarSign,
  Calculator,
  Activity,
  Plus,
  FileSignature,
  CheckCircle,
  Clock,
  AlertTriangle,
  Tag,
  Repeat,
  Shield
} from 'lucide-react'
import { toast } from 'react-hot-toast'
import Link from 'next/link'

interface Contract {
  id: string
  contractNumber: string
  title: string
  description: string | null
  type: 'SERVICE' | 'PRODUCT' | 'SUBSCRIPTION' | 'MAINTENANCE' | 'CONSULTING' | 'OTHER'
  status: 'DRAFT' | 'REVIEW' | 'SENT' | 'SIGNED' | 'ACTIVE' | 'COMPLETED' | 'CANCELLED' | 'EXPIRED'
  value: number | null
  currency: string
  startDate: string | null
  endDate: string | null
  renewalDate: string | null
  autoRenewal: boolean
  renewalPeriod: number | null
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
  signatureRequired: boolean
  terms: string | null
  conditions: string | null
  notes: string | null
  tags: string[]
  customer: {
    id: string
    name: string
    email: string | null
    company: string | null
    phone: string | null
    address: string | null
    city: string | null
    state: string | null
    country: string | null
    postalCode: string | null
  }
  quotation: {
    id: string
    quotationNumber: string
    title: string
    total: number
  } | null
  invoice: {
    id: string
    invoiceNumber: string
    total: number
    status: string
  } | null
  template: {
    id: string
    name: string
    type: string
    content: string | null
  } | null
  createdBy: {
    name: string | null
    email: string | null
  }
  assignedTo: {
    name: string | null
    email: string | null
  } | null
  signatures: any[]
  documents: any[]
  activities: any[]
  _count: {
    activities: number
    signatures: number
    documents: number
  }
}

export default function ContractDetailPage() {
  const params = useParams()
  const router = useRouter()
  const [contract, setContract] = useState<Contract | null>(null)
  const [loading, setLoading] = useState(true)
  const [showEditForm, setShowEditForm] = useState(false)

  const fetchContract = async () => {
    try {
      const response = await fetch(`/api/contracts/${params.id}`)
      if (!response.ok) {
        if (response.status === 404) {
          toast.error('Contract not found')
          router.push('/dashboard/contracts')
          return
        }
        throw new Error('Failed to fetch contract')
      }
      
      const data = await response.json()
      setContract(data)
    } catch (error) {
      toast.error('Failed to load contract details')
      console.error('Error fetching contract:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (params.id) {
      fetchContract()
    }
  }, [params.id])

  const handleDelete = async () => {
    if (!contract || !confirm(`Are you sure you want to delete contract "${contract.contractNumber}"?`)) return

    try {
      const response = await fetch(`/api/contracts/${contract.id}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to delete contract')
      }

      toast.success('Contract deleted successfully')
      router.push('/dashboard/contracts')
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to delete contract')
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'DRAFT':
        return <Badge variant="secondary">Draft</Badge>
      case 'REVIEW':
        return <Badge variant="warning">Review</Badge>
      case 'SENT':
        return <Badge variant="info">Sent</Badge>
      case 'SIGNED':
        return <Badge variant="success">Signed</Badge>
      case 'ACTIVE':
        return <Badge variant="success">Active</Badge>
      case 'COMPLETED':
        return <Badge variant="success">Completed</Badge>
      case 'CANCELLED':
        return <Badge variant="destructive">Cancelled</Badge>
      case 'EXPIRED':
        return <Badge variant="destructive">Expired</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'SIGNED':
      case 'ACTIVE':
      case 'COMPLETED':
        return <CheckCircle className="h-5 w-5 text-green-600" />
      case 'EXPIRED':
      case 'CANCELLED':
        return <AlertTriangle className="h-5 w-5 text-red-600" />
      case 'DRAFT':
        return <Clock className="h-5 w-5 text-gray-600" />
      default:
        return <FileSignature className="h-5 w-5 text-blue-600" />
    }
  }

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'URGENT':
        return <Badge variant="destructive">Urgent</Badge>
      case 'HIGH':
        return <Badge variant="warning">High</Badge>
      case 'MEDIUM':
        return <Badge variant="info">Medium</Badge>
      case 'LOW':
        return <Badge variant="secondary">Low</Badge>
      default:
        return <Badge variant="secondary">{priority}</Badge>
    }
  }

  const getTypeBadge = (type: string) => {
    const colors: Record<string, string> = {
      SERVICE: 'bg-blue-100 text-blue-800',
      PRODUCT: 'bg-green-100 text-green-800',
      SUBSCRIPTION: 'bg-purple-100 text-purple-800',
      MAINTENANCE: 'bg-orange-100 text-orange-800',
      CONSULTING: 'bg-indigo-100 text-indigo-800',
      OTHER: 'bg-gray-100 text-gray-800'
    }
    
    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${colors[type] || colors.OTHER}`}>
        {type}
      </span>
    )
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!contract) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Contract not found</p>
        <Button asChild className="mt-4">
          <Link href="/dashboard/contracts">Back to Contracts</Link>
        </Button>
      </div>
    )
  }

  const isExpired = contract.endDate && new Date(contract.endDate) < new Date()
  const isExpiring = contract.endDate && new Date(contract.endDate) < new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/dashboard/contracts">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Contracts
            </Link>
          </Button>
          <div>
            <div className="flex items-center space-x-3">
              {getStatusIcon(contract.status)}
              <h1 className="text-3xl font-bold text-gray-900">{contract.contractNumber}</h1>
            </div>
            <div className="flex items-center space-x-2 mt-1">
              {getStatusBadge(contract.status)}
              {getPriorityBadge(contract.priority)}
              {getTypeBadge(contract.type)}
              {isExpired && <Badge variant="destructive">Expired</Badge>}
              {isExpiring && !isExpired && <Badge variant="warning">Expiring Soon</Badge>}
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline">
            <Copy className="h-4 w-4 mr-2" />
            Duplicate
          </Button>
          <Button variant="outline">
            <Send className="h-4 w-4 mr-2" />
            Send for Signature
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            PDF
          </Button>
          <Button variant="outline">
            <FileSignature className="h-4 w-4 mr-2" />
            Signatures
          </Button>
          <Button variant="outline" onClick={() => setShowEditForm(true)}>
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
          <Button 
            variant="destructive" 
            onClick={handleDelete}
            disabled={contract.status === 'SIGNED' || contract.status === 'ACTIVE' || contract._count.signatures > 0}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Contract Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileSignature className="h-5 w-5 mr-2" />
                Contract Overview
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold">{contract.title}</h3>
                {contract.description && (
                  <p className="text-gray-600 mt-2">{contract.description}</p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-500">Contract Number</p>
                  <p className="font-medium">{contract.contractNumber}</p>
                </div>
                
                <div>
                  <p className="text-sm text-gray-500">Type</p>
                  <div className="mt-1">{getTypeBadge(contract.type)}</div>
                </div>
                
                <div>
                  <p className="text-sm text-gray-500">Status</p>
                  <div className="mt-1">{getStatusBadge(contract.status)}</div>
                </div>
                
                <div>
                  <p className="text-sm text-gray-500">Priority</p>
                  <div className="mt-1">{getPriorityBadge(contract.priority)}</div>
                </div>

                {contract.value && (
                  <div>
                    <p className="text-sm text-gray-500">Contract Value</p>
                    <p className="font-medium text-lg text-green-600">
                      {contract.currency} {contract.value.toLocaleString()}
                    </p>
                  </div>
                )}

                <div>
                  <p className="text-sm text-gray-500">Signature Required</p>
                  <div className="flex items-center space-x-2 mt-1">
                    {contract.signatureRequired ? (
                      <>
                        <Shield className="h-4 w-4 text-green-600" />
                        <span className="text-green-600">Yes</span>
                      </>
                    ) : (
                      <>
                        <Shield className="h-4 w-4 text-gray-400" />
                        <span className="text-gray-600">No</span>
                      </>
                    )}
                  </div>
                </div>
              </div>

              {/* Tags */}
              {contract.tags && contract.tags.length > 0 && (
                <div>
                  <p className="text-sm text-gray-500 mb-2">Tags</p>
                  <div className="flex flex-wrap gap-2">
                    {contract.tags.map((tag) => (
                      <Badge key={tag} variant="outline" className="flex items-center space-x-1">
                        <Tag className="h-3 w-3" />
                        <span>{tag}</span>
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Customer Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="h-5 w-5 mr-2" />
                Customer Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-500">Customer Name</p>
                  <p className="font-medium">{contract.customer.name}</p>
                </div>
                
                {contract.customer.company && (
                  <div>
                    <p className="text-sm text-gray-500">Company</p>
                    <p className="font-medium">{contract.customer.company}</p>
                  </div>
                )}
                
                {contract.customer.email && (
                  <div>
                    <p className="text-sm text-gray-500">Email</p>
                    <p className="font-medium">{contract.customer.email}</p>
                  </div>
                )}
                
                {contract.customer.phone && (
                  <div>
                    <p className="text-sm text-gray-500">Phone</p>
                    <p className="font-medium">{contract.customer.phone}</p>
                  </div>
                )}
              </div>

              {/* Address */}
              {(contract.customer.address || contract.customer.city) && (
                <div className="pt-4 border-t">
                  <p className="text-sm text-gray-500 mb-2">Address</p>
                  <div className="text-gray-900">
                    {contract.customer.address && <p>{contract.customer.address}</p>}
                    <p>
                      {[contract.customer.city, contract.customer.state, contract.customer.postalCode].filter(Boolean).join(', ')}
                    </p>
                    {contract.customer.country && <p>{contract.customer.country}</p>}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Related Documents */}
          {(contract.quotation || contract.invoice) && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileText className="h-5 w-5 mr-2" />
                  Related Documents
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {contract.quotation && (
                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <p className="font-medium">Quotation: {contract.quotation.quotationNumber}</p>
                      <p className="text-sm text-gray-500">{contract.quotation.title}</p>
                      <p className="text-sm text-green-600 font-medium">
                        Total: ${contract.quotation.total.toLocaleString()}
                      </p>
                    </div>
                    <Button variant="outline" size="sm" asChild>
                      <Link href={`/dashboard/quotations/${contract.quotation.id}`}>
                        <Eye className="h-4 w-4 mr-2" />
                        View
                      </Link>
                    </Button>
                  </div>
                )}

                {contract.invoice && (
                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <p className="font-medium">Invoice: {contract.invoice.invoiceNumber}</p>
                      <p className="text-sm text-gray-500">Status: {contract.invoice.status}</p>
                      <p className="text-sm text-green-600 font-medium">
                        Total: ${contract.invoice.total.toLocaleString()}
                      </p>
                    </div>
                    <Button variant="outline" size="sm" asChild>
                      <Link href={`/dashboard/invoices/${contract.invoice.id}`}>
                        <Eye className="h-4 w-4 mr-2" />
                        View
                      </Link>
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Terms and Conditions */}
          {(contract.terms || contract.conditions) && (
            <Card>
              <CardHeader>
                <CardTitle>Terms & Conditions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {contract.terms && (
                  <div>
                    <p className="text-sm text-gray-500 mb-2">Terms & Conditions</p>
                    <p className="text-gray-900 whitespace-pre-wrap">{contract.terms}</p>
                  </div>
                )}
                
                {contract.conditions && (
                  <div className="pt-4 border-t">
                    <p className="text-sm text-gray-500 mb-2">Additional Conditions</p>
                    <p className="text-gray-900 whitespace-pre-wrap">{contract.conditions}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {contract.notes && (
            <Card>
              <CardHeader>
                <CardTitle>Internal Notes</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-900 whitespace-pre-wrap">{contract.notes}</p>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Contract Details */}
          <Card>
            <CardHeader>
              <CardTitle>Contract Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {contract.startDate && (
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-gray-400" />
                    <span className="text-sm">Start Date</span>
                  </div>
                  <span className="font-medium text-sm">
                    {new Date(contract.startDate).toLocaleDateString()}
                  </span>
                </div>
              )}
              
              {contract.endDate && (
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-gray-400" />
                    <span className="text-sm">End Date</span>
                  </div>
                  <span className={`font-medium text-sm ${isExpired || isExpiring ? 'text-red-600' : ''}`}>
                    {new Date(contract.endDate).toLocaleDateString()}
                  </span>
                </div>
              )}

              {contract.renewalDate && (
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Repeat className="h-4 w-4 text-gray-400" />
                    <span className="text-sm">Renewal Date</span>
                  </div>
                  <span className="font-medium text-sm">
                    {new Date(contract.renewalDate).toLocaleDateString()}
                  </span>
                </div>
              )}

              {contract.autoRenewal && (
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Repeat className="h-4 w-4 text-green-600" />
                    <span className="text-sm">Auto Renewal</span>
                  </div>
                  <span className="font-medium text-sm text-green-600">
                    {contract.renewalPeriod ? `Every ${contract.renewalPeriod} months` : 'Enabled'}
                  </span>
                </div>
              )}
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <User className="h-4 w-4 text-gray-400" />
                  <span className="text-sm">Created By</span>
                </div>
                <span className="font-medium text-sm">
                  {contract.createdBy.name || 'Unknown'}
                </span>
              </div>

              {contract.assignedTo && (
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <User className="h-4 w-4 text-gray-400" />
                    <span className="text-sm">Assigned To</span>
                  </div>
                  <span className="font-medium text-sm">
                    {contract.assignedTo.name || 'Unknown'}
                  </span>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Activity Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Activity Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm">Total Activities</span>
                <span className="font-medium">{contract._count.activities}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm">Signatures</span>
                <span className="font-medium">{contract._count.signatures}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm">Documents</span>
                <span className="font-medium">{contract._count.documents}</span>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button variant="outline" className="w-full justify-start">
                <Send className="h-4 w-4 mr-2" />
                Send for Signature
              </Button>
              
              <Button variant="outline" className="w-full justify-start">
                <Download className="h-4 w-4 mr-2" />
                Download PDF
              </Button>
              
              <Button variant="outline" className="w-full justify-start">
                <FileSignature className="h-4 w-4 mr-2" />
                Manage Signatures
              </Button>
              
              <Button variant="outline" className="w-full justify-start">
                <Copy className="h-4 w-4 mr-2" />
                Duplicate Contract
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Edit Form Modal */}
      <ContractForm
        isOpen={showEditForm}
        onClose={() => setShowEditForm(false)}
        onSuccess={fetchContract}
        contract={contract}
        mode="edit"
      />
    </div>
  )
}
