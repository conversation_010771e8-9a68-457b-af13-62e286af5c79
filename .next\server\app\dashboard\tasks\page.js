(()=>{var e={};e.id=4276,e.ids=[4276],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},49057:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>i.a,__next_app__:()=>h,originalPathname:()=>o,pages:()=>x,routeModule:()=>m,tree:()=>d});var t=a(50482),l=a(69108),r=a(62563),i=a.n(r),c=a(68300),n={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>c[e]);a.d(s,n);let d=["",{children:["dashboard",{children:["tasks",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,36096)),"C:\\proj\\nextjs-saas\\app\\dashboard\\tasks\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,96339)),"C:\\proj\\nextjs-saas\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,59504)),"C:\\proj\\nextjs-saas\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,69361,23)),"next/dist/client/components/not-found-error"]}],x=["C:\\proj\\nextjs-saas\\app\\dashboard\\tasks\\page.tsx"],o="/dashboard/tasks/page",h={require:a,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:l.x.APP_PAGE,page:"/dashboard/tasks/page",pathname:"/dashboard/tasks",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},85647:(e,s,a)=>{Promise.resolve().then(a.bind(a,49909))},49909:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>A});var t=a(95344),l=a(3729),r=a(61351),i=a(16212),c=a(69436),n=a(92549),d=a(17470),x=a(20886),o=a(10763),h=a(11880),m=a(50340),j=a(33733),u=a(2246),p=a(17910),g=a(66138),y=a(25545),N=a(55794),b=a(89895),f=a(44669);function v(){let[e,s]=(0,l.useState)(null),[a,n]=(0,l.useState)(!0),[x,o]=(0,l.useState)("30"),h=async()=>{try{n(!0);let e=await fetch(`/api/tasks/analytics?period=${x}`);if(!e.ok)throw Error("Failed to fetch analytics");let a=await e.json();s(a)}catch(e){f.toast.error("Failed to load task analytics"),console.error("Error fetching analytics:",e)}finally{n(!1)}};(0,l.useEffect)(()=>{h()},[x]);let v=e=>{switch(e){case"TODO":default:return"bg-gray-100 text-gray-800";case"IN_PROGRESS":return"bg-blue-100 text-blue-800";case"REVIEW":return"bg-yellow-100 text-yellow-800";case"DONE":return"bg-green-100 text-green-800";case"CANCELLED":return"bg-red-100 text-red-800"}},k=e=>{switch(e){case"LOW":default:return"bg-gray-100 text-gray-600";case"MEDIUM":return"bg-blue-100 text-blue-600";case"HIGH":return"bg-orange-100 text-orange-600";case"URGENT":return"bg-red-100 text-red-600";case"CRITICAL":return"bg-red-200 text-red-800"}};return a?t.jsx("div",{className:"flex items-center justify-center py-8",children:t.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):e?(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx("h3",{className:"text-lg font-semibold",children:"Task Analytics"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(d.Ph,{value:x,onValueChange:o,children:[t.jsx(d.i4,{className:"w-32",children:t.jsx(d.ki,{})}),(0,t.jsxs)(d.Bw,{children:[t.jsx(d.Ql,{value:"7",children:"Last 7 days"}),t.jsx(d.Ql,{value:"30",children:"Last 30 days"}),t.jsx(d.Ql,{value:"90",children:"Last 90 days"}),t.jsx(d.Ql,{value:"365",children:"Last year"})]})]}),(0,t.jsxs)(i.z,{variant:"outline",onClick:h,size:"sm",children:[t.jsx(j.Z,{className:"h-4 w-4 mr-2"}),"Refresh"]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[t.jsx(r.Zb,{children:t.jsx(r.aY,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[t.jsx("div",{className:"p-3 bg-blue-100 rounded-full",children:t.jsx(u.Z,{className:"h-6 w-6 text-blue-600"})}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm text-gray-500",children:"Total Tasks"}),t.jsx("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.totalTasks})]})]})})}),t.jsx(r.Zb,{children:t.jsx(r.aY,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[t.jsx("div",{className:"p-3 bg-green-100 rounded-full",children:t.jsx(p.Z,{className:"h-6 w-6 text-green-600"})}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm text-gray-500",children:"Completed"}),t.jsx("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.completedTasks}),(0,t.jsxs)("p",{className:"text-xs text-green-600",children:[e.summary.completionRate.toFixed(1),"% rate"]})]})]})})}),t.jsx(r.Zb,{children:t.jsx(r.aY,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[t.jsx("div",{className:"p-3 bg-red-100 rounded-full",children:t.jsx(g.Z,{className:"h-6 w-6 text-red-600"})}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm text-gray-500",children:"Overdue"}),t.jsx("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.overdueTasks})]})]})})}),t.jsx(r.Zb,{children:t.jsx(r.aY,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[t.jsx("div",{className:"p-3 bg-purple-100 rounded-full",children:t.jsx(y.Z,{className:"h-6 w-6 text-purple-600"})}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm text-gray-500",children:"Avg Completion"}),(0,t.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[e.summary.averageCompletionTime.toFixed(1),"d"]})]})]})})})]}),(0,t.jsxs)(r.Zb,{children:[t.jsx(r.Ol,{children:(0,t.jsxs)(r.ll,{className:"flex items-center",children:[t.jsx(N.Z,{className:"h-5 w-5 mr-2"}),"Today's Summary"]})}),t.jsx(r.aY,{children:(0,t.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("p",{className:"text-2xl font-bold text-blue-600",children:e.summary.tasksCreatedToday}),t.jsx("p",{className:"text-sm text-gray-500",children:"Created"})]}),(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("p",{className:"text-2xl font-bold text-green-600",children:e.summary.tasksCompletedToday}),t.jsx("p",{className:"text-sm text-gray-500",children:"Completed"})]}),(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("p",{className:"text-2xl font-bold text-orange-600",children:e.summary.tasksDueToday}),t.jsx("p",{className:"text-sm text-gray-500",children:"Due Today"})]})]})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)(r.Zb,{children:[t.jsx(r.Ol,{children:t.jsx(r.ll,{children:"Tasks by Status"})}),t.jsx(r.aY,{children:t.jsx("div",{className:"space-y-3",children:e.tasksByStatus.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx("div",{className:"flex items-center space-x-2",children:t.jsx(c.C,{className:v(e.status),children:e.status.replace("_"," ")})}),t.jsx("span",{className:"font-semibold",children:e.count})]},e.status))})})]}),(0,t.jsxs)(r.Zb,{children:[t.jsx(r.Ol,{children:t.jsx(r.ll,{children:"Tasks by Priority"})}),t.jsx(r.aY,{children:t.jsx("div",{className:"space-y-3",children:e.tasksByPriority.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx("div",{className:"flex items-center space-x-2",children:t.jsx(c.C,{className:k(e.priority),children:e.priority})}),t.jsx("span",{className:"font-semibold",children:e.count})]},e.priority))})})]}),(0,t.jsxs)(r.Zb,{children:[t.jsx(r.Ol,{children:t.jsx(r.ll,{children:"Tasks by Type"})}),t.jsx(r.aY,{children:t.jsx("div",{className:"space-y-3",children:e.tasksByType.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx("span",{className:"text-sm",children:e.type}),t.jsx("span",{className:"font-semibold",children:e.count})]},e.type))})})]}),(0,t.jsxs)(r.Zb,{children:[t.jsx(r.Ol,{children:(0,t.jsxs)(r.ll,{className:"flex items-center",children:[t.jsx(b.Z,{className:"h-5 w-5 mr-2"}),"Top Assignees"]})}),t.jsx(r.aY,{children:t.jsx("div",{className:"space-y-3",children:e.tasksByAssignee.slice(0,5).map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx("span",{className:"text-sm",children:e.assignee.name||e.assignee.email}),t.jsx("span",{className:"font-semibold",children:e.count})]},e.assignee.id))})})]})]}),(0,t.jsxs)(r.Zb,{children:[t.jsx(r.Ol,{children:t.jsx(r.ll,{children:"Upcoming Tasks (Next 7 Days)"})}),t.jsx(r.aY,{children:t.jsx("div",{className:"space-y-3",children:0===e.upcomingTasks.length?t.jsx("p",{className:"text-gray-500 text-center py-4",children:"No upcoming tasks"}):e.upcomingTasks.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,t.jsxs)("div",{children:[t.jsx("p",{className:"font-medium",children:e.title}),(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:["Due: ",new Date(e.dueDate).toLocaleDateString(),e.assignedTo&&` • ${e.assignedTo.name||e.assignedTo.email}`]})]}),t.jsx(c.C,{className:k(e.priority),children:e.priority})]},e.id))})})]}),(0,t.jsxs)(r.Zb,{children:[t.jsx(r.Ol,{children:t.jsx(r.ll,{children:"High Priority Tasks"})}),t.jsx(r.aY,{children:t.jsx("div",{className:"space-y-3",children:0===e.highPriorityTasks.length?t.jsx("p",{className:"text-gray-500 text-center py-4",children:"No high priority tasks"}):e.highPriorityTasks.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,t.jsxs)("div",{children:[t.jsx("p",{className:"font-medium",children:e.title}),(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:[e.dueDate&&`Due: ${new Date(e.dueDate).toLocaleDateString()}`,e.assignedTo&&` • ${e.assignedTo.name||e.assignedTo.email}`]})]}),t.jsx(c.C,{className:k(e.priority),children:e.priority})]},e.id))})})]})]}):(0,t.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[t.jsx(m.Z,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),t.jsx("p",{children:"Failed to load analytics data"})]})}var k=a(18822),w=a(62093),T=a(53148),C=a(75695),D=a(38271),E=a(46064),Z=a(51838),S=a(17418),P=a(28765),O=a(20783),I=a.n(O);function A(){let[e,s]=(0,l.useState)([]),[a,m]=(0,l.useState)(!0),[j,b]=(0,l.useState)(!1),[O,A]=(0,l.useState)(!1),[L,R]=(0,l.useState)(null),[_,M]=(0,l.useState)(""),[Y,Q]=(0,l.useState)(""),[F,G]=(0,l.useState)(""),[U,V]=(0,l.useState)(""),[$,q]=(0,l.useState)(1),[H,B]=(0,l.useState)(1),[W,z]=(0,l.useState)(0),K=async()=>{try{m(!0);let e=new URLSearchParams({page:$.toString(),limit:"20"});_&&"all"!==_&&e.append("status",_),Y&&"all"!==Y&&e.append("priority",Y),F&&e.append("assignedToId",F),U&&e.append("search",U);let a=await fetch(`/api/tasks?${e}`);if(!a.ok)throw Error("Failed to fetch tasks");let t=await a.json();s(t.tasks),B(t.pagination.pages),z(t.pagination.total)}catch(e){f.toast.error("Failed to load tasks"),console.error("Error fetching tasks:",e)}finally{m(!1)}};(0,l.useEffect)(()=>{K()},[$,_,Y,F,U]);let X=async e=>{if(confirm("Are you sure you want to delete this task?"))try{if(!(await fetch(`/api/tasks/${e}`,{method:"DELETE"})).ok)throw Error("Failed to delete task");f.toast.success("Task deleted successfully"),K()}catch(e){f.toast.error("Failed to delete task"),console.error("Error deleting task:",e)}},J=async(e,s)=>{try{if(!(await fetch(`/api/tasks/${e}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:s})})).ok)throw Error("Failed to update task status");f.toast.success("Task status updated"),K()}catch(e){f.toast.error("Failed to update task status"),console.error("Error updating task status:",e)}},ee=e=>{let s={TODO:{label:"To Do",className:"bg-gray-100 text-gray-800"},IN_PROGRESS:{label:"In Progress",className:"bg-blue-100 text-blue-800"},REVIEW:{label:"Review",className:"bg-yellow-100 text-yellow-800"},DONE:{label:"Done",className:"bg-green-100 text-green-800"},CANCELLED:{label:"Cancelled",className:"bg-red-100 text-red-800"}},a=s[e]||s.TODO;return t.jsx(c.C,{className:a.className,children:a.label})},es=e=>{let s={LOW:{label:"Low",className:"bg-gray-100 text-gray-600"},MEDIUM:{label:"Medium",className:"bg-blue-100 text-blue-600"},HIGH:{label:"High",className:"bg-orange-100 text-orange-600"},URGENT:{label:"Urgent",className:"bg-red-100 text-red-600"},CRITICAL:{label:"Critical",className:"bg-red-200 text-red-800"}},a=s[e]||s.MEDIUM;return t.jsx(c.C,{className:a.className,children:a.label})};return(0,t.jsxs)("div",{className:"container mx-auto py-6 space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[t.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Task Management"}),t.jsx("p",{className:"text-gray-600",children:"Manage and track your team's tasks and projects"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(i.z,{variant:"outline",onClick:()=>A(!O),children:[t.jsx(E.Z,{className:"h-4 w-4 mr-2"}),"Analytics"]}),(0,t.jsxs)(i.z,{onClick:()=>b(!0),children:[t.jsx(Z.Z,{className:"h-4 w-4 mr-2"}),"New Task"]})]})]}),O&&t.jsx(v,{}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[t.jsx(r.Zb,{children:t.jsx(r.aY,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[t.jsx("div",{className:"p-3 bg-blue-100 rounded-full",children:t.jsx(u.Z,{className:"h-6 w-6 text-blue-600"})}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm text-gray-500",children:"Total Tasks"}),t.jsx("p",{className:"text-2xl font-bold text-gray-900",children:W})]})]})})}),t.jsx(r.Zb,{children:t.jsx(r.aY,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[t.jsx("div",{className:"p-3 bg-green-100 rounded-full",children:t.jsx(p.Z,{className:"h-6 w-6 text-green-600"})}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm text-gray-500",children:"Completed"}),t.jsx("p",{className:"text-2xl font-bold text-gray-900",children:e.filter(e=>"DONE"===e.status).length})]})]})})}),t.jsx(r.Zb,{children:t.jsx(r.aY,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[t.jsx("div",{className:"p-3 bg-yellow-100 rounded-full",children:t.jsx(y.Z,{className:"h-6 w-6 text-yellow-600"})}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm text-gray-500",children:"In Progress"}),t.jsx("p",{className:"text-2xl font-bold text-gray-900",children:e.filter(e=>"IN_PROGRESS"===e.status).length})]})]})})}),t.jsx(r.Zb,{children:t.jsx(r.aY,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[t.jsx("div",{className:"p-3 bg-red-100 rounded-full",children:t.jsx(g.Z,{className:"h-6 w-6 text-red-600"})}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm text-gray-500",children:"Overdue"}),t.jsx("p",{className:"text-2xl font-bold text-gray-900",children:e.filter(e=>e.dueDate&&new Date(e.dueDate)<new Date&&"DONE"!==e.status).length})]})]})})})]}),(0,t.jsxs)(r.Zb,{children:[t.jsx(r.Ol,{children:(0,t.jsxs)(r.ll,{className:"flex items-center",children:[t.jsx(S.Z,{className:"h-5 w-5 mr-2"}),"Filters"]})}),t.jsx(r.aY,{children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,t.jsxs)("div",{className:"relative",children:[t.jsx(P.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),t.jsx(n.I,{placeholder:"Search tasks...",value:U,onChange:e=>V(e.target.value),className:"pl-10"})]}),(0,t.jsxs)(d.Ph,{value:_,onValueChange:M,children:[t.jsx(d.i4,{children:t.jsx(d.ki,{placeholder:"All Statuses"})}),(0,t.jsxs)(d.Bw,{children:[t.jsx(d.Ql,{value:"all",children:"All Statuses"}),t.jsx(d.Ql,{value:"TODO",children:"To Do"}),t.jsx(d.Ql,{value:"IN_PROGRESS",children:"In Progress"}),t.jsx(d.Ql,{value:"REVIEW",children:"Review"}),t.jsx(d.Ql,{value:"DONE",children:"Done"}),t.jsx(d.Ql,{value:"CANCELLED",children:"Cancelled"})]})]}),(0,t.jsxs)(d.Ph,{value:Y,onValueChange:Q,children:[t.jsx(d.i4,{children:t.jsx(d.ki,{placeholder:"All Priorities"})}),(0,t.jsxs)(d.Bw,{children:[t.jsx(d.Ql,{value:"all",children:"All Priorities"}),t.jsx(d.Ql,{value:"LOW",children:"Low"}),t.jsx(d.Ql,{value:"MEDIUM",children:"Medium"}),t.jsx(d.Ql,{value:"HIGH",children:"High"}),t.jsx(d.Ql,{value:"URGENT",children:"Urgent"}),t.jsx(d.Ql,{value:"CRITICAL",children:"Critical"})]})]}),t.jsx(i.z,{variant:"outline",onClick:()=>{M("all"),Q("all"),G(""),V("")},children:"Clear Filters"})]})})]}),(0,t.jsxs)(r.Zb,{children:[t.jsx(r.Ol,{children:t.jsx(r.ll,{children:"Tasks"})}),t.jsx(r.aY,{children:a?t.jsx("div",{className:"flex items-center justify-center py-8",children:t.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):t.jsx(o.w,{columns:[{accessorKey:"title",header:"Task",cell:({row:e})=>{let s=e.original;return(0,t.jsxs)("div",{className:"space-y-1",children:[t.jsx("div",{className:"font-medium",children:s.title}),s.description&&t.jsx("div",{className:"text-sm text-gray-500 truncate max-w-xs",children:s.description}),s.tags.length>0&&(0,t.jsxs)("div",{className:"flex flex-wrap gap-1",children:[s.tags.slice(0,2).map((e,s)=>t.jsx(c.C,{variant:"outline",className:"text-xs",children:e},s)),s.tags.length>2&&(0,t.jsxs)(c.C,{variant:"outline",className:"text-xs",children:["+",s.tags.length-2]})]})]})}},{accessorKey:"status",header:"Status",cell:({row:e})=>ee(e.getValue("status"))},{accessorKey:"priority",header:"Priority",cell:({row:e})=>es(e.getValue("priority"))},{accessorKey:"assignedTo",header:"Assigned To",cell:({row:e})=>{let s=e.getValue("assignedTo");return s?(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center",children:t.jsx(k.Z,{className:"h-3 w-3 text-blue-600"})}),t.jsx("span",{className:"text-sm",children:s.name||s.email})]}):t.jsx("span",{className:"text-gray-400 text-sm",children:"Unassigned"})}},{accessorKey:"dueDate",header:"Due Date",cell:({row:e})=>{let s=e.getValue("dueDate");if(!s)return t.jsx("span",{className:"text-gray-400",children:"No due date"});let a=new Date(s),l=a<new Date&&"DONE"!==e.original.status;return(0,t.jsxs)("div",{className:`flex items-center space-x-1 ${l?"text-red-600":""}`,children:[t.jsx(N.Z,{className:"h-3 w-3"}),t.jsx("span",{className:"text-sm",children:a.toLocaleDateString()}),l&&t.jsx(g.Z,{className:"h-3 w-3"})]})}},{accessorKey:"type",header:"Type",cell:({row:e})=>{let s=e.getValue("type");return t.jsx(c.C,{variant:"outline",children:s})}},{id:"actions",header:"Actions",cell:({row:e})=>{let s=e.original;return(0,t.jsxs)(x.h_,{children:[t.jsx(x.$F,{asChild:!0,children:t.jsx(i.z,{variant:"ghost",className:"h-8 w-8 p-0",children:t.jsx(w.Z,{className:"h-4 w-4"})})}),(0,t.jsxs)(x.AW,{align:"end",children:[t.jsx(x.Xi,{asChild:!0,children:(0,t.jsxs)(I(),{href:`/dashboard/tasks/${s.id}`,children:[t.jsx(T.Z,{className:"h-4 w-4 mr-2"}),"View Details"]})}),(0,t.jsxs)(x.Xi,{onClick:()=>R(s),children:[t.jsx(C.Z,{className:"h-4 w-4 mr-2"}),"Edit Task"]}),"DONE"!==s.status&&(0,t.jsxs)(x.Xi,{onClick:()=>J(s.id,"DONE"),children:[t.jsx(u.Z,{className:"h-4 w-4 mr-2"}),"Mark Complete"]}),(0,t.jsxs)(x.Xi,{onClick:()=>X(s.id),className:"text-red-600",children:[t.jsx(D.Z,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]})}}],data:e,searchPlaceholder:"Search tasks..."})})]}),(j||L)&&t.jsx(h.t,{task:L,onClose:()=>{b(!1),R(null)},onSuccess:()=>{b(!1),R(null),K()}})]})}},55794:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},25545:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},17418:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},46064:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},36096:(e,s,a)=>{"use strict";a.r(s),a.d(s,{$$typeof:()=>r,__esModule:()=>l,default:()=>i});let t=(0,a(86843).createProxy)(String.raw`C:\proj\nextjs-saas\app\dashboard\tasks\page.tsx`),{__esModule:l,$$typeof:r}=t,i=t.default}};var s=require("../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[1638,7948,6671,4626,7792,2506,8830,3117,2125,5045,5232,5904],()=>a(49057));module.exports=t})();