import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import Strip<PERSON> from 'stripe'
import { stripe, verifyStripeWebhook } from '@/lib/stripe'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const body = await request.text()
    const signature = headers().get('stripe-signature')

    if (!signature) {
      console.error('Missing Stripe signature')
      return NextResponse.json(
        { error: 'Missing signature' },
        { status: 400 }
      )
    }

    if (!process.env.STRIPE_WEBHOOK_SECRET) {
      console.error('Missing STRIPE_WEBHOOK_SECRET')
      return NextResponse.json(
        { error: 'Webhook secret not configured' },
        { status: 500 }
      )
    }

    let event: Stripe.Event

    try {
      event = verifyStripeWebhook(body, signature, process.env.STRIPE_WEBHOOK_SECRET)
    } catch (error) {
      console.error('Webhook signature verification failed:', error)
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 400 }
      )
    }

    console.log('Processing Stripe webhook:', event.type)

    switch (event.type) {
      case 'customer.subscription.created':
        await handleSubscriptionCreated(event.data.object as Stripe.Subscription)
        break

      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object as Stripe.Subscription)
        break

      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object as Stripe.Subscription)
        break

      case 'invoice.payment_succeeded':
        await handlePaymentSucceeded(event.data.object as Stripe.Invoice)
        break

      case 'invoice.payment_failed':
        await handlePaymentFailed(event.data.object as Stripe.Invoice)
        break

      case 'customer.subscription.trial_will_end':
        await handleTrialWillEnd(event.data.object as Stripe.Subscription)
        break

      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return NextResponse.json({ received: true })

  } catch (error) {
    console.error('Webhook error:', error)
    return NextResponse.json(
      { error: 'Webhook handler failed' },
      { status: 500 }
    )
  }
}

async function handleSubscriptionCreated(subscription: Stripe.Subscription) {
  try {
    const customer = await stripe.customers.retrieve(subscription.customer as string)
    
    if (customer.deleted) {
      console.error('Customer was deleted')
      return
    }

    const companyId = customer.metadata?.companyId
    if (!companyId) {
      console.error('No companyId found in customer metadata')
      return
    }

    // Find the pricing plan based on the price ID
    const priceId = subscription.items.data[0]?.price.id
    const pricingPlan = await prisma.pricingPlan.findFirst({
      where: {
        OR: [
          { stripePriceId: priceId },
          { stripeYearlyPriceId: priceId }
        ]
      }
    })

    if (!pricingPlan) {
      console.error('No pricing plan found for price ID:', priceId)
      return
    }

    const billingCycle = subscription.items.data[0]?.price.recurring?.interval === 'year' ? 'YEARLY' : 'MONTHLY'

    // Create or update subscription in database
    await prisma.subscription.upsert({
      where: {
        stripeSubscriptionId: subscription.id
      },
      update: {
        status: mapStripeStatus(subscription.status),
        currentPeriodStart: new Date(subscription.current_period_start * 1000),
        currentPeriodEnd: new Date(subscription.current_period_end * 1000),
        trialEnd: subscription.trial_end ? new Date(subscription.trial_end * 1000) : null,
        cancelAtPeriodEnd: subscription.cancel_at_period_end,
        billingCycle
      },
      create: {
        companyId,
        pricingPlanId: pricingPlan.id,
        stripeSubscriptionId: subscription.id,
        stripeCustomerId: subscription.customer as string,
        status: mapStripeStatus(subscription.status),
        billingCycle,
        currentPeriodStart: new Date(subscription.current_period_start * 1000),
        currentPeriodEnd: new Date(subscription.current_period_end * 1000),
        trialEnd: subscription.trial_end ? new Date(subscription.trial_end * 1000) : null,
        cancelAtPeriodEnd: subscription.cancel_at_period_end
      }
    })

    console.log('Subscription created/updated:', subscription.id)

  } catch (error) {
    console.error('Error handling subscription created:', error)
  }
}

async function handleSubscriptionUpdated(subscription: Stripe.Subscription) {
  try {
    const priceId = subscription.items.data[0]?.price.id
    const pricingPlan = await prisma.pricingPlan.findFirst({
      where: {
        OR: [
          { stripePriceId: priceId },
          { stripeYearlyPriceId: priceId }
        ]
      }
    })

    const billingCycle = subscription.items.data[0]?.price.recurring?.interval === 'year' ? 'YEARLY' : 'MONTHLY'

    await prisma.subscription.update({
      where: {
        stripeSubscriptionId: subscription.id
      },
      data: {
        pricingPlanId: pricingPlan?.id,
        status: mapStripeStatus(subscription.status),
        billingCycle,
        currentPeriodStart: new Date(subscription.current_period_start * 1000),
        currentPeriodEnd: new Date(subscription.current_period_end * 1000),
        trialEnd: subscription.trial_end ? new Date(subscription.trial_end * 1000) : null,
        cancelAtPeriodEnd: subscription.cancel_at_period_end
      }
    })

    console.log('Subscription updated:', subscription.id)

  } catch (error) {
    console.error('Error handling subscription updated:', error)
  }
}

async function handleSubscriptionDeleted(subscription: Stripe.Subscription) {
  try {
    await prisma.subscription.update({
      where: {
        stripeSubscriptionId: subscription.id
      },
      data: {
        status: 'CANCELED',
        canceledAt: new Date()
      }
    })

    console.log('Subscription canceled:', subscription.id)

  } catch (error) {
    console.error('Error handling subscription deleted:', error)
  }
}

async function handlePaymentSucceeded(invoice: Stripe.Invoice) {
  try {
    if (invoice.subscription) {
      await prisma.subscription.update({
        where: {
          stripeSubscriptionId: invoice.subscription as string
        },
        data: {
          status: 'ACTIVE'
        }
      })
    }

    console.log('Payment succeeded for invoice:', invoice.id)

  } catch (error) {
    console.error('Error handling payment succeeded:', error)
  }
}

async function handlePaymentFailed(invoice: Stripe.Invoice) {
  try {
    if (invoice.subscription) {
      await prisma.subscription.update({
        where: {
          stripeSubscriptionId: invoice.subscription as string
        },
        data: {
          status: 'PAST_DUE'
        }
      })
    }

    console.log('Payment failed for invoice:', invoice.id)

  } catch (error) {
    console.error('Error handling payment failed:', error)
  }
}

async function handleTrialWillEnd(subscription: Stripe.Subscription) {
  try {
    // You could send notification emails here
    console.log('Trial will end for subscription:', subscription.id)

  } catch (error) {
    console.error('Error handling trial will end:', error)
  }
}

function mapStripeStatus(stripeStatus: Stripe.Subscription.Status): string {
  switch (stripeStatus) {
    case 'active':
      return 'ACTIVE'
    case 'trialing':
      return 'TRIALING'
    case 'past_due':
      return 'PAST_DUE'
    case 'canceled':
      return 'CANCELED'
    case 'unpaid':
      return 'PAST_DUE'
    case 'incomplete':
      return 'INCOMPLETE'
    case 'incomplete_expired':
      return 'CANCELED'
    case 'paused':
      return 'PAUSED'
    default:
      return 'UNKNOWN'
  }
}
