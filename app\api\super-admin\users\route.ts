import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'

// GET /api/super-admin/users - Get all users with admin details
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || session?.user?.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Super admin access required' }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const search = searchParams.get('search') || ''
    const role = searchParams.get('role') || ''
    const status = searchParams.get('status') || ''
    const companyId = searchParams.get('companyId') || ''

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {}
    
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
        { firstName: { contains: search, mode: 'insensitive' } },
        { lastName: { contains: search, mode: 'insensitive' } }
      ]
    }

    if (role && role !== 'all') {
      where.role = role
    }

    if (status && status !== 'all') {
      where.status = status
    }

    if (companyId) {
      where.companyId = companyId
    }

    // Get users with pagination
    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        skip,
        take: limit,
        include: {
          company: {
            select: {
              id: true,
              name: true,
              status: true,
              industry: true
            }
          },
          ownedCompany: {
            select: {
              id: true,
              name: true,
              status: true
            }
          },
          _count: {
            select: {
              createdLeads: true,
              createdCustomers: true,
              createdQuotations: true,
              createdInvoices: true,
              createdContracts: true,
              activities: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      }),
      prisma.user.count({ where })
    ])

    // Get user statistics
    const stats = await Promise.all([
      prisma.user.count({ where: { status: 'ACTIVE' } }),
      prisma.user.count({ where: { status: 'INACTIVE' } }),
      prisma.user.count({ where: { status: 'SUSPENDED' } }),
      prisma.user.count({ where: { status: 'PENDING' } }),
      prisma.user.groupBy({
        by: ['role'],
        _count: { id: true }
      }),
      prisma.user.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
          }
        }
      })
    ])

    const [activeUsers, inactiveUsers, suspendedUsers, pendingUsers, usersByRole, newUsers] = stats

    return NextResponse.json({
      users: users.map(user => ({
        id: user.id,
        email: user.email,
        name: user.name,
        firstName: user.firstName,
        lastName: user.lastName,
        avatar: user.avatar,
        phone: user.phone,
        role: user.role,
        status: user.status,
        title: user.title,
        department: user.department,
        timezone: user.timezone,
        language: user.language,
        emailVerified: user.emailVerified,
        twoFactorEnabled: user.twoFactorEnabled,
        lastLoginAt: user.lastLoginAt,
        loginCount: user.loginCount,
        lastActiveAt: user.lastActiveAt,
        company: user.company,
        ownedCompany: user.ownedCompany,
        stats: {
          leadsCreated: user._count.createdLeads,
          customersCreated: user._count.createdCustomers,
          quotationsCreated: user._count.createdQuotations,
          invoicesCreated: user._count.createdInvoices,
          contractsCreated: user._count.createdContracts,
          activitiesCount: user._count.activities
        },
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      })),
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      stats: {
        total,
        active: activeUsers,
        inactive: inactiveUsers,
        suspended: suspendedUsers,
        pending: pendingUsers,
        new: newUsers,
        byRole: usersByRole.map(item => ({
          role: item.role,
          count: item._count.id
        }))
      }
    })

  } catch (error) {
    console.error('Error fetching users:', error)
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    )
  }
}

// POST /api/super-admin/users - Create new user (admin only)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || session?.user?.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Super admin access required' }, { status: 403 })
    }

    const body = await request.json()
    const {
      email,
      password,
      name,
      firstName,
      lastName,
      phone,
      role,
      status,
      title,
      department,
      companyId,
      sendWelcomeEmail = true
    } = body

    // Validate required fields
    if (!email || !password || !name) {
      return NextResponse.json(
        { error: 'Email, password, and name are required' },
        { status: 400 }
      )
    }

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email }
    })

    if (existingUser) {
      return NextResponse.json(
        { error: 'User with this email already exists' },
        { status: 400 }
      )
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12)

    // Create user
    const user = await prisma.user.create({
      data: {
        email,
        password: hashedPassword,
        name,
        firstName,
        lastName,
        phone,
        role: role || 'USER',
        status: status || 'ACTIVE',
        title,
        department,
        companyId,
        emailVerified: new Date() // Auto-verify for admin-created users
      },
      include: {
        company: {
          select: {
            id: true,
            name: true,
            status: true
          }
        }
      }
    })

    // Log the action
    await prisma.auditLog.create({
      data: {
        action: 'USER_CREATED',
        entityType: 'User',
        entityId: user.id,
        userId: session.user.id,
        userEmail: session.user.email,
        userRole: session.user.role,
        newValues: {
          email: user.email,
          name: user.name,
          role: user.role,
          status: user.status
        },
        metadata: {
          createdByAdmin: true,
          adminId: session.user.id
        }
      }
    })

    // Remove password from response
    const { password: _, ...userResponse } = user

    return NextResponse.json({
      user: userResponse,
      message: 'User created successfully'
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating user:', error)
    return NextResponse.json(
      { error: 'Failed to create user' },
      { status: 500 }
    )
  }
}
