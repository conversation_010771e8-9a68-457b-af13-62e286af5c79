(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3185],{89314:function(e,t,n){Promise.resolve().then(n.bind(n,82494)),Promise.resolve().then(n.t.bind(n,43445,23)),Promise.resolve().then(n.t.bind(n,52445,23)),Promise.resolve().then(n.bind(n,5925))},82494:function(e,t,n){"use strict";n.r(t),n.d(t,{Providers:function(){return d}});var r=n(57437),o=n(82749),c=n(6435),l=n(99715),a=n(38038),s=n(2265),i=n(218);function d(e){let{children:t}=e,[n]=(0,s.useState)(()=>new l.S({defaultOptions:{queries:{staleTime:6e4,retry:1}}}));return(0,r.jsx)(o<PERSON>,{children:(0,r.jsx)(a.aH,{client:n,children:(0,r.jsx)(i.lY,{children:(0,r.jsx)(c.f,{attribute:"class",defaultTheme:"light",enableSystem:!0,disableTransitionOnChange:!0,children:t})})})})}},218:function(e,t,n){"use strict";n.d(t,{TC:function(){return s},lY:function(){return a}});var r=n(57437),o=n(2265);let c={appName:"SaaS Platform",logoUrl:"",faviconUrl:"",primaryColor:"#3b82f6",secondaryColor:"#64748b",accentColor:"#10b981",backgroundColor:"#ffffff",textColor:"#1f2937",theme:"light",fontFamily:"Inter, sans-serif",customCss:""},l=(0,o.createContext)(void 0);function a(e){let{children:t}=e,[n,a]=(0,o.useState)(c),[s,i]=(0,o.useState)(!0);(0,o.useEffect)(()=>{d()},[]);let d=async()=>{try{let e=await fetch("/api/global-config/branding"),t=await e.json();t.success&&t.branding?(a({...c,...t.branding}),u({...c,...t.branding})):u(c)}catch(e){console.error("Error fetching branding config:",e),u(c)}finally{i(!1)}},u=e=>{let t=document.documentElement;if(t.style.setProperty("--primary-color",e.primaryColor),t.style.setProperty("--secondary-color",e.secondaryColor),t.style.setProperty("--accent-color",e.accentColor),t.style.setProperty("--background-color",e.backgroundColor),t.style.setProperty("--text-color",e.textColor),t.style.setProperty("--font-family",e.fontFamily),document.body.className=document.body.className.replace(/theme-\w+/g,""),document.body.classList.add("theme-".concat(e.theme)),document.title=e.appName,e.faviconUrl){let t=document.querySelector('link[rel="icon"]');t||((t=document.createElement("link")).rel="icon",document.head.appendChild(t)),t.href=e.faviconUrl}let n=document.getElementById("custom-branding-css");e.customCss?(n||((n=document.createElement("style")).id="custom-branding-css",document.head.appendChild(n)),n.textContent=e.customCss):n&&n.remove();let r=document.querySelector('meta[name="theme-color"]');r||((r=document.createElement("meta")).name="theme-color",document.head.appendChild(r)),r.content=e.primaryColor};return(0,r.jsx)(l.Provider,{value:{branding:n,updateBranding:e=>{let t={...n,...e};a(t),u(t)},loading:s},children:t})}function s(){let e=(0,o.useContext)(l);if(void 0===e)throw Error("useBranding must be used within a BrandingProvider");return e}},52445:function(){}},function(e){e.O(0,[2749,9545,2971,4938,1744],function(){return e(e.s=89314)}),_N_E=e.O()}]);