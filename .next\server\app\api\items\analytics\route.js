"use strict";(()=>{var e={};e.id=5813,e.ids=[5813],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},6574:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>_,originalPathname:()=>v,patchFetch:()=>k,requestAsyncStorage:()=>y,routeModule:()=>d,serverHooks:()=>g,staticGenerationAsyncStorage:()=>p,staticGenerationBailout:()=>I});var r={};a.r(r),a.d(r,{GET:()=>m});var o=a(95419),n=a(69108),i=a(99678),c=a(78070),s=a(81355),u=a(3205),l=a(9108);async function m(e){try{let t=await (0,s.getServerSession)(u.L);if(!t?.user?.id||!t?.user?.companyId)return c.Z.json({error:"Unauthorized"},{status:401});let{searchParams:a}=new URL(e.url),r=a.get("period")||"30",o=new Date;o.setDate(o.getDate()-parseInt(r));let[n,i,m,d,y,p,g,_,I,v,k,w]=await Promise.all([l._.item.count({where:{companyId:t.user.companyId,createdAt:{gte:o}}}),l._.item.groupBy({by:["category"],where:{companyId:t.user.companyId,createdAt:{gte:o}},_count:{id:!0},_avg:{unitPrice:!0},_sum:{stockQuantity:!0}}),l._.item.groupBy({by:["active"],where:{companyId:t.user.companyId,createdAt:{gte:o}},_count:{id:!0},_avg:{unitPrice:!0}}),Promise.all([l._.item.count({where:{companyId:t.user.companyId,trackInventory:!0}}),l._.item.aggregate({where:{companyId:t.user.companyId,trackInventory:!0,stockQuantity:{not:null}},_sum:{stockQuantity:!0}}),l._.item.aggregate({where:{companyId:t.user.companyId,trackInventory:!0,stockQuantity:{not:null}},_avg:{stockQuantity:!0}}),l._.item.count({where:{companyId:t.user.companyId,trackInventory:!0,AND:[{stockQuantity:{not:null}},{lowStockAlert:{not:null}},{stockQuantity:{lte:l._.item.fields.lowStockAlert}}]}})]),Promise.all([l._.item.aggregate({where:{companyId:t.user.companyId,active:!0},_avg:{unitPrice:!0}}),l._.item.aggregate({where:{companyId:t.user.companyId,active:!0},_min:{unitPrice:!0},_max:{unitPrice:!0}}),l._.item.count({where:{companyId:t.user.companyId,costPrice:{not:null}}})]),l._.item.findMany({where:{companyId:t.user.companyId,createdAt:{gte:o}},select:{id:!0,name:!0,unitPrice:!0,category:!0,stockQuantity:!0},take:10,orderBy:{createdAt:"desc"}}),l._.item.findMany({where:{companyId:t.user.companyId,trackInventory:!0,AND:[{stockQuantity:{not:null}},{lowStockAlert:{not:null}},{stockQuantity:{lte:l._.item.fields.lowStockAlert}}]},select:{id:!0,name:!0,sku:!0,category:!0,stockQuantity:!0,lowStockAlert:!0,unitPrice:!0},orderBy:[{stockQuantity:"asc"}],take:10}),l._.item.findMany({where:{companyId:t.user.companyId,createdAt:{gte:new Date(Date.now()-6048e5)}},select:{id:!0,name:!0,sku:!0,category:!0,unitPrice:!0,stockQuantity:!0,trackInventory:!0,active:!0,createdAt:!0},orderBy:{createdAt:"desc"},take:10}),l._.item.groupBy({by:["category"],where:{companyId:t.user.companyId,createdAt:{gte:o}},_count:{id:!0},_avg:{unitPrice:!0},orderBy:{_count:{id:"desc"}}}),Promise.resolve([{total_stock_value:0,total_cost_value:0,tracked_items_count:await l._.item.count({where:{companyId:t.user.companyId,trackInventory:!0}}),avg_item_value:0}]),Promise.resolve([{used_items_count:await l._.item.count({where:{companyId:t.user.companyId}}),total_usage_count:0,avg_quantity_per_use:1,total_usage_value:0}]),l._.item.findMany({where:{companyId:t.user.companyId,costPrice:{not:null},active:!0},select:{id:!0,name:!0,category:!0,unitPrice:!0,costPrice:!0},take:10})]),[P,h,b,f]=d,[N,x,A]=y,Q=v[0],S=k[0];return c.Z.json({summary:{totalItems:n,activeItems:m.find(e=>e.active)?._count.id||0,inactiveItems:m.find(e=>!e.active)?._count.id||0,trackedItems:P,lowStockCount:f,totalStockQuantity:h._sum.stockQuantity||0,avgStockPerItem:Math.round(b._avg.stockQuantity||0),avgUnitPrice:Number(N._avg.unitPrice||0),minPrice:Number(x._min.unitPrice||0),maxPrice:Number(x._max.unitPrice||0),itemsWithCostPrice:A,totalStockValue:Number(Q?.total_stock_value||0),totalCostValue:Number(Q?.total_cost_value||0),usedItemsCount:Number(S?.used_items_count||0),totalUsageCount:Number(S?.total_usage_count||0),totalUsageValue:Number(S?.total_usage_value||0)},itemsByCategory:i.map(e=>({category:e.category||"Uncategorized",count:e._count.id,avgPrice:Number(e._avg.unitPrice||0),totalStock:e._sum.stockQuantity||0})),itemsByStatus:m.map(e=>({status:e.active?"Active":"Inactive",count:e._count.id,avgPrice:Number(e._avg.unitPrice||0)})),topSellingItems:p.map(e=>({id:e.id,name:e.name,category:e.category,unitPrice:Number(e.unitPrice),stockQuantity:e.stockQuantity,usageCount:Number(e.usage_count),totalQuantitySold:Number(e.total_quantity_sold),totalRevenue:Number(e.total_revenue)})),lowStockItems:g.map(e=>({id:e.id,name:e.name,sku:e.sku,category:e.category,stockQuantity:e.stockQuantity,lowStockAlert:e.lowStockAlert,unitPrice:Number(e.unitPrice),stockValue:Number(e.unitPrice)*(e.stockQuantity||0)})),recentItems:_.map(e=>({id:e.id,name:e.name,sku:e.sku,category:e.category,unitPrice:Number(e.unitPrice),stockQuantity:e.stockQuantity,trackInventory:e.trackInventory,active:e.active,createdAt:e.createdAt})),categoryPerformance:I.map(e=>({category:e.category||"Uncategorized",itemCount:Number(e.item_count),avgPrice:Number(e.avg_price),totalStock:Number(e.total_stock),trackedItems:Number(e.tracked_items),activeItems:Number(e.active_items),usageCount:Number(e.usage_count)})),profitabilityAnalysis:w.map(e=>({id:e.id,name:e.name,category:e.category,unitPrice:Number(e.unitPrice),costPrice:Number(e.costPrice),profitMarginPercent:e.profit_margin_percent?Number(e.profit_margin_percent):null,profitPerUnit:e.profit_per_unit?Number(e.profit_per_unit):null,usageCount:Number(e.usage_count)})),period:parseInt(r)})}catch(e){return console.error("Error fetching item analytics:",e),c.Z.json({error:"Failed to fetch item analytics"},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/items/analytics/route",pathname:"/api/items/analytics",filename:"route",bundlePath:"app/api/items/analytics/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\items\\analytics\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:y,staticGenerationAsyncStorage:p,serverHooks:g,headerHooks:_,staticGenerationBailout:I}=d,v="/api/items/analytics/route";function k(){return(0,i.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:p})}},3205:(e,t,a)=>{a.d(t,{L:()=>u});var r=a(86485),o=a(10375),n=a(50694),i=a(6521),c=a.n(i),s=a(9108);let u={providers:[(0,r.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await s._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),a=t?.companyId;if(!a&&t){let e=await s._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(a=e?.id)&&await s._.user.update({where:{id:t.id},data:{companyId:a}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await c().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await s._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:a}}catch(e){return console.error("Authentication error:",e),null}}}),(0,o.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,n.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,a)=>{a.d(t,{_:()=>o});let r=require("@prisma/client"),o=globalThis.prisma??new r.PrismaClient}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[1638,6206,6521,2455,4520],()=>a(6574));module.exports=r})();