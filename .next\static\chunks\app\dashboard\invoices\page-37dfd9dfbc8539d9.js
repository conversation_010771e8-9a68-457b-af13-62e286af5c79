(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4490],{72894:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},92457:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},92919:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("Calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]])},28203:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},13008:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6141:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},56224:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},71738:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},41298:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},35817:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},76637:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]])},76020:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]])},85790:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},67972:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},25750:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},75323:function(e,s,t){Promise.resolve().then(t.bind(t,21565))},21565:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return q}});var a=t(57437),l=t(2265),n=t(82749),r=t(27815),c=t(85754),i=t(31478),d=t(25462),o=t(76426),x=t(45509),m=t(92457),h=t(64280),u=t(76637),j=t(41298),y=t(13008),p=t(72894),f=t(66654),v=t(6141),N=t(85790),g=t(71738),b=t(25750),w=t(28203),k=t(5925);function Z(){let[e,s]=(0,l.useState)(null),[t,n]=(0,l.useState)(!0),[d,o]=(0,l.useState)("30"),Z=async()=>{try{n(!0);let e=await fetch("/api/invoices/analytics?period=".concat(d));if(!e.ok)throw Error("Failed to fetch analytics");let t=await e.json();s(t)}catch(e){k.toast.error("Failed to load invoice analytics"),console.error("Error fetching analytics:",e)}finally{n(!1)}};(0,l.useEffect)(()=>{Z()},[d]);let C=e=>{switch(e){case"DRAFT":default:return"bg-gray-100 text-gray-800";case"SENT":return"bg-blue-100 text-blue-800";case"PAID":return"bg-green-100 text-green-800";case"OVERDUE":return"bg-red-100 text-red-800";case"CANCELLED":return"bg-orange-100 text-orange-800"}},D=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e),S=e=>{switch(e){case"0-30 days":return"text-green-600";case"31-60 days":return"text-yellow-600";case"61-90 days":return"text-orange-600";case"90+ days":return"text-red-600";default:return"text-gray-600"}};return t?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):e?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Invoice Analytics"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(x.Ph,{value:d,onValueChange:o,children:[(0,a.jsx)(x.i4,{className:"w-32",children:(0,a.jsx)(x.ki,{})}),(0,a.jsxs)(x.Bw,{children:[(0,a.jsx)(x.Ql,{value:"7",children:"Last 7 days"}),(0,a.jsx)(x.Ql,{value:"30",children:"Last 30 days"}),(0,a.jsx)(x.Ql,{value:"90",children:"Last 90 days"}),(0,a.jsx)(x.Ql,{value:"365",children:"Last year"})]})]}),(0,a.jsxs)(c.z,{variant:"outline",onClick:Z,size:"sm",children:[(0,a.jsx)(h.Z,{className:"h-4 w-4 mr-2"}),"Refresh"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsx)(r.Zb,{children:(0,a.jsx)(r.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-blue-100 rounded-full",children:(0,a.jsx)(u.Z,{className:"h-6 w-6 text-blue-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Total Invoices"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.totalInvoices})]})]})})}),(0,a.jsx)(r.Zb,{children:(0,a.jsx)(r.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-green-100 rounded-full",children:(0,a.jsx)(j.Z,{className:"h-6 w-6 text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Total Invoiced"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:D(e.summary.totalInvoicedAmount)})]})]})})}),(0,a.jsx)(r.Zb,{children:(0,a.jsx)(r.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-purple-100 rounded-full",children:(0,a.jsx)(y.Z,{className:"h-6 w-6 text-purple-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Total Paid"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:D(e.summary.totalPaidAmount)})]})]})})}),(0,a.jsx)(r.Zb,{children:(0,a.jsx)(r.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-red-100 rounded-full",children:(0,a.jsx)(p.Z,{className:"h-6 w-6 text-red-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Outstanding"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:D(e.summary.outstandingAmount)})]})]})})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsx)(r.Zb,{children:(0,a.jsx)(r.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-indigo-100 rounded-full",children:(0,a.jsx)(f.Z,{className:"h-6 w-6 text-indigo-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Collection Rate"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[e.summary.collectionRate.toFixed(1),"%"]})]})]})})}),(0,a.jsx)(r.Zb,{children:(0,a.jsx)(r.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-pink-100 rounded-full",children:(0,a.jsx)(v.Z,{className:"h-6 w-6 text-pink-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Avg Days to Pay"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[e.summary.avgDaysToPayment.toFixed(1),"d"]})]})]})})}),(0,a.jsx)(r.Zb,{children:(0,a.jsx)(r.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-orange-100 rounded-full",children:(0,a.jsx)(N.Z,{className:"h-6 w-6 text-orange-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Avg Invoice Value"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:D(e.summary.averageValue)})]})]})})}),(0,a.jsx)(r.Zb,{children:(0,a.jsx)(r.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-teal-100 rounded-full",children:(0,a.jsx)(g.Z,{className:"h-6 w-6 text-teal-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Paid Invoices"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.paidInvoicesCount})]})]})})})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsx)(r.Ol,{children:(0,a.jsxs)(r.ll,{className:"flex items-center",children:[(0,a.jsx)(j.Z,{className:"h-5 w-5 mr-2"}),"Revenue Overview"]})}),(0,a.jsx)(r.aY,{children:(0,a.jsxs)("div",{className:"grid grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-green-600",children:D(e.revenueMetrics.totalRevenue)}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Total Revenue"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:D(e.revenueMetrics.pendingRevenue)}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Pending Revenue"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-purple-600",children:D(e.revenueMetrics.thisMonthRevenue)}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"This Month"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-orange-600",children:D(e.revenueMetrics.projectedRevenue)}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Projected Total"})]})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)(r.Zb,{children:[(0,a.jsx)(r.Ol,{children:(0,a.jsx)(r.ll,{children:"Invoices by Status"})}),(0,a.jsx)(r.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:e.invoicesByStatus.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsx)(i.C,{className:C(e.status),children:e.status})}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("span",{className:"font-semibold",children:e.count}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:D(e.value)})]})]},e.status))})})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsx)(r.Ol,{children:(0,a.jsxs)(r.ll,{className:"flex items-center",children:[(0,a.jsx)(v.Z,{className:"h-5 w-5 mr-2"}),"Aging Report"]})}),(0,a.jsx)(r.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:e.agingReport.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsx)("span",{className:"font-medium ".concat(S(e.period)),children:e.period})}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("span",{className:"font-semibold",children:e.count}),(0,a.jsx)("p",{className:"text-sm ".concat(S(e.period)),children:D(e.amount)})]})]},e.period))})})]})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsx)(r.Ol,{children:(0,a.jsxs)(r.ll,{className:"flex items-center",children:[(0,a.jsx)(b.Z,{className:"h-5 w-5 mr-2"}),"Top Customers by Invoice Value"]})}),(0,a.jsx)(r.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:e.customerInvoices.slice(0,5).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center",children:(0,a.jsxs)("span",{className:"text-xs font-semibold text-blue-600",children:["#",s+1]})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:e.customer.name}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:e.customer.company})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"font-semibold text-green-600",children:D(e.totalValue)}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[e.invoiceCount," invoices"]}),e.outstandingAmount>0&&(0,a.jsxs)("p",{className:"text-sm text-red-500",children:[D(e.outstandingAmount)," outstanding"]})]})]},e.customer.id))})})]}),e.overdueInvoices.length>0&&(0,a.jsxs)(r.Zb,{children:[(0,a.jsx)(r.Ol,{children:(0,a.jsxs)(r.ll,{className:"flex items-center",children:[(0,a.jsx)(p.Z,{className:"h-5 w-5 mr-2 text-red-600"}),"Overdue Invoices"]})}),(0,a.jsx)(r.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:e.overdueInvoices.slice(0,5).map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:e.invoiceNumber}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:e.customer.name}),(0,a.jsxs)("p",{className:"text-sm text-red-600",children:[e.daysOverdue," days overdue"]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"font-semibold text-red-600",children:D(e.total-e.paidAmount)}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Due: ",new Date(e.dueDate).toLocaleDateString()]})]})]},e.id))})})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsx)(r.Ol,{children:(0,a.jsxs)(r.ll,{className:"flex items-center",children:[(0,a.jsx)(w.Z,{className:"h-5 w-5 mr-2"}),"Recent Invoices (Last 7 Days)"]})}),(0,a.jsx)(r.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:0===e.recentInvoices.length?(0,a.jsx)("p",{className:"text-gray-500 text-center py-4",children:"No recent invoices"}):e.recentInvoices.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:e.invoiceNumber}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:e.title||"No title"}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[e.customer.name," • ",new Date(e.createdAt).toLocaleDateString()]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"font-semibold text-green-600",children:D(e.total)}),(0,a.jsx)(i.C,{className:C(e.status),variant:"outline",children:e.status})]})]},e.id))})})]})]}):(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)(m.Z,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,a.jsx)("p",{children:"Failed to load analytics data"})]})}var C=t(45179),D=t(49842),S=t(23444),E=t(42706),A=t(47934),P=t(67972),I=t(45367);function F(e){let{open:s,invoice:t,onClose:n,onSuccess:r}=e,[d,o]=(0,l.useState)([]),[m,h]=(0,l.useState)(!1),[y,p]=(0,l.useState)(!1),[f,v]=(0,l.useState)({amount:"",paymentDate:new Date().toISOString().split("T")[0],paymentMethod:"CASH",reference:"",notes:""}),N=async()=>{if(t)try{h(!0);let e=await fetch("/api/invoices/".concat(t.id,"/payments"));if(!e.ok)throw Error("Failed to fetch payments");let s=await e.json();o(s.payments)}catch(e){k.toast.error("Failed to load payments"),console.error("Error fetching payments:",e)}finally{h(!1)}};(0,l.useEffect)(()=>{if(s&&t){N();let e=t.total-t.paidAmount;v(s=>({...s,amount:e>0?e.toString():""}))}},[s,t]);let b=(e,s)=>{v(t=>({...t,[e]:s}))},Z=async e=>{if(e.preventDefault(),!t)return;let s=parseFloat(f.amount);if(isNaN(s)||s<=0){k.toast.error("Please enter a valid payment amount");return}let a=t.total-t.paidAmount;if(s>a){k.toast.error("Payment amount cannot exceed remaining balance of $".concat(a.toFixed(2)));return}p(!0);try{let e=await fetch("/api/invoices/".concat(t.id,"/payments"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({amount:s,paymentDate:f.paymentDate,paymentMethod:f.paymentMethod,reference:f.reference||void 0,notes:f.notes||void 0})});if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to record payment")}k.toast.success("Payment recorded successfully!"),v({amount:"",paymentDate:new Date().toISOString().split("T")[0],paymentMethod:"CASH",reference:"",notes:""}),await N(),r()}catch(e){k.toast.error(e instanceof Error?e.message:"Failed to record payment")}finally{p(!1)}},F=async e=>{if(t&&confirm("Are you sure you want to delete this payment?"))try{let s=await fetch("/api/invoices/".concat(t.id,"/payments?paymentId=").concat(e),{method:"DELETE"});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to delete payment")}k.toast.success("Payment deleted successfully!"),await N(),r()}catch(e){k.toast.error(e instanceof Error?e.message:"Failed to delete payment")}},M=()=>{v({amount:"",paymentDate:new Date().toISOString().split("T")[0],paymentMethod:"CASH",reference:"",notes:""}),o([]),n()};if(!t)return null;let R=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e),O=t.total-t.paidAmount;return(0,a.jsx)(E.Vq,{open:s,onOpenChange:M,children:(0,a.jsxs)(E.cZ,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)(E.fK,{children:[(0,a.jsxs)(E.$N,{className:"flex items-center",children:[(0,a.jsx)(g.Z,{className:"h-5 w-5 mr-2"}),"Payment Management"]}),(0,a.jsx)(E.Be,{children:"Record and manage payments for this invoice. View payment history and track remaining balance."})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(u.Z,{className:"h-5 w-5 text-blue-600"}),(0,a.jsx)("span",{className:"font-semibold",children:t.invoiceNumber}),(0,a.jsx)(i.C,{className:(e=>{switch(e){case"DRAFT":default:return"bg-gray-100 text-gray-800";case"SENT":return"bg-blue-100 text-blue-800";case"PAID":return"bg-green-100 text-green-800";case"OVERDUE":return"bg-red-100 text-red-800";case"CANCELLED":return"bg-orange-100 text-orange-800"}})(t.status),children:t.status})]}),(0,a.jsx)("span",{className:"text-lg font-bold text-green-600",children:R(t.total)})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,a.jsx)("p",{className:"font-medium",children:t.title||"No title"}),(0,a.jsxs)("div",{className:"flex items-center mt-1",children:[(0,a.jsx)(P.Z,{className:"h-4 w-4 mr-1"}),(0,a.jsx)("span",{children:t.customer.name}),t.customer.company&&(0,a.jsxs)("span",{className:"ml-1",children:["(",t.customer.company,")"]})]})]}),(0,a.jsxs)("div",{className:"mt-3 grid grid-cols-3 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Total:"}),(0,a.jsx)("div",{className:"font-semibold",children:R(t.total)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Paid:"}),(0,a.jsx)("div",{className:"font-semibold text-green-600",children:R(t.paidAmount)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Balance:"}),(0,a.jsx)("div",{className:"font-semibold ".concat(O>0?"text-red-600":"text-green-600"),children:R(O)})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Record New Payment"}),(0,a.jsxs)("form",{onSubmit:Z,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(D._,{htmlFor:"amount",children:"Payment Amount *"}),(0,a.jsx)(C.I,{id:"amount",type:"number",step:"0.01",min:"0.01",max:O,value:f.amount,onChange:e=>b("amount",e.target.value),placeholder:"0.00",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(D._,{htmlFor:"paymentDate",children:"Payment Date *"}),(0,a.jsx)(C.I,{id:"paymentDate",type:"date",value:f.paymentDate,onChange:e=>b("paymentDate",e.target.value),required:!0})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(D._,{htmlFor:"paymentMethod",children:"Payment Method *"}),(0,a.jsxs)(x.Ph,{value:f.paymentMethod,onValueChange:e=>b("paymentMethod",e),children:[(0,a.jsx)(x.i4,{children:(0,a.jsx)(x.ki,{})}),(0,a.jsxs)(x.Bw,{children:[(0,a.jsx)(x.Ql,{value:"CASH",children:"Cash"}),(0,a.jsx)(x.Ql,{value:"CHECK",children:"Check"}),(0,a.jsx)(x.Ql,{value:"CREDIT_CARD",children:"Credit Card"}),(0,a.jsx)(x.Ql,{value:"BANK_TRANSFER",children:"Bank Transfer"}),(0,a.jsx)(x.Ql,{value:"PAYPAL",children:"PayPal"}),(0,a.jsx)(x.Ql,{value:"OTHER",children:"Other"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(D._,{htmlFor:"reference",children:"Reference (optional)"}),(0,a.jsx)(C.I,{id:"reference",value:f.reference,onChange:e=>b("reference",e.target.value),placeholder:"Check #, Transaction ID, etc."})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(D._,{htmlFor:"notes",children:"Notes (optional)"}),(0,a.jsx)(S.g,{id:"notes",value:f.notes,onChange:e=>b("notes",e.target.value),placeholder:"Additional notes about this payment...",rows:3})]}),(0,a.jsx)(c.z,{type:"submit",disabled:y||O<=0,children:y?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Recording..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(j.Z,{className:"h-4 w-4 mr-2"}),"Record Payment"]})})]})]}),(0,a.jsx)(A.Z,{}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Payment History"}),m?(0,a.jsx)("div",{className:"flex items-center justify-center py-4",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"})}):0===d.length?(0,a.jsx)("p",{className:"text-gray-500 text-center py-4",children:"No payments recorded"}):(0,a.jsx)("div",{className:"space-y-3",children:d.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"font-semibold",children:R(e.amount)}),(0,a.jsx)(i.C,{variant:"outline",children:e.paymentMethod})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(w.Z,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:new Date(e.paymentDate).toLocaleDateString()})]}),e.reference&&(0,a.jsxs)("div",{children:["Reference: ",e.reference]}),e.notes&&(0,a.jsxs)("div",{children:["Notes: ",e.notes]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["Recorded by ",e.createdBy.name||e.createdBy.email," on"," ",new Date(e.createdAt).toLocaleDateString()]})]})]}),(0,a.jsx)(c.z,{variant:"outline",size:"sm",onClick:()=>F(e.id),className:"text-red-600 hover:text-red-700",children:(0,a.jsx)(I.Z,{className:"h-4 w-4"})})]},e.id))})]}),(0,a.jsx)("div",{className:"flex justify-end pt-4 border-t",children:(0,a.jsx)(c.z,{variant:"outline",onClick:M,children:"Close"})})]})]})})}var M=t(17472),R=t(99670),O=t(49617),T=t(56224),V=t(76020),L=t(35817),Y=t(9883),z=t(93930),U=t(61396),_=t.n(U);function q(){let{data:e}=(0,n.useSession)(),[s,t]=(0,l.useState)([]),[x,h]=(0,l.useState)(!0),[f,N]=(0,l.useState)(!1),[b,C]=(0,l.useState)(null),[D,S]=(0,l.useState)(!1),[E,A]=(0,l.useState)(!1),[U,q]=(0,l.useState)(null),[B,H]=(0,l.useState)({total:0,draft:0,sent:0,paid:0,overdue:0,totalValue:0,totalPaid:0}),K=(0,l.useCallback)(async()=>{try{let e=await fetch("/api/invoices");if(!e.ok)throw Error("Failed to fetch invoices");let s=await e.json();t(s.invoices);let a=s.invoices.length,l=s.invoices.filter(e=>"DRAFT"===e.status).length,n=s.invoices.filter(e=>"SENT"===e.status).length,r=s.invoices.filter(e=>"PAID"===e.status).length,c=s.invoices.filter(e=>"OVERDUE"===e.status).length,i=s.invoices.reduce((e,s)=>e+(s.total||0),0),d=s.invoices.filter(e=>"PAID"===e.status).reduce((e,s)=>e+(s.total||0),0);H({total:a,draft:l,sent:n,paid:r,overdue:c,totalValue:i,totalPaid:d})}catch(e){k.toast.error("Failed to load invoices"),console.error("Error fetching invoices:",e)}finally{h(!1)}},[]);(0,l.useEffect)(()=>{K()},[K]);let Q=(0,l.useCallback)(async e=>{if(confirm('Are you sure you want to delete invoice "'.concat(e.invoiceNumber,'"?')))try{let s=await fetch("/api/invoices/".concat(e.id),{method:"DELETE"});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to delete invoice")}k.toast.success("Invoice deleted successfully"),K()}catch(e){k.toast.error(e instanceof Error?e.message:"Failed to delete invoice")}},[K]),X=(0,l.useCallback)(e=>{C(e),N(!0)},[]),$=(0,l.useCallback)(async e=>{try{let s=await fetch("/api/invoices/".concat(e,"/pdf"));if(!s.ok)throw Error("Failed to generate PDF");let t=await s.blob(),a=window.URL.createObjectURL(t),l=document.createElement("a");l.href=a,l.download="invoice-".concat(e,".html"),document.body.appendChild(l),l.click(),window.URL.revokeObjectURL(a),document.body.removeChild(l),k.toast.success("PDF downloaded successfully")}catch(e){k.toast.error("Failed to download PDF"),console.error("Error downloading PDF:",e)}},[]),W=(0,l.useCallback)(e=>{q(e),A(!0)},[]),J=(0,l.useCallback)(e=>()=>X(e),[X]),G=(0,l.useCallback)(e=>()=>Q(e),[Q]),ee=(0,l.useCallback)(e=>()=>$(e),[$]),es=(0,l.useCallback)(e=>()=>W(e),[W]),et=e=>{if(!e)return(0,a.jsx)(i.C,{variant:"secondary",children:"Unknown"});switch(e){case"DRAFT":return(0,a.jsx)(i.C,{variant:"secondary",children:"Draft"});case"SENT":return(0,a.jsx)(i.C,{variant:"info",children:"Sent"});case"VIEWED":return(0,a.jsx)(i.C,{variant:"warning",children:"Viewed"});case"PAID":return(0,a.jsx)(i.C,{variant:"success",children:"Paid"});case"OVERDUE":return(0,a.jsx)(i.C,{variant:"destructive",children:"Overdue"});case"CANCELLED":return(0,a.jsx)(i.C,{variant:"secondary",children:"Cancelled"});default:return(0,a.jsx)(i.C,{variant:"secondary",children:e})}},ea=[{accessorKey:"invoiceNumber",header:"Invoice",cell:e=>{let{row:s}=e,t=s.original;return(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(u.Z,{className:"h-4 w-4 text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:t.invoiceNumber}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:t.quotation?"From ".concat(t.quotation.quotationNumber):"Direct Invoice"})]})]})}},{accessorKey:"customer",header:"Customer",cell:e=>{let{row:s}=e,t=s.original.customer;return(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:t.name}),t.companyName&&(0,a.jsx)("div",{className:"text-sm text-gray-500",children:t.companyName})]})}},{accessorKey:"status",header:"Status",cell:e=>{let{row:s}=e;return et(s.getValue("status"))}},{accessorKey:"total",header:"Amount",cell:e=>{let{row:s}=e,t=s.getValue("total");return(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(j.Z,{className:"h-3 w-3 text-green-600"}),(0,a.jsxs)("span",{className:"font-medium",children:["$",t.toLocaleString()]})]})}},{accessorKey:"issueDate",header:"Issue Date",cell:e=>{let{row:s}=e,t=s.getValue("issueDate");return(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(w.Z,{className:"h-3 w-3 text-gray-400"}),(0,a.jsx)("span",{className:"text-sm",children:new Date(t).toLocaleDateString()})]})}},{accessorKey:"dueDate",header:"Due Date",cell:e=>{let{row:s}=e,t=new Date(s.getValue("dueDate")),l=t<new Date&&"PAID"!==s.original.status;return(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(w.Z,{className:"h-3 w-3 ".concat(l?"text-red-400":"text-gray-400")}),(0,a.jsx)("span",{className:"text-sm ".concat(l?"text-red-600":""),children:t.toLocaleDateString()})]})}},{accessorKey:"createdBy",header:"Created By",cell:e=>{let{row:s}=e,t=s.original.createdBy;return(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(P.Z,{className:"h-3 w-3 text-gray-400"}),(0,a.jsx)("span",{className:"text-sm",children:t.name||"Unknown"})]})}},{id:"actions",cell:e=>{var s,t;let{row:l}=e,n=l.original,r=J(n),i=G(n),d=ee(n.id),o=es(n);return(0,a.jsxs)(z.h_,{children:[(0,a.jsx)(z.$F,{asChild:!0,children:(0,a.jsx)(c.z,{variant:"ghost",className:"h-8 w-8 p-0",children:(0,a.jsx)(M.Z,{className:"h-4 w-4"})})}),(0,a.jsxs)(z.AW,{align:"end",children:[(0,a.jsx)(z.Ju,{children:"Actions"}),(0,a.jsx)(z.Xi,{asChild:!0,children:(0,a.jsxs)(_(),{href:"/dashboard/invoices/".concat(n.id),children:[(0,a.jsx)(R.Z,{className:"mr-2 h-4 w-4"}),"View Details"]})}),(0,a.jsxs)(z.Xi,{onClick:r,children:[(0,a.jsx)(O.Z,{className:"mr-2 h-4 w-4"}),"Edit"]}),(0,a.jsxs)(z.Xi,{children:[(0,a.jsx)(T.Z,{className:"mr-2 h-4 w-4"}),"Duplicate"]}),(0,a.jsx)(z.VD,{}),(0,a.jsxs)(z.Xi,{children:[(0,a.jsx)(V.Z,{className:"mr-2 h-4 w-4"}),"Send to Customer"]}),(0,a.jsxs)(z.Xi,{onClick:d,children:[(0,a.jsx)(L.Z,{className:"mr-2 h-4 w-4"}),"Download PDF"]}),(0,a.jsxs)(z.Xi,{onClick:o,children:[(0,a.jsx)(g.Z,{className:"mr-2 h-4 w-4"}),"Record Payment"]}),(0,a.jsx)(z.VD,{}),(0,a.jsxs)(z.Xi,{onClick:i,className:"text-red-600",disabled:"PAID"===n.status||(null!==(t=null===(s=n._count)||void 0===s?void 0:s.transactions)&&void 0!==t?t:0)>0,children:[(0,a.jsx)(I.Z,{className:"mr-2 h-4 w-4"}),"Delete"]})]})]})}}];return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Invoices"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"Create and manage your invoices"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(c.z,{variant:"outline",onClick:()=>S(!D),children:[(0,a.jsx)(m.Z,{className:"h-4 w-4 mr-2"}),"Analytics"]}),(0,a.jsxs)(c.z,{onClick:()=>N(!0),children:[(0,a.jsx)(Y.Z,{className:"h-4 w-4 mr-2"}),"New Invoice"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4",children:[(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ll,{className:"text-sm font-medium",children:"Total Invoices"}),(0,a.jsx)(u.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(r.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:B.total}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"All invoices"})]})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ll,{className:"text-sm font-medium",children:"Draft"}),(0,a.jsx)(v.Z,{className:"h-4 w-4 text-gray-600"})]}),(0,a.jsxs)(r.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:B.draft}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Draft invoices"})]})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ll,{className:"text-sm font-medium",children:"Sent"}),(0,a.jsx)(V.Z,{className:"h-4 w-4 text-blue-600"})]}),(0,a.jsxs)(r.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:B.sent}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Sent to customers"})]})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ll,{className:"text-sm font-medium",children:"Paid"}),(0,a.jsx)(y.Z,{className:"h-4 w-4 text-green-600"})]}),(0,a.jsxs)(r.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:B.paid}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Paid invoices"})]})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ll,{className:"text-sm font-medium",children:"Overdue"}),(0,a.jsx)(p.Z,{className:"h-4 w-4 text-red-600"})]}),(0,a.jsxs)(r.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:B.overdue}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Overdue invoices"})]})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ll,{className:"text-sm font-medium",children:"Total Value"}),(0,a.jsx)(j.Z,{className:"h-4 w-4 text-green-600"})]}),(0,a.jsxs)(r.aY,{children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:["$",B.totalValue.toLocaleString()]}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Total invoice value"})]})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ll,{className:"text-sm font-medium",children:"Total Paid"}),(0,a.jsx)(g.Z,{className:"h-4 w-4 text-green-600"})]}),(0,a.jsxs)(r.aY,{children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:["$",B.totalPaid.toLocaleString()]}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Total payments received"})]})]})]}),D&&(0,a.jsx)(Z,{}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsx)(r.Ol,{children:(0,a.jsx)(r.ll,{children:"Invoice Management"})}),(0,a.jsx)(r.aY,{children:x?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,a.jsx)(d.w,{columns:ea,data:s,searchPlaceholder:"Search invoices..."})})]}),(0,a.jsx)(o.q,{isOpen:f,onClose:()=>{N(!1),C(null)},onSuccess:K,invoice:b,mode:b?"edit":"create"}),(0,a.jsx)(F,{open:E,invoice:U,onClose:()=>{A(!1),q(null)},onSuccess:()=>{A(!1),q(null),K()}})]})}},47934:function(e,s,t){"use strict";t.d(s,{Z:function(){return c}});var a=t(57437),l=t(2265),n=t(26823),r=t(1657);let c=l.forwardRef((e,s)=>{let{className:t,orientation:l="horizontal",decorative:c=!0,...i}=e;return(0,a.jsx)(n.f,{ref:s,decorative:c,orientation:l,className:(0,r.cn)("shrink-0 bg-border","horizontal"===l?"h-[1px] w-full":"h-full w-[1px]",t),...i})});c.displayName=n.f.displayName},23444:function(e,s,t){"use strict";t.d(s,{g:function(){return r}});var a=t(57437),l=t(2265),n=t(1657);let r=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...l})});r.displayName="Textarea"},26823:function(e,s,t){"use strict";t.d(s,{f:function(){return d}});var a=t(2265),l=t(9381),n=t(57437),r="horizontal",c=["horizontal","vertical"],i=a.forwardRef((e,s)=>{let{decorative:t,orientation:a=r,...i}=e,d=c.includes(a)?a:r;return(0,n.jsx)(l.WV.div,{"data-orientation":d,...t?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...i,ref:s})});i.displayName="Separator";var d=i}},function(e){e.O(0,[6723,9502,2749,1706,4138,1396,4997,2881,2012,5385,528,6426,2971,4938,1744],function(){return e(e.s=75323)}),_N_E=e.O()}]);