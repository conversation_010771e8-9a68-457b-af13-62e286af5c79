"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"510d109943ab\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vYXBwL2dsb2JhbHMuY3NzPzVhOTEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI1MTBkMTA5OTQzYWJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/providers.tsx":
/*!**********************************!*\
  !*** ./components/providers.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: function() { return /* binding */ Providers; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(app-pages-browser)/./node_modules/next-themes/dist/index.module.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _contexts_BrandingContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/BrandingContext */ \"(app-pages-browser)/./contexts/BrandingContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction Providers(param) {\n    let { children } = param;\n    _s();\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(()=>new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.QueryClient({\n            defaultOptions: {\n                queries: {\n                    staleTime: 60 * 1000,\n                    retry: 1\n                }\n            }\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__.QueryClientProvider, {\n            client: queryClient,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_BrandingContext__WEBPACK_IMPORTED_MODULE_4__.BrandingProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                    attribute: \"class\",\n                    defaultTheme: \"light\",\n                    enableSystem: true,\n                    disableTransitionOnChange: true,\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\providers.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\providers.tsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\providers.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\providers.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n_s(Providers, \"4a8f9/a4qj2mUNd/bjx3mzx4Q1k=\");\n_c = Providers;\nvar _c;\n$RefreshReg$(_c, \"Providers\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/providers.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./contexts/BrandingContext.tsx":
/*!**************************************!*\
  !*** ./contexts/BrandingContext.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BrandingProvider: function() { return /* binding */ BrandingProvider; },\n/* harmony export */   useBranding: function() { return /* binding */ useBranding; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ BrandingProvider,useBranding,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst defaultBranding = {\n    appName: \"SaaS Platform\",\n    logoUrl: \"\",\n    faviconUrl: \"\",\n    primaryColor: \"#3b82f6\",\n    secondaryColor: \"#64748b\",\n    accentColor: \"#10b981\",\n    backgroundColor: \"#ffffff\",\n    textColor: \"#1f2937\",\n    theme: \"light\",\n    fontFamily: \"Inter, sans-serif\",\n    customCss: \"\"\n};\nconst BrandingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction BrandingProvider(param) {\n    let { children } = param;\n    _s();\n    const [branding, setBranding] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultBranding);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchBranding();\n    }, []);\n    const fetchBranding = async ()=>{\n        try {\n            const response = await fetch(\"/api/global-config/branding\");\n            const data = await response.json();\n            if (data.success && data.branding) {\n                setBranding({\n                    ...defaultBranding,\n                    ...data.branding\n                });\n                applyBranding({\n                    ...defaultBranding,\n                    ...data.branding\n                });\n            } else {\n                applyBranding(defaultBranding);\n            }\n        } catch (error) {\n            console.error(\"Error fetching branding config:\", error);\n            applyBranding(defaultBranding);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const updateBranding = (config)=>{\n        const newBranding = {\n            ...branding,\n            ...config\n        };\n        setBranding(newBranding);\n        applyBranding(newBranding);\n    };\n    const applyBranding = (config)=>{\n        // Apply CSS custom properties\n        const root = document.documentElement;\n        root.style.setProperty(\"--primary-color\", config.primaryColor);\n        root.style.setProperty(\"--secondary-color\", config.secondaryColor);\n        root.style.setProperty(\"--accent-color\", config.accentColor);\n        root.style.setProperty(\"--background-color\", config.backgroundColor);\n        root.style.setProperty(\"--text-color\", config.textColor);\n        root.style.setProperty(\"--font-family\", config.fontFamily);\n        // Apply theme class\n        document.body.className = document.body.className.replace(/theme-\\w+/g, \"\");\n        document.body.classList.add(\"theme-\".concat(config.theme));\n        // Update document title\n        document.title = config.appName;\n        // Update favicon\n        if (config.faviconUrl) {\n            let favicon = document.querySelector('link[rel=\"icon\"]');\n            if (!favicon) {\n                favicon = document.createElement(\"link\");\n                favicon.rel = \"icon\";\n                document.head.appendChild(favicon);\n            }\n            favicon.href = config.faviconUrl;\n        }\n        // Apply custom CSS\n        let customStyleElement = document.getElementById(\"custom-branding-css\");\n        if (config.customCss) {\n            if (!customStyleElement) {\n                customStyleElement = document.createElement(\"style\");\n                customStyleElement.id = \"custom-branding-css\";\n                document.head.appendChild(customStyleElement);\n            }\n            customStyleElement.textContent = config.customCss;\n        } else if (customStyleElement) {\n            customStyleElement.remove();\n        }\n        // Update meta theme-color for mobile browsers\n        let themeColorMeta = document.querySelector('meta[name=\"theme-color\"]');\n        if (!themeColorMeta) {\n            themeColorMeta = document.createElement(\"meta\");\n            themeColorMeta.name = \"theme-color\";\n            document.head.appendChild(themeColorMeta);\n        }\n        themeColorMeta.content = config.primaryColor;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BrandingContext.Provider, {\n        value: {\n            branding,\n            updateBranding,\n            loading\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\contexts\\\\BrandingContext.tsx\",\n        lineNumber: 126,\n        columnNumber: 5\n    }, this);\n}\n_s(BrandingProvider, \"oRbMRDkGFTNq1OqVLfXpGcpiZY0=\");\n_c = BrandingProvider;\nfunction useBranding() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(BrandingContext);\n    if (context === undefined) {\n        throw new Error(\"useBranding must be used within a BrandingProvider\");\n    }\n    return context;\n}\n_s1(useBranding, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n/* harmony default export */ __webpack_exports__[\"default\"] = (BrandingContext);\nvar _c;\n$RefreshReg$(_c, \"BrandingProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./contexts/BrandingContext.tsx\n"));

/***/ })

});