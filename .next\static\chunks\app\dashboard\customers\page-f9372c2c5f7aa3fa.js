(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[923],{90998:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]])},92457:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},98253:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("Building2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},13008:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},41298:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},35817:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},76637:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]])},1295:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},12741:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},55340:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},85790:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},31541:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},52431:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("UserCheck",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]])},25750:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},82104:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},99709:function(e,s,t){Promise.resolve().then(t.bind(t,21768))},21768:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return W}});var a=t(57437),l=t(2265),r=t(82749),c=t(27815),n=t(85754),i=t(31478),d=t(25462),o=t(5129),x=t(45509),m=t(92457),h=t(64280),u=t(25750),p=t(41298),j=t(85790),f=t(52431),y=t(90998),N=t(55340),v=t(66654),g=t(5925);function b(){let[e,s]=(0,l.useState)(null),[t,r]=(0,l.useState)(!0),[d,o]=(0,l.useState)("30"),b=async()=>{try{r(!0);let e=await fetch("/api/customers/analytics?period=".concat(d));if(!e.ok)throw Error("Failed to fetch analytics");let t=await e.json();s(t)}catch(e){g.toast.error("Failed to load customer analytics"),console.error("Error fetching analytics:",e)}finally{r(!1)}};(0,l.useEffect)(()=>{b()},[d]);let w=e=>{switch(e){case"ACTIVE":return"bg-green-100 text-green-800";case"INACTIVE":default:return"bg-gray-100 text-gray-800";case"PROSPECT":return"bg-blue-100 text-blue-800"}},k=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e);return t?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):e?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Customer Analytics"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(x.Ph,{value:d,onValueChange:o,children:[(0,a.jsx)(x.i4,{className:"w-32",children:(0,a.jsx)(x.ki,{})}),(0,a.jsxs)(x.Bw,{children:[(0,a.jsx)(x.Ql,{value:"7",children:"Last 7 days"}),(0,a.jsx)(x.Ql,{value:"30",children:"Last 30 days"}),(0,a.jsx)(x.Ql,{value:"90",children:"Last 90 days"}),(0,a.jsx)(x.Ql,{value:"365",children:"Last year"})]})]}),(0,a.jsxs)(n.z,{variant:"outline",onClick:b,size:"sm",children:[(0,a.jsx)(h.Z,{className:"h-4 w-4 mr-2"}),"Refresh"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4",children:[(0,a.jsx)(c.Zb,{children:(0,a.jsx)(c.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-blue-100 rounded-full",children:(0,a.jsx)(u.Z,{className:"h-6 w-6 text-blue-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Total Customers"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.totalCustomers})]})]})})}),(0,a.jsx)(c.Zb,{children:(0,a.jsx)(c.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-green-100 rounded-full",children:(0,a.jsx)(p.Z,{className:"h-6 w-6 text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Total Revenue"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:k(e.summary.totalRevenue)})]})]})})}),(0,a.jsx)(c.Zb,{children:(0,a.jsx)(c.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-purple-100 rounded-full",children:(0,a.jsx)(j.Z,{className:"h-6 w-6 text-purple-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Avg CLV"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:k(e.summary.avgCLV)})]})]})})}),(0,a.jsx)(c.Zb,{children:(0,a.jsx)(c.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-orange-100 rounded-full",children:(0,a.jsx)(f.Z,{className:"h-6 w-6 text-orange-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Retention Rate"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[e.summary.retentionRate.toFixed(1),"%"]})]})]})})}),(0,a.jsx)(c.Zb,{children:(0,a.jsx)(c.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-indigo-100 rounded-full",children:(0,a.jsx)(y.Z,{className:"h-6 w-6 text-indigo-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Engagement Rate"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[e.summary.engagementRate,"%"]})]})]})})}),(0,a.jsx)(c.Zb,{children:(0,a.jsx)(c.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-pink-100 rounded-full",children:(0,a.jsx)(N.Z,{className:"h-6 w-6 text-pink-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"New This Week"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.newCustomersThisWeek})]})]})})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)(c.Zb,{children:[(0,a.jsx)(c.Ol,{children:(0,a.jsx)(c.ll,{children:"Customers by Status"})}),(0,a.jsx)(c.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:e.customersByStatus.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsx)(i.C,{className:w(e.status),children:e.status})}),(0,a.jsx)("span",{className:"font-semibold",children:e.count})]},e.status))})})]}),(0,a.jsxs)(c.Zb,{children:[(0,a.jsx)(c.Ol,{children:(0,a.jsx)(c.ll,{children:"Customer Segmentation"})}),(0,a.jsx)(c.aY,{children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm",children:"High Value Customers"}),(0,a.jsx)("span",{className:"font-semibold",children:e.segmentation.highValue})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm",children:"Active Customers"}),(0,a.jsx)("span",{className:"font-semibold",children:e.segmentation.active})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm",children:"Prospects"}),(0,a.jsx)("span",{className:"font-semibold",children:e.segmentation.prospects})]})]})})]}),(0,a.jsxs)(c.Zb,{children:[(0,a.jsx)(c.Ol,{children:(0,a.jsx)(c.ll,{children:"Customers by Industry"})}),(0,a.jsx)(c.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:e.customersByIndustry.slice(0,5).map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm",children:e.industry}),(0,a.jsx)("span",{className:"font-semibold",children:e.count})]},e.industry))})})]}),(0,a.jsxs)(c.Zb,{children:[(0,a.jsx)(c.Ol,{children:(0,a.jsx)(c.ll,{children:"Customer Engagement"})}),(0,a.jsx)(c.aY,{children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm",children:"With Recent Activities"}),(0,a.jsx)("span",{className:"font-semibold",children:e.engagement.withActivities})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm",children:"With Recent Quotations"}),(0,a.jsx)("span",{className:"font-semibold",children:e.engagement.withQuotations})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm",children:"With Recent Invoices"}),(0,a.jsx)("span",{className:"font-semibold",children:e.engagement.withInvoices})]})]})})]})]}),(0,a.jsxs)(c.Zb,{children:[(0,a.jsx)(c.Ol,{children:(0,a.jsxs)(c.ll,{className:"flex items-center",children:[(0,a.jsx)(v.Z,{className:"h-5 w-5 mr-2"}),"Top Customers by Revenue"]})}),(0,a.jsx)(c.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:0===e.topCustomers.length?(0,a.jsx)("p",{className:"text-gray-500 text-center py-4",children:"No customer data available"}):e.topCustomers.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,a.jsxs)("span",{className:"text-sm font-semibold text-blue-600",children:["#",s+1]})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[e.company&&"".concat(e.company," • "),e.email]})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"font-semibold text-green-600",children:k(e.totalRevenue)}),(0,a.jsx)(i.C,{className:w(e.status),variant:"outline",children:e.status})]})]},e.id))})})]}),(0,a.jsxs)(c.Zb,{children:[(0,a.jsx)(c.Ol,{children:(0,a.jsx)(c.ll,{children:"Recent Customers (Last 7 Days)"})}),(0,a.jsx)(c.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:0===e.recentCustomers.length?(0,a.jsx)("p",{className:"text-gray-500 text-center py-4",children:"No new customers this week"}):e.recentCustomers.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[e.company&&"".concat(e.company," • "),"Added by ",e.createdBy.name||e.createdBy.email]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:new Date(e.createdAt).toLocaleDateString()}),(0,a.jsx)(i.C,{className:w(e.status),variant:"outline",children:e.status})]})]},e.id))})})]}),(0,a.jsxs)(c.Zb,{children:[(0,a.jsx)(c.Ol,{children:(0,a.jsx)(c.ll,{children:"Customer Lifetime Value Analysis"})}),(0,a.jsx)(c.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:0===e.clvMetrics.length?(0,a.jsx)("p",{className:"text-gray-500 text-center py-4",children:"No CLV data available"}):e.clvMetrics.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Customer for ",e.customerAge," days"]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("p",{className:"font-semibold text-purple-600",children:["CLV: ",k(e.clv)]}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Total: ",k(e.totalRevenue)]})]})]},e.id))})})]})]}):(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)(m.Z,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,a.jsx)("p",{children:"Failed to load analytics data"})]})}var w=t(45179),k=t(49842),C=t(54900),Z=t(42706),E=t(47934),S=t(31541),R=t(76637),O=t(13008),I=t(82104);function V(e){let{open:s,onClose:t,onSuccess:r}=e,[d,o]=(0,l.useState)(null),[x,m]=(0,l.useState)(!1),[h,u]=(0,l.useState)(null),[p,j]=(0,l.useState)({skipDuplicates:!0,updateExisting:!1}),f=(0,l.useRef)(null),y=e=>{let s=e.split("\n").filter(e=>e.trim());if(s.length<2)throw Error("CSV file must have at least a header row and one data row");let t=s[0].split(",").map(e=>e.trim().replace(/"/g,"")),a=[];for(let e=1;e<s.length;e++){let l=s[e].split(",").map(e=>e.trim().replace(/"/g,"")),r={};t.forEach((e,s)=>{let t=l[s]||"";switch(e.toLowerCase()){case"name":r.name=t;break;case"email":r.email=t||null;break;case"phone":r.phone=t||null;break;case"company":r.company=t||null;break;case"address":r.address=t||null;break;case"city":r.city=t||null;break;case"state":r.state=t||null;break;case"country":r.country=t||null;break;case"postalcode":case"postal_code":case"zip":r.postalCode=t||null;break;case"industry":r.industry=t||null;break;case"website":r.website=t||null;break;case"notes":r.notes=t||null;break;case"status":r.status=["ACTIVE","INACTIVE","PROSPECT"].includes(t.toUpperCase())?t.toUpperCase():"PROSPECT";break;case"tags":r.tags=t?t.split(";").map(e=>e.trim()).filter(Boolean):[]}}),r.name&&a.push(r)}return a},N=async()=>{if(!d){g.toast.error("Please select a file to import");return}m(!0);try{let e=await d.text(),s=y(e);if(0===s.length)throw Error("No valid customer data found in the file");let t=await fetch("/api/customers/import",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({customers:s,options:p})});if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to import customers")}let a=await t.json();u(a.results),(a.results.imported>0||a.results.updated>0)&&(g.toast.success("Successfully processed ".concat(a.results.imported+a.results.updated," customers")),r())}catch(e){g.toast.error(e instanceof Error?e.message:"Failed to import customers"),console.error("Import error:",e)}finally{m(!1)}},v=()=>{o(null),u(null),j({skipDuplicates:!0,updateExisting:!1}),t()},b=async()=>{try{let e=await fetch("/api/customers/import?format=csv");if(!e.ok)throw Error("Failed to download template");let s=await e.blob(),t=window.URL.createObjectURL(s),a=document.createElement("a");a.href=t,a.download="customer-import-template.csv",document.body.appendChild(a),a.click(),window.URL.revokeObjectURL(t),document.body.removeChild(a),g.toast.success("Template downloaded successfully")}catch(e){g.toast.error("Failed to download template")}};return(0,a.jsx)(Z.Vq,{open:s,onOpenChange:v,children:(0,a.jsxs)(Z.cZ,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsx)(Z.fK,{children:(0,a.jsx)(Z.$N,{children:"Import Customers"})}),(0,a.jsxs)("div",{className:"space-y-6",children:[h?(0,a.jsxs)(c.Zb,{children:[(0,a.jsx)(c.Ol,{children:(0,a.jsxs)(c.ll,{className:"flex items-center",children:[(0,a.jsx)(O.Z,{className:"h-5 w-5 mr-2 text-green-600"}),"Import Results"]})}),(0,a.jsxs)(c.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:h.total}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Total"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:h.imported}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Imported"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:h.updated}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Updated"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-600",children:h.skipped}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Skipped"})]})]}),h.errors.length>0&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(E.Z,{}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h4",{className:"font-medium text-red-600 mb-2 flex items-center",children:[(0,a.jsx)(I.Z,{className:"h-4 w-4 mr-2"}),"Errors (",h.errors.length,")"]}),(0,a.jsx)("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:h.errors.map((e,s)=>(0,a.jsxs)("div",{className:"p-2 bg-red-50 rounded text-sm",children:[(0,a.jsxs)("div",{className:"font-medium text-red-800",children:["Row ",e.row,": ",e.error]}),(0,a.jsxs)("div",{className:"text-red-600 text-xs mt-1",children:["Data: ",JSON.stringify(e.data)]})]},s))})]})]})]})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(c.Zb,{children:[(0,a.jsx)(c.Ol,{children:(0,a.jsxs)(c.ll,{className:"flex items-center",children:[(0,a.jsx)(S.Z,{className:"h-5 w-5 mr-2"}),"Upload CSV File"]})}),(0,a.jsxs)(c.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(k._,{htmlFor:"file",children:"Select CSV File"}),(0,a.jsx)(w.I,{id:"file",type:"file",accept:".csv",onChange:e=>{var s;let t=null===(s=e.target.files)||void 0===s?void 0:s[0];t&&("text/csv"===t.type||t.name.endsWith(".csv")?(o(t),u(null)):g.toast.error("Please select a CSV file"))},ref:f})]}),d&&(0,a.jsx)("div",{className:"p-3 bg-green-50 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(R.Z,{className:"h-4 w-4 text-green-600"}),(0,a.jsx)("span",{className:"text-sm font-medium text-green-800",children:d.name}),(0,a.jsxs)(i.C,{variant:"outline",children:[(d.size/1024).toFixed(1)," KB"]})]})}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,a.jsx)("p",{children:"Upload a CSV file with customer data. The file should include columns like:"}),(0,a.jsx)("p",{className:"mt-1 font-mono text-xs bg-gray-100 p-2 rounded",children:"name, email, phone, company, address, city, state, country, postalCode, industry, website, notes, status, tags"})]}),(0,a.jsxs)(n.z,{variant:"outline",onClick:b,className:"w-full",children:[(0,a.jsx)(R.Z,{className:"h-4 w-4 mr-2"}),"Download Template"]})]})]}),(0,a.jsxs)(c.Zb,{children:[(0,a.jsx)(c.Ol,{children:(0,a.jsx)(c.ll,{children:"Import Options"})}),(0,a.jsxs)(c.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(C.X,{id:"skipDuplicates",checked:p.skipDuplicates,onCheckedChange:e=>j(s=>({...s,skipDuplicates:e}))}),(0,a.jsx)(k._,{htmlFor:"skipDuplicates",children:"Skip duplicate customers (by email)"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(C.X,{id:"updateExisting",checked:p.updateExisting,onCheckedChange:e=>j(s=>({...s,updateExisting:e}))}),(0,a.jsx)(k._,{htmlFor:"updateExisting",children:"Update existing customers"})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,a.jsx)("p",{children:"• Skip duplicates: Existing customers with the same email will be ignored"}),(0,a.jsx)("p",{children:"• Update existing: Existing customers will be updated with new data"})]})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-2 pt-4 border-t",children:[(0,a.jsx)(n.z,{variant:"outline",onClick:v,children:h?"Close":"Cancel"}),!h&&(0,a.jsx)(n.z,{onClick:N,disabled:!d||x,children:x?"Importing...":"Import Customers"})]})]})]})})}var L=t(1295),A=t(12741),T=t(98253),U=t(17472),F=t(99670),M=t(49617),z=t(45367),D=t(35817),P=t(9883),Y=t(93930),_=t(61396),q=t.n(_);function W(){let{data:e}=(0,r.useSession)(),[s,t]=(0,l.useState)([]),[x,h]=(0,l.useState)(!0),[f,y]=(0,l.useState)(!1),[N,v]=(0,l.useState)(null),[w,k]=(0,l.useState)(!1),[C,Z]=(0,l.useState)(!1),[E,O]=(0,l.useState)({total:0,active:0,prospects:0,inactive:0}),I=async()=>{try{let e=await fetch("/api/customers");if(!e.ok)throw Error("Failed to fetch customers");let s=await e.json();t(s.customers);let a=s.customers.length,l=s.customers.filter(e=>"ACTIVE"===e.status).length,r=s.customers.filter(e=>"PROSPECT"===e.status).length,c=s.customers.filter(e=>"INACTIVE"===e.status).length;O({total:a,active:l,prospects:r,inactive:c})}catch(e){g.toast.error("Failed to load customers"),console.error("Error fetching customers:",e)}finally{h(!1)}};(0,l.useEffect)(()=>{I()},[]);let _=async e=>{if(confirm("Are you sure you want to delete ".concat(e.name,"?")))try{let s=await fetch("/api/customers/".concat(e.id),{method:"DELETE"});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to delete customer")}g.toast.success("Customer deleted successfully"),I()}catch(e){g.toast.error(e instanceof Error?e.message:"Failed to delete customer")}},W=e=>{v(e),y(!0)},X=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"csv";try{let s=await fetch("/api/customers/export?format=".concat(e,"&includeStats=true"));if(!s.ok)throw Error("Failed to export customers");if("csv"===e){let e=await s.blob(),t=window.URL.createObjectURL(e),a=document.createElement("a");a.href=t,a.download="customers-export-".concat(new Date().toISOString().split("T")[0],".csv"),document.body.appendChild(a),a.click(),window.URL.revokeObjectURL(t),document.body.removeChild(a)}else{let e=await s.json(),t=new Blob([JSON.stringify(e,null,2)],{type:"application/json"}),a=window.URL.createObjectURL(t),l=document.createElement("a");l.href=a,l.download="customers-export-".concat(new Date().toISOString().split("T")[0],".json"),document.body.appendChild(l),l.click(),window.URL.revokeObjectURL(a),document.body.removeChild(l)}g.toast.success("Customers exported successfully")}catch(e){g.toast.error("Failed to export customers"),console.error("Error exporting customers:",e)}},B=async()=>{try{let e=await fetch("/api/customers/import?format=csv");if(!e.ok)throw Error("Failed to get import template");let s=await e.blob(),t=window.URL.createObjectURL(s),a=document.createElement("a");a.href=t,a.download="customer-import-template.csv",document.body.appendChild(a),a.click(),window.URL.revokeObjectURL(t),document.body.removeChild(a),g.toast.success("Import template downloaded")}catch(e){g.toast.error("Failed to download import template"),console.error("Error downloading template:",e)}},H=e=>{if(!e)return(0,a.jsx)(i.C,{variant:"secondary",children:"Unknown"});switch(e){case"ACTIVE":return(0,a.jsx)(i.C,{variant:"success",children:"Active"});case"INACTIVE":return(0,a.jsx)(i.C,{variant:"secondary",children:"Inactive"});case"PROSPECT":return(0,a.jsx)(i.C,{variant:"warning",children:"Prospect"});default:return(0,a.jsx)(i.C,{variant:"secondary",children:e})}},K=[{accessorKey:"name",header:"Name",cell:e=>{let{row:s}=e,t=s.original;return(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-blue-600 font-medium text-sm",children:t.name.charAt(0).toUpperCase()})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:t.name}),t.company&&(0,a.jsx)("div",{className:"text-sm text-gray-500",children:t.company})]})]})}},{accessorKey:"email",header:"Contact",cell:e=>{let{row:s}=e,t=s.original;return(0,a.jsxs)("div",{className:"space-y-1",children:[t.email&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,a.jsx)(L.Z,{className:"h-3 w-3 text-gray-400"}),(0,a.jsx)("span",{children:t.email})]}),t.phone&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,a.jsx)(A.Z,{className:"h-3 w-3 text-gray-400"}),(0,a.jsx)("span",{children:t.phone})]})]})}},{accessorKey:"status",header:"Status",cell:e=>{let{row:s}=e;return H(s.getValue("status"))}},{accessorKey:"industry",header:"Industry",cell:e=>{let{row:s}=e,t=s.getValue("industry");return t?(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(T.Z,{className:"h-3 w-3 text-gray-400"}),(0,a.jsx)("span",{className:"text-sm",children:t})]}):(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"-"})}},{accessorKey:"_count",header:"Activity",cell:e=>{let{row:s}=e,t=s.getValue("_count");return(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(j.Z,{className:"h-3 w-3 text-green-600"}),(0,a.jsx)("span",{children:t.leads})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(p.Z,{className:"h-3 w-3 text-blue-600"}),(0,a.jsx)("span",{children:t.quotations})]})]})}},{accessorKey:"tags",header:"Tags",cell:e=>{let{row:s}=e,t=s.getValue("tags");return t&&Array.isArray(t)&&0!==t.length?(0,a.jsxs)("div",{className:"flex flex-wrap gap-1",children:[t.slice(0,2).map(e=>(0,a.jsx)(i.C,{variant:"outline",className:"text-xs",children:e},e)),t.length>2&&(0,a.jsxs)(i.C,{variant:"outline",className:"text-xs",children:["+",t.length-2]})]}):(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"-"})}},{id:"actions",cell:e=>{let{row:s}=e,t=s.original;return(0,a.jsxs)(Y.h_,{children:[(0,a.jsx)(Y.$F,{asChild:!0,children:(0,a.jsx)(n.z,{variant:"ghost",className:"h-8 w-8 p-0",children:(0,a.jsx)(U.Z,{className:"h-4 w-4"})})}),(0,a.jsxs)(Y.AW,{align:"end",children:[(0,a.jsx)(Y.Ju,{children:"Actions"}),(0,a.jsx)(Y.Xi,{asChild:!0,children:(0,a.jsxs)(q(),{href:"/dashboard/customers/".concat(t.id),children:[(0,a.jsx)(F.Z,{className:"mr-2 h-4 w-4"}),"View Details"]})}),(0,a.jsxs)(Y.Xi,{onClick:()=>W(t),children:[(0,a.jsx)(M.Z,{className:"mr-2 h-4 w-4"}),"Edit"]}),(0,a.jsx)(Y.VD,{}),(0,a.jsxs)(Y.Xi,{onClick:()=>_(t),className:"text-red-600",children:[(0,a.jsx)(z.Z,{className:"mr-2 h-4 w-4"}),"Delete"]})]})]})}}];return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Customers"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"Manage your customer relationships"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(n.z,{variant:"outline",onClick:()=>k(!w),children:[(0,a.jsx)(m.Z,{className:"h-4 w-4 mr-2"}),"Analytics"]}),(0,a.jsxs)(Y.h_,{children:[(0,a.jsx)(Y.$F,{asChild:!0,children:(0,a.jsxs)(n.z,{variant:"outline",children:[(0,a.jsx)(D.Z,{className:"h-4 w-4 mr-2"}),"Export"]})}),(0,a.jsxs)(Y.AW,{children:[(0,a.jsxs)(Y.Xi,{onClick:()=>X("csv"),children:[(0,a.jsx)(R.Z,{className:"h-4 w-4 mr-2"}),"Export as CSV"]}),(0,a.jsxs)(Y.Xi,{onClick:()=>X("json"),children:[(0,a.jsx)(R.Z,{className:"h-4 w-4 mr-2"}),"Export as JSON"]})]})]}),(0,a.jsxs)(Y.h_,{children:[(0,a.jsx)(Y.$F,{asChild:!0,children:(0,a.jsxs)(n.z,{variant:"outline",children:[(0,a.jsx)(S.Z,{className:"h-4 w-4 mr-2"}),"Import"]})}),(0,a.jsxs)(Y.AW,{children:[(0,a.jsxs)(Y.Xi,{onClick:B,children:[(0,a.jsx)(D.Z,{className:"h-4 w-4 mr-2"}),"Download Template"]}),(0,a.jsxs)(Y.Xi,{onClick:()=>Z(!0),children:[(0,a.jsx)(S.Z,{className:"h-4 w-4 mr-2"}),"Import Customers"]})]})]}),(0,a.jsxs)(n.z,{onClick:()=>y(!0),children:[(0,a.jsx)(P.Z,{className:"h-4 w-4 mr-2"}),"Add Customer"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,a.jsxs)(c.Zb,{children:[(0,a.jsxs)(c.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(c.ll,{className:"text-sm font-medium",children:"Total Customers"}),(0,a.jsx)(u.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(c.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:E.total}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"All customers"})]})]}),(0,a.jsxs)(c.Zb,{children:[(0,a.jsxs)(c.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(c.ll,{className:"text-sm font-medium",children:"Active"}),(0,a.jsx)(u.Z,{className:"h-4 w-4 text-green-600"})]}),(0,a.jsxs)(c.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:E.active}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Active customers"})]})]}),(0,a.jsxs)(c.Zb,{children:[(0,a.jsxs)(c.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(c.ll,{className:"text-sm font-medium",children:"Prospects"}),(0,a.jsx)(u.Z,{className:"h-4 w-4 text-yellow-600"})]}),(0,a.jsxs)(c.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:E.prospects}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Potential customers"})]})]}),(0,a.jsxs)(c.Zb,{children:[(0,a.jsxs)(c.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(c.ll,{className:"text-sm font-medium",children:"Inactive"}),(0,a.jsx)(u.Z,{className:"h-4 w-4 text-gray-400"})]}),(0,a.jsxs)(c.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:E.inactive}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Inactive customers"})]})]})]}),w&&(0,a.jsx)(b,{}),(0,a.jsxs)(c.Zb,{children:[(0,a.jsx)(c.Ol,{children:(0,a.jsx)(c.ll,{children:"Customer Directory"})}),(0,a.jsx)(c.aY,{children:x?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,a.jsx)(d.w,{columns:K,data:s,searchPlaceholder:"Search customers..."})})]}),(0,a.jsx)(o.A,{isOpen:f,onClose:()=>{y(!1),v(null)},onSuccess:I,customer:N,mode:N?"edit":"create"}),(0,a.jsx)(V,{open:C,onClose:()=>Z(!1),onSuccess:()=>{Z(!1),I()}})]})}},54900:function(e,s,t){"use strict";t.d(s,{X:function(){return i}});var a=t(57437),l=t(2265),r=t(66062),c=t(62442),n=t(1657);let i=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.fC,{ref:s,className:(0,n.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",t),...l,children:(0,a.jsx)(r.z$,{className:(0,n.cn)("flex items-center justify-center text-current"),children:(0,a.jsx)(c.Z,{className:"h-4 w-4"})})})});i.displayName=r.fC.displayName},47934:function(e,s,t){"use strict";t.d(s,{Z:function(){return n}});var a=t(57437),l=t(2265),r=t(26823),c=t(1657);let n=l.forwardRef((e,s)=>{let{className:t,orientation:l="horizontal",decorative:n=!0,...i}=e;return(0,a.jsx)(r.f,{ref:s,decorative:n,orientation:l,className:(0,c.cn)("shrink-0 bg-border","horizontal"===l?"h-[1px] w-full":"h-full w-[1px]",t),...i})});n.displayName=r.f.displayName},66062:function(e,s,t){"use strict";t.d(s,{fC:function(){return g},z$:function(){return w}});var a=t(2265),l=t(42210),r=t(56989),c=t(85744),n=t(73763),i=t(85184),d=t(94977),o=t(85606),x=t(9381),m=t(57437),h="Checkbox",[u,p]=(0,r.b)(h),[j,f]=u(h);function y(e){let{__scopeCheckbox:s,checked:t,children:l,defaultChecked:r,disabled:c,form:i,name:d,onCheckedChange:o,required:x,value:u="on",internal_do_not_use_render:p}=e,[f,y]=(0,n.T)({prop:t,defaultProp:r??!1,onChange:o,caller:h}),[N,v]=a.useState(null),[g,b]=a.useState(null),w=a.useRef(!1),k=!N||!!i||!!N.closest("form"),C={checked:f,disabled:c,setChecked:y,control:N,setControl:v,name:d,form:i,value:u,hasConsumerStoppedPropagationRef:w,required:x,defaultChecked:!Z(r)&&r,isFormControl:k,bubbleInput:g,setBubbleInput:b};return(0,m.jsx)(j,{scope:s,...C,children:"function"==typeof p?p(C):l})}var N="CheckboxTrigger",v=a.forwardRef(({__scopeCheckbox:e,onKeyDown:s,onClick:t,...r},n)=>{let{control:i,value:d,disabled:o,checked:h,required:u,setControl:p,setChecked:j,hasConsumerStoppedPropagationRef:y,isFormControl:v,bubbleInput:g}=f(N,e),b=(0,l.e)(n,p),w=a.useRef(h);return a.useEffect(()=>{let e=i?.form;if(e){let s=()=>j(w.current);return e.addEventListener("reset",s),()=>e.removeEventListener("reset",s)}},[i,j]),(0,m.jsx)(x.WV.button,{type:"button",role:"checkbox","aria-checked":Z(h)?"mixed":h,"aria-required":u,"data-state":E(h),"data-disabled":o?"":void 0,disabled:o,value:d,...r,ref:b,onKeyDown:(0,c.M)(s,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,c.M)(t,e=>{j(e=>!!Z(e)||!e),g&&v&&(y.current=e.isPropagationStopped(),y.current||e.stopPropagation())})})});v.displayName=N;var g=a.forwardRef((e,s)=>{let{__scopeCheckbox:t,name:a,checked:l,defaultChecked:r,required:c,disabled:n,value:i,onCheckedChange:d,form:o,...x}=e;return(0,m.jsx)(y,{__scopeCheckbox:t,checked:l,defaultChecked:r,disabled:n,required:c,onCheckedChange:d,name:a,form:o,value:i,internal_do_not_use_render:({isFormControl:e})=>(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(v,{...x,ref:s,__scopeCheckbox:t}),e&&(0,m.jsx)(C,{__scopeCheckbox:t})]})})});g.displayName=h;var b="CheckboxIndicator",w=a.forwardRef((e,s)=>{let{__scopeCheckbox:t,forceMount:a,...l}=e,r=f(b,t);return(0,m.jsx)(o.z,{present:a||Z(r.checked)||!0===r.checked,children:(0,m.jsx)(x.WV.span,{"data-state":E(r.checked),"data-disabled":r.disabled?"":void 0,...l,ref:s,style:{pointerEvents:"none",...e.style}})})});w.displayName=b;var k="CheckboxBubbleInput",C=a.forwardRef(({__scopeCheckbox:e,...s},t)=>{let{control:r,hasConsumerStoppedPropagationRef:c,checked:n,defaultChecked:o,required:h,disabled:u,name:p,value:j,form:y,bubbleInput:N,setBubbleInput:v}=f(k,e),g=(0,l.e)(t,v),b=(0,i.D)(n),w=(0,d.t)(r);a.useEffect(()=>{if(!N)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,s=!c.current;if(b!==n&&e){let t=new Event("click",{bubbles:s});N.indeterminate=Z(n),e.call(N,!Z(n)&&n),N.dispatchEvent(t)}},[N,b,n,c]);let C=a.useRef(!Z(n)&&n);return(0,m.jsx)(x.WV.input,{type:"checkbox","aria-hidden":!0,defaultChecked:o??C.current,required:h,disabled:u,name:p,value:j,form:y,...s,tabIndex:-1,ref:g,style:{...s.style,...w,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function Z(e){return"indeterminate"===e}function E(e){return Z(e)?"indeterminate":e?"checked":"unchecked"}C.displayName=k},26823:function(e,s,t){"use strict";t.d(s,{f:function(){return d}});var a=t(2265),l=t(9381),r=t(57437),c="horizontal",n=["horizontal","vertical"],i=a.forwardRef((e,s)=>{let{decorative:t,orientation:a=c,...i}=e,d=n.includes(a)?a:c;return(0,r.jsx)(l.WV.div,{"data-orientation":d,...t?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...i,ref:s})});i.displayName="Separator";var d=i}},function(e){e.O(0,[6723,9502,2749,1706,4138,1396,4997,2881,2012,5385,528,5129,2971,4938,1744],function(){return e(e.s=99709)}),_N_E=e.O()}]);