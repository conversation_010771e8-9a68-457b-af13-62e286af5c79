"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js":
/*!*****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/credit-card.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CreditCard; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst CreditCard = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"CreditCard\", [\n  [\"rect\", { width: \"20\", height: \"14\", x: \"2\", y: \"5\", rx: \"2\", key: \"ynyp8z\" }],\n  [\"line\", { x1: \"2\", x2: \"22\", y1: \"10\", y2: \"10\", key: \"1b3vmo\" }]\n]);\n\n\n//# sourceMappingURL=credit-card.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY3JlZGl0LWNhcmQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXNEOztBQUV0RCxtQkFBbUIsZ0VBQWdCO0FBQ25DLGFBQWEsbUVBQW1FO0FBQ2hGLGFBQWEsc0RBQXNEO0FBQ25FOztBQUVpQztBQUNqQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL2NyZWRpdC1jYXJkLmpzPzQ1MDYiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZSBsdWNpZGUtcmVhY3QgdjAuMzAzLjAgLSBJU0NcbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBJU0MgbGljZW5zZS5cbiAqIFNlZSB0aGUgTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICovXG5cbmltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24uanMnO1xuXG5jb25zdCBDcmVkaXRDYXJkID0gY3JlYXRlTHVjaWRlSWNvbihcIkNyZWRpdENhcmRcIiwgW1xuICBbXCJyZWN0XCIsIHsgd2lkdGg6IFwiMjBcIiwgaGVpZ2h0OiBcIjE0XCIsIHg6IFwiMlwiLCB5OiBcIjVcIiwgcng6IFwiMlwiLCBrZXk6IFwieW55cDh6XCIgfV0sXG4gIFtcImxpbmVcIiwgeyB4MTogXCIyXCIsIHgyOiBcIjIyXCIsIHkxOiBcIjEwXCIsIHkyOiBcIjEwXCIsIGtleTogXCIxYjN2bW9cIiB9XVxuXSk7XG5cbmV4cG9ydCB7IENyZWRpdENhcmQgYXMgZGVmYXVsdCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y3JlZGl0LWNhcmQuanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/dashboard/sidebar.tsx":
/*!******************************************!*\
  !*** ./components/dashboard/sidebar.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: function() { return /* binding */ Sidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_CreditCard_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronLeft,ChevronRight,CreditCard,Crown,FileCheck,FileText,LayoutDashboard,Package,Receipt,Settings,Shield,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_CreditCard_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronLeft,ChevronRight,CreditCard,Crown,FileCheck,FileText,LayoutDashboard,Package,Receipt,Settings,Shield,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_CreditCard_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronLeft,ChevronRight,CreditCard,Crown,FileCheck,FileText,LayoutDashboard,Package,Receipt,Settings,Shield,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_CreditCard_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronLeft,ChevronRight,CreditCard,Crown,FileCheck,FileText,LayoutDashboard,Package,Receipt,Settings,Shield,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_CreditCard_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronLeft,ChevronRight,CreditCard,Crown,FileCheck,FileText,LayoutDashboard,Package,Receipt,Settings,Shield,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/receipt.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_CreditCard_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronLeft,ChevronRight,CreditCard,Crown,FileCheck,FileText,LayoutDashboard,Package,Receipt,Settings,Shield,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_CreditCard_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronLeft,ChevronRight,CreditCard,Crown,FileCheck,FileText,LayoutDashboard,Package,Receipt,Settings,Shield,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_CreditCard_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronLeft,ChevronRight,CreditCard,Crown,FileCheck,FileText,LayoutDashboard,Package,Receipt,Settings,Shield,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_CreditCard_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronLeft,ChevronRight,CreditCard,Crown,FileCheck,FileText,LayoutDashboard,Package,Receipt,Settings,Shield,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_CreditCard_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronLeft,ChevronRight,CreditCard,Crown,FileCheck,FileText,LayoutDashboard,Package,Receipt,Settings,Shield,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_CreditCard_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronLeft,ChevronRight,CreditCard,Crown,FileCheck,FileText,LayoutDashboard,Package,Receipt,Settings,Shield,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_CreditCard_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronLeft,ChevronRight,CreditCard,Crown,FileCheck,FileText,LayoutDashboard,Package,Receipt,Settings,Shield,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_CreditCard_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronLeft,ChevronRight,CreditCard,Crown,FileCheck,FileText,LayoutDashboard,Package,Receipt,Settings,Shield,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_CreditCard_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronLeft,ChevronRight,CreditCard,Crown,FileCheck,FileText,LayoutDashboard,Package,Receipt,Settings,Shield,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_CreditCard_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronLeft,ChevronRight,CreditCard,Crown,FileCheck,FileText,LayoutDashboard,Package,Receipt,Settings,Shield,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst menuItems = [\n    {\n        title: \"Main\",\n        items: [\n            {\n                title: \"Dashboard\",\n                href: \"/dashboard\",\n                icon: _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_CreditCard_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n            },\n            {\n                title: \"Customers\",\n                href: \"/dashboard/customers\",\n                icon: _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_CreditCard_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n            },\n            {\n                title: \"Leads\",\n                href: \"/dashboard/leads\",\n                icon: _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_CreditCard_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n            }\n        ]\n    },\n    {\n        title: \"Documents\",\n        items: [\n            {\n                title: \"Quotations\",\n                href: \"/dashboard/quotations\",\n                icon: _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_CreditCard_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n            },\n            {\n                title: \"Invoices\",\n                href: \"/dashboard/invoices\",\n                icon: _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_CreditCard_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n            },\n            {\n                title: \"Contracts\",\n                href: \"/dashboard/contracts\",\n                icon: _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_CreditCard_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n            },\n            {\n                title: \"Payments\",\n                href: \"/dashboard/payments\",\n                icon: _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_CreditCard_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n            }\n        ]\n    },\n    {\n        title: \"Inventory\",\n        items: [\n            {\n                title: \"Items\",\n                href: \"/dashboard/items\",\n                icon: _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_CreditCard_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n            }\n        ]\n    },\n    {\n        title: \"Analytics\",\n        items: [\n            {\n                title: \"Reports\",\n                href: \"/dashboard/reports\",\n                icon: _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_CreditCard_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n            }\n        ]\n    },\n    {\n        title: \"Account\",\n        items: [\n            {\n                title: \"Subscription\",\n                href: \"/dashboard/subscription\",\n                icon: _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_CreditCard_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                badge: \"PRO\",\n                badgeVariant: \"secondary\"\n            },\n            {\n                title: \"Settings\",\n                href: \"/dashboard/settings\",\n                icon: _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_CreditCard_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"]\n            }\n        ]\n    }\n];\nconst superAdminItems = [\n    {\n        title: \"Super Admin\",\n        items: [\n            {\n                title: \"Admin Panel\",\n                href: \"/super-admin\",\n                icon: _barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_CreditCard_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                badge: \"ADMIN\",\n                badgeVariant: \"destructive\"\n            }\n        ]\n    }\n];\nfunction Sidebar(param) {\n    let { user, collapsed = false, onToggle, className } = param;\n    var _user_name_charAt, _user_name, _user_email_charAt, _user_email, _user_company;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    // Combine menu items with super admin items if user is super admin\n    const allMenuItems = (user === null || user === void 0 ? void 0 : user.role) === \"SUPER_ADMIN\" ? [\n        ...menuItems,\n        ...superAdminItems\n    ] : menuItems;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex h-full flex-col bg-gray-900 text-white transition-all duration-300\", collapsed ? \"w-16\" : \"w-64\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-16 items-center justify-between px-4 border-b border-gray-800\",\n                children: [\n                    !collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_CreditCard_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                className: \"h-6 w-6 text-blue-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-semibold\",\n                                children: \"Business SaaS\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 11\n                    }, this),\n                    onToggle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"ghost\",\n                        size: \"icon\",\n                        onClick: onToggle,\n                        className: \"text-gray-400 hover:text-white hover:bg-gray-800\",\n                        children: collapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_CreditCard_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 26\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronLeft_ChevronRight_CreditCard_Crown_FileCheck_FileText_LayoutDashboard_Package_Receipt_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 65\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this),\n            user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.Avatar, {\n                            className: \"h-8 w-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarImage, {\n                                    src: user.image || \"\",\n                                    alt: user.name || \"\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarFallback, {\n                                    className: \"bg-blue-600 text-white\",\n                                    children: ((_user_name = user.name) === null || _user_name === void 0 ? void 0 : (_user_name_charAt = _user_name.charAt(0)) === null || _user_name_charAt === void 0 ? void 0 : _user_name_charAt.toUpperCase()) || ((_user_email = user.email) === null || _user_email === void 0 ? void 0 : (_user_email_charAt = _user_email.charAt(0)) === null || _user_email_charAt === void 0 ? void 0 : _user_email_charAt.toUpperCase()) || \"U\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 13\n                        }, this),\n                        !collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium text-white truncate\",\n                                    children: user.name || user.email\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400 truncate\",\n                                    children: ((_user_company = user.company) === null || _user_company === void 0 ? void 0 : _user_company.name) || user.role || \"User\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                lineNumber: 183,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex-1 overflow-y-auto py-4\",\n                children: allMenuItems.map((group)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            !collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"px-4 mb-2 text-xs font-semibold text-gray-400 uppercase tracking-wider\",\n                                children: group.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-1 px-2\",\n                                children: group.items.map((item)=>{\n                                    const isActive = pathname === item.href;\n                                    const Icon = item.icon;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: item.href,\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex items-center px-2 py-2 text-sm rounded-md transition-colors\", isActive ? \"bg-blue-600 text-white\" : \"text-gray-300 hover:bg-gray-800 hover:text-white\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"h-5 w-5\", collapsed ? \"mx-auto\" : \"mr-3\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 23\n                                                }, this),\n                                                !collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex-1\",\n                                                            children: item.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            variant: item.badgeVariant || \"default\",\n                                                            className: \"ml-2\",\n                                                            children: item.badge\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                                            lineNumber: 235,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, item.href, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, group.title, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                lineNumber: 206,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\dashboard\\\\sidebar.tsx\",\n        lineNumber: 154,\n        columnNumber: 5\n    }, this);\n}\n_s(Sidebar, \"xbyQPtUVMO7MNj7WjJlpdWqRcTo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/dashboard/sidebar.tsx\n"));

/***/ })

});