import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Lead scoring criteria and weights
const SCORING_CRITERIA = {
  // Company Information (30 points max)
  companySize: {
    'STARTUP': 5,
    'SMALL': 10,
    'MEDIUM': 20,
    'LARGE': 25,
    'ENTERPRISE': 30
  },
  industry: {
    'TECHNOLOGY': 25,
    'FINANCE': 20,
    'HEALTHCARE': 20,
    'MANUFACTURING': 15,
    'RETAIL': 10,
    'OTHER': 5
  },
  
  // Contact Information (20 points max)
  hasPhone: 10,
  hasWebsite: 5,
  hasCompanyEmail: 5, // vs personal email
  
  // Engagement (25 points max)
  activityCount: {
    0: 0,
    1: 5,
    2: 10,
    3: 15,
    4: 20,
    5: 25
  },
  recentActivity: 10, // within last 7 days
  
  // Budget & Timeline (25 points max)
  hasBudget: 15,
  hasTimeline: 10
}

// Validation schema for manual score update
const scoreUpdateSchema = z.object({
  manualScore: z.number().min(0).max(100).optional(),
  reason: z.string().optional(),
  qualificationNotes: z.string().optional()
})

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get lead with related data for scoring
    const lead = await prisma.lead.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId
      },
      include: {
        activities: {
          orderBy: { createdAt: 'desc' },
          take: 10
        },
        _count: {
          select: {
            activities: true,
            leadNotes: true
          }
        }
      }
    })

    if (!lead) {
      return NextResponse.json({ error: 'Lead not found' }, { status: 404 })
    }

    // Calculate automated score
    const automatedScore = calculateLeadScore(lead)
    
    // Get score history
    const scoreHistory = await prisma.leadScoreHistory.findMany({
      where: { leadId: params.id },
      orderBy: { createdAt: 'desc' },
      take: 20,
      include: {
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    // Determine lead temperature
    const temperature = getLeadTemperature(lead.score)
    
    // Get qualification status
    const qualificationStatus = getQualificationStatus(lead)

    return NextResponse.json({
      leadId: params.id,
      currentScore: lead.score,
      automatedScore,
      temperature,
      qualificationStatus,
      scoreBreakdown: getScoreBreakdown(lead),
      scoreHistory,
      recommendations: getScoreRecommendations(lead, automatedScore)
    })

  } catch (error) {
    console.error('Error fetching lead score:', error)
    return NextResponse.json(
      { error: 'Failed to fetch lead score' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = scoreUpdateSchema.parse(body)

    // Check if lead exists
    const existingLead = await prisma.lead.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId
      }
    })

    if (!existingLead) {
      return NextResponse.json({ error: 'Lead not found' }, { status: 404 })
    }

    let newScore = existingLead.score

    // Update score if manual score provided
    if (validatedData.manualScore !== undefined) {
      newScore = validatedData.manualScore

      // Record score history
      await prisma.leadScoreHistory.create({
        data: {
          leadId: params.id,
          previousScore: existingLead.score,
          newScore: newScore,
          changeReason: validatedData.reason || 'Manual score update',
          isManual: true,
          createdById: session.user.id,
          companyId: session.user.companyId
        }
      })

      // Update lead score
      await prisma.lead.update({
        where: { id: params.id },
        data: { 
          score: newScore,
          updatedAt: new Date()
        }
      })
    }

    // Add qualification notes if provided
    if (validatedData.qualificationNotes) {
      await prisma.leadNote.create({
        data: {
          leadId: params.id,
          title: 'Qualification Notes',
          content: validatedData.qualificationNotes,
          isPrivate: false,
          createdById: session.user.id,
          companyId: session.user.companyId
        }
      })
    }

    return NextResponse.json({ 
      message: 'Lead score updated successfully',
      newScore 
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating lead score:', error)
    return NextResponse.json(
      { error: 'Failed to update lead score' },
      { status: 500 }
    )
  }
}

// Helper functions
function calculateLeadScore(lead: any): number {
  let score = 0

  // Company size scoring
  if (lead.companySize && SCORING_CRITERIA.companySize[lead.companySize]) {
    score += SCORING_CRITERIA.companySize[lead.companySize]
  }

  // Industry scoring
  if (lead.industry && SCORING_CRITERIA.industry[lead.industry]) {
    score += SCORING_CRITERIA.industry[lead.industry]
  }

  // Contact information scoring
  if (lead.phone) score += SCORING_CRITERIA.hasPhone
  if (lead.website) score += SCORING_CRITERIA.hasWebsite
  if (lead.email && lead.companyName && lead.email.includes(lead.companyName.toLowerCase())) {
    score += SCORING_CRITERIA.hasCompanyEmail
  }

  // Activity scoring
  const activityCount = Math.min(lead._count.activities, 5)
  score += SCORING_CRITERIA.activityCount[activityCount] || 0

  // Recent activity bonus
  if (lead.activities.length > 0) {
    const lastActivity = new Date(lead.activities[0].createdAt)
    const daysSinceLastActivity = (Date.now() - lastActivity.getTime()) / (1000 * 60 * 60 * 24)
    if (daysSinceLastActivity <= 7) {
      score += SCORING_CRITERIA.recentActivity
    }
  }

  // Budget and timeline scoring
  if (lead.budget && lead.budget > 0) score += SCORING_CRITERIA.hasBudget
  if (lead.timeline) score += SCORING_CRITERIA.hasTimeline

  return Math.min(score, 100) // Cap at 100
}

function getScoreBreakdown(lead: any) {
  const breakdown = []

  // Company information
  if (lead.companySize) {
    breakdown.push({
      category: 'Company Size',
      points: SCORING_CRITERIA.companySize[lead.companySize] || 0,
      maxPoints: 30,
      description: `${lead.companySize} company`
    })
  }

  if (lead.industry) {
    breakdown.push({
      category: 'Industry',
      points: SCORING_CRITERIA.industry[lead.industry] || 0,
      maxPoints: 25,
      description: `${lead.industry} industry`
    })
  }

  // Contact information
  let contactPoints = 0
  if (lead.phone) contactPoints += SCORING_CRITERIA.hasPhone
  if (lead.website) contactPoints += SCORING_CRITERIA.hasWebsite
  if (lead.email && lead.companyName && lead.email.includes(lead.companyName.toLowerCase())) {
    contactPoints += SCORING_CRITERIA.hasCompanyEmail
  }

  breakdown.push({
    category: 'Contact Information',
    points: contactPoints,
    maxPoints: 20,
    description: 'Phone, website, company email'
  })

  // Engagement
  const activityCount = Math.min(lead._count.activities, 5)
  let engagementPoints = SCORING_CRITERIA.activityCount[activityCount] || 0
  
  if (lead.activities.length > 0) {
    const lastActivity = new Date(lead.activities[0].createdAt)
    const daysSinceLastActivity = (Date.now() - lastActivity.getTime()) / (1000 * 60 * 60 * 24)
    if (daysSinceLastActivity <= 7) {
      engagementPoints += SCORING_CRITERIA.recentActivity
    }
  }

  breakdown.push({
    category: 'Engagement',
    points: engagementPoints,
    maxPoints: 25,
    description: `${lead._count.activities} activities, recent engagement`
  })

  // Budget & Timeline
  let budgetPoints = 0
  if (lead.budget && lead.budget > 0) budgetPoints += SCORING_CRITERIA.hasBudget
  if (lead.timeline) budgetPoints += SCORING_CRITERIA.hasTimeline

  breakdown.push({
    category: 'Budget & Timeline',
    points: budgetPoints,
    maxPoints: 25,
    description: 'Budget and timeline information'
  })

  return breakdown
}

function getLeadTemperature(score: number): 'HOT' | 'WARM' | 'COLD' {
  if (score >= 70) return 'HOT'
  if (score >= 40) return 'WARM'
  return 'COLD'
}

function getQualificationStatus(lead: any) {
  const criteria = {
    hasContactInfo: !!(lead.phone || lead.email),
    hasCompanyInfo: !!lead.companyName,
    hasBudget: !!(lead.budget && lead.budget > 0),
    hasTimeline: !!lead.timeline,
    hasEngagement: lead._count.activities > 0
  }

  const qualifiedCount = Object.values(criteria).filter(Boolean).length
  const totalCriteria = Object.keys(criteria).length

  return {
    isQualified: qualifiedCount >= 3,
    qualificationScore: Math.round((qualifiedCount / totalCriteria) * 100),
    criteria,
    missingCriteria: Object.entries(criteria)
      .filter(([_, value]) => !value)
      .map(([key]) => key)
  }
}

function getScoreRecommendations(lead: any, automatedScore: number) {
  const recommendations = []

  if (!lead.phone) {
    recommendations.push({
      type: 'CONTACT_INFO',
      priority: 'HIGH',
      message: 'Add phone number to increase lead score by 10 points',
      action: 'Add phone number'
    })
  }

  if (!lead.website) {
    recommendations.push({
      type: 'CONTACT_INFO',
      priority: 'MEDIUM',
      message: 'Add company website to increase lead score by 5 points',
      action: 'Add website'
    })
  }

  if (!lead.budget || lead.budget === 0) {
    recommendations.push({
      type: 'QUALIFICATION',
      priority: 'HIGH',
      message: 'Qualify budget to increase lead score by 15 points',
      action: 'Qualify budget'
    })
  }

  if (!lead.timeline) {
    recommendations.push({
      type: 'QUALIFICATION',
      priority: 'HIGH',
      message: 'Determine timeline to increase lead score by 10 points',
      action: 'Determine timeline'
    })
  }

  if (lead._count.activities === 0) {
    recommendations.push({
      type: 'ENGAGEMENT',
      priority: 'URGENT',
      message: 'No activities recorded. Schedule a call or meeting.',
      action: 'Schedule activity'
    })
  }

  return recommendations
}
