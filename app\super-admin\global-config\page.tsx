'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { redirect } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { 
  Globe, 
  Save, 
  RefreshCw, 
  Settings,
  Shield,
  Mail,
  Database,
  Clock,
  Users,
  Building,
  CreditCard,
  Bell,
  Lock,
  Eye,
  EyeOff
} from 'lucide-react'
import { toast } from 'react-hot-toast'

interface GlobalConfig {
  // Application Settings
  appName: string
  appDescription: string
  appUrl: string
  supportEmail: string
  companyName: string
  companyAddress: string
  companyPhone: string

  // Branding Settings
  logoUrl: string
  faviconUrl: string
  primaryColor: string
  secondaryColor: string
  accentColor: string
  backgroundColor: string
  textColor: string
  theme: string // 'light' | 'dark' | 'auto'
  fontFamily: string
  customCss: string

  // System Settings
  timezone: string
  dateFormat: string
  currency: string
  language: string
  
  // Feature Flags
  enableRegistration: boolean
  enableTrials: boolean
  enableMultiTenant: boolean
  enableApiAccess: boolean
  enableWebhooks: boolean
  
  // Security Settings
  sessionTimeout: number
  passwordMinLength: number
  requireTwoFactor: boolean
  allowSocialLogin: boolean
  
  // Email Settings
  emailProvider: string
  smtpHost: string
  smtpPort: number
  smtpUsername: string
  smtpPassword: string
  smtpSecure: boolean
  
  // Limits
  maxUsersPerCompany: number
  maxCompaniesPerUser: number
  defaultStorageLimit: number
  
  // Maintenance
  maintenanceMode: boolean
  maintenanceMessage: string
}

export default function GlobalConfigPage() {
  const { data: session, status } = useSession()
  const [config, setConfig] = useState<GlobalConfig>({
    appName: 'SaaS Platform',
    appDescription: 'Modern SaaS application for business management',
    appUrl: 'https://yourapp.com',
    supportEmail: '<EMAIL>',
    companyName: 'Your Company',
    companyAddress: '123 Business St, City, Country',
    companyPhone: '+****************',
    logoUrl: '',
    faviconUrl: '',
    primaryColor: '#3b82f6',
    secondaryColor: '#64748b',
    accentColor: '#10b981',
    backgroundColor: '#ffffff',
    textColor: '#1f2937',
    theme: 'light',
    fontFamily: 'Inter, sans-serif',
    customCss: '',
    timezone: 'UTC',
    dateFormat: 'MM/DD/YYYY',
    currency: 'USD',
    language: 'en',
    enableRegistration: true,
    enableTrials: true,
    enableMultiTenant: true,
    enableApiAccess: true,
    enableWebhooks: true,
    sessionTimeout: 24,
    passwordMinLength: 8,
    requireTwoFactor: false,
    allowSocialLogin: true,
    emailProvider: 'smtp',
    smtpHost: '',
    smtpPort: 587,
    smtpUsername: '',
    smtpPassword: '',
    smtpSecure: true,
    maxUsersPerCompany: 100,
    maxCompaniesPerUser: 5,
    defaultStorageLimit: 5,
    maintenanceMode: false,
    maintenanceMessage: 'System is under maintenance. Please check back later.'
  })
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [showPassword, setShowPassword] = useState(false)

  useEffect(() => {
    fetchConfig()
  }, [])

  const fetchConfig = async () => {
    try {
      const response = await fetch('/api/super-admin/global-config')
      const data = await response.json()

      if (data.success && data.config) {
        setConfig(prevConfig => ({ ...prevConfig, ...data.config }))
      }
    } catch (error) {
      console.error('Error fetching config:', error)
      toast.error('Failed to load configuration')
    } finally {
      setLoading(false)
    }
  }

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (status === 'unauthenticated') {
    redirect('/auth/signin')
  }

  if (session?.user?.role !== 'SUPER_ADMIN') {
    redirect('/dashboard')
  }



  useEffect(() => {
    fetchConfig()
  }, [])

  const updateConfig = (key: keyof GlobalConfig, value: any) => {
    setConfig(prev => ({ ...prev, [key]: value }))
  }

  const saveConfig = async () => {
    setSaving(true)
    try {
      const response = await fetch('/api/super-admin/global-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Configuration saved successfully!')
        // Refresh branding context if branding settings were updated
        window.location.reload()
      } else {
        toast.error(data.error || 'Failed to save configuration')
      }
    } catch (error) {
      console.error('Error saving config:', error)
      toast.error('Failed to save configuration')
    } finally {
      setSaving(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <div className="flex items-center space-x-3">
            <Globe className="h-8 w-8 text-blue-600" />
            <h1 className="text-3xl font-bold text-gray-900">Global Configuration</h1>
          </div>
          <p className="text-gray-500 mt-1">Manage system-wide settings and configuration</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={fetchConfig} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button onClick={saveConfig} disabled={saving}>
            <Save className={`h-4 w-4 mr-2 ${saving ? 'animate-spin' : ''}`} />
            Save Changes
          </Button>
        </div>
      </div>

      {/* Configuration Tabs */}
      <Tabs defaultValue="application" className="space-y-6">
        <TabsList className="grid w-full grid-cols-7">
          <TabsTrigger value="application">Application</TabsTrigger>
          <TabsTrigger value="branding">Branding</TabsTrigger>
          <TabsTrigger value="system">System</TabsTrigger>
          <TabsTrigger value="features">Features</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="email">Email</TabsTrigger>
          <TabsTrigger value="limits">Limits</TabsTrigger>
        </TabsList>

        {/* Application Settings */}
        <TabsContent value="application">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Building className="h-5 w-5 mr-2" />
                Application Settings
              </CardTitle>
              <CardDescription>
                Basic application information and company details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="appName">Application Name</Label>
                  <Input
                    id="appName"
                    value={config.appName}
                    onChange={(e) => updateConfig('appName', e.target.value)}
                    placeholder="Your SaaS App"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="appUrl">Application URL</Label>
                  <Input
                    id="appUrl"
                    value={config.appUrl}
                    onChange={(e) => updateConfig('appUrl', e.target.value)}
                    placeholder="https://yourapp.com"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="appDescription">Application Description</Label>
                <Textarea
                  id="appDescription"
                  value={config.appDescription}
                  onChange={(e) => updateConfig('appDescription', e.target.value)}
                  placeholder="Brief description of your application"
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="companyName">Company Name</Label>
                  <Input
                    id="companyName"
                    value={config.companyName}
                    onChange={(e) => updateConfig('companyName', e.target.value)}
                    placeholder="Your Company Inc."
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="supportEmail">Support Email</Label>
                  <Input
                    id="supportEmail"
                    type="email"
                    value={config.supportEmail}
                    onChange={(e) => updateConfig('supportEmail', e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="companyPhone">Company Phone</Label>
                  <Input
                    id="companyPhone"
                    value={config.companyPhone}
                    onChange={(e) => updateConfig('companyPhone', e.target.value)}
                    placeholder="+****************"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="companyAddress">Company Address</Label>
                  <Input
                    id="companyAddress"
                    value={config.companyAddress}
                    onChange={(e) => updateConfig('companyAddress', e.target.value)}
                    placeholder="123 Business St, City, Country"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Branding Settings */}
        <TabsContent value="branding">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Building className="h-5 w-5 mr-2" />
                Branding & Appearance
              </CardTitle>
              <CardDescription>
                Customize your application's visual identity and branding
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Logo and Favicon */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="logoUrl">Logo URL</Label>
                  <Input
                    id="logoUrl"
                    value={config.logoUrl}
                    onChange={(e) => updateConfig('logoUrl', e.target.value)}
                    placeholder="https://example.com/logo.png"
                  />
                  <p className="text-xs text-gray-500">Recommended: 200x50px PNG or SVG</p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="faviconUrl">Favicon URL</Label>
                  <Input
                    id="faviconUrl"
                    value={config.faviconUrl}
                    onChange={(e) => updateConfig('faviconUrl', e.target.value)}
                    placeholder="https://example.com/favicon.ico"
                  />
                  <p className="text-xs text-gray-500">Recommended: 32x32px ICO or PNG</p>
                </div>
              </div>

              {/* Color Scheme */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Color Scheme</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="primaryColor">Primary Color</Label>
                    <div className="flex space-x-2">
                      <Input
                        id="primaryColor"
                        type="color"
                        value={config.primaryColor}
                        onChange={(e) => updateConfig('primaryColor', e.target.value)}
                        className="w-16 h-10 p-1 border rounded"
                      />
                      <Input
                        value={config.primaryColor}
                        onChange={(e) => updateConfig('primaryColor', e.target.value)}
                        placeholder="#3b82f6"
                        className="flex-1"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="secondaryColor">Secondary Color</Label>
                    <div className="flex space-x-2">
                      <Input
                        id="secondaryColor"
                        type="color"
                        value={config.secondaryColor}
                        onChange={(e) => updateConfig('secondaryColor', e.target.value)}
                        className="w-16 h-10 p-1 border rounded"
                      />
                      <Input
                        value={config.secondaryColor}
                        onChange={(e) => updateConfig('secondaryColor', e.target.value)}
                        placeholder="#64748b"
                        className="flex-1"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="accentColor">Accent Color</Label>
                    <div className="flex space-x-2">
                      <Input
                        id="accentColor"
                        type="color"
                        value={config.accentColor}
                        onChange={(e) => updateConfig('accentColor', e.target.value)}
                        className="w-16 h-10 p-1 border rounded"
                      />
                      <Input
                        value={config.accentColor}
                        onChange={(e) => updateConfig('accentColor', e.target.value)}
                        placeholder="#10b981"
                        className="flex-1"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Theme and Typography */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="theme">Theme</Label>
                  <Select value={config.theme} onValueChange={(value) => updateConfig('theme', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select theme" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="light">Light</SelectItem>
                      <SelectItem value="dark">Dark</SelectItem>
                      <SelectItem value="auto">Auto (System)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="fontFamily">Font Family</Label>
                  <Select value={config.fontFamily} onValueChange={(value) => updateConfig('fontFamily', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select font" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Inter, sans-serif">Inter</SelectItem>
                      <SelectItem value="Roboto, sans-serif">Roboto</SelectItem>
                      <SelectItem value="Open Sans, sans-serif">Open Sans</SelectItem>
                      <SelectItem value="Lato, sans-serif">Lato</SelectItem>
                      <SelectItem value="Poppins, sans-serif">Poppins</SelectItem>
                      <SelectItem value="Montserrat, sans-serif">Montserrat</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Custom CSS */}
              <div className="space-y-2">
                <Label htmlFor="customCss">Custom CSS</Label>
                <Textarea
                  id="customCss"
                  value={config.customCss}
                  onChange={(e) => updateConfig('customCss', e.target.value)}
                  placeholder="/* Add your custom CSS here */
.custom-class {
  color: #333;
}"
                  rows={6}
                  className="font-mono text-sm"
                />
                <p className="text-xs text-gray-500">
                  Add custom CSS to override default styles. Use with caution.
                </p>
              </div>

              {/* Preview */}
              <div className="space-y-2">
                <Label>Preview</Label>
                <div
                  className="p-4 border rounded-lg"
                  style={{
                    backgroundColor: config.backgroundColor,
                    color: config.textColor,
                    fontFamily: config.fontFamily
                  }}
                >
                  <div className="flex items-center space-x-2 mb-2">
                    {config.logoUrl ? (
                      <img src={config.logoUrl} alt="Logo" className="h-8" />
                    ) : (
                      <div
                        className="w-8 h-8 rounded"
                        style={{ backgroundColor: config.primaryColor }}
                      ></div>
                    )}
                    <span className="font-bold">{config.appName}</span>
                  </div>
                  <button
                    className="px-4 py-2 rounded text-white text-sm"
                    style={{ backgroundColor: config.primaryColor }}
                  >
                    Primary Button
                  </button>
                  <button
                    className="px-4 py-2 rounded text-white text-sm ml-2"
                    style={{ backgroundColor: config.accentColor }}
                  >
                    Accent Button
                  </button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* System Settings */}
        <TabsContent value="system">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Settings className="h-5 w-5 mr-2" />
                System Settings
              </CardTitle>
              <CardDescription>
                Configure system-wide preferences and defaults
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="timezone">Default Timezone</Label>
                  <Select value={config.timezone} onValueChange={(value) => updateConfig('timezone', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="UTC">UTC</SelectItem>
                      <SelectItem value="America/New_York">Eastern Time</SelectItem>
                      <SelectItem value="America/Chicago">Central Time</SelectItem>
                      <SelectItem value="America/Denver">Mountain Time</SelectItem>
                      <SelectItem value="America/Los_Angeles">Pacific Time</SelectItem>
                      <SelectItem value="Europe/London">London</SelectItem>
                      <SelectItem value="Europe/Paris">Paris</SelectItem>
                      <SelectItem value="Asia/Tokyo">Tokyo</SelectItem>
                      <SelectItem value="Asia/Shanghai">Shanghai</SelectItem>
                      <SelectItem value="Asia/Kolkata">India</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="dateFormat">Date Format</Label>
                  <Select value={config.dateFormat} onValueChange={(value) => updateConfig('dateFormat', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="MM/DD/YYYY">MM/DD/YYYY</SelectItem>
                      <SelectItem value="DD/MM/YYYY">DD/MM/YYYY</SelectItem>
                      <SelectItem value="YYYY-MM-DD">YYYY-MM-DD</SelectItem>
                      <SelectItem value="DD-MM-YYYY">DD-MM-YYYY</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="currency">Default Currency</Label>
                  <Select value={config.currency} onValueChange={(value) => updateConfig('currency', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="USD">USD - US Dollar</SelectItem>
                      <SelectItem value="EUR">EUR - Euro</SelectItem>
                      <SelectItem value="GBP">GBP - British Pound</SelectItem>
                      <SelectItem value="JPY">JPY - Japanese Yen</SelectItem>
                      <SelectItem value="INR">INR - Indian Rupee</SelectItem>
                      <SelectItem value="CAD">CAD - Canadian Dollar</SelectItem>
                      <SelectItem value="AUD">AUD - Australian Dollar</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="language">Default Language</Label>
                  <Select value={config.language} onValueChange={(value) => updateConfig('language', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="en">English</SelectItem>
                      <SelectItem value="es">Spanish</SelectItem>
                      <SelectItem value="fr">French</SelectItem>
                      <SelectItem value="de">German</SelectItem>
                      <SelectItem value="it">Italian</SelectItem>
                      <SelectItem value="pt">Portuguese</SelectItem>
                      <SelectItem value="ja">Japanese</SelectItem>
                      <SelectItem value="ko">Korean</SelectItem>
                      <SelectItem value="zh">Chinese</SelectItem>
                      <SelectItem value="hi">Hindi</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Maintenance Mode */}
              <div className="space-y-4 p-4 border rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="maintenanceMode" className="text-base font-medium">
                      Maintenance Mode
                    </Label>
                    <p className="text-sm text-gray-500">
                      Enable to put the application in maintenance mode
                    </p>
                  </div>
                  <Switch
                    id="maintenanceMode"
                    checked={config.maintenanceMode}
                    onCheckedChange={(checked) => updateConfig('maintenanceMode', checked)}
                  />
                </div>
                {config.maintenanceMode && (
                  <div className="space-y-2">
                    <Label htmlFor="maintenanceMessage">Maintenance Message</Label>
                    <Textarea
                      id="maintenanceMessage"
                      value={config.maintenanceMessage}
                      onChange={(e) => updateConfig('maintenanceMessage', e.target.value)}
                      placeholder="System is under maintenance..."
                      rows={2}
                    />
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Feature Flags */}
        <TabsContent value="features">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Shield className="h-5 w-5 mr-2" />
                Feature Flags
              </CardTitle>
              <CardDescription>
                Enable or disable application features
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <Label htmlFor="enableRegistration" className="text-base font-medium">
                        User Registration
                      </Label>
                      <p className="text-sm text-gray-500">
                        Allow new users to register accounts
                      </p>
                    </div>
                    <Switch
                      id="enableRegistration"
                      checked={config.enableRegistration}
                      onCheckedChange={(checked) => updateConfig('enableRegistration', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <Label htmlFor="enableTrials" className="text-base font-medium">
                        Free Trials
                      </Label>
                      <p className="text-sm text-gray-500">
                        Enable free trial periods for new users
                      </p>
                    </div>
                    <Switch
                      id="enableTrials"
                      checked={config.enableTrials}
                      onCheckedChange={(checked) => updateConfig('enableTrials', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <Label htmlFor="enableMultiTenant" className="text-base font-medium">
                        Multi-Tenant
                      </Label>
                      <p className="text-sm text-gray-500">
                        Allow users to manage multiple companies
                      </p>
                    </div>
                    <Switch
                      id="enableMultiTenant"
                      checked={config.enableMultiTenant}
                      onCheckedChange={(checked) => updateConfig('enableMultiTenant', checked)}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <Label htmlFor="enableApiAccess" className="text-base font-medium">
                        API Access
                      </Label>
                      <p className="text-sm text-gray-500">
                        Enable REST API access for integrations
                      </p>
                    </div>
                    <Switch
                      id="enableApiAccess"
                      checked={config.enableApiAccess}
                      onCheckedChange={(checked) => updateConfig('enableApiAccess', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <Label htmlFor="enableWebhooks" className="text-base font-medium">
                        Webhooks
                      </Label>
                      <p className="text-sm text-gray-500">
                        Enable webhook notifications for events
                      </p>
                    </div>
                    <Switch
                      id="enableWebhooks"
                      checked={config.enableWebhooks}
                      onCheckedChange={(checked) => updateConfig('enableWebhooks', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <Label htmlFor="allowSocialLogin" className="text-base font-medium">
                        Social Login
                      </Label>
                      <p className="text-sm text-gray-500">
                        Allow login with Google, GitHub, etc.
                      </p>
                    </div>
                    <Switch
                      id="allowSocialLogin"
                      checked={config.allowSocialLogin}
                      onCheckedChange={(checked) => updateConfig('allowSocialLogin', checked)}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Security Settings */}
        <TabsContent value="security">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Lock className="h-5 w-5 mr-2" />
                Security Settings
              </CardTitle>
              <CardDescription>
                Configure security policies and authentication settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="sessionTimeout">Session Timeout (hours)</Label>
                  <Input
                    id="sessionTimeout"
                    type="number"
                    value={config.sessionTimeout}
                    onChange={(e) => updateConfig('sessionTimeout', parseInt(e.target.value) || 24)}
                    min="1"
                    max="168"
                  />
                  <p className="text-xs text-gray-500">Maximum session duration before auto-logout</p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="passwordMinLength">Minimum Password Length</Label>
                  <Input
                    id="passwordMinLength"
                    type="number"
                    value={config.passwordMinLength}
                    onChange={(e) => updateConfig('passwordMinLength', parseInt(e.target.value) || 8)}
                    min="6"
                    max="32"
                  />
                  <p className="text-xs text-gray-500">Minimum characters required for passwords</p>
                </div>
              </div>

              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <Label htmlFor="requireTwoFactor" className="text-base font-medium">
                    Require Two-Factor Authentication
                  </Label>
                  <p className="text-sm text-gray-500">
                    Force all users to enable 2FA for enhanced security
                  </p>
                </div>
                <Switch
                  id="requireTwoFactor"
                  checked={config.requireTwoFactor}
                  onCheckedChange={(checked) => updateConfig('requireTwoFactor', checked)}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Email Settings */}
        <TabsContent value="email">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Mail className="h-5 w-5 mr-2" />
                Email Configuration
              </CardTitle>
              <CardDescription>
                Configure SMTP settings for transactional emails
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="emailProvider">Email Provider</Label>
                <Select value={config.emailProvider} onValueChange={(value) => updateConfig('emailProvider', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="smtp">Custom SMTP</SelectItem>
                    <SelectItem value="sendgrid">SendGrid</SelectItem>
                    <SelectItem value="mailgun">Mailgun</SelectItem>
                    <SelectItem value="ses">Amazon SES</SelectItem>
                    <SelectItem value="postmark">Postmark</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {config.emailProvider === 'smtp' && (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="smtpHost">SMTP Host</Label>
                      <Input
                        id="smtpHost"
                        value={config.smtpHost}
                        onChange={(e) => updateConfig('smtpHost', e.target.value)}
                        placeholder="smtp.gmail.com"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="smtpPort">SMTP Port</Label>
                      <Input
                        id="smtpPort"
                        type="number"
                        value={config.smtpPort}
                        onChange={(e) => updateConfig('smtpPort', parseInt(e.target.value) || 587)}
                        placeholder="587"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="smtpUsername">SMTP Username</Label>
                      <Input
                        id="smtpUsername"
                        value={config.smtpUsername}
                        onChange={(e) => updateConfig('smtpUsername', e.target.value)}
                        placeholder="<EMAIL>"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="smtpPassword">SMTP Password</Label>
                      <div className="relative">
                        <Input
                          id="smtpPassword"
                          type={showPassword ? 'text' : 'password'}
                          value={config.smtpPassword}
                          onChange={(e) => updateConfig('smtpPassword', e.target.value)}
                          placeholder="your-app-password"
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </Button>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <Label htmlFor="smtpSecure" className="text-base font-medium">
                        Use TLS/SSL
                      </Label>
                      <p className="text-sm text-gray-500">
                        Enable secure connection for SMTP
                      </p>
                    </div>
                    <Switch
                      id="smtpSecure"
                      checked={config.smtpSecure}
                      onCheckedChange={(checked) => updateConfig('smtpSecure', checked)}
                    />
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Limits */}
        <TabsContent value="limits">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="h-5 w-5 mr-2" />
                System Limits
              </CardTitle>
              <CardDescription>
                Configure default limits and quotas
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="maxUsersPerCompany">Max Users per Company</Label>
                  <Input
                    id="maxUsersPerCompany"
                    type="number"
                    value={config.maxUsersPerCompany}
                    onChange={(e) => updateConfig('maxUsersPerCompany', parseInt(e.target.value) || 100)}
                    min="1"
                  />
                  <p className="text-xs text-gray-500">Default limit for new companies</p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="maxCompaniesPerUser">Max Companies per User</Label>
                  <Input
                    id="maxCompaniesPerUser"
                    type="number"
                    value={config.maxCompaniesPerUser}
                    onChange={(e) => updateConfig('maxCompaniesPerUser', parseInt(e.target.value) || 5)}
                    min="1"
                  />
                  <p className="text-xs text-gray-500">How many companies a user can join</p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="defaultStorageLimit">Default Storage Limit (GB)</Label>
                  <Input
                    id="defaultStorageLimit"
                    type="number"
                    value={config.defaultStorageLimit}
                    onChange={(e) => updateConfig('defaultStorageLimit', parseInt(e.target.value) || 5)}
                    min="1"
                  />
                  <p className="text-xs text-gray-500">Default storage quota for new companies</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
