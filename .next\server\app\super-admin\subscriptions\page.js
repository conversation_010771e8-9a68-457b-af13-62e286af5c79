(()=>{var e={};e.id=9136,e.ids=[9136],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},49119:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>p,tree:()=>d});var t=a(50482),l=a(69108),r=a(62563),i=a.n(r),n=a(68300),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);a.d(s,c);let d=["",{children:["super-admin",{children:["subscriptions",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,76845)),"C:\\proj\\nextjs-saas\\app\\super-admin\\subscriptions\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,11285)),"C:\\proj\\nextjs-saas\\app\\super-admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,59504)),"C:\\proj\\nextjs-saas\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,69361,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\proj\\nextjs-saas\\app\\super-admin\\subscriptions\\page.tsx"],x="/super-admin/subscriptions/page",m={require:a,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:l.x.APP_PAGE,page:"/super-admin/subscriptions/page",pathname:"/super-admin/subscriptions",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},70395:(e,s,a)=>{Promise.resolve().then(a.bind(a,48722))},48722:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>R});var t=a(95344),l=a(3729),r=a(47674),i=a(22254),n=a(61351),c=a(16212),d=a(92549),o=a(69436),x=a(81036),m=a(17470),p=a(16802),u=a(1586),h=a(7060),j=a(25545),f=a(73229),g=a(45961),y=a(85674),b=a(33733),N=a(51838),v=a(46064),w=a(50340),C=a(1750),S=a(77402),D=a(28765),E=a(17418),P=a(53148),I=a(75695),Z=a(62093);function R(){let{data:e,status:s}=(0,r.useSession)(),[a,R]=(0,l.useState)([]),[k,A]=(0,l.useState)(null),[_,T]=(0,l.useState)(!0),[z,F]=(0,l.useState)(""),[L,M]=(0,l.useState)("all"),[Y,O]=(0,l.useState)("all"),[Q,V]=(0,l.useState)(1),[q,U]=(0,l.useState)(1),[B,$]=(0,l.useState)(!1),[G,H]=(0,l.useState)([]),[X,J]=(0,l.useState)([]),[K,W]=(0,l.useState)({companyId:"",planId:"",status:"ACTIVE",billingCycle:"MONTHLY",amount:0,startDate:new Date().toISOString().split("T")[0],endDate:"",trialEndDate:"",stripeSubscriptionId:"",stripeCustomerId:""});if("loading"===s)return t.jsx("div",{className:"min-h-screen flex items-center justify-center",children:t.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})});"unauthenticated"===s&&(0,i.redirect)("/auth/signin"),e?.user?.role!=="SUPER_ADMIN"&&(0,i.redirect)("/dashboard");let ee=async()=>{try{T(!0);let e=new URLSearchParams({page:Q.toString(),limit:"20",...z&&{search:z},...L&&"all"!==L&&{status:L},...Y&&"all"!==Y&&{plan:Y}}),s=await fetch(`/api/super-admin/subscriptions?${e}`);if(!s.ok)throw Error("Failed to fetch subscriptions");let a=await s.json();R(a.subscriptions),A(a.stats),U(a.pagination.pages)}catch(e){console.error("Error fetching subscriptions:",e)}finally{T(!1)}},es=async()=>{try{let e=await fetch("/api/super-admin/companies?limit=1000"),s=await e.json();s.companies&&H(s.companies)}catch(e){console.error("Error fetching companies:",e)}},ea=async()=>{try{let e=await fetch("/api/pricing-plans"),s=await e.json();s.success&&J(s.data)}catch(e){console.error("Error fetching pricing plans:",e)}};(0,l.useEffect)(()=>{ee()},[Q,z,L,Y]),(0,l.useEffect)(()=>{B&&(es(),ea())},[B]);let et=e=>{let s={ACTIVE:{variant:"default",icon:h.Z,color:"text-green-600"},TRIAL:{variant:"secondary",icon:j.Z,color:"text-blue-600"},CANCELLED:{variant:"destructive",icon:f.Z,color:"text-red-600"},EXPIRED:{variant:"outline",icon:g.Z,color:"text-orange-600"},SUSPENDED:{variant:"destructive",icon:g.Z,color:"text-red-600"}},a=s[e]||s.CANCELLED,l=a.icon;return(0,t.jsxs)(o.C,{variant:a.variant,className:"flex items-center space-x-1",children:[t.jsx(l,{className:"h-3 w-3"}),t.jsx("span",{children:e})]})},el=e=>t.jsx("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${{BASIC:"bg-gray-100 text-gray-800",PROFESSIONAL:"bg-blue-100 text-blue-800",PREMIUM:"bg-purple-100 text-purple-800",ENTERPRISE:"bg-orange-100 text-orange-800"}[e]||"bg-gray-100 text-gray-800"}`,children:e}),er=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:0,maximumFractionDigits:0}).format(e),ei=e=>e?new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}):"N/A",en=async()=>{try{let e=X.find(e=>e.id===K.planId);if(!e){toast.error("Please select a pricing plan");return}let s="YEARLY"===K.billingCycle?e.yearlyPrice||12*e.monthlyPrice:e.monthlyPrice,a=await fetch("/api/super-admin/subscriptions",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...K,amount:s,plan:e.name.toUpperCase(),startDate:K.startDate?new Date(K.startDate):new Date,endDate:K.endDate?new Date(K.endDate):void 0,trialEndDate:K.trialEndDate?new Date(K.trialEndDate):void 0})}),t=await a.json();a.ok?(toast.success(t.message||"Subscription created successfully"),$(!1),W({companyId:"",planId:"",status:"ACTIVE",billingCycle:"MONTHLY",amount:0,startDate:new Date().toISOString().split("T")[0],endDate:"",trialEndDate:"",stripeSubscriptionId:"",stripeCustomerId:""}),ee()):toast.error(t.error||"Failed to create subscription")}catch(e){console.error("Error creating subscription:",e),toast.error("Failed to create subscription")}};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[t.jsx(y.Z,{className:"h-8 w-8 text-blue-600"}),t.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Subscription Management"})]}),t.jsx("p",{className:"text-gray-500 mt-1",children:"Manage subscriptions, billing, and revenue analytics"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(c.z,{variant:"outline",onClick:ee,disabled:_,children:[t.jsx(b.Z,{className:`h-4 w-4 mr-2 ${_?"animate-spin":""}`}),"Refresh"]}),(0,t.jsxs)(p.Vq,{open:B,onOpenChange:$,children:[t.jsx(p.hg,{asChild:!0,children:(0,t.jsxs)(c.z,{children:[t.jsx(N.Z,{className:"h-4 w-4 mr-2"}),"Add Subscription"]})}),(0,t.jsxs)(p.cZ,{className:"max-w-2xl",children:[(0,t.jsxs)(p.fK,{children:[t.jsx(p.$N,{children:"Create New Subscription"}),t.jsx(p.Be,{children:"Add or modify a company's subscription plan."})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 py-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(u._,{htmlFor:"company",children:"Company"}),(0,t.jsxs)(m.Ph,{value:K.companyId,onValueChange:e=>W(s=>({...s,companyId:e})),children:[t.jsx(m.i4,{children:t.jsx(m.ki,{placeholder:"Select company"})}),t.jsx(m.Bw,{children:G.map(e=>(0,t.jsxs)(m.Ql,{value:e.id,children:[e.name," (",e.email,")"]},e.id))})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(u._,{htmlFor:"plan",children:"Pricing Plan"}),(0,t.jsxs)(m.Ph,{value:K.planId,onValueChange:e=>{let s=X.find(s=>s.id===e);if(s){let a="YEARLY"===K.billingCycle?s.yearlyPrice||12*s.monthlyPrice:s.monthlyPrice;W(s=>({...s,planId:e,amount:a}))}},children:[t.jsx(m.i4,{children:t.jsx(m.ki,{placeholder:"Select plan"})}),t.jsx(m.Bw,{children:X.map(e=>(0,t.jsxs)(m.Ql,{value:e.id,children:[e.name," - $",e.monthlyPrice,"/month"]},e.id))})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(u._,{htmlFor:"status",children:"Status"}),(0,t.jsxs)(m.Ph,{value:K.status,onValueChange:e=>W(s=>({...s,status:e})),children:[t.jsx(m.i4,{children:t.jsx(m.ki,{})}),(0,t.jsxs)(m.Bw,{children:[t.jsx(m.Ql,{value:"ACTIVE",children:"Active"}),t.jsx(m.Ql,{value:"TRIALING",children:"Trial"}),t.jsx(m.Ql,{value:"CANCELED",children:"Canceled"}),t.jsx(m.Ql,{value:"EXPIRED",children:"Expired"}),t.jsx(m.Ql,{value:"SUSPENDED",children:"Suspended"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(u._,{htmlFor:"billingCycle",children:"Billing Cycle"}),(0,t.jsxs)(m.Ph,{value:K.billingCycle,onValueChange:e=>W(s=>({...s,billingCycle:e})),children:[t.jsx(m.i4,{children:t.jsx(m.ki,{})}),(0,t.jsxs)(m.Bw,{children:[t.jsx(m.Ql,{value:"MONTHLY",children:"Monthly"}),t.jsx(m.Ql,{value:"YEARLY",children:"Yearly"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(u._,{htmlFor:"startDate",children:"Start Date"}),t.jsx(d.I,{id:"startDate",type:"date",value:K.startDate,onChange:e=>W(s=>({...s,startDate:e.target.value}))})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(u._,{htmlFor:"endDate",children:"End Date (Optional)"}),t.jsx(d.I,{id:"endDate",type:"date",value:K.endDate,onChange:e=>W(s=>({...s,endDate:e.target.value}))})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(u._,{htmlFor:"trialEndDate",children:"Trial End Date (Optional)"}),t.jsx(d.I,{id:"trialEndDate",type:"date",value:K.trialEndDate,onChange:e=>W(s=>({...s,trialEndDate:e.target.value}))})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(u._,{htmlFor:"amount",children:"Amount"}),t.jsx(d.I,{id:"amount",type:"number",value:K.amount,onChange:e=>W(s=>({...s,amount:parseFloat(e.target.value)||0})),placeholder:"0.00"})]}),(0,t.jsxs)("div",{className:"space-y-2 md:col-span-2",children:[t.jsx(u._,{htmlFor:"stripeSubscriptionId",children:"Stripe Subscription ID (Optional)"}),t.jsx(d.I,{id:"stripeSubscriptionId",value:K.stripeSubscriptionId,onChange:e=>W(s=>({...s,stripeSubscriptionId:e.target.value})),placeholder:"sub_..."})]}),(0,t.jsxs)("div",{className:"space-y-2 md:col-span-2",children:[t.jsx(u._,{htmlFor:"stripeCustomerId",children:"Stripe Customer ID (Optional)"}),t.jsx(d.I,{id:"stripeCustomerId",value:K.stripeCustomerId,onChange:e=>W(s=>({...s,stripeCustomerId:e.target.value})),placeholder:"cus_..."})]})]}),(0,t.jsxs)(p.cN,{children:[t.jsx(c.z,{variant:"outline",onClick:()=>$(!1),children:"Cancel"}),t.jsx(c.z,{onClick:en,disabled:!K.companyId||!K.planId,children:"Create Subscription"})]})]})]})]})]}),k&&(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[t.jsx(n.Zb,{className:"hover:shadow-lg transition-shadow duration-200",children:(0,t.jsxs)(n.aY,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Monthly Recurring Revenue"}),t.jsx("p",{className:"text-2xl font-bold text-green-600",children:er(k.revenue.mrr)})]}),t.jsx(v.Z,{className:"h-8 w-8 text-green-600"})]}),t.jsx("div",{className:"mt-4",children:(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[t.jsx("span",{className:"text-gray-500",children:"Growth Rate:"}),(0,t.jsxs)("span",{className:"font-medium text-green-600",children:["+",k.metrics.growthRate.toFixed(1),"%"]})]})})]})}),t.jsx(n.Zb,{className:"hover:shadow-lg transition-shadow duration-200",children:(0,t.jsxs)(n.aY,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Annual Recurring Revenue"}),t.jsx("p",{className:"text-2xl font-bold text-blue-600",children:er(k.revenue.arr)})]}),t.jsx(w.Z,{className:"h-8 w-8 text-blue-600"})]}),t.jsx("div",{className:"mt-4",children:(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[t.jsx("span",{className:"text-gray-500",children:"Avg/Customer:"}),t.jsx("span",{className:"font-medium text-blue-600",children:er(k.revenue.average)})]})})]})}),t.jsx(n.Zb,{className:"hover:shadow-lg transition-shadow duration-200",children:(0,t.jsxs)(n.aY,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Active Subscriptions"}),t.jsx("p",{className:"text-2xl font-bold text-purple-600",children:k.revenue.activeSubscriptions})]}),t.jsx(C.Z,{className:"h-8 w-8 text-purple-600"})]}),t.jsx("div",{className:"mt-4",children:(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[t.jsx("span",{className:"text-gray-500",children:"New (30d):"}),(0,t.jsxs)("span",{className:"font-medium text-purple-600",children:["+",k.metrics.newSubscriptions]})]})})]})}),t.jsx(n.Zb,{className:"hover:shadow-lg transition-shadow duration-200",children:(0,t.jsxs)(n.aY,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Churn Rate"}),(0,t.jsxs)("p",{className:"text-2xl font-bold text-red-600",children:[k.metrics.churnRate.toFixed(1),"%"]})]}),t.jsx(S.Z,{className:"h-8 w-8 text-red-600"})]}),t.jsx("div",{className:"mt-4",children:(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[t.jsx("span",{className:"text-gray-500",children:"Churned (30d):"}),t.jsx("span",{className:"font-medium text-red-600",children:k.metrics.churnCount})]})})]})})]}),k&&(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)(n.Zb,{children:[t.jsx(n.Ol,{children:t.jsx(n.ll,{children:"Subscription Status"})}),t.jsx(n.aY,{children:t.jsx("div",{className:"space-y-3",children:k.byStatus.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx("div",{className:`w-3 h-3 rounded-full ${"ACTIVE"===e.status?"bg-green-500":"TRIAL"===e.status?"bg-blue-500":"CANCELLED"===e.status?"bg-red-500":"EXPIRED"===e.status?"bg-orange-500":"bg-gray-500"}`}),t.jsx("span",{className:"text-sm font-medium",children:e.status})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx("span",{className:"text-sm text-gray-600",children:e.count}),(0,t.jsxs)("span",{className:"text-xs text-gray-400",children:["(",(e.count/k.total*100).toFixed(1),"%)"]})]})]},e.status))})})]}),(0,t.jsxs)(n.Zb,{children:[t.jsx(n.Ol,{children:t.jsx(n.ll,{children:"Plan Distribution"})}),t.jsx(n.aY,{children:t.jsx("div",{className:"space-y-3",children:k.byPlan.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx("div",{className:`w-3 h-3 rounded-full ${0===s?"bg-purple-500":1===s?"bg-blue-500":2===s?"bg-green-500":"bg-orange-500"}`}),t.jsx("span",{className:"text-sm font-medium",children:e.plan})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx("span",{className:"text-sm text-gray-600",children:e.count}),(0,t.jsxs)("span",{className:"text-xs text-gray-400",children:["(",(e.count/k.total*100).toFixed(1),"%)"]})]})]},e.plan))})})]})]}),t.jsx(n.Zb,{children:t.jsx(n.aY,{className:"p-6",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[t.jsx("div",{className:"lg:col-span-1",children:(0,t.jsxs)("div",{className:"relative",children:[t.jsx(D.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),t.jsx(d.I,{placeholder:"Search companies...",value:z,onChange:e=>F(e.target.value),className:"pl-10"})]})}),(0,t.jsxs)(m.Ph,{value:L,onValueChange:M,children:[t.jsx(m.i4,{children:t.jsx(m.ki,{placeholder:"All Statuses"})}),(0,t.jsxs)(m.Bw,{children:[t.jsx(m.Ql,{value:"all",children:"All Statuses"}),t.jsx(m.Ql,{value:"ACTIVE",children:"Active"}),t.jsx(m.Ql,{value:"TRIAL",children:"Trial"}),t.jsx(m.Ql,{value:"CANCELLED",children:"Cancelled"}),t.jsx(m.Ql,{value:"EXPIRED",children:"Expired"}),t.jsx(m.Ql,{value:"SUSPENDED",children:"Suspended"})]})]}),(0,t.jsxs)(m.Ph,{value:Y,onValueChange:O,children:[t.jsx(m.i4,{children:t.jsx(m.ki,{placeholder:"All Plans"})}),(0,t.jsxs)(m.Bw,{children:[t.jsx(m.Ql,{value:"all",children:"All Plans"}),k?.byPlan.map(e=>t.jsxs(m.Ql,{value:e.plan,children:[e.plan," (",e.count,")"]},e.plan))]})]}),(0,t.jsxs)(c.z,{variant:"outline",className:"w-full",children:[t.jsx(E.Z,{className:"h-4 w-4 mr-2"}),"More Filters"]})]})})}),(0,t.jsxs)(n.Zb,{children:[t.jsx(n.Ol,{children:(0,t.jsxs)(n.ll,{children:["Subscriptions (",a.length,")"]})}),t.jsx(n.aY,{children:_?t.jsx("div",{className:"flex items-center justify-center py-8",children:t.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):t.jsx("div",{className:"overflow-x-auto",children:(0,t.jsxs)(x.iA,{children:[t.jsx(x.xD,{children:(0,t.jsxs)(x.SC,{children:[t.jsx(x.ss,{children:"Company"}),t.jsx(x.ss,{children:"Plan"}),t.jsx(x.ss,{children:"Status"}),t.jsx(x.ss,{children:"Amount"}),t.jsx(x.ss,{children:"Billing"}),t.jsx(x.ss,{children:"Start Date"}),t.jsx(x.ss,{children:"Usage"}),t.jsx(x.ss,{children:"Actions"})]})}),t.jsx(x.RM,{children:a.map(e=>(0,t.jsxs)(x.SC,{children:[t.jsx(x.pj,{children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[t.jsx("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold",children:e.company.name.charAt(0).toUpperCase()}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"font-medium text-gray-900",children:e.company.name}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-500",children:[t.jsx("span",{children:e.company.industry}),e.company.size&&(0,t.jsxs)(t.Fragment,{children:[t.jsx("span",{children:"•"}),t.jsx("span",{children:e.company.size})]})]})]})]})}),t.jsx(x.pj,{children:el(e.plan)}),t.jsx(x.pj,{children:et(e.status)}),t.jsx(x.pj,{children:(0,t.jsxs)("div",{className:"text-sm",children:[t.jsx("p",{className:"font-medium",children:er(e.amount)}),(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:["per ",e.billingCycle.toLowerCase()]})]})}),t.jsx(x.pj,{children:t.jsx(o.C,{variant:"outline",children:e.billingCycle})}),t.jsx(x.pj,{children:t.jsx("span",{className:"text-sm text-gray-600",children:ei(e.startDate)})}),t.jsx(x.pj,{children:(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsxs)("p",{children:[e.company.metrics.totalUsers," users"]}),(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:[e.company.metrics.totalCustomers," customers"]})]})}),t.jsx(x.pj,{children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(c.z,{variant:"ghost",size:"sm",children:t.jsx(P.Z,{className:"h-4 w-4"})}),t.jsx(c.z,{variant:"ghost",size:"sm",children:t.jsx(I.Z,{className:"h-4 w-4"})}),t.jsx(c.z,{variant:"ghost",size:"sm",children:t.jsx(Z.Z,{className:"h-4 w-4"})})]})})]},e.id))})]})})})]}),q>1&&(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:["Page ",Q," of ",q]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(c.z,{variant:"outline",onClick:()=>V(Q-1),disabled:1===Q,children:"Previous"}),t.jsx(c.z,{variant:"outline",onClick:()=>V(Q+1),disabled:Q===q,children:"Next"})]})]})]})}},92549:(e,s,a)=>{"use strict";a.d(s,{I:()=>i});var t=a(95344),l=a(3729),r=a(91626);let i=l.forwardRef(({className:e,type:s,...a},l)=>t.jsx("input",{type:s,className:(0,r.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:l,...a}));i.displayName="Input"},1586:(e,s,a)=>{"use strict";a.d(s,{_:()=>d});var t=a(95344),l=a(3729),r=a(14217),i=a(49247),n=a(91626);let c=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=l.forwardRef(({className:e,...s},a)=>t.jsx(r.f,{ref:a,className:(0,n.cn)(c(),e),...s}));d.displayName=r.f.displayName},17470:(e,s,a)=>{"use strict";a.d(s,{Bw:()=>h,Ph:()=>o,Ql:()=>j,i4:()=>m,ki:()=>x});var t=a(95344),l=a(3729),r=a(1146),i=a(25390),n=a(12704),c=a(62312),d=a(91626);let o=r.fC;r.ZA;let x=r.B4,m=l.forwardRef(({className:e,children:s,...a},l)=>(0,t.jsxs)(r.xz,{ref:l,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...a,children:[s,t.jsx(r.JO,{asChild:!0,children:t.jsx(i.Z,{className:"h-4 w-4 opacity-50"})})]}));m.displayName=r.xz.displayName;let p=l.forwardRef(({className:e,...s},a)=>t.jsx(r.u_,{ref:a,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:t.jsx(n.Z,{className:"h-4 w-4"})}));p.displayName=r.u_.displayName;let u=l.forwardRef(({className:e,...s},a)=>t.jsx(r.$G,{ref:a,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:t.jsx(i.Z,{className:"h-4 w-4"})}));u.displayName=r.$G.displayName;let h=l.forwardRef(({className:e,children:s,position:a="popper",...l},i)=>t.jsx(r.h_,{children:(0,t.jsxs)(r.VY,{ref:i,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...l,children:[t.jsx(p,{}),t.jsx(r.l_,{className:(0,d.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),t.jsx(u,{})]})}));h.displayName=r.VY.displayName,l.forwardRef(({className:e,...s},a)=>t.jsx(r.__,{ref:a,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...s})).displayName=r.__.displayName;let j=l.forwardRef(({className:e,children:s,...a},l)=>(0,t.jsxs)(r.ck,{ref:l,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...a,children:[t.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:t.jsx(r.wU,{children:t.jsx(c.Z,{className:"h-4 w-4"})})}),t.jsx(r.eT,{children:s})]}));j.displayName=r.ck.displayName,l.forwardRef(({className:e,...s},a)=>t.jsx(r.Z0,{ref:a,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...s})).displayName=r.Z0.displayName},81036:(e,s,a)=>{"use strict";a.d(s,{RM:()=>c,SC:()=>d,iA:()=>i,pj:()=>x,ss:()=>o,xD:()=>n});var t=a(95344),l=a(3729),r=a(91626);let i=l.forwardRef(({className:e,...s},a)=>t.jsx("div",{className:"relative w-full overflow-auto",children:t.jsx("table",{ref:a,className:(0,r.cn)("w-full caption-bottom text-sm",e),...s})}));i.displayName="Table";let n=l.forwardRef(({className:e,...s},a)=>t.jsx("thead",{ref:a,className:(0,r.cn)("[&_tr]:border-b",e),...s}));n.displayName="TableHeader";let c=l.forwardRef(({className:e,...s},a)=>t.jsx("tbody",{ref:a,className:(0,r.cn)("[&_tr:last-child]:border-0",e),...s}));c.displayName="TableBody",l.forwardRef(({className:e,...s},a)=>t.jsx("tfoot",{ref:a,className:(0,r.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...s})).displayName="TableFooter";let d=l.forwardRef(({className:e,...s},a)=>t.jsx("tr",{ref:a,className:(0,r.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...s}));d.displayName="TableRow";let o=l.forwardRef(({className:e,...s},a)=>t.jsx("th",{ref:a,className:(0,r.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...s}));o.displayName="TableHead";let x=l.forwardRef(({className:e,...s},a)=>t.jsx("td",{ref:a,className:(0,r.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...s}));x.displayName="TableCell",l.forwardRef(({className:e,...s},a)=>t.jsx("caption",{ref:a,className:(0,r.cn)("mt-4 text-sm text-muted-foreground",e),...s})).displayName="TableCaption"},53148:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},17418:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},62093:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("MoreHorizontal",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},75695:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},51838:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},77402:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("TrendingDown",[["polyline",{points:"22 17 13.5 8.5 8.5 13.5 2 7",key:"1r2t7k"}],["polyline",{points:"16 17 22 17 22 11",key:"11uiuu"}]])},46064:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},73229:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},76845:(e,s,a)=>{"use strict";a.r(s),a.d(s,{$$typeof:()=>r,__esModule:()=>l,default:()=>i});let t=(0,a(86843).createProxy)(String.raw`C:\proj\nextjs-saas\app\super-admin\subscriptions\page.tsx`),{__esModule:l,$$typeof:r}=t,i=t.default},14217:(e,s,a)=>{"use strict";a.d(s,{f:()=>n});var t=a(3729),l=a(62409),r=a(95344),i=t.forwardRef((e,s)=>(0,r.jsx)(l.WV.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));i.displayName="Label";var n=i}};var s=require("../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[1638,7948,6671,4626,7792,2506,8830,1729,2125,3965],()=>a(49119));module.exports=t})();