'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  FileText,
  DollarSign,
  TrendingUp,
  Clock,
  Target,
  Users,
  Calendar,
  RefreshCw,
  BarChart3,
  CheckCircle,
  AlertTriangle,
  CreditCard,
  TrendingDown
} from 'lucide-react'
import { toast } from 'react-hot-toast'

interface InvoiceAnalytics {
  summary: {
    totalInvoices: number
    totalInvoicedAmount: number
    totalPaidAmount: number
    outstandingAmount: number
    averageValue: number
    paidInvoicesCount: number
    collectionRate: number
    avgDaysToPayment: number
  }
  invoicesByStatus: Array<{
    status: string
    count: number
    value: number
  }>
  invoicesByMonth: Array<{
    month: string
    invoice_count: number
    total_value: number
    paid_value: number
    collected_amount: number
  }>
  agingReport: Array<{
    period: string
    count: number
    amount: number
  }>
  topInvoices: Array<{
    id: string
    invoiceNumber: string
    title: string | null
    total: number
    paidAmount: number
    status: string
    customer: {
      id: string
      name: string
      company: string | null
      email: string | null
    }
    createdBy: {
      name: string | null
      email: string | null
    }
    createdAt: string
    dueDate: string | null
  }>
  recentInvoices: Array<{
    id: string
    invoiceNumber: string
    title: string | null
    total: number
    paidAmount: number
    status: string
    customer: {
      id: string
      name: string
      company: string | null
    }
    createdBy: {
      name: string | null
      email: string | null
    }
    createdAt: string
    dueDate: string | null
  }>
  customerInvoices: Array<{
    customer: {
      id: string
      name: string
      company: string | null
      email: string | null
    }
    invoiceCount: number
    totalValue: number
    paidAmount: number
    outstandingAmount: number
  }>
  revenueMetrics: {
    totalRevenue: number
    pendingRevenue: number
    thisMonthRevenue: number
    projectedRevenue: number
  }
  overdueInvoices: Array<{
    id: string
    invoiceNumber: string
    total: number
    paidAmount: number
    dueDate: string
    daysOverdue: number
    customer: {
      id: string
      name: string
      company: string | null
      email: string | null
    }
  }>
  period: number
}

export function InvoiceAnalytics() {
  const [analytics, setAnalytics] = useState<InvoiceAnalytics | null>(null)
  const [loading, setLoading] = useState(true)
  const [period, setPeriod] = useState('30')

  const fetchAnalytics = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/invoices/analytics?period=${period}`)
      if (!response.ok) {
        throw new Error('Failed to fetch analytics')
      }

      const data = await response.json()
      setAnalytics(data)
    } catch (error) {
      toast.error('Failed to load invoice analytics')
      console.error('Error fetching analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchAnalytics()
  }, [period])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DRAFT':
        return 'bg-gray-100 text-gray-800'
      case 'SENT':
        return 'bg-blue-100 text-blue-800'
      case 'PAID':
        return 'bg-green-100 text-green-800'
      case 'OVERDUE':
        return 'bg-red-100 text-red-800'
      case 'CANCELLED':
        return 'bg-orange-100 text-orange-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const getAgingColor = (period: string) => {
    switch (period) {
      case '0-30 days':
        return 'text-green-600'
      case '31-60 days':
        return 'text-yellow-600'
      case '61-90 days':
        return 'text-orange-600'
      case '90+ days':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!analytics) {
    return (
      <div className="text-center py-8 text-gray-500">
        <BarChart3 className="h-12 w-12 mx-auto mb-4 text-gray-300" />
        <p>Failed to load analytics data</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Invoice Analytics</h3>
        <div className="flex items-center space-x-2">
          <Select value={period} onValueChange={setPeriod}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">Last 7 days</SelectItem>
              <SelectItem value="30">Last 30 days</SelectItem>
              <SelectItem value="90">Last 90 days</SelectItem>
              <SelectItem value="365">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={fetchAnalytics} size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-blue-100 rounded-full">
                <FileText className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Total Invoices</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.summary.totalInvoices}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-green-100 rounded-full">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Total Invoiced</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(analytics.summary.totalInvoicedAmount)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-purple-100 rounded-full">
                <CheckCircle className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Total Paid</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(analytics.summary.totalPaidAmount)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-red-100 rounded-full">
                <AlertTriangle className="h-6 w-6 text-red-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Outstanding</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(analytics.summary.outstandingAmount)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-indigo-100 rounded-full">
                <Target className="h-6 w-6 text-indigo-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Collection Rate</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analytics.summary.collectionRate.toFixed(1)}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-pink-100 rounded-full">
                <Clock className="h-6 w-6 text-pink-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Avg Days to Pay</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analytics.summary.avgDaysToPayment.toFixed(1)}d
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-orange-100 rounded-full">
                <TrendingUp className="h-6 w-6 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Avg Invoice Value</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(analytics.summary.averageValue)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-teal-100 rounded-full">
                <CreditCard className="h-6 w-6 text-teal-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Paid Invoices</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analytics.summary.paidInvoicesCount}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Revenue Metrics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <DollarSign className="h-5 w-5 mr-2" />
            Revenue Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">
                {formatCurrency(analytics.revenueMetrics.totalRevenue)}
              </p>
              <p className="text-sm text-gray-500">Total Revenue</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600">
                {formatCurrency(analytics.revenueMetrics.pendingRevenue)}
              </p>
              <p className="text-sm text-gray-500">Pending Revenue</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-purple-600">
                {formatCurrency(analytics.revenueMetrics.thisMonthRevenue)}
              </p>
              <p className="text-sm text-gray-500">This Month</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-orange-600">
                {formatCurrency(analytics.revenueMetrics.projectedRevenue)}
              </p>
              <p className="text-sm text-gray-500">Projected Total</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Charts and Breakdowns */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Invoices by Status */}
        <Card>
          <CardHeader>
            <CardTitle>Invoices by Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.invoicesByStatus.map((item) => (
                <div key={item.status} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Badge className={getStatusColor(item.status)}>
                      {item.status}
                    </Badge>
                  </div>
                  <div className="text-right">
                    <span className="font-semibold">{item.count}</span>
                    <p className="text-sm text-gray-500">
                      {formatCurrency(item.value)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Aging Report */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Clock className="h-5 w-5 mr-2" />
              Aging Report
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.agingReport.map((item) => (
                <div key={item.period} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className={`font-medium ${getAgingColor(item.period)}`}>
                      {item.period}
                    </span>
                  </div>
                  <div className="text-right">
                    <span className="font-semibold">{item.count}</span>
                    <p className={`text-sm ${getAgingColor(item.period)}`}>
                      {formatCurrency(item.amount)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Customers */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Users className="h-5 w-5 mr-2" />
            Top Customers by Invoice Value
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {analytics.customerInvoices.slice(0, 5).map((item, index) => (
              <div key={item.customer.id} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-xs font-semibold text-blue-600">#{index + 1}</span>
                  </div>
                  <div>
                    <p className="font-medium">{item.customer.name}</p>
                    <p className="text-sm text-gray-500">{item.customer.company}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-green-600">
                    {formatCurrency(item.totalValue)}
                  </p>
                  <p className="text-sm text-gray-500">
                    {item.invoiceCount} invoices
                  </p>
                  {item.outstandingAmount > 0 && (
                    <p className="text-sm text-red-500">
                      {formatCurrency(item.outstandingAmount)} outstanding
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Overdue Invoices */}
      {analytics.overdueInvoices.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2 text-red-600" />
              Overdue Invoices
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.overdueInvoices.slice(0, 5).map((invoice) => (
                <div key={invoice.id} className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200">
                  <div>
                    <p className="font-medium">{invoice.invoiceNumber}</p>
                    <p className="text-sm text-gray-600">{invoice.customer.name}</p>
                    <p className="text-sm text-red-600">
                      {invoice.daysOverdue} days overdue
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-red-600">
                      {formatCurrency(invoice.total - invoice.paidAmount)}
                    </p>
                    <p className="text-sm text-gray-500">
                      Due: {new Date(invoice.dueDate).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recent Invoices */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="h-5 w-5 mr-2" />
            Recent Invoices (Last 7 Days)
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {analytics.recentInvoices.length === 0 ? (
              <p className="text-gray-500 text-center py-4">No recent invoices</p>
            ) : (
              analytics.recentInvoices.map((invoice) => (
                <div key={invoice.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium">{invoice.invoiceNumber}</p>
                    <p className="text-sm text-gray-600">{invoice.title || 'No title'}</p>
                    <p className="text-sm text-gray-500">
                      {invoice.customer.name} • {new Date(invoice.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-green-600">
                      {formatCurrency(invoice.total)}
                    </p>
                    <Badge className={getStatusColor(invoice.status)} variant="outline">
                      {invoice.status}
                    </Badge>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
