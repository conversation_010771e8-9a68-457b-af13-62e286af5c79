(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7547],{92457:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(62898).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},62442:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(62898).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},97332:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(62898).Z)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},49423:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(62898).Z)("Headphones",[["path",{d:"M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3",key:"1xhozi"}]])},49036:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(62898).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},55340:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(62898).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},25750:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(62898).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},52369:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(62898).Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]])},24033:function(e,t,r){e.exports=r(15313)},85744:function(e,t,r){"use strict";function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}r.d(t,{M:function(){return n}})},56989:function(e,t,r){"use strict";r.d(t,{b:function(){return i},k:function(){return o}});var n=r(2265),u=r(57437);function o(e,t){let r=n.createContext(t),o=e=>{let{children:t,...o}=e,i=n.useMemo(()=>o,Object.values(o));return(0,u.jsx)(r.Provider,{value:i,children:t})};return o.displayName=e+"Provider",[o,function(u){let o=n.useContext(r);if(o)return o;if(void 0!==t)return t;throw Error(`\`${u}\` must be used within \`${e}\``)}]}function i(e,t=[]){let r=[],o=()=>{let t=r.map(e=>n.createContext(e));return function(r){let u=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:u}}),[r,u])}};return o.scopeName=e,[function(t,o){let i=n.createContext(o),c=r.length;r=[...r,o];let s=t=>{let{scope:r,children:o,...s}=t,a=r?.[e]?.[c]||i,l=n.useMemo(()=>s,Object.values(s));return(0,u.jsx)(a.Provider,{value:l,children:o})};return s.displayName=t+"Provider",[s,function(r,u){let s=u?.[e]?.[c]||i,a=n.useContext(s);if(a)return a;if(void 0!==o)return o;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let u=r.reduce((t,{useScope:r,scopeName:n})=>{let u=r(e)[`__scope${n}`];return{...t,...u}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:u}),[u])}};return r.scopeName=t.scopeName,r}(o,...t)]}},9381:function(e,t,r){"use strict";r.d(t,{WV:function(){return c},jH:function(){return s}});var n=r(2265),u=r(54887),o=r(67256),i=r(57437),c=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,o.Z8)(`Primitive.${t}`),u=n.forwardRef((e,n)=>{let{asChild:u,...o}=e,c=u?r:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(c,{...o,ref:n})});return u.displayName=`Primitive.${t}`,{...e,[t]:u}},{});function s(e,t){e&&u.flushSync(()=>e.dispatchEvent(t))}},92376:function(e,t,r){"use strict";r.d(t,{bU:function(){return M},fC:function(){return g}});var n=r(2265),u=r(85744),o=r(42210),i=r(56989),c=r(73763),s=r(85184),a=r(94977),l=r(9381),f=r(57437),d="Switch",[p,h]=(0,i.b)(d),[v,y]=p(d),b=n.forwardRef((e,t)=>{let{__scopeSwitch:r,name:i,checked:s,defaultChecked:a,required:p,disabled:h,value:y="on",onCheckedChange:b,form:m,...k}=e,[g,M]=n.useState(null),S=(0,o.e)(t,e=>M(e)),Z=n.useRef(!1),C=!g||m||!!g.closest("form"),[E,N]=(0,c.T)({prop:s,defaultProp:a??!1,onChange:b,caller:d});return(0,f.jsxs)(v,{scope:r,checked:E,disabled:h,children:[(0,f.jsx)(l.WV.button,{type:"button",role:"switch","aria-checked":E,"aria-required":p,"data-state":x(E),"data-disabled":h?"":void 0,disabled:h,value:y,...k,ref:S,onClick:(0,u.M)(e.onClick,e=>{N(e=>!e),C&&(Z.current=e.isPropagationStopped(),Z.current||e.stopPropagation())})}),C&&(0,f.jsx)(w,{control:g,bubbles:!Z.current,name:i,value:y,checked:E,required:p,disabled:h,form:m,style:{transform:"translateX(-100%)"}})]})});b.displayName=d;var m="SwitchThumb",k=n.forwardRef((e,t)=>{let{__scopeSwitch:r,...n}=e,u=y(m,r);return(0,f.jsx)(l.WV.span,{"data-state":x(u.checked),"data-disabled":u.disabled?"":void 0,...n,ref:t})});k.displayName=m;var w=n.forwardRef(({__scopeSwitch:e,control:t,checked:r,bubbles:u=!0,...i},c)=>{let l=n.useRef(null),d=(0,o.e)(l,c),p=(0,s.D)(r),h=(0,a.t)(t);return n.useEffect(()=>{let e=l.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(p!==r&&t){let n=new Event("click",{bubbles:u});t.call(e,r),e.dispatchEvent(n)}},[p,r,u]),(0,f.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:r,...i,tabIndex:-1,ref:d,style:{...i.style,...h,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function x(e){return e?"checked":"unchecked"}w.displayName="SwitchBubbleInput";var g=b,M=k},73763:function(e,t,r){"use strict";r.d(t,{T:function(){return c}});var n,u=r(2265),o=r(51030),i=(n||(n=r.t(u,2)))[" useInsertionEffect ".trim().toString()]||o.b;function c({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[o,c,s]=function({defaultProp:e,onChange:t}){let[r,n]=u.useState(e),o=u.useRef(r),c=u.useRef(t);return i(()=>{c.current=t},[t]),u.useEffect(()=>{o.current!==r&&(c.current?.(r),o.current=r)},[r,o]),[r,n,c]}({defaultProp:t,onChange:r}),a=void 0!==e,l=a?e:o;{let t=u.useRef(void 0!==e);u.useEffect(()=>{let e=t.current;if(e!==a){let t=a?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=a},[a,n])}return[l,u.useCallback(t=>{if(a){let r="function"==typeof t?t(e):t;r!==e&&s.current?.(r)}else c(t)},[a,e,c,s])]}Symbol("RADIX:SYNC_STATE")},51030:function(e,t,r){"use strict";r.d(t,{b:function(){return u}});var n=r(2265),u=globalThis?.document?n.useLayoutEffect:()=>{}},85184:function(e,t,r){"use strict";r.d(t,{D:function(){return u}});var n=r(2265);function u(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},94977:function(e,t,r){"use strict";r.d(t,{t:function(){return o}});var n=r(2265),u=r(51030);function o(e){let[t,r]=n.useState(void 0);return(0,u.b)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,u;if(!Array.isArray(t)||!t.length)return;let o=t[0];if("borderBoxSize"in o){let e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,u=t.blockSize}else n=e.offsetWidth,u=e.offsetHeight;r({width:n,height:u})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}}}]);