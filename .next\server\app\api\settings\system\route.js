"use strict";(()=>{var e={};e.id=5398,e.ids=[5398],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},25528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},31045:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>Z,originalPathname:()=>I,patchFetch:()=>_,requestAsyncStorage:()=>j,routeModule:()=>h,serverHooks:()=>x,staticGenerationAsyncStorage:()=>S,staticGenerationBailout:()=>q});var s={};r.r(s),r.d(s,{DELETE:()=>w,GET:()=>g,POST:()=>m,PUT:()=>f});var n=r(95419),i=r(69108),a=r(99678),o=r(78070),u=r(81355),l=r(3205),c=r(9108),d=r(25252),p=r(52178);let y=d.Ry({key:d.Z_().min(1),value:d.Yj(),category:d.Z_().min(1),description:d.Z_().optional(),isPublic:d.O7().default(!1),isEditable:d.O7().default(!0)});async function g(e){try{let t=await (0,u.getServerSession)(l.L);if(!t?.user?.id)return o.Z.json({error:"Unauthorized"},{status:401});let{searchParams:r}=new URL(e.url),s=r.get("category"),n="true"===r.get("public"),i={};s&&(i.category=s),n&&(i.isPublic=!0);let a=(await c._.systemSettings.findMany({where:i,orderBy:[{category:"asc"},{key:"asc"}]})).reduce((e,t)=>(e[t.category]||(e[t.category]=[]),e[t.category].push(t),e),{});return o.Z.json({settings:a,categories:Object.keys(a)})}catch(e){return console.error("Error fetching system settings:",e),o.Z.json({error:"Failed to fetch system settings"},{status:500})}}async function m(e){try{let t=await (0,u.getServerSession)(l.L);if(!t?.user?.id)return o.Z.json({error:"Unauthorized"},{status:401});let r=await e.json(),s=y.parse(r);if(await c._.systemSettings.findUnique({where:{key:s.key}}))return o.Z.json({error:"A setting with this key already exists"},{status:400});let n=await c._.systemSettings.create({data:s});return o.Z.json({setting:n,message:"System setting created successfully"})}catch(e){if(e instanceof p.jm)return o.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("Error creating system setting:",e),o.Z.json({error:"Failed to create system setting"},{status:500})}}async function f(e){try{let t=await (0,u.getServerSession)(l.L);if(!t?.user?.id)return o.Z.json({error:"Unauthorized"},{status:401});let{key:r,...s}=await e.json();if(!r)return o.Z.json({error:"Setting key is required"},{status:400});let n=await c._.systemSettings.findUnique({where:{key:r}});if(!n)return o.Z.json({error:"Setting not found"},{status:404});if(!n.isEditable)return o.Z.json({error:"This setting is not editable"},{status:403});let i=await c._.systemSettings.update({where:{key:r},data:{...s,updatedAt:new Date}});return o.Z.json({setting:i,message:"System setting updated successfully"})}catch(e){return console.error("Error updating system setting:",e),o.Z.json({error:"Failed to update system setting"},{status:500})}}async function w(e){try{let t=await (0,u.getServerSession)(l.L);if(!t?.user?.id)return o.Z.json({error:"Unauthorized"},{status:401});let{searchParams:r}=new URL(e.url),s=r.get("key");if(!s)return o.Z.json({error:"Setting key is required"},{status:400});let n=await c._.systemSettings.findUnique({where:{key:s}});if(!n)return o.Z.json({error:"Setting not found"},{status:404});if(!n.isEditable)return o.Z.json({error:"This setting cannot be deleted"},{status:403});return await c._.systemSettings.delete({where:{key:s}}),o.Z.json({message:"System setting deleted successfully"})}catch(e){return console.error("Error deleting system setting:",e),o.Z.json({error:"Failed to delete system setting"},{status:500})}}let h=new n.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/settings/system/route",pathname:"/api/settings/system",filename:"route",bundlePath:"app/api/settings/system/route"},resolvedPagePath:"C:\\proj\\nextjs-saas\\app\\api\\settings\\system\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:j,staticGenerationAsyncStorage:S,serverHooks:x,headerHooks:Z,staticGenerationBailout:q}=h,I="/api/settings/system/route";function _(){return(0,a.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:S})}},3205:(e,t,r)=>{r.d(t,{L:()=>l});var s=r(86485),n=r(10375),i=r(50694),a=r(6521),o=r.n(a),u=r(9108);let l={providers:[(0,s.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){try{if(!e?.email||!e?.password)return console.log("Missing credentials"),null;console.log("Attempting to authenticate user:",e.email);let t=await u._.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0,role:!0,companyId:!0}}),r=t?.companyId;if(!r&&t){let e=await u._.company.findFirst({where:{ownerId:t.id},select:{id:!0}});(r=e?.id)&&await u._.user.update({where:{id:t.id},data:{companyId:r}})}if(!t)return console.log("User not found:",e.email),null;if(!t.password)return console.log("User has no password set:",e.email),null;if(!await o().compare(e.password,t.password))return console.log("Invalid password for user:",e.email),null;return await u._.user.update({where:{id:t.id},data:{lastLoginAt:new Date,loginCount:{increment:1}}}),console.log("User authenticated successfully:",t.email),{id:t.id,email:t.email,name:t.name,role:t.role,companyId:r}}catch(e){return console.error("Authentication error:",e),null}}}),(0,n.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,i.Z)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(console.log("JWT callback - user data:",{id:t.id,email:t.email,role:t.role,companyId:t.companyId}),e.role=t.role,e.companyId=t.companyId),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.companyId=t.companyId,e.user.company&&delete e.user.company,console.log("Session callback - final session:",{id:e.user.id,email:e.user.email,role:e.user.role,companyId:e.user.companyId})),e)},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"}}},9108:(e,t,r)=>{r.d(t,{_:()=>n});let s=require("@prisma/client"),n=globalThis.prisma??new s.PrismaClient}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1638,6206,6521,2455,4520,5252],()=>r(31045));module.exports=s})();