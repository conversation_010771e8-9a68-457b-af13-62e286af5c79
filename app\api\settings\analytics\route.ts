import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get comprehensive settings analytics
    const [
      companySettings,
      userSettingsStats,
      systemSettingsStats,
      featureUsage,
      securityMetrics,
      integrationStatus,
      notificationStats,
      themePreferences,
      languageDistribution,
      timezoneDistribution
    ] = await Promise.all([
      // Company settings
      prisma.companySettings.findUnique({
        where: {
          companyId: session.user.companyId
        }
      }),

      // User settings statistics
      Promise.all([
        // Total users with settings
        prisma.userSettings.count({
          where: {
            user: {
              companyId: session.user.companyId
            }
          }
        }),
        // Users by theme preference
        prisma.userSettings.groupBy({
          by: ['theme'],
          where: {
            user: {
              companyId: session.user.companyId
            }
          },
          _count: {
            id: true
          }
        }),
        // Users by notification preferences
        prisma.userSettings.aggregate({
          where: {
            user: {
              companyId: session.user.companyId
            }
          },
          _count: {
            emailNotifications: true,
            pushNotifications: true,
            smsNotifications: true
          }
        })
      ]),

      // System settings statistics
      Promise.all([
        // Total system settings
        prisma.systemSettings.count(),
        // Settings by category
        prisma.systemSettings.groupBy({
          by: ['category'],
          _count: {
            id: true
          }
        }),
        // Public vs private settings
        prisma.systemSettings.groupBy({
          by: ['isPublic'],
          _count: {
            id: true
          }
        }),
        // Editable vs non-editable settings
        prisma.systemSettings.groupBy({
          by: ['isEditable'],
          _count: {
            id: true
          }
        })
      ]),

      // Feature usage from company settings
      companySettings?.featureSettings ? Object.entries(companySettings.featureSettings as any).map(([feature, enabled]) => ({
        feature,
        enabled: Boolean(enabled)
      })) : [],

      // Security metrics
      companySettings?.securitySettings ? {
        twoFactorEnabled: Boolean((companySettings.securitySettings as any)?.twoFactorRequired),
        sessionTimeout: (companySettings.securitySettings as any)?.sessionTimeout || 30,
        passwordPolicyEnabled: Boolean((companySettings.securitySettings as any)?.passwordPolicy),
        ipWhitelistCount: Array.isArray((companySettings.securitySettings as any)?.ipWhitelist) 
          ? (companySettings.securitySettings as any).ipWhitelist.length 
          : 0,
        allowedDomainsCount: Array.isArray((companySettings.securitySettings as any)?.allowedDomains) 
          ? (companySettings.securitySettings as any).allowedDomains.length 
          : 0
      } : null,

      // Integration status
      companySettings?.integrationSettings ? {
        webhooksCount: Array.isArray((companySettings.integrationSettings as any)?.webhooks) 
          ? (companySettings.integrationSettings as any).webhooks.length 
          : 0,
        apiKeysCount: Object.keys((companySettings.integrationSettings as any)?.apiKeys || {}).length,
        connectedServicesCount: Object.keys((companySettings.integrationSettings as any)?.connectedServices || {}).length
      } : null,

      // Notification statistics
      companySettings?.notificationSettings ? Object.entries(companySettings.notificationSettings as any).map(([type, enabled]) => ({
        type,
        enabled: Boolean(enabled)
      })) : [],

      // Theme preferences distribution
      prisma.userSettings.groupBy({
        by: ['theme'],
        where: {
          user: {
            companyId: session.user.companyId
          }
        },
        _count: {
          id: true
        }
      }),

      // Language distribution
      prisma.userSettings.groupBy({
        by: ['language'],
        where: {
          user: {
            companyId: session.user.companyId
          }
        },
        _count: {
          id: true
        }
      }),

      // Timezone distribution
      prisma.userSettings.groupBy({
        by: ['timezone'],
        where: {
          user: {
            companyId: session.user.companyId
          }
        },
        _count: {
          id: true
        }
      })
    ])

    const [totalUsersWithSettings, themeStats, notificationAggregates] = userSettingsStats
    const [totalSystemSettings, settingsByCategory, settingsByPublic, settingsByEditable] = systemSettingsStats

    // Calculate notification preferences
    const totalUsers = await prisma.user.count({
      where: {
        companyId: session.user.companyId
      }
    })

    return NextResponse.json({
      summary: {
        totalUsers,
        usersWithSettings: totalUsersWithSettings,
        settingsConfigurationRate: totalUsers > 0 ? (totalUsersWithSettings / totalUsers) * 100 : 0,
        totalSystemSettings,
        companySettingsConfigured: Boolean(companySettings),
        lastSettingsUpdate: companySettings?.updatedAt || null
      },
      companySettings: {
        configured: Boolean(companySettings),
        currency: companySettings?.defaultCurrency || 'USD',
        timezone: companySettings?.timezone || 'UTC',
        language: companySettings?.language || 'en',
        taxRate: companySettings ? Number(companySettings.taxRate) : 0,
        branding: {
          primaryColor: companySettings?.primaryColor || '#3b82f6',
          secondaryColor: companySettings?.secondaryColor || '#1e3a8a',
          accentColor: companySettings?.accentColor || '#f59e0b',
          fontFamily: companySettings?.fontFamily || 'Inter'
        },
        documentSettings: {
          invoicePrefix: companySettings?.invoicePrefix || 'INV',
          quotationPrefix: companySettings?.quotationPrefix || 'QUO',
          contractPrefix: companySettings?.contractPrefix || 'CON',
          invoiceNumbering: companySettings?.invoiceNumbering || 'sequential'
        }
      },
      userPreferences: {
        themes: themeStats.map(stat => ({
          theme: stat.theme,
          count: stat._count.id,
          percentage: totalUsersWithSettings > 0 ? (stat._count.id / totalUsersWithSettings) * 100 : 0
        })),
        languages: languageDistribution.map(stat => ({
          language: stat.language,
          count: stat._count.id,
          percentage: totalUsersWithSettings > 0 ? (stat._count.id / totalUsersWithSettings) * 100 : 0
        })),
        timezones: timezoneDistribution.map(stat => ({
          timezone: stat.timezone,
          count: stat._count.id,
          percentage: totalUsersWithSettings > 0 ? (stat._count.id / totalUsersWithSettings) * 100 : 0
        })),
        notifications: {
          emailEnabled: totalUsersWithSettings > 0 ? (notificationAggregates._count.emailNotifications / totalUsersWithSettings) * 100 : 0,
          pushEnabled: totalUsersWithSettings > 0 ? (notificationAggregates._count.pushNotifications / totalUsersWithSettings) * 100 : 0,
          smsEnabled: totalUsersWithSettings > 0 ? (notificationAggregates._count.smsNotifications / totalUsersWithSettings) * 100 : 0
        }
      },
      systemSettings: {
        total: totalSystemSettings,
        categories: settingsByCategory.map(cat => ({
          category: cat.category,
          count: cat._count.id
        })),
        public: settingsByPublic.find(s => s.isPublic)?._count.id || 0,
        private: settingsByPublic.find(s => !s.isPublic)?._count.id || 0,
        editable: settingsByEditable.find(s => s.isEditable)?._count.id || 0,
        readonly: settingsByEditable.find(s => !s.isEditable)?._count.id || 0
      },
      features: {
        enabled: featureUsage.filter(f => f.enabled).length,
        disabled: featureUsage.filter(f => !f.enabled).length,
        usage: featureUsage.map(f => ({
          feature: f.feature,
          enabled: f.enabled
        }))
      },
      security: securityMetrics ? {
        twoFactorEnabled: securityMetrics.twoFactorEnabled,
        sessionTimeout: securityMetrics.sessionTimeout,
        passwordPolicyEnabled: securityMetrics.passwordPolicyEnabled,
        ipWhitelistCount: securityMetrics.ipWhitelistCount,
        allowedDomainsCount: securityMetrics.allowedDomainsCount,
        securityScore: [
          securityMetrics.twoFactorEnabled ? 20 : 0,
          securityMetrics.passwordPolicyEnabled ? 20 : 0,
          securityMetrics.sessionTimeout <= 30 ? 20 : 0,
          securityMetrics.ipWhitelistCount > 0 ? 20 : 0,
          securityMetrics.allowedDomainsCount > 0 ? 20 : 0
        ].reduce((a, b) => a + b, 0)
      } : null,
      integrations: integrationStatus ? {
        webhooksCount: integrationStatus.webhooksCount,
        apiKeysCount: integrationStatus.apiKeysCount,
        connectedServicesCount: integrationStatus.connectedServicesCount,
        integrationScore: [
          integrationStatus.webhooksCount > 0 ? 33 : 0,
          integrationStatus.apiKeysCount > 0 ? 33 : 0,
          integrationStatus.connectedServicesCount > 0 ? 34 : 0
        ].reduce((a, b) => a + b, 0)
      } : null,
      notifications: {
        types: notificationStats.map(stat => ({
          type: stat.type,
          enabled: stat.enabled
        })),
        enabledCount: notificationStats.filter(s => s.enabled).length,
        disabledCount: notificationStats.filter(s => !s.enabled).length
      }
    })

  } catch (error) {
    console.error('Error fetching settings analytics:', error)
    return NextResponse.json(
      { error: 'Failed to fetch settings analytics' },
      { status: 500 }
    )
  }
}
