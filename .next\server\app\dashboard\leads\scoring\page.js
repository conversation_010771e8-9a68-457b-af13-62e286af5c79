(()=>{var e={};e.id=2533,e.ids=[2533],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},70283:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>c.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>d});var a=t(50482),r=t(69108),l=t(62563),c=t.n(l),i=t(68300),n={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);t.d(s,n);let d=["",{children:["dashboard",{children:["leads",{children:["scoring",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,79618)),"C:\\proj\\nextjs-saas\\app\\dashboard\\leads\\scoring\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,96339)),"C:\\proj\\nextjs-saas\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,59504)),"C:\\proj\\nextjs-saas\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\proj\\nextjs-saas\\app\\dashboard\\leads\\scoring\\page.tsx"],x="/dashboard/leads/scoring/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/leads/scoring/page",pathname:"/dashboard/leads/scoring",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},24491:(e,s,t)=>{Promise.resolve().then(t.bind(t,49774))},49774:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>f});var a=t(95344),r=t(3729),l=t(61351),c=t(16212),i=t(69436),n=t(45961),d=t(33733),o=t(89895),x=t(17910),m=t(88534),h=t(46064),p=t(66985),u=t(50340),y=t(7060),j=t(44669),g=t(27109);function b(){let[e,s]=(0,r.useState)(null),[t,b]=(0,r.useState)(!0),[f,v]=(0,r.useState)(!1),N=async()=>{try{b(!0);let e=await fetch("/api/leads/scoring/bulk",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"analyze"})});if(!e.ok)throw Error("Failed to fetch analytics");let t=await e.json();s(t)}catch(e){j.toast.error("Failed to load scoring analytics"),console.error("Error fetching analytics:",e)}finally{b(!1)}},k=async()=>{try{v(!0);let e=await fetch("/api/leads/scoring/bulk",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"recalculate"})});if(!e.ok)throw Error("Failed to recalculate scores");let s=await e.json();j.toast.success(`Recalculated ${s.updatedCount} lead scores`),N()}catch(e){j.toast.error("Failed to recalculate scores"),console.error("Error recalculating scores:",e)}finally{v(!1)}};(0,r.useEffect)(()=>{N()},[]);let w=e=>{switch(e){case"URGENT":return"text-red-600 bg-red-100";case"HIGH":return"text-orange-600 bg-orange-100";case"MEDIUM":return"text-yellow-600 bg-yellow-100";case"LOW":return"text-green-600 bg-green-100";default:return"text-gray-600 bg-gray-100"}};return t?a.jsx("div",{className:"flex items-center justify-center py-8",children:a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):e?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Lead Scoring Analytics"}),(0,a.jsxs)(c.z,{onClick:k,disabled:f,children:[a.jsx(d.Z,{className:`h-4 w-4 mr-2 ${f?"animate-spin":""}`}),f?"Recalculating...":"Recalculate All Scores"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[a.jsx(l.Zb,{children:a.jsx(l.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-blue-100 rounded-full",children:a.jsx(o.Z,{className:"h-6 w-6 text-blue-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Total Leads"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.totalLeads})]})]})})}),a.jsx(l.Zb,{children:a.jsx(l.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-green-100 rounded-full",children:a.jsx(x.Z,{className:"h-6 w-6 text-green-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Average Score"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.averageScore})]})]})})}),a.jsx(l.Zb,{children:a.jsx(l.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-purple-100 rounded-full",children:a.jsx(m.Z,{className:"h-6 w-6 text-purple-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Recent Leads (30d)"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.recentLeads})]})]})})}),a.jsx(l.Zb,{children:a.jsx(l.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-3 bg-orange-100 rounded-full",children:a.jsx(h.Z,{className:"h-6 w-6 text-orange-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Recent Avg Score"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.recentAverageScore})]})]})})})]}),(0,a.jsxs)(l.Zb,{children:[a.jsx(l.Ol,{children:(0,a.jsxs)(l.ll,{className:"flex items-center",children:[a.jsx(p.Z,{className:"h-5 w-5 mr-2"}),"Lead Temperature Distribution"]})}),a.jsx(l.aY,{children:(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-3xl font-bold text-red-600",children:e.temperatureDistribution.HOT}),a.jsx("div",{className:"text-sm text-gray-500",children:"Hot Leads"}),a.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2 mt-2",children:a.jsx("div",{className:"bg-red-600 h-2 rounded-full",style:{width:`${e.temperatureDistribution.HOT/e.summary.totalLeads*100}%`}})})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-3xl font-bold text-orange-600",children:e.temperatureDistribution.WARM}),a.jsx("div",{className:"text-sm text-gray-500",children:"Warm Leads"}),a.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2 mt-2",children:a.jsx("div",{className:"bg-orange-600 h-2 rounded-full",style:{width:`${e.temperatureDistribution.WARM/e.summary.totalLeads*100}%`}})})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-3xl font-bold text-blue-600",children:e.temperatureDistribution.COLD}),a.jsx("div",{className:"text-sm text-gray-500",children:"Cold Leads"}),a.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2 mt-2",children:a.jsx("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:`${e.temperatureDistribution.COLD/e.summary.totalLeads*100}%`}})})]})]})})]}),(0,a.jsxs)(l.Zb,{children:[a.jsx(l.Ol,{children:(0,a.jsxs)(l.ll,{className:"flex items-center",children:[a.jsx(u.Z,{className:"h-5 w-5 mr-2"}),"Score Distribution"]})}),a.jsx(l.aY,{children:a.jsx("div",{className:"space-y-3",children:Object.entries(e.scoreRanges).map(([s,t])=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"text-sm font-medium",children:[s," points"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("div",{className:"w-32 bg-gray-200 rounded-full h-2",children:a.jsx("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:`${t/e.summary.totalLeads*100}%`}})}),a.jsx("span",{className:"text-sm text-gray-500 w-8",children:t})]})]},s))})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)(l.Zb,{children:[a.jsx(l.Ol,{children:(0,a.jsxs)(l.ll,{className:"flex items-center",children:[a.jsx(y.Z,{className:"h-5 w-5 mr-2 text-green-600"}),"Top Performers"]})}),a.jsx(l.aY,{children:a.jsx("div",{className:"space-y-2",children:e.topPerformers.slice(0,5).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-2 bg-gray-50 rounded",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("span",{className:"text-sm font-medium",children:["#",s+1]}),a.jsx(g.q9,{score:e.score})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(i.C,{variant:"outline",className:"text-xs",children:e.status}),a.jsx("span",{className:"text-sm font-bold",children:e.score})]})]},e.id))})})]}),(0,a.jsxs)(l.Zb,{children:[a.jsx(l.Ol,{children:(0,a.jsxs)(l.ll,{className:"flex items-center",children:[a.jsx(n.Z,{className:"h-5 w-5 mr-2 text-red-600"}),"Needs Attention"]})}),a.jsx(l.aY,{children:a.jsx("div",{className:"space-y-2",children:e.bottomPerformers.slice(0,5).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-2 bg-gray-50 rounded",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("span",{className:"text-sm font-medium",children:["#",s+1]}),a.jsx(g.q9,{score:e.score})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(i.C,{variant:"outline",className:"text-xs",children:e.status}),a.jsx("span",{className:"text-sm font-bold",children:e.score})]})]},e.id))})})]})]}),e.recommendations.length>0&&(0,a.jsxs)(l.Zb,{children:[a.jsx(l.Ol,{children:a.jsx(l.ll,{children:"Recommendations"})}),a.jsx(l.aY,{children:a.jsx("div",{className:"space-y-3",children:e.recommendations.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-start space-x-3 p-3 bg-gray-50 rounded-lg",children:[a.jsx(i.C,{className:`${w(e.priority)} text-xs`,children:e.priority}),(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("p",{className:"text-sm font-medium text-gray-900",children:e.message}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["Recommended action: ",e.action]})]})]},s))})})]})]}):(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[a.jsx(n.Z,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),a.jsx("p",{children:"Failed to load analytics data"})]})}function f(){return a.jsx("div",{className:"container mx-auto py-6",children:a.jsx(b,{})})}},27109:(e,s,t)=>{"use strict";t.d(s,{q9:()=>x,OT:()=>m});var a=t(95344),r=t(69436),l=t(69224);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let c=(0,l.Z)("Flame",[["path",{d:"M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z",key:"96xj49"}]]),i=(0,l.Z)("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]),n=(0,l.Z)("Snowflake",[["line",{x1:"2",x2:"22",y1:"12",y2:"12",key:"1dnqot"}],["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"m20 16-4-4 4-4",key:"rquw4f"}],["path",{d:"m4 8 4 4-4 4",key:"12s3z9"}],["path",{d:"m16 4-4 4-4-4",key:"1tumq1"}],["path",{d:"m8 20 4-4 4 4",key:"9p200w"}]]);var d=t(66985);function o({score:e,size:s="md",showIcon:t=!0,showLabel:l=!0}){let o=e>=70?"HOT":e>=40?"WARM":"COLD",x=(e=>{switch(e){case"HOT":return{color:"text-red-600 bg-red-100 border-red-200",icon:c,label:"Hot Lead",description:"High priority, ready to convert"};case"WARM":return{color:"text-orange-600 bg-orange-100 border-orange-200",icon:i,label:"Warm Lead",description:"Good potential, needs nurturing"};case"COLD":return{color:"text-blue-600 bg-blue-100 border-blue-200",icon:n,label:"Cold Lead",description:"Low engagement, requires attention"};default:return{color:"text-gray-600 bg-gray-100 border-gray-200",icon:d.Z,label:"Unknown",description:"Temperature not determined"}}})(o),m=(e=>{switch(e){case"sm":return{badge:"text-xs px-2 py-1",icon:"h-3 w-3",text:"text-xs"};case"lg":return{badge:"text-base px-4 py-2",icon:"h-5 w-5",text:"text-base"};default:return{badge:"text-sm px-3 py-1",icon:"h-4 w-4",text:"text-sm"}}})(s),h=x.icon;return(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(r.C,{className:`${x.color} border ${m.badge} font-medium`,variant:"outline",children:(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[t&&a.jsx(h,{className:m.icon}),a.jsx("span",{children:o})]})}),l&&(0,a.jsxs)("div",{className:"flex flex-col",children:[a.jsx("span",{className:`${m.text} font-medium text-gray-900`,children:x.label}),"lg"===s&&a.jsx("span",{className:"text-xs text-gray-500",children:x.description})]})]})}function x({score:e}){return a.jsx(o,{score:e,size:"sm",showLabel:!1})}function m({score:e}){return a.jsx(o,{score:e,size:"lg",showIcon:!0,showLabel:!0})}},88534:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]])},45961:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},7060:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},71542:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("FileCheck",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["path",{d:"m9 15 2 2 4-4",key:"1grp1n"}]])},91917:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},74243:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Receipt",[["path",{d:"M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1-2-1Z",key:"wqdwcb"}],["path",{d:"M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8",key:"1h4pet"}],["path",{d:"M12 17V7",key:"pyj7ub"}]])},33733:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},17910:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},66985:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Thermometer",[["path",{d:"M14 4v10.54a4 4 0 1 1-4 0V4a2 2 0 0 1 4 0Z",key:"17jzev"}]])},46064:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},28240:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},79618:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>l,__esModule:()=>r,default:()=>c});let a=(0,t(86843).createProxy)(String.raw`C:\proj\nextjs-saas\app\dashboard\leads\scoring\page.tsx`),{__esModule:r,$$typeof:l}=a,c=a.default}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[1638,7948,6671,4626,7792,2506,2125,5045],()=>t(70283));module.exports=a})();