(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[451],{6141:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(62898).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},35817:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(62898).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},76637:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(62898).Z)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]])},29409:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(62898).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},45367:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(62898).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},31541:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(62898).Z)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},25750:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(62898).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},52369:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(62898).Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]])},944:function(e,s,a){Promise.resolve().then(a.bind(a,96870))},96870:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return Z}});var t=a(57437),i=a(2265),n=a(27815),c=a(31478),l=a(85754),r=a(10500),d=a(40110),x=a(19160),m=a(13008),u=a(72894),h=a(90998),o=a(64280),j=a(92457),p=a(76637),v=a(6141),f=a(29409),y=a(35817),N=a(25750),b=a(45367),w=a(52369),g=a(469),k=a(31541);function Z(){let[e,s]=(0,i.useState)(!0),[a,Z]=(0,i.useState)(new Date),[S]=(0,i.useState)([{name:"Total Size",value:"2.4",unit:"GB",status:"good",description:"Total database size across all tables"},{name:"Active Connections",value:24,status:"good",description:"Current active database connections"},{name:"Query Performance",value:"89",unit:"ms",status:"warning",description:"Average query execution time"},{name:"Cache Hit Rate",value:"94.2",unit:"%",status:"good",description:"Database query cache efficiency"},{name:"Disk Usage",value:"68",unit:"%",status:"warning",description:"Database storage utilization"},{name:"Backup Status",value:"Success",status:"good",description:"Last backup completion status"}]),[D]=(0,i.useState)([{name:"users",rows:15420,size:"245 MB",lastUpdated:new Date(Date.now()-12e4),status:"healthy"},{name:"companies",rows:1250,size:"89 MB",lastUpdated:new Date(Date.now()-3e5),status:"healthy"},{name:"subscriptions",rows:3420,size:"156 MB",lastUpdated:new Date(Date.now()-6e5),status:"healthy"},{name:"invoices",rows:8950,size:"1.2 GB",lastUpdated:new Date(Date.now()-9e5),status:"warning"},{name:"audit_logs",rows:125e3,size:"890 MB",lastUpdated:new Date(Date.now()-6e4),status:"healthy"}]),[z]=(0,i.useState)([{id:"1",user:"app_user",database:"nextjs_saas_db",host:"********",state:"active",duration:"00:02:15",query:"SELECT * FROM users WHERE company_id = ?"},{id:"2",user:"admin",database:"nextjs_saas_db",host:"********",state:"idle",duration:"00:15:30"},{id:"3",user:"backup_user",database:"nextjs_saas_db",host:"********",state:"active",duration:"01:45:20",query:"BACKUP DATABASE TO /backups/daily_backup.sql"},{id:"4",user:"app_user",database:"nextjs_saas_db",host:"********",state:"waiting",duration:"00:00:45",query:"UPDATE subscriptions SET status = ? WHERE id = ?"}]);(0,i.useEffect)(()=>{let e=setTimeout(()=>{s(!1)},1e3);return()=>clearTimeout(e)},[]);let C=e=>{switch(e){case"good":case"healthy":return(0,t.jsx)(m.Z,{className:"h-4 w-4 text-green-500"});case"warning":return(0,t.jsx)(u.Z,{className:"h-4 w-4 text-yellow-500"});case"critical":case"error":return(0,t.jsx)(u.Z,{className:"h-4 w-4 text-red-500"});default:return(0,t.jsx)(h.Z,{className:"h-4 w-4 text-gray-500"})}},U=e=>{switch(e){case"good":case"healthy":return"default";case"warning":return"secondary";case"critical":case"error":return"destructive";default:return"outline"}},M=e=>{switch(e){case"active":return"default";case"idle":return"secondary";case"waiting":return"destructive";default:return"outline"}};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Database Management"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Monitor database performance, connections, and manage data"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(c.C,{variant:"outline",className:"text-xs",children:["Last updated: ",a.toLocaleTimeString()]}),(0,t.jsxs)(l.z,{onClick:()=>{s(!0),Z(new Date),setTimeout(()=>s(!1),1e3)},disabled:e,size:"sm",children:[(0,t.jsx)(o.Z,{className:"h-4 w-4 mr-2 ".concat(e?"animate-spin":"")}),"Refresh"]})]})]}),(0,t.jsx)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-3",children:S.map((e,s)=>(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(n.ll,{className:"text-sm font-medium",children:e.name}),C(e.status)]}),(0,t.jsxs)(n.aY,{children:[(0,t.jsxs)("div",{className:"text-2xl font-bold",children:[e.value,e.unit]}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:e.description})]})]},s))}),(0,t.jsxs)(d.mQ,{defaultValue:"overview",className:"space-y-4",children:[(0,t.jsxs)(d.dr,{children:[(0,t.jsx)(d.SP,{value:"overview",children:"Overview"}),(0,t.jsx)(d.SP,{value:"tables",children:"Tables"}),(0,t.jsx)(d.SP,{value:"connections",children:"Connections"}),(0,t.jsx)(d.SP,{value:"performance",children:"Performance"}),(0,t.jsx)(d.SP,{value:"backup",children:"Backup & Restore"})]}),(0,t.jsx)(d.nU,{value:"overview",className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{children:[(0,t.jsxs)(n.ll,{className:"flex items-center",children:[(0,t.jsx)(j.Z,{className:"h-5 w-5 mr-2"}),"Database Statistics"]}),(0,t.jsx)(n.SZ,{children:"Key database metrics and health indicators"})]}),(0,t.jsx)(n.aY,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:"Storage Usage"}),(0,t.jsx)("span",{className:"text-sm",children:"68%"})]}),(0,t.jsx)(r.E,{value:68,className:"h-2"}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:"Connection Pool"}),(0,t.jsx)("span",{className:"text-sm",children:"24/100"})]}),(0,t.jsx)(r.E,{value:24,className:"h-2"}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:"Query Cache"}),(0,t.jsx)("span",{className:"text-sm",children:"94.2%"})]}),(0,t.jsx)(r.E,{value:94.2,className:"h-2"})]})})]}),(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{children:[(0,t.jsxs)(n.ll,{className:"flex items-center",children:[(0,t.jsx)(h.Z,{className:"h-5 w-5 mr-2"}),"Real-time Activity"]}),(0,t.jsx)(n.SZ,{children:"Current database activity and operations"})]}),(0,t.jsx)(n.aY,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,t.jsx)("span",{className:"text-sm",children:"Queries/sec"})]}),(0,t.jsx)("span",{className:"text-sm font-medium",children:"145"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),(0,t.jsx)("span",{className:"text-sm",children:"Transactions/sec"})]}),(0,t.jsx)("span",{className:"text-sm font-medium",children:"89"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-yellow-500 rounded-full"}),(0,t.jsx)("span",{className:"text-sm",children:"Slow queries"})]}),(0,t.jsx)("span",{className:"text-sm font-medium",children:"3"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),(0,t.jsx)("span",{className:"text-sm",children:"Failed queries"})]}),(0,t.jsx)("span",{className:"text-sm font-medium",children:"0"})]})]})})]})]})}),(0,t.jsx)(d.nU,{value:"tables",className:"space-y-4",children:(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{children:[(0,t.jsx)(n.ll,{children:"Database Tables"}),(0,t.jsx)(n.SZ,{children:"Overview of all database tables and their status"})]}),(0,t.jsx)(n.aY,{children:(0,t.jsxs)(x.iA,{children:[(0,t.jsx)(x.xD,{children:(0,t.jsxs)(x.SC,{children:[(0,t.jsx)(x.ss,{children:"Table Name"}),(0,t.jsx)(x.ss,{children:"Rows"}),(0,t.jsx)(x.ss,{children:"Size"}),(0,t.jsx)(x.ss,{children:"Last Updated"}),(0,t.jsx)(x.ss,{children:"Status"}),(0,t.jsx)(x.ss,{children:"Actions"})]})}),(0,t.jsx)(x.RM,{children:D.map(e=>(0,t.jsxs)(x.SC,{children:[(0,t.jsx)(x.pj,{className:"font-medium",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(p.Z,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:e.name})]})}),(0,t.jsx)(x.pj,{children:e.rows.toLocaleString()}),(0,t.jsx)(x.pj,{children:e.size}),(0,t.jsx)(x.pj,{children:(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(v.Z,{className:"h-3 w-3"}),(0,t.jsx)("span",{className:"text-sm",children:e.lastUpdated.toLocaleTimeString()})]})}),(0,t.jsx)(x.pj,{children:(0,t.jsx)(c.C,{variant:U(e.status),className:"capitalize",children:e.status})}),(0,t.jsx)(x.pj,{children:(0,t.jsxs)("div",{className:"flex space-x-1",children:[(0,t.jsx)(l.z,{size:"sm",variant:"outline",children:(0,t.jsx)(f.Z,{className:"h-3 w-3"})}),(0,t.jsx)(l.z,{size:"sm",variant:"outline",children:(0,t.jsx)(y.Z,{className:"h-3 w-3"})})]})})]},e.name))})]})})]})}),(0,t.jsx)(d.nU,{value:"connections",className:"space-y-4",children:(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{children:[(0,t.jsx)(n.ll,{children:"Active Database Connections"}),(0,t.jsx)(n.SZ,{children:"Current active connections to the database"})]}),(0,t.jsx)(n.aY,{children:(0,t.jsxs)(x.iA,{children:[(0,t.jsx)(x.xD,{children:(0,t.jsxs)(x.SC,{children:[(0,t.jsx)(x.ss,{children:"User"}),(0,t.jsx)(x.ss,{children:"Database"}),(0,t.jsx)(x.ss,{children:"Host"}),(0,t.jsx)(x.ss,{children:"State"}),(0,t.jsx)(x.ss,{children:"Duration"}),(0,t.jsx)(x.ss,{children:"Query"}),(0,t.jsx)(x.ss,{children:"Actions"})]})}),(0,t.jsx)(x.RM,{children:z.map(e=>(0,t.jsxs)(x.SC,{children:[(0,t.jsx)(x.pj,{className:"font-medium",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(N.Z,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:e.user})]})}),(0,t.jsx)(x.pj,{children:e.database}),(0,t.jsx)(x.pj,{children:e.host}),(0,t.jsx)(x.pj,{children:(0,t.jsx)(c.C,{variant:M(e.state),className:"capitalize",children:e.state})}),(0,t.jsx)(x.pj,{children:e.duration}),(0,t.jsx)(x.pj,{children:(0,t.jsx)("div",{className:"max-w-xs truncate text-sm",children:e.query||"No active query"})}),(0,t.jsx)(x.pj,{children:(0,t.jsx)(l.z,{size:"sm",variant:"outline",children:(0,t.jsx)(b.Z,{className:"h-3 w-3"})})})]},e.id))})]})})]})}),(0,t.jsx)(d.nU,{value:"performance",className:"space-y-4",children:(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{children:[(0,t.jsxs)(n.ll,{className:"flex items-center",children:[(0,t.jsx)(w.Z,{className:"h-5 w-5 mr-2"}),"Query Performance"]}),(0,t.jsx)(n.SZ,{children:"Database query performance metrics and optimization"})]}),(0,t.jsx)(n.aY,{children:(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-3",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("div",{className:"text-sm font-medium",children:"Average Query Time"}),(0,t.jsx)("div",{className:"text-2xl font-bold",children:"89ms"}),(0,t.jsx)("div",{className:"text-xs text-muted-foreground",children:"+12ms from yesterday"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("div",{className:"text-sm font-medium",children:"Slow Queries"}),(0,t.jsx)("div",{className:"text-2xl font-bold",children:"3"}),(0,t.jsx)("div",{className:"text-xs text-muted-foreground",children:"Queries > 1000ms"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("div",{className:"text-sm font-medium",children:"Index Usage"}),(0,t.jsx)("div",{className:"text-2xl font-bold",children:"96.8%"}),(0,t.jsx)("div",{className:"text-xs text-muted-foreground",children:"Queries using indexes"})]})]})})]})}),(0,t.jsx)(d.nU,{value:"backup",className:"space-y-4",children:(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{children:[(0,t.jsxs)(n.ll,{className:"flex items-center",children:[(0,t.jsx)(g.Z,{className:"h-5 w-5 mr-2"}),"Backup & Restore"]}),(0,t.jsx)(n.SZ,{children:"Database backup status and restore operations"})]}),(0,t.jsx)(n.aY,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("div",{className:"text-sm font-medium",children:"Last Backup"}),(0,t.jsx)("div",{className:"text-lg font-bold",children:"2 hours ago"}),(0,t.jsx)("div",{className:"text-xs text-muted-foreground",children:"Daily backup completed successfully"}),(0,t.jsx)(c.C,{variant:"default",children:"Success"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("div",{className:"text-sm font-medium",children:"Backup Size"}),(0,t.jsx)("div",{className:"text-lg font-bold",children:"1.8 GB"}),(0,t.jsx)("div",{className:"text-xs text-muted-foreground",children:"Compressed backup file size"})]})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsxs)(l.z,{children:[(0,t.jsx)(y.Z,{className:"h-4 w-4 mr-2"}),"Create Backup"]}),(0,t.jsxs)(l.z,{variant:"outline",children:[(0,t.jsx)(k.Z,{className:"h-4 w-4 mr-2"}),"Restore from Backup"]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("div",{className:"text-sm font-medium",children:"Backup Schedule"}),(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:"Daily backups at 2:00 AM UTC"}),(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:"Weekly full backups on Sundays"}),(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:"Retention: 30 days for daily, 12 weeks for weekly"})]})]})})]})})]})]})}}},function(e){e.O(0,[6723,9502,4522,9187,2971,4938,1744],function(){return e(e.s=944)}),_N_E=e.O()}]);